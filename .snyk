# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.25.1
# ignores vulnerabilities until expiry date; change duration by modifying expiry date
ignore:
  'snyk:lic:unmanaged:github.com:lz4:lz4:GPL-2.0':
    - '*':
        reason: Li<PERSON> is licensed as BSD-2
        created: 2024-11-01T22:05:46.469Z
  SNYK-UNMANAGED-LLOYDYAJL-5670902:
    - '*':
        reason: Patched file 'yajl-CVE-2023-33460.patch'
        created: '2025-03-28T13:05:24.234Z'
  'snyk:lic:unmanaged:github.com:unicode-org:icu:GPL-2.0':
    - '*':
        reason: Permissively licensed as ICU
        created: 2025-04-18T19:47:13.233Z
  'snyk:lic:unmanaged:github.com:unicode-org:icu:GPL-3.0':
    - '*':
        reason: Permissively licensed as ICU
        created: 2025-04-18T19:47:32.267Z
  'snyk:lic:unmanaged:gitee.com:libcap:GPL-2.0':
    - '*':
        reason: Dual licensed as BSD-3
        created: 2025-04-18T19:50:04.127Z
  SNYK-UNMANAGED-LIBEXPAT-9459883:
    - '*':
        reason: Actual version 2.7.1 is not properly detected by snyk
        created: 2025-04-18T19:56:36.171Z
patch: {}
exclude:
  global:
    - src/rebound
