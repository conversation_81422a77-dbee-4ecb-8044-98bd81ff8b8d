/*
 * wally_table.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Table access/update within wally. The core of wally data manipulation lives here.
 */

#include <arpa/inet.h>
#include <stdio.h>
#include "zevent/zevent.h"
#include "zpath_misc/zpath_misc.h"

#include "wally/wally.h"
#include "wally/wally_column_nullify.h"
#include "wally/wally_filter_table.h"
#include "wally/wally_hash.h"
#include "wally/wally_private.h"
#include "wally/wally_oper.h"


/* debug info */
int interest_cb_count = 0;    /* count for interest_list */
#define WALLY_LOCK_TABLE_TIMEOUT_US     2000000

static int wally_index_remove_row(struct wally_index_column *column, struct wally_row *row);

int wally_table_write_origin_row(struct wally_table *table, struct argo_object *row)
{
    struct wally_origin *origin;

    if (!table) {
        WALLY_DEBUG_WRITE_ROW("No table to write row");
        return WALLY_RESULT_NOT_FOUND;
    }
    if (!table->origins_count) {
        WALLY_DEBUG_WRITE_ROW("No origin DBs to write row");
        return WALLY_RESULT_NOT_FOUND;
    }
    origin = table->origins[table->origins_count - 1];
    if (!origin) {
        WALLY_LOG(AL_ERROR, "No origin?");
        return WALLY_RESULT_ERR;
    }

    if (!origin->is_writable) {
        WALLY_DEBUG_WRITE_ROW("DB table %s, wally %s not writable",
                              table->name,
                              table->wally->name);
        return WALLY_RESULT_ERR;
    }

    if (wally_debug & WALLY_DEBUG_WRITE_ROW_BIT) {
        char dump[10000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            WALLY_DEBUG_WRITE_ROW("Write row for db %s, table %s: %s",
                                  table->wally->name,
                                  table->name,
                                  dump);
        } else {
            WALLY_DEBUG_WRITE_ROW("Write large row for db %s, table %s",
                                  table->wally->name,
                                  table->name);
        }
    }
    return wally_origin_enqueue_write_row(origin, table->origin_write_queue_index[table->origins_count - 1], row, 0);
}


int wally_table_set_min_valid_sequence(struct wally_table *table, int64_t sequence)
{
    int res;
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    res = wally_table_set_min_valid_sequence_locked(table, sequence);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return res;
}


int wally_table_set_min_valid_sequence_locked(struct wally_table *table, int64_t sequence)
{
    int res;
    size_t i;

    WALLY_LOG(AL_NOTICE, "Set min valid sequence for table %s:%s to %ld", table->wally->name, table->name, (long) sequence);

    for (i = 0; i < table->origins_count; i++) {
        if (table->origins[i]->set_sequence) {
            res = (table->origins[i]->set_sequence)(table->origins[i]->callout_handle, table->name, sequence);
            if (res) {
                WALLY_LOG(AL_ERROR, "Could not set sequence for table %s", table->name);
            } else {
                //WALLY_LOG(AL_DEBUG, "Set min sequence for table %s", table->name);
            }
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

enum filter_result is_row_filtered(struct zhash_table *filter_table_hash,
                           struct wally_table *table,
                           struct argo_object *row)
{
    int ret;
    if (!filter_table_hash) {
        return NOT_FILTERED;
    }
    struct wally_filter_set* f_s = zhash_table_lookup(filter_table_hash,
                                                      table->name,
                                                      strlen(table->name),
                                                      NULL);
    if (!f_s) {
        WALLY_LOG(AL_DEBUG, "No filter set found for table %s or filter was disabled", table->name);
        return NOT_FILTERED;
    }

    int ii = 0;
    int filter_exists = 0;
    for (ii = 0; ii < f_s->num_filters; ii++) {
        struct wally_filter_column* f_c = &(f_s->filter_objects[ii]);
        if (!f_c || !f_c->enabled) {
            WALLY_LOG(AL_DEBUG, "No filter set found for table %s or filter was disabled", table->name);
            continue;
        }
        if (wally_debug & WALLY_DEBUG_ROW_BIT) {
            char dump[8000];
            if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                WALLY_LOG(AL_DEBUG, "Row being filtered: %s, table name = %s column name = %s",
                                    dump,
                                    table->name,
                                    f_c->name);
            }
        }

        uint8_t* field_value;
        size_t field_len;
        ret = argo_object_read_binary_by_column_name(row, f_c->name, &field_value, &field_len);
        if (ret != ARGO_RESULT_NO_ERROR) {
            // The row does not contain any columns for which a filter is present
            //
            WALLY_LOG(AL_DEBUG, "Error reading field from row: %s", argo_result_string(ret));
            continue;
        }

        filter_exists = 1;

        if (f_c->key_length == field_len && memcmp(f_c->key_data, (void*)field_value, f_c->key_length) == 0) {
            // We found a matching filter. This row can be sent downstream.
            //
            return NOT_FILTERED;
        }
    }

    if (filter_exists) {
        WALLY_LOG(AL_DEBUG, "Filtering row out for table %s", table->name);
        return FILTERED;
    } else {
        WALLY_LOG(AL_DEBUG, "No valid filters exist for table %s", table->name);
        return NOT_FILTERED;
    }
}

void registrant_request_failure_stats(struct wally_registrant *registrant , struct wally_table *table) {
    if (registrant && table && table->default_registrant != registrant) {
         registrant->table->wally->registrant_stat.total_failed_requests ++;
    }
}


/*
 * This callback processes the callback for outstanding data for a
 * registrant's callback. That is, if there are rows for this
 * interest, this callback continues to send them.
 *
 * It is generally called whenever data transmission to a registrant
 * is to be performed/resumed. Ordinary row arrival does not
 * necessarily trigger this routine to be called- that case is usually
 * accomplished directly. (simply for efficiency)
 *
 * Doing row searches on this callback structure is a little bit
 * tricky- AVL searches only work with a valid row to compare. For
 * that reason, we tend to keep a handle on the last row we have
 * successfully sent. For the "first" time, this row is a "fake" row-
 * generated by copying any row associated with the interest and
 * setting its sequence to zero, then using it to search.
 *
 * This makes this routine a little bit involved, but it is quite
 * simple. It is also the reason why this routine is often skipped
 * when possible.
 */
int wally_process_registrant_callback(struct wally_registrant_callback *cb)
{
    struct wally_row *row;
    int64_t row_sequence;
    int res;
    int xmit_complete = 0;
    int64_t now = epoch_us();
    int64_t now_s = 0, delta_s = 0;
    struct zthread_info *zthread = zthread_self();
    uint32_t max_hb_miss_timeout_s = 0;

    if (cb->is_feeding) {
        /*
         * The callback is blocked on the registrant feed list. In
         * this case, we take the callback off the feed list in order
         * to continue with normal row transmission. The callback will
         * be replaced on the feed list if it needs to block again.
         */
        ZTAILQ_REMOVE(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
        cb->is_feeding = 0;
    }

    if (zthread) {
        /* This function can be called from the context of Wally or FOHH thread.
         * FOHH thread heartbeat delta timeout is 20 seconds, whereas for Wally threads
         * it can to 20 or 60 seconds. So get the configured timeout and find
         * the one third timeout
         * */
        max_hb_miss_timeout_s = WALLY_DIV_ROUND_CLOSEST(zthread->maximum_heartbeat_delta_s, WALLY_ONE_THIRD_DIVISOR);
    }

    /* Okay, now we need to see if we have any rows to call
     * back. There is a "special" case we check first- if there is
     * only one row for this interest, then we don't do any fancy
     * searching of any sort. Of any sort. /snicker */
    if (cb->just_callback) {
        xmit_complete = 1;
    } else {
        row = ZLIST_FIRST(&(cb->interest->rows));
        if (row && ! ZLIST_NEXT(row, index_match_list[cb->interest->column->my_index])) {
            /* Send exactly one row... If sequence is okay. */
            if (argo_object_read_int_by_column_index(row->current_row, cb->interest->column->table->row_sequence_argo_index, &row_sequence)) {
                /* XXX LOG */
            } else {
                /* We make sure that we only send rows back that have
                 * come in as a result of our own registration. We
                 * only increment the interests' sequence on rows
                 * arriving for our specific registration request. */
                if ((row_sequence > cb->sequence)) { //  && (row_sequence <= cb->interest->max_sequence_seen)) {
                    if (is_row_filtered(cb->filter_table_hash, cb->interest->column->table, row->current_row) == FILTERED) {
                        if (row->previous_row) {
                            wally_object_release_slow(cb->registrant->table->wally, row->previous_row);
                            row->previous_row = NULL;
                        }
                        cb->sequence = row_sequence;
                        cb->row_count++;
                        cb->filter_count++;
                        xmit_complete = 1;
                        cb->last_row_us = now;
                    } else if (cb->registrant->row_callback) {
                        //fprintf(stderr, "%s: Send row\n", __FUNCTION__);
                        struct argo_object *redacted_row = wally_column_nullify(row->current_row, cb->nullify_state);
                        res = (cb->registrant->row_callback)(cb->registrant->row_callback_cookie,
                                                             cb->registrant,
                                                             cb->interest->column->table,
                                                             row->previous_row,
                                                             redacted_row ? redacted_row : row->current_row,
                                                             cb->callback_request_id);
                        if (redacted_row)
                            argo_object_release(redacted_row);

                        if (res == WALLY_RESULT_WOULD_BLOCK) {
                            cb->is_feeding = 1;
                            ZTAILQ_INSERT_TAIL(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
                        } else {
                            if (row->previous_row) {
                                wally_object_release_slow(cb->registrant->table->wally, row->previous_row);
                                row->previous_row = NULL;
                            }
                            cb->sequence = row_sequence;
                            cb->row_count++;
                            xmit_complete = 1;
                            cb->last_row_us = now;
                        }
                    } else {
                        xmit_complete = 1;
                        cb->last_row_us = now;
                    }
                } else {
                    /* The row has already been sent, apparently. */
                    xmit_complete = 1;
                }
            }
        } else if (!row) {
            /* Send zero rows, since we have none. */
            xmit_complete = 1;
        } else {
            /* We have more than one row. Find the next row to send, and
             * send it. Continue sending rows until we would block or
             * until we are complete. */

            /* For now, for simplicity, we copy a row from the table, set
             * its sequence appropriately in order to do an AVL
             * getnext. Not that the AVL tree stores wally_rows, so we
             * make a fake wally_row to go along with the fake object. It
             * is okay for this object to be on the stack, as it is only
             * used for comparison (avl seaches) */
            struct argo_object *fake_object;
            struct wally_row fake_row;
            fake_object = argo_object_copy(row->current_row);
            if (!fake_object) {
                /* XXX LOG */
                WALLY_LOG(AL_ERROR, "Could not copy row.");
            } else {
                struct wally_row *next_row = &fake_row;
                next_row->current_row = fake_object;
                /* Set fake_row sequence appropriately. */
                if ((res = argo_object_write_int_by_column_index(fake_object, cb->interest->column->table->row_sequence_argo_index, cb->sequence))) {
                    /* XXX LOG */
                    WALLY_LOG(AL_ERROR, "Could not write sequence in fake row.");
                } else {
                    //fprintf(stderr, "%s:%s:%d: Resuming xmission at sequence = %ld\n", __FILE__, __FUNCTION__, __LINE__, (long) cb->sequence);
                    while ((next_row = avl_get_next_object(cb->interest->column->tree, next_row))) {
                        /* Get the sequence of the returned row. */
                        if (argo_object_read_int_by_column_index(next_row->current_row, cb->interest->column->table->row_sequence_argo_index, &row_sequence)) {
                            /* Huh? Could not read sequence value? */
                            /* XXX LOG */
                            break;
                        } else {
                            /* If the index key value has changed, then we are done reading the rows for this index. */
                            if (cb->interest->column->data_type == argo_field_data_type_string) {
                                char *string;
                                if (argo_object_read_string_by_column_index(next_row->current_row,
                                                                            cb->interest->column->argo_field_index,
                                                                            &string)) {
                                    WALLY_LOG(AL_ERROR, "Read column index failed for string? type mismatch? table = %s", cb->interest->column->table->name);
                                    registrant_request_failure_stats(cb->registrant,cb->interest->column->table);
                                    argo_object_release(fake_object);
                                    return WALLY_RESULT_ERR;
                                }
                                if (!string) string = "";
                                if (strcmp(string, cb->interest->key_data)) {
                                    /* We have switched rows... Stop walk. */
                                    xmit_complete = 1;
                                    break;
                                }
                            } else if (cb->interest->column->data_type == argo_field_data_type_integer) {
                                int64_t value;
                                if (argo_object_read_int_by_column_index(next_row->current_row,
                                                                         cb->interest->column->argo_field_index,
                                                                         &value)) {
                                    WALLY_LOG(AL_ERROR, "Read column index failed for int? type mismatch? table = %s", cb->interest->column->table->name);
                                    registrant_request_failure_stats(cb->registrant,cb->interest->column->table);
                                    argo_object_release(fake_object);
                                    return WALLY_RESULT_ERR;
                                }
                                if (value != cb->interest->key) {
                                    /* We have switched rows... Stop walk. */
                                    xmit_complete = 1;
                                    break;
                                }
                            } else if ((cb->interest->column->data_type == argo_field_data_type_binary) &&
                                       wally_column_is_inet(cb->interest->column)) {
                                uint8_t *data;
                                size_t data_len;
                                if (argo_object_read_binary_by_column_index(next_row->current_row,
                                                                            cb->interest->column->argo_field_index,
                                                                            &data,
                                                                            &data_len)) {
                                    WALLY_LOG(AL_ERROR, "Read column index failed for binary? type mismatch? table = %s", cb->interest->column->table->name);
                                    registrant_request_failure_stats(cb->registrant,cb->interest->column->table);
                                    argo_object_release(fake_object);
                                    return WALLY_RESULT_ERR;
                                }
                                if ((data_len != cb->interest->key_length) ||
                                    (memcmp(data, cb->interest->key_data, data_len) != 0)) {
                                    /* We have switched rows... Stop walk. */
                                    xmit_complete = 1;
                                    break;
                                }
                            } else if (cb->interest->column->is_null) {
                                /* Null index- no need to check index value. */
                            } else {
                                WALLY_LOG_NOT_IMPLEMENTED();
                                argo_object_release(fake_object);
                                registrant_request_failure_stats(cb->registrant,cb->interest->column->table);
                                return WALLY_RESULT_NOT_IMPLEMENTED;
                            }
                            if (row_sequence <= cb->sequence) {
                                /* XXX LOG: This is an out of order occurrence, and SHOULD NOT HAPPEN */
                                WALLY_LOG(AL_ERROR, "Error: Out of sequence: row sequence = %ld, cb sequence = %ld",
                                          (long) row_sequence,
                                          (long) cb->sequence);
                                break;
                            } else {
                                /* As above, we make sure that we only
                                 * send rows back that have come in as
                                 * a result of our own registration.
                                 * We only increment the interests'
                                 * sequence on rows arriving for our
                                 * specific registration request. */
                                if (is_row_filtered(cb->filter_table_hash, cb->interest->column->table, next_row->current_row) == FILTERED) {
                                    if (next_row->previous_row) {
                                        wally_object_release_slow(cb->registrant->table->wally, next_row->previous_row);
                                        next_row->previous_row = NULL;
                                    }
                                    cb->sequence = row_sequence;
                                    cb->row_count++;
                                    cb->filter_count++;
                                    cb->last_row_us = now;
                                } else if (cb->registrant->row_callback) {
                                    // fprintf(stderr, "%s: Send row\n", __FUNCTION__);
                                    struct argo_object *redacted_row =
                                            wally_column_nullify(next_row->current_row, cb->nullify_state);
                                    res = (cb->registrant->row_callback)(
                                            cb->registrant->row_callback_cookie,
                                            cb->registrant,
                                            cb->interest->column->table,
                                            next_row->previous_row,
                                            redacted_row ? redacted_row : next_row->current_row,
                                            cb->callback_request_id);
                                    if (redacted_row)
                                        argo_object_release(redacted_row);

                                    if (res == WALLY_RESULT_WOULD_BLOCK) {
                                        cb->is_feeding = 1;
                                        ZTAILQ_INSERT_TAIL(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
                                        break;
                                    } else {
                                        if (next_row->previous_row) {
                                            wally_object_release_slow(cb->registrant->table->wally, next_row->previous_row);
                                            next_row->previous_row = NULL;
                                        }
                                        cb->sequence = row_sequence;
                                        cb->row_count++;
                                        cb->last_row_us = now;
                                        if (zthread && (cb->row_count % wally_max_hb_iteration) == 0) {
                                            now_s = monotime_s();
                                            delta_s = (now_s - zthread->last_heartbeat_monotime_s);
                                            if ((delta_s > max_hb_miss_timeout_s)) {
                                                WALLY_DEBUG_HB_TIMEOUT(
                                                        "Null colume transfer heartbeat threshold reached. count %d delta_s %" PRId64 "",
                                                        cb->row_count, delta_s);
                                                cb->registrant->table->wally->batch_hb_count++;
                                                WALLY_LOG(AL_NOTICE,
                                                        "Null colume transfer heartbeat threshold reached. count %d delta_s %" PRId64 "",
                                                        cb->row_count, delta_s);
                                                zthread_heartbeat(zthread);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!next_row) {
                        xmit_complete = 1;
                    }

                    /* XXX If there is a "search row" associated with this
                    * callback, use it to perform the AVL search. (That row may
                    * or may not have yet been transmitted) Note that only
                    * non-copied rows are stored in the callback. */

                    /* XXX keep calling getnext and sending rows until we have
                    * exceeded this interest's rows or we would block. */
                }
                argo_object_release(fake_object);
            }
        }
    }

    /* If we have completed sending rows, and if we need to send a
    * result, send a result. */

    if (xmit_complete) {
        /* This might mark the interest feeding again */
        wally_interest_result_callback_cb(cb->interest, cb);
    }
    if (cb->is_feeding) {
        return WALLY_RESULT_WOULD_BLOCK;
    } else {
        return WALLY_RESULT_NO_ERROR;
    }
}

/*
 * Send index deregistration to specified db.
 *
 * wally lock MUST be held as write lock when called.
 *
 * Returns success, or, well, failure. Duh.
 *
 * If deregistration would block, the interest is queued on the origin DB.
 * If deregistration does not block, the interest itself is removed.
 *
 * Note that there is no deregistration acknowledgement.
 */
int send_deregister_interest_internal(struct wally_interest *interest)
{
    struct wally_index_column *column = interest->column;
    struct wally_table *table = column->table;
    struct wally *wally = table->wally;
    char *column_name;
    int db_index = interest->origin_db_index;
    int result;

    WALLY_DEBUG_REGISTRATION("%s: %s: Deregistering interest from origin %d", wally->name, interest->description, (int) interest->origin_db_index);

    /* Handle input states. */
    switch (interest->origin_db_state) {
    case unregistered:
    case deleted:
    case passive:
        // Bad state.
        WALLY_LOG(AL_CRITICAL, "%s/%s/%s/%s: Deregistration to %s started in bad state: %s",
                  wally->name,
                  table->name,
                  column->name,
                  interest->description,
                  table->origins[db_index]->name,
                  wally_origin_state_strings[interest->origin_db_state]);
        // Abort rather than try recover.
        wally_oper_event_post(E_TERMINATE, SE_FSM_FAIL, NULL);
        return WALLY_RESULT_BAD_STATE;

    case deregistration_xmit_pending:
        // This happens when our pending queue is reevaluated. Remove
        // self from queue. We will probably be added below- that's
        // fine.
        pthread_mutex_lock(&(table->origins[db_index]->interest_deregister_list_lock));
        ZTAILQ_REMOVE(&(table->origins[db_index]->deregistering_interests_list),
                interest, origin_db_processing);
        pthread_mutex_unlock(&(table->origins[db_index]->interest_deregister_list_lock));
        interest->still_in_deregister_pending_list = 0;
        break;

    case registration_xmit_pending:
        // Cancel the old registration, and we're done.
        pthread_mutex_lock(&(table->origins[db_index]->interest_register_list_lock));
        ZTAILQ_REMOVE(&(table->origins[db_index]->registering_interests_list),
                interest, origin_db_processing);
        pthread_mutex_unlock(&(table->origins[db_index]->interest_register_list_lock));
        interest->still_in_register_pending_list = 0;
        interest->db_block_queue_insert_us = 0;
        interest_assign_state(interest, unregistered);
        return WALLY_RESULT_NO_ERROR;
        /*break;*/

    case registered_waiting_for_response:
    case registered:
    default:
        // Nothing special.
        break;
    }

    /* Ask origin DB to perform the request. (Yay for
     * super-internal-argo-state powers, here!) */
    if (column->is_null) {
        column_name = "";
    } else {
        /* Note: argo column names are permanent. But following the
         * ptrs to them can change dynamically, thus we need the read
         * lock */
        argo_read_lock();
        column_name = table->argo_description->description[column->argo_field_index]->public_description.field_name;
        argo_unlock();
    }

    /* This is called with a lock held. That's a little
     * scary. Hope it doesn't call back to us and deadlock. */
    if (column->data_type == argo_field_data_type_string) {
        result = (table->origins[db_index]->deregister_index)(table->origins[db_index]->callout_handle,
                                                              table->name,
                                                              column_name,
                                                              interest->key_data);
    } else if (column->data_type == argo_field_data_type_integer) {
        char str[100];
        snprintf(str, sizeof(str), "%ld", (long)interest->key);
        result = (table->origins[db_index]->deregister_index)(table->origins[db_index]->callout_handle,
                                                              table->name,
                                                              column_name,
                                                              str);
    } else if (wally_column_is_inet(column)) {
        /* Binary Inet */
        char str[ARGO_INET_ADDRSTRLEN];
        char *sstr = str;
        argo_json_inet_generate(&sstr, sizeof(str), (struct argo_inet *)interest->key_data, 0);
        result = (table->origins[db_index]->deregister_index)(table->origins[db_index]->callout_handle,
                                                              table->name,
                                                              column_name,
                                                              str);
    } else if (column->is_null) {
        /* Null column. Key doesn't matter. */
        result = (table->origins[db_index]->deregister_index)(table->origins[db_index]->callout_handle,
                                                              table->name,
                                                              column_name,
                                                              "0");
    } else {
        /* Binary. */
        /* XXX LOG */
        interest->db_block_queue_insert_us = 0;
        WALLY_LOG_NOT_IMPLEMENTED();
        return WALLY_RESULT_NOT_IMPLEMENTED;
    }
    if ((result == WALLY_RESULT_NO_ERROR) ||
        (result == WALLY_RESULT_ASYNCHRONOUS)) {
        /* Success. NOW we can modify our request index state, etc */
        result = zhash_table_remove(table->requests, &(interest->origin_db_request_id), sizeof(interest->origin_db_request_id), NULL);
        table->request_id++;
        interest->origin_db_request_id = table->request_id;
        interest->db_block_queue_insert_us = 0;
        interest_assign_state(interest, unregistered);
    } else if (result == WALLY_RESULT_WOULD_BLOCK) {
        /* Print the logs and move the interest to TAIL, only if
         * 1. WALLY_RESULT_WOULD_BLOCK is held for multiple times continueosly
         * 2. Delay is more than 2 seconds
         *
         * Reason for the check 1.
         * Queue processing is serialised, node can be in tail and unprocessed for
         * any number of seconds and this is expected. So the calculation should start
         * only after the node started processing.
         * Epoch time and origin_db_state is help us to find whether the node is currently
         * in processing.
         * First time Epoch time is marked only when the current state is registration_xmit_pending
         * and db_block_queue_insert_us is 0
         */
        if (interest->origin_db_state == deregistration_xmit_pending) {
            /* Set the time for the first time */
            if (interest->db_block_queue_insert_us &&
                    (epoch_us() - interest->db_block_queue_insert_us) > WALLY_LOCK_TABLE_TIMEOUT_US) {
                WALLY_LOG(AL_NOTICE, "%s: %s - Deregister table blocked",
                        table->origins[db_index]->name, table->name);
                interest->db_block_queue_insert_us = epoch_us();
                pthread_mutex_lock(&(table->origins[db_index]->interest_deregister_list_lock));
                ZTAILQ_INSERT_TAIL(&(table->origins[db_index]->deregistering_interests_list), interest, origin_db_processing);
                pthread_mutex_unlock(&(table->origins[db_index]->interest_deregister_list_lock));
                interest->still_in_deregister_pending_list = 1;

            } else {
                if (interest->db_block_queue_insert_us == 0) {
                    interest->db_block_queue_insert_us = epoch_us();
                }
                pthread_mutex_lock(&(table->origins[db_index]->interest_deregister_list_lock));
                ZTAILQ_INSERT_HEAD(&(table->origins[db_index]->deregistering_interests_list), interest, origin_db_processing);
                pthread_mutex_unlock(&(table->origins[db_index]->interest_deregister_list_lock));
                interest->still_in_deregister_pending_list = 1;
            }

        } else {
            interest_assign_state(interest, deregistration_xmit_pending);
            pthread_mutex_lock(&(table->origins[db_index]->interest_deregister_list_lock));
            ZTAILQ_INSERT_HEAD(&(table->origins[db_index]->deregistering_interests_list), interest, origin_db_processing);
            pthread_mutex_unlock(&(table->origins[db_index]->interest_deregister_list_lock));
            interest->still_in_deregister_pending_list = 1;
        }
    } else {
        interest->db_block_queue_insert_us = 0;
        WALLY_LOG(AL_ERROR, "%s/%s/%s/%s: Deregistration to %s returned %s",
                  wally->name, table->name, column->name, interest->description, table->origins[db_index]->name, wally_error_strings[result]);
        /* Do nothing. An attempt will be made again in the future via
         * the state machine */
    }
    return result;
}


/*
 * Send index registration to specified db.
 *
 * wally lock MUST be held as write lock when called.
 *
 * Returns success, or, well, failure. Duh.
 *
 */
int send_register_interest_internal(struct wally_interest *interest)

{
    struct wally_index_column *column = interest->column;
    struct wally_table *table = column->table;
    struct wally *wally = table->wally;
    char str[ARGO_INET_ADDRSTRLEN]; /* Big enough for any 64 bit int or IP address */
    char *column_name;
    int result;
    int db_index = interest->origin_db_index;
    char *q = NULL;
    int64_t next_request_id;

    //WALLY_DEBUG_REGISTRATION("Enter");

    /* Handle input states. */
    switch (interest->origin_db_state) {
    case registered:
    case registered_waiting_for_response:
    case deleted:
        // Bad state.
        WALLY_LOG(AL_CRITICAL, "%s/%s/%s/%s: Registration to %s started in bad state: %s",
                  wally->name,
                  table->name,
                  column->name,
                  interest->description,
                  table->origins[db_index]->name,
                  wally_origin_state_strings[interest->origin_db_state]);
        // Abort via watchdog
        wally_oper_event_post(E_TERMINATE, SE_FSM_FAIL, NULL);
        return WALLY_RESULT_BAD_STATE;
    case registration_xmit_pending:
        // This happens when our pending queue is reevaluated. Remove
        // self from queue. We will probably be added below- that's
        // fine.
        pthread_mutex_lock(&(table->origins[db_index]->interest_register_list_lock));
        ZTAILQ_REMOVE(&(table->origins[db_index]->registering_interests_list), interest, origin_db_processing);
        pthread_mutex_unlock(&(table->origins[db_index]->interest_register_list_lock));
        interest->still_in_register_pending_list = 0;
        break;
    case deregistration_xmit_pending:
        // Cancel the old deregistration, and we're done.
        pthread_mutex_lock(&(table->origins[db_index]->interest_deregister_list_lock));
        ZTAILQ_REMOVE(&(table->origins[db_index]->deregistering_interests_list), interest, origin_db_processing);
        pthread_mutex_unlock(&(table->origins[db_index]->interest_deregister_list_lock));
        interest->still_in_deregister_pending_list = 0;
        interest_assign_state(interest, registered);
        interest->waiting_response_time_hist = 0;
        interest->db_block_queue_insert_us = 0;
        return WALLY_RESULT_NO_ERROR;
        /*break;*/
    case unregistered:
    case passive:
    default:
        // Nothing special.
        break;
    }

    /* Get value that is being registered */
    if (column->data_type == argo_field_data_type_string) {
        q = interest->key_data;
    } else if (column->data_type == argo_field_data_type_integer) {
        snprintf(str, sizeof(str), "%ld", (long) interest->key);
        q = str;
    } else if (column->data_type == argo_field_data_type_binary) {
        if (wally_column_is_inet(column)) {
            char *sstr = str;
            argo_json_inet_generate(&sstr, sizeof(str), (struct argo_inet *)interest->key_data, 0);
            q = str;
        } else {
            /* XXX LOG. */
            interest->db_block_queue_insert_us = 0;
            WALLY_LOG_NOT_IMPLEMENTED();
            return ARGO_RESULT_NOT_IMPLEMENTED;
        }
    }

    /* Ask origin DB to perform the request. (Yay for
     * super-internal-argo-state powers, here!) */
    if (column->is_null) {
        column_name = "";
        str[0] = 0;
        q = str;
    } else {
        /* Note: argo column names are permanent. But following the
         * ptrs to them can change dynamically, thus we need the read
         * lock */
        argo_read_lock();
        column_name = table->argo_description->description[column->argo_field_index]->public_description.field_name;
        argo_unlock();
    }

    /* Grab a sequence to use- but it might not GET used unless we
     * actually successfully transmit */
    next_request_id = table->request_id + 1;

    /* This is called with a lock held. That's a little
     * scary. Hope it doesn't call back to us and deadlock. */
    result = (table->origins[db_index]->register_index)(table->origins[db_index]->callout_handle,
                                                        next_request_id,
                                                        interest->max_sequence_seen,
                                                        table->name,
                                                        column_name,
                                                        q);
    if (result) {
        if (result == WALLY_RESULT_WOULD_BLOCK) {

            /* Print the logs and move the interest to TAIL, only if
             * 1. WALLY_RESULT_WOULD_BLOCK is held for multiple times continueosly
             * 2. Delay is more than 2 seconds
             *
             * Reason for the check 1.
             * Queue processing is serialised, node can be in tail and unprocessed for
             * any number of seconds and this is expected. So the calculation should start
             * only after the node started processing.
             * Epoch time and origin_db_state is help us to find whether the node is currently
             * in processing.
             * First time Epoch time is marked only when the current state is registration_xmit_pending
             * and db_block_queue_insert_us is 0
             */
            if (interest->origin_db_state == registration_xmit_pending) {
                /* db_block_queue_insert_us is assigned and delay is more than  */
                if (interest->db_block_queue_insert_us &&
                        (epoch_us() - interest->db_block_queue_insert_us) > WALLY_LOCK_TABLE_TIMEOUT_US) {
                    WALLY_LOG(AL_NOTICE, "%s: %s - Register table blocked",
                            table->origins[db_index]->name, table->name);
                    interest->db_block_queue_insert_us = epoch_us();
                    /* Delay is more than 2 seconds. Just move the interest to TAIL and move on
                     * to next interest in the queue */
                    pthread_mutex_lock(&(table->origins[db_index]->interest_register_list_lock));
                    ZTAILQ_INSERT_TAIL(&(table->origins[db_index]->registering_interests_list), interest, origin_db_processing);
                    pthread_mutex_unlock(&(table->origins[db_index]->interest_register_list_lock));
                    interest->still_in_register_pending_list = 1;
                } else {
                    /* Delay is less than 2 seconds and still not unblocked. Reinsert to the HEAD */
                    if (interest->db_block_queue_insert_us == 0) {
                        interest->db_block_queue_insert_us = epoch_us();
                    }
                    pthread_mutex_lock(&(table->origins[db_index]->interest_register_list_lock));
                    ZTAILQ_INSERT_HEAD(&(table->origins[db_index]->registering_interests_list), interest, origin_db_processing);
                    pthread_mutex_unlock(&(table->origins[db_index]->interest_register_list_lock));
                    interest->still_in_register_pending_list = 1;
                }

            } else {
                /* Queue this request (in the form of the interest) on the
                 * origin DB. Note that we don't actually assign
                 * next_request_id. No use consuming it if it is not
                 * used. Keeps request IDs pretty much strictly
                 * sequential */
                interest_assign_state(interest, registration_xmit_pending);
                pthread_mutex_lock(&(table->origins[db_index]->interest_register_list_lock));
                ZTAILQ_INSERT_HEAD(&(table->origins[db_index]->registering_interests_list), interest, origin_db_processing);
                pthread_mutex_unlock(&(table->origins[db_index]->interest_register_list_lock));
                interest->still_in_register_pending_list = 1;
            }

            WALLY_DEBUG_REGISTRATION("Origin DB is blocking (%s) with ID = %ld, sequence = %ld",
                                     table->origins[db_index]->name,
                                     (long)interest->origin_db_request_id,
                                     (long)interest->max_sequence_seen);
        } else if (result == WALLY_RESULT_ASYNCHRONOUS) {
            /* Common code for success... */
            interest->db_block_queue_insert_us = 0;
            goto success;
        } else {
            /* Any other error: Log it, and set to unregistered. The
             * state machine will attempt to re-register in the
             * future */
            WALLY_LOG(AL_ERROR, "%s/%s/%s/%s: Registration to %s returned %s",
                      wally->name, table->name, column->name, interest->description, table->origins[db_index]->name, wally_error_strings[result]);
            interest->db_block_queue_insert_us = 0;
            interest_assign_state(interest, unregistered);
        }
    } else {
        /* Common code for success */
        goto success;
    }
    return result;

 success:

    /* Since we successfully performed the registration, NOW we update
     * our indexing state. To do otherwise is in error. */
    table->request_id = next_request_id;
    interest->origin_db_request_id = table->request_id;

    /* Add our sequence/interest to the outstanding reqeust
     * set. */
    result = zhash_table_store(table->requests, &(interest->origin_db_request_id), sizeof(interest->origin_db_request_id), 0, interest);
    if (result) {
        /* This is violently bad. */
        WALLY_LOG(AL_CRITICAL, "%s/%s/%s/%s: Origin %s, Hash table store for request failed: %d. Memory?",
                  wally->name, table->name, column->name, interest->description, table->origins[db_index]->name, result);
        /* We track as though registration succeeded. But this will
           result in lost rows from origin... */
        return result;
    }

    interest_assign_state(interest, registered_waiting_for_response);
    interest->origin_db_waiting_for_response_start_us = epoch_us();
    interest->origin_db_waiting_for_response_complete_us = 0;
    interest->logged = 0;
    WALLY_DEBUG_REGISTRATION("%s: %s: Sent registration request to origin DB (%s) with ID = %ld, sequence = %ld",
                             wally->name,
                             interest->description,
                             table->origins[db_index]->name,
                             (long)interest->origin_db_request_id,
                             (long)interest->max_sequence_seen);

    return result;
}

/*
 * See header. (for non-internal version)
 *
 * The wally global RW lock must be held (as a write lock) when this routine is called.
 */
int wally_table_deregister_for_row_internal(struct wally_registrant *registrant,
                                                   struct wally_index_column *column,
                                                   const void *key,
                                                   const size_t key_length)
{
    struct wally_interest *interest;
    struct wally_registrant_callback *cb, *tmp;
    int res = WALLY_RESULT_NOT_FOUND;


    /* Get the interest for this key. */
    if (column->is_null) {
        interest = column->null_column_interest;
    } else if (column->data_type == argo_field_data_type_string) {
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        interest = zhash_table_lookup(column->index_hash, ((int64_t *)key), key_length, NULL);
    } else {
        /* Binary */
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    }
    if (!interest) {
        /* If there is no interest, we shouldn't have had to deregister. */
        /* XXX LOG */
        //fprintf(stderr, "Error: No interest to deregister\n");
        return WALLY_RESULT_BAD_STATE;
    }

    //fprintf(stderr, "Deregister interest, currently in state %d\n", interest->origin_db_state);

    /* This is the part that sucks- we need to find the callback for
     * the registration. */
    ZLIST_FOREACH_SAFE(cb, &(interest->registrant_callbacks), interest_list, tmp) {
        if (cb->registrant == registrant) {
            //fprintf(stderr, "Found CB to destroy for registrant\n");
            wally_registrant_callback_destroy(cb);
        }
    }

    /* Run the interest state machine. It will deregister if necessary. */
    res = wally_interest_state_machine(interest);

    return res;
}

/*
 * See header. (for non-internal version)
 *
 * The wally global RW lock must be held (as a write lock) when this routine is called.
 */
static int wally_table_is_registered_for_row_internal(struct wally_registrant *registrant,
                                                      struct wally_index_column *column,
                                                      const void *key,
                                                      const size_t key_length)
{
    struct wally_interest *interest;
    struct wally_registrant_callback *cb, *tmp;

    /* Get the interest for this key. */
    if (column->is_null) {
        interest = column->null_column_interest;
    } else if (column->data_type == argo_field_data_type_string) {
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        interest = zhash_table_lookup(column->index_hash, ((int64_t *)key), key_length, NULL);
    } else {
        /* Binary */
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    }
    if (!interest) {
        /* If there is no interest, we clearly weren't registered. */
        return WALLY_RESULT_NOT_FOUND;
    }

    //fprintf(stderr, "Deregister interest, currently in state %d\n", interest->origin_db_state);

    /* Okay, now we get to do a linear search of all registrants for
     * our registrant. This isn't super-efficient... */
    ZLIST_FOREACH_SAFE(cb, &(interest->registrant_callbacks), interest_list, tmp) {
        if (cb->registrant == registrant) {
            return WALLY_RESULT_NO_ERROR;
        }
    }

    return WALLY_RESULT_NOT_FOUND;
}

static int create_interest(struct wally_index_column *column,
                           const void *key,
                           const size_t key_length,
                           struct wally_interest **result_interest, bool is_internal)
{
    char tmp_str[100];
    size_t tmp_str_len;
    struct wally_table *t = column->table;
    struct wally_interest *interest = NULL;
    int result;

    /* Check if the origin DB exists. */
    if (!t->origins[0]) {
        /* XXX LOG? */
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    /* Need to create/add this interest to the set of interests. */
    interest = (struct wally_interest *) WALLY_MALLOC(sizeof(*interest));
    if (!interest) {
        return WALLY_RESULT_NO_MEMORY;
    }
    //fprintf(stderr, "Creating interest = %p\n", interest);
    memset(interest, 0, sizeof(*interest));
    // Superfluous    interest->origin_db_index = 0;
    interest->column = column;

    if (is_internal  == 0) {
        interest->ext_reg_cb_count = 1;
    }
    interest_add_state(interest, unregistered);
    interest->key_length = key_length;
    if (column->is_null) {
        /* Ignore key. */
        tmp_str_len = sxprintf(tmp_str, tmp_str + sizeof(tmp_str), "%s: (NULL)", t->name);
    } else {
        if (column->data_type == argo_field_data_type_string) {
            interest->key_data = WALLY_MALLOC(key_length + 1);
            if (!interest->key_data) {
                WALLY_FREE(interest);
                return WALLY_RESULT_NO_MEMORY;
            }
            strncpy(interest->key_data, (char *) key, key_length + 1);
            ((char *)(interest->key_data))[key_length] = 0;
            tmp_str_len = sxprintf(tmp_str, tmp_str + sizeof(tmp_str), "%s: %s: %s", t->name, column->name, (char *) key);
        } else if (column->data_type == argo_field_data_type_integer) {
            if (key) {
                interest->key = *((int64_t *)key);
            } else {
                interest->key = 0;
            }
            tmp_str_len = sxprintf(tmp_str, tmp_str + sizeof(tmp_str), "%s: %s: %ld", t->name, column->name, (long)interest->key);
        } else {
            /* Binary. */
            interest->key_data = WALLY_MALLOC(key_length);
            if (!interest->key_data) {
                WALLY_FREE(interest);
                return WALLY_RESULT_NO_MEMORY;
            }
            memcpy(interest->key_data, key, key_length);
            tmp_str_len = sxprintf(tmp_str, tmp_str + sizeof(tmp_str), "%s: %s: <binary>", t->name, column->name);
        }
    }
    interest->description = WALLY_STRDUP(tmp_str, tmp_str_len);

    ZLIST_INIT(&(interest->registrant_callbacks));

    /* Add self to column index hash. */
    if (column->is_null) {
        column->null_column_interest = interest;
        result = ARGO_RESULT_NO_ERROR;
    } else if ((column->data_type == argo_field_data_type_string) || (column->data_type == argo_field_data_type_binary)) {
        //fprintf(stderr, "ARGO_HASH_STORE: Store %s\n", interest->key_data);
        result = zhash_table_store(column->string_index_hash,
                                 interest->key_data,
                                 key_length,
                                 1,
                                 interest);
    } else {
        result = zhash_table_store(column->index_hash,
                                   &(interest->key),
                                   sizeof(interest->key),
                                   0,
                                   interest);
    }
    if (result) {
        if (!(column->is_null)) {
            if ((column->data_type == argo_field_data_type_string) || (column->data_type == argo_field_data_type_binary)) {
                WALLY_FREE(interest->key_data);
            }
        }
        WALLY_FREE(interest);
        return result;
    }
    interest->column->table->interest_count ++;

    *result_interest = interest;
    return result;
}

/*
 * See header. (for non-internal version)
 *
 * The wally global RW lock must be held (as a write lock) when this routine is called.
 */
int wally_table_register_for_row_internal(struct wally_registrant *registrant,
                                                 struct wally_index_column *column,
                                                 const void *key,
                                                 const size_t key_length,
                                                 int64_t request_id,
                                                 int64_t request_sequence,
                                                 int request_atleast_one,
                                                 int just_callback,
                                                 int unique_registration,
                                                 wally_response_callback_f *response_callback,
                                                 void *response_callback_cookie,
                                                 struct zhash_table *request_filter_table_hash,
                                                 struct zhash_table *nullify_state,
                                                 int callback_on_calling_thread,
                                                 int pierce_fully_loaded) {
    struct wally_interest *interest;
    struct wally_registrant_callback *cb;
    int result;
    bool is_internal  = 1;

    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    /* Get the interest for this key. */
    if (column->is_null) {
        interest = column->null_column_interest;
    } else if (column->data_type == argo_field_data_type_string) {
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        interest = zhash_table_lookup(column->index_hash, key, key_length, NULL);
    } else {
        /* Binary. */
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    }

    /* In fully loaded mode all table has default registrant*/
    if (column->table->default_registrant != registrant) {
         is_internal = 0;
    }

    if (!interest) {
        result = create_interest(column, key, key_length, &interest, is_internal);
        if (result) return result;
        /* If this isn't the NULL column, we assume we are continuously registered */
        if (!column->is_null && column->table->fully_loaded && !pierce_fully_loaded) {
            /* This data has already been loaded by fully loaded, so
             * don't do the 'normal' interest work */
            interest_assign_state(interest, registered);
            interest->request_complete = 1;
            interest->request_complete_atleast_one = 1;
            interest->waiting_response_time_hist = 0;
        }
        if (pierce_fully_loaded) {
            interest->pierce_fully_loaded = 1;
        }
    } else {
        if (unique_registration) return WALLY_RESULT_NO_ERROR;

          if (is_internal == 0) {
              if (registrant && registrant->table && registrant->table->wally) {
                  registrant->table->wally->registrant_stat.interest_duplicate ++;
              }
              if (interest->ext_reg_cb_count == 0) {
                  registrant_interest_add_state_stat(interest);
              }
              interest->ext_reg_cb_count ++;
          }

        /* If it was a passively created interest, upgrade it. */
        /* if table is in recovery mode, change null column interest into unregistered mode
         *   to read from the sequence number given.
         */
        if ((interest->origin_db_state == passive) ||
                    (column->table->recovery_state.state == recovery_state_rowsyncstart)) {
            interest_assign_state(interest, unregistered);
            interest->max_sequence_seen = request_sequence;
            interest->origin_db_index = 0;
        }

    }

    if (!(wally_is_wallyd(column->table->wally)) &&
        (!wally_multiple_index_consistency_support) &&
        (column->table->last_registered_column != -1) &&
        (column->table->last_registered_column != column->my_index)) {
        /* We registered at least two columns, which requires
         * multiple_index_consistency set! */
        WALLY_LOG(AL_DEBUG, "%s: %s: Registering vs column %s, last was %s, multiple index consistency not enabled!",
                  column->table->wally->name,
                  column->table->name,
                  column->name,
                  column->table->indexed_columns[column->table->last_registered_column]->name);
    }
    column->table->last_registered_column = column->my_index;

    /* We have a valid interest. Now we just need to add this
     * registrant to the callback set for the interest. */

    /*
     * XXX NOTE: This is not checking for duplicate inserts, here!! I
     * do not want to do that check because that check would be
     * expensive! Rather this is relying on our user to be smart and
     * not request the same thing multiple times!
     *
     * Of course, if that's what they want, then that's what they
     * want...
     */

    if (registrant && registrant->table && registrant->table->wally) {
        if (is_internal == 0) {
            registrant->table->wally->registrant_stat.total_requests ++;
        }
    }

    cb = (struct wally_registrant_callback *) WALLY_MALLOC(sizeof(*cb));
    if (!cb) {
        if(registrant && column){
            registrant_request_failure_stats(registrant,column->table);
        }
        return WALLY_RESULT_NO_MEMORY;
    }

    memset(cb, 0, sizeof(*cb));

    cb->registrant = registrant;
    cb->interest = interest;
    cb->just_callback = just_callback;
    cb->callback = response_callback;
    if (callback_on_calling_thread) {
        cb->callback_to_zevent_base = zevent_self();
        if (!cb->callback_to_zevent_base) {
            WALLY_DEBUG_REGISTRATION("While registering %s:%s:%s Could not get event base",
                                     column->table->wally->name,
                                     column->table->name,
                                     column->name);
        }
    }
    cb->callback_cookie = response_callback_cookie;
    cb->callback_request_id = request_id;
    cb->sequence = request_sequence;
    cb->original_sequence = request_sequence;
    cb->request_atleast_one = request_atleast_one;
    cb->request_received_us = epoch_us();
    cb->filter_table_hash = request_filter_table_hash;
    cb->nullify_state = nullify_state;
    if (!request_sequence) cb->grab_all_rows = 1;

    ZLIST_INSERT_HEAD(&(interest->registrant_callbacks), cb, interest_list);
    interest_cb_count++;
    if (registrant && registrant->table) {
        WALLY_DEBUG_INTEREST_CB("Add interest callback, table:%s, total interest callback count:%d",
                            registrant->table->name, interest_cb_count);
    }
    if (registrant) {
        ZLIST_INSERT_HEAD(&(registrant->all_registrant_callbacks), cb, registrant_list);
    }

    /* Run the interest state machine- it might need to register,
     * etc. */
    wally_interest_state_machine(interest);

    //fprintf(stderr, "Leaving register_for_row_internal, interest db state = %d\n", interest->origin_db_state);

    /* if recovery mode, do not process registrants now and wait for callback */
    if (column->table->recovery_state.state != recovery_state_rowsyncstart )
    {
        /* We may have data for this interest already in memory that we
         * can send out. Check for it/make callbacks now. NOTE: callbacks
         * had better be thread-recursive, here, or this will
         * deadlock. (Because a lock is likely held when this routine is
         * called, and the callbacks likely grab the same lock) */
        result = wally_process_registrant_callback(cb);
    }

    return WALLY_RESULT_ASYNCHRONOUS;
}

/*
 * See header.
 */
int wally_table_deregister_for_row(struct wally_registrant *registrant,
                                   struct wally_index_column *column,
                                   const void *key,
                                   const size_t key_length)
{
    int result;
    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    result = wally_table_deregister_for_row_internal(registrant,
                                                     column,
                                                     key,
                                                     key_length);
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}

/*
 * See header.
 */
int wally_table_is_registered_for_row(struct wally_registrant *registrant,
                                      struct wally_index_column *column,
                                      const void *key,
                                      const size_t key_length)
{
    int result;
    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    result = wally_table_is_registered_for_row_internal(registrant,
                                                        column,
                                                        key,
                                                        key_length);
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}


/*
 * See header.
 */
int wally_table_register_for_row(struct wally_registrant *registrant,
                                 struct wally_index_column *column,
                                 const void *key,
                                 const size_t key_length,
                                 int64_t request_id,
                                 int64_t request_sequence,
                                 int request_atleast_one,
                                 int just_callback,
                                 int unique_registration,
                                 wally_response_callback_f *response_callback,
                                 void *response_callback_cookie)
{
    int result;

    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (column->table->recovery_state.state == recovery_state_rowsyncstart)
    {
        ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
        return WALLY_RESULT_ERR;
    }
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    column->table->fully_loaded_start_epoch_us = epoch_us();
    result = wally_table_register_for_row_internal(registrant,
                                                   column,
                                                   key,
                                                   key_length,
                                                   request_id,
                                                   request_sequence,
                                                   request_atleast_one,
                                                   just_callback,
                                                   unique_registration,
                                                   response_callback,
                                                   response_callback_cookie,
                                                   NULL,
                                                   NULL,
                                                   0,
                                                   0);
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}

/*
 * See header.
 */
int wally_table_register_for_row_recovery(struct wally_registrant *registrant,
                                                      struct wally_index_column *column,
                                                      const void *key,
                                                      const size_t key_length,
                                                      int64_t request_id,
                                                      int64_t request_sequence,
                                                      int request_atleast_one,
                                                      int just_callback,
                                                      int unique_registration,
                                                      wally_response_callback_f *response_callback,
                                                      void *response_callback_cookie)
{
    int result;

    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    result = wally_table_register_for_row_internal(registrant,
                                                   column,
                                                   key,
                                                   key_length,
                                                   request_id,
                                                   request_sequence,
                                                   request_atleast_one,
                                                   just_callback,
                                                   unique_registration,
                                                   response_callback,
                                                   response_callback_cookie,
                                                   NULL,
                                                   NULL,
                                                   0,
                                                   1);
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}

/*
 * See header.
 */
int wally_table_register_for_row_through_fully_loaded(struct wally_registrant *registrant,
                                                      struct wally_index_column *column,
                                                      const void *key,
                                                      const size_t key_length,
                                                      int64_t request_id,
                                                      int64_t request_sequence,
                                                      int request_atleast_one,
                                                      int just_callback,
                                                      int unique_registration,
                                                      wally_response_callback_f *response_callback,
                                                      void *response_callback_cookie)
{
    int result;

    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (column->table->recovery_state.state == recovery_state_rowsyncstart)
    {
        ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
        return WALLY_RESULT_ERR;
    }
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    result = wally_table_register_for_row_internal(registrant,
                                                   column,
                                                   key,
                                                   key_length,
                                                   request_id,
                                                   request_sequence,
                                                   request_atleast_one,
                                                   just_callback,
                                                   unique_registration,
                                                   response_callback,
                                                   response_callback_cookie,
                                                   NULL,
                                                   NULL,
                                                   0,
                                                   1);
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}

int wally_table_register_for_row_filtered(struct wally_registrant *registrant,
                                          struct wally_index_column *column,
                                          const void *key,
                                          size_t key_length,
                                          int64_t request_id,
                                          int64_t request_sequence,
                                          int request_atleast_one,
                                          int just_callback,
                                          int unique_registration,
                                          wally_response_callback_f *response_callback,
                                          void *response_callback_cookie,
                                          struct zhash_table *request_filter_table_hash,
                                          struct zhash_table *nullify_state) {
    int result;

    if (!column) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
    if (column->table->recovery_state.state == recovery_state_rowsyncstart)
    {
        ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
        return WALLY_RESULT_ERR;
    }
    if (!registrant) {
        registrant = column->table->default_registrant;
    }
    result = wally_table_register_for_row_internal(registrant,
                                                   column,
                                                   key,
                                                   key_length,
                                                   request_id,
                                                   request_sequence,
                                                   request_atleast_one,
                                                   just_callback,
                                                   unique_registration,
                                                   response_callback,
                                                   response_callback_cookie,
                                                   request_filter_table_hash,
                                                   nullify_state,
                                                   0,
                                                   0);

    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
    return result;
}

/*
 * See header.
 */
int wally_table_resume_callbacks(struct wally_registrant *registrant)
{
    struct wally_registrant_callback *cb;
    int res = WALLY_RESULT_NO_ERROR;
    ZPATH_RWLOCK_WRLOCK(&(registrant->table->lock), __FILE__, __LINE__);
    while ((cb = ZTAILQ_FIRST(&(registrant->feed_registrant_callbacks)))) {
        res = wally_process_registrant_callback(cb);
        if (res) {
#if 0
            fprintf(stderr, "%s:%s:%d: process_registrant_callbacks returned %s\n",
                    __FILE__,
                    __FUNCTION__,
                    __LINE__,
                    wally_error_strings[res]);
#endif
            break;
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(registrant->table->lock), __FILE__, __LINE__);
    return res;
}


/*
 * See header
 *
 * Basic operation:
 *
 * If there is a row to return return it immediately.
 *
 * If there is an interest for the index already, and it has returned
 * a miss, return a miss immediately.
 *
 * Otherwise, queue up an outstanding registration. It's easy (and
 * probable) to have multiple callbacks sitting on an interest at a
 * time.
 *
 * Must be called with lock held.
 *
 */
static int wally_table_get_rows_internal(struct wally_index_column *column,
                                         const void *key,
                                         const size_t key_length,
                                         void **row_result,
                                         size_t *result_count,
                                         int register_on_miss,
                                         wally_response_callback_f *response_callback,
                                         void *response_callback_cookie,
                                         int64_t response_callback_int,
                                         int callback_on_calling_thread)
{
    struct wally_interest *interest;
    struct wally_row *r;
    int result;
    size_t found_count = 0;
    int found_any = 0;
    int do_insert = 0;

    if (!column) {
        WALLY_LOG(AL_ERROR, "No column for get_rows");
        (*result_count) = 0;
        return WALLY_RESULT_NOT_FOUND;
    }

    /* Direct lookup... */
    if (column->is_null) {
        /* No unique row lookup for null columns, as they represent
         * the whole table. */
        (*result_count) = 0;
        return WALLY_RESULT_BAD_ARGUMENT;
    }
    if ((column->data_type == argo_field_data_type_string) || (column->data_type == argo_field_data_type_binary)) {
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else {
        interest = zhash_table_lookup(column->index_hash, key, key_length, NULL);
    }
    if (interest) {
        /* Update timestamp. */
        interest->default_registrant_last_access_timestamp = epoch_us_accuracy_s();

        /* We have an interest match. This is a unique table, so there
         * will only be one row. But zero rows is possible, too */
        ZLIST_FOREACH(r, &(interest->rows), index_match_list[column->my_index]) {
            found_any = 1; /* Even deleted rows are an indication we 'found' something */
            if (argo_object_is_deleted(r->current_row)) continue;
            if (found_count >= *result_count) {
                WALLY_LOG(AL_CRITICAL, "%s:%s:%s: More than %ld non-deleted results",
                          column->table->wally->name,
                          column->table->name,
                          column->name,
                          (long) *result_count);
                break;
            }
            row_result[found_count] = r->current_row->base_structure_void;
            argo_object_hold(r->current_row);
            found_count++;
        }

        if (((interest->origin_db_state == passive) && register_on_miss) ||
            (found_any && register_on_miss && (!(interest->request_complete)))) {
            result = WALLY_RESULT_ASYNCHRONOUS;
            do_insert = 1;
        } else if (found_any) {
            if (found_count) {
                result = WALLY_RESULT_NO_ERROR;
            } else {
                result = WALLY_RESULT_NOT_FOUND;
            }
            do_insert = 0;
        } else {
            if ((!response_callback) ||
                (!register_on_miss) ||
                (interest->request_complete)) {
                result = WALLY_RESULT_NOT_FOUND;
                do_insert = 0;
            } else {
                result = WALLY_RESULT_ASYNCHRONOUS;
                do_insert = 1;
            }
        }
    } else {
        if (!register_on_miss) {
            result = WALLY_RESULT_NOT_FOUND;
            do_insert = 0;
        } else {
            result = WALLY_RESULT_ASYNCHRONOUS;
            do_insert = 1;
        }
    }

    if (do_insert) {
        /* Need to upconvert lock. *should* be safe here as all these
         * fields are pretty safe */
        ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);
        ZPATH_RWLOCK_WRLOCK(&(column->table->lock), __FILE__, __LINE__);
        if (column->table->recovery_state.state == recovery_state_rowsyncstart)
        {
            /* Do not unlock. caller is expected to unlock */
            return WALLY_RESULT_ERR;
        }
        if ((result = wally_table_register_for_row_internal(column->table->default_registrant,
                                                            column,
                                                            key,
                                                            key_length,
                                                            response_callback_int,
                                                            0,
                                                            0,
                                                            0,
                                                            0,
                                                            response_callback,
                                                            response_callback_cookie,
                                                            NULL,
                                                            NULL,
                                                            callback_on_calling_thread,
                                                            0))) {
            /* XXX LOG? */
            /* Could be would_block, err, etc */
        } else {
            result = WALLY_RESULT_ASYNCHRONOUS;
        }
    }

    *result_count = found_count;
    return result;
}

static int wally_table_get_rows_fast_internal(struct wally_index_column *column,
                                              const void *key,
                                              const size_t key_length,
                                              void **row_result,
                                              size_t *result_count,
                                              int register_on_miss,
                                              wally_response_callback_f *response_callback,
                                              void *response_callback_cookie,
                                              int64_t response_callback_int)
{
    struct wally_interest *interest;
    struct wally_row *r;
    int result;
    size_t found_count = 0;
    int found_any = 0;
    int do_locked_version = 0;

    if (!column) {
        WALLY_LOG(AL_ERROR, "No column for get_rows");
        (*result_count) = 0;
        return WALLY_RESULT_NOT_FOUND;
    }

    /* Direct lookup... */
    if (column->is_null) {
        /* No unique row lookup for null columns, as they represent
         * the whole table. */
        (*result_count) = 0;
        return WALLY_RESULT_BAD_ARGUMENT;
    }
    if ((column->data_type == argo_field_data_type_string) || (column->data_type == argo_field_data_type_binary)) {
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else {
        interest = zhash_table_lookup(column->index_hash, key, key_length, NULL);
    }
    if (interest) {
        /* Update timestamp. */
        interest->default_registrant_last_access_timestamp = epoch_us_accuracy_s();

        /* We have an interest match. This is a unique table, so there
         * will only be one row. But zero rows is possible, too */
        ZLIST_FOREACH(r, &(interest->rows), index_match_list[column->my_index]) {
            found_any = 1; /* Even deleted rows are an indication we 'found' something */
            if (argo_object_is_deleted(r->current_row)) continue;
            if (found_count >= *result_count) {
                if (column->data_type == argo_field_data_type_string) {
                    WALLY_DEBUG_TABLE("%s:%s:%s: '%.*s': More than %ld non-deleted results\n",
                              column->table->wally->name,
                              column->table->name,
                              column->name,
                              (int)key_length,
                              (char *) key,
                              (long) *result_count);
                } else if (column->data_type == argo_field_data_type_integer) {
                    WALLY_DEBUG_TABLE("%s:%s:%s: %ld: More than %ld non-deleted results\n",
                              column->table->wally->name,
                              column->table->name,
                              column->name,
                              *((long *)key),
                              (long) *result_count);
                } else {
                    WALLY_DEBUG_TABLE("%s:%s:%s: More than %ld non-deleted results\n",
                              column->table->wally->name,
                              column->table->name,
                              column->name,
                              (long) *result_count);
                }
                break;
            }
            row_result[found_count] = r->current_row->base_structure_void;
            found_count++;
        }

        if (((interest->origin_db_state == passive) && register_on_miss) ||
            (found_any && register_on_miss && (!(interest->request_complete)))) {
            result = WALLY_RESULT_ASYNCHRONOUS;
            do_locked_version = 1;
        } else if (found_any) {
            if (found_count) {
                result = WALLY_RESULT_NO_ERROR;
            } else {
                result = WALLY_RESULT_NOT_FOUND;
            }
        } else {
            if ((!response_callback) ||
                (!register_on_miss) ||
                (interest->request_complete)) {
                result = WALLY_RESULT_NOT_FOUND;
            } else {
                result = WALLY_RESULT_ASYNCHRONOUS;
                do_locked_version = 1;
            }
        }
    } else {
        if (!register_on_miss) {
            result = WALLY_RESULT_NOT_FOUND;
        } else {
            result = WALLY_RESULT_ASYNCHRONOUS;
            do_locked_version = 1;
        }
    }

    if (do_locked_version) {

        ZPATH_RWLOCK_RDLOCK(&(column->table->lock), __FILE__, __LINE__);

        result = wally_table_get_rows_internal(column,
                                               key,
                                               key_length,
                                               row_result,
                                               result_count,
                                               register_on_miss,
                                               response_callback,
                                               response_callback_cookie,
                                               response_callback_int,
                                               1);

        ZPATH_RWLOCK_UNLOCK(&(column->table->lock), __FILE__, __LINE__);

        if (result == WALLY_RESULT_NO_ERROR) {
            for (found_count = 0; found_count < (*result_count); found_count++) {
                argo_structure_release(row_result[found_count]);
            }
        }
        return result;
    }

    *result_count = found_count;
    return result;
}

int wally_table_get_rows(struct wally_index_column *column,
                         const void *key,
                         const size_t key_length,
                         void **row_result,
                         size_t *result_count,
                         int register_on_miss,
                         wally_response_callback_f *response_callback,
                         void *response_callback_cookie,
                         int64_t response_callback_int)
{
    struct wally_table *w = column->table;
    int result;
    ZPATH_RWLOCK_RDLOCK(&(w->lock), __FILE__, __LINE__);

    result = wally_table_get_rows_internal(column,
                                           key,
                                           key_length,
                                           row_result,
                                           result_count,
                                           register_on_miss,
                                           response_callback,
                                           response_callback_cookie,
                                           response_callback_int,
                                           0);

    if (result != WALLY_RESULT_ASYNCHRONOUS) {
        ZPATH_RWLOCK_UNLOCK(&(w->lock), __FILE__, __LINE__);
    }
    return result;
}

int wally_table_get_rows_fast(struct wally_index_column *column,
                              const void *key,
                              const size_t key_length,
                              void **row_result,
                              size_t *result_count,
                              int register_on_miss,
                              wally_response_callback_f *response_callback,
                              void *response_callback_cookie,
                              int64_t response_callback_int)
{
    return (wally_table_get_rows_fast_internal(column,
                                               key,
                                               key_length,
                                               row_result,
                                               result_count,
                                               register_on_miss,
                                               response_callback,
                                               response_callback_cookie,
                                               response_callback_int));
}


int wally_table_get_rows_no_locking(struct wally_index_column *column,
                                    const void *key,
                                    const size_t key_length,
                                    void **row_result,
                                    size_t *result_count)
{
    return wally_table_get_rows_internal(column,
                                         key,
                                         key_length,
                                         row_result,
                                         result_count,
                                         0,
                                         NULL,
                                         NULL,
                                         0,
                                         0);
}

void wally_table_release_column_lock(struct wally_index_column *column, const char *file, int line)
{
    ZPATH_RWLOCK_UNLOCK(&(column->table->lock), file, line);
}

void wally_table_lock(struct wally_table *table)
{
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
}

void wally_table_unlock(struct wally_table *table)
{
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
}

int wally_unique_row_synchronous_callback(void *response_callback_cookie,
                                          struct wally_registrant *registrant,
                                          struct wally_table *table,
                                          int64_t sequence,
                                          int row_count)
{
    struct wally_interlock *lock = (struct wally_interlock *) response_callback_cookie;

    wally_interlock_release(lock);

    return WALLY_RESULT_NO_ERROR;
}

/*
 * See header
 *
 * Basic operation: Perform wally_get_unique_row, but synchronously.
 */
int wally_table_get_rows_synchronous(struct wally_index_column *column,
                                     const void *key,
                                     const size_t key_length,
                                     void **row_result,
                                     size_t *result_count,
                                     int register_on_miss)
{
    int result;
    struct wally_interlock lock;
    size_t original_result_count = *result_count;

    result = wally_table_get_rows(column, key, key_length, row_result,
                                  result_count, register_on_miss,
                                  wally_unique_row_synchronous_callback, &lock, 0);
    if (result == WALLY_RESULT_ASYNCHRONOUS) {

        wally_interlock_lock_1(&lock);
        wally_table_release_column_lock(column, __FILE__, __LINE__);
        wally_interlock_lock_2(&lock);

        *result_count = original_result_count;
        result = wally_table_get_rows(column, key, key_length, row_result,
                                      result_count, register_on_miss,
                                      NULL, NULL, 0);
        if ((result == WALLY_RESULT_NOT_FOUND) &&
            (wally_table_get_origin_status(column->table, 1) == wally_origin_status_not_ready)) {
            return WALLY_RESULT_NOT_READY;
        }
    }
    return result;
}


int wally_table_ignore_row_by_index(struct wally_index_column *column,
                                    void *key)
{
    struct wally_table *table = column->table;
    //struct wally *wally = table->wally;

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    /* This is a simple deregister on the default registrant. */

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    /* XXX LOG not implemented */
    WALLY_LOG_NOT_IMPLEMENTED();
    return WALLY_RESULT_NOT_IMPLEMENTED;
}


/*
 * See header.
 *
 * Returns status of first origin DB.
 */
enum wally_origin_status wally_table_get_origin_status(struct wally_table *table, int origin_index)
{
    int result = wally_origin_status_not_ready;

    if (!table->origins_count) return wally_origin_status_not_ready;
    if (origin_index >= table->origins_count) origin_index = table->origins_count - 1;
    result = (table->origins[origin_index]->get_status)(table->origins[origin_index]->callout_handle);
    return result;
}



/*
 * Internal version intended to be called with write lock
 */
struct wally_registrant *wally_table_create_registrant_internal(struct wally_table *table,
                                                                wally_row_callback_f *row_callback,
                                                                void *row_callback_cookie,
                                                                const char *debug_str)
{
    struct wally_registrant *r;

    r = (struct wally_registrant *) WALLY_MALLOC(sizeof(*r));
    if (!r) return NULL;

    memset(r, 0, sizeof(*r));

    r->table = table;
    ZLIST_INIT(&(r->all_registrant_callbacks));
    ZTAILQ_INIT(&(r->feed_registrant_callbacks));
    ZLIST_INSERT_HEAD(&(table->all_registrants), r, all_registrants);
    r->row_callback = row_callback;
    r->row_callback_cookie = row_callback_cookie;
    r->debug_str = WALLY_STRDUP(debug_str, strlen(debug_str));

    return r;
}


struct wally_registrant *wally_table_create_registrant(struct wally_table *table,
                                                       wally_row_callback_f *row_callback,
                                                       void *row_callback_cookie,
                                                       const char *debug_str)
{
    struct wally_registrant *res;

    /* Grab a write lock and call the internal version. */
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    res = wally_table_create_registrant_internal(table, row_callback, row_callback_cookie, debug_str);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return res;
}

/*
 * Must be called with write lock held.
 */
void wally_row_just_release(struct wally_table *table, struct wally_row *row)
{
    size_t i;
    int res;

    if (!row->current_row) {
        /* XXX LOG */
        WALLY_LOG(AL_CRITICAL, "No row on row release");
    } else {
        /* Need to remove row from global hash of indexes. */
        if (table->integer_key) {
            int64_t key;
            argo_object_read_int_by_column_index(row->current_row, table->row_key_argo_index, &key);
            res = zhash_table_remove(table->key_rows_integer, &key, sizeof(key), NULL);
            if (res) {
                /* XXX LOG */
                //fprintf(stderr, "Removing row from table row has returned %s\n", wally_error_strings[res]);
                WALLY_LOG(AL_CRITICAL, "Could not remove row from integer hash table");
            }
        } else {
            char *key;
            argo_object_read_string_by_column_index(row->current_row, table->row_key_argo_index, &key);
            if (!key) key = "";
            res = zhash_table_remove(table->key_rows_string, key, strlen(key), NULL);
            if (res) {
                /* XXX LOG */
                WALLY_LOG(AL_CRITICAL, "Could not remove row from argo hash table");
            }
        }
    }


    /* Need to remove row from AVL tree */
    for (i = 0; i < table->indexed_columns_count; i++) {
        wally_index_remove_row(table->indexed_columns[i], row);
    }

    if (row->current_row) wally_object_release_slow(table->wally, row->current_row);
    if (row->previous_row) wally_object_release_slow(table->wally, row->previous_row);

    //ZLIST_REMOVE(r, all_rows_in_table_list);

    /* Now, remove self from AVL trees. XXX */
    //fprintf(stderr, "ROWROWROW free: %p\n", row);
    WALLY_FREE_SLOW(row);
}

/*
 * Must be called with write lock held.
 */
void wally_row_release(struct wally_table *table, struct wally_row *row)
{
    if (row->registered_interest_count <= 0) {
        WALLY_LOG(AL_CRITICAL, "Registered interest count <= 0 on release");
    } else {
        row->registered_interest_count--;
        if (row->registered_interest_count == 0) {
            WALLY_DEBUG_ROW_DETAIL("Truly releasing row, since refcount=0");
            wally_row_just_release(table, row);
        }
    }
}

/*
 * Must be called with write lock held.
 */
void wally_row_hold(struct wally_row *r)
{
    r->registered_interest_count++;
}

/*
 * Must be called with write lock held
 */
void wally_interest_delete(struct wally_interest *interest)
{
    //struct wally_row *r, *tmp;
    //int index;
    //int res;
    struct wally_origin *origin = interest->column->table->origins[interest->origin_db_index];

    if (interest->column->data_type == argo_field_data_type_string) {
        //fprintf(stderr, "Deleting interest: %p : %s\n", interest, interest->key_data);
    } else if (interest->column->data_type == argo_field_data_type_integer) {
        //fprintf(stderr, "Deleting interest: %p : %ld\n", interest, (long)interest->key);
    }

    //fprintf(stderr, "Interest state = %d\n", interest->origin_db_state);

    if (interest->origin_db_state == registration_xmit_pending) {
        pthread_mutex_lock(&(origin->interest_register_list_lock));
        ZTAILQ_REMOVE(&(origin->registering_interests_list),
                interest, origin_db_processing);
        pthread_mutex_unlock(&(origin->interest_register_list_lock));
        interest->still_in_register_pending_list = 0;
        interest->db_block_queue_insert_us = 0;
    }
    if (interest->origin_db_state == deregistration_xmit_pending) {
        pthread_mutex_lock(&(origin->interest_deregister_list_lock));
        ZTAILQ_REMOVE(&(origin->deregistering_interests_list),
            interest, origin_db_processing);
        pthread_mutex_unlock(&(origin->interest_deregister_list_lock));
        interest->db_block_queue_insert_us = 0;
        interest->still_in_deregister_pending_list = 0;
    //fprintf(stderr, "Removing interest from origin\n");
    }

    /* Interest may be in query response hash table... Remove it. */
    //res = wally_hash_remove(interest->column->table->wally->requests, interest->origin_db_request_id);
    zhash_table_remove(interest->column->table->requests, &(interest->origin_db_request_id), sizeof(interest->origin_db_request_id), NULL);
    interest->origin_db_request_id = 0;
    //fprintf(stderr, "Removing request from hash table resulted in %s\n", wally_error_strings[res]);
    //interest->origin_db_state = deleted;

    /*
     * For all cases: We leave the interest intact.
     */
    interest_assign_state(interest, passive);

#if 0
    if (interest->is_passive) {
        interest_assign_state(interest, passive);
    } else {
        /* Acutally delete the interest */

        /* Go through all the interest's rows and release them. This
         * must be done safely. */
        index = interest->column->my_index;
        ZLIST_FOREACH_SAFE(r, &(interest->rows), index_match_list[index], tmp) {
            CLEAR_BIT(r->in_list, index);
            ZLIST_REMOVE(r, index_match_list[index]);
            interest->interest_list_row_count--;
            //fprintf(stderr, "rowrow release on interest %p\n", interest);
            wally_row_release(interest->column->table, r);
        }

        /* registrant_callbacks should be empty. Need to remove self from indexed column */

        if (interest->column->is_null) {
            /* Do nothing. */
        } else if ((interest->column->data_type == argo_field_data_type_string) ||
                   (interest->column->data_type == argo_field_data_type_binary)) {
            zhash_table_remove(interest->column->string_index_hash, interest->key_data, interest->key_length, NULL);
            WALLY_FREE_SLOW(interest->key_data);
        } else {
            zhash_table_remove(interest->column->index_hash, &(interest->key), sizeof(interest->key), NULL);
        }

        if (interest->origin_db_state != deleted) {
            //fprintf(stderr, "Freeing interest %p\n", interest);
            if (interest->column->is_null) {
                interest->column->null_column_interest = NULL;
            }
            if (interest->description) {
                WALLY_FREE_SLOW(interest->description);
            }
            WALLY_FREE_SLOW(interest);
        } else {
            //fprintf(stderr, "NOT Freeing interest %p\n", interest);
        }
    }
#endif // 0
}

/*
 * Must be called with global wally write lock
 *
 * Callbacks exist in two lists, and must be removed from
 * both. Callbacks must also correctly adjust row reference counts.
 *
 */
void wally_registrant_callback_destroy(struct wally_registrant_callback *cb)
{
    ZLIST_REMOVE(cb, interest_list);
    interest_cb_count--;
    if (cb->interest && cb->interest->ext_reg_cb_count > 0) {
        cb->interest->ext_reg_cb_count --;
        if (cb->interest->ext_reg_cb_count == 0) {
            registrant_interest_remove_state_stat(cb->interest);
        }
    }

    if (cb->registrant && cb->registrant->table && cb->registrant->table->wally) {
        if(cb->registrant != cb->registrant->table->default_registrant) {
            cb->registrant->table->wally->registrant_stat.total_deregister_count++;
        }
        WALLY_DEBUG_INTEREST_CB("Remove interest callback, table:%s, total interest callback count:%d",
                            cb->registrant->table->name, interest_cb_count);
    }

    ZLIST_REMOVE(cb, registrant_list);
    if (cb->is_feeding) {
        if (cb->registrant) {
            ZTAILQ_REMOVE(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
        }
    }

    WALLY_FREE_SLOW(cb);
}


/*
 * Internal version intended to be called with write lock
 *
 * Process is pretty simple: Remove all callbacks, remove self from registrant list, free self.
 */
void wally_table_destroy_registrant_internal(struct wally_registrant *registrant)
{
    struct wally_registrant_callback *cb;

    /*
     * Remove all callbacks.
     */
    while ((cb = ZLIST_FIRST(&(registrant->all_registrant_callbacks)))) {
        wally_registrant_callback_destroy(cb);
    }

    /*
     * Remove self from set of all registrants.
     */
    ZLIST_REMOVE(registrant, all_registrants);
    WALLY_FREE_SLOW(registrant->debug_str);
    WALLY_FREE_SLOW(registrant);
}


/*
 * Process is pretty simple: Remove all callbacks, remove self from registrant list, free self.
 */
void wally_table_destroy_registrant(struct wally_registrant *registrant)
{
    struct wally_table *wally_table = registrant->table;

    ZPATH_RWLOCK_WRLOCK(&(wally_table->lock), __FILE__, __LINE__);
    wally_table_destroy_registrant_internal(registrant);
    ZPATH_RWLOCK_UNLOCK(&(wally_table->lock), __FILE__, __LINE__);
}


/*
 * Remove row from all indexes.
 */
int wally_index_remove_row(struct wally_index_column *column, struct wally_row *row)
{
    struct wally_interest *interest;
    int res;

    if (column->is_null) {
        interest = column->null_column_interest;
    } else if (column->data_type == argo_field_data_type_string) {
        char *str_key;
        argo_object_read_string_by_column_index(row->current_row, column->argo_field_index, &str_key);
        if (!str_key) str_key = "";
        interest = zhash_table_lookup(column->string_index_hash, str_key, strlen(str_key), NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        int64_t key;
        argo_object_read_int_by_column_index(row->current_row, column->argo_field_index, &key);
        interest = zhash_table_lookup(column->index_hash, &key, sizeof(key), NULL);
    } else if (column->data_type == argo_field_data_type_binary) {
        size_t key_length;
        uint8_t *key;
        argo_object_read_binary_by_column_index(row->current_row, column->argo_field_index, &key, &key_length);
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else {
        interest = NULL;
    }
    if (interest) {
        if (GET_BIT(row->in_list, column->my_index)) {
            ZLIST_REMOVE(row, index_match_list[column->my_index]);
            row->registered_interest_count--;
            interest->interest_list_row_count--;
            CLEAR_BIT(row->in_list, column->my_index);
        }
    }

    if ((res = avl_delete_object(column->tree, row))) {
        /* It's okay to fail deleting an object- it was simply not
         * found. wally_index_remove_row is often called idempotently,
         * particularly when cleaning up a row. */
        //WALLY_LOG(AL_ERROR, "Row delete from AVL: Failed: %d", res);
    } else {
        //fprintf(stderr, "ROWROWROW %p delete from AVL\n", row);
    }


    return WALLY_RESULT_NO_ERROR;
}


/*
 * Returns: NUMBER of times the previous row was released! (Should only be 0 or 1 times...)
 */
int wally_interest_send_row_to_registrants(struct wally_interest *interest, struct wally_row *row)
{
    struct wally_registrant_callback *cb;
    int release_count = 0;
    int res;
    int64_t now = epoch_us();
    int64_t t_delta = 0;
    int count_bypassed = 0;
    int count_processed = 0;

    /* For each callback linked to this row... */
    WALLY_DEBUG_INTEREST_CB("process interest callback start at time(us):%" PRId64 , now);
    ZLIST_FOREACH(cb, &(interest->registrant_callbacks), interest_list) {
        if (cb->just_callback) {
            /* Do nothing... */
            count_bypassed++;
        } else {
            count_processed++;
            if (cb->is_feeding) {
                int64_t row_sequence;
                if (argo_object_read_int_by_column_index(row->current_row, interest->column->table->row_sequence_argo_index, &row_sequence)) {
                    /* XXX LOG */
                    continue;
                }
                //fprintf(stderr, "%s:%s:%d: Read sequence = %ld\n", __FILE__, __FUNCTION__, __LINE__, (long) row_sequence);
                if (row_sequence > cb->sequence) {
                    /* If the callback is on the feed list, AND the sequence of this
                     * row is GREATER than the callback sequence, then do nothing with
                     * this callback. */
                } else {
                    /* If the callback is on the feed list, AND the
                     * sequence of this row is LESS than the callback
                     * sequence, AND if the initially asked-for sequence
                     * was 0, then reset callback sequence to less than
                     * this row. (will cause some potential
                     * retransmissions. But this case shouldn't really
                     * happen anyways, so that's okay) */
                    if (cb->grab_all_rows) {
                        cb->sequence = row_sequence - 1;
                    }
                }
            } else {
                /* If the callback is NOT on the feed list, then try the regisrant
                 * callback. If the registrant callback would have blocked, then
                 * set the callback sequence appropriately, and enqueue on the
                 * feed list. */
                if (cb->registrant->row_callback) {
                    int64_t row_sequence;
                    if (argo_object_read_int_by_column_index(row->current_row, interest->column->table->row_sequence_argo_index, &row_sequence)) {
                        /* XXX LOG */
                        continue;
                    }
                    //fprintf(stderr, "%s:%s:%d: Read sequence = %ld\n", __FILE__, __FUNCTION__, __LINE__, (long) row_sequence);
                    if ((row_sequence > cb->sequence) || (cb->grab_all_rows)) {
                        if (is_row_filtered(cb->filter_table_hash, cb->interest->column->table, row->current_row) == FILTERED) {
                            if (row->previous_row) {
                                wally_object_release_slow(cb->registrant->table->wally, row->previous_row);
                                row->previous_row = NULL;
                            }
                            cb->row_count++;
                            cb->sequence = row_sequence;
                            cb->last_row_us = now;
                            cb->filter_count++;
                        } else {
                            struct argo_object *redacted_row =
                                    wally_column_nullify(row->current_row, cb->nullify_state);
                            res = (cb->registrant->row_callback)(cb->registrant->row_callback_cookie,
                                                                 cb->registrant,
                                                                 interest->column->table,
                                                                 row->previous_row,
                                                                 redacted_row ? redacted_row : row->current_row,
                                                                 cb->callback_request_id);
                            if (redacted_row)
                                argo_object_release(redacted_row);

                            if (res == WALLY_RESULT_WOULD_BLOCK) {
                                /* Need to enqueue this row for asynchronous
                                * transmission to registrant. */
                                cb->sequence = row_sequence - 1;
                                cb->is_feeding = 1;
                                //fprintf(stderr, "%s:%s: Would block. Sequence = %ld, is_feeding now 1\n", __FILE__, __FUNCTION__, (long) cb->sequence);
                                ZTAILQ_INSERT_TAIL(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
                            } else {
                                cb->row_count++;
                                cb->sequence = row_sequence;
                                cb->last_row_us = now;
                                //fprintf(stderr, "%s:%s: Successful send. Sequence = %ld, is_feeding now 1\n", __FILE__, __FUNCTION__, (long) cb->sequence);
                            }
                        }
                    }
                } else {
                    /* Increment the row count, because we would have sent
                     * it if we had a callback to use. This is done so
                     * that a "result" callback can correctly indicate how
                     * many rows "would have" been transferred. */
                    cb->row_count++;
                    cb->last_row_us = now;
                }
            }
        }
    }
    if (wally_debug & WALLY_DEBUG_INTEREST_CB_BIT) t_delta = epoch_us() - now;
    WALLY_DEBUG_INTEREST_CB("process interest callback complete, time spent(us):%" PRId64
                            ", %d callback processed, %d callback bypassed",
                            t_delta, count_processed, count_bypassed);

    return release_count;
}


/*
 * Add row to all appropriate indexes, AND perform callbacks to the
 * registered callbacks as appropriate.
 *
 * Returns the number of times the previous row was released. (should be 0 or 1 times)
 */
int wally_index_process_row(struct wally_index_column *column, struct wally_row *row, int64_t request_id)
{
    struct wally_interest *interest;
    int64_t int_key;
    uint8_t *ukey;
    void *key;
    size_t key_length = 0;
    int res;
    int release_count = 0;
    bool internal = 1;
    struct wally_table *table = column->table;


    if ((res = avl_add_object(column->tree, row))) {
        /* XXX Log */
        if (column->data_type == argo_field_data_type_integer) {
            int64_t value = -1;
            argo_object_read_int_by_column_index(row->current_row, column->argo_field_index, &value);

            WALLY_LOG(AL_ERROR, "Could not add object to AVL tree: %d, table = %s, column = %s, value = %ld", res, column->table->name,
                      column->table->argo_description->static_description[column->argo_field_index].field_name, (long)value);
        } else {
            WALLY_LOG(AL_ERROR, "Could not add object to AVL tree: %d, table = %s, column = %s", res, column->table->name,
                      column->table->argo_description->static_description[column->argo_field_index].field_name);
        }
    } else {
        //fprintf(stderr, "ROWROWROW %p add to AVL\n", row);
    }

    if (column->is_null) {
        interest = column->null_column_interest;
        key = NULL;
    } else if (column->data_type == argo_field_data_type_string) {
        char *str_key;
        argo_object_read_string_by_column_index(row->current_row, column->argo_field_index, &str_key);
        key = str_key;
        if (!key) key = "";
        key_length = strlen(key);
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        argo_object_read_int_by_column_index(row->current_row, column->argo_field_index, &int_key);
        key = &int_key;
        key_length = sizeof(int_key);
        interest = zhash_table_lookup(column->index_hash, &int_key, sizeof(int_key), NULL);
    } else if (column->data_type == argo_field_data_type_binary) {
        argo_object_read_binary_by_column_index(row->current_row, column->argo_field_index, &ukey, &key_length);
        key = ukey;
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else {
        key = NULL;
        interest = NULL;
        key_length = 0;
    }

    if (!interest) {
        /* Create interest in passive state. */
        res = create_interest(column,
                              key,
                              key_length,
                              &interest , internal);
        if (res) return res;
        if (!column->is_null && column->table->fully_loaded) {
            /* fully loaded created interests are automatically registered... (And complete!) */
            interest_assign_state(interest, registered);
            interest->waiting_response_time_hist = 0;
            interest->request_complete = 1;
            interest->request_complete_atleast_one = 1;
        } else {
            WALLY_DEBUG_REGISTRATION("Created passive interest: %s", interest->description);
            interest_assign_state(interest, passive);
            interest->is_passive = 1;
        }
    } else {
        if (table->recovery_state.state == recovery_state_rowsyncstart)
        {
            if (interest->max_sequence_seen < table->recovery_state.sequence)
            {
                /* If interest is having less max-sequence than recovery sequence, just skip
                 * this interest. It will get the rows on next registration request from registrants.
                 * Note: Not checking for passive state as it can happen only with passive
                 */
                return release_count;
            }
        }
    }


    if (interest) {

        SET_BIT(row->in_list, column->my_index);
        ZLIST_INSERT_HEAD(&(interest->rows), row, index_match_list[column->my_index]);
        row->registered_interest_count++;
        interest->interest_list_row_count++;
        interest->last_row_us = epoch_us();

        WALLY_DEBUG_ROW_DETAIL("%s/%s/%s: origin state = %s, request_id = %ld, origin_request_id = %ld, max_sequence_seen = %ld",
                               interest->column->table->wally->name,
                               interest->column->table->name,
                               interest->column->name,
                               wally_interest_state_str(interest->origin_db_state),
                               (long) request_id,
                               (long) interest->origin_db_request_id,
                               (long) interest->max_sequence_seen);

#if 0
        int64_t row_sequence;

        /* Moved to process_row_2, because we need to have all the
         * indexes filled in before we send the row to them, since the
         * callbacks may directly look up expecting to find us */
        if (interest->origin_db_state != passive) {

            if ((request_id == 0) || (request_id == interest->origin_db_request_id)) {

                argo_object_read_int_by_column_index(row->current_row, column->table->row_sequence_argo_index, &row_sequence);

                if (row_sequence > interest->max_sequence_seen) {
                    interest->max_sequence_seen = row_sequence;
                }

                release_count += wally_interest_send_row_to_registrants(interest, row);
            }
        }
#endif // 0
    }

    return release_count;
}

int wally_index_process_row_2(struct wally_index_column *column, struct wally_row *row, int64_t request_id)
{
    struct wally_interest *interest;
    int release_count = 0;
    int64_t int_key;
    uint8_t *ukey;
    void *key;
    size_t key_length;

    if (column->is_null) {
        interest = column->null_column_interest;
        key = NULL;
    } else if (column->data_type == argo_field_data_type_string) {
        char *str_key;
        argo_object_read_string_by_column_index(row->current_row, column->argo_field_index, &str_key);
        key = str_key;
        if (!key) key = "";
        key_length = strlen(key);
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else if (column->data_type == argo_field_data_type_integer) {
        argo_object_read_int_by_column_index(row->current_row, column->argo_field_index, &int_key);
        key = &int_key;
        key_length = sizeof(int_key);
        interest = zhash_table_lookup(column->index_hash, &int_key, sizeof(int_key), NULL);
    } else if (column->data_type == argo_field_data_type_binary) {
        argo_object_read_binary_by_column_index(row->current_row, column->argo_field_index, &ukey, &key_length);
        key = ukey;
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
    } else {
        key = NULL;
        interest = NULL;
    }

    if (interest) {
        int64_t row_sequence;

        if (interest->origin_db_state != passive) {

            if ((request_id == 0) || (request_id == interest->origin_db_request_id)) {

                argo_object_read_int_by_column_index(row->current_row, column->table->row_sequence_argo_index, &row_sequence);

                if (row_sequence > interest->max_sequence_seen) {
                    interest->max_sequence_seen = row_sequence;
                }
                /* Recovery Mode should be handled for all clients irrespective of interests */
                if (((column->table->recovery_state.state == recovery_state_rowsyncstart) &&
                            (row_sequence > column->table->recovery_state.prev_max_sequence)) ||
                      (column->table->recovery_state.state != recovery_state_rowsyncstart)) {
                    release_count += wally_interest_send_row_to_registrants(interest, row);
                }
            }
        }
		/* We update lookup table iff
		 * 1) multiple_index_consistency_feature flag is enabled
		 * 2) if the response is from the remote origin
		 * 3) if interest is already in registered state.  ie request and xfer response has already been sent earlier
		 * 4) if table is not fully loaded
		 * 5) if interest is pierce_fully_loaded
		 */
		if (wally_multiple_index_consistency_support) {
		    if ((interest->origin_db_index > 0) &&
				(interest->origin_db_state == registered) &&
				(!(interest->column->table->fully_loaded) ||
				(interest->pierce_fully_loaded))) {

				//update the lookup table
				WALLY_DEBUG_MIC("%s/%s/%s: Lookup table update from row processing",
									interest->column->table->wally->name,
									interest->column->table->name,
									interest->column->name);
				(void)wally_origin_enqueue_write_lookup_table(interest, 0);

			}
		}
    }
    return release_count;
}



/*
 * Lock must be held when called.
 *
 * origin_index is with respect to the table, not wally.
 */
int wally_table_process_row(struct wally_table *table, struct argo_object *object, int origin_index, int64_t request_id, int64_t *row_store_time, int64_t *row_cb_time)
{
    struct wally_row *row;
    int64_t row_key_integer;
    char *row_key_string;
    int release_count = 0;
    int res;
    size_t i;
    int64_t deleted_value = 0;
    int64_t new_sequence;
    int64_t t1_us = 0;
    int64_t t2_us = 0;


    if (wally_debug & WALLY_DEBUG_ROW_BIT) {
        char str[3000];

        if (argo_object_dump(object, str, sizeof(str), NULL, 1)) {
            str[0] = 0;
        }
        WALLY_DEBUG_ROW_DETAIL("%s: Process %srow sequence %ld, request_id = %ld, contains %s",
                               table->wally->name,
                               ((origin_index > 0) && (table->origins[origin_index - 1]->is_writable)) ? "AND WRITE " : "",
                               (long) argo_object_get_sequence(object),
                               (long) request_id,
                               str);
    }
    //fprintf(stderr, "%s:%s: Process row ID %ld\n", __FILE__, __FUNCTION__, (long) argo_object_get_sequence(object));

    if (table->wally->is_endpoint && (table->row_deleted_argo_index >= 0)) {
        /* Check if this is a deleted row! */
        argo_object_read_int_by_column_index(object, table->row_deleted_argo_index, &deleted_value);
        WALLY_DEBUG_ROW_DETAIL("Checked for deleted value. Have deleted value = %ld", (long) deleted_value);
    } else {
        //WALLY_DEBUG_ROW_DETAIL("No deleted field for this table: endpoint = %d, index = %d", table->wally->is_endpoint, table->row_deleted_argo_index);
    }
    if (table->recovery_state.state == recovery_state_rowsyncstart)
    {
        if (table->recovery_state.stop_processing_rows == 1) {
            return WALLY_RESULT_NO_ERROR;
        }
        table->recovery_state.received_rows++;
    }

    /* update last table updated time */
    table->last_update_time_s = epoch_s();

    /* If this origin has a LESSER origin, then have that origin DB
     * write this row. If that DB is writing... */
    if ((origin_index > 0) && (table->origins[origin_index - 1]->is_writable)) {
        table->last_remote_update_time_s = epoch_s();
        /* XXX Eventually, this should be able to filter DB
         * writes. But since row writing is written idempotent, it's
         * not so big a deal to be rewriting all rows here. Might make
         * postgres a little busy, but that's okay. (Keeping It
         * Simple, Senor) */
        /* Also, this routine updates for deleted rows as well, which
         * is good- postgres will issue a delete command if it
         * recognizes that the row is a deleted row and the postgres
         * is operating as an endpoint. */
        //fprintf(stderr, "%s:%s: Enqueue object for write:\n%s\n", __FILE__, __FUNCTION__, str);
        res = wally_origin_enqueue_write_row(table->origins[origin_index - 1],
                                             table->origin_write_queue_index[origin_index - 1],
                                             object,
                                             0 /* Tail insert */);
        if (res) return res;
    }


    /* Look up row by its key- either string form or integer form */
    if (table->integer_key) {
        res = argo_object_read_int_by_column_index(object, table->row_key_argo_index, &row_key_integer);
        if (res) {
            /* XXX LOG */
            WALLY_LOG(AL_ERROR, "Error: could not read key column from object??? (integer)");
            argo_object_release(object);
            return WALLY_RESULT_NOT_FOUND;
        }
        row = zhash_table_lookup(table->key_rows_integer, &row_key_integer, sizeof(row_key_integer), NULL);
    } else {
        res = argo_object_read_string_by_column_index(object, table->row_key_argo_index, &row_key_string);
        if (res) {
            /* XXX LOG */
            WALLY_LOG(AL_ERROR, "Error: could not read key column from object??? (string)");
            argo_object_release(object);
            return WALLY_RESULT_NOT_FOUND;
        }
        if (!row_key_string) row_key_string = "";
        row = zhash_table_lookup(table->key_rows_string, row_key_string, strlen(row_key_string), NULL);
    }

    /* If new row: If it goes active immediately, add it to all
     * indexes as appropriate. */
    /* If existing row, AND it will be replaced: Remove old row from
     * all indexes, add new row to all indexes. */
    argo_object_read_int_by_column_index(object, table->row_sequence_argo_index, &new_sequence);
    if (!row) {
        /* Create row. */
        row = (struct wally_row *) WALLY_MALLOC(sizeof(*row));
        memset(row, 0, sizeof(*row));

        if (table->integer_key) {
            res = zhash_table_store(table->key_rows_integer, &row_key_integer, sizeof(row_key_integer), 0, row);
            if (res) {
                /* XXX LOG */
                WALLY_FREE(row);
                return WALLY_RESULT_NO_MEMORY;
            }
        } else {
            res = zhash_table_store(table->key_rows_string, row_key_string, strlen(row_key_string), 0, row);
            if (res) {
                /* XXX LOG */
                WALLY_FREE(row);
                return WALLY_RESULT_NO_MEMORY;
            }
        }
        if (table->integer_key) {
            WALLY_DEBUG_ROW("New row, table = %s, key = %ld, sequence = %ld",
                            table->name,
                            (long) row_key_integer,
                            (long) new_sequence);
        } else {
            WALLY_DEBUG_ROW("New row, table = %s, key = %s, sequence = %ld",
                            table->name,
                            row_key_string,
                            (long) new_sequence);
        }
        table->row_count++;
        if ((table->recovery_state.state == recovery_state_rowsyncstart) &&
                ((new_sequence <= table->recovery_state.prev_max_sequence) ||
                 (0 == table->recovery_state.prev_max_sequence)))  {
            table->recovery_state.recovered_rows++;
        }

        /* It will go active shortly */
        //fprintf(stderr, "ROWROWROW allocate: %ld %p\n", (long)row_key, row);
    } else {
        int64_t current_sequence;

        //fprintf(stderr, "ROWROWROW already exists: %ld %p\n", (long) row_key, row);
        argo_object_read_int_by_column_index(row->current_row, table->row_sequence_argo_index, &current_sequence);

        if (table->integer_key) {
            WALLY_DEBUG_ROW("Updated row, table = %s, key = %ld, current sequence = %ld, new sequence = %ld",
                            table->name,
                            (long) row_key_integer,
                            (long) current_sequence,
                            (long) new_sequence);
        } else {
            WALLY_DEBUG_ROW("Updated row, table = %s, key = %s, current sequence = %ld, new sequence = %ld",
                            table->name,
                            row_key_string,
                            (long) current_sequence,
                            (long) new_sequence);
        }

        /* In recovery_mode, if sync_missing_rows is set, then skip all rows */
        if (table->recovery_state.state == recovery_state_rowsyncstart) {
            if (table->recovery_state.sync_missing_rows == 1) {
                if (new_sequence == current_sequence) {
                    return WALLY_RESULT_NO_ERROR;
                }
            }
        }

        /* IMPORTANT: We can arrive here if some system is asking
         * for a row that some other indexed column encountered!
         * We will cheat this case by allowing equal sequences
         * through! */
        if (new_sequence < current_sequence) {
            /* It's not an updated row. */
            return WALLY_RESULT_NO_ERROR;
        }

        /* No active/stale check- We're always active, at the moment. */
        /* Remove row from all indexes. */
        for (i = 0; i < table->indexed_columns_count; i++) {
            wally_index_remove_row(table->indexed_columns[i], row);
        }
    }

    if (!row->previous_row) {
        row->previous_row = row->current_row;
    } else {
        wally_object_release_slow(table->wally, row->current_row);
    }
    argo_object_hold(object);
    row->current_row = object;
    row->current_row_origin_db_index = origin_index;

    if (row->registered_interest_count != 0) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "Weird: registered interest count should be zero (it is %d)\n", (int)row->registered_interest_count);
    }

    /* Update max_sequence_seen at this point */
    table->max_sequence_seen = new_sequence;

    /* Update row indexing, and send row to interested parties. */
    if (wally_debug & WALLY_DEBUG_INTEREST_CB_BIT) t1_us = epoch_us();
    WALLY_DEBUG_INTEREST_CB("Update row indexing start at time(us):%" PRId64 ", table=%s, indexed column =%zd",
                            t1_us, table->name, table->indexed_columns_count);
    {
        int64_t start_time = 0;
        if (row_store_time) {
            start_time = epoch_us();
        }
        for (i = 0; i < table->indexed_columns_count; i++) {
            wally_index_process_row(table->indexed_columns[i], row, request_id);
        }
        if (row_store_time) {
            *row_store_time = epoch_us() - start_time;
        }
        if (row_cb_time) {
            start_time = epoch_us();
        }
        /* Send row to all clients in recovery mode */
        if ((table->recovery_state.state == recovery_state_rowsyncstart) &&
                    (new_sequence <= table->recovery_state.prev_max_sequence)) {
            wally_send_recovered_rows_to_all_clients(table, row);
        }
        /* Send to all clients in recovery mode but still interest registrants to be updated */
        /* In case new row also read as part of recovery mode, this requires interest updates */
        for (i = 0; i < table->indexed_columns_count; i++) {
            if (table->fully_loaded) {
                /* Fully loaded table uses request_id = 0 to make reads apply to all interests */
                release_count += wally_index_process_row_2(table->indexed_columns[i], row, 0);
            } else {
                release_count += wally_index_process_row_2(table->indexed_columns[i], row, request_id);
            }
        }
        if (row_cb_time) {
            *row_cb_time = epoch_us() - start_time;
        }
    }
    if (wally_debug & WALLY_DEBUG_INTEREST_CB_BIT) t2_us = epoch_us();
    WALLY_DEBUG_INTEREST_CB("Update row indexing complete, time spent(us):%" PRId64 ", table=%s, indexed column =%zd",
                            t2_us-t1_us, table->name, table->indexed_columns_count);

    /* Release previous row in case our registrants for some reason
     * don't care about it any more even though we are tracking it for
     * them */
    /*
     * TODO: release_count will always 0 as its not used by wally_interest_send_row_to_registrants
     *       Refactor this code and remove release_count
     */
    if (row->previous_row && (!release_count)) {
        wally_object_release_slow(table->wally, row->previous_row);
        row->previous_row = NULL;
    }

    //fprintf(stderr, "Row processing complete. Interest count now %d\n", (int)row->registered_interest_count);

    /*
     * If it's a deleted row, then we'll re-remove all
     * interests. (note- we do this 'late' in order to make sure that
     * interested parties see deleted rows.)
     */
    if (deleted_value) {
        WALLY_DEBUG_ROW_DETAIL("Deleting row");
        for (i = 0; i < table->indexed_columns_count; i++) {
            wally_index_remove_row(table->indexed_columns[i], row);
        }
    }

    /* If our interests all went away, free ourselves up using
     * row_release. */
    if (row->registered_interest_count == 0) {
        /* Free it up. */
        WALLY_DEBUG_ROW_DETAIL("Freeing row");
        wally_row_hold(row);
        wally_row_release(table, row);
        table->row_count--;
    }

    //WALLY_DEBUG_ROW_DETAIL("Row processing complete");

    return WALLY_RESULT_NO_ERROR;
}

static int wally_table_read_synchronized_response_cb(void *cookie,
                                                     struct wally_registrant *registrant,
                                                     struct wally_table *table,
                                                     int64_t sequence,
                                                     int status) {
    struct wally_interlock *lock = (struct wally_interlock *)cookie;
    if (lock)
        wally_interlock_release(lock);
    return WALLY_RESULT_NO_ERROR;
}

void wally_table_read_synchronized(struct wally_index_column *column, void *key, size_t key_len) {
    struct wally_interlock lock;

    wally_interlock_lock_1(&lock);
    int res = wally_table_register_for_row(NULL, column, key, key_len, 0, 0, 1, 0, 0,
                                           wally_table_read_synchronized_response_cb, &lock);
    while (res == WALLY_RESULT_NOT_READY) {
        WALLY_LOG(AL_ERROR, "Could not read table: register_for_row failed: %d", res);
        res = wally_table_deregister_for_row(NULL, column, key, key_len);
        sleep(1);
        res = wally_table_register_for_row(NULL, column, key, key_len, 0, 0, 1, 0, 0,
                                           wally_table_read_synchronized_response_cb, &lock);
    }

    WALLY_LOG(AL_NOTICE, "Reading %s...", column->table->name);
    wally_interlock_lock_2(&lock);
    WALLY_LOG(AL_NOTICE, "Reading %s... COMPLETE", column->table->name);
}
