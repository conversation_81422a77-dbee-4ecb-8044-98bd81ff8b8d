/*
 * wally_fohh.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Wally origin database implementation for accessing a remote wally
 *    as an origin database. Also includes code for exporting local
 *    wally as an origin database.
 *
 * ASSUMPTIONS:
 *
 * 1. Assumes FOHH has already been initialized.
 *
 * 2. When server is created, assumes wally is already running.
 */

#ifndef __WALLY_FOHH_H__
#define __WALLY_FOHH_H__

extern struct argo_structure_description *wally_fohh_register_row_request_string_description;
extern struct argo_structure_description *wally_fohh_deregister_row_request_string_description;
extern struct argo_structure_description *wally_fohh_request_result_description;
extern struct argo_structure_description *wally_fohh_register_row_request_integer_description;
extern struct argo_structure_description *wally_fohh_deregister_row_request_integer_description;
extern struct argo_structure_description *wally_fohh_version_description;
extern struct argo_structure_description *wally_fohh_version_ack_description;
struct wally_fohh_register_row_request_string {   /* _ARGO: object_definition */
    int64_t request_id;                           /* _ARGO: integer */
    int64_t sequence;                             /* _ARGO: integer */
    char *table_name;                             /* _ARGO: string */
    char *column_name;                            /* _ARGO: string */
    char *key;                                    /* _ARGO: string */
};

struct wally_fohh_deregister_row_request_string { /* _ARGO: object_definition */
    char *table_name;                             /* _ARGO: string */
    char *column_name;                            /* _ARGO: string */
    char *key;                                    /* _ARGO: string */
};

struct wally_fohh_request_result {                /* _ARGO: object_definition */
    int64_t request_id;                           /* _ARGO: integer */
    int64_t row_count;                            /* _ARGO: integer */
    int64_t table_exists;                         /* _ARGO: integer */
    char *table_name;                             /* _ARGO: string */
};

struct wally_fohh_recovery_begin_request {        /* _ARGO: object_definition */
    char *table_name;                             /* _ARGO: string */
    int64_t recovery_sequence;                    /* _ARGO: integer */
    int64_t recovery_timeout;                     /* _ARGO: integer */
    int sync_missing_rows;                        /* _ARGO: integer */
};

struct wally_fohh_recovery_end_request {        /* _ARGO: object_definition */
    char *table_name;                             /* _ARGO: string */
    int64_t server_sent_rows;                     /* _ARGO: integer */
};

struct wally_fohh_recovery_complete_response {      /* _ARGO: object_definition */
    char *table_name;                               /* _ARGO: string */
    int64_t received_row_count;                    /* _ARGO: integer */
};

/* Not Currently Used: */
struct wally_fohh_register_row_request_integer {  /* _ARGO: object_definition */
    int64_t request_id;                           /* _ARGO: integer */
    int64_t sequence;                             /* _ARGO: integer */
    const char *table_name;                       /* _ARGO: string */
    const char *column_name;                      /* _ARGO: string */
    const int64_t key;                            /* _ARGO: integer */
};

/* Not Currently Used: */
struct wally_fohh_deregister_row_request_integer {/* _ARGO: object_definition */
    const char *table_name;                       /* _ARGO: string */
    const char *column_name;                      /* _ARGO: string */
    const int64_t key;                            /* _ARGO: integer */
};

/*
 * A version is always sent as a first message, from client to server,
 * in any client/server connection. (i.e. sent from client to broker,
 * from assistant to broker, and from broker to dispatcher)
 *
 * A version_ack is always sent, but is empty if there is no
 * error. Otherwise error contains a sad string.
 */
struct wally_fohh_version {            /* _ARGO: object_definition */
    int32_t version_major;             /* _ARGO: integer */
    int32_t version_minor;             /* _ARGO: integer */
    int32_t version_patch;             /* _ARGO: integer */
    const char *ver_str;               /* _ARGO: string */
    int client_capabilities;           /* _ARGO: integer */
};

struct wally_fohh_version_ack {        /* _ARGO: object_definition */
    const char *error;                 /* _ARGO: string */
};

void wally_fohh_register_structures(void);

#endif /* __WALLY_FOHH_H__ */
