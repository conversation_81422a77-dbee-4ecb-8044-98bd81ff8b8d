#ifndef __WALLY_DB_H__
#define __WALLY_DB_H__

#include <event2/event.h>
#include "wally/wally_private.h"

#define WP_MAX_ROWS 10000
#define WP_MAX_ROWS_STR "10000"
#define DEFAULT_DB_STRING_SIZE 10000
#define WP_POLL_INTERVAL_US (5 * 1000) /* 5ms, 200 times/s */
#define WP_CONN_INTERVAL_US (1000000)  /* every sec */
#define WP_CONN_RECOVERY_COUNT    2    /* Recover DB connection for every 2 seconds */
#define WP_SERVER_INTERVAL_US (60*1000000)  /* every min */
#define WALLY_DB_MAX_CONNS 256
#define MAX_COLUMN_META_SIZE 512
#define MAX_WRITE_OBJECT_CNT 5000
#define WP_UPDATE_MIN_SEQ_US (300*1000000) /* 5 minutes */
#define MIN_POLL_TIME 1
#define MAX_POLL_TIME 60
#define POLL_INTERVAL_NA 0
#define MAX_DATA_WAITING_BUCKET_SIZE 5
#define WALLY_DB_STATS_NAME_SIZE 256
#define POSTGRES_TIMESTAMP_STR_LENGTH 256


#define db_min(a,b) (((a)<(b))?(a):(b))
#define db_max(a,b) (((a)>(b))?(a):(b))

/*
 * Structure for returning current db connection/handler state. Note- no
 * locking is performed during this operation, so some things may seem
 * inconsistent.
 */
struct wally_db_connection_state {  /* _ARGO: object_definition */
    const char *name;               /* _ARGO: string */
    const char *state;              /* _ARGO: string */
};

enum wp_type {
    db_postgres,
    db_sqlt
};

struct wally_table_poll_time {
    char table_name[MAX_TABLE_NAME_LEN];
    int pool_time_s;
};


enum wp_table_state {
    table_ready = 1,
    table_hard_error,    //2
    table_resynchronize, //3
    table_read_table,    //4
    table_create_table,  //5
    table_read_columns,  //6
    table_read_indexes,  //7
    table_add_columns,   //8
    table_add_indexes,   //9
    table_read_highest_sequence_begin,  //10
    table_read_highest_sequence_lock,   //11
    table_read_highest_sequence_select, //12
    table_read_highest_sequence_commit, //13
    table_add_notification,    //14
    table_doesnt_exist,         //15
    table_read_highest_sequence_gvr, //16
    table_read_highest_sequence_gvr_query_pending, // 17
    table_read_get_last_transcation_gvr, // 18
    table_read_get_last_transcation_gvr_query_pending, // 19
    table_read_lookup_table, //20
    table_create_lookup_table //21

};

/*
 * Wally table states
 */
enum wp_db_table_poll_state {
    wp_db_table_poll_idle = 0,
    wp_db_table_poll_check,
    wp_db_table_poll_begin,
    wp_db_table_poll_lock,
    wp_db_table_poll_select,
    wp_db_table_poll_commit,
    wp_db_table_poll_rollback,
    wp_db_table_gvr_poll_check,
    wp_db_table_gvr_pending_txn_check,
    wp_db_table_gvr_poll_select,
    wp_db_table_gvr_poll_pending_txn
};

/* ET-22520, feature: Trim table */
enum wp_cleanup_state {
    cleanup_idle = 0,
    cleanup_start,
    cleanup_in_process
};

enum wp_table_bootup_stats {
    wp_bootup_table_num_queries = 1,
    wp_bootup_table_batches_completed,
    wp_bootup_table_seqlock_start,
    wp_bootup_table_seqlock_end,
    wp_bootup_table_readlock_start,
    wp_bootup_table_readlock_end,
    wp_bootup_table_query_start,
    wp_bootup_table_query_end,
    wp_bootup_table_rowparse_start,
    wp_bootup_table_rowparse_end,
    wp_bootup_table_rowstore_time,
    wp_bootup_table_rowcb_time,
    wp_bootup_table_total_start,
    wp_bootup_table_total_end,
    wp_bootup_table_nolock_start,
    wp_bootup_table_txn_pending_start,
    wp_bootup_table_excl_lock_start,
    wp_bootup_table_nolock_end,
    wp_bootup_excl_lock_counter,
    wp_bootup_txn_pending_counter
};


/*
 * Connection state- whether the connection is simply reading rows,
 * whether it is doing a table sequence update (still reading rows,
 * but a little more special), or whether the connection is doing
 * table resynchronization.
 */
enum wp_connection_state {
    conn_idle = 1,
    conn_table_write,

    conn_table_read_begin,
    conn_table_read_lock,
    conn_table_read_select,
    conn_table_read_commit,

    conn_table_poll_check,
    conn_table_poll_begin,
    conn_table_poll_lock,
    conn_table_poll_select,
    conn_table_poll_commit,
    conn_table_poll_rollback,

    conn_table_delete_sequence,
    conn_table_resynchronize,
    conn_table_create_db,

    conn_table_cleanup_update,
    conn_table_minseq_update,
    conn_table_read_select_nolock,  /* Read DB rows without exclusive lock */
    conn_table_gvr_poll_check,
    conn_table_gvr_poll_select,
    conn_table_gvr_pending_txn_check,
    conn_table_batch_write,
    conn_failed
};

extern const char *wp_connection_state_names[];
extern const char *wp_table_state_names[];
extern int db_read_disabled;
extern struct wally_db_connection_state c_state[WALLY_DB_MAX_CONNS];
extern struct wp_connection *c_conns[WALLY_DB_MAX_CONNS];
extern int c_state_count;

void set_et_26295_test_size();

/*
 * meta data for a table column
 */
struct column_meta {
    char name[MAX_COLUMN_META_SIZE];
    char data_type[MAX_COLUMN_META_SIZE];
    char udt_name[MAX_COLUMN_META_SIZE];   /* not apply to SQLite */
};

struct write_object_info {
    struct argo_object **objects;
    int *qindexes;
    int cnt;
};

/*
 * Column indexes replicate a lot of data seen within wally itself,
 * but are not NEARLY as complex, here- though the interest indexes
 * are very much replicated. (Eventually they might be shared, but for
 * now they are left separate for simplicity)
 */
struct wp_index {
    /* Name of the column: */
    char *column_name;

    /* Indication whether or not this index is a null column */
    int is_null;

    /* Indication whether or not this null column is registered. */
    int is_null_registered;

    int64_t null_request_id;

    /* Index (within wp_table) */
    int index;

    /* Hash table of the requested data. */
    struct argo_hash_table *interests;

    /* Index within argo for this column */
    int argo_index;
    enum argo_field_data_type argo_field_type;
};

struct cleanup_table_data {
    int64_t sequence;
    int64_t deleted;
    int64_t key_int;
};

struct pg_table_bootup_stats {
    int64_t bootup_table_num_queries;
    int64_t bootup_table_batches_completed;
    int64_t bootup_table_seqlock_start_us;
    int64_t bootup_table_seqlock_time_us;
    int64_t bootup_table_readlock_start_us;
    int64_t bootup_table_readlock_time_us;
    int64_t bootup_table_query_start_us;
    int64_t bootup_table_query_time_us;
    int64_t bootup_table_rowparse_start_us;
    int64_t bootup_table_rowparse_time_us;
    int64_t bootup_table_rowstore_time_us;
    int64_t bootup_table_rowcb_time_us;
    int64_t bootup_table_total_start_us;
    int64_t bootup_table_total_time_us;
    int64_t bootup_table_nolock_start_us;
    int64_t bootup_table_nolock_time_us;
    int64_t bootup_table_txn_pending_start_us;
    int64_t bootup_table_txn_pending_time_us;
    int64_t bootup_table_excl_lock_start_us;
    int64_t bootup_table_excl_lock_time_us;
    int64_t bootup_table_excl_lock_cont;
    int64_t bootup_table_txn_pending_cont;
};

/* Table stats measurements and update */
#define WP_DB_STATS_STOP(DB, OP_TIME, FIELD)                                                                    \
{                                                                                                               \
    DB->db_oper_stats.db_##FIELD##_time_us+=OP_TIME;                                                            \
    DB->db_oper_stats.db_##FIELD##_count++;                                                                     \
    if (OP_TIME > DB->db_oper_stats.db_##FIELD##_time_max_us)                                                   \
        DB->db_oper_stats.db_##FIELD##_time_max_us = OP_TIME;                                                   \
}
#define WP_DB_STATS_UPDATE(DB, STATS, FIELD)                                                                    \
{                                                                                                               \
    STATS->db_##FIELD##_count=DB->db_oper_stats.db_##FIELD##_count;                                             \
    STATS->db_##FIELD##_time_max_us = DB->db_oper_stats.db_##FIELD##_time_max_us;                               \
    if (DB->db_oper_stats.db_##FIELD##_count > 0)                                                               \
        STATS->db_##FIELD##_time_avg_us =                                                                           \
                (DB->db_oper_stats.db_##FIELD##_time_us / DB->db_oper_stats.db_##FIELD##_count);                \
    else  \
        STATS->db_##FIELD##_time_avg_us = 0;\
}
#define WP_DB_STATS_RESET(DB, FIELD)                                                                            \
{                                                                                                               \
    DB->db_oper_stats.db_##FIELD##_time_us = 0;                                                                 \
    DB->db_oper_stats.db_##FIELD##_count = 0;                                                                   \
    DB->db_oper_stats.db_##FIELD##_time_max_us = 0;                                                             \
}


/* DB stats measurement and update */
#define WP_DB_TABLE_STATS_START(TBL,FIELD)                                                                      \
{                                                                                                               \
      TBL->db_table_oper_stats.db_table_##FIELD##_time_start_us =  epoch_us();                                  \
}
#define WP_DB_TABLE_STATS_STOP(TBL, OP_TIME, FIELD)                                                             \
{                                                                                                               \
    TBL->db_table_oper_stats.db_table_##FIELD##_time_us+=OP_TIME;                                               \
    TBL->db_table_oper_stats.db_table_##FIELD##_count++;                                                        \
    if (OP_TIME >  TBL->db_table_oper_stats.db_table_##FIELD##_time_max_us)                                     \
        TBL->db_table_oper_stats.db_table_##FIELD##_time_max_us = OP_TIME;                                      \
}
#define WP_DB_TABLE_STATS_UPDATE(TBL, STATS, FIELD)                                                             \
{                                                                                                               \
    STATS->db_table_##FIELD##_count=TBL->db_table_oper_stats.db_table_##FIELD##_count;                          \
    STATS->db_table_##FIELD##_time_max_us = TBL->db_table_oper_stats.db_table_##FIELD##_time_max_us;            \
    if (TBL->db_table_oper_stats.db_table_##FIELD##_count > 0)                                                  \
        STATS->db_table_##FIELD##_time_avg_us =                                                                     \
            (TBL->db_table_oper_stats.db_table_##FIELD##_time_us / TBL->db_table_oper_stats.db_table_##FIELD##_count); \
    else \
        STATS->db_table_##FIELD##_time_avg_us = 0;\
}

/* Do not reset start time as it may cross-over publishing interval */
#define WP_DB_TABLE_STATS_RESET(TBL, FIELD)                                                                     \
{                                                                                                               \
    TBL->db_table_oper_stats.db_table_##FIELD##_time_us = 0;                                                    \
    TBL->db_table_oper_stats.db_table_##FIELD##_time_max_us = 0;                                                \
    TBL->db_table_oper_stats.db_table_##FIELD##_count = 0;                                                      \
}

#define WP_STATS_CONN_START(WP_CONN)   WP_CONN->db_op_start_time_us = epoch_us();
#define WP_STATS_CONN_STOP(WP_CONN, DB, TBL, FIELD)  \
if (WP_CONN->db_op_start_time_us != 0)     \
{ \
    int64_t oper_time = (epoch_us() - WP_CONN->db_op_start_time_us);    \
    WP_DB_STATS_STOP(DB, oper_time, FIELD)      \
    WP_DB_TABLE_STATS_STOP(TBL, oper_time, FIELD)   \
    WP_CONN->db_op_start_time_us = 0;               \
}\

struct wp_db_oper_stats {
    int64_t db_query_time_us;
    int64_t db_query_count;
    int64_t db_query_time_max_us;
    int64_t db_update_time_us;
    int64_t db_update_count;
    int64_t db_update_time_max_us;
    int64_t db_insert_time_us;
    int64_t db_insert_count;
    int64_t db_insert_time_max_us;
    int64_t db_delete_time_us;
    int64_t db_delete_count;
    int64_t db_delete_time_max_us;
    int64_t db_lock_time_us;
    int64_t db_lock_count;
    int64_t db_lock_time_max_us;
    int64_t db_ddl_time_us;
    int64_t db_ddl_count;
    int64_t db_ddl_time_max_us;
    int64_t db_tcl_time_us;
    int64_t db_tcl_count;
    int64_t db_tcl_time_max_us;
};

struct wp_db_table_oper_stats {
    int64_t db_table_query_time_us;
    int64_t db_table_query_count;
    int64_t db_table_query_time_max_us;
    int64_t db_table_update_time_us;
    int64_t db_table_update_count;
    int64_t db_table_update_time_max_us;
    int64_t db_table_insert_time_us;
    int64_t db_table_insert_count;
    int64_t db_table_insert_time_max_us;
    int64_t db_table_delete_time_us;
    int64_t db_table_delete_count;
    int64_t db_table_delete_time_max_us;
    int64_t db_table_lock_time_us;
    int64_t db_table_lock_count;
    int64_t db_table_lock_time_max_us;
    int64_t db_table_ddl_time_us;
    int64_t db_table_ddl_count;
    int64_t db_table_ddl_time_max_us;
    int64_t db_table_tcl_time_us;
    int64_t db_table_tcl_count;
    int64_t db_table_tcl_time_max_us;
    int64_t schema_mismatch_count;
    int64_t transaction_failure_count;
    int64_t object_wrtie_failure_count;
};


struct wp_table {
    struct wp_db *db;

    char *db_table_name;
    char *argo_object_name;

    /* Name of the sequence column. */
    const char *sequence_column_name;
    /* Name of the column indicating deleted row. NULL if doesn't exist. */
    const char *deleted_column_name;
    int64_t current_max_sequence;
    bool first_row_sync_completed; /* Will be set to true, if first time insert is
                                      happened to local postgres. This is required to
                                      identify the schema_mismatch.
                                      Schema_mistmatch will be reported only during the runtime,
                                      and bootup column mismatch(first time insert) will be omitted.
                                      Bootup column mismatches are expected as always there will be
                                      some extra columns in the RDS than the ARGO definition.
                                      Schema_mistmatch will be reported only when existing
                                      in-memory ARGO/local_db columns are different from the
                                      received one. This flag will be set to true if the
                                      row insert is completed for table.
                                    */

    /* The time at which the last read completed. */
    int64_t last_read_complete_us;
    int64_t now_us;

    /* table cleanup */
    struct cleanup_table_data *cleanup_buf;
    int cleanup_buf_sz;
    int cleanup_count;
    int cleanup_index;
    int64_t cleanup_process_start_us;
    enum wp_cleanup_state cleanup_state;

    /* The last time we successfully got a polling lock... */
    int64_t xpoll_first_detected_data_to_read_us; /* The time at which we detected (without locking) data to read */
    int64_t xpoll_last_trylocked_read_us;         /* Last time we sent trylock */
    int64_t xpoll_last_read_us;                   /* Last time we did any locked read (lock or trylock) */
    int64_t xpoll_last_unlocked_read_us;          /* Last time we did a non-locked poll (check for content) */
    int64_t xpoll_last_trylock_failed_us;         /* last time we failed to get trylock */
    /* The number of times we have failed getting a lock in a row... */
    int64_t xpoll_repeated_trylock_fail_count; /* Number of times in a row we did trylock without success */
    /* The number of times that we have failed or succeeded to lock when trying */
    int64_t xpoll_check;                   /* Number of times we did check */
    int64_t xpoll_check_rate;                   /* Number of times in stats publishing interval we did check */
    int64_t xpoll_check_new_data;          /* Number of times we did check with new data detected */
    int64_t xpoll_trylock;                /* Number of times we did trylocked poll (regardless of success) */
    int64_t xpoll_trylock_fails;          /* Number of times trylock failed */
    int64_t xpoll_poll_interval_us;          /* Poll interval in us */

    /* Orthogonal state to other reading- this is an indication that
     * the table is currently being read to max-sequence from the
     * database. (Only exists to keep multiple DB connections from
     * trying to perform sequence synchronization simultaneously */
    int sequence_is_being_read;

    /* Indication whether or not this table has been marked as needing
     * resynchronization- i.e. argo and postgres fields/columns are
     * out of sync. */
    int needs_to_be_resynchronized;

    /* The minumum valid sequence that should exist in this table. */
    int64_t min_valid_sequence_set;

    /* min valid sequence updated in zpath_table last time */
    int64_t min_squence_last;
    int64_t min_squence_last_rollback;

    /* last time when min_sequence was updated in zpath_table */
    int64_t min_sequence_last_us;

    /* should min sequence e automatically updated on zpath_table ? */
    int min_seq_auto_update;

    /* The minimum valid sequence that has been enacted in this
     * table. */
    int64_t min_valid_sequence_performed;

	/* To be set only for creating lookup table */
	bool lookup_table_exists;

	/*
	 * if table is fully_loaded or not
	 */
	int fully_loaded;
    /*
     * How many rows in the table
     */
    int64_t cur_row_count; /* current row count */
    int64_t max_row_count; /* maximun row count since the wally client start up */
    int64_t del_row_count; /* soft deleted row count */

    int stats_registerd;   /* if registered for stats logging */

    /* Indicates that sequence deletion is in progress... */
    int sequence_deletion_in_progress;

    struct argo_hash_table *index_columns;
    struct wp_index *indexes[WALLY_MAX_INDEXED_COLUMNS];
    int index_count;

    /* The number of rows we last heard about from argo. This can
     * change over time- but will only grow. We track it here in order
     * to tell if we might need to synchronize DB columns with argo
     * fields. */
    int argo_field_count;

    /* If non null, the following contains allocated data */
    char *scratch;

    /* Argo description of structure representing rows in this
     * column */
    struct argo_structure_description *description;

    enum wp_table_state state;

    struct pg_table_bootup_stats db_pg_table_bootup_stats;

    struct argo_log_registered_structure *wally_db_table_bootup_stats_structure;
    struct argo_log_registered_structure *wally_db_table_stats_structure;

    struct wally_postgres_table_bootup_stats wp_pg_table_bootup_stats;
    struct wally_postgres_table_stats wp_pg_table_stats;

    int64_t data_waiting_time_buckets[MAX_DATA_WAITING_BUCKET_SIZE];

    enum wp_db_table_poll_state db_state;

    struct wp_db_table_oper_stats db_table_oper_stats;
    char latest_transaction_start_time[POSTGRES_TIMESTAMP_STR_LENGTH];
    int64_t last_gvr_request_time_us;

    int64_t poll_max_sequence;
    int64_t poll_gap_sequence;

    int64_t last_pending_txn_gvr_chk_time; /* Last transaction check time for interval calcualtion */
    int64_t db_table_pending_txn_chk_count; /* Pending transaction check count */
    int64_t db_poll_skip_excl_lock; /* Number of times polling skipped due to exclusive db table lock */
    int64_t db_table_pending_txn_chk_start_time; /* Pending transaction check start time */
    int64_t db_table_pending_txn_waiting_time;  /* Total waiting time for pending transaction to complete */
    int64_t db_table_max_waiting_time; /*Max waiting in the publishing period */
    int64_t db_table_gvr_poll_time;     /* Poll statement execution time */
    int64_t last_successful_read_us;  /*last successful row read from db */

    int64_t db_poll_table_parse_time;
    int64_t db_poll_table_read_time;
    int64_t db_poll_num_gap_txns;
    int64_t db_poll_num_non_gap_txns;
    int64_t db_poll_table_total_time;
    int64_t db_poll_table_processing_time;

	int64_t db_lookup_table_write_retry; /* No of lookup table write retry per sec */
	int64_t db_lookup_table_total_operations_successful; /* Total no of db operations performed on lookup table per sec */
	int64_t db_lookup_table_total_operations_failed; /* Total no of db operations failed on lookup table per sec */
	int64_t db_lookup_table_start_time_us;    /*start time of lookup table operations */
	int64_t db_lookup_table_total_time_us;    /*Time spent in the db operations for lookup table per sec */
    int stop_polling_table;
    int64_t poll_lock_count; /* Non-zero at polling if table is locked */


    LIST_ENTRY(wp_table) all_tables;
};


/* Per-DB connection state. */

TAILQ_HEAD(wp_connection_head, wp_connection);
LIST_HEAD(wp_table_head, wp_table);

struct wp_connection {
    /* The wp_db to which we refer. */
    struct wp_db *wp_db;

    /* A name for this connection. */
    const char *name;

    /* DB connection or handler */
    void *db_conn;
    struct event *ev_conn;

    int my_index;

    /* Request management state for when this connection is being
     * used */
    enum wp_connection_state state;
    /* The cookie-sequence number handed to us by wally- to be handed
     * back to them when the reponse has been completed. */
    int64_t wally_request_id;
    int row_count;

    /* The current request sequence. */
    int64_t row_request_sequence;
    /* The current request table. */
    struct wp_table *table;
    /* The current request column index. */
    int request_wp_index_index;
    /* The current request key. */
    char *request_key;

    /* This object only exists (is non-null) if postgres was only able
     * to do 1/2 of the the two required writes for each row
     * update. The existence of this object indicates that the second
     * write must be performed. This value should only be set when the
     * connection is writing. */
    struct wally_local_db_write_queue_object *write_object;
    int write_q_index;  // index into wally_origin.write_queue

    /* This is the bulk objects to write, only apply to SQLite */
    struct write_object_info object_info;

    /* Debug_str is updated to carry debugging state through the state
     * machine... Generally used as-needed, and not guaranteed to not
     * be overwritten */
    char debug_str[256];

    /* If set, then we have more data to read (i.e. we read our max
     * number of rows, but there were more to read) */
    unsigned read_more:1;

    /* DB operation start time */
    int64_t db_op_start_time_us;

    void *cookie; /* for batch information */

    TAILQ_ENTRY(wp_connection) free_connections;
};

#define WALLY_PSQL_PARAM_MAX_INDEX   11
#define WALLY_PSQL_PARAM_BUF         256
struct wp_db {
    /* Name of the db. Just the name of the postgres DB. Only really
     * there for debugging. */
    char *name;

    /* Database type, posgres or SQLite */
    enum wp_type db_type;

    /*
     * If set, then this postgres instance is an endpoint, which
     * should treat soft-deletes as hard-deletes.
     */
    int is_endpoint;

    /* Thread watchdog state */
    struct zthread_info *zthread;
    struct zevent_base *zevent;

    /* Cookie for all our callbacks into wally. */
    void *db_to_wally_cookie;

    /* tables inserted with objects for each timer event */
    struct argo_hash_table *tables_inserted;

    /* The tables in the database, indexed by argo name. */
    struct argo_hash_table *tables_by_argo_name;
    /* List of all the tables. */
    struct wp_table_head all_tables;
    /* Count of tables. */
    int all_tables_count;

    /* DB file size */
    size_t cur_db_size; /* current size */
    size_t max_db_size; /* maximun size since startup */

    /* link to the next table to scan */
    struct wp_table *next_table;
    /* The db connections. */
    struct wp_connection *connections;
    int connections_count;
    int failed_connections_count;
    uint32_t recovery_try_count;
    char *wally_postgres_param_names[WALLY_PSQL_PARAM_MAX_INDEX];
    char *wally_postgres_param_values[WALLY_PSQL_PARAM_MAX_INDEX];

    /* List of currently unused and ready DB connections */
    struct wp_connection_head free_connections;

    /* The thread for processing all this stuff. (libevent based) */
    pthread_t thread;

    /* Event base for the thread. (Note: Sort of silly to multithread
     * db handler when wally updates end up basically single threaded
     * on their own. */
    struct event_base *ev_base;

    /* Timer event for database maintenance. */
    struct event *ev_timer;

    /* Whether or not the wally accessing this DB is currently
     * blocking.
     *
     * Note that unblocked DB may immediately re-block on the very
     * next attempt to have the database do anything. */
    int is_blocking;

    /* Whether or not we're allowed to write rows to this DB. */
    int is_row_writable;

    /* Whether or not we're allowed to alter/create this DB. */
    int is_alterable;

    /* Whether or not this is a true origin DB. Handles writes in a
     * special manner. */
    int is_true_origin;

    /* The interval at which we poll this database. If zero, we don't
     * poll this database for updates. (Common to never poll slave
     * databases) */
    int64_t polling_interval_us;
    bool polling_failure; /* Polling failed*/

    /* Time when last time wally polled */
    int64_t last_poll_time_us;

    /* To enable/disable poll timeout restart functionality */
    int poll_rate_check;

    /* Duration after that wally restart if not polling */
    int64_t poll_rate_timeout_us;

    /* DB conn and server status poll */
    int64_t wp_get_conn_status_poll;
    int64_t wp_get_ping_status_poll;

    /* R/W lock for access to this DB. Could probabl be a mutex, but
     * leaving as a RW lock for now. */
    zpath_rwlock_t lock;

    /* Name of the schema to look for tables.
     * Only used for wallyd to remote db connections.
     * Only used with postgres connection, not by sqlite. */
    char *schema_name;

    struct zevent_base *db_base;

    struct wp_db_oper_stats db_oper_stats;

};



struct zthread_info;
extern void *wally_db_thread(struct zthread_info *zthread, void *arg);

int wp_add_index_column(struct wp_table *t, const char *column_name);
struct wp_table *wp_just_create_table(struct wp_db *wp_db, const char *db_table_name, const char *argo_object_name);
int wp_create_table(struct wp_db *wp_db, struct wp_connection *wp_conn, const char *db_table_name, const char *argo_object_name);

/*
 * The standard callins from wally into this origin DB.
 */
int wally_db_register_for_index(void *callout_cookie,
                                int64_t request_id,
                                int64_t request_sequence,
                                char *table_name,
                                char *column_name,
                                char *key);

int wally_db_deregister_for_index(void *callout_cookie,
                                  char *table_name,
                                  char *column_name,
                                  char *key);

int wally_db_add_table(void *callout_cookie,
                       const char *db_table_name,
                       const char *argo_object_name);

int wally_db_set_cookie(void *callout_cookie, void *callback_cookie);

enum wally_origin_status wally_db_get_status(void *callout_cookie);

void wally_db_get_connection_state(struct wally_db_connection_state **state, size_t *state_count);

int wally_db_set_min_sequence(void *callout_cookie,
                                    const char *table_name,
                                    int64_t min_valid_sequence);

int wally_db_dump_state(void *callout_cookie, char *s, char *e);

int wally_db_set_read_disabled(bool disabled);

void wally_timer_event_callback(evutil_socket_t sock, short flags, void *cookie);

void wally_db_log_row_counts(struct wp_table *t);
void wally_db_set_row_counts(struct wp_table *t, int64_t count, int do_log);
void wally_db_set_deleted_row_counts(struct wp_table *t, int64_t count);
void wally_db_set_size(struct wp_db *wp_db);

int fill_wally_db_stats(void *cookie, int counter, void *structure_data);
int fill_wally_db_table_stats(void *cookie, int counter, void *structure_data);
int fill_wp_db_table_stats(void *cookie, int counter, void *structure_data);
int fill_wp_db_stats(void *cookie, int counter, void *structure_data);

int wally_db_table_log_register(struct wp_table *t);
void wally_db_insert_free_conn (struct wp_db *wp_db, struct wp_connection *wp_conn);

void reset_cleanup(struct wp_table *t, int64_t min_seq);

void wp_bootup_update_table_stats(struct wp_table *table, enum wp_table_bootup_stats bootup_stats);
void wp_bootup_update_table_row_stats(struct wp_table *table, enum wp_table_bootup_stats bootup_stats, int64_t duration);
int wally_db_update_table_poll_time(char** pool_table_info, int count);
void wally_db_enable_poll_rate_check(struct wp_db *wp_db, int poll_rate_timeout_min);
int64_t wally_db_get_table_poll_time(char * table_name);
void wally_postgres_update_data_waiting_stats(struct wp_table *table, int64_t duration_us);
void wp_release_write_object(struct wally_local_db_write_queue_object *write_object, bool only_row_release);
/* Function : wally_db_cmd_execute
 * Arg      : callout_cookie - wp_db object
 *            table_name - table name
 *            cmd_id    - command id
 *            param1 - parameter for the command
 * Ret      :  WALLY_RESULT_NO_ERROR if success.
 * Desc     : This function executes command initated from wally layer.
 *                  SET_DB_TABLE_POLL - stops polling for the table
 */
int wally_db_cmd_execute(void *callout_cookie, const char* table_name, int cmd_id, void *param1);

#endif /* __WALLY_DB_H__ */
