/*
 * wally_fohh.c Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Use wally as an origin database from both client and server
 * perspectives.
 */
#define _GNU_SOURCE

#include <stdio.h>
#include <sys/queue.h>
#include <event2/event.h>
#include <pthread.h>
#include <string.h>
#include "argo/argo_hash.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_fohh.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_table_queue.h"
#include "wally/wally_oper.h"
#include "zpath_misc/zpath_version.h"
#include "fohh/fohh_private.h"

int32_t dont_send_version = 0;

struct wally_fohh_client {
    struct wally *wally;

    char *domain_name;
    uint16_t tcp_port_ne;

    /* This system will hardly need locks at all... But here they are. */
    pthread_mutex_t lock_outbound;
    pthread_mutex_t lock_inbound;

    /* Client connection state (For us connecting to someone else) */
    struct fohh_connection *client;
    void  *fohh_to_wally_cookie;

    /* Set of argo descriptions for row registration and deregistration */
    struct argo_structure_description *register_row_string;
    struct argo_structure_description *deregister_row_string;
    struct argo_structure_description *register_row_integer;
    struct argo_structure_description *deregister_row_integer;
    struct argo_structure_description *request_result;
    struct argo_structure_description *version;
    struct argo_structure_description *version_ack;
    struct argo_structure_description *recovery_begin_request;
    struct argo_structure_description *recovery_end_request;
    struct argo_structure_description *recovery_complete_response;

    /* Optional connection callback. */
    fohh_connection_callback_f *connection_callback;
};

static int server_recovery_end_callback(void *cookie, void *structure_cookie, struct argo_object *object);
static int server_recovery_begin_callback(void *cookie, void *structure_cookie, struct argo_object *object);
#if 0
static inline void wfc_lock_outbound(struct wally_fohh_client *wfc)
{
    pthread_mutex_lock(&(wfc->lock_outbound));
}
static inline void wfc_unlock_outbound(struct wally_fohh_client *wfc)
{
    pthread_mutex_unlock(&(wfc->lock_outbound));
}


static inline void wfc_lock_inbound(struct wally_fohh_client *wfc)
{
    pthread_mutex_lock(&(wfc->lock_inbound));
}
static inline void wfc_unlock_inbound(struct wally_fohh_client *wfc)
{
    pthread_mutex_unlock(&(wfc->lock_inbound));
}
#endif // 0

/*
 * Callback from argo for rows received from an FOHH. i.e. from our
 * client connection to a remote server.
 *
 * Since this is coming from FOHH, the object exists on the stack. We
 * need to copy it out.
 */
static int server_row_callback(void *cookie, void *structure_cookie, struct argo_object *row)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) structure_cookie;
    struct argo_object *copy;
    int64_t request_id;
    int res;

    if (wally_debug & WALLY_DEBUG_FOHH_CLIENT_ROW_BIT) {
        char object_str[1000];

        if (argo_object_dump(row, object_str, sizeof(object_str), NULL, 1)) {
            object_str[0] = 0;
        }

        WALLY_DEBUG_FOHH_CLIENT_ROW("%s: Row from server:%s", wfc->wally->name, object_str);
    }

    /* We received a row... Send it to wally, 'cause wally should be
     * expecting it. This requires that we figure out which of wally's
     * tables wanted this row... It also requires that we copy the
     * row, since wally is likely to want to hold on to it. */

    /* We also try to extract the request id from within the
     * object. The request id was embedded into the object for
     * backwards compatibility reasons. */

	if(IS_WALLY_LAYER_ENABLED) {
		res = wally_table_queue_enqueue_fohh_xfer_row(wfc->fohh_to_wally_cookie,
													  row);
	} else {
		copy = argo_object_copy(row);
		request_id = argo_object_read_request_id(copy);
		res = wally_xfer_row(wfc->fohh_to_wally_cookie, argo_object_get_type(copy), copy, request_id, NULL, NULL);
		argo_object_release(copy);
	}

    return res;
}

static int server_result_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) structure_cookie;
    struct wally_fohh_request_result *obj = (struct wally_fohh_request_result *) object->base_structure_data;
    int res;

	WALLY_DEBUG_RESULT("%s: Result callback, ID = %ld, status = %ld table_name = %s",
					   wfc->wally->name,
					   (long) obj->request_id,
					   (long) obj->row_count,
					   obj->table_name);

    if ( !obj->table_name) {
        WALLY_LOG(AL_NOTICE, "Table name is NULL in FOHH Xfer Response recevied from remote wally(origin/global)");
		WALLY_LOG(AL_NOTICE, "Please upgrade wally in %s(%s) to %s version or higher ",
                                                            wfc->client->remote_address_name,
                                                            wfc->client->remote_address_str,
                                                            ZPATH_VERSION);
        wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);

    }

	if(IS_WALLY_LAYER_ENABLED) {
		res = wally_table_queue_enqueue_fohh_xfer_response(wfc->fohh_to_wally_cookie,
			   											   obj->request_id,
														   obj->row_count,
														   obj->table_exists,
														   obj->table_name);
	} else {
			res = wally_xfer_response(wfc->fohh_to_wally_cookie, obj->request_id, obj->row_count, obj->table_exists, obj->table_name);
	}

    return res;
}

static int server_version_ack_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) structure_cookie;
    struct wally_fohh_version_ack *ver_ack = (struct wally_fohh_version_ack *)object->base_structure_data;

    WALLY_LOG(AL_DEBUG, "wally_fohh: received version_ack from server: error = %s", ver_ack->error ? : "NONE");

    if (ver_ack->error) {
        WALLY_LOG(AL_WARNING, "wally_fohh: Client running incompatible version. version_ack from server : error = %s",
                  ver_ack->error);
        fohh_connection_disable_async(wfc->client, fohh_connection_incarnation(wfc->client), CLIENT_VERSION_TOO_OLD);
    }

    return WALLY_RESULT_NO_ERROR;
}

/*
 * Generic connection callback is used for client connections and for
 * accepted server connections. It is also called back for failed
 * server connections.
 */
static int generic_connection_callback(struct fohh_connection *connection,
                                       enum fohh_connection_state state,
                                       void *cookie)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) cookie;
    size_t i;
    struct argo_state *argo;

    WALLY_DEBUG_REGISTRATION("Connection callback received from server@%s:%d. New state = %s",
                             wfc->domain_name,
                             ntohs(wfc->tcp_port_ne),
                             fohh_connection_state_strings[state]);

    switch(state) {
    case fohh_connection_disconnected:
    case fohh_connection_unresolved:
    case fohh_connection_unreachable:
    case fohh_connection_backoff:
    case fohh_connection_connecting:
    case fohh_connection_deleted:
    default:
        if (connection->state == fohh_connection_connected) {
             wally_fohh_client_disconnect_stat(wfc->wally);
             if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
                 /* Post the event only if
                  * 1. Wally is at least once ACTIVE, to complete all the inits
                  * 2. Oper FSM is initialize. */
                 wally_oper_event_post(E_ORIGIN_DISCONNECT, SE_FOHH_DISCONNECT, NULL);
             }
        }
        wally_xfer_status_update(wfc->fohh_to_wally_cookie, wally_origin_status_not_ready);
        break;
    case fohh_connection_connected:
        /* Need to register our structure callbacks... */
        /* For all tables, register their argo rows with our callback. */
        argo = fohh_argo_get_tx(wfc->client);
        argo_register_structure(argo, wfc->register_row_string, NULL, NULL);
        argo_register_structure(argo, wfc->deregister_row_string, NULL, NULL);
        argo_register_structure(argo, wfc->register_row_integer, NULL, NULL);
        argo_register_structure(argo, wfc->deregister_row_integer, NULL, NULL);

        argo = fohh_argo_get_rx(wfc->client);
        argo_register_structure(argo, wfc->request_result, server_result_callback, wfc);
        argo_register_structure(argo, wfc->version_ack, server_version_ack_callback, wfc);
        argo_register_structure(argo, wfc->recovery_begin_request, server_recovery_begin_callback, wfc);
        argo_register_structure(argo, wfc->recovery_end_request, server_recovery_end_callback, wfc);
        argo_register_structure(argo, wfc->recovery_complete_response, NULL, NULL);

        ZPATH_RWLOCK_RDLOCK(&(wfc->wally->lock), __FILE__, __LINE__);
        for (i = 0; i < wfc->wally->tables_count; i++) {
            argo_register_structure(argo, wfc->wally->tables[i]->argo_description, server_row_callback, wfc);
        }
        ZPATH_RWLOCK_UNLOCK(&(wfc->wally->lock), __FILE__, __LINE__);
        wally_xfer_status_update(wfc->fohh_to_wally_cookie, wally_origin_status_ready);

        /*  Client version message is sent to check version compatibility */

        if (!dont_send_version) {
            /* Send out the version message */
            struct wally_fohh_version req = {0};

            WALLY_DEBUG_REGISTRATION("Send out wally version to server");
            req.version_major = ZPATH_VERSION_MAJOR;
            req.version_minor = ZPATH_VERSION_MINOR;
            req.version_patch = ZPATH_VERSION_PATCH;
            req.ver_str = ZPATH_VERSION;
            req.client_capabilities |= WALLY_CLIENT_TYPE_ITASCA;
            fohh_argo_serialize(connection, wfc->version, &req, 0, fohh_queue_element_type_control);
        }

        wally_fohh_client_connect_stat(wfc->wally);
        if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
            /* Post the event only if
             * 1. Wally is at least once ACTIVE, to complete all the inits
             * 2. Oper FSM is initialized. */
            wally_oper_event_post(E_ORIGIN_CONNECT, SE_FOHH_ACTIVE, NULL);
        }
        break;
    }

    if (wfc->connection_callback) {
        return (wfc->connection_callback)(connection, state, wfc->fohh_to_wally_cookie);
    }

    return FOHH_RESULT_NO_ERROR;
}

static int generic_unblock_callback(struct fohh_connection *connection,
                                    enum fohh_queue_element_type element_type,
                                    void *cookie)
{
    //WALLY_LOG(AL_DEBUG, "Unblock");
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) cookie;
    wally_xfer_resume_xmit(wfc->fohh_to_wally_cookie);
    return FOHH_RESULT_NO_ERROR;
}

static int wally_fohh_register_argo_structures(struct wally_fohh_client *wfc)
{
    wally_fohh_register_structures();

    wfc->register_row_string = argo_get_structure_description("wally_fohh_register_row_request_string");
    wfc->deregister_row_string = argo_get_structure_description("wally_fohh_deregister_row_request_string");
    wfc->register_row_integer = argo_get_structure_description("wally_fohh_register_row_request_integer");
    wfc->deregister_row_integer = argo_get_structure_description("wally_fohh_deregister_row_request_integer");
    wfc->request_result = argo_get_structure_description("wally_fohh_request_result");
    wfc->version = argo_get_structure_description("wally_fohh_version");
    wfc->version_ack = argo_get_structure_description("wally_fohh_version_ack");
    wfc->recovery_begin_request = argo_get_structure_description("wally_fohh_recovery_begin_request");
    wfc->recovery_end_request = argo_get_structure_description("wally_fohh_recovery_end_request");
    wfc->recovery_complete_response = argo_get_structure_description("wally_fohh_recovery_complete_response");

    if (!wfc->register_row_string) return WALLY_RESULT_ERR;
    if (!wfc->deregister_row_string) return WALLY_RESULT_ERR;
    if (!wfc->register_row_integer) return WALLY_RESULT_ERR;
    if (!wfc->deregister_row_integer) return WALLY_RESULT_ERR;
    if (!wfc->request_result) return WALLY_RESULT_ERR;
    if (!wfc->version) return WALLY_RESULT_ERR;
    if (!wfc->version_ack) return WALLY_RESULT_ERR;
    if (!wfc->recovery_begin_request) return WALLY_RESULT_ERR;
    if (!wfc->recovery_end_request) return WALLY_RESULT_ERR;
    if (!wfc->recovery_complete_response) return WALLY_RESULT_ERR;

    return WALLY_RESULT_NO_ERROR;
}


void wally_fohh_client_timer_event_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct wally_fohh_client *wfc = cookie;
    struct wally_local_db_write_queue_object *object;
    int queue_index;
    int res;

    while ((object = wally_origin_dequeue_write_row(wfc->fohh_to_wally_cookie, &queue_index))) {
        if (fohh_get_state(wfc->client) == fohh_connection_connected) {
            /*
             * We decided to have it as data because these events keep get transmitted often.
             */
            res = fohh_argo_serialize_object(wfc->client, object->data.row_to_write, 0, fohh_queue_element_type_data);
            if (res) {
                if (res != FOHH_RESULT_WOULD_BLOCK) {
                    WALLY_LOG(AL_CRITICAL, "Could not serialize write object");
                }
                wally_origin_enqueue_write_row(wfc->fohh_to_wally_cookie, queue_index, object->data.row_to_write, 1);
                argo_object_release(object->data.row_to_write);
				WALLY_FREE(object);
                break;
            }
        } else {
            wally_origin_enqueue_write_row(wfc->fohh_to_wally_cookie, queue_index, object->data.row_to_write, 1);
            argo_object_release(object->data.row_to_write);
			WALLY_FREE(object);
            break;
        }
        argo_object_release(object->data.row_to_write);
		WALLY_FREE(object);
    }
}

struct wally_origin *wally_fohh_client_get_slave_origin(struct wally_fohh_client *wfc)
{
    struct wally_origin *ret = NULL;

    if (wfc == NULL) {
        return ret;
    }

    for (int i = 0; i < wfc->wally->origins_count; i++) {
        if (strncmp(wfc->wally->origins[i]->name, "sqlt:", 5) == 0) {
            ret = wfc->wally->origins[i];
            break;
        }
    }

    return ret;
}

struct wally* wally_fohh_client_get_wally(struct fohh_connection *f_conn)
{
    struct wally *w = NULL;

    struct wally_fohh_client *wfc = (struct wally_fohh_client *)f_conn->cookie;
    if (wfc) {
        w = wfc->wally;
    }
    return w;
}

struct wally_fohh_client *wally_fohh_client_create_using_ctx(struct wally *wally,
                                                             void *fohh_to_wally_cookie,
                                                             char *domain_name,
                                                             char *SNI,
                                                             char *sni_suffix,
                                                             uint16_t tcp_port_ne,
                                                             fohh_connection_callback_f *callback,
                                                             SSL_CTX *ctx)
{
    struct wally_fohh_client *wfc;
    static pthread_mutexattr_t mutex_attributes;
    char *tmp_str;
    struct event *ev_timer;
    struct event_base *base;
    struct timeval tv;

    wfc = (struct wally_fohh_client *) WALLY_MALLOC(sizeof(*wfc));
    if (!wfc) return NULL;

    memset(wfc, 0, sizeof(*wfc));

    wfc->wally = wally;
    wfc->tcp_port_ne = tcp_port_ne;
    wfc->fohh_to_wally_cookie = fohh_to_wally_cookie;
    wfc->connection_callback = callback;

    pthread_mutexattr_init(&mutex_attributes);
    pthread_mutexattr_settype(&mutex_attributes, PTHREAD_MUTEX_RECURSIVE);
    pthread_mutex_init(&(wfc->lock_inbound), &mutex_attributes);
    pthread_mutex_init(&(wfc->lock_outbound), &mutex_attributes);

    if (wally_fohh_register_argo_structures(wfc)) {
        WALLY_FREE(wfc);
        return NULL;
    }

    tmp_str = WALLY_MALLOC(strlen(domain_name) + 1);
    if (!tmp_str) {
        WALLY_FREE(wfc);
        return NULL;
    }
    strcpy(tmp_str, domain_name);
    wfc->domain_name = tmp_str;

    wfc->client = fohh_client_create(FOHH_WORKER_WALLY,
                                     wfc->domain_name,
                                     argo_serialize_binary,
                                     fohh_connection_style_argo,
                                     0,
                                     wfc,
                                     generic_connection_callback,
                                     NULL,
                                     generic_unblock_callback,
                                     NULL,
                                     wfc->domain_name,
                                     SNI, // SNI
                                     sni_suffix,
                                     tcp_port_ne,
                                     ctx,
                                     1, /* Require SSL */
                                     (3*60)); /* 3m idle timeout */
    if (!wfc->client) {
        WALLY_FREE(wfc->domain_name);
        WALLY_FREE(wfc);
        return NULL;
    }
    fohh_set_sticky(wfc->client, 1);

    /* Add a timer for this client to read rows, in case there are
     * rows to read. (Doesn't happen all that often...) We put the
     * timer on the same thread as our FOHH thread, so that
     * serialization can be direct. */
    base = fohh_get_thread_event_base(fohh_connection_get_thread_id(wfc->client));
    ev_timer = event_new(base,
                         -1,                     /* No socket, so -1. */
                         EV_PERSIST,             /* Repeating event */
                         wally_fohh_client_timer_event_callback,   /* Callback function */
                         wfc);                   /* Callback cookie- our db state. */
    if (!ev_timer) {
        /* MEMORY LEAK */
        WALLY_LOG(AL_CRITICAL, "Initialization failed for client, wally %s", wally->name);
        return NULL;
    }

    /* Start the timer for this thread. This runs every 0.1s This
     * timer is related to the polling interval for database
     * changes as well, so if you want faster response time, you
     * can spin this down to a smaller query rate. */
    tv.tv_sec = 0;
    tv.tv_usec = 500000;
    if (event_add(ev_timer, &tv)) {
        /* MEMORY LEAK */
        WALLY_LOG(AL_CRITICAL, "Initialization failed for client, wally %s", wally->name);
        return NULL;
    }

    return wfc;
}

struct wally_fohh_client *wally_fohh_client_create(struct wally *wally,
                                                   void *fohh_to_wally_cookie,
                                                   char *domain_name,
                                                   char *SNI,
                                                   char *sni_suffix,
                                                   uint16_t tcp_port_ne,
                                                   fohh_connection_callback_f *callback)
{
    return wally_fohh_client_create_using_ctx(wally,
                                              fohh_to_wally_cookie,
                                              domain_name,
                                              SNI,
                                              sni_suffix,
                                              tcp_port_ne,
                                              callback,
                                              NULL);
}



int wally_fohh_register_for_index(void *callout_cookie,
                                  int64_t request_id,
                                  int64_t request_sequence,
                                  char *table_name,
                                  char *column_name,
                                  char *key)
{
    /* There is really no state to keep, here. */
    struct wally_fohh_client *wfc = (struct wally_fohh_client *)callout_cookie;
    struct wally_fohh_register_row_request_string req;
    int res;

    WALLY_DEBUG_REGISTRATION("Register ID = %ld for: %s : %s : %s",
                             (long) request_id,
                             table_name,
                             column_name ? column_name : "null",
                             key ? key : "null");

    req.request_id = request_id;
    req.sequence = request_sequence;
    req.table_name = table_name;
    req.column_name = column_name;
    req.key = key;

    res = fohh_argo_serialize(wfc->client, wfc->register_row_string, &req, 0, fohh_queue_element_type_control);
    if (res == WALLY_RESULT_NO_ERROR) {
        WALLY_DEBUG_REGISTRATION("%s: Register ID = %ld for: %s : %s : %s",
                                 wfc->domain_name,
                                 (long) request_id,
                                 table_name,
                                 column_name ? column_name : "null",
                                 key ? key : "null");
    }

    return res;
}

int wally_fohh_register_for_table(void *callout_cookie,
                                  int64_t request_id,
                                  const char *table_name,
                                  const char *column_name,
                                  const char *key)
{
    WALLY_LOG(AL_NOTICE, "notice");
    return WALLY_RESULT_NO_ERROR;
}

int wally_fohh_deregister_for_index(void *callout_cookie,
                                    char *table_name,
                                    char *column_name,
                                    char *key)
{
    /* There is really no state to keep, here. */
    struct wally_fohh_client *wfc = (struct wally_fohh_client *)callout_cookie;
    struct wally_fohh_deregister_row_request_string req;
    int res;

    WALLY_DEBUG_REGISTRATION("Deregister for: %s : %s : %s",
                             table_name,
                             column_name ? column_name : "null",
                             key ? key : "null");

    req.table_name = table_name;
    req.column_name = column_name;
    req.key = key;

    res = fohh_argo_serialize(wfc->client, wfc->deregister_row_string, &req, 0, fohh_queue_element_type_control);

    return res;
}


int wally_fohh_deregister_for_table(void *callout_cookie,
                                    const char *table_name,
                                    const char *column_name)
{
    WALLY_LOG(AL_NOTICE, "notice");
    return WALLY_RESULT_NO_ERROR;
}


int wally_fohh_set_cookie(void *callout_cookie,
                          void *callback_cookie)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) callout_cookie;
    struct wally_origin *origin = (struct wally_origin *) callback_cookie;
    origin->pauseable = 1;

    wfc->fohh_to_wally_cookie = callback_cookie;
    wally_xfer_status_update(wfc->fohh_to_wally_cookie,
                             wally_fohh_get_status(wfc));

    return WALLY_RESULT_NO_ERROR;
}

int wally_fohh_dump_state(void *callout_cookie, char *s, char *e)
{
    return sxprintf(s, e, "Wally FOHH client dump state\n");
}


int wally_fohh_add_table(void *callout_cookie,
                         const char *db_table_name,
                         const char *argo_object_name)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) callout_cookie;
    struct argo_state *argo;
    size_t i;

    WALLY_DEBUG_TABLE("Add table %s", argo_object_name);

    pthread_mutex_lock(&(wfc->client->lock_disconnect));

    argo = fohh_argo_get_rx(wfc->client);
    if (argo && wfc->client->state != fohh_connection_disconnected)
    {
        for (i = 0; i < wfc->wally->tables_count; i++) {
            argo_register_structure(argo, wfc->wally->tables[i]->argo_description, server_row_callback, wfc);
        }
    } else {
        WALLY_LOG(AL_NOTICE, "argo_state has been deleted");
    }

    pthread_mutex_unlock(&(wfc->client->lock_disconnect));

    return WALLY_RESULT_NO_ERROR;
}


enum wally_origin_status wally_fohh_get_status(void *callout_cookie)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) callout_cookie;
    enum fohh_connection_state state;

    state = fohh_get_state(wfc->client);

    /* Enumeration used here just to show all the states- to keep them in mind. */
    switch(state) {
    case fohh_connection_unresolved:
    case fohh_connection_unreachable:
    case fohh_connection_backoff:
    case fohh_connection_connecting:
    case fohh_connection_disconnected:
    case fohh_connection_deleted:
    default:
        return wally_origin_status_not_ready;
    case fohh_connection_connected:
        return wally_origin_status_ready;
    }
}

struct fohh_connection *
wally_fohh_client_get_f_conn(struct wally_fohh_client *client)
{
    if (client) return client->client;
    return NULL;
}

/* Function : server_recovery_begin_callback
 * Arg      : cookie -  wally fohh client object
 *            structure_cookie - wally fohh client object
 *            object - wally_fohh_recover_begin_request object
 * Ret      : WALLY_RESULT_NO_ERROR if success, otherwise appropriate error.
 * Desc     : Wally client receives begin request, starts recovery mode
 */
static int server_recovery_begin_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) structure_cookie;
    struct wally_fohh_recovery_begin_request *recovery_begin_request = (struct wally_fohh_recovery_begin_request *) object->base_structure_data;

    WALLY_DEBUG_RECOVERY("Begin_Request_Receiver: Received begin request from %s table_name = %s"
                        "recovery_sequence = %"PRId64" recovery_timeout = %"PRId64" sync_missing_rows = %d",
                        wfc->client->remote_address_str, recovery_begin_request->table_name,
                        recovery_begin_request->recovery_sequence,
                        recovery_begin_request->recovery_timeout, recovery_begin_request->sync_missing_rows);

    if(IS_WALLY_LAYER_ENABLED) {
        wally_table_queue_enqueue_fohh_recovery_begin_req(wfc->fohh_to_wally_cookie,
                                                    recovery_begin_request->table_name,
                                    recovery_begin_request->recovery_sequence,
                                    recovery_begin_request->recovery_timeout,
                                    recovery_begin_request->sync_missing_rows);
    } else {
        wally_table_client_recovery_begin_request_handler(wfc->fohh_to_wally_cookie, recovery_begin_request->table_name,
                                    recovery_begin_request->recovery_sequence,
                                    recovery_begin_request->recovery_timeout,
                                    recovery_begin_request->sync_missing_rows);
    }
    return WALLY_RESULT_NO_ERROR;
}

/* Function : server_recovery_end_callback
 * Arg      : cookie - Wally Fohh Client object
 *            structure_cookie - Wally Fohh Client object
 *            object - recovery end request object from server
 * Ret      : WALLY_RESULT_NO_ERROR if success, otherwise appropriate error
 * Desc     : This function receives end requests and forwards to clients
 */
static int server_recovery_end_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_client *wfc = (struct wally_fohh_client *) structure_cookie;
    struct wally_fohh_recovery_end_request *recovery_end_request = (struct wally_fohh_recovery_end_request *) object->base_structure_data;

    WALLY_DEBUG_RECOVERY("End_Request_Reciever: Received recovery_end_request from %s for %s table "
                            "server_sent_rows = %"PRId64" ",
                            wfc->client->remote_address_str, recovery_end_request->table_name, recovery_end_request->server_sent_rows);

    if(IS_WALLY_LAYER_ENABLED) {
        wally_table_queue_enqueue_fohh_recovery_end_req(wfc->fohh_to_wally_cookie,
                                                    recovery_end_request->table_name, recovery_end_request->server_sent_rows);
    } else {
        wally_table_client_recovery_end_request_handler(wfc->fohh_to_wally_cookie, recovery_end_request->table_name, recovery_end_request->server_sent_rows);
    }

    return WALLY_RESULT_NO_ERROR;
}

/* Function : wally_fohh_send_recovery_complete_response
 * Arg      : Callcout_cookie - WFC object
 *            table_name - Table name for which response received
 *            row_count - Number of rows received in client
 * Ret      : WALLY_RESULT_NO_ERR if success
 * Desc     : This function receives complete response from client before
 *              forwarding to remote wallys.
 */
int wally_fohh_send_recovery_complete_response(void *callout_cookie,
                                  char *table_name, int64_t received_rows)
{
    /* There is really no state to keep, here. */
    struct wally_fohh_client *wfc = (struct wally_fohh_client *)callout_cookie;
    struct wally_fohh_recovery_complete_response resp = {0};
    int res = WALLY_RESULT_NO_ERROR;;

    resp.table_name = table_name;
    resp.received_row_count = received_rows;

    res = fohh_argo_serialize(wfc->client, wfc->recovery_complete_response, &resp, 0, fohh_queue_element_type_control);
    WALLY_DEBUG_RECOVERY("Complete_Resp_Sender: %s Recovery Response for: table %s row_count =%"PRId64" "
            " res = %s",
            wfc->domain_name,
            table_name, resp.received_row_count, argo_result_string(res));

    return res;
}
