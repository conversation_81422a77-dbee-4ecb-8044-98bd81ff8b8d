#include <sys/stat.h>
#include <event2/event.h>
#include "zthread/zthread.h"
#include "zevent/zevent.h"
#include <math.h>
#include <libpq-fe.h>
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_db.h"
#include "wally/wally_postgres.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_oper.h"

#define WP_MAX_CLEANUP_PROCESS_US (120*1000000) /* 2 minutes */
#define WALLY_PGDB_TRY_LOCK_INTERVAL_US (1000000) /* 1 second */
#define WALLY_TIMER_DB_CONN_LOOP_COUNT  (12000) /* count to 1 minute */
#define WALLY_TIMER_DB_CMD_TIMEOUT      (1800000000)  /* 30 Mins */

/* RDS DB connections disconnected manually */
bool wally_origin_disc_manual = false;
int et_26295_test_size = 0;
int c_state_count = 0;
int wally_db_init_read_nolock = 0; // skip table exclusive lock on initial db read
struct wally_db_connection_state c_state[WALLY_DB_MAX_CONNS];
struct wp_connection *c_conns[WALLY_DB_MAX_CONNS];
struct wally_table_poll_time *wtpt = NULL;
int wally_table_poll_time_count = 0;

const char *wp_connection_state_names[] = {
    [(enum wp_connection_state)conn_idle] = "conn_idle",
    [(enum wp_connection_state)conn_table_write] = "conn_table_write",

    [(enum wp_connection_state)conn_table_read_begin] = "conn_table_read_begin",
    [(enum wp_connection_state)conn_table_read_lock] = "conn_table_read_lock",
    [(enum wp_connection_state)conn_table_read_select] = "conn_table_read_select",
    [(enum wp_connection_state)conn_table_read_commit] = "conn_table_read_commit",

    [(enum wp_connection_state)conn_table_poll_check] = "conn_table_poll_check",
    [(enum wp_connection_state)conn_table_poll_begin] = "conn_table_poll_begin",
    [(enum wp_connection_state)conn_table_poll_lock] = "conn_table_poll_lock",
    [(enum wp_connection_state)conn_table_poll_select] = "conn_table_poll_select",
    [(enum wp_connection_state)conn_table_poll_commit] = "conn_table_poll_commit",
    [(enum wp_connection_state)conn_table_poll_rollback] = "conn_table_poll_rollback",

    [(enum wp_connection_state)conn_table_delete_sequence] = "conn_table_delete_sequence",
    [(enum wp_connection_state)conn_table_resynchronize] = "conn_table_resynchronize",
    [(enum wp_connection_state)conn_table_create_db] = "conn_table_create_db",

    [(enum wp_connection_state)conn_table_cleanup_update] = "conn_table_cleanup_update",
    [(enum wp_connection_state)conn_table_minseq_update] = "conn_table_minseq_update",
    [(enum wp_connection_state)conn_table_read_select_nolock] = "conn_table_read_select_nolock",
    [(enum wp_connection_state)conn_table_gvr_poll_check] = "conn_table_gvr_poll_check",
    [(enum wp_connection_state)conn_table_gvr_poll_select] = "conn_table_gvr_poll_select",
    [(enum wp_connection_state)conn_table_gvr_pending_txn_check] = "conn_table_gvr_pending_txn_check",
    [(enum wp_connection_state)conn_table_batch_write] = "conn_table_batch_write",
    [(enum wp_connection_state)conn_failed] = "conn_failed"
};

const char *wp_table_state_names[] = {
    [(enum wp_table_state)table_ready] = "table_ready",
    [(enum wp_table_state)table_hard_error] = "table_hard_error",
    [(enum wp_table_state)table_resynchronize] = "table_resynchronize",
    [(enum wp_table_state)table_read_table] = "table_read_table",
    [(enum wp_table_state)table_create_table] = "table_create_table",
    [(enum wp_table_state)table_read_columns] = "table_read_columns",
    [(enum wp_table_state)table_read_indexes] = "table_read_indexes",
    [(enum wp_table_state)table_add_columns] = "table_add_columns",
    [(enum wp_table_state)table_add_indexes] = "table_add_indexes",
    [(enum wp_table_state)table_read_highest_sequence_begin] = "table_read_highest_sequence_begin",
    [(enum wp_table_state)table_read_highest_sequence_lock] = "table_read_highest_sequence_lock",
    [(enum wp_table_state)table_read_highest_sequence_select] = "table_read_highest_sequence_select",
    [(enum wp_table_state)table_read_highest_sequence_commit] = "table_read_highest_sequence_commit",
    [(enum wp_table_state)table_add_notification] = "table_add_notification",
    [(enum wp_table_state)table_doesnt_exist] = "table_doesnt_exist",
    [(enum wp_table_state)table_read_highest_sequence_gvr] = "table_read_highest_sequence_gvr",
    [(enum wp_table_state)table_read_highest_sequence_gvr_query_pending] = "table_read_highest_sequence_gvr_query_pending",
    [(enum wp_table_state)table_read_get_last_transcation_gvr] = "table_read_get_last_transcation_gvr",
    [(enum wp_table_state)table_read_get_last_transcation_gvr_query_pending] = "table_read_get_last_transcation_gvr_query_pending",
    [(enum wp_table_state)table_read_lookup_table] = "table_read_lookup_table",
    [(enum wp_table_state)table_create_lookup_table] = "table_create_lookup_table",
};

/*
 * Release the queue object memory based on the type
 * if it is lookutable update free the memory allocated for value
 * if it is actual row update argo relase the row object
 */
void wp_release_write_object(struct wally_local_db_write_queue_object *write_object, bool only_row_release)
{
	if (write_object == NULL) {
		WALLY_LOG(AL_ERROR, "Trying to free NULL object");
		return;
	}
	if (write_object->db_op == db_oper_lookuptable_update) {
		WALLY_FREE(write_object->data.lookup_table_write.value);
	} else if (write_object->db_op == db_oper_rowargo_object) {
		argo_object_release(write_object->data.row_to_write);
	} else if (write_object->db_op == db_oper_recovery_update) {
        WALLY_DEBUG_POSTGRES("No free required for recovery update");
    }
	if (!only_row_release) {
		WALLY_FREE(write_object);
	}
}
/* for et_26295 unit test */
void set_et_26295_test_size()
{
    if (et_26295_test_size == 0) {
        WALLY_DEBUG_POSTGRES("Check if it is for ET_26295_TEST");
        char *p = getenv("ET_26295_TEST_SIZE");
        if (p) {
            et_26295_test_size = atoi(p);
        }
    }

    /*
     * The getenv() is expensive, If it is invalid value, set it to negative,
     * so we will never try to set it anymore. This makes sure getenv() is called just once
     */
    if (et_26295_test_size == 0) et_26295_test_size = -1;
}

int fill_wally_db_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_db *wp_db = cookie;
    struct wally_db_stats *stats = structure_data;

    stats->cur_db_size = wp_db->cur_db_size;
    stats->max_db_size = wp_db->max_db_size;

    return ARGO_RESULT_NO_ERROR;
}

int fill_wp_db_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_db *db = cookie;
    struct wp_db_stats *stats = structure_data;

    WP_DB_STATS_UPDATE(db, stats, query)
    WP_DB_STATS_UPDATE(db, stats, update)
    WP_DB_STATS_UPDATE(db, stats, insert)
    WP_DB_STATS_UPDATE(db, stats, delete)
    WP_DB_STATS_UPDATE(db, stats, lock)
    WP_DB_STATS_UPDATE(db, stats, tcl)
    WP_DB_STATS_UPDATE(db, stats, ddl)

    stats->poll_interval = db->polling_interval_us;
    stats->poll_rate_timeout = db->poll_rate_timeout_us;

    WP_DB_STATS_RESET(db, query)
    WP_DB_STATS_RESET(db, update)
    WP_DB_STATS_RESET(db, insert)
    WP_DB_STATS_RESET(db, delete)
    WP_DB_STATS_RESET(db, lock)
    WP_DB_STATS_RESET(db, tcl)
    WP_DB_STATS_RESET(db, ddl)

    return ARGO_RESULT_NO_ERROR;
}

int fill_wp_db_table_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_table *tbl = cookie;
    struct wp_db_table_stats *stats = structure_data;

    WP_DB_TABLE_STATS_UPDATE(tbl, stats, query)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, update)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, insert)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, delete)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, lock)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, tcl)
    WP_DB_TABLE_STATS_UPDATE(tbl, stats, ddl)

    stats->schema_mismatch_count = tbl->db_table_oper_stats.schema_mismatch_count;
    stats->transaction_failure_count = tbl->db_table_oper_stats.transaction_failure_count;
    stats->object_wrtie_failure_count = tbl->db_table_oper_stats.object_wrtie_failure_count;
	stats->db_lookup_table_write_retry = tbl->db_lookup_table_write_retry;
	stats->db_lookup_table_total_time_us = tbl->db_lookup_table_total_time_us;
	stats->db_lookup_table_total_operations_successful = tbl->db_lookup_table_total_operations_successful;
	stats->db_lookup_table_total_operations_failed = tbl->db_lookup_table_total_operations_failed;

	//reset lookup table stats
	tbl->db_lookup_table_write_retry = 0;
	tbl->db_lookup_table_total_time_us = 0;
	tbl->db_lookup_table_total_operations_successful = 0;
	tbl->db_lookup_table_total_operations_failed = 0;

    WP_DB_TABLE_STATS_RESET(tbl, query)
    WP_DB_TABLE_STATS_RESET(tbl, update)
    WP_DB_TABLE_STATS_RESET(tbl, insert)
    WP_DB_TABLE_STATS_RESET(tbl, delete)
    WP_DB_TABLE_STATS_RESET(tbl, lock)
    WP_DB_TABLE_STATS_RESET(tbl, tcl)
    WP_DB_TABLE_STATS_RESET(tbl, ddl)

    return ARGO_RESULT_NO_ERROR;
}


int fill_wally_db_table_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_table *t = cookie;
    struct wally_db_table_stats *stats = structure_data;

    stats->cur_row_count = t->cur_row_count;
    stats->max_row_count = t->max_row_count;
    stats->del_row_count = t->del_row_count;
    return ARGO_RESULT_NO_ERROR;
}

int wally_db_table_log_register(struct wp_table *t)
{
    if (!t->stats_registerd) {
        struct wally_db_table_stats *z_stats;
        char name[256];

        snprintf(name, sizeof(name), "wally_db_%s_table_%s_stats", t->db->name, t->db_table_name);
        z_stats = WALLY_MALLOC(sizeof(*z_stats));
        argo_log_register_structure(wally_stats_log,
                                    name,
                                    AL_INFO,
                                    600*1000*1000,    /* 10 minutes */
                                    wally_db_table_stats_description,
                                    z_stats,
                                    0,
                                    fill_wally_db_table_stats,
                                    t);
        t->stats_registerd = 1;
    }

    return 0;
}

/* Insert the free DB connection back to TAILQ and init the values to default.
 * This routine will not  insert the connection if it is in failed state.*/
void wally_db_insert_free_conn (struct wp_db *wp_db, struct wp_connection *wp_conn)
{
    if (wp_db == NULL || wp_conn == NULL) {
        return;
    }

    /* Connection is in FAILED state, means no access to DB. So don't insert it*/
    if (wp_conn->state == conn_failed) {
        WALLY_DEBUG_POSTGRES_CONN("Failed connection %s/%d", wp_conn->wp_db->name, wp_conn->my_index);
        return;
    }

    WALLY_DEBUG_POSTGRES_CONN("Free connection %s/%d", wp_conn->wp_db->name, wp_conn->my_index);
    wp_conn->state = conn_idle;
    wp_conn->db_op_start_time_us = 0;
    TAILQ_INSERT_TAIL(&(wp_db->free_connections), wp_conn, free_connections);
}

void *wally_db_thread(struct zthread_info *zthread, void *arg)
{
    struct wp_db *wp_db = (struct wp_db *) arg;

    wp_db->zevent = zevent_attach(wp_db->ev_base);
    wp_db->zthread = zthread;
    while(1) {
        /* XXX Event dispatch */
        WALLY_LOG(AL_NOTICE, "%s: WALLY thread starting.", wp_db->name);
        zevent_base_dispatch(wp_db->ev_base);
        WALLY_LOG(AL_ERROR, "%s: WALLY thread: Should never reach.", wp_db->name);
    }
    return NULL;
}

struct defer_empty_response_cookie {
	void *db_to_wally_cookie;
	char *table_name;
};

static void defer_empty_response_cb(struct zevent_base *base,
                                    void *void_cookie,
                                    int64_t int_cookie)
{
	struct defer_empty_response_cookie *cookie = void_cookie;
    WALLY_DEBUG_POSTGRES_EVENT("Facsimile empty response sent, request id=%"PRId64, int_cookie);
    wally_xfer_response(cookie->db_to_wally_cookie, int_cookie, 0, false, cookie->table_name);
	WALLY_FREE(cookie);
}

/*
 * Add an indexed column to a table.
 *
 * Does nothing (and returns no error) if duplicate addition is attempted.
 *
 *
 *
 * NOTE: Must have BOTH wally write lock and an ARGO lock when calling this routine.
 */
int wp_add_index_column(struct wp_table *t, const char *column_name)
{
    struct wp_index *wi;
    struct argo_private_field_description *fd = NULL;
    int res;
    int is_null = 0;

    if (strlen(column_name) == 0) is_null = 1;

    wi = argo_hash_lookup(t->index_columns, column_name, strlen(column_name), NULL);
    if (wi) {
        /* XXX LOG */
        return WALLY_RESULT_NO_ERROR;
    }

    if (t->index_count >= WALLY_MAX_INDEXED_COLUMNS) {
        int i;
        WALLY_LOG(AL_ERROR, "Exceeded index count, table %s, column %s: %d>=%d",
                  t->db_table_name, column_name, t->index_count, WALLY_MAX_INDEXED_COLUMNS);
        for (i = 0; i < WALLY_MAX_INDEXED_COLUMNS; i++) {
            WALLY_LOG(AL_ERROR, "Index column %d: %s:", i, t->indexes[i]->column_name);
        }
        return WALLY_RESULT_NO_MEMORY;
    }
    WALLY_DEBUG_REGISTRATION("Adding index %d for table %s, column %s",
                             t->index_count, t->db_table_name, is_null ? "NULL" : column_name);

    if (!is_null) {
        /* Get type/index of argo field for this index. */
        fd = zhash_table_lookup(t->description->described_fields, column_name, strlen(column_name), NULL);
        if (!fd) {
            /*
             * Currently, we don't add indexed fields dynamically. It
             * would probably be trivial to add the column here if
             * required, as argo allows column adding pretty easily.
             */
            return WALLY_RESULT_BAD_ARGUMENT;
        }
    }

    wi = (struct wp_index *) WALLY_MALLOC(sizeof(*wi));
    if (!wi) {
        /* XXX LOG */
        return WALLY_RESULT_NO_MEMORY;
    }
    memset(wi, 0, sizeof(*wi));

    if (!is_null) {
        wi->argo_index = fd->index;
        wi->argo_field_type = fd->public_description.argo_field_type;
    }
    wi->is_null= is_null;

    wi->column_name = WALLY_MALLOC(strlen(column_name) + 1);
    if (!wi->column_name) {
        WALLY_FREE(wi);
        return WALLY_RESULT_NO_MEMORY;
    }
    strcpy(wi->column_name, column_name);

    if (!is_null) {
        wi->interests = argo_hash_alloc(8, 1);
        if (!wi->interests) {
            WALLY_FREE(wi->column_name);
            WALLY_FREE(wi);
            return WALLY_RESULT_NO_MEMORY;
        }
    }

    res = argo_hash_store(t->index_columns, wi->column_name, strlen(wi->column_name), 1, wi);
    if (res) {
        if (!is_null) {
            argo_hash_free(wi->interests);
        }
        WALLY_FREE(wi->column_name);
        WALLY_FREE(wi);
        return res;
    }

    wi->index = t->index_count;

    t->indexes[t->index_count] = wi;
    t->index_count++;
    return WALLY_RESULT_NO_ERROR;
}

void wp_bootup_update_table_stats(struct wp_table *table, enum wp_table_bootup_stats bootup_stats)
{
    /* stats only published during bootup. Callback is called only during row read still check is required */
    if ( !table || (get_wally_app_state() >= wally_state_tables_loaded) )
    {
        return;
    }
    switch(bootup_stats)
    {
        case wp_bootup_table_num_queries:
            table->db_pg_table_bootup_stats.bootup_table_num_queries++;
            break;
        case wp_bootup_table_batches_completed:
            table->db_pg_table_bootup_stats.bootup_table_batches_completed++;
            break;
        case wp_bootup_table_total_start:
            table->db_pg_table_bootup_stats.bootup_table_total_start_us = epoch_us();
            break;
        case wp_bootup_table_total_end:
            if (table->db_pg_table_bootup_stats.bootup_table_total_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_total_time_us = (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_total_start_us);
                table->db_pg_table_bootup_stats.bootup_table_total_start_us = 0;
            }
            break;
        case wp_bootup_table_seqlock_start:
            table->db_pg_table_bootup_stats.bootup_table_seqlock_start_us = epoch_us();
            break;
        case wp_bootup_table_seqlock_end:
            if (table->db_pg_table_bootup_stats.bootup_table_seqlock_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_seqlock_time_us += (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_seqlock_start_us);
                table->db_pg_table_bootup_stats.bootup_table_seqlock_start_us = 0;
            }
            break;
        case wp_bootup_table_readlock_start:
            table->db_pg_table_bootup_stats.bootup_table_readlock_start_us = epoch_us();
            break;
        case wp_bootup_table_readlock_end:
            if (table->db_pg_table_bootup_stats.bootup_table_readlock_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_readlock_time_us += (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_readlock_start_us);
                table->db_pg_table_bootup_stats.bootup_table_readlock_start_us = 0;
            }
            break;
        case wp_bootup_table_query_start:
            table->db_pg_table_bootup_stats.bootup_table_query_start_us = epoch_us();
            break;
        case wp_bootup_table_query_end:
            if (table->db_pg_table_bootup_stats.bootup_table_query_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_query_time_us += (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_query_start_us);
                table->db_pg_table_bootup_stats.bootup_table_query_start_us = 0;
            }
            break;
        case wp_bootup_table_rowparse_start:
            table->db_pg_table_bootup_stats.bootup_table_rowparse_start_us = epoch_us();
            break;
        case wp_bootup_table_rowparse_end:
            if (table->db_pg_table_bootup_stats.bootup_table_rowparse_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_rowparse_time_us += (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_rowparse_start_us);
                table->db_pg_table_bootup_stats.bootup_table_rowparse_start_us = 0;
             }
             break;

        case wp_bootup_table_nolock_start:
             table->db_pg_table_bootup_stats.bootup_table_nolock_start_us = epoch_us();
             break;

        case wp_bootup_table_txn_pending_start:
             table->db_pg_table_bootup_stats.bootup_table_txn_pending_start_us = epoch_us();
             break;

        case wp_bootup_table_excl_lock_start:
             if (table->db_pg_table_bootup_stats.bootup_table_excl_lock_start_us == 0) {
                table->db_pg_table_bootup_stats.bootup_table_excl_lock_start_us = epoch_us();
             }
             break;
        case wp_bootup_table_nolock_end:
            if (table->db_pg_table_bootup_stats.bootup_table_nolock_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_nolock_time_us = (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_nolock_start_us);
                table->db_pg_table_bootup_stats.bootup_table_nolock_start_us = 0;
            }

            if (table->db_pg_table_bootup_stats.bootup_table_txn_pending_start_us != 0) {
                table->db_pg_table_bootup_stats.bootup_table_txn_pending_time_us = (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_txn_pending_start_us);
                table->db_pg_table_bootup_stats.bootup_table_txn_pending_start_us = 0;
            }

            if (table->db_pg_table_bootup_stats.bootup_table_excl_lock_start_us != 0)
            {
                table->db_pg_table_bootup_stats.bootup_table_excl_lock_time_us = (epoch_us() - table->db_pg_table_bootup_stats.bootup_table_excl_lock_start_us);
                table->db_pg_table_bootup_stats.bootup_table_excl_lock_start_us = 0;
            }
            break;
        case wp_bootup_excl_lock_counter:
            table->db_pg_table_bootup_stats.bootup_table_excl_lock_cont++;
            break;
        case wp_bootup_txn_pending_counter:
            table->db_pg_table_bootup_stats.bootup_table_txn_pending_cont++;
            break;
        default:
           WALLY_LOG(AL_ERROR, "Bad stats request for table  %s - %s", table->db->name, table->db_table_name);
           break;
    }
}
void wp_bootup_update_table_row_stats(struct wp_table *table, enum wp_table_bootup_stats bootup_stats, int64_t duration)
{
    if (!table) {
        return;
    }
    switch (bootup_stats)
    {
        case wp_bootup_table_rowstore_time:
            table->db_pg_table_bootup_stats.bootup_table_rowstore_time_us += duration;
            break;
        case wp_bootup_table_rowcb_time:
            table->db_pg_table_bootup_stats.bootup_table_rowcb_time_us += duration;
            break;
        default:
            WALLY_LOG(AL_ERROR, "Bad stats request for table  %s - %s", table->db->name, table->db_table_name);
            break;
    }
}

int fill_wally_postgres_table_bootup_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_table *table = cookie;
    struct wally_postgres_table_bootup_stats *stats = structure_data;
    if ( !table || !stats)
    {
        return WALLY_RESULT_ERR;
    }
    stats->bootup_table_num_queries = table->db_pg_table_bootup_stats.bootup_table_num_queries;
    stats->bootup_table_batches_completed = table->db_pg_table_bootup_stats.bootup_table_batches_completed;
    stats->bootup_table_total_time_us = table->db_pg_table_bootup_stats.bootup_table_total_time_us;
    stats->bootup_table_seqlock_time_us = table->db_pg_table_bootup_stats.bootup_table_seqlock_time_us;
    stats->bootup_table_readlock_time_us = table->db_pg_table_bootup_stats.bootup_table_readlock_time_us;
    stats->bootup_table_query_time_us = table->db_pg_table_bootup_stats.bootup_table_query_time_us;
    stats->bootup_table_rowparse_time_us = table->db_pg_table_bootup_stats.bootup_table_rowparse_time_us;
    stats->bootup_table_rowstore_time_us = table->db_pg_table_bootup_stats.bootup_table_rowstore_time_us;
    stats->bootup_table_rowcb_time_us = table->db_pg_table_bootup_stats.bootup_table_rowcb_time_us;

   //GVR
    stats->bootup_gvr_txn_pending_waiting_time = table->db_pg_table_bootup_stats.bootup_table_txn_pending_time_us;
    stats->bootup_gvr_excl_lock_waiting_time = table->db_pg_table_bootup_stats.bootup_table_excl_lock_time_us;
    stats->bootup_gvr_table_max_seq_read_time = table->db_pg_table_bootup_stats.bootup_table_nolock_time_us;
    stats->bootup_gvr_pending_no_of_txns = table->db_pg_table_bootup_stats.bootup_table_txn_pending_cont;
    stats->bootup_gvr_pending_no_of_query = table->db_pg_table_bootup_stats.bootup_table_excl_lock_cont;

    return WALLY_RESULT_NO_ERROR;
}

/*
 * Just do table creation- don't start up any processing of the table.
 */
struct wp_table *wp_just_create_table(struct wp_db *wp_db, const char *db_table_name, const char *argo_object_name)
{
    struct wp_table *t;
    size_t i;
    char str[ARGO_MAX_NAME_LENGTH];
    int result;

    WALLY_LOG(AL_NOTICE, "%s: %s: Creating/Opening table using argo object type %s", wp_db->name, db_table_name, argo_object_name);

    argo_read_lock();

    t = (struct wp_table *) WALLY_MALLOC(sizeof(*t));
    if (!t) goto fail_free;
    /* prev_max_sequence is set to 0 */
    memset(t, 0, sizeof(*t));

    t->db = wp_db;
    t->db_table_name = WALLY_STRDUP(db_table_name, strlen(db_table_name));
    if (!t->db_table_name) goto fail_free;

    t->argo_object_name = WALLY_STRDUP(argo_object_name, strlen(argo_object_name));
    if (!t->argo_object_name) goto fail_free;

    t->db_state = wp_db_table_poll_idle;

    /*
     * Search for a structure with a name of <table_name> or db_<table_name>
     */
    snprintf(str, sizeof(str), "db_%s", argo_object_name);
    t->description = zhash_table_lookup(argo_global.all_descriptions_hash, str, strlen(str), NULL);
    if (!t->description) {
        t->description = zhash_table_lookup(argo_global.all_descriptions_hash, argo_object_name, strlen(argo_object_name), NULL);
    }
    if (!t->description) {
        /* Create a generic sequence/ID table structure. */
        argo_unlock();
        t->description = generic_row_description;
        argo_read_lock();
        if (!t->description) goto fail_free;
    }

    /* go through argo structure to find delete column */
    for (i = 0; i < t->description->description_count; i++) {
        if (t->description->description[i]->public_description.is_deleted) {
            t->deleted_column_name = t->description->description[i]->public_description.field_name;
        }
    }

    /* go through argo structure to find sequence. */
    for (i = 0; i < t->description->description_count; i++) {
        if (t->description->description[i]->public_description.is_sequence) break;
    }
    if (i == t->description->description_count) {
        /* No sequence? */
        goto fail_free;
    }

    /* create hash of indexes. */
    t->index_columns = argo_hash_alloc(4, 1);
    if (!t->index_columns) {
        goto fail_free;
    }

    /* XXX WARNING: This is not allocated. */
    t->sequence_column_name = t->description->description[i]->public_description.field_name;

    /* Leave argo_field_count at zero- will force full postgres table synchronization. */
    t->state = table_ready;
    t->needs_to_be_resynchronized = 1;
	t->lookup_table_exists = false;

    t->xpoll_poll_interval_us = wally_db_get_table_poll_time(t->db_table_name);

    /* Add table to hash... */
    if (argo_hash_store(wp_db->tables_by_argo_name, t->argo_object_name, strlen(t->argo_object_name), 0, t)) {
        goto fail_free;
    }

    /* Add index column for null index. */
    result = wp_add_index_column(t, "");
    if (result) {
        /* XXX LOG */
        goto fail_free;
    }

    LIST_INSERT_HEAD(&(wp_db->all_tables), t, all_tables);
    wp_db->all_tables_count++;
    if (wp_db->db_type == db_postgres)
    {
        /* Register bootup stats during bootup table loading */
        if ( get_wally_app_state() == wally_state_tables_loading )
        {
            char name[WALLY_DB_STATS_NAME_SIZE] = {0};
            snprintf(name, sizeof(name), "%s_%s", wp_db->name, db_table_name);
            t->wally_db_table_bootup_stats_structure =  argo_log_register_structure(wally_stats_log, name, AL_INFO,
                                                                                60*1000*1000,    /* 1 minute */
                                                                                wally_db_pg_table_bootup_stats_description,
                                                                                &(t->wp_pg_table_bootup_stats), 0,
                                                                                fill_wally_postgres_table_bootup_stats, t);
        }

    }
    argo_unlock();

    return t;

 fail_free:
    WALLY_LOG(AL_ERROR, "fail_free");
    if (t) {
        if (t->db_table_name) WALLY_FREE(t->db_table_name);
        if (t->argo_object_name) WALLY_FREE(t->argo_object_name);
        if (t->index_columns) argo_hash_free(t->index_columns);
        WALLY_FREE(t);
    }
    argo_unlock();
    WALLY_LOG(AL_ERROR, "Err");
    return NULL;
}

/*
 * Write lock must be held before call.
 *
 * Note that this call "owns" wp_conn, and must free it up if it is not used.
 */
int wp_create_table(struct wp_db *wp_db, struct wp_connection *wp_conn, const char *db_table_name, const char *argo_object_name)
{
    /* Allocate table, add it initialize, then run the state machine on it. */
    struct wp_table *t;
    int rc;

    WALLY_LOG(AL_NOTICE, "Creating/Opening table <%s/%s> using object %s, db_type=%d",
                         wp_db->name, db_table_name, argo_object_name, wp_db->db_type);

    t = wp_just_create_table(wp_db, db_table_name, argo_object_name);
    if (!t) {
        goto fail_free;
    }

    t->needs_to_be_resynchronized = 0;
    t->state = table_resynchronize;

    /* Associate table with connection, and start the table creation process */
    wp_conn->table = t;
    if (wp_db->db_type == db_postgres) {
        rc = postgres_update_table_state_machine(t, wp_conn);
    } else {
        wp_conn->state = conn_table_write;
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                         sqlt_synchronize_1,
                         wp_conn,
                         0);
        rc = WALLY_RESULT_WOULD_BLOCK;
    }

    return rc;

 fail_free:
    WALLY_LOG(AL_ERROR, "fail_free");
    wally_db_insert_free_conn (wp_db, wp_conn);
    return WALLY_RESULT_NO_MEMORY;
}

/*
 * Standard index registration code.
 *
 * The only time this code returns "block" is:
 *
 * 1. If the system is still doing table initialization.
 *
 * 2. If all DB connections are busy.
 */
int wally_db_register_for_index(void *callout_cookie,
                                int64_t request_id,
                                int64_t request_sequence,
                                char *table_name,
                                char *column_name,
                                char *key)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    struct wp_table *t;
    struct wp_connection *wp_conn;
    struct wp_index *index;
    int64_t *int_req_id = NULL;
    void *data;
    int result;
    int conn_ix;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    WALLY_DEBUG_REGISTRATION("%s: Register ID %ld, sequence %ld: %s:%s:%s",
                             wp_db->name,
                             (long) request_id,
                             (long) request_sequence,
                             table_name,
                             column_name,
                             key);

    if (wp_db->is_blocking && wp_db->db_type != db_sqlt) {
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_DEBUG_POSTGRES("Would block (already blocking) (wp = %s, table = %s, column = %s, key = %s",
                             wp_db->name, table_name, column_name, key);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    if (wally_origin_disc_manual) {
        wp_db->is_blocking = 1;
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_DEBUG_POSTGRES_FC("Block (read disabled) (wp = %s, table = %s, column = %s, key = %s",
                                wp_db->name, table_name, column_name, key);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    /* We assume registration will require a db connection to use. So
     * does most other stuff we end up doing here.  */
    wp_conn = TAILQ_FIRST(&(wp_db->free_connections));
    if (!wp_conn) {
        wp_db->is_blocking = 1;
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_DEBUG_POSTGRES_FC("Block (no db conns) (wp = %s, table = %s, column = %s, key = %s",
                                wp_db->name, table_name, column_name, key);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
    WALLY_DEBUG_POSTGRES_CONN("Alloc connection %s:%d for %s:%s:%s", wp_conn->wp_db->name, wp_conn->my_index, table_name, column_name, key);

    /* We need to remember this request- this might require creating
     * tables/indexes (in our internal state- we don't mess with
     * postgres here) */
    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
        wp_conn->state = conn_table_resynchronize;
        wp_db->is_blocking = 1;
        result = wp_create_table(wp_db, wp_conn, table_name, table_name);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_DEBUG_POSTGRES_FC("Block (creating table) (wp = %s, table = %s, column = %s, key = %s",
                                wp_db->name, table_name, column_name, key);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    if (t->state == table_doesnt_exist) {
        /* However, we want to tell the requestor there are
         * zero rows, so they can make progress, too. (They might not know what tables should/not exist...) */
		struct defer_empty_response_cookie *cookie;
		cookie = (struct defer_empty_response_cookie *)WALLY_CALLOC(sizeof(struct defer_empty_response_cookie));
		if (cookie == NULL) {
			WALLY_LOG(AL_ERROR, "No Memory");
			wally_db_insert_free_conn (wp_db, wp_conn);
			ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
			wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
			return WALLY_RESULT_NO_MEMORY;
		}
		cookie->db_to_wally_cookie = wp_conn->wp_db->db_to_wally_cookie;
		cookie->table_name = t->argo_object_name;
        zevent_base_call(wp_conn->wp_db->zevent, defer_empty_response_cb, cookie, request_id);
        WALLY_DEBUG_REGISTRATION("Registration rejected because table doesn't exist. Registration will never return");
        wally_db_insert_free_conn (wp_db, wp_conn);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        /* Until we can make a zero-rows-returned callback happen, we
         * return asynchronous and let the request hang, because that
         * at least lets others play. */
        WALLY_LOG(AL_NOTICE, "Request for %s/%s/%s; table does not exist.", table_name, column_name, key);
        return WALLY_RESULT_ASYNCHRONOUS;
    }

    wp_conn->state = conn_table_read_begin;

    /* Remember the table within this connection- for callbacks. */
    wp_conn->table = t;

    /* XXX If table not in ready state, return would_block. */
    if ((t->state != table_ready) || t->needs_to_be_resynchronized) {
        wp_db->is_blocking = 1;
        wally_db_insert_free_conn (wp_db, wp_conn);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_DEBUG_POSTGRES_FC("Block (table not ready) (wp = %s, table = %s, column = %s, key = %s",
                                wp_db->name, table_name, column_name, key);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    /* XXX Get index. */
    index = argo_hash_lookup(t->index_columns, column_name, strlen(column_name), NULL);
    if (!index) {
        /* Not indexed? Bleh. */
        /* XXX Log */
        wally_db_insert_free_conn (wp_db, wp_conn);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_LOG(AL_ERROR, "Bad Arg (%s)", column_name);
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    if (index->is_null) {
        index->is_null_registered = 1;
        index->null_request_id = request_id;
		if ( get_wally_app_state() == wally_state_tables_loading ) {
			/* if we receive null column interest during loading state then table is being fully loaded.
			 */
			t->fully_loaded = 1;
		}
    } else {
        /* XXX Check if already registered. Note that registration index
         * is simply an "existence" check in the hash table. */
        data = argo_hash_lookup(index->interests, key, strlen(key), NULL);
        if (data) {
            /* Already exists. XXX Log (Account) */
            /* We want to dispatch this request anyway */
            WALLY_LOG(AL_WARNING, "Index already registered: %s %s %s", table_name, column_name, key);
        } else {
            /* We need to remember the request ID. We'll actually
             * allocate it so we have it sitting around. */
            int_req_id = WALLY_MALLOC(sizeof(*int_req_id));
            if (!int_req_id) {
                /* XXX Log */
                WALLY_LOG(AL_WARNING, "Could not store index %s %s %s", table_name, column_name, key);
                wally_db_insert_free_conn (wp_db, wp_conn);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                WALLY_LOG(AL_ERROR, "Err");
                result = WALLY_RESULT_NO_MEMORY;
                return result;
            }
            *int_req_id = request_id;
            result = argo_hash_store(index->interests, key, strlen(key), 0, int_req_id);
            if (result) {
                /* XXX Log */
                WALLY_LOG(AL_WARNING, "Could not store index %s %s %s", table_name, column_name, key);
                WALLY_FREE(int_req_id);
                wally_db_insert_free_conn (wp_db, wp_conn);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                WALLY_LOG(AL_ERROR, "Err");
                return result;
            }
        }
    }

    /* Send postgres query. Note that we limit how many rows can come
     * back, and we always order by sequence. This should be pretty
     * easy, as every index includes sequence in the least-significant
     * position. */
    wp_conn->row_request_sequence = request_sequence;
    wp_conn->wally_request_id = request_id;
    wp_conn->row_count = 0;
    wp_conn->request_wp_index_index = index->index;
    /* Need to copy the key... */
    if (wp_conn->request_key) {
        WALLY_FREE(wp_conn->request_key);
    }
    if (index->is_null) {
        wp_conn->request_key = NULL;
    } else {
        wp_conn->request_key = WALLY_MALLOC(strlen(key) + 1);
        if (!wp_conn->request_key) {
            argo_hash_remove(index->interests, key, strlen(key), NULL);
            if (int_req_id) WALLY_FREE(int_req_id);
            wally_db_insert_free_conn (wp_db, wp_conn);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            WALLY_LOG(AL_ERROR, "No memory");
            return WALLY_RESULT_NO_MEMORY;
        }
        strcpy(wp_conn->request_key, key);
    }

    if (wp_db->db_type == db_sqlt) {
        WALLY_DEBUG_POSTGRES("SQLT SELECT db=%s, table=%s, req=%d, select->req=%d",
                             wp_conn->wp_db->name, wp_conn->table->db_table_name,
                             (int)request_id, (int)wp_conn->wally_request_id);
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                         sqlt_read_select_1,
                         wp_conn,
                         0);
    } else if (wp_db->db_type == db_postgres) {
        /* wally table synchronisation is completed through wally_db timer. Read Rows now
         * with wally timer callback.
         * call nolock function only if wallyd loading and feature is enabled, otherwise
         * call existing transaction lock functionality.
         */
        /* Stats deregistered during table_synchronization and registered again on reading rows during bootup */
        if ( get_wally_app_state() == wally_state_tables_loading )
        {
            if (!t->wally_db_table_bootup_stats_structure)
            {
                char name[WALLY_DB_STATS_NAME_SIZE] = {0};
                snprintf(name, sizeof(name), "%s_%s", wp_db->name, wp_conn->table->db_table_name);
                t->wally_db_table_bootup_stats_structure =  argo_log_register_structure(wally_stats_log, name, AL_INFO,
                                                                                    60*1000*1000,    /* 1 minute */
                                                                                    wally_db_pg_table_bootup_stats_description,
                                                                                    &(t->wp_pg_table_bootup_stats), 0,
                                                                                    fill_wally_postgres_table_bootup_stats, t);
            }
            wp_bootup_update_table_stats(wp_conn->table, wp_bootup_table_total_start);

        }
        /* Reading rows during bootup will skip exclusive lock on table */
        if ((get_wally_app_state() == wally_state_tables_loading) && wally_db_init_read_nolock)
        {
            result = wp_send_read_select_nolock(wp_conn);
        } else {
            result = wp_send_read_begin(wp_conn);
        }
        if (!result) {
            /* Error */
            /* XXX LOG */
            WALLY_LOG(AL_CRITICAL, "Could not send row request: id = %ld, sequence=%ld, table=%s, column=%s, key=%s",
                      (long)request_id,
                      (long)request_sequence,
                      table_name,
                      column_name,
                      key);
            wally_error_handler(NULL, wp_conn, true,
                    E_TRANSACTION_FAIL, SE_NONE);
            return WALLY_RESULT_ERR;
#if 0
            wp_conn->state = conn_idle;
            WALLY_DEBUG_POSTGRES_CONN("Free connection %s/%d", wp_conn->wp_db->name, wp_conn->my_index);
            TAILQ_INSERT_TAIL(&(wp_db->free_connections), wp_conn, free_connections);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return WALLY_RESULT_ERR;
#endif
        }
    }

    conn_ix = wp_conn->my_index;
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    WALLY_DEBUG_REGISTRATION("%s: Registration Success. (conn ix = %d) (table = %s, column = %s, key = %s), req_id = %ld", wp_db->name, conn_ix, table_name, column_name, key, (long)request_id);
    return WALLY_RESULT_ASYNCHRONOUS;
}

int wally_db_deregister_for_index(void *callout_cookie,
                                  char *table_name,
                                  char *column_name,
                                  char *key)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    struct wp_table *t;
    struct wp_index *index;
    void *data;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    if (wp_db->is_blocking) {
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_LOG(AL_WARNING, "%s: No table: <%s>", wp_db->name, table_name);
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    index = argo_hash_lookup(t->index_columns, column_name, strlen(column_name), NULL);
    if (!index) {
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_LOG(AL_WARNING, "%s: No index: <%s>", wp_db->name, column_name);
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    if (index->is_null) {
        index->is_null_registered = 0;
        WALLY_DEBUG_REGISTRATION("%s: Removing NULL postgres registration for table %s.", wp_db->name, table_name);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    } else {
        data = argo_hash_lookup(index->interests, key, strlen(key), NULL);
        if (data) {
            /* Exists... remove it... */
            WALLY_DEBUG_REGISTRATION("%s: Removing postgres registration for %s/%s/%s", wp_db->name, table_name, column_name, key);
            argo_hash_remove(index->interests, key, strlen(key), NULL);
            WALLY_FREE(data);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return WALLY_RESULT_NO_ERROR;
        } else {
            /* Doesn't exist. */
            WALLY_LOG(AL_WARNING, "%s: Not found: postgres registration for %s/%s/%s", wp_db->name, table_name, column_name, key);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return WALLY_RESULT_NOT_FOUND;
        }
    }
    return WALLY_RESULT_NO_ERROR;
}

int wally_db_set_cookie(void *callout_cookie,
                        void *callback_cookie)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    wp_db->db_to_wally_cookie = callback_cookie;
    wally_xfer_status_update(wp_db->db_to_wally_cookie, wally_origin_status_ready);
    return WALLY_RESULT_NO_ERROR;
}

enum wally_origin_status wally_db_get_status(void *callout_cookie)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;

    if (wp_db) {
        return wally_origin_status_ready;
    } else {
        return wally_origin_status_not_ready;
    }
}

int wally_db_add_table(void *callout_cookie,
                       const char *db_table_name,
                       const char *argo_object_name)
{
    struct wp_db *wp_db = callout_cookie;
    struct wp_table *t;

    WALLY_DEBUG_REGISTRATION("WP:%s: Adding table %s using argo object %s", wp_db->name, db_table_name, argo_object_name);
    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    t = argo_hash_lookup(wp_db->tables_by_argo_name, argo_object_name, strlen(argo_object_name), NULL);
    if (!t) {
        t = wp_just_create_table(wp_db, db_table_name, argo_object_name);
        if (!t) {
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            WALLY_LOG(AL_ERROR, "Could not add table %s", db_table_name);
            return WALLY_RESULT_ERR;
        }
    }

    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}

void wally_db_get_connection_state(struct wally_db_connection_state **state,
                                   size_t *state_count)
{
    int i;
    for (i = 0; i < c_state_count; i++) {
        c_state[i].state = wp_connection_state_names[c_conns[i]->state];
    }
    *state = &(c_state[0]);
    *state_count = c_state_count;
}

int wally_db_dump_state(void *callout_cookie, char *s, char *e)
{
    struct wp_db *wp_db = callout_cookie;
    struct wp_table *table;
    int i;
    char *start = s;
    int64_t now_us = epoch_us();

    ZPATH_RWLOCK_RDLOCK(&(wp_db->lock), __FILE__, __LINE__);
    for (i = 0; i < wp_db->connections_count; i++) {
        s += sxprintf(s, e, "     %s:%-64s:%s\n",
                      wp_db->connections[i].name,
                      (wp_db->connections[i].state != conn_idle) && wp_db->connections[i].table ? wp_db->connections[i].table->db_table_name : "N/A",
                      wp_connection_state_names[wp_db->connections[i].state]);
    }

    if (wp_db->db_type == db_postgres) {

#define DELTA_T(__x) ((__x) ? (double)(now_us - (__x)) / 1000000.0 : INFINITY)

        i = 0;
        LIST_FOREACH(table, &(wp_db->all_tables), all_tables) {
            s += sxprintf(s, e, "     Min seq = %10ld, %20s, Test: %5.3fs ago, Trylock: %11.3fs ago, checks: %10ld, new data: %7ld, trylocks: %7ld, trylock fails: %7ld, Table: %s\n",
                          (long) table->min_valid_sequence_set,

                          wp_table_state_names[table->state],

                          DELTA_T(table->xpoll_last_unlocked_read_us),
                          DELTA_T(table->xpoll_last_trylocked_read_us),

                          (long)table->xpoll_check,
                          (long)table->xpoll_check_new_data,
                          (long)table->xpoll_trylock,
                          (long)table->xpoll_trylock_fails,

                          table->db_table_name);
        }
    } else {
        s += sxprintf(s, e, "     DB file: %s    Current size: %zu bytes   Max size since startup: %zu bytes\n",
                            wp_db->name, wp_db->cur_db_size, wp_db->max_db_size);
        s += sxprintf(s, e, "     %10s%20s%20s%20s%20s     %s\n",
                            "Min seq", "State", "cur row count", "max row count", "del row count", "Table");
        LIST_FOREACH(table, &(wp_db->all_tables), all_tables) {
            s += sxprintf(s, e, "     %10ld%20s%20" PRId64 "%20" PRId64 "%20" PRId64 "     %s\n",
                          (long) table->min_valid_sequence_set,
                          wp_table_state_names[table->state],
                          table->cur_row_count,
                          table->max_row_count,
                          table->del_row_count,
                          table->db_table_name);
        }

    }


    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    return s - start;
}

int wally_db_set_read_disabled(bool disabled)
{
    wally_origin_disc_manual = disabled;
    return WALLY_RESULT_NO_ERROR;
}

int wally_db_set_min_sequence(void *callout_cookie,
                                    const char *table_name,
                                    int64_t min_valid_sequence)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    struct wp_table *t;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
        t = wp_just_create_table(wp_db, table_name, table_name);
        if (!t) {
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            WALLY_LOG(AL_ERROR, "Could not set sequence for table %s: could not create table", table_name);
            return WALLY_RESULT_ERR;
        }
    }
    t->min_valid_sequence_set = min_valid_sequence;
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}

void wally_db_log_row_counts(struct wp_table *t)
{
    WALLY_DEBUG_ROW("TOTAL ROW COUNTS: db=%s, table=%s, cur_row_count=%" PRId64 ", max_row_count=%" PRId64,
                    t->db->name, t->db_table_name,  t->cur_row_count, t->max_row_count);
}

void wally_db_set_row_counts(struct wp_table *t, int64_t count, int do_log)
{
    t->cur_row_count = count;
    if (t->max_row_count < count) t->max_row_count = count;

    if (do_log) {
        wally_db_log_row_counts(t);
    }
}

void wally_db_set_deleted_row_counts(struct wp_table *t, int64_t count)
{
    t->del_row_count = count;
    WALLY_LOG(AL_NOTICE, "DEL ROW COUNTS: db=%s, table=%s, del_row_count=%" PRId64,
                         t->db->name, t->db_table_name, t->del_row_count);
}

void wally_db_set_size(struct wp_db *wp_db)
{
    struct stat st;
    if(stat(wp_db->name, &st)==0) {
        wp_db->cur_db_size = st.st_size;
        if (wp_db->cur_db_size > wp_db->max_db_size) wp_db->max_db_size = wp_db->cur_db_size;
        WALLY_DEBUG_POSTGRES("DB SIZE: db=%s, cur_db_size=%zu bytes, max_db_size=%zu bytes",
                             wp_db->name, wp_db->cur_db_size, wp_db->max_db_size);
    } else {
        WALLY_LOG(AL_ERROR, "wally_db_get_size fail: db=%s", wp_db->name);
    }
}

/*
 * Must be called with DB lock
 */
 void reset_cleanup(struct wp_table *t, int64_t min_seq)
 {
        WALLY_DEBUG_POSTGRES("complete cleanup db: %s, table:%s,"
                             " %d rows, sequences: [%" PRId64 ", %" PRId64 "], min_seq=%" PRId64,
                             t->db->name,
                             t->db_table_name,
                             t->cleanup_count,
                             t->cleanup_buf[0].sequence,
                             t->cleanup_buf[t->cleanup_count-1].sequence,
                             min_seq);

        t->min_valid_sequence_set = min_seq;
        t->cleanup_index = 0;
        t->cleanup_count = 0;
        t->cleanup_state = cleanup_idle;
 }

static void wally_detect_db_conn_failure (struct wp_db *wp_db, int64_t now)
{
    static uint64_t loop = 0;
    uint32_t i = 0;
    bool one_down = false;
    loop++;

    /* If RDS is failed, then already all connections are down, so no need to check
     * the status*/
    if (oper_mode_g.rds_failure == true){
        return;
    }

    if (get_wally_app_state() == wally_state_tables_loaded &&
            (loop % WALLY_TIMER_DB_CONN_LOOP_COUNT) == 0) { /* Check 1 min once */

        loop = 0;

        for (i = 0; i < wp_db->connections_count; i++) {
            /* Connection is already in failed state, skip the check */
            if ((wp_db->connections[i].db_conn == NULL) ||
                (wp_db->connections[i].state == conn_failed)) {
                continue;
            }
            /* No query reponse for more than WALLY_TIMER_DB_CMD_TIMEOUT, mark the connection
             * as FAILED*/
            if (wp_db->connections[i].db_op_start_time_us &&
                    (now - wp_db->connections[i].db_op_start_time_us > WALLY_TIMER_DB_CMD_TIMEOUT)) {
                wp_db->connections[i].state = conn_failed;
                wp_db->failed_connections_count++;
                one_down = true;
                WALLY_LOG(AL_NOTICE, "No response for SQL query. Postgres conn %d down", i);
                WALLY_LOG(AL_NOTICE, "DB failed connection count %d db name %s\n",
                        wp_db->failed_connections_count, wp_db->name);
            }
        }

        if (one_down) {
            /* At least one connection is active */
            wally_oper_event_post (E_DB_FAIL, SE_DB_REMOTE, wp_db);
        }
    }
}

/*
 * Periodic maintenance for wally_postgres.
 *
 * This system does:
 *
 * 1. Database table reads.
 * 2. Database table synchronization.
 * 3. Database writes. (polls for writes from wally.)
 *
 * It should be noted that database writes from wally are forced to be
 * serial here by using a single writing connection.
 */

void wally_timer_event_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct wp_db *wp_db = (struct wp_db *) cookie;
    struct wp_table *t;
    struct wp_connection *wp_conn;
    int result = 0;
    int64_t now = epoch_us();

    zthread_heartbeat(wp_db->zthread);

    /* Check DB connection status every sec. */
    if ((now - wp_db->wp_get_conn_status_poll) > WP_CONN_INTERVAL_US) {
        if (wp_db->db_type == db_sqlt) {
            wp_get_sqlt_status(wp_db);
        } else {
            wp_get_status(wp_db);
            /* Manual disconnect is not active and some of the RDS DB connection
             * is down. Try to recover every WP_CONN_RECOVERY_COUNT once.
             *
             * IMPORTANT: This timer is depends on wally_timer_event_callback timer event.
             * So any change in the timer, will affect this recovery */
            if (!wally_origin_disc_manual && wp_db->failed_connections_count != 0) {
                wp_db->recovery_try_count++;
                if (wp_db->recovery_try_count % WP_CONN_RECOVERY_COUNT) {
                    wp_recovery_db_conn(wp_db);
                    wp_db->recovery_try_count = 0;
                }
            }
        }
        wp_db->wp_get_conn_status_poll = now;
    }

    /* Check DB server status using ping every min. */
    if ((now - wp_db->wp_get_ping_status_poll) > WP_SERVER_INTERVAL_US) {
        wp_db->wp_get_ping_status_poll = now;

        if (wally_origin_disc_manual) {
            WALLY_LOG(AL_NOTICE, "Postgres access is disabled by curl command /wally/oper/mode/set?origin-disconnect");
            WALLY_LOG(AL_NOTICE, "            To enable it, execute curl command /wally/oper/mode/set?origin-connect");
        }
    }

    /*
     * check if postgres is not disabed and poll check feature is enabed,
     * Report the issue if polling did not happen for more than polling interval.
     * If polling failure is already active, don't check again
     */
    /* poll_rate_check is enabled only after application loads all the tables */
    if ((!wally_origin_disc_manual) && (wp_db->db_type == db_postgres) && wp_db->poll_rate_check) {
       if (!wp_db->polling_failure &&
               (wp_db->last_poll_time_us) && ((now - wp_db->last_poll_time_us) > wp_db->poll_rate_timeout_us )) {
                 WALLY_LOG(AL_NOTICE, "Wally is not polling to RDS");
                 /* Polling is not happening for poll_rate_timeout_us time.
                  * Could be all the connections are down. Report the issue */
                 wp_db->polling_failure = true;
                 wally_oper_event_post (E_DB_FAIL, SE_DB_POLL_FAILED, wp_db);
       } else if (wp_db->polling_failure &&
               (wp_db->last_poll_time_us) && ((now - wp_db->last_poll_time_us) < wp_db->poll_rate_timeout_us )) {
           /* Polling started again. Recover the issue */
           wp_db->polling_failure = false;
           WALLY_LOG(AL_NOTICE, "Wally polling to RDS recovered");
           wally_oper_event_post (E_DB_RECO, SE_DB_POLL_FAILED, wp_db);
       }
    }

    /*
     * Check to see if there are any tables that have been notified of
     * updates for which we need to request updated rows.
     */
    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    /* Check only on the Origin Wally */
    if ((!wally_origin_disc_manual) && (wp_db->db_type == db_postgres) &&
            wp_db->is_true_origin && !wp_db->is_alterable)  {
        /* Check the DB connection status and report the event */
        wally_detect_db_conn_failure(wp_db, now);
    }

    if (!wally_origin_disc_manual) {
        if(wp_db->next_table) {
            for((t) = wp_db->next_table; (t); (t) = LIST_NEXT((t), all_tables)) {
                if (t->stop_polling_table) {
                    continue;
                }
                if (t->db_state == wp_db_table_gvr_poll_pending_txn)
                {
                    if ((epoch_us() - t->last_pending_txn_gvr_chk_time) > WALLY_DB_PENDING_TXN_CHK_INTVL)
                    {
                        break;
                    }
                }
                if ((t->state == table_ready) &&
                    (!t->sequence_is_being_read))
                {
                    /* Case 1: Check for polling interval expired */
                    /* if xpoll_last_trylock_failed_us is 0, then it is just polling, if it is non-zero, then
                     * this is a re-attempt after earlier try-lock failure, then check for try lock interval.
                     */
                    if (t->db_state == wp_db_table_poll_idle)
                    {
                        /* Enable polling only after loading all the tables during bootup */
                        if ((get_wally_app_state() >= wally_state_tables_loaded))
                        {
                            if ((((wp_db->polling_interval_us) && ((now - t->last_read_complete_us) >
                                 (t->xpoll_poll_interval_us ? t->xpoll_poll_interval_us : wp_db->polling_interval_us)))))
                            {
                                 /*Case 1.1: If try lock failed, then check for interval */
                                if ((t->xpoll_last_trylock_failed_us == 0) ||
                                      ((now - t->xpoll_last_trylock_failed_us) > WALLY_PGDB_TRY_LOCK_INTERVAL_US))
                                        break;
                            }
                        }
                    }
                    /* Case 2: delete rows less than minimum sequence */
                    if (t->min_valid_sequence_set > t->min_valid_sequence_performed)
                        break;
                    /* Case 3: Resynchronize the table */
                    if(t->needs_to_be_resynchronized)
                        break;
                }
                if (t->state == table_read_get_last_transcation_gvr || t->state == table_read_highest_sequence_gvr_query_pending) {
                    if ((epoch_us() - t->last_gvr_request_time_us) > WALLY_DB_PENDING_TXN_CHK_INTVL) {
                        break;
                    }
                }
            }
        }
        else {
            LIST_FOREACH(t, &(wp_db->all_tables), all_tables) {
                if (t->stop_polling_table) {
                    continue;
                }
                if (t->db_state == wp_db_table_gvr_poll_pending_txn)
                {
                    if ((epoch_us() - t->last_pending_txn_gvr_chk_time) > WALLY_DB_PENDING_TXN_CHK_INTVL)
                    {
                        break;
                    }
                }
                if ((t->state == table_ready) &&
                    (!t->sequence_is_being_read))
                {
                    /* Case 1: Check for polling interval expired */
                    /* if xpoll_last_trylock_failed_us is 0, then it is just polling, if it is non-zero, then
                     * this is a re-attempt after earlier try-lock failure, then check for try lock interval.
                     */
                    if (t->db_state == wp_db_table_poll_idle)
                    {
                        /* Enable polling only after loading all the tables during bootup */
                        if ((get_wally_app_state() >= wally_state_tables_loaded))
                        {
                            if ((((wp_db->polling_interval_us) && ((now - t->last_read_complete_us) >
                             (t->xpoll_poll_interval_us ? t->xpoll_poll_interval_us : wp_db->polling_interval_us)))))
                            {
                                /*Case 1.1: If try lock failed, then check for interval */
                                if ((t->xpoll_last_trylock_failed_us == 0) ||
                                      ((now - t->xpoll_last_trylock_failed_us) > WALLY_PGDB_TRY_LOCK_INTERVAL_US))
                                        break;
                            }
                        }
                    }
                    /* Case 2: delete rows less than minimum sequence */
                    if (t->min_valid_sequence_set > t->min_valid_sequence_performed)
                        break;
                    /* Case 3: Resynchronize the table */
                    if(t->needs_to_be_resynchronized)
                        break;
                }
                if (t->state == table_read_get_last_transcation_gvr || t->state == table_read_highest_sequence_gvr_query_pending) {
                    if ((epoch_us() - t->last_gvr_request_time_us) > WALLY_DB_PENDING_TXN_CHK_INTVL) {
                        break;
                    }
                }
            }
        }
        if (t) {
            wp_db->next_table = LIST_NEXT((t), all_tables);
        } else {
            wp_db->next_table = NULL;
        }
        /* Check for table read/synchronization: */
        if (t) {
            t->now_us = now;
            //fprintf(stderr, "WP: Event callback needed on table\n");
            wp_conn = TAILQ_FIRST(&(wp_db->free_connections));
            if (wp_conn) {
                wp_conn->table = t;
                TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
                WALLY_DEBUG_POSTGRES_CONN("%s: %s: Alloc connection: %d, cleanup state: %d",
                                          t->db->name, t->db_table_name, wp_conn->my_index, t->cleanup_state);
                if (t->db_state == wp_db_table_gvr_poll_pending_txn)
                {
                    if ((epoch_us() - t->last_pending_txn_gvr_chk_time) > WALLY_DB_PENDING_TXN_CHK_INTVL)
                    {
                        result = wp_send_gvr_pending_transaction_check(wp_conn);
                    }
                }  else if (t->state == table_read_get_last_transcation_gvr ||
                         t->state == table_read_highest_sequence_gvr_query_pending) {
                    wp_conn->state = conn_table_resynchronize;
                    if (((wp_db->db_type == db_postgres) &&
                            ((epoch_us() - t->last_gvr_request_time_us) > WALLY_DB_PENDING_TXN_CHK_INTVL))) {
                        result = postgres_update_table_state_machine(t, wp_conn);
                        if (result != WALLY_RESULT_WOULD_BLOCK) {
                           /* Boo, hiss... */
                           /* XXX LOG */
                            wally_db_insert_free_conn (wp_db, wp_conn);
                        }
                    }
                }  else if (t->needs_to_be_resynchronized) {
                    wp_conn->state = conn_table_resynchronize;
                    t->state = table_resynchronize;
                    WALLY_DEBUG_POSTGRES("%s: %s: Resynchronizing table: %d", t->db->name, t->db_table_name, wp_conn->my_index);
                    if (wp_db->db_type == db_postgres) {
                        result = postgres_update_table_state_machine(t, wp_conn);
                        if (result != WALLY_RESULT_WOULD_BLOCK) {
                            /* Boo, hiss... */
                            /* XXX LOG */
                            wally_db_insert_free_conn (wp_db, wp_conn);
                        }
                    } else {
                        wp_conn->state = conn_table_write;
                        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                                         sqlt_synchronize_1,
                                         wp_conn,
                                         0);
                   }
                } else if (t->min_valid_sequence_set > t->min_valid_sequence_performed) {
                    /* If we need to delete old rows, delete them... */
                    if (wp_db->db_type == db_postgres) {
                        wp_send_sequence_delete_request(wp_conn);
                    } else {
                        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                                         sqlt_sequence_delete_1,
                                         wp_conn,
                                         0);
                    }
                } else if (wp_db->db_type == db_postgres
                        &&t->state == table_ready
                        && t->min_seq_auto_update
                        && t->min_squence_last != t->min_valid_sequence_set
                        && now - t->min_sequence_last_us > WP_UPDATE_MIN_SEQ_US) {
                    wally_postgres_update_min_seq(wp_conn);
                } else if (wp_db->db_type == db_postgres
			               && t->state == table_ready
			               && t->cleanup_state != cleanup_idle ) {

                    WALLY_DEBUG_TABLE_CLEANUP("Table cleanup, db: %s, table: %s, sequence: %" PRId64 ", key_int: %" PRId64,
                                              t->db->name,
                                              t->db_table_name,
                                              t->cleanup_buf[t->cleanup_index].sequence,
                                              t->cleanup_buf[t->cleanup_index].key_int);

                    if (t->cleanup_state == cleanup_start) {
                        wally_postgres_do_cleanup(wp_conn);
                        /* Tick the HB after cleanup */
                        zthread_heartbeat(wp_db->zthread);
                    } else {
                        if (now - t->cleanup_process_start_us >= WP_MAX_CLEANUP_PROCESS_US) {
                            /* this is abnormal, reset it so it will not block in state "cleanup_in_process" forever */
                            WALLY_LOG(AL_CRITICAL, "Table cleanup hangup, db: %s, table: %s, sequence: %" PRId64 ", key_int: %" PRId64,
                                                   t->db->name,
                                                   t->db_table_name,
                                                   t->cleanup_buf[t->cleanup_index].sequence,
                                                   t->cleanup_buf[t->cleanup_index].key_int);
                            reset_cleanup(t, t->cleanup_buf[t->cleanup_index].sequence);
                        }
                    }
                } else {
                    /* Not apply to to SQLite */
                    /* Enable polling only after loading all the tables during bootup */
                    if ((wp_conn->wp_db->db_type == db_postgres) && (get_wally_app_state() >= wally_state_tables_loaded)) {
                        if ((wp_db->polling_interval_us) && ((now - t->last_read_complete_us) >
                            (t->xpoll_poll_interval_us ? t->xpoll_poll_interval_us : wp_db->polling_interval_us))) {
                            WALLY_DEBUG_POSTGRES_POLL("%s: %s: Polling table", t->db->name, t->db_table_name);
                            /* if xpoll_last_trylock_failed_us is 0, then it is just polling, if it is non-zero, then
                             * this is a re-attempt after earlier try-lock failure, then check for try lock interval.
                             */
                            if ((t->xpoll_last_trylock_failed_us == 0) ||
                                    ((now - t->xpoll_last_trylock_failed_us) > WALLY_PGDB_TRY_LOCK_INTERVAL_US))
                            {
                                wp_db->last_poll_time_us = epoch_us();
                                if (WALLY_DB_GVR_MODE_DISABLED ||  !(wp_db->is_true_origin && !wp_db->is_alterable))
                                {
                                    result =  wp_send_poll_check(wp_conn);
                                }
                                else
                                {
                                    result =  wp_send_gvr_poll_check(wp_conn);
                                }
                                if (!result) {
                                    /* Boo, hiss... */
                                    WALLY_LOG(AL_ERROR, "%s: %s: Could not poll table: %s", t->db->name, t->db_table_name, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
                                    wally_db_insert_free_conn (wp_db, wp_conn);
                                }
                            }
                        }
                    }
                    /* Connection is not returned because above condition will always be true (Top of the function checks this condition first) */
                }
            }
        }
    }

    /* Check for table writes. Yeah, this does a little more queueing
     * than may strictly be required, but that's okay for simplicity
     * for the moment. */

    if (wp_db->db_type == db_sqlt) {
        sqlt_write_object(wp_db);
    } else {
        /* Batch write only for writing to local db */
        if(!wp_db->is_true_origin && wally_gbl_cfg.enable_batch_write) {
            wp_write_batch_object(wp_db);
        } else {
            wp_write_object(wp_db);
        }
		/* Polling is disabled during load time, due to which wally_xfer_resume_xmit is not invoked to take care of
		 * pending registeration.  We need to check during load time if the connections are free set rest is_blocking
		 * such that other tables can continue loading
		 */
		if ((get_wally_app_state() == wally_state_tables_loading) ) {
            wp_conn = TAILQ_FIRST(&(wp_db->free_connections));
			if (wp_conn) {
				wp_db->is_blocking = 0;
				zevent_base_call(wp_db->zevent,
								 do_wally_xfer_resume_xmit,
								 wp_db->db_to_wally_cookie,
								 0);
			} else {
                wp_db->is_blocking = 1;
            }
		}
    }

    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    zthread_heartbeat(wp_db->zthread);

    if ( wp_db->db_type == db_sqlt) {
        //wally_xfer_resume_xmit(wp_db->db_to_wally_cookie);
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                         do_wally_xfer_resume_xmit,
                         wp_db->db_to_wally_cookie,
                         0);
    }
}


int wally_db_update_table_poll_time(char** pool_table_info, int count)
{
    char local_db_val[MAX_TABLE_NAME_LEN];
    char* token;
    int time_s;

    wally_table_poll_time_count = count;
    wtpt = (struct wally_table_poll_time *) WALLY_MALLOC(sizeof(*wtpt)*count);
    if (wtpt == NULL) {
        return WALLY_RESULT_NO_MEMORY ;
    }
    memset(wtpt, 0, (sizeof(*wtpt) * count));
    memset(local_db_val, 0, sizeof(local_db_val));

    for (int i = 0, j=0; i < count;i++) {
        if(pool_table_info[i]) {
            snprintf(local_db_val, MAX_TABLE_NAME_LEN, "%s",pool_table_info[i]);
            token = strchr(local_db_val, ':');
            if (token && strlen(token)>1) {
                token[0]='\0';
                token ++;
                time_s = strtol(token,NULL,10);
                if (time_s < MIN_POLL_TIME || time_s > MAX_POLL_TIME) {
                    WALLY_LOG(AL_WARNING, "Poll time is not correct for %s it should be in range of %d-%d seconds "
                                                                                       ,local_db_val,MIN_POLL_TIME,MAX_POLL_TIME);
                    time_s = POLL_INTERVAL_NA;
                }
                snprintf(wtpt[j].table_name, MAX_TABLE_NAME_LEN, "%s",local_db_val);
                wtpt[j].pool_time_s  = time_s;
                WALLY_LOG(AL_NOTICE, "Wally table poll interval updated name: %s time %d", wtpt[j].table_name,wtpt[j].pool_time_s);
                j++;
            }
        }

    }
    return WALLY_RESULT_NO_ERROR;
}

int64_t wally_db_get_table_poll_time(char * table_name)
{
    int64_t ret = POLL_INTERVAL_NA;
    if (table_name) {
        for (int i =0; i<wally_table_poll_time_count; i++) {
            if (strcmp(wtpt[i].table_name,table_name)==0) {

                //converting to microsecond
                ret = wtpt[i].pool_time_s * 1000000L;
                break;
            }
        }
    }
    return ret;
}

void wally_db_enable_poll_rate_check(struct wp_db *wp_db , int poll_rate_timeout_min)
{
     if(wp_db) {
         ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);
         wp_db->poll_rate_check = 1;
         //converting minutes to microsecond
         wp_db->poll_rate_timeout_us = poll_rate_timeout_min * 60 * 1000000L;
         ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
     }
}

/* Function : wally_db_cmd_execute
 * Arg      : callout_cookie - wp_db object
 *            table_name - table name
 *            cmd_id    - command id
 *            param1 - parameter for the command
 * Ret      :  WALLY_RESULT_NO_ERROR if success.
 * Desc     : This function executes command initated from wally layer.
 *                  WALLY_SET_DB_TABLE_POLL - stops polling for the table
 */
int wally_db_cmd_execute(void *callout_cookie, const char* table_name, int cmd_id, void *param1)
{
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    struct wp_table *t;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        WALLY_LOG(AL_ERROR, " table %s not found", table_name);
        return WALLY_RESULT_ERR;
    }
    switch (cmd_id)
    {
        case WALLY_SET_DB_TABLE_POLL:
        {
            int poll_disable = *(int *) param1;
            /* No impact to polling timeout, as poll timeout is per db and not at table level */
            /* Recovery can be started for all tables but not a real scenario */
            t->stop_polling_table = poll_disable;
        }
        break;
        default:
        {
            WALLY_LOG(AL_ERROR, "Invalid DB layer command");
        }
        break;
    }
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);

    return WALLY_RESULT_NO_ERROR;
}
