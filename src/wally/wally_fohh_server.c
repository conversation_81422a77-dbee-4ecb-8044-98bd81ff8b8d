/*
 * wally_fohh_server.c Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Export a wally via FOHH to an arbitrary number of clients.
 */
#define _GNU_SOURCE

#include "wally/wally_fohh_server.h"

#include <pthread.h>
#include <stdio.h>
#include <string.h>
#include <sys/queue.h>

#include "argo/argo_hash.h"
#include "wally/wally.h"
#include "wally/wally_column_nullify.h"
#include "wally/wally_filter_table.h"
#include "wally/wally_fohh.h"
#include "wally/wally_private.h"
#include "zpath_misc/zpath_misc.h"
#include "fohh/fohh_private.h"
#include "wally/wally_table_queue.h"

/* Minimum version of wally_fohh_client, from which the client will be
 * considered compatible . The minimum required version needs to be
 * updated as per new dependencies across releases
 */
int32_t VERSION_MAJOR_MIN = _WALLY_VERSION_MAJOR_MIN_;
int32_t VERSION_MINOR_MIN = _WALLY_VERSION_MINOR_MIN_;
int32_t VERSION_PATCH_MIN = _WALLY_VERSION_PATCH_MIN_;

LIST_HEAD(wally_fohh_server_client_head, wally_fohh_server_client);
LIST_HEAD(wally_fohh_registrant_head, wally_fohh_registrant);
ZTAILQ_HEAD(wally_fohh_recovery_feed_head, wally_client_recovery_callback);

/*
 * Per-table, per-client registrant information.
 */
struct wally_fohh_registrant {
    struct wally_fohh_server_client *client;
    struct wally_registrant *registrant;
    struct wally_table *table;
    LIST_ENTRY(wally_fohh_registrant) all_registrants;
};

/* Wally client Drain status */
enum wally_server_client_drain_status {
    WALLYC_DRAIN_NONE,         /* No status */
    WALLYC_DRAIN_MARKED_P1,    /* Marked for high priority drain; Fohh reset request not initiated */
    WALLYC_DRAIN_MARKED_P2,    /* Marked for low priority drain; Fohh reset request not initiated */
    WALLYC_DRAIN_INITATED,     /* FOHH reset request initiated and waiting for cleanup callback */
    WALLYC_DRAIN_DESTROY_PEND, /* Cleanup callback received, but drain in progress. So
                                 cannot free the memory. wally_cleanup thread destroys
                                 the memory once drain is completed. */
    WALLYC_DRAIN_INVALID
};

volatile enum wally_drain_status drain_status = WALLY_DRAIN_NONE;
volatile bool wally_client_reject = false; /* Set to true to reject the client connection */

struct wally_client_recovery_callback {
    struct argo_object *row_object;
    struct wally_table *table;
    ZTAILQ_ENTRY(wally_client_recovery_callback) feed_list;

};
/*
 * Per-client-connection state. (connected to our server_
 *
 * Used to associate an FOHH with a wally.
 *
 * per_table_registrant - Registrants are created per-table. For each
 * table that is queried, we create one.
 */
struct wally_fohh_server_client {
    struct wally_fohh_server *server;
    struct fohh_connection *client_connection;
    struct argo_hash_table *per_table_registrant;  /* Contains wally_fohh_registrant's */
    struct wally_fohh_registrant_head all_registrants;
    uint32_t num_tables_registered;  /* Number of tables registered */
    uint32_t num_row_registered;     /* Total number of row */
    uint32_t num_null_registered;    /* Total of NULL registered */

    pthread_mutex_t lock_inbound;
    pthread_mutex_t lock_outbound;

    int64_t filter_mask;
    int64_t filter_value;

    int64_t pass_key;

    struct argo_hash_table *filter_tables;
    struct argo_hash_table *allow_tables;

    int is_dead; /* fohh/libevent likes to send us extra events, so we
                  * only really punt the connection after a few
                  * seconds. */
    enum wally_server_client_drain_status drain_status;

    struct zhash_table* table_filter;
    struct zhash_table *nullify_state;

    fohh_connection_callback_f  *disconnect_callback;

    /* 1 if we have received version message */
    int version_message_received;
    int64_t count_in_wally_layer_queue;

    struct wally_fohh_recovery_feed_head recovery_feed_list;
    int client_capabilities;

    /* List of all the clients connected to this server */
    LIST_ENTRY(wally_fohh_server_client) all_client_connections;
    int is_deferred_for_destroy;
};

struct wally_fohh_server {
    struct wally *wally;

    uint16_t tcp_port_ne;

    pthread_mutex_t lock_server;

    /* Server connection state (For someone else to connect to us) */
    struct sockaddr_in server_sin;
    struct sockaddr_in6 server_sin6;
    struct fohh_connection *server_ipv6;
    struct fohh_connection *server_ipv4;

    /* Set of argo descriptions for interface functionality. */
    struct argo_structure_description *register_row_string;
    struct argo_structure_description *deregister_row_string;
    struct argo_structure_description *register_row_integer;
    struct argo_structure_description *deregister_row_integer;
    struct argo_structure_description *request_result;
    struct argo_structure_description *recovery_begin_request;
    struct argo_structure_description *recovery_end_request;
    struct argo_structure_description *recovery_complete_response;

    struct argo_structure_description *version;
    struct argo_structure_description *version_ack;

    /* List of all clients connected to us */
    struct wally_fohh_server_client_head all_client_connections;
};

static void wally_fohh_server_recovery_list_reset(struct wally_fohh_server_client *wfc);
static void wally_fohh_server_process_recovery_pending_list(struct wally_fohh_server_client *wfc);
static int wally_fohh_client_recovery_complete_response_callback(void *cookie, void *structure_cookie, struct argo_object *object);
#define WALLY_FOHH_COMPARE(domain, port, dest_port, f_conn)  ((!domain ||      \
                     (strcasestr(f_conn->peer_common_name, domain) ||    \
                        (strcasestr(f_conn->remote_address_name, domain)))) && \
                (!port ||  (!strncmp(port, dest_port, strlen(port)))))

static inline void wfs_lock(struct wally_fohh_server *wfs)
{
    pthread_mutex_lock(&(wfs->lock_server));
}
static inline void wfs_unlock(struct wally_fohh_server *wfs)
{
    pthread_mutex_unlock(&(wfs->lock_server));
}
static inline void wfc_lock_inbound(struct wally_fohh_server_client *wfc)
{
    pthread_mutex_lock(&(wfc->lock_inbound));
}
static inline void wfc_unlock_inbound(struct wally_fohh_server_client *wfc)
{
    pthread_mutex_unlock(&(wfc->lock_inbound));
}
static inline void wfc_lock_outbound(struct wally_fohh_server_client *wfc)
{
    pthread_mutex_lock(&(wfc->lock_outbound));
}
static inline void wfc_unlock_outbound(struct wally_fohh_server_client *wfc)
{
    pthread_mutex_unlock(&(wfc->lock_outbound));
}

/*  Client-disconnect/Offline is set. Reject the client connection */
void wally_disable_client_access()
{
    wally_client_reject = true;
}

/*  Client-connect/Active is set. Reset the variable */
void wally_enable_client_access()
{
    wally_client_reject = false;
}

int unknown_rx_object_callback(void *argo_cookie_ptr,
                               void *argo_structure_cookie_ptr,
                               struct argo_object *object)
{
    struct wally_fohh_server_client *wfc = argo_cookie_ptr;
    struct wally_fohh_server *wfs = wfc->server;
    char *table_name = argo_object_get_type(object);
    struct wally_table *table;
    struct argo_object *copy;

    if (!table_name) {
        WALLY_LOG(AL_WARNING, "Row update received without known type??");
        return FOHH_RESULT_ERR;
    }
    table = wally_table_get(wfs->wally, table_name);
    if (!table) {
        WALLY_LOG(AL_WARNING, "Received row update for table %s, but table does not exist",
                  table_name);
        return FOHH_RESULT_ERR;
    }
    /* argo callback objects only exist on stack, so copy it, here, so
     * it can be ref counted, etc. */
    copy = argo_object_copy(object);
    if (!copy) {
        WALLY_LOG(AL_CRITICAL, "Could not copy object");
        return FOHH_RESULT_ERR;
    }

    /* Purposely ignoring error cases, which we don't want to
     * propagate downstream. */
    wally_table_write_origin_row(table, copy);

    argo_object_release(copy);
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Callback from wally (database) with row updates, based on
 * registrations made by some client.
 */
static int wally_row_callback_f_for_client(void *cookie,
                                           struct wally_registrant *registrant,
                                           struct wally_table *table,
                                           struct argo_object *previous_row,
                                           struct argo_object *row,
                                           int64_t request_id)
{
    struct wally_fohh_registrant *wfr = (struct wally_fohh_registrant *) cookie;
    int res;
    int64_t tmp_id;
    struct argo_object *copy = NULL;
    char object_str[1000];

    if (wally_debug & WALLY_DEBUG_ROW_DETAIL_BIT) {
        if (argo_object_dump(row, object_str, sizeof(object_str), NULL, 0)) {
            object_str[0] = 0;
        }
        WALLY_LOG(AL_DEBUG, "%s: Row callback ID = %ld, row = %s", fohh_description(wfr->client->client_connection), (long) request_id, object_str);
    }

    if(registrant && registrant->table && registrant->table->wally) {
        if( registrant != table->default_registrant ) {
           __sync_add_and_fetch_8(&(registrant->table->wally->registrant_stat.row_notification_count),1);
        }
    }

    (void) previous_row;
    tmp_id = argo_object_read_request_id(row);
    if (tmp_id && (tmp_id != request_id)) {
        copy = argo_object_copy(row);
        argo_object_set_request_id(copy, request_id);
        if (__sync_add_and_fetch_8(&(wfr->client->is_dead),0 )) {
            res = ARGO_RESULT_NO_ERROR;
            WALLY_LOG(AL_WARNING, "Tried sending row copy; But client connection is gone");
        }else {
            res = fohh_argo_serialize_object(wfr->client->client_connection, copy, 0, fohh_queue_element_type_control);
        }
    } else {
        if (tmp_id != request_id) {
            argo_object_set_request_id(row, request_id);
        }
        if (__sync_add_and_fetch_8(&(wfr->client->is_dead),0 )) {
            res = ARGO_RESULT_NO_ERROR;
            WALLY_LOG(AL_WARNING, "Tried sending row; But client connection is gone");
        }else {
            res = fohh_argo_serialize_object(wfr->client->client_connection, row, 0, fohh_queue_element_type_control);
        }
    }
    if (res == ARGO_RESULT_BAD_ARGUMENT) {
        /* XXX LOG. We haven't registered this object type with argo yet. */
        WALLY_LOG(AL_ERROR, "Client = %s, Unrecognized object type, row = %s\n",
                  fohh_description(wfr->client->client_connection),
                  argo_object_get_type(row));

        if(registrant && registrant->table && registrant->table->wally ) {
            if( registrant != table->default_registrant ) {
                __sync_add_and_fetch_8(&(registrant->table->wally->registrant_stat.row_notification_failed_count),1);
            }
        }
    } else {
        //fprintf(stdout, "%s:%s: Xmit row returned: %s\n", __FILE__, __FUNCTION__, wally_error_strings[res]);
        if ((res != ARGO_RESULT_WOULD_BLOCK) &&
            (res != ARGO_RESULT_NO_ERROR)) {
            /* XXX LOG */
        }
    }

    if (copy) argo_object_release(copy);
    return res;
}

/* Displaying drain_status string */
const char *wally_server_drain_status_strings[] = {
    [WALLYC_DRAIN_NONE]         = "none",
    [WALLYC_DRAIN_MARKED_P1]    = "marked p1",
    [WALLYC_DRAIN_MARKED_P2]    = "marked p2",
    [WALLYC_DRAIN_INITATED]     = "initiated",
    [WALLYC_DRAIN_DESTROY_PEND] = "destroy pending",
    [WALLYC_DRAIN_INVALID]      = "",
};

/* Display strings for */
const char *wally_server_get_drain_status_string(enum wally_server_client_drain_status drain_status)
{
    if (drain_status > WALLYC_DRAIN_INVALID || !wally_server_drain_status_strings[drain_status])
        return "";
    return wally_server_drain_status_strings[drain_status];
}

/*
 * Callback from wally (database) with a registration request complete
 * indication. (response).
 *
 * So this is a wally->fohh(acting as a service for multiple clients)
 * callback.
 */
static int wally_result_callback_f_for_client(void *cookie,
                                              struct wally_registrant *registrant,
                                              struct wally_table *table,
                                              int64_t request_id,
                                              int row_count)
{
    struct wally_fohh_registrant *wfr = (struct wally_fohh_registrant *) cookie;
    struct wally_fohh_request_result r;
    int res;

    WALLY_LOG(AL_NOTICE, "%s: %s: ID = %ld, Sent %d rows to client for table %s table exists = %d",
              table->wally->name,
              fohh_description(wfr->client->client_connection),
              (long) request_id,
              row_count,
              table->name,
			  table->exists);

    r.request_id = request_id;
    r.row_count = row_count;
    r.table_name = table->name;
    r.table_exists = table->exists;

    res = fohh_argo_serialize(wfr->client->client_connection, wfr->client->server->request_result, &r, 0,
                              fohh_queue_element_type_control);
    if (res == ARGO_RESULT_BAD_ARGUMENT) {
        /* XXX LOG. We haven't registered this object type with this argo yet. */
        WALLY_LOG(AL_ERROR, "Could not serialize: %s", argo_result_string(res));
    } else {
        if ((res != ARGO_RESULT_WOULD_BLOCK) &&
            (res != ARGO_RESULT_NO_ERROR)) {
            WALLY_LOG(AL_ERROR, "Could not serialize: %s", argo_result_string(res));
        }
    }
    return res;
}


/*
 * Must be called with lock held, in order to protect argo.
 */
static struct wally_fohh_registrant *wally_fohh_client_get_table(struct wally_fohh_server_client *wfc, char *table_name)
{
    struct wally_fohh_registrant *wfr;
    struct wally *wally = wfc->server->wally;
    int res;

    wfr = argo_hash_lookup(wfc->per_table_registrant, table_name, strlen(table_name), NULL);
    if (!wfr) {
        struct wally_table *table;

        wfr = (struct wally_fohh_registrant *) WALLY_MALLOC(sizeof(*wfr));
        memset(wfr, 0, sizeof(*wfr));

        wfr->client = wfc;

        table = wally_table_get(wally, table_name);
        if (!table) {
            WALLY_FREE(wfr);
            /* XXX LOG */
            return NULL;
        }

        wfr->table = table;

        wfr->registrant = wally_table_create_registrant(table, wally_row_callback_f_for_client, wfr, fohh_description(wfc->client_connection));
        if (!wfr->registrant) {
            WALLY_FREE(wfr);
            /* XXX LOG */
            return NULL;
        }

        if ((res = argo_hash_store(wfc->per_table_registrant, table_name, strlen(table_name), 0, wfr))) {
            /* XXX LOG */
            wally_table_destroy_registrant(wfr->registrant);
            WALLY_FREE(wfr);
            return NULL;
        }

        /* Need to register this table type with our outbound argo instance. */
        /* XXX Need to modify this in some manner such that it will
         * not deadlock. In the meantime, need to register all rows up
         * front */
        //argo_register_structure(fohh_argo_get_tx(wfc->client_connection), table->argo_description, NULL, NULL);

        LIST_INSERT_HEAD(&(wfc->all_registrants), wfr, all_registrants);
        wfc->num_tables_registered++;
    }
    return wfr;
}

/*
 * Routine to update the client register stats */
static void wally_server_client_register_stats (void *cookie, bool inc, int is_null)
{
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) cookie;

    if (wfc == NULL) {
        WALLY_LOG(AL_ERROR, "WFC Pointer NULL");
        return;
    }

    if (is_null) {
        if (inc) {
            wfc->num_null_registered++;
        } else {
            wfc->num_null_registered--;
        }
    } else {
        if (inc) {
            wfc->num_row_registered++;
        } else {
            wfc->num_row_registered--;
        }
    }
}
static inline void inc_wfc_count(void* wfc_in) {
        struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) wfc_in;
        wfc_lock_inbound(wfc);
        wfc_lock_outbound(wfc);
        wfc->count_in_wally_layer_queue++;
        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);

}
static inline void dec_wfc_count(void* wfc_in) {
        struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) wfc_in;
        wfc_lock_inbound(wfc);
        wfc_lock_outbound(wfc);
        wfc->count_in_wally_layer_queue--;
        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);

}
/*
 * Call(back) from one of our clients, via FOHH, registering for a row
 */
static int client_register_row_string_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    if (IS_WALLY_LAYER_ENABLED) {
        inc_wfc_count(structure_cookie);
        wally_table_queue_enqueue_fohh_client_register_row_string(cookie, structure_cookie, object);
    } else {
        return client_register_row_string_callback_internal(cookie, structure_cookie, object);
    }
    return WALLY_RESULT_NO_ERROR;
}
/*
 * Wally thread processing function
 */
int client_register_row_string_callback_internal(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_register_row_request_string *req = (struct wally_fohh_register_row_request_string *) object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;

    int res = WALLY_RESULT_NO_ERROR;

    if (__sync_add_and_fetch(&(wfc->is_dead),0)) {
        WALLY_LOG(AL_CRITICAL, "Client connection disconnected before row registration could be completed!");
        res = WALLY_RESULT_ERR;
        goto end;
    }

    struct wally_index_column *column;
    struct wally_fohh_registrant *wfr;
    int filtered = 0;

    if (!wfc->version_message_received) {
        /* Wally clients connected without sending version message
         * are not considered for version incompatibility check. */
        WALLY_DEBUG_REGISTRATION("%s: Message received without version", fohh_description(wfc->client_connection));
    }

    /* Drain in progress for this client. So skipping the register . Anyway, connection will be
     * reset.
     * */
    if (wfc->drain_status) {
        WALLY_DEBUG_REGISTRATION("Register client drain status %s",
                wally_server_get_drain_status_string(wfc->drain_status));
        res = WALLY_RESULT_NO_ERROR;
        goto end;
    }

    /* Look up registrant for this table.  Might be able to get away
     * with only locking inbound, but we'll do both for now. Note that
     * wally calls are threadsafe already. */
    wfc_lock_inbound(wfc);
    wfc_lock_outbound(wfc);
    wfr = wally_fohh_client_get_table(wfc, req->table_name);
    wfc_unlock_outbound(wfc);
    wfc_unlock_inbound(wfc);

    if (!wfr) {
        /* XXX LOG. BUT Don't kill conn! Just ignore the request! */
        WALLY_LOG(AL_ERROR, "Could not get table %s:%s:%s", req->table_name, req->column_name ? req->column_name : "null", req->key ? req->key : "null");
        res = WALLY_RESULT_NO_ERROR;
        goto end;
    }

    /* Look up column. */
    column = wally_table_get_index(wfr->table, req->column_name);
    if (!column) {
        /* XXX LOG. BUT Don't kill conn! Just ignore the request! */
        WALLY_LOG(AL_ERROR, "Could not get column %s:%s:%s", req->table_name, req->column_name ? req->column_name : "null", req->key ? req->key : "null");
		res = WALLY_RESULT_NO_ERROR;
		goto end;
    }

    if (wfc->filter_tables) {
        if (!argo_hash_lookup(wfc->filter_tables, req->table_name, strlen(req->table_name), NULL)) {
            if (wfc->allow_tables) {
                if (argo_hash_lookup(wfc->allow_tables, req->table_name, strlen(req->table_name), NULL)) {
                    /* Not in filter table, is in allow table. Allowed for all purposes. */
                    filtered = 0;
                } else {
                    /* Not in filter table, not in allow table. Not allowed at all. */
                    WALLY_LOG(AL_ERROR, "%s: Request for table %s did not pass allow/filter check", fohh_description(wfc->client_connection), req->table_name);
                    res = WALLY_RESULT_NO_ERROR;
                    goto end;
                }
            } else {
                /* Not in filter table, no allow table. Not allowed at all. */
                WALLY_LOG(AL_ERROR, "%s: Request for table %s did not pass allow/filter check", fohh_description(wfc->client_connection), req->table_name);
                res = WALLY_RESULT_NO_ERROR;
                goto end;
            }
        } else {
            /* Exists in filter table. */
            filtered = 1;
        }
    } else {
        /* No filter table */
        if (wfc->allow_tables) {
            if (argo_hash_lookup(wfc->allow_tables, req->table_name, strlen(req->table_name), NULL)) {
                /* Not in filter table, is in allow table. Allowed for all purposes. */
                filtered = 0;
            } else {
                /* No filter table, not in allow table, drop. */
                WALLY_LOG(AL_ERROR, "%s: Request for table %s did not pass allow/filter check", fohh_description(wfc->client_connection), req->table_name);
                res = WALLY_RESULT_NO_ERROR;
                goto end;
            }
        } else {
            /* No filter or allow table. */
            if (wfc->filter_value) {
                filtered = 1;
            } else {
                filtered = 0;
            }
        }
    }

    WALLY_LOG(AL_NOTICE, "%s: %s: ID = %ld, %s Request for seq=%ld:%s %s %s",
              column->table->wally->name,
              fohh_description(wfc->client_connection),
              (long)req->request_id,
              filtered ? "F" : " ",
              (long)req->sequence,
              req->table_name,
              req->column_name,
              req->key);

    /* Register our interest. */
    if (column->data_type == argo_field_data_type_string) {
        if (filtered) {
            WALLY_LOG(AL_ERROR, "Attempt to query string %s:%s:%s with filter in place", req->table_name, req->column_name ? req->column_name : "null", req->key ? req->key : "null");
            res = WALLY_RESULT_ERR;
            goto end;
        }
        res = wally_table_register_for_row_filtered(wfr->registrant,
                                                    column,
                                                    req->key,
                                                    req->key ? strlen(req->key) : 0,
                                                    req->request_id,
                                                    req->sequence,
                                                    0,
                                                    0,
                                                    0,
                                                    wally_result_callback_f_for_client,
                                                    wfr,
                                                    wfc->table_filter,
                                                    wfc->nullify_state);
    } else if (column->data_type == argo_field_data_type_integer) {
        int64_t key;
        key = strtoll(req->key, NULL, 0);
        if (filtered) {
            if ((key & wfc->filter_mask) == wfc->filter_value || (wfc->pass_key && key == wfc->pass_key)) {
              WALLY_DEBUG_REGISTRATION("Attempt to query int %s:%s:%s PASSED filter %ld",
                                         req->table_name,
                                         req->column_name ? req->column_name : "null",
                                         req->key ? req->key : "null",
                                         (long) wfc->filter_value);
            } else {
                WALLY_LOG(AL_ERROR, "Attempt to query int %s:%s:%s but did not pass filter %ld",
                          req->table_name,
                          req->column_name ? req->column_name : "null",
                          req->key ? req->key : "null",
                          (long) wfc->filter_value);
                res = WALLY_RESULT_ERR;
                goto end;
            }
        }
        res = wally_table_register_for_row_filtered(wfr->registrant,
                                                    column,
                                                    &key,
                                                    sizeof(key),
                                                    req->request_id,
                                                    req->sequence,
                                                    0,
                                                    0,
                                                    0,
                                                    wally_result_callback_f_for_client,
                                                    wfr,
                                                    wfc->table_filter,
                                                    wfc->nullify_state);
    } else if ((column->data_type == argo_field_data_type_binary) &&
               wally_column_is_inet(column)){
        struct argo_inet inet;
        if (filtered) {
            WALLY_LOG(AL_ERROR, "Attempt to query inet %s:%s:%s with filter in place", req->table_name, req->column_name ? req->column_name : "null", req->key ? req->key : "null");
            res = WALLY_RESULT_ERR;
            goto end;
        }
        res = argo_string_to_inet(req->key, &inet);
        if (res) {
            WALLY_LOG(AL_ERROR, "Error parsing inet <%s>\n", req->key);
            res = WALLY_RESULT_ERR;
            goto end;
        }
        res = wally_table_register_for_row_filtered(wfr->registrant,
                                                    column,
                                                    &inet,
                                                    sizeof(inet),
                                                    req->request_id,
                                                    req->sequence,
                                                    0,
                                                    0,
                                                    0,
                                                    wally_result_callback_f_for_client,
                                                    wfr,
                                                    wfc->table_filter,
                                                    wfc->nullify_state);
    } else if (column->is_null) {
        if (filtered) {
            WALLY_LOG(AL_ERROR, "Attempt to query null column %s:%s:%s with filter in place", req->table_name, req->column_name ? req->column_name : "null", req->key ? req->key : "null");
            res = WALLY_RESULT_ERR;
            goto end;
        }
        res = wally_table_register_for_row_filtered(wfr->registrant,
                                                    column,
                                                    NULL,
                                                    0,
                                                    req->request_id,
                                                    req->sequence,
                                                    0,
                                                    0,
                                                    0,
                                                    wally_result_callback_f_for_client,
                                                    wfr,
                                                    wfc->table_filter,
                                                    wfc->nullify_state);
    } else {
        /* XXX LOG */
        WALLY_LOG_NOT_IMPLEMENTED();
        res = WALLY_RESULT_NOT_IMPLEMENTED;
        goto end;
    }

    if (res & (res != WALLY_RESULT_ASYNCHRONOUS)) {
        WALLY_LOG(AL_ERROR, "Register for row failed");
        /* XXX LOG */
    } else {
        res = WALLY_RESULT_NO_ERROR;
        /* Registration is successful. Increment the stats */
        wally_server_client_register_stats (wfc, true, column->is_null);
    }

    end:
	if (IS_WALLY_LAYER_ENABLED) {
		argo_object_release(object);
        dec_wfc_count(wfc);
	}
    return res;
}

/*
 * Call(back) from one of our clients, via FOHH, deregistering for a row
 */
static int client_deregister_row_string_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    if (IS_WALLY_LAYER_ENABLED) {
        inc_wfc_count(structure_cookie);
        wally_table_queue_enqueue_fohh_client_deregister_row_string(cookie, structure_cookie, object);
    } else {
        return client_deregister_row_string_callback_internal(cookie, structure_cookie, object);
    }
    return WALLY_RESULT_NO_ERROR;
}

int client_deregister_row_string_callback_internal(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_deregister_row_request_string *req = (struct wally_fohh_deregister_row_request_string *) object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;
    struct wally_index_column *column;
    struct wally_fohh_registrant *wfr;
    int res = WALLY_RESULT_NO_ERROR;

    if (__sync_add_and_fetch(&(wfc->is_dead),0)) {
        WALLY_LOG(AL_CRITICAL, "Client connection disconnected before row deregistration could be completed!");
        res = WALLY_RESULT_ERR;
        goto end;
    }
    if (!wfc->version_message_received) {
        /* Wally clients connected without sending version message
         * are not considered for version incompatibility check. */
        WALLY_DEBUG_REGISTRATION("%s: Message received without version", fohh_description(wfc->client_connection));
    }

    /* Drain in progress for this client. So skipping the deregister . Anyway, connection will be
     * reset.
     * */
    if (wfc->drain_status) {
        WALLY_DEBUG_REGISTRATION("Deregister client drain status %s, cannot proceed",
                wally_server_get_drain_status_string(wfc->drain_status));
        res = WALLY_RESULT_NO_ERROR;
        goto end;
    }

    /* Look up registrant for this table.  Might be able to get away
     * with only locking inbound, but we'll do both for now. Note that
     * wally calls are threadsafe already. */
    wfc_lock_inbound(wfc);
    wfc_lock_outbound(wfc);
    wfr = wally_fohh_client_get_table(wfc, req->table_name);
    wfc_unlock_outbound(wfc);
    wfc_unlock_inbound(wfc);

    if (!wfr) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "Could not get registrant");
        res = WALLY_RESULT_ERR;
        goto end;
    }

    /* Look up column. */
    column = wally_table_get_index(wfr->table, req->column_name);
    if (!column) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "Could not get column");
        res = WALLY_RESULT_ERR;
        goto end;
    }

    WALLY_LOG(AL_NOTICE,
              "%s: %s: Deregister row request for %s %s %s",
              column->table->wally->name,
              fohh_description(wfc->client_connection),
              req->table_name,
              req->column_name,
              req->key);

    /* Deregister our interest. */
    if (column->data_type == argo_field_data_type_string) {
        res = wally_table_deregister_for_row(wfr->registrant, column, req->key, strlen(req->key));
    } else if (column->data_type == argo_field_data_type_integer) {
        int64_t key;
        key = strtoll(req->key, NULL, 0);
        res = wally_table_deregister_for_row(wfr->registrant, column, &key, sizeof(key));
    } else if ((column->data_type == argo_field_data_type_binary) &&
               wally_column_is_inet(column)){
        struct argo_inet inet;
        res = argo_string_to_inet(req->key, &inet);
        if (res) {
            WALLY_LOG(AL_ERROR, "Error parsing inet <%s>", req->key);
            return WALLY_RESULT_ERR;
        }
        res = wally_table_deregister_for_row(wfr->registrant, column, &inet, sizeof(inet));
    } else {
        res = WALLY_RESULT_ERR;
    }

    if (res) {
        WALLY_LOG(AL_ERROR, "Register for row failed: %d(%s)", res, argo_result_string(res));
        /* XXX LOG */
    } else {
        /* Deregistration is successful. Decrement the stats */
        wally_server_client_register_stats (wfc, false, column->is_null);
    }

    end:
	if (IS_WALLY_LAYER_ENABLED) {
		argo_object_release(object);
        dec_wfc_count(wfc);
	}

    return res;
}

static int client_register_row_integer_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_register_row_request_integer *res = (struct wally_fohh_register_row_request_integer *) object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;

    if (!wfc->version_message_received) {
        /* Wally clients connected without sending version message
         * are not considered for version incompatibility check. */
        WALLY_DEBUG_REGISTRATION("%s: Message received without version", fohh_description(wfc->client_connection));
    }

    fprintf(stdout,
            "wally_fohh: Received from client: Row request for %ld:%s %s %ld\n",
            (long)res->request_id,
            res->table_name,
            res->column_name,
            (long)res->key);
    return WALLY_RESULT_NO_ERROR;
}

static int client_deregister_row_integer_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_deregister_row_request_integer *res = (struct wally_fohh_deregister_row_request_integer *) object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;

    if (!wfc->version_message_received) {
        /* Wally clients connected without sending version message
         * are not considered for version incompatibility check. */
        WALLY_DEBUG_REGISTRATION("%s: Message received without version", fohh_description(wfc->client_connection));
    }

    fprintf(stdout,
            "wally_fohh: Received from client: Row deregister request for %s %s %ld\n",
            res->table_name,
            res->column_name,
            (long)res->key);
    return WALLY_RESULT_NO_ERROR;
}

static int client_version_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_version *ver = (struct wally_fohh_version *)object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;
    struct wally_fohh_server *wfs = wfc->server;
    struct wally *wally = wfs->wally;
    struct wally_fohh_version_ack ver_ack;

    /* Drain in progress for this client. So skip the check. Anyway, connection will be
     * reset.
     * */
    if (wfc->drain_status) {
        WALLY_DEBUG_REGISTRATION("Version callback client drain status %s, cannot proceed",
                wally_server_get_drain_status_string(wfc->drain_status));
        return WALLY_RESULT_NO_ERROR;
    }

    wfc->client_capabilities = ver->client_capabilities;

    wfc->version_message_received = 1;

    WALLY_DEBUG_REGISTRATION("wally_fohh: Received version from client: %s\n", ver->ver_str);

    if ((ver->version_major > VERSION_MAJOR_MIN) ||
       ((ver->version_major == VERSION_MAJOR_MIN) && (ver->version_minor > VERSION_MINOR_MIN)) ||
       ((ver->version_major == VERSION_MAJOR_MIN) && (ver->version_minor == VERSION_MINOR_MIN) && (ver->version_patch >= VERSION_PATCH_MIN))) {
        ver_ack.error = NULL;
    } else {
        WALLY_LOG(AL_WARNING, "%s: client version %d.%d.%d is below the minimal required version %d.%d.%d\n",
                    fohh_description(wfc->client_connection),
                    ver->version_major, ver->version_minor, ver->version_patch,
                    VERSION_MAJOR_MIN, VERSION_MINOR_MIN, VERSION_PATCH_MIN);
        if (wally->drop_incompatible_version) {
        /* Un-register argo structures for receiving requests */
            struct argo_state *argo;

            argo = fohh_argo_get_rx(wfc->client_connection);
            argo_register_structure(argo, wfc->server->register_row_string, NULL, NULL);
            argo_register_structure(argo, wfc->server->deregister_row_string, NULL, NULL);
            argo_register_structure(argo, wfc->server->register_row_integer, NULL, NULL);
            argo_register_structure(argo, wfc->server->deregister_row_integer, NULL, NULL);

            /* If we come here, then the version falls below the minimal required version */
            WALLY_LOG(AL_WARNING, "%s: Incompatible client version %d.%d.%d . Dropping the connection\n",
                    fohh_description(wfc->client_connection),
                    ver->version_major, ver->version_minor, ver->version_patch);
            ver_ack.error = CLIENT_VERSION_TOO_OLD;

        } else {
            ver_ack.error = NULL;
        }
    }
    /* Send version_ack back to wally client */
    fohh_argo_serialize(wfc->client_connection, wfc->server->version_ack, &ver_ack, 0, fohh_queue_element_type_control);

    return WALLY_RESULT_NO_ERROR;
}
/*
 *  wally_get_client_port
 *  Get the client port from the fohh description string
 */
static int wally_get_client_port(char *desc, char *port, uint32_t len) {
    const char *delimiters = ":;";
    char *token = NULL;
    char buffer[WALLY_LOG_FILE_LEN] = {0};
    int pos = 0;

    if (desc == NULL || port == NULL) {
        return WALLY_RESULT_ERR;
    }

    /* Copy the desc into a buffer to use with strtok */
    memset (buffer, 0, sizeof (buffer));
    snprintf(buffer, sizeof(buffer), "%s", desc);
    buffer[sizeof(buffer) - 1] = '\0';

    /* Tokenize the buffer using the delimiters */
    token = strtok(buffer, delimiters);
    while (token != NULL) {
        pos++;
        /* In fohh description, port is in position 5 */
        if (pos == WALLY_PORT_POS) {
            snprintf(port, len, "%s", token);
            return WALLY_RESULT_NO_ERROR;
        }
        token = strtok(NULL, delimiters);
    }
    return WALLY_RESULT_ERR;
}

/* Get timestamp */
static void
wally_get_timestamp(char *buf, uint32_t buf_len) {
    struct timeval tv;
    struct tm tm;

    if (buf == NULL) {
        return;
    }

    memset (&tm, 0, sizeof (tm));

    gettimeofday(&tv, NULL);
    gmtime_r(&(tv.tv_sec), &tm);
    snprintf(buf, buf_len, "%04d-%02d-%02d.%02d:%02d:%02d.%06d",
             tm.tm_year + 1900,
             tm.tm_mon + 1,
             tm.tm_mday,
             tm.tm_hour,
             tm.tm_min,
             tm.tm_sec,
             (int)tv.tv_usec);

    return;
}

/*
 *  wally_server_client_list
 *
 *  Read the server's client connection and redirect details to given file
 */
int wally_server_client_list (struct wally_fohh_server *wfs, FILE *fp,
                              const char *domain, const char *port,
                              uint32_t *count)
{
    struct fohh_connection *f_conn = NULL;
    struct wally_fohh_server_client *tmp_c = NULL, *wfc = NULL;
    char wfc_port[WALLY_PORT_LEN] = {0};
    int res = 0;

    if (wfs == NULL || fp == NULL || count == NULL) {
        WALLY_LOG(AL_ERROR, "WFS/count/File pointer is NULL");
        return FOHH_RESULT_BAD_ARGUMENT;
    }

    /* Wallyd has 2 servers, so print the header only for the first one */
    if (*count == 0) {
        fprintf(fp, "\nClient list command requested for domain: %s port: %s \n\n",
                domain?domain:"NONE", port?port:"NONE");

        /* Initialize to default values */
        fprintf(fp, "Domain                                          Port     Registered-     Row-           Null-          Drain\n");
        fprintf(fp, "                                                         tables          registered     registered     status\n");
        fprintf(fp, "-------------------------------------------------------------------------------------------------------------\n");
    }

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        memset (wfc_port, 0, sizeof (wfc_port));
        f_conn = wfc->client_connection;
        res = wally_get_client_port(f_conn->connection_description, wfc_port, WALLY_PORT_LEN);
        if (res != WALLY_RESULT_NO_ERROR) {
            fprintf(fp, "Invalid port for the Client %s\n",
                    fohh_description(wfc->client_connection));
            continue;
        }

        if (WALLY_FOHH_COMPARE(domain, port, wfc_port, f_conn)) {

            fprintf(fp, "%-48.46s", f_conn->peer_common_name);
            fprintf(fp, "%-10s", wfc_port);
            fprintf(fp, "%-15d", wfc->num_tables_registered);
            fprintf(fp, "%-15d", wfc->num_row_registered);
            fprintf(fp, "%-15d%s\n", wfc->num_null_registered,
                    wally_server_get_drain_status_string(wfc->drain_status));
            (*count)++;
        }
    }
    wfs_unlock(wfs);
    return FOHH_RESULT_NO_ERROR;
}

/*
 *  wally_server_fohh_reset_clients
 *
 *  Reset the marked client from client list
 */
int wally_server_fohh_reset_clients (struct wally_fohh_server *wfs,
                                     bool *is_completed,
                                     FILE *fp, int drain_rate,
                                     uint32_t *reset_count,
                                     uint32_t *reset_fail_count,
                                     bool two_phase_drain,
                                     enum wally_drain_phase phase)
{
    struct fohh_connection *f_conn = NULL;
    struct wally_fohh_server_client *tmp_c = NULL, *wfc = NULL;
    int res = 0;
    uint32_t index_1 = 0, index = 0;
    char wfc_port[WALLY_PORT_LEN] = { 0 };
    char buf[WALLY_LOG_FILE_LEN] = { 0 };

    if (fp == NULL || wfs == NULL || is_completed == NULL) {
        WALLY_LOG(AL_ERROR, "WFS/is_completed/file pointer is NULL");
        return FOHH_RESULT_BAD_ARGUMENT;
    }

    if (drain_rate == 0) {
        *is_completed = true;
        WALLY_LOG(AL_ERROR, "Drain rate cannot be 0");
        return FOHH_RESULT_BAD_ARGUMENT;
    }

    index = *reset_count;
    index_1 = *reset_fail_count;
    while (true) {
        wfs_lock(wfs);
        /* We don't want to keep the WFS global lock for long time, which delays the client destory and
         * insert. So LIST_FOREACH_SAFE is only to get the WFC and remaining execution runs in while(true).
         * During the unlocked situation, wfs->all_client_connections could have been modified in insert
         * or delete flow, so this loop will always run from the beginning, which is fine
         * */
        LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
            /* Initiate the drain only for the marked clients */
            if (wfc->drain_status != WALLYC_DRAIN_MARKED_P1 &&
                    wfc->drain_status != WALLYC_DRAIN_MARKED_P2) {
                continue;
            }
            /* Two phase drain is initiated. First run the drain for high/P1
             * client(leaf-wally) and then run drain for low/p2 clients.
             * Skip the P2 clients, when drain phase is 1.
             * Skip the P1 clients, when drain phase is 2.
             * */
            if (two_phase_drain && ((phase == WALLY_DRAIN_P1 &&
                    wfc->drain_status == WALLYC_DRAIN_MARKED_P2) ||
                        (phase == WALLY_DRAIN_P2 &&
                    wfc->drain_status == WALLYC_DRAIN_MARKED_P1))) {
                continue;
            }
            /* Drain marked WFC found, break and start the fohh connection reset */
            wfs_unlock(wfs);
            break;
        }
        /* List walk completed and no drain marked WFC found */
        if (wfc == NULL) {
            wfs_unlock(wfs);
            break;
        }

        memset (wfc_port, 0, sizeof (wfc_port));
        f_conn = wfc->client_connection;
        res = wally_get_client_port(f_conn->connection_description, wfc_port, WALLY_PORT_LEN);
        if (res != WALLY_RESULT_NO_ERROR) {
            WALLY_LOG(AL_ERROR, "Invalid port for the Client %s. Cannot reset\n",
                    fohh_description(wfc->client_connection));
            continue;
        }
        wally_get_timestamp(buf, sizeof(buf));
        fprintf(fp, "%s: Sending reset for domain %s port %s Registations: tables %d row %d  NULL %d\n",
                    buf, f_conn->peer_common_name, wfc_port, wfc->num_tables_registered,
                    wfc->num_row_registered, wfc->num_null_registered);

        wfc_lock_inbound(wfc);
        wfc_lock_outbound(wfc);

        /* Call the fohh reset function with full domain and port, both
         * params uniquely find the single client to reset */
        WALLY_DEBUG_REGISTRATION("Drain for domain %s port %s", f_conn->peer_common_name, wfc_port);
        res = fohh_connections_reset(f_conn->peer_common_name, wfc_port,
                fohh_connection_type_server, 1, NULL, NULL);
        wally_get_timestamp(buf, sizeof(buf));
        if (res == 0) {
            index++;
            fprintf(fp, "  %s:Reset domain %s port %s success\n",
                    buf, f_conn->peer_common_name, wfc_port);
        } else {
            index_1++;
            fprintf(fp, "  %s:Reset domain %s port %s failed\n",
                    buf, f_conn->peer_common_name, wfc_port);
        }

        /* Drain request initiated to fohh, but disconnect callback could come
         * later. Update the status here
         * */
        wfc->drain_status = WALLYC_DRAIN_INITATED;

        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);

        /* Reset only for the given drain rate. List is not completed yet.
         * So don't set the is_completed flag */
        if ((index % drain_rate) == 0) {
            *is_completed = false;
            goto EXIT;
        }

    }

    *is_completed = true;

EXIT:

    (*reset_count) = index;
    (*reset_fail_count) = index_1;
    return FOHH_RESULT_NO_ERROR;
}

/*
 *  wally_server_mark_drain
 *
 *  Mark the clients for reset based on given domain and port
 */
int wally_server_mark_drain (struct wally_fohh_server *wfs, const char *domain, const char *port,
        uint32_t force, uint32_t *marked_count, int drain_count, bool high_prio)
{
    struct fohh_connection *f_conn = NULL;
    struct wally_fohh_server_client *tmp_c = NULL, *wfc = NULL;
    char wfc_port[WALLY_PORT_LEN] = { 0 };
    uint32_t count = 0;
    int res = 0;

    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "WFS is NULL");
        return FOHH_RESULT_BAD_ARGUMENT;
    }

    if (drain_count == 0) {
        WALLY_LOG(AL_ERROR, "Drain count cannot be 0");
        return FOHH_RESULT_BAD_ARGUMENT;
    }
    count = *marked_count;

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {

        /* Don't mark the clients which are,
         * 1. Already marked
         * 2. Drain initiated
         * 3. Destory pending
         * */
        if (wfc->drain_status != WALLYC_DRAIN_NONE) {
            continue;
        }

        memset (wfc_port, 0, sizeof (wfc_port));
        f_conn = wfc->client_connection;
        res = wally_get_client_port(f_conn->connection_description, wfc_port, WALLY_PORT_LEN);
        if (res != WALLY_RESULT_NO_ERROR) {
            WALLY_LOG(AL_ERROR, "Invalid port for the Client %s. Cannot mark\n",
                    fohh_description(wfc->client_connection));
            continue;
        }

        if (WALLY_FOHH_COMPARE(domain, port, wfc_port, f_conn)) {
            /* Reset only works for the following,
             * 1. Wally clients should not be reset by default, unless
             * 2. "wally" domain is fully matched or force is 1
             * */
            if (force ||
                    (strcasestr(f_conn->connection_description, WALLY_DRAIN_FILTER) == NULL &&
                        (strcasestr(f_conn->remote_address_name, WALLY_DRAIN_FILTER)== NULL)) ||
                    (domain && (!strncmp(domain, f_conn->peer_common_name, strlen(f_conn->peer_common_name)))))
            {
                count++;
                /* Mark the clients for drain. If high_prio is set, mark those
                 * clients is P1 */
                if (high_prio) {
                    wfc->drain_status = WALLYC_DRAIN_MARKED_P1;
                } else {
                    wfc->drain_status = WALLYC_DRAIN_MARKED_P2;
                }
                if (count == drain_count) {
                    break;
                }
            }
        }
    }
    *marked_count = count;
    wfs_unlock(wfs);
    return FOHH_RESULT_NO_ERROR;
}

/*
 *  wally_server_clients_count
 *
 *  Count and return the number of clients connected with given domain and port
 */
int wally_server_clients_count (struct wally_fohh_server *wfs, const char *domain, const char *port,
        uint32_t force, uint32_t *pattern_match_client, uint32_t *wally_client)
{
    struct fohh_connection *f_conn = NULL;
    struct wally_fohh_server_client *tmp_c = NULL, *wfc = NULL;
    char wfc_port[WALLY_PORT_LEN] = {0};
    int res = 0;

    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "WFS is NULL");
        return FOHH_RESULT_BAD_ARGUMENT;
    }

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        memset (wfc_port, 0, sizeof (wfc_port));
        f_conn = wfc->client_connection;
        res = wally_get_client_port(f_conn->connection_description, wfc_port, WALLY_PORT_LEN);
        if (res != WALLY_RESULT_NO_ERROR) {
            WALLY_LOG(AL_ERROR, "Invalid port for the Client %s. Cannot count\n",
                    fohh_description(wfc->client_connection));
            continue;
        }

        if (WALLY_FOHH_COMPARE(domain, port, wfc_port, f_conn)) {

            /* Reset only works for the following,
             * 1. Wally clients should not be reset by default, unless
             * 2. "wally" domain is fully matched or force option is 1
             * */
            if (force ||
                    (strcasestr(f_conn->connection_description, WALLY_DRAIN_FILTER) == NULL &&
                        (strcasestr(f_conn->remote_address_name, WALLY_DRAIN_FILTER)== NULL)) ||
                    (domain && (!strncmp(domain, f_conn->peer_common_name, strlen(f_conn->peer_common_name)))))
            {
                (*pattern_match_client)++;
            } else {
                (*wally_client)++;
            }
        }
    }
    wfs_unlock(wfs);
    return FOHH_RESULT_NO_ERROR;
}

static void wally_fohh_client_destroy_deferred(void *cookie1, void *cookie2) {
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *)cookie1;
    wfc_lock_inbound(wfc);
    wfc_lock_outbound(wfc);
    wfc->is_deferred_for_destroy = 0;
    wfc_unlock_outbound(wfc);
    wfc_unlock_inbound(wfc);
    wally_fohh_client_destroy(wfc);
}

/*
 * Remove a client from this wally_fohh.
 *
 * Must hold lock.
 */
void wally_fohh_client_destroy(struct wally_fohh_server_client *wfc)
{
    __sync_add_and_fetch(&(wfc->is_dead),1);

    //struct wally_fohh *wf = wfc->wally_fohh;
    struct wally_fohh_registrant *wf_registrant, *tmp_r = NULL;
    struct zhash_table *z_table_filter = wfc->table_filter;
    struct zhash_table *nullify_state = wfc->nullify_state;

    wfc_lock_inbound(wfc);
    wfc_lock_outbound(wfc);
    if ( wfc->is_deferred_for_destroy ) {
        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);
        return ;
    } else if ( ( wfc->count_in_wally_layer_queue ) && !wfc->is_deferred_for_destroy ) {
        wfc->is_deferred_for_destroy = 1;
        zevent_defer(wally_fohh_client_destroy_deferred,wfc,NULL,100000);
        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);
        return ;
    }

    if (drain_status ==  WALLY_DRAIN_IN_PROGRESS) {
        /* Drain in progress. Don't allow to change the "all_client_connections" list.
         * Already drain command walking "all_client_connections". So wait for the
         * drain to complete.
         * After the completion, wally_cleanup thread destroys the pending
         * clients
         * */
        WALLY_DEBUG_REGISTRATION("Drain in progress. Mark for destroy pending for %s",
                fohh_description(wfc->client_connection));
        wfc->drain_status = WALLYC_DRAIN_DESTROY_PEND;
        wfc_unlock_outbound(wfc);
        wfc_unlock_inbound(wfc);
        return ;
    }

    /* For all registrants this client has created, remove them. */
    LIST_FOREACH_SAFE(wf_registrant, &(wfc->all_registrants), all_registrants, tmp_r) {
        /* First remove table from hash table on the client. */
        argo_hash_remove(wfc->per_table_registrant, wf_registrant->registrant->table->name, strlen(wf_registrant->registrant->table->name), NULL);
        wally_table_destroy_registrant(wf_registrant->registrant);
        LIST_REMOVE(wf_registrant, all_registrants);
        WALLY_FREE(wf_registrant);
    }

    if (wfc->allow_tables) argo_hash_free(wfc->allow_tables);
    wfc->allow_tables = NULL;
    if (wfc->filter_tables) argo_hash_free(wfc->filter_tables);
    wfc->filter_tables = NULL;
    argo_hash_free(wfc->per_table_registrant);
    wfc->per_table_registrant = NULL;
    wally_fohh_server_recovery_list_reset(wfc);
    /* Note: The fohh_connection is freed up by fohh upon return from the callback */
    wfc_unlock_outbound(wfc);
    wfc_unlock_inbound(wfc);
    //coverity[MISSING_LOCK:FALSE]

    WALLY_FREE(wfc);

    /* We are keeping the same old behaviour deleting filter tables after WFC free.
     * Don't want to change the existing flow */

    if (z_table_filter) {
        deallocate_table_filter(&(z_table_filter));
    }
    if (nullify_state) {
        wally_column_nullify_destroy_state(nullify_state);
    }
}



void wally_server_destroy_clients (struct wally_fohh_server *wfs)
{
    struct wally_fohh_server_client *tmp_c = NULL, *wfc = NULL;

    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "WFS is NULL");
        return;
    }

    if (drain_status == WALLY_DRAIN_IN_PROGRESS) {
        return;
    }

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        /* Destroy only the clients are in WALLYC_DRAIN_DESTROY_PEND state,
         * which are set in wally_fohh_client_destroy() routine during the previour
         * drain command execution */
        if (wfc->drain_status == WALLYC_DRAIN_DESTROY_PEND) {
            WALLY_DEBUG_REGISTRATION("Destroying the marked client %s",
                    fohh_description(wfc->client_connection));
            LIST_REMOVE(wfc, all_client_connections);

            wally_fohh_client_destroy(wfc);
        }
    }
    wfs_unlock(wfs);
}

static int client_connection_callback(struct fohh_connection *connection,
                                      enum fohh_connection_state state,
                                      void *cookie)
{
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) cookie;
    //WALLY_LOG(AL_NOTICE, "Connection callback received (entering) from client. New state = %s", fohh_connection_state_strings[state]);

    if (__sync_add_and_fetch(&(wfc->is_dead),0)) {
        WALLY_LOG(AL_ERROR, "Connection is already dead");
    }

    switch(state) {
    case fohh_connection_unresolved:
    case fohh_connection_unreachable:
    case fohh_connection_backoff:
    case fohh_connection_connecting:
    case fohh_connection_disconnected:
    case fohh_connection_deleted:
    default:
        if (connection->state == fohh_connection_connected) {
            wally_fohh_server_disconnect_stat(wfc->server->wally);
        }

        /* All these cases are a disconnected connection, and we remove our state. */
        if (wfc->disconnect_callback) {
            (wfc->disconnect_callback)(connection, state, NULL);
        }

        if (drain_status !=  WALLY_DRAIN_IN_PROGRESS) {
            wfs_lock(wfc->server);
            LIST_REMOVE(wfc, all_client_connections);
            wfs_unlock(wfc->server);
        }
        wally_fohh_client_destroy(wfc);
        /* wfc is now invalid. */
        break;
    case fohh_connection_connected:
        /* We really shouldn't get a connected callback, ever. But if we do, we ignore it. */
        break;
    }

    //WALLY_LOG(AL_NOTICE, "Connection callback received (leaving) from client. New state = %s", fohh_connection_state_strings[state]);

    return FOHH_RESULT_NO_ERROR;
}

static int client_unblock_callback(struct fohh_connection *connection,
                                   enum fohh_queue_element_type element_type,
                                   void *cookie)
{
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) cookie;
    struct wally_fohh_registrant *wf_registrant, *tmp_r;
    int res;

    /* Drain in progress for this client. So skipping table resume. Anyway, connection will be
     * reset.
     * */
    if (wfc->drain_status) {
        WALLY_DEBUG_REGISTRATION("unblock client drain status %s",
                wally_server_get_drain_status_string(wfc->drain_status));
        return WALLY_RESULT_NO_ERROR;
    }

    wfc_lock_outbound(wfc);

    wally_fohh_server_process_recovery_pending_list(wfc);

    //fprintf(stderr, "Received client unblock callback\n");
    LIST_FOREACH_SAFE(wf_registrant, &(wfc->all_registrants), all_registrants, tmp_r) {
        //fprintf(stderr, "Unblocking table %s\n", wf_registrant->table->name);
        res = wally_table_resume_callbacks(wf_registrant->registrant);
        if (res) break;
    }

    wfc_unlock_outbound(wfc);

    return FOHH_RESULT_NO_ERROR;
}

struct wally_fohh_server_client *wally_fohh_create_fohh_from_client(struct wally_fohh_server *wfs,
                                                                    struct fohh_connection *connection,
                                                                    char **allow_tables,
                                                                    int allow_tables_count,
                                                                    char **filter_tables,
                                                                    int filter_tables_count,
                                                                    int64_t filter_mask,
                                                                    int64_t filter_value,
                                                                    int64_t pass_key,
                                                                    struct zhash_table *zhash_table_filter,
                                                                    struct zhash_table *nullify_state,
                                                                    fohh_connection_callback_f  *disconnect_callback) {
    struct wally_fohh_server_client *wfc;
    struct argo_state *argo;
    int i;

    wfc = (struct wally_fohh_server_client *) WALLY_MALLOC(sizeof(*wfc));
    if (!wfc) return NULL;
    memset(wfc, 0, sizeof(*wfc));

    if (filter_mask) {
        WALLY_LOG(AL_NOTICE, "wally_fohh: Create connection for %s, filtered by %16lx:%16lx",
                  fohh_description(connection),
                  (long) filter_mask,
                  (long) filter_value);
    } else {
        WALLY_LOG(AL_NOTICE, "wally_fohh: Create connection for %s", fohh_description(connection));
    }

    if (allow_tables_count) {
        wfc->allow_tables = argo_hash_alloc(5, 1);
        if (!wfc->allow_tables) goto fail;
        for (i = 0; i < allow_tables_count; i++) {
            /* Note- have to store SOMETHING in the ptr, so we store the hash table. */
            if (argo_hash_store(wfc->allow_tables, allow_tables[i], strlen(allow_tables[i]), 0, wfc->allow_tables)) {
                WALLY_LOG(AL_CRITICAL, "Could not add allow table %s", allow_tables[i]);
                goto fail;
            }
        }
    }

    if (filter_tables_count) {
        wfc->filter_tables = argo_hash_alloc(5, 1);
        if (!wfc->filter_tables) goto fail;
        for (i = 0; i < filter_tables_count; i++) {
            /* Note- have to store SOMETHING in the ptr, so we store the hash table. */
            if (argo_hash_store(wfc->filter_tables, filter_tables[i], strlen(filter_tables[i]), 0, wfc->filter_tables)) {
                WALLY_LOG(AL_CRITICAL, "Could not add allow table %s", filter_tables[i]);
                goto fail;
            }
        }
    }

    wfc->client_connection = connection;
    wfc->server = wfs;
    wfc->filter_mask = filter_mask;
    wfc->filter_value = filter_value;
    wfc->pass_key = pass_key;
    wfc->per_table_registrant = argo_hash_alloc(8, 1);
    wfc->num_tables_registered = 0;
    wfc->num_row_registered = 0;
    wfc->num_null_registered = 0;
    wfc->table_filter = zhash_table_filter;
    wfc->nullify_state = nullify_state;
    if (!wfc->per_table_registrant) goto fail;
    LIST_INIT(&(wfc->all_registrants));
    ZTAILQ_INIT(&(wfc->recovery_feed_list));

    /* Register argo structures for receiving requests */
    argo = fohh_argo_get_rx(connection);
    argo_register_structure(argo, wfs->register_row_string, client_register_row_string_callback, wfc);
    argo_register_structure(argo, wfs->deregister_row_string, client_deregister_row_string_callback, wfc);
    argo_register_structure(argo, wfs->register_row_integer, client_register_row_integer_callback, wfc);
    argo_register_structure(argo, wfs->deregister_row_integer, client_deregister_row_integer_callback, wfc);
    argo_register_structure(argo, wfs->version, client_version_callback, wfc);
    argo_register_structure(argo, wfs->version_ack, NULL, NULL);
    argo_register_structure(argo, wfs->recovery_begin_request, NULL, NULL);
    argo_register_structure(argo, wfs->recovery_end_request, NULL, NULL);
    argo_register_structure(argo, wfs->recovery_complete_response, wally_fohh_client_recovery_complete_response_callback, wfc);

    argo = fohh_argo_get_tx(connection);
    argo_register_structure(argo, wfs->request_result, NULL, NULL);

    /* Register all tables associated with this wally. */
    ZPATH_RWLOCK_RDLOCK(&(wfs->wally->lock), __FILE__, __LINE__);
    for (i = 0; i < wfs->wally->tables_count; i++) {
        argo_register_structure(argo, wfs->wally->tables[i]->argo_description, NULL, NULL);
    }
    ZPATH_RWLOCK_UNLOCK(&(wfs->wally->lock), __FILE__, __LINE__);

    /* Switch callbacks to service callback. (so callback
     * cookies, etc, are correct) */
    fohh_update_callback(connection, wfc, client_connection_callback, client_unblock_callback, NULL, NULL, 0);
    wfc->disconnect_callback = disconnect_callback;

    wally_fohh_server_connect_stat(wfs->wally);

    wfs_lock(wfs);
    LIST_INSERT_HEAD(&(wfs->all_client_connections), wfc, all_client_connections);
    wfs_unlock(wfs);

    return wfc;

 fail:
    if (wfc) {
        if (wfc->per_table_registrant) argo_hash_free(wfc->per_table_registrant);
        if (wfc->allow_tables) argo_hash_free(wfc->allow_tables);
        if (wfc->filter_tables) argo_hash_free(wfc->filter_tables);
        WALLY_FREE(wfc);
    }
    return NULL;
}

/*
 * Generic connection callback is used for client connections and for
 * accepted server connections. It is also called back for failed
 * server connections.
 */
static int generic_connection_callback(struct fohh_connection *connection,
                                       enum fohh_connection_state state,
                                       void *cookie)
{
    struct wally_fohh_server_client *wfc;

    struct wally_fohh_server *wfs = (struct wally_fohh_server *) cookie;

    if (wally_client_reject == true) {
        WALLY_LOG(AL_NOTICE, "Client access disabled. Reject the connection %s",
                fohh_description(connection));
        return FOHH_RESULT_ERR;
    }

    if (drain_status ==  WALLY_DRAIN_IN_PROGRESS) {
        WALLY_DEBUG_REGISTRATION("Drain in progress. Cannot accept connection %s",
                fohh_description(connection));
        return FOHH_RESULT_ERR;
    }
    //WALLY_LOG(AL_NOTICE, "Connection callback received from client. New state = %s", fohh_connection_state_strings[state]);
    if (state == fohh_connection_connected) {
        //fohh_set_debug(connection, 1);

        wfc = wally_fohh_create_fohh_from_client(wfs, connection, NULL, 0, NULL, 0, 0, 0, 0, NULL, NULL, NULL);
        if (!wfc) {
            /* Failure. */
            /* XXX LOG */
            /* XXX IMPLEMENT ME */
            fprintf(stderr, "Error: Could not create client fohh\n");
        }
    } else {
        /* XXX LOG */
        /* Disconnected on generic callback? This shouldn't really be possible */
    }
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Generic unblock callback- Really shouldn't ever be called.
 */
static int generic_unblock_callback(struct fohh_connection *connection,
                                    enum fohh_queue_element_type element_type,
                                    void *cookie)
{
    fprintf(stderr, "%s:%s: Err: We shouldn't really be called\n", __FILE__, __FUNCTION__);
    return FOHH_RESULT_ERR;
}

static int wally_fohh_register_argo_structures(struct wally_fohh_server *wfs)
{
    wally_fohh_register_structures();

    wfs->register_row_string = argo_get_structure_description("wally_fohh_register_row_request_string");
    wfs->deregister_row_string = argo_get_structure_description("wally_fohh_deregister_row_request_string");
    wfs->register_row_integer = argo_get_structure_description("wally_fohh_register_row_request_integer");
    wfs->deregister_row_integer = argo_get_structure_description("wally_fohh_deregister_row_request_integer");
    wfs->request_result = argo_get_structure_description("wally_fohh_request_result");
    wfs->version = argo_get_structure_description("wally_fohh_version");
    wfs->version_ack = argo_get_structure_description("wally_fohh_version_ack");
    wfs->recovery_begin_request = argo_get_structure_description("wally_fohh_recovery_begin_request");
    wfs->recovery_end_request = argo_get_structure_description("wally_fohh_recovery_end_request");
    wfs->recovery_complete_response = argo_get_structure_description("wally_fohh_recovery_complete_response");

    if (!wfs->register_row_string) return WALLY_RESULT_ERR;
    if (!wfs->deregister_row_string) return WALLY_RESULT_ERR;
    if (!wfs->register_row_integer) return WALLY_RESULT_ERR;
    if (!wfs->deregister_row_integer) return WALLY_RESULT_ERR;
    if (!wfs->request_result) return WALLY_RESULT_ERR;
    if (!wfs->version) return WALLY_RESULT_ERR;
    if (!wfs->version_ack) return WALLY_RESULT_ERR;

    return WALLY_RESULT_NO_ERROR;
}


struct wally_fohh_server *wally_fohh_server_create(struct wally *wally,
                                                   enum argo_serialize_mode encoding,
                                                   enum fohh_connection_style style,
                                                   int no_service_ip,
                                                   struct argo_inet *service_ip,
                                                   uint16_t tcp_port_ne,
                                                   char *root_cert_file_name,
                                                   char *my_cert_file_name,
                                                   char *my_cert_key_file_name,
                                                   int use_ssl)
{
    struct wally_fohh_server *wfs;
    static pthread_mutexattr_t mutex_attributes;
    char str[100];

    wfs = (struct wally_fohh_server *) WALLY_MALLOC(sizeof(*wfs));
    if (!wfs) return NULL;

    memset(wfs, 0, sizeof(*wfs));

    WALLY_LOG(AL_NOTICE, "Creating server %s:%d using %s/%s/%s: SSL = %s",
              service_ip ? argo_inet_generate(str, service_ip) : "ADDR_ANY",
              ntohs(tcp_port_ne),
              root_cert_file_name ? root_cert_file_name : "NULL",
              my_cert_file_name ? my_cert_file_name : "NULL",
              my_cert_key_file_name ? my_cert_key_file_name : "NULL",
              use_ssl ? "Yes" : "No");

    wfs->wally = wally;
    wfs->tcp_port_ne = tcp_port_ne;
    if (wally != NULL) {
        wally->wally_fohh_server_handle[wally->num_fohh_servers++] = wfs;
    }

    pthread_mutexattr_init(&mutex_attributes);
    pthread_mutexattr_settype(&mutex_attributes, PTHREAD_MUTEX_RECURSIVE);
    pthread_mutex_init(&(wfs->lock_server), &mutex_attributes);

    if (wally_fohh_register_argo_structures(wfs)) {
        WALLY_FREE(wfs);
        return NULL;
    }

    /* Create IPv4 server */
    if (!no_service_ip) {
        if (!service_ip ||
            (service_ip && service_ip->length == 4)) {
            wfs->server_sin.sin_family = AF_INET;
            if (service_ip) {
                memcpy(&(wfs->server_sin.sin_addr.s_addr), &(service_ip->address[0]), 4);
            } else {
                wfs->server_sin.sin_addr.s_addr = INADDR_ANY;
            }
            wfs->server_sin.sin_port = tcp_port_ne;
            wfs->server_ipv4 = fohh_server_create(0,
                                                  encoding,
                                                  style,
                                                  wfs,
                                                  generic_connection_callback,
                                                  NULL,
                                                  generic_unblock_callback,
                                                  unknown_rx_object_callback,
                                                  (struct sockaddr *)&(wfs->server_sin),
                                                  sizeof(wfs->server_sin),
                                                  root_cert_file_name,
                                                  my_cert_file_name,
                                                  my_cert_key_file_name,
                                                  use_ssl ? 1 : 0,  /* Require client cert */
                                                  use_ssl,          /* Require SSL */
                                                  NULL,             /* No SNI callback */
                                                  NULL,             /* No verify callback */
                                                  NULL,             /* No post callback */
                                                  1,                /* Allow binary argo */
                                                  15*60),           /* 15m idle timeout */
            WALLY_LOG(AL_DEBUG, "Created IPV4 wally_fohh_server at %p", wfs);
        }

        /* Create IPv6 server */
        if (!service_ip ||
            (service_ip && service_ip->length == 16)) {
            wfs->server_sin6.sin6_family = AF_INET6;
            if (service_ip) {
                memcpy(&(wfs->server_sin6.sin6_addr), &(service_ip->address[0]), 16);
            } else {
                wfs->server_sin6.sin6_addr = in6addr_any;
            }
            wfs->server_sin6.sin6_port = tcp_port_ne;
            wfs->server_ipv6 = fohh_server_create(0,
                                                  encoding,
                                                  style,
                                                  wfs,
                                                  generic_connection_callback,
                                                  NULL,
                                                  generic_unblock_callback,
                                                  unknown_rx_object_callback,
                                                  (struct sockaddr *)&(wfs->server_sin6),
                                                  sizeof(wfs->server_sin6),
                                                  root_cert_file_name,
                                                  my_cert_file_name,
                                                  my_cert_key_file_name,
                                                  use_ssl ? 1 : 0,  /* Require client cert */
                                                  use_ssl,          /* Require SSL */
                                                  NULL,             /* No SNI callback */
                                                  NULL,             /* No verify callback */
                                                  NULL,             /* No post verify callback */
                                                  1,                /* Allow binary argo */
                                                  15*60);           /* 15m idle timeout */
        }

        if (!(wfs->server_ipv4) && !(wfs->server_ipv6)) {
            WALLY_FREE(wfs);
            return NULL;
        }
    }
    LIST_INIT(&(wfs->all_client_connections));

    return wfs;
}

/* Function : wally_fohh_client_recovery_complete_response_callback
 * Arg      : cookie - wally fohh client object
 *            structure_cookie - wally fohh client object
 *            object - wally fohh recovery complete response
 * Ret      : WALLY_RESULT_NO_ERROR if success
 * Desc     : This function handles recovery complete response message from
 *              clients
 */
static int wally_fohh_client_recovery_complete_response_callback(void *cookie, void *structure_cookie, struct argo_object *object)
{
    struct wally_fohh_recovery_complete_response *resp = (struct wally_fohh_recovery_complete_response *)object->base_structure_data;
    struct wally_fohh_server_client *wfc = (struct wally_fohh_server_client *) structure_cookie;
    struct wally_fohh_server *wfs = wfc->server;
    struct wally *wally = wfs->wally;

    if (IS_WALLY_LAYER_ENABLED) {
        inc_wfc_count(structure_cookie);
        wally_table_queue_enqueue_fohh_recovery_cmplt_resp(wally, resp->table_name, resp->received_row_count);
    } else {
        wally_table_server_handle_complete_response(wally, resp->table_name, resp->received_row_count);
    }
    return WALLY_RESULT_NO_ERROR;

}

/* Function : wally_fohh_retrieve_unsupported_clients
 * Arg      : wfs - wally fohh server
 *            buffer - buffer to be filled.
 *            start - starting position in buffer
 *            bufsize - total buffer size
 * Ret      : appropriate WALLY_RESULT
 * Desc     : This function iterates and write recovery unsupported clients
 *              into buffer
 */
int wally_fohh_retrieve_unsupported_clients(struct  wally_fohh_server *wfs, char *buffer, int64_t *start, size_t bufsize)
{
    struct wally_fohh_server_client *wfc = NULL, *tmp_c = NULL;
    int res = 0;
    char *s = buffer + *start;
    char *e = buffer + bufsize;
    if (s >= e) {
        return WALLY_RESULT_ERR_TOO_LARGE;
    }
    /* validations */
    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "Begin_Request_Sender: WFS object is NULL");
        return WALLY_RESULT_ERR;
    }

    /* iterate all clients and send begin recovery, if one client fails, still sent to other clients */
    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        wfc_lock_outbound(wfc);
        if (!(wfc->client_capabilities & WALLY_CLIENT_TYPE_ITASCA)) {
            s +=sxprintf(s, e, "%s\n", fohh_description(wfc->client_connection));
        }
        wfc_unlock_outbound(wfc);
    }
    wfs_unlock(wfs);
    *start = (int64_t) (s - buffer);
    return res;
}

/* Function : wally_fohh_server_send_recovery_begin_request
 * Arg      : wfs - wally fohh server for which requests to be sent all clients
 *            table_name - table name to be recovered
 *            recovery_sequence - recovery sequence number
 *            recovery_timeout - recovery timeout
 *            sync_missing_rows - sync only missing rows or all rows
 * Ret      : Number of requests sent out for this server.
 * Desc     : This function iterates all the clients for the server and sends begin recovery
 *              requests for each client.
 */
int wally_fohh_server_send_recovery_begin_request(struct wally_fohh_server *wfs, char *table_name,
                            int64_t recovery_sequence, int64_t recovery_timeout, int sync_missing_rows)
{
    struct wally_fohh_server_client *wfc = NULL, *tmp_c = NULL;
    int request_count = 0, res = WALLY_RESULT_NO_ERROR;
    /* validations */
    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "Begin_Request_Sender: WFS object is NULL");
        return -1;
    }
    /* iterate all clients and send begin recovery, if one client fails, still sent to other clients */
    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        struct wally_fohh_recovery_begin_request recovery_begin_request = {0};
        wfc_lock_outbound(wfc);
        if (!(wfc->client_capabilities & WALLY_CLIENT_TYPE_ITASCA)) {
            wfc_unlock_outbound(wfc);
            continue;
        }
        /* Fill the begin recovery request */
        recovery_begin_request.recovery_sequence = recovery_sequence;
        recovery_begin_request.table_name = table_name;
        recovery_begin_request.recovery_timeout = recovery_timeout;
        recovery_begin_request.sync_missing_rows = sync_missing_rows;
        /* Send begin recovery request to this client */
        res = fohh_argo_serialize(wfc->client_connection, wfs->recovery_begin_request,
                    &recovery_begin_request, 0, fohh_queue_element_type_control);
        if (res == ARGO_RESULT_BAD_ARGUMENT) {
            /* XXX LOG. We haven't registered this object type with this argo yet. */
            WALLY_LOG(AL_ERROR, "Begin_Request_Sender: Could not serialize: %s",
                            argo_result_string(res));
        } else {
            if ((res != ARGO_RESULT_WOULD_BLOCK) &&
                    (res != ARGO_RESULT_NO_ERROR)) {
                WALLY_LOG(AL_ERROR, "Begin_Request_Sender: Could not serialize: %s",
                        argo_result_string(res));
            }
            else {
                request_count++;
            }
        }
        wfc_unlock_outbound(wfc);
    }
    wfs_unlock(wfs);
    return request_count;
}

/* Function : wally_fohh_server_send_recovery_end_request
 * Arg      : wfs - Wally Fohh Server
 *            table_name - name of the recovery table
 *            server_sent_rows - server_sent_rows
 * Ret      : Number of requests sent
 * Desc     : This function iterates clients for the server and sends request
 *                  to the clients.
 */
int wally_fohh_server_send_recovery_end_request(struct wally_fohh_server *wfs,
                                                    char *table_name, int64_t server_sent_rows)
{
    struct wally_fohh_server_client *wfc = NULL, *tmp_c = NULL;
    int res = FOHH_RESULT_NO_ERROR, recovery_end_requests = 0;

    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "WFS is NULL");
        return -1;
    }

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        struct wally_fohh_recovery_end_request recovery_end_request;
        wfc_lock_outbound(wfc);
        if (!(wfc->client_capabilities & WALLY_CLIENT_TYPE_ITASCA)) {
            wfc_unlock_outbound(wfc);
            continue;
        }
        recovery_end_request.table_name = table_name;
        recovery_end_request.server_sent_rows = server_sent_rows;
        res = fohh_argo_serialize(wfc->client_connection, wfs->recovery_end_request,
                    &recovery_end_request, 0, fohh_queue_element_type_control);
        if (res == ARGO_RESULT_BAD_ARGUMENT) {
            /* XXX LOG. We haven't registered this object type with this argo yet. */
            WALLY_LOG(AL_ERROR, "End_Request_Sender: Could not serialize: %s", argo_result_string(res));
        } else {
            if ((res != ARGO_RESULT_WOULD_BLOCK) &&
                    (res != ARGO_RESULT_NO_ERROR)) {
                WALLY_LOG(AL_ERROR, "End_Request_Sender: Could not serialize: %s", argo_result_string(res));
            } else {
                recovery_end_requests++;
            }
        }
        wfc_unlock_outbound(wfc);
    }
    wfs_unlock(wfs);
    return recovery_end_requests;
}

/* Function : wally_fohh_server_send_row_to_client
 * Arg      : connection - Connection in which object to be sent to client
 *            object - argo object to be sent
 * Ret      : FOHH_RESULT_NO_ERROR
 * Desc     : This function sends row object through the connection
 */
int wally_fohh_server_send_row_to_client(struct fohh_connection *connection, struct argo_object *object)
{
    struct argo_object *copy = NULL;
    int res = FOHH_RESULT_NO_ERROR;
    copy = argo_object_copy(object);
    if (copy == NULL ) return res;
    argo_object_set_request_id(copy, 0);
    res = fohh_argo_serialize_object(connection, copy, 0, fohh_queue_element_type_control);
    argo_object_release(copy);
    return res;
}

/* Function : wally_fohh_server_recovery_list_reset
 * Arg      : wfc - client object
 * Ret      : None
 * Desc     : This function iterates all recovery pending rows objects and cleans up.
 */
static void wally_fohh_server_recovery_list_reset(struct wally_fohh_server_client *wfc)
{
    struct wally_client_recovery_callback *recovery_callback = NULL;
    while ((recovery_callback = ZTAILQ_FIRST(&(wfc->recovery_feed_list)))) {
        ZTAILQ_REMOVE(&(wfc->recovery_feed_list), recovery_callback, feed_list);
        argo_object_release(recovery_callback->row_object);
        WALLY_FREE_SLOW(recovery_callback);
        continue;
    }
}

/* Function : wally_fohh_server_process_recovery_pending_list
 * Arg      : wfc - Wally Fohh Client
 * Ret      : None
 * Desc     : This function iterates pending list and sends recovery rows to client on unblock callback
 */
static void wally_fohh_server_process_recovery_pending_list(struct wally_fohh_server_client *wfc)
{
    struct wally_client_recovery_callback *recovery_callback = NULL;
    int res = WALLY_RESULT_NO_ERROR;
    int64_t now_s = 0, delta_s = 0;
    struct zthread_info *zthread = zthread_self();
    int64_t max_hb_miss_timeout_s = 0;

    if (zthread) {
        max_hb_miss_timeout_s = WALLY_DIV_ROUND_CLOSEST(zthread->maximum_heartbeat_delta_s, WALLY_ONE_THIRD_DIVISOR);
    }

    while ((recovery_callback = ZTAILQ_FIRST(&(wfc->recovery_feed_list)))) {
        struct wally_table *table = recovery_callback->table;
        if (zthread) {
            now_s = monotime_s();
            delta_s = (now_s - zthread->last_heartbeat_monotime_s);
            if ((delta_s > max_hb_miss_timeout_s)) {
                zthread_heartbeat(zthread);
            }
        }
        res = wally_fohh_server_send_row_to_client(wfc->client_connection, recovery_callback->row_object);
        if (res == ARGO_RESULT_BAD_ARGUMENT)
        {
            WALLY_LOG(AL_ERROR, "Row_Resender:%s Could not serialize: %s",
                                        wally_recovery_state_string(table->recovery_state.state), argo_result_string(res));
        } else {
            if ((res != ARGO_RESULT_WOULD_BLOCK) && (res != ARGO_RESULT_NO_ERROR)) {
                WALLY_LOG(AL_ERROR, "Row_Resender:%s Could not serialize: %s",
                                            wally_recovery_state_string(table->recovery_state.state), argo_result_string(res));
            }
        }
        if (res != ARGO_RESULT_WOULD_BLOCK) {
            if ((table->recovery_state.state != recovery_state_noactive) &&
                    (__atomic_load_n(&(table->recovery_state.pending_send_rows), __ATOMIC_RELAXED)>0)) {
                __sync_fetch_and_sub_8(&(recovery_callback->table->recovery_state.pending_send_rows),1);
            }
            ZTAILQ_REMOVE(&(wfc->recovery_feed_list), recovery_callback, feed_list);
            argo_object_release(recovery_callback->row_object);
            WALLY_FREE_SLOW(recovery_callback);
        } else {
            break;
        }
    }
}
/* Function : wally_fohh_server_send_row_to_all_clients
 * Arg      : wfs - Wally Fohh Server
 *            table - wall table
 *            row - row argo object
 * Ret      : FOHH_RESULT_NO_ERROR if no error, otherwise appropriate error.
 * Desc     : Sends rows to fohh clients connected to this server
 */
int wally_fohh_server_send_row_to_all_clients(struct wally_fohh_server *wfs, struct wally_table *table, struct argo_object *row)
{
    struct wally_fohh_server_client *wfc = NULL, *tmp_c = NULL;
    int res = FOHH_RESULT_NO_ERROR;
    int64_t now_s = 0, delta_s = 0;
    struct zthread_info *zthread = zthread_self();
    int64_t max_hb_miss_timeout_s = 0;
    int total_pending_rows = 0;

    if (wfs == NULL) {
        WALLY_LOG(AL_ERROR, "Row_Sender: WFS is NULL");
        return -1;
    }

    if (zthread) {
        max_hb_miss_timeout_s = WALLY_DIV_ROUND_CLOSEST(zthread->maximum_heartbeat_delta_s, WALLY_ONE_THIRD_DIVISOR);
    }

    wfs_lock(wfs);
    LIST_FOREACH_SAFE(wfc, &(wfs->all_client_connections), all_client_connections, tmp_c) {
        if (zthread) {
            now_s = monotime_s();
            delta_s = (now_s - zthread->last_heartbeat_monotime_s);
            if ((delta_s > max_hb_miss_timeout_s)) {
                zthread_heartbeat(zthread);
            }
        }
        wfc_lock_outbound(wfc);
        if (!(wfc->client_capabilities & WALLY_CLIENT_TYPE_ITASCA)) {
            wfc_unlock_outbound(wfc);
            continue;
        }
        res = wally_fohh_server_send_row_to_client(wfc->client_connection, row);
        if (res == ARGO_RESULT_BAD_ARGUMENT) {
            /* XXX LOG. We haven't registered this object type with this argo yet. */
            WALLY_LOG(AL_ERROR, "Row_Sender: Could not serialize: %s",
                            argo_result_string(res));
        } else {
            if ((res != ARGO_RESULT_WOULD_BLOCK) &&
                    (res != ARGO_RESULT_NO_ERROR)) {
                WALLY_LOG(AL_ERROR, "Row_Sender: Could not serialize: %s",
                            argo_result_string(res));
            } else if ( res == ARGO_RESULT_WOULD_BLOCK) {
                struct wally_client_recovery_callback *recovery_callback = NULL;
                recovery_callback = (struct wally_client_recovery_callback *)
                    WALLY_MALLOC(sizeof(*recovery_callback));
                if (recovery_callback) {
                    argo_object_hold(row);
                    recovery_callback->row_object = row;
                    /* Table is stored in recovery callback as wally_table never released */
                    recovery_callback->table = table;
                    ZTAILQ_INSERT_TAIL(&(wfc->recovery_feed_list), recovery_callback, feed_list);
                    total_pending_rows++;
                }
            }
        }
        wfc_unlock_outbound(wfc);
    }
    wfs_unlock(wfs);
    return total_pending_rows;
}
