/*
 * wally_internal.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Internal data structures and definitions.
 */


#ifndef __WALLY_PRIVATE_H__
#define __WALLY_PRIVATE_H__

#include <pthread.h>
#include <stdbool.h>

#include "zpath_misc/zpath_misc.h"
#include "zhash/zhash_table.h"

#include "argo/argo.h"
#include "argo/argo_hash.h"
#include "argo/argo_private.h"
#include "argo/argo_structure.h"
#include "argo/argo_log.h"
#include "avl/avl.h"

#include "wally/wally.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_fohh_client.h"

#define BASE10                            10

#define WALLY_TABLE_CLEANUP_NO_ERROR      0
#define WALLY_TABLE_CLEANUP_ERROR         1
#define WALLY_TABLE_CLEANUP_BUSY          2
#define WALLY_TABLE_CLEANUP_POLL          3
#define WALLY_TABLE_CLEANUP_WAITING       4
#define INTEREST_PENDING_WINDOW_SIZE      6
#define INTEREST_PENDING_WINDOW_INTERVAL  10*1000*1000 // 10ses
#define WALLY_MIN_TO_MILLISEC             60000

#define WALLY_MAX_TABLES 256
#define WALLY_MAX_ORIGINS 10
#define WALLY_MAX_INDEXED_COLUMNS 5
#define WALLY_TABLE_REREGN_RATE 1
#define WALLY_BUFF_LEN          512
#define WALLY_INT_REG_HIS_SIZE 6
#define WALLY_MAX_INT_STATE_COUNT 10
#define WALLY_MAX_THREAD_NAME_LEN   250
#define WALLY_MAX_COLUMNS 1024

#define ORIGIN_WALLY_DB_INDEX 0
#define RECOVERY_RESET_STATE 0x0001
#define RECOVERY_RESET_STATS 0x0002
#define RECOVERY_RESET_ALL   0x0003

/* Filter used to skip the clients from drain */
#define WALLY_DRAIN_FILTER  "wally"

extern uint32_t wally_max_hb_iteration;
extern uint32_t wally_max_hb_miss_timeout_s;
extern uint32_t wally_default_hb_timeout_s;

extern uint64_t tcp_keepalives_idle;
extern uint64_t tcp_keepalives_interval;
extern uint64_t tcp_keepalives_count;
extern uint64_t tcp_user_timeout;
extern bool disable_wally_tcp_config;

/* Divide and return the closest round of value*/
#define WALLY_DIV_ROUND_CLOSEST(dividend, divisor)  ((dividend + (divisor / 2)) / divisor)
#define WALLY_DEFAULT_MAX_HB_CHECK_ITERATION        100  /* Max iteration between Heartbeat tick */
#define WALLY_MAX_HB_CHECK_ITERATION                1000000  /* Max iteration between Heartbeat tick */
#define WALLY_DEFAULT_HB_TIMEOUT_S                  20   /* Default HB tick */
#define WALLY_DEFAULT_EXT_HB_TIMEOUT_S              300  /* Default extended HB tick; 5 minutes  */
#define WALLY_ONE_THIRD_DIVISOR                     3
#define WALLY_DEFAULT_HB_THRESHOLD WALLY_DIV_ROUND_CLOSEST(WALLY_DEFAULT_HB_TIMEOUT_S, WALLY_ONE_THIRD_DIVISOR)
#define WALLY_MAX_SERVER                            10

/* Wally keepalive params to override kernel defaults  */
#define WALLY_KEEPALIVES_IDLE      60     /* Controls the number of seconds of inactivity after which
                                             TCP should send a keepalive message to the server. */
#define WALLY_KEEPALIVES_INTERVAL  20     /* Controls the number of seconds after which a TCP keepalive
                                             message that is not acknowledged by the server should be retransmitted.*/
#define WALLY_KEEPALIVES_COUNT     3      /* Controls the number of TCP keepalives that can be lost before
                                             the client’s connection to the server is considered dead.*/
#define WALLY_KEEPALIVES           "1"    /* Set 1 to enable wally-side TCP keepalives values */
#define WALLY_TCP_USER_TIMEOUT     240000 /* 4-mins. Controls the number of milliseconds that transmitted data
                                               may remain unacknowledged before a connection is forcibly closed.
                                               Recommended: TCP_USER_TIMEOUT = TCP_KEEPIDLE + TCP_KEEPINTVL * TCP_KEEPCNT*/

#define WALLY_TCP_KEEPALIVES_IDLE_MIN       10      /* in seconds */
#define WALLY_TCP_KEEPALIVES_IDLE_MAX       7200    /* in seconds; 2 hours*/
#define WALLY_TCP_KEEPALIVES_INTERVAL_MIN   15      /* in seconds */
#define WALLY_TCP_KEEPALIVES_INTERVAL_MAX   75      /* in seconds */
#define WALLY_TCP_KEEPALIVES_COUNT_MIN      1
#define WALLY_TCP_KEEPALIVES_COUNT_MAX      255
#define WALLY_TCP_USER_TIMEOUT_MIN          60000   /* 1 Minute */
#define WALLY_TCP_USER_TIMEOUT_MAX          7875000 /* 2 hours 11 mins 25 seconds; Linux default keepalive timeout */

/* Abort any statement that takes more than the specified amount of time.
 * The timeout is measured from the time a command arrives at the server
 * until it is completed by the server. If multiple SQL statements appear
 * in a single simple-query message, the timeout is applied to each statement
 * separately.*/
#define WALLY_STATEMENT_OUT_DEFAULT         0   /* In minutes */
#define WALLY_STATEMENT_OUT_MIN             0   /* In minutes */
#define WALLY_STATEMENT_OUT_MAX             30  /* In minutes */

/* lock_timeout controls how long a transaction will wait to acquire a lock on a
 * database object, such as a table or a row, before giving up and returning an error.*/
#define WALLY_LOCK_TIMEOUT_DEFAULT          0  /* In minutes */
#define WALLY_LOCK_TIMEOUT_MIN              0  /* In minutes */
#define WALLY_LOCK_TIMEOUT_MAX              30 /* In minutes */

/* idle_in_transaction_session_timeout controls the maximum amount of time that a
 * session can remain idle while inside a transaction. If a session stays idle within
 * a transaction for longer than the specified timeout period, PostgreSQL will
 * automatically terminate the session and roll back the ongoing transaction. */
#define WALLY_IDLE_TRANS_SESS_TIMEOUT_DEFAULT          0   /* In minutes */
#define WALLY_IDLE_TRANS_SESS_TIMEOUT_MIN              0   /* In minutes */
#define WALLY_IDLE_TRANS_SESS_TIMEOUT_MAX              30  /* In minutes */

/* Default GVR mode is enabled */
#define WALLY_DEFAULT_ENABLE_GVR_MODE               0
#define WALLY_DB_GVR_MODE_DISABLED                  (wally_gbl_cfg.enable_db_gvr_mode == 0)
#define WALLY_DB_GVR_MODE_ENABLED                   (wally_gbl_cfg.enable_db_gvr_mode == 1)
#define WALLY_DB_PENDING_TXN_CHK_INTVL              (1 * 500 * 1000) // 500ms

#define WALLY_DEFAULT_ENABLE_BATCH_WRITE  0
#define WALLY_MIN_WRITE_BATCH_SIZE        1
#define WALLY_MAX_WRITE_BATCH_SIZE        1000
#define WALLY_DEFAULT_WRITE_BATCH_SIZE    100

/* idle_session_timeout controls the maximum amount of time a session can remain
 * idle before it is automatically terminated. Unlike idle_in_transaction_session_timeout,
 * which applies only to sessions that are idle while inside a transaction,
 * idle_session_timeout applies to any session that is idle, whether or not it is within a
 * transaction.*/
#define WALLY_IDLE_SESS_TIMEOUT_DEFAULT          0    /* In minutes */
#define WALLY_IDLE_SESS_TIMEOUT_MIN              0    /* In minutes */
#define WALLY_IDLE_SESS_TIMEOUT_MAX              120  /* In minutes */

/* Postgres param type */
enum wally_postgres_config_e {
    WALLY_TCP_KEEPALIVES_IDLE,
    WALLY_TCP_KEEPALIVES_INTERVAL,
    WALLY_TCP_KEEPALIVES_COUNT,
    WALLY_TCP_USR_TIMEOUT,
    WALLY_STATEMENT_TIMEOUT,
    WALLY_LOCK_TIMEOUT,
    WALLY_IDLE_TRANS_SESS_TIMEOUT,
    WALLY_IDLE_SESS_TIMEOUT,
    WALLY_TCP_INVALID,
};

extern struct zpath_allocator wally_allocator;
#define WALLY_MALLOC(x) zpath_malloc(&wally_allocator, x, __LINE__, __FILE__)
#define WALLY_REALLOC(orig, x) zpath_realloc(&wally_allocator, orig, x, __LINE__, __FILE__)
#define WALLY_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define WALLY_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define WALLY_CALLOC(x) zpath_calloc(&wally_allocator, x, __LINE__, __FILE__)
#define WALLY_STRDUP(x, y) zpath_strdup(&wally_allocator, x, y, __LINE__, __FILE__)


#define WALLY_DEBUG_RESULT_BIT          (uint64_t)0x000000001
#define WALLY_DEBUG_TABLE_BIT           (uint64_t)0x000000002
#define WALLY_DEBUG_REGISTRATION_BIT    (uint64_t)0x000000004
#define WALLY_DEBUG_ROW_BIT             (uint64_t)0x000000008
#define WALLY_DEBUG_POSTGRES_BIT        (uint64_t)0x000000010
#define WALLY_DEBUG_FOHH_CLIENT_ROW_BIT (uint64_t)0x000000020
#define WALLY_DEBUG_POSTGRES_POLL_BIT   (uint64_t)0x000000040
#define WALLY_DEBUG_POSTGRES_FC_BIT     (uint64_t)0x000000080
#define WALLY_DEBUG_POSTGRES_CONN_BIT   (uint64_t)0x000000100
#define WALLY_DEBUG_POSTGRES_EVENT_BIT  (uint64_t)0x000000200
#define WALLY_DEBUG_ROW_DETAIL_BIT      (uint64_t)0x000000400
#define WALLY_DEBUG_WRITE_ROW_BIT       (uint64_t)0x000000800
#define WALLY_DEBUG_POSTGRES_WRITE_BIT  (uint64_t)0x000001000
#define WALLY_DEBUG_TEST_ORIGIN_BIT     (uint64_t)0x000002000
#define WALLY_DEBUG_TIMEOUT_BIT         (uint64_t)0x000004000
#define WALLY_DEBUG_TABLE_CLEANUP_BIT   (uint64_t)0x000008000
#define WALLY_DEBUG_INTEREST_CB_BIT     (uint64_t)0x000010000
#define WALLY_DEBUG_POSTGRES_SQL_BIT    (uint64_t)0x000020000
#define WALLY_DEBUG_SYNC_PAUSE_BIT      (uint64_t)0x000040000
#define WALLY_DEBUG_HB_TIMEOUT_BIT      (uint64_t)0x000100000
#define WALLY_DEBUG_TABLE_QUEUE_BIT     (uint64_t)0x000200000
#define WALLY_DEBUG_MIC_BIT             (uint64_t)0x000400000
#define WALLY_DEBUG_RECOVERY_BIT        (uint64_t)0x000800000

#define WALLY_LOG(priority, format...) ARGO_LOG(wally_event_log, priority, "wally", ##format)
#define WALLY_LOG_NOT_IMPLEMENTED() WALLY_LOG(AL_ERROR, "Not Implemented")
#define WALLY_DEBUG_LOG(condition, format...) ARGO_DEBUG_LOG(condition, wally_event_log, argo_log_priority_debug, "wally", ##format)
#define WALLY_NOTICE_LOG(condition, format...) ARGO_DEBUG_LOG(condition, wally_event_log, argo_log_priority_notice, "wally", ##format)

#define WALLY_DEBUG_RESULT(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_RESULT_BIT, ##format)
#define WALLY_DEBUG_TABLE(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_TABLE_BIT, ##format)
#define WALLY_DEBUG_REGISTRATION(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_REGISTRATION_BIT, ##format)
#define WALLY_DEBUG_ROW(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_ROW_BIT, ##format)
#define WALLY_DEBUG_POSTGRES(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_BIT, ##format)
#define WALLY_DEBUG_FOHH_CLIENT_ROW(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_FOHH_CLIENT_ROW_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_POLL(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_POLL_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_FC(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_FC_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_CONN(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_CONN_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_EVENT(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_EVENT_BIT, ##format)
#define WALLY_DEBUG_ROW_DETAIL(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_ROW_DETAIL_BIT, ##format)
#define WALLY_DEBUG_WRITE_ROW(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_WRITE_ROW_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_WRITE(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_WRITE_BIT, ##format)
#define WALLY_DEBUG_TEST_ORIGIN(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_TEST_ORIGIN_BIT, ##format)
#define WALLY_DEBUG_TIMEOUT(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_TIMEOUT_BIT, ##format)
#define WALLY_DEBUG_TABLE_CLEANUP(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_TABLE_CLEANUP_BIT, ##format)
#define WALLY_DEBUG_INTEREST_CB(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_INTEREST_CB_BIT, ##format)
#define WALLY_DEBUG_POSTGRES_SQL(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_POSTGRES_SQL_BIT, ##format)
#define WALLY_DEBUG_SYNC_PAUSE(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_SYNC_PAUSE_BIT, ##format)
#define WALLY_DEBUG_HB_TIMEOUT(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_HB_TIMEOUT_BIT, ##format)
#define WALLY_DEBUG_TABLE_QUEUE(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_TABLE_QUEUE_BIT, ##format)
#define WALLY_DEBUG_MIC(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_MIC_BIT, ##format)
#define WALLY_DEBUG_RECOVERY(format...) WALLY_DEBUG_LOG(wally_debug & WALLY_DEBUG_RECOVERY_BIT, ##format)

extern struct argo_log_collection *wally_event_log;
extern struct argo_log_collection *wally_stats_log;

ZLIST_HEAD(wally_registrant_head, wally_registrant);
ZLIST_HEAD(wally_registrant_callback_list_head, wally_registrant_callback);
ZTAILQ_HEAD(wally_registrant_callback_tailq_head, wally_registrant_callback);
ZTAILQ_HEAD(wally_interest_head, wally_interest);
ZLIST_HEAD(wally_row_head, wally_row);
ZTAILQ_HEAD(wally_local_db_write_q_head, wally_local_db_write_q);
ZTAILQ_HEAD(wally_update_tbl_exists_queue_head, wally_tbl_update_exists_queue);

struct table_cleanup_parameter {
    enum wally_table_cleanup_status state;
    int64_t max_cleanup_rows;
    int64_t max_scan_rows;
    int64_t cleanup_interval_us;
    int64_t row_expire_sec;
    int64_t min_seq_auto_update;
};

struct table_cleanup_status {
    int64_t rotated_rows;
    int64_t deleted_rows;
    int64_t scanned_rows;
    int64_t set_min_seq_times;
    int64_t max_retry_count;
    int64_t scan_time_us;
    int64_t clean_time_us;
    int64_t request_time_us;
    int64_t process_time_us;
    int64_t retry_time_us;
    int64_t retry_time_poll_us;
    int64_t retry_time_busy_us;
    int64_t retry_time_waiting_us;
    int64_t retry_time_error_us;
    double process_rate; /* processed rows per second */

    /* used for calculate process_rate, reset every time when reach end of table */
    unsigned restart:1;
    int64_t start_time_us;
    int64_t elapsed_us;
    int64_t scanned_count;
    int64_t touched_count; /* deleted + rotated */
};

struct registrant_status {
    int64_t total_requests;
    int64_t total_failed_requests;
    int64_t total_response;
    int64_t total_response_60s;
    int64_t response_time_60s[MAX_REG_REQ_CNT];
    int64_t max_response_us;
    int64_t min_response_us;
    int64_t interest_duplicate;
    int64_t row_notification_count;
    int64_t row_notification_failed_count;
    int64_t total_deregister_count;
    int64_t interest_state_count[WALLY_MAX_INT_STATE_COUNT];
    int64_t last_stats_update_us;
    int64_t interest_registered_his[WALLY_INT_REG_HIS_SIZE];
    int64_t total_interest_registration_60s;
    int64_t interest_registration_60s[MAX_REG_REQ_CNT];
    int64_t max_interest_registration_us;
    int64_t min_interest_registration_us;

};

/*
 * Callbacks from wally. All callbacks were registered by some
 * registrant or other. The callback "lives" on an interest
 * normally. However, if the registrant backs off its callback
 * (i.e. buffers full transmitting or some such) then the the callback
 * will be "moved" to the registrant feed list while the asynchronous
 * feeding occurs. Once the feeding is complete, the callback will be
 * moved back to the interest.
 *
 * There are two meshed lists of callbacks:
 *
 * interest_list- The list of all callbacks associated with a
 *    particular interest.
 *
 * registrant_list- The list of all callbacks associate with a
 *    particular registrant.
 *
 * feed_list- The queue of callbacks waiting to send data to a
 *    registrant. (Used when the registrant blocks)
 *
 * registrant - Any one callback can only be associated with one
 *    registrant.
 *
 * interest - Any one callback can only be associated with one
 *    interest.
 *
 * sequence - The last ID successfully sent to the callback. i.e. the
 *    next sequence to send is > than this value, and is NOT this
 *    value.
 *
 * callback/callback_cookie/callback_request_sequence are set to NULL
 *    after first real callback.
 *
 * callback_request_id is the ID used by the remote client that is
 *    accessing our state. (Doesn't really belong here, but the fohh
 *    server side becomes ridiculously easy when it can be stored
 *    here.)
 *
 * is_feeding - Whether or not this callback is on the registrant's
 *    feed list. (i.e. whether it still has data to send to its
 *    registrant that it hasn't sent yet- probably because it would
 *    have blocked)
 *
 * just_callback - Whether or not this callback is ONLY to be used for
 *    calling back- i.e. not interested in the row callbacks. (because
 *    some other callback is already receiving them)
 *
 * So many ptrs here. Wish this weren't so big. Looks like 8 ptrs- 64
 * bytes atm.
 */
struct wally_registrant_callback {
    /* Set of registered callbacks for a specific interest.  This
     * callback gets shifted between an interest and a registrant
     * based on whether or not the registrant can accept row callback
     * data. */
    ZLIST_ENTRY(wally_registrant_callback) interest_list;
    ZLIST_ENTRY(wally_registrant_callback) registrant_list;
    ZTAILQ_ENTRY(wally_registrant_callback) feed_list;
    struct wally_registrant *registrant;
    struct wally_interest *interest;
    int64_t sequence;  /* The sequence of the last row
                        * transmitted. Sequence zero is never used,
                        * and is thus a good initial value for
                        * searches. This value may be ignored for rows
                        * arriving if the initial sequence was zero
                        * when the cb was registered. This difference
                        * in behavior is necessary to deal with tables
                        * with multiple overlapping indexes that may
                        * arrive out of order. (The request with
                        * initial sequence zero will have all rows
                        * arriving at wally (matching the column
                        * value) sent to it, regardless of sequence
                        * number. If initial sequence is non-zero,
                        * then only in-order rows are allowed. */
    int64_t original_sequence; /* For logging purposes */
    wally_response_callback_f *callback;
    void *callback_cookie;
    struct zevent_base *callback_to_zevent_base;
    int64_t callback_request_id;
    int8_t grab_all_rows; /* Only set if initial requested sequence is zero */
    int8_t is_feeding;
    int8_t just_callback;
    int8_t request_atleast_one;

    int row_count;  /* The number of rows sent to registrant. Used for
                     * response callback when response callback is
                     * sent, and is an ongoing value */

    int filter_count;
    int row_count_complete; /* The number of rows that had been sent
                             * when complete was sent */
    int64_t request_received_us; /* When the request was received */
    int64_t request_complete_us; /* When the request complete was sent */
    int64_t last_row_us; /* Time when we got our most recent row */
    struct zhash_table* filter_table_hash;
    struct zhash_table *nullify_state;
};

enum recovery_state_t {
    recovery_state_noactive  = 0,
    recovery_state_begin,
    recovery_state_rowsyncstart,
    recovery_state_rowsyncend,
    recovery_state_end,
    recovery_state_complete,
    recovery_state_timeout,
    recovery_state_failure,
    recovery_state_stopped
};

struct wally_recovery_update {
    char table_name[MAX_TABLE_NAME_LEN];
    int64_t recovery_sequence;
};

struct wally_lookup_table {
	char table_name[MAX_TABLE_NAME_LEN];
	int column_index;
	int64_t max_sequence_seen;
	char *value;
};
enum wally_db_operation_t {
    db_oper_rowargo_object,
    db_oper_lookuptable_update,
    db_oper_recovery_update
};
struct wally_local_db_write_queue_object {
    enum wally_db_operation_t db_op;
	union {
	struct argo_object *row_to_write;
	struct wally_lookup_table lookup_table_write;
    struct wally_recovery_update recovery_update;
	} data;
};
/* Queue of objects to write to local DB.
 * is_lookuptable_update is true then queue object is for lookuptable write
 * is_lookuptable_update is false then queue object is for row write to main table
 */

struct wally_local_db_write_q {
	struct wally_local_db_write_queue_object *wrq_obj;
	ZTAILQ_ENTRY(wally_local_db_write_q) write_queue;
};

#if 0
/*
 * Queue of row objects to write.
 */
struct wally_row_queue {
    struct argo_object *row_to_write;
    ZTAILQ_ENTRY(wally_row_queue) write_queue;
};

#endif

/*
 * Per-row state. Used internally only.
 */
struct wally_row {
    struct argo_object *current_row;
    struct argo_object *previous_row;

    /* The following DB indexes are LOCAL to the TABLE within which
     * the row resides. They do NOT necessarily map directly to the
     * wally origin database indexes. */
    int current_row_origin_db_index;

    /* The count of registered interests. Basically a reference count. */
    int registered_interest_count;

    /* The set of rows matching a particular index. There is one
     * linked list per indexed column. If indexing column count can be
     * to be complete before any row allocation, then this could be
     * allocated state. (to save space)- in fact, that allocated state
     * could live right off the end of the row structure...  in_list
     * is just a membership indication, since it is not very easy to
     * predetermine when a row is in each list... */
    char in_list[(WALLY_MAX_INDEXED_COLUMNS + 7) / 8];
    ZLIST_ENTRY(wally_row) index_match_list[WALLY_MAX_INDEXED_COLUMNS];
};

/* WARNING: This must EXACTLY match struct wally_interest_stats from wally.h */
#define INTEREST_DB_STATE_XX(XX)                                                            \
    XX(unregistered, "unregistered", "unregisterd")                                         \
    XX(registered_waiting_for_response, "registered_waiting_for_response", "reg_waiting")   \
    XX(registered, "registered", "registered")                                              \
    XX(registration_xmit_pending, "registration_xmit_pending", "reg_xmit_pend")             \
    XX(deregistration_xmit_pending, "deregistration_xmit_pending", "dereg_xmit_pend")       \
    XX(passive, "passive", "passive")                                                       \
    XX(deleted, "deleted", "deleted")

/*
 * The state of this interest with respect to its DB.
 */
enum interest_db_state {
#define XX(a, b, c) a,
    INTEREST_DB_STATE_XX(XX)
#undef XX
};


extern char *wally_origin_state_strings[];
extern char *wally_origin_state_short_strings[];


/*
 * Interest state is in several distinct pieces:
 *
 * - Generic interest state
 *
 * - Registration state - State tracking this interest being
 *   registered with origin databases.
 *
 * - Registrant state - State tracking the behavior of consumers of
 *   this interest.
 *
 * - Completeness state - State tracking whether or not this interest
 *   has retrieved a complete set of state from origin databases
 *
 * - Row state - The rows belonging to this interest.
 *
 */
struct wally_interest {
    /***************************************************
     * Interest state, generic:
     */
    struct wally_index_column *column;
    /* The unique key. */
    union {
        struct {
            uint64_t key;
        };
        char *key_data;
    };
    size_t key_length;

    /* Ish. Remove this for memory savings of the largish nature */
    char *description;

    /* We need to not ever delete a passive interest. (This should
     * really be done better in general...) */
    unsigned is_passive:1;
    /* Logged is used to log an issue once, not repeatedly. */
    unsigned logged:1;
    /* Whether this interest is 'piercing' fully loaded config */
    unsigned pierce_fully_loaded:1;

    /*
     * The last time this interest was accessed by the default
     * registrant. Used for timeouts. (Only the default registrant
     * will time out interests)
     *
     * This value will be ZERO if the default registrant has not
     * registered an interest.
     */
    int64_t default_registrant_last_access_timestamp;


    /***************************************************
     * Registration (Origin DB) state:
     *
     * origin_db_state: Status of the interest.
     *    - registration xmit pending
     *    - deregistration xmit pending
     *    - registered
     *    - unregistered
     *
     * origin_db_index- Which DB is currently used.
     *
     * origin_db_promote- If set, then we need to promote this interest.
     *
     * origin_incarnation- The incarnation of the origin database to
     *   which we are registered.
     *
     * origin_db_reqeust_id - The ID number of the outstanding request
     *   to the DB.
     *
     * origin_db_reqeust_timestamp - The time at which the request was sent.
     */
    enum interest_db_state origin_db_state;
    int64_t origin_db_waiting_for_response_start_us;
    int64_t origin_db_waiting_for_response_complete_us;
    int8_t origin_db_index;
    int8_t origin_db_promote;
    int origin_incarnation;
    int64_t origin_db_request_id;
    int64_t origin_db_request_timestamp;
    /* Max sequence used to track what sequence number to promote. */
    int64_t max_sequence_seen;
    /* Either the origin DB's registration or deregistration queue */
    ZTAILQ_ENTRY(wally_interest) origin_db_processing;
    /* origin_db_processing enqueue epoch time, which is used to
     * find the delay in interest processing and skip the interest
     * if it is blocked */
    int64_t db_block_queue_insert_us;
    uint8_t still_in_register_pending_list;
    uint8_t still_in_deregister_pending_list;

    /***************************************************
     * Completeness state:
     *
     * Once request_complete has been set, we have completed asking at
     * least one origin DB for its state. For row fetch via the
     * 'get_rows' calls, this is the only status that indicates
     * whether or not the existing state is complete.
     *
     * Once request_complete_atleast_one has been set, we have
     * completed asking origin databases for data until at least one
     * row has successfully arrived or the last origin DB has
     * completed its query. This status can only be used for table/row
     * registrations. (not for 'get_rows' calls)
     */
    unsigned request_complete:1;
    unsigned request_complete_atleast_one:1;

    uint8_t waiting_response_time_hist;

    /***************************************************
     *
     * Row state
     */
    struct wally_row_head rows;
    /* Timestamp of last row arrival */
    int64_t last_row_us;
    int64_t interest_list_row_count;

    /**************************************************
     * Registrant state:
     *
     * List of callbacks to registrants interested in this interest.
     * A list here could be slow if there is a lot of deregistering
     * single interests going on. Note that mass deregistration will
     * be efficient- it is large scale deregistration that could be
     * stressful to the system. Note that this large-scale
     * deregistration will occur when intermediate systems have
     * clients disconnecting. Finally, note that registrant callbacks
     * either live on this list or on a the "feed" callbacks list of
     * the registrant itself.
     */
    struct wally_registrant_callback_list_head registrant_callbacks;
    /*
     * Since callbacks for this interest will sometimes live on the
     * registrant queue, we keep a reference count of them. Only when
     * this reference count reaches zero can this interest be
     * removed. i.e. this is NOT always equal to the number of
     * elements of the registrant_callbacks list above.
     */

    int registrant_callback_count;
    int ext_reg_cb_count;
};

/*
 * Column information... Used for accessing/manipulating column
 * information in wally. Pretty reliant on internal argo information.
 *
 * There does not need to be a wally_index_column for every column in
 * a table- just a wally_index_column for every column against which
 * you wish to perform lookups/have indexed.
 *
 * Wally automatically creates indexed columns for the index and
 * sequence columns.
 *
 * These columns do NOT need to have unique keys. Only the table
 * index/sequence columns must have unique keys.
 *
 * Column information within the argo structure/row can ONLY be 64-bit
 * int or a referenced string. Nothing else is allowed. (And column
 * registration will fail if it is attempted.)
 *
 * table : The table this column belongs to.
 *
 * argo_field_index : The argo field index for his column. (May not be
 *      needed, in the end)
 *
 * is_varlen : If non-zero, means this column is a variable length
 *     binary string (or text) column. Behaves a bit different from
 *     integer column, but not much
 *
 * is_null : If non-zero, means that this column is not keyed. That
 *     is, the only valid interest is the single interest representing
 *     the while table. It is impossible to ask for individual
 *     rows. It is only possible to ask for all rows. The sequence
 *     numbers of all the callbacks, etc, are used (like normal) to do
 *     interest propagation.
 *
 * my_index : which column this is for this table, starting at 0.
 *
 * (string_)index_hash : May be NULL. Hash table lookup for the
 *     column. This contains wally_interest objects. If ther index is
 *     unique per row, then there will be a 1:1 relationship between
 *     interests and rows. (Not required, though)
 *
 * tree : May be NULL. AVL tree for the column, keyed on the column,
 *     then the sequence column. (Used for interest propagation)
 */
struct wally_index_column {
    struct wally_table *table;
    char *name;
    int argo_field_index;
    enum argo_field_data_type data_type;
    int is_null;
    int my_index;

    /*
     * The interests indexed by the column. Interests are unique per
     * index. If the index is unique per row, then there will be a 1:1
     * relationship between interests and rows. But this is not
     * required. (though it is assumed for the primary key for a
     * table)
     */
    union {
        struct zhash_table *index_hash;
        struct zhash_table *string_index_hash;
        struct wally_interest *null_column_interest;
    };
    struct avl_tree *tree;

    struct zhash_table *paused_keys;
};


/*
 * Handle for a regisrant. Used to track registrations, surprisingly
 * enough.
 *
 * A registrant is only valid for one table.
 */
struct wally_registrant {
    /* The table for this registration. */
    struct wally_table *table;

    /* The set of callbacks this registrant has registered. */
    struct wally_registrant_callback_list_head all_registrant_callbacks;

    /* The callbacks waiting to feed us data- Used to do backoff when
     * blocking on row callbacks. This queue will be empty in steady
     * state. Note that feed_registrant_callbacks uses different list
     * state in the callback structure than the
     * all_registrant_callbacks list. */
    struct wally_registrant_callback_tailq_head feed_registrant_callbacks;

    /* Generic table-wide callbacks. */
    wally_row_callback_f *row_callback;
    void *row_callback_cookie;

    /* Whether or not callbacks are blocking for this registrant. */
    int currently_blocking;
    int64_t turnaround_time_us;

    /* Debug string for this registrant. Allocated/freed */
    char *debug_str;

    /* Membership in list of all registrants. */
    ZLIST_ENTRY(wally_registrant) all_registrants;
};

enum recovery_status_t {
    recovery_status_invalid = 0,
    recovery_status_complete,
    recovery_status_incomplete
};

struct wally_recovery_state {
    /* Recovery state */
    enum recovery_state_t state;    /* Recovery state */
    int64_t sequence;               /* Recovery Sequence */
    int64_t timeout;                /* Recovery Timeout */
    int sync_missing_rows;          /* Sync only missing rows or all rows */
    int64_t pending_send_rows;      /* Rows pending in fohh queue */
    int64_t start_time;             /* Recovery Start Time */
    int64_t server_sent_rows;       /* Number of rows sent by server to client */
    int64_t endreq_sent_time;       /* End request sent time */
    int64_t prev_max_sequence;      /* Max Sequence previous to recovery */
    int stop_processing_rows;       /* flag to support stop command while reading rows */
    /* Stats for recovery operation */
    int64_t recovery_requests;      /* Number of recovery requests received */
    int64_t begin_requests;         /* Number of recovery begin requests sent */
    int64_t end_requests;           /* Number of recover end requests sent */
    int64_t complete_responses;     /* Number of complete responses received */
    int64_t pending_responses;      /* Number of pending responses from client */
    int64_t received_rows;          /* Received rows from remote wally */
    int64_t recovered_rows;         /* Recovered missing rows out of received rows */
    int64_t duration;               /* Duration from recovery start time */
    int64_t clients;                /* Number of clients for which recovery initiated */
    int64_t failures;               /* Number of recovery failures */
    enum recovery_status_t status;  /* Recovery status - complete or incomplete */
    int64_t incomplete_reports;     /* Incomplete recovery reports from clients */
    int64_t total_timeout;          /* Total number of time recovery timeout */
    int64_t end_request_received;   /* End request received but still rows can be read*/
};

struct wally_recovery_stats {
    /* Stats for recovery reporting */
    enum recovery_state_t state;    /* Recovery state */
    int64_t start_time;             /* Recovery Sequence */
    int64_t recovery_requests;      /* Number of recovery requests received */
    int64_t begin_requests;         /* Number of recovery begin requests sent */
    int64_t end_requests;           /* Number of recover end requests sent */
    int64_t complete_responses;     /* Number of complete responses received */
    int64_t sequence;               /* Recovery Sequence */
    int64_t prev_sequence;          /* Max Sequence previous to recovery */
    int64_t received_rows;          /* Received rows from remote wally */
    int64_t recovered_rows;         /* Recovered missing rows out of received rows */
    int64_t duration;               /* Duration from recovery start time */
    int64_t clients;                /* Number of clients for which recovery initiated */
    int64_t failures;               /* Number of recovery failures */
    enum recovery_status_t status;  /* Recovery status - complete or incomplete */
    int64_t incomplete_reports;     /* Incomplete recovery reports from clients */
    int64_t total_timeout;          /* Total number of time recovery timeout */
};

/*
 * Wally table state.
 */
struct wally_table {
    /* Table name. This is based on argo directly. */
    char *name;

    /* Database name. This is usually the same as name, but may in rare cases be different */
    char *db_name;

    /* Reference to our wally. */
    struct wally *wally;

    /* Argo description of structure representing this row. */
    struct argo_structure_description *argo_description;

    wally_row_fixup_f *row_fixup_f;

    /* Indexes we have for columns. Hashed by column name. These are
     * only needed for columns that must be indexed. */
    struct zhash_table *column_indexes;
    struct wally_index_column *indexed_columns[WALLY_MAX_INDEXED_COLUMNS];
    size_t indexed_columns_count;
    int last_registered_column;

    /* Column sequences. TBD. */
    struct zhash_table *column_sequences;

    /* Default registrant for local access to wally. Used for
     * user-api, basically. */
    struct wally_registrant *default_registrant;

    /* Key hash table- to get to rows. integer_key, if non-zero,
     * indicates that the key is an integer rather than a
     * string. (integer being the common case) */
    int integer_key;
    union {
        struct zhash_table *key_rows_integer;
        struct zhash_table *key_rows_string;
    };

    /* Count of interests, and their states */
    int64_t interest_state_count[10];

    /* Argo field index of key. */
    int row_key_argo_index;
    int row_sequence_argo_index;
    int row_deleted_argo_index; /* -1 if doesn't exist */

    /* The set of origin databases this table uses. */
    /* We prefer to return data from the origin of the HIGHEST index
     * (i.e. added last) */
    struct wally_origin *origins[WALLY_MAX_ORIGINS];
    /* The write queue this table uses to write to the origin
     * database. Seperate queues for each table, to ensure a wee bit
     * of fairness. */
    int origin_write_queue_index[WALLY_MAX_ORIGINS];
    int global_to_local_origin_index_map[WALLY_MAX_ORIGINS];
    size_t origins_count;

    int is_row_writable;
    int multiple_index_consistency;

    /*
     * Fully_loaded. If a table is 'fully loaded' it is assumed that
     * the entireity of the table is read into memory. There is a
     * specific registration routine for placing a table into this
     * mode. It cannot be undone.
     */
    int fully_loaded;
    int64_t fully_loaded_start_epoch_us;
    int64_t fully_loaded_end_epoch_us;

    /* List of all registrants. */
    struct wally_registrant_head all_registrants;

    /* Number of rows in a table */
    int64_t row_count;

    /*
     * Table pause/resume operations
     * a db(eg. i_0) can be asked to pause receiving updates from remote.
     * NOTE: Used only for updates coming from remote origins. (currently only wally_fohh_client).
     *       For updates coming from local origins, this is not used.
     *
     * This functionality is used on selected brokers in production as a means to
     * canary global data change, making sure it works on some brokers before letting
     * the whole cloud getting it.
     */
    int is_pausable;                /* indicates if table is registered for sync pause */
    int paused;                     /* actual state of table; 1: paused 0: unpaused */
    enum table_pause_status pause_status;     /* Status of pause/resume */
    int64_t max_sequence_seen;      /* max sequence seen uptil now for the table */
    int64_t paused_seq;             /* seq num at which table pause got invoked */
    int64_t max_seq_paused;         /* Max sequence paused uptil now - only for stats purpose */
    /* stats for pause/resume */
    int64_t table_pause_count;      /* total times the table was paused */
    int64_t table_resume_count;     /* total times the table was resumed */
    int64_t table_resumed_err;       /* cumulative count of errors in table resume */
    int64_t rows_skipped_count;      /* cumulative count of rows paused when in paused state */
    int64_t rows_resumed_count;     /* cumulative count of rows resumed */

    /*
     * cleanup Table
     */

    /* set for unit test only, never set it for product */
    int cleanup_unit_test;

    /* timestamp for last cleanup */
    int64_t last_cleanup_us;

    /* timestamp for last update */
    int64_t last_update_time_s;

    /* timestamp for last update from remote*/
    int64_t last_remote_update_time_s;

    /* stop cleaning here, and switch to scanning state*/
    int64_t cleanup_stop_sequence;
    int64_t cleanup_stop_key_int;

    /* next scan start from this row */
    struct wally_row cleanup_scan_start_row;

    /* how many time it fail untill success */
    int cleanup_retry_count;

    /* temporary buffers */
    int64_t *cleanup_sequence;
    int64_t *cleanup_deleted;
    int64_t *cleanup_key_int;
    struct wally_row **cleanup_rows;
    int cleanup_buf_sz;

    /* configuraions */
    struct table_cleanup_parameter cleanup_param;

    /* statistical data for performanece */
    struct table_cleanup_status cleanup_stat;

    /* Number of interest in a table */
    int64_t interest_count;
    int64_t interest_pending_count_window[INTEREST_PENDING_WINDOW_SIZE];
    int64_t interest_pending_long;

    struct argo_log_registered_structure *wally_table_stats_structure;
    struct wally_table_stats *wt_stats;
    /* Measurement of row processing */
    int64_t row_store_time;
    int64_t row_callback_time;
    int64_t row_process_time;

	/*Whether table exists in RDS or not */
	uint8_t exists;

    /* Table level lock */
    zpath_rwlock_t lock;

    /* Outstanding request state. Used to associate response messages
     * with origin DB requests. Note that response messages only
     * arrive from the origin DB the first time they are satisfied,
     * and not thereafter. i.e. requests can be removed from the hash
     * when a response with its sequence is seen. */
    int64_t request_id;
    struct zhash_table *requests;

    /* Recovery state for current recovery transaction */
    struct wally_recovery_state recovery_state;
    /* Previous recovery transaction state for status command */
    struct wally_recovery_state prev_recovery_state;
    /* Recovery stats for publish */
    struct wally_recovery_stats recovery_stats;

};

struct wally_write_queue_info {
    char *name;             // table name
    int wq_indx;            // write queue index in the origin
    int conn_indx;          // connection responsible for this queue
    int64_t count_in;       // number of rows enqueued
    int64_t count_out;      // number of rows dequeued
    int64_t count_fail;   // as name suggests, failed to write - review postgres logs to find out why
    int last_batch;         // number of rows written in the last batch (the connection held without releasing to freeq)
    int batch_size;         // number of rows in batch
    int64_t total_executed_batch; // total batch send to postgres server for update/insert
    int64_t total_success_batch;   // total batch get sucesfull response form postgres server for update/insert
    int64_t total_retry_batch;   // total batch retry after error msg from the postgres
    int64_t total_retry_conflict_batch;   // total batch retry after row conflict error from the postgres
    int64_t total_failed_batch;   // total batch get error response form postgres server for update/insert
    int64_t query_creation_time; // total time for prsing row and creation query
    int64_t query_execution_time;  // total time for execution of query
    int64_t last_sequence;  // total time for execution of query
    int64_t mem_reallocation;  // total time row object go out of memory
    int64_t batch_row_conflict;  // total number of time conflict upsert fail due to row conflict
    int64_t object_out_of_memory;  // total number of time conflict upsert fail due to row conflict
    int64_t retry;  // total time for execution of query
    int64_t sequence_unordered; // total unordered sequence
};

/*
 * Origin DB state.
 *
 * index - the index within WALLY for the DB. Note that tables may
 * have arbitrary mappings of origin DB's within each table.
 *
 * incarnation is updated every time an origin database
 * reconnects. (Used to do mass re-registration)
 */
struct wally_origin {
    struct wally *wally;
    char *name; /* For debugging only, really. */
    int my_index;
    int incarnation;
    enum wally_origin_status last_status;
    void *callout_handle;
    wally_xfer_callout_register_for_index_f *register_index;
    wally_xfer_callout_deregister_for_index_f *deregister_index;
    wally_xfer_callout_set_cookie_f *set_cookie;
    wally_xfer_callout_get_status_f *get_status;
    wally_xfer_callout_add_table_f *add_table;
    wally_xfer_callout_set_min_sequence_f *set_sequence;
    wally_xfer_callout_dump_state *dump_state;
    wally_xfer_callout_cleanup_f *cleanup;
    wally_xfer_callout_cmd_trigger_f *cmd_trigger;
    pthread_mutex_t interest_register_list_lock;
    struct wally_interest_head registering_interests_list;
    pthread_mutex_t interest_deregister_list_lock;
    struct wally_interest_head deregistering_interests_list;

    /* Indication whether this origin DB is writable, and the queue of
     * rows to be written. The row queue is polled by the origin DB,
     * for simplicity. (it does not need to be particularly
     * real-time.) */
    pthread_mutex_t write_queue_lock;
    int is_writable;
    struct wally_local_db_write_q_head write_queue[WALLY_MAX_TABLES];
    int write_queue_last_index;
    int write_queue_count;
    uint64_t write_queue_start_time;
    uint64_t write_queue_last_update_time;
    /* this is currently used for debugging/stats purposes */
    struct wally_write_queue_info wq_info[WALLY_MAX_TABLES];
    int pauseable;

	/* This queue is for processing the table exists or does not exists
     * request
	 */
    pthread_mutex_t tbl_update_exists_queue_lock;
    struct wally_update_tbl_exists_queue_head wally_tbl_update_exists_queue;

    /* Origin level lock */
    zpath_rwlock_t lock;
};



ZTAILQ_HEAD(wally_object_queue_head, wally_object_queue);
struct wally_object_queue {
    ZTAILQ_ENTRY(wally_object_queue) list;
    struct argo_object *object;
    int64_t timestamp_s;
};

/*
 * Wally state. Basically a lock, some hash tables, and not too much
 * else.
 */
struct wally {
    /* Name of this wally. Primarily for debugging. */
    char *name;

    /* User-set data we contain here, for convenience. */
    void *custom_data;

    /* Callback function for registering debug commands for each
     * indexed column created in this wally. */
    wally_debug_register_f *debug_registration_callback;
    void *callback_cookie;

    zpath_mutex_t slow_free_object_list_lock;
    struct wally_object_queue_head slow_free_object_list;

    /* All the tables this wally manages. */
    struct wally_table *tables[WALLY_MAX_TABLES];
    size_t tables_count;
    struct zhash_table *tables_hash; /* Hashed on name */

    /* All the origin databases we connect to, either local or
     * remote. */
    struct wally_origin *origins[WALLY_MAX_ORIGINS];
    size_t origins_count;

    /* Thread for managing timeouts and some callbacks */
    pthread_t timeout_thread;

    /* Global database read-write lock. */
    zpath_rwlock_t lock;


    /* Zthread context */
    struct zthread_info *zthread;
    struct event *timeout_thread_event;
    int timeout_cleanup_table_index;
    int timeout_table_index;
    int timeout_column_index;
    int64_t timeout_walk_key;

    bool table_cleanup_pause;        /*No connection to RDS, trimmer operation not allowed*/

    /* Heartbeat tick count.
     * Number of times heartbeat tick happened after wally_max_hb_timeout_s
     * timer, which tells the system is highly loaded and processing takes
     * more time than expected.
     */
    uint64_t batch_hb_count;

    int64_t drop_incompatible_version;

    int64_t num_server_connect;
    int64_t num_server_disconnect;
    int64_t num_server_active;
    int64_t num_client_connect;
    int64_t num_client_disconnect;
    int64_t num_client_active;

    // All flags can go here
    uint8_t is_endpoint            : 1; /* An indication that this wally is an end-point node. End point
                                          * nodes treat deletes directly- they actually get deleted.
                                          */
    uint8_t is_wallyd              : 1;
    uint8_t is_fully_load_complete : 1;
    uint8_t spare                  : 5;
    int64_t table_priority_idx;
    int64_t wally_tbl_intrst_reg_prio;

    int64_t last_sequence_write;

    /* Indicates if the wally is registered for sync pause */
    int is_pausable;
    struct argo_log_registered_structure *sync_pause_stats_reg;
    struct registrant_status registrant_stat;

    /* FOHH server handles for current wally */
    struct wally_fohh_server *wally_fohh_server_handle[WALLY_MAX_SERVER];
    /* Number of FOHH ipv4/v6 servers */
    int num_fohh_servers;

    /* Post resume callback function - called after all tables have been resumed */
    wally_post_resume_callback_f *post_resume_callback;
};

ZLIST_HEAD(wally_callback_head, wally_callback);

struct wally_callback {
    ZLIST_ENTRY(wally_callback) list;
    struct zevent_base *zbase;
    wally_response_callback_f *callback;
    void *void_cookie;
    int64_t int_cookie;
};

struct wally_callback_queue {
    zpath_mutex_t lock;
    struct wally_callback_head callbacks;
};

/* Some forward declarations... */
struct wally_registrant *wally_table_create_registrant_internal(struct wally_table *table,
                                                                wally_row_callback_f *row_callback,
                                                                void *row_callback_cookie,
                                                                const char *debug_str);

int send_register_interest_internal(struct wally_interest *interest);
int send_deregister_interest_internal(struct wally_interest *interest);
int wally_interest_state_machine(struct wally_interest *interest);
void wally_interest_delete(struct wally_interest *interest);

int wally_origin_enqueue_write_row(struct wally_origin *origin, int write_queue_index, struct argo_object *row, int insert_head);
int wally_origin_enqueue_write_object(struct wally_origin *origin, int write_queue_index,
		struct wally_local_db_write_queue_object *write_object, int insert_head);
int wally_origin_enqueue_write_lookup_table(struct wally_interest *interest, int insert_head);
struct wally_local_db_write_queue_object *wally_origin_dequeue_write_row(struct wally_origin *origin, int *queue_index);
struct wally_local_db_write_queue_object *wally_origin_dequeue_write_object_specific(struct wally_origin *origin, int queue_index);
int wally_origin_dequeue_write_objects_specific(struct wally_origin *origin, int queue_index, struct wally_local_db_write_queue_object *write_objects[]);
int wally_origin_enqueue_write_table_update_exists(struct wally_origin *origin, const char *table_name);
int wally_origin_dequeue_write_table_update_exists(struct wally_origin *origin);

void wally_table_destroy_registrant_internal(struct wally_registrant *registrant);
void wally_registrant_callback_destroy(struct wally_registrant_callback *cb);
int wally_table_process_row(struct wally_table *table, struct argo_object *object, int origin_index, int64_t request_id, int64_t *store_time, int64_t *cb_time);
int wally_column_is_inet(struct wally_index_column *column);

void wally_object_release_slow(struct wally *wally, struct argo_object *object);
void wally_periodic_free(struct wally *wally);
void wally_interest_result_callback_cb(struct wally_interest *interest, struct wally_registrant_callback *cb);

const char *wally_interest_state_str(enum interest_db_state state);

void interest_assign_state(struct wally_interest *interest, enum interest_db_state state);
void interest_remove_state(struct wally_interest *interest);
void interest_add_state(struct wally_interest *interest, enum interest_db_state state);

void registrant_interest_change_state_stat(struct wally_interest *interest, enum interest_db_state state);
void registrant_interest_add_state_stat(struct wally_interest *interest);
void registrant_interest_remove_state_stat(struct wally_interest *interest);

void wally_set_wallyd(struct wally *wally);
int wally_is_wallyd(struct wally *wally);

void wally_set_fully_load_complete(struct wally *wally);
int wally_is_fully_load_complete(struct wally *wally);
void wally_infinite_loop();
#endif /* __WALLY_PRIVATE_H__ */
