/*
 * zpn_enrollment.c. Copyright (C) 2018 Zscaler, Inc. All Rights Reserved.
 *
 */
#ifndef _GNU_SOURCE
#define _GNU_SOURCE 1
#endif

#ifndef _ZPN_ENROLLMENT_H_
#define _ZPN_ENROLLMENT_H_

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <signal.h>
#include <sys/stat.h>
#include <utime.h>

#include "avl/avl.h"

#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "wally/wally.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "zcdns/zcdns_libevent.h"
#include "zpath_misc/zpath_misc.h"
#include "zhw/zhw_id.h"
#include "zcrypt/zcrypt.h"
#include "base64/base64.h"
#include "parson/parson.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"

#include "zpn/zpn_lib.h"
#include "zvm/zvm.h"

#include "zthread/zthread.h"

#include "zpath_misc/zpath_platform.h"


/*
 * version file contains the version that the cloud wants the connector to be in.
 */
#define FILENAME_VERSION         "version"
#define FILENAME_RUNNING_VERSION "running_version"
#define FILENAME_ID              "instance_id.crypt"
#define FILENAME_FRR_VERSION     "frr_version"

#define FILENAME_PROVISION_CRYPT "provision_key.crypt"
#define FILENAME_PROVISION_KEY   "provision_key"

#define FILENAME_KEY             "rsa"
#define FILENAME_KEY_NEW         "rsa_new"
#define FILENAME_KEY_PRIV        "rsa_key.pem"
#define FILENAME_KEY_PUB         "rsa_pub.pem"

#define FILENAME_KEY_NEW_PRIV    "rsa_new_key.pem"
#define FILENAME_KEY_NEW_PUB     "rsa_new_pub.pem"

#define FILENAME_CERT            "cert.pem"

#define FILENAME_CHAIN           "chain.pem"
#define FILENAME_CERT_NEW        "cert_new.pem"
#define FILENAME_CSR_NEW         "csr_new.pem"
#define FILENAME_CHAIN_NEW       "chain_new.pem"

#define FILENAME_ROOT            "root.pem"
#define FILENAME_ROOT_NEW        "root_new.pem"
#define FILENAME_CLOUD_ROOT      "cloud.pem"

#define PROVISIONING_KEY_ENV     "PROVISION_KEY"
#define OPENSHIFT_ENV            "OPENSHIFT_ENVIRONMENT"
#define KUBERNETES_ENV           "KUBERNETES_ENVIRONMENT"

#define SARGE_ROLE               "zpa-sarge"

#define FILENAME_OAUTH_DETAILS   "oauth_provisioning_key"
#define ENV_ZPA_OAUTH_ENROLLMENT "OAUTH_ENROLLMENT"
#define FILENAME_OAUTH_TOKEN     "oauth_token"

/*
 * Enrollment types
 */
enum zpn_enrollment_type {
    ZPN_ENROLLMENT_TYPE_ASSISTANT       = 1,
    ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER  = 2,
    ZPN_ENROLLMENT_TYPE_SITEC = 3,
    ZPN_ENROLLMENT_TYPE_SARGE = 4
};

/*
 * Enrollment style v1/v2/v3/v4
 */
enum zpn_enrollment_style {
    ZPN_ENROLLMENT_STYLE_V1 = 1,        /* V1 style - Old: assistant only */
    ZPN_ENROLLMENT_STYLE_V2 = 2,        /* V2 style - New: assistant/broker/znf */
    ZPN_ENROLLMENT_STYLE_V3 = 3,        /* V3 style - Latest: assistant/broker/znf */
    ZPN_ENROLLMENT_STYLE_V4 = 4         /* V4 style - Oauth2.0 Enrollment: assistant/pcc/pse */
};

/*
 * Enrollment API endpoint types
 */
#define ENROLLMENT_API_ENDPOINT_TYPE_ASSISTANT      "assistant"
#define ENROLLMENT_API_ENDPOINT_TYPE_PRIVATE_BROKER "broker"
#define ENROLLMENT_API_ENDPOINT_TYPE_SITEC          "site_controller"

#define CFG_PROVISION_KEY_SIZE 1024

/*
 * Enrollment api types
 */
enum zpn_enrollment_api_type {
    ENROLLMENT_API_SIGN_IN = 1,
    ENROLLMENT_API_FETCH_CSR_DETAILS = 2,
    ENROLLMENT_API_SEND_CSR = 3,
    ENROLLMENT_API_GET_CERTIFICATE = 4,
    ENROLLMENT_API_GET_ENROLL_DETAILS = 5,
    ENROLLMENT_API_SEND_CSR_AND_GET_CERT = 6,
    ENROLLMENT_API_SEND_PUB_KEY = 7
};

/*
 * Re-enroll time factors
 */
#define ZPN_RE_ENROLL_TIME_FACTOR          90
#define ZPN_RE_ENROLL_LEAST_DAYS           180

/* Enrollment API buf size */
#define ENROLLMENT_API_BUF_SIZE             256

#define INSTANCE_ID_BYTES ZHW_ID_BYTES
#define INSTANCE_ID_ENCRYPTED_BYTES         64

/* Blessed state */
#define ENROLLMENT_API_INIT_MAGIC          0xb1e55ed

/* Slow stop time */
#define SLOW_STOP_TIME_S                   300

#define SUB_MODULE_FAIL_TIME               600

#define COMPONENT_NAME_LEN                  64

#define API_NAME_BUF_SIZE                   512

/* Number of bytes of provisioning key to log, initial prefix is like: "3|api-f4.dev.zpath.net|", 23 bytes */
#define PROVISION_KEY_LOG_BYTES     35

/* Number of bytes of hardware fingerprint to log */
#define HW_FINGERPRINT_LOG_BYTES    10

/* Enrollment API state */
struct zpn_enroll_state {
    int64_t magic;                                  /* Magic to indicate initialized state */
    enum zpn_enrollment_type    enroll_type;        /* Enrolling assistant/private_broker ? */
    enum zpn_enrollment_style   enroll_style;       /* V1 or V2 or V3 or V4*/
    struct zcrypt_rsa_key *cfg_rsa_key;             /* Rsa key */
    struct zcdns *zcdns;                            /* Cdns */
    char cfg_key_cloud[ENROLLMENT_API_BUF_SIZE];    /* Cloud name */
    int cfg_api_direct;                             /* Direct api */
    int cfg_write_plain;                            /* Output plaintext */
    int can_drop_fips;                              /* Disable fips */
    int always_re_enroll;                           /* Re-enroll */
    int64_t upgrade_id;                             /* Either assistant_id or broker_id based on enroll_type */
    void *upgrade_state;                            /* Either struct zpn_assistant_state* or struct zpn_private_broker_state* depending on the entity being upgraded */
};

/*
 * Enroll API init - Returned state is pointer to static API storgae owned by library
 */
extern int zpn_enroll_init(struct zpn_enroll_state **state);

/*
 * Get Instance ID
 */
extern int zpn_enroll_get_instance_id(struct zcrypt_key *key, uint8_t *instance_bytes);

/*
 * Extract Key Fields
 */
extern int
zpn_enroll_extract_key_fields(char *key, int *shard_id, char *api_name, size_t api_name_len, char *cloud_name,
                              size_t cloud_name_len, char **root_cert, time_t *root_cert_time);

/*
 * Get Provisioning Key
 */
extern int
zpn_enroll_get_provisioning_key(struct zcrypt_key *key, char *provisioning_key, size_t provisioning_key_length);

int zpn_enroll_get_oauth_key(char *provisioning_key, size_t provisioning_key_length);

/*
 * Get Provisioning Key:Sarge only api
 */
extern int
zpn_enroll_get_provision_key_from_plain_text_for_sarge(char *cfg_key_cloud,
                                                       int cfg_key_cloud_sz);


int zpn_get_provision_key_from_plain_text_for_sarge(char *provisioning_key, size_t provisioning_key_length);

/*
 * Get private key
 */
extern int zpn_enroll_get_private_key(struct zcrypt_key *key, struct zcrypt_rsa_key *rsa_key, char *filename);

/*
 * (Re)-Enroll Process:
 *
 * Have old key pair (optional)
 *
 * Get new keypair/write them.
 *
 * Login
 *
 * Get CSR/Write it.
 *
 * Post CSR
 *
 * Get Cert/Write it.
 *
 * Rename Keypair.
 * Rename Cert.
 * Remove CSR
 */
extern int zpn_enroll(struct zcrypt_key *key, struct zcrypt_rsa_key *old_rsa_key, char *hw_fingerprint, char *provisioning_key, int shard, char *api_hostname,
                      enum zpn_enrollment_type enroll_type, enum zpn_enrollment_style enroll_style, char *cloud, int is_re_enroll);

/*
 * Returns NO_ERROR if there is a valid cert to use.
 *
 * This routine will attempt to re-enroll if necessary.
 */
extern int
zpn_enroll_check(struct zcrypt_key *key, struct zcrypt_rsa_key *rsa_key, char *hw_fingerprint, char *provisioning_key,
                 int shard, char *api_hostname, enum zpn_enrollment_type enroll_type, enum zpn_enrollment_style enroll_style,
                 char *cloud, const char *cwd, int always_re_enroll, int64_t site_reenroll_period, int64_t customer_gid, const char *token);

/*
 * Update version
 */
extern int zpn_enroll_update_version(const char *filename, const char *new_version, char *written_version, size_t written_version_space);

/*
 * Upload stack
 */
extern void zpn_enroll_upload_stack(char *upload_host, char *upload_path, char *app_name, char *app_inst, uint64_t customer_id);

extern int zpn_admin_probe_s3_upload(char *upload_buffer, int size, char *hdr, int hdr_size, char *bucket_name, char *upload_path);
/*
 * Upgrade prep callback
 */
typedef int (zpn_enroll_upgrade_prep_callback)(int64_t upgrade_id, void *upgrade_state, int64_t time_delta, int auto_upgrade_disabled);

/*
 * Upgrade prep
 */
extern int zpn_enroll_upgrade_prep(zpn_enroll_upgrade_prep_callback *prep_callback, int64_t upgrade_id, void *upgrade_state, int64_t time_delta, int auto_upgrade_disabled);

/*
 * Get provisioning key env
 */
extern int zpn_enroll_get_provisioning_key_env(char *env, size_t env_len);

/*
 * Get provisioning key file
 */
extern int zpn_enroll_get_provisioning_key_file(char *env, size_t env_len);

/*
 * To get enrollment details. It provides the enrollment hostname and cloud cert along with root_cert_time.
 */
int zpn_enroll_get_enrollment_details(char *api_hostname,
                                      size_t api_hostname_len,
                                      struct evbuffer *body,
                                      char *cloud,
                                      char **root_cert,
                                      time_t *root_cert_time,
                                      int64_t *customer_gid,
                                      char *api_version,
                                      size_t api_version_len,
                                      char *offline_domain,
                                      size_t offline_domain_len,
                                      uint64_t *reenroll_period);

int zpn_enroll_send_public_key_per_entity_v3(const struct zcrypt_rsa_key *rsa_key,
                                             const char *api_hostname,
                                             enum zpn_enrollment_type enroll_type,
                                             const char *public_key_str,
                                             int64_t public_key_expiry_s,
                                             const char *provisioning_key,
                                             const char *hw_fingerprint,
                                             int64_t customer_gid);

struct evbuffer * zpn_enroll_get_enrollment_details_raw(const char *cloud_name,
                                    struct zcrypt_rsa_key *rsa_key,
                                    char *hw_fingerprint,
                                    char *provisioning_key,
                                    char *api_hostname,
                                    size_t api_hostname_len,
                                    enum zpn_enrollment_type enroll_type,
                                    enum zpn_enrollment_style enroll_style,
                                    const char *token,
                                    int64_t customer_gid);

int zpn_enroll_is_enrollment_completed(void);
#endif
