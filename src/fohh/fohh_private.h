/*
 * fohh_private.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Private info for fohh library.
 */

#ifndef _FOHH_PRIVATE_H_
#define _FOHH_PRIVATE_H_

#include "zpath_misc/zpath_misc.h"
#include "fohh/fohh.h"

#include <openssl/ssl.h>
#include <sys/queue.h>

#include "fohh/fohh_http_private.h"
#include "fohh/http_parser.h"
#include "zhash/zhash_table.h"
#include "fohh/fohh_rtt_histogram.h"
#include "fohh/fohh_history.h"
#include "fohh/fohh_tracker.h"
#include "fohh/fohh_resolver.h"

#define GUARDBUFF_SIZE             (256)

#define FOHH_CONN_FREE_Q_PRE_ALLOC_LIMIT      100
#define FOHH_CONN_FREE_Q_LIMIT_PERCENTAGE     10      /* as percentage of pre_alloc limit above */
#define FOHH_CONN_FREE_Q_LIMIT_VALUE          (FOHH_CONN_FREE_Q_PRE_ALLOC_LIMIT * FOHH_CONN_FREE_Q_LIMIT_PERCENTAGE / 100)
/*
 * Just some debugging...
 */
#define FOHH_DEBUG_QUEUE_BIT       (uint64_t)0x000000001
#define FOHH_DEBUG_CONN_BIT        (uint64_t)0x000000002
#define FOHH_DEBUG_SERIALIZE_BIT   (uint64_t)0x000000004
#define FOHH_DEBUG_RESOLVER_BIT    (uint64_t)0x000000008
/* PP = Proxy Protocol */
#define FOHH_DEBUG_HTTP_PP_BIT     (uint64_t)0x000000010
#define FOHH_DEBUG_HTTP_SERVER_BIT (uint64_t)0x000000020
#define FOHH_DEBUG_HTTP_PARSER_BIT (uint64_t)0x000000040
/* QUIET_BIT is really used to turn off some NOTICE messages. */
#define FOHH_DEBUG_QUIET_BIT       (uint64_t)0x000000080
#define FOHH_DEBUG_LOG_BIT         (uint64_t)0x000000100
#define FOHH_DEBUG_LOG_DETAIL_BIT  (uint64_t)0x000000200
#define FOHH_DEBUG_AUTOTUNE_BIT    (uint64_t)0x000000400
#define FOHH_DEBUG_SSL_ERRORS_BIT  (uint64_t)0x000000800
#define FOHH_DEBUG_FCONN_USAGE_BIT (uint64_t)0x000001000
#define FOHH_DEBUG_FCONN_BURST_BIT (uint64_t)0x000002000
#define FOHH_DEBUG_SITE_BIT        (uint64_t)0x000004000
/* FOHH fproxy debug bit */
#define FOHH_DEBUG_FPROXY_BIT      (uint64_t)0x000008000
#define FOHH_DEBUG_LATENCY_BIT     (uint64_t)0x000010000


#define FOHH_LOG(priority, format...) ARGO_LOG(fohh_g.event_log, priority, "fohh", ##format)
#define _FOHH_DEBUG_LOG(condition, format...) ARGO_DEBUG_LOG(condition, fohh_g.event_log, argo_log_priority_debug, "fohh", ##format)

#define FOHH_DEBUG_QUEUE(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_QUEUE_BIT, ##format)
#define FOHH_DEBUG_CONN(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_CONN_BIT, ##format)
#define FOHH_DEBUG_SERIALIZE(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_SERIALIZE_BIT, ##format)
#define FOHH_DEBUG_RESOLVER(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_RESOLVER_BIT, ##format)
#define FOHH_DEBUG_HTTP_PP(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_HTTP_PP_BIT, ##format)
#define FOHH_DEBUG_HTTP_SERVER(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_HTTP_SERVER_BIT, ##format)
#define FOHH_DEBUG_HTTP_PARSER(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT, ##format)
#define FOHH_NOTICE_QUIET(format...) ARGO_DEBUG_LOG(fohh_debug & FOHH_DEBUG_QUIET_BIT, fohh_g.event_log, argo_log_priority_notice, "fohh", ##format)
#define FOHH_DEBUG_LOG(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_LOG_BIT, ##format)
#define FOHH_DEBUG_LOG_DETAIL(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_LOG_DETAIL_BIT, ##format)
#define FOHH_DEBUG_AUTOTUNE(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_AUTOTUNE_BIT, ##format)
#define FOHH_DEBUG_SSL_ERRORS(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_SSL_ERRORS_BIT, ##format)
#define FOHH_DEBUG_FCONN_USAGE(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_FCONN_USAGE_BIT, ##format)
#define FOHH_DEBUG_BURST(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_FCONN_BURST_BIT, ##format)
#define FOHH_DEBUG_SITE(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_SITE_BIT, ##format)
#define FOHH_DEBUG_FPROXY(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_FPROXY_BIT, ##format)
#define FOHH_DEBUG_LATENCY(format...) _FOHH_DEBUG_LOG(fohh_debug & FOHH_DEBUG_LATENCY_BIT, ##format)

extern struct zpath_allocator fohh_allocator;
#define FOHH_MALLOC(x) zpath_malloc(&fohh_allocator, x, __LINE__, __FILE__)
#define FOHH_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define FOHH_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define FOHH_CALLOC(x) zpath_calloc(&fohh_allocator, x, __LINE__, __FILE__)
#define FOHH_STRDUP(x, y) zpath_strdup(&fohh_allocator, x, y, __LINE__, __FILE__)
#define FOHH_REALLOC(x, y) zpath_realloc(&fohh_allocator, x, y, __LINE__, __FILE__)

#define FOHH_TRIGGER(ip) if (fohh_g.trigger_start) (fohh_g.trigger_start)((ip))
#define FOHH_UNTRIGGER(ip) if (fohh_g.trigger_end) (fohh_g.trigger_end)((ip))

/* Maximum number of SNI callbacks before closing connection */
#define MAX_SNI_CALLBACKS 10

/* Maximum number of seconds to wait for SNI before closing connection */
#define MAX_SNI_WAIT 10

LIST_HEAD(fohh_connection_head, fohh_connection);

TAILQ_HEAD(fohh_generic_registrant_head, fohh_generic_registrant);

TAILQ_HEAD(fohh_connection_free_head, fohh_connection);

/*
 * One 'listener' is used per thread per listening address. so if
 * generic_server_listener_count is high, then these are used up quite
 * quickly.
 */
#define FOHH_GENERIC_LISTENER_MAX   1024

#define FOHH_SSL_MAX_VERIFY_DEPTH 5

// us
#define ZPN_FOHH_HOP_LATENCY_HISTOGRAM_MAX 10
#define ZPN_FOHH_HOP_LATENCY_BKT_0 10000
#define ZPN_FOHH_HOP_LATENCY_BKT_1 20000
#define ZPN_FOHH_HOP_LATENCY_BKT_2 30000
#define ZPN_FOHH_HOP_LATENCY_BKT_3 50000
#define ZPN_FOHH_HOP_LATENCY_BKT_4 75000
#define ZPN_FOHH_HOP_LATENCY_BKT_5 100000
#define ZPN_FOHH_HOP_LATENCY_BKT_6 250000
#define ZPN_FOHH_HOP_LATENCY_BKT_7 500000
#define ZPN_FOHH_HOP_LATENCY_BKT_8 1000000

// us
#define ZPN_FOHH_PIPELINE_LATENCY_HISTOGRAM_MAX 11
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_0 50
// under ideal conditions, most common bucket
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_1 100
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_2 250
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_3 500
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_4 1000
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_5 5000
// 10ms is likely concerning
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_6 10000
// 25ms is a very concerning internal latency; voice or other low latency
// apps see observed issues with total latency > 50ms
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_7 25000
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_8 50000
#define ZPN_FOHH_PIPELINE_LATENCY_BKT_9 100000

typedef void (fohh_accept_callback_f)(struct fohh_thread *thread, int sock, struct sockaddr *sa, int sa_len, void *cookie);

/*
 * Structure for tracking SNI server state.
 */
struct fohh_generic_listener {
    struct fohh_generic_server *server;
    struct fohh_thread *f_thread;
    struct evconnlistener *evlistener;
    struct sockaddr_storage address;
    int address_len;
    int do_proxy_protocol;
};

/* Only one of f_conn and accept_f will be non-null */
struct fohh_generic_registrant {
    TAILQ_ENTRY(fohh_generic_registrant) list;
    struct fohh_connection *f_conn;

    fohh_generic_server_pre_accept_f *pre_accept_f;
    fohh_generic_server_accept_f *accept_f;
    SSL_CTX *ssl_ctx_hahaha;
    void *cookie;
    fohh_generic_server_cookie_free_cb *cookie_free_cb;
    char *suffix;
    int wildcard_prefix;
    char *worker_pool;
    /* one held by fohh_generic_server, additional referenced may be held by fohh_generic_accept */
    uint32_t ref_count;
    int disabled;
};

/*
 * Structures for sending status back and forth.
 */
struct fohh_status_request {       /* _ARGO: object_definition */
    int64_t request_id;            /* _ARGO: integer */
    int64_t request_epoch_in_us;   /* _ARGO: integer */
    /* Desired interval in seconds between fohh_status_messages from the peer,
    or 0 for the default interval in seconds, for compatibility with older App Connectors etc. */
    int64_t status_interval;       /* _ARGO: integer */
    uint32_t transport_rtt_in_us;  /* _ARGO: integer */
};

struct fohh_status_response {       /* _ARGO: object_definition */
    int64_t request_id;             /* _ARGO: integer */
    int64_t request_epoch_in_us;    /* _ARGO: integer */
    int64_t response_epoch_in_us;   /* _ARGO: integer */
    int64_t received_bytes;         /* _ARGO: integer */
    uint32_t transport_rtt_in_us;   /* _ARGO: integer */
};

/*
 * fohh_connection_cipher_stats - Stores the count of clients using the specific
 * cipher suite.
 */
struct fohh_connection_cipher_stats {     /* _ARGO: object_definition */
    char cipher_suite_name[MAX_CIPHER_SUITE_NAME_LEN];        /* _ARGO: string */
    uint64_t connections_count;           /* _ARGO: integer */
};

/*
 * Status is information about the connection itself. It is generally
 * determined by querying our peer.
 */
struct fohh_connection_stats {     /* _ARGO: object_definition */
    char *description;             /* _ARGO: string */
    const char *state;             /* _ARGO: string */

    uint64_t latest_app_rtt_in_us; /* _ARGO: integer */
    uint64_t latest_tcp_rtt_in_us; /* _ARGO: integer */

    uint64_t transmit_bytes;       /* _ARGO: integer */
    uint64_t transmit_objects;     /* _ARGO: integer */
    uint64_t transmit_objects_fail;/* _ARGO: integer */
    uint64_t transmit_raw_tlv;     /* _ARGO: integer */
    uint64_t receive_bytes;        /* _ARGO: integer */
    uint64_t receive_objects;      /* _ARGO: integer */
    uint64_t receive_raw_tlv;      /* _ARGO: integer */

    uint64_t raw_tlv_buffer_len;     /* _ARGO: integer */
    uint64_t raw_tlv_buffer_len_max; /* _ARGO: integer */
    uint64_t raw_tlv_buffer_enq;     /* _ARGO: integer */
    uint64_t raw_tlv_buffer_deq;     /* _ARGO: integer */
    uint64_t bufferevent_out_len;    /* _ARGO: integer */
    uint64_t bufferevent_out_len_max;/* _ARGO: integer */
    uint64_t tx_sock_unsent;         /* _ARGO: integer */
    uint64_t tx_sock_unsent_max;     /* _ARGO: integer */
    uint64_t tlv_read_buf_len;       /* _ARGO: integer */
    uint64_t tlv_read_buf_len_max;   /* _ARGO: integer */
    uint64_t bufferevent_in_len;     /* _ARGO: integer */
    uint64_t bufferevent_in_len_max; /* _ARGO: integer */
    uint64_t rx_sock_unrcv;          /* _ARGO: integer */
    uint64_t rx_sock_unrcv_max;      /* _ARGO: integer */
    int64_t  last_reset_us;          /* _ARGO: integer */
    int64_t  dump_us;                /* _ARGO: integer */

    uint64_t transmit_fohh_win_updates;  /* _ARGO: integer */
    uint64_t transmit_batch_win_updates; /* _ARGO: integer */
    uint64_t transmit_mconn_win_updates; /* _ARGO: integer */
    uint64_t transmit_pause;             /* _ARGO: integer */
    uint64_t transmit_resume;            /* _ARGO: integer */
    uint64_t last_pause_time_s;            /* _ARGO: integer */
    uint64_t last_resume_time_s;           /* _ARGO: integer */
    uint64_t max_pause_time_s;             /* _ARGO: integer */

    uint64_t queue_depth;          /* _ARGO: integer */

    /* Only valid for HTTP connections: This is the number of POST's
     * that have been sent without seeing an acknowledgement */
    uint64_t unacknowledged_count; /* _ARGO: integer */

    /*
     * total number of _us the conn is down _after_ the connected state and the current disconnected state.
     * i.e 1. ignore the time before the first connected state.
     *     2. ignore the time of the current disconnected state if at the current state is so.
     */
    int64_t  previous_cumulative_downtime_s; /* _ARGO: integer */
    /* number of times the connection is disconnected */
    uint64_t num_disconnects;                /* _ARGO: integer */

    /* The followings are calculated only when we display the stats.
     * such as in fohh_connections_describe()
     */
    uint64_t since_last_tx_bytes_us;     /* _ARGO: integer */
    uint64_t since_last_tx_obj_us;       /* _ARGO: integer */
    uint64_t since_last_rx_bytes_us;     /* _ARGO: integer */
    uint64_t since_last_rx_obj_us;       /* _ARGO: integer */

    int64_t fohh_peer_tx;                /* _ARGO: integer */
    int64_t fohh_rx_data;                /* _ARGO: integer */
    int64_t fohh_rx_data_us;             /* _ARGO: integer */
    int64_t fohh_enq_data_us;            /* _ARGO: integer */
    int64_t fohh_enq_bytes;              /* _ARGO: integer */
    int64_t fohh_deq_bytes;              /* _ARGO: integer */
    int64_t fohh_tx_data;                /* _ARGO: integer */
    int64_t fohh_tx_data_drop;          /* _ARGO: integer */
    int64_t fohh_tx_limit;               /* _ARGO: integer */
    int64_t fohh_remote_rx_data;         /* _ARGO: integer */
    int32_t fohh_unblocked_thread_call_count; /* _ARGO: integer */
    int64_t fohh_data_write_blocked;     /* _ARGO: integer */
    int64_t fohh_data_write_fohh_blocked;     /* _ARGO: integer */
    int64_t fohh_data_write_evbuf_blocked;     /* _ARGO: integer */
    int64_t fohh_fc_blocked;             /* _ARGO: integer */
    int64_t fohh_fc_blocked_time;        /* _ARGO: integer */
    int64_t max_fohh_fc_blocked_time;    /* _ARGO: integer */
    int64_t max_fohh_fc_blocked_time_us; /* _ARGO: integer */
    int64_t tot_fohh_fc_blocked_time; /* _ARGO: integer */
    int64_t fohh_win_update_expired;     /* _ARGO: integer */
    int64_t fohh_win_update_low_water_mark; /* _ARGO: integer */
    int64_t fohh_win_update_available_buf_above_threshold; /* _ARGO: integer */
    int64_t num_status_request_sent;     /* _ARGO: integer */
    int64_t num_status_response_received;/* _ARGO: integer */
};

/* Per-fohh-thread stats for errors in fohh connections.
 */
struct fohh_connection_error_stats {                /* _ARGO: object_definition */
    uint64_t fohh_connection_thread_mismatch;       /* _ARGO: integer */
    uint64_t fohh_connection_ctype_deleted;         /* _ARGO: integer */
    uint64_t fohh_connection_incarnation_mismatch;  /* _ARGO: integer */
    uint64_t fohh_sni_not_found;                    /* _ARGO: integer */
    uint64_t fohh_sni_disabled;                     /* _ARGO: integer */
    uint64_t fohh_sni_fetch_failed;                 /* _ARGO: integer */
    uint64_t fohh_sni_fetch_split;                  /* _ARGO: integer */
    uint64_t fohh_sni_access_denied;                /* _ARGO: integer */
};
/* Dump fohh_connection_error_stats */
void fohh_connection_error_stats_dump(void *req_state);
/* Dump fohh connection allocate and free_q stats */
void fohh_thread_alloc_stats_dump(void *req_state);

/* Per-fohh-thread stats for number of fohh connections in a given state.
 * IMPORTANT: must have the same order as enums defined in fohh_connection_state!!
 */
struct fohh_connection_state_stats {            /* _ARGO: object_definition */
    uint64_t fohh_connection_unresolved;         /* _ARGO: integer */
    uint64_t fohh_connection_unreachable;        /* _ARGO: integer */
    uint64_t fohh_connection_backoff;            /* _ARGO: integer */
    uint64_t fohh_connection_proxy_connect;      /* _ARGO: integer */
    uint64_t fohh_connection_proxy_parse;        /* _ARGO: integer */
    uint64_t fohh_connection_proxy_done;         /* _ARGO: integer */
    uint64_t fohh_connection_wait_sni;           /* _ARGO: integer */
    uint64_t fohh_connection_wait_sni_ctx;       /* _ARGO: integer */
    uint64_t fohh_connection_connecting;         /* _ARGO: integer */
    uint64_t fohh_connection_validate;           /* _ARGO: integer */
    uint64_t fohh_connection_complete;           /* _ARGO: integer */
    uint64_t fohh_connection_connected;          /* _ARGO: integer */
    uint64_t fohh_connection_disconnected;       /* _ARGO: integer */
    uint64_t fohh_connection_deleted;            /* _ARGO: integer */
};
/* Dump fohh_state_stats */
void fohh_connection_state_stats_dump(char *out_str, size_t out_str_len);

/* Dump consolidated fohh_state_stats */
void fohh_connection_state_stats_consolidated_dump(char *out_str, size_t out_str_len);
/* Accumulate fohh connection state stats across threads into dest. */
void fohh_connection_state_stats_accumulate(struct fohh_connection_state_stats *dest);

/* Aggregated fohh connection stats */
enum fohh_aggregated_stats_server_instance_type {
    fohh_aggregated_stats_server_instance_type_none = 0,
    fohh_aggregated_stats_server_instance_type_wally = 1,
    fohh_aggregated_stats_server_instance_type_dispatcher = 2,
    fohh_aggregated_stats_server_instance_type_broker = 3,
    fohh_aggregated_stats_server_instance_type_producer = 4,
    fohh_aggregated_stats_server_instance_type_object_store = 5,
    fohh_aggregated_stats_server_instance_type_sme = 6,
    MAX_AGGREGATED_SERVER_TYPES = 7 // must be the last
};

struct fohh_connection_aggregated_stats_sum {   /* _ARGO: object_definition */
    uint32_t accumulation_count;                /* _ARGO: integer */

    uint64_t queue_depth;                       /* _ARGO: integer */
    uint64_t num_disconnects;                   /* _ARGO: integer */

    uint64_t transmit_objects;                  /* _ARGO: integer */
    uint64_t transmit_objects_fail;             /* _ARGO: integer */
    uint64_t transmit_raw_tlv;                  /* _ARGO: integer */
    uint64_t receive_objects;                   /* _ARGO: integer */
    uint64_t receive_raw_tlv;                   /* _ARGO: integer */

    uint64_t transmit_fohh_win_updates;         /* _ARGO: integer */
    uint64_t transmit_batch_win_updates;        /* _ARGO: integer */
    uint64_t transmit_mconn_win_updates;        /* _ARGO: integer */
    uint64_t transmit_pause;                    /* _ARGO: integer */
    uint64_t transmit_resume;                   /* _ARGO: integer */
};

struct fohh_connection_aggregated_hop_latency_stats {  /* _ARGO: object_definition */
    uint64_t hop_lat_hist_10;  /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_20;  /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_30;  /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_50;  /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_75;  /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_100; /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_250; /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_500; /* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_1000;/* _ARGO: integer, nozero */
    uint64_t hop_lat_hist_gt_1000;/* _ARGO: integer, nozero */
};

void fohh_connection_aggregated_stats_dump(void *req_state);

struct fohh_connection_aggregated_pipeline_latency_stats {  /* _ARGO: object_definition */
    uint64_t pipe_lat_hist_50;  /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_100; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_250; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_500; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_1k;  /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_5k;  /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_10k; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_25k; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_50k; /* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_100k;/* _ARGO: integer, nozero */
    uint64_t pipe_lat_hist_gt_100k;/* _ARGO: integer, nozero */
};

struct fohh_private_tcp_info {       /* _ARGO: object_definition */
    uint8_t    tcpi_state;           /* _ARGO: integer */
    uint8_t    tcpi_ca_state;        /* _ARGO: integer */
    uint8_t    tcpi_retransmits;     /* _ARGO: integer */
    uint8_t    tcpi_probes;          /* _ARGO: integer */
    uint8_t    tcpi_backoff;         /* _ARGO: integer */
    uint8_t    tcpi_options;         /* _ARGO: integer */
    uint8_t    tcpi_snd_wscale;      /* _ARGO: integer */
    uint8_t    tcpi_rcv_wscale;      /* _ARGO: integer */

    uint32_t   tcpi_rto;             /* _ARGO: integer */
    uint32_t   tcpi_ato;             /* _ARGO: integer */
    uint32_t   tcpi_snd_mss;         /* _ARGO: integer */
    uint32_t   tcpi_rcv_mss;         /* _ARGO: integer */

    uint32_t   tcpi_unacked;         /* _ARGO: integer */
    uint32_t   tcpi_sacked;          /* _ARGO: integer */
    uint32_t   tcpi_lost;            /* _ARGO: integer */
    uint32_t   tcpi_retrans;         /* _ARGO: integer */
    uint32_t   tcpi_fackets;         /* _ARGO: integer */

    /* Times. */
    uint32_t   tcpi_last_data_sent;  /* _ARGO: integer */
    uint32_t   tcpi_last_ack_sent;   /* _ARGO: integer */
    uint32_t   tcpi_last_data_recv;  /* _ARGO: integer */
    uint32_t   tcpi_last_ack_recv;   /* _ARGO: integer */

    /* Metrics. */
    uint32_t   tcpi_pmtu;            /* _ARGO: integer */
    uint32_t   tcpi_rcv_ssthresh;    /* _ARGO: integer */
    uint32_t   tcpi_rtt;             /* _ARGO: integer */
    uint32_t   tcpi_rttvar;          /* _ARGO: integer */
    uint32_t   tcpi_snd_ssthresh;    /* _ARGO: integer */
    uint32_t   tcpi_snd_cwnd;        /* _ARGO: integer */
    uint32_t   tcpi_advmss;          /* _ARGO: integer */
    uint32_t   tcpi_reordering;      /* _ARGO: integer */

    uint32_t   tcpi_rcv_rtt;         /* _ARGO: integer */
    uint32_t   tcpi_rcv_space;       /* _ARGO: integer */

    uint32_t   tcpi_total_retrans;   /* _ARGO: integer */

    uint64_t   tcpi_pacing_rate;     /* _ARGO: integer */
    uint64_t   tcpi_max_pacing_rate; /* _ARGO: integer */
    uint64_t   tcpi_bytes_acked;     /* _ARGO: integer */
    uint64_t   tcpi_bytes_received;  /* _ARGO: integer */
    uint32_t   tcpi_segs_out;        /* _ARGO: integer */
    uint32_t   tcpi_segs_in;         /* _ARGO: integer */
};


struct fohh_connection_alloc_stats {   /* _ARGO: object_definition */
    int64_t allocations;                    /* _ARGO: integer */
    int64_t in_use;                         /* _ARGO: integer */
    int64_t free_queue_count;               /* _ARGO: integer */
    int64_t free_queue_replenish_count;     /* _ARGO: integer */
};

struct fohh_connection_free_queue {
    pthread_mutex_t lock;
    struct fohh_connection_free_head connections;
    struct fohh_connection_alloc_stats stats;
};

#define FOHH_RESOLVE_MAX_ADDRESSES 10


ZTAILQ_HEAD(fohh_queue_elements_head, fohh_queue_element);
/*
 * This corresponds to each element in the fohh_queue. One queue element corresponds to either an object or tlv_buffer.
 * In object case - only object is valid and there is not data inserted into fohh_queue->raw_tlv_buffer
 * In tlv_buffer case - object is NULL, tlv_size is valid & fohh_queue->raw_tlv_buffer will not have any data
 *      associated with this element.
 */
struct fohh_queue_element {
    ZTAILQ_ENTRY(fohh_queue_element)    list;
    enum fohh_queue_element_type        type;
    struct argo_object*                 object;
    union {
        size_t                          tlv_size;
        int64_t                         epoch_us;
    };
};
/*
 * Queue for objects to be sent via FOHH.  The size of the data on
 * this queue is considered via object size, without encoding
 * considerations. This queue will indicate "blocking" if the queue
 * itself gets full by object count, or if the queue exceeds some data
 * cap. The thread responsible for the connection for this queue will
 * perform any waking required of this queue as needed.
 *
 * We queue both object and sequence(opaque). This allows for sequence
 * complete feedback when such feedback is required.
 *
 * Though the fohh_queue can hold argo_object & tlv_buffer, its all FIFO - the first element that got into the
 * fohh_queue will get out.
 *
 * The stats should never be cleared out when the queue is operational as the runtime depends on the stats.
 *
 * atleast_one_element_type_is_blocked - the Q have more than one type of elements sitting in order. This flag
 * indicates that atleast one of those elements types were blocked. And so, when the Q becomes empty, we do a unblock
 * callback into the application. Note that we unblock the application only when the Q is empty, this means if the
 * application is constantly keeping the Q with control elem type, and the data elem type is earlier blocked, it will
 * be in that state for a while till the Q is really empty.
 *
 * cfg - Today we only have config to limit the **amount** of data of each particular type that can be enqueued.
 *
 * history - If history_current_depth is > 0, then history contains a history
 * of the last N elements (objects only) in the queue. (Held by extra
 * reference count)
 */
struct fohh_queue {
    struct fohh_connection *f_conn;
    struct fohh_queue_elements_head elements;
    struct evbuffer *raw_tlv_buffer;

    struct fohh_queue_elements_head history;
    int history_max_depth;
    int history_current_depth;

    int queue_invalid; /* If non-zero, means don't use the queue. Will
                        * be in this state for short periods as
                        * connections are set up/torn down. Enqueueing
                        * in this state will return would_block. */
    pthread_mutex_t lock;

    int element_type_blocked[fohh_queue_element_type_max_valid + 1];
    struct {
        size_t  max_allowed_bytes_per_elem_type[fohh_queue_element_type_max_valid + 1];
    } cfg;
    struct {
        size_t total_queued_items_per_elem_type[fohh_queue_element_type_max_valid + 1];
        size_t total_queued_bytes_per_elem_type[fohh_queue_element_type_max_valid + 1];
        size_t total_dequeued_items_per_elem_type[fohh_queue_element_type_max_valid + 1];
        size_t total_dequeued_bytes_per_elem_type[fohh_queue_element_type_max_valid + 1];
        size_t current_bytes_per_elem_type[fohh_queue_element_type_max_valid +1];
    } stats;
};


struct fohh_argo_connection_state {
    int binary_encoding_version;
    enum argo_serialize_mode encoding;
    struct argo_state *tx_argo;
    struct argo_state *rx_argo;
};

struct fohh_argo_tlv_connection_state {
    struct evbuffer *tlv_read_buf;
    int32_t tlv[2];
    size_t tlv_header_len;
};

/* Count- The number of outstanding objects we can be sending to a
 * remote HTTP server. */
enum http_parse_state {
    read_response_line = 0,
    read_generic_line,
    read_content_length,
    read_content
};
#define FOHH_HTTP_SEQUENCE_COUNT (64*1024)
#define FOHH_HTTP_SEQUENCE_MASK  (FOHH_HTTP_SEQUENCE_COUNT - 1)
#define FOHH_HTTP_LOG_RECONNECT_INTERVAL_MINIMUM 3600 /* 1 hour minimum logging connection up time to the broker */
#define FOHH_HTTP_LOG_WAIT_FOR_ACKS_INTERVAL 120 /* 2 minute interval, waiting for acks to come back from the log producer*/
typedef void (reconnect_cb_f)(void* c1, void* c2);
struct fohh_http_connection_state {
    /*
     * We totally 'cheat' for watching http sequences.
     *
     * Sequences are expected to start at 0 or 1 and be monotonically
     * increasing. The only time sequences should be skipped is when
     * there is buffer overrun, in which case the "old" sequence data
     * cannot be recovered in any case.
     */
    int64_t current_connection_transmit_count;
    int64_t current_connection_receive_count;
    int64_t max_log_sequence_seen;
    int64_t max_log_sequence_acked;

    /* These are each arrays of FOHH_HTTP_SEQUENCE_COUNT
     * size. i.e. 1MB between them. Thus they are allocated- and worth
     * noting that they are only used for client connections, and only
     * HTTP ones at that. */
    int64_t *tx_us;
    int64_t *tx_id;

    /* For HTTP connections, we keep rx data in case of error, we can
     * dump it out. */
    struct evbuffer *rx_data;
    /* The number of bytes we have parsed of the http response. */
    size_t rx_count;

    enum http_parse_state state;
    int cur_offset; /* On the current line */
    char current_content_length[20];
    int ccl_offset; /* Within content length- offset */
    char current_response_code[20];
    int crc_offset; /* Within the response code- offset */
    char current_content_data[20];
    int ccd_offset; /* Within the content data- offset */
    int64_t content_value;
    int remaining_content_length;
    struct http_log_state {
        int64_t log_conn_reconnect_interval_s;
        int wait_for_acks;
        int reconnect_now;
        struct event *ev_timer;
    } hls;
};


typedef int (fohh_send_raw_internal_fn)(struct fohh_connection *connection,
                                        int64_t connection_incarnation,
                                        struct evbuffer *data,
                                        size_t len,
                                        enum fohh_queue_element_type elem_type,
                                        uint8_t *block_fohh,
                                        uint8_t *block_evbuf);

typedef int (fohh_argo_serialize_internal_fn)(struct fohh_connection *connection,
                        struct argo_structure_description *description,
                        void *data,
                        int64_t sequence,
                        enum fohh_queue_element_type elem_type);


/*
 * Instance state for FOHH.
 *
 * Note: Mutual exclusion is provided by lock, augmented by per-thread
 * locks. Furthermore, connections are reference counted- and marked
 * deleted in order to allow for easy connection closing despite
 * having multiple outstanding handles on a connection.
 *
 * Common elements holding connection references:
 *
 * - Creator (client), and accept callback (server).
 * - DNS resolvers, once each for IPv4 and IPv6
 *
 * It should be noted that connection state is never freed- it is only
 * set aside in a free queue. This is done, with an incarnation, to
 * ensure that late arriving callbacks from slow modules can test
 * their references on callback.
 *
 */
struct fohh_connection {
    LIST_ENTRY(fohh_connection) thread_list;

    TAILQ_ENTRY(fohh_connection) free_list;

    int64_t incarnation;

    int no_hostname;      /* If set, then we don't have a hostname, we
                           * have an IP address. (Only used on client
                           * connections) */

    int rx_data_timeout_s;/* Maximum seconds connection can be idle
                           * before timeout */

    unsigned quiet:1;     /* If quiet, we endeavor to never send
                           * extraneous data in-stream */
    unsigned sticky:1;    /* Whether or not we drop connection if we
                           * lose name resolution. Client only */
    unsigned disabled:1;  /* Whether or not this (Client) connection
                           * is disabled. Prevents connection state
                           * machine from running. */
    unsigned tlv_conn:1;  /* true if TLV connection; false otherwise; don't depend on 'enum fohh_connection_style' */
    unsigned ignore_service_prefix:1;
    unsigned suppress_connection_event_logs:1; /* Normally fohh will log all the connection events, but if a
                                                * component wants to supress the logs, it should set it
                                                */
    unsigned quickack_read_config:1; /* The quickack from tcp manual says the quickack setting for socket is not permanent.
                                        This flag is not permanent, it only enables a
                                        switch to or from quickack mode.  Subsequent operation of
                                        the TCP protocol will once again enter/leave quickack mode
                                        depending on internal protocol processing and factors such
                                        as delayed ack timeouts occurring and data transfer.
                                        We had to enable at read callback to enforce the quickack mode everytime */
    unsigned libevent_low_write_watermark_config:1; /* The low write watermark is set */

    int reference_count;  /* Protected by thread lock */
    int delete_count;     /* Used to slowly delete
                           * connection. Libevent likes to give us
                           * events late. */

    SSL_CTX *ssl_ctx;

    /* used when we switch connections between broker and siteC */
    SSL_CTX *ssl_ctx_pub;
    SSL_CTX *ssl_ctx_pri;

    SSL *ssl;
    struct fohh_ssl_status ssl_status;
    int use_ssl;          /* Control whether or not we even use our
                           * SSL state... */

    int is_customer_selected_cipher;    /* This flag describe if there is a cipher chosen by the customer
                                            We are required to store this per fohh connection as this flag
                                            will be used by the fohh_connection_state_machine to force the
                                            broker level cipher configuration if not set by customer*/

    int current_cipher_index;       /* It will represent the current cipher list in use by ssl ctx */

    int sock;
    int64_t sock_buf;     /* How big the output socket buffer is
                           * currently configured. 0 if it hasn't been
                           * touched yet */

    /*
     * Tuning configuration
     */
    unsigned tune_enabled;
    double tune_buffer_factor;
    double tune_shrink_factor;
    int tune_buffer_size_total;
    int tune_buffer_size_count;
    int tune_buffer_size_max;
    int tune_max_sockbuf;

    /* To see if we need to initialize our callbacks, etc. This is
     * needed because libevent uses a plethora of different callbacks,
     * not any of which is a nice succinct "connected" event
     * callback. Furthermore, we don't necessarily want to initialize
     * right when we create a connection, as that could potentially be
     * a resource drain- we want to wait until SSL is complete to do
     * our initialization. */
    int initialized;

    /* Delay transmit until a version is set... */
    int delay_xmit;

    /* Whether or not to accept binary argo... */
    int accept_binary_argo;

    /* Queue of objects to be transmitted. */
    struct fohh_queue object_queue;

#ifdef ADDRESS_SANITIZER
    char guardbuf_pre[GUARDBUFF_SIZE];                /* TODO: remove once ET-29584 is fixed */
#endif

    /* Per-connection lock */
    pthread_mutex_t lock_outbound;
    pthread_mutex_t lock_inbound;

#ifdef ADDRESS_SANITIZER
    char guardbuf_post[GUARDBUFF_SIZE];               /* TODO: remove once ET-29584 is fixed */
#endif

    /* lock for disconnecting */
    pthread_mutex_t lock_disconnect;

    /* The fohh_thread_id receiving traffic on this fohh */
    int fohh_thread_id;

    int sni_callbacks;

    /* Status of connection with peer- with some history. */
    int64_t next_transmit_index;
    int64_t expected_receive_index;

    /* This is the difference in time from a received status request
     * message compared to local time. If the local time is 'ahead' of
     * remote time, this value will be negative. If the local time is
     * 'behind' remote time, this value will be positive. If this
     * value is 0, it is unknown, or no delta.
     *
     * it is 'remote_us - local_us'
     */
    int64_t timestamp_differential_us;
    int timestamp_differential_valid;

    /* Service port. */
    uint16_t service_port_ne;

    /* Remote/Local address of active connection, in socket and string
     * form */
    char connection_description[FOHH_MAX_NAMELEN*2];
    char remote_address_name[FOHH_MAX_NAMELEN];
    char remote_address_str[INET6_ADDRSTRLEN];
    char peer_common_name[FOHH_MAX_NAMELEN];
    char sni_name[FOHH_MAX_NAMELEN];
    char domain_name[FOHH_MAX_NAMELEN];
    char *sni_suffix;
    char *original_remote_address_suffix;
    char *identity_str;
    int64_t peer_id;
    /* e.q. Applies to "Machine Tunnel" type Peer - Aux ID there can be Group ID of Machine*/
    int64_t peer_aux_id;
    struct sockaddr_storage remote_address;
    struct argo_inet remote_address_inet;
    int have_peer_common_name;
    int remote_address_len;

    char local_address_str[INET6_ADDRSTRLEN];
    struct sockaddr_storage local_address;
    int local_address_len;

    /*
     * How far we're backing off, so far.
     */
    int backoff;
    int backoff_max_s; /* The maximum number of seconds to be backed off */

    /* An indication that this connection should be reaped */
    int reapit;

    /* And indication we should log debugging info... */
    int debug;

    /*
     * Usually, only one of the following two will be
     * non-NULL. listener will have a listener (duh), and
     * clients/servers have io_bufferevent.
     *
     * rx_buffer is a buffer we leave around, allocated, for use on rx
     * callbacks. (Particularly for TLV callbacks)
     */
    struct evconnlistener *listener;
    struct bufferevent *io_bufferevent;
    struct event *sni_event;
    struct event *proxy_connect_event;
    struct event *xmit_wake_event;
    int xmit_wake_fds[2];

    /* Parser state... For proxy parsing only */
    struct http_parser proxy_parser;

    /* Callbacks + cookie */
    fohh_connection_callback_f *callback;
    fohh_connection_unblock_f *unblock_callback;
    fohh_connection_ack_f *ack_callback;
    fohh_connection_sanity_f *sanity_callback;
    fohh_connection_tlv_f *tlv_callback;
    argo_structure_callback_f *argo_callback;
    fohh_ssl_ctx_get_f *ssl_ctx_callback;
    fohh_connection_verify_f *verify_callback;
    fohh_connection_post_verify_f *post_verify_callback;
    fohh_connection_version_done_f *version_callback;
    fohh_connection_info_cb_f *info_callback;
    fohh_connection_verify_info_cb_f *verify_info_callback;

    void *cookie;

    /* Reference that can be used to track owner's state...*/
    void *dynamic_cookie;

    /* Connection state machine state. Client/Server are identical-
     * both send heartbeats to the other every second. Listener is
     * pretty close to "single-state." */
    enum fohh_connection_state state;

    /* Listener, client, or server: */
    enum fohh_connection_type type;

    /* ARGO connection or HTTP connection, really. */
    enum fohh_connection_style style;

    /* The connection state, based on style. */
    struct fohh_argo_connection_state argo;
    struct fohh_http_connection_state http;
    struct fohh_argo_tlv_connection_state tlv;

    /* Timestamp of last connection end, update on backoff too. */
    int64_t last_connection_end_epoch_s;
    /* Timestamp of last connection disconnection, on init it is set to that time. So the time till the first
     * connected is counted as downtime, which really is */
    int64_t last_disconnect_epoch_s;
    /* last time we detected the peer is alive - Connection successfully set up or last data arrived. Both update this
     * timestamp */
    int64_t peer_alive_detected_epoch_s;
    /* Timestamp of last connection complete, in seconds */
    int last_connection_connected_epoch_s;

    /* Timestamp of last tx/rx */
    uint64_t last_tx_bytes_us;
    uint64_t last_tx_obj_us;
    uint64_t last_rx_bytes_us;
    uint64_t last_rx_obj_us;

    // for idle timeout, ignoring RPCs
    uint64_t last_rx_data_bytes_s;
    uint64_t last_tx_data_bytes_s;

    /* Reason the connection was closed. This will be one of the
     * strings in FOHH_CLOSE_REASON_XXX */
    const char *close_reason;

    // contains strerror for the socket, dont need to be freed
    const char *socket_error;

    /* Some stats. */
    struct fohh_connection_stats stats;
    /* The periodic stats loggers for stats... There are two because
     * one is high frequency and one is low frequency. */
    struct argo_log_registered_structure *stats_argo;
    struct argo_log_registered_structure *stats_argo2;

    enum fohh_aggregated_stats_server_instance_type server_instance_type;

    /* TODO: Need remote system health/state */

    fohh_send_raw_internal_fn *send_raw_internal;
    fohh_argo_serialize_internal_fn *argo_serialize_internal;

    /* Performance tuning stuff */
    int delay_optimized;          /* whether we send data directly to eventbuf or put on fohh queue */
    int sock_sndbuf;              /* number of 1k bytes, 0 means default */
    int chunk_size;               /* number of 1k bytes chunk to be sent as a unit for each sub tunnel (mtunnel), 0 means default */
    int allowed_chunk;            /* number of chunks per transmit for each sub tunnel (mtunnel), 0 means default */

    /*
     * The max amount of time the connection can be in not-connected state once it is connected. If zero, then no
     * such check is done. Once this time is exceeded, sanity callback is attempted.
     */
    int64_t max_allowed_downtime_s;

    /*
     * Aggregated by taking max over the past app/tcp rtts within a query time interval.
     * Consumers: connector/pbroker monitor thread, broker auth log, etc. Field will be zeroed out after it is consumed.
     */
    int64_t max_app_rtt_since_last_query;
    int64_t max_tcp_rtt_since_last_query;

    /* Previous app rtt histogram to calculate the delta app RTTs */
    struct fohh_histogram prev_app_rtt_histogram;

    /* App rtt histogram */
    struct fohh_histogram app_rtt_histogram;
    /* TCP congestion win histogram */
    struct fohh_histogram tcp_win_histogram;
    /* TCP rtt histogram */
    struct fohh_histogram tcp_rtt_histogram;

    /* List of important events happened for this object*/
    struct fohh_history*        history;

    /* User connection debug string */
    char user_debug_str[FOHH_USER_DEBUG_STR_LEN];

    /* Redirect related stuff */
    int64_t last_redirect_us;
    char original_remote_address_name[FOHH_MAX_NAMELEN];
    char redirect_remote_address_name[FOHH_MAX_REDIRECT_NAME][FOHH_MAX_NAMELEN];
    int  total_num_redirect_remote_name;
    int  curr_redirect_remote_name_index;

    /*
     * ET-53895 Alt cloud related stuff
     *   Below are new fields for supporting broker redirect from/to alt cloud.
     *   Pain point: we're only provided with alt cloud suffix, but we need to construct a new sni_name string.
     *   e.g. we have f_conn->sni_name = "123.actl.dev.zpath.net"
     *      received redirect RPC with cloud suffix: "zscaler.vip"
     *      need to update f_conn->sni_name to "123.actl.zscaler.vip"
     *      remote_address_name update is easy because the redirect RPC contains remote host FQDN
     *
     * Fields explanation:
     *   original_sni_name: similar to original_remote_address_name, copy of sni when we first connected (rbroker)
     *                      note: this is not guaranteed to be on default cloud, when connector/pse boots up with cached
     *                      config, it can be set to co2br.alt_cloud
     *   redirect_sni_name: sni name of the redirect candidate, constructed by replacing the old sni's suffix with
     *                      redirect_sni_suffixes provided in broker redirect message
     *   redirect_sni_set: indicator - whether the sni suffix field exists in the broker redirect message
     *   redirect_sni_suffixes: cloud suffix for each redirect candidate, read from the broker redirect RPC
     *   default_sni_name: a copy of the sni name on default cloud, this is used when we want to switch to default cloud
     *                     while using a alt cloud cached config
     *
     * Reason of adding above fields:
     *   previously redirect won't have any cloud suffix change, but now we might be switching clouds, so we need to
     *   keep a copy of original sni name whenever we need to move back to default cloud, or construct a new sni name
     *   whenever we need to move to alt cloud.
     *
     * Note:
     *   original_sni_name should always be on the same cloud as original_remote_address_name
     *   for reach redirect candidate, redirect_sni_name should always be on the same cloud as redirect_sni_suffixes
     *   default_sni_name should always be on the default cloud
     */
    char original_sni_name[FOHH_MAX_NAMELEN];
    char redirect_sni_name[FOHH_MAX_REDIRECT_NAME][FOHH_MAX_NAMELEN];
    int redirect_sni_set[FOHH_MAX_REDIRECT_NAME];
    char redirect_sni_suffixes[FOHH_MAX_REDIRECT_NAME][FOHH_MAX_NAMELEN];
    char default_sni_name[FOHH_MAX_NAMELEN];
    char *default_sni_suffix;

    int redirect_retries;         /* Number of times we cannot connect to any redirect candidates */

    char last_connected_peer_name[FOHH_MAX_NAMELEN];
    struct argo_inet last_connected_peer_pub_ip;

    int64_t peer_last_redirect_us;
    char peer_last_connect_peer_name[FOHH_MAX_NAMELEN];

    int redirect_list_sent_to_client; /* If set - redirect list is sent to client to connect to other server. */

    /* The epoch at which this tunnel was created */
    int64_t creation_time_s;

    /* DDIL stuff */
    int site_offline_domain_enabled;
    int ep_always_sitec;
    enum fohh_site_connection_state site_connection_status;
    int switch_allowed;
    enum fohh_site_connection_endpoint_type site_conn_ep_type;
    char site_offline_domain_name[FOHH_MAX_NAMELEN];
    char site_offline_domain_prefix[FOHH_MAX_NAMELEN];
    char default_remote_address_name[FOHH_MAX_NAMELEN];
    uint64_t nr_switch_to_sitec;
    uint64_t nr_switch_to_broker;
    uint64_t best_sitec_time_counter;
    /*
     * Interval in seconds for sending fohh_status_request messages.
     * status_interval points to the address containing the configured status interval on App Connector.
     * NULL is the default value for status_interval. If status_interval is NULL, the default interval is used on connected broker.
     * The status_interval value is sent from App Connector to Broker, only when status_interval is not NULL .
     * peer_status_interval is the status interval used by connected peer (broker) for sending fohh_status_request messages.
     * Default value is 0 for peer_status_interval.
     */
    int64_t* status_interval;
    int64_t peer_status_interval;

    /*
     * All the flags goes here.
     */
    uint32_t                    is_history_enabled:1;

    unsigned customer_preloaded:1; /* Connecting customer preloaded state. 0 - preloaded, 1 - Not loaded*/

    /* at least one fohh_status_request is received*/
    int64_t fohh_last_status_request_us;
    int64_t fohh_last_tickle_us;
    uint32_t fohh_number_of_tickle_errors;

   /* Stores the log collection name received*/
    const char  *log_collection_name;
    const char  *log_type;

    /* track server connection time */
    fohh_tracker_t tracker;

    /*flag to indicate whether if we want rescover meanism when we detect fohh is flow control bad state*/
    int kick_fc_reset_to_recover_fc_bad_state_enable;
    double kick_fc_continuous_threshold_percent;
    int64_t kick_fc_continous_timeout_interval_s;
    int kick_flow_control_reset;

    /*alt_cloud */
    int alt_cloud_aware;
    int redirect_alt_cloud_change;
    int times_contacted_default;

    /* connection type 0 - active, 1 - passive */
    int conn_mode;

    /* does the connection require a burst packet  */
    int burst_flag;
    /* idle channel burst packet counter */
    int burst_packet_counter;

    /* conn describe flag */
    int conn_desc_flag;
    int inst_update_in_logs_not_required;

    /* user name for vdi client type */
    char *user_name;
    /* connection to prefer ipv6, ipv4, or no preference. This can be set to no pref if conn fails multiple times */
    enum fohh_resolve_ip_preference ip_pref;

    /* if 1 and client connection, validate with hostname(sni) against the server cert DNS SAN */
    unsigned validate_server_cert_san:1;

    int fohh_fc_enhacement_config;

    unsigned pipeline_latency_trace_config:1;  // config override

    unsigned batched_window_updates_config:1; // includes only the mconn level
    unsigned syn_status_msg:1; // only for data connections initially
};

#define VALIDATE_SERVER_CERT 1
#define NO_SERVER_CERT_VALIDATION 0

/*
 * Structure used for invoking in-thread-context calls.
 *
 * (no return provided)
 */
struct fohh_thread_call {
    fohh_thread_call_f *call;
    void *cookie;
    int64_t int_cookie;
};

/*
 * State kept per-thread.
 */
struct fohh_thread {
    /* Lock for all data structures "owned" by this thread- but NOT
     * for tx/rx data. (that is handled by bufferevents). This lock is
     * for manipulating thread ownership of resources- maintaining
     * connection queues and the like. */
    pthread_mutex_t thread_lock;

    /* Thread ID, posix-style */
    pthread_t thread;

    /* Index of this thread, within FOHH's thread pool */
    int fohh_thread_id;

    /* thread watchdog for this thread */
    struct zthread_info *zthread;

    /* The set of fohh connections we have */
    struct fohh_connection_head fohh_connections;

    /* The libevent structure managing all our events. */
    struct event_base *ev_base;

    /* zevent to handle thread call */
    struct zevent_base *zevent_base;

    /* The timer event for this thread. */
    struct event *ev_timer;

    /* The call event- used to make a function call in the context of
     * fohh_thread. */
    struct event *ev_call;
    int call_wake_fds[2];
    int init_done;

    /* filled in by fohh thread timer callback */
    struct fohh_connection_state_stats state_stats;
    struct argo_log_registered_structure *state_stats_structure;

    int64_t fohh_server_connection_connected;

    struct fohh_connection_error_stats error_stats;
    struct argo_log_registered_structure *error_stats_structure;

    /* holds the f_conn objects for this thread */
    struct fohh_connection_free_queue free_q;
};

struct fohh_worker_pool {
    char *name;
    int first_thread;
    int last_thread;

    /* use_count is atomically incrememnted and modulo (1+last-first)
     * to assign next use of this pool to a thread */
    int64_t use_count;
};

struct fohh_global_state {
    int thread_count;

    /* Listen_count. If 0, a single thread does all the listening for
     * generic servers. If non-zero, then that many threads (starting
     * at 0) perform reuseaddr/reuseport listening for generic
     * servers. Furthermore, if a worker pool maps exactly to the size
     * of listen count, then connections are processed on the thread
     * on which the listen callback was received, making the kernel's
     * work much easier. */
    int generic_server_listen_count;

    /* TODO: Add system state for this system */
    /* There are two states:
     *
     * 1. The state of this system, as it sees it. (load, etc).
     *
     * 2. The inherited state of this system- That is used to
     *    propagate state dependencies throughout the system. ex: if
     *    mama is down, then config management can become down, etc.
     *
     * For now, we're not doing this- just simple echo-health for the
     * time being...
     */

    /* Lock for access to global state. (thread manipulations and the
     * like) This lock, like all locks should be, is padded to sit on
     * its own cache line. */
    pthread_mutex_t global_lock;

    /* Rate limiting configuration for fohh connections. Note that
     * this is only modified during initialization, and is safe to use
     * at other times. */
    struct ev_token_bucket_cfg *rate_limit;
    struct timeval tick_len;


    /* Array of threads. FOHH threads never die. These are protected
     * by lock. */
    struct fohh_thread threads[FOHH_MAX_THREADS];

    /* For now, connections are simply allocated round-robin. Later
     * there can be "intelligent" balancing of this stuff, but not for
     * now. This is protected by lock. */
    int thread_round_robin;
    int thread_round_robin_accept;

    /* If set, then absolutely fully disable SSL- but do so silently-
     * i.e. services asking for SSL will succeed, just not use SSL */
    int fully_disable_ssl;

    /* Prefix added to any service name FOHH attempts to connect to
     * each. */
    char *service_prefix;

    /* Context used for client connections, and sometimes for server
     * connections */
    SSL_CTX *ssl_ctx;

    /* consolidating fohh connection state stats across threads. */
    struct fohh_connection_state_stats total_state_stats;
    struct argo_log_registered_structure *total_state_stats_structure;

    /* consolidating alloc stats across threads */
    struct fohh_connection_alloc_stats total_alloc_stats;

    int fohh_ciphers_count;
    struct fohh_connection_cipher_stats fohh_cipher_stats[MAX_CIPHER_SUITES];
    struct zpath_mutex lock;

    /* DNS context. DNS resolver is thread safe- can be used from
     * multiple threads. */
    //struct ub_ctx *unbound_resolver;
    struct argo_structure_description *argo_status_request;
    struct argo_structure_description *argo_status_response;
    struct argo_structure_description *fohh_connection_stats_desc;
    struct argo_structure_description *fohh_private_tcp_info_description;
    struct argo_structure_description *fohh_info_description;
    struct argo_structure_description *fohh_connection_state_stats_desc;
    struct argo_structure_description *fohh_connection_error_stats_desc;
    struct argo_structure_description *fohh_connection_alloc_stats_desc;
    struct argo_structure_description *fohh_connection_cipher_stats_desc;

    int fohh_aggregated_stats_count;
    struct fohh_connection_aggregated_stats_sum sum_aggregated_stats[MAX_AGGREGATED_SERVER_TYPES];
    struct argo_structure_description *fohh_connection_aggregated_stats_sum_desc;

    struct fohh_connection_aggregated_hop_latency_stats _fohh_connection_aggregated_hop_latency_stats;
    struct argo_structure_description *fohh_connection_aggregated_hop_latency_stats_desc;

    struct fohh_connection_aggregated_pipeline_latency_stats _fohh_connection_aggregated_pipeline_latency_stats;
    struct argo_structure_description *fohh_connection_aggregated_pipeline_latency_stats_desc;

    struct argo_log_collection *event_log;
    struct argo_log_collection *stats_log;

    fohh_ip_trigger_f *trigger_start;
    fohh_ip_trigger_f *trigger_end;

    struct zhash_table *worker_pools;

    struct zhash_table *filter_history;

    struct fohh_worker_pool *fohh_pools[FOHH_MAX_WORKER_POOLS];
    int fohh_pools_count;

    char **fohh_args;
    int fohh_args_count;
};

/*
 * Information about fohh connection. Usually sent right after connection is up.
 */
struct fohh_info {                       /* _ARGO: object_definition */
    int64_t last_redirect_us;            /* _ARGO: integer */
    char *last_connected_peer_name;      /* _ARGO: string */
    const char **capabilities;           /* _ARGO: string */
    int capabilities_count;              /* _ARGO: quiet, integer, count:capabilities */
    char *current_alt_cloud;             /* _ARGO: string */
};


extern struct fohh_global_state fohh_g;

/* exposed for testing */
int fohh_worker_pool_create(char *name, int first_thread, int last_thread);

/* Fohh connection monitor error stats */
struct fohh_error_debug_stats {
    int64_t  f_conn_is_null_in_mon;
    int64_t  f_conn_is_inval_state_in_mon;
};

#endif /* _FOHH_PRIVATE_H_ */
