add_executable(sarge sarge.c sarge_utils.c check_admin_passwd.c)
target_link_libraries(
    sarge
    PRIVATE zhw zpn_enrollment zvm zpn npwg_lib zpath_app_simple
)
if(UNIX AND NOT APPLE)
    target_link_libraries(sarge PUBLIC crypt)
endif()

find_program(wg wg REQUIRED)
find_program(wg-quick wg-quick REQUIRED)

add_nostrip(TARGET sarge)

strip_and_rename(TARGET sarge NAME zpa-connector OUTPUT_VAR connector)
strip_and_rename(TARGET sarge NAME np-connector OUTPUT_VAR np-connector)
strip_and_rename(TARGET sarge NAME zpa-service-edge OUTPUT_VAR pse)
strip_and_rename(TARGET sarge NAME zpa-pcc OUTPUT_VAR pcc)

add_rpm(
    NAME zpa-connector
    SPEC rpm-zpa-connector/zpa-connector.spec
    MAKEFILE Makefile.rpm.zpa-connector
    FILES ${connector} rpm-zpa-connector/zpa-connector.service rpm-zpa-connector/50-zscaler.preset license.txt
)

add_rpm(
    NAME np-connector
    SPEC rpm-np-connector/np-connector.spec
    MAKEFILE Makefile.rpm.np-connector
    FILES
        ${np-connector}
        rpm-np-connector/np-connector.service
        rpm-np-connector/50-zscaler.preset
        rpm-np-connector/14-zscaler-user
        rpm-np-connector/npwg0.conf
        rpm-np-connector/frr.conf
        ${wg}
        ${wg-quick}
        license.txt
)

add_rpm(
    NAME zpa-service-edge
    SPEC rpm-zpa-service-edge/zpa-service-edge.spec
    MAKEFILE Makefile.rpm.zpa-service-edge
    FILES ${pse} rpm-zpa-service-edge/zpa-service-edge.service rpm-zpa-service-edge/50-zscaler.preset license.txt
)

add_rpm(
    NAME zpa-pcc
    SPEC rpm-zpa-pcc/zpa-pcc.spec
    MAKEFILE Makefile.rpm.zpa-pcc
    FILES ${pcc} go-exe-insight rpm-zpa-pcc/zpa-pcc.service rpm-zpa-pcc/50-zscaler.preset license.txt
)
