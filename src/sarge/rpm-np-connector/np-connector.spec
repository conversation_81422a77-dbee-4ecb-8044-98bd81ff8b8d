Name:           np-connector
Version:        %{_version}
Release:        1%{?dist}
Summary:        Zscaler Virtual Private Network Connector
Group:          Applications/Internet
License:        license.txt
Vendor:         Zscaler
URL:            https://www.zscaler.com
Source:         %{name}-%{version}.tar
Prefix:         /opt/zscaler
Requires(pre):  shadow-utils libcap
%{?systemd_requires}
BuildRequires:  systemd

%global debug_package %{nil}
%global __debug_install_post /bin/true
%global __os_install_post %{nil}

%description
np-connector is the connector for the Zscaler Virtual Private Network

%prep

%setup -q

%build

%pre
getent group zscaler >/dev/null || groupadd -r zscaler
getent passwd zscaler >/dev/null || \
    useradd -r -g zscaler -d /opt/zscaler -s /sbin/nologin \
    -c "Zscaler Service Account" zscaler
exit 0

%define _build_id_links none

%install
rm -rf $RPM_BUILD_ROOT
%make_install
mkdir -p $RPM_BUILD_ROOT%{_unitdir}
mkdir -p $RPM_BUILD_ROOT%{_unitdir}/../system-preset
install -p -D -m 644 50-zscaler.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/
install -p -D -m 644 np-connector.service $RPM_BUILD_ROOT%{_unitdir}/
install -d -m 700 $RPM_BUILD_ROOT%{prefix}/var
install -d -m 700 $RPM_BUILD_ROOT%{prefix}/.config

%clean
[ "$RPM_BUILD_ROOT" != "/" ] && rm -rf $RPM_BUILD_ROOT

%files
%defattr(-,zscaler,zscaler)
%{prefix}/bin/*
%{prefix}/share/*
%{_unitdir}/*
%{_unitdir}/../system-preset/*
%attr(700, zscaler, zscaler) %{prefix}/var
%attr(700, zscaler, zscaler) %{prefix}/.config
%attr(644, root, root) %{_unitdir}/../system-preset/50-zscaler.preset
%attr(644, root, root) %{_unitdir}/np-connector.service
%attr(700, zscaler, zscaler) %{prefix}/etc/wireguard
%attr(700, zscaler, zscaler) %{prefix}/etc/frr
%attr(440, root, root) /etc/sudoers.d/14-zscaler-user

%doc

%post
%systemd_post np-connector.service
systemctl start np-connector.service >/dev/null || exit 0

%preun
if [ "$1" -eq 0 ]; then
    # Only remove the file on full uninstall, not during upgrade
    rm -f /etc/sudoers.d/14-zscaler-user
fi
%systemd_preun np-connector.service

%postun
%systemd_postun np-connector.service

%changelog
