/*
 * sarge->c. Copyright (C) 2015 Zscaler Inc. All Rights Reserved.
 *
 * Utility for run control and software configuration management.
 */

#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <unistd.h>
#include <dirent.h>

#include <sys/stat.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <signal.h>
#include <sys/param.h>
#include <pwd.h>

#include <openssl/x509v3.h>

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_debug.h"
#include "zvm/zvm.h"
#include "zhw/zhw_id.h"
#include "zhw/zhw_os.h"
#include "zcrypt/zcrypt.h"
#include "base64/base64.h"

#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "zcdns/zcdns_libevent.h"

#include "zcrypt/zcrypt_meta.h"

#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_lib.h"
#include "zvm/zvm.h"
#include "zpath_misc/zpath_version.h"
#include "argo/argo.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpa_cloud_config.h"

#include "npwg_lib/npwg_provider.h"
#include "sarge_utils.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "zpath_lib/zpath_capability_util.h"
#include "zpath_lib/zpath_oauth_utils.h"

#ifdef __linux__
#include <sys/capability.h>
#include <sys/prctl.h>
#include <linux/securebits.h>
#endif

/* How long to sleep (in seconds) between software attempted fetches. This is a
 * delay of FETCH_BACKOFF * MAX_FAILS_FETCH */
#define DEFAULT_FETCH_BACKOFF 300
#define TEST_FETCH_BACKOFF    1

/* If the software doesn't run at least this many seconds, call it a
 * failure. */
#define DEFAULT_MIN_RUN_TIME 60
#define TEST_MIN_RUN_TIME    60

/* The maximum failures in a row before we will revert to default
 * software */
#define DEFAULT_MAX_FAILS_RUNNING 3
#define TEST_MAX_FAILS_RUNNING    2

/* The maximum failures to fetch some version of software in a row
 * before we will fetch default software. */
#define DEFAULT_MAX_FAILS_FETCH 5
#define TEST_MAX_FAILS_FETCH    2

/* The length of time, in seconds, that will will backoff if our
 * default software fails. (This is to prevent us from fetching
 * software over and over and over, etc, when an assistant is
 * configured weirdly. This is also the timeout that affects how often
 * an assistant that has a bad provisioning key will fetch software. */
#define DEFAULT_DEF_BACKOFF_SLEEP (60*60*12)
#define TEST_DEF_BACKOFF_SLEEP    30

/* Amount of time in seconds after starting application after which it
 * must have written or re-written it's software version or we will
 * assume that the software has broken and kill it ourself. */
#define DEFAULT_MAX_WAIT_CONFIG (60*60*24)
#define TEST_MAX_WAIT_CONFIG (60)

/* Number of times after which we can't get our configured version
 * that we will revert to default software. It is this count *
 * MAX_WAIT_CONFIG after which we will revert to default software. */
#define DEFAULT_MAX_FAILS_CONNECT 2
#define TEST_MAX_FAILS_CONNECT 2

#define SARGE_NAME "zscaler-update"
#define YUM "yum"
#define MICRODNF "microdnf"

#define DEV_PATH "/dev"
#define ISSUE_PATH "/etc/issue"

/* Default maximum logging size for sarge is 8 MB */
#define DEFAULT_MAX_LOGGING_MB_SARGE        8


#define FILENAME_ID                      "instance_id.crypt"
#define FILENAME_BIN                     "image.bin"
#define FILENAME_META                    "metadata"
#define FILENAME_BIN_NEW                 "image.bin.new"
#define FILENAME_META_NEW                "metadata.new"
#define FILENAME_VERSION                 "version"
#define FILENAME_RUNNING_VERSION         "running_version"
#define SARGE_STACK_FILE_NAME            "zpa-sarge.stack"

#define PASS_ENV               "PROVISION_KEY"

#define SARGE_V2                         "zscaler-sarge-v2"
#define OAUTH_SERVER                     "OAUTH_SERVER"
#define OAUTH_SERVER_URL                 "zpa-oauth2.dev.zpath.net"
#define OAUTH_DEVICEAUTH_API             "/zpn/api/v1/oauth2/devicecode"
#define OAUTH_DEVICETOKEN_API            "/zpn/api/v1/oauth2/token"
#define OAUTH_DEVICECODE_GRANT           "urn:ietf:params:oauth:grant-type:device_code"

#define CAPABIITY_MODE                   "ZPA_CAPABILITY_MODE"
#define CUSTOM_CAPABILITIES              "ZPA_CUSTOM_CAPABILITIES"

/* MAX_ENV_VARS: Maximum number of environment variables passed to the child process.
 * Current expected entries (up to 6), leaving extra room for future use:
 * 1. PASS_ENV
 * 2. ZCRYPT_ZPA_DEVELOP_ENV
 * 3. ZCRYPT_ZPA_DISABLE_GEOIP
 * 4. ENV_ZPA_OAUTH_ENROLLMENT
 * 5. CAPABIITY_MODE (optional)
 * 6. CUSTOM_CAPABILITIES (optional)
 * 7 onwards (reserved or conditional future additions) */
#define MAX_ENV_VARS 16

/* Set on ZscalerOS (branch connector image) */
#define ZSCALER_OS_ENV         "LINUX_COMPAT_MODE"
/* Write sarge pid for rc.d service script to stop the process, only used in branch connector Zscaler OS */
#define SARGE_PID_FILE         "zpa-connector.pid"

#define INSTANCE_ID_BYTES ZHW_ID_BYTES

#define SARGE_EXIT_CODE_SETCAP_FAILED   2

/* Timeout in seconds for FOHH HTTP Client request */
#define FOHH_HTTP_CLIENT_TIMEOUT 720

/* Maximum timeout in milliseconds for sarge and os upgrades*/
#define MAX_OS_UPGRADE_TIMEOUT_SEC (15 * 60) /*15 mins*/
#define MAX_SARGE_UPGRADE_TIMEOUT_MSEC  MINUTE_TO_MSEC(2) /*2 min*/

#define REBOOT_MAX_FAILS 3

#define INSTANCE_NAME_MAX_LEN 256

char *config_role;
char config_fetch_hostname[MAX_DIST_HOSTNAME_LEN];
char config_fetch_ca_file[MAX_CA_FILE_LEN];
char config_fetch_proxyname[MAX_DIST_HOSTNAME_LEN];
char config_cloud_name[MAX_CLOUD_NAME_LEN];
char config_fetch_path[MAX_DIST_FETCH_PATH_LEN];
char config_fetch_path_develop[MAX_DIST_FETCH_PATH_DEVELOP];
static int require_fips = 0;

/* If the software doesn't run at least this many seconds, call it a
 * failure. */
int config_fetch_backoff = DEFAULT_FETCH_BACKOFF;
int config_min_run_time = DEFAULT_MIN_RUN_TIME;
int config_max_fails_running = DEFAULT_MAX_FAILS_RUNNING;
int config_max_fails_fetch = DEFAULT_MAX_FAILS_FETCH;
int config_def_backoff_sleep = DEFAULT_DEF_BACKOFF_SLEEP;
int config_max_wait_config = DEFAULT_MAX_WAIT_CONFIG;
int config_max_fails_connect = DEFAULT_MAX_FAILS_CONNECT;
int config_test = 0;
int config_develop = 0;
int disable_geoip = 0;
int dropdb = 0;
int enable_oauth_enrollment = 0;

int config_override_no_run = 0; /* only download, do not run, for testing only. */
char *config_override_plat = NULL;
char *config_override_arch = NULL;

int is_zscaler_os = 0; /* branch connector - FreeBSD system called 'ZscalerOS' */

int sarge_debug = 0;
int run_child_as_current_user = 0;
int is_fips_mode = 1;
int wrote_last_working_sarge_version = 0;

/* Next-Gen Sarge */
int sarge_v2 = 0;

char device_token[128];
char user_token[128];
char jwt_token[1024];

/*
 * Special case in container:
 * 1. Capabilities will be set manually when container is created. Usually cap_sys_boot is not
 *    set by the customer (see ET-32579 for details). So we need to skip setting & checking the
 *    cap_sys_boot capability.
 */
int is_container_env = 0;
int is_openshift_env = 0;
int is_kubernetes_env = 0;

#define ARG_CHILD_EVENT_LOG_LVL_CONSOLE "-child-log-lvl-console"
#define ARG_CHILD_EVENT_LOG_LVL_SYSLOG "-child-log-lvl-syslog"
static int child_event_log_lvl_console = -1;    /* -1 means command line arg was not passed */
static int child_event_log_lvl_syslog = -1;     /* -1 means command line arg was not passed */
static char child_event_log_lvl_console_str[2] = {'\0'};
static char child_event_log_lvl_syslog_str[2] = {'\0'};

static struct zpath_cloud_config * sarge_get_zpa_cloud_config(int argc, char *argv[],const char *api_name, const char *cloud_name);
int get_enrollment_type_by_config_role();

char *max_logging_mb_child = NULL;

char *memory_arena_count = NULL;

char proxy_file_data[1000];

/***************************************************************/
/* Moved the certs and cert counts from sarge to zcrypt_meta.c */
/***************************************************************/


typedef struct {
    char version_str[256];     // Buffer for storing the version string
    char *version;             // Pointer to the version string
    int64_t last_change;       // Last modification timestamp
} version_info;

version_info config_version_context = { .version = NULL, .last_change = 0 };
version_info config_frr_version_context = { .version = NULL, .last_change = 0 };

#define FRR_DEFAULT_GOLDEN_VERSIION "0.0"
#define FRR_INSTALL                 "install"
#define FRR_REINSTALL               "reinstall"
#define FRR_PACKAGE_NAME            "frr"
#define FAILS_SWITCH_TO_REINSTALL   1
int frr_install_require;
void check_frr_version();
void check_frr_installation();

static struct zcdns *sarge_zcdns;

struct cert_info {
    unsigned char *data;
    size_t data_len;
};

struct oauth_user_code_status {
    char user_code[20];
    char message[100];
    int is_code_consumed;
    pthread_mutex_t user_code_lock;
} user_code_status;

void check_admin_passwd();

/* Convert seconds to a string of hours minutes seconds. The buffer
 * passed in has to be big enough. */
static char *seconds_to_dhms(char *buf, size_t buf_len, int seconds)
{
    int days = seconds / 86400;
    int hours = (seconds % 86400) / 3600;
    int minutes = (seconds % 3600) / 60;
    int sec = seconds % 60;
    char *s = buf;
    char *start = buf;
    char *e = s + buf_len;

    if (!seconds) {
        snprintf(buf, buf_len, "0 seconds");
    } else {
        if (days) {
            s += sxprintf(s, e, "%d days ", days);
        }
        if (hours) {
            s += sxprintf(s, e, "%d hours ", hours);
        }
        if (minutes) {
            s += sxprintf(s, e, "%d minutes ", minutes);
        }
        if (sec) {
            s += sxprintf(s, e, "%d seconds ", sec);
        }
        /* Take off trailing space */
        s--;
        if ((s >= start) && (s < e)) {
            *s = 0;
        }
    }
    return buf;
}


/* Bunch of stuff to dump on failure. */
static void dump_on_failure(void)
{
    struct sockaddr_storage sa[10];
    struct zinterfaces interfaces[MAX_INTERFACES];
    struct zroute route;
    socklen_t sl[10];
    int count = 0;
    int i;
    char str[100];

    zcdns_get_resolvers(sarge_zcdns, sa, sl, &count);
    if (count) {
        if (count > 1) {
            for (i = 0; i < count; i++) {
                zcdns_sockaddr_storage_to_str(&(sa[i]), str, sizeof(str));
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Resolver %d = %s", i + 1, str);
            }
        } else {
            zcdns_sockaddr_storage_to_str(&(sa[0]), str, sizeof(str));
            ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Resolver = %s", str);
        }
    }
    count = 0;
    if (get_interfaces(interfaces, &count) == ZSYSINFO_SUCCESS) {
        for (i = 0; i < count; i++) {
            zcdns_sockaddr_storage_to_str(&(interfaces[i].addr), str, sizeof(str));
            ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Interface %d: %s: %s", i + 1, interfaces[i].name, str);
        }
    }

    if (get_default_route(&route) == ZSYSINFO_SUCCESS) {
        zcdns_sockaddr_storage_to_str(&(route.gw), str, sizeof(str));
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Default Route Interface %s: %s", route.intf_name, str);
    } else {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Default Route Unknown");
    }
}

static void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
static void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s:\n", argv0);
    fprintf(stdout,
            "Automatic run-control and update for Zscaler software.\n"
            "\n"
            "  -dir PATH : optional, defaults to current working directory. This is where\n"
            "              the Zscaler software will run and where its configuration will\n"
            "              be stored.\n"
            "  -role ROLE : optional, defaults zpa_connector.\n"
            "  -develop : optional, allows software installs with developmental images\n"
            "  -gov : optional, forces software to run in FIPS/gov mode\n"
            "  -debug : optional, shows more debugging\n"
            "  -container : optional, set current environment to container\n"
            "  -repo NAME : optional, name of the host providing software images\n"
            "               defaults as per cloud_config\n"
            "  -repo_ca_file PATH : optional, signing CA certicate file (pem) for\n"
            "                       software repository TLS verification\n"
            "  -fproxy NAME : optional, System through which to forward proxy traffic\n"
            "                 to the repository.\n"
            "  -fproxy_port PORT : optional (defaults 80), port through which to forward\n"
            "                      proxy traffic to the repository\n"
            "  -proxy NAME : optional, System through which to proxy traffic to the\n"
            "                repository. Defaults as per cloud_config-> Do not\n"
            "                confuse with forward proxy configuration. This setting is\n"
            "                really a SNI proxy\n"
            "  -noproxy : optional. Do not fetch software through an intermediate SNI\n"
            "                       proxy.\n"
            "  -test_plat : optional. Simulate downloading for a different platform\n"
            "                       This will only download, but not run, the binary.\n"
            "  -test_arch : optional. Simulate downloading for a different architecture\n"
            "                       This will only download, but not run, the binary.\n"
            "  -test : optional. Use test timeouts for quicker testing. Test mode\n"
            "          purposely kills child and hangs after six hours\n"
            " -run-child-as-current-user: optional. By default the zpa-connector-child\n"
            "          process is run as 'zscaler' username, which is the only\n"
            "          thing recommended for customers.\n"
            " -autoproxy The proxy name is read by the provision key\n"
            " -maxlogmb         : optional, specify maximum MB to use for sarge argo logging.\n"
            " -maxlogmb_child   : optional, specify maximum MB to use for connector-child argo logging.\n"
            " -memory_arena_count COUNT: optional. Passed to zpa-connector-child\n"
            " -disable_geoip    : Disable geoip file downloads\n"
            " -oauth_enrollment : Enable OAuth based provisioning\n"
            " -strict_platform_check: optional. Force validation of platform with installed rpm. \n"
            " " ARG_CHILD_EVENT_LOG_LVL_CONSOLE "  [0-8]: zpa-connector-child console logging verbosity\n"
            " " ARG_CHILD_EVENT_LOG_LVL_SYSLOG "  [0-8]: zpa-connector-child syslog verbosity\n"
            );
    zpath_app_logging_usage_print();

    fprintf(stdout,
            "\n"
            "Default/Test timeouts:\n"
            "%d/%d: seconds\n"
            "   Fetch Backoff. How long in seconds we will sleep between attempted\n"
            "   software fetches. Each backoff adds this much time to the sleep.\n"
            "%d/%d: seconds\n"
            "   Minimum Run Time. If the software doesn't run at least this many seconds\n"
            "   it is considered a failure.\n"
            "%d/%d: count\n"
            "   Maximum Running Failures. The maximum number of software failures in a\n"
            "   row before we will revert to default software.\n"
            "%d/%d: count\n"
            "   Maximum Fetch Failures. The maximum number of failures we will accept\n"
            "   attempting to fetch some version of software before we will try to fetch\n"
            "   the default software again.\n"
            "%d/%d: seconds\n"
            "   Default Software Failure Backoff. The amount of time we will sleep if the\n"
            "   default software is failing before fetching the default software and\n"
            "   trying again.\n"
            "%d/%d: seconds\n"
            "   Maximum time to wait for verification. The maximum amount of time we will\n"
            "   let the software run without seeing it successfully verify what software\n"
            "   version it is running. This is the equivalent of 'maximum time to wait for\n"
            "   the software to connect to the cloud'. This is a reasonably large amount of\n"
            "   time because there are significante periods of time when networks can be\n"
            "   down. Software is restarted if this is exceeded\n"
            "%d/%d: count\n"
            "   Maximum number of times we will allow the software to fail version\n"
            "   verification before reverting to default software.\n"
            "\n"
            "The software downloaded must have a provisioning key. This\n"
            "provisioning key will be stored encrypted on disk once\n"
            "known. The provisioning key can come from the following\n"
            "sources, in the following order:\n"
            "\n"
            "  1. File: provision_key\n"
            "     Note: File will be removed once it has been read and encrypted.\n"
            "  2. Environment Variable: 'PROVISION_KEY'\n"
            "  3. VmWare custom configuration\n"
            "  4. Amazon AWS custom configuration\n"
            "  5. Microsoft Azure custom configuration\n"
            "\n",
            DEFAULT_FETCH_BACKOFF,
            TEST_FETCH_BACKOFF,
            DEFAULT_MIN_RUN_TIME,
            TEST_MIN_RUN_TIME,
            DEFAULT_MAX_FAILS_RUNNING,
            TEST_MAX_FAILS_RUNNING,
            DEFAULT_MAX_FAILS_FETCH,
            TEST_MAX_FAILS_FETCH,
            DEFAULT_DEF_BACKOFF_SLEEP,
            TEST_DEF_BACKOFF_SLEEP,
            DEFAULT_MAX_WAIT_CONFIG,
            TEST_MAX_WAIT_CONFIG,
            DEFAULT_MAX_FAILS_CONNECT,
            TEST_MAX_FAILS_CONNECT
            );
    exit(1);
}

#ifdef __linux__
#include <sys/prctl.h>
static void set_process_name(char* str) {
    if (prctl(PR_SET_NAME, str, NULL, NULL, NULL)) {
        ZPN_LOG(AL_ERROR, "Failed to set child process name: %s", strerror(errno));
    } else {
        ZPN_LOG(AL_INFO, "Successfully set process name to: %s", str);
    }
}
#else
static void set_process_name(char* str) {
    return;
}
#endif

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    ZPATH_LOG(priority, SARGE_NAME ": %s", dump);
}

/* Verify version logger */
static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "%s: %s", SARGE_NAME, log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

static int upgrade_system_os(char *sudo_path, char *install_manager_path, int full_os_upgrade_enabled, uint64_t *os_upgrade_timeout) {

    int res = ZPATH_RESULT_NO_ERROR;

    if (install_manager_path == NULL || install_manager_path[0] == '\0') {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Install manager path is empty");
        return ZPATH_RESULT_ERR;
    }

    if (is_container_env) {
        res = run_os_upgrade(install_manager_path, os_upgrade_timeout, MAX_OS_UPGRADE_TIMEOUT_SEC, "update", "-y", NULL);
    } else if (full_os_upgrade_enabled) {
        res = run_os_upgrade(sudo_path, os_upgrade_timeout, MAX_OS_UPGRADE_TIMEOUT_SEC, install_manager_path, "update", "-y", NULL);
    } else {
        res = run_os_upgrade(sudo_path, os_upgrade_timeout, MAX_OS_UPGRADE_TIMEOUT_SEC, install_manager_path, "update", "--security", "-y", NULL);
    }

    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": OS Upgrade failed");
    } else {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": OS Upgrade success");
    }
    return res;

}

static int reboot_required() {

    char needs_restarting_path[PATH_MAX];
    char out_buf[10000];
    size_t len = sizeof(out_buf);
    int reboot_required = 0;
    int res = ZVM_RESULT_NO_ERROR;

    if (is_container_env) {
        reboot_required = 1;
        return reboot_required;
    }

    find_executable_in_path("needs-restarting", needs_restarting_path);

    if (strlen(needs_restarting_path) > 0) {
        ZPATH_LOG(AL_NOTICE, "needs-restarting found at: %s", needs_restarting_path);
    } else {
        ZPATH_LOG(AL_ERROR, "needs-restarting not found");
        return reboot_required;
    }

    res = zvm_run_cmd(out_buf, &len, needs_restarting_path, 0, "-r", NULL);
    /* Needs-restarting -r returns 1 if reboot is needed*/
    if (res == ZVM_RESULT_UNLCEAN_EXIT) {
        ZPATH_LOG(AL_INFO, " Reboot required");
        reboot_required = 1;
    } else {
        ZPATH_LOG(AL_INFO, " Reboot not required");
    }

    return reboot_required;
}

static int reboot_system(char *sudo_path) {

    char out_buf[10000];
    size_t len = sizeof(out_buf);
    int res;

    ZPATH_LOG(AL_INFO, " Getting ready to reboot system");
    sleep(5);

    if (is_container_env) {
        exit(0);
    } else {
        res = zvm_run_cmd(out_buf, &len, sudo_path, 0, "reboot", NULL);
    }

    if (res) {
        ZPATH_LOG(AL_ERROR, " Reboot failed");
    }
    return res;
}

static int fetch_and_install_sarge(char *expected_sarge_version,
                                   char* install_manager_path,
                                   char *sudo_path, int is_upgrade) {

    char out_buf[10000];
    char package[2000];
    size_t len;
    char sarge_role[256];
    int res = ZPATH_RESULT_NO_ERROR;
    const char *install_type;

    len = sizeof(out_buf);

    if (expected_sarge_version == NULL || expected_sarge_version[0] == '\0') {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Expected sarge version is empty");
        return ZPATH_RESULT_ERR;
    }

    //Get sarge role from the copy of the config_role}
    snprintf(sarge_role, sizeof(sarge_role), "%s", config_role);
    char *suffix = strstr(sarge_role, "-child");
    if (suffix != NULL) {
        *suffix = '\0';
    }

    if (is_upgrade) {
        install_type = "upgrade";
    } else {
        install_type = "downgrade";
    }

    snprintf(package, sizeof(package), "%s-%s-1.%s.%s", sarge_role, expected_sarge_version, zhw_platform(), zhw_platform_arch());
    ZPATH_LOG(AL_INFO, SARGE_NAME ": Attempting to install %s\n", package);

    if (is_container_env) {
        res = zvm_run_cmd(out_buf, &len, install_manager_path, MAX_SARGE_UPGRADE_TIMEOUT_MSEC, "install", "-y", package, NULL);
    } else {
        res = zvm_run_cmd(out_buf, &len, sudo_path, MAX_SARGE_UPGRADE_TIMEOUT_MSEC,  install_manager_path, install_type, "-y", package, NULL);
    }

    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Installing rpm %s failed - %s", package, out_buf);
        res = ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_INFO, SARGE_NAME ": Installing rpm %s successful - %s", package, out_buf);
    }
    return res;
}

static int is_serial_device(const char *device_name) {
    return (strstr(device_name, "ttyS") != NULL || strstr(device_name, "hvc") != NULL || strncmp(device_name, "console", 7) == 0);
}

static void print_to_serial_devices_and_issue(const char *token) {
    int issue_fd = open(ISSUE_PATH, O_WRONLY | O_CREAT | O_TRUNC, 0644);

    // Continue with writing the code to tty regardless of /etc/issue display.
    if (issue_fd >= 0) {
        ssize_t bytes_written = write(issue_fd, token, strlen(token));
        if (bytes_written >= 0) {
            // token written
        }
        close(issue_fd);
    }

    DIR *dir = opendir(DEV_PATH);
    if (!dir) {
        return;
    }

    struct dirent *entry;
    while ((entry = readdir(dir)) != NULL) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }
        if (!is_serial_device(entry->d_name)) {
            continue;
        }

        char device_path[512];
        snprintf(device_path, sizeof(device_path), "%s/%s", DEV_PATH, entry->d_name);
        int fd = open(device_path, O_WRONLY | O_NOCTTY);
        if (fd < 0) {
            continue;
        }
        // Continue printing on the next device even if writing to current one fails.
        ssize_t bytes_written = write(fd, token, strlen(token));
        if (bytes_written >= 0) {
            // token written
        }
        close(fd);
    }

    closedir(dir);
}

/*
 * Extract the required fields from device auth response from OAuth Server
 *
 * Response format:
 * "user_code": "FJAUK-PGY7S",
 * "device_code": "<UUID4 string randomly generated>",
 * "expires_at": "1742966841", // epoch in seconds
 * "interval": "5", // seconds
 * "message": "To sign in, use a web browser to open the admin UI and enter the code FJAU-PGYY2 to authenticate."
 */
static int parse_oauth_device_auth_response(char *response, int64_t* code_expiry, int64_t* retry_interval)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char console_message[200] = {0};

    if (!response) {
        return ZPATH_RESULT_ERR;
    }

    JSON_Value *root_value = json_parse_string(response);

    if (root_value == NULL) {
        ZPATH_LOG(AL_NOTICE, "[OAuth] No/Invalid response from server device auth call");
        res = ZPATH_RESULT_BAD_DATA;
        goto cleanup;
    }

    JSON_Object *root_object = json_value_get_object(root_value);
    if (root_object == NULL) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Invalid json structure received for device auth response");
        res = ZPATH_RESULT_BAD_DATA;
        goto cleanup;
    }

    pthread_mutex_lock(&user_code_status.user_code_lock);

    const char* user_code  = json_object_get_string(root_object, "user_code");
    if (!user_code) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid user_code field in the device auth response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        pthread_mutex_unlock(&user_code_status.user_code_lock);
        goto cleanup;
    }

    snprintf(user_code_status.user_code, sizeof(user_code_status.user_code), "%s", user_code);

    const char* message  = json_object_get_string(root_object, "message");
    if (!message) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid message field in the device auth response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        pthread_mutex_unlock(&user_code_status.user_code_lock);
        goto cleanup;
    }

    snprintf(user_code_status.message, sizeof(user_code_status.message), "%s", message);

    snprintf(console_message, sizeof(console_message), "\n\nOAuth Token: %s\n%s\n\n", user_code_status.user_code, user_code_status.message);

    pthread_mutex_unlock(&user_code_status.user_code_lock);

    const char* device_code  = json_object_get_string(root_object, "device_code");
    if (!device_code) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid device_code field in the device auth response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    int64_t expires_at  = (int64_t)json_object_get_number(root_object, "expires_at");
    if (!expires_at) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid field expires_at in the device auth response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    int64_t interval  = (int64_t)json_object_get_number(root_object, "interval");
    if (!interval) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid field interval in the device auth response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    print_to_serial_devices_and_issue(console_message);

    ZPATH_LOG(AL_DEBUG, "User Code [%s] Device Code [%s] Expires [%"PRId64"] Interval [%"PRId64"] Message [%s]",
                         user_code, device_code, expires_at, interval, message);

    snprintf(user_token, sizeof(user_token), "%s", user_code);
    snprintf(device_token, sizeof(device_token), "%s", device_code);

    if (code_expiry != NULL) {
        *code_expiry = expires_at;
    }

    if (retry_interval != NULL) {
        *retry_interval = interval;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);

    return res;
}

int change_ownership(const char *file_name) {
    int res = ZPATH_RESULT_ERR;
    struct passwd passwd_entry = {};
    struct passwd *passwd_entry_ptr = NULL;
    char buf[1024] = {0};

    res = getpwnam_r("zscaler", &passwd_entry, buf, sizeof(buf), &passwd_entry_ptr);
    if (res != 0) {
        fprintf(stdout, "ERROR: getpwnam failed for zscaler user: %s \n", strerror(errno));
    } else {
        if (passwd_entry_ptr) {
            int fd = open(file_name, O_RDONLY);
            if (fd == -1) {
                fprintf(stdout, "NOTICE: No %s or couldn't open it: %s\n", file_name, strerror(errno));
            } else {
                if (fchown(fd, passwd_entry.pw_uid, passwd_entry.pw_gid) == -1) {
                    fprintf(stdout, "ERROR: Found %s but couldn't change ownership: %s \n", file_name, strerror(errno));
                } else {
                    res = ZPATH_RESULT_NO_ERROR;
                }
                close(fd);
            }
        } else {
            fprintf(stdout, "ERROR: zscaler user not found: %s \n", strerror(errno));
        }
    }
    return res;
}

static int parse_oauth_device_access_response(char *response)
{
    char sarge_key[256] = {0};
    FILE *fp = NULL;
    int res = ZPATH_RESULT_NO_ERROR;

    if (!response) {
        return ZPATH_RESULT_ERR;
    }


    JSON_Value *root_value = json_parse_string(response);

    if (root_value == NULL) {
        ZPATH_LOG(AL_NOTICE, "Could not parse Oauth Device Access response");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    JSON_Object *root_object = json_value_get_object(root_value);
    if (root_object == NULL) {
        ZPATH_LOG(AL_ERROR, "Failed to parse OAuth Device Access response");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    /*
     * token_type - Bearer
     * tenant_id - Tenant GID (Tenant GID is part of path for Enrollment V4 APIs)
     * component_group_gid - Assistant/PCC/PSE group GID
     * enrolment_server - Enrollment server
     * cloud_name - This is used by API for API-UI communication. We don't use it.
     * config_cloud - Actual cloud name is stored in the config_cloud field.
     * access_token - JWT Token for authorizing enrollment API calls in child process.
     */
    const char* token_type  = json_object_get_string(root_object, "token_type");

    int64_t tenant_id  = (int64_t)json_object_get_number(root_object, "tenant_id");
    if (!tenant_id) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid tenant_id field in the device provisioning response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    int64_t component_group_id  = (int64_t)json_object_get_number(root_object, "component_group_id");

    const char* enrolment_server  = json_object_get_string(root_object, "enrolment_server");
    if (!enrolment_server) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid enrolment_server field in the device provisioning response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    /* Sarge does not care for this field. Do a NULL check, log it and fall thru */
    const char* cloud_name  = json_object_get_string(root_object, "cloud_name");
    if (!cloud_name) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid cloud_name field in the device provisioning response from OAuth Server");
    }

    const char* config_cloud  = json_object_get_string(root_object, "config_cloud");
    if (!config_cloud) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid config_cloud field in the device provisioning response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    const char* access_token  = json_object_get_string(root_object, "access_token");
    if (!access_token) {
        ZPATH_LOG(AL_ERROR, "Missing or invalid access_token field in the device provisioning response from OAuth Server");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    ZPATH_LOG(AL_DEBUG, "Token Type [%s] Tenant ID [%"PRId64"] CGID [%"PRId64"] "
                         "Enrolment Server [%s] Cloud Name [%s] Cloud Config [%s] "
                         "Token [%s]", token_type, tenant_id, component_group_id,
                         enrolment_server, cloud_name, config_cloud, access_token);

    snprintf(sarge_key, sizeof(sarge_key), "%s|%s|%"PRId64"", enrolment_server, config_cloud, tenant_id);
    snprintf(jwt_token, sizeof(jwt_token), "%s", access_token);

    fp = fopen(FILENAME_OAUTH_DETAILS, "w");
    if(!fp) {
        ZPATH_LOG(AL_NOTICE, "Could not parse Oauth Device Access response");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }
    if (fwrite(sarge_key, strlen(sarge_key), 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, SARGE_V2 ": Could not write pid to %s", FILENAME_OAUTH_DETAILS);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    fclose(fp);

    fp = fopen(FILENAME_OAUTH_TOKEN, "w");
    if(!fp) {
        ZPATH_LOG(AL_NOTICE, "Could not open token file");
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }
    if (fwrite(access_token, strlen(access_token), 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, SARGE_V2 ": Could not write token to %s", FILENAME_OAUTH_TOKEN);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }
    if (change_ownership(FILENAME_OAUTH_TOKEN)) {
        ZPATH_LOG(AL_NOTICE, SARGE_V2 ": Unable to change the ownership of %s", FILENAME_OAUTH_TOKEN);
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    if (fp)
        fclose(fp);

    return res;
}

static int64_t read_version(version_info *ver_ctx,
                            const char *file_name)
{
    struct stat st;
    FILE *fp;
    size_t len;
    int64_t ret = 0;


    if (stat(file_name, &st) != 0) {
        ver_ctx->version = NULL;
        return ret;
    }

    ret = st.st_mtime;
    if (ret == ver_ctx->last_change) {
        return ret;
    }

    fp = fopen(file_name, "r");
    if (!fp) {
        ver_ctx->version = NULL;
        return ret;
    }

    len = fread(ver_ctx->version_str, 1, sizeof(ver_ctx->version_str) - 1, fp);
    if (len <= 0) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Bad value (zero length file) in %s file. Removing it.", file_name);
        ver_ctx->version = NULL;
        fclose(fp);
        unlink(file_name);
        return 0;
    }

    ver_ctx->version_str[len] = '\0';

    do {
        len--;
        if (isspace(ver_ctx->version_str[len])) {
            ver_ctx->version_str[len] = '\0';
        } else {
            if (!isprint(ver_ctx->version_str[len])) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": Binary characters read from %s file. Removing it.", file_name);
                ver_ctx->version = NULL;
                fclose(fp);
                unlink(file_name);
                return 0;
            }
        }
    } while (len);

    if (ver_ctx->version_str[0] == '\0') {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Empty %s file", file_name);
        ver_ctx->version = NULL;
        fclose(fp);
        unlink(file_name);
        return 0;
    }
    ver_ctx->version  = ver_ctx->version_str;
    fclose(fp);
    return ret;
}

static int evbuffer_write_file(struct evbuffer *buf, const char *filename)
{
    FILE *fp;
    char buffer[65536];
    size_t len;

    fp = fopen(filename, "w");
    if (!fp) return ZPATH_RESULT_ERR;
    while ((len = evbuffer_remove(buf, buffer, sizeof(buffer))) > 0) {
        if (fwrite(buffer, 1, len, fp) != len) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not write to %s", filename);
            fclose(fp);
            return ZPATH_RESULT_ERR;
        }
    }
    fclose(fp);
    return ZPATH_RESULT_NO_ERROR;
}

static int write_version_file(const char *version, const char *filename)
{
    FILE *fp;
    size_t len;

    fp = fopen(filename, "w");
    if (!fp) return ZPATH_RESULT_ERR;
    len = strlen(version);
    if (fwrite(version, len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not write version to %s", filename);
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    fclose(fp);
    return ZPATH_RESULT_NO_ERROR;
}

static int write_pid_file(const char *pid_str, const char *filename)
{
    FILE *fp;
    size_t len;

    fp = fopen(filename, "w");
    if (!fp) return ZPATH_RESULT_ERR;
    len = strlen(pid_str);
    if (fwrite(pid_str, len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not write pid to %s", filename);
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    fclose(fp);
    return ZPATH_RESULT_NO_ERROR;
}

static struct fohh_http_client *get_http_client(SSL_CTX *ssl_ctx, char *proxy_hostname, char *api_hostname)
{
    struct fohh_http_client *client;
    enum fohh_http_client_status status;
    struct fohh_ssl_status *ssl_status;
    char str[INET6_ADDRSTRLEN];
    struct sockaddr_storage remote_addr;

    if (fohh_proxy_hostname) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Fetching from %s via %s via proxy %s:%d", api_hostname, proxy_hostname, fohh_proxy_hostname, fohh_proxy_port);
    } else {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Fetching from %s via %s", api_hostname, proxy_hostname ? proxy_hostname : "direct");
    }
    client = fohh_http_client_create_synchronous(sarge_zcdns,
                                                 ssl_ctx,
                                                 api_hostname,
                                                 proxy_hostname,
                                                 fohh_proxy_hostname,
                                                 443,
                                                 fohh_proxy_port,
                                                 10*(1000*1000),
                                                 &status);
    if (!client) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not create HTTP context");
        return NULL;
    }

    if (status != fohh_http_client_status_success) {
        if (!fohh_http_client_get_remote_addr(client, &remote_addr)) {
            zcdns_sockaddr_storage_to_str(&remote_addr, str, sizeof(str));
        } else {
            snprintf(str, sizeof(str), "unknown");
        }
        switch(status) {
        case fohh_http_client_status_dns_timeout:
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": DNS timed out for %s", proxy_hostname);
            break;
        case fohh_http_client_status_dns_fail:
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": DNS resolution failed for %s", proxy_hostname ? proxy_hostname : api_hostname);
            break;
        case fohh_http_client_status_connect_timeout:
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": TCP connection timed out to %s: %s:443", proxy_hostname, str);
            break;
        case fohh_http_client_status_connect_fail:
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": TCP connection failed to %s: %s:443", proxy_hostname, str);
            break;
        case fohh_http_client_status_connect_fail_ssl:
            ssl_status = fohh_http_client_get_ssl_status(client);
            if (ssl_status->err) {
                if (fohh_proxy_hostname) {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": TLS Verification Failure via %s:443 via proxy %s:%d: Failed certificate check at depth=%d, where subject=%s, issuer=%s. Error=%s",
                              str,
                              fohh_proxy_hostname,
                              fohh_proxy_port,
                              ssl_status->x509_err_depth,
                              ssl_status->err_subject,
                              ssl_status->err_issuer,
                              ssl_status->x509_err ? ssl_status->x509_err : "Unknown");
                } else {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": TLS Verification Failure via %s:443: Failed certificate check at depth=%d, where subject=%s, issuer=%s. Error=%s",
                              str,
                              ssl_status->x509_err_depth,
                              ssl_status->err_subject,
                              ssl_status->err_issuer,
                              ssl_status->x509_err ? ssl_status->x509_err : "Unknown");
                }
            } else {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": TLS connection failed before certificate verification");
            }
            break;
        case fohh_http_client_status_timeout:
            if (fohh_proxy_hostname) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": HTTP request timeout to %s via %s:443 via proxy %s:%d", api_hostname, str, fohh_proxy_hostname, fohh_proxy_port);
            } else {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": HTTP request timeout to %s via %s:443", api_hostname, str);
            }
            break;
        case fohh_http_client_status_failure:
        default:
            if (fohh_proxy_hostname) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": HTTP failure to %s via %s:443 via proxy %s:%d", api_hostname, str, fohh_proxy_hostname, fohh_proxy_port);
            } else {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": HTTP failure to %s via %s:443", api_hostname, str);
            }
            break;
        }
        fohh_http_client_destroy_from_another_thread(client);
        return NULL;
    }
    return client;
}

static void setcap(const char *filename)
{
    char out_buf[1000] = {0};
    size_t len = sizeof(out_buf);
    int res;

    char cap_string[512] = {0};

    if (is_container_env) {
        int cap_count = 0;
        const char * const *cap_list = zpath_get_final_capabilities(&cap_count);
        zpath_build_cap_string(cap_list, cap_count, cap_string, sizeof(cap_string), is_container_env);
    } else {
        strncpy(cap_string,
                "cap_net_admin,"
                "cap_net_bind_service,"
                "cap_net_raw,"
                "cap_sys_boot,"
                "cap_sys_nice,"
                "cap_sys_time+pe",
                sizeof(cap_string) - 1);
        cap_string[sizeof(cap_string) - 1] = '\0';
    }
    res = zvm_run_cmd(out_buf, &len, "/sbin/setcap", 0, cap_string, filename, NULL);

    if (res) {
        /*
            * When this fails, the child process is not supposed to work and will result in hard to debug issue. So
            * let us exit here and cry loud.
            */
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": setting capabilities for %s failed - %s", filename, out_buf);
#ifdef __linux__
        sleep(1);
        exit(SARGE_EXIT_CODE_SETCAP_FAILED);
#endif
    }
}

/*
 * NP connector does not stop wireguard driver on crashes except for SIGINT,SIGTERM
 * reason being each lan subnets don't have an alternate path and each lan subnet only map to one np connector
 * the decision is to have wireguard fail open since sarge always restart the child again once a child terminates
 *
 * But there would be a scenario where sarge falls back to default image if there are too many failure attempt on spinning up child,
 * and each attemp is less than 1min
 * In the case where sarge needs to fall back to a default image,
 * sarge will have to stop wireguard if wireguard is currently running.
 *
 */
static void stop_wg()
{
    FILE *fp;
    int status;
    int res = ZPATH_RESULT_NO_ERROR;

    fp = fopen(NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME, "r");
    if (fp) {
        if (fscanf(fp, "%d", &status) != 1) {
            ZPATH_LOG(AL_ERROR, "Could not read %s content", NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME);
            fclose(fp);
            return;
        }
        if (1 == status) {
            res = npwg_delete_wireguard_interface();
            if (res) {
                ZPATH_LOG(AL_ERROR, "Could not stop wireguard");
                fclose(fp);
                return;
            }

            npwg_provider_update_wg_status_file(0);
        }
        fclose(fp);
    }

    return;
}

/* Same FOHH based http client. For OAuth, connection just go direct without co2br proxy */
struct fohh_http_client* oauth_get_http_client(SSL_CTX *ssl_ctx, char *oauth_server_hostname)
{
    struct fohh_http_client* client = NULL;

    ZPATH_LOG(AL_INFO, SARGE_NAME ": [OAuth] Connecting to server %s ", oauth_server_hostname);

    // Going direct to OAuth Server is fine. FOHH proxy will still be honored.
    client = get_http_client(ssl_ctx, NULL, oauth_server_hostname);
    if (!client) {
        if (fohh_proxy_hostname) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": [OAuth] Could not connect to %s via proxy %s:%d",
                                                                          oauth_server_hostname,
                                                                          fohh_proxy_hostname,
                                                                          fohh_proxy_port);
        } else {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": [OAuth] Could not connect to %s", oauth_server_hostname);
        }
    }

    return client;
}

/*
 * 0.) The value for Oauth Server URL will come from service file.
 *     Export OAUTH_SERVER=fed/prod.oauth.zscaler.com (for manually overriding)
 * 1.) Get http Client
 * 2.) Read OAuth Server URL from ENV variable
 * 3.) Connect to OAuth Server
 * 4.) Get the Token
 *
 * Arguments
 * oauth_server : OAuth server hostname
 * resource_path : API URL
 * fingerprint : HW based fingerprint
 * oauth_challenge : Random challenge string
 * code_expiry : Expiry time for user code
 * interval : Retry every interval seconds
 * return_code : Filled in by this function, indicates the http response code
 */
static int oauth_get_device_auth(char* oauth_server,
                                 const char *resource_path,
                                 char* fingerprint,
                                 char* oauth_challenge,
                                 int64_t* code_expiry,
                                 int64_t* interval,
                                 int* return_code)
{
    struct fohh_http_client* client = NULL;
    int http_status = 0;
    char d_path[4096] = {0};
    struct evbuffer *body = NULL;
    char device_response[2048] = {0};
    int res = ZPATH_RESULT_ERR;
    SSL_CTX *ssl_ctx = NULL;

    if (config_fetch_ca_file[0] != '\0') {
        ssl_ctx = fohh_http_client_get_ssl_ctx_from_file(config_fetch_ca_file);
    } else {
        /* Create SSL ctx with hardcoded certs, this should never fail */
        ssl_ctx = fohh_http_client_get_ssl_ctx();
        /* Add OS bundle */
        if (ssl_ctx) {
            res = fohh_http_client_add_os_bundle_to_ssl_ctx(ssl_ctx, is_zscaler_os);
            if (res) {
                ZPATH_LOG(AL_INFO, "[OAuth] Cannot fetch OS bundle, error: %s, using built in root CAs", fohh_result_string(res));
            }
        }
    }

    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": [OAuth] Cannot get SSL context %s - %s", oauth_server,
                                                                                   resource_path);
        return ZPN_RESULT_ERR;
    }

    client = oauth_get_http_client(ssl_ctx, oauth_server);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Failed to get http client for %s - %s", oauth_server,
                                                                             resource_path);
        res = ZPN_RESULT_ERR;
        goto cleanup_err;
    }

    snprintf(d_path, sizeof(d_path), "%s?%s=%s&%s=%s", resource_path,
                                                       "client_id", fingerprint,
                                                       "challenge", oauth_challenge);
    ZPATH_LOG(AL_INFO, " Adjusted Device path = [%s] ", d_path);
    res = fohh_http_client_fetch_synchronous(SARGE_V2 ": Get device auth from OAuth Server",
                                             client,
                                             FOHH_HTTP_METHOD_POST,
                                             d_path,
                                             200,          /* Expected HTTP status */
                                             &http_status, /* Received HTTP status */
                                             &body,
                                             0);
    if (res) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get Device auth respone - Failed to get User token - %s",
                                                                     fohh_result_string(res));
        res = evbuffer_copyout(body, device_response, sizeof(device_response) - 1);
        goto cleanup_err;
    }

    res = evbuffer_copyout(body, device_response, sizeof(device_response) - 1);
    if (res <= 0) {
        goto cleanup_err;
    }

    res = parse_oauth_device_auth_response(device_response, code_expiry, interval);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Failed to parse the device auth response - %s", zpath_result_string(res));
    }

    *return_code = http_status;

cleanup_err:
    if (body) evbuffer_free(body);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);

    return res;
}

static int oauth_get_device_token(char* oauth_server,
                                    const char *resource_path,
                                    char* fingerprint,
                                    char* oauth_challenge,
                                    int *return_code)
{
    struct fohh_http_client* client = NULL;
    int http_status = 0;
    char t_path[4096] = {0};
    struct evbuffer *body = NULL;
    int res = ZPATH_RESULT_ERR;
    char device_response[2048] = {0};
    SSL_CTX *ssl_ctx = NULL;

    if (config_fetch_ca_file[0] != '\0') {
        ssl_ctx = fohh_http_client_get_ssl_ctx_from_file(config_fetch_ca_file);
    } else {
        /* Create SSL ctx with hardcoded certs, this should never fail */
        ssl_ctx = fohh_http_client_get_ssl_ctx();
        /* Add OS bundle */
        if (ssl_ctx) {
            res = fohh_http_client_add_os_bundle_to_ssl_ctx(ssl_ctx, is_zscaler_os);
            if (res) {
                ZPATH_LOG(AL_INFO, "[OAuth] Cannot fetch OS bundle, error: %s, using built in root CAs",
                                                                                 fohh_result_string(res));
            }
        }
    }

    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": [OAuth] Cannot get SSL context %s - %s", oauth_server, resource_path);
        return ZPN_RESULT_ERR;
    }

    client = oauth_get_http_client(ssl_ctx, oauth_server);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Failed to get http client for %s - %s", oauth_server,
                                                                             resource_path);
        res = ZPN_RESULT_ERR;
        goto cleanup_err;
    }

    snprintf(t_path,sizeof(t_path), "%s?%s=%s&%s=%s&%s=%s&%s=%s",resource_path,
                                                           "grant_type", OAUTH_DEVICECODE_GRANT,
                                                           "code", device_token,
                                                           "client_id", fingerprint,
                                                           "verifier", oauth_challenge);
    ZPATH_LOG(AL_INFO, " Adjusted Token path = [%s] ", t_path);
    res = fohh_http_client_fetch_synchronous(SARGE_V2 ": Get Provision Key from OAuth server",
                                             client,
                                             FOHH_HTTP_METHOD_POST,
                                             t_path,
                                             200,          /* Expected HTTP status */
                                             &http_status, /* Received HTTP status */
                                             &body,
                                             0);
    if (res == ZPATH_RESULT_NO_ERROR) {
        res = evbuffer_copyout(body, device_response, sizeof(device_response) - 1);
        if (res <= 0) {
            ZPATH_LOG(AL_ERROR, "[OAuth] Get Provision Key from OAuth server - Unable to copy data from response buffer - %s",
                                 zpath_result_string(res));
            goto cleanup_err;
        }

        res = parse_oauth_device_access_response(device_response);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "[OAuth] Get Provision Key from OAuth server - Failed to parse the provisioing key response - %s",
                                 zpath_result_string(res));
            goto cleanup_err;
        }

        pthread_mutex_lock(&user_code_status.user_code_lock);
        user_code_status.is_code_consumed = 1;
        pthread_mutex_unlock(&user_code_status.user_code_lock);
    }

    *return_code = http_status;

cleanup_err:
    if (body) evbuffer_free(body);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);

    return res;
}

static void fetch_and_display_user_code(evutil_socket_t sock, short flags, void *cookie) {
    struct zthread_info *zthread = (struct zthread_info *) cookie;
    int write_to_console = 0;
    char console_message[200] = {0};

    zthread_heartbeat(zthread);

    ZPATH_LOG(AL_NOTICE, "CALLED: fetch_and_display_user_code");
    pthread_mutex_lock(&user_code_status.user_code_lock);
    if (user_code_status.user_code[0] != '\0' && !user_code_status.is_code_consumed) {
        write_to_console = 1;
        snprintf(console_message, sizeof(console_message), "\n\nOAuth Token: %s\n%s\n\n", user_code_status.user_code, user_code_status.message);
    }
    pthread_mutex_unlock(&user_code_status.user_code_lock);
    if (write_to_console) {
        print_to_serial_devices_and_issue(console_message);
    }
}

static void * user_code_monitor_thread(struct zthread_info *zthread, void *cookie) {
    struct event_base *base = NULL;
    struct timeval tv = {0};
    struct event *ev_oauth_token_timer;

    base = event_base_new();

    ev_oauth_token_timer = event_new(base,
                                     -1,
                                     EV_PERSIST,
                                     fetch_and_display_user_code,
                                     zthread);

    tv.tv_sec = 60;
    tv.tv_usec = 0;
    if (event_add(ev_oauth_token_timer, &tv)) {
        ZPATH_LOG(AL_ERROR, "Could not activate timer");
        return NULL;
    }

    event_base_dispatch(base);
    return NULL;
}

int user_code_monitor_init() {
    pthread_t thread;

    int res = zthread_create(&thread,
                             user_code_monitor_thread,
                             NULL,
                             "user_code_monitor",
                             120,
                             16*1024*1024,
                             60*1000*1000, /* 60s statistics interval */
                             NULL);
    if (res) {
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int fetch_version(const char *bin_path)
{
    char metadata_path[1000];
    char binary_path[1000];
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    int res;
    struct stat st;

    unlink(FILENAME_BIN_NEW);
    unlink(FILENAME_META_NEW);

    snprintf(metadata_path, sizeof(metadata_path), "%s/%s.%s.%s.%s.meta",
             bin_path,
             config_role,
             config_override_plat ? config_override_plat : zhw_platform(),
             config_override_arch ? config_override_arch : zhw_platform_arch(),
             config_version_context.version ? config_version_context.version : "default");
    snprintf(binary_path, sizeof(binary_path), "%s/%s.%s.%s.%s.bin",
             bin_path,
             config_role,
             config_override_plat ? config_override_plat : zhw_platform(),
             config_override_arch ? config_override_arch : zhw_platform_arch(),
             config_version_context.version ? config_version_context.version : "default");

    if (config_fetch_ca_file[0] != '\0') {
        ssl_ctx = fohh_http_client_get_ssl_ctx_from_file(config_fetch_ca_file);
    } else {
        /* Create SSL ctx with hardcoded certs, this should never fail */
        ssl_ctx = fohh_http_client_get_ssl_ctx();
        /* Add OS bundle */
        if (ssl_ctx) {
            res = fohh_http_client_add_os_bundle_to_ssl_ctx(ssl_ctx, is_zscaler_os);
            if (res) {
                ZPATH_LOG(AL_INFO, "Cannot fetch OS bundle, error: %s, using built in root CAs", fohh_result_string(res));
            }
        }
    }
    const struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(config_cloud_name);
    if ( (cloud_config) && (cloud_config->zpa_cloud_trusted_ca_certs_count > 0 )) {
        for (int certind=0; certind < cloud_config->zpa_cloud_trusted_ca_certs_count; certind++ ){
            int cert_res=fohh_http_client_add_trusted_certs_to_ssl_ctx_from_buffer(ssl_ctx,cloud_config->zpa_cloud_trusted_ca_certs[certind].root_cert , strnlen(cloud_config->zpa_cloud_trusted_ca_certs[certind].root_cert, ZCRYPT_MAX_CERT_LEN));
            if (cert_res) {
                ZPATH_LOG(AL_ERROR, "FAILED: ADD custom trusted ca cert to SSL \n");
            } else {
                ZPATH_LOG(AL_ERROR, "SUCCESS: ADD custom trusted ca cert to SSL \n");
            }
        }
    }
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Cannot get SSL context");
        goto cleanup_err;
    }

    client = get_http_client(ssl_ctx, config_fetch_proxyname, config_fetch_hostname);
    if (!client) {
        if (fohh_proxy_hostname) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not connect to %s via %s via proxy %s:%d", config_fetch_hostname, config_fetch_proxyname, fohh_proxy_hostname, fohh_proxy_port);
        } else {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not connect to %s via %s", config_fetch_hostname, config_fetch_proxyname);
        }
        dump_on_failure();
        goto cleanup_err;
    }

    if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Fetching metadata from <%s>", metadata_path);

    res = fohh_http_client_fetch_synchronous(SARGE_NAME ": Fetch Metadata",
                                             client,
                                             FOHH_HTTP_METHOD_GET,
                                             metadata_path,
                                             200,          /* Expected HTTP status */
                                             &http_status, /* Received HTTP status */
                                             &body,
                                             0);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not get metadata for version %s", config_version_context.version ? config_version_context.version : "default");
        dump_on_failure();
        goto cleanup_err;
    }
    ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Successfully fetched metadata from <%s>", metadata_path);

    res = evbuffer_write_file(body, FILENAME_META_NEW);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not write downloaded metadata file");
        goto cleanup_err;
    }

    evbuffer_free(body);
    body = NULL;

    /* We will short circuit re-downloading binary if the metadata matches the binary we already have */
    if ((stat(FILENAME_BIN, &st) == 0) && (zcrypt_metadata_verify_version(FILENAME_BIN, FILENAME_META_NEW, config_version_context.version, config_version_context.version_str, sizeof(config_version_context.version_str), log_verify_version_f, config_develop, 0) == ZCRYPT_RESULT_NO_ERROR)) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Downloaded metadata verifies binary we already have.");

        res = rename(FILENAME_META_NEW, FILENAME_META);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not rename %s to %s", FILENAME_META_NEW, FILENAME_META);
            goto cleanup_err;
        }

    } else {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Downloaded metadata does not match any binary we already have.");

        if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Fetching binary from <%s>", binary_path);

        res = fohh_http_client_fetch_synchronous(SARGE_NAME ": Fetch Image",
                                                 client,
                                                 FOHH_HTTP_METHOD_GET,
                                                 binary_path,
                                                 200,
                                                 &http_status,
                                                 &body,
                                                 FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR,
                                                 FOHH_HTTP_CLIENT_TIMEOUT);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not fetch binary image %s", binary_path);
            dump_on_failure();
            goto cleanup_err;
        }
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Successfully fetched binary from <%s>", binary_path);


        res = evbuffer_write_file(body, FILENAME_BIN_NEW);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not write downloaded metadata file");
            goto cleanup_err;
        }

        if (zcrypt_metadata_verify_version(FILENAME_BIN_NEW, FILENAME_META_NEW, config_version_context.version, config_version_context.version_str, sizeof(config_version_context.version_str), log_verify_version_f, config_develop, 0)) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Fetched version %s does not verify", config_version_context.version ? config_version_context.version : "default");
            goto cleanup_err;
        }
        config_version_context.version = config_version_context.version_str;

        if (chmod(FILENAME_BIN_NEW, 0755) != 0) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not mark image as executable");
            goto cleanup_err;
        }

        res = rename(FILENAME_META_NEW, FILENAME_META);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not rename %s to %s", FILENAME_META_NEW, FILENAME_META);
            goto cleanup_err;
        }

        res = rename(FILENAME_BIN_NEW, FILENAME_BIN);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Could not rename %s to %s", FILENAME_BIN_NEW, FILENAME_BIN);
            goto cleanup_err;
        }
    }

    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_NO_ERROR;

 cleanup_err:
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_ERR;
}

/*
 * return the timestamp of when the config related to "next software version of zpa-connector-child" changed
 * in the context of zpa-connector-child process. the child process writes the new version to a file and we just stat
 * that file's modified time and return back.
 *
 */
static int64_t fetch_software(void)
{
    char str[100];
    int count = 0;
    int res;
    static int64_t last_change = 0;
    int64_t change;
    int force_update = 0;

    change = read_version(&(config_version_context), FILENAME_VERSION);

    if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Zscaler software version change debug: change: %ld, last_change: %ld",
                               (long)change, (long)last_change);

    if (change && (change == last_change)) {
        if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Zscaler software version has not changed");
        return change;
    }
    last_change = change;

    if (zcrypt_metadata_verify_version(FILENAME_BIN, FILENAME_META, config_version_context.version, NULL, 0, log_verify_version_f, config_develop, 0) == ZCRYPT_RESULT_NO_ERROR) {
        if (zcrypt_metadata_verify_platform(FILENAME_META, zhw_platform(), log_verify_version_f, 0) == ZCRYPT_RESULT_NO_ERROR) {
            if (config_version_context.version) {
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Zscaler software update: Currently installed version verified as %s", config_version_context.version ? config_version_context.version : "NULL");
            }
            return change;
        } else {
            force_update = 1;
            config_version_context.version = NULL;
            ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Force update rpm on %s platform", zhw_platform());
        }
    }

    if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Fetching version %s", config_version_context.version ? config_version_context.version : "default");
    while (1) {
        /* Check if expected version has changed... but only read if
         * we already have a version in place, otherwise we cannot
         * fetch default. */
        if (config_version_context.version && !force_update) read_version(&(config_version_context), FILENAME_VERSION);
        res = fetch_version(config_fetch_path);
        if (res == ZPATH_RESULT_NO_ERROR) {
            if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Fetched and verified version %s", config_version_context.version ? config_version_context.version : "default");
            break;
        }
        if (config_develop) {
            res = fetch_version(config_fetch_path_develop);
            if (res == ZPATH_RESULT_NO_ERROR) {
                if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Fetched and verified version %s", config_version_context.version ? config_version_context.version : "default");
                break;
            }
        }
        count++;
        if (count > config_max_fails_fetch) {
            if (config_version_context.version) {
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Too many failures, fetching default version in %s", seconds_to_dhms(str, sizeof(str), config_fetch_backoff * count));
            } else {
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Reattempting to fetch software in %s and nofips is %d", seconds_to_dhms(str, sizeof(str), config_fetch_backoff * count), is_fips_mode);
            }
            config_version_context.version = NULL;
            if (count > config_max_fails_fetch * 2) {
                count = config_max_fails_fetch * 2;
                /* If we still can't get new software, at least make
                 * sure existing software is running if it is
                 * correctly installed */
                if (zcrypt_metadata_verify_version(FILENAME_BIN, FILENAME_META, NULL, NULL, 0, log_verify_version_f, config_develop, 0) == ZCRYPT_RESULT_NO_ERROR) {
                    return change;
                }
            }
        } else {
            ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Reattempting to fetch software in %s", seconds_to_dhms(str, sizeof(str), config_fetch_backoff * count));
        }
        sleep(config_fetch_backoff * count);
    }
    return change;
}

static int get_sudo_path(char *sudo_path) {

    int res = ZPATH_RESULT_NO_ERROR;

    if (!is_container_env) {
        find_executable_in_path("sudo", sudo_path);

        if (strlen(sudo_path) > 0) {
            ZPATH_LOG(AL_NOTICE,"sudo found at: %s", sudo_path);
        } else {
            ZPATH_LOG(AL_ERROR,"sudo not found");
            res = ZPATH_RESULT_ERR;
        }
    }

    return res;
}

static int get_package_manager_path(char *install_manager_path) {

    int res = ZPATH_RESULT_NO_ERROR;
    const char *package_manager;

    if (is_container_env) {
        package_manager = MICRODNF;
    } else {
        package_manager = YUM;
    }

    find_executable_in_path(package_manager, install_manager_path);

    if (strlen(install_manager_path) > 0) {
        ZPATH_LOG(AL_NOTICE, "%s found at: %s", package_manager, install_manager_path);
    } else {
        ZPATH_LOG(AL_ERROR, "%s not found", package_manager);
        res = ZPATH_RESULT_ERR;
    }

    return res;
}

static void upgrade_sarge_and_os() {

    char sudo_path[PATH_MAX];
    char install_manager_path[PATH_MAX];
    char expected_sarge_version[ZPATH_VERSION_STR_MAX_LEN];

    int os_upgrade_enabled = 0;
    int os_upgrade_feature_flag = 0;
    int sarge_upgrade_feature_flag = 0;
    int full_os_upgrade_enabled_feature_flag = 0;

    int sarge_upgrade_status = 0;
    int os_upgrade_status = 0;
    int reboot_retries = 0;
    int is_upgrade = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    struct zpath_upgrade_stats upgrade_stats = {0};

    zpath_upgrade_read_stats(&upgrade_stats);

    /* If there was an error, we log and continue as we need to report the status of upgrade as failed */
    res = zpath_upgrade_read_sarge_and_os_upgrade_cfg(&os_upgrade_enabled, &os_upgrade_feature_flag, &full_os_upgrade_enabled_feature_flag,
                                        &sarge_upgrade_feature_flag, expected_sarge_version, sizeof(expected_sarge_version));
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in getting sarge and os upgrade cfg info from %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        upgrade_stats.sarge_os_cfg_read_fail++;
        goto cleanup;
    }

    res = get_sudo_path(sudo_path);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in getting path. Aborting upgrade process");
        upgrade_stats.sudo_path_fail++;
        goto cleanup;
    }

    res = get_package_manager_path(install_manager_path);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in getting package manager. Aborting upgrade process");
        upgrade_stats.package_manager_path_fail++;
        goto cleanup;
    }

    int cmp = version_compare(expected_sarge_version, ZPATH_VERSION);
    if (cmp == 1) {
        is_upgrade = 1;
    }

    if (is_upgrade) {
        /* os_upgrade_enabled from db is true and feature flag is on*/
        if (os_upgrade_enabled && os_upgrade_feature_flag) {
            res = upgrade_system_os(sudo_path, install_manager_path, full_os_upgrade_enabled_feature_flag, &upgrade_stats.os_upgrade_timeout);
            if (res) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in upgrading OS");
                __sync_add_and_fetch_8(&(upgrade_stats.os_upgrade_fail), 1);
            } else {
                os_upgrade_status = 1;
                ZPATH_LOG(AL_INFO, SARGE_NAME ": successfuly upgraded OS");
                __sync_add_and_fetch_8(&(upgrade_stats.os_upgrade_success), 1);
            }
        }
    } else {
        ZPATH_LOG(AL_INFO, SARGE_NAME ": Skipping OS upgrade since Sarge did not get upgraded");
    }

    if (sarge_upgrade_feature_flag) {
        res = fetch_and_install_sarge(expected_sarge_version, install_manager_path, sudo_path, is_upgrade);
        if (res) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in upgrading Sarge");
            __sync_add_and_fetch_8(&(upgrade_stats.sarge_upgrade_fail), 1);
        } else {
            sarge_upgrade_status = 1;
            __sync_add_and_fetch_8(&(upgrade_stats.sarge_upgrade_success), 1);
        }
    }

    res = zpath_upgrade_write_upgrade_status(os_upgrade_status, sarge_upgrade_status);
    if (res) {
        /* Not a fatal failure. Log and continue with the rest of the upgrade process.*/
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in writing Sarge and OS Upgrade status into %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    }

    res = zpath_upgrade_write_stats(&upgrade_stats);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in writing Sarge and OS failure status into %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    }

    if (os_upgrade_status && reboot_required()) {
        ZPATH_LOG(AL_INFO, SARGE_NAME ": OS upgrade successful! Rebooting system to apply latest changes...");
        while (reboot_retries < REBOOT_MAX_FAILS) {
            res = reboot_system(sudo_path);
            reboot_retries += 1;
            /* No need to log successful case, would have rebooted */
            if (res) {
                ZPATH_LOG(AL_CRITICAL, SARGE_NAME ": Retrying reboot... (Attempt %d of %d)\n", reboot_retries, REBOOT_MAX_FAILS);
                sleep(1);
            }
        }
    } else if (sarge_upgrade_status) {
        ZPATH_LOG(AL_INFO, SARGE_NAME ": Only Sarge upgrade successful! Rebooting Sarge to apply latest changes...");
        sleep(1);
        exit(0);
    } else {
        ZPATH_LOG(AL_INFO, SARGE_NAME ": Sarge restart not required");
    }
    return;

cleanup:
    res = zpath_upgrade_write_stats(&upgrade_stats);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in writing Sarge and OS failure status into %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    }
}

static int install_last_working_sarge_version() {

    char sudo_path[PATH_MAX];
    char install_manager_path[PATH_MAX];
    char last_working_sarge_version[ZPATH_VERSION_STR_MAX_LEN];
    int is_upgrade = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    /* Read last working sarge version*/
    res = zpath_upgrade_get_last_working_sarge_version(last_working_sarge_version, sizeof(last_working_sarge_version));
    if (res) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Could not get last sarge version. Aborting rollback");
        return ZPATH_RESULT_ERR;
    }

    res = get_sudo_path(sudo_path);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in getting path. Aborting upgrade process");
        return ZPATH_RESULT_ERR;
    }

    res = get_package_manager_path(install_manager_path);
    if (res) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Error in getting package manager. Aborting rollback");
        return ZPATH_RESULT_ERR;
    }

    int cmp = version_compare(last_working_sarge_version, ZPATH_VERSION);
    if (cmp == 1) {
        is_upgrade = 1;
    }

    res = fetch_and_install_sarge(last_working_sarge_version, install_manager_path, sudo_path, is_upgrade);
    if (res) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Failed to install last sarge version. Aborting rollback");
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

int is_sarge_role_np() {
    if (config_role == NULL) {
        ZPATH_LOG(AL_CRITICAL, SARGE_NAME ": Config role is empty. This should not happen!!");
    }
    /* This is for pre drop42 np sarge where they are deployed as zpa-connector*/
    return (strstr(config_role, "zpa-connector") != NULL &&
            access(NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME, F_OK) == 0);
}

int is_sarge_role_sitec() {
    if (config_role == NULL) {
        ZPATH_LOG(AL_CRITICAL, SARGE_NAME ": Config role is empty. This should not happen!!");
        sleep(1);
        exit(1);
    }
    return (strstr(config_role, "zpa-pcc") != NULL);
}

static int is_dropdb_required()
{
    char dropdb[100];
    char running_version[256];
    int drop = 0;
    FILE *fp;
    size_t len;

    zcrypt_get_metadata_dropdb(FILENAME_META, dropdb, sizeof(dropdb), log_verify_version_f, 0);
    /* If dropdb is configured
     *  if running version file is available and has a proper version
     *      if config_version != running version then dropdb
     */
    if (strcmp(dropdb, "1") == 0) {
        fp = fopen(FILENAME_RUNNING_VERSION, "r");
        if (!fp) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": unable to open running version file in read mode. File might not be preset");
            /*running_version file not there, then it is child new installation.
             * dropdb when spawning child for first time
             */
            drop = 1;
            return drop;
        }

        len = fread(running_version, 1, sizeof(running_version) - 1, fp);
        if (len <= 0) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Bad value (zero length file) in running version file. Removing it.");
            fclose(fp);
            unlink(FILENAME_RUNNING_VERSION);
            drop = 1;
            return drop;
        }
        running_version[len] = 0;

        do {
            len--;
            if (isspace(running_version[len])) {
                running_version[len] = 0;
            } else {
                if (!isprint(running_version[len])) {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": Binary characters read from running version file. Removing it.");
                    fclose(fp);
                    unlink(FILENAME_RUNNING_VERSION);
                    drop = 1;
                    return drop;
                }
            }
        } while (len);
        len = strlen(running_version);
        if (!len) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Empty running version file");
            fclose(fp);
            unlink(FILENAME_RUNNING_VERSION);
            drop = 1;
            return drop;
        }
        fclose(fp);

        if (config_version_context.version && strcmp(config_version_context.version, running_version)) {
            drop = 1;
        }
    }

    return drop;
}

static void run_software(int64_t version_timestamp)
{
    char *args[22];
    char *env[MAX_ENV_VARS] = {0};
    char cwd[MAXPATHLEN + 1];
    char full_path[MAXPATHLEN + 16];
    char str[100];
    static pid_t pid = -1;
    static int64_t start_s = 0;
    static int64_t fails = 0;
    static int64_t def = 0;
    int64_t now;
    int64_t diff;
    int res;
    char *c;
    int arg_count = 1;
    int env_idx = 0;

    if (config_override_no_run) {
        ZPATH_LOG(AL_DEBUG, SARGE_NAME ": skipping running of software (test mode)");
        return;
    }

    if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Checking running software");


    now = epoch_s();
    diff = now - start_s;

    if (pid > 0) {
        /* Check if we still have a running process */
        pid_t check;
        int status;
        check = waitpid(pid, &status, WNOHANG);
        if (check == 0) {
            if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Child still running. Uptime %s",
                                       seconds_to_dhms(str, sizeof(str), diff));
            if (version_timestamp) {
                /*
                 * FIXME: what haappens when the process is started at X-th sec by sargeand the version_timestamp
                 * is also written by child process at X-th sec?
                 */
                if (version_timestamp > start_s) {
                    /* Do nothing- version is up to date... */
                } else {
                    if (diff > config_max_wait_config) {
                        /* We waited long enough for software to start up- it couldn't connect, so kick it. */
                        ZPATH_LOG(AL_ERROR, SARGE_NAME ": %s Did not successfully connect to cloud. Restarting it.", config_role);
                        fails++;
                        kill(pid, SIGTERM);
                        /* Kill child... */
                        if (fails >= config_max_fails_connect) {
                            ZPATH_LOG(AL_ERROR, SARGE_NAME ": %s Did not successfully connect to cloud too many times in a row- reverting to default software", config_role);
                            stop_wg();
                            unlink(FILENAME_VERSION);
                            unlink(FILENAME_META);
                            fails = 0;
                        }
                        return;
                    }
                }
            }
            if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Child still running,");
        } else if (check > 0) {
            if (diff < config_min_run_time) {
                fails++;
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": %s exited too fast, was running for %s", config_role, seconds_to_dhms(str, sizeof(str), diff));
                if (fails >= config_max_fails_running) {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": %s failed too many times in a row- reverting to default software", config_role);
                    stop_wg();
                    unlink(FILENAME_VERSION);
                    unlink(FILENAME_META);
                    if (def) {
                        ZPATH_LOG(AL_ERROR, SARGE_NAME ": %s Default software failing; waiting %s for smoke to clear", config_role, seconds_to_dhms(str, sizeof(str), config_def_backoff_sleep));
                        sleep(config_def_backoff_sleep);
                    }
                    def = 1;
                    fails = 0;
                    pid = -1;
                    return;
                }
            } else {
                if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Child exited, was running for %s",
                                           seconds_to_dhms(str, sizeof(str), diff));
                fails = 0;
                def = 0;
                if (is_sarge_role_np()) {
                    ZPATH_LOG(AL_INFO, SARGE_NAME ": Auto sarge and OS upgrade feature not available for NP Connector");
                } else {
                    if (WIFEXITED(status) && (WEXITSTATUS(status) == CHILD_UPGRADE_SUCCESS_EXIT_CODE)) {
                        // Child exited normally
                        ZPATH_LOG(AL_INFO, SARGE_NAME ": Child exited due to an upgrade. Attempting to upgrade Sarge and OS");
                        if (zvm_vm_type_is_zscaler_rh_image()) {
                            upgrade_sarge_and_os();
                        } else {
                            ZPATH_LOG(AL_INFO, SARGE_NAME ": Auto Sarge and OS upgrade feature not supported for Non Zscaler OS");
                        }
                    }
                }
            }
            pid = -1;
        } else {
            if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Error checking child status");
        }
    }
    if (pid < 0) {

        if (strstr(config_role, "np-connector") != NULL) {
            check_frr_installation();
        }

        char envstr[2000];
        char envstr_develop[64];
        char envstr_disable_geoip[64];
        char envstr_cap_mode[64];
        char envstr_custom_caps[1024];
        const char *cap_mode_env;
        const char *custom_caps;
        char proxy_str1[100];
        char envstr_oauth_enrollment[100] = {0};
        struct passwd *pw;

        if (!is_zscaler_os) {
            /* Set the required capabilities on child image.
             * This needs to be executed right before executing child to
             * avoid child running with incorrect privileges (ET-30558)
             * Note: This function will fail when called on ZscalerOS */
            setcap(FILENAME_BIN);
        }
        /*
         * The RPM when installed creates the user zscaler. We want the
         * zpa-connector-child process to not run as 'root' user for security
         * reasons - any externally downloaded software running as a root is
         * giving so much privilage. run_child_as_current_user is applicable
         * only for dev environment.
         */
        pw = NULL;
        if (!run_child_as_current_user) {
            pw = getpwnam("zscaler");
            if (!pw) {
                ZPATH_LOG(AL_ALERT, SARGE_NAME ": Cannot find Zscaler user to setuid to - %s", strerror(errno));
                return;
            }
        }
        /* Need to spawn a child... */
        if (!getcwd(cwd, sizeof(cwd))) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Cannot find current working directory - %s", strerror(errno));
            return;
        }
        snprintf(full_path, sizeof(full_path), "%s/%s", cwd, FILENAME_BIN);
        memset(args, 0, sizeof(args));
        args[0] = config_role;

        if (strcmp(config_fetch_proxyname,config_fetch_hostname) == 0) {
            args[arg_count] = "-direct";
            arg_count++;
        }
        if (sarge_debug) {
            args[arg_count] = "-debuglog";
            arg_count++;
        }

        if (is_sarge_role_sitec()) {
            if (is_dropdb_required()) {
                args[arg_count] = "-dropdb";
                arg_count++;
            }
            unlink(FILENAME_RUNNING_VERSION);
        }

        if (is_container_env) {
            args[arg_count] = "-container";
            arg_count++;
        }

        if (is_zscaler_os) {
            args[arg_count] = "-freebsd_compat_mode";
            arg_count++;
        }

        if (max_logging_mb_child != NULL) {
            /* Send argument to child to set argo memory limit to whatever specified */
            args[arg_count] = "-maxlogmb";
            arg_count++;

            args[arg_count] = max_logging_mb_child;
            arg_count++;
        }

        if( memory_arena_count !=  NULL) {
            args[arg_count] = "-memory_arena_count";
            arg_count++;

            args[arg_count] = memory_arena_count;
            arg_count++;
        }

        if (fohh_proxy_hostname) {
            args[arg_count] = "-fproxy";
            arg_count++;
            args[arg_count] = fohh_proxy_hostname;
            arg_count++;
            args[arg_count] = "-fproxy_port";
            arg_count++;
            snprintf(proxy_str1, sizeof(proxy_str1), "%d", fohh_proxy_port);
            args[arg_count] = proxy_str1;
            arg_count++;
        }

        if (!is_fips_mode) {
            args[arg_count] = "-nofips";
            arg_count++;
        }

        /* pass child logging command line arguments */
        if (child_event_log_lvl_console != -1) {
            args[arg_count] = ZPATH_APP_EVENT_LOG_LVL_CONSOLE;
            arg_count++;
            snprintf(child_event_log_lvl_console_str, sizeof(child_event_log_lvl_console_str), "%d", child_event_log_lvl_console);
            args[arg_count] = child_event_log_lvl_console_str;
            arg_count++;
        }

        if (child_event_log_lvl_syslog != -1) {
            args[arg_count] = ZPATH_APP_EVENT_LOG_LVL_SYSLOG;
            arg_count++;
            snprintf(child_event_log_lvl_syslog_str, sizeof(child_event_log_lvl_syslog_str), "%d", child_event_log_lvl_syslog);
            args[arg_count] = child_event_log_lvl_syslog_str;
            arg_count++;
        }

        /* Add provision_key from env to the child environment */
        if ((c = getenv(PASS_ENV))) {
            snprintf(envstr, sizeof(envstr), "%s=%s", PASS_ENV, c);
            env[env_idx] = envstr;
            env_idx++;
        }

        if (config_develop) {
            /* Add develop enabled flag to the child environment */
            snprintf(envstr_develop, sizeof(envstr_develop), "%s=%s", ZCRYPT_ZPA_DEVELOP_ENV, "ENABLED");
            env[env_idx] = envstr_develop;
            env_idx++;
        } else {
            /* Add develop disabled flag to the child environment */
            snprintf(envstr_develop, sizeof(envstr_develop), "%s=%s", ZCRYPT_ZPA_DEVELOP_ENV, "DISABLED");
            env[env_idx] = envstr_develop;
            env_idx++;
        }

        /* add disable geoip flag to child env*/
        snprintf(envstr_disable_geoip, sizeof(envstr_disable_geoip), "%s=%s", ZCRYPT_ZPA_DISABLE_GEOIP, disable_geoip?"TRUE":"FALSE");
        env[env_idx] = envstr_disable_geoip;
        env_idx++;

        /* add oauth based enrollment flag to child env*/
        snprintf(envstr_oauth_enrollment, sizeof(envstr_oauth_enrollment), "%s=%s", ENV_ZPA_OAUTH_ENROLLMENT, sarge_v2?"TRUE":"FALSE");
        env[env_idx] = envstr_oauth_enrollment;
        env_idx++;

        if (is_container_env) {
            // Pass ZPA_CAPABILITY_MODE to child if set
            cap_mode_env = getenv(CAPABIITY_MODE);
            if (cap_mode_env) {
                snprintf(envstr_cap_mode, sizeof(envstr_cap_mode), "%s=%s", CAPABIITY_MODE, cap_mode_env);
                env[env_idx] = envstr_cap_mode;
                env_idx++;
            }

            // Pass ZPA_CUSTOM_CAPABILITIES to child if set
            custom_caps = getenv(CUSTOM_CAPABILITIES);
            if (custom_caps) {
                snprintf(envstr_custom_caps, sizeof(envstr_custom_caps), "%s=%s", CUSTOM_CAPABILITIES, custom_caps);
                env[env_idx] = envstr_custom_caps;
                env_idx++;
            }
        }

        env[env_idx] = 0;

        pid = fork();
        res = 0;
        if (pid == 0) {
            /* CHILD */
            /* Drop Root */
            if (!run_child_as_current_user) {
                if (is_openshift_env || is_kubernetes_env) {
#ifdef __linux__
                    if (prctl(PR_SET_SECUREBITS, SECBIT_NO_SETUID_FIXUP | SECBIT_NO_SETUID_FIXUP_LOCKED) < 0) {
                        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed to set securebits - %s", strerror(errno));
                    } else {
                        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Successfully set securebits");
                    }
#else
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": Not Linux, could not set securebits");
#endif
				}
                if (setgid(pw->pw_gid) == -1) {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed to setgid - %s", strerror(errno));
                    sleep(1);
                    exit(1);
                }
                if (setuid(pw->pw_uid) == -1) {
                    ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed to setuid - %s", strerror(errno));
                    sleep(1);
                    exit(1);
                }
            }
            res = execve(full_path, args, env);
            /* If we're here, we failed! Note that res is only set on
             * failure. */
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": execve failed - %s", strerror(errno));
            sleep(1);
            _exit(0);
        } else if (pid < 0) {
            /* FAILED TO FORK */
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed to fork - %s", strerror(errno));
        } else {
            /* PARENT */
            if (res < 0) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": execve failed for %s at %s - %s", config_role, full_path,
                          strerror(errno));
            } else {
                start_s = now;
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": %s Started from %s", config_role, full_path);
            }
        }
    }
}

// If one of the fields couldn't be read, we keep trying
static void fetch_inst_id_and_org_name_from_cert(int *instance_id_read, int *org_name_read)
{
    struct zcrypt_cert *cert;
    char inst_name[INSTANCE_NAME_MAX_LEN] = {0};
    char org_name[128] = {0};

    cert = zcrypt_cert_read(FILENAME_CERT);
    if (!cert) {
        ZPATH_LOG(AL_WARNING, "Could not read cert file %s", FILENAME_CERT);
        return;
    }

    if (*instance_id_read == 0) {
        if (!fohh_x509_get_cn(zcrypt_cert_get_x509(cert), inst_name, sizeof(inst_name))) {
            *instance_id_read = 1;
            zthread_init("zpa-sarge", ZPATH_VERSION, inst_name, NULL, NULL);
        } else {
            ZPATH_LOG(AL_WARNING, "Could not get CN from cert file %s yet", FILENAME_CERT);
        }
    }

    if (*org_name_read == 0) {
        if (!fohh_x509_get_on(zcrypt_cert_get_x509(cert), org_name, sizeof(org_name))) {
            *org_name_read = 1;
            zthread_set_org_name(org_name);
        } else {
            ZPATH_LOG(AL_WARNING, "Could not get ON from cert file %s yet", FILENAME_CERT);
        }
    }

    zcrypt_cert_free(cert);
}

static int sub_main(int argc, char *argv[])
{
    int64_t        start_time = epoch_us();
    pid_t          pid;
    char           pid_str[128];
    struct stat    st;
    int            instance_id_read = 0;
    int            org_name_read = 0;


    char version_to_write[ZPATH_VERSION_STR_MAX_LEN];
    strncpy(version_to_write, ZPATH_VERSION, ZPATH_VERSION_STR_MAX_LEN);

    if (config_develop) {
        convert_version_dashes_to_underscores(version_to_write);
    }

    if (zvm_vm_type_is_zscaler_rh_image() && sarge_backup_version_feature_enabled()) {
        if (zpath_upgrade_update_current_sarge_version(version_to_write) != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_NOTICE, "Could not update current sarge version");
        }
        if(is_sarge_unstable() && (install_last_working_sarge_version() == ZPATH_RESULT_NO_ERROR)) {
            zpath_upgrade_cleanup_rollback_cfg();
            ZPATH_LOG(AL_NOTICE, "Successfully reverted sarge to prev working version. Restarting sarge to apply changes");
            sleep(5);
            exit(0);
        }
    } else {
        ZPATH_LOG(AL_NOTICE, "Skipping Backup sarge version");
    }

    if (is_zscaler_os) {
        /* write our pid when running on ZscalerOS *
         * systemd is not supported on FreeBSD hence we need pid to kill the process */
        pid = getpid();
        snprintf(pid_str, sizeof(pid_str), "%ld", (long)pid);
        if (write_pid_file(pid_str, SARGE_PID_FILE) != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, SARGE_NAME ": Cannot write our pid to file.");
        }
    }

    /* Write our version */
    if (write_version_file(version_to_write, ZPATH_SARGE_FILENAME_UPDATER) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Cannot write our version to file.");
    }

    /* Init DNS */
    //sarge_zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
    //                                    1,
    //                                    NULL,
    //                                    "/etc/resolv.conf",
    //                                    "/etc/hosts",
    //                                    log_f,
    //                                    NULL);


    //ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Completed basic initialization");

    /* see zpa_connector.c for details on the below code block
     */
    if (is_zscaler_os) {
        set_process_name("zpa-connector");
    }

    for (int iterations=0; 1; iterations++) {
        int64_t timestamp;
        int64_t now;
        static int once = 0;
        if (once) sleep(1);
        once = 1;

        timestamp = fetch_software();
        if (strstr(config_role, "np-connector") != NULL) {
            check_frr_version();
        }
        run_software(timestamp);

        /* Need to read instance id and org name out of cert.pem for stacktraces and set . */
        if ((instance_id_read == 0 || org_name_read == 0) && stat(FILENAME_CERT, &st) == 0) {
            fetch_inst_id_and_org_name_from_cert(&instance_id_read, &org_name_read);
        }

        /* Every 8 seconds ... */
        if ((iterations % 8) == 0) {
            check_admin_passwd();
            /* check if backup sarge version can be updated*/
            if (((epoch_us() - start_time) > HOUR_TO_US(1)) && wrote_last_working_sarge_version == 0) {
                if (zvm_vm_type_is_zscaler_rh_image() && sarge_backup_version_feature_enabled()) {
                    zpath_upgrade_write_last_working_sarge_version(version_to_write);
                    wrote_last_working_sarge_version = 1;
                }
            }
        }

        if (config_test) {
            now = epoch_us();
            if ((now - start_time) > (6 * 60 * 60 /* 6 hours */)) {
                ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Testing complete");
                while (1) sleep (1);
            }
        }
    }
    return 0;

}

int oauth_get_provision_key(char* oauth_prov_key, size_t key_len) {
    FILE *fp = NULL;
    int len = 0;

    fp = fopen(FILENAME_OAUTH_DETAILS, "r");
    if(!fp) {
        ZPATH_LOG(AL_ERROR, SARGE_V2 ": Could not open OAuth provisioning key file %s", FILENAME_OAUTH_DETAILS);
        return ZPATH_RESULT_ERR;
    }

    len = fread(oauth_prov_key, 1, key_len - 1, fp);
    if (len <= 0) {
        ZPATH_LOG(AL_ERROR, SARGE_V2 ": Could not read OAuth provisioing details from %s", FILENAME_OAUTH_DETAILS);
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }

    fclose(fp);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Check the enrollment status
 * 0 = Fresh Enrolment
 * 1 = Reboot/Restart
 */
int oauth_enrollment_done() {
    return (!access(FILENAME_OAUTH_DETAILS, F_OK));
}

int main(int argc, char *argv[])
{
    int res;
    int i;
    int int_val = 0;
    static char role[256];
    char debug_str[1000];
    char zhw_err_str[1000] = {0};
    struct zpn_enroll_state *enroll_state = NULL;
    struct zhw_id cfg_hw_id;
    struct zcrypt_key cfg_hw_key;
    char cfg_provisioning_key[1024] = {0};
    uint8_t cfg_instance_bytes[INSTANCE_ID_BYTES];
    uint8_t cfg_fingerprint_bytes[INSTANCE_ID_BYTES];
    char cfg_fingerprint_str[(((INSTANCE_ID_BYTES + 2) / 3) * 4) + 1];
    int sarge_autoproxy = 0;
    int sarge_proxy_set = 1;
    int plain_text_provision_key = 0;
    FILE *fp;
    int provision_crypt_file_exist = 0;
    int instance_crypt_file_exist = 0;
    int validate_hw_key = 0;
    int load_hw_info_fail = 0;
    int strict_platform_check = 0;
    char runtime_platform[FOHH_MAX_NAMELEN] = {0};
    struct zpath_cloud_config *cloud_config;
    int result=ZPATH_RESULT_ERR;
    char api_name[MAX_CLOUD_NAME_LEN];
    char cloud_name[MAX_CLOUD_NAME_LEN];
    int64_t customer_gid = 0;

    config_fetch_hostname[0] = '\0';
    config_fetch_ca_file[0] = '\0';
    config_fetch_proxyname[0] = '\0';
    config_cloud_name[0] = '\0';
    config_fetch_path[0] = '\0';
    config_fetch_path_develop[0] = '\0';
    cloud_name[0] = '\0';
    api_name[0] = '\0';

    int provisioning_done = 0;

    /* Extract our executable name, without any path crap */
    char *c = &(argv[0][strlen(argv[0]) - 1]);
    while (c >= argv[0]) {
        if (*c == '/') {
            c++;
            break;
        }
        c--;
    }
    if (c < argv[0]) c = argv[0];

    snprintf(role, sizeof(role), "%s-child", c);
    config_role = role;

    zthread_do_stack_dump(&argc, argv);

    zthread_init("zpa-sarge", ZPATH_VERSION, "unknown", NULL, NULL);

    // Change the ownership of zpa-sarge.stack to 'zscaler' as the child needs to read and upload.
    res = change_ownership(SARGE_STACK_FILE_NAME);
    if (res != ZPATH_RESULT_NO_ERROR) {
        /* Its OK */
        fprintf(stdout, "Error: Unable to change the ownership of file %s", SARGE_STACK_FILE_NAME);
    }

    /* Set default max logging size, will be rewritten if caller specifies */
    zpath_app_set_specific_max_logging_mb(DEFAULT_MAX_LOGGING_MB_SARGE);

    if (zpath_app_logging_parse_args(&argc, argv)) {
        fprintf(stdout, "ERROR: zpath_app_logging_parse_args failed \n");
        fflush(stdout);
        sleep(1);
        exit(1);
    }

    for (i = 1; i < argc; i++) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-develop") == 0) {
            /*
             * -develop does two different things: 1) It adds the develop root cert to the trust store for checking
             * signed images. 2) It searches both /dist and /dist/develop for downloading images.
             * i.e. all images are signed and verified. The production signage is just more tightly controlled.
             */
            config_develop = 1;
        } else if (strcmp(argv[i], "-noproxy") == 0) {
            sarge_proxy_set = 0;
        }else if (strcmp(argv[i], "-test") == 0) {
            config_fetch_backoff = TEST_FETCH_BACKOFF;
            config_min_run_time = TEST_MIN_RUN_TIME;
            config_max_fails_running = TEST_MAX_FAILS_RUNNING;
            config_max_fails_fetch = TEST_MAX_FAILS_FETCH;
            config_def_backoff_sleep = TEST_DEF_BACKOFF_SLEEP;
            config_max_wait_config = TEST_MAX_WAIT_CONFIG;
            config_max_fails_connect = TEST_MAX_FAILS_CONNECT;
            config_test = 1;
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-debug") == 0) {
            sarge_debug = 1;
        } else if (strcmp(argv[i], "-run-child-as-current-user") == 0) {
            run_child_as_current_user= 1;
        } else if (strcmp(argv[i], "-nofips") == 0) {
            is_fips_mode = 0;
        } else if (strcmp(argv[i], "-container") == 0) {
            is_container_env = 1;
        }else if (strcmp(argv[i], "-autoproxy") == 0) {
            sarge_autoproxy = 1;
        } else if (strcmp(argv[i], "-disable_geoip") == 0) {
            disable_geoip = 1;
        } else if (strcmp(argv[i], "-strict_platform_check") == 0) {
            strict_platform_check = 1;
        } else if (strcmp(argv[i], "-oauth_enrollment") == 0) {
            enable_oauth_enrollment = 1;
        } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                /* Exits */
            }
            if (strcmp(argv[i], "-dir") == 0) {
                i++;
                if (chdir(argv[i])) {
                    usage(argv[0], "Invalid directory: %s\n", argv[i]);
                    exit(1);
                }
            } else if (strcmp(argv[i], "-role") == 0) {
                i++;
                config_role = argv[i];
            } else if (strcmp(argv[i], "-proxy") == 0) {
                i++;
                snprintf(config_fetch_proxyname,MAX_DIST_HOSTNAME_LEN, "%s", argv[i]);
            } else if (strcmp(argv[i], "-fproxy") == 0) {
                i++;
                fohh_proxy_hostname = argv[i];
            } else if (strcmp(argv[i], "-fproxy_port") == 0) {
                i++;
                fohh_proxy_port = atoi(argv[i]);
            } else if (strcmp(argv[i], "-repo") == 0) {
                i++;
                snprintf(config_fetch_hostname,MAX_DIST_HOSTNAME_LEN, "%s", argv[i]);
             } else if (strcmp(argv[i], "-repo_ca_file") == 0) {
                i++;
                snprintf(config_fetch_ca_file, MAX_CA_FILE_LEN , "%s", argv[i]);
             } else if (strcmp(argv[i], "-test_plat") == 0) {
                i++;
                config_override_plat = argv[i];
                config_override_no_run = 1;
             } else if (strcmp(argv[i], "-test_arch") == 0) {
                i++;
                config_override_arch = argv[i];
                config_override_no_run = 1;
            } else if (strcmp(argv[i], "-maxlogmb") == 0) {
                i++;
                zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
            } else if (strcmp(argv[i], "-maxlogmb_child") == 0) {
                i++;
                max_logging_mb_child = argv[i];
            } else if (strcmp(argv[i], "-memory_arena_count") == 0) {
                i++;
                memory_arena_count = argv[i];
            } else if (strcmp(argv[i], ARG_CHILD_EVENT_LOG_LVL_CONSOLE) == 0) {
                int_val = atoi(argv[i+1]);
                if (int_val > 8 || int_val < 0) {
                    usage(argv[0], ARG_CHILD_EVENT_LOG_LVL_CONSOLE " invalid argument. Allowed: [0-8]");
                    /* Exits */
                }
                child_event_log_lvl_console = int_val;
                i++;
            } else if (strcmp(argv[i], ARG_CHILD_EVENT_LOG_LVL_SYSLOG) == 0) {
                int_val = atoi(argv[i+1]);
                if (int_val > 8 || int_val < 0) {
                    usage(argv[0], ARG_CHILD_EVENT_LOG_LVL_SYSLOG " invalid argument. Allowed: [0-8]");
                    /* Exits */
                }
                child_event_log_lvl_syslog = int_val;
                i++;
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                /* Exits */
            }
        }
    }

    if (sarge_autoproxy) {
        fprintf(stdout, "autoproxy is enabled by default \n");
    }
    if (!sarge_proxy_set) {
        fprintf(stdout, "noproxy is not supported \n");
    }

    if (!fohh_proxy_hostname) {
        FILE *fp;
        size_t len;
        fp = fopen("proxy", "r");
        if (fp) {
            if (fgets(proxy_file_data, sizeof(proxy_file_data), fp)) {
                len = strlen(proxy_file_data);
                /* Remove newline... */
                while (len) {
                    len--;
                    if ((proxy_file_data[len] != '\n') &&
                        (proxy_file_data[len] != '\r')) {
                        break;
                    }
                    proxy_file_data[len] = 0;
                }
                if (len) {
                    size_t len2 = len;
                    while (len2) {
                        len2 --;
                        if (proxy_file_data[len2] == ':') {
                            fohh_proxy_port = strtoul(&(proxy_file_data[len2 + 1]), NULL, 10);
                            proxy_file_data[len2] = 0;
                            break;
                        }
                    }
                    fohh_proxy_hostname = proxy_file_data;
                }
            }
            fclose(fp);
        }
    }

    if (sarge_debug) {
        zpath_debug |=
                    (ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
                    0;
    }

    if (is_container_env) {
        zpath_app_enable_console_log();
    }

    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = c;
    app_params.fohh_thread_count = 1;
    app_params.debuglog = 1;
    app_params.personality = ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT;
    app_params.load_zpa_cloud_config = 0;

    res = zpath_simple_app_init(&app_params);
    if (res) {
        fprintf(stderr, "Cannot simple_app_init\n");
        usleep(100000);
        return -1;
    }

    if ( config_fetch_ca_file[0] != '\0' ) {
        fohh_add_ext_trusted_certs_from_file(config_fetch_ca_file);
    }

    /*Check if we are in openshift env*/
    char *openshift_str = getenv(OPENSHIFT_ENV);

    if (openshift_str) {
        if (!strncmp(openshift_str, "enabled", sizeof("enabled"))) {
            is_openshift_env = 1;
        }
        ZPN_LOG(AL_NOTICE, "Openshift Environment is set to %s", openshift_str);
    }

    /*Check if we are in kubernetes env*/
    char *kubernetes_str = getenv(KUBERNETES_ENV);
    if (kubernetes_str) {
        if (!strncmp(kubernetes_str, "enabled", sizeof("enabled"))) {
            is_kubernetes_env = 1;
        }
        ZPN_LOG(AL_NOTICE, "Kubernetes Environment is set to %s", kubernetes_str);
    }

    fp = fopen(FILENAME_PROVISION_KEY, "r");
    if (fp) {
        plain_text_provision_key = 1;
        fclose(fp);
    }
    if(getenv(PROVISIONING_KEY_ENV)) {
        plain_text_provision_key = 1;
    }

    fp = fopen(FILENAME_PROVISION_CRYPT, "r");
    if (fp) {
        provision_crypt_file_exist = 1;
        fclose(fp);
    }

    fp = fopen(FILENAME_ID, "r");
    if (fp) {
        instance_crypt_file_exist = 1;
        fclose(fp);
    }

    enum zhw_os_type os_type = zhw_os_get();
    if (os_type == zhw_os_type_linux) {
        res = zhw_get_runtime_platform(runtime_platform, sizeof(runtime_platform), NULL);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Error %s while opening /etc/os-release", strerror(res));
        } else {
            if (strcmp(zhw_platform(), runtime_platform)) {
                ZPATH_LOG(AL_ERROR, "Platform incompatible: RPM installed is for %s, but expected %s. Please install right RPM", zhw_platform(), runtime_platform);
                if (strict_platform_check) {
                    ZPATH_LOG(AL_ERROR, "Strict platform check enforced, exiting...");
                    return -1;
                }
            }
        }
    }

    // Enable OAuth enrollment based on the argument passed from rpm spec file
    sarge_v2 =  (!(provision_crypt_file_exist || plain_text_provision_key) && enable_oauth_enrollment) ? 1 : 0;

    /* - If both plain and encrypted provision key file are not present
     * - If Oauth enrolment is not enabled
     * then do not proceed further
     */
    if ( !provision_crypt_file_exist && !plain_text_provision_key && !sarge_v2) {
        ZPATH_LOG(AL_ERROR,
                        "Cannot get provisioning key, please check if provision_key or provision_key.crypt exists in the current working "
                        "directory");
        /*
         * Keeping the sleep as-it-is for the time being.
         */
        usleep(100000);

        return -1;
    }

    /* Init enrollment lib */
    if (zpn_enroll_init(&enroll_state) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Unable to init enrollment api");
        usleep(100000);
        return -1;
    }
    enroll_state->enroll_type = ZPN_ENROLLMENT_TYPE_SARGE;

    enroll_state->zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0), 1, NULL, "/etc/resolv.conf",
                                                "/etc/hosts", log_f, NULL);
    if (!enroll_state->zcdns) {
        ZPN_LOG(AL_NOTICE, "Cannot start dns resolver");
        usleep(100000);
        return -1;
    }

    res = zvm_init(zpath_event_collection, enroll_state->zcdns);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Cannot init zvm");
        return -1;
    }

    // Check if we have already received certs for child
    provisioning_done = zpn_enroll_is_enrollment_completed();

    memset(debug_str, 0, sizeof(debug_str));
    load_hw_info_fail = load_prev_zhw_id_sha_info();
    if (load_hw_info_fail) {
        /* This is not a critical error for us to take an aggressive action */
        ZPATH_LOG(AL_DEBUG,
                "Error while loading hw_id_info from file %s, it is possible that the file is not present",
                INSTANCE_ID_BIN_FILE);
    }

    /* Get Hardware ID */
    res = zhw_id_get(&cfg_hw_id, debug_str, sizeof(debug_str), zhw_err_str, sizeof(zhw_err_str), 0);
    if (strlen(zhw_err_str)) {
        ZPATH_LOG(AL_NOTICE, "Error occurred when getting hardware id: %s", zhw_err_str);
    }
    if (res) {
        ZPATH_LOG(AL_NOTICE, "Cannot get hardware id");
        usleep(100000);
        return -1;
    }

    if (zcrypt_gen_key(&cfg_hw_key, cfg_hw_id.id, sizeof(cfg_hw_id.id)) != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Cannot get key");
        usleep(100000);
        return -1;
    }

    if (!load_hw_info_fail) {
        memset(debug_str, 0, sizeof(debug_str));
        res = validate_zhw_id_sha_info(&validate_hw_key, debug_str, sizeof(debug_str));
        if (!res) {
            if (!validate_hw_key && strnlen(debug_str, sizeof(debug_str))) {
                ZPATH_LOG(AL_ERROR, "Validation for HW ID failed due to change in %s", debug_str);
            }
        } else {
            ZPATH_LOG(AL_ERROR, "Cannot validate current zhw id info with the previous info");
        }
    }

    /*
     * Try generating a fingerprint
     * HW id based Fingerprint needs instance_id information, So only create it
     * when instance_id file is available.
     */
    if (instance_crypt_file_exist) {

        res = zpn_enroll_get_instance_id(&cfg_hw_key, cfg_instance_bytes);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Cannot get instance id");
            usleep(100000);
            return -1;
        }

        /* This fingerprint is used for the enrollment API calls */
        for (i = 0; i < INSTANCE_ID_BYTES; i++) {
            cfg_fingerprint_bytes[i] = cfg_instance_bytes[i] ^ cfg_hw_id.id[i];
        }
        base64_encode_binary(cfg_fingerprint_str, cfg_fingerprint_bytes, sizeof(cfg_fingerprint_bytes));

    } else {
        ZPATH_LOG(AL_DEBUG, "Error while loading file %s, it is possible that the file is not present",
                FILENAME_ID);
        /* Dummy fingerprint - We should not come here except the fresh enrollment case
         * Used for OAuth Service & downloading cloud config from enrollment server.
         * OAuth service want same fingerprint for devicecode & Token call.
         * While downloading cloud config, this fingerprint is ignored by V4 enrollment APIs
         * as JWT token is present for enrollment APIs.
         */
        if (zcrypt_rand_bytes(cfg_fingerprint_bytes, INSTANCE_ID_BYTES) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot generate crypto random bytes\n");
        }
        base64_encode_binary(cfg_fingerprint_str, cfg_fingerprint_bytes, sizeof(cfg_fingerprint_bytes));
    }

    if (provision_crypt_file_exist) {
        res = zpn_enroll_get_provisioning_key(&cfg_hw_key, cfg_provisioning_key, sizeof(cfg_provisioning_key));
        if (res) {
            ZPATH_LOG(AL_NOTICE,
                        "Cannot parse provisioning key, please check if provision_key exists in the current working "
                        "directory");
            usleep(100000);
            return -1;
        }
    } else if (plain_text_provision_key) {
        res = zpn_get_provision_key_from_plain_text_for_sarge(cfg_provisioning_key, sizeof(cfg_provisioning_key));
        if (res) {
            ZPATH_LOG(AL_NOTICE,
                        "Cannot get provisioning key, please check if provision_key exists in the current working "
                        "directory, error %s ",zpn_result_string(res));
            usleep(100000);
            return -1;
        }
    }

    /* Init DNS */
    sarge_zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                        1,
                                        NULL,
                                        "/etc/resolv.conf",
                                        "/etc/hosts",
                                        log_f,
                                        NULL);

    /*
     * OAuth Service based enrollment
     *
     * Get User token
     * Wait for the Token to be consumed via admin UI
     * Get Provision Key Details
     * Dump OAuth Provision Key to the disk
     * Dump JWT token to the disk
     * Resume the existing flow - Like nothings is changed.
     * Precheck - If any of these flags are set - plain_text_provision_key & provision_crypt_file_exist, DONT DO OAUTH based enrollment.
     */
    if (sarge_v2 && !oauth_enrollment_done()) {
        char *oauth_server_hostname = OAUTH_SERVER_URL;
        char oauth_challenge[OAUTH_SHA256_HASH_SIZE+1] = {0}; // 256bits hash
        char oauth_challenge_hash[2 * OAUTH_SHA256_HASH_SIZE + 1] = {0}; // hex bytes
        int oret_code = 0;
        int64_t code_expiry = 0;
        int64_t interval = OAUTH_DEFAULT_RETRY_INTERVAL; /* Default OAuth service retries interval in seconds*/
        int device_auth_retries = 0;
        int device_token_retries = 0;

        /*
         * Assuming this path is always set,
         * If not point to generic one - prod.oauth.*
         * For Fed cloud, we can force exporting this server's URL
         */
        char* env_oauth_server = getenv(OAUTH_SERVER);
        if (env_oauth_server != NULL) {
            oauth_server_hostname = env_oauth_server;
        }

        if (oauth_get_challenge(oauth_challenge, OAUTH_SHA256_HASH_SIZE) != 0) {
            ZPATH_LOG(AL_ERROR, "OAuth - Unable to generate OAuth challenge");
            return ZPATH_RESULT_ERR;
        }

        if (oauth_get_challenge_hash(oauth_challenge, OAUTH_SHA256_HASH_SIZE, oauth_challenge_hash,
                                      sizeof(oauth_challenge_hash) - 1) != 0) {
            ZPATH_LOG(AL_ERROR, "OAuth - Unable to get OAuth challenge hash");
            return ZPATH_RESULT_ERR;
        }

        user_code_monitor_init();

        ZPATH_LOG(AL_NOTICE, "[OAuth] Started provisioning this device using OAuth2.0 method");

        while (device_auth_retries < OAUTH_MAX_DEVICE_AUTH_REQUESTS) {
            device_auth_retries++;
            res = oauth_get_device_auth(oauth_server_hostname, OAUTH_DEVICEAUTH_API, cfg_fingerprint_str,
                                         oauth_challenge_hash, &code_expiry, &interval, &oret_code);
            if (res == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_ERROR, "[OAuth] Received device auth details from OAuth server [%s]."
                                     "Took [%d] attempt/s, code [%d]", oauth_server_hostname, device_auth_retries,
                                      oret_code);
                break;
            }

            ZPATH_LOG(AL_ERROR, "[OAuth] Failed to get successful device auth response from OAuth server. "
                                "Received response code [%d] for API [%s] from server [%s]. "
                                "Retrying - retry count [%d]", oret_code, OAUTH_DEVICEAUTH_API,
                                 oauth_server_hostname, device_auth_retries);

            sleep(OAUTH_DEFAULT_RETRY_INTERVAL); //interval is not reliable in error cases
        }

        if (device_auth_retries >= OAUTH_MAX_DEVICE_AUTH_REQUESTS) {
            ZPATH_LOG(AL_ERROR, "Tried provisioning this machine using OAuth server [%d] time, Response code for the most recent failure - [%d]. "
                                "OAuth is the preferred way of enrollment. Make sure OAuth server [%s] is reachable from this machine and try again. "
                                "Alternatively, fallback to the older way of adding provision key to this machine manually and restart the service!\n",
                                 device_auth_retries, oret_code, oauth_server_hostname);
            return ZPATH_RESULT_ERR;
        }

        device_auth_retries = 0;

        /*
         * Wait for provision key.
         */
        while (device_token_retries < OAUTH_MAX_DEVICE_TOKEN_REQUESTS) {
            res = oauth_get_device_token(oauth_server_hostname, OAUTH_DEVICETOKEN_API, cfg_fingerprint_str,
                                          oauth_challenge, &oret_code);
            if (res == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_ERROR, "[OAuth] Successfully received provisioning details form OAuth server. Retries [%d]",
                                 device_token_retries+1);
                break;
            }

            sleep(interval);

            if (oret_code == OAUTH_RESPONSE_PENDING) { // Auth Pending
                if (epoch_s() > code_expiry) { // User Code is expired, generate a new one
                    device_token_retries++;

                    ZPATH_LOG(AL_NOTICE, "[OAuth] User Code already expired. Generating new User Code for device auth. "
                                         "Retrying request [%s], Retry count [%d]", OAUTH_DEVICEAUTH_API, device_token_retries);

                    res = oauth_get_device_auth(oauth_server_hostname, OAUTH_DEVICEAUTH_API, cfg_fingerprint_str,
                                                oauth_challenge_hash, &code_expiry, &interval, &oret_code);
                    if (res != ZPATH_RESULT_NO_ERROR) {
                        ZPATH_LOG(AL_ERROR, "[OAuth] Request to get a new User Code [%s] from OAuth server failed with response code [%d] "
                                            "Make sure OAuth Server [%s] is reachable from this machine and try again. "
                                            "Alternatively, add provision key to this machine manually and restart the service!",
                                            OAUTH_DEVICEAUTH_API, oret_code, oauth_server_hostname);
                        return ZPATH_RESULT_ERR;
                    }
                } else { // This case should match normally
                    ZPATH_LOG(AL_NOTICE, "[OAuth] Provision key details are not available yet. Re-trying request [%s]",
                                          OAUTH_DEVICETOKEN_API);
                }
            } else if (oret_code == OAUTH_RESPONSE_TOKEN_EXPIRED) { // Auth Token Expired
                device_token_retries++;

                ZPATH_LOG(AL_NOTICE, "[OAuth] User Code is already expired. Re-Trying to get a new user code..");
                res = oauth_get_device_auth(oauth_server_hostname, OAUTH_DEVICEAUTH_API, cfg_fingerprint_str,
                                            oauth_challenge_hash, &code_expiry, &interval, &oret_code);
                if (res != ZPATH_RESULT_NO_ERROR) {
                    ZPATH_LOG(AL_ERROR, "[OAuth] Request to get a new User Code [%s] from OAuth server failed. "
                                        "Make sure OAuth Server [%s] is reachable from this machine and try again. "
                                        "Alternatively, add provision key to this machine manually and restart the service!",
                                         OAUTH_DEVICEAUTH_API, oauth_server_hostname);
                    return ZPATH_RESULT_ERR;
                }
            } else { //Received unknown response code from OAuth server
                device_token_retries++;

                ZPATH_LOG(AL_ERROR, "[OAuth] Failed to get provision key from OAuth server [%s],"
                                    "Request [%s] failed with response code [%d]", oauth_server_hostname,
                                     OAUTH_DEVICEAUTH_API, oret_code);
            }
        }

        if (device_token_retries >= OAUTH_MAX_DEVICE_TOKEN_REQUESTS) {
            ZPATH_LOG(AL_ERROR, "Tried provisioning this machine using OAuth server [%d] time, Response code for the most recent failure - [%d]. "
                                "OAuth is the preferred way of enrollment. Make sure OAuth server [%s] is reachable from this machine and try again. "
                                "Alternatively, fallback to the older way of adding provision key to this machine manually and restart the service!\n",
                                 device_auth_retries, oret_code, oauth_server_hostname);
            return ZPATH_RESULT_ERR;
        }
    }

    zpath_init_cloud_config();

    if (sarge_v2) {
        result = oauth_get_provision_key(cfg_provisioning_key, sizeof(cfg_provisioning_key));
        if (result == ZPATH_RESULT_NO_ERROR) {
            result = zpath_get_oauth_cloud_details(cfg_provisioning_key, api_name, cloud_name, &customer_gid);
        }
    } else {
        result = zpath_get_provisioning_key_details(cfg_provisioning_key, api_name, cloud_name);
    }

    if ( result != ZPATH_RESULT_NO_ERROR ) {
        ZPATH_LOG(AL_NOTICE,
                        "Failed parsing provisioning details");
        return -1;
    }

    // If the cloud_config data was already downloaded from enrollment server in previous execution.
    // We use the stored (encrypted) cloud_config from disk.
    // Only if there is NO existing cloud_config file, then we attempt to read the cloud_config data
    // from the enrollment api.
    // To read the cloud_config from enrollment api, we need a dummy encryption key to given to the admin api.
    // So we add some dummy fingerprint for the admin api.
    // This fingerprint and key is NOT used for any encryption/decryption.

    // The AppC/PSE child apps use the actual instance_id based fingerprint while fetching data from enrollment api
    // And AppC/PSE encrypts the cloud_config based on that fingerprint into disk.

    struct evbuffer *evbuffer = NULL;
    if ((zpath_check_if_local_cloud_config_file_exists() == ZPN_RESULT_NOT_FOUND) &&
        (cloud_name[0] != '\0') ) {

        struct zcrypt_rsa_key *cfg_rsa_key = zcrypt_rsa_key_create();
        if (!cfg_rsa_key) {
            ZPATH_LOG(AL_ERROR, "Cannot create rsa keyholder\n");
            return -1;
        }

        if (provisioning_done) {
            if (zpn_enroll_get_private_key(&cfg_hw_key, cfg_rsa_key, FILENAME_KEY) != ZPATH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Could not get rsa key from an already provisioned machine, Fresh enrollment required!\n");
                return -1;
            }
        } else {
            if (zcrypt_rsa_key_gen_keys(cfg_rsa_key) != ZCRYPT_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_ERROR, "Could not generate zcrypt rsa key");
                return -1;
            }
        }

        if (result == ZPATH_RESULT_NO_ERROR) {
            evbuffer = zpn_enroll_get_enrollment_details_raw(cloud_name,
                                                            cfg_rsa_key,
                                                            cfg_fingerprint_str,
                                                            cfg_provisioning_key,
                                                            api_name,
                                                            sizeof(api_name),
                                                            get_enrollment_type_by_config_role(),
                                                            sarge_v2 ? ZPN_ENROLLMENT_STYLE_V4 : ZPN_ENROLLMENT_STYLE_V2,
                                                            sarge_v2 ? jwt_token : NULL,
                                                            sarge_v2 ? customer_gid : 0);
        }
    }

    // Here provision_crypt_file_exist -> false, if Sarge starts up for first time
    // provision_crypt_file_exist -> true , if Sarge is restarted with a valid provision_crypt
    // This zpath_load_cloud_config_for_customer_apps -> Does not encrypt the file,
    // it only decrypts the existing cloud config file using the provided cfg_hw_key (if valid)
    // If cfg_hw_key is NULL, then cloud_config from evbuffer(if NULL) or default cloud_config is returned.
    if (provision_crypt_file_exist) {
        result = zpath_load_cloud_config_for_customer_apps(&cfg_hw_key, api_name, evbuffer);
    } else {
        result = zpath_load_cloud_config_for_customer_apps(NULL, api_name, evbuffer);
    }

    if (evbuffer) evbuffer_free(evbuffer);

    if(result != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Cannot get zpa_cloud_config for the given cloud error - %s",zpn_result_string(result));
        return -1;
    }

    cloud_config = sarge_get_zpa_cloud_config(argc, argv, api_name, cloud_name);
    if(!cloud_config) {
        ZPATH_LOG(AL_NOTICE, "Cannot get zpa_cloud_config for the given cloud ");
        return -1;
    }

    require_fips = cloud_config->sarge->require_fips;
    if ( config_fetch_hostname[0] == '\0') {
        snprintf(config_fetch_hostname, MAX_DIST_HOSTNAME_LEN, "%s", cloud_config->sarge->dist_hostname);
    }
    if ( config_fetch_proxyname[0] == '\0') {
        snprintf(config_fetch_proxyname,MAX_DIST_HOSTNAME_LEN, "%s",cloud_config->sarge->dist_proxyname);
    }
    snprintf(config_fetch_path, MAX_CA_FILE_LEN, "%s",cloud_config->sarge->dist_path);
    snprintf(config_fetch_path_develop, MAX_DIST_FETCH_PATH_DEVELOP, "%s",cloud_config->sarge->dist_path_develop);
    if (!config_develop) {
        config_develop = cloud_config->sarge->develop;
    }
    snprintf(config_cloud_name,MAX_CLOUD_NAME_LEN, "%s",cloud_config->cloud_name);

    if (config_fetch_proxyname[0] == '\0') {
        snprintf(config_fetch_proxyname, MAX_DIST_HOSTNAME_LEN , "%s" , config_fetch_hostname);
    }

    ZPATH_LOG(AL_NOTICE, SARGE_NAME ":Cloud name %s, Cloud host %s, cloud proxy %s, path %s, develop %s", config_cloud_name, config_fetch_hostname, config_fetch_proxyname,
              config_fetch_path, config_fetch_path_develop);

    if (require_fips) {
        if (FIPS_mode() == 0) {
            fprintf(stderr, "FIPS mode not enabled, but federal cloud\n");
            usleep(100000);
            return -1;
        }
    }

    if (fohh_proxy_hostname) {
        ZPATH_LOG(AL_NOTICE, SARGE_NAME ": Using %s:%d as forward proxy for all internet-bound traffic", fohh_proxy_hostname, fohh_proxy_port);
    }

    /*
     * we use this environment variable to detect ZscalerOS and apply corresponding changes:
     * 1. disable setcap (not supported)
     * 2. run child as current user (since we don't have setcap)
     */
    if (getenv(ZSCALER_OS_ENV)) {
        is_zscaler_os = 1;
        run_child_as_current_user = 1;
        ZPATH_LOG(AL_NOTICE, "ZscalerOS detected");
    }

    res = sub_main(argc, argv);
    usleep(100000);
    return res;
}


static struct zpath_cloud_config * sarge_get_zpa_cloud_config(int argc, char *argv[],const char *api_name, const char *cloud_name) {
    struct zpath_cloud_config *cloud_config=NULL;
    // Fetch cloud config by cloud name
    if (cloud_name){
        cloud_config=zpath_get_cloud_config_from_name(cloud_name);
        if(!cloud_config){
            ZPATH_LOG(AL_WARNING, "Fetching Cloud Config by Cloud Name - %s - Failed ", cloud_name);
        }else {
            ZPATH_LOG(AL_NOTICE, "Fetching Cloud Config by Cloud Name - %s - Success ", cloud_name);
        }
    }
    // Fetch cloud config by api name
    if (!cloud_config && api_name) {
        cloud_config=zpath_get_cloud_config_from_apiname(api_name);
        if(!cloud_config){
            ZPATH_LOG(AL_WARNING, "Fetching Cloud Config by API Name - %s - Failed ", api_name);
        }else {
            ZPATH_LOG(AL_NOTICE, "Fetching Cloud Config by API Name - %s - Success ", api_name);
        }
    }
    // Fetch cloud config by argv parameter
    if(!cloud_config) {
        int i=0;
        while ( (!cloud_config) && (argc>0) && (i<argc)) {
            cloud_config=zpath_get_cloud_config_by_filename_argv(&(argv[i][1]));
            if(cloud_config) {
                ZPATH_LOG(AL_NOTICE, "Fetching Cloud Config by Command Line params - Success");
                break;
            }
            i++;
        }
        ZPATH_LOG(AL_WARNING, "Fetching Cloud Config by Command Line params - Failed");
    }
    // Fetch cloud config based on a folder in file system
    if(!cloud_config){
        cloud_config=zpath_get_cloud_config_by_filesystem();
        if(!cloud_config){
            ZPATH_LOG(AL_WARNING, "Fetching Cloud Config by File system - Failed ");
        }else {
            ZPATH_LOG(AL_NOTICE, "Fetching Cloud Config by File system - Success ");
        }
    }
    return cloud_config;
}

int get_enrollment_type_by_config_role() {
    if (strcmp(config_role, "zpa-connector-child") == 0) {
        return ZPN_ENROLLMENT_TYPE_ASSISTANT;
    } else if (strcmp(config_role, "zpa-service-edge-child") == 0) {
        return ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER;
    } else if (strcmp(config_role, "zpa-pcc-child") == 0) {
        return ZPN_ENROLLMENT_TYPE_SITEC;
    } else {
        return ZPN_ENROLLMENT_TYPE_SARGE;
    }
}

int install_frr(int force_reinstall, int force_default)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char frr_version_str[256];
    char sudo_path[PATH_MAX];
    char install_manager_path[PATH_MAX];
    char out_buf[10000];
    char *install_cmd = force_reinstall? FRR_REINSTALL: FRR_INSTALL;
    size_t len;

    if (is_container_env) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME "FRR is not supported on container, skipping FRR installation");
        frr_install_require = 0;
        return res;
    }

    len = sizeof(out_buf);

    if (config_frr_version_context.version && !force_default) {
        /*
         * for specific frr version installation, its required we configure the whole version string including revision tag.
         * ie: 10.2-1 or 10.2.1-1, or 10.2.1-2, or 10.2.1-3
         *
         * Note its unlikely we will need to release 10.2.1-3 (revision 3 of real version 10.2.1),
         * but we will keep the option opened in case we have to do new release for frr (spec file change roll out)
         *
         * Since we are including the revision tag in yum install command, we then have to mention the platform.force_reinstall
         *
         * ie,
         * yum command that works:
         * yum install frr-0.0
         * yum install frr-10.2.1
         * yum install frr-10.2.1-1.el9
         *
         * yum command that does not work:
         * yum install frr-10.2.1-1  --> we have to include the platform details to make this installation work
         */
        const char *revision_tag = strchr(config_frr_version_context.version, '-');
        if (revision_tag) {
        // If it contains a revision tag, yum install frr-10.2-1.el9
            snprintf(frr_version_str, sizeof(frr_version_str), "frr-%s.%s", config_frr_version_context.version, zhw_platform());
        } else {
            // If no revision tag, yum install frr-10.2 (ignore revision)
            snprintf(frr_version_str, sizeof(frr_version_str), "frr-%s", config_frr_version_context.version);
        }

    } else {
        snprintf(frr_version_str, sizeof(frr_version_str), "frr-%s", FRR_DEFAULT_GOLDEN_VERSIION);
    }

    res = get_sudo_path(sudo_path);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": install_frr() Error %s in getting path during installation of %s. Aborting upgrade process", zpath_result_string(res), frr_version_str);
        return res;
    }

    res = get_package_manager_path(install_manager_path);
    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Error in getting package manager during installation of %s. Aborting upgrade process", frr_version_str);
        return res;
    }
    ZPATH_LOG(AL_INFO, SARGE_NAME ": Attempting to install %s\n", frr_version_str);

    res = zvm_run_cmd(out_buf, &len, sudo_path, MAX_SARGE_UPGRADE_TIMEOUT_MSEC,  install_manager_path, install_cmd, "-y", frr_version_str, NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Installing rpm for %s failed err %s.\n %s", frr_version_str, zpath_result_string(res), out_buf);
        return res;
    }

    ZPATH_LOG(AL_INFO, SARGE_NAME "frr_install_require %d: Installing rpm %s,read successful - %s", frr_install_require, frr_version_str, out_buf);
    if (frr_install_require) {
        frr_install_require = 0;
    }

    return res;

}


void check_frr_version()
{
    int64_t change;
    static int64_t last_change = 0;
    static char prev_version_read_str[256] = {0};

    change = read_version(&(config_frr_version_context), FILENAME_FRR_VERSION);
    if (sarge_debug) {
        ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Zscaler software version change debug %s file change: %ld, last_change: %ld",
                 FILENAME_FRR_VERSION, (long)change, (long)last_change);
    }

    /*
     * frr_version file is present (written by child)
     * and the timestamp of the file has not chanegd since the last time modification which we already read
     */
    if (change && (change == last_change)) {
        if (sarge_debug) ZPATH_LOG(AL_DEBUG, SARGE_NAME ": Zscaler software version %s has not changed", FILENAME_FRR_VERSION);
        return;
    }

    last_change = change;

    if (NULL == config_frr_version_context.version) {
        if (prev_version_read_str[0] == '\0') {
            return;
        } else {
            /*
             * frr_version was present and now is gone
             * install default
             */
            frr_install_require = 1;
            ZPATH_LOG(AL_INFO, SARGE_NAME ": Zscaler frr routing software version %s changed from %s to default", FILENAME_FRR_VERSION, prev_version_read_str);
            prev_version_read_str[0] = '\0';
            return;
        }
    } else {
        if (!strcmp(prev_version_read_str, config_frr_version_context.version)) {
            /* version same */
        } else {
            frr_install_require = 1;
            ZPATH_LOG(AL_INFO, SARGE_NAME ": Zscaler frr routing software version %s changed from %s to %s", FILENAME_FRR_VERSION, prev_version_read_str[0]== '\0'?"no prev":prev_version_read_str, config_frr_version_context.version_str);
            snprintf(prev_version_read_str, sizeof(prev_version_read_str), "%s", config_frr_version_context.version);
            return;
        }
    }

}

/*
 * frr_version is NULL, meaning sarge has to download default, frr-0.0
 * if currently a frr-0.0 is already downloaded, sarge to use yum reinstall command.
 * all else case, ie no frr installed yet or frr-xxx (specific version) is installed, use yum install command.asm
 *
 * this is because yum reinstall requires the exact version being installed.
 *
 * return
 *  1 - FRR is installed, reinstall required
 *  0 - FRR is not installed, fresh install
 */
static int check_if_reinstall_cmd_required_for_default_frr()
{
    char package_name[] = FRR_PACKAGE_NAME;
    char command[256];
    char result[1024];

    snprintf(command, sizeof(command), "rpm -q %s", package_name);

    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed running command %s, errno=%d (%s)", command, errno, strerror(errno));
        return 0;
    }

    if (fgets(result, sizeof(result), fp) == NULL) {
        ZPATH_LOG(AL_ERROR, SARGE_NAME ": Failed to read input using fgets for command %s, errno=%d (%s)", command, errno, strerror(errno));
        pclose(fp);
        return 0;
    }

    pclose(fp);

    result[strcspn(result, "\n")] = '\0';

    if (strncmp(result, package_name, strlen(package_name)) != 0) {
        ZPATH_LOG(AL_DEBUG, SARGE_NAME ": FRR not installed, fresh installation required");
        return 0;
    }

    ZPATH_LOG(AL_DEBUG, SARGE_NAME ": FRR package detected: %s", result);

    // Extract the version part (e.g., "0.0" from "frr-0.0-28334a1897.el9.x86_64")
    char *version_start = strchr(result, '-'); // Find the first '-'
    if (version_start != NULL) {
        version_start++;
        char *version_end = strchr(version_start, '-');
        if (version_end != NULL) {
            *version_end = '\0';

            if (strcmp(version_start, "0.0") == 0) {
                ZPATH_LOG(AL_DEBUG, SARGE_NAME ": FRR version is 0.0, reinstall required");
                return 1;
            } else {
                ZPATH_LOG(AL_DEBUG, SARGE_NAME ": FRR version is %s, fresh installation required", version_start);
                return 0;
            }
        }
    }

    ZPATH_LOG(AL_ERROR, SARGE_NAME ": Unable to parse FRR version from %s, fresh install required", result);
    return 0;
}


void check_frr_installation()
{
    int res = ZPATH_RESULT_NO_ERROR;
    int fail = 0;
    int reinstall = 0;
    int force_golden_image = 0;

    if (NULL == config_frr_version_context.version) {
        if (!frr_install_require) {
            /*
             * In phase 1, child will not write expected_version to local_db since db_schema support is not there,
             * thus sarge will always use frr-0.0.0.rpm , the default rpm, if frr-0.0.0 gets upgraded,
             * there is no way sarge knows that its a different frr-0.0.0,
             * thus we going to always install default to before we start child, in case the default gets changed.
             */
            ZPATH_LOG(AL_INFO, SARGE_NAME "Use default frr-0.0 version, install/re-install default frr-0.0");
            frr_install_require = 1;
            reinstall = check_if_reinstall_cmd_required_for_default_frr();
        }
    }

    while (frr_install_require) {
        res = install_frr(reinstall, force_golden_image);
        if (res) {
            fail++;

            if (fail == config_max_fails_running && config_frr_version_context.version != NULL) {
                /*
                 * yum install kept failing for config_max_fails_running times
                 * fall back to golden image which is frr-0.0
                 */
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": install_frr failed %d times, trying golden image frr-0.0", fail);
                force_golden_image = 1;
            }

            if (fail > config_max_fails_running) {
                ZPATH_LOG(AL_ERROR, SARGE_NAME ": install_frr failed too many times, giving up on retries, fail total %d, more than limit %d", fail, config_max_fails_running);
                break;
            }

            ZPATH_LOG(AL_INFO, SARGE_NAME ": install_frr failed %d times %d, sleeping for 1 second and will retry", fail, res);
            sleep(1);

        }
    }

    return;
}
