
#include <gtest/gtest.h>
#include <gmock/gmock.h>


#include <arpa/inet.h>
extern "C" {
#define ZPATH_MALLOC_DEBUG 1
#define ZPATH_MALLOC_OVERWRITE_FULL 1
#define ZPATH_MALLOC_OVERWRITE_WORDS 128
#define ZPATH_MALLOC_DEBUG_PER_LINE_STATS 1
#define ZPATH_MALLOC_DF_DEBUG 1
#define ZPATH_MALLOC_MAGIC 1
#define ZPATH_MALLOC_OVERWRITE_FULL 1
#define ZPATH_MALLOC_OVERWRITE 1
#include "admin_probe/admin_probe_uploader.h"
#include "fohh/fohh.h"
#include "zcdns/zcdns_libevent.h"
}
using ::testing::Test;
using ::testing::Return;
using ::testing::_;

class AdminProbeUploader : public testing::Test {
public:

    void SetUp() override {
    }

    void TearDown() override {
    }

};

TEST_F(Admin<PERSON>robeUploader, TestProbeSimpleUploader) {
    int expected_result = 0;
    int actual_result = 0;
    ASSERT_EQ(expected_result, actual_result);
}

TEST_F(AdminProbeUploader, TestUpload) {
#if 0
    int ret = admin_probe_task_module_create_new_task_from_pending_event("tcurl9",
                                                                217246660303025115,
                                                                21724666030299545,
                                                                "TCPDUMP",
                                                                ADMIN_PROBE_STATUS_PENDING,
                                                                "151.101.193.187",
                                                                NULL,
                                                                30,
                                                                "commandprobes.s3-us-west-2.amazonaws.com",
                                                                "command-output",
                                                                1641327296,
                                                                1, //is UT
                                                                2,
                                                                0); //is UT

    ASSERT_EQ(ret, 0);
    #endif
}

int callback_called;
void admin_probe_dummy_output_cb(struct uploader_output uploader_output, void *cookie)
{
    (void)uploader_output;
    (void)cookie;
    std::cout << "callback called " << uploader_output.status;
    callback_called = 1;
}

int dummy_get_remote_host_for_s3_upload(char *remote_host, int size, int *no_proxy)
{
    if (remote_host) {
        snprintf(remote_host, size, "co2br.dev.zpath.net");
    }
    if (no_proxy) {
        *no_proxy = 0;
    }
    return 0;

}

TEST_F( AdminProbeUploader, ASyncProbeUploader) {

    struct admin_probe_uploader_file_list_head head;
    struct admin_probe_uploader_file_list *cur_list;
    struct event_base *base = NULL;

    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = "zpa-connector-test";
    app_params.role_version_string = "1.0";
    app_params.log_filename = "logfile";
    app_params.debug_port = 9000;
    app_params.debuglog = 1;
    app_params.personality = ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT;

    int result = zpath_simple_app_init(&app_params);
    ASSERT_EQ(result, 0);

    admin_probe_callbacks.get_remote_host_for_s3_upload_cb = dummy_get_remote_host_for_s3_upload;

    base = event_base_new();
    if (!base) {
        std::cout << "Base error";
    }

    admin_probe_state.zcdns_ctx = zcdns_libevent_create(base,
                                                        1,
                                                        NULL,
                                                        "/etc/resolv.conf",
                                                        "/etc/hosts",
                                                        NULL,
                                                        NULL);
    admin_probe_state.ebase = base;
    ZTAILQ_INIT(&head);
    cur_list = (struct admin_probe_uploader_file_list*) ADMIN_PROBE_MALLOC(sizeof(*cur_list));
    cur_list->file_seqno = 1;
    snprintf(cur_list->file_name , sizeof(cur_list->file_name), "zpa_admin_probe_tcpdump/admin_probe_tcpdump_tcpdump9de.UTC.2022-01-04.20:18:45.971125.pcap");
    ZTAILQ_INSERT_TAIL(&head, cur_list, file_list_entry);

    result = admin_probe_send_to_s3_uploader(( char*)"commandprobes.s3-us-west-2.amazonaws.com", (char*)"file_location",
                                (char*)"test-command-uuid", (char*)"zpa_admin_probe_tcpdump", admin_probe_dummy_output_cb, NULL);


    ASSERT_EQ(callback_called, 1);
}
