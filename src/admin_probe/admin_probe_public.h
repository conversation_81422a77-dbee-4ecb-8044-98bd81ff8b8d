/*
 * admin_probe_public.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_PUBLIC_H_
#define _ADMIN_PROBE_PUBLIC_H_

//dns, icmp, tcp, mtr, tcpdump
#define MAX_TASKS_TYPE 26
enum admin_probe_task_type{
    admin_probe_invalid_task_type = 0,
    admin_probe_task_type_restart_process = 1,
    admin_probe_task_type_restart_system  = 2,
    admin_probe_task_type_dns = 3,
    admin_probe_task_type_icmp = 4,
    admin_probe_task_type_tcp = 5,
    admin_probe_task_type_mtr = 6,
    admin_probe_task_type_tcpdump = 7,
    admin_probe_task_type_ip_route = 8,
    admin_probe_task_type_ip_interfaces = 9,
    admin_probe_task_type_ip_bgp = 10,
    admin_probe_task_type_ip_bgp_neighbors = 11,
    admin_probe_task_type_ip_bgp_summary = 12,
    admin_probe_task_type_ip_clear_bgp = 13,
    admin_probe_task_type_bgp_running_config = 14,
    admin_probe_task_type_bgp_failed_config = 15,
    admin_probe_task_type_bgp_generated_config = 16,
    admin_probe_task_type_bgp_config_validate = 17,
    admin_probe_task_type_bgp_config_status = 18,
    admin_probe_task_type_bgp_config_status_details = 19,
    admin_probe_task_type_bgp_get_logs = 20,
    admin_probe_task_type_stop_bgp = 21,
    admin_probe_task_type_start_bgp = 22,
    admin_probe_task_type_restart_bgp = 23,
    admin_probe_task_type_status_bgp = 24,
    admin_probe_task_type_reload_bgp = 25,
    max_admin_probe_task_type = 26,
};


enum admin_probe_app_type {
    admin_probe_app_type_connector,
    admin_probe_app_type_private_broker,
    admin_probe_app_type_np_gateway,
};

enum restart_type {
    process_restart,
    system_restart,
};

#define STR_LEN_256 256

enum zpn_frr_config_type {
    zpn_frr_config_type_generated = 0,
    zpn_frr_config_type_override = 1
};
struct zpn_frr_cmds_params
{
    char target[STR_LEN_256];
    char interface[STR_LEN_256];
    int since_mins;
    int until_mins;
    int number;
    int is_force_reload;
    enum zpn_frr_config_type config_type;
};

struct zpn_frr_cmds_execute_args
{
    enum admin_probe_task_type task_type;
    struct zpn_frr_cmds_params *params;
    char *file_path;
};




#endif // _ADMIN_PROBE_PUBLIC_H_
