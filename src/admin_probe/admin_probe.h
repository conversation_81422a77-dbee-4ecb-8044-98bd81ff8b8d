/*
 * admin_probe.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 * admin_probe is a library that allows users who does not have direct control of a system to perform
 * certain probe commands on that system through Admin UI/API withoug actually logging into the system.
 * Thus, we are calling these commands performed this way the 'admin probe'.
 *
 * How admin probe works in a typical routine:
 *
 * 1. Ad<PERSON> triggers a probe request on UI. ie. dns resolution on abc.com to connector_1
 * 2. The request gets translated to a row entry in zpn_command_probe table in shard DB in state PENDING.
 * 3. connector_1 will pick up this request, perform the probe request, upload the result to s3 bucket.
 * 4. connector_1 will update the status of this probe request by sending a status RPC message to kafka.
 *    upon receiving the message, kafka will then update the corresponding db entries to the recent status.
 * 5. Once the kafka receives a 'COMPLETE' status for a certain task, it will go to s3 bucket to get the result and display on UI.
 *
 * Current library support following commands: NONE
 *
 * Caveat:
 *  - current library can only be inheriated by the process that is spinned up per tenant as the admin probe requests
 *    currently resides in only shards table not master.
 *
 * More details designed doc: http://10.80.1.18/doc/_build/html/ds_admin_probe.html
 *
 */

#ifndef _ADMIN_PROBE_H_
#define _ADMIN_PROBE_H_

#include "argo/argo.h"
#include "wally/wally.h"
#include "admin_probe/admin_probe_public.h"
#include "admin_probe/admin_probe_rpc.h"

/*
 * This callback is called whenever the admin_probe library needs to update the status of a command_probe entry in zpn_command_probe.
 * Once it is called, the callback function should send the status report to the kafka topic: service.zpa.log.zpn.command_probe
 *
 * this callback should return 0 on successfully transferred out the report, and non-zero otherside.
 * admin_probe routine will have resend logic to keep calling this callback until the status report has been successfully sent out.
 *
 */
typedef int (task_status_update_cb_f(void* status, int is_np_command_probe));

/*
 * this callback is only needed if the 'user' using admin_probe library wants to check the feature flag enablement for each probe action
 * before admin_probe proceed taking actions for incoming probe requests.
 *
 * if a feature flag callback is provided at the admin_probe_init() time, admin_probe lib will call this function everytime it receives a new probe request.
 * only if the callback returns a non-zero value, saying the feature is enabled, admin_probe will proceed accepting the probe request.
 *
 * if the feature flag callback is not provided, admin_probe will accept the probe request without checking the feature flag.
 */
typedef int (check_feature_flag_cb_f(enum admin_probe_task_type type));

/*
 * if the 'user' using admin_probe wants to support action RESTART_PROCESS/RESTART_SYSTEM from remote control, this callback must be provided.
 * admin_probe lib does not expect this callback function to call exit() immediately once admin_probe calls this function.
 *
 * we expect this callback to return
 *  - 0 : meaning, yes the system ack on the restart action and confirm its able to restart, and it will restart soon.
 *  - 1: meaning, no, the system can not perform restart action.
 */
typedef int (restart_cb_f(enum restart_type type));

/*
 * This callback is called whenever the admin_probe library needs to execute the frr commands in np_command_probe.
 * Once it is called, the callback function should execute frr commands through vtysh and store the output to a file for s3 upload.
 *
 * we expect this callback to return
 *  - 0 : meaning, the frr command is executed successfully by application.
 *  - appropriate error code : incase of failures.
 *
 *  cmd_result - set 1 incase of success and 0 for failure.
 */
typedef int (frr_cmds_execute_cb_f(struct zpn_frr_cmds_execute_args *args, int *cmd_result));

/*
 * this callback must be provided in order to init admin_probe.
 * this callback should return the accurate epoch_s() in the zscaler cloud time rather than the local system epoch time.
 */
typedef int64_t (get_cloud_epoch_s_cb_f());

/*
 * this callback is needed and called when probe results are sent to s3 bucket over proxy
 * and this api will return the proxy name.
 *
 * If the application prefers to bypass the proxy and upload the results directly to the S3 bucket,
 * set the `no_proxy` flag to true.
 */
typedef int (get_remote_host_for_s3_upload_f(char *, int size, int *no_proxy));

/*
 * if this callback is provided, upon every command_probe request,
 * admin_probe will call it to check if the entity state is ok, before proceeding with the request.
 */
typedef int (get_entity_state_paused_mode_f());


/*
 * For the process that wants to inherit admin probe feature, this is the only API that process will need to call at init time.
 * Once this api is called, admin_probe will enter standby state by listening all the admin probe requests(table rows) from database.
 * Only when the first admin probe request comes, the admin_probe library will be fully initialized with all the modules running since then.
 *
 * Arguments:
 *
 * - Required:
 *      wally : the wally that
 *      entity_gid : the gid of the components (ie, connector gid, pbroker gid, broker gid)
 *      customer_gid : customer gid
 *      app_type : AC/PSE/NP_Gateway
 *      is_dev_env - specify if we are running in dev environment (for soft assert check)
 *      is_np_command_probe - specify true if admin probe library needs to be initialized for np command probe(targets : np gateway and np connector)
 *
 * required callbacks:
 *      status_update_cb - the user must provide a status_update_cb function that can send status report to kafka in order
 *                         to use admin_probe library.
 *
 *      get_current_cloud_epoch_s_cb - must provide one as kafka is using cloud cloud epoch_s
 *
 * Optional callbacks:
 *      feature_flag_check_cb - if the user wants to enable admin_probe based on feature flag in zpath_config_override.
 *                              if its not provided, then admin_probe will go ahead executing the task if there is an request entries in db.
 *
 *      restart_cb - if the components wants to support restart functionarlity, must provide an restart_cb there.
 *
 *      frr_cmds_execute_cb - if the component wants to support np command probes related to bgp/frr service.
 *
 *      get_remote_host_for_s3_upload - if data needs to goto s3
 *
 * Note: once we support taking admin probe request from master DB, wally and customer gid will become optional arguments there.
 */
int admin_probe_init(struct wally *wally,
                    int64_t entity_gid,
                    char *entity_name,
                    int64_t customer_gid,
                    enum admin_probe_app_type app_type,
                    task_status_update_cb_f* status_update_cb,
                    check_feature_flag_cb_f* feature_flag_check_cb,
                    restart_cb_f* restart_cb,
                    frr_cmds_execute_cb_f* frr_cmds_execute_cb,
                    get_cloud_epoch_s_cb_f* get_current_cloud_epoch_s_cb,
                    int is_dev_env,
                    int is_np_command_probe,
                    get_remote_host_for_s3_upload_f* get_remote_host_for_s3_upload,
                    get_entity_state_paused_mode_f* get_entity_paused_mode_cb,
                    struct argo_log_collection *event_log,
                    int is_zpath_config_override_inited);


#endif /* _ADMIN_PROBE_H_ */
