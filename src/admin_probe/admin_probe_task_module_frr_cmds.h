/*
 * admin_probe_task_module_frr_cmds.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 *
 */
#include "admin_probe/admin_probe_private.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_public.h"
#include <inttypes.h>
#include "admin_probe/admin_probe_task_module_private.h"

#define ADMIN_PROBE_FRR_CMDS_FILE_LOCATION   "zpa-admin-probe-frr"
#define ADMIN_PROBE_FRR_CMDS_FILE_NAME       "admin-probe-frr"
#define ADMIN_PROBE_FRR_CMDS_DIR_MODE        0700

#define ADMIN_PROBE_FRR_CMDS_FILE_LOCATION_FOR_GATEWAY   "/opt/zscaler/var/zpa-admin-probe-frr"

#define ADMIN_PROBE_FRR_CMDS_MAX_FILE_NAME   2048

#define FRR_CMD_DEFAULT_TARGET_ALL "all"
#define FRR_CMD_DEFAULT_LOG_NUMBER  1000
#define FRR_CMD_MAX_LOG_NUMBER      5000

struct admin_probe_frr_resp {
    char *reason;
    void *cookie;
};

int
admin_probe_task_module_frr_cmds_init(enum admin_probe_app_type app_type);
int
admin_probe_task_module_frr_cmds_debug_init();

int64_t admin_probe_get_total_number_of_task_frr_cmds_success();
int64_t admin_probe_get_total_number_of_task_frr_cmds_fail();

extern struct admin_probe_task_module_callouts admin_probe_task_module_callouts_frr_cmds;
