/*
 * admin_probe_task_module_frr_cmds.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 *
 */
#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>
#include "argo/argo.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn_pcap/zpn_pcap.h"
#include "parson/parson.h"
#include "admin_probe/admin_probe_task_module_frr_cmds.h"
#include "admin_probe/admin_probe_private.h"
#include "admin_probe/admin_probe_uploader.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include "admin_probe/admin_probe_task_module_common.h"

#define MAX_FRR_RESPONSE_TXT_LEN 556

static struct {
    int64_t total_frr_cmds_in_exec;
} frr_cmds_state;

static struct admin_probe_task_module_frr_cmds_stats{                        /* _ARGO: * object_definition */
    int64_t failed_to_exec_frr_cmds_because_of_threshold_reached;           /* _ARGO: integer */
    int64_t create_directory_fail;                                          /* _ARGO: integer */
    int64_t open_directory_fail_init;                                       /* _ARGO: integer */
    int64_t invalid_task_struct_s3_output_callback;                         /* _ARGO: integer */
    int64_t frr_cmds_app_execute_cb_error;                                  /* _ARGO: integer */
    int64_t frr_cmds_invalid_arguments_error;                               /* _ARGO: integer */
    int64_t upload_setup_fail;                                              /* _ARGO: integer */
    int64_t frr_cmds_total_attempted;                                        /* _ARGO: integer */
    int64_t frr_cmds_total_success;                                          /* _ARGO: integer */
    int64_t frr_cmds_total_failure;                                          /* _ARGO: integer */
    int64_t frr_cmds_s3_output_failure;                                      /* _ARGO: integer */
    int64_t frr_cmds_total_cancel;                                           /* _ARGO: integer */
} frr_cmds_stats;

#include "admin_probe/admin_probe_task_module_frr_cmds_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_frr_cmds_stats_description;

char *g_frr_cmds_dir_path = ADMIN_PROBE_FRR_CMDS_FILE_LOCATION;

int admin_probe_task_module_frr_cmds_init(enum admin_probe_app_type app_type) {
    int status;
    char buf[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME] = {0};
    struct dirent *dir;

    admin_probe_task_module_frr_cmds_debug_init();

    if (app_type == admin_probe_app_type_np_gateway) {
        g_frr_cmds_dir_path = ADMIN_PROBE_FRR_CMDS_FILE_LOCATION_FOR_GATEWAY;
    }

    status = mkdir(g_frr_cmds_dir_path, ADMIN_PROBE_FRR_CMDS_DIR_MODE);
    if (status == -1) {
        if (errno != EEXIST) {
            frr_cmds_stats.create_directory_fail++;
            ADMIN_PROBE_LOG(AL_ERROR, "Could not create sub directory:%s for frr cmds. err:%s", g_frr_cmds_dir_path, strerror(errno));
            return status;
        } else {
            /*Cleanup all old files after a restart*/
            DIR *dirp = opendir(g_frr_cmds_dir_path);
            if (dirp == NULL) {
                ADMIN_PROBE_LOG(AL_ERROR, "Open directory:%s failed during cleanup", g_frr_cmds_dir_path);
                frr_cmds_stats.open_directory_fail_init++;
                status = ADMIN_PROBE_RESULT_BAD_STATE;
                goto init_error;
            }
            if (dirp) {
                while ((dir = readdir(dirp)) != NULL) {
                    if (!(strcmp(dir->d_name, ".")) || !(strcmp(dir->d_name, ".."))) {
                        continue;
                    }
                    snprintf(buf, sizeof(buf), "%s/%s", g_frr_cmds_dir_path, dir->d_name);
                    unlink(buf);
                }
                closedir(dirp);
            }
            status = ADMIN_PROBE_RESULT_NO_ERROR;
        }
    }
init_error:
    return status;
}

int admin_probe_frr_cmds_parse_additional_arguments(struct admin_probe_task *cur_task, char *additional_arguments, struct zpn_frr_cmds_params *params)
{
    JSON_Value *jv = NULL;
    int number = 0;

    if (additional_arguments == NULL || params == NULL) {
        return ADMIN_PROBE_RESULT_BAD_ARGUMENT;
    }

    jv = json_parse_string(additional_arguments);
    if (json_value_get_type(jv) != JSONObject) {
       ADMIN_PROBE_LOG(AL_ERROR, "Failed to validate JSON:%s", additional_arguments);
        if (jv) {
            json_value_free(jv);
        }
        return ADMIN_PROBE_RESULT_BAD_DATA;
    }

    JSON_Object* jo = json_value_get_object(jv);
    if (!jo) {
        ADMIN_PROBE_LOG(AL_ERROR, "Failed to get JSON root object for:%s", additional_arguments);
        if (jv) {
            json_value_free(jv);
        }
        return ADMIN_PROBE_RESULT_BAD_DATA;
    }

    const char *interface = json_object_get_string (jo, "interface");
    if (interface) {
        strncpy(params->interface, interface, STR_LEN_256 - 1);
        ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("Interface:%s for cmd:%s action:%s",
                         params->interface, cur_task->command_uuid, cur_task->action);
    } else {
        strncpy(params->interface, FRR_CMD_DEFAULT_TARGET_ALL, STR_LEN_256 - 1);
    }

    const char *force_str = json_object_get_string (jo, "force_reload");
    params->is_force_reload = force_str ? atoi(force_str) : 0;

    const char *config_type = json_object_get_string (jo, "config_mode");
    if (config_type) {
        if (strcmp(config_type, "generated") == 0) {
            params->config_type = zpn_frr_config_type_generated;
        } else if (strcmp(config_type, "override") == 0) {
            params->config_type = zpn_frr_config_type_override;
        }
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("force_reload:%d config_type:%d for cmd:%s action:%s",
             params->is_force_reload, params->config_type, cur_task->command_uuid, cur_task->action);

    const char *since_str = json_object_get_string (jo, "since");
    const char *until_str = json_object_get_string (jo, "until");

    params->since_mins = since_str ? atoi(since_str) : 0;
    params->until_mins = until_str ? atoi(until_str) : 0;

    const char *num_str = json_object_get_string (jo, "number");
    number = num_str ? atoi(num_str) : 0;
    if (number > 0 && number <= FRR_CMD_MAX_LOG_NUMBER) {
        params->number = number;
    } else {
        ADMIN_PROBE_LOG(AL_INFO, "Using default log number lines:%d, configured:%d",
                                                     FRR_CMD_DEFAULT_LOG_NUMBER, number);
        params->number = FRR_CMD_DEFAULT_LOG_NUMBER;
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("since_m:%d until_m:%d number:%d for cmd:%s action:%s",
            params->since_mins, params->until_mins, params->number, cur_task->command_uuid, cur_task->action);

    json_value_free(jv);

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

void admin_probe_frr_cmds_s3_output_cb (struct uploader_output uploader_output, void *cookie)
{
    frr_cmds_state.total_frr_cmds_in_exec--;
    struct admin_probe_task *cur_task = (struct admin_probe_task *) cookie;
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid task in s3 output");
        frr_cmds_stats.invalid_task_struct_s3_output_callback++;
        return;
    }

    if (cur_task->cleanup_criteria.is_uploader_callback_outstanding) {
        cur_task->cleanup_criteria.is_uploader_callback_outstanding = 0;
    }

    ADMIN_PROBE_DEBUG_UPLOADER("Upload has been completed for %s %d %s", cur_task->command_uuid, uploader_output.status,
                                uploader_output.error_message);
    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (0 == uploader_output.status) {
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_success);
        frr_cmds_stats.frr_cmds_total_success++;
    } else {
        cur_task->err_message = uploader_output.error_message;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_failed);
        frr_cmds_stats.frr_cmds_s3_output_failure++;
        frr_cmds_stats.frr_cmds_total_failure++;
    }
}

 /*{
    "action": "CLEAR_BGP",
    "target": "240.7.7.27",
    "entity_name": "siva-npconnector-key-1-1747917712847",
    "entity_gid": "145261245337961000",
    "result": "success"
}*/
static char* admin_probe_task_module_frr_cmds_report_in_json(char *command_uuid, char *action, char *target, int probe_result)
{
    char *buf = NULL;
    char entity_id_str[ADMIN_PROBE_MAX_ENTITY_ID] = {0};

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating result in json form");
        return NULL;
    }

    JSON_Value *report_val = json_value_init_object();
    JSON_Object *report_obj = json_value_get_object(report_val);

    snprintf(entity_id_str, sizeof(entity_id_str), "%"PRId64"", admin_probe_state.entity_gid);

    json_object_set_string(report_obj, "action", action);
    if (target) {
        json_object_set_string(report_obj, "target", target);
    }
    json_object_set_string(report_obj, "entity_name", admin_probe_state.entity_name);
    json_object_set_string(report_obj, "entity_gid", entity_id_str);

    if (strcmp(action, ADMIN_PROBE_ACTION_CONFIG_STATUS) == 0) {
        json_object_set_string(report_obj, "result", (probe_result? "CONFIG_VALID" : "CONFIG_ERROR"));
    } else {
        json_object_set_string(report_obj, "result", (probe_result? "success" : "fail"));
    }

    buf = json_serialize_to_string_pretty(report_val);

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : Clear BGP probe result (JSON) :\n %s", command_uuid?command_uuid:" ", buf);

    json_value_free(report_val);

    return buf;
}

static char* admin_probe_task_module_frr_cmds_report_in_txt(char* command_uuid, char *action, char *target, int probe_result)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating result in txt form");
        return NULL;
    }

    char *buf = ADMIN_PROBE_MALLOC(MAX_FRR_RESPONSE_TXT_LEN);
    char *s = buf;
    char *e = s + MAX_FRR_RESPONSE_TXT_LEN;

    if (target) {
        s += sxprintf(s, e, "%s : %s\n", action, target);
    } else {
        s += sxprintf(s, e, "%s\n", action);
    }

    s += sxprintf(s, e, "entity_name : %s\n",admin_probe_state.entity_name);
    s += sxprintf(s, e, "entity_gid : %"PRId64"\n",admin_probe_state.entity_gid);
    if (strcmp(action, ADMIN_PROBE_ACTION_CONFIG_STATUS) == 0) {
        s += sxprintf(s, e, "result : %s\n", (probe_result ? "CONFIG_VALID" : "CONFIG_ERROR"));
    } else {
        s += sxprintf(s, e, "result : %s\n", (probe_result ? "success" : "fail"));
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : Clear BGP probe result (txt) :\n %s", command_uuid?command_uuid:" ", buf);

    return buf;

}

static void admin_probe_task_module_frr_cmds_format_probe_result_report(struct admin_probe_task *cur_task, int cmd_result)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no frrcmds info, can not proceed with formatting frr cmds probe result");
        return;
    }

    cur_task->result_txt = admin_probe_task_module_frr_cmds_report_in_txt(cur_task->command_uuid, cur_task->action, cur_task->target, cmd_result);
    cur_task->result_json = admin_probe_task_module_frr_cmds_report_in_json(cur_task->command_uuid, cur_task->action, cur_task->target, cmd_result);
    ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("Command id:%s action:%s result txt:%s json:%s", cur_task->command_uuid,
                                                         cur_task->action, cur_task->result_txt, cur_task->result_json);
}

static int is_admin_probe_s3_upload_required(enum admin_probe_task_type type) {
    int res = 0;
    switch(type) {
        case admin_probe_task_type_ip_route:
        case admin_probe_task_type_ip_interfaces:
        case admin_probe_task_type_ip_bgp:
        case admin_probe_task_type_ip_bgp_neighbors:
        case admin_probe_task_type_ip_bgp_summary:
        case admin_probe_task_type_bgp_running_config:
        case admin_probe_task_type_bgp_config_validate:
        case admin_probe_task_type_bgp_config_status_details:
        case admin_probe_task_type_bgp_get_logs:
        case admin_probe_task_type_status_bgp:
            // As per the design, s3 upload required for all these commands
            res = 1;
            break;
        case admin_probe_task_type_ip_clear_bgp:
        case admin_probe_task_type_bgp_config_status:
        case admin_probe_task_type_stop_bgp:
        case admin_probe_task_type_start_bgp:
        case admin_probe_task_type_restart_bgp:
            // s3 upload doesn't required. Command status will be send with result_txt and result_json.
            res = 0;
        default:
            break;
    }
    return res;
}

int admin_probe_task_module_process_frr_cmds_task_f(struct admin_probe_task *cur_task)
{
    int64_t     frr_cmds_limit_exec;
    struct zpn_frr_cmds_params params = {0};
    struct zpn_frr_cmds_execute_args args = {0};
    int cmd_result = 0;
    char        file_name[ADMIN_PROBE_FRR_CMDS_MAX_FILE_NAME] = {0};
    int res = 0;

    frr_cmds_limit_exec = admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(cur_task->type);
    ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("threshold check - 's limit is currently at %"PRId64" ", frr_cmds_limit_exec);

    if (frr_cmds_state.total_frr_cmds_in_exec >= frr_cmds_limit_exec) {
        frr_cmds_stats.failed_to_exec_frr_cmds_because_of_threshold_reached++;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        return ADMIN_PROBE_TASK_LIMIT_REACHED;
    }

    frr_cmds_state.total_frr_cmds_in_exec++;
    frr_cmds_stats.frr_cmds_total_attempted++;

    if (cur_task->target) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("Parsing target:%s for cmd:%s action:%s",
                                 cur_task->target, cur_task->command_uuid, cur_task->action);
        strncpy(params.target, cur_task->target, STR_LEN_256 - 1);
    } else {
        strncpy(params.target, FRR_CMD_DEFAULT_TARGET_ALL, STR_LEN_256 - 1);
    }

    if (cur_task->additional_arguments) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("Parsing additional arguments:%s for cmd:%s action:%s",
                                 cur_task->additional_arguments, cur_task->command_uuid, cur_task->action);
        res = admin_probe_frr_cmds_parse_additional_arguments(cur_task, cur_task->additional_arguments, &params);
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "Error parsing additional arguments:%s for cmd:%s type:%d error:%s",
                cur_task->additional_arguments, cur_task->command_uuid, cur_task->type, zpath_result_string(res));
            cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
            frr_cmds_stats.frr_cmds_invalid_arguments_error++;
            frr_cmds_state.total_frr_cmds_in_exec--;
            frr_cmds_stats.frr_cmds_total_failure++;
            return ADMIN_PROBE_RESULT_ERR;
        }
    }

    cur_task->start_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    admin_probe_task_module_switch_task_state_to_processing_for_synchronous_cmds(cur_task);

    // Prepare local file path to store the output results for s3 upload.
    snprintf(file_name, sizeof(file_name), "%s/%s-%s",
            g_frr_cmds_dir_path, ADMIN_PROBE_FRR_CMDS_FILE_NAME,
            cur_task->command_uuid);

    // Prepare args for the application layer callback
    args.task_type = cur_task->type;
    args.params = &params;
    args.file_path = file_name;

    ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS("Executing frr cmd %s type:%d",
                                         cur_task->command_uuid, cur_task->type);

    res = ADMIN_PROBE_RESULT_ERR;
    if (admin_probe_callbacks.frr_cmds_execute_cb) {
        res = admin_probe_callbacks.frr_cmds_execute_cb(&args, &cmd_result);
    }

    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Error executing NP probe cmd:%s type:%d error:%s",
                             cur_task->command_uuid, cur_task->type, zpath_result_string(res));
        cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_IN_GENERAL;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        frr_cmds_state.total_frr_cmds_in_exec--;
        frr_cmds_stats.frr_cmds_total_failure++;
        frr_cmds_stats.frr_cmds_invalid_arguments_error++;
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!is_admin_probe_s3_upload_required(cur_task->type)) {
        // Send command status with result_txt and result_json
        admin_probe_task_module_frr_cmds_format_probe_result_report(cur_task, cmd_result);
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                    internal_task_execute_done_success);
        frr_cmds_state.total_frr_cmds_in_exec--;
        frr_cmds_stats.frr_cmds_total_success++;
    } else {
        // Upload command output to s3 bucket
        cur_task->cleanup_criteria.is_uploader_callback_outstanding = 1;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                    internal_task_execute_done_but_pending_upload);
        res = admin_probe_send_to_s3_uploader(cur_task->bucket_name, cur_task->file_location,
                                                    g_frr_cmds_dir_path, cur_task->command_uuid,
                                                    admin_probe_frr_cmds_s3_output_cb, cur_task);
        if (res == ADMIN_PROBE_RESULT_BAD_ARGUMENT) {
            cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                        internal_task_upload_failed);
            frr_cmds_state.total_frr_cmds_in_exec--;
            frr_cmds_stats.upload_setup_fail++;
            frr_cmds_stats.frr_cmds_total_failure++;
        }
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int admin_probe_task_module_frr_cmds_dump_stats(struct zpath_debug_state *request_state,
                                                      const char **query_values,
                                                      int query_value_count,
                                                      void *cookie) {
    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_frr_cmds_stats_description,
                                                    &frr_cmds_stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int admin_probe_task_module_frr_cmds_debug_rate_limit(struct zpath_debug_state *request_state,
                                                    const char **query_values,
                                                    int query_value_count,
                                                    void *cookie)
{
    if (!query_values[0]) {
        ZDP("Missing argument for rate limit\n");
        return ADMIN_PROBE_RESULT_ERR;
    }
    frr_cmds_state.total_frr_cmds_in_exec = strtoll(query_values[0], NULL, 10);
    ZDP("Rate limit set to %"PRId64"\n", frr_cmds_state.total_frr_cmds_in_exec);
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int
admin_probe_task_module_frr_cmds_debug_init()
{
    int res;

    if (!(admin_probe_task_module_frr_cmds_stats_description = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_FRR_CMDS_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of task_module_frr_cmds",
                                  "/admin_probe/task_module_frr_cmds_stats",
                                  admin_probe_task_module_frr_cmds_dump_stats,
                                  NULL,
                                  NULL);
    if (res) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_write_command("Set the frr_cmds rate limit",
                                  "/admin_probe/task_module_frr_cmds/rate_limit",
                                   admin_probe_task_module_frr_cmds_debug_rate_limit, NULL,
                                  "value" , "rate limiting value",
                                   NULL);
    if (res) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_frr_cmds_init_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_frr_cmds_check_expiry_in_processing_state_f(struct admin_probe_task *cur_task)
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

void admin_probe_task_module_frr_cmds_free_task_state_f(struct admin_probe_task *cur_task)
{
    if (cur_task->task_state.tcp_task_state.request_domain) {
        ADMIN_PROBE_FREE(cur_task->task_state.tcp_task_state.request_domain);
    }
    return;
}

int admin_probe_task_module_frr_cmds_cancel_f(struct admin_probe_task *cur_task)
{
    int res = ADMIN_PROBE_RESULT_NO_ERROR;
    return res;
}

struct admin_probe_task_module_callouts admin_probe_task_module_callouts_frr_cmds = {
    admin_probe_task_module_frr_cmds_init_f,
    admin_probe_task_module_process_frr_cmds_task_f,
    admin_probe_task_module_frr_cmds_check_expiry_in_processing_state_f,
    admin_probe_task_module_frr_cmds_free_task_state_f,
    admin_probe_task_module_frr_cmds_cancel_f
};

int64_t admin_probe_get_total_number_of_task_frr_cmds_success()
{
    return frr_cmds_stats.frr_cmds_total_success;
}

int64_t admin_probe_get_total_number_of_task_frr_cmds_fail()
{
    return frr_cmds_stats.frr_cmds_total_failure + frr_cmds_stats.frr_cmds_total_cancel;
}
