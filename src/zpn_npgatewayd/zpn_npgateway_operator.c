/*
 * zpn_npgateway_operator.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * NP Gateway's NP Common Library operator.
 *
 */

#include "zpn_npgatewayd/zpn_npgateway_operator.h"
#include "argo/argo_log.h"
#include "np_lib/np_connectors.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn_npgatewayd/zpn_npgateway_common.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpn_npgatewayd/zpn_npgateway_stats.h"
#include "zpn_npgatewayd/zpn_npgateway_bgp.h"


#include "argo/argo.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_clients.h"
#include "np_lib/np_connector_groups.h"
#include "np_lib/np_connectors.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_frr_utils.h"
#include "npwg_lib/npwg_provider.h"
#include <strings.h>
#include <sys/socket.h>
#include "zevent/zevent.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn_npgatewayd/zpn_npgateway_alloc.h"
#include "zthread/zthread.h"
#include "zpn_npgatewayd/zpn_npgateway_common_compiled.h"

#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"

struct zpn_npgateway_common_stats operate_stats;

static int64_t zpn_npgateway_operator_multilan_feature_enable(int64_t gateway_gid, int64_t customer_gid)
{
    int64_t value;

    value = zpath_config_override_get_config_int(CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                &value,
                                                DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                gateway_gid,
                                                customer_gid,
                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                (int64_t)0);
    return value;
}

void clients_set_handler(__attribute__((unused)) evutil_socket_t sock,
                         __attribute__((unused)) short flags,
                         void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *row = event_param->row;
    const struct np_clients *client = (struct np_clients *)row->base_structure_void;
    // note: handle key expiry here

    NPGWD_LOG(AL_INFO,
              "Setting client with gid: %" PRId64 ", public_key: %s, c_name: %s",
              client->gid,
              client->public_key,
              client->c_name);
    int rc = npwg_set_client_peer(gateway_config->provider,
                                  client->public_key,
                                  client->ip);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "could not set client peer config, client c_name: %s",
                  client->c_name);
    }

    free_gateway_event_param(&event_param);
}

void clients_delete_handler(__attribute__((unused)) evutil_socket_t sock,
                            __attribute__((unused)) short flags,
                            void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *row = event_param->row;
    const struct np_clients *client = (struct np_clients *)row->base_structure_void;
    int rc = npwg_delete_peer_config(gateway_config->provider,
                                     client->public_key);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "could not delete client peer config");
    }

    free_gateway_event_param(&event_param);
    return;
}


static inline void connector_set_helper(const np_gateway_config_t *gateway_config,
                                        const struct np_connectors *connector)
{
    // this is a set event, return if the connector is no longer settable
    if (connector->state != CONNECTOR_STATE_ONLINE) {
        return;
    }
    int64_t connector_group_gid = connector->connector_group_gid;

    struct np_lan_subnets *subnets[NPWG_MAX_ALLOWED_IPS_PER_PEER];
    size_t subnet_count = NPWG_MAX_ALLOWED_IPS_PER_PEER;
    int rc = np_lan_subnets_get_by_connector_group_gid_immediate(
                connector_group_gid,
                (struct np_lan_subnets **)&subnets,
                &subnet_count);

    // rc can be non-zero in two cases:
    // 1. LAN subnets have not been added yet.
    // 2. LAN subnets were soft deleted and we can no longer query for them.
    // It is critical to handle the later
    if (rc) {
        NPGWD_LOG(AL_INFO,
                  "LAN subnets not found in Wally for connector group: %" PRId64 ".",
                  connector_group_gid);
        rc = npwg_delete_peer_config(gateway_config->provider,
                                     connector->public_key);
        if (rc) {
            NPGWD_LOG(AL_CRITICAL,
                      "Could not delete connector peer, gid: %" PRId64,
                      connector->gid);
        }
        return;
    }

    static struct argo_inet lan_cidrs[NPWG_MAX_ALLOWED_IPS_PER_PEER];
    int lan_cidrs_count = 0;
    int64_t g_gw_multi_lan_feature_enable = zpn_npgateway_operator_multilan_feature_enable(gateway_config->gid, gateway_config->customer_gid);

    if (g_gw_multi_lan_feature_enable) {
        for (int ii = 0; ii < subnet_count; ++ii) {
            struct np_lan_subnets *current_row = subnets[ii];

            if (current_row->lan_subnets && current_row->lan_subnets_count > 0) {
                for (int jj = 0; jj < current_row->lan_subnets_count; ++jj) {
                    if (current_row->lan_subnets[jj] == NULL) {
                        NPGWD_LOG(AL_WARNING,
                                "Ignoring NULL LAN subnet at index %d while processing connector group: %" PRId64
                                ", current_row gid: %" PRId64,
                                jj, connector_group_gid, current_row->gid);
                        continue;
                    }

                    if (lan_cidrs_count >= NPWG_MAX_ALLOWED_IPS_PER_PEER) {
                        NPGWD_LOG(AL_ERROR,
                                "Exceeded maximum allowed LAN subnets (%d) while processing connector group: %" PRId64,
                                NPWG_MAX_ALLOWED_IPS_PER_PEER, connector_group_gid);
                        break;
                    }

                    lan_cidrs[lan_cidrs_count++] = *current_row->lan_subnets[jj];
                }
            }

            NPGWD_LOG(AL_NOTICE,
                    "In MultiLan %s: subnet gid: %" PRId64
                    ", customer_gid: %" PRId64
                    ", connector_group_gid: %" PRId64
                    ", current_row lansubnet_count: %d, lan_cidrs_count: %d",
                     __FUNCTION__,
                    current_row->gid,
                    current_row->customer_gid,
                    current_row->connector_group_gid,
                    current_row->lan_subnets_count,
                    lan_cidrs_count);
        }
    } else {
        for (int ii = 0; ii < subnet_count; ++ii) {
            NPGWD_LOG(AL_NOTICE,
                        "In %s: subnet gid: %" PRId64
                        ", customer_gid: %" PRId64
                        ", connector_group_gid: %" PRId64
                        ", subnet: %p",
                        __FUNCTION__,
                         subnets[ii]->gid,
                        subnets[ii]->customer_gid,
                        subnets[ii]->connector_group_gid,
                        subnets[ii]->subnet);
            lan_cidrs[ii] = *subnets[ii]->subnet;

            /*
             * np_lan_subnets.subnet will be deprecated after MultiLan subnets support feature enablement.
             * backend is not expected to consume np_lan_subnet.subnet once the feature is enabled.
             *
             */
            if (subnets[ii]->subnet == NULL) {
                NPGWD_LOG(AL_WARNING,
                        "ignoring subnet gid: %" PRId64
                        ", customer_gid: %" PRId64
                        ", connector_group_gid: %" PRId64
                        " due to NULL np_lan_subnets.subnet.",
                        subnets[ii]->gid,
                        subnets[ii]->customer_gid,
                        subnets[ii]->connector_group_gid);
                continue;
    }

            }
    }

    NPGWD_LOG(AL_INFO,
              "Subnet count for connector group: %" PRId64 ", %lu",
              connector_group_gid,
              subnet_count);

    NPGWD_LOG(AL_INFO, "Setting connector with gid: %" PRId64 ", public_key: %s",
                connector->gid, connector->public_key);
    rc = npwg_set_connector_peer(gateway_config->provider,
                                 connector->public_key,
                                 lan_cidrs,
                                 g_gw_multi_lan_feature_enable?lan_cidrs_count:subnet_count,
                                 true);

    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Could not set connector peer, gid: %" PRId64,
                  connector->gid);
    }
    return;
}

/**
 * Remove Connector WG interface and do the clean up
 */
void connector_event_handler_remove_connector(const np_gateway_config_t *gateway_config,
                                              npwg_provider provider,
                                              int64_t connector_gid)
{
    if (!gateway_config || !provider) {
        NPGWD_LOG(AL_ERROR, "Invalid input for connector_event_handler_remove_connector");
        return;
    }

    int rc = NPWG_RESULT_NO_ERROR;
    char *interface_name = npwg_provider_return_interface_name(provider);
    if (!interface_name) {
        interface_name = NPWG_INVALID_INTERFACE_NAME;
    }

    NPGWD_LOG(AL_NOTICE, "Removing Connector %"PRId64", interface %s", connector_gid, interface_name);

    rc = npwg_provider_ip_link_set_up_down_npwg_intf(provider, 0);
    if (rc) {
        NPGWD_LOG(AL_WARNING, "Could not bring down wireguard interface %s gid %"PRId64" ", interface_name, connector_gid);
        __sync_add_and_fetch_8(&(operate_stats.ip_link_down_err), 1);
    }

    rc = npwg_provider_ip_link_add_delete_npwg_intf(provider, 0);
    if (rc) {
        NPGWD_LOG(AL_WARNING, "Could not bring down wireguard interface %s gid %"PRId64" ", interface_name, connector_gid);
        __sync_add_and_fetch_8(&(operate_stats.ip_link_delete_err), 1);
    }

    rc = zhash_table_remove(gateway_config->npc_hash_by_gid, &(connector_gid), sizeof(connector_gid), provider);
    if (rc) {
        NPGWD_LOG(AL_WARNING, "Connector %"PRId64", couldn't remove npc wg interface from hash ..", connector_gid);
        __sync_add_and_fetch_8(&(operate_stats.npc_provider_remove_hash_err), 1);
    } else {
        __sync_add_and_fetch_8(&(operate_stats.npc_provider_remove_hash), 1);
    }

    npwg_destroy_provider(&provider);
    __sync_add_and_fetch_8(&(operate_stats.npc_provider_destroyed), 1);

    return;

}

/*
 * Bring Up wireguard Interface for NPC
 *
 * steps:
 * 1. bring down and delete the interface if previously exist
 * 2. ip link add dev wg0 type wireguard
 * 3. wg setconf wg0 wg0.conf
 * 4. ip link set wg1 master vpn_vrf
 * 5. ip link set up dev wg0
 */
static int connector_event_handler_redundancy_start_wg(npwg_provider provider, int mtu)
{
    if (!provider) {
        return NPWG_RESULT_ERR;
    }

    int rc = NPWG_RESULT_NO_ERROR;
    int rc2 = NPWG_RESULT_NO_ERROR;

    char *interface_name = npwg_provider_return_interface_name(provider);
    if (!interface_name) {
        interface_name = NPWG_INVALID_INTERFACE_NAME;
    }

    /* Safeguard: bring down and delete the wg interface if previously exist*/
    rc = npwg_check_and_delete_interface(provider);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not check and delete wireguard driver interface %s failed %s", interface_name, npgwd_result_string(rc));
        __sync_add_and_fetch_8(&(operate_stats.interface_check_and_delete_fail), 1);
        //fall through
    }

    /* create wg interface using ip link command */
    rc = npwg_provider_ip_link_add_delete_npwg_intf(provider, 1);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not create wireguard driver interface %s failed %s", interface_name, npgwd_result_string(rc));
        __sync_add_and_fetch_8(&(operate_stats.ip_link_add_err), 1);
        return rc;
    }

    /* assign vrf as master to newly created wg interface */
    rc = npwg_link_wg_interface_to_vrf(provider, NPWG_VRF_NAME);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not link to interface %s with err %s for table " NPWG_VRF_NAME , interface_name, npgwd_result_string(rc));
        rc2 = npwg_provider_ip_link_add_delete_npwg_intf(provider, 0);
        if (rc2) {
            NPGWD_LOG(AL_CRITICAL, "Could not delete interface %s with err %s", interface_name, npgwd_result_string(rc));
            __sync_add_and_fetch_8(&(operate_stats.ip_link_delete_err), 1);
        }
        __sync_add_and_fetch_8(&(operate_stats.link_wg_intf_to_vrf_err), 1);
        return rc;
    }

    /* wg setconf */
    rc = npwg_provider_wg_setconf_nwpg_intf(provider);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not setconf for wg interface %s with err %s", interface_name, npgwd_result_string(rc));
        rc2 = npwg_provider_ip_link_add_delete_npwg_intf(provider, 0);
        if (rc2) {
            NPGWD_LOG(AL_CRITICAL, "Could not delete interface %s with err %s", interface_name, npgwd_result_string(rc));
            __sync_add_and_fetch_8(&(operate_stats.ip_link_delete_err), 1);
        }
        __sync_add_and_fetch_8(&(operate_stats.wg_setconf_err), 1);
        return rc;
    }

    /* bring down interface before bring up just in case */
    npwg_provider_ip_link_set_up_down_npwg_intf(provider, 0);

    /* Bring up the interface */
    rc = npwg_provider_ip_link_set_up_down_npwg_intf(provider, 1);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not bring up wg interface %s with err %s", interface_name, npgwd_result_string(rc));
        rc2 = npwg_provider_ip_link_add_delete_npwg_intf(provider, 0);
        if (rc2) {
            NPGWD_LOG(AL_CRITICAL, "Could not delete interface %s with err %s", interface_name, npgwd_result_string(rc));
            __sync_add_and_fetch_8(&(operate_stats.ip_link_delete_err), 1);
        }
        __sync_add_and_fetch_8(&(operate_stats.ip_link_up_err), 1);
        return rc;
    }

    rc = npwg_provider_set_mtu(provider, mtu);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "Wireguard set configured mtu failed %s, for interface %s",
                  npgwd_result_string(rc),npwg_provider_return_interface_name(provider));
        //FALL THROUGH, its fine..
    }

    return rc;
}


void connector_event_handler_redundancy(const np_gateway_config_t *gateway_config,
                                        struct argo_object *previous_row,
                                        struct np_connectors *connector)
{
    int rc = NPWG_RESULT_NO_ERROR;
    npwg_provider cur_provider = NULL;
    uint16_t connector_port = 0;

    if (connector->deleted) {
        if (!previous_row) {
            /*
             * ignoring deleted connector rows as it has no previous row
             * meaning its the row_callback happens for the first time after gateway initialize
             */
            return;
        }

        /* NPC got deleted, bring down the interface */
        cur_provider = zhash_table_lookup(gateway_config->npc_hash_by_gid, &(connector->gid), sizeof(connector->gid), NULL);
        if (cur_provider) {
            connector_event_handler_remove_connector(gateway_config, cur_provider, connector->gid);
        }
        return;
    }

    if (connector->state == CONNECTOR_STATE_ONLINE) {
        cur_provider = zhash_table_lookup(gateway_config->npc_hash_by_gid, &(connector->gid), sizeof(connector->gid), NULL);
        if (!cur_provider) {
            cur_provider = npwg_create_provider();
            if (!cur_provider) {
                NPGWD_LOG(AL_ERROR, "NP gateway provider creation failed for connector %"PRId64" ", connector->gid);
                return;
            }
            __sync_add_and_fetch_8(&(operate_stats.npc_provider_created), 1);

            connector_port = (uint16_t)connector->gateway_listener_port;
            npwg_provider_set_provider_meta(cur_provider, connector->gid);

            NPGWD_LOG(AL_NOTICE, "Bringing up NPC wg interface for connector %"PRId64" with port %d",connector->gid, connector_port);

            /* set interface config in memory */
            rc = npwg_set_interface_config(cur_provider,
                                           gateway_config->private_key,
                                           connector_port);
            if (rc) {
                NPGWD_LOG(AL_ERROR, "npwg_set_interface_config failed with %s", npgwd_result_string(rc));
                __sync_add_and_fetch_8(&(operate_stats.npc_set_intf_err), 1);
                npwg_destroy_provider(&cur_provider);
                return;
            }

            /* set peer config in memory */
            struct argo_inet  allowed_ip;
            argo_string_to_inet(NPWG_DEFAULT_ROUTE, &allowed_ip);
            rc = npwg_set_connector_peer(cur_provider,
                                         connector->public_key,
                                         &allowed_ip,
                                         1,
                                         false);
            if (rc) {
                NPGWD_LOG(AL_ERROR, "npwg_set_connector_peer failed with %s", npgwd_result_string(rc));
                __sync_add_and_fetch_8(&(operate_stats.npc_set_peer_err), 1);
                npwg_destroy_provider(&cur_provider);
                return;
            }

            rc = connector_event_handler_redundancy_start_wg(cur_provider, gateway_config->mtu?gateway_config->mtu:NPWG_DEFAULT_MTU);
            if (rc) {
                __sync_add_and_fetch_8(&(operate_stats.redundancy_start_wg_fail), 1);
                /* Fall Through */
            }

            rc = zhash_table_store(gateway_config->npc_hash_by_gid, &(connector->gid), sizeof(connector->gid), 0, cur_provider);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "store npc in cache failed %s", npgwd_result_string(rc));
                //fall through
            } else {
                __sync_add_and_fetch_8(&(operate_stats.npc_provider_add_hash), 1);
            }

            return;

        } else {
            /* wg setconf */
            char *interface_name = npwg_provider_return_interface_name(cur_provider);
            if (!interface_name) {
                interface_name = NPWG_INVALID_INTERFACE_NAME;
            }
            rc = npwg_provider_wg_setconf_nwpg_intf(cur_provider);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "Could not setconf for wg interface %s with err %s", interface_name, npgwd_result_string(rc));
                __sync_add_and_fetch_8(&(operate_stats.wg_setconf_err), 1);
                return;
            }
        }

        return;

    } else if (previous_row != NULL) {
        int16_t connector_previous_state =
            ((struct np_connectors *)previous_row->base_structure_void)->state;
        if (connector_previous_state == CONNECTOR_STATE_ONLINE) {
            cur_provider = zhash_table_lookup(gateway_config->npc_hash_by_gid, &(connector->gid), sizeof(connector->gid), NULL);
            if (cur_provider) {
                connector_event_handler_remove_connector(gateway_config, cur_provider, connector->gid);
            }
            return;
        }
    }
    return;

}

void connectors_event_handler(__attribute__((unused)) evutil_socket_t sock,
                              __attribute__((unused)) short flags,
                              void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *previous_row = event_param->previous_row;
    struct argo_object *row = event_param->row;
    struct np_connectors *connector = (struct np_connectors *)row->base_structure_void;
    int16_t connector_state = connector->state;

    /*
     * if gateway is running in redundancy mode, use the new handler for registering connector peer
     */
    if (gateway_config->redundancy_feature_enabled && gateway_config->redundant_mode_enabled) {
        if (connector->gateway_listener_port != NPWG_INVALID_PORT) {
                connector_event_handler_redundancy(gateway_config, previous_row, connector);
            free_gateway_event_param(&event_param);
        }
        return;
    }

    int rc = NPWG_RESULT_NO_ERROR;

    // if the peer connector was just deleted and was previously configured in the driver
    // note, double deletes are possible, npwg_lib wil handle it
    if (connector->deleted) {
        if (!previous_row) {
            free_gateway_event_param(&event_param);
            return;
        }

        int16_t connector_previous_state =
            ((struct np_connectors *)previous_row->base_structure_void)->state;
        if (connector_previous_state == CONNECTOR_STATE_ONLINE) {
            NPGWD_LOG(AL_NOTICE, "Deleting connector: %" PRId64, connector->gid);
            rc = npwg_delete_peer_config(gateway_config->provider, connector->public_key);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "could not delete connector peer config");
            }
        }
        free_gateway_event_param(&event_param);
        return;
    }

    // if the connector is not deleted and there was an update to it and the
    // current state is online or unhealthy (sic), we need to set the connector.
    if (connector_state == CONNECTOR_STATE_ONLINE) {
        NPGWD_LOG(AL_NOTICE, "Setting connector: %" PRId64, connector->gid);
        connector_set_helper(gateway_config, connector);
        free_gateway_event_param(&event_param);
        return;
    }

    // if there is an update to the connector and the previous state was online,
    // we need to delete the connector.
    if (previous_row) {
        int16_t connector_previous_state =
            ((struct np_connectors *)previous_row->base_structure_void)->state;
        if (connector_previous_state == CONNECTOR_STATE_ONLINE) {
            NPGWD_LOG(AL_NOTICE, "Deleting connector: %" PRId64, connector->gid);
            rc = npwg_delete_peer_config(gateway_config->provider, connector->public_key);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "could not delete connector peer config");
            }
            free_gateway_event_param(&event_param);
            return;
        }
    }

    NPGWD_LOG(AL_CRITICAL,
              "No appropriate action for connector peer, gid: %" PRId64,
              connector->gid);
    free_gateway_event_param(&event_param);
    return;
}

void connector_groups_event_handler(__attribute__((unused)) evutil_socket_t sock,
                                    __attribute__((unused)) short flags,
                                    void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *row = event_param->row;
    const struct np_connector_groups *connector_group =
        (struct np_connector_groups *)row->base_structure_void;

    if (gateway_config->redundancy_feature_enabled && gateway_config->redundant_mode_enabled) {
        /* gateway is running in redundancy mode, does not need connector group info */
        free_gateway_event_param(&event_param);
        return;
    }

    struct np_connectors *connector = NULL;
    size_t connector_count = 1;
    int rc = np_connectors_get_by_connector_group_gid_immediate(connector_group->gid,
                                                                &connector,
                                                                &connector_count);
    // Not a critical error. Most likely the connector has not been added yet.
    if (rc) {
        NPGWD_LOG(AL_INFO,
                  "Could not query for connector, given connector group: %" PRId64,
                  connector_group->gid);
        free_gateway_event_param(&event_param);
        return;
    }

    connector_set_helper(gateway_config, connector);
    free_gateway_event_param(&event_param);
}

void lan_subnets_event_handler(__attribute__((unused)) evutil_socket_t sock,
                               __attribute__((unused)) short flags,
                               void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *row = event_param->row;
    const struct np_lan_subnets *lan_subnet =
        (struct np_lan_subnets *)row->base_structure_void;

    if (gateway_config->redundancy_feature_enabled && gateway_config->redundant_mode_enabled) {
        //redundant mode does not need lan subnet info of connectors.
        free_gateway_event_param(&event_param);
        return;
    }

    struct np_connectors *connector = NULL;
    size_t connector_count = 1;
    int rc = np_connectors_get_by_connector_group_gid_immediate
                (lan_subnet->connector_group_gid,
                 &connector,
                 &connector_count);
    // Not a critical error. Possibly the connector has not been added yet.
    if (rc) {
        NPGWD_LOG(AL_INFO,
                  "Could not query for connector, given connector group: %" PRId64,
                  lan_subnet->connector_group_gid);
        free_gateway_event_param(&event_param);
        return;
    }

    connector_set_helper(gateway_config, connector);
    free_gateway_event_param(&event_param);
}

static inline void gateway_set_helper(const np_gateway_config_t *gateway_config,
                                      const struct np_tenant_gateways *tenant_gateway)
{
    struct np_client_subnets *subnets[NPWG_MAX_ALLOWED_IPS_PER_PEER];
    size_t subnet_count = NPWG_MAX_ALLOWED_IPS_PER_PEER;
    int rc = np_client_subnets_get_by_gateway_gid_immediate(tenant_gateway->gid,
                                                            (struct np_client_subnets **)
                                                            &subnets,
                                                            &subnet_count);
    // rc can be non-zero in two cases:
    // 1. subnets have not been added yet.
    // 2. subnets were soft deleted and we can no longer query for them.
    // It is critical to handle the later. We can have a false delete in the
    // former case. We will let the WG common library help us with the redundant
    // delete.
    if (rc) {
        NPGWD_LOG(AL_INFO,
                  "Subnets not found in Wally for gateway gid: %" PRId64,
                  tenant_gateway->gid);

        rc = npwg_delete_peer_config(gateway_config->provider, tenant_gateway->public_key);
        if (rc) {
            NPGWD_LOG(AL_CRITICAL,
                      "Could not delete gateway peer, gid: %" PRId64,
                      tenant_gateway->gid);
        }
        return;
    }

    static struct argo_inet remote_cidrs[NPWG_MAX_ALLOWED_IPS_PER_PEER];
    size_t remote_cidrs_count = 0;
    for (int ii = 0; ii < subnet_count; ++ii) {
        if (!subnets[ii]->subnet) {
            NPGWD_LOG(AL_NOTICE, "Subnet gid: %"PRId64" has NULL subnet, ignoring", subnets[ii]->gid);
            continue;
        }
        remote_cidrs[ii] = *subnets[ii]->subnet;
        remote_cidrs_count++;
    }

    NPGWD_LOG(AL_INFO,
              "Setting gateway with gid: %" PRId64 ", public_key: %s, subnet_count: %lu",
              tenant_gateway->gid,
              tenant_gateway->public_key,
              remote_cidrs_count);
    rc = npwg_set_gateway_peer(gateway_config->provider,
                               tenant_gateway->public_key,
                               tenant_gateway->listener_ip_address,
                               (uint16_t)tenant_gateway->listener_port,
                               remote_cidrs,
                               remote_cidrs_count);

    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Could not set gateway peer, gid: %" PRId64,
                  tenant_gateway->gid);
    }
}

/* Callback to set the configured MTU for connector-facing WireGuard interfaces.*/
static int configure_wireguard_interfaces_mtu_f(void *cookie,
                                                void *object,
                                                __attribute__((unused)) void *key,
                                                __attribute__((unused)) size_t key_len)
{
    np_gateway_config_t *gateway_config = (np_gateway_config_t *)cookie;
    npwg_provider provider = (npwg_provider)object;
    int rc = NPWG_RESULT_NO_ERROR;

    if (!gateway_config || !provider) {
        return rc;
    }

    rc = npwg_provider_set_mtu(provider, gateway_config->mtu);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "Wireguard set configured mtu failed %s, for interface %s",
                  npgwd_result_string(rc),npwg_provider_return_interface_name(provider));
    }

    return rc;
}

void tenant_gateways_event_handler(__attribute__((unused)) evutil_socket_t sock,
                                   __attribute__((unused)) short flags,
                                   void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *previous_row = event_param->previous_row;
    struct argo_object *row = event_param->row;
    const struct np_tenant_gateways *tenant_gateway =
        (struct np_tenant_gateways *)row->base_structure_void;

    int64_t tenant_gateway_gid = tenant_gateway->gid;
    int16_t tenant_gateway_state = tenant_gateway->gateway_state;

    int rc = NPGWD_RESULT_NO_ERROR;

    if (tenant_gateway_gid == gateway_config->gid) {

        zpn_npgateway_bgp_set_np_tenant_gateway_gid(tenant_gateway_gid);

        /* check redundant_mode_enabled value */
        int redundancy_mode_changed = 0;
        redundancy_mode_changed = gateway_config->redundant_mode_enabled != tenant_gateway->redundant_mode_enabled;
        if (redundancy_mode_changed) {
            gateway_config->redundant_mode_enabled = tenant_gateway->redundant_mode_enabled;
            if (gateway_config->redundant_mode_enabled && gateway_config->redundancy_feature_enabled) {
                /* redundancy mode is set to enabled and feature is also enabled, initializing bgp state (if not initialized) */
                NPGWD_LOG(AL_INFO, "Redundancy mode is set to enabled on np tenant gateway, initializing NP BGP state");
                zpn_npgateway_bgp_state_init(gateway_config->wally, gateway_config->customer_gid);
            } else if (!gateway_config->redundant_mode_enabled) {
                /* redundancy mode is set to disabled , destroy bgp state (if initialized) */
                NPGWD_LOG(AL_INFO, "Redundancy mode is set to disabled on on np tenant gateway, destroying NP BGP state");
                zpn_npgateway_bgp_state_destroy();
            } else {
                /* do nothing */
                NPGWD_LOG(AL_INFO, "Redundancy mode is set to enabled but feature is disabled, skip initializing BGP");
            }
        }

        /*
         * db value unset, gateway value not default     -> set gateway mtu to default
         * db value unset, gateway value set to default  -> do nothing
         * db value set,   gateway value set to db value -> do nothing
         * db value set,   gateway value unset           -> set gateway mtu to db value if within the range
         * else, set interface to default
         */
        if (!tenant_gateway->mtu && gateway_config->mtu != NPWG_DEFAULT_MTU) {
            rc = npwg_provider_set_mtu(NULL, NPWG_DEFAULT_MTU);
            if (rc) {
                NPGWD_LOG(AL_ERROR,
                          "Failed setting my gateway's gid: %" PRId64 " to default MTU %d",
                          gateway_config->gid, NPWG_DEFAULT_MTU);
                free_gateway_event_param(&event_param);
                return;
            }
            gateway_config->mtu = NPWG_DEFAULT_MTU;
        } else if ((tenant_gateway->mtu && (gateway_config->mtu == tenant_gateway->mtu)) ||
                   (!tenant_gateway->mtu && (gateway_config->mtu == NPWG_DEFAULT_MTU))) {
            NPGWD_LOG(AL_NOTICE,
                          "my gateway's gid: %" PRId64 " already have wireguard MTU set to %d",
                          gateway_config->gid, gateway_config->mtu);
            free_gateway_event_param(&event_param);
            return;
        } else if (tenant_gateway->mtu && tenant_gateway->mtu >= NPWG_MINIMUM_MTU && tenant_gateway->mtu <= NPWG_MAXIMUM_MTU) {
            rc = npwg_provider_set_mtu(NULL, tenant_gateway->mtu);
            if (rc) {
                NPGWD_LOG(AL_ERROR,
                          "Failed setting my gateway's gid: %" PRId64 " to MTU %d",
                          gateway_config->gid, tenant_gateway->mtu);
                free_gateway_event_param(&event_param);
                return;
            }
            gateway_config->mtu = tenant_gateway->mtu;
            rc = zhash_table_walk(gateway_config->npc_hash_by_gid, NULL, configure_wireguard_interfaces_mtu_f, gateway_config);
            if (rc) {
                return;
            }
        } else {
            rc = npwg_provider_set_mtu(NULL, NPWG_DEFAULT_MTU);
            if (rc) {
                NPGWD_LOG(AL_ERROR,
                          "Failed setting my gateway's gid: %" PRId64 " to default MTU %d",
                          gateway_config->gid, NPWG_DEFAULT_MTU);
                free_gateway_event_param(&event_param);
                return;
            }
            gateway_config->mtu = NPWG_DEFAULT_MTU;
        }

        NPGWD_LOG(AL_NOTICE,
                  "Set cur gateway's gid: %" PRId64 " wireguard interface MTU to %d",
                  gateway_config->gid, gateway_config->mtu);
        __sync_lock_test_and_set(&(operate_stats.mtu), gateway_config->mtu);

        free_gateway_event_param(&event_param);
        return;
    }

    NPGWD_LOG(AL_NOTICE,
              "In Wally, my gateway's gid: %" PRId64 ", this gateway's gid: %" PRId64,
              gateway_config->gid, tenant_gateway_gid);


    // if the peer gateway was just deleted and was previously configured in the driver
    if (tenant_gateway->deleted) {
        if (!previous_row) {
            free_gateway_event_param(&event_param);
            return;
        }

        int16_t tenant_gateway_previous_state =
            ((struct np_tenant_gateways *)previous_row->base_structure_void)->gateway_state;
        if (tenant_gateway_previous_state == NP_TENANT_GATEWAY_STATE_ONLINE ||
            tenant_gateway_previous_state == NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
            NPGWD_LOG(AL_NOTICE,
                        "Deleting tenant gateway: %" PRId64,
                        tenant_gateway->gid);
            rc = npwg_delete_peer_config(gateway_config->provider,
                                        tenant_gateway->public_key);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "could not delete tenant gateway peer config");
            }
        }
        free_gateway_event_param(&event_param);
        return;
    }

    // if the tenant gateway is not deleted and there was an update to it and the
    // current state is online or unhealthy (sic), we need to set the gateway.
    if (tenant_gateway_state == NP_TENANT_GATEWAY_STATE_ONLINE ||
        tenant_gateway_state == NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
        NPGWD_LOG(AL_NOTICE, "Setting tenant gateway: %" PRId64, tenant_gateway->gid);
        gateway_set_helper(gateway_config, tenant_gateway);
        free_gateway_event_param(&event_param);
        return;
    }

    // if there is an update to the tenant gateway and the previous state was online
    // or unhealthy (sic), we need to delete the gateway.
    if (previous_row) {
        int16_t tenant_gateway_previous_state =
            ((struct np_tenant_gateways *)previous_row->base_structure_void)->gateway_state;

        if (tenant_gateway_previous_state == NP_TENANT_GATEWAY_STATE_ONLINE ||
            tenant_gateway_previous_state == NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
            NPGWD_LOG(AL_NOTICE, "Deleting tenant gateway: %" PRId64, tenant_gateway->gid);
            rc = npwg_delete_peer_config(gateway_config->provider,
                                        tenant_gateway->public_key);
            if (rc) {
                NPGWD_LOG(AL_CRITICAL, "could not delete tenant gateway peer config");
            }
            free_gateway_event_param(&event_param);
            return;
        }
    }

    NPGWD_LOG(AL_CRITICAL, "No appropriate action for gateway peer, gid: %" PRId64,
              tenant_gateway_gid);
    free_gateway_event_param(&event_param);
    return;
}

void client_subnets_event_handler(__attribute__((unused)) evutil_socket_t sock,
                                  __attribute__((unused)) short flags,
                                  void *cookie)
{
    np_gateway_event_param_t *event_param = cookie;
    const np_gateway_config_t *gateway_config = event_param->gateway_config;
    struct argo_object *row = event_param->row;
    const struct np_client_subnets *client_subnet =
        (struct np_client_subnets *)row->base_structure_void;

    if (client_subnet->gateway_gid == gateway_config->gid) {
        if (gateway_config->redundancy_feature_enabled && gateway_config->redundant_mode_enabled) {
            /* NP Redundancy feature enabled and we are running in redundancy mode */
            struct argo_object *previous_row_copy = NULL;
            if (event_param->previous_row) previous_row_copy = argo_object_copy(event_param->previous_row);
            struct argo_object *row_copy = argo_object_copy(event_param->row);
            if (zpn_npgateway_bgp_client_subnet_handler(previous_row_copy, row_copy)) {
                NPGWD_LOG(AL_ERROR, "Unable to make thread call to bgp thread for handling client subnet row callback");
                if (previous_row_copy) argo_object_release(previous_row_copy);
                if (row_copy) argo_object_release(row_copy);
            }
            /* np_connector_bgp_client_subnet_handler will free the row copies */
        }
        // if we are not running in redundant mode, ignore if this client subnet is this tenant gateway's client subnet
        free_gateway_event_param(&event_param);
        return;
    }

    // look up the peer gateway
    struct np_tenant_gateways *tenant_gateway = NULL;
    int rc = np_tenant_gateways_get_by_id_immediate(client_subnet->gateway_gid,
                                                    &tenant_gateway);

    // This is an error, the gateways are fully loaded
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Gateway with gid: %" PRId64 " not found in Wally.",
                  client_subnet->gateway_gid);
        free_gateway_event_param(&event_param);
        return;
    }

    // Ignore gateways that are not currently { ONLINE or UNHEALTHY}
    int16_t tenant_gateway_state = tenant_gateway->gateway_state;
    if (tenant_gateway_state != NP_TENANT_GATEWAY_STATE_ONLINE &&
        tenant_gateway_state != NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
        NPGWD_LOG(AL_INFO,
                  "Ignoring tenant gateway with gid: %" PRId64 ". It has state: %u",
                  tenant_gateway->gid,
                  tenant_gateway_state);
        return;
    }

    gateway_set_helper(gateway_config, tenant_gateway);

    free_gateway_event_param(&event_param);
}


static void np_operator_update_driver(__attribute__((unused)) evutil_socket_t sock,
                                      __attribute__((unused)) short flags,
                                      void *cookie)
{
    struct zthread_info *zthread_arg = cookie;
    np_gateway_config_t *gateway_config = zthread_arg->user_void;
    int rc = npwg_update_wireguard(gateway_config->provider, true);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not update driver.");
    }
}

static void np_operator_heartbeat_generator(__attribute__((unused)) evutil_socket_t sock,
                                            __attribute__((unused)) short flags,
                                            void *cookie)
{
    struct zthread_info *zthread_arg = cookie;
    zthread_heartbeat(zthread_arg);
}

static int schedule_driver_event(struct event_base *operator_event_base,
                                 struct zthread_info *zthread_arg)
{
    struct event *operator_driver_event = event_new(operator_event_base,
                                                    -1,            // fd unused
                                                    EV_PERSIST,
                                                    np_operator_update_driver,
                                                    zthread_arg);

    if (!operator_driver_event) {
        NPGWD_LOG(AL_CRITICAL, "event_new() failed!");
        return NPWG_RESULT_ERR;
    }

    struct timeval tv = {.tv_sec = DRIVER_UPDATE_INTERVAL_SECONDS, .tv_usec = 0};

    if (0 != event_add(operator_driver_event, &tv)) {
        NPGWD_LOG(AL_CRITICAL, "event_add() failed.");
        return NPWG_RESULT_ERR;
    }

    return NPWG_RESULT_NO_ERROR;
}

static int schedule_heartbeat_event(struct event_base *operator_event_base,
                                    struct zthread_info *zthread_arg)
{

    struct event *operator_heartbeat_event = event_new(operator_event_base,
                                                       -1,            // fd unused
                                                       EV_PERSIST,
                                                       np_operator_heartbeat_generator,
                                                       zthread_arg);

    if (!operator_heartbeat_event) {
        NPGWD_LOG(AL_CRITICAL, "event_new() failed!");
        return NPWG_RESULT_ERR;
    }

    event_priority_set(operator_heartbeat_event, 0);

    struct timeval tv = {.tv_sec = HEARTBEAT_INTERVAL_SECONDS, .tv_usec = 0};

    if (0 != event_add(operator_heartbeat_event, &tv)) {
        NPGWD_LOG(AL_CRITICAL, "event_add() failed.");
        return NPWG_RESULT_ERR;
    }

    return NPWG_RESULT_NO_ERROR;
}

static void *np_operator_thread_f(struct zthread_info *zthread_arg,
                                  __attribute__((unused)) void *cookie)
{
    NPGWD_LOG(AL_INFO, "in np_operator_thread_f()");
    const np_gateway_config_t *gateway_config = zthread_arg->user_void;

    struct event_base *operator_event_base = gateway_config->operator_event_base;

    if (NPWG_RESULT_NO_ERROR != schedule_heartbeat_event(operator_event_base, zthread_arg))
        return NULL;

    if (NPWG_RESULT_NO_ERROR != schedule_driver_event(operator_event_base, zthread_arg))
        return NULL;

    zevent_base_dispatch(operator_event_base);

    // should never get here
    return NULL;
}

int np_gateway_operator_event_init(np_gateway_config_t *gateway_config)
{
    struct event_base *operator_event_base = event_base_new();
    if (!operator_event_base) {
        NPGWD_LOG(AL_CRITICAL, "event_base_new() failed!");
        return NPGWD_RESULT_NO_MEMORY;
    }
    gateway_config->operator_event_base = operator_event_base;

    return NPGWD_RESULT_NO_ERROR;
}

static int
np_gateway_dump_opereate_stats(struct zpath_debug_state*             request_state,
                               __attribute__((unused))const char**   query_values,
                               __attribute__((unused))int            query_value_count,
                               __attribute__((unused))void*          cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_npgateway_common_stats_description,
                                                    &operate_stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return NPGWD_RESULT_NO_ERROR;
}


int np_gateway_operator_thread_init(np_gateway_config_t *gateway_config)
{
    int rc = npwg_provider_log_init(zpath_event_collection);

    if (rc) {
        NPGWD_LOG(AL_ERROR,
                  "Initialization of NP Common library logging failed: %s",
                  npgwd_result_string(rc));
        return NPGWD_RESULT_ERR;
    }

    gateway_config->npc_hash_by_gid = zhash_table_alloc(&np_gateway_allocator);
    if (!gateway_config->npc_hash_by_gid) {
        NPGWD_LOG(AL_ERROR,
                  "NP Gateway npc_hash_by_gid creation failed: %s",
                  npgwd_result_string(rc));
        return NPGWD_RESULT_ERR;
    }

    rc = zpath_debug_add_read_command("Dump gateway operate stats",
                                     "/npgateway/operate_stats", np_gateway_dump_opereate_stats,
                                     NULL, NULL);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "Couldn't add /npgateway/operate_stats");
        return NPGWD_RESULT_ERR;
    }

    gateway_config->provider = npwg_create_provider();
    if (!gateway_config->provider) {
        NPGWD_LOG(AL_ERROR,
                  "NP Gateway provider creation failed: %s",
                  npgwd_result_string(rc));
        return NPGWD_RESULT_ERR;
    }

    // update interface config
    rc = npwg_set_interface_config(gateway_config->provider,
                                   gateway_config->private_key,
                                   gateway_config->listener_port);
    if (rc) {
        NPGWD_LOG(AL_ERROR,
                  "npwg_set_interface_config() failed: %s",
                  npgwd_result_string(rc));
        return rc;
    }

    // preemptively stop the driver if it is already running
    rc = npwg_stop_wireguard(gateway_config->provider);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Wireguard driver stop failed: %s",
                  npgwd_result_string(rc));
        return rc;
    }

    rc = npwg_start_wireguard(gateway_config->provider);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Wireguard driver start failed: %s",
                  npgwd_result_string(rc));
        return rc;
    }

    rc = npwg_wireguard_vrf(gateway_config->provider);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Wireguard VRF creation failed: %s",
                  npgwd_result_string(rc));
        return rc;
    }
    rc = zthread_create(&gateway_config->operator_thread,
                        np_operator_thread_f,
                        NULL,
                        "operator_thread",
                        NPGATEWAY_MONITOR_HEARTBEAT_TIMEOUT_S, // heartbeat timeout in seconds
                        NPGATEWAY_MONITOR_THREAD_STACK_SIZE, // stack size in bytes
                        NPGATEWAY_MONITOR_THREAD_STATS_INTERVAL_US, // stats monitoring interval
                        gateway_config);

    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "NP operator thread creation failed: %s",
                  npgwd_result_string(rc));
        return rc;
    }

    return NPGWD_RESULT_NO_ERROR;
}
