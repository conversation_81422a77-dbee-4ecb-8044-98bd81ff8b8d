/*
 * zpn_npgateway_bgp.c . Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "argo/argo.h"
#include "zthread/zthread.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_bgp_gateway_session_config.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_frr_utils.h"

#include "zpn_npgatewayd/zpn_npgateway_bgp.h"
#include "zpn_npgatewayd/zpn_npgateway_alloc.h"
#include "zpn_npgatewayd/zpn_npgateway_common.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"

#ifdef __linux__
#include <net/if.h>
#include <sys/ioctl.h>
#endif

struct zpn_npgateway_bgp_state {
    int64_t gateway_gid;
    int64_t customer_gid;
    struct wally *wally;

    struct np_bgp_state *bgp_state;

    int64_t init_us;

    uint8_t redundancy_feature_enabled:1, // feature flag
            redundant_mode_enabled:1; // np tenant gateway level config

};
static struct zpn_npgateway_bgp_state state;


static inline int dump_peer_config(void *cookie,
                                   void *value,
                                   void *key __attribute__((unused)),
                                   size_t key_len __attribute__((unused)))
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie;
    struct argo_object *object = (struct argo_object *)value;
    if (!object) return NPGWD_RESULT_NO_ERROR;
    struct np_bgp_peer_config *peer = object->base_structure_void;
    if (!peer) return NPGWD_RESULT_NO_ERROR;
    char jsonout[1000];
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_peer_config_description, peer, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("peer config\n%s\n", jsonout);
    }
    return NPGWD_RESULT_NO_ERROR;
}

static inline void dump_rows(struct argo_object *prev_row, struct argo_object *curr_row, const char *caller)
{
    char dump[1024] = {0};
    if (prev_row && (argo_object_dump(prev_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR)) {
        NPGWD_LOG(AL_INFO, "%s Previous row: %s", caller, dump);
    }

    if (argo_object_dump(curr_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        NPGWD_LOG(AL_INFO, "%s Current row: %s", caller, dump);
    }
}

static int
np_bgp_dump_state(struct zpath_debug_state  *request_state,
                  const char                **query_values __attribute__((unused)),
                  int                       query_value_count __attribute__((unused)),
                  void                      *cookie __attribute__((unused)))
{
    ZDP("np tenant gateway gid: %"PRId64"\n", state.gateway_gid);
    ZDP("customer_gid: %"PRId64"\n", state.customer_gid);

    if (state.bgp_state) {
        if (state.bgp_state->deletion_in_progress) {
            ZDP("np bgp state is being deleted\n");
        } else {
            ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
            ZDP("\tfrr_version: %s\n", state.bgp_state->frr_version ? state.bgp_state->frr_version : "unknown");
            ZDP("\trouter_id: %s\n", state.bgp_state->router_id ? state.bgp_state->router_id : "null");
            ZDP("\tasn: %"PRId64"\n", state.bgp_state->asn);
            ZDP("\tconfig_mode: %d\n", state.bgp_state->config_mode);
            ZDP("\toverride_config: %s\n", state.bgp_state->override_config ? state.bgp_state->override_config : "null");
            ZDP("\tforce_reload_config: %d\n", state.bgp_state->force_reload_config);
            ZDP("\tstored client_subnets_count: %"PRId64"\n", state.bgp_state->client_subnets_count);
            ZDP("\tstored peer_connectors_count: %"PRId64"\n", state.bgp_state->peer_connectors_count);
            ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        }
    } else {
        ZDP("np bgp state is NULL\n");
    }
    ZDP("init_us: %"PRId64"\n", state.init_us);
    ZDP("redundancy_feature_enabled: %s\n", state.redundancy_feature_enabled ? "yes" : "no");
    ZDP("redundant_mode_enabled: %s\n", state.redundant_mode_enabled ? "yes" : "no");
    return NPGWD_RESULT_NO_ERROR;
}

static int
np_bgp_dump_peers(struct zpath_debug_state  *request_state,
                  const char                **query_values __attribute__((unused)),
                  int                       query_value_count __attribute__((unused)),
                  void                      *cookie __attribute__((unused)))
{
    if (state.bgp_state) {
        if (state.bgp_state->deletion_in_progress) {
            ZDP("np bgp state is being deleted\n");
        } else {
            ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
            ZDP("Connector peers\n");
            zhash_table_walk(state.bgp_state->peer_connectors, NULL, dump_peer_config, request_state);
            ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        }
    } else {
        ZDP("np bgp state is NULL\n");
    }
    return NPGWD_RESULT_NO_ERROR;
}

static void
bgp_config_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                         void *void_cookie,
                                         int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct np_bgp_gateways_config *bgp_gw = (struct np_bgp_gateways_config *)row->base_structure_void;

    if (!bgp_gw || bgp_gw->gateway_gid != state.gateway_gid) {
        goto done;
    }

    if (bgp_gw->deleted) {
        NPGWD_LOG(AL_WARNING, "Gateway BGP config is deleted, need re-provisioning. Destroying np gateway bgp state");
        zpn_npgateway_bgp_state_destroy();
        /* BGP config is deleted, we're supposed to clean up wireguard peers but for now we can leave it as is to avoid
         * adding extra complexity in dynamic BGP state management
         * if something bad happens, either feature should be disabled, or gateway itself should be deprovisioned */
        goto done;
    }

    int res = np_bgp_update_instance_config(state.bgp_state, bgp_gw->gid, bgp_gw->asn, bgp_gw->router_id,
                                bgp_gw->config_mode, bgp_gw->override_config, bgp_gw->force_reload_config);
    if (res) {
        NPGWD_LOG(AL_ERROR, "Unable to update bgp instance config: %s", zpath_result_string(res));
    }

done:
    argo_object_release(row);
    return;

}

static int
zpn_npgateway_bgp_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                           struct wally_registrant *registrant __attribute__((unused)),
                                           struct wally_table      *table __attribute__((unused)),
                                           struct argo_object      *previous_row,
                                           struct argo_object      *row,
                                           int64_t                 request_id __attribute__((unused)))
{
    if (npgwd_debug_log & NPGWD_DEBUG_REDUNDANCY_IDX) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NPGWD_LOG(AL_NOTICE, "ignoring np_bgp_gateways_config row callback as feature is disabled");
        return NPGWD_RESULT_NO_ERROR;
    }

    struct np_bgp_gateways_config *bgp_gw = (struct np_bgp_gateways_config *)row->base_structure_void;
    if (!bgp_gw) {
        return NPGWD_RESULT_NO_ERROR;
    }

    if (bgp_gw->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(state.bgp_state->zevent_base, bgp_config_table_row_callback_bgp_thread, row, 0)) {
        NPGWD_LOG(AL_WARNING, "Failed to make thread call to bgp_config_table_row_callback_bgp_thread");
        argo_object_release(row);
    }
    return NPGWD_RESULT_NO_ERROR;
}

/* Gateway's BGP peer is always connector */
static void
bgp_session_config_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                                 void *void_cookie,
                                                 int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;

    struct np_bgp_gateway_session_config *new_session_config = (struct np_bgp_gateway_session_config *)row->base_structure_void;
    if (!new_session_config) goto done;

    if (new_session_config->bgp_config_gid != state.bgp_state->bgp_config_gid) goto done;

    /*
     * the row will be created/deleted if the neighbor has been created/deleted
     * the row will be updated if the session config is updated
     * - incl. the bgp session parameters, and the neighbor ASN, IP info.
     * - but the neighbor_config_gid, neighbor_instance_type will not be updated
     *
     */
    if (new_session_config->deleted) {
        /* session config has been deleted, delete the corresponding in memory peer config */
        np_bgp_gateway_delete_peer_config(state.bgp_state, new_session_config);
        /* previous row is obsolete, ignore processing it */
    } else {
        struct np_bgp_connectors_config *conn_config = NULL;
        int res = np_bgp_connectors_config_get_by_gid_immediate(new_session_config->neighbor_config_gid, &conn_config);
        if (!conn_config) {
            /* received session config row callback but peer connector's config has already been deleted? this should never happen */
            NPGWD_LOG(AL_WARNING, "Trying to update peer (connector) session config but connector's BGP config gid %"PRId64" is not found: %s",
                                new_session_config->neighbor_config_gid, zpath_result_string(res));
            goto done;
        }
        /* pull out connector's wireguard interface */

#ifdef __linux__
        char interface[IFNAMSIZ];
#else
        char interface[16];
#endif
        npwg_provider_redundancy_wg_interface_name(conn_config->connector_gid, &(interface[0]), sizeof(interface));

        /* flush out and replace
         * TODO: optimize this routine */
        np_bgp_gateway_add_or_update_peer_config(state.bgp_state, new_session_config, &(interface[0]));
    }

done:
    argo_object_release(row);
    return;
}

static int
zpn_npgateway_bgp_session_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                                    struct wally_registrant *registrant __attribute__((unused)),
                                                    struct wally_table      *table __attribute__((unused)),
                                                    struct argo_object      *previous_row,
                                                    struct argo_object      *row,
                                                    int64_t                 request_id __attribute__((unused)))
{
    if (npgwd_debug_log & NPGWD_DEBUG_REDUNDANCY_IDX) {
       dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NPGWD_LOG(AL_NOTICE, "ignoring np_bgp_gateway_session_config row callback as feature is disabled");
        return NPGWD_RESULT_NO_ERROR;
    }

    struct np_bgp_gateway_session_config *session_config = (struct np_bgp_gateway_session_config *)row->base_structure_void;
    if (!session_config) {
        return NPGWD_RESULT_NO_ERROR;
    }

    if (session_config->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(state.bgp_state->zevent_base, bgp_session_config_table_row_callback_bgp_thread, row, 0)) {
         NPGWD_LOG(AL_ERROR, "Failed to call bgp_session_config_table_row_callback on NP BGP thread");
        argo_object_release(row);
    }
    return NPGWD_RESULT_NO_ERROR;
}

static void
bgp_connector_config_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                                   void *void_cookie,
                                                   int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct np_bgp_connectors_config *bgp_conn = (struct np_bgp_connectors_config *)row->base_structure_void;
    int64_t bgp_config_gid;
    int found = 0;

    if (!bgp_conn || bgp_conn->deleted) goto done;

    if (!state.bgp_state || state.bgp_state->deletion_in_progress) goto done;
    ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
    bgp_config_gid = state.bgp_state->bgp_config_gid;
    ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);

    struct np_bgp_gateway_session_config *session_config[NP_BGP_MAX_PEERS];
    size_t session_config_count = NP_BGP_MAX_PEERS;
    int res = np_bgp_gateway_session_config_get_by_bgp_config_gid_immediate(bgp_config_gid, session_config, &session_config_count);
    if (res) {
        NPGWD_LOG(AL_ERROR, "couldn't query np_bgp_gateway_session_config table by bgp_config_gid %"PRId64": %s",
                            bgp_config_gid, zpath_result_string(res));
        goto done;
    }

    for (int i = 0; i < session_config_count; i++) {
        if (session_config[i]->neighbor_config_gid == bgp_conn->gid) {
            /* We found session config for this connector's bgp config gid, update peer config in hash */
#ifdef __linux__
            char interface[IFNAMSIZ];
#else
            char interface[16];
#endif
            npwg_provider_redundancy_wg_interface_name(bgp_conn->connector_gid, &(interface[0]), sizeof(interface));

            /* Flush out and replace
             * TODO: optimize this routine */
            np_bgp_gateway_add_or_update_peer_config(state.bgp_state, session_config[i], &(interface[0]));
            found = 1;
            break;
        }
    }

    if (!found) {
        NPGWD_LOG(AL_INFO, "%s Trying to update peer (connector) session config but no matching neighbor_config_gid with connector's BGP config gid %"PRId64"",
                                __FUNCTION__, bgp_conn->gid);
    }

done:
    argo_object_release(row);
    return;

}

static int
zpn_npgateway_bgp_connector_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                                      struct wally_registrant *registrant __attribute__((unused)),
                                                      struct wally_table      *table __attribute__((unused)),
                                                      struct argo_object      *previous_row,
                                                      struct argo_object      *row,
                                                      int64_t                 request_id __attribute__((unused)))
{
    if (npgwd_debug_log & NPGWD_DEBUG_REDUNDANCY_IDX) {
       dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NPGWD_LOG(AL_NOTICE, "ignoring np_bgp_connectors_config row callback as feature is disabled");
        return NPGWD_RESULT_NO_ERROR;
    }

    struct np_bgp_connectors_config *bgp_conn = (struct np_bgp_connectors_config *)row->base_structure_void;
    if (!bgp_conn) {
        return NPGWD_RESULT_NO_ERROR;
    }

    if (bgp_conn->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(state.bgp_state->zevent_base, bgp_connector_config_table_row_callback_bgp_thread, row, 0)) {
        NPGWD_LOG(AL_WARNING, "Failed to make thread call to bgp_connector_config_table_row_callback_bgp_thread");
        argo_object_release(row);
    }
    return NPGWD_RESULT_NO_ERROR;
}

/*
 * Load NP Redundancy tables
 * This routine will block until all the rows are loaded, will only execute once
 * bgp_state is assumed to be initialized already
 */
static void
load_bgp_tables_on_bgp_thread(struct zevent_base *base __attribute__((unused)),
                              void               *void_cookie __attribute__((unused)),
                              int64_t            int_cookie __attribute__((unused)))
{
    static int tables_initialized = 0;
    struct np_bgp_gateways_config *bgp_gw = NULL;

    if (!tables_initialized) {
        int res = NPGWD_RESULT_NO_ERROR;

        res = np_bgp_gateways_config_table_init(state.wally,
                                                state.customer_gid,
                                                zpn_npgateway_bgp_config_table_row_callback,
                                                NULL,
                                                0);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Cannot init table np_bgp_gateways_config: %s", zpath_result_string(res));
            return;
        }

        /* Do not proceed further if np config conn is not up, because the following table init calls will block
         * on waiting for wally rows and eventually will crash due to heartbeat timeout */
        int not_ready_count = 0;
        do {
            res = np_bgp_gateways_config_get_by_gateway_gid(state.gateway_gid, &bgp_gw, NULL, NULL, 0);
            if (res) {
                if ((not_ready_count++ % 10) == 0) {
                    if (res == NPGWD_RESULT_NOT_READY) {
                        NPGWD_LOG(AL_NOTICE, "NP gateway not yet connected to NP config channel, please check connectivity");
                    } else {
                        if (res != NPGWD_RESULT_ASYNCHRONOUS) {
                            NPGWD_LOG(AL_ERROR, "Get gateway BGP config returned %s, please check BGP config", zpath_result_string(res));
                        } else {
                            NPGWD_LOG(AL_NOTICE, "Waiting for NP gateway to retrieve BGP configuration");
                        }
                    }
                }
                sleep(1);
            }
            zthread_heartbeat(NULL);
        } while (res);

        NPGWD_LOG(AL_INFO, "Initialized np_bgp_gateways_config table");

        /* Fully load np_bgp_connectors_config customer table
         * gateway needs to lookup connector gid by its bgp config gid for getting the wireguard interface name */
        res = np_bgp_connectors_config_table_init(state.wally,
                                                  state.customer_gid,
                                                  zpn_npgateway_bgp_connector_config_table_row_callback,
                                                  NULL,
                                                  1/*single tenant fully load*/);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Cannot init table np_bgp_connectors_config: %s", zpath_result_string(res));
            return;
        }

        NPGWD_LOG(AL_INFO, "Initialized np_bgp_connectors_config table");

        /* This table can be huge, so load the table by gateway's BGP config gid instead of fully load customer table */
        res = np_bgp_gateway_session_config_table_init(state.wally,
                                                       state.customer_gid,
                                                       zpn_npgateway_bgp_session_config_table_row_callback,
                                                       NULL,
                                                       0/*do not fully load customer table*/);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Cannot init table np_bgp_gateway_session_config: %s", zpath_result_string(res));
            return;
        }

        if (bgp_gw) {
            /* load rows by gateway's BGP config gid */
            res = np_bgp_gateway_session_config_register_by_bgp_config_gid(bgp_gw->gid);
            if (res) {
                NPGWD_LOG(AL_ERROR, "Cannot register table np_bgp_gateway_session_config by bgp_config_gid %"PRId64": %s",
                                            bgp_gw->gid, zpath_result_string(res));
                return;
            }
        } else {
            NP_LOG(AL_ERROR, "bgp config is not found for gateway bgp config gid:%"PRId64", unable to load remaining tables", bgp_gw->gid);
            return;
        }

        NPGWD_LOG(AL_INFO, "Initialized np_bgp_gateway_session_config table");
        zthread_heartbeat(NULL);
    }

    tables_initialized = 1;
    return;
}

void
zpn_npgateway_bgp_load_config()
{
    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        NPGWD_LOG(AL_ERROR, "Failed to load NP BGP configs as bgp state is not initialized");
        return;
    }
    if (0 != zevent_base_call(state.bgp_state->zevent_base, load_bgp_tables_on_bgp_thread, NULL, 0)) {
        NPGWD_LOG(AL_ERROR, "Failed to call zpn_npgateway_bgp_load_config on NP BGP thread");
    }
    return;
}

/*
 * Callback from np feature thread NP_FEATURE_TOGGLE_THREAD
 * Restart if redundancy mode is enabled but feature flag flips during runtime
 */
void np_gateway_redundancy_feature_status_toggled(int64_t customer_gid __attribute__((unused)), int enabled)
{
    static int seen_config = 0;

    state.redundancy_feature_enabled = enabled;
    NPGWD_LOG(AL_NOTICE, "NP redundancy feature changed to %s", enabled ? "Enabled" : "Disabled");

    if (enabled) {
        /* NP redundancy feature changed from disabled to enabled */
        if (!seen_config) {
            seen_config = 1;
            /* ignore first time feature toggle as this happens during initialization
             * BGP module init is done in np_tenant_gateways row callback */
        } else {
            if (state.redundant_mode_enabled) {
                /* if this toggle happens during runtime, let's restart to ensure we initialize everything correctly,
                * otherwise, we might stuck in an inconsistent state */
                NPGWD_LOG(AL_NOTICE, "NP redundancy feature is enabled during run time, restarting...");
                sleep(2);
                exit(0);
            }
        }
    } else {
        if (!seen_config) {
            seen_config = 1;
            /* ignore first time feature toggle as this happens during initialization */
        } else {
            if (state.redundant_mode_enabled) {
                /* NP redundancy feature changed from enabled to disabled */
                NPGWD_LOG(AL_NOTICE, "NP redundancy feature is disabled during run time, restarting...");
                sleep(2);
                exit(0);
            }
        }
    }
    return;
}

static void
client_subnets_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                   void               *void_cookie,
                                   int64_t            int_cookie __attribute__((unused)),
                                   void               *extra_cookie1,
                                   void               *extra_cookie2 __attribute__((unused)),
                                   void               *extra_cookie3 __attribute__((unused)),
                                   int64_t            extra_int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct argo_object* previous_row = extra_cookie1;
    struct np_client_subnets *subnet = (struct np_client_subnets *)row->base_structure_void;
    struct np_client_subnets *prev_subnet = previous_row ? (struct np_client_subnets *)previous_row->base_structure_void : NULL;
    char dump[ARGO_INET_ADDRSTRLEN];

    if (npgwd_debug_log & NPGWD_DEBUG_REDUNDANCY_IDX) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!subnet || !subnet->subnet) goto done;

    if (subnet->deleted) {
        NP_LOG(AL_NOTICE, "Deleting client subnet: %s from in memory hash", argo_inet_generate(dump, subnet->subnet));
        np_bgp_delete_subnet_config(state.bgp_state, subnet->subnet, 0);
        goto done;
    }

    if (prev_subnet && prev_subnet->subnet) {
        /* client subnet has been updated, check if it belongs to us */
        if (!argo_inet_is_same(prev_subnet->subnet, subnet->subnet)) {
            char dump2[ARGO_INET_ADDRSTRLEN];
            NP_LOG(AL_NOTICE, "client subnet gid %"PRId64" updated from %s to %s, updating in memory hash",
                                subnet->gid,
                                argo_inet_generate(dump, prev_subnet->subnet),
                                argo_inet_generate(dump2, subnet->subnet));
            np_bgp_delete_subnet_config(state.bgp_state, prev_subnet->subnet, 0);
            np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 0);
        }
    } else {
        /* new client subnet */
        NP_LOG(AL_NOTICE, "Adding client subnet: %s to in memory hash", argo_inet_generate(dump, subnet->subnet));
        np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 0);
    }

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
    return;
}

int np_bgp_gateway_reload_frr_conf(int force_regenerate) {
    struct np_bgp_state *bgp_state = NULL;
    bgp_state = state.bgp_state;

    if (!bgp_state || bgp_state->deletion_in_progress) {
        return NPGWD_RESULT_NOT_READY;
    }

    return np_bgp_reload_frr_conf(bgp_state, np_bgp_instance_gateway, force_regenerate);
}

int zpn_npgateway_bgp_client_subnet_handler(struct argo_object *previous_row, struct argo_object *row)
{
    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
        return NPGWD_RESULT_NOT_READY;
    }

    if (0 != zevent_base_big_call(state.bgp_state->zevent_base, client_subnets_callback_bgp_thread,
                                    row, 0, previous_row, NULL, NULL, 0)) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
        return NPGWD_RESULT_ERR;
    }

    return NPGWD_RESULT_NO_ERROR;
}

int np_gateway_get_bgp_config_mode() {
    struct np_bgp_state *bgp_state = NULL;
    int bgp_config_mode = -1;

    bgp_state = state.bgp_state;

    if (bgp_state) {
        ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        bgp_config_mode = np_bgp_get_config_mode(bgp_state);
        ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
    }

    return bgp_config_mode;
}

void zpn_npgateway_bgp_set_np_tenant_gateway_gid(int64_t gateway_gid)
{
    state.gateway_gid = gateway_gid;
}

/*
 * Called from NP thread to initialize BGP during feature/mode enablement events
 */
void
zpn_npgateway_bgp_state_init(struct wally *wally, int64_t customer_gid)
{
    state.wally = wally;
    state.customer_gid = customer_gid;

    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        state.bgp_state = np_bgp_state_create(state.customer_gid, np_bgp_instance_gateway);
        if (!state.bgp_state) {
            NPGWD_LOG(AL_CRITICAL, "Failed to create np bgp state!");
        } else {
            NPGWD_LOG(AL_NOTICE, "Initialized NP BGP state");
        }
        state.init_us = epoch_us();
    } else {
        NPGWD_LOG(AL_NOTICE, "NP BGP state is already initialized");
    }

    char buf[256];
    if (np_frr_start(buf, sizeof(buf))) {
        NP_LOG(AL_ERROR, "Failed to start FRR: %s", buf);
    }

    if (npwg_provider_setup_interface_dummy(1/*is_gateway*/)) {
        NP_LOG(AL_ERROR, "Failed to setup dummy interface");
    }

    /* Load BGP related wally tables */
    zpn_npgateway_bgp_load_config();

    return;
}

int np_gateway_get_bgp_peer_info_from_neighbor_ip(const char *neighbor_ip, int64_t neighbor_asn, int64_t *peer_gid, int *peer_type) {
    struct np_bgp_state *bgp_state = NULL;
    int res = 0;
    bgp_state = state.bgp_state;

    if (bgp_state) {
        ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        res = np_bgp_peer_ip_to_instance_gid_lookup(state.bgp_state, neighbor_ip,
                     neighbor_asn, peer_gid, peer_type);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Error fetching instance gid from neighbor ip:%s error:%s",
                                 neighbor_ip, zpath_result_string(res));
        }
        ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
    }

    return res;
}

/*
 * Called from NP thread to initialize BGP during feature/mode enablement events
 */
void
zpn_npgateway_bgp_state_destroy()
{
    int res;

    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        NPGWD_LOG(AL_NOTICE, "BGP state is uninitialized or already deleting, skipping destroy attempt");
        return;
    }
    res = np_bgp_state_destroy(state.bgp_state);
    if (res) {
        NPGWD_LOG(AL_CRITICAL, "Failed to destroy np bgp state!");
    } else {
        NPGWD_LOG(AL_NOTICE, "NP BGP state destroyed");
    }
    state.bgp_state = NULL;

    return;
}

int
zpn_npgateway_bgp_init()
{
    int res;

    npgwd_debug_log = (NPGWD_DEBUG_REDUNDANCY_IDX) |
                      0;

    res = zpath_debug_add_read_command("dump the NP gateway's BGP state",
                                       "/np/bgp/dump_state",
                                       np_bgp_dump_state,
                                       NULL,
                                       NULL);
    if (res){
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_state");
        return res;
    }

    res = zpath_debug_add_read_command("dump the NP gateway's BGP peer config (currently stored in memory)",
                                       "/np/bgp/dump_peers",
                                       np_bgp_dump_peers,
                                       NULL,
                                       NULL);
    if (res){
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_peers");
        return res;
    }

    return NPGWD_RESULT_NO_ERROR;
}
