/*
 * zpn_npgateway_config_override_desc.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 *
 * ZPN NP Gateway's config override description registration.
 */

#include "zpn_npgatewayd/zpn_npgateway_config_override_desc.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"


static struct zpath_config_override_desc zpn_npgateway_config_override_descriptions[] = {
        {
                .key                = NETWORK_PRESENCE_BGP_STATS_ENABLE,
                .desc               = "enable np bgp stats monitoring",
                .details            = "0: np bgp stats monitoring is not enabled\n"
                                      "1: np bgp stats monitoring is enabled\n"
                                      "Order of check: instance_gid, customer_gid, global\n"
                                      "default: 1",
                .val_type           = config_type_int,
                .component_types    = config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_BGP_STATS_ENABLE_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = GATEWAY_ADMIN_PROBE_FEATURE_FRR_CMDS,
                .desc               = "enable or disable FRR commands support through NP admin probe",
                .details            = "0: FRR commands execution through Admin Probe is disabled\n"
                                      "1: FRR commands execution through Admin Probe is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. FRR commands execution through Admin Probe is disabled by default)",
                .val_type           = config_type_int,
                .component_types    = config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_GATEWAY_ADMIN_PROBE_FRR_CMDS_ENABLED,
                .feature_group      = FEATURE_GROUP_NP_GATEWAY_FRR_ADMIN_PROBE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                .desc               = "enable np redundancy feature",
                .details            = "0: np redundancy is not enabled\n"
                                      "1: np redundancy is enabled\n"
                                      "Order of check: instance_gid, customer_gid, global\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .component_types    = config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .desc               = "Enable NP MultiLan Subnet",
                .details            = "Enable NP MultiLan Subnet"
                                      "default: disabled",
                .val_type           = config_type_int,
                .component_types    = config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .int_range_hi       = HIGH_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .int_default        = DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        }
};

int zpn_npgateway_config_ovd_desc_register_all() {

        int desc_count = sizeof(zpn_npgateway_config_override_descriptions) / sizeof(struct zpath_config_override_desc);

        int res = NPGWD_RESULT_NO_ERROR;
        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&zpn_npgateway_config_override_descriptions[i]);
                if (res) {
                        break;
                }
        }

        return res;
}
