/*
 * zpn_npgateway_wally_client.c. Copyright (C) 2024-2025 Zscaler, Inc. All Rights Reserved.
 *
 * NP Gateway Wally configuration / access.
 *
 */

#include "zpn_npgatewayd/zpn_npgateway_wally_client.h"
#include "zpn_npgatewayd/zpn_npgateway_alloc.h"
#include "zpn_npgatewayd/zpn_npgateway_common.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpn_npgatewayd/zpn_npgateway_operator.h"
#include "zpn_npgatewayd/zpn_npgateway_bgp.h"

#include "np_lib/np.h"
#include "np_lib/np_clients.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_connectors.h"
#include "np_lib/np_connector_groups.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_config.h"

#include <event2/event_struct.h>
#include "wally/wally.h"
#include "wally/wally_fohh_client.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_config_override.h"

// reused code in zpath_app.c:zpath_app_create_shard()
static int gateway_wally_create(np_gateway_config_t *gateway_config)
{
    // e.g. np-s1-wally.dev.zpath.net
    char str[ARGO_MAX_NAME_LENGTH];
    char wally_host_name[ARGO_MAX_NAME_LENGTH];
    const int shard_index = ZPATH_SHARD_FROM_GID(gateway_config->customer_gid);
    snprintf(str, sizeof(str), "np-shard-%d_wally", shard_index);
    snprintf(wally_host_name, sizeof(wally_host_name), "np-s%d-wally.%s", shard_index, ZPATH_LOCAL_CLOUD_NAME);

    gateway_config->wally = wally_create(str,
                                         1,
                                         zpath_debug_wally_endpoints_init,
                                         NULL,
                                         NULL,
                                         NULL);
    if (!gateway_config->wally) {
        NPGWD_LOG(AL_ERROR, "Wally create failed.");
        return NPGWD_RESULT_NO_MEMORY;
    }

    // NOTE: not using a slave DB

    // attach remote DB
    struct wally_fohh_client *wally_fohh_client_handle =
        wally_fohh_client_create(gateway_config->wally,
                                 NULL,
                                 wally_host_name,
                                 wally_host_name,
                                 NULL,
                                 htons((uint16_t)ZPATH_LOCAL_FOHH_PORT),
                                 NULL);

    if (!wally_fohh_client_handle) {
        NPGWD_LOG(AL_CRITICAL,
                  "Could not create wally client for accessing %s",
                  wally_host_name);
        return NPGWD_RESULT_ERR;
    }

    struct wally_origin *remote_db = wally_add_origin(gateway_config->wally,
                                                      wally_host_name,
                                                      wally_fohh_client_handle,
                                                      wally_fohh_register_for_index,
                                                      wally_fohh_deregister_for_index,
                                                      wally_fohh_set_cookie,
                                                      wally_fohh_get_status,
                                                      wally_fohh_add_table,
                                                      NULL, // set_sequence
                                                      wally_fohh_dump_state,
                                                      1);
    if (!remote_db) {
        NPGWD_LOG(AL_CRITICAL,
                  "Could not attach wally client for accessing %s",
                  wally_host_name);
        return NPGWD_RESULT_ERR;
    }

    // No local DB
    int rc = zpath_table_init(gateway_config->wally, NULL, remote_db, 0);

    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                  "Could not add remote origin for accessing %s",
                  wally_host_name);
        return rc;
    }

    NPGWD_LOG(AL_NOTICE, "Initializing NP Gateway Wally Complete");
    return NPGWD_RESULT_NO_ERROR;
}

static inline void dump_rows(struct argo_object *prev_row, struct argo_object *curr_row/*!=NULL*/)
{
    char dump[1024] = {0};
    if (prev_row && argo_object_dump(prev_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        NPGWD_LOG(AL_NOTICE, "Previous row: %s", dump);
    }

    if (argo_object_dump(curr_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        NPGWD_LOG(AL_NOTICE, "Current row: %s", dump);
    }
}

inline static int register_handler(void *cookie,
                                   struct argo_object *previous_row,
                                   struct argo_object *row,
                                   event_callback_fn callback,
                                   const char *caller)
{
    // note: drop this if block immediately after drop-39
    if (!cookie || !row) {
        NPGWD_LOG(AL_CRITICAL,
                  "Invoked by %s, NULL cookie or row. cookie: %p, row: %p",
                  caller,
                  cookie,
                  row);
        return NPGWD_RESULT_BAD_ARGUMENT;
    }

    np_gateway_config_t *gateway_config = cookie;
    np_gateway_event_param_t *event_param;
    int rc = allocate_gateway_event_param(&event_param,
                                          gateway_config,
                                          previous_row,
                                          row,
                                          __FUNCTION__);
    if (rc) return rc;

    struct event *operator_event = event_new(gateway_config->operator_event_base,
                                             -1,            // fd unused
                                             0,
                                             callback,
                                             event_param);

    if (!operator_event) {
        NPGWD_LOG(AL_CRITICAL, "event_new() failed!");
        free_gateway_event_param(&event_param);
        return NPWG_RESULT_ERR;
    }

    struct timeval tv = { 0 };

    if (0 != event_add(operator_event, &tv)) {
        NPGWD_LOG(AL_CRITICAL, "event_add() failed.");
        free_gateway_event_param(&event_param);
        return NPGWD_RESULT_ERR;
    }

    return NPGWD_RESULT_NO_ERROR;
}

static int client_subnets_callback_(void *cookie,
                                    __attribute__((unused)) struct wally_registrant *registrant,
                                    __attribute__((unused)) struct wally_table *table,
                                    struct argo_object *previous_row,
                                    struct argo_object *row,
                                    __attribute__((unused)) int64_t request_id)
{
    dump_rows(previous_row, row);
    const struct np_client_subnets *client_subnet = row->base_structure_void;

    // ignore cases where we just started and the previous_row is null
    if (client_subnet->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    return register_handler(cookie,
                            previous_row,
                            row,
                            client_subnets_event_handler,
                            __FUNCTION__);
}

static int clients_callback_(void *cookie,
                             __attribute__((unused)) struct wally_registrant *registrant,
                             __attribute__((unused)) struct wally_table *table,
                             struct argo_object *previous_row,
                             struct argo_object *row,
                             __attribute__((unused)) int64_t request_id)
{
    int res = NPGWD_RESULT_NO_ERROR;
    dump_rows(previous_row, row);
    const np_gateway_config_t *gateway_config = cookie;
    struct np_clients *new_client = row->base_structure_void;
    // note: old_client is the previous state of this client
    struct np_clients *old_client = previous_row ? previous_row->base_structure_void : NULL;

    /* When we don't have a previous state, it means the client is not yet registered.
     * 1. ignore cases where we just started and the previous_row is null
     * 2. also ignore cases where we just started and the gateway gid is NULL in the DB
     * 3. also ignore if we just started and the client subnet is not owned by this
     *    tenant gateway
     */
    if (!old_client) {
        if (new_client->deleted ||
            !new_client->gateway_gid ||
            new_client->gateway_gid != gateway_config->gid) {
                return NPGWD_RESULT_NO_ERROR;
        }

        // this is a new client that needs to be "set"
        res = register_handler(cookie,
                               previous_row,
                               row,
                               clients_set_handler,
                               __FUNCTION__);
        return res;
    }

    // we have a previous state, this means the client is already enrolled and may have been registered
    /* (client is deleted)
     * this is an existing client that needs to be deleted */
    if (new_client->deleted) {
        res = register_handler(cookie,
                               previous_row,
                               row,
                               clients_delete_handler,
                               __FUNCTION__);
        return res;
    }

    /* (client disconnected)
     * if the gateway gid is empty and the client previously set this gateway as its gateway, we need to delete the client */
    if (!new_client->gateway_gid) {
        // the client previously used some other gateway, not our concern
        if (old_client->gateway_gid != gateway_config->gid) {
            return NPGWD_RESULT_NO_ERROR;
        }

        res = register_handler(cookie,
                               previous_row,
                               row,
                               clients_delete_handler,
                               __FUNCTION__);
        return res;
    }

    /* (client moved to another gateway)
     * if the gateway gid exists but it is not the same as the gid of this gateway, we need to delete this client */
    if (new_client->gateway_gid != gateway_config->gid) {
        /* the client previously used some other gateway, not our concern */
        if (old_client->gateway_gid != gateway_config->gid) {
            return NPGWD_RESULT_NO_ERROR;
        }

        res = register_handler(cookie,
                               previous_row,
                               row,
                               clients_delete_handler,
                               __FUNCTION__);
        return res;
    }

    /* check if wireguard config changed on the existing client */
    if (strcmp(old_client->public_key, new_client->public_key) == 0) {
        if (old_client->ip && new_client->ip) {
            if (argo_inet_is_same(old_client->ip, new_client->ip)) {
                /* wireguard config is not changed, skipping */
                NPGWD_LOG(AL_INFO, "Skip setting client due to wireguard config is not changed: gid: %"PRId64", public_key: %s, c_name: %s",
                                    new_client->gid, new_client->public_key, new_client->c_name);
                return NPGWD_RESULT_NO_ERROR;
            }
            /* (client updated its IP)
             * if the client's IP changed,and the client was previously assigned to this gateway, we need to delete
             * the old client and set the client with new IP */
            if (old_client->gateway_gid == gateway_config->gid) {
                NPGWD_LOG(AL_INFO, "%s: Client IP changed, deleting old client", new_client->c_name);
                res = register_handler(cookie,
                                       NULL,
                                       previous_row,
                                       clients_delete_handler,
                                       __FUNCTION__);
                if (res) {
                    NPGWD_LOG(AL_ERROR, "%s: Failed to delete the old client: %s", old_client->c_name, npgwd_result_string(res));
                }
            }
            // continue to set the new client
        }
    } else {
        /* (client updated its public key)
         * if the client's public key changed, we need to delete the old client and set the client with new key */
        NPGWD_LOG(AL_INFO, "%s: Client public key changed (from %s to %s), deleting old client",
                            new_client->c_name, old_client->public_key, new_client->public_key);
        res = register_handler(cookie,
                               NULL,
                               previous_row,
                               clients_delete_handler,
                               __FUNCTION__);
        if (res) {
            NPGWD_LOG(AL_ERROR, "%s: Failed to delete the old client: %s", old_client->c_name, npgwd_result_string(res));
        }
        // continue to set the new client
    }

    res = register_handler(cookie,
                           previous_row,
                           row,
                           clients_set_handler,
                           __FUNCTION__);

    return res;
}

static int connector_groups_callback_(void *cookie,
                                      __attribute__((unused)) struct wally_registrant *registrant,
                                      __attribute__((unused)) struct wally_table *table,
                                      struct argo_object *previous_row,
                                      struct argo_object *row,
                                      __attribute__((unused)) int64_t request_id)
{
    dump_rows(previous_row, row);
    const struct np_connector_groups *connector_group = row->base_structure_void;
    // ignore cases where we just started and the previous_row is null
    if (connector_group->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    return register_handler(cookie,
                            previous_row,
                            row,
                            connector_groups_event_handler,
                            __FUNCTION__);
}

static int connectors_callback_(void *cookie,
                                __attribute__((unused)) struct wally_registrant *registrant,
                                __attribute__((unused)) struct wally_table *table,
                                struct argo_object *previous_row,
                                struct argo_object *row,
                                __attribute__((unused)) int64_t request_id)
{
    dump_rows(previous_row, row);
    const struct np_connectors *connector = row->base_structure_void;
    // ignore cases where we just started and the previous_row is null
    if (connector->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    return register_handler(cookie,
                            previous_row,
                            row,
                            connectors_event_handler,
                            __FUNCTION__);
}

static int lan_subnets_callback_(void *cookie,
                                 __attribute__((unused)) struct wally_registrant *registrant,
                                 __attribute__((unused)) struct wally_table *table,
                                 struct argo_object *previous_row,
                                 struct argo_object *row,
                                 __attribute__((unused)) int64_t request_id)
{
    dump_rows(previous_row, row);
    const struct np_lan_subnets *lan_subnet = row->base_structure_void;
    // ignore cases where we just started and the previous_row is null
    if (lan_subnet->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    return register_handler(cookie,
                            previous_row,
                            row,
                            lan_subnets_event_handler,
                            __FUNCTION__);
}

static int tenant_gateways_callback_(void *cookie,
                                     __attribute__((unused)) struct wally_registrant *registrant,
                                     __attribute__((unused)) struct wally_table *table,
                                     struct argo_object *previous_row,
                                     struct argo_object *row,
                                     __attribute__((unused)) int64_t request_id)
{
    dump_rows(previous_row, row);
    const struct np_tenant_gateways *tenant_gateway = row->base_structure_void;
    // ignore cases where we just started and the previous_row is null
    if (tenant_gateway->deleted && !previous_row) {
        return NPGWD_RESULT_NO_ERROR;
    }

    return register_handler(cookie,
                            previous_row,
                            row,
                            tenant_gateways_event_handler,
                            __FUNCTION__);
}

int np_gateway_wally_init(np_gateway_config_t *gateway_config)
{
    int rc = gateway_wally_create(gateway_config);
    if (rc) return rc;

    rc = zpath_config_override_init(zpath_global_wally,
                                     gateway_config->customer_gid, 0,
                                     config_component_np_gateway);
    if (rc) {
        NPGWD_LOG(AL_CRITICAL,
                        "Could not read/fully load zpath_config_override data. error: %s",
                        zpath_result_string(rc));
        return rc;
    }

    rc = np_init(gateway_config->gid);
    if (rc) return rc;

    rc = np_bgp_init();
    if (rc) return rc;

    np_set_redundancy_feature_config_monitor(np_gateway_redundancy_feature_status_toggled);
    gateway_config->redundancy_feature_enabled = np_is_redundancy_feature_enabled(gateway_config->customer_gid);

    /* with NP BGP support, np_tenant_gateways table has to be initialized first */
    rc = np_tenant_gateways_table_init(gateway_config->wally,
                                       gateway_config->customer_gid,
                                       tenant_gateways_callback_,
                                       (void *)gateway_config,
                                       1);
    if (rc) return rc;

    rc = np_clients_table_init(gateway_config->wally,
                               gateway_config->customer_gid,
                               clients_callback_,
                               (void *)gateway_config,
                               1);
    if (rc) return rc;

    rc = np_client_subnets_table_init(gateway_config->wally,
                                      gateway_config->customer_gid,
                                      client_subnets_callback_,
                                      (void *)gateway_config,
                                      1);
    if (rc) return rc;

    rc = np_connectors_table_init(gateway_config->wally,
                                  gateway_config->customer_gid,
                                  connectors_callback_,
                                  (void *)gateway_config,
                                  1);
    if (rc) return rc;

    rc = np_connector_groups_table_init(gateway_config->wally,
                                        gateway_config->customer_gid,
                                        connector_groups_callback_,
                                        (void *)gateway_config,
                                        1);
    if (rc) return rc;

    rc = np_lan_subnets_table_init(gateway_config->wally,
                                   gateway_config->customer_gid,
                                   lan_subnets_callback_,
                                   (void *)gateway_config,
                                   1);
    if (rc) return rc;

    return NPGWD_RESULT_NO_ERROR;
}
