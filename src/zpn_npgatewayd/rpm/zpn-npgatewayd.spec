Name:           zpn-npgatewayd
Version:        %{_version}
Release:        1%{?dist}
Summary:        Zscaler Private Access Network Presence Gateway.
Group:          Applications/Internet
License:        license.txt
Vendor:         Zscaler
URL:            https://www.zscaler.com
Source:         %{name}-%{version}.tar
Prefix:         /opt/zscaler
Requires(pre):  shadow-utils
%{?systemd_requires}
BuildRequires:  systemd

%global debug_package %{nil}
%global __debug_install_post /bin/true
%global __os_install_post %{nil}
%define _source_filedigest_algorithm 8
%define _binary_filedigest_algorithm 8

%description
zpn-npgatewayd is the Gateway for the Zscaler Private Access Network Presence. Deploys service zpn_npgatewayd.
%prep

%setup -q

%build

%pre
getent group zscaler >/dev/null || groupadd -r zscaler
getent passwd zscaler >/dev/null || \
    useradd -r -g zscaler -d /opt/zscaler -s /sbin/nologin \
    -c "Zscaler Service Account" zscaler
exit 0

%define _build_id_links none

%install
rm -rf $RPM_BUILD_ROOT
%make_install
mkdir -p $RPM_BUILD_ROOT%{_unitdir}
mkdir -p $RPM_BUILD_ROOT%{_unitdir}/../system-preset
mkdir -p $RPM_BUILD_ROOT/etc/sysctl.d
install -p -D -m 444 64-zscaler.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/
install -p -D -m 444 zpn_npgatewayd.service $RPM_BUILD_ROOT%{_unitdir}/
install -d -m 755 $RPM_BUILD_ROOT%{prefix}/log
install -p -D -m 444 53-maps.conf $RPM_BUILD_ROOT/etc/sysctl.d/
install -d -m 755 $RPM_BUILD_ROOT%{prefix}/var
%if 0%{?_sanitized}
    install -d -m 755 $RPM_BUILD_ROOT%{prefix}/report
    install -p -D -m 644 asan.options $RPM_BUILD_ROOT%{prefix}/etc/asan.options
%endif

%clean
[ "$RPM_BUILD_ROOT" != "/" ] && rm -rf $RPM_BUILD_ROOT

%files
%defattr(-,zscaler,zscaler)
%{prefix}/bin/*
%{prefix}/share/*
%{_unitdir}/*
%{_unitdir}/../system-preset/*
%attr(755, zscaler, zscaler) %{prefix}/log
%attr(755, zscaler, zscaler) %{prefix}/var
%attr(700, zscaler, zscaler) %{prefix}/etc/wireguard
%attr(700, zscaler, zscaler) %{prefix}/etc/frr
%attr(440, root, root) /etc/sudoers.d/10-zscaler-user
%attr(644, root, root) /etc/sysctl.d/*
%if 0%{?_sanitized}
    %dir %{prefix}/report
    %config(noreplace) %{prefix}/etc/asan.options
%endif

%doc

%post
%systemd_post zpn_npgatewayd.service
%if 0%{?_sanitized}
    setcap cap_sys_ptrace,cap_net_admin,cap_net_bind_service=ep %{prefix}/bin/zpn_npgatewayd
%else
    setcap cap_net_admin,cap_net_bind_service=ep %{prefix}/bin/zpn_npgatewayd
%endif
/sbin/sysctl -q -p /etc/sysctl.d/53-maps.conf

%preun
%systemd_preun zpn_npgatewayd.service

%postun
%systemd_postun zpn_npgatewayd.service

%changelog
