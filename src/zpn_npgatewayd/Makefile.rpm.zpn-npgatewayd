#
# Makefile for doing what RPM needs. Note that we don't really use RPM
# to build. We're just putting things in the right places.
#

PACKAGE_NAME=zpn-npgatewayd
APPLICATIONS_BIN=zpn_npgatewayd wg wg-quick
LICENSE=license.txt
SYSTEMD_UNIT_FILE=zpn_npgatewayd.service
FULLDIR=$(DESTDIR)/opt/zscaler
WIREGUARD_DIR=$(FULLDIR)/etc/wireguard
FRR_DIR=$(FULLDIR)/etc/frr
WIREGUARD_CONFIG_FILE=npwg0.conf
FRR_CONFIG_FILE=frr.conf
SUDOERS_DIR=$(DESTDIR)/etc/sudoers.d
ZSCALER_SUDOERS_FILE=10-zscaler-user

install:
	mkdir -p $(FULLDIR)/bin
	mkdir -p $(FULLDIR)/share
	mkdir -p $(WIREGUARD_DIR)
	mkdir -p $(FRR_DIR)
	mkdir -p $(SUDOERS_DIR)
	cp $(APPLICATIONS_BIN) $(FULLDIR)/bin
	cp $(LICENSE) $(FULLDIR)/share/$(PACKAGE_NAME).$(LICENSE)
	cp $(WIREGUARD_CONFIG_FILE) $(WIREGUARD_DIR)
	cp $(FRR_CONFIG_FILE) $(FRR_DIR)
	cp $(ZSCALER_SUDOERS_FILE) $(SUDOERS_DIR)
