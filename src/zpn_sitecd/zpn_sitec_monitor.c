/*
 * zpn_sitec_monitor.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * All the periodic monitoring stuff goes here
 */

#include <time.h>
#include <arpa/inet.h>
#include "argo/argo_hash.h"
#include <event2/bufferevent.h>
#include "zpath_lib/zpath_system.h"
#include "fohh/fohh_log.h"

#include "zpn_sitecd/zpn_sitec.h"
#include "zpn_sitecd/zpn_sitec_monitor.h"
#include "zpn_sitecd/zpn_sitec_proxy.h"
#include "zpn_sitecd/zpn_sitec_util.h"
#include "zpn/zpn_sitec_log.h"
#include "zpn_sitecd/zpn_sitec_siem.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_system.h"
#include "zhw/zhw_os.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpath_misc/zpath_version.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpn/zpn_site.h"

static struct zthread_info *tickle_me = NULL;
struct event *ev_firedrill_timer = NULL;
extern struct zpn_firedrill_stats g_sitec_firedrill_stats_obj;
extern int g_firedrill_disable_invoked_on_cmdline;

static struct zpn_sitec_interface_stats {
    int64_t intf_rb;
    int64_t intf_rp;
    int64_t intf_re;
    int64_t intf_rd;
    int64_t intf_tb;
    int64_t intf_tp;
    int64_t intf_te;
    int64_t intf_td;
} prev_intf_stats;


int zpn_sitec_is_ready(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return ((gs->magic == SITEC_GLOBAL_MAGIC) ? ZPN_RESULT_NO_ERROR : ZPN_RESULT_NOT_READY);
}

uint16_t zpn_sitec_get_cpu_util(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (gs->sys_stats.cpu_util);
}

uint16_t zpn_sitec_get_cpu_steal_perc(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (gs->sys_stats.cpu_steal_perc);
}

int zpn_sitec_get_system_mem_util(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (gs->sys_stats.system_mem_util);
}

int zpn_sitec_get_process_mem_util(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (gs->sys_stats.process_mem_util);
}


/*
 * Log the status of the config connection. Called periodically
 */
static void zpn_sitec_cfg_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->cfg_fohh_conn) {
        f_conn = gs->sitec_state->cfg_fohh_conn;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker config connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker config connection - NONE");
    }
}

/*
 * Log the status of the config override connection. Called periodically
 */
static void zpn_sitec_ovd_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->ovd_fohh_conn) {
        f_conn = gs->sitec_state->ovd_fohh_conn;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker config override connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker config override connection - NONE");
    }
}

/*
 * Log the status of the control connection. Called periodically
 */
static void zpn_sitec_ctl_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->ctrl_fohh_conn) {
        f_conn = gs->sitec_state->ctrl_fohh_conn;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker control connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker control override connection - NONE");
    }
}

/*
 * Log the status of the static wally connection. Called periodically
 */
static void zpn_sitec_rcfg_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->rcfg_fohh_conn) {
        f_conn = gs->sitec_state->rcfg_fohh_conn;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker static wally config connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker static wally config connection - NONE");
    }
}

/*
 * Log the status of the stats connection. Called periodically
 */
static void zpn_sitec_stat_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->stats_fohh_conn) {
        f_conn = gs->sitec_state->stats_fohh_conn;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker stats connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker stats connection - NONE");
    }
}

/*
 * Log the status of the event connection. Called periodically
 */
static void zpn_sitec_event_log_status(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->event_log_channel) {
        f_conn = gs->sitec_state->event_log_channel;
    }

    if (f_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(f_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(f_conn);

        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        SITEC_LOG(AL_NOTICE, "Public Broker event log connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(f_conn), fohh_description(f_conn),
                      fohh_get_uptime_str(f_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(f_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(f_conn),
                      tx_b, rx_b);
    } else {
        SITEC_LOG(AL_NOTICE, "Public Broker event log connection - NONE");
    }
}

char* sc_get_override_server_cn()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    if (!gs || !gs->sitec_state || !gs->sitec_state->ovd_fohh_conn) {
        return "";
    }

    return fohh_peer_cn(gs->sitec_state->ovd_fohh_conn);
}

void sitec_log_get_endpoint_data(struct fohh_log_endpoint_cns** cn)
{
    struct fohh_connection *fohh_conn = NULL;
    char fohh_desc_info[1024] = {'\0'};
    int i=0;
    char *peer_cn = NULL;

    *cn = (struct fohh_log_endpoint_cns*) ZPN_CALLOC(sizeof(struct fohh_log_endpoint_cns));
    (*cn)->num_cns = 0;
    (*cn)->cns = ZPN_CALLOC(sizeof(char*)*MAX_COLLECTIONS);

    //Event log
    fohh_conn = fohh_get_log_handle(zpn_pb_event_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[PB event_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Transaction log
    fohh_conn = fohh_get_log_handle(zpn_transaction_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[PB transaction_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Auth log
    fohh_conn = fohh_get_log_handle(zpn_pb_client_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[PB auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Ast Auth log
    fohh_conn = fohh_get_log_handle(zpn_ast_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[PB ast_auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Dns log
    fohh_conn = fohh_get_log_handle(zpn_dns_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[PB dns_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //ast waf log
    fohh_conn = fohh_get_log_handle(zpn_ast_waf_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_waf_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //ast app inspection log
    fohh_conn = fohh_get_log_handle(zpn_ast_app_inspection_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_app_inspection_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //ast ptag log
    fohh_conn = fohh_get_log_handle(zpn_ast_ptag_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_ptag_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //ast waf api log
    fohh_conn = fohh_get_log_handle(zpn_ast_waf_api_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_waf_api_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //ast event log
    fohh_conn = fohh_get_log_handle(zpn_asst_event_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_event_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //event log
    fohh_conn = fohh_get_log_handle(zpn_event_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[event_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //sc_ast_auth_log
    fohh_conn = fohh_get_log_handle(zpn_sc_asst_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[sc_ast_auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //sc_pb_auth
    fohh_conn = fohh_get_log_handle(zpn_sc_pb_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[sc_pb_auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //zpn_auth
    fohh_conn = fohh_get_log_handle(zpn_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    (*cn)->num_cns = i;
    return;
}

static void zpn_sitec_tx_status_report(void)
{
    int res;
    struct zpn_sitec_status_report sc_report;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_system_stats *sys_stats = &(gs->sys_stats);
    struct fohh_connection *f_conn = NULL;
    struct zinterfaces interfaces[MAX_INTERFACES];
    int interface_count;
    struct zroute default_route;
    struct sockaddr_storage resolver[ZCDNS_MAX_RESOLVERS];
    socklen_t resolver_len[ZCDNS_MAX_RESOLVERS];
    int resolver_count = 0;
    int i;
    int64_t rb = 0, rp = 0, re = 0, rd = 0, tb = 0, tp = 0, te = 0, td = 0;
    char* status_conn_buf = NULL;

    if (gs->sitec_state &&
        gs->sitec_state->ctrl_fohh_conn) {
        f_conn = gs->sitec_state->ctrl_fohh_conn;
    }

    memset(interfaces, 0, sizeof(struct zinterfaces)*MAX_INTERFACES);
    memset(&default_route, 0, sizeof(struct zroute));
    memset(resolver, 0, sizeof(struct sockaddr_storage)*ZCDNS_MAX_RESOLVERS);
    memset(resolver_len, 0, sizeof(socklen_t)*ZCDNS_MAX_RESOLVERS);
    memset(&sc_report, 0, sizeof(sc_report));

    sc_report.sitec_gid = gs->sitec_id;

    /* Get sysinfo */
    if (0 == zpath_system_get_uptime_s(&gs->host_uptime_s)) {
        /*
         * This becomes inaccurate when system clock changes after the start and now. This is ok as there is no
         * functional impact.
         */
        sc_report.sys_uptime_s = epoch_s() - gs->host_uptime_s;
    } else {
        SITEC_LOG(AL_INFO, "Could not get the system uptime info");
        sc_report.sys_uptime_s = 0;
    }

    sc_report.cpu_util = sys_stats->cpu_util;
    sc_report.mem_util = sys_stats->system_mem_util;

    SITEC_DEBUG_RPC("cpu_util = %d, mem_util = %d", sc_report.cpu_util, sc_report.mem_util);

    /* Get interfaces */
    if (get_interfaces(interfaces, &interface_count) == ZSYSINFO_SUCCESS) {
        SITEC_DEBUG_RPC("Interface count = %d", interface_count);
        for (i = 0; i < interface_count; i++) {
            struct sockaddr_in *addr_in = (struct sockaddr_in *)&(interfaces[i].addr);

            rb += interfaces[i].rb;
            rp += interfaces[i].rp;
            re += interfaces[i].re;
            rd += interfaces[i].rd;
            tb += interfaces[i].tb;
            tp += interfaces[i].tp;
            te += interfaces[i].te;
            td += interfaces[i].td;

            SITEC_DEBUG_RPC("  - %s: %s, rb=%ld, rp=%ld, re=%ld, rd=%ld, tb=%ld, tp=%ld, te=%ld, td=%ld",
                                    interfaces[i].name, inet_ntoa(addr_in->sin_addr),
                                    (long)interfaces[i].rb, (long)interfaces[i].rp, (long)interfaces[i].re, (long)interfaces[i].rd,
                                    (long)interfaces[i].tb, (long)interfaces[i].tp, (long)interfaces[i].te, (long)interfaces[i].td);
        }
    }

    /* Get default route */
    if (get_default_route(&default_route) == ZSYSINFO_SUCCESS) {
        struct sockaddr_in *gw_addr_in = (struct sockaddr_in *)&(default_route.gw);
        struct argo_inet inet;
        uint16_t port_ne;

        argo_sockaddr_to_inet((struct sockaddr *)&default_route.gw, &inet, &port_ne);

        SITEC_DEBUG_RPC("Default route: interface = %s, gateway = %s", default_route.intf_name, inet_ntoa(gw_addr_in->sin_addr));
        sc_report.dft_rt_intf = default_route.intf_name;
        sc_report.dft_rt_gw = inet;
    }

    /* Get resolver */
    if (gs->zcdns) {
        zcdns_get_resolvers(gs->zcdns, resolver, resolver_len, &resolver_count);

        for (i = 0; i < resolver_count; i++) {
            char str[ARGO_INET_ADDRSTRLEN];
            struct sockaddr_storage *ss = &resolver[i];
            struct argo_inet inet;
            uint16_t port_ne;

            argo_sockaddr_to_inet((struct sockaddr *)ss, &inet, &port_ne);

            SITEC_DEBUG_RPC("Resolver %d: addr = %s, port = %d", i+1, argo_inet_generate(str, &inet), ntohs(port_ne));
            if (i == 0) {
                sc_report.resolver = inet;
            }
        }
    }

    sc_report.intf_count = interface_count;
    sc_report.intf_rb = rb;
    sc_report.intf_rp = rp;
    sc_report.intf_re = re;
    sc_report.intf_rd = rd;
    sc_report.intf_tb = tb;
    sc_report.intf_tp = tp;
    sc_report.intf_te = te;
    sc_report.intf_td = td;

    sc_report.delta_intf_rb = rb - prev_intf_stats.intf_rb;
    sc_report.delta_intf_rp = rp - prev_intf_stats.intf_rp;
    sc_report.delta_intf_re = re - prev_intf_stats.intf_re;
    sc_report.delta_intf_rd = rd - prev_intf_stats.intf_rd;
    sc_report.delta_intf_tb = tb - prev_intf_stats.intf_tb;
    sc_report.delta_intf_tp = tp - prev_intf_stats.intf_tp;
    sc_report.delta_intf_te = te - prev_intf_stats.intf_te;
    sc_report.delta_intf_td = td - prev_intf_stats.intf_td;
    sc_report.total_intf_b = rb + tb;
    sc_report.total_intf_p = rp + tp;
    sc_report.total_intf_e = re + te;
    sc_report.total_intf_d = rd + td;
    sc_report.delta_total_intf_b = rb + tb - (prev_intf_stats.intf_rb + prev_intf_stats.intf_tb);
    sc_report.delta_total_intf_p = rp + tp - (prev_intf_stats.intf_rp + prev_intf_stats.intf_tp);
    sc_report.delta_total_intf_e = re + te - (prev_intf_stats.intf_re + prev_intf_stats.intf_te);
    sc_report.delta_total_intf_d = rd + td - (prev_intf_stats.intf_rd + prev_intf_stats.intf_td);

    prev_intf_stats.intf_rb = rb;
    prev_intf_stats.intf_rp = rp;
    prev_intf_stats.intf_re = re;
    prev_intf_stats.intf_rd = rd;
    prev_intf_stats.intf_tb = tb;
    prev_intf_stats.intf_tp = tp;
    prev_intf_stats.intf_te = te;
    prev_intf_stats.intf_td = td;

    sc_report.platform = zhw_platform();

    SITEC_DEBUG_RPC("Platform = %s", sc_report.platform);

    sc_report.sc_uptime_s = gs->sc_start_time_s;

    sc_report.time_delta_us = zpn_sitec_get_cloud_time_delta_us();

    snprintf(sc_report.log_broker_cn, sizeof(sc_report.log_broker_cn), "%s", fohh_log_get_endpoint_cn(argo_log_get_name(zpn_event_collection)));
    sc_report.udp4_port_util = zpath_system_get_udpv4_socket_util();
    sc_report.udp6_port_util = zpath_system_get_udpv6_socket_util();
    sc_report.sys_fd_util = zpath_system_get_system_fd_util();
    sc_report.proc_fd_util = zpath_system_get_process_fd_util();

    /*
     * don't send 0 as that broker can't differentiate if this is coming from a sitec without this capability or
     * a real value of 0. So let the minimum be 1%
     */
    if (0 == sc_report.cpu_util) {
        sc_report.cpu_util = 1;
    }
    if (0 == sc_report.mem_util) {
        sc_report.mem_util = 1;
    }
    if (0 == sc_report.udp4_port_util) {
        sc_report.udp4_port_util = 1;
    }
    if (0 == sc_report.udp6_port_util) {
        sc_report.udp6_port_util = 1;
    }
    if (0 == sc_report.sys_fd_util) {
        sc_report.sys_fd_util = 1;
    }
    if (0 == sc_report.proc_fd_util) {
        sc_report.proc_fd_util = 1;
    }

    snprintf(sc_report.ovd_broker_cn, sizeof(sc_report.ovd_broker_cn), "%s", sc_get_override_server_cn());
    struct fohh_log_endpoint_cns* uniq_cns;
    fohh_log_get_uniq_endpoint_cns(&uniq_cns);
    if (uniq_cns && uniq_cns->num_cns) {
        sc_report.log_brokers_uniq = uniq_cns->cns;
        sc_report.log_brokers_uniq_count = uniq_cns->num_cns;
    } else {
        sc_report.log_brokers_uniq = NULL;
        sc_report.log_brokers_uniq_count = 0;
    }

    struct fohh_log_endpoint_cns* cns_data;
    sitec_log_get_endpoint_data(&cns_data);
    if (cns_data && cns_data->num_cns) {
        sc_report.all_log_brokers_data = cns_data->cns;
        sc_report.all_log_brokers_data_count = cns_data->num_cns;
    } else {
        sc_report.all_log_brokers_data = NULL;
        sc_report.all_log_brokers_data_count = 0;
    }

    sc_report.last_os_upgrade_time = gs->last_os_upgrade_time;
    sc_report.last_sarge_upgrade_time = gs->last_sarge_upgrade_time;
    sc_report.platform_version = gs->platform_version;
    status_conn_buf = zpn_sitec_get_status_conn_info(0);
    sc_report.status_conn_info = status_conn_buf;
    zpn_sitec_get_database_sync_times(&sc_report.master_last_sync_time, &sc_report.shard_last_sync_time, &sc_report.userdb_last_sync_time);
    if (f_conn) {
        /* Send status report via RPC */
        res = fohh_argo_serialize(f_conn,
                                  zpn_sitec_status_report_description,
                                  &sc_report,
                                  0,
                                  fohh_queue_element_type_control);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not send sitec status report: %s", zpn_result_string(res));
        } else {
            char dump[8000];
            argo_structure_dump(zpn_sitec_status_report_description, &sc_report, dump, sizeof(dump), NULL, 0);
            SITEC_DEBUG_AUTH("Sent sitec status report: %s", dump);
        }
    } else {
        SITEC_DEBUG_CONN("sitec control connection is not up");
    }

    if (uniq_cns) {
        fohh_free_uniq_cns(uniq_cns);
        uniq_cns = NULL;
    }

    if(cns_data) {
        fohh_free_uniq_cns(cns_data);
        cns_data = NULL;
    }

    if (status_conn_buf) {
        json_free_serialized_string(status_conn_buf);
        status_conn_buf = NULL;
    }
 }

/*
 * 1. See if I am still eligible to continue to run,
 *        a. the config is still valid, otherwise stop the connector & delete provision key.
 *        b. the mem util is <= 95%, otherwise stop the connector.
 * 2. send all connector status messages 1min
 * 3. send smaps stats every 24hrs.
 */
static void
zpn_sitec_monitor_status_report_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    //struct timeval tv;
    int64_t now_cloud_s;
    int64_t time_remaining;
    int64_t local_time_lags_cloud_time_delta_us;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_system_stats *sys_stats = &(gs->sys_stats);

    static int64_t sys_disk_total_bytes;
    static int meminfo_read = 0;
    char uptime_str[32];

    if (tickle_me) zthread_heartbeat(tickle_me);

    if (0 == gs->sitec_id) {
        SITEC_LOG(AL_ERROR, "sitec gid is isnull");
        return;
    }

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_disk_free_bytes_for_non_root_user(&sys_stats->free_disk_bytes_for_non_root_user)) {
        SITEC_LOG(AL_INFO, "Could not get the disk usage info");
    }

#ifdef __linux__
    if (gs->cgroup_version) {
        zpn_sitec_system_get_cpu_util_from_cgroups(sys_stats, gs->cgroup_version);

        if (zpath_system_get_system_and_process_memory_util_percentage_from_cgroups(&sys_stats->system_mem_util, &sys_stats->process_mem_util, gs->cgroup_version)) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            SITEC_LOG(AL_INFO, "Could not get the Memory usage info");
        }

        if (zpn_system_get_memory_usage_info_from_cgroups(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem,
                                                          &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem,
                                                          &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem,
                                                          gs->cgroup_version) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            SITEC_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    } else
#endif
    {
        zpn_sitec_system_get_cpu_util(sys_stats);

        if (zpath_system_get_system_and_process_memory_util_percentage(&sys_stats->system_mem_util, &sys_stats->process_mem_util)) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            SITEC_LOG(AL_INFO, "Could not get the Memory usage info for sitec");
        }

        if (zpn_system_get_memory_usage_info(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem, &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem, &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            SITEC_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    }

    zpath_system_get_udp_socket_util(&sys_stats->udp4_port_util, &sys_stats->udp6_port_util);
    zpath_system_get_tcp_socket_util(&sys_stats->tcp4_port_util, &sys_stats->tcp6_port_util);

    zpath_system_get_fd_util(&sys_stats->sys_fd_util, &sys_stats->proc_fd_util);
    zpath_system_get_fd_in_use(&sys_stats->system_fd_in_use, &sys_stats->process_fd_in_use);
    zpath_system_get_tcp_socket_inuse(&sys_stats->num_system_tcpv4_socket_inuse, &sys_stats->num_system_tcpv6_socket_inuse);
    zpath_system_get_udp_socket_inuse(&sys_stats->num_system_udpv4_socket_inuse, &sys_stats->num_system_udpv6_socket_inuse);

    sitec_state_get_uptime_str(uptime_str, sizeof(uptime_str));

    if (meminfo_read == 0) {
        zpath_system_get_disk_info(NULL, NULL, &sys_disk_total_bytes);
        meminfo_read = 1;
    }

    if (zpn_system_get_memory_usage_info(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem, &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem, &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem) == ZPN_RESULT_ERR) {
        sys_stats->memtotal_abs_mem = 0;
        sys_stats->memfree_abs_mem = 0;
        sys_stats->swaptotal_abs_mem = 0;
        sys_stats->swapfree_abs_mem = 0;
        sys_stats->system_used_abs_mem = 0;
        sys_stats->process_used_abs_mem = 0;
        SITEC_LOG(AL_INFO, "Could not get the Absolute Memory usage info for sitec");
    }

    SITEC_LOG(AL_NOTICE, "-------- Sitec Status:ID=%ld:Name=%s:Ver=%s:Mem(System|Process)=%d%%|%d%%:"
                       "Mem_abs(MemTotal|MemFree|SwapTotal|SwapFree|SysUsed|ProcessUsed)= %" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB :"
                       "Disk (Avail|Total)=%.2fGB|%.2fGB:CPU(Util|Steal|Configured|Avail)=%d%%|%d%%|%d|%d:Uptime=%s --------",
            (long)gs->sitec_id,
            gs->cfg_name,
            ZPATH_VERSION,
            sys_stats->system_mem_util,
            sys_stats->process_mem_util,
            sys_stats->memtotal_abs_mem, sys_stats->memfree_abs_mem, sys_stats->swaptotal_abs_mem, sys_stats->swapfree_abs_mem, sys_stats->system_used_abs_mem, sys_stats->process_used_abs_mem,
            (double)sys_stats->free_disk_bytes_for_non_root_user / ZPN_SYSTEM_GB_TO_BYTES,
            (double)sys_disk_total_bytes / ZPN_SYSTEM_GB_TO_BYTES,
            sys_stats->cpu_util,
            sys_stats->cpu_steal_perc,
			zpn_sitec_state_get_configured_cpus(),
			zpn_sitec_state_get_available_cpus(),
            uptime_str);

    /* cookie == NULL makes sure that we don't call this routine at the init time */
    if (cookie == NULL) {
        char* thread_cpu_usage_log = zthread_get_thread_cpu_usage_log();
        if (thread_cpu_usage_log != NULL) {
            SITEC_LOG(AL_NOTICE, "-------- Sitec thread CPU usage: %s", thread_cpu_usage_log);
            SITEC_FREE(thread_cpu_usage_log);
        }
    }

    if (zpn_sitec_get_swap_config()) {
        SITEC_LOG(AL_NOTICE, "Swap partition is found to be configured,"
			   " Zscaler does not recommend swap config as this could have impact on data transfer rate");
    }

    /* Certificate expiration */
    now_cloud_s = zpn_sitec_cloud_adjusted_epoch_s();
    time_remaining = gs->cert_validity_end_time_cloud_s - now_cloud_s;
    ZPN_SYSTEM_ASSERT(gs->cert_validity_end_time_cloud_s, 1, "unexpected code path, please report to customer support team");
    if (now_cloud_s > gs->cert_force_re_enroll_time_cloud_s) {
        SITEC_LOG(AL_CRITICAL, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds, restarting to re-enroll",
                time_remaining / 86400, (time_remaining % 86400) / 3600, ((time_remaining % 86400) % 3600) / 60,
                ((time_remaining % 86400) % 3600) % 60);
        sleep(1);
        exit(1);
    } else {
        SITEC_LOG(AL_NOTICE, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds",
                time_remaining / 86400, (time_remaining % 86400) / 3600, ((time_remaining % 86400) % 3600) / 60,
                ((time_remaining % 86400) % 3600) % 60);
    }

    if (gs->auto_upgrade_disabled) {
        SITEC_LOG(AL_NOTICE, "Auto Upgrade disabled, currently at version(%s)", ZPATH_VERSION);
    }

    zpn_sitec_cfg_log_status();
    zpn_sitec_ovd_log_status();
    zpn_sitec_ctl_log_status();
    zpn_sitec_rcfg_log_status();
    zpn_sitec_stat_log_status();
    zpn_sitec_event_log_status();
    zpn_sitec_features_fproxy_log_status();

    /* Clock skew */
    local_time_lags_cloud_time_delta_us = zpn_sitec_get_cloud_time_delta_us();
    if (local_time_lags_cloud_time_delta_us < 0) {
        SITEC_LOG(AL_NOTICE, "Time skew: local time is ahead of cloud time by %ld.%06lds",
                (long)(0 - local_time_lags_cloud_time_delta_us) / 1000000,
                (long)(0 - local_time_lags_cloud_time_delta_us) % 1000000);
    } else {
        SITEC_LOG(AL_NOTICE, "Time skew: local time lags cloud time by %ld.%06lds",
                (long)local_time_lags_cloud_time_delta_us / 1000000, (long)local_time_lags_cloud_time_delta_us % 1000000);
    }

    res = zpn_sitec_get_num_hw_id_changed();
    if (res) {
        int i;
        int64_t* time_arr = zpn_sitec_get_hw_id_changed_time_us();
        SITEC_LOG(AL_NOTICE, "Detected %d device(s) id changed, note that the next site controller process restart will fail", res);
        for (i = 0; i < res ; i++) {
            char time_str[128] = {0};
            int64_t changed_time_us = time_arr[i];
            argo_log_gen_time(changed_time_us, time_str, sizeof(time_str), 0, 1);
            SITEC_LOG(AL_NOTICE, "%d/%d device id changed around time: %s", i+1, res, time_str);
        }
    }

    zpn_sitec_siem_log_stats_log();

    zpn_system_sitec_monitor(sys_stats);

    zpn_sitec_tx_status_report();

    // only for debugging
    if(fohh_get_state(gs->sitec_state->cfg_fohh_conn) == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "cfg_fohh_conn connected");
    }

}

static int zpn_sitec_monitor_base_init(struct event_base *base)
{
    int init_f = 0;
    struct timeval tv;
    struct event *ev_status_timer;

    ev_status_timer = event_new(base,
                                -1,
                                EV_PERSIST,
                                zpn_sitec_monitor_status_report_timer_cb,
                                NULL);
    if (!ev_status_timer) {
        SITEC_LOG(AL_ERROR, "Could not create status timer for sitec");
        return ZPN_RESULT_ERR;
    }

    /* Make it show health once right away */
    zpn_sitec_monitor_status_report_timer_cb(-1, 0, &init_f);

    tv.tv_sec = ZPN_SITEC_MONITOR_TIMER_S;
    tv.tv_usec = 0;
    if (event_add(ev_status_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate status timer for sitec");
        return ZPN_RESULT_ERR;
    }

    /* create the firedrill timer */
    /* but activate it in the row callback when it is trigerred through adminUI */
    ev_firedrill_timer = event_new(base,
                                    -1,
                                    EV_TIMEOUT,
                                    zpn_sitec_firedrill_stop,
                                    NULL);
    if (!ev_firedrill_timer) {
        SITEC_LOG(AL_ERROR, "Could not create firedrill timer for sitec");
        return ZPN_RESULT_ERR;
    }
    return ZPN_RESULT_NO_ERROR;
}

static void
zpn_sitec_monitor_reenroll_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    uint64_t reenroll_period = 0;

    if (tickle_me) zthread_heartbeat(tickle_me);

    if (gs->offline_domain && gs->offline_domain[0]) {
        reenroll_period = gs->reenroll_period;
        if (reenroll_period == 0) {
            reenroll_period = ZPN_RE_ENROLL_LEAST_DAYS;
        }
    }

    int result = zpn_enroll_check(&gs->cfg_hw_key, gs->enroll_state->cfg_rsa_key, gs->cfg_fingerprint_str,
                              gs->cfg_provisioning_key, gs->cfg_key_shard, gs->cfg_key_api,
                              ZPN_ENROLLMENT_TYPE_SITEC, gs->enroll_state->enroll_style, gs->cfg_key_cloud,
                              SITEC_PROVISION_KEY_PATH, 0,
                              reenroll_period, gs->customer_id, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Failed to enroll invoked from enroll timer callback.");
    }
}

static struct event *reenroll_timer = NULL;

static int
zpn_sitec_check_reenroll_status(struct zpath_debug_state* request_state,
                                const char** query_values,
                                int query_value_count,
                                void* cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct timeval now, reenroll;

    ZDP("Reenroll period is set to %"PRId64" days. ", gs->reenroll_period);

    evutil_gettimeofday(&now, NULL);

    if (!evtimer_pending(reenroll_timer, &reenroll)) {
        ZDP("Reenroll timer is not running.\n");
    } else if (reenroll.tv_sec < now.tv_sec) {
        ZDP("Reenroll timer is just invoked.\n");
    } else {
        ZDP("Will do reenroll check after %ld seconds.\n", reenroll.tv_sec - now.tv_sec);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_monitor_reenroll_init(struct event_base *base)
{
    reenroll_timer = event_new(base,
                               -1,
                               EV_PERSIST,
                               zpn_sitec_monitor_reenroll_timer_cb,
                               NULL);
    if (!reenroll_timer) {
        SITEC_LOG(AL_ERROR, "Could not create timer for re-enroll");
        return ZPN_RESULT_ERR;
    }

    struct timeval tv;
    tv.tv_sec = 300;
    tv.tv_usec = 0;
    if (event_add(reenroll_timer, &tv)) {
        evtimer_del(reenroll_timer);
        ZPN_LOG(AL_ERROR, "Could not activate timer for re-enroll");
        return ZPN_RESULT_ERR;
    }

    int res = zpath_debug_add_read_command("check reenroll status",
                                      "/sitec/reenroll_status",
                                      zpn_sitec_check_reenroll_status,
                                      NULL,
                                      NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register sitec/reenroll_status. Error: %s", zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

void * zpn_sitec_monitor_thread(struct zthread_info *zthread_arg, void *cookie) {
    struct event_base *base = NULL;
    int res;

    tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        SITEC_LOG(AL_ERROR, "Could not create event_base: zpn_sitec_monitor");
        goto fail;
    }

    res = zpn_sitec_monitor_base_init(base);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_sitec_monitor thread");
        goto fail;
    }

    res = zpn_sitec_monitor_reenroll_init(base);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_sitec_monitor_reenroll thread");
        goto fail;
    }

    zevent_base_dispatch(base);

    SITEC_LOG(AL_ERROR, "Not reachable, zpn_sitec_monitor thread");
fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}


int zpn_sitec_monitor_thread_init() {
    pthread_t thread;
    int res;
    res = zthread_create_with_priority(&thread,
                            zpn_sitec_monitor_thread,
                            NULL,
                            "zpn_sitec_monitor",
                            120,
                            16 * 1024 * 1024,
                            60 * 1000 * 1000,
                            NULL,
                            zthread_priority_high);
    if (res) return ZPN_RESULT_ERR;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_stats_monitor_init(void)
{
    int res;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    res = zpn_sitec_init_cert_validity_counters(FILENAME_CERT);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init cert validity counters");
        return res;
    }

    res = zpn_system_init(ZPN_SYSTEM_THRESHOLD_TYPE_SITEC,
                          zpn_sitec_cloud_adjusted_epoch_us,
                          zpath_assert,
                          is_sitec_dev_environment,
                          zpn_sitec_is_ready,
                          zpn_sitec_get_cpu_util,
                          zpn_sitec_get_cpu_steal_perc,
                          zpn_sitec_get_system_mem_util,
                          zpn_sitec_get_process_mem_util,
                          zpn_sitec_state_get_available_cpus,
                          gs->sc_start_time_m_s);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init system module");
        return res;
    }

    res = zpn_sitec_monitor_thread_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init sitec_monitor_thread_init");
        return res;
    }

    return res;
}
