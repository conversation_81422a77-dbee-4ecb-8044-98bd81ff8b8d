/*
 * zpn_sitec.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "avl/avl.h"
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_http.h"
#include "fohh/fohh_private.h"
#include "wally/wally.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "zpath_misc/zpath_misc.h"
#include "zhw/zhw_os.h"
#include "zsaml/zsaml.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_misc/zpath_platform.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/et_translate_wally.h"
#include "zpath_lib/et_geoip_override.h"

#include "zthread/zthread.h"

#include "zpn/zpn_lib.h"
#include "zcdns/zcdns_libevent.h"

#include "zpath_misc/zpath_version.h"

#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn_sitecd/zpn_sitec.h"
#include "zpn_sitecd/zpn_sitec_util.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_customer_config.h"

#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_step_up_auth_level.h"
#include "zpn/zpn_rule_to_step_up_auth_level_mapping.h"
#include "zpn/zpn_svcp_profile.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_servergroup_server_relation.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zbalance/zpn_balance_redirect.h"

#include "zpn/zpn_znf.h"
#include "zpn/zpn_znf_group.h"
#include "zpn/zpn_znf_to_group.h"
#include "zpn/zpn_branch_connector.h"
#include "zpn/zpn_location.h"

#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_client_table.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_cert.h"

#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_condition_operand.h"
#include "zpn/zpn_rule_condition_set.h"
#include "zpn/zpn_rule_to_assistant_group.h"
#include "zpn/zpn_rule_to_server_group.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_customer_application_group.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_shared_customer_domain.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_private_broker_version.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_customer.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_et_userdb.h"
#include "zpath_lib/zpath_et_customer_userdb.h"
#include "zpn/zpn_scope_engine.h"
#include "zpn/zpn_policy_engine.h"

#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpn/zpn_pbrokergroup_pbroker_relation.h"
#include "zpn/zpn_machine_group.h"
#include "zpn/zpn_machine_table.h"
#include "zpn/zpn_machine_to_group.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_user_risk.h"
#include "zpn/zpn_cbi_mapping.h"
#include "zpn/zpn_cbi_profile.h"
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_ipars.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn_waf/zpn_public_certificate.h"
#include "admin_probe/zpn_command_probe.h"
#include "zpn_waf/zpn_inspection_profile_to_control.h"
#include "zpn_waf/zpn_inspection_prof_to_zsdefined_ctrl.h"
#include "zpn_waf/zpn_inspection_profile.h"
#include "zpn/zpn_policy_overrides.h"

#include "zpn/zpn_debug.h"

#include "zpn/zpn_site.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_table_compiled.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_group_compiled.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_sitec_version.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zcrypt/zcrypt.h"
#include "zpn/zpn_siem.h"
#include "lookup_lib/lookup_lib.h"
#include "zpn_sitecd/zpn_sitec_siem.h"

#include "zpath_lib/zpath_category.h"
#include "zpath_lib/zpath_category_compiled.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_customer_compiled.h"
#include "zpath_lib/zpath_rule.h"
#include "zpath_lib/zpath_rule_compiled.h"
#include "zpn/zpn_aae.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_compiled.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_compiled.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application_server_compiled.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistant_group_compiled.h"
#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_assistant_table_compiled.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_policy_set_compiled.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_compiled.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_saml_attrs_compiled.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_compiled.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_compiled.h"
#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_posture_profile_compiled.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_trusted_network_compiled.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_private_broker_table_compiled.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_group_compiled.h"
#include "zpath_lib/zpath_service.h"
#include "zpath_lib/zpath_service_compiled.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_idp_cert_compiled.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "zpn_waf/zpn_inspection_zsdefined_control.h"
#include "zpn/zpn_workload_tag_group.h"
#include "zpn_sitecd/zpn_sitec_private.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpn/zpn_client_setting.h"
#include "zpn/zpn_broker_client_scim.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpath_lib/zpath_oauth_utils.h"

static struct lookup_lib_init_table init_table[] = {
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_CATEGORY_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_CUSTOMER_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_RULE_HELPER,              0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APPLICATION_HELPER,         0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APPLICATION_GROUP_HELPER,   0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APP_SERVER_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_ASSISTANT_GROUP_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_ASSISTANT_HELPER,           0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_POLICY_SET_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_RULE_HELPER,                0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SAML_ATTRS_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SERVER_GROUP_HELPER,        0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_POSTURE_PROFILE_DB_HELPER,  0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_TRUSTED_NETWORK_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_IDP_HELPER,                 0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_PRIVATE_BROKER_HELPER,      0, 1},
	{GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_PRIVATE_BROKER_GROUP_HELPER,0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_IDP_CERT_HELPER,            0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_SERVICE_HELPER,           0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SITE_CONTROLLER_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SITE_CONTROLLER_GROUP_HELPER, 0, 1},
};

char sc_sni_customer_domain_ctl[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_cfg[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_rcfg[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_ovd[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_log[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_stats[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_pblog[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_alog[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_pbstats[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_astats[SITEC_SNI_DOMAIN_LEN];
char sc_sni_customer_domain_scmc[SITEC_SNI_DOMAIN_LEN];


int64_t g_sitec_geoip_disable = DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE;

struct argo_log_collection *zpn_asst_event_collection;
struct argo_log_collection *zpn_pb_event_collection;
struct argo_log_collection *zpn_pb_client_auth_collection;
struct argo_log_collection *zpn_sc_asst_auth_collection;
struct argo_log_collection *zpn_sc_pb_auth_collection;

struct zpn_firedrill_stats g_sitec_firedrill_stats_obj = {0};

int is_sitec_dev_environment;
int g_config_init_done = 0;

int64_t sarge_upgrade_feature_flag = 0;
int64_t os_upgrade_feature_flag = 0;
int64_t sarge_backup_version_feature_flag = 0;
int64_t full_os_upgrade_feature_flag = 0;
int64_t oauth_enrollment_feature_flag = 0;

struct zpath_allocator sitec_allocator = ZPATH_ALLOCATOR_INIT("sitec");
extern int zpn_balance_config_override_desc_register_all();
extern struct event *ev_firedrill_timer;

int g_firedrill_disable_invoked_on_cmdline = 0;


void set_is_sitec_dev_environment(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    is_sitec_dev_environment = zpath_is_cloud_dev_env(gs->cfg_key_cloud);
    SITEC_LOG(AL_INFO, "%s debug environment is %d", SITEC_LOG_NAME, is_sitec_dev_environment);

}

int sitec_is_dev_environment(void)
{
    return is_sitec_dev_environment;
}

int zpn_sitec_enroll(int always_re_enroll)
{
    char str[1000];
    char debug_str[1000];
    struct stat st;
    struct zpn_enroll_state *enroll_state;
    int result;
    int res;
    int i;
    FILE *fp;
    int write_cloud_root = 0;
    char zhw_err_str[1000] = {0};
    int validate_hw_key = 0;
    int load_hw_info_fail = 0;
    char api_version[10] = {0};
    char token[1024] = {0};

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    /* Init enrollment lib */
    if (zpn_enroll_init(&gs->enroll_state) != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to init enrollment api");
        sleep(1);
        exit(1);
    }
    /* sitec only does V2 or V3 or V4 enrollment */
    gs->enroll_version = 2;

    /* Local pointer to enroll state */
    enroll_state = gs->enroll_state;

    /* Set enroll style */
    enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V2;

    /* If OAuth based enrollment is supported, then use V4 Enrollment APIs */
    if (gs->oauth_enroll) {
        enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V4;
        gs->enroll_version = 4;
    }

    /* Set role name */
    snprintf(gs->role_name, sizeof(gs->role_name), "%s", SITEC_LOG_NAME);

    /* Use global zcdns */
    enroll_state->zcdns = gs->zcdns;
    SITEC_LOG(AL_NOTICE, "Checking %s Enrollment", gs->role_name);

    /* Init ZVM */
    result = zvm_init(zpath_event_collection, enroll_state->zcdns);
    if (result) {
        SITEC_LOG(AL_ERROR, "Cannot init zvm");
        return 1;
    }

    memset(debug_str, 0, sizeof(debug_str));
    load_hw_info_fail = load_prev_zhw_id_sha_info();
    if (load_hw_info_fail) {
        /* This is not a critical error for us to take an aggressive action */
        SITEC_LOG(AL_DEBUG, "Error while loading hw_id_info from file %s, it is possible that the file is not present", INSTANCE_ID_BIN_FILE);
    }

    /* Get Hardware ID */
    result = zhw_id_get(&gs->cfg_hw_id, str, sizeof(str), zhw_err_str, sizeof(zhw_err_str), 1);
    if (strlen(zhw_err_str)) {
        SITEC_LOG(AL_ERROR, "Error occurred when getting hardware id: %s", zhw_err_str);
    }
    if (result) {
        SITEC_LOG(AL_ERROR, "Cannot get hardware id");
        return 1;
    }

    /* Generate key */
    if (zcrypt_gen_key(&gs->cfg_hw_key,
                       gs->cfg_hw_id.id,
                       sizeof(gs->cfg_hw_id.id)) != ZCRYPT_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Cannot get key");
        return 1;
    }

    if (!load_hw_info_fail) {
        memset(debug_str, 0, sizeof(debug_str));
        res = validate_zhw_id_sha_info(&validate_hw_key, debug_str, sizeof(debug_str));
        if (!res) {
            if (!validate_hw_key && strnlen(debug_str, sizeof(debug_str))) {
                SITEC_LOG(AL_ERROR, "Validation for HW ID failed due to change in %s", debug_str);
            }
        } else {
            SITEC_LOG(AL_ERROR, "Cannot validate current zhw id info with the previous info");
        }
    } else {
        res = store_curr_zhw_id_sha_info();
        if (res) {
            SITEC_LOG(AL_DEBUG, "Error while storing current zhw id info into a file %s", INSTANCE_ID_BIN_FILE);
        }
    }

    /* Get instance */
    res = zpn_enroll_get_instance_id(&gs->cfg_hw_key, gs->cfg_instance_bytes);
    if (res) {
        SITEC_LOG(AL_ERROR, "Cannot get instance id");
        return 1;
    }

    for (i = 0; i < INSTANCE_ID_BYTES; i++) {
        gs->cfg_fingerprint_bytes[i] = gs->cfg_instance_bytes[i] ^ gs->cfg_hw_id.id[i];
    }

    /* Base64 enocde fingerprint */
    base64_encode_binary(gs->cfg_fingerprint_str,
                         gs->cfg_fingerprint_bytes,
                         sizeof(gs->cfg_fingerprint_bytes));

    /* Get provisioning key */
    if (gs->oauth_enroll) {
        res = zpn_enroll_get_oauth_key(gs->cfg_provisioning_key, sizeof(gs->cfg_provisioning_key));
    } else {
        res = zpn_enroll_get_provisioning_key(&gs->cfg_hw_key,
                                          gs->cfg_provisioning_key,
                                          sizeof(gs->cfg_provisioning_key));
    }
    if (res) {
        ZPATH_LOG(AL_ERROR, "Cannot get provisioning key, please check if provision_key exists in the current working directory");
        return 1;
    }

    enroll_state->cfg_rsa_key = zcrypt_rsa_key_create();
    if (!enroll_state->cfg_rsa_key) {
        SITEC_LOG(AL_ERROR, "Cannot create keyholder\n");
        return ZPATH_RESULT_ERR;
    }

    /* Get private key */
    if (zpn_enroll_get_private_key(&gs->cfg_hw_key,
                                   enroll_state->cfg_rsa_key,
                                   FILENAME_KEY) != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Cannot get private key\n");
        return ZPATH_RESULT_ERR;
    }

    /* Read JWT Token required for enrollment. This will be available only for the fresh
     * enrollment using OAuth Server
     */
    if (gs->oauth_enroll) {
        fp = fopen(FILENAME_OAUTH_TOKEN, "r");
        if (fp) {
            if (fgets(token, sizeof(token), fp) == NULL) {
                ZPATH_LOG(AL_ERROR, "[OAuth] Unable to read JWT token needed for V4 enrollment %s - %s",
                                                                                          FILENAME_OAUTH_TOKEN,
                                                                                          strerror(errno));
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            fclose(fp);
        }
    }

    int new_provision_key = 0;
    struct evbuffer *evbuffer = NULL;

    if (gs->oauth_enroll) {
        res = zpath_get_oauth_cloud_details(gs->cfg_provisioning_key, gs->cfg_key_api, gs->cfg_key_cloud, &gs->customer_id);
    } else {
        res = zpath_get_provisioning_key_details(gs->cfg_provisioning_key, gs->cfg_key_api, gs->cfg_key_cloud);
    }

    /* Only if parsing of new provisioning key is success
     * and we have a valid cloud name, we call the enrollment with api and cloud name
     */

    if ((!gs->cloud_config) &&
        (gs->cfg_key_cloud[0] != '\0') &&
        ( res == ZPATH_RESULT_NO_ERROR ) ) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(gs->cfg_key_cloud,
                                                        enroll_state->cfg_rsa_key,
                                                        gs->cfg_fingerprint_str,
                                                        gs->cfg_provisioning_key,
                                                        gs->cfg_key_api,
                                                        sizeof(gs->cfg_key_api),
                                                        ZPN_ENROLLMENT_TYPE_SITEC,
                                                        enroll_state->enroll_style,
                                                        token,
                                                        gs->customer_id);
        new_provision_key = 1;
    }
    result = zpath_load_cloud_config_for_customer_apps(&gs->cfg_hw_key, gs->cfg_key_api, evbuffer);
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Cannot load zpa_cloud_config for the given cloud ");
        return -1;
    }
    /* Enroll provisioning key */
    if (!gs->oauth_enroll && zpn_enroll_extract_key_fields(gs->cfg_provisioning_key,
                                      &gs->cfg_key_shard,
                                      gs->cfg_key_api,
                                      sizeof(gs->cfg_key_api),
                                      gs->cfg_key_cloud,
                                      sizeof(gs->cfg_key_cloud),
                                      &gs->root_cert,
                                      &gs->root_cert_time) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Invalid provisioning key: <%.*s...>",
                  PROVISION_KEY_LOG_BYTES,
                  gs->cfg_provisioning_key);
        return 1;
    }
    /* Try fetching the cloud config based on cloud name, only if we dont have valid cloud config
    */
    if(!gs->cloud_config) {
        gs->cloud_config=zpath_get_cloud_config_from_name(gs->cfg_key_cloud);
    }
    if (!gs->cloud_config) {
        ZPATH_LOG(AL_ERROR, "Unable to fetch cloud configuration file");
        return 1;
    }
    zthread_set_cloud_name(gs->cfg_key_cloud);

    ZPATH_LOG(AL_NOTICE, "Provisioning key shard = %d, api = %s",
              gs->cfg_key_shard, gs->cfg_key_api);

    if ( new_provision_key == 0 ) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(gs->cfg_key_cloud,
                                                        enroll_state->cfg_rsa_key,
                                                        gs->cfg_fingerprint_str,
                                                        gs->cfg_provisioning_key,
                                                        gs->cfg_key_api,
                                                        sizeof(gs->cfg_key_api),
                                                        ZPN_ENROLLMENT_TYPE_SITEC,
                                                        enroll_state->enroll_style,
                                                        token,
                                                        gs->customer_id);
    }

    result = zpn_enroll_get_enrollment_details(gs->cfg_key_api, sizeof(gs->cfg_key_api), evbuffer,
                                               gs->cfg_key_cloud,
                                               &gs->root_cert,
                                               &gs->root_cert_time,
                                               &gs->customer_id,
                                               api_version,
                                               sizeof(api_version),
                                               NULL, 0, NULL);
    if (result) {
        /*
         * Get enrollment details failed, still continue and use api hostname mentioned in
         * provision key and cloud cert coming from hardcoded data.
         */
        SITEC_LOG(AL_ERROR, "Could not fetch enrollment details but it's okay, continuing with existing data");
    } else {
        /* Use V3 enrollment style if api_version returned is "V3". Otherwise V2 by default. */
        if (api_version[0] != 0 && strcmp(api_version, "V3") == 0) {
            gs->enroll_version = 3;
            enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V3;
            ZPN_LOG(AL_DEBUG, "Received api_version: %s in get_enrollment_details response, using api style %d for enrollment",
                    api_version, enroll_state->enroll_style);
        }
    }

    fp = fopen(FILENAME_CLOUD_ROOT, "wx");

    if (!gs->root_cert && fp == NULL) {
        /*
         * This is a fresh enrollment (cloud.pem doesn't exist) and couldn't get cloud cert
         * from hardcoded data and fetch from api also failed. Log and stop.
         */
        SITEC_LOG(AL_ERROR, "Could not get cloud cert, stopping.");
        return ZPATH_RESULT_ERR;
    }

    /* Enrollment check */
    result = zpn_enroll_check(&gs->cfg_hw_key,
                              enroll_state->cfg_rsa_key,
                              gs->cfg_fingerprint_str,
                              gs->cfg_provisioning_key,
                              gs->cfg_key_shard,
                              gs->cfg_key_api,
                              ZPN_ENROLLMENT_TYPE_SITEC,
                              enroll_state->enroll_style,
                              gs->cfg_key_cloud,
                              SITEC_PROVISION_KEY_PATH,
                              always_re_enroll,
                              0,
                              gs->customer_id,
                              token);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to enroll.");
        if (fp) fclose(fp);
        return ZPATH_RESULT_ERR;
    }

    if (fp == NULL) {
        // File not found...
        write_cloud_root = 1;
    } else {
        res = stat(FILENAME_CLOUD_ROOT, &st);
        // Do root_cert_time check only if it's known cloud
        if (gs->root_cert && (res == 0 && st.st_mtime != gs->root_cert_time)) {
            // File has wrong timestamp...
            write_cloud_root = 1;
            SITEC_LOG(AL_NOTICE, "sitec is updating cloud certificates");
        }
        fclose(fp);
        if (write_cloud_root) {
            unlink(FILENAME_CLOUD_ROOT);
        }
    }

    if (write_cloud_root) {
        struct utimbuf tm;

        fp = fopen(FILENAME_CLOUD_ROOT, "w");
        if (!fp) {
            SITEC_LOG(AL_ERROR, "Could not open file for writing cloud certificates");
            return ZPATH_RESULT_ERR;
        }
        if (fwrite(gs->root_cert, strlen(gs->root_cert), 1, fp) != 1) {
            fclose(fp);
            SITEC_LOG(AL_ERROR, "Could not write cloud certificates");
            return ZPATH_RESULT_ERR;
        }

        tm.actime = gs->root_cert_time;
        tm.modtime = gs->root_cert_time;
        if (utime(FILENAME_CLOUD_ROOT, &tm) != 0) {
            SITEC_LOG(AL_ERROR, "Could not update modification time on cloud certificates: %s. Continuing...", strerror(errno));
        }
        fclose(fp);
    }

    gs->cfg_pkey = EVP_PKEY_new();
    res = EVP_PKEY_set1_RSA(gs->cfg_pkey, zcrypt_rsa_key_get_rsa(enroll_state->cfg_rsa_key));
    if (res != 1) {
        ZPATH_LOG(AL_ERROR, "Could not configure private key");
        return ZPATH_RESULT_ERR;
    }

    result = fohh_reset_global_ssl_ctx(FILENAME_CLOUD_ROOT,
                                       FILENAME_CERT,
                                       FILENAME_KEY_PRIV,
                                       gs->cfg_pkey,
                                       VERIFY_PEER);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not configure SSL for cloud communication");
        return ZPATH_RESULT_ERR;
    }

    /* Need to read sitec ID out of certificate */
    {
        struct zcrypt_cert *cert;
        char *w;

        cert = zcrypt_cert_read(FILENAME_CERT);
        if (!cert) {
            SITEC_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (fohh_x509_get_cn(zcrypt_cert_get_x509(cert), gs->cn, sizeof(gs->cn))) {
            SITEC_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }
        zcrypt_cert_free(cert);


        /* Parse first integer of CN */
        for (w = &(gs->cn[0]); *w; w++) {
            if (isdigit(*w)) {
                gs->sitec_id = strtoll(w, &w, 0);
                break;
            }
        }

        if (!gs->sitec_id) {
            SITEC_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }
    }

    gs->customer_id = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);
    zpn_enroll_upload_stack(gs->cloud_config->sarge->stack_upload_host,
                            gs->cloud_config->sarge->stack_upload_path,
                            APP_ROLE,
                            gs->cn,
                            gs->customer_id);

    zpn_enroll_upload_stack(gs->cloud_config->sarge->stack_upload_host,
                            gs->cloud_config->sarge->stack_upload_path,
                            SARGE_ROLE,
                            gs->cn,
                            gs->customer_id);

    /* Set dev environmaent if applicable */
    set_is_sitec_dev_environment();

    SITEC_LOG(AL_DEBUG, "Site Controller Enrollment Initialization Complete - %"PRId64"",
            gs->sitec_id);

    return ZPATH_RESULT_NO_ERROR;
}

char *zpn_sitec_get_cloud_name()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char *cloud_name;

    if (gs->alt_cloud && gs->alt_cloud[0] && gs->alt_cloud_enabled) {
        cloud_name = gs->alt_cloud;
    } else {
        cloud_name = gs->cfg_key_cloud;
    }

    return cloud_name;
}

int zpn_sitec_conn_sni_init(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    /* Create Broker: control, data, config connections to public broker */
    if (gs->enroll_version == 2 || gs->enroll_version == 3 || gs->enroll_version == 4) {
        char *cloud_name = zpn_sitec_get_cloud_name();

        snprintf(sc_sni_customer_domain_cfg, sizeof(sc_sni_customer_domain_cfg),
                "%"PRId64".sccfg.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_rcfg, sizeof(sc_sni_customer_domain_rcfg),
                "%"PRId64".scrcfg.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_ctl, sizeof(sc_sni_customer_domain_ctl),
                "%"PRId64".scctl.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_log, sizeof(sc_sni_customer_domain_log),
                "%"PRId64".sclog.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_ovd, sizeof(sc_sni_customer_domain_ovd),
                "%"PRId64".scovd.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_stats, sizeof(sc_sni_customer_domain_stats),
                "%"PRId64".scstats.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_pblog, sizeof(sc_sni_customer_domain_pblog),
                "%"PRId64".scpblog.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_alog, sizeof(sc_sni_customer_domain_alog),
                "%"PRId64".scalog.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_pbstats, sizeof(sc_sni_customer_domain_pbstats),
                "%"PRId64".scpbstats.%s", gs->sitec_id, cloud_name);
        snprintf(sc_sni_customer_domain_astats, sizeof(sc_sni_customer_domain_astats),
                "%"PRId64".scastats.%s", gs->sitec_id, cloud_name);
    } else {
        SITEC_LOG(AL_ERROR, "Unsupported sitec enroll version: %d", gs->enroll_version);
        return ZPATH_RESULT_BAD_STATE;
    }

    return ZPN_RESULT_NO_ERROR;

}

#define UPDATE_DISABLE_FLAG(var_add, var_remove, condition, flag)   \
    do {                                                            \
        if (condition) {                                            \
            var_add |= (flag);                                      \
        } else {                                                    \
            var_remove |= (flag);                                   \
        }                                                           \
    } while (0)

const char* zpn_sitec_get_disable_flag()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->disable_flags == 0) {
        return "enabled";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DELETED) {
        return "sitec is deleted";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DISABLED) {
        return "sitec is disabled";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DELETED) {
        return "sitec group is deleted";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DISABLED) {
        return "sitec group is disabled";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITE_IS_DELETED) {
        return "site is deleted";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITE_IS_DISABLED) {
        return "site is disabled";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_REMOVED_FROM_SITE) {
        return "site gid is zero";
    } else if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_DISABLED_BY_CURL) {
        return "disabled by curl";
    }

    return "undefined";
}

void zpn_sitec_update_service(zpn_sitec_disable_flag_t add, zpn_sitec_disable_flag_t remove, int force)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    uint32_t current_flags = gs->disable_flags;
    int allow_c2site = 0;
    int res;

    gs->disable_flags |= add;
    gs->disable_flags &= ~(uint32_t)remove;

    SITEC_LOG(AL_DEBUG, "Update service: disable_flags 0x%02x is changed to 0x%02x, force update %d",
            current_flags, gs->disable_flags, force);

    if (!!current_flags == gs->disable_flags && !force) {
        return;
    }

    int enable = !gs->disable_flags;

    if (enable) {
        res = zpn_sitec_get_new_user_support(&allow_c2site);
        if (res) {
            SITEC_LOG(AL_ERROR, "Unable to get new_user_support: %s, assume it is disabled",
                    zpn_result_string(res));
            allow_c2site = 0;
        }
    }

    zpn_sitec_enable_pse_snis(enable);
    zpn_sitec_enable_asst_snis(enable);
    zpn_sitec_enable_client_snis(allow_c2site, enable);
    zpn_sitec_enable_sitec_snis(enable);

    if (gs->sni_server) {
        if (gs->offline_domain) {
            char bcpsp[1024];
            snprintf(bcpsp, sizeof(bcpsp), "bcpsp.%s", gs->offline_domain);
            res = fohh_generic_server_set_domain_disabled(gs->sni_server, bcpsp, 0, !enable);
            if (res) {
                SITEC_LOG(AL_ERROR, "Could not %s bcpsp, sni is %s", enable? "enable" : "disable", bcpsp);
            }
        }

        if (gs->sitesp_fqdn) {
            res = fohh_generic_server_set_domain_disabled(gs->sni_server, gs->sitesp_fqdn, 0, !enable);
            if (res) {
                SITEC_LOG(AL_ERROR, "Could not %s sitesp fqdn, sni is %s", enable? "enable" : "disable", gs->sitesp_fqdn);
            }
        }
    }

    if (gs->disable_flags & ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DELETED) {
        zpn_sitec_delete_broker_connections(FOHH_CLOSE_REASON_SITEC_DELETED);
    }
}

int zpn_sitec_cfg_override_register()
{
    int res;

    static struct zpath_config_override_desc sitec_cfg_descriptions[] = {
        {
            .key                = ZPN_SITEC_REGISTRATION_WALLY_ENABLED,
            .desc               = "If enabled, sietc will have static/registration wally connection, otherwise it will get static tables from shard wally connection",
            .details            = "If enabled, sitec will have static/registration wally connection, otherwise it will get static tables from shard wally connection. (by default it is disabled.)\n"
                                  "The target_gid_types from most specific to most general are: instance, customer, and global. Instance group is not available",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PSE_REGISTRATION_WALLY_ENABLED_MIN,
            .int_range_hi       = ZPN_PSE_REGISTRATION_WALLY_ENABLED_MAX,
            .int_default        = ZPN_PSE_REGISTRATION_WALLY_ENABLED_DEFAULT,
            .feature_group      = FEATURE_GROUP_SITE_CONTROLLER_STATIC_WALLY,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = CONFIG_FEATURE_ALT_CLOUD,
            .desc               = "feature flag to enable or disable support for alt cloud",
            .details            = "1: feature is enabled.\n"
                                "0: feature is disabled.\n"
                                "Order of check: component id, component group id, customer gid, global id\n"
                                "default: 1, feature is enabled",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ALT_CLOUD_DISABLED,
            .int_range_hi       = CONFIG_FEATURE_ALT_CLOUD_ENABLED,
            .int_default        = CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
            .feature_group      = FEATURE_GROUP_ALT_CLOUD,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = FOHH_CONNECTION_SETUP_TIMEOUT,
            .desc               = "timeout option to delete fohh connections that doesn't progress to connected state",
            .details            = "Control timeout config in seconds to delete incomplete fohh connections, between 10 seconds to 60 seconds\n"
                                "Order of check: component id, component group id, customer gid, global id\n"
                                "default: 10 (i.e. fohh connection that doesn't progress to connected state within 10 secs will be deleted by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MIN,
            .int_range_hi       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MAX,
            .int_default        = FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
            .feature_group      = FEATURE_GROUP_FOHH,
            .value_traits       = config_value_traits_normal,
        },
        {
            .key                = ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE,
            .desc               = "Control sitec MMDB download disable",
            .details            = "Control sitec MMDB download disable, 0 = allow mmdb download, 1 = disable mmdb download\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 0 (i.e. PSE mmdb download is enabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_MIN,
            .int_range_hi       = DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_MAX,
            .int_default        = DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE,
            .feature_group      = FEATURE_GROUP_COUNTRY_CODE_POLICY,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = SITEC_CLIENT_AUTH_TIMEOUT,
            .desc               = "timeout option to delete client connection that doesn't complete authentication within the configured time",
            .details            = "Control timeout config in seconds to delete unauthenticated client connections, between 20 seconds to 120 seconds\n"
                                "Order of check: component id, component group id, customer gid, global id\n"
                                "default: 20 (i.e. client connection that doesn't complete authentication within 20 secs will be deleted by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = SITEC_CLIENT_AUTH_TIMEOUT_MIN_SEC,
            .int_range_hi       = SITEC_CLIENT_AUTH_TIMEOUT_MAX_SEC,
            .int_default        = SITEC_CLIENT_AUTH_TIMEOUT_SEC,
            .feature_group      = FEATURE_GROUP_SITEC_SESSION_AUTHENTICATION,
            .value_traits       = config_value_traits_normal,
        },
        {
            .key                = SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE,
            .desc               = "Control sitec comprehensive stats enable/disable, used by sitec dashboard and Magellan",
            .details            = "Control sitec comprehensive stats enable/disable, 0 = sitec comprehensive stats disabled, 1 = sitec comprehensive stats enabled\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 1 (i.e. sitec comprehensive stats enabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MIN,
            .int_range_hi       = SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MAX,
            .int_default        = SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT,
            .feature_group      = FEATURE_GROUP_SITE_CONTROLLER_STATS,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = CONFIG_FEATURE_SITEC_FPROXY,
            .desc               = "Feature Knob for sitec forward proxy",
            .details            = "Enable/Disable localhost forward proxy on sitec, default port 8510\n"
                                        "default: 0 (disabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
            .int_range_lo       = 0,
            .int_range_hi       = 1,
            .int_default        = CONFIG_FEATURE_SITEC_FPROXY_DEFAULT_STATUS,
            .feature_group      = FEATURE_GROUP_AUTH_SNI,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = SITEC_SARGE_UPGRADE_ENABLE,
            .desc               = "Enable automatic upgrade of Sitec sarge",
            .details            = "1: feature is enabled\n"
                                  "0: feature is disabled \n"
                                  "default: 0",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = SITEC_SARGE_UPGRADE_ENABLE_MIN,
            .int_range_hi       = SITEC_SARGE_UPGRADE_ENABLE_MAX,
            .int_default        = DEFAULT_SITEC_SARGE_UPGRADE_ENABLE,
            .feature_group      = FEATURE_GROUP_SARGE,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = AUTOMATIC_OS_UPGRADE_ENABLE,
            .desc               = "Enable automatic upgrade of OS",
            .details            = "1: feature is enabled\n"
                                  "0: feature is disabled \n"
                                  "default: 0",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = AUTOMATIC_OS_UPGRADE_ENABLE_MIN,
            .int_range_hi       = AUTOMATIC_OS_UPGRADE_ENABLE_MAX,
            .int_default        = DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
            .feature_group      = FEATURE_GROUP_SARGE,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
            .feature_group      = FEATURE_GROUP_SARGE,
            .desc               = "Enable automatic upgrade of entire OS",
            .details            = "1: feature is enabled\n"
                                  "0: feature is disabled \n"
                                  "default: 0",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_feature_enablement,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = AUTOMATIC_FULL_OS_UPGRADE_MIN,
            .int_range_hi       = AUTOMATIC_FULL_OS_UPGRADE_MAX,
            .int_default        = DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE
        },
        {
            .key                = SARGE_BACKUP_VERSION_ENABLE,
            .feature_group      = FEATURE_GROUP_SARGE,
            .desc               = "Enable/disable backup sarge version feature for sarge",
            .details            = "1: feature is enabled\n"
                                  "0: feature is disabled \n"
                                  "default: 0",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_feature_enablement,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = SARGE_BACKUP_VERSION_ENABLE_MIN,
            .int_range_hi       = SARGE_BACKUP_VERSION_ENABLE_MAX,
            .int_default        = DEFAULT_SARGE_BACKUP_VERSION_ENABLE
        },
        {
            .key                = SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE,
            .desc               = "Control sitec split brain config sync enable/disable",
            .details            = "Control sitec split brain config sync enable/disable, 0 = sitec config sync disabled, 1 = sitec config sync enabled\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 0 (i.e. sitec split brain config sync disabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_MIN,
            .int_range_hi       = SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_MAX,
            .int_default        = SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_DEFAULT,
            .feature_group      = FEATURE_GROUP_SITE_CONTROLLER,
            .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = SITEC_OAUTH_ENROLL_DISABLE,
            .feature_group      = FEATURE_GROUP_SARGE,
            .desc               = "Enable enrollment via OAuth service",
            .details            = "1: OAuth Enrollment disabled\n"
                                  "0: OAuth Enrollment enabled \n"
                                  "default: 0 (Oauth Enrollment Enabled)",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_feature_enablement,
            .component_types    = config_component_sitec,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = SITEC_OAUTH_ENROLL_DISABLE_MIN,
            .int_range_hi       = SITEC_OAUTH_ENROLL_DISABLE_MAX,
            .int_default        = DEFAULT_SITEC_OAUTH_ENROLL_DISABLE
        }
    };

    /* Register all descriptions */
    int len = sizeof(sitec_cfg_descriptions) / sizeof(sitec_cfg_descriptions[0]);
    for (int i = 0; i < len; i++) {
        res = zpath_config_override_desc_register(&sitec_cfg_descriptions[i]);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Unable to register sitec_cfg_descriptions[%d] for key: %s, err: %s", i, sitec_cfg_descriptions[i].key, zpn_result_string(res));
            return res;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_is_split_brain_cfg_sync_enabled()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);
    int64_t enabled;

    enabled = zpath_config_override_get_config_int(SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE,
                                                   &enabled,
                                                   SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_DEFAULT,
                                                   gs->sitec_id,
                                                   gs->sitec_group_id,
                                                   customer_gid,
                                                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                   (int64_t)0);
    return enabled;
}

int zpn_sitec_monitor_geoip_configuration()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    zpath_config_override_monitor_int(ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE,
                                      &g_sitec_geoip_disable,
                                      NULL,
                                      DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE,
                                      (int64_t)gs->sitec_id,
                                      (int64_t)gs->sitec_group_id,
                                      (int64_t)gs->customer_id,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);


    SITEC_LOG(AL_INFO, "start sitc geoip config monitor for sitec_id:%"PRId64" sitec_grp:%"PRId64" customer_gid:%"PRId64"", gs->sitec_id, gs->sitec_group_id, gs->customer_id);

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_on_offline_domain_change(const char* offline_domain, int need_exit)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->offline_domain && strcmp(gs->offline_domain, offline_domain) != 0) {
        SITEC_LOG(AL_CRITICAL, "Offline domain is changed from %s to %s", gs->offline_domain, offline_domain);

        const char *files[] = {SITESP_SIGNING_PKEY_FILE, SITESP_PKEY_FILE, FILENAME_ID, FILENAME_CERT};

        for (int i = 0; i < sizeof(files) / sizeof(char*); ++i) {
            if (unlink(files[i]) == 0) {
                ZPN_LOG(AL_NOTICE, "Successfully deleted file '%s'", files[i]);
            } else {
                /* Check if the error is ENOENT (no such file or directory) */
                if (errno != ENOENT) {
                    ZPN_LOG(AL_ERROR, "Cannot delete file '%s', error: %s", files[i], strerror(errno));
                }
            }
        }

        if (need_exit) {
            SITEC_LOG(AL_CRITICAL, "Offline domain is changed, restarting to reload the certs and keys");
            sleep(1);
            exit(1);
        }
    }

    zpn_sitec_update_offline_domain(offline_domain);
}

int zpn_sitec_add_debug_allocator()
{
    int res;

    res = zpath_debug_add_allocator(&zsaml_allocator, "zsaml");
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not add zsaml allocator");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_allocator(&zse_allocator, "zse");
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not add zse allocator");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_allocator(&zpe_allocator, "zpe");
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not add zpe allocator");
        return ZPN_RESULT_ERR;
    }


    return res;
}

static int zpn_sitec_cmd_change_offline_domain(struct zpath_debug_state* request_state,
                                               const char **             query_values,
                                               int                       query_value_count,
                                               void*                     cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    const char* domain = query_values[0] ? query_values[0] : NULL;
    int need_exit = query_values[1] ? atoi(query_values[1]) : 0;

    const char* original_domain = zpn_sitec_get_offline_domain();

    ZDP("  Original offline domain: '%s'\n", original_domain ? original_domain : "");

    if (domain) {
        ZDP("       Old offline domain: '%s'\n", gs->offline_domain ? gs->offline_domain : "");
        zpn_sitec_on_offline_domain_change(domain, need_exit);
        ZDP("       New offline domain: '%s'\n", gs->offline_domain ? gs->offline_domain : "");
    } else {
        ZDP("   Current offline domain: '%s'\n", gs->offline_domain ? gs->offline_domain : "");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_cmd_show_config(struct zpath_debug_state* request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_ddil_config *ddil_config;
    struct zpn_site *site;
    size_t row_count = 1;
    int res;

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id,
                                              &ddil_config,
                                              &row_count,
                                              NULL,
                                              0,
                                              0);
    if (res) {
        ZDP("Failed to get ddil_config, error: %s\n", zpath_result_string(res));
        return ZPN_RESULT_ERR;
    }

    res = zpn_site_get_by_customer_gid(gs->customer_id,
                                       &site,
                                       &row_count,
                                       NULL,
                                       0,
                                       0);
    if (res) {
        ZDP("Failed to get site config, error: %s\n", zpath_result_string(res));
        return ZPN_RESULT_ERR;
    }

    ZDP("%26s: %"PRIu64"\n", "instance_gid", gs->sitec_id);
    ZDP("%26s: %"PRIu64"\n", "customer_gid", ddil_config->customer_gid);
    ZDP("%26s: %"PRIu64"\n", "group_gid", gs->sitec_group_id);
    ZDP("%26s: %"PRIu64"\n", "site_gid", gs->site_gid);
    ZDP("%26s: %s, 0x%02x\n", "enabled", zpn_sitec_get_disable_flag(), gs->disable_flags);
    ZDP("%26s: %s\n", "offline_domain", ddil_config->offline_domain);
    ZDP("%26s: %d\n", "fproxy", (int)gs->fproxy_enabled);
    ZDP("%26s: %"PRIu64" days\n", "reenroll_period", site->reenroll_period);
    ZDP("%26s: %d\n", "sitec_preferred", site->sitec_preferred);
    ZDP("%26s: %d\n", "new_user_support", ddil_config->new_user_support);
    ZDP("%26s: %"PRIu64" seconds\n", "max_allowed_downtime_s", ddil_config->max_allowed_downtime_s);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_cmd_disable_service(struct zpath_debug_state* request_state,
                                         const char **             query_values,
                                         int                       query_value_count,
                                         void*                     cookie)
{
    zpn_sitec_update_service(ZPN_SITEC_DISABLE_FLAG_DISABLED_BY_CURL, 0, 0);
    ZDP("Site controller is disabled, wont accept new connections\n");
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_firedrill_stats(struct zpath_debug_state* request_state,
                                    const char **             query_values,
                                    int                       query_value_count,
                                    void*                     cookie)
{
    struct zpn_firedrill_stats sitec_firedrill_stats;
    sitec_firedrill_stats.firedrill_triggered_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_triggered_count, 0);
    sitec_firedrill_stats.firedrill_completed_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_completed_count, 0);
    sitec_firedrill_stats.firedrill_transit_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_transit_count, 0);
    sitec_firedrill_stats.firedrill_cmdline_disable_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_cmdline_disable_count, 0);

    ZDP("Firedrill stats:\n");
    ZDP("Triggered:%d\n", sitec_firedrill_stats.firedrill_triggered_count);
    ZDP("Completed:%d\n", sitec_firedrill_stats.firedrill_completed_count);
    ZDP("Transit triggered:%d\n", sitec_firedrill_stats.firedrill_transit_count);
    ZDP("CmdLine disabled:%d\n", sitec_firedrill_stats.firedrill_cmdline_disable_count);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_firedrill_status(struct zpath_debug_state* request_state,
                                    const char **             query_values,
                                    int                       query_value_count,
                                    void*                     cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char *ptr;
    switch(gs->firedrill_status) {
        case ZPN_SITEC_FIREDRILL_DISABLED:
            ptr = "disabled";
            break;
        case ZPN_SITEC_FIREDRILL_ENABLED:
            ptr = "enabled";
            break;
        case ZPN_SITEC_FIREDRILL_TRANSIT:
            ptr = "transit";
            break;
        default:
            ptr = "invalid";
            break;
    }
    ZDP("Firedrill status %s\n", ptr);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_cmd_disable_firedrill(struct zpath_debug_state* request_state,
                                         const char **             query_values,
                                         int                       query_value_count,
                                         void*                     cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();


    if(ZPN_SITEC_FIREDRILL_DISABLED == gs->firedrill_status) {
        ZDP("Site controller is not in firedrill mode\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("Disable firedrill and switch back to the cloud\n");

    /* stop the pcc firedrill timer */
    if(ev_firedrill_timer) {
        event_del(ev_firedrill_timer);
    }

    /* set the firedrill status to zero */
    gs->firedrill_status = ZPN_SITEC_FIREDRILL_TRANSIT;

    g_firedrill_disable_invoked_on_cmdline = 1;

    if(fohh_thread_call( fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MC), zpn_sitec_mission_critical_conn_create, NULL, 0)) {
        ZPN_LOG(AL_CRITICAL, "Implement me");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_cmd_restore_service(struct zpath_debug_state* request_state,
                                         const char **             query_values,
                                         int                       query_value_count,
                                         void*                     cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    zpn_sitec_update_service(0, ZPN_SITEC_DISABLE_FLAG_DISABLED_BY_CURL, 0);
    ZDP("Site controller is restored to configured status: %s (0x%02x)\n", zpn_sitec_get_disable_flag(), gs->disable_flags);

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_backup_progress(void *request_state, int remain, int page_count, int *current_percent)
{
    int percent_complete;

    percent_complete = 100 * (page_count - remain)/page_count;
    if (*current_percent != percent_complete) {
        SITEC_LOG(AL_NOTICE, "%d%% completed", percent_complete);
        *current_percent = percent_complete;
    }
}

static int zpn_sitec_cmd_sqlt_backup(struct zpath_debug_state* request_state,
                                     const char **             query_values,
                                     int                       query_value_count,
                                     void*                     cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int res = ZPN_RESULT_NO_ERROR;
    const char* db = query_values[0] ? query_values[0] : NULL;
    struct wally_origin *slave_db[4];
    int db_count;

    if (db == NULL) {
        ZDP("provide DB option to backup. It can be master|shard|static|userdb|all\n");
        return ZPN_RESULT_NO_ERROR;
    }

    SITEC_LOG(AL_NOTICE, "starting SQLite backup with db:%s", db);

    if (strcmp(db, "master") == 0) {
        slave_db[0] = gs->sitec_state->ovd_slave_db;
        db_count = 1;
    } else if (strcmp(db, "shard") == 0) {
        slave_db[0] = gs->sitec_state->cfg_slave_db;
        db_count = 1;
    } else if (strcmp(db, "static") == 0) {
        slave_db[0] = gs->sitec_state->rcfg_slave_db;
        db_count = 1;
    } else if (strcmp(db, "userdb") == 0) {
        slave_db[0] = gs->sitec_state->userdb_slave_db;
        db_count = 1;
    } else if (strcmp(db, "all") == 0) {
        slave_db[0] = gs->sitec_state->ovd_slave_db;
        slave_db[1] = gs->sitec_state->rcfg_slave_db;
        slave_db[2] = gs->sitec_state->cfg_slave_db;
        slave_db[3] = gs->sitec_state->userdb_slave_db;
        db_count = 4;

    } else {
        ZDP("provide DB option to backup. It can be master|shard|userdb|all\n");
        return ZPN_RESULT_NO_ERROR;
    }

    for (int i=0; i<db_count; i++) {
        if (slave_db[i]) {
            SITEC_LOG(AL_NOTICE, "Initiating SQLite backup origin:%s", slave_db[i]->name);
            res = wally_sqlt_backup(slave_db[i],
                                    "/opt/zscaler/backup",
                                    request_state,
                                    zpn_sitec_backup_progress);
            if (res == ZPN_RESULT_NO_ERROR) {
                SITEC_LOG(AL_NOTICE, "SQLite backup for origin:%s complete", slave_db[i]->name);
            }
        }
    }
    ZDP("Backup completed with return value:%d\n", res);

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_get_database_sync_times(int64_t *master, int64_t *shard, int64_t *userdb)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_site_controller_version *version;
    int result;

    *master = wally_get_last_remote_sync_time(gs->sitec_state->ovd_wally, NULL);
    *shard = wally_get_last_remote_sync_time(gs->sitec_state->cfg_wally, gs->shard_skip_table);
    struct wally *userdb_wally = wally_fohh_client_get_wally(gs->sitec_state->userdb_wally_conn);
    *userdb = wally_get_last_remote_sync_time(userdb_wally, NULL);

    result = zpn_sitec_version_get_by_id(gs->sitec_id,
                                        &version,
                                        NULL,
                                        NULL,
                                        0);

    if (result == ZPN_RESULT_NO_ERROR) {
        if (*master == 0) {
            *master = version->master_last_sync_time;
        }
        if (*shard == 0) {
            *shard = version->shard_last_sync_time;
        }
        if (*userdb == 0) {
            *userdb = version->userdb_last_sync_time;
        }
    }
}

static int zpn_sitec_display_last_sync_time(struct zpath_debug_state* request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    int64_t master, shard, userdb;
    char master_time_str[255];
    char shard_time_str[255];
    char userdb_time_str[255];
    struct  tm tm_info;
    time_t sec;

    zpn_sitec_get_database_sync_times(&master, &shard, &userdb);

    memset(&tm_info, 0, sizeof(struct tm));
    sec = (time_t)master;
    localtime_r(&sec, &tm_info);
    strftime(master_time_str, sizeof(master_time_str), "%b %d %H:%M %Y", &tm_info);

    memset(&tm_info, 0, sizeof(struct tm));
    sec = (time_t)shard;
    localtime_r(&sec, &tm_info);
    strftime(shard_time_str, sizeof(shard_time_str), "%b %d %H:%M %Y", &tm_info);

    memset(&tm_info, 0, sizeof(struct tm));
    sec = (time_t)userdb;
    localtime_r(&sec, &tm_info);
    strftime(userdb_time_str, sizeof(userdb_time_str), "%b %d %H:%M %Y", &tm_info);

    ZDP("Master last sync time: %"PRId64" (%s)\n", master, master_time_str);
    ZDP("Shard last sync time: %"PRId64" (%s)\n", shard, shard_time_str);
    ZDP("Userdb last sync time: %"PRId64" (%s)\n", userdb, userdb_time_str);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_add_debug_commands()
{
    int res;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    res = zpath_debug_add_fohh_generic_server(gs->sni_server, "sitec");
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not add generic_server sni debugger: %s", zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                   zpn_debug_catch_cnxt(),
                                   zpn_debug_cnxt_cnt(),
                                   "zpn",
                                   zpn_debug_names);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_admin_command("change offline domain, for testing only",
                                  "/sitec/offline_domain",
                                  zpn_sitec_cmd_change_offline_domain,
                                  NULL,
                                  "domain", "the new offline domain",
                                  "exit", "set to 1 to exit the daemon, default value is 0",
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for changing ddil config");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("show current site controller configuration",
                                  "/sitec/config",
                                  zpn_sitec_cmd_show_config,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for sitec config");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("disable site controller - wont accept any new connections (for debug)",
                                  "/sitec/disable",
                                  zpn_sitec_cmd_disable_service,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for disable site controller");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("restore site controller - act according to configurations (for debug)",
                                  "/sitec/restore",
                                  zpn_sitec_cmd_restore_service,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for restore site controller");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_write_command("Backup SQLite DBs",
                                  "/sitec/sqlt/backup",
                                  zpn_sitec_cmd_sqlt_backup,
                                  NULL,
                                  "db", "options are master|shard|static|userdb|all",
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for restore site controller");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("disable firedrill, switch back to cloud",
                                  "/sitec/disable_firedrill",
                                  zpn_sitec_cmd_disable_firedrill,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for disable site controller firedrill");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("firedrill stats",
                                        "/sitec/firedrill_stats",
                                        zpn_sitec_firedrill_stats,
                                        NULL,
                                        NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for firedrill stats");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("firedrill status",
                                        "/sitec/firedrill_status",
                                        zpn_sitec_firedrill_status,
                                        NULL,
                                        NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for firedrill status");
        return ZPN_RESULT_ERR;
    }

    res = zpn_sitec_add_split_brain_debug_commands();
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for restore site controller");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("show site controller last sync time for databases",
                                  "/sitec/last_sync_time",
                                  zpn_sitec_display_last_sync_time,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for sitec config");
        return ZPN_RESULT_ERR;
    }

    return res;
}

int zpn_sitec_generate_sitesp_fqdn()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char sitesp_fqdn[1024];

    if (!gs->offline_domain) {
        SITEC_LOG(AL_ERROR, "offline domain not yet configured");
        return ZPN_RESULT_ERR;
    }

    snprintf(sitesp_fqdn, sizeof(sitesp_fqdn), "bcpsp-%"PRId64".%s", gs->sitec_id, gs->offline_domain);

    if (gs->sitesp_fqdn && strcmp(gs->sitesp_fqdn, sitesp_fqdn) == 0) {
        return ZPN_RESULT_NO_ERROR;
    }

    gs->sitesp_fqdn = SITEC_STRDUP(sitesp_fqdn, strlen(sitesp_fqdn));

    zpath_downcase(gs->sitesp_fqdn);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_decrypt_sitesp_private_keys()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_ddil_config *ddil_config;
    struct zpn_decrypt_key_request sitesp_signing_key_req;
    struct zpn_decrypt_key_request sitesp_key_req;
    struct zcrypt_pkey *zpkey;
    int res;
    FILE *fp;
    uint8_t file_data[8192];
    size_t file_len;
    size_t out_len;
    size_t row_count = 1;

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id, &ddil_config, &row_count, NULL, 0, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to fetch ddil config error:%s", zpn_result_string(res));
        return res;
    }

    gs->sitesp_ca_cert_pem = SITEC_STRDUP(ddil_config->sitesp_ca_cert, strlen(ddil_config->sitesp_ca_cert));
    gs->sitesp_cert_pem = SITEC_STRDUP(ddil_config->sitesp_cert, strlen(ddil_config->sitesp_cert));

    if (!gs->sitesp_ca_cert_pem || !gs->sitesp_cert_pem) {
        SITEC_LOG(AL_ERROR, "unable to allocate certs from ddil config");
        return ZPN_RESULT_NO_MEMORY;
    }

decrypt_signing_pkey:
    fp = fopen(SITESP_SIGNING_PKEY_FILE, "r");
    if (!fp) {
        //file not there send request
        zpath_interlock_init(&gs->sitesp_interlock);
        sitesp_signing_key_req.requestor = SITESP_SIGNING_PRIVATE_KEY_STR;
        sitesp_signing_key_req.encrypted_key = ddil_config->sitesp_signing_pkey;
        SITEC_LOG(AL_ERROR, "send decrypt req for signing key to: %s", fohh_description(gs->sitec_state->ctrl_fohh_conn));
        res = zpn_send_decrypt_key_request(gs->sitec_state->ctrl_fohh_conn,
                                        fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                        &sitesp_signing_key_req);
        if (res) {
            SITEC_LOG(AL_ERROR, "unable to send decrypt req for signing key error:%s", zpn_result_string(res));
            zpath_interlock_unlock(&gs->sitesp_interlock);
            return res;
        }
        gs->waiting_for_decrypt_key_resp = 1;
        zpath_interlock_wait(&gs->sitesp_interlock);
        gs->waiting_for_decrypt_key_resp = 0;
        zpath_interlock_unlock(&gs->sitesp_interlock);
    } else {
        file_len = fread(file_data, 1, sizeof(file_data), fp);
        fclose(fp);
        out_len = file_len*2;
        char *out_buf = SITEC_CALLOC(out_len);
        if (zcrypt_decrypt(&gs->cfg_hw_key, file_data, file_len, out_buf, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot decrypt data from %s", SITESP_SIGNING_PKEY_FILE);
            SITEC_FREE(out_buf);
            return ZPATH_RESULT_ERR;
        }
        gs->cfg_sitesp_signing_pkey = SITEC_STRDUP(out_buf, strlen(out_buf));
        SITEC_FREE(out_buf);
    }

    if (!gs->cfg_sitesp_signing_pkey) {
        SITEC_LOG(AL_ERROR, "failed to get signing key, retry after 1 second");
        sleep(1);
        goto decrypt_signing_pkey;
    }

decrypt_pkey:
    fp = fopen(SITESP_PKEY_FILE, "r");
    if (!fp) {
        zpath_interlock_init(&gs->sitesp_interlock);
        sitesp_key_req.requestor = SITESP_PRIVATE_KEY_STR;
        sitesp_key_req.encrypted_key = ddil_config->sitesp_pkey;
        SITEC_LOG(AL_ERROR, "send decrypt req for sitesp key to: %s", fohh_description(gs->sitec_state->ctrl_fohh_conn));
        res = zpn_send_decrypt_key_request(gs->sitec_state->ctrl_fohh_conn,
                                        fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                        &sitesp_key_req);
        if (res) {
            SITEC_LOG(AL_ERROR, "unable to send decrypt req for sitesp key error:%s", zpn_result_string(res));
            zpath_interlock_unlock(&gs->sitesp_interlock);
            return res;
        }
        gs->waiting_for_decrypt_key_resp = 1;
        zpath_interlock_wait(&gs->sitesp_interlock);
        gs->waiting_for_decrypt_key_resp = 0;
        zpath_interlock_unlock(&gs->sitesp_interlock);
    } else {
        file_len = fread(file_data, 1, sizeof(file_data), fp);
        fclose(fp);
        out_len = file_len*2;
        char *out_buf = SITEC_CALLOC(out_len);
        if (zcrypt_decrypt(&gs->cfg_hw_key, file_data, file_len, out_buf, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Cannot decrypt data from %s", SITESP_PKEY_FILE);
            SITEC_FREE(out_buf);
            return ZPATH_RESULT_ERR;
        }
        zpkey = zcrypt_pkey_create_from_pem(out_buf);
        if (zpkey == NULL) {
            SITEC_LOG(AL_ERROR, "Cannot create pkey from %s", SITESP_PKEY_FILE);
            SITEC_FREE(out_buf);
            return ZPN_RESULT_ERR;
        }
        gs->cfg_sitesp_pkey = zpkey;
        SITEC_FREE(out_buf);
    }

    if (!gs->cfg_sitesp_pkey) {
        SITEC_LOG(AL_ERROR, "failed to get sitesp key, retry after 1 second");
        sleep(1);
        goto decrypt_pkey;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_init_site_info()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_site_controller_to_group *grp_relation;
    struct zpn_site_controller_group *group;
    struct zpn_site *site = NULL;
    struct zpn_site_controller *sitec = NULL;
    uint32_t dflag_add = 0, dflag_remove = 0;
    size_t count = 1;
    int res;

    g_broker_common_cfg->public_broker.broker_id = gs->sitec_id;

    res = zpn_sitec_get_by_id(gs->sitec_id, &sitec, NULL, NULL, 0);
    if (ZPN_RESULT_NO_ERROR != res) {
        SITEC_LOG(AL_ERROR, "Could not get sitec with gid = %"PRId64": %s", gs->sitec_id, zpn_result_string(res));
        return res;
    }

    gs->sitec_scope_id = sitec->scope_gid;

    res = zpn_sitec_to_group_get_by_sitec_gid(gs->sitec_id, &grp_relation, &count, 0, NULL, NULL, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not get sitec to group for gid = %"PRId64": %s",
                  gs->sitec_id, zpn_result_string(res));
        return res;
    }

    gs->sitec_group_id = grp_relation->site_controller_group_gid;
    count = 1;

    res = zpn_sitec_group_get_by_gid(gs->sitec_group_id,
                                     &group,
                                     &count,
                                     1,
                                     NULL,
                                     NULL,
                                     0);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not get sitec group for gid = %"PRId64": %s",
                  gs->sitec_id, zpn_result_string(res));
        return res;
    }

    gs->site_gid = group->site_gid;
    gs->lat = group->latitude;
    gs->lon = group->longitude;
    snprintf(gs->cc, sizeof(gs->cc), "%s", group->country_code);

    if (gs->site_gid) {
        res = zpn_site_get_by_gid(gs->site_gid, &site, 1, NULL, NULL, 0);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not get site for group gid = %"PRId64" site_gid = %"PRId64": %s",
                    gs->sitec_group_id, gs->site_gid, zpn_result_string(res));
            return res;
        }
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, site->deleted, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DELETED);
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !site->enabled, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DISABLED);
        gs->reenroll_period = site->reenroll_period;
        res = zpn_sitec_connect_to_siem_servers(site->siem_gids, site->siem_gid_count);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not connect to siem servers for site_gid = %"PRId64": %s",
                    gs->site_gid, zpn_result_string(res));
            res = ZPN_RESULT_NO_ERROR;
        }
    } else {
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, group->site_gid == 0, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_REMOVED_FROM_SITE);
    }

    UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, sitec->deleted, ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DELETED);
    UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !sitec->enabled, ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DISABLED);
    UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, group->deleted, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DELETED);
    UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !group->enabled, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DISABLED);

    zpn_sitec_update_service(dflag_add, dflag_remove, 0);

    return res;
}

char* zpn_sitec_get_offline_domain()
{

    int res;
    struct zpn_ddil_config *ddil_config;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    size_t row_count = 1;

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id, &ddil_config, &row_count, NULL, 0, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to fetch ddil config error:%s", zpn_result_string(res));
        return NULL;
    }

    return ddil_config->offline_domain;
}

int zpn_sitec_get_max_allowed_downtime()
{

    int res;
    struct zpn_ddil_config *ddil_config;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    size_t row_count = 1;

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id, &ddil_config, &row_count, NULL, 0, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to fetch ddil config error:%s", zpn_result_string(res));
        return 0;
    }

    return ddil_config->max_allowed_downtime_s;
}

int zpn_sitec_get_new_user_support(int *allow)
{

    int res;
    struct zpn_ddil_config *ddil_config;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    size_t row_count = 1;

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id, &ddil_config, &row_count, NULL, 0, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to fetch ddil config error:%s", zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    if (allow) *allow = ddil_config->new_user_support;

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_create_wally_fohh_servers()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    gs->sitec_state->cfg_wally_server = wally_fohh_server_create(gs->sitec_state->cfg_wally,
                                                                 argo_serialize_binary,
                                                                 fohh_connection_style_argo,
                                                                 1,
                                                                 NULL,
                                                                 0,
                                                                 NULL,
                                                                 NULL,
                                                                 NULL,
                                                                 1);
    if (!gs->sitec_state->cfg_wally_server) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally server for cfg wally");
        return ZPN_RESULT_ERR;
    }

    gs->sitec_state->rcfg_wally_server = wally_fohh_server_create(gs->sitec_state->rcfg_wally,
                                                                 argo_serialize_binary,
                                                                 fohh_connection_style_argo,
                                                                 1,
                                                                 NULL,
                                                                 0,
                                                                 NULL,
                                                                 NULL,
                                                                 NULL,
                                                                 1);
    if (!gs->sitec_state->rcfg_wally_server) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally server for rcfg wally");
        return ZPN_RESULT_ERR;
    }

    gs->sitec_state->ovd_wally_server = wally_fohh_server_create(gs->sitec_state->ovd_wally,
                                                                 argo_serialize_binary,
                                                                 fohh_connection_style_argo,
                                                                 1,
                                                                 NULL,
                                                                 0,
                                                                 NULL,
                                                                 NULL,
                                                                 NULL,
                                                                 1);
    if (!gs->sitec_state->ovd_wally_server) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally global server");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_fohh_conn_setup_timemout_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    fohh_set_conn_setup_timeout((uint64_t)*config_value);
}

void zpn_sitec_setup_fohh_conn_setup_timeout()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);

    gs->sitec_fohh_conn_timeout_override = zpath_config_override_get_config_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                                                        &gs->sitec_fohh_conn_timeout_override,
                                                                        FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                                                        gs->sitec_id,
                                                                        gs->sitec_group_id,
                                                                        customer_gid,
                                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                        (int64_t)0);
    fohh_set_conn_setup_timeout((uint64_t)gs->sitec_fohh_conn_timeout_override);

    zpath_config_override_monitor_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                        &gs->sitec_fohh_conn_timeout_override,
                                        zpn_sitec_fohh_conn_setup_timemout_monitor_callback,
                                        FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                        gs->sitec_id,
                                        gs->sitec_group_id,
                                        customer_gid,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
}

static void zpn_sitec_auth_timeout_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    zpn_broker_client_set_auth_timeout((uint64_t)*config_value);
}

static void zpn_sitec_sarge_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (*config_value != gs->sarge_upgrade_feature_flag) {
        gs->sarge_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Sitec sarge upgrade feature flag changed to %"PRId64, gs->sarge_upgrade_feature_flag);
        zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(gs->sarge_upgrade_feature_flag);
    }
    return;
}

static void zpn_sitec_os_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (*config_value != gs->os_upgrade_feature_flag) {
        gs->os_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Sitec os upgrade feature flag changed to %"PRId64, gs->os_upgrade_feature_flag);
        zpath_upgrade_set_os_upgrade_feature_flag_cfg(gs->os_upgrade_feature_flag);
    }
    return;
}

static void zpn_sitec_sarge_backup_version_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (*config_value != gs->sarge_backup_version_feature_flag) {
        gs->sarge_backup_version_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Sitec sarge backup version feature flag changed to %"PRId64, gs->sarge_backup_version_feature_flag);
        zpath_upgrade_set_sarge_backup_version_feature_flag(gs->sarge_backup_version_feature_flag);
    }
    return;
}

void zpn_sitec_full_os_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (*config_value != gs->full_os_upgrade_feature_flag) {
        gs->full_os_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Sitec full os upgrade feature flag changed to %"PRId64, gs->full_os_upgrade_feature_flag);
        zpath_upgrade_set_full_os_upgrade_version_feature_flag(gs->full_os_upgrade_feature_flag);
    }
    return;
}

void zpn_sitec_oauth_enrollment_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int32_t feature_disabled = *config_value ? 1 : 0;
    SITEC_LOG(AL_INFO, "Sitec oauth enrollment feature status set to %s", feature_disabled ? "disabled" : "enabled");

    if (feature_disabled) {
        FILE *fp = fopen(OAUTH_ENROLL_DISABLE_FLAG, "w");
        if (fp == NULL) {
            SITEC_LOG(AL_ERROR, "Error creating oauth flag file");
            return;
        }
        fclose(fp);
        SITEC_LOG(AL_NOTICE, "[OAuth] OAuth Enrollment Disabled. OAuth is the Preferred way of enrollment. Alternatively, you need to copy Provision key to the machine and restart service.");
    } else {
        if (unlink(OAUTH_ENROLL_DISABLE_FLAG) != 0) {
            if (errno != ENOENT) {
                SITEC_LOG(AL_ERROR, "Error deleting oauth flag file: %s", strerror(errno));
            }
        }
        SITEC_LOG(AL_NOTICE, "[OAuth] Oauth Enrollment Enabled");
    }
    return;
}

void zpn_sitec_setup_client_auth_complete_timeout()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);

    gs->sitec_client_auth_timeout_override = zpath_config_override_get_config_int(SITEC_CLIENT_AUTH_TIMEOUT,
                                                                                &gs->sitec_client_auth_timeout_override,
                                                                                SITEC_CLIENT_AUTH_TIMEOUT_SEC,
                                                                                gs->sitec_id,
                                                                                gs->sitec_group_id,
                                                                                customer_gid,
                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                (int64_t)0);

    zpn_broker_client_set_auth_timeout((uint64_t)gs->sitec_client_auth_timeout_override);

    zpath_config_override_monitor_int(SITEC_CLIENT_AUTH_TIMEOUT,
                                        &gs->sitec_client_auth_timeout_override,
                                        zpn_sitec_auth_timeout_monitor_callback,
                                        SITEC_CLIENT_AUTH_TIMEOUT_SEC,
                                        gs->sitec_id,
                                        gs->sitec_group_id,
                                        customer_gid,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
}

void zpn_sitec_setup_oauth_enrollment()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);

    zpath_config_override_monitor_int(SITEC_OAUTH_ENROLL_DISABLE,
                                      &oauth_enrollment_feature_flag,
                                      zpn_sitec_oauth_enrollment_config_override_monitor_callback,
                                      DEFAULT_SITEC_OAUTH_ENROLL_DISABLE,
                                      gs->sitec_id,
                                      gs->sitec_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

void zpn_sitec_sarge_and_os_overrides_setup()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id);

    gs->sarge_upgrade_feature_flag = zpath_config_override_get_config_int(SITEC_SARGE_UPGRADE_ENABLE,
                                                                          &gs->sarge_upgrade_feature_flag,
                                                                          DEFAULT_SITEC_SARGE_UPGRADE_ENABLE,
                                                                          gs->sitec_id,
                                                                          gs->sitec_group_id,
                                                                          customer_gid,
                                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                          (int64_t)0);

    gs->os_upgrade_feature_flag = zpath_config_override_get_config_int(AUTOMATIC_OS_UPGRADE_ENABLE,
                                                                       &gs->os_upgrade_feature_flag,
                                                                       DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
                                                                       gs->sitec_id,
                                                                       gs->sitec_group_id,
                                                                       customer_gid,
                                                                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                       (int64_t)0);

    gs->sarge_backup_version_feature_flag = zpath_config_override_get_config_int(SARGE_BACKUP_VERSION_ENABLE,
                                                                                 &gs->sarge_backup_version_feature_flag,
                                                                                 DEFAULT_SARGE_BACKUP_VERSION_ENABLE,
                                                                                 gs->sitec_id,
                                                                                 gs->sitec_group_id,
                                                                                 customer_gid,
                                                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                 (int64_t)0);

    gs->full_os_upgrade_feature_flag = zpath_config_override_get_config_int(AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                                            &gs->full_os_upgrade_feature_flag,
                                                                            DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                                            gs->sitec_id,
                                                                            gs->sitec_group_id,
                                                                            customer_gid,
                                                                            (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                            (int64_t)0);

    zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(gs->sarge_upgrade_feature_flag);
    zpath_upgrade_set_os_upgrade_feature_flag_cfg(gs->os_upgrade_feature_flag);
    zpath_upgrade_set_sarge_backup_version_feature_flag(gs->sarge_backup_version_feature_flag);
    zpath_upgrade_set_full_os_upgrade_version_feature_flag(gs->full_os_upgrade_feature_flag);

    zpath_config_override_monitor_int(SITEC_SARGE_UPGRADE_ENABLE,
                                      &sarge_upgrade_feature_flag,
                                      zpn_sitec_sarge_upgrade_monitor_callback,
                                      DEFAULT_SITEC_SARGE_UPGRADE_ENABLE,
                                      gs->sitec_id,
                                      gs->sitec_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(AUTOMATIC_OS_UPGRADE_ENABLE,
                                      &os_upgrade_feature_flag,
                                      zpn_sitec_os_upgrade_monitor_callback,
                                      DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
                                      gs->sitec_id,
                                      gs->sitec_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(SARGE_BACKUP_VERSION_ENABLE,
                                      &sarge_backup_version_feature_flag,
                                      zpn_sitec_sarge_backup_version_monitor_callback,
                                      DEFAULT_SARGE_BACKUP_VERSION_ENABLE,
                                      gs->sitec_id,
                                      gs->sitec_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                      &full_os_upgrade_feature_flag,
                                      zpn_sitec_full_os_upgrade_monitor_callback,
                                      DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                      gs->sitec_id,
                                      gs->sitec_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

void zpn_sitec_balance_config_init()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    memset(&g_balance_config, 0, sizeof(struct zpn_balance_config));
    g_balance_config.gid = gs->sitec_id;
    g_balance_config.report_age_reduction_s = LOAD_STALE_AGE_DEFAULT_S;
    g_balance_config.report_age_exclusion_s = POSSIBLE_BROKER_AGE_DEFAULT_S;

    snprintf(g_balance_config.instance_name, ZPN_BALANCE_INST_NAME_LEN, "%s", EMPTY_STR);
    snprintf(g_balance_config.balance_role, ZPN_BALANCE_ROLE_MODE_LEN, "%s", BALANCE_ROLE_REDIRECT);
    snprintf(g_balance_config.balance_mode, ZPN_BALANCE_ROLE_MODE_LEN, "%s", BALANCE_MODE_MAINTENANCE);
    g_balance_config.restart_time = 0;
    g_balance_config.load_skew = 0;
    g_balance_config.single_dc_redir_priv = 1; /* doesn't matter since we are unconfigured... */
    g_balance_config.is_role_redirect = 1;

    /* initially allocate a large number of viable_instances, mostly for public brokers.
    * grow by smaller chunks for private brokers */
    g_balance_config.inst_list_alloc = 1024;
    g_balance_config.inst_list_growth = 128;
}

int zpn_sitec_redirect_init()
{
    int res;

    zpn_balance_config_override_desc_register_all();

    zpn_sitec_balance_config_init();

    res = zpn_balance_stats_init();
    if (res) return res;

    res = zpn_balance_inst_load_init();
    if (res) return res;

    res = zpn_broker_balance_debug_init();

    return res;
}

int zpn_sitec_redirect_client(struct zpn_broker_client_fohh_state *c_state,
                              int force_redirect,
                              const char* reason)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_broker_redirect redirect_msg;
    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    int res;
    int i;
    char buf[4000] = {0};
    double client_lat = 0, client_lon = 0;
    char client_cc[CC_BUF_LEN] = {0};
    struct argo_inet remote_ip;
    struct zpn_balance_redirect_instance_types rbt;
    struct zpn_balance_policy_req policy_req = {0};

    policy_req.is_client_capable = is_zpn_client_eligible_for_broker_policy_redirect(c_state);
    policy_req.general_context_hash = c_state->general_context_hash;
    policy_req.saml_hash = c_state->saml_enabled ? c_state->saml_hash : NULL;
    policy_req.scim_hash = zpn_broker_client_scim_get_scim_state_hashed(c_state);

    if (!c_state->auth_complete) {
        /* Need to wait for trusted network delivery. For now we will
         * just log this, will call up redirect again once authenticated
         */
        c_state->redir_waiting_auth_complete = 1;
        SITEC_LOG(AL_INFO, "%s:%s Redirection waiting on auth complete", c_state->tunnel_id, zpn_tlv_description(tlv));
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!zpn_client_static_config[c_state->client_type].fohh_redirect) {
        SITEC_LOG(AL_INFO,"%s:%s Do not do redirect for %s", c_state->tunnel_id, zpn_tlv_description(tlv), zpn_client_type_string(c_state->client_type));
        return ZPATH_RESULT_NO_ERROR;
    }

    if (c_state->trusted_networks) {
        argo_object_hold(c_state->trusted_networks);
    }

    SITEC_LOG(AL_INFO,"%s:%s Redirecting for client tunnel", c_state->tunnel_id, zpn_tlv_description(tlv));
    char dest_str[100];
    argo_inet_generate(dest_str, &(c_state->client_ip));
    ZPN_LOG(AL_INFO, "client_ip = %s",dest_str);

    memset((char *)&redirect_msg, 0, sizeof(struct zpn_broker_redirect));
    redirect_msg.reason = reason;

    rbt.type[PRIVATE_BROKER] = zpn_client_static_config[c_state->client_type].fohh_redirect_priv_pb;
    rbt.type[PUBLIC_PRIVATE_BROKER] = zpn_client_static_config[c_state->client_type].fohh_redirect_pub_pb;
    rbt.type[PUBLIC_BROKER] = 0;

    zpath_geoip_lookup_double(&(c_state->client_ip), &client_lat, &client_lon, client_cc);

    policy_req.int_hash = zhash_table_alloc(&zpn_allocator);
    if (!policy_req.int_hash) {
        ZPN_LOG(AL_ERROR,"%s:%s Could not allocate memory for int_hash", c_state->tunnel_id, zpn_tlv_description(tlv));
        return ZPN_RESULT_NO_MEMORY;
    }

    if (c_state->idp_gid) {
        zhash_table_store(policy_req.int_hash, &(c_state->idp_gid), sizeof(c_state->idp_gid), 1, policy_req.int_hash);
    } else {
        ZPN_DEBUG_BALANCE("%s:%s Client state IDP gid is 0", c_state->tunnel_id, zpn_tlv_description(tlv));
    }

    zpn_broker_balance_redirect(c_state->customer_gid, c_state->scope_gid, c_state->trusted_networks,
                                &(c_state->log.priv_ip), client_lat, client_lon, client_cc, &policy_req,
                                &redirect_msg.broker_count, &redirect_msg.brokers[0], NULL, NULL, NULL,
                                &redirect_msg.sni_suffixes_count, &redirect_msg.sni_suffixes[0],
                                &rbt, c_state->client_type, 0,
                                c_state->capability_alt_cloud_aware, c_state->capability_ipv6_redirect, 0, gs->site_gid,
                                zpn_tlv_description(tlv));

    if (!redirect_msg.broker_count) {
        ZPN_LOG(AL_ERROR, "%s:%s Cannot find broker to redirect to for customer %"PRId64"",
                c_state->tunnel_id, zpn_tlv_description(tlv), c_state->customer_gid);
        if (c_state->trusted_networks) argo_object_release(c_state->trusted_networks);
        c_state->no_brokers_to_redir = 1;
        zpn_update_log_about_redirect_policy(c_state, &policy_req);
        if (policy_req.int_hash) {
            zhash_table_free(policy_req.int_hash);
            policy_req.int_hash = NULL;
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if (c_state->capability_alt_cloud_aware) {
        for (int i = 0; i < redirect_msg.sni_suffixes_count; i++) {
            if (redirect_msg.sni_suffixes[i]) {
                redirect_msg.alt_cloud = redirect_msg.sni_suffixes[i];
                __sync_fetch_and_add_8(&(zpn_fohh_workers[c_state->conn_thread_id].client_stats.num_client_alt_cloud_redirects), 1);
                break;
            }
        }
    }
    if (!redirect_msg.alt_cloud) {
        redirect_msg.alt_cloud = "";
    }

    if (c_state->trusted_networks) argo_object_release(c_state->trusted_networks);

    redirect_msg.redirect_only = 1;
    redirect_msg.reason = BRK_REDIRECT_REASON_REDIRECT_SITEC;
    redirect_msg.redirect_source = REDIRECT_SOURCE_SITEC;

    zpn_update_log_about_redirect_policy(c_state, &policy_req);

    /* Clear old redirect logging state: */
    for (i = 0; i < c_state->log.brk_redirect_count; ++i) {
        if (c_state->log.brk_redirect[i]) {
            SITEC_FREE(c_state->log.brk_redirect[i]);
            c_state->log.brk_redirect[i] = NULL;
        }
    }

    c_state->log.brk_redirect_count = 0;
    /* Set new redirect logging state: */
    for (i = 0; (i < redirect_msg.broker_count) && (i < ZPN_CLIENT_MAX_BROKERS); ++i) {
        c_state->log.brk_redirect[i] = SITEC_STRDUP(redirect_msg.brokers[i], strlen(redirect_msg.brokers[i]));
    }

    c_state->log.brk_redirect_count = i;
    c_state->log.brk_redirect_ts_us = epoch_us();

    redirect_msg.timestamp_s = epoch_s();
    redirect_msg.force_redirect = force_redirect;

    /* Get the remote (public) IP of the connection and transmit it */
    zpn_tlv_address(tlv, &(remote_ip), NULL);
    redirect_msg.pub_ip = &remote_ip;

    redirect_msg.allow_probe = 0;
    redirect_msg.broker_info_count = 0;

    res = zpn_send_zpn_broker_redirect_struct(tlv, &redirect_msg);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? zpn_send_zpn_broker_redirect_struct failed: %s", c_state->tunnel_id, zpn_result_string(res));
    }

    snprintf(buf, sizeof(buf), "Redirect client %s:%s (cc: %s lat:%f, lon:%f) (Alt Cloud: %s, %s) to ",
            zpn_tlv_peer_cn(tlv), zpn_tlv_description(tlv), client_cc, client_lat, client_lon,
            c_state->capability_alt_cloud_aware ? "aware" : "un-aware",
            (zpath_instance_is_alt_cloud_enabled_for_customer(c_state->customer_gid)) ?
            "supported" : "un-supported");

    /* Now clean up */
    for (i = 0; i < redirect_msg.broker_count; i++) {
        if (redirect_msg.brokers[i]) {
            if (i > 0) strncat(buf, ", ", sizeof(buf)-strlen(buf)-1);
            strncat(buf, redirect_msg.brokers[i], sizeof(buf)-strlen(buf)-1);
            SITEC_FREE(redirect_msg.brokers[i]);
        }
        if(redirect_msg.brokers_info[i]) {
            argo_object_release(redirect_msg.brokers_info[i]);
        }
        if (redirect_msg.sni_suffixes[i]) {
            SITEC_FREE(redirect_msg.sni_suffixes[i]);
        }
    }

    SITEC_LOG(AL_INFO, "%s", buf);

    c_state->redir_waiting_auth_complete = 0;
    c_state->redir_network_change = 0;

    if (policy_req.int_hash) {
        zhash_table_free(policy_req.int_hash);
        policy_req.int_hash = NULL;
    }

    return res;
}

int zpn_sitec_firedrill_start(int64_t firedrill_interval)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    SITEC_LOG(AL_ERROR, "firedrill zpn_sitec_firedrill_start");

    /* the firedrill config is fetched in one shot */
    if((gs->firedrill_status == ZPN_SITEC_FIREDRILL_DISABLED ||
        gs->firedrill_status == ZPN_SITEC_FIREDRILL_TRANSIT) &&
        firedrill_interval) {
        /* activate the timer */
        if(zpn_site_firedrill_timer_activate(firedrill_interval)) {
            SITEC_LOG(AL_ERROR, "firedrill timer activation failed");
            return ZPN_RESULT_ERR;
        }
        /* delete all the broker connections */
        zpn_sitec_delete_broker_connections(FOHH_CLOSE_REASON_SITEC_FIREDRILL);
    }
    return ZPN_RESULT_NO_ERROR;
}


int zpn_sitec_firedrill_session_request(struct zpn_sitec_global_state *gs)
{
    int res = 0;
    struct zpn_firedrill_site *firedrill_config = NULL;

    if(!gs->site_gid) {
        SITEC_LOG(AL_ERROR, "gs->site_gid NULL");
        return ZPN_RESULT_ERR;
    }

    SITEC_LOG(AL_INFO, "firedrill triggered for customer: %"PRId64"", gs->customer_id);

    /* get the firedrill config for this site using the site_gid */
    res = zpn_sitec_firedrill_get_config(&firedrill_config);
    if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_ERR;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }

    if(!firedrill_config) {
         SITEC_LOG(AL_ERROR, "firedrill config is NULL");
        return ZPN_RESULT_ERR;
    }

    if(gs->firedrill_status != ZPN_SITEC_FIREDRILL_ENABLED &&
        zpn_sitec_firedrill_start(firedrill_config->firedrill_interval_s) == ZPN_RESULT_ERR) {
        return ZPN_RESULT_ERR;
    }
    SITEC_LOG(AL_INFO, "firedrill activation successful for customer: %"PRId64" for interval: %"PRId64"",
                                                gs->customer_id, firedrill_config->firedrill_interval_s);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_site_firedrill_config_fetch_callback(void *response_callback_cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    int64_t request_id,
                                                    int row_count)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if(zpn_sitec_firedrill_session_request(gs)) {
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_site_row_callback(void *cookie,
                                       struct wally_registrant *registrant,
                                       struct wally_table *table,
                                       struct argo_object *previous_row,
                                       struct argo_object *row,
                                       int64_t request_id)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_site *site = (struct zpn_site *)row->base_structure_void;
    struct zpn_site *prev_site;
    int res;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    if (gs->site_gid == site->gid) {
        uint32_t dflag_add = 0, dflag_remove = 0;

        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, site->deleted, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DELETED);
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !site->enabled, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DISABLED);

        zpn_sitec_update_service(dflag_add, dflag_remove, 0);

        gs->reenroll_period = site->reenroll_period;

        if (site->deleted || !site->enabled) {
            for (int i = 0; i < site->siem_gid_count; i++) {
                zpn_sitec_siem_conn_delete(site->siem_gids[i]);
            }
        }

        if (previous_row != NULL) {
            int found;
            prev_site = (struct zpn_site *)previous_row->base_structure_void;
            for (int i = 0; i < prev_site->siem_gid_count; i++) {
                found = 0;
                for (int j = 0; j < site->siem_gid_count; j++) {
                    if (prev_site->siem_gids[i] == site->siem_gids[j]) {
                        found = 1;
                        break;
                    }
                }
                if (!found) {
                    zpn_sitec_siem_conn_delete(prev_site->siem_gids[i]);
                }
            }
        }

        for (int i= 0 ; i < site->siem_gid_count; i++) {
            res = zpn_sitec_siem_conn_create(site->siem_gids[i]);
            if (res) {
                SITEC_LOG(AL_ERROR, "siem conn create failed with error %s", zpn_result_string(res));
            }
        }
    }

    if (site->firedrill_enabled) {
        SITEC_LOG(AL_ERROR, "firedrill row callback: %"PRId64"", site->customer_gid);
        /* check if a firedrill session is on-going */
        if(gs->firedrill_status == ZPN_SITEC_FIREDRILL_TRANSIT) {
            /* guess nothing to do as the site monitor cb will take care of triggering firedrill if required */
        } else if(gs->firedrill_status == ZPN_SITEC_FIREDRILL_DISABLED) {
            if(zpn_sitec_firedrill_session_request(gs)) {
                return ZPN_RESULT_ERR;
            }
        } else {
            SITEC_LOG(AL_INFO, "firedrill ongoing for customer: %"PRId64"", site->customer_gid);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_firedrill_get_config(struct zpn_firedrill_site **firedrill_config)
{
    int res = 0;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    res = zpn_firedrill_site_by_site_gid(gs->site_gid, gs->customer_id, zpn_site_firedrill_config_fetch_callback, firedrill_config);
    if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
        SITEC_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64"",gs->site_gid);
        return ZPN_RESULT_ERR;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        SITEC_LOG(AL_ERROR, "firedrill config fetch asyncronous for site_gid = %"PRId64"",gs->site_gid);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_create_status_conn_deferred(void *cookie1,
                                                  void *cookie2,
                                                  int64_t peer_sitec_gid)
{
    zpn_sitec_create_status_conn(peer_sitec_gid);
}

static int zpn_site_controller_row_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            struct argo_object *previous_row,
                                            struct argo_object *row,
                                            int64_t request_id)
{
    int res;
    struct zpn_site_controller *sitec = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(sitec->scope_gid && sitec->scope_gid != sitec->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    if (gs->sitec_id == sitec->gid) {
        uint32_t dflag_add = 0, dflag_remove = 0;

        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, sitec->deleted, ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DELETED);
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !sitec->enabled, ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DISABLED);

        zpn_sitec_update_service(dflag_add, dflag_remove, 0);
        if (!gs->disable_flags) {
            res = zpn_sitec_create_status_conn_to_all_sitec();
            if (res) {
                SITEC_LOG(AL_ERROR, "Could not initiate sitec status conns from sitec row callback");
                return ZPN_RESULT_NO_ERROR;
            }
        }
    } else {
        if (zpath_service_init_complete) {
            if (sitec->deleted || !sitec->enabled) {
                zpn_sitec_delete_status_conn(sitec->gid);
            } else {
                zevent_big_defer(zpn_sitec_create_status_conn_deferred, NULL, NULL, sitec->gid, 1000000);
            }
        }
    }


    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_firedrill_row_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            struct argo_object *previous_row,
                                            struct argo_object *row,
                                            int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_ddil_config_row_callback(void *cookie,
                                              struct wally_registrant *registrant,
                                              struct wally_table *table,
                                              struct argo_object *previous_row,
                                              struct argo_object *row,
                                              int64_t request_id)
{
    struct zpn_ddil_config *ddil_config = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(site->scope_gid && site->scope_gid != site->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->sni_server) {
        zpn_sitec_enable_client_snis(ddil_config->new_user_support, -1);
        zpn_sitec_on_offline_domain_change(ddil_config->offline_domain, 1);
    }

    zpn_idp_cert_set_refresh(ddil_config->customer_gid);

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_group_row_callback_1(void *cookie,
                                          struct wally_registrant *registrant,
                                          struct wally_table *table,
                                          struct argo_object *previous_row,
                                          struct argo_object *row,
                                          int64_t request_id)
{
    int group_site_changed = 0;
    int res;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    struct zpn_site_controller_group *grp = row->base_structure_void;
    struct zpn_site *site = NULL;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->sitec_group_id == grp->gid) {
        uint32_t dflag_add = 0, dflag_remove = 0;

        if (gs->site_gid != grp->site_gid) {
            // `zpn_sitec_site_row_callback()` could be called earlier than this `row_callback()`,
            // so we need to check site's enable/disable status here
            group_site_changed = 1;
            if (!zpn_site_get_by_gid(grp->site_gid, &site, 1, NULL, NULL, 0)) {
                UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, site->deleted, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DELETED);
                UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !site->enabled, ZPN_SITEC_DISABLE_FLAG_SITE_IS_DISABLED);
            }
        }

        gs->site_gid = grp->site_gid;

        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, grp->deleted, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DELETED);
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !grp->enabled, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DISABLED);
        UPDATE_DISABLE_FLAG(dflag_add, dflag_remove, !gs->site_gid, ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_REMOVED_FROM_SITE);

        zpn_sitec_update_service(dflag_add, dflag_remove, 0);

        if (group_site_changed && gs->site_gid && !gs->disable_flags) {
            res = zpn_sitec_create_status_conn_to_all_sitec();
            if (res) {
                SITEC_LOG(AL_ERROR, "Could not initiate sitec status conns from sitec group row callback");
                return ZPN_RESULT_NO_ERROR;
            }
        }
    } else {
        if (zpath_service_init_complete &&
            (gs->site_gid == grp->site_gid) &&
            !grp->deleted && grp->enabled) {
            struct zpn_site_controller_to_group *sc_to_group[200];
            size_t sc_to_group_count = 200;

            res = zpn_sitec_to_group_get_by_grpid(grp->gid,
                                                  &(sc_to_group[0]),
                                                  &sc_to_group_count,
                                                  NULL,
                                                  NULL,
                                                  0);
            if (res) {
                SITEC_LOG(AL_ERROR, "unable to fetch sitec to group mapping for site:%"PRId64" res:%s",
                            gs->site_gid, zpn_result_string(res));
                return ZPN_RESULT_NO_ERROR;
            }

            for (int i = 0; i < sc_to_group_count; i++) {
                zpn_sitec_create_status_conn(sc_to_group[i]->site_controller_gid);
            }
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

static int zpn_sitec_c2c_client_registration_row_cb(void *cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    struct argo_object *previous_row,
                                                    struct argo_object *row,
                                                    int64_t request_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_get_customer_userdb_row_default(int64_t customer_gid, int64_t userdb_id)
{
    if (zpath_service_init_complete) {
        //customer default userdb got changed. restart the service
        SITEC_LOG(AL_CRITICAL, "customer userdb changed to userdb id :%"PRId64" restarting PCC", userdb_id);
        sleep(1);
        exit(1);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_idp_row_callback(void *cookie,
                                      struct wally_registrant *registrant,
                                      struct wally_table *table,
                                      struct argo_object *previous_row,
                                      struct argo_object *row,
                                      int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_IDP_IDX)) {
        char dump[ARGO_BUF_DEFAULT_SIZE];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_IDP("Row callback: %s", dump);
        }
    }

    struct zpn_idp *idp = row->base_structure_void;
    int register_endpoint;

    if (zpath_service_init_complete) {
        if (idp->is_user_idp) {
            register_endpoint = (idp->deleted == 0) ? 1 : 0;
            zpn_sitesp_update_sso_end_point(idp->gid, register_endpoint);
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

int zpn_sitec_is_cloud_reachable()
{
    int status = 0;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->sitec_state->ctrl_fohh_conn &&
        fohh_get_state(gs->sitec_state->ctrl_fohh_conn) == fohh_connection_connected) {
        status = 1;
    }

    return status;
}

int zpn_sitec_init()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t sitec_id = gs->sitec_id;
    int64_t customer_gid = gs->customer_id;
    struct wally *shard_wally = gs->sitec_state->cfg_wally;
    struct wally *global_wally = gs->sitec_state->ovd_wally;
    int res;

    zpn_transaction_collection = argo_log_create("zpn_transaction_log", NULL, NULL); /* 64K entries */
    zpn_health_collection = argo_log_create("zpn_health_log", NULL, NULL); /* 64K entries */
    zpn_auth_collection = argo_log_create("zpn_auth_log", NULL, NULL); /* 64K entries */
    zpn_ast_auth_collection = argo_log_create("zpn_ast_auth_log", NULL, NULL); /* 64K entries */
    zpn_dns_collection = argo_log_create("zpn_dns_log", NULL, NULL); /* 64K entries */
    zpn_ast_waf_collection = argo_log_create(ZPN_WAF_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_app_inspection_collection = argo_log_create(ZPN_APP_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_krb_inspection_collection = argo_log_create(ZPN_KRB_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_ldap_inspection_collection = argo_log_create(ZPN_LDAP_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_smb_inspection_collection = argo_log_create(ZPN_SMB_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_ptag_collection = argo_log_create(ZPN_PTAG_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_waf_api_collection = argo_log_create(ZPN_API_LOG, NULL, NULL); /* 64K entries */
    zpn_sc_asst_auth_collection = argo_log_create("zpn_sc_asst_auth_log", NULL, NULL); /* 64K entries */
    zpn_sc_pb_auth_collection = argo_log_create("zpn_sc_pb_auth_log", NULL, NULL); /* 64K entries */
    zpn_auth_state_collection = argo_log_create("zpn_auth_state_log", NULL, NULL); /* 64K entries */
    zpn_asst_event_collection = argo_log_create("asst_event_log", NULL, NULL);
    zpn_pb_event_collection = argo_log_create("pb_event_log", NULL, NULL);
    zpn_pb_client_auth_collection = argo_log_create("zpn_pb_client_auth_log", NULL, NULL); /* 64K entries */
    zia_health_collection = argo_log_create("zpn_zia_health", NULL, NULL); /* 64K entries */

    if (!zpn_transaction_collection ||
        !zpn_health_collection ||
        !zpn_auth_collection ||
        !zpn_ast_auth_collection ||
        !zpn_dns_collection ||
        !zpn_ast_waf_collection ||
        !zpn_ast_app_inspection_collection ||
        !zpn_ast_krb_inspection_collection ||
        !zpn_ast_ldap_inspection_collection ||
        !zpn_ast_smb_inspection_collection ||
        !zpn_ast_ptag_collection ||
        !zpn_ast_waf_api_collection ||
        !zpn_sc_asst_auth_collection ||
        !zpn_sc_pb_auth_collection ||
        !zpn_asst_event_collection ||
        !zpn_pb_event_collection ||
        !zpn_pb_client_auth_collection ||
        !zpn_auth_state_collection ||
        !zia_health_collection) {
        SITEC_LOG(AL_ERROR, "Could not create transaction logs");
        return ZPN_RESULT_ERR;
    }

    res = zpn_sitec_siem_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init sitec_siem");
        return res;
    }

    res = zse_init(zpn_is_dr_mode_active());
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zse");
        return res;
    }

    res = zpe_init(0);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpe");
        return res;
    }

    zpn_broker_c_state_free_q_init();

    if (zpn_fohh_worker_state_init(1) != ZPN_RESULT_NO_ERROR) {
        return ZPN_RESULT_ERR;
    }

    res = zpn_siem_tenant_init(shard_wally, sitec_id, zpn_sitec_siem_row_callback, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_siem");
        return res;
    }

    srand(time(NULL)); // Seed the random number generator with current time

    res = zsaml_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zsaml");
        return res;
    }

    res = zpath_config_override_init(global_wally,
                                     customer_gid,
                                     1,
                                     config_component_sitec);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpath_config_override failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpn_inspection_config_data_tenant_init(global_wally,
                                                 customer_gid,
                                                 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpn_inspection_config_data... failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpn_inspection_zsdefined_control_tenant_init(global_wally,
                                                       customer_gid,
                                                       1);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpn_inspection_zsdefined_control... failed: %s", zpath_result_string(res));
        return res;
    }

    res = et_geoip_override_tenant_init(global_wally, customer_gid, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "et_geoip_override... failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpn_sitec_cfg_override_register();
    if (res) {
        SITEC_LOG(AL_ERROR, "Unable to register zpn_sitec_cfg_override_register, err: %s",
                zpn_result_string(res));
        return res;
    }

    /*
     * Initialize SCIM Data Tables
     * ORDER IS IMPORTANT!
     * 1. Init each table inside the userdb that we need an index for
     * 2. Init the et_userdb as the row callbacks require all the index information from 1 to function
     */
    res = zpn_scim_user_attribute_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize scim_user_attribute table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_scim_group_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize scim_group table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_scim_user_group_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize scim_user_group table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_scim_user_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize scim_user table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_user_risk_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize user_risk table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_aae_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize aae tables : %s", zpn_result_string(res));
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Initializing et_userdb...");
    res = zpath_et_userdb_init(global_wally, 1, customer_gid);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpath_et_userdb_init failed: %s", zpath_result_string(res));
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Initializing et_customer_zone...");
    res = zpath_et_customer_zone_init(global_wally, customer_gid);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpath_et_customer_zone_init failed: %s", zpath_result_string(res));
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Initializing zpath_et_customer_userdb...");
    res = zpath_et_customer_userdb_init(global_wally, customer_gid, zpn_sitec_get_customer_userdb_row_default);
    if (res) {
        SITEC_LOG(AL_ERROR, "Initializing zpath_et_customer_userdb failed: %s", zpath_result_string(res));
        return res;
    }

    zpath_et_userdb_fullyload_by_customer(customer_gid);

    int64_t is_enabled = 0;
    is_static_wally_enabled = (int)zpath_config_override_get_config_int(ZPN_SITEC_REGISTRATION_WALLY_ENABLED,
                                                                   &is_enabled,
                                                                   ZPN_SITEC_REGISTRATION_WALLY_ENABLED_DEFAULT,
                                                                   sitec_id,
                                                                   customer_gid,
                                                                   ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                   0);

    if (is_static_wally_enabled) {
        res = zpn_sitec_rcfg_conn_init();
        if (res) {
            SITEC_LOG(AL_CRITICAL, "Unable to initilize sitec state for static wally for customer %"PRId64", on %"PRId64", res=%s", customer_gid, sitec_id, zpn_result_string(res));
            return res;
        }

        SITEC_LOG(AL_NOTICE, "sitec state for static wally is initialized for customer %"PRId64", on %"PRId64,  customer_gid, sitec_id);
    } else {
        SITEC_LOG(AL_NOTICE, "sitec static wally is disabled for customer %"PRId64", on %"PRId64,
                customer_gid, sitec_id);
    }

    zpath_upgrade_get_restart_timestamps(&(gs->last_os_upgrade_time), &(gs->last_sarge_upgrade_time));
    zpath_upgrade_read_stats(&(gs->upgrade_stats));
    gs->platform_version = zvm_vm_type_rh_image_version();

    ZPN_DEBUG_STARTUP("Initializing zpath_customer table");
    res = zpath_customer_init(shard_wally, sitec_id, 1, 1);
    if (res != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpath_customer table");
        return res;
    }

    ZPN_DEBUG_STARTUP("Initializing zpath_domain_lookup");
    res = zpath_domain_lookup_init_single_tenant(sitec_id);
    if (res != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpath_domain_lookup");
        return res;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_customer_config...");
    res = zpn_customer_config_init(shard_wally, customer_gid, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpn_customer_config_init failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpn_bc_init(shard_wally, sitec_id, 1, 1, 0);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_bc_init");
        return res;
    }

    res = zpn_location_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_location_init");
        return res;
    }

    res = zpn_workload_tag_group_init(shard_wally, sitec_id, 1, 1, 0); /* dont register overrides */
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_workload_tag_group_init");
        return res;
    }

    ZPN_DEBUG_STARTUP("Initializing et_translate");
    res = et_translate_init_wally(global_wally);
    if (res != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "et_translate_init_wally failed: %s", zpath_result_string(res));
        return res;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_application table");
    res = zpn_application_init(shard_wally, customer_gid, 0, 1, 1, 1);
    if (res != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_application table");
        return res;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_application_domain table");
    res = zpn_application_domain_init(shard_wally, sitec_id, 1);
    if (res != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_application_domain table");
        return res;
    }

    for (int i = 0; i < SITEC_APP_CALC_THREADS; i++) {
        struct zevent_base *z_base;
        char str[100];
        snprintf(str, sizeof(str), "app_thread_%d", i);
        z_base = zevent_handler_create(str, 16*1024*1024, 20);
        if (!z_base) {
            SITEC_LOG(AL_ERROR, "ERROR: Could not create zevent...\n");
            return ZPN_RESULT_ERR;
        }
        zevent_add_to_class(z_base, ZEVENT_CLASS_APP_CALC);
    }

    res = zpn_server_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_server_group table");
        return res;
    }

    res = zpn_server_group_assistant_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_server_group_assistant_group table");
        return res;
    }

    res = zpn_assistant_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_assistant_group table");
        return res;
    }

    res = zpn_assistantgroup_assistant_relation_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_assistantgroup_asssistant_relation table");
        return res;
    }

    res = zpn_assistant_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init assistant table");
        return res;
    }

    res = zpn_cbi_mapping_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_cbi_mapping table");
        return res;
    }

    res = zpn_cbi_profile_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_cbi_profile table");
        return res;
    }

    res = zpn_private_broker_table_init(shard_wally, sitec_id, 1, 1, NULL);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init private broker table");
        return res;
    }

    res = zpn_idp_cert_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_idp_cert table");
        return res;
    }

    res = zpn_idp_init(shard_wally, sitec_id, 1, 1, zpn_sitec_idp_row_callback);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_idp table");
        return res;
    }

    res = zpn_ddil_config_init(shard_wally, sitec_id, zpn_sitec_ddil_config_row_callback, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site table");
        return res;
    }

    res = zpn_firedrill_site_init(shard_wally, sitec_id, zpn_sitec_firedrill_row_callback, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site_firedrill table");
        return res;
    }

    res = zpn_site_init(shard_wally, sitec_id, zpn_sitec_site_row_callback, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site table");
        return res;
    }

    res = zpn_sitec_table_init(shard_wally, sitec_id, zpn_site_controller_row_callback, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site_controller table");
        return res;
    }

    res = zpn_sitec_group_init(shard_wally, sitec_id, zpn_sitec_group_row_callback_1, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site_controller_group table");
        return res;
    }

    res = zpn_sitec_to_group_table_init(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site_controller_to_group table");
        return res;
    }

    res = zpn_sitec_version_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_site_controller_version table");
        return res;
    }

    res = zpn_pbroker_group_init(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init private broker group table");
        return res;
    }

    res = zpn_pbroker_to_group_table_init(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init private broker group mapping table");
        return res;
    }

    res = zpn_privatebrokergroup_trustednetwork_mapping_table_init(shard_wally, sitec_id, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init private broker group trusted network mapping table");
        return res;
    }

    res = zpn_machine_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init machine table");
        return res;
    }

    res = zpn_machine_group_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init machine group table");
        return res;
    }

    res = zpn_machine_to_group_table_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init machine group mapping table");
        return res;
    }

    res = zpn_assistant_version_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init assistant_version table");
        return res;
    }

    res = zpn_app_group_relation_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_app_group_relation table");
        return res;
    }

    res = zpn_servergroup_server_relation_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_servergroup_server_relation table");
        return res;
    }

    res = zpn_application_server_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_app_server table");
        return res;
    }

    res = zpn_private_broker_version_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_private_broker_version_init table");
        return res;
    }

    res = zpn_sub_module_upgrade_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_sub_module_upgrade table");
        return res;
    }

    res = zpn_rule_to_pse_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_rule_to_pse_group_init table. Error:%s", zpath_result_string(res));
        return res;
    }

    res = zpn_svcp_profile_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_svcp_profile table. Error:%s", zpath_result_string(res));
        return res;
    }

    res = zpn_rule_to_step_up_auth_level_mapping_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_rule_to_step_up_auth_level_mapping_init table. Error:%s", zpath_result_string(res));
        return res;
    }

    res = zpn_step_up_auth_level_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_step_up_auth_level_init table. Error:%s", zpath_result_string(res));
        return res;
    }

    res = zpn_customer_resiliency_settings_init(shard_wally, customer_gid, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR,"Could not init private broker resiliency settings: %s",
                            zpn_result_string(res));
        return res;
    }

    res = zpn_signing_cert_init(shard_wally, sitec_id, gs->cfg_pkey, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_signing_cert table");
        return res;
    }

    res = zpn_issuedcert_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_issuedcert table");
        return res;
    }

    /*
     * init znf tables
     */
    res = zpn_znf_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_znf table") ;
        return res;
    }

    res = zpn_znf_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_znf_group table");
        return res;
    }

    res = zpn_znf_to_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
       SITEC_LOG(AL_ERROR, "Could not init zpn_znf_to_group table") ;
       return res;
    }

    res = zpn_scope_init(shard_wally, sitec_id, 1, 1);
    if(res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_scope table");
        return res;
    }


    /* Static Wally Feature */
    if (is_static_wally_enabled) {
        res = zpn_client_table_init_static(gs->sitec_state->rcfg_wally, sitec_id, 1 , 1, 1);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not init zpn_client table for static wally");
            return res;
        }
    } else {
        res = zpn_client_table_init_static(shard_wally, sitec_id, 0, 1, 1);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not init zpn_client table for regular  wally");
            return res;
        }
    }

    res = zpn_shared_customer_domain_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_shared_customer_domain table");
        return res;
    }

    res = zpn_application_group_application_mapping_init(shard_wally, sitec_id, 1, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_application_group_application_mapping table");
        return res;
    }

    res = zpn_application_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_application_group table");
        return res;
    }

    res = zpn_posture_profile_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_posture_profile table");
        return res;
    }

    res = zpn_trusted_network_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_trusted_network table");
        return res;
    }

    /* Initialize policy related tables */
    res = zpn_policy_set_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn policy set table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_command_probe_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpn_command_probe_init failed");
        return res;
    }

    res = zpn_rule_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn rule table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_rule_condition_set_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn condition set table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_rule_condition_operand_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn condition operand table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_rule_to_server_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_rule_to_server_group table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_rule_to_assistant_group_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn_rule_to_assistant_group table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_saml_attrs_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn saml attr table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_scim_attr_header_init(shard_wally, sitec_id, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not initialize zpn scim attr table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_broker_client_apps_init(shard_wally, sitec_id, DEFAULT_RECALC_PUSH_US, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_broker_client_apps");
        return res;
    }

    res = zpn_approval_init(shard_wally, sitec_id, 1, 1);
    if (res) {
      SITEC_LOG(AL_ERROR, "Could not init zpn_approval table: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_approval_mapping_init(shard_wally, sitec_id, 1, 1);
    if (res) {
      SITEC_LOG(AL_ERROR, "Could not init zpn_approval_mapping table: %s", zpn_result_string(res));
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Initializing zpn_c2c_client_registration...");
    res = zpn_c2c_client_registration_init(shard_wally, sitec_id, 1, 1, zpn_sitec_c2c_client_registration_row_cb);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_c2c_client_registration_init table");
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Initializing zpn_c2c_ip_ranges");
    res = zpn_c2c_ip_ranges_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_CRITICAL, "Could not init zpn_c2c_ip_ranges table");
        return res;
    }

    res = zpn_customer_init(); /* must happen before listening for clients */
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not zpn_customer_init()");
        return res;
    }

    res = zpn_inspection_application_init(shard_wally, sitec_id, 1, 1, NULL);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_inspection_application table");
        return res;
    }

    res = zpn_public_certificate_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init public certificate: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_client_setting_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_client_setting: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_inspection_profile_to_control_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init profile_to_control: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_inspection_prof_to_zsdefined_ctrl_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init profile_to_zsdefined_control: %s", zpn_result_string(res));
        return res;
    }

    res = zpn_inspection_profile_init(shard_wally, sitec_id, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init inspection profile: %s", zpn_result_string(res));
        return res;
    }

    SITEC_LOG(AL_NOTICE, "Retrieving cloud configuration from zpath_cloud...");
    res = zpath_cloud_init(global_wally,
                           gs->sitec_state->ovd_slave_db,
                           gs->sitec_state->ovd_remote_db,
                           0,
                           1/*use zpath_table*/);
    if (res) {
        SITEC_LOG(AL_ERROR, "zpath_cloud_init failed");
        return res;
    }
    SITEC_LOG(AL_NOTICE, "Retrieving cloud configuration from zpath_cloud... COMPLETE");

    res = zpn_sitec_redirect_init();
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init sitec redirect");
        return res;
    }

    zpn_set_client_redirect_override_fn(zpn_sitec_redirect_client);

    res = zpn_private_broker_load_init_internal(shard_wally, sitec_id, 1, 1, zpn_balance_inst_load_update_pbroker, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_private_broker_load table");
        return res;
    }

    gs->sni_server = fohh_generic_server_create(1, 1);
    if (!gs->sni_server) {
        SITEC_LOG(AL_ERROR, "Could not create generic SNI dispatching server");
        return res;
    }

    res = lookup_lib_init(zpath_event_collection);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init lookup_lib: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_category_init_per_tenant(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpath_category: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_rule_init_per_tenant(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpath_rule: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_service_init_per_tenant(shard_wally, sitec_id, NULL, 1, 1);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not init zpath_service: %s", zpn_result_string(res));
        return res;
    }

    res = lookup_lib_add_per_tenant(init_table, sizeof(init_table) / sizeof(init_table[0]), shard_wally, sitec_id);
    if (res) {
        SITEC_LOG(AL_ERROR, "Could nit initialize lookup tables: %s", zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_update_offline_domain(const char* offline_domain)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->offline_domain == NULL) {
        gs->offline_domain = SITEC_STRDUP(offline_domain, strlen(offline_domain));
    } else if (strcmp(gs->offline_domain, offline_domain) == 0) {
        SITEC_LOG(AL_ERROR, "No change in offline domain. ");
    } else {
        //There is a change in offline domain. Remove the old domain and add new one.
        SITEC_FREE(gs->offline_domain);
        gs->offline_domain = SITEC_STRDUP(offline_domain, strlen(offline_domain));
        zpn_sitec_update_client_listeners(gs->sni_server, gs->offline_domain);
        zpn_sitec_update_pse_listeners(gs->sni_server, gs->offline_domain);
        zpn_sitec_update_asst_listeners(gs->sni_server, gs->offline_domain);
        zpn_sitec_update_sitec_listeners(gs->sni_server, gs->offline_domain);
    }
}

int zpn_sitec_listen_offline_domain(struct fohh_generic_server *sni_server)
{
    char *offline_domain;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int enable_c2site;
    int res = ZPN_RESULT_NO_ERROR;

    offline_domain = zpn_sitec_get_offline_domain();
    if (offline_domain == NULL) {
        SITEC_LOG(AL_ERROR, "offline domain not configured");
        return ZPN_RESULT_ERR;
    }

    zpn_sitec_update_offline_domain(offline_domain);

    res = zpn_sitec_get_new_user_support(&enable_c2site);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to get new-user-support flag");
        return res;
    }

    res = zpn_sitec_add_client_listeners(gs->sni_server, gs->offline_domain, enable_c2site);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to add client offline domains");
        return res;
    }

    res = zpn_sitec_add_pse_listeners(gs->sni_server, gs->offline_domain);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to add client offline domains");
        return res;
    }

    res = zpn_sitec_add_asst_listeners(gs->sni_server, gs->offline_domain);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to add client offline domains");
        return res;
    }

    res = zpn_sitec_add_sitec_listeners(gs->sni_server, gs->offline_domain);
    if (res) {
        SITEC_LOG(AL_ERROR, "unable to add client offline domains");
        return res;
    }

    return res;
}

static int validate_listen_ip(struct argo_inet *ip)
{
    int res = ZPN_RESULT_ERR;
    char ip_str[ARGO_INET_ADDRSTRLEN];

    if (!ip) {
        SITEC_LOG(AL_CRITICAL, "Null ip");
        return res;
    }

    argo_inet_generate(ip_str, ip);

    if ((ip->length == 4) && ((ip->netmask == 32) || (strcmp(ip_str, "0.0.0.0") == 0))) {
        /* Valid IPv4 address or INADDR_ANY */
        res = ZPN_RESULT_NO_ERROR;
    } else if ((ip->length == 16) && ((ip->netmask == 128) || (strcmp(ip_str, "::1") == 0))) {
        /* Valid IPv6 address or INADDR_ANY */
        res = ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_LOG(AL_ERROR, "Invalid listen address: %s", ip_str);
    }

    return res;
}

void zpn_sitec_generic_listen()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    ZPN_SITEC_ASSERT_HARD(gs->sitec_id != 0, "sitec gid is 0!");

    struct zpn_site_controller *sc;
    struct argo_inet localhost[2];
    struct argo_inet *listen_ips;
    int listen_ips_count;
    int successful_listen_ips_count;
    int j;
    int listen_all_ips = 0;
    int res;
    int i;

    res = zpn_sitec_get_by_id(gs->sitec_id, &sc, NULL, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not get site controller");
        return;
    }

    if (sc->listen_ips_count) {
        listen_ips = sc->listen_ips;
        listen_ips_count = sc->listen_ips_count;

        for (j = 0; j < listen_ips_count; j++) {
            char str[ARGO_INET_ADDRSTRLEN] = {0};
            struct argo_inet l_ip = sc->listen_ips[j];
            /* Validate each listen ips, fallback to listening on all ips if any bad ip config detected */
            if (validate_listen_ip(&l_ip) != ZPN_RESULT_NO_ERROR) {
                listen_all_ips = 1;
                ZPN_LOG(AL_CRITICAL, "Invalid listen address:%s, fallback to listening on all ips", argo_inet_generate(str, &l_ip));
                break;
            }
        }
    } else {
        /* Listen on all ips */
        listen_all_ips = 1;
    }

    if (listen_all_ips) {
        listen_ips = localhost;
        listen_ips_count = 2;
        argo_string_to_inet("0.0.0.0", &(localhost[0]));
        argo_string_to_inet("::", &(localhost[1]));
        ZPN_LOG(AL_INFO, "Listening on all ips, port:%d", ZPN_CLIENT_BROKER_PORT);
    }

    successful_listen_ips_count = 0;
    for (i = 0; i < listen_ips_count; i++) {

        char str[ARGO_INET_ADDRSTRLEN];
        struct argo_inet *listen_ip = &(listen_ips[i]);

        struct sockaddr_storage addr;
        socklen_t addr_len;

        argo_inet_to_sockaddr(listen_ip, (struct sockaddr *)&addr, &addr_len, htons(ZPN_SITEC_PORT));
        res = fohh_generic_server_listen(gs->sni_server,(struct sockaddr *)&addr, addr_len, 0);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not listen for clients, port %d, IP %s, reason = %s",
                    ZPN_SITEC_PORT, argo_inet_generate(str, listen_ip), zpn_result_string(res));
        } else {
            successful_listen_ips_count++;
            SITEC_LOG(AL_NOTICE, "Listen for clients, port %d, IP %s",
                    ZPN_SITEC_PORT, argo_inet_generate(str, listen_ip));
        }
    }
    if (0 == successful_listen_ips_count) {
        ZPN_SITEC_ASSERT_HARD(0,
                                "Couldn't listen on any ports, may be bad listen IP config?");
    }
}

void zpn_sitec_firedrill_fill_comprehensive_stats(struct zpn_sitec_comprehensive_stats *out_data)
{
    out_data->firedrill_triggered_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_triggered_count, 0);
    out_data->firedrill_completed_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_completed_count, 0);
    out_data->firedrill_cmdline_disable_count = __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_cmdline_disable_count, 0);
}

int zpn_sitec_create_skip_table_for_sync_time_init()
{
    static char *shard_skip_tables_for_sync_time[] =
        {
         "zpn_private_broker_load",
         "zpn_site_controller_version"
        };
    static int shard_skip_tables_for_sync_time_cnt = sizeof(shard_skip_tables_for_sync_time) / sizeof(shard_skip_tables_for_sync_time[0]);
    int i;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    gs->shard_skip_table = argo_hash_alloc(5, 1);
    if (!gs->shard_skip_table) goto fail;
    for (i = 0; i < shard_skip_tables_for_sync_time_cnt; i++) {
        if (argo_hash_store(gs->shard_skip_table, shard_skip_tables_for_sync_time[i], strlen(shard_skip_tables_for_sync_time[i]), 0, gs->shard_skip_table)) {
            SITEC_LOG(AL_CRITICAL, "Could not add shard skip table %s", shard_skip_tables_for_sync_time[i]);
            goto fail;
        }
    }

    return ZPN_RESULT_NO_ERROR;
fail:
    return ZPN_RESULT_ERR;
}
