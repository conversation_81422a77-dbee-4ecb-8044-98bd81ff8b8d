/*
 * zpn_sitec_broker_conn.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <string.h>
#include "argo/argo.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_db.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include <pthread.h>
#include "fohh/fohh_log.h"
#include "zpn_sitecd/zpn_sitec.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zpn_sitecd/zpn_sitec_proxy.h"
#include "zpn_sitecd/zpn_sitec_util.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn_sitecd/zpn_sitec_alt_cloud.h"
#include "fohh/fohh_private.h"
#include "zpn/zpn_file_fetch.h"
#include "zpn/zpn_broker.h"
#include "zpn_sitecd/zpn_sitec_siem.h"
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
static struct zpn_sitec_stats_tx_stats {
    uint64_t stats;
} stats;

static struct  zpn_sitec_stats_tx_cfg cfg[SITEC_STATS_TX_MAX_OBJECTS];
int64_t sitec_stats_feature_enabled = SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT;
static int cfg_ready;
int debuglog;

extern int key_retry_flag[FILE_CODE_MAX];
extern struct ff_stats file_stats[FILE_CODE_MAX];
const char *dbhost = "localhost";

extern int g_firedrill_disable_invoked_on_cmdline;

char sc_sni_customer_domain_userdb_tmpl[ARGO_MAX_NAME_LENGTH];
char userdb_wally_host[ARGO_MAX_NAME_LENGTH];
static int64_t cfg_status_interval = STATUS_INTVL_DEFAULT;
static int64_t rcfg_status_interval = STATUS_INTVL_DEFAULT;
static int64_t ovd_status_interval = STATUS_INTVL_DEFAULT;
static int64_t userdb_status_interval = STATUS_INTVL_DEFAULT;
static struct zpn_sitec_comprehensive_stats last_comprehensive_stats_data;
extern struct argo_structure_description *zpn_broker_mission_critical_description_resp;
extern struct zpn_firedrill_stats g_sitec_firedrill_stats_obj;


int zpn_sitec_redirect_request_cb(void* argo_cookie_ptr,
                                  void* argo_structure_cookie_ptr,
                                  struct argo_object* object)
{
    struct fohh_connection *conn = argo_structure_cookie_ptr;
    struct zpn_broker_redirect *req = object->base_structure_void;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char dump[5000];

    ZPN_LOG(AL_INFO, "Received zpn_broker_redirect for connection:%s", fohh_description(conn));

    /* Dump the broker redirect message */
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_INFO, "Rx: %s", dump);
    }

    if (req->broker_count) {
        if (fohh_redirect(conn, (const char **)req->brokers, req->broker_count, req->timestamp_s, req->sni_suffixes) != 0) {
            ZPN_LOG(AL_INFO, "fohh_redirect failed for connection:%s", fohh_description(conn));
        }
    } else {
        ZPN_LOG(AL_WARNING, "broker redirect message returns 0 broker candidate for connection:%s", fohh_description(conn));
    }

    if (req->alt_cloud) {
        zpn_sitec_store_alt_cloud_to_file(gs->alt_cloud_enabled, req->alt_cloud);
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_send_fohh_info_with_alt_cloud_capability(struct fohh_connection *connection)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char *alt_cloud = NULL;

    ZPATH_RWLOCK_RDLOCK(&(gs->alt_cloud_lock), __FILE__, __LINE__);
    if (gs->alt_cloud) {
        alt_cloud = SITEC_STRDUP(gs->alt_cloud, strlen(gs->alt_cloud));
    }
    ZPATH_RWLOCK_UNLOCK(&(gs->alt_cloud_lock), __FILE__, __LINE__);

    /* Send fohh connection info to peer */
    const char *sc_client_capabilities[] = {ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR};
    struct fohh_info fohh_cookie;
    fohh_cookie.capabilities_count = 1;
    fohh_cookie.current_alt_cloud = alt_cloud;
    fohh_cookie.capabilities = sc_client_capabilities;

    fohh_send_info_with_capability(connection, &fohh_cookie);

    SITEC_FREE(alt_cloud);
}

static int zpn_sitec_cfg_conn_cb(struct fohh_connection*    connection,
                                 enum fohh_connection_state state,
                                 void*                      cookie)
{
    if (state == fohh_connection_connected) {
        int64_t sc_gid = 0;
        char *cn_iter;
        char *cn;
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_sitec_redirect_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for config connection %s",
                    fohh_description(connection));
            return res;
        }

        zpn_sitec_send_fohh_info_with_alt_cloud_capability(connection);

        if (fohh_connection_on_sitec == fohh_connection_get_site_ep_type(connection)) {
            cn = fohh_peer_cn(connection);
            for (cn_iter = &(cn[0]); *cn_iter; cn_iter++) {
                if (isdigit(*cn_iter)) {
                    sc_gid = strtoll(cn_iter, &cn_iter, 0);
                    break;
                }
            }
            if (sc_gid == 0) {
                ZPN_LOG(AL_WARNING, "Could not derive sitec gid from cname(%s)", cn);
                return ZPN_RESULT_ERR;
            }
        }
        fohh_peer_set_id(connection, sc_gid);
        zpn_sitec_send_status_to_all_sitec();
    } else {
        if (fohh_get_state(connection) == fohh_connection_connected) {
            zpn_sitec_send_status_to_all_sitec();
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_log_tx_conn_cb(struct fohh_connection *f_conn,
                            enum fohh_connection_state f_conn_state,
                            void *cookie)
{
    struct fohh_log_tx_state *tx_state = cookie;
    struct argo_state *argo;
    int res;

    /* We can get a callback before the connection is complete. We'll
     * assign tx_state->f_conn here in case that happens, as tx_state->f_conn is
     * never destroyed once assigned */
    if (!tx_state->f_conn) tx_state->f_conn = f_conn;

    ZPN_DEBUG_STARTUP("Connection callback %s -> %s[%s]:%d, connection state = %s, max sequence acked = %ld",
                       argo_log_get_name(tx_state->collection),
                       tx_state->f_conn->remote_address_name,
                       tx_state->f_conn->remote_address_str,
                       ntohs(tx_state->f_conn->service_port_ne),
                       fohh_connection_state_strings[f_conn_state],
                       (long) tx_state->max_sequence_acked);

    if (f_conn_state == fohh_connection_connected) {
        //fohh_set_debug(f_conn, 1);
        SITEC_LOG(AL_NOTICE, "Log(%s) successfully connected to Zscaler Cloud: %s",
                 argo_log_get_name(tx_state->collection), fohh_description(f_conn));
        ZPN_DEBUG_STARTUP("%p: %s: Connected, will start reading from id: %"PRId64", setting incarnation %"PRId64"", tx_state, argo_log_get_name(tx_state->collection), (tx_state->max_sequence_acked + 1), fohh_connection_incarnation(f_conn));
        argo_log_read_set_index(tx_state->reader, tx_state->max_sequence_acked + 1);
        tx_state->f_conn_incarnation = fohh_connection_incarnation(f_conn);
        argo = fohh_argo_get_rx(f_conn);
        if ((res = argo_register_structure(argo, fohh_log_element_ack_description, log_tx_conn_ack_cb, cookie))) {
            SITEC_LOG(AL_ERROR, "Could not register log upload for connection %s", fohh_description(f_conn));
            return res;
        }

        res = log_tx_conn_type(f_conn, argo_log_get_name(tx_state->collection));
        if (res) {
            SITEC_LOG(AL_ERROR, "log(%s) failed to send log type to receiver side %s", argo_log_get_name(tx_state->collection), fohh_description(f_conn));
        }

        /* Register handler for zpn_broker_redirect message */
        res = argo_register_structure(fohh_argo_get_rx(f_conn), zpn_broker_redirect_description, zpn_sitec_redirect_request_cb, f_conn);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for log connection %s, error: %s", fohh_description(f_conn), argo_result_string(res));
        }

        /* Need to send this message to trigger redirect evaluation logic on the broker side */
        zpn_sitec_send_fohh_info_with_alt_cloud_capability(f_conn);
    }
    if (f_conn_state == fohh_connection_disconnected) {
        SITEC_LOG(AL_NOTICE, "Log(%s) to Zscaler Cloud closed: %s %s",
                 argo_log_get_name(tx_state->collection), fohh_description(f_conn), fohh_close_reason(f_conn));
        tx_state->current_connection_transmit_count = 0;
        tx_state->current_connection_ack_receive_count = 0;
        tx_state->current_connection_unacknowledged_count = 0;
    }
    return FOHH_RESULT_NO_ERROR;
}

int zpn_sitec_log_conn_init()
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int result;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    result = fohh_log_send(argo_log_get_name(zpn_event_collection),
                            argo_log_get_name(zpn_event_collection),
                            broker_name,
                            sc_sni_customer_domain_log,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up event log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_event_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    gs->sitec_state->event_log_channel = fohh_get_log_handle(zpn_event_collection);
    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_event_collection),
                                    (char *)gs->cfg_key_cloud);


    zpath_debug_add_fohh_log_collection(zpn_event_collection);

    result = fohh_log_send(argo_log_get_name(zpn_sc_asst_auth_collection),
                            argo_log_get_name(zpn_sc_asst_auth_collection),
                            broker_name,
                            sc_sni_customer_domain_log,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up sc asst auth log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_sc_asst_auth_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_sc_asst_auth_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_sc_pb_auth_collection),
                            argo_log_get_name(zpn_sc_pb_auth_collection),
                            broker_name,
                            sc_sni_customer_domain_log,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up sc pb auth log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_sc_pb_auth_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_sc_pb_auth_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_auth_collection),
                            argo_log_get_name(zpn_auth_collection),
                            broker_name,
                            sc_sni_customer_domain_log,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up auth log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_auth_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_auth_collection),
                                    (char *)gs->cfg_key_cloud);

    result = zpn_sitec_pse_log_conn_init();
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up pse log conn: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_sitec_asst_log_conn_init();
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up asst log conn: %s", zpath_result_string(result));
        return result;
    }

    return result;
}

int zpn_sitec_cfg_conn_init()
{
    int result;
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db = NULL;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[512];
    int is_endpoint = 1;
    char *cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    if (!gs->sitec_id) {
        return ZPN_RESULT_ERR;
    }

    sitec_state->cfg_wally = wally_create("sitec_wally_cfg",
                                          is_endpoint,
                                          zpath_debug_wally_endpoints_init,
                                          NULL,
                                          NULL,
                                          NULL);
    if (!sitec_state->cfg_wally) {
        ZPN_LOG(AL_ERROR, "Could not create zpa sitec wally");
        return ZPN_RESULT_ERR;
    }

    if (gs->use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s.%d", ZPATH_LOCAL_SLAVE_DB_NAME, gs->cfg_key_shard);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_shard_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(sitec_state->cfg_wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            return ZPN_RESULT_ERR;
        }

        snprintf(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(sitec_state->cfg_wally,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            return ZPN_RESULT_ERR;
        }
        sitec_state->cfg_slave_db = slave_db;
    }

    snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    wally_fohh_client_handle = wally_fohh_client_create(sitec_state->cfg_wally,
                                                        NULL,
                                                        hostname,
                                                        sc_sni_customer_domain_cfg,
                                                        (char *)cloud_name,
                                                        htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                        zpn_sitec_cfg_conn_cb);
    if (!wally_fohh_client_handle) {
        ZPN_LOG(AL_ERROR, "Could not create fohh origin");
        return ZPN_RESULT_ERR;
    }

    sitec_state->cfg_wally_fohh_client_handle = wally_fohh_client_handle;
    sitec_state->cfg_fohh_conn = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

    /* Make the config connection sticky */
    fohh_set_sticky(sitec_state->cfg_fohh_conn, 1);

    //fohh_connection_site_init(sitec_state->cfg_fohh_conn, gs->offline_domain, NULL, hostname, 0, 1, 0, gs->sitec_ssl_ctx, fohh_g.ssl_ctx);

    fohh_connection_monitor_sanity(sitec_state->cfg_fohh_conn,
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->cfg_fohh_conn,
                                    (char *)gs->cfg_key_cloud);

    fohh_set_status_interval(sitec_state->cfg_fohh_conn, &cfg_status_interval);

    sitec_state->cfg_remote_db = wally_add_origin(sitec_state->cfg_wally,
                                                  hostname,
                                                  wally_fohh_client_handle,
                                                  wally_fohh_register_for_index,
                                                  wally_fohh_deregister_for_index,
                                                  wally_fohh_set_cookie,
                                                  wally_fohh_get_status,
                                                  wally_fohh_add_table,
                                                  NULL,
                                                  wally_fohh_dump_state,
                                                  1);
    if (!sitec_state->cfg_remote_db) {
        ZPN_LOG(AL_ERROR, "Could not add remote origin");
        return ZPN_RESULT_ERR;
    }

    if (gs->use_sqlt) {
        result = zpath_table_init(sitec_state->cfg_wally,
                                  slave_db,
                                  sitec_state->cfg_remote_db,
                                  1);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_table_init failed for config shard %d", gs->cfg_key_shard);
            return ZPN_RESULT_ERR;
        }
    }

    zpath_debug_wally_add(sitec_state->cfg_wally, 0);

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_on_userdb_client_created(struct fohh_connection *f_conn,
        int64_t zone, int64_t userdb, int current_to_sitec)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    /*
     * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
     * Default status interval is 1 second for sending fohh_status_request messages.
     */
    fohh_set_status_interval(f_conn, &userdb_status_interval);
    gs->sitec_state->userdb_wally_conn = f_conn;
    gs->sitec_state->userdb_slave_db = wally_fohh_client_get_slave_origin(f_conn->cookie);

    fohh_connection_monitor_sanity(f_conn,
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(f_conn,
                                    (char *)gs->cfg_key_cloud);
}

int zpn_sitec_userdb_conn_init()
{
    int result;

    char *cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    snprintf_nowarn(sc_sni_customer_domain_userdb_tmpl, sizeof(sc_sni_customer_domain_userdb_tmpl), "%ld.%%ld.%%ld.scuserdb.%s", (long) gs->sitec_id, cloud_name);
    snprintf_nowarn(userdb_wally_host, sizeof(userdb_wally_host), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    result = zpath_et_wally_userdb_init_using_ctx(dbhost,
                                        userdb_wally_host,
                                        sc_sni_customer_domain_userdb_tmpl,
                                        1,
                                        gs->use_sqlt,
                                        0,
                                        1,
                                        ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_id),
                                        zpn_sitec_cfg_conn_cb,
                                        cloud_name,
                                        gs->cfg_key_cloud,
                                        NULL,
                                        zpn_sitec_on_userdb_client_created,
                                        0);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally userdb library: %s", zpn_result_string(result));
    }

    return result;
}

int  zpn_sitec_cfg_ovd_conn_init()
{
    int result;
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db = NULL;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[512];
    int is_endpoint = 1;
    char *cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    if (!sitec_state) {
        return ZPN_RESULT_ERR;
    }

    if (!cloud_name) {
        return ZPN_RESULT_ERR;
    }

    sitec_state->ovd_wally = wally_create("sitec_wally_ovd",
                                          is_endpoint,
                                          zpath_debug_wally_endpoints_init,
                                          NULL,
                                          NULL,
                                          NULL);
    if (!sitec_state->ovd_wally) {
        ZPN_LOG(AL_ERROR, "Could not create zpa sitec wally override");
        return ZPN_RESULT_ERR;
    }

    if (gs->use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s", ZPATH_LOCAL_SLAVE_DB_NAME);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_global_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(sitec_state->ovd_wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            return ZPN_RESULT_ERR;
        }

        snprintf(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(sitec_state->ovd_wally,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            return ZPN_RESULT_ERR;
        }
        sitec_state->ovd_slave_db = slave_db;
    }

    snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    wally_fohh_client_handle = wally_fohh_client_create(sitec_state->ovd_wally,
                                                        NULL,
                                                        hostname,
                                                        sc_sni_customer_domain_ovd,
                                                        (char *)cloud_name,
                                                        htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                        zpn_sitec_cfg_conn_cb);
    if (!wally_fohh_client_handle) {
        ZPN_LOG(AL_ERROR, "Could not create fohh origin for overide");
        return ZPN_RESULT_ERR;
    }

    sitec_state->ovd_remote_db = wally_add_origin(sitec_state->ovd_wally,
                                                  hostname,
                                                  wally_fohh_client_handle,
                                                  wally_fohh_register_for_index,
                                                  wally_fohh_deregister_for_index,
                                                  wally_fohh_set_cookie,
                                                  wally_fohh_get_status,
                                                  wally_fohh_add_table,
                                                  NULL,
                                                  wally_fohh_dump_state,
                                                  1);
    if (!sitec_state->ovd_remote_db) {
        ZPN_LOG(AL_ERROR, "Could not add remote origin");
        return ZPN_RESULT_ERR;
    }
    sitec_state->ovd_wally_fohh_client_handle = wally_fohh_client_handle;
    sitec_state->ovd_fohh_conn = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

    fohh_connection_monitor_sanity(sitec_state->ovd_fohh_conn,
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->ovd_fohh_conn,
                                    (char *)gs->cfg_key_cloud);

    fohh_set_status_interval(sitec_state->ovd_fohh_conn, &ovd_status_interval);

    if (gs->use_sqlt) {
        result = zpath_table_init(sitec_state->ovd_wally,
                                  slave_db,
                                  sitec_state->ovd_remote_db,
                                  1);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_table_init failed for config override wally");
            return ZPN_RESULT_ERR;
        }
    }

    zpath_debug_wally_add(sitec_state->ovd_wally, 0);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_rcfg_conn_init()
{
    int result;
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db = NULL;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[512];
    int is_endpoint = 1;
    char *cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    if (!sitec_state) {
        return ZPN_RESULT_ERR;
    }

    if (!cloud_name) {
        return ZPN_RESULT_ERR;
    }

    sitec_state->rcfg_wally = wally_create("sitec_static_wally",
                                           is_endpoint,
                                           zpath_debug_wally_endpoints_init,
                                           NULL,
                                           NULL,
                                           NULL);
    if (!sitec_state->rcfg_wally) {
        ZPN_LOG(AL_ERROR, "Could not create zpa sitec static wally");
        return ZPN_RESULT_ERR;
    }

    if (gs->use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s.%dr", ZPATH_LOCAL_SLAVE_DB_NAME, gs->cfg_key_shard);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_shard_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(sitec_state->rcfg_wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            return ZPN_RESULT_ERR;
        }

        snprintf(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(sitec_state->rcfg_wally,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            return ZPN_RESULT_ERR;
        }
        sitec_state->rcfg_slave_db = slave_db;
    }

    snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    wally_fohh_client_handle = wally_fohh_client_create(sitec_state->rcfg_wally,
                                                        NULL,
                                                        hostname,
                                                        sc_sni_customer_domain_rcfg,
                                                        (char *)cloud_name,
                                                        htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                        zpn_sitec_cfg_conn_cb);
    if (!wally_fohh_client_handle) {
        ZPN_LOG(AL_ERROR, "Could not create fohh origin for overide");
        return ZPN_RESULT_ERR;
    }

    sitec_state->rcfg_remote_db = wally_add_origin(sitec_state->rcfg_wally,
                                                   hostname,
                                                   wally_fohh_client_handle,
                                                   wally_fohh_register_for_index,
                                                   wally_fohh_deregister_for_index,
                                                   wally_fohh_set_cookie,
                                                   wally_fohh_get_status,
                                                   wally_fohh_add_table,
                                                   NULL,
                                                   wally_fohh_dump_state,
                                                   1);
    if (!sitec_state->rcfg_remote_db) {
        ZPN_LOG(AL_ERROR, "Could not add remote origin");
        return ZPN_RESULT_ERR;
    }
    sitec_state->rcfg_wally_fohh_client_handle = wally_fohh_client_handle;
    sitec_state->rcfg_fohh_conn = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

    fohh_connection_monitor_sanity(sitec_state->rcfg_fohh_conn,
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->rcfg_fohh_conn,
                                    (char *)gs->cfg_key_cloud);

    fohh_set_status_interval(sitec_state->rcfg_fohh_conn, &rcfg_status_interval);

    if (gs->use_sqlt) {
        result = zpath_table_init(sitec_state->rcfg_wally,
                                  slave_db,
                                  sitec_state->rcfg_remote_db,
                                  1);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_table_init failed for static config for shard %d", gs->cfg_key_shard);
            return ZPN_RESULT_ERR;
        }
    }

    zpath_debug_wally_add(sitec_state->rcfg_wally, 0);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_log_control_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_sitec_log_control *req = object->base_structure_void;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_connected_broker *conn_brk = fohh_connection_get_dynamic_cookie(f_conn);

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    if (conn_brk) {
        pthread_mutex_lock(&gs->lock);
        conn_brk->log_upload = req->upload;
        conn_brk->debug_flag = req->flag;

        /* Process logging update request from broker */
        if (req->type == ZPN_SCCTL_LOG_CONTROL_OPCODE_SET_LOGGING_FLAG) {
            /* Update zpn flag for sitec process */
            if (zpn_debug_get(req->flag)) {
                ZPN_LOG(AL_INFO, "Logging updated from cloud, turning OFF zpn flag %s = %"PRIu64, zpn_debug_names[req->flag], req->flag);
                zpn_debug_reset(req->flag);
            } else {
                zpn_debug_set(req->flag);
                ZPN_LOG(AL_INFO, "Logging updated from cloud, turning ON zpn flag %s = %"PRIu64, zpn_debug_names[req->flag], req->flag);
            }
        }

        /* Update sitec_global*/
        pthread_mutex_unlock(&gs->lock);
    } else {
        ZPN_LOG(AL_ERROR, "%s: Cannot find connected broker for this sitec",
                fohh_description(f_conn));
    }

    return ZPN_RESULT_NO_ERROR;
}

/* PBroker stats control callback */
static int zpn_sitec_stats_control_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct fohh_connection *connection = argo_structure_cookie_ptr;
    struct zpn_sitec_stats_control *req = object->base_structure_void;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_connected_broker *conn_brk = fohh_connection_get_dynamic_cookie(connection);

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Rx: %s", dump);
        }
    }

    if (conn_brk) {
        pthread_mutex_lock(&gs->lock);
        conn_brk->stats_upload = req->upload;
        pthread_mutex_unlock(&gs->lock);
        SITEC_LOG(AL_INFO, "%s - stats upload status changed from %d to %d",
                  fohh_description(connection), conn_brk->stats_upload, req->upload);
    }

    return ZPN_RESULT_NO_ERROR;
}

int sitec_encrypt_content_to_file(char *filename, const char *plaintext)
{
    FILE *fp;
    uint8_t file_data[8192];
    size_t file_len;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    fp = fopen(filename, "w");
    if (!fp) {
        SITEC_LOG(AL_ERROR, "Cannot write %s - %s\n", filename, strerror(errno));
        return ZPATH_RESULT_ERR;
    }

    file_len = sizeof(file_data);
    if (zcrypt_encrypt(&gs->cfg_hw_key, (char *)plaintext, strlen(plaintext), file_data, &file_len) != ZCRYPT_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Cannot encrypt data\n");
        fclose(fp);
        unlink(filename);
        return ZPATH_RESULT_ERR;
    }

    if (fwrite(file_data, file_len, 1, fp) != 1) {
        SITEC_LOG(AL_ERROR, "Cannot write to file %s\n", filename);
        fclose(fp);
        unlink(filename);
        return ZPATH_RESULT_ERR;
    }
    fclose(fp);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_decrypt_key_response_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zcrypt_pkey *zpkey;
    struct zpn_decrypt_key_response *resp = (struct zpn_decrypt_key_response *)object->base_structure_void;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char *decoded_key;

    if (resp->status != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s decrypt failed with error %d", resp->requestor, resp->status);
        zpath_interlock_signal(&gs->sitesp_interlock);
        return ZPN_RESULT_ERR;
    }

    decoded_key = base64_decode(resp->decrypted_key);
    if (decoded_key == NULL) {
        SITEC_LOG(AL_ERROR, "%s base64 decode failed", resp->requestor);
        zpath_interlock_signal(&gs->sitesp_interlock);
        return ZPN_RESULT_ERR;
    }

    if (strcmp(resp->requestor, SITESP_SIGNING_PRIVATE_KEY_STR) == 0) {
        sitec_encrypt_content_to_file(SITESP_SIGNING_PKEY_FILE, decoded_key);
        gs->cfg_sitesp_signing_pkey = SITEC_STRDUP(decoded_key, strlen(decoded_key));
    } else if (strcmp(resp->requestor, SITESP_PRIVATE_KEY_STR) == 0) {
        sitec_encrypt_content_to_file(SITESP_PKEY_FILE, decoded_key);
        zpkey = zcrypt_pkey_create_from_pem(decoded_key);
        gs->cfg_sitesp_pkey = zpkey;
    } else {
        ZPN_SITEC_ASSERT_HARD(0, "not a valid requestor");
    }

    free(decoded_key);
    zpath_interlock_signal(&gs->sitesp_interlock);

    return ZPN_RESULT_NO_ERROR;
}

/*Callback when decrypted key is received*/
static int zpn_sitec_key_cb(void *argo_cookie_ptr,
                            void *argo_structure_cookie_ptr,
                            struct argo_object *object)
{
    struct zpn_file_fetch_key *key = object->base_structure_void;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (!key) {
        ZPN_LOG(AL_ERROR,"Received NULL key from broker!");
        return ZPN_RESULT_ERR;
    }

    if (!key->enc_or_dec_pvt_key && !key->enc_or_dec_pvt_key2) {
        if (key->filename && !strcmp(key->filename, FILENAME_GEOIP_ENC)) {
            key_retry_flag[MMDB_GEOIP] = 1;
            file_stats[MMDB_GEOIP].key_fail_cnt++;
            if (file_stats[MMDB_GEOIP].key_fail_cnt > MAX_FAIL_COUNT && !strcmp(key->version, "default")) {
                SITEC_LOG(AL_ERROR, " FILE_FETCH: max retries reached for default file.. exiting!");
                if(!sub_module_upgrade_failed) {
                    zpn_sitec_write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
                key_retry_flag[MMDB_GEOIP] = 0;
            } else {
                ZPN_LOG(AL_NOTICE,"The key was not set by broker on attempt %d! Waiting for retry",
                        file_stats[MMDB_GEOIP].key_fail_cnt);
            }
        } else if (key->filename && !strcmp(key->filename, FILENAME_ISP_ENC)) {
            key_retry_flag[MMDB_ISP] = 1;
            file_stats[MMDB_ISP].key_fail_cnt++;
            if (file_stats[MMDB_ISP].key_fail_cnt > MAX_FAIL_COUNT && !strcmp(key->version,"default")) {
                ZPATH_LOG(AL_ERROR, " FILE_FETCH: max retries reached for default file.. exiting!");
                if (!sub_module_upgrade_failed) {
                    zpn_sitec_write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
                key_retry_flag[MMDB_ISP] = 0;
            } else {
                ZPN_LOG(AL_NOTICE,"The key was not set by broker on attempt %d! Waiting for retry", file_stats[MMDB_ISP].key_fail_cnt);
            }
        } else {
            ZPN_LOG(AL_ERROR,"NULL/Invalid filename received");
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if (!strcmp(key->filename,FILENAME_GEOIP_ENC)) {
        file_stats[MMDB_GEOIP].key_fail_cnt = 0;
        zpn_sitec_geoip_replace_and_trigger_reload(key->filename, key, gs->cfg_hw_key);
    } else if(!strcmp(key->filename,FILENAME_ISP_ENC)) {
        file_stats[MMDB_ISP].key_fail_cnt = 0;
        zpn_sitec_ispip_replace_and_trigger_reload(key->filename, key, gs->cfg_hw_key);
    } else {
        ZPN_LOG(AL_ERROR,"Received key for invalid filename! Filename received: %s",key->filename);
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_control_conn_cb(struct fohh_connection *connection,
                                     enum fohh_connection_state state,
                                     void *cookie)
{

    struct argo_state *argo;
    int res;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_connected_broker *conn_brk;

    if (state == fohh_connection_connected) {

        SITEC_LOG(AL_NOTICE, "%s - Control channel successfully connected to Zscaler Cloud",
                  fohh_description(connection));

        argo = fohh_argo_get_rx(connection);

        conn_brk = ZPN_CALLOC(sizeof(*conn_brk));
        if (!conn_brk) {
            ZPN_LOG(AL_ERROR, "Unable to alloc connected_broker");
            return ZPN_RESULT_NO_MEMORY;
        }

        conn_brk->f_conn = connection;
        conn_brk->f_conn_incarnation = fohh_connection_incarnation(connection);

        {   /* Lock scope */
            pthread_mutex_lock(&gs->lock);
            fohh_connection_set_dynamic_cookie(connection, conn_brk);
            gs->sitec_state->connected_broker = conn_brk;
            pthread_mutex_unlock(&gs->lock);
        }

        zpn_sitec_send_fohh_info_with_alt_cloud_capability(connection);

        if ((res = argo_register_structure(argo,
                                           zpn_sitec_log_control_description,
                                           zpn_sitec_log_control_cb,
                                           connection))) {
            SITEC_LOG(AL_ERROR, "Could not register log ctrl for sitec connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo,
                                           zpn_sitec_stats_control_description,
                                           zpn_sitec_stats_control_cb,
                                           connection))) {
            SITEC_LOG(AL_ERROR, "Could not register stats ctrl for sitec connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo,
                                           zpn_broker_redirect_description,
                                           zpn_sitec_redirect_request_cb,
                                           connection))) {
            SITEC_LOG(AL_ERROR, "Could not register broker_redirect for sitec connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo,
                                           zpn_file_fetch_key_description,
                                           zpn_sitec_key_cb,
                                           connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_key_cb for sitec connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo,
                                           zpn_decrypt_key_response_description,
                                           zpn_sitec_decrypt_key_response_cb,
                                           connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_key_cb for sitec connection %s", fohh_description(connection));
            return res;
        }


        zpn_send_zpn_tcp_info_report(connection,
                                    fohh_connection_incarnation(connection),
                                    connection,
                                    zvm_vm_type_to_str_concise(zvm_type_get()),
                                    gs->sitec_runtime_os);

        SITEC_LOG(AL_INFO, "sitec connection %s, Platform Detail is %s, Runtime OS is %s",
                 fohh_description(connection), zvm_vm_type_to_str_concise(zvm_type_get()), gs->sitec_runtime_os);

        zpn_send_zpn_sitec_environment_report(connection, 0, gs->sarge_version);

        if (gs->waiting_for_decrypt_key_resp) {
            SITEC_LOG(AL_INFO, "%s - new connection is just created while waiting for decrypt_key_response",
                    fohh_description(connection));
            zpath_interlock_signal(&gs->sitesp_interlock);
        }
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: sitec control connection DOWN: %s",
                fohh_description(connection),
                reason);

        if (gs->waiting_for_decrypt_key_resp) {
            SITEC_LOG(AL_INFO, "%s - connection is to be closed while waiting for decrypt_key_response",
                    fohh_description(connection));
            zpath_interlock_signal(&gs->sitesp_interlock);
        }

        {
            /* Lock scope */
            pthread_mutex_lock(&gs->lock);
            conn_brk = fohh_connection_get_dynamic_cookie(connection);
            if (conn_brk) {
                /* Clear out connection */
                fohh_connection_set_dynamic_cookie(connection, NULL);
                gs->sitec_state->connected_broker = NULL;
                ZPN_FREE(conn_brk);
            }
            pthread_mutex_unlock(&gs->lock);
            SITEC_LOG(AL_INFO, "%s - Disconnected from broker", fohh_description(connection));
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_control_conn_unblock_cb(struct fohh_connection *connection,
                                             enum fohh_queue_element_type element_type,
                                             void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

int zpn_sitec_ctrl_conn_init()
{
    char        broker_name[1000];
    char        broker_stats_name[1000 + 16];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);
    snprintf(broker_stats_name, sizeof(broker_stats_name), "brk-ctl-%s", broker_name);

    SITEC_LOG(AL_INFO, "SNI for control connection(%s)", sc_sni_customer_domain_ctl);

    sitec_state->ctrl_fohh_conn = fohh_client_create(FOHH_WORKER_ZPN_SCCTL,
                                                     broker_stats_name,
                                                     argo_serialize_binary,
                                                     fohh_connection_style_argo,
                                                     0,
                                                     sitec_state,
                                                     zpn_sitec_control_conn_cb,
                                                     NULL,
                                                     zpn_sitec_control_conn_unblock_cb,
                                                     NULL,
                                                     broker_name,
                                                     sc_sni_customer_domain_ctl,
                                                     cloud_name,
                                                     htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                     NULL,
                                                     1,
                                                     ZPN_SITEC_BROKER_RX_TIMEOUT_S);

    if (!sitec_state->ctrl_fohh_conn) {
        SITEC_LOG(AL_ERROR, "Could not initialize sitec control connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_sticky(sitec_state->ctrl_fohh_conn, 1);

    fohh_connection_monitor_sanity(sitec_state->ctrl_fohh_conn,
                                   zpn_sitec_fohh_connection_sanity_callback,
                                   SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->ctrl_fohh_conn, gs->cfg_key_cloud);

    fohh_suppress_connection_event_logs(sitec_state->ctrl_fohh_conn);

    fohh_history_enable(sitec_state->ctrl_fohh_conn);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_astats_conn_cb(struct fohh_connection*    connection,
                                    enum fohh_connection_state state,
                                    void *cookie)
{
    if (state == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "sitec astats connection successfully connected to Zscaler Cloud: %s",
                                    fohh_description(connection));
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_sitec_redirect_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for astats connection %s",
                    fohh_description(connection));
        }

        zpn_sitec_send_fohh_info_with_alt_cloud_capability(connection);

    } else {
        ZPN_LOG(AL_NOTICE, "sitec astats connection to Zscaler Cloud closed: %s %s",
                fohh_description(connection), fohh_close_reason(connection));
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_sitec_astats_conn_unblock_cb(struct fohh_connection *connection,
                                            enum fohh_queue_element_type element_type,
                                            void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbstats_conn_cb(struct fohh_connection*    connection,
                                    enum fohh_connection_state state,
                                    void *cookie)
{
    if (state == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "sitec pbstats connection successfully connected to Zscaler Cloud: %s",
                                    fohh_description(connection));
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_sitec_redirect_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for pbstats connection %s",
                    fohh_description(connection));
        }

        zpn_sitec_send_fohh_info_with_alt_cloud_capability(connection);

    } else {
        ZPN_LOG(AL_NOTICE, "sitec pbstats connection to Zscaler Cloud closed: %s %s",
                fohh_description(connection), fohh_close_reason(connection));
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_sitec_pbstats_conn_unblock_cb(struct fohh_connection *connection,
                                            enum fohh_queue_element_type element_type,
                                            void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

int zpn_sitec_astats_conn_init()
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    SITEC_LOG(AL_INFO, "SNI for astats connection(%s)", sc_sni_customer_domain_astats);

    sitec_state->astats_fohh_conn = fohh_client_create(FOHH_WORKER_ZPN_SCSTATS,
                                                       NULL,
                                                       argo_serialize_binary,
                                                       fohh_connection_style_argo,
                                                       0,
                                                       sitec_state,
                                                       zpn_sitec_astats_conn_cb,
                                                       NULL,
                                                       zpn_sitec_astats_conn_unblock_cb,
                                                       NULL,
                                                       broker_name,
                                                       sc_sni_customer_domain_astats,
                                                       cloud_name,
                                                       htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                       NULL,
                                                       1,
                                                       ZPN_SITEC_BROKER_RX_TIMEOUT_S);

    if (!sitec_state->astats_fohh_conn) {
        SITEC_LOG(AL_ERROR, "Could not initialize sitec astats connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_sticky(sitec_state->astats_fohh_conn, 1);

    fohh_connection_monitor_sanity(sitec_state->astats_fohh_conn,
                                   zpn_sitec_fohh_connection_sanity_callback,
                                   SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->astats_fohh_conn, gs->cfg_key_cloud);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_pbstats_conn_init()
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    SITEC_LOG(AL_INFO, "SNI for pbstats connection(%s)", sc_sni_customer_domain_pbstats);

    sitec_state->pbstats_fohh_conn = fohh_client_create(FOHH_WORKER_ZPN_SCSTATS,
                                                        NULL,
                                                        argo_serialize_binary,
                                                        fohh_connection_style_argo,
                                                        0,
                                                        sitec_state,
                                                        zpn_sitec_pbstats_conn_cb,
                                                        NULL,
                                                        zpn_sitec_pbstats_conn_unblock_cb,
                                                        NULL,
                                                        broker_name,
                                                        sc_sni_customer_domain_pbstats,
                                                        cloud_name,
                                                        htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                        NULL,
                                                        1,
                                                        ZPN_SITEC_BROKER_RX_TIMEOUT_S);

    if (!sitec_state->pbstats_fohh_conn) {
        SITEC_LOG(AL_ERROR, "Could not initialize sitec pbstats connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_sticky(sitec_state->pbstats_fohh_conn, 1);

    fohh_connection_monitor_sanity(sitec_state->pbstats_fohh_conn,
                                   zpn_sitec_fohh_connection_sanity_callback,
                                   SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->pbstats_fohh_conn, gs->cfg_key_cloud);

    return ZPN_RESULT_NO_ERROR;
}

static int sitec_stats_conn_history_dump(struct zpath_debug_state*   request_state,
                                         const char **               query_values,
                                         int                         query_value_count,
                                         void*                       cookie)
{
    char* history_str;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    history_str = SITEC_CALLOC(sizeof(char) * FOHH_HISTORY_STR_MIN_LEN);
    fohh_history_get_str(gs->sitec_state->stats_fohh_conn, history_str, FOHH_HISTORY_STR_MIN_LEN);
    ZDP("%s", history_str);
    SITEC_FREE(history_str);

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_stats_conn_unblock_cb(struct fohh_connection*      connection,
                                           enum fohh_queue_element_type element_type,
                                           void*                        cookie)
{
    ZPN_LOG(AL_NOTICE, "fohh unblock on log(stats_log) connection");
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_stats_conn_cb(struct fohh_connection*    connection,
                                   enum fohh_connection_state state,
                                   void*                      cookie)
{
    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "Log(stats_log) connection successfully connected to Zscaler Cloud: %s",
                                    fohh_description(connection));
        /* Register zpn_broker_redirect */
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_sitec_redirect_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for stats connection %s",
                    fohh_description(connection));
        }

        /* Need to send this message to trigger redirect evaluation logic on the broker side */
        zpn_sitec_send_fohh_info_with_alt_cloud_capability(connection);
    } else {
        ZPN_LOG(AL_NOTICE, "Log(stats_log) connection to Zscaler Cloud closed: %s %s",
                fohh_description(connection), fohh_close_reason(connection));
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_stats_conn_init()
{
    int         result;
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_state *sitec_state = gs->sitec_state;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    SITEC_LOG(AL_INFO, "SNI for stats connection(%s)", sc_sni_customer_domain_stats);

    sitec_state->stats_fohh_conn = fohh_client_create(FOHH_WORKER_ZPN_SCSTATS,
                                                     argo_log_get_name(zpath_stats_collection),
                                                     argo_serialize_binary,
                                                     fohh_connection_style_argo,
                                                     0,
                                                     sitec_state,
                                                     zpn_sitec_stats_conn_cb,
                                                     NULL,
                                                     zpn_sitec_stats_conn_unblock_cb,
                                                     NULL,
                                                     broker_name,
                                                     sc_sni_customer_domain_stats,
                                                     cloud_name,
                                                     htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                     NULL,
                                                     1,
                                                     ZPN_SITEC_BROKER_RX_TIMEOUT_S);

    if (!sitec_state->stats_fohh_conn) {
        SITEC_LOG(AL_ERROR, "Could not initialize sitec stats connection");
        return ZPN_RESULT_ERR;
    }

    fohh_set_sticky(sitec_state->stats_fohh_conn, 1);

    fohh_connection_monitor_sanity(sitec_state->stats_fohh_conn,
                                   zpn_sitec_fohh_connection_sanity_callback,
                                   SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(sitec_state->stats_fohh_conn, gs->cfg_key_cloud);

    fohh_suppress_connection_event_logs(sitec_state->stats_fohh_conn);

    fohh_history_enable(sitec_state->stats_fohh_conn);

    result = zpath_debug_add_read_command("Dump history of events happened to broker log(stats_log) connection.",
                                     "/sitec/stats/conn/history/dump",
                                     sitec_stats_conn_history_dump,
                                     NULL, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /sitec/stats/conn/history/dump");
        return result;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_broker_conn_tx_rx_stats_fill(struct fohh_connection *f_conn,
                                                   struct zpn_sitec_comprehensive_stats *out_data)
{
    uint64_t tx_b = 0;
    uint64_t rx_b = 0;
    uint64_t tx_o = 0;
    uint64_t rx_o = 0;
    uint64_t tx_raw_tlv = 0;
    uint64_t rx_raw_tlv = 0;

    if (! f_conn) return;

    fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    out_data->to_broker_bytes += tx_b;
    out_data->from_broker_bytes += rx_b;

    if (fohh_get_state(f_conn) == fohh_connection_connected) {
        out_data->active_conn_to_broker++;
    } else {
        out_data->backed_off_conn_to_broker++;
    }
}

void zpn_sitec_broker_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                    struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data) {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->cfg_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->ovd_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->rcfg_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->ctrl_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->stats_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->astats_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->pbstats_fohh_conn, out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(gs->sitec_state->userdb_wally_conn, out_data);

    /* event log collection */
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_event_collection), out_data);

    /* collections used by assistant */
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_ast_waf_collection), out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_ast_app_inspection_collection), out_data);

    /* collections used by pbroker */
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_transaction_collection), out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_auth_collection), out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_ast_auth_collection), out_data);
    zpn_sitec_broker_conn_tx_rx_stats_fill(fohh_get_log_handle(zpn_dns_collection), out_data);

    out_data->to_broker_bytes_delta = out_data->to_broker_bytes - last_comprehensive_stats_data->to_broker_bytes;
    out_data->from_broker_bytes_delta = out_data->from_broker_bytes - last_comprehensive_stats_data->from_broker_bytes;
    out_data->to_broker_bytes_rate = out_data->to_broker_bytes_delta ?
            ((out_data->to_broker_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
    out_data->from_broker_bytes_rate = out_data->from_broker_bytes_delta ?
            ((out_data->from_broker_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
}

/*
 * Collect sitec comprehensive stats and upload
 * We have to collect the stats from different places...
 */
static int zpn_sitec_comprehensive_stats_fill(void*     cookie,
                                              int       counter,
                                              void*     structure_data)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    static int64_t sys_disk_total_bytes;
    static int meminfo_read = 0;
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *)structure_data;
    struct mmdb_stats mmdb_s;
    struct zpn_balance_stats redir_stats;

    memset(&mmdb_s, 0, sizeof(struct mmdb_stats));
    get_mmdb_stats(&mmdb_s);

    memset(out_data, 0, sizeof(struct zpn_sitec_comprehensive_stats));
    out_data->g_sitec = gs->sitec_id;
    out_data->g_cst = gs->customer_id;
    out_data->g_microtenant = gs->sitec_scope_id == gs->customer_id ? 0 : gs->sitec_scope_id;
    out_data->cloud_time_us = zpn_sitec_cloud_adjusted_epoch_us();

    if (meminfo_read == 0) {
        zpath_system_get_disk_info(NULL, NULL, &sys_disk_total_bytes);
        meminfo_read = 1;
    }
    out_data->total_system_mem_mb = (int32_t)((gs->sys_stats.memtotal_abs_mem)/1024);
    out_data->total_disk_bytes = sys_disk_total_bytes;

    zpn_sitec_broker_conn_comprehensive_stats_fill(out_data, &last_comprehensive_stats_data);
    zpn_sitec_connector_conn_comprehensive_stats_fill(out_data, &last_comprehensive_stats_data);
    zpn_sitec_pbroker_conn_comprehensive_stats_fill(out_data, &last_comprehensive_stats_data);

    zpn_system_sitec_comprehensive_stats_fill(structure_data, cookie);

    /* mmdb stats */
    out_data->num_geo_queries = mmdb_s.num_geo_queries;
    out_data->num_geo_cc_queries_failed = mmdb_s.num_geo_cc_queries_failed;
    out_data->num_geo_city_queries_failed  = mmdb_s.num_geo_city_queries_failed;
    out_data->num_geo_subdivision_queries_failed  = mmdb_s.num_geo_subdivision_queries_failed;
    out_data->geo_query_rate = mmdb_s.geo_query_rate;
    out_data->num_isp_queries = mmdb_s.num_isp_queries;
    out_data->num_isp_queries_failed = mmdb_s.num_isp_queries_failed;
    out_data->isp_query_rate = mmdb_s.isp_query_rate;

    memcpy(&last_comprehensive_stats_data, out_data, sizeof(struct zpn_sitec_comprehensive_stats));

    zpn_sitesp_comprehensive_stats_fill(out_data);
    zpn_balance_redirect_get_stats(&redir_stats);

    out_data->redirect_success = redir_stats.success;
    out_data->redirect_failure = redir_stats.failure;
    out_data->gf_success = redir_stats.gf_success;
    out_data->gf_failure = redir_stats.gf_failure;
    out_data->redirects_to_alt_cloud = redir_stats.redirects_to_alt_cloud;
    out_data->no_brokers_in_alt_cloud = redir_stats.no_brokers_in_alt_cloud;
    out_data->no_target_instance = redir_stats.no_target_instance;

    zpn_sitec_siem_log_stats_fill(out_data);

    zpn_sitec_siem_sitec_comprehensive_stats(out_data, gs->customer_id, "sitec", gs->sitec_id, gs->sitec_group_id);

    zpn_sitec_firedrill_fill_comprehensive_stats(out_data);

    return ZPATH_RESULT_NO_ERROR;
}


static void zpn_sitec_stats_config_override_monitor(int64_t sitec_id, int64_t customer_gid)
{
    zpath_config_override_monitor_int(SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE,
                                      &sitec_stats_feature_enabled,
                                      NULL,
                                      SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT,
                                      (int64_t) sitec_id,
                                      (int64_t) customer_gid,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);

}

static void zpn_sitec_stats_tx_reader_cb_log(void* void_cookie,
                                             char* reason,
                                             int status)
{
    struct argo_log *log = void_cookie;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (FOHH_RESULT_NO_ERROR != status) {
        /*
         * Even this below message may be dropped while being sent to the stats broker if the FOHH is in the mood
         * of WOULD_BLOCK. But logging this, so that if any customer is willing to provide /var/log/messages, we
         * will get some insights.
         */
        ZPN_LOG(AL_ERROR, "stats upload to stats broker otyp(%s) name(%s) - dropped",
                      log->l_otyp, log->l_name);
        if (0 == strncmp("sitec_stats_comprehensive", log->l_name, sizeof("sitec_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&gs->sitec_state->num_comprehensive_stats_upload_fails, 1);
        }
    } else {
        SITEC_DEBUG_STATS_TX("stats upload to stats broker otyp(%s) name(%s), queued into fohh",
                             log->l_otyp, log->l_name);
        if (0 == strncmp("sitec_stats_comprehensive", log->l_name, sizeof("sitec_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&gs->sitec_state->num_comprehensive_stats_upload, 1);
        }
    }
}

int zpn_sitec_stats_is_connected()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    if (!gs->sitec_state->stats_fohh_conn) {
        return 0;
    }

    if (fohh_connection_connected == fohh_get_state(gs->sitec_state->stats_fohh_conn)) {
        return 1;
    }

    return 0;
}

static void zpn_sitec_stats_tx(struct argo_object* log_object,
                               int64_t argo_log_sequence,
                               zpn_sitec_stats_tx_done_cb done_cb,
                               void* done_cb_void_cookie,
                               char* done_cb_str_cookie)
{
    int fohh_ret;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    // check the stats connection state
    if(!zpn_sitec_stats_is_connected()) {
        // sitec doesn't have connection with its stats broker
        return;
    }
    // dump the object that we are transmitting if sitec runs with -debuglog option
    if (debuglog) {
        char dump[8000];
        if (argo_object_dump(log_object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Sitec Stats Tx: %s", dump);
        }
    }

    fohh_ret = fohh_argo_serialize_object(gs->sitec_state->stats_fohh_conn, log_object, argo_log_sequence,
                                          fohh_queue_element_type_control);
    if (done_cb) done_cb(done_cb_void_cookie, done_cb_str_cookie, fohh_ret);
    stats.stats++;
}


/*
 * This is a filter for all the stats messages that are going out of the sitec. We need this because the
 * libraries that we rely on could register for a stats upload which we don't intend to be useful for the sitec.
 * This function will make sure that sitec know exactly what it is sending out of the system - so explicitly check
 * for each and every message going out.
 */
int zpn_sitec_stats_tx_reader_cb(struct argo_object*    log_object,
                                 void*                  callback_cookie,
                                 int64_t                argo_log_sequence)
{
    struct argo_log *log = log_object->base_structure_void;
    int             upload_ok;
    size_t          cfg_iter;

    upload_ok = 0;

    if (0 == cfg_ready) {
        ZPN_LOG(AL_ERROR, "sitec stats cfg is not ready !");
        return ZPATH_RESULT_NO_ERROR;
    }
    for (cfg_iter = 0; cfg_iter < SITEC_STATS_TX_MAX_OBJECTS; cfg_iter++) {
        if (0 != strcmp(cfg[cfg_iter].l_name, log->l_name)) {
            continue;
        }

        if ((cfg[cfg_iter].l_otyp) && (0 != strcmp(cfg[cfg_iter].l_otyp, log->l_otyp))) {
            continue;
        }

        if (0 == cfg[cfg_iter].enabled) {
            continue;
        }

        if (0 == strcmp(log->l_name, "sitec_stats_comprehensive")) {
            // check the config flag and allow comprehensive_stats tx only when config flag is enabled.
            if (sitec_stats_feature_enabled) {
                upload_ok = 1;
            }
        } else {
            upload_ok = 1;
        }
        goto done;
    }

    if ((0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) ||
        (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats")))) {
        upload_ok = 1;
    }

done:
    if (upload_ok) {
        zpn_sitec_stats_tx(log_object, argo_log_sequence, zpn_sitec_stats_tx_reader_cb_log, log, "");
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_upgrade_stats_fill(void*     cookie,
                                        int       counter,
                                        void*     structure_data)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_sitec_upgrade_stats *out_data = (struct zpn_sitec_upgrade_stats *)structure_data;

    out_data->os_upgrade_fail = gs->upgrade_stats.os_upgrade_fail;
    out_data->sarge_upgrade_fail = gs->upgrade_stats.sarge_upgrade_fail;
    out_data->os_upgrade_success = gs->upgrade_stats.os_upgrade_success;
    out_data->sarge_upgrade_success = gs->upgrade_stats.sarge_upgrade_success;
    out_data->os_upgrade_timeout = gs->upgrade_stats.os_upgrade_timeout;
    out_data->sarge_os_cfg_read_fail = gs->upgrade_stats.sarge_os_cfg_read_fail;
    out_data->sudo_path_fail = gs->upgrade_stats.sudo_path_fail;
    out_data->package_manager_path_fail = gs->upgrade_stats.package_manager_path_fail;

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_stats_tx_dump(struct zpath_debug_state*   request_state,
                                   const char**                query_values,
                                   int                         query_value_count,
                                   void*                       cookie)
{
    ZDP("stats_tx: %"PRIu64"\n", stats.stats);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_sitec_stats_tx_init()
{
    static int available_cpus;
    int res = ZPATH_RESULT_NO_ERROR;
    struct argo_log_reader *stats_upload_reader;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if ((available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        ZPN_LOG(AL_NOTICE, "Failed to get available cpus");
    }

    zpn_sitec_stats_config_override_monitor(gs->sitec_id, gs->customer_id);

    // Do not forget to increment SITEC_STATS_TX_MAX_OBJECTS if you add a new entry for cfg
    cfg[0] = (struct zpn_sitec_stats_tx_cfg){"sitec_stats_comprehensive", NULL, 1, SITEC_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC, zpn_sitec_comprehensive_stats_description, sizeof(struct zpn_sitec_comprehensive_stats), zpn_sitec_comprehensive_stats_fill, &available_cpus, 1};
    cfg[1] = (struct zpn_sitec_stats_tx_cfg){"sitec_stats_system_memory", NULL, 1, SITEC_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC, zpath_system_memory_stats_description, sizeof(struct zpn_system_memory_stats), zpn_system_memory_stats_fill, NULL, 1};
    cfg[2] = (struct zpn_sitec_stats_tx_cfg){"sitec_stats_fproxy", NULL, 1, SITEC_STATS_TX_FPROXY_USEC, zpn_sitec_fproxy_stats_description, sizeof(struct zpn_sitec_fproxy_stats), zpn_sitec_fproxy_stats_fill, NULL, 1};
    cfg[3] = (struct zpn_sitec_stats_tx_cfg){"sitec_stats_upgrade", NULL, 1, SITEC_STATS_TX_UPGRADE_USEC, zpn_sitec_upgrade_stats_description, sizeof(struct zpn_sitec_upgrade_stats), zpn_sitec_upgrade_stats_fill, NULL, 1};
    cfg_ready = 1;

    {
        size_t cfg_iter;
        void* obj;

        for (cfg_iter = 0; cfg_iter < SITEC_STATS_TX_MAX_OBJECTS; cfg_iter++) {
            if (0 == cfg[cfg_iter].enabled) {
                continue;
            }
            obj = SITEC_CALLOC(cfg[cfg_iter].size_of_object);
            if (!argo_log_register_structure(argo_log_get("statistics_log"), cfg[cfg_iter].l_name, AL_INFO,
                                             cfg[cfg_iter].interval_us, cfg[cfg_iter].argo_description, obj,
                                             cfg[cfg_iter].log_immediate, cfg[cfg_iter].pre_log_cb,
                                             cfg[cfg_iter].pre_log_cb_cookie)) {
                ZPN_LOG(AL_ERROR, "Could not register %s stats", cfg[cfg_iter].l_name);
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        }
    }

    stats_upload_reader = argo_log_read(zpath_stats_collection, "stats_upload", 0, 1,
                                        zpn_sitec_stats_tx_reader_cb, NULL, 0, NULL,
                                        60 * 1000 * 1000);
    if (!stats_upload_reader) {
        ZPN_LOG(AL_ERROR, "Could not create stats log upload reader");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (zpath_debug_add_read_command("Dump info related to stats TX operation.",
                               "/sitec/stats/tx/stats/dump", zpn_sitec_stats_tx_dump, NULL,
                               NULL)) {
        ZPN_LOG(AL_ERROR, "couldn't add /sitec/stats/tx/stats/dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }
done:
    return res;
}

void zpn_sitec_delete_broker_connections(const char* reason)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *log_conn;

    ZPN_LOG(AL_ALERT, "firedrill zpn_sitec_delete_broker_connections");

    if(!gs) {
        ZPN_LOG(AL_ALERT, "sitec global state is NULL");
        return;
    }

    if (gs->sitec_state->cfg_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->cfg_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->cfg_fohh_conn), reason);
    }


    if (gs->sitec_state->ovd_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->ovd_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->ovd_fohh_conn), reason);
    }

    if (gs->sitec_state->rcfg_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->rcfg_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->rcfg_fohh_conn), reason);
    }

    if (gs->sitec_state->ctrl_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->ctrl_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn), reason);
    }

    if (gs->sitec_state->stats_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->stats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->stats_fohh_conn), reason);
    }

    if (gs->sitec_state->astats_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->astats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->astats_fohh_conn), reason);
    }

    if (gs->sitec_state->pbstats_fohh_conn) {
        fohh_connection_disable_async(gs->sitec_state->pbstats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->pbstats_fohh_conn), reason);
    }

    if (gs->sitec_state->userdb_wally_conn) {
        fohh_connection_disable_async(gs->sitec_state->userdb_wally_conn,
                                      fohh_connection_incarnation(gs->sitec_state->userdb_wally_conn), reason);
    }

    if (gs->sitec_state->event_log_channel) {
        fohh_connection_disable_async(gs->sitec_state->event_log_channel,
                                      fohh_connection_incarnation(gs->sitec_state->event_log_channel), reason);
    }

    log_conn = fohh_get_log_handle(zpn_sc_asst_auth_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_sc_pb_auth_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_auth_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_waf_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_app_inspection_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), FOHH_CLOSE_REASON_SITEC_DELETED);
    }

    log_conn = fohh_get_log_handle(zpn_ast_ptag_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_app_inspection_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_smb_inspection_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_krb_inspection_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_waf_api_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_asst_event_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_transaction_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_pb_client_auth_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_auth_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_dns_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_pb_event_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }

    log_conn = fohh_get_log_handle(zpn_ast_ldap_inspection_collection);
    if (log_conn) {
        fohh_connection_disable_async(log_conn,
                                      fohh_connection_incarnation(log_conn), reason);
    }
}


void zpn_sitec_switch_to_broker_connections()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *log_conn;

    if (gs->sitec_state->cfg_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->cfg_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->cfg_fohh_conn));
    }


    if (gs->sitec_state->ovd_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->ovd_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->ovd_fohh_conn));
    }

    if (gs->sitec_state->rcfg_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->rcfg_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->rcfg_fohh_conn));
    }

    if (gs->sitec_state->ctrl_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->ctrl_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn));
    }

    if (gs->sitec_state->stats_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->stats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->stats_fohh_conn));
    }

    if (gs->sitec_state->astats_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->astats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->astats_fohh_conn));
    }

    if (gs->sitec_state->pbstats_fohh_conn) {
        fohh_connection_enable_async(gs->sitec_state->pbstats_fohh_conn,
                                      fohh_connection_incarnation(gs->sitec_state->pbstats_fohh_conn));
    }

    if (gs->sitec_state->userdb_wally_conn) {
        fohh_connection_enable_async(gs->sitec_state->userdb_wally_conn,
                                      fohh_connection_incarnation(gs->sitec_state->userdb_wally_conn));
    }

    if (gs->sitec_state->event_log_channel) {
        fohh_connection_enable_async(gs->sitec_state->event_log_channel,
                                      fohh_connection_incarnation(gs->sitec_state->event_log_channel));
    }

    log_conn = fohh_get_log_handle(zpn_sc_asst_auth_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_sc_pb_auth_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_auth_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_waf_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_app_inspection_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_smb_inspection_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_krb_inspection_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_ptag_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_waf_api_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_asst_event_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_transaction_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_pb_client_auth_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_auth_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_dns_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_pb_event_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }

    log_conn = fohh_get_log_handle(zpn_ast_ldap_inspection_collection);
    if (log_conn) {
        fohh_connection_enable_async(log_conn,
                                      fohh_connection_incarnation(log_conn));
    }
}

void zpn_sitec_send_mission_critical_req(void *cookie1, void *cookie2)
{
    struct zpn_broker_mission_critical mc_data = {0};
    struct fohh_connection *f_conn = cookie1;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    mc_data.site_gid = gs->site_gid;
    if(zpn_broker_mission_critical_req(f_conn, 0, &mc_data)) {
        ZPN_LOG(AL_ERROR, "failed to send mission critical request");
        return;
    }
    ZPN_LOG(AL_ERROR, "firedrill sitec sent mission critical message request successfully");
}


int zpn_sitec_mission_critical_request_cb(void* argo_cookie_ptr,
                                            void* argo_structure_cookie_ptr,
                                            struct argo_object* object)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_broker_mission_critical_resp *mc_resp = object->base_structure_void;

    ZPN_LOG(AL_NOTICE,"zpn_sitec_mission_critical_request_cb firedrill entered");

    /* disconnect the scmc connection */
    if (gs->sitec_state->scmc) {
        fohh_connection_delete(gs->sitec_state->scmc, FOHH_CLOSE_REASON_MISSION_ACCOMPLISHED);
    }

    /* check the firedrill status */
    if(mc_resp->firedrill_status) {

        ZPN_LOG(AL_NOTICE,"firedrill enabled, trigger firedrill again...");

        /*status change will be done in start call */
        if(zpn_sitec_firedrill_start(mc_resp->firedrill_interval)) {
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_transit_count, 1);

    } else {
        ZPN_LOG(AL_NOTICE,"zpn_sitec_mission_critical_request_cb firedrill disabled, connecting back to cloud g_firedrill_disable_invoked_on_cmdline = %d",g_firedrill_disable_invoked_on_cmdline);

        zpn_sitec_switch_to_broker_connections();

        gs->firedrill_status = ZPN_SITEC_FIREDRILL_DISABLED;

        /* check if the firedrill disabled is from cmd line */
        if(g_firedrill_disable_invoked_on_cmdline) {
            /* send rpc to pse and appc */
            zpn_sitec_private_broker_send_firedrill_exit_rpc();
            zpn_sitec_assistant_send_firedrill_exit_rpc();
            __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_cmdline_disable_count, 1);
            g_firedrill_disable_invoked_on_cmdline = 0;
        }
        /* increment disconnect count */
        __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_completed_count, 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_mission_critical_conn_unblock_cb(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_broker_mission_critical_conn_cb(struct fohh_connection*   connection,
                                    enum fohh_connection_state state,
                                    void *cookie)
{
    if (state == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "firedrill: sitec scmc connection successfully connected to Zscaler Cloud: %s",
                                    fohh_description(connection));
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_mission_critical_description_resp,
                                                zpn_sitec_mission_critical_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "firedrill: could not register broker_redirect for scmc connection %s",
                    fohh_description(connection));
        }

       zevent_defer(zpn_sitec_send_mission_critical_req, connection, NULL, 0);

    } else {
        ZPN_LOG(AL_NOTICE, "firedrill: sitec scmc connection to Zscaler Cloud closed: %s %s",
                fohh_description(connection), fohh_close_reason(connection));
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    snprintf(sc_sni_customer_domain_scmc, sizeof(sc_sni_customer_domain_scmc), "%"PRId64".scmc.%s", gs->sitec_id, cloud_name);

    SITEC_LOG(AL_INFO, "SNI for scmc connection(%s)", sc_sni_customer_domain_scmc);

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    /* connect to broker on scmc channel */
     gs->sitec_state->scmc = fohh_client_create(FOHH_WORKER_ZPN_MC,
                                                NULL,
                                                argo_serialize_binary,
                                                fohh_connection_style_argo,
                                                0,
                                                NULL,
                                                zpn_sitec_broker_mission_critical_conn_cb,
                                                NULL,
                                                zpn_sitec_mission_critical_conn_unblock_cb,
                                                NULL,
                                                broker_name,
                                                sc_sni_customer_domain_scmc,
                                                cloud_name,
                                                htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                                                NULL,
                                                1,
                                                ZPN_SITEC_BROKER_RX_TIMEOUT_S);
    if (!gs->sitec_state->scmc) {
        SITEC_LOG(AL_ERROR, "firedrill: could not initialize sitec scmc connection");
        return;
    }

    SITEC_LOG(AL_ERROR, "firedrill: sitec scmc connection successful");
    return;
}
