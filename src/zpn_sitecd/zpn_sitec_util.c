/*
 * zpn_sitec_util.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <string.h>
#include <dirent.h>
#include "zpn_sitecd/zpn_sitec.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn_sitecd/zpn_sitec_util.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zpn/zpn_file_fetch.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_misc/zpath_version.h"
#include "zpn/zpn_rpc.h"

#define SITEC_STATE_DAY_STR_LEN 21
#define SITEC_STATE_HOUR_MIN_SEC_STR_LEN 5

extern int key_retry_flag[FILE_CODE_MAX];
extern int file_clean_downloaded[FILE_CODE_MAX];
extern int new_version_downloaded[FILE_CODE_MAX];
extern char zpath_geoip_filename[FILE_PATH_LEN];
extern char zpath_ispip_filename[FILE_PATH_LEN];

extern struct zpn_firedrill_stats g_sitec_firedrill_stats_obj;
extern struct event *ev_firedrill_timer;

int64_t zpn_sitec_cloud_adjusted_epoch_us(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (epoch_us() + gs->cloud_time_delta_us);
}

int64_t zpn_sitec_cloud_adjusted_epoch_s(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return (epoch_s() + gs->cloud_time_delta_us / 1000000L);
}

void zpn_sitec_set_swap_config(uint64_t bytes)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    gs->swap_config = (bytes != 0)? 1: 0;
}

int zpn_sitec_get_swap_config()
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->swap_config;
}

int zpn_sitec_state_get_configured_cpus() {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->configured_cpus;
}

int zpn_sitec_state_get_available_cpus() {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->available_cpus;
}

char *sitec_get_name_by_id(const int64_t sitec_id) {
    int res = 0;
    char *sitec_name = NULL;
    struct zpn_site_controller *sitec = NULL;

    res = zpn_sitec_get_by_id(sitec_id, &sitec, NULL, NULL, 0);
    if (ZPN_RESULT_NO_ERROR != res) {
        ZPN_LOG(AL_ERROR, "Could not get sitec with gid = %"PRId64": %s", sitec_id, zpn_result_string(res));
        goto end;
    }

    sitec_name = (sitec && sitec->name) ? sitec->name : NULL;

end:
    return sitec_name;
}


char* sitec_state_get_uptime_str(char *buf, int buf_len)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    int64_t day;
    int64_t hour;
    int64_t min;
    int64_t sec;
    char    day_str[SITEC_STATE_DAY_STR_LEN];
    char    hour_str[SITEC_STATE_HOUR_MIN_SEC_STR_LEN];
    char    min_str[SITEC_STATE_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[SITEC_STATE_HOUR_MIN_SEC_STR_LEN];

    struct timespec ts;

    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        SITEC_LOG(AL_NOTICE, "clock_gettime() failed with errno %d: %s", errno, strerror(errno));
        return "-1";
    }

    sec = ts.tv_sec - gs->sc_start_time_m_s;

    day = sec / (24 * 60 * 60);
    sec %= (24 * 60 * 60);

    hour = sec / (60 * 60);
    sec %= (60 * 60);

    min = sec / 60;
    sec %= 60;

    snprintf_nowarn(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf_nowarn(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf_nowarn(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf_nowarn(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf(buf, buf_len, "%s%s%s%s",  (day==0)? "": day_str,
                                            (hour==0 && day==0)? "": hour_str,
                                            (day==0 && hour==0 && min==0)? "": min_str,
                                            (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    return buf;
}

int zpn_sitec_get_max_fohh_threads()
{
    int num_cores;
    num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (-1 == num_cores) {
        fprintf(stderr, "Failed to get number of available cpus - %s\n", strerror(errno));
        num_cores = SITEC_DEFAULT_THREAD_CNT;
    } else if (num_cores > FOHH_MAX_THREADS) {
        num_cores = FOHH_MAX_THREADS;
    } else if (num_cores < SITEC_DEFAULT_THREAD_CNT) {
        num_cores = SITEC_DEFAULT_THREAD_CNT;
    }

    return num_cores;

}

const char *zpn_sitec_get_sarge_version(void) {
    FILE *fp = fopen(ZPATH_SARGE_FILENAME_UPDATER, "r");
    if (!fp)
        return NULL;

    char *str = SITEC_CALLOC(ZPATH_VERSION_STR_MAX_LEN + 1);
    if (!str) {
        fclose(fp);
        return NULL;
    }

    if (!fread(str, 1, ZPATH_VERSION_STR_MAX_LEN, fp)) {
        fclose(fp);
        SITEC_FREE(str);
        return NULL;
    };

    fclose(fp);
    return str;
}

void get_fohh_connection_stats(struct fohh_connection *fohh_conn, uint64_t *tx_b, uint64_t *rx_b,
                                uint64_t *tx_o, uint64_t *rx_o, uint64_t *tx_raw_tlv,
                                uint64_t *rx_raw_tlv) {
    *tx_b = 0;
    *rx_b = 0;
    *tx_o = 0;
    *rx_o = 0;
    *tx_raw_tlv = 0;
    *rx_raw_tlv = 0;

    if (fohh_conn == NULL) return;

    if (fohh_get_state(fohh_conn) == fohh_connection_connected) {
        fohh_connection_get_stats(fohh_conn, tx_b, rx_b, tx_o, rx_o, tx_raw_tlv, rx_raw_tlv);
    }
}

int zpn_sitec_write_to_file_and_update()
{
    int val;
    FILE * fp = fopen(ROLLBACK_FILE, "r");

    if (!fp) {
        //rollback file does not exist
        fp = fopen(ROLLBACK_FILE, "w");
        if (!fp) {
            ZPATH_LOG(AL_ERROR, "Could create file:%s. err:%s", ROLLBACK_FILE, strerror(errno));
            return ZPATH_RESULT_ERR;
        }
        fprintf(fp, "%d", 1);
        fclose(fp);
        ZPATH_LOG(AL_NOTICE, "Write to file successful. File:%s", ROLLBACK_FILE);
        return ZPATH_RESULT_NO_ERROR;
    }

    //If file is present, increment the counter
    if(fscanf(fp, "%d", &val))
        val++;
    fclose(fp);
    fp = NULL;

    //reopen for write
    fp = fopen(ROLLBACK_FILE, "w");
    if(!fp) {
        ZPATH_LOG(AL_ERROR, "Could not open file for write. File:%s. err:%s", ROLLBACK_FILE, strerror(errno));
        return ZPATH_RESULT_ERR;
    }

    fprintf(fp, "%d", val);
    fclose(fp);

    ZPATH_LOG(AL_NOTICE, "Write to file successful. File:%s. val:%d", ROLLBACK_FILE, val);
    return ZPATH_RESULT_NO_ERROR;
}

static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "FILE_FETCH:%s", log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

int zpn_sitec_geoip_replace_and_trigger_reload(char *filename,
                                               struct zpn_file_fetch_key *key,
                                               struct zcrypt_key local_key)
{
    int res = 0,ret = 0;
    char *geoip_data = NULL, *geoip_fallback_data = NULL;
    size_t geoip_size = 0,geoip_fallback_size = 0;
    char metafile_name[MAX_FF_STR_LEN+5] = {0}; //5 for _meta
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int develop_mode = 1,geo_sanity = 1;
    char file_version[FILE_PATH_LEN] = {0};
    char old_version[MAX_VERSION_LEN] = {0};
    int key2_used = 0;

    if (read_version(filename, old_version)) {
        goto err;
    }

    if (key->version) {
        write_version(filename, key->version);
    } else if (!zpath_is_geoip_running()) {
        write_version(filename, "default");
    }

   if (gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled) {
       develop_mode = 0;
    }

    snprintf(metafile_name, MAX_FF_STR_LEN+5, "%s_%s", filename, "meta");
    if (key->enc_or_dec_pvt_key && (key->enc_or_dec_pvt_key[0]!='\0')) {
        geoip_data = decrypt_file(key->enc_or_dec_pvt_key, filename, &geoip_size, 1);
    }
KEY2_DECRYPT:
    if (!geoip_data && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0]!='\0')) {
        key2_used = 1;
        ZPN_LOG(AL_NOTICE,"Trying decryption with backup key for: %s", filename);
        geoip_data = decrypt_file(key->enc_or_dec_pvt_key2, filename, &geoip_size, 1);
    }

    if (!geoip_data) {
        ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s. Cannot load the new geoip database", filename);
        key_retry_flag[MMDB_GEOIP] = 1; //retry getting the key
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: unable to decrypt default file.. Rolling back sitec to previous version!");
            if(!sub_module_upgrade_failed){
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
            key_retry_flag[MMDB_GEOIP] = 0;
        }
        ret = ZPATH_RESULT_NO_ERROR;
        goto err;
    }

    key_retry_flag[MMDB_GEOIP] = 0;//We have the key now
    ZPN_LOG(AL_INFO,"Successfuly decrypted file:%s.",filename);

    if (zcrypt_metadata_verify_version_in_memory(geoip_data, geoip_size, metafile_name, key->version, NULL, 0, log_verify_version_f, develop_mode, 0) != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, " FILE_FETCH: could not verify metadata info for the file");
        if (!key2_used && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0]!='\0')) {
            ZPN_LOG(AL_INFO,"Retrying decryption");
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
            goto KEY2_DECRYPT;
        }
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: meta file validation failed for default file.. Rolling back sitec to previous version!");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        } else {
            //some issue with file, we should retry downloading the file in some time
            key_retry_flag[MMDB_GEOIP] = 0; //no need to set this, key will automatically be retried with file
            write_version(filename, old_version);
            file_clean_downloaded[MMDB_GEOIP] = 0;
        }
        goto err;
    }

    ZPATH_LOG(AL_NOTICE," FILE_FETCH: successfully verified metadata for file:%s", filename);
    new_version_downloaded[MMDB_GEOIP] = 1;

    if(file_exists(FILENAME_GEOIP) && file_exists(FILENAME_GEOIP_META) && zpath_is_geoip_running()  && !zpath_is_geoip_fallback_running()) {
        //delete the old fallback file
        unlink(FILENAME_GEOIP_FALLBACK);

        //current running geoip file becomes the fallback file
        res = rename(FILENAME_GEOIP, FILENAME_GEOIP_FALLBACK);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_GEOIP, FILENAME_GEOIP_FALLBACK);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        res = rename(FILENAME_GEOIP_META, FILENAME_GEOIP_FALLBACK_META);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_GEOIP_META, FILENAME_GEOIP_FALLBACK_META);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    }

    //Get the key and decrypt fallback file if it exists - to be used in the reload/init function
    if (file_exists(FILENAME_GEOIP_FALLBACK)) {
        geoip_fallback_data = decrypt_file((char*)local_key.data, FILENAME_GEOIP_FALLBACK, &geoip_fallback_size, 0);
        if(!geoip_fallback_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_GEOIP_FALLBACK);
        }
    }

    //create the new db file by encrypting it with local key
    if (encrypt_to_file((char*)local_key.data, geoip_data, geoip_size, FILENAME_GEOIP, 0)) {
        ZPN_LOG(AL_ERROR,"Unable to encrypt file and create:%s",FILENAME_GEOIP);
        ret = ZPN_RESULT_ERR;
    }
    res = rename(metafile_name, FILENAME_GEOIP_META);
    if (res) {
        ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", metafile_name, FILENAME_GEOIP_META);
        unlink(metafile_name);
    }

    unlink(filename);

    if (!zpath_is_geoip_running()) {
        snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP);
        geoip_db_file = zpath_geoip_filename;
        res = zpath_geoip_init_from_memory(geoip_db_file, geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_geoip_init failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            if(key->version && !strcmp(key->version,"default")){
                ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default geoip file.. Rolling back sitec to previous version!");
                if(!sub_module_upgrade_failed){
                    zpn_sitec_write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
            }
            goto err;
        }
    } else {
        res = zpath_trigger_geoip_reload(NULL,geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_trigger_geoip_reload failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        if (!zpath_is_geoip_running()) {
            ZPN_LOG(AL_ERROR, "No geoip file loaded, retrying download of default file");
            geoip_db_file = NULL;
            goto err;
        }
    }

    //sanity check
    res = zpath_geoip_sanity_verify();
    if (res) {
        ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
        geoip_db_file = NULL;
        geo_sanity = 0;
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default geoip file.. Rolling back service-edge-child to previous version!");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    //check if new file failed sanity check, if so, move to the fallback file
    if (!geoip_db_file && geoip_fallback_data && !zpath_is_geoip_fallback_running()) {
        snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
        geoip_db_file = zpath_geoip_filename;
        ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
        res = zpath_trigger_geoip_reload(NULL, geoip_fallback_data, geoip_fallback_size, NULL, 0);
        if (!res) {
            res = zpath_geoip_sanity_verify();
            if (!res) {
                geo_sanity = 1;
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database from fallback file..\n");
            } else {
                geo_sanity = 0;
                ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
                geoip_db_file = NULL;
                goto err;
            }
        } else {
            ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed from fallback file: %s",
                zpath_result_string(res));
            geo_sanity = 0;
            geoip_db_file = NULL;
            goto err;
        }
    } else if(!geoip_db_file) {
        res = ZPATH_RESULT_ERR;
        goto err;
    }

    zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
    write_version(filename, file_version);
    ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database..\n");

    if(geo_sanity && zpath_is_geoip_fallback_running()) {
        zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(filename, file_version);
        if(geoip_data){
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
        }
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
    } else if (geoip_fallback_data) {
        ZPN_FF_FREE(geoip_fallback_data);
        geoip_fallback_data = NULL;
    }

    if (geo_sanity && !zpath_is_geoip_fallback_running())
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
    //update the auth log
    zpn_send_zpn_tcp_info_report(gs->sitec_state->ctrl_fohh_conn,
                                 fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                 gs->sitec_state->ctrl_fohh_conn,
                                 zvm_vm_type_to_str_concise(zvm_type_get()),
                                 gs->sitec_runtime_os);

    return ret;
err:
    if (!key_retry_flag[MMDB_GEOIP] && file_exists(FILENAME_GEOIP_ENC))
        unlink(FILENAME_GEOIP_ENC);
    if (!key_retry_flag[MMDB_GEOIP] && file_exists(metafile_name))
        unlink(metafile_name);
    if (geoip_data) {
        ZPN_FF_FREE(geoip_data);
        geoip_data = NULL;
    }
    if (geoip_fallback_data) {
        ZPN_FF_FREE(geoip_fallback_data);
        geoip_fallback_data = NULL;
    }
    return ret;
}

int zpn_sitec_ispip_replace_and_trigger_reload(char *filename,
                                               struct zpn_file_fetch_key *key,
                                               struct zcrypt_key local_key)
{
    int res = 0,ret = 0;
    char *ispip_data = NULL, *ispip_fallback_data = NULL;
    size_t ispip_size = 0,ispip_fallback_size = 0;
    char metafile_name[MAX_FF_STR_LEN+5] = {0}; //5 for _meta
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int develop_mode = 1;
    char file_version[FILE_PATH_LEN] = {0};
    int isp_sanity = 1;
    char old_version[MAX_VERSION_LEN] = {0};
    int key2_used = 0;

    if (read_version(filename, old_version)) {
        goto err;
    }

    if (key->version) {
        write_version(filename, key->version);
    } else if(!zpath_is_ispip_running()) {
        write_version(filename, "default");
    }

    if (gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled) {
       develop_mode = 0;
    }

    snprintf(metafile_name, MAX_FF_STR_LEN+5, "%s_%s", filename,"meta");

    if (key->enc_or_dec_pvt_key && (key->enc_or_dec_pvt_key[0] != '\0')) {
        ispip_data = decrypt_file(key->enc_or_dec_pvt_key, filename, &ispip_size, 1);
    }

KEY2_ISP_DECRYPT:
    if (!ispip_data && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0] != '\0')) {
        key2_used = 1;
        ZPN_LOG(AL_NOTICE,"Trying decryption with backup key for: %s",filename);
        ispip_data = decrypt_file(key->enc_or_dec_pvt_key2, filename, &ispip_size, 1);
    }

    if (!ispip_data) {
        ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s. Cannot load the new ispip database",filename);
        key_retry_flag[MMDB_ISP] = 1; //retry getting the key
        ret = ZPATH_RESULT_NO_ERROR;
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: unable to decrypt default file.. Rolling back sitec to previous version!");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
            key_retry_flag[MMDB_ISP] = 0;
        }
        goto err;
    }

    key_retry_flag[MMDB_ISP] = 0;//We have the key now
    ZPN_LOG(AL_INFO,"Successfuly decrypted file:%s.",filename);

    if (zcrypt_metadata_verify_version_in_memory(ispip_data, ispip_size, metafile_name, key->version, NULL, 0, log_verify_version_f, develop_mode, 0) != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, " FILE_FETCH: could not verify metadata info for the file");
        if (!key2_used && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0] != '\0')) {
            ZPN_LOG(AL_INFO,"Retrying decryption");
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
            goto KEY2_ISP_DECRYPT;
        }
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: meta file validation failed for default isp file.. Rolling back sitec to previous version!");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        } else {
            //some issue with file, we should retry downloading the file in some time
            key_retry_flag[MMDB_ISP] = 0;
            write_version(filename, old_version);
            file_clean_downloaded[MMDB_ISP] = 0;
        }
        goto err;
    }

    ZPATH_LOG(AL_NOTICE," FILE_FETCH: successfully verified metadata for file:%s",filename);
    new_version_downloaded[MMDB_ISP] = 1;

    if (file_exists(FILENAME_ISP) && zpath_is_ispip_running() && !zpath_is_ispip_fallback_running()) {
        //delete the old fallback file
        unlink(FILENAME_ISP_FALLBACK);

        //current running ispip file becomes the fallback file
        res = rename(FILENAME_ISP, FILENAME_ISP_FALLBACK);
        if (res) {
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_ISP, FILENAME_ISP_FALLBACK);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        res = rename(FILENAME_ISP_META, FILENAME_ISP_FALLBACK_META);
        if (res) {
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_ISP_META, FILENAME_ISP_FALLBACK_META);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    }

    //Get the key and decrypt fallback file if it exists - to be used in the reload/init function
    if (file_exists(FILENAME_ISP_FALLBACK)) {
        ispip_fallback_data = decrypt_file((char*)local_key.data, FILENAME_ISP_FALLBACK, &ispip_fallback_size, 0);
        if (!ispip_fallback_data) {
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP_FALLBACK);
        }
    }

    //create the new db file by encrypting it with local key
    if (encrypt_to_file((char*)local_key.data, ispip_data, ispip_size, FILENAME_ISP, 0)) {
        ZPN_LOG(AL_ERROR,"Unable to encrypt file and create:%s",FILENAME_ISP);
        ret = ZPN_RESULT_ERR;
    }
    res = rename(metafile_name, FILENAME_ISP_META);
    if (res) {
        ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", metafile_name, FILENAME_ISP_META);
        unlink(metafile_name);
    }

    unlink(filename);

    if (!zpath_is_ispip_running()) {
        snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP);
        geoip_isp_db_file = zpath_ispip_filename;
        res = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_ispip_init failed: %s",
                    zpath_result_string(res));
            if (key->version && !strcmp(key->version,"default")) {
                ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default isp file.. Rolling back sitec to previous version!");
                if(!sub_module_upgrade_failed){
                    zpn_sitec_write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
            }
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    } else {
        res = zpath_trigger_ispip_reload(NULL,ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_trigger_ispip_reload failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            unlink(FILENAME_ISP_META);
            goto err;
        }
        if (!zpath_is_ispip_running()) {
            ZPN_LOG(AL_ERROR, "No geo ISP file loaded, retrying download of default file");
            geoip_isp_db_file = NULL;
            goto err;
        }
    }

    //sanity check
    res = zpath_ispip_sanity_verify();
    if (res) {
        ZPN_LOG(AL_ERROR,"ISP db failed sanity check\n");
        geoip_isp_db_file = NULL;
        isp_sanity = 0;
        if (key->version && !strcmp(key->version,"default")) {
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default isp file.. Rolling back sitec to previous version!");
            if(!sub_module_upgrade_failed){
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    //check if new file failed sanity check, if so, move to the fallback file
    if (!geoip_isp_db_file && ispip_fallback_data && !zpath_is_ispip_fallback_running()) {
        snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
        geoip_isp_db_file = zpath_ispip_filename;
        ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
        res = zpath_trigger_ispip_reload(NULL,ispip_fallback_data, ispip_fallback_size, NULL, 0);
        if (!res) {
            res = zpath_ispip_sanity_verify();
            if (!res) {
                isp_sanity = 1;
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database from fallback file..\n");
            } else {
                isp_sanity = 0;
                ZPN_LOG(AL_ERROR,"ISP db failed sanity check\n");
                geoip_isp_db_file = NULL;
                goto err;
            }
        } else {
            isp_sanity = 0;
            ZPN_LOG(AL_ERROR, "ISP databse init failed from fallback file: %s",
                zpath_result_string(res));
            geoip_isp_db_file = NULL;
            goto err;
        }
    } else if(!geoip_isp_db_file) {
        res = ZPATH_RESULT_ERR;
        goto err;
    }

    ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database..\n");
    zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
    write_version(filename, file_version);

    if (isp_sanity && zpath_is_ispip_fallback_running()) {
        zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(filename, file_version);
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP version:%s", file_version);
        if (ispip_data) {
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
        }
    } else if(ispip_fallback_data) {
        ZPN_FF_FREE(ispip_fallback_data);
        ispip_fallback_data = NULL;
    }

    if (isp_sanity && !zpath_is_ispip_fallback_running())
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP version:%s", file_version);
    //update the auth log
    zpn_send_zpn_tcp_info_report(gs->sitec_state->ctrl_fohh_conn,
                                 fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                 gs->sitec_state->ctrl_fohh_conn,
                                 zvm_vm_type_to_str_concise(zvm_type_get()),
                                 gs->sitec_runtime_os);

    return ret;
err:
    if (!key_retry_flag[MMDB_ISP] && file_exists(FILENAME_ISP_ENC))
        unlink(FILENAME_ISP_ENC);
    if (!key_retry_flag[MMDB_ISP] && file_exists(metafile_name))
        unlink(metafile_name);
    if (ispip_data) {
        ZPN_FF_FREE(ispip_data);
        ispip_data = NULL;
    }
    if (ispip_fallback_data) {
        ZPN_FF_FREE(ispip_fallback_data);
        ispip_fallback_data = NULL;
    }
    return ret;
}

int zpn_sitec_init_cert_validity_counters(const char *cert_file_name)
{
    struct zcrypt_cert *cert;
    int64_t one_percent_time;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    cert = zcrypt_cert_read(cert_file_name);
    if (!cert) {
        return ZPATH_RESULT_ERR;
    }

    gs->cert_validity_start_time_cloud_s = zcrypt_cert_valid_from_epoch(cert);
    gs->cert_validity_end_time_cloud_s = zcrypt_cert_valid_to_epoch(cert);
    zcrypt_cert_free(cert);

    one_percent_time = (gs->cert_validity_end_time_cloud_s - gs->cert_validity_start_time_cloud_s) / 100;
    gs->cert_force_re_enroll_time_cloud_s = gs->cert_validity_end_time_cloud_s -
        (one_percent_time * (100 - ZPN_SITEC_FORCE_RE_ENROLL_TIME_FACTOR));

    return ZPATH_RESULT_NO_ERROR;
}

/* Get cloud time delta */
int64_t zpn_sitec_get_cloud_time_delta_us(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->cloud_time_delta_us;
}

void zpn_sitec_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed) {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    gs->number_of_hw_id_changed = num_of_hw_id_changed;
    gs->hw_id_changed_time_us[num_of_hw_id_changed-1] = id_changed_time_us;
}

int zpn_sitec_get_num_hw_id_changed() {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->number_of_hw_id_changed;
}

int64_t* zpn_sitec_get_hw_id_changed_time_us() {
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    return gs->hw_id_changed_time_us;
}

int zpn_site_firedrill_timer_activate(int64_t firedrill_interval)
{
    struct timeval tv = {0};
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    /* activate the timer */
    tv.tv_sec = firedrill_interval;
    tv.tv_usec = 0;
    if (event_add(ev_firedrill_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate status timer for sitec");
        return ZPN_RESULT_ERR;
    }
    gs->firedrill_status = ZPN_SITEC_FIREDRILL_ENABLED;
    __sync_add_and_fetch(&g_sitec_firedrill_stats_obj.firedrill_triggered_count, 1);
    ZPN_LOG(AL_INFO, "firedrill activated, customer: %"PRId64" for interval(secs) %"PRId64"", gs->customer_id, firedrill_interval);

    return ZPATH_RESULT_NO_ERROR;
}

void zpn_sitec_firedrill_stop(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if(!gs) {
        ZPN_LOG(AL_ERROR, "global sitec state NULL\n");
        return;
    }

    if(ZPN_SITEC_FIREDRILL_DISABLED == gs->firedrill_status) {
         ZPN_LOG(AL_ERROR, "PCC not in firedrill mode\n");
         return;
    }

    gs->firedrill_status = ZPN_SITEC_FIREDRILL_TRANSIT;

    ZPN_LOG(AL_INFO, "sitec: firedrill interval completed, connect back to cloud");

    if(fohh_thread_call( fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MC), zpn_sitec_mission_critical_conn_create, NULL, 0)) {
        ZPN_LOG(AL_CRITICAL, "Implement me");
    }
}

int zpn_sitec_drop_files(char *starts_with)
{
    struct dirent *entry;
    DIR *dp = opendir(".");

    if (dp == NULL) {
        SITEC_LOG(AL_ERROR, "Unable to open current directory");
        return ZPN_RESULT_ERR;
    }

    while ((entry = readdir(dp)) != NULL) {
        // Check if the filename starts with "i.0"
        if (strncmp(entry->d_name, starts_with, strlen(starts_with)) == 0) {
            // Try to delete the file
            if (remove(entry->d_name) == 0) {
                SITEC_LOG(AL_WARNING, "Deleted file: %s", entry->d_name);
            } else {
                SITEC_LOG(AL_ERROR, "Error deleting file %s", entry->d_name);
            }
        }
    }

    closedir(dp);
    return ZPN_RESULT_NO_ERROR;
}
