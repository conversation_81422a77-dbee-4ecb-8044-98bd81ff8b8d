/*
 * zpn_sitec_private.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#ifndef _ZPN_SITEC_PRIVATE_H_
#define _ZPN_SITEC_PRIVATE_H_

#include "zpath_lib/zpa_cloud_config.h"
#include "zpn/zpn_sitec_version.h"
#include "zcdns/zcdns_libevent.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zhw/zhw_id.h"
#include "zcrypt/zcrypt.h"
#include "zcrypt/zcrypt_meta.h"
#include "zpn/zpn_system.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_sitec_log.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_upgrade_utils.h"

/* Site Controller defines */
#define SITEC_PROV_KEY_LEN             1024
#define SITEC_KEY_API_LEN              100
#define SITEC_INSTANCE_NAME_LEN        ZPN_MAX_INSTANCE_NAME_LEN
#define SITEC_CLOUD_NAME_LEN           ZPN_MAX_CLOUD_NAME_LEN
#define SITEC_CN_LEN                   256
#define SITEC_INVALID_ID               0
#define SITEC_GLOBAL_MAGIC             (0xBEADBEAD)
#define SITEC_ROLE_NAME                "Site Controller"
#define SITEC_ROLE_NAME_LEN            32
#define SITEC_SNI_DOMAIN_LEN           ZPN_MAX_DOMAIN_NAME_LEN
#define SITEC_ENROLL_VERSION           2
#define SITEC_INSTANCE_FULL_NAME       (ZPN_MAX_INSTANCE_NAME_LEN + 1 + ZPN_MAX_CLOUD_NAME_LEN)

#define SITEC_DEFAULT_FOHH_THREADS		4
#define ZPN_SITEC_FORCE_RE_ENROLL_TIME_FACTOR               99

#define SITEC_CONTROL_TX_STATS_PROC_SMAPS_TIMEPERIOD_SEC    ((int64_t)(24ll * 60ll * 60ll))

#define SITEC_COMMAND_LINE_ARG_LEN        1024

extern int is_sitec_dev_environment;

#define DUMP_CONFIG_WALLY_STATE_FILE        "wally_config_dump"
#define DUMP_CONFIG_WALLY_OVD_STATE_FILE    "wally_config_ovd_dump"

//#define SITEC_ATOMICS_API_C11_STDATOMIC

#ifdef SITEC_ATOMICS_API_C11_STDATOMIC
#include <stdatomic.h>
#endif

/*
 * Connected broker
 */
struct zpn_sitec_connected_broker {
    int64_t broker_gid_from_config;     /* Broker gid */
    struct fohh_connection *f_conn;     /* sitec -> Broker conn */
    int64_t f_conn_incarnation;         /* f_conn_incarnation */
    int log_upload;                     /* sitec -> Broker log upload state */
    uint64_t debug_flag;                /* Debug logs flag for broker */
    int stats_upload;                   /* sitec -> Broker stats upload state */
};

#ifdef PRIVATE_BROKER_ATOMICS_API_C11_STDATOMIC

  /*
   * Use the standard C11 Atomics API
   */

  #define ZPN_ATOMIC_UINT64  atomic_uint_least64_t
  #define ZPN_ATOMIC_INT64   atomic_int_least64_t

  #define ZPN_ATOMIC_UINT32  atomic_uint_least64_t
  #define ZPN_ATOMIC_INT32   atomic_int_least32_t

  #define ZPN_ATOMIC_LOAD_EXPLICIT(ptr,var,memorder) \
    do { var = atomic_load_explicit(&ptr, memorder); } while(0)

  #define ZPN_ATOMIC_LOAD(ptr,var) \
    do { var = atomic_load(ptr); } while(0)

  #define ZPN_ATOMIC_STORE_EXPLICIT(ptr,var,memorder) \
    do { atomic_store_explicit(&ptr, var, memorder); } while(0)

  #define ZPN_ATOMIC_STORE(ptr,var) \
    do { atomic_store(ptr, var); } while(0)

  #define ZPN_ATOMIC_FETCH_ADD8(ptr,val) atomic_fetch_add((atomic_uint_least64_t *)(ptr), val)
  #define ZPN_ATOMIC_FETCH_SUB8(ptr,val) atomic_fetch_sub((atomic_uint_least64_t *)(ptr), val)

  #define ZPN_ATOMIC_FETCH_ADD4(ptr,val) atomic_fetch_add((atomic_uint_least32_t *)(ptr), val)
  #define ZPN_ATOMIC_FETCH_SUB4(ptr,val) atomic_fetch_sub((atomic_uint_least32_t *)(ptr), val)

#else

  /*
   * Use gcc builtin atomic API. These have been deprecated in favor of standard C11 Atomics API
   * Their internal implementation uses the standard C11 Atomics API
   */

  #define ZPN_ATOMIC_UINT64  volatile uint64_t
  #define ZPN_ATOMIC_INT64   volatile int64_t

  #define ZPN_ATOMIC_UINT32  volatile uint32_t
  #define ZPN_ATOMIC_INT32   volatile int32_t

  #define ZPN_ATOMIC_LOAD_EXPLICIT(ptr,var,memorder) ZPN_ATOMIC_LOAD(ptr,var)
  #define ZPN_ATOMIC_LOAD(ptr,var) \
    do { var = *(ptr); } while(0)

  #define ZPN_ATOMIC_STORE_EXPLICIT(ptr,var,memorder) ZPN_ATOMIC_STORE(ptr,var)
  #define ZPN_ATOMIC_STORE(ptr,var) \
    do { *(ptr) = var; } while(0)

  #define ZPN_ATOMIC_FETCH_ADD8(ptr,val) __sync_add_and_fetch_8(ptr, val)
  #define ZPN_ATOMIC_FETCH_SUB8(ptr,val) __sync_sub_and_fetch_8(ptr, val)

  #define ZPN_ATOMIC_FETCH_ADD4(ptr,val) __sync_add_and_fetch_4(ptr, val)
  #define ZPN_ATOMIC_FETCH_SUB4(ptr,val) __sync_sub_and_fetch_4(ptr, val)

#endif

/*
 * Site Controller state - place for common runtime state
 */
struct zpn_sitec_state {
    int64_t next_restart_time;
    int64_t time_delta_us;

    /* Local wally states */
    struct wally *cfg_wally;
    struct wally_origin *cfg_remote_db;
    struct wally_origin *cfg_slave_db;
    struct wally_fohh_client *cfg_wally_fohh_client_handle;
    struct fohh_connection *cfg_fohh_conn;
    struct wally_fohh_server *cfg_wally_server;

    struct wally *ovd_wally;
    struct wally_origin *ovd_remote_db;
    struct wally_origin *ovd_slave_db;
    struct wally_fohh_client *ovd_wally_fohh_client_handle;
    struct fohh_connection *ovd_fohh_conn;
    struct wally_fohh_server *ovd_wally_server;

    struct wally *rcfg_wally;
    struct wally_origin *rcfg_remote_db;
    struct wally_origin *rcfg_slave_db;
    struct wally_fohh_client *rcfg_wally_fohh_client_handle;
    struct fohh_connection *rcfg_fohh_conn;
    struct wally_fohh_server *rcfg_wally_server;

    /* FOHH connection for control channel */
    struct fohh_connection *ctrl_fohh_conn;

    /* FOHH connection for stats channel */
    struct fohh_connection *stats_fohh_conn;
    /* FOHH connection for astats channel */
    struct fohh_connection *astats_fohh_conn;
    /* FOHH connection for pbstats channel */
    struct fohh_connection *pbstats_fohh_conn;

    /* FOHH connection for userdb wally channel */
    struct fohh_connection *userdb_wally_conn;
    struct wally_origin *userdb_slave_db;

    /* FOHH connection for event log channel */
    struct fohh_connection *event_log_channel;

    int64_t num_comprehensive_stats_upload;
    int64_t num_comprehensive_stats_upload_fails;

    /* sitec's connected broker */
    struct zpn_sitec_connected_broker *connected_broker;

    /* scmc connection for firedrill transit state */
    struct fohh_connection *scmc;
};

enum zpn_sitec_pause_reason {
    zpn_sitec_pause_reason_invalid,
    zpn_sitec_pause_reason_restart_process,
    zpn_sitec_pause_reason_restart_system,
    zpn_sitec_pause_reason_certificate_expiry,
    zpn_sitec_pause_reason_upgrade_inprogress
};

typedef enum {
    ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DELETED = 0x01,
    ZPN_SITEC_DISABLE_FLAG_SITEC_IS_DISABLED = 0x02,
    ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DELETED = 0x04,
    ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_IS_DISABLED = 0x08,
    ZPN_SITEC_DISABLE_FLAG_SITEC_GROUP_REMOVED_FROM_SITE = 0x10,
    ZPN_SITEC_DISABLE_FLAG_SITE_IS_DELETED = 0x20,
    ZPN_SITEC_DISABLE_FLAG_SITE_IS_DISABLED = 0x40,
    ZPN_SITEC_DISABLE_FLAG_DISABLED_BY_CURL = 0x80,
} zpn_sitec_disable_flag_t;

/*
 * Site Controller global state
 */
struct zpn_sitec_global_state {
  int magic;
  pthread_mutex_t lock;
  char role_name[SITEC_ROLE_NAME_LEN];
  struct zpn_enroll_state *enroll_state;
  char *root_cert;
  time_t root_cert_time;
  char cn[SITEC_CN_LEN];
  char cfg_name[SITEC_CN_LEN];
  uint32_t disable_flags;
  int use_sqlt;
  int is_container_env;
  int cgroup_version;
  enum zpn_sitec_pause_reason pause_reason;
  int64_t pause_mode_enter_time_cloud_s;
  int64_t sc_restart_time_cloud_s;
  int64_t customer_id;
  int64_t sitec_id;
  int64_t sitec_scope_id;
  int64_t sitec_group_id;
  int64_t site_gid;
  double lat;
  double lon;
  char cc[3];
  int64_t reenroll_period;
  char *offline_domain;
  char *sitesp_fqdn;
  struct zpn_site_controller_version *sitec_version;
  struct zpn_sitec_state *sitec_state;
  struct zcdns *zcdns;
  char sitec_runtime_os[FOHH_MAX_NAMELEN];
  uint8_t cfg_instance_bytes[INSTANCE_ID_BYTES];
  uint8_t cfg_fingerprint_bytes[INSTANCE_ID_BYTES];
  char cfg_fingerprint_str[(((INSTANCE_ID_BYTES + 2)/ 3)* 4)+ 1];
  char cfg_provisioning_key[SITEC_PROV_KEY_LEN];
  int cfg_key_shard;
  char cfg_key_api[SITEC_KEY_API_LEN];
  int disable_auto_upgrade;
  int disable_geo_ip;

  struct zhw_id cfg_hw_id;
  struct zcrypt_key cfg_hw_key;
  EVP_PKEY *cfg_pkey;
  struct zcrypt_pkey *cfg_sitesp_pkey;
  char *cfg_sitesp_signing_pkey;
  char *sitesp_ca_cert_pem;
  char *sitesp_cert_pem;
  struct zthread_info *zthread;
  int enroll_version;
  int debug_flag;
  struct zpath_interlock sitesp_interlock;
  SSL_CTX *sitesp_ssl_ctx;
  SSL_CTX *sitesp_local_ssl_ctx;
  SSL_CTX *sitesp_client_ctx;
  SSL_CTX *sitec_ssl_ctx;
  int64_t sc_start_time_s;
  int64_t sc_start_time_m_s;
  enum zcrypt_metadata_develop_mode develop_certs_mode;
  int64_t cloud_time_delta_us;
  int auto_upgrade_disabled;
  uint32_t host_uptime_s;
  int64_t cert_validity_start_time_cloud_s;
  int64_t cert_validity_end_time_cloud_s;
  int64_t cert_force_re_enroll_time_cloud_s;
  int number_of_hw_id_changed;
  int64_t hw_id_changed_time_us[ZHW_TOTAL_HW_USED];
  struct event *sitec_monitor_timer;
  uint32_t configured_cpus;
  uint32_t available_cpus;
  uint32_t fohh_threads;
  int64_t swap_config;
  uint64_t waiting_for_decrypt_key_resp:1;

  /* Alt cloud config feature flag state */
  zpath_rwlock_t alt_cloud_lock;
  char*   alt_cloud;
  int64_t alt_cloud_enabled;
  int split_brain_sync_enabled;                                                  /* _ARGO: integer */

  struct event *recovery_timer;

  /*
    * This is the default cloud name, viz. prod.zpath.net
    * In the absence of any alt_cloud, this will be used as both redir and listener alt cloud names.
    */
  char cfg_key_cloud[SITEC_CLOUD_NAME_LEN + 1];

  /* System Stats */
  struct zpn_sitec_system_stats sys_stats;

  const char *sarge_version;

  struct fohh_generic_server *sni_server;
  struct fohh_http_server *http_server;

  int64_t sitec_fohh_conn_timeout_override;
  int64_t sitec_client_auth_timeout_override;

  struct zhash_table* pblog_collection_map;
  struct zhash_table* alog_collection_map;
  struct zpath_cloud_config *cloud_config;
  int firedrill_status;

  /* sitec fproxy */
  int64_t fproxy_enabled;

  struct fohh_generic_server *proxy_server;
  pthread_mutex_t proxy_server_lock;
  int proxy_server_initialized;
  int64_t os_upgrade_feature_flag;
  int64_t sarge_upgrade_feature_flag;
  int64_t sarge_backup_version_feature_flag;
  int64_t full_os_upgrade_feature_flag;
  uint64_t last_os_upgrade_time;
  uint64_t last_sarge_upgrade_time;
  char *platform_version;
  struct zpath_upgrade_stats upgrade_stats;
  struct argo_hash_table *shard_skip_table;

  /* Oauth based enrollment configs */
  int oauth_enroll;
};

extern struct zpath_allocator sitec_allocator;
#define SITEC_MALLOC(x) zpath_malloc(&sitec_allocator, x, __LINE__, __FILE__)
#define SITEC_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define SITEC_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define SITEC_CALLOC(x) zpath_calloc(&sitec_allocator, x, __LINE__, __FILE__)
#define SITEC_STRDUP(x, y) zpath_strdup(&sitec_allocator, x, y, __LINE__, __FILE__)

void zpn_sitec_enable_pse_snis(int enable);
void zpn_sitec_enable_asst_snis(int enable);
void zpn_sitec_enable_client_snis(int enable_c2site, int enable_others);

const char* zpn_sitec_get_disable_flag();
void zpn_sitec_update_service(zpn_sitec_disable_flag_t add, zpn_sitec_disable_flag_t remove, int force);

#endif //_ZPN_SITEC_PRIVATE_H_
