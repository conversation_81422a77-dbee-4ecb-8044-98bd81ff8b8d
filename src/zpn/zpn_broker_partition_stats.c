/*
 * zpn_broker_partition_stats.c. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */

#include "argo/argo_log.h"
#include "zpn/zpn_lib.h"
#include "zpath_lib/zpath_partition.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpath_lib/zpath_partition_stats.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_partition_stats.h"
#include "zpn/zpn_broker_partition_stats_compiled.h"

struct argo_structure_description *broker_partition_stats_description;

struct zpath_partition_object broker_none_partition_obj;
struct zpath_partition_object broker_wrong_partition_obj;

static int debug_partition_stats(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *object)
{
    int64_t partition_gid;
    char partition_gid_str[128];

    if(!zpath_get_logical_partition_feature_status()) {
         ZDP("enable logical partitioning feature \n");
         return ZPN_RESULT_NO_ERROR;
    }

    struct broker_partition_stats_obj *stats_obj;

    if (!query_values[0]) {
        ZDP("partition_gid is required\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if(strcasecmp(query_values[0],"None") == 0)
    {
        stats_obj = (struct broker_partition_stats_obj * )broker_none_partition_obj.stats.app_stats;
        ZDP("partition_gid: None \n");
        ZDP("num_client                 :%"PRId64"\n", stats_obj->part_stats.num_client);
        ZDP("num_client_connect         :%"PRId64"\n", stats_obj->part_stats.num_client_connect);
        ZDP("num_client_disconnect      :%"PRId64"\n", stats_obj->part_stats.num_client_disconnect);
        ZDP("num_mtunnel                :%"PRId64"\n", stats_obj->part_stats.num_mtunnel);
        ZDP("num_mtunnel_create         :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_create);
        ZDP("num_mtunnel_destroy        :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_destroy);
        ZDP("total_customers            :%"PRId64"\n", stats_obj->part_stats.total_customers);
        ZDP("total_lookups              :%"PRId64"\n", stats_obj->part_stats.total_lookups);
        ZDP("redirect_count             :%"PRId64"\n", stats_obj->part_stats.redirect_count);
        ZDP("mdc_pdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_0_partition_broker);
        ZDP("mdc_pdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_1_partition_broker);
        ZDP("mdc_pdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_2_partition_broker);
        ZDP("mdc_sdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_0_partition_broker);
        ZDP("mdc_sdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_1_partition_broker);
        ZDP("mdc_sdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_2_partition_broker);
        ZDP("partition_broker_count0    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count0);
        ZDP("partition_broker_count1    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count1);
        ZDP("partition_broker_count2    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count2);
        return ZPN_RESULT_NO_ERROR;
    }
    if(strcasecmp(query_values[0],"Wrong") == 0)
    {
        stats_obj = (struct broker_partition_stats_obj * )broker_wrong_partition_obj.stats.app_stats;
        ZDP("partition_gid: wrong \n");
        ZDP("num_client                 :%"PRId64"\n", stats_obj->part_stats.num_client);
        ZDP("num_client_connect         :%"PRId64"\n", stats_obj->part_stats.num_client_connect);
        ZDP("num_client_disconnect      :%"PRId64"\n", stats_obj->part_stats.num_client_disconnect);
        ZDP("num_mtunnel                :%"PRId64"\n", stats_obj->part_stats.num_mtunnel);
        ZDP("num_mtunnel_create         :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_create);
        ZDP("num_mtunnel_destroy        :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_destroy);
        ZDP("total_customers            :%"PRId64"\n", stats_obj->part_stats.total_customers);
        ZDP("total_lookups              :%"PRId64"\n", stats_obj->part_stats.total_lookups);
        ZDP("redirect_count             :%"PRId64"\n", stats_obj->part_stats.redirect_count);
        ZDP("mdc_pdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_0_partition_broker);
        ZDP("mdc_pdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_1_partition_broker);
        ZDP("mdc_pdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_2_partition_broker);
        ZDP("mdc_sdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_0_partition_broker);
        ZDP("mdc_sdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_1_partition_broker);
        ZDP("mdc_sdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_2_partition_broker);
        ZDP("partition_broker_count0    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count0);
        ZDP("partition_broker_count1    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count1);
        ZDP("partition_broker_count2    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count2);
        return ZPN_RESULT_NO_ERROR;
    }

    partition_gid = strtoull(query_values[0], NULL, 10);
    snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", partition_gid);

    zpath_partition_stats_rdlock();

    stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
    if (!stats_obj) {
        zpath_partition_stats_rdunlock();
        return ZPN_RESULT_ERR;
    }
    ZDP("num_client                 :%"PRId64"\n", stats_obj->part_stats.num_client);
    ZDP("num_client_connect         :%"PRId64"\n", stats_obj->part_stats.num_client_connect);
    ZDP("num_client_disconnect      :%"PRId64"\n", stats_obj->part_stats.num_client_disconnect);
    ZDP("num_mtunnel                :%"PRId64"\n", stats_obj->part_stats.num_mtunnel);
    ZDP("num_mtunnel_create         :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_create);
    ZDP("num_mtunnel_destroy        :%"PRId64"\n", stats_obj->part_stats.num_mtunnel_destroy);
    ZDP("total_customers            :%"PRId64"\n", stats_obj->part_stats.total_customers);
    ZDP("total_lookups              :%"PRId64"\n", stats_obj->part_stats.total_lookups);
    ZDP("redirect_count             :%"PRId64"\n", stats_obj->part_stats.redirect_count);
    ZDP("mdc_pdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_0_partition_broker);
    ZDP("mdc_pdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_1_partition_broker);
    ZDP("mdc_pdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_pdc_2_partition_broker);
    ZDP("mdc_sdc_0_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_0_partition_broker);
    ZDP("mdc_sdc_1_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_1_partition_broker);
    ZDP("mdc_sdc_2_partition_broker :%"PRId64"\n", stats_obj->part_stats.mdc_sdc_2_partition_broker);
    ZDP("partition_broker_count0    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count0);
    ZDP("partition_broker_count1    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count1);
    ZDP("partition_broker_count2    :%"PRId64"\n", stats_obj->part_stats.partition_broker_count2);

    zpath_partition_stats_rdunlock();

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_partition_common_stats_fill(void* cookie,
                                            int counter,
                                            void* structure_data)
{
    struct broker_partition_stats* out_data;
    char partition_gid_str[128];
    struct broker_partition_stats_obj *stats_obj = cookie;
    struct broker_partition_stats *none_wrong_part_stats;

    out_data = (struct broker_partition_stats *)structure_data;
    if(!out_data) {
        ZPN_LOG(AL_ERROR, "bad argument: structure_data");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (stats_obj != NULL) {

        /* check for none and wrong partition ids */
        if(stats_obj->partition_gid != ZPN_BROKER_PARTITION_NONE_GID && stats_obj->partition_gid != ZPN_BROKER_PARTITION_WRONG_GID) {
            /* get stats for all the partitions other then none and wrong */
            snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", stats_obj->partition_gid);
            zpath_partition_get_common_stats(partition_gid_str,
                                            &out_data->total_lookups,
                                            &out_data->total_customers);
        } else {
            /* fill in stats from none or wrong partitions */
            out_data = (struct broker_partition_stats *)structure_data;
            if(stats_obj->partition_gid == ZPN_BROKER_PARTITION_NONE_GID) {
                none_wrong_part_stats = (struct broker_partition_stats *) broker_none_partition_obj.stats.app_stats;
                if(none_wrong_part_stats) {
                    out_data->total_customers = __sync_add_and_fetch_8(&none_wrong_part_stats->total_customers, 0);
                    out_data->total_lookups = __sync_add_and_fetch_8(&none_wrong_part_stats->total_lookups, 0);
                }
            }
            if(stats_obj->partition_gid == ZPN_BROKER_PARTITION_WRONG_GID) {
                none_wrong_part_stats = (struct broker_partition_stats *) broker_wrong_partition_obj.stats.app_stats;
                if(none_wrong_part_stats) {
                    out_data->total_customers = __sync_add_and_fetch_8(&none_wrong_part_stats->total_customers, 0);
                    out_data->total_lookups = __sync_add_and_fetch_8(&none_wrong_part_stats->total_lookups, 0);
                }
            }
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        out_data->total_customers = 0;
        out_data->total_lookups = 0;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_partition_stats_obj_init(int64_t partition_gid, int create, void **obj)
{
    struct broker_partition_stats_obj *stats_obj = NULL;
    char log_name[256];
    char *partition_name;

    if (create) {
        stats_obj = ZPN_CALLOC(sizeof(*stats_obj));
        if (!stats_obj) {
            *obj = NULL;
            ZPN_LOG(AL_CRITICAL, "memory allocation failed");
            return ZPN_RESULT_ERR;
        }

        if(partition_gid != ZPN_BROKER_PARTITION_NONE_GID && partition_gid != ZPN_BROKER_PARTITION_WRONG_GID) {
            partition_name = zpath_partition_gid_to_name(partition_gid);
            if (partition_name) {
                snprintf(log_name, sizeof(log_name), "%s",partition_name);
            } else {
                snprintf(log_name, sizeof(log_name), "%"PRId64"", partition_gid);
            }
        }
        else if (partition_gid == ZPN_BROKER_PARTITION_NONE_GID) {
            snprintf(log_name, sizeof(log_name), "%s","None");
        }
        else if(partition_gid == ZPN_BROKER_PARTITION_WRONG_GID) {
            snprintf(log_name, sizeof(log_name), "%s","Wrong");
        }
        stats_obj->partition_gid = partition_gid;

        /* register for per partition stats */
        stats_obj->broker_partition_stats_log = argo_log_register_structure(argo_log_get("statistics_log"),
                                                                    log_name,
                                                                    AL_INFO,
                                                                    60 * 1000 * 1000,
                                                                    broker_partition_stats_description,
                                                                    &stats_obj->part_stats,
                                                                    1,
                                                                    zpn_broker_partition_common_stats_fill,
                                                                    stats_obj);
        if (!stats_obj->broker_partition_stats_log) {
            ZPN_LOG(AL_ERROR, "Could not register broker_partition_stats_log obj: statistics_log not initialized?");
            ZLIB_FREE(stats_obj);
            *obj = NULL;
            return ZPN_RESULT_ERR;
        }
        /* All good, copy the stats obj */
        *obj = stats_obj;
    } else {
        stats_obj = *obj;
        if (stats_obj) {
            argo_log_deregister_structure(stats_obj->broker_partition_stats_log, 0);
            ZPN_FREE(stats_obj);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_partition_stats_init()
{

    int res = 0;

    broker_partition_stats_description = argo_register_global_structure(BROKER_PARTITION_STATS_HELPER);
    if (!broker_partition_stats_description) {
        ZPN_LOG(AL_ERROR, "Could not register broker_partition_stats_description");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_safe_read_command("Partition stats",
                "/balance/partition/stats",
                debug_partition_stats,
                NULL,
                "partition_gid",
                "partition_gid",
                NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add command: /balance/partition/stats %s", zpath_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_create_additional_stats_partitions(int64_t partition_gid, char *partition_name, struct zpath_partition_object *part_obj)
{
    int res = 0;

    res = zpn_broker_partition_stats_obj_init(partition_gid, 1, &part_obj->stats.app_stats);
    if(res) {
        ZPN_LOG(AL_ERROR, "ZPN_BROKER_PARTITION_NONE stats obj creation failed ");
        return res;
    }
    return res;
}

int zpn_broker_additional_stats_partitions()
{
    int res = 0;

    memset(&broker_none_partition_obj, 0 , sizeof(struct zpath_partition_object));
    memset(&broker_wrong_partition_obj, 0 , sizeof(struct zpath_partition_object));

    res = zpn_broker_create_additional_stats_partitions(ZPN_BROKER_PARTITION_NONE_GID, "None", &broker_none_partition_obj);
    if(res) {
        ZPN_LOG(AL_ERROR, "Could not create none partition stats obj");
        return res;
    }
    res = zpn_broker_create_additional_stats_partitions(ZPN_BROKER_PARTITION_WRONG_GID, "Wrong", &broker_wrong_partition_obj);
    if(res) {
        ZPN_LOG(AL_ERROR, "Could not create none partition stats obj");
        return res;
    }
    return res;
}

void incr_num_redirects_for_partition(int64_t partition_gid, int do_stats)
{
    char partition_gid_str[128] = {0};
    struct broker_partition_stats_obj *stats_obj;

    if (!do_stats) {
        return;
    }

    if (partition_gid == ZPN_BROKER_PARTITION_NONE_GID) {
        stats_obj = (struct broker_partition_stats_obj *)broker_none_partition_obj.stats.app_stats;
    } else if (partition_gid == ZPN_BROKER_PARTITION_WRONG_GID) {
        stats_obj = (struct broker_partition_stats_obj *)broker_wrong_partition_obj.stats.app_stats;
    } else {
        snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", partition_gid);

        zpath_partition_stats_rdlock();
        stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
        if(stats_obj) {
            __sync_add_and_fetch_8(&(stats_obj->part_stats.redirect_count), 1);
        }
        zpath_partition_stats_rdunlock();
        return;
    }

    if(stats_obj) {
        __sync_add_and_fetch_8(&(stats_obj->part_stats.redirect_count), 1);
    }
}

void incr_num_client_connect_part_stats(struct zpn_broker_client_fohh_state *c_state)
{
    struct broker_partition_stats_obj *stats_obj;
    char partition_gid_str[128];

    if(c_state->is_target_part_flag) {
        snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", c_state->cust_part_gid);
        zpath_partition_stats_rdlock();
        stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
        if (stats_obj) {
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client_connect), 1);
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client), 1);
        }
        zpath_partition_stats_rdunlock();
        return;
    } else if (c_state->is_inst_not_part_flag) {
        stats_obj = (struct broker_partition_stats_obj *)broker_none_partition_obj.stats.app_stats;
    } else {
        stats_obj = (struct broker_partition_stats_obj *)broker_wrong_partition_obj.stats.app_stats;
    }
    if (stats_obj) {
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client_connect), 1);
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client), 1);
    }

    /* Mark that this client was counted in connect stats */
    c_state->partition_connect_stats_counted = 1;
}

void incr_num_client_disconnect_part_stats(struct zpn_broker_client_fohh_state *c_state)
{
    struct broker_partition_stats_obj *stats_obj;
    char partition_gid_str[128];

    /* Only count disconnect stats if we previously counted connect stats */
    if (!c_state->partition_connect_stats_counted) {
        ZPATH_DEBUG_PARTITIONING("LP: Skipping disconnect stats for client that was never counted in connect stats: customer_gid %"PRId64"", c_state->customer_gid);
        return;
    }

    if(c_state->is_target_part_flag) {
        snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", c_state->cust_part_gid);
        zpath_partition_stats_rdlock();
        stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
        if (stats_obj) {
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client_disconnect), 1);
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client), -1);
        }
        zpath_partition_stats_rdunlock();
        return;
    } else if (c_state->is_inst_not_part_flag) {
        stats_obj = (struct broker_partition_stats_obj *)broker_none_partition_obj.stats.app_stats;
    } else {
        stats_obj = (struct broker_partition_stats_obj *)broker_wrong_partition_obj.stats.app_stats;
    }
    if (stats_obj) {
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client_disconnect), 1);
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_client), -1);
    }
}

int zpn_broker_update_tc_for_spl_partition(int64_t partition_gid, int add)
{
    struct broker_partition_stats_obj *none_wrong_part_stats_obj = NULL;
    int res = ZPN_RESULT_NOT_FOUND;

    if (partition_gid == ZPN_BROKER_PARTITION_NONE_GID) {
        none_wrong_part_stats_obj = (struct broker_partition_stats_obj *) broker_none_partition_obj.stats.app_stats;
    }
    else if (partition_gid == ZPN_BROKER_PARTITION_WRONG_GID) {
        none_wrong_part_stats_obj = (struct broker_partition_stats_obj *) broker_wrong_partition_obj.stats.app_stats;
    }

    if(none_wrong_part_stats_obj) {
        __sync_add_and_fetch_8(&none_wrong_part_stats_obj->part_stats.total_customers, 1);
        res = ZPN_RESULT_NO_ERROR;
    }

    return res;
}

void update_total_customer_part_stats(struct zpn_broker_client_fohh_state *c_state)
{
    if(c_state->is_target_part_flag) {
        zpath_incr_unique_customer(c_state->cust_part_gid, c_state->customer_gid);
    } else if (c_state->is_inst_not_part_flag) {
        zpath_incr_unique_customer(ZPN_BROKER_PARTITION_NONE_GID, c_state->customer_gid);
    } else {
        zpath_incr_unique_customer(ZPN_BROKER_PARTITION_WRONG_GID, c_state->customer_gid);
    }
}

void incr_num_mtunnel_create_part_stats(struct zpn_broker_client_fohh_state *c_state)
{
    struct broker_partition_stats_obj *stats_obj;
    char partition_gid_str[128];

     if(c_state->is_target_part_flag) {
        /* update stats for connection landed on the partition customer is also assigned / calculated to */
        snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", c_state->cust_part_gid);
        zpath_partition_stats_rdlock();
        stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
        if (stats_obj) {
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel_create), 1);
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel), 1);
        }
        zpath_partition_stats_rdunlock();
        return;
    } else if (c_state->is_inst_not_part_flag) {
        /* unpartitioned list - free pool broker instance stats update */
        stats_obj = (struct broker_partition_stats_obj *)broker_none_partition_obj.stats.app_stats;
    } else {
        /* connection landed on a wrong broker, but not the partition on which this customer is assigned to */
        stats_obj = (struct broker_partition_stats_obj *)broker_wrong_partition_obj.stats.app_stats;
    }
    if (stats_obj) {
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel_create), 1);
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel), 1);
    }
}

void incr_num_mtunnel_destroy_part_stats(struct zpn_broker_client_fohh_state *c_state)
{
    struct broker_partition_stats_obj *stats_obj;
    char partition_gid_str[128];

    if(c_state->is_target_part_flag) {
        snprintf(partition_gid_str, sizeof(partition_gid_str), "%"PRId64"", c_state->cust_part_gid);
        zpath_partition_stats_rdlock();
        stats_obj = zpath_partition_get_app_stats_obj(partition_gid_str);
        if (stats_obj) {
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel_destroy), 1);
            __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel), -1);
        }
        zpath_partition_stats_rdunlock();
        return;
    } else if (c_state->is_inst_not_part_flag) {
        stats_obj = (struct broker_partition_stats_obj *)broker_none_partition_obj.stats.app_stats;
    } else {
        stats_obj = (struct broker_partition_stats_obj *)broker_wrong_partition_obj.stats.app_stats;
    }
    if (stats_obj) {
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel_destroy), 1);
        __sync_add_and_fetch_8(&(stats_obj->part_stats.num_mtunnel), -1);
    }
}

/*
 * update_cstate_partition_flags
 *  Updates partition related info in cstate; used during stats evaluation
 */
void update_cstate_partition_flags(struct zpn_broker_client_fohh_state *c_state)
{
    int res;
    int64_t partition_gids[ZPATH_MAX_PARTITIONS];
    size_t count = ZPATH_MAX_PARTITIONS;

    if (!c_state) {
        return;
    }

    /* get customer partition gid from customer gid */
    c_state->cust_part_gid = zpath_partition_gid_from_customer_gid(c_state->customer_gid, NULL);

    /* get the instance partition gids list, check if the customer gid lands in any one of this */
    /* if yes. set the flag to true, otherwise to false */
    c_state->is_target_part_flag = 0;
    c_state->is_inst_not_part_flag = 0;

    res = zpath_partition_get_instance_partitions(broker_instance_id, partition_gids, &count);
    if (!res) {
        if(count) {
            for (int loop_counter = 0; loop_counter < count; loop_counter++) {
                if (partition_gids[loop_counter] == c_state->cust_part_gid) {
                    c_state->is_target_part_flag = 1;
                    c_state->is_inst_not_part_flag = 0;
                    break;
                }
            }
        }
        else {
            c_state->is_inst_not_part_flag = 1;
        }
    }

    ZPATH_DEBUG_PARTITIONING("LP: customer_gid %"PRId64" and cust_part_gid %"PRId64" Flags: is_target_part: %d is_inst_not_part: %d",
                             c_state->customer_gid, c_state->cust_part_gid,
                             c_state->is_target_part_flag, c_state->is_inst_not_part_flag);
}
