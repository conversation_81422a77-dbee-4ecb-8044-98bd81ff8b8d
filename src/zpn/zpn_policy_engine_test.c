/*
 * zpn_policy_engine_test.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 *
 * This has been expanded to be able to test client app download as
 * well, to save time. (client app download depends on policy)
 */


/*
 * Operates on a file. Each line of the file is a command.
 *
 * If the first non-whitespace of a line is '#', that line is ignored. (Comment line)
 * If a line is all whitespace, it is ignored.
 * Within a line, all data inluding and after the first '#' is ignored. (Comment at end of line)
 *
 * It is worth noting that the ID and GID space is well manages within
 * the actual product. In these test cases, however, they are simply
 * integers that rely on the definer to keep consistent amongst each
 * other. You can even use overlapping GIDs so long as they are for
 * different types. (Not recommended though, for readability reasons)
 *
 * The commands:
 *
 * NOTE: ADD is deprecated in favor of using Wally test origin
 *       commands for policy. (You must run the WALLY_POLICY command
 *       to run the 'new' way. This is more backwards compatibly with
 *       older scripts)
 *
 * WAITFOR ANY_OTHER_COMMAND
 *
 *     Will try to do ANY_OTHER_COMMAND until it succeds. Will result
 *     in test failure if ANY_OTHER_COMMAND does not succeed within 1
 *     second.
 *
 * LOAD -file FILENAME
 *     Load and execute specified filename
 *
 * WALLY_POLICY
 *     Tells the system to use wally for fetching policy data rather then the data added with the 'add' command.
 *
 * LOAD_POLICY -scope GID EXPECTED_RESULT
 *     Run zpn_policy_load for scope. Expected_result is likely ZPATH_RESULT_ASYNCHRONOUS or ZPATH_RESULT_NO_ERROR
 *
 * ZPATH_TABLE
 *     requires WALLY_POLICY
 *     Tells the system to initialize zpath_table. (Needs to be
 *     initialized for many other tables to be initialized) You will
 *     have to perform WALLY_REG_VERIFY and WALLY_RESPOND as a result
 *     of this initialization. Furthermore, the initialization occurs
 *     on a different thread (for synchronization reasons), so you
 *     will need to wait a little bit before doing the registration
 *     verification.
 *
 * ZPATH_CONSTELLATION
 *     requires ZPATH_TABLE
 *     initializes tables:
 *         zpath_constellation
 *         zpath_constellation_instance
 *         zpath_customer_to_constellation
 *     in that order
 *     You will have to perform WALLY_REG_VERIFY and WALLY_RESPOND as
 *     a result of this initialization, for each table, in the
 *     specified order. Like ZPATH_TABLE, the initialization is in
 *     another thread, so a short pause between each reg_verify is in
 *     order.
 *
 * ZPATH_CONSTELLATION_VERIFY -customer CUSTOMER_GID (-contains INSTANCE_ID) (-excludes INSTANCE_ID)
 *
  * ZPN_APPLICATION_DOMAIN
 *     requires ZPATH_TABLE
 *     initializes tables:
 *         zpn_application
 *         zpn_application_domain
 *     in that order
 *     You will have to perform WALLY_REG_VERIFY and WALLY_RESPOND as
 *     a result of this initialization, for each table, in the
 *     specified order. Like ZPATH_TABLE, the initialization is in
 *     another thread, so a short pause between each reg_verify is in
 *     order.
 *
 * ZPN_TLD_1_UPDATE -customer CUSTOMER_GID -domain DOMAIN_NAME
 *     update domain_name for TLD+1 domains
 * ZPN_TLD_1_VERIFY -customer CUSTOMER_GID (-contains TLD_1_DOMAIN) (-excludes TLD_1_DOMAIN)
 *     verify if a customer contains or excludes a TLD+1 domain
 *
 * ZPATH_INSTANCE
 *     initializes table:
 *         zpath_instance
 *         zpn_broker_load
 *         zpn_broker_balance_control
 *
 * VIABLE_BRK_VERIFY -customer CUSTOMER_GID (-contains BROKER_NAME) (-excludes BROKER_NAME)
 *     test get_viable_brokers()
 *     requires ZPATH_CONSTELLATION, ZPATH_INSTANCE
 *
 *  ZPN_PRIVATE_BROKER_LOAD -pbroker PBROKER_PID -customer CUSTOMER_PID
 *     initializes table:
 *         zpn_private_broker_load
 *
 * BRK_BALANCE_REDIR_VERIFY -latitude CLIENT_LAT -longitude CLIENTLON -country CLIENT_CC (-contains BROKER_NAME) (-excludes BROKER_NAME)
 *     test zpn_broker_balance_redirect()
 *     requires ZPATH_CONSTELLATION, ZPATH_INSTANCE
 *
 * BRK_BALANCE_REDIR_V2_VERIFY -latitude CLIENT_LAT -longitude CLIENTLON -country CLIENT_CC (-contains BROKER_NAME) (-excludes BROKER_NAME)
 *     test zpn_balance_redirect()
 *     requires ZPATH_CONSTELLATION, ZPATH_INSTANCE
 *     Optional arguments for policy+redirect test -saml idp_gid|SAML|attr_name|attr_val -scim_group idp_gid|SCIM_GROUP|group_gid -scim idp_gid|SCIM|attr_gid|attr_val
 *
 * DEPRECATED: see WALLY_POLICY: ADD (POLICY_SET|RULE|CONDITION_SET|OPERAND|ATTRIBUTE) -id <GID> [-children <CHILDREN_COUNT> [CHILD1 [CHILD2 [...]]]] [options]
 *     Note: Operands and Attributes do not have children_count. It defaults to 0 for the others, but must be specified if there are options.
 *     Note: Options must come at the END of the line.
 *     Options:
 *        -id n
 *            For those entries with an id, this is the value of id. For those entries with a gid, this is the value of gid
 *        -access    Same as -global_set 1
 *        -reauth    Same as -global_set 2
 *        -siem      Same as -global_set 3
 *        -bypass    Same as -global_set 4
 *        -redirect  Same as -global_set 8
 *        -global_set n
 *            For policy_set, sets the global_set of the policy set.
 *        -negate
 *            For condition_set, configures the condition set to be negated. (defaults not negated)
 *        -lhs <str>
 *            For operand, configures the operand to have the specified lhs string
 *        -rhs <str>
 *            For operand, configures the operand to have the specified rhs string
 *        -object_type <str>
 *            For operand, configures the operand to have the specified object_type string
 *        -operator xxx
 *            For condition set and rule, configures the operator (OR or AND should be the string. Defaults AND for rule, OR for condition set)
 *        -custom_msg xxx
 *            For rule, set the custom message field. Defaults NULL
 *        -timeout xxx
 *            For rule, set the reauth_timeout (defaults 100)
 *        -idle xxx
 *            For rule, set the reauth_idle_timeout (defaults 100)
 *        -rule_order xxx
 *            For rule, set the rule_order value. Defaults to incrementing.
 *        -action_result xxx
 *            For rule, set the action_result
 *        -saml_name xxx
 *            For attribute, set the saml name to xxx
 *        -idp_gid xxx
 *            For attribute, the gid of the IDP
 *        -async xxx
 *            For anything, control how many times a request for the object will return an asynchronous result. Defaults 0
 *
 * GET|GET_AND_SAVE <scope_gid> (access|reauth|siem|redirect|vpn) <expected_response_string> [Hex dump of expected built rule] [nobuild]
 *    GET : get policy in local variable
 *    GET_AND_SAVE: get policy and save it in global variable
 *    nobuild: get policy without rebuild
 *
 * USER <username> add <string>
 *    Adds <string> to the user's string hash table. Adds the user if it doesn't exist.
 *
 * USER <username> remove <string>
 *    Removes <string> from the user's string hash table. (Can make some scripts easier)
 *
 * APP_SEARCH -scope <id> -app <id> -req_app <domain:port>
 *    Verify most specific app found by zpn_application_domain_search() for a app access request
 *    -app :the expected app gid
 *    -req_app: the application (domain+port) to access
 *
 * EVAL -scope <id> (-access|-reauth|-siem|-bypass|-redirect|-vpn) [-negative] [-newpolicy] [-noget|-nobuild] -user username -verify-rule <id> [-app <app_gid>] [[-gid <gid]]
 *    For -verify-rule <id>, use id = 0 to verify that nothing matched.
 *    -app is the gid of which app is evaluated, to use new policy evaluation algorithm, we must secify which app is evaluated.
 *    -negative: expect opposite result, i.e. not match (POLICY_EVALUATION_COMPARE must be on)
 *    -newpolicy: if this is set, do not count it as mismatch if a new version of policy is being built or has been built.
 *    -noget: do not get policy, use the one saved in global variale
 *    -nobuild: get policy without rebuild
 *    The [<gids>] at the end are a list of GIDs to use for the integer-based hash table. These entries must be at the end.
 *
 * TICKLE -customer|scope <id> <-pretend>
 *    Causes the system to think that customer or scope <id>'s policy has
 *    changed. (as though wally presented more data to us). This only
 *    affects the policy evaluator testing- it does not affect
 *    client_app testing. To tell client app about policy change, use
 *    CLIENT_APP policy_change
 *    -pretend: increments the policy version number but will not really re-build policy. This pretend a new policy is being built.
 *
 * TICKLE_CANCEL -customer|scope <id> <-pretend>
 *    Causes the system cancel any
 *    outstanding policy build events (they are timer driven) in order
 *    to make unit tests more robust. Not all unit tests cancel
 *    tickles, but some do.
 *    -pretend: just decrements policy version.
 *
 * TICKLED -customer <id> (YES|NO)
 *
 *    Verifies that the specified customer is tickled or not. Tickled
 *    in this sense means that there is a future build that has not
 *    yet started.
 *
 * BUILD_OUTSTANDING -customer <id> (YES|NO)
 *
 *    Checks whether there is an incomplete outstanding build.
 *
 * MODIFIED_TIME now|<number>|off
 *    Specify the value of modified_time column for a table. If it is turned on, all WALLY_INJECT rows will have the
 *    modified_time column set to that value.
 *      now: turn on and set modified_time to current epoch time in second
 *      <number>: add additional seconds (can be negative) to current modified_time value
 *      off: turn off the functionality to set modified_time column(by deault it is off)
 *
 **** Client test commands: Note: user must be unique globally in this
 *    test program. Same user is not allowed in two customers
 *
 * CLIENT_APP start [-segment] -user <username> -customer <customer_gid>
 *    Tells the system to start processing/sending. If -segment is
 *    set, then the user behaves as though they want independent app
 *    segment updates. (Like edge connector and IP anchoring)
 *
 * CLIENT_APP update -user <username>
 *    Tells the system that the specified user's characteristics have
 *    changed.
 *
 * CLIENT_APP done -user <username>
 *    Tells the system that the specified user is no longer interested
 *    in apps.
 *
 * CLIENT_APP check -user <username> -domain <domain> -proto (tcp|udp) -port <port> -action (bypass|intercept|not_found) [-double_encrypt (YES|NO)] [-netcount N]
 *    Checks if the specified user has been told they can access the
 *    specified domain + proto + port with the specified
 *    action. Action not_found is an indication that the client does
 *    not have config for the specified domain + port +
 *    proto. -netcount checks if there are N apps in the cache for the
 *    client, where N is the value returned by
 *    zpn_broker_client_apps_sent_count(). -netcount cannot check for 0.
 *
 * CLIENT_APP complete -user <username>
 *    Verifies that the specified user has received app_complete.
 *
 * CLIENT_APP timer -customer <customer_gid> -fired (true|false)
 *    Verifies that a timer has (or has not) fired for the specified
 *    customer. This test clears the indication that the timer has
 *    fired if the timer had fired.
 *
 * CLIENT_APP dump -user <username>
 *    Dumps the current app state of given user.
 *
 **** WALLY test origin commands:
 *
 * WALLY_INJECT table_name JSON_OBJECT
 *    This injects the specified JSON object as a received wally
 *    row. The format of the JSON object is standard ARGO row format.
 *
 * WALLY_REG_VERIFY -table TABLE_NAME -register (YES|NO|NONE) [-id ID_NAME] [-column COLUMN_NAME] [-key KEY_VALUE]
 *    Checks to see if the front of the registration queue matches the
 *    specified (de)registration. NONE is used to verify the
 *    registration queue is empty.
 *    if '-id' is specified, then the request ID of the registration
 *    will be stored in ID_NAME for future use in responses
 *
 * WALLY_RESPOND -id ID_NAME -rows ROW_COUNT -table TABLE_NAME
 *    Sends a respons to wally for the given ID, with the given row_count for a table name
 *
 * WALLY_AUTORESPOND
 *    Configures wally to automatically respond to requests for data
 *    with preloaded row data. (loaded via WALLY_LOAD_FILE or
 *    WALLY_LOAD_OBJECT)
 *
 * WALLY_LOAD_FILE -file FILENAME
 *    Loads json objects from specified file into wally memory.
 *
 * WALLY_LOAD_OBJECT JSON_OBJECT
 *    Loads the specified JSON object into test wally memory for
 *    automatic fetching. table_name is automatic based on the object
 *    arriving, but can be overriden in case there is a compatibility
 *    issue between object names, DB names, etc
 *
 *
 **** Sequencing commands: (Needed because some stuff is on timers...)
 *
 * DELAY <microseconds>
 *
 * Example:
 *
 * # Add an empty policy set:
 * ADD POLICY_SET 1 0 -global_id 2  # global_id 2 is reauth policy
 *
 * # Add a policy set with one rule, that checks a SAML assertion.
 * ADD POLICY_SET 2 1 10 -global_id 1  # global_id 1 is access policy
 * ADD RULE 10 1 100 -action_result allow -rule_order 1
 * ADD CONDITION_SET 100 1 1000
 * ADD OPERAND 1000 -lhs 10000 -rhs john_doe -object_type SAML
 * ADD ATTRIBUTE 10000 -saml_name user_name -idp_gid 999
 *
 * # Check if the empty policy set builds correctly.
 * GET 1 reauth ZPATH_RESULT_NO_ERROR 00 00 <ENTER CORRECT HEX HERE>
 *
 * # Check if the simple policy builds correctly
 * GET 1 access ZPATH_RESULT_NO_ERROR 00 00 <ENTER CORRECT HEX HERE>
 */

#include "zthread/zthread.h"
#include "zevent/zevent.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_test_origin.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_lib.h"
#include "zhash/zhash_table.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_rule_condition_operand.h"
#include "zpn/zpn_rule_condition_set.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_policy_engine_build.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_group.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_instance.h"
#include "zpn/zpn_broker_load.h"

#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpath_lib/zpath_customer_partition_override.h"

#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_policy_engine_static.h"

#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_broker_client_apps_db_test.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_private.h"

#include "zpath_lib/zpath_partition_profile.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpath_lib/zpath_partition_stats.h"

#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_scope_engine.h"

#include "zpn_ot/zpn_credential_rule_mapping.h"
#include "zpn_ot/zpn_credentials.h"

#include "zpn_ot/zpn_credential_rule_mapping.h"
#include "zpn_ot/zpn_credentials.h"

#include "zpath_misc/zpath_misc.h"
#include <event2/event.h>
#include <event2/thread.h>
#include <libgen.h>

#include <limits.h>

struct zpn_common_broker_cfg    *g_broker_common_cfg = NULL;
int64_t broker_instance_id = 0;
int64_t modified_time_s = 0;

enum zpn_broker_status broker_status;
struct zpn_broker_termination_context g_broker_termination_ctx;
int g_broker_load_update_blocking;
int g_broker_termination_ready;

struct zpe_policy_built *g_policy_built = NULL;

#define MAX_WAITFOR_US (1000000*10)

#define OUTPUT_RED "\033[31;3m"
#define OUTPUT_GRN "\033[32;3m"
#define OUTPUT_YEL "\033[33;3m"
#define OUTPUT_BLU "\033[34;3m"
#define OUTPUT_PRP "\033[35;3m"
#define OUTPUT_LBL "\033[36;3m"
#define OUTPUT_RST "\033[0m"

int max_app_download_embedded_feature = 0;
int max_app_download_embedded = DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_POLICY_TEST;
int found_app_download_embedded = 0;
int not_found_app_download_embedded = 0;
int total_app_access = 0;
int current_platform = 0;
char current_username[128];
int64_t broker_alt_cloud_support_enabled = 0;

struct zhash_table *users;

extern int policy_eval_comp;

/*
 * user_apps contains zhash_table. The internal zhash_table is indexed
 * by domain, where the contents of the domain is the client_app
 * object that was sent to the client.
 */
struct zhash_table *user_apps;

/*
 * Hash table of IDs for dealing with registrations and responses.
 *
 * This is hashed by arbitrary string name, but contains pointer to
 * int64. (Which must be freed)
 */
struct zhash_table *ids;

/*
 * Hash table of usernames that have received app_complete.
 */
struct zhash_table *user_complete;

struct zhash_table *saml_hash;

/* Event_base for receiving events... Kind of kludgy but works. */
struct event_base *script_ev_base;

/* zevent_base for doing some initialization, when it needs to be off
 * main thread */
struct zevent_base *init_base;

struct user_app_state {
    char *username;
    struct zhash_table *domains;
    struct zpn_client_app_state *app_state;
    struct zpn_broker_client_app_stats *app_stats;
};

/*
 * wally used for testing.
 */
static struct wally *test_wally;
static struct wally_test_origin *test_origin;
struct wally_origin *test_origin_origin;
static int using_test_wally = 0;

struct argo_structure_description *zpn_zdx_probe_legs_info_description = NULL;

uint8_t expected_data[1024*1024];

#define MAX_ARGS 2500

/* Forward declarations */
int process_constellation_verify_line(char **argv, int argc, int line);
int process_partition_profile_activate(char **argv, int argc, int line);
int process_partition_profile_verify_line(char **argv, int argc, int line);
int process_partition_profile_instance_verify_line(char **argv, int argc, int line);
int process_partition_profile_partition_verify_line(char **argv, int argc, int line);
int process_customer_partition_override_verify_line(char **argv, int argc, int line);
int process_viable_brk_verify_line(char **argv, int argc, int line);
int process_zpn_tld_1_update_line(char **argv, int argc, int line);
int process_zpn_tld_1_verify_line(char **argv, int argc, int line);
int process_brk_redir_verify_line(char **argv, int argc, int line);
int process_brk_redir_v2_inspect_line(char **argv, int argc, int line);
int process_brk_redir_v2_verify_line(char **argv, int argc, int line);
int process_zpn_private_broker_load(char **argv, int argc, int line);
int process_file(char *filename);

int pbroker_load_cb(void *response_callback_cookie,
                    struct wally_registrant *registrant,
                    struct wally_table *table,
                    int64_t request_id,
                    int row_count)
{
    int *complete = response_callback_cookie;
    *complete = 1;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Convert to arguments...
 */
void argify(char *str, char **argv, int *argc)
{
    int max = *argc;
    *argc = 0;

    int ix = 0;

    char *w = str;
    while (*w) {
        while (isspace(*w)) {
            *w = 0;
            w++;
        }
        if ((*w == '#') || (*w == 0)) {
            break;
        }
        if ((ix + 1) == max) {
            return;
        }
        argv[ix] = w;
        (*argc)++;
        ix++;
        while ((*w) && (!isspace(*w))) w++;
    }
}

char *get_option_string(char **argv, int argc, int *used, char *str, char *default_str)
{
    int i;
    for (i = 0; i < argc; i++) {
        if (strcasecmp(argv[i], str) == 0) {
            if ((i + 1) < argc) {
                used[i] = 1;
                used[i+ 1] = 1;
                return ZPE_STRDUP(argv[i + 1], strlen(argv[i + 1]));
            }
        }
    }
    return default_str;
}

/* param num_values is in/out: initially contains number of slots in values,
 * when returned, contains number of values filled in. */
void get_option_strings(char **argv, int argc, int *used, char *option, char **values, int *num_values)
{
    int i;
    int count = 0;
    for (i = 0; i < argc && count < *num_values; i++) {
        if (strcasecmp(argv[i], option) == 0) {
            if ((i + 1) < argc) {
                used[i] = 1;
                used[i+ 1] = 1;
                values[count] = argv[i + 1];
                count++;
            }
        }
    }
    *num_values = count;
    return;
}

int64_t get_option_int64(char **argv, int argc, int *used, char *str, int64_t default_val)
{
    int i;
    for (i = 0; i < argc; i++) {
        if (strcasecmp(argv[i], str) == 0) {
            if ((i + 1) < argc) {
                used[i] = 1;
                used[i+ 1] = 1;
                return strtoul(argv[i + 1], NULL, 0);
            }
        }
    }
    return default_val;
}

int has_option(char **argv, int argc, int *used, char *str)
{
    int i;
    for (i = 0; i < argc; i++) {
        if (strcasecmp(argv[i], str) == 0) {
            used[i] = 1;
            return 1;
        }
    }
    return 0;
}


/*
 * Do one line for add
 */
int process_add_line(char **argv, int argc, int line)
{
    static int used[MAX_ARGS];
    int64_t *arr = NULL;
    int arr_len = 0;
    int i, j;

    memset(used, 0, sizeof(used));

    arr = NULL;
    arr_len = 0;
    /* Do add here */
    /* Verify enough arguments... */
    if (argc < 3) {
        ZPATH_LOG(AL_ERROR, "Line %d: Not enough arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    /* Read array of GIDs if they are there, and set up for
     * option parsing on a case-by-case basis afterwards */
    for (i = 0; i < argc; i++) {
        if (strcasecmp(argv[i], "-children") == 0) {
            used[i] = 1;
            if ((i + 1) >= argc) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting argument to -children", line);
                return ZPATH_RESULT_BAD_ARGUMENT;
            }
            used[i+1] = 1;
            arr_len = strtoul(argv[i+1], NULL, 0);
            if (arr_len) {
                arr = ZPE_CALLOC(arr_len * sizeof(*arr));
                if ((i + arr_len + 1) >= argc) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Not enough children specified", line);
                }
                for (j = 0; j < arr_len; j++) {
                    arr[j] = strtoul(argv[i + j + 2], NULL, 0);
                    if (!arr[j]) {
                        ZPATH_LOG(AL_ERROR, "Line %d: Bad child id: %s", line, argv[i + j + 2]);
                        return ZPATH_RESULT_BAD_ARGUMENT;
                    }
                    used[i + j + 2] = 1;
                }
            }
        }
    }
    if (strcasecmp(argv[1], "policy_set") == 0) {
        struct zpn_policy_set *set = ZPE_CALLOC(sizeof(*set));
        set->rule_ids_count = arr_len;
        set->rule_ids = arr;
        set->global_set = get_option_int64(argv, argc, used, "-global_set", 0);
        if (has_option(argv, argc, used, "-access")) set->global_set = zpe_policy_type_access;
        if (has_option(argv, argc, used, "-reauth")) set->global_set = zpe_policy_type_reauth;
        if (has_option(argv, argc, used, "-siem")) set->global_set = zpe_policy_type_siem;
        if (has_option(argv, argc, used, "-bypass")) set->global_set = zpe_policy_type_bypass;
        if (has_option(argv, argc, used, "-redirect")) set->global_set = zpe_policy_type_broker_redirect;
        if (has_option(argv, argc, used, "-vpn")) set->global_set = zpe_policy_type_global_vpn;
        set->sequence = get_option_int64(argv, argc, used, "-async", 0);
        set->customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
        set->scope_gid = get_option_int64(argv, argc, used, "-scope", 0);
        add_policy_set(set);
        if (!set->global_set) {
            ZPATH_LOG(AL_ERROR, "Line %d: Policy type not set", line);
            return ZPATH_RESULT_ERR;
        }
    } else if (strcasecmp(argv[1], "rule") == 0) {
        struct zpn_rule *rule = ZPE_CALLOC(sizeof(*rule));
        static int rule_order = 1;
        rule_order++;
        rule->condition_set_ids_count = arr_len;
        rule->condition_set_ids = arr;
        rule->oper = get_option_string(argv, argc, used, "-oper", "AND");
        rule->action_result = get_option_string(argv, argc, used, "-action_result", NULL);
        rule->rule_order = get_option_int64(argv, argc, used, "-rule_order", rule_order);
        rule->sequence = get_option_int64(argv, argc, used, "-async", 0);
        rule->custom_msg = get_option_string(argv, argc, used, "-custom_msg", NULL);
        rule->gid = get_option_int64(argv, argc, used, "-id", 0);
        add_rule(rule);
    } else if (strcasecmp(argv[1], "condition_set") == 0) {
        struct zpn_rule_condition_set *set = ZPE_CALLOC(sizeof(*set));
        set->operand_ids_count = arr_len;
        set->operand_ids = arr;
        set->oper = get_option_string(argv, argc, used, "-oper", "OR");
        set->negated = has_option(argv, argc, used, "-negate");
        set->sequence = get_option_int64(argv, argc, used, "-async", 0);
        set->id = get_option_int64(argv, argc, used, "-id", 0);
        add_condition_set(set);
    } else if (strcasecmp(argv[1], "operand") == 0) {
        struct zpn_rule_condition_operand *operand = ZPE_CALLOC(sizeof(*operand));
        if (arr_len) return ZPATH_RESULT_BAD_ARGUMENT;
        operand->lhs = get_option_string(argv, argc, used, "-lhs", NULL);
        operand->rhs = get_option_string(argv, argc, used, "-rhs", NULL);
        operand->object_type = get_option_string(argv, argc, used, "-object_type", NULL);
        operand->sequence = get_option_int64(argv, argc, used, "-async", 0);
        operand->id = get_option_int64(argv, argc, used, "-id", 0);
        add_operand(operand);
    } else if (strcasecmp(argv[1], "attribute") == 0) {
        struct zpn_saml_attrs *attribute = ZPE_CALLOC(sizeof(*attribute));
        if (arr_len) return ZPATH_RESULT_BAD_ARGUMENT;
        attribute->saml_name = get_option_string(argv, argc, used, "-saml_name", NULL);
        attribute->idp_gid = get_option_int64(argv, argc, used, "-idp_gid", 0);
        attribute->sequence = get_option_int64(argv, argc, used, "-async", 0);
        attribute->gid = get_option_int64(argv, argc, used, "-id", 0);
        add_attribute(attribute);
    } else {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    /* Verify all our options were used. */
    int broken = 0;
    used[0] = 1;
    used[1] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;
    return ZPATH_RESULT_NO_ERROR;
}

static int async_callback(void *response_callback_cookie,
                          struct wally_registrant *registrant,
                          struct wally_table *table,
                          int64_t request_id,
                          int row_count)
{
    return ZPATH_RESULT_NO_ERROR;
}

int hexbyte(char *data) {
    static uint8_t xlate[256] =
        {
         ['0'] = 0,
         ['1'] = 1,
         ['2'] = 2,
         ['3'] = 3,
         ['4'] = 4,
         ['5'] = 5,
         ['6'] = 6,
         ['7'] = 7,
         ['8'] = 8,
         ['9'] = 9,
         ['a'] = 10,
         ['A'] = 10,
         ['B'] = 11,
         ['b'] = 11,
         ['C'] = 12,
         ['c'] = 12,
         ['D'] = 13,
         ['d'] = 13,
         ['E'] = 14,
         ['e'] = 14,
         ['F'] = 15,
         ['f'] = 15
        };

    if (!data) return 0;
    if (data[0] && data[1]) {
        return xlate[(int)data[0]] << 4 | xlate[(int)data[1]];
    }
    return 0;
}

int load_policy_callback(void *response_callback_cookie,
                         struct wally_registrant *registrant,
                         struct wally_table *table,
                         int64_t request_id,
                         int row_count)
{
    ZPATH_LOG(AL_DEBUG, "Received load_policy_callback from line %ld", (long)request_id);
    return ZPATH_RESULT_NO_ERROR;
}

int process_load_policy_line(char **argv, int argc, int line)
{
    int64_t scope_gid;
    int res;

    if (argc != 4) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting 4 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (strcasecmp(argv[1], "-scope")) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting -scope", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    scope_gid = strtoul(argv[2], NULL, 0);
    if (!scope_gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Invalid scope_gid %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    res = zpe_load_policy(scope_gid, load_policy_callback, NULL, line, NULL, NULL);
    if (strcasecmp(argv[3], zpath_result_string(res))) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting result %s, got result %s", line, argv[3], zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_get_line(char **argv, int argc, int line)
{
    int64_t scope_gid;
    enum zpe_policy_type ptype;
    struct zpe_policy_built *policy_built = NULL;
    struct zpe_policy_set *set;
    int no_build = 0;
    int res;

    struct zpe_policy_built **p_policy_built;

    if (argc < 4) {
        ZPATH_LOG(AL_ERROR, "Line %d: Not enough arguments for 'add'", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (!strcasecmp(argv[0], "get_and_save")) {
        g_policy_built = NULL;
        p_policy_built = &g_policy_built;
    } else {
        p_policy_built = &policy_built;
    }

    scope_gid = strtoul(argv[1], NULL, 0);
    if (!scope_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting non-zero scope ID for argument: %s", line, argv[1]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (strcasecmp(argv[2], "access") == 0) {
        ptype = zpe_policy_type_access;
    } else if (strcasecmp(argv[2], "reauth") == 0) {
        ptype = zpe_policy_type_reauth;
    } else if (strcasecmp(argv[2], "siem") == 0) {
        ptype = zpe_policy_type_siem;
    } else if (strcasecmp(argv[2], "bypass") == 0) {
        ptype = zpe_policy_type_bypass;
    } else  if (strcasecmp(argv[2], "redirect") == 0) {
        ptype = zpe_policy_type_broker_redirect;
    } else if (strcasecmp(argv[2], "clientless-session-protection") == 0) {
        ptype = zpe_policy_type_csp_policy;
    } else if (strcasecmp(argv[2], "cred-map") == 0) {
        ptype = zpe_policy_type_cred_map;
    } else if (strcasecmp(argv[2], "priv-portal") == 0) {
        ptype = zpe_policy_type_priv_portal_policy;
    } else if (strcasecmp(argv[2], "vpn") == 0) {
        ptype = zpe_policy_type_global_vpn;
    } else {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting one of access|reauth|siem|redirect|clientless-session-protection|priv-portal|vpn, got %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if ((argc == 5 && !strcasecmp(argv[4], "nobuild")) || (argc == 6 && !strcasecmp(argv[5], "nobuild"))) {
        no_build = 1;
    }

    if (no_build) {
        res = zpe_get_policy_without_rebuild(scope_gid, ptype, p_policy_built, NULL, NULL);
    } else {
        res = zpe_get_current_policy(scope_gid, ptype, p_policy_built, async_callback, NULL, line, NULL, NULL, NULL, NULL);
    }
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
    }
    if (strcasecmp(zpath_result_string(res), argv[3])) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting result %s, got result %s", line, argv[3], zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }
    if (!using_test_wally && (res == ZPATH_RESULT_ASYNCHRONOUS)) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (res == ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Line %d: Have policy set of %d bytes, version=%"PRId64, line, (*p_policy_built)->policy_set->total_bytes, (*p_policy_built)->policy_version);
    }
    if (argc > 4 && strcasecmp(argv[4], "nobuild")) {
        /* Check real data */
        int expected_len;
        int i;
        void **addr;
        uint64_t *uints;
        static uint8_t  b_copy[10000000];
        uint64_t *w_copy = (uint64_t *)&(b_copy[0]);
        static char buf[10000000];
        char *s, *e;
        int length_error = 0;
        s = buf;
        e = s + sizeof(buf);

        if (argc > 5) {
            ZPATH_LOG(AL_ERROR, "Line %d: Too many arguments", line);
            return ZPATH_RESULT_ERR;
        }

        expected_len = strtoul(argv[4], NULL, 0);

        if (!(*p_policy_built) || !(*p_policy_built)->policy_set) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting set, don't have one", line);
            return ZPATH_RESULT_ERR;
        }

        set = (*p_policy_built)->policy_set;
        if (set->total_bytes != expected_len) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting %d bytes, got %d bytes", line, expected_len, (*p_policy_built)->policy_set->total_bytes);
            length_error = 1;
        }
        memcpy(b_copy, set, set->total_bytes);
        /* This is a bit special: We walk through the data, and
         * translate 8 byte addresses to offsets, by determining if
         * the 8-byte address is within the range of the original
         * data. */
        addr = (void **) set;
        uints = (uint64_t *) set;
        for (i = 0; i < set->total_bytes; i+=8) {
            if ((i % 16) == 0) s += sxprintf(s, e, "\n0x%08x: ", i);
            if (((char *)addr[i/8] >= (char *)set) &&
                ((char *)addr[i/8] < (((char *)set) + set->total_bytes))) {
                w_copy[i/8] = ((char *)addr[i/8]) - ((char *)set);
                s += sxprintf(s, e, OUTPUT_GRN "%02x %02x %02x %02x %02x %02x %02x %02x " OUTPUT_RST,
                              b_copy[i],
                              b_copy[i+1],
                              b_copy[i+2],
                              b_copy[i+3],
                              b_copy[i+4],
                              b_copy[i+5],
                              b_copy[i+6],
                              b_copy[i+7]);
            } else {
                w_copy[i/8] = uints[i/8];
                s += sxprintf(s, e, "%02x %02x %02x %02x %02x %02x %02x %02x ",
                              b_copy[i],
                              b_copy[i+1],
                              b_copy[i+2],
                              b_copy[i+3],
                              b_copy[i+4],
                              b_copy[i+5],
                              b_copy[i+6],
                              b_copy[i+7]);
            }
            if (i % 16) {
                s += sxprintf(s, e, "  #  ");
                int j;
                for (j = i - 8; j < i + 8; j++) {
                    if (isprint(b_copy[j])) {
                        s += sxprintf(s, e, "%c", b_copy[j]);
                    } else {
                        s += sxprintf(s, e, ".");
                    }
                }
            }
        }
        if (i % 16) {
            s += sxprintf(s, e, "                          #  ");
            int j;
            for (j = i - 8; j < i; j++) {
                if (isprint(b_copy[j])) {
                    s += sxprintf(s, e, "%c", b_copy[j]);
                } else {
                    s += sxprintf(s, e, ".");
                }
            }
        }
        ZPATH_LOG(AL_DEBUG, "Policy set::%s", buf);

        if (length_error) {
            return ZPATH_RESULT_ERR;
        }

        if (memcmp(b_copy, expected_data, expected_len)) {
            s = buf;
            e = s + sizeof(buf);
            for (i = 0; i < set->total_bytes; i++) {
                if ((i % 16) == 0) s += sxprintf(s, e, "\n0x%08x: ", i);

                if (b_copy[i] != expected_data[i]) {
                    s += sxprintf(s, e, OUTPUT_RED "%02x " OUTPUT_RST, expected_data[i]);
                } else {
                    s += sxprintf(s, e, "%02x ", expected_data[i]);
                }
                if ((i % 16) == 15) {
                    s += sxprintf(s, e, "  #  ");
                    int j;
                    for (j = i - 8; j < i + 8; j++) {
                        if (isprint(b_copy[j])) {
                            s += sxprintf(s, e, "%c", b_copy[j]);
                        } else {
                            s += sxprintf(s, e, ".");
                        }
                    }
                }
            }
            if (i % 16) {
                int j;
                for (j = i % 16; j < 16; j++) {
                    s += sxprintf(s, e, "   ");
                }
                s += sxprintf(s, e, "  #  ");
                for (j = 0; j < i % 16; j++) {
                    if (isprint(expected_data[((i | 0xf) - 0xf) + j])) {
                        s += sxprintf(s, e, "%c", expected_data[((i | 0xf) - 0xf) + j]);
                    } else {
                        s += sxprintf(s, e, ".");
                    }
                }
            }
            ZPATH_LOG(AL_ERROR, "Mismatch output:\n%s", buf);
            return ZPATH_RESULT_ERR;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_load_scope_line(char **argv, int argc, int line)
{
    int64_t customer_gid;
    int64_t count;
    int res;

    if (argc < 3 || argc > 4) {
        ZPATH_LOG(AL_ERROR, "Line %d: Bad arguments for 'load_scope'", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    customer_gid = strtoul(argv[1], NULL, 0);
    if (!customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting non-zero scope ID for argument: %s", line, argv[1]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    res = zse_load_scope(customer_gid, NULL, NULL, 0);
    if (strcasecmp(zpath_result_string(res), argv[2])) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting result %s, got result %s", line, argv[2], zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }

    if (argc == 4) {
        count = strtoul(argv[3], NULL, 0);
        int64_t count_got = get_attr_to_scope_counts(customer_gid);
        if(count != count_got) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting %ld counts, got result %ld", line, (long)count, (long)count_got);
            return ZPATH_RESULT_ERR;
        }
    }

    if (!using_test_wally && (res == ZPATH_RESULT_ASYNCHRONOUS)) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (res == ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Line %d: Have scope built", line);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int process_flush_line(char **argv, int argc, int line)
{
    int64_t gid;
    if (argc != 2) {
        ZPATH_LOG(AL_ERROR, "line %d: Flush is expecting exactly one argument", line);
        return ZPATH_RESULT_ERR;
    }
    gid = strtoul(argv[1], NULL, 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Flush gid cannot be zero: %s", line, argv[1]);
        return ZPATH_RESULT_ERR;
    }
    zpe_scope_flush(gid);
    return ZPATH_RESULT_NO_ERROR;
}


int process_read_data(char **argv, int argc, int line)
{
    uint64_t offset;
    int i;

    if (!argc) return ZPATH_RESULT_BAD_ARGUMENT;
    offset = strtoul(argv[0], NULL, 0);

    for (i = 1; i < argc; i++) {
        if ((i + offset - 1) >= sizeof(expected_data)) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad address- exceeds 1 MB %s", line, argv[0]);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        expected_data[i + offset - 1] = hexbyte(argv[i]);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_user_line(char **argv, int argc, int line)
{
    struct zhash_table *user;
    if (argc != 4) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expecting exactly 4 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    // Username = argv[1]
    if (strcasecmp(argv[2], "add") == 0) {
        user = zhash_table_lookup(users, argv[1], strlen(argv[1]), NULL);
        if (!user) {
            user = zhash_table_alloc(NULL);
            zhash_table_store(users, argv[1], strlen(argv[1]), 0, user);
        }
        if (zhash_table_lookup(user, argv[3], strlen(argv[3]), NULL)) {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s already has string %s", line, argv[1], argv[3]);
            return ZPATH_RESULT_ERR;
        }
        zhash_table_store(user, argv[3], strlen(argv[3]), 0, user);

        if (strstr(argv[3], "SAML") && zhash_table_lookup(saml_hash, argv[3], strlen(argv[3]), NULL)) {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s already has string %s", line, argv[1], argv[3]);
            return ZPATH_RESULT_ERR;
        }
        zhash_table_store(saml_hash, argv[3], strlen(argv[3]), 0, user);
    } else if (strcasecmp(argv[2], "remove") == 0) {
        void *data;
        user = zhash_table_lookup(users, argv[1], strlen(argv[1]), NULL);
        if (!user) {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s does not exist", line, argv[1]);
            return ZPATH_RESULT_ERR;
        }
        if ((data = zhash_table_lookup(user, argv[3], strlen(argv[3]), NULL))) {
            zhash_table_remove(user, argv[3], strlen(argv[3]), data);
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s, string %s does not exist", line, argv[1], argv[3]);
            return ZPATH_RESULT_ERR;
        }
        if ((data = zhash_table_lookup(saml_hash, argv[3], strlen(argv[3]), NULL))) {
            zhash_table_remove(saml_hash, argv[3], strlen(argv[3]), data);
        }

    } else {
        ZPATH_LOG(AL_ERROR, "Line %d: Bad argument '%s'", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_tickle_line(char **argv, int argc, int line)
{
    int64_t gid;
    int pretend = 0;
    int for_scope = 0;

    if (argc < 3 || argc > 4) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting 3 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (strcasecmp(argv[1], "-scope") == 0) {
        for_scope = 1;
    } else if (strcasecmp(argv[1], "-customer")) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    gid = strtoul(argv[2], NULL, 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Invalid gid %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (for_scope && argc == 4 && !strcasecmp(argv[3], "-pretend")) pretend = 1;
    if (for_scope) {
        if (pretend) {
            zpe_pretend_scope_tickle(gid);
        } else {
            zpe_scope_tickle(gid);
        }
    } else {
        zpe_customer_tickle(gid);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_tickled_line(char **argv, int argc, int line)
{
    int64_t gid;

    if (argc != 4) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting 4 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (strcasecmp(argv[1], "-scope")) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting -scope", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    gid = strtoul(argv[2], NULL, 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Invalid gid %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    int tickled = zpe_is_scope_tickled(gid);
    if (tolower(argv[3][0]) == 'y') {
        if (tickled) return ZPATH_RESULT_NO_ERROR;
        ZPATH_LOG(AL_ERROR, "Line %d: Customer %ld: Expecting customer to be tickled but is not", line, (long) gid);
        return ZPATH_RESULT_ERR;
    } else if (tolower(argv[3][0]) == 'n') {
        if (!tickled) return ZPATH_RESULT_NO_ERROR;
        ZPATH_LOG(AL_ERROR, "Line %d: Customer %ld: Expecting customer to not be tickled but it is", line, (long) gid);
        return ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting yes or no for last argument: %s", line, argv[3]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_ERR;
}

int process_build_outstanding_line(char **argv, int argc, int line)
{
    int64_t gid;

    if (argc != 4) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting 4 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (strcasecmp(argv[1], "-scope")) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting -scope", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    gid = strtoul(argv[2], NULL, 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Invalid gid %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    int outstanding = zpe_is_scope_build_outstanding(gid);
    if (tolower(argv[3][0]) == 'y') {
        if (outstanding) return ZPATH_RESULT_NO_ERROR;
        ZPATH_LOG(AL_ERROR, "Line %d: scope %ld: Expecting scope to be build outstanding but is not", line, (long) gid);
        return ZPATH_RESULT_ERR;
    } else if (tolower(argv[3][0]) == 'n') {
        if (!outstanding) return ZPATH_RESULT_NO_ERROR;
        ZPATH_LOG(AL_ERROR, "Line %d: scope %ld: Expecting scope to not be build outstanding but it is", line, (long) gid);
        return ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting yes or no for last argument: %s", line, argv[3]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_ERR;
}

int process_tickle_cancel_line(char **argv, int argc, int line)
{
    int64_t gid;
    int pretend = 0;

    if (argc < 3 || argc > 4) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting 3 arguments", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (strcasecmp(argv[1], "-scope")) {
        ZPATH_LOG(AL_ERROR, "line %d: Expecting -scope", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    gid = strtoul(argv[2], NULL, 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "line %d: Invalid gid %s", line, argv[2]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (argc == 4 && !strcasecmp(argv[3], "-pretend")) pretend = 1;

    if (pretend) {
        zpe_pretend_scope_cancel_tickle(gid);
    } else {
        zpe_scope_cancel_tickle(gid);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_check_build_line(char **argv, int argc, int line)
{
    int used[MAX_ARGS];
    int64_t fail = 0;
    int64_t scope_gid = 0;
    int64_t count = 0;
    enum zpe_policy_type policy_type = zpe_policy_type_deprecated;
    int res = ZPATH_RESULT_NO_ERROR;

    memset(used, 0, sizeof(used[0])*argc);

    scope_gid = get_option_int64(argv, argc, used, "-scope", 0);
    if (!scope_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Need -scope", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    if (has_option(argv, argc, used, "-access"))   policy_type = zpe_policy_type_access;
    if (has_option(argv, argc, used, "-reauth"))   policy_type = zpe_policy_type_reauth;
    if (has_option(argv, argc, used, "-siem"))     policy_type = zpe_policy_type_siem;
    if (has_option(argv, argc, used, "-bypass"))   policy_type = zpe_policy_type_bypass;
    if (has_option(argv, argc, used, "-redirect")) policy_type = zpe_policy_type_broker_redirect;
    if (has_option(argv, argc, used, "-vpn")) policy_type = zpe_policy_type_global_vpn;

    if (policy_type == zpe_policy_type_deprecated) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require -access -reauth -siem or -bypass or -redirect or -cred-map or -csp or -vpn", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    fail = get_option_int64(argv, argc, used, "-fail", -1);
    if (fail < 0 || fail > 1) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require -fail (0|1)", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    count = get_option_int64(argv, argc, used, "-count", -1);
    if (count < 0) {
        ZPATH_LOG(AL_ERROR, "Line %d: Need -count ", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    int64_t fail_count= 0;
    int build_fail = is_policy_build_failed(scope_gid, policy_type, &fail_count);
    if (build_fail != fail ) {
        ZPATH_LOG(AL_ERROR, "Line %d: expect fail %"PRId64" but get %d", line, fail, build_fail);
        res = ZPATH_RESULT_ERR;
        goto done;
    }
    if (count != fail_count) {
        ZPATH_LOG(AL_ERROR, "Line %d: except count %"PRId64" but get %"PRId64, line, count, fail_count);
        res = ZPATH_RESULT_ERR;
        goto done;
    }

done:
    return res;
}

int process_app_search_line(char **argv, int argc, int line)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t scope_gid = 0;
    int64_t app_gid = 0;
    int64_t app_expected = -1;
    int req_protocol = IPPROTO_TCP;
    int req_port = 80;
    char *req_app_name = NULL;
    int used[MAX_ARGS];
    memset(used, 0, sizeof(used[0])*argc);

    scope_gid = get_option_int64(argv, argc, used, "-scope", 0);
    if (!scope_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Need -scope", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    app_expected = get_option_int64(argv, argc, used, "-app", -1);
    if (app_expected < 0) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require expected app gid", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    req_app_name = get_option_string(argv, argc, used, "-req_app", NULL);
    if (!req_app_name) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require application req", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    if (has_option(argv, argc, used, "-udp")) req_protocol = IPPROTO_UDP;
    if (has_option(argv, argc, used, "-icmp")) req_protocol = IPPROTO_ICMP;

    if (req_protocol != IPPROTO_ICMP) {
        char *req_app_port;
        if ((req_app_port = strstr(req_app_name, ":")) == NULL) {
            ZPATH_LOG(AL_ERROR, "Line %d: Specify app port", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto done;
        } else {
            req_port = (int)strtoul(req_app_port + 1, NULL, 0);
            *(req_app_port) = '\0';
        }
    }

    struct zpn_application *app = NULL;
    zpn_application_get_by_gid_immediate_test(app_expected,
                                                    &app);

    app_gid = zpn_application_domain_search(scope_gid, req_app_name, strnlen(req_app_name, MAX_DOMAIN_LEN_SIZE), NULL, NULL,
                                            NULL, req_protocol, req_port, zpn_app_search_none, 0 /* TODO: handle client type */);

    if (app_gid != app_expected) {
        ZPATH_LOG(AL_ERROR, "Line %d: expect to match app: %"PRId64", but %"PRId64" is matched instead", line, app_expected, app_gid);
        res = ZPATH_RESULT_ERR;
        goto done;
    }

done:
    ZPN_FREE(req_app_name);

    return res;

}

int process_eval_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int i;
    int64_t app_gid = 0;
    int64_t expected_rule_gid;
    int64_t evaluated_rule_gid;
    struct zpe_policy_built *policy_built = NULL;
    int newpolicy = 0;
    int no_build = 0;
    int negative = 0;
    int app_req = 0;
    int no_get = 0;
    int eval_rule_count = -1;
    int res = ZPATH_RESULT_NO_ERROR;
    int used[MAX_ARGS];
    char *username = NULL;
    char *attr = NULL;
    char *req_app_name = NULL;
    struct zhash_table *int_table = NULL;
    int64_t app_gids[ZPN_MAX_APP_GIDS];
    size_t app_gids_count_max = sizeof(app_gids) / sizeof(app_gids[0]);
    size_t app_gids_count = app_gids_count_max;

    int64_t app_groups[ZPN_MAX_APP_GIDS];
    size_t app_groups_count_max = sizeof(app_groups) / sizeof(app_groups[0]);
    size_t app_groups_count = app_groups_count_max;

    int64_t most_specific_app_gid = 0;
    int     is_wildcard = 0;

    memset(used, 0, sizeof(used[0])*argc);
    int64_t customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
    if (!customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Need -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    int64_t scope_gid = get_option_int64(argv, argc, used, "-scope", 0);
    if (!scope_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Need -scope", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    enum zpe_policy_type global_set = 0;
    if (has_option(argv, argc, used, "-csp")) {
        global_set = zpe_policy_type_csp_policy;
        g_policy_eval_type = zpe_eval_new;
    }
    if (has_option(argv, argc, used, "-access")) global_set = zpe_policy_type_access;
    if (has_option(argv, argc, used, "-reauth")) global_set = zpe_policy_type_reauth;
    if (has_option(argv, argc, used, "-siem")) global_set = zpe_policy_type_siem;
    if (has_option(argv, argc, used, "-bypass")) global_set = zpe_policy_type_bypass;
    if (has_option(argv, argc, used, "-redirect")) global_set = zpe_policy_type_broker_redirect;
    if (has_option(argv, argc, used, "-vpn")) global_set = zpe_policy_type_global_vpn;
    if (has_option(argv, argc, used, "-nobuild")) no_build = 1;
    if (has_option(argv, argc, used, "-negative")) negative = 1;
    if (has_option(argv, argc, used, "-req_app")) app_req = 1;
    if (has_option(argv, argc, used, "-cred-map")) {
        global_set = zpe_policy_type_cred_map;
        g_policy_eval_type = zpe_eval_new;
    }
    if (has_option(argv, argc, used, "-priv-portal")) {
        global_set = zpe_policy_type_priv_portal_policy;
        g_policy_eval_type = zpe_eval_both;
    }
    if (!global_set) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require -access -reauth -siem or -bypass or -redirect or -cred-map or -csp or -priv-portal or -vpn", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    if (has_option(argv, argc, used, "-noget")) no_get = 1;

    if (no_build && no_get) {
        ZPATH_LOG(AL_ERROR, "Line %d: cannot have both \"-noget\" and \"-nobuild\"", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    if (has_option(argv, argc, used, "-newpolicy")) {
        if (policy_eval_comp) {
            newpolicy = 1;
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: POLICY_EVALUATION_COMPARE must be on to set \"-newpolicy\"", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto done;
        }
    }

    username = get_option_string(argv, argc, used, "-user", NULL);
    if (!username) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require username", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    /*  attr_name|attr_value */
    attr = get_option_string(argv, argc, used, "-attr", NULL);

    int64_t app_to_evaluate = get_option_int64(argv, argc, used, "-app", 0);

    struct zhash_table *string_table = zhash_table_lookup(users, username, strlen(username), NULL);
    if (!string_table) {
        ZPATH_LOG(AL_ERROR, "Line %d: Username %s not found", line, username);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    int_table = zhash_table_alloc(NULL);
    for (i = 0; i < (argc - 1); i++) {
        if (strcasecmp(argv[i], "-gid") == 0) {
            app_gid = strtoul(argv[i + 1], NULL, 0);
            if (!app_gid) {
                ZPATH_LOG(AL_ERROR, "Line %d: Invalid GID %s", line, argv[i + 1]);
                res = ZPATH_RESULT_ERR;
                goto done;
            }
            zhash_table_store(int_table, &app_gid, sizeof(app_gid), 0, int_table);
            used[i] = 1;
            used[i + 1] = 1;
        }
    }

    expected_rule_gid = get_option_int64(argv, argc, used, "-verify-rule", -1);
    if (expected_rule_gid == -1) {
        ZPATH_LOG(AL_ERROR, "Line %d: Require -verify-rule", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto done;
    }

    eval_rule_count = get_option_int64(argv, argc, used, "-eval_rule_count", -1);

    struct zpn_application *app = NULL;
    res = zpn_application_get_by_gid_immediate_test(app_to_evaluate, &app);

    if (app && !is_app_owned_or_shared(app, &scope_gid, attr, 0, 0)) {
        ZPATH_LOG(AL_ERROR, "Line %d: App GID = %ld is not owned by or shared to scope %ld",
                            line, (long)app_to_evaluate, (long)scope_gid);
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (app_to_evaluate) {
        app_gids[0] = app_to_evaluate;
        app_gids_count = 1;
    }

    if (app && app_req) {
        int req_port = 0;
        int req_protocol = IPPROTO_TCP;
        char *req_app_port;
        app_gids_count = app_gids_count_max;
        req_app_name = get_option_string(argv, argc, used, "-req_app", NULL);
        if (!req_app_name) {
            ZPATH_LOG(AL_ERROR, "Line %d: Require application req", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto done;
        }

        if (has_option(argv, argc, used, "-udp")) req_protocol = IPPROTO_UDP;
        if (has_option(argv, argc, used, "-icmp")) req_protocol = IPPROTO_ICMP;

        if (req_protocol != IPPROTO_ICMP) {
            if ((req_app_port = strstr(req_app_name, ":")) == NULL) {
                ZPATH_LOG(AL_ERROR, "Line %d: Specify app port", line);
                res = ZPATH_RESULT_BAD_ARGUMENT;
                goto done;
            } else {
                req_port = (int)strtoul(req_app_port + 1, NULL, 0);
                *(req_app_port) = '\0';
            }
        }

        if (zpath_match_style_string_to_enum(app->match_style) == zpn_match_style_inclusive) {
            /* First pull out everything for the requested domain */
            res = zpn_application_search_all(scope_gid,
                                             req_app_name,
                                             strnlen(req_app_name, MAX_DOMAIN_LEN_SIZE),
                                             req_protocol,
                                             req_port,
                                             0, // Don't filter by assistant.
                                             NULL,
                                             app_gids,
                                             &app_gids_count,
                                             app_groups,
                                             &app_groups_count,
                                             &is_wildcard,
                                             &most_specific_app_gid,
                                             NULL,
                                             NULL,
                                             0);
            if (res && (res != ZPATH_RESULT_NOT_FOUND)) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    ZPATH_LOG(AL_ERROR, "Line %d: operation was asynchronous, evaluator does not test this.", line);
                } else {
                    ZPATH_LOG(AL_ERROR, "Line %d: Application Search returned %s", line, zpath_result_string(res));
                }
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        } else {
            app_gids[0] = zpn_application_domain_search(scope_gid, req_app_name, strnlen(req_app_name, MAX_DOMAIN_LEN_SIZE), NULL, NULL,
                                                        NULL, req_protocol, req_port, zpn_app_search_none, 0 /* TODO: handle client type */);

            if (app_gids[0]) {
                /* Get application group relation for app. */
                res = zpn_application_group_application_mapping_get_group_by_app_id(app_gids[0],
                                                                                    &(app_groups[0]),
                                                                                    &app_groups_count,
                                                                                    NULL,
                                                                                    NULL,
                                                                                    0);
                if (res && (res != ZPATH_RESULT_NOT_FOUND)) {
                    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                        ZPATH_LOG(AL_ERROR, "Line %d: operation was asynchronous, evaluator does not test this.", line);
                    } else {
                        ZPATH_LOG(AL_ERROR, "Line %d: Application group mapping returned %s", line, zpath_result_string(res));
                    }
                    res = ZPATH_RESULT_ERR;
                    goto done;
                }

                app_gids_count = 1;
                most_specific_app_gid = app_gids[0];
            } else {
                app_gids_count = 0;
                app_groups_count = 0;
            }
        }

        if ((expected_rule_gid != 0) &&
            (app_to_evaluate != most_specific_app_gid)) {
            ZPATH_LOG(AL_ERROR, "Line %d: Evaluation expected to find app gid for connector selection = %"PRId64", found connector app gid = %"PRId64"",
                      line, app_to_evaluate, most_specific_app_gid);
            res = ZPATH_RESULT_ERR;
            goto done;
        }

        app_to_evaluate = app_gids[0];
        for (i = 0; i < app_gids_count; i++) {
            zhash_table_store(int_table, &(app_gids[i]), sizeof(app_gids[i]), 1, int_table);
        }
        for (i = 0; i < app_groups_count; i++) {
            zhash_table_store(int_table, &(app_groups[i]), sizeof(app_groups[i]), 1, int_table);
        }
    }

    res = ZPATH_RESULT_NO_ERROR;
    if (no_get) {
        policy_built = g_policy_built;
        if (!policy_built) res = ZPATH_RESULT_ERR;
    } else {
        if (no_build) {
            res = zpe_get_policy_without_rebuild(scope_gid,
                                                 global_set,
                                                 &policy_built,
                                                 NULL,
                                                 NULL);
        } else {
            res = zpe_get_current_policy(scope_gid,
                                         global_set,
                                         &policy_built,
                                         NULL,
                                         NULL,
                                         0,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL);
        }
    }
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            ZPATH_LOG(AL_ERROR, "Line %d: operation was asynchronous, evaluator does not test this. Use 'GET' to validate asynchronous policy building", line);
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: Get policy returned %s", line, zpath_result_string(res));
        }
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    int64_t before = epoch_us();
    int rules_evaluated;
    static int64_t total_time_elapsed = 0;
    static int64_t total_rules_evaluated = 0;
#define LOOPS 1l
    for (i = 0; i < LOOPS; i++) {
        if (global_set == zpe_policy_type_broker_redirect){
            res = zpe_evaluate_scope(NULL,
                                     0,
                                     policy_built,
                                     policy_built,
                                     string_table,
                                     NULL,
                                     NULL,
                                     NULL,
                                     "Redirect policies",
                                     &rules_evaluated,
                                     &evaluated_rule_gid,
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     (customer_gid==scope_gid?1:0));
        } else{
            res = zpe_evaluate(0,
                           0,
                           app_gids,
                           app_gids_count,
                           policy_built,
                           string_table,
                           NULL,
                           NULL,
                           int_table,
                           username,
                           &rules_evaluated,
                           &evaluated_rule_gid,
                           NULL,
                           NULL,
                           NULL,
                           NULL,
                           NULL);
        }
    }
    int64_t after = epoch_us();
    if (res) {
        if (res == ZPATH_RESULT_NOT_FOUND) {
            if (expected_rule_gid == 0) {
                /* This is correct. */
                res = ZPATH_RESULT_NO_ERROR;
            } else {
                ZPATH_LOG(AL_ERROR, "Line %d: Evaluation expected to find rule gid = 0x%lx, found none", line, (long) expected_rule_gid);
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        } else {
            /* If negative is set, we expect opposite expectation as no_error */
            /* If newpolicy is set and there is a new policy being built or has beeb built, we consider a mismatch as no error */
            if ((negative && ((res == ZPATH_RESULT_BAD_DATA) || (!newpolicy && res == ZPATH_RESULT_BAD_STATE)))
                   || (!negative && newpolicy && res == ZPATH_RESULT_BAD_STATE)) {
                res = ZPATH_RESULT_NO_ERROR;
            } else {
                ZPATH_LOG(AL_ERROR, "Line %d: Evaluation failed, newpolicy=%d, returned %s.", line, newpolicy, zpath_result_string(res));
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        }
    } else {
        if (expected_rule_gid == evaluated_rule_gid) {
            if (negative) {
                ZPATH_LOG(AL_ERROR, "Line %d: Evaluation not expected to find rule gid = 0x%lx, found rule gid = 0x%lx", line, (long) expected_rule_gid, (long) evaluated_rule_gid);
                res = ZPATH_RESULT_ERR;
                goto done;
            } else {
                ZPATH_LOG(AL_INFO, "Test Succesfull: %"PRId64"", evaluated_rule_gid);
                /* This is correct. */
                res = ZPATH_RESULT_NO_ERROR;
            }
            if (global_set == zpe_policy_type_cred_map) {
                struct zpn_credential_rule_mapping *console_credential_rule_mapping[1];
                res = zpn_credential_rule_mapping_get_by_zpn_rule_gid_immediate(evaluated_rule_gid,
                        console_credential_rule_mapping);
                if (res == ZPATH_RESULT_NO_ERROR) {
                    struct zpn_credentials *credentials[1];
                    ZPATH_LOG(AL_DEBUG, "console_credential_rule_mapping entry is found for zpn_rule_gid %"PRId64""
                            " id %"PRId64" credential_id %"PRId64"", evaluated_rule_gid, console_credential_rule_mapping[0]->id, console_credential_rule_mapping[0]->credential_id);
                    res = zpn_credentials_get_by_id_immediate(console_credential_rule_mapping[0]->credential_id, scope_gid , credentials);
                    if (res == ZPATH_RESULT_NO_ERROR) {
                        ZPATH_LOG(AL_DEBUG, "Credential ID %"PRId64" is found", credentials[0]->id);
                    } else {
                        res = ZPATH_RESULT_ERR;
                        goto done;
                    }
                } else {
                    res = ZPATH_RESULT_ERR;
                    goto done;
                }
            }
            /* This is correct. */
        } else {
            if (!negative) {
                ZPATH_LOG(AL_ERROR, "Line %d: Evaluation expected to find rule gid = 0x%lx, found rule gid = 0x%lx", line, (long) expected_rule_gid, (long) evaluated_rule_gid);
                res = ZPATH_RESULT_ERR;
                goto done;
            } else {
                /* This is correct. because negative is set and we are expecting the opposite of rule_gid not match */
                res = ZPATH_RESULT_NO_ERROR;
            }
        }
    }

    if (eval_rule_count >= 0) {
        if (eval_rule_count == rules_evaluated) {
            res = ZPATH_RESULT_NO_ERROR;
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: Expect %d rules evaluated, but %d found instead", line, eval_rule_count, rules_evaluated);
            res = ZPATH_RESULT_ERR;
            goto done;
        }
    }

    int64_t total_evaluations = (int64_t)rules_evaluated * LOOPS;
    total_rules_evaluated += total_evaluations;
    total_time_elapsed += (after - before);
    ZPATH_LOG(AL_NOTICE, "Line %d: Performed %ld evaluations in %7.6fs, or %6.3f evaluations/s, total %ld evaluations in %5.3fs, or %6.3f evaluations/s",
              line,
              (long) total_evaluations,
              ((double)(after - before) / 1000000.0),
              (double)total_evaluations/(((double)(after - before) / 1000000.0)),
              (long) total_rules_evaluated,
              ((double)(total_time_elapsed) / 1000000.0),
              (double)total_rules_evaluated/(((double)(total_time_elapsed) / 1000000.0))
              );

done:
    zhash_table_free(int_table);
    ZPN_FREE(username);
    ZPN_FREE(attr);
    ZPN_FREE(req_app_name);

    return res;
}

static int client_app_callback(struct argo_object *app,
                               char *char_cookie,
                               int64_t int_cookie)
{
    char* username_str = char_cookie;
    struct user_app_state *app_state;
    struct argo_object *old_object;
    struct zpn_client_app *capp = app->base_structure_void;

    char key[1000];

    (void) int_cookie;
    app_state = zhash_table_lookup(user_apps, username_str, strlen(username_str), NULL);
    if (!app_state) {
        ZPATH_LOG(AL_NOTICE, "user_app_state not found in hash table for username %s", username_str);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (capp->app_gid) {
        snprintf(key, sizeof(key), "%ld:%s", (long)capp->app_gid, capp->app_domain);
    } else {
        snprintf(key, sizeof(key), "%s", capp->app_domain);
    }

    char dump[8000];
    if (argo_object_dump(app, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
    } else {
        dump[0] = 0;
    }

    old_object = zhash_table_lookup(app_state->domains, key, strlen(key), NULL);
    if (old_object) {
        if (capp->deleted) {
            ZPATH_LOG(AL_DEBUG, "%s: Removing domain " OUTPUT_YEL "%s: %s" OUTPUT_RST, app_state->username, key, dump);
        }
        zhash_table_remove(app_state->domains, key, strlen(key), old_object);
        /* do not release the object here, it will be released in client_app_rx() zpn_broker_client_apps.c */
        /*argo_object_release(old_object);*/
        if (!capp->deleted) {
            ZPATH_LOG(AL_DEBUG, "%s: Updating domain " OUTPUT_PRP "%s: %s" OUTPUT_RST, app_state->username, key, dump);
            argo_object_hold(app);
            zhash_table_store(app_state->domains, key, strlen(key), 0, app);
        }
    } else {
        if (capp->deleted) {
            ZPATH_LOG(AL_ERROR, "%s: Ignoring domain (was deleted, still deleted) %s: %s", app_state->username, key, dump);
        } else {
            ZPATH_LOG(AL_DEBUG, "%s: Adding domain " OUTPUT_BLU "%s: %s" OUTPUT_RST, app_state->username, key, dump);
            argo_object_hold(app);
            zhash_table_store(app_state->domains, key, strlen(key), 0, app);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int client_app_complete_callback(char *char_cookie,
                                        int64_t int_cookie)
{
    char* username_str = char_cookie;

    (void) int_cookie;

    ZPATH_LOG(AL_DEBUG, "%s: Client app complete callback", username_str);
    zhash_table_store(user_complete, username_str, strlen(username_str), 0, user_complete);
    return ZPATH_RESULT_NO_ERROR;
}

static int dump_domain_walk(void *cookie,
                            void *object,
                            void *key,
                            size_t key_len)
{
    char *username = cookie;
    struct argo_object *app = object;
    char dump[8000];

    if (argo_object_dump(app, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
    } else {
        dump[0] = 0;
    }

    ZPATH_LOG(AL_DEBUG, "Dump User " OUTPUT_LBL "%s: Domain %.*s: %s" OUTPUT_RST, username, (int) key_len, (char *)key, dump);
    return ZPATH_RESULT_NO_ERROR;
}


int process_client_app_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];

    memset(used, 0, sizeof(used[0])*argc);

    if (argc < 4) {
        ZPATH_LOG(AL_ERROR, "Line %d: Expected more args", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    int64_t customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
    int64_t scope_gid = get_option_int64(argv, argc, used, "-scope", 0);

    if (strcasecmp(argv[1], "start") == 0) {
        struct zpn_broker_client_fohh_state *faked_c_state = ZPN_CALLOC(sizeof(struct zpn_broker_client_fohh_state));
        char *platform;
        char *username;
        struct zhash_table *user_string_hash = NULL;
        int platform_type = zpn_platform_type_windows;
        int has_segment = has_option(argv, argc, used, "-segment");
        int no_ip_download = has_option(argv, argc, used, "-no_ip_download");
        int no_domain_download = has_option(argv, argc, used, "-no_domain_download");
        int64_t client_type = zpn_client_type_zapp;
        char *attr_name;
        char *attr_val;

        client_type = get_option_int64(argv, argc, used, "-client_type", zpn_client_type_zapp);
        attr_name = get_option_string(argv, argc, used, "-attr_name", NULL);
        attr_val = get_option_string(argv, argc, used, "-attr_val", NULL);

        username = get_option_string(argv, argc, used, "-user", NULL);
        if (!customer_gid || !username ) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expected -user and -customer", line);
            if (attr_name) ZPE_FREE(attr_name);
            if (attr_val) ZPE_FREE(attr_val);
            if (faked_c_state) ZPE_FREE(faked_c_state);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        snprintf(current_username, sizeof(current_username), "%s", username);

        platform = get_option_string(argv, argc, used, "-platform", NULL);
        if (platform) {
            if (strcmp(platform, "ios") == 0) {
                platform_type = zpn_platform_type_ios;
            } else if (strcmp(platform, "android") == 0) {
                platform_type = zpn_platform_type_android;
            } else if (strcmp(platform, "windows") == 0) {
                platform_type = zpn_platform_type_windows;
            } else if (strcmp(platform, "linux") == 0) {
                platform_type = zpn_platform_type_linux;
            } else if (strcmp(platform, "mac") == 0) {
                platform_type = zpn_platform_type_mac;
            }
            ZPE_FREE(platform);
        }
        current_platform = platform_type;

        if (!is_dta_test() && !scope_gid ) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expected -scope", line);
            if (attr_name) ZPE_FREE(attr_name);
            if (attr_val) ZPE_FREE(attr_val);
            if (faked_c_state) ZPE_FREE(faked_c_state);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        if (is_dta_test()) {
            if (!attr_name || !attr_val) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expected -attr_name and -attr_val", line);
                if (attr_name) ZPE_FREE(attr_name);
                if (attr_val) ZPE_FREE(attr_val);
                if (faked_c_state) ZPE_FREE(faked_c_state);
                return ZPATH_RESULT_BAD_ARGUMENT;
            } else {
                scope_gid = zpn_get_scope_by_attr(customer_gid, attr_name, attr_val, faked_c_state->attr,  sizeof(faked_c_state->attr));
            }
        }
        if (attr_name) ZPE_FREE(attr_name);
        if (attr_val) ZPE_FREE(attr_val);

        user_string_hash = zhash_table_lookup(users, argv[1], strlen(argv[1]), NULL);
        if (!user_string_hash) {
            user_string_hash = zhash_table_alloc(NULL);
            zhash_table_store(users, username, strlen(username), 0, user_string_hash);
        }

        struct user_app_state *u_state;
        u_state = zhash_table_lookup(user_apps, username, strlen(username), NULL);
        if (u_state) {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s already started", line, username);
            if (faked_c_state) ZPE_FREE(faked_c_state);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        u_state = ZPE_CALLOC(sizeof(*u_state));
        u_state->username = ZPE_STRDUP(username, strlen(username));
        u_state->domains = zhash_table_alloc(NULL);
        u_state->app_stats = NULL;

        zhash_table_store(user_apps, username, strlen(username), 0, u_state);
        snprintf(faked_c_state->tunnel_id, sizeof(faked_c_state->tunnel_id), "%s", username);
        faked_c_state->customer_gid = customer_gid;
        faked_c_state->scope_gid = scope_gid;
        faked_c_state->general_context_hash = user_string_hash;
        faked_c_state->saml_hash = saml_hash;
        faked_c_state->scim_hash = NULL;
        faked_c_state->app_stats = u_state->app_stats;
        faked_c_state->client_type = client_type;
        faked_c_state->client_aux_id = 0;
        faked_c_state->incarnation = 0;
        faked_c_state->capability_no_ip_download = no_ip_download;
        faked_c_state->capability_no_domain_download = no_domain_download;
        faked_c_state->platform_type = platform_type;

        res = zpn_broker_client_apps_start(faked_c_state,
                                           has_segment,
                                           client_app_callback,
                                           client_app_complete_callback);

        if (res != ZPATH_RESULT_ASYNCHRONOUS) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting asynchronous, got %s", line, zpn_result_string(res));
            return ZPATH_RESULT_ERR;
        }
        u_state->app_state = faked_c_state->app_state; /* This is client (struct zpn_client_app_state) */
    } else if (strcasecmp(argv[1], "update") == 0) {
        ZPATH_LOG(AL_ERROR, "Line %d: Implement me", line);
        return ZPATH_RESULT_ERR;
    } else if (strcasecmp(argv[1], "done") == 0) {
        ZPATH_LOG(AL_ERROR, "Line %d: Implement me", line);
        return ZPATH_RESULT_ERR;
    } else if (strcasecmp(argv[1], "refresh") == 0) {
        ZPATH_LOG(AL_ERROR, "Line %d: Implement me", line);
        return ZPATH_RESULT_ERR;
    } else if (strcasecmp(argv[1], "check") == 0) {
        char *username_str = get_option_string(argv, argc, used, "-user", NULL);
        char *domain_str = get_option_string(argv, argc, used, "-domain", NULL);
        /* app_gid, if set, indicates that this client was expecting
         * segment based downloads */
        int64_t app_gid = get_option_int64(argv, argc, used, "-app_gid", 0);
        char *proto_str = get_option_string(argv, argc, used, "-proto", NULL);
        char *port_str = get_option_string(argv, argc, used, "-port", NULL);
        char *action_str = get_option_string(argv, argc, used, "-action", NULL);
        int64_t ip_anchored = get_option_int64(argv, argc, used, "-ip_anchored", 0);
        int64_t inspected = get_option_int64(argv, argc, used, "-inspect_traffic_with_zia", 0);
        int64_t bypass = get_option_int64(argv, argc, used, "-bypass", -1);
        char *double_encrypt_str = get_option_string(argv, argc, used, "-double_encrypt", "no");

        int64_t match_style = get_option_int64(argv, argc, used, "-match_style", -1);
        //int64_t netcount = get_option_int64(argv, argc ,used, "-netcount", 0);

        if (inspected) {
            ZPATH_LOG(AL_ERROR, "Line %d: Inspected is set", line);
        }

        if (!username_str ||
            !domain_str ||
            !action_str) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad args", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        struct user_app_state *app_state = zhash_table_lookup(user_apps, username_str, strlen(username_str), NULL);
        if (!app_state) {
            ZPATH_LOG(AL_ERROR, "Line %d: Could not find user %s", line, username_str);
            return ZPATH_RESULT_ERR;
        }

        struct argo_object *app_object;
        if (!app_gid) {
            app_object = zhash_table_lookup(app_state->domains, domain_str, strlen(domain_str), NULL);
            if (max_app_download_embedded_feature && !app_object && strcasecmp(action_str, "check_found") == 0) {
                total_app_access++;
                ZPN_LOG(AL_NOTICE, "Embedded feature is enabled and Did not found domain %s", domain_str);
                not_found_app_download_embedded++;
                return ZPATH_RESULT_NO_ERROR;
            }
            if (!app_object &&
                strcasecmp(action_str, "not_found")) {
                ZPATH_LOG(AL_ERROR, "Line %d: For %s, Domain %s not found, and was expecting %s", line, app_state->username, domain_str, action_str);
                return ZPATH_RESULT_ERR;
            }
        } else {
            char str[1000];
            snprintf(str, sizeof(str), "%ld:%s", (long)app_gid, domain_str);
            app_object = zhash_table_lookup(app_state->domains, str, strlen(str), NULL);
            if (!app_object &&
                strcasecmp(action_str, "not_found")) {
                ZPATH_LOG(AL_ERROR, "Line %d: For %s, app_gid %s not found, and was expecting %s", line, app_state->username, str, action_str);
                return ZPATH_RESULT_ERR;
            }
        }

        if (!app_object) {
            /* Was supposed to be not found... */
            return ZPATH_RESULT_NO_ERROR;
        }

        if (!proto_str ||
            !port_str) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad args", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        int port;
        int proto = 0;

        if (strcasecmp(proto_str, "tcp") == 0) proto = 6;
        if (strcasecmp(proto_str, "udp") == 0) proto = 17;
        if ((proto != 6) && (proto != 17)) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad proto %s", line, proto_str);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        port = strtol(port_str, NULL, 0);
        if ((port <= 0) || (port > 65535)) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad port %s", line, port_str);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        /* Handle domain based download */
        struct zpn_client_app *client_app = app_object->base_structure_void;
        int hit = 0;
        if ((strcasecmp(client_app->bypass_type, "NEVER") == 0) ||
            (strcasecmp(client_app->bypass_type, "ON_NET") == 0) ||
            (strcasecmp(client_app->bypass_type, "OFF_NET") == 0)) {
            if (proto == 6) {
                int i;
                for (i = 0; (i + 1) < client_app->tcp_port_ranges_count; i+=2) {
                    if ((port >= client_app->tcp_port_ranges[i]) &&
                        (port <= client_app->tcp_port_ranges[i + 1])) {
                        /* Found! */
                        hit = 1;
                    }
                }
            } else {
                int i;
                for (i = 0; (i + 1) < client_app->udp_port_ranges_count; i+=2) {
                    if ((port >= client_app->udp_port_ranges[i]) &&
                        (port <= client_app->udp_port_ranges[i + 1])) {
                        /* Found! */
                        hit = 1;
                    }
                }
            }
            if (strcasecmp(action_str, "check_found") == 0) {
                if (!max_app_download_embedded_feature) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Restrict app download feature is not enabled", line);
                    return ZPATH_RESULT_ERR;
                }
                total_app_access++;
                if (hit) {
                    found_app_download_embedded++;
                    ZPATH_LOG(AL_NOTICE, "Line %d: Found domain %s", line, client_app->app_domain);
                    return ZPATH_RESULT_NO_ERROR;
                } else {
                    not_found_app_download_embedded++;
                    ZPATH_LOG(AL_NOTICE, "Line %d: Not found domain %s", line, client_app->app_domain);
                    return ZPATH_RESULT_NO_ERROR;
                }
            } else if (strcasecmp(action_str, "not_found") == 0) {
                if (hit) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Expecting not found, but got a hit", line);
                    return ZPATH_RESULT_ERR;
                }
            } else {
                if (!hit) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Expecting found, but was not, action='%s'", line, action_str);
                    return ZPATH_RESULT_ERR;
                }
                if (strcasecmp(client_app->bypass_type, action_str)) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Expecting bypass type '%s', got bypass type %s", line, action_str, client_app->bypass_type);
                    return ZPATH_RESULT_ERR;
                }
            }
        } else if (strcasecmp(client_app->bypass_type, "ALWAYS") == 0) {
            if (strcasecmp(action_str, "bypass") == 0) {
                /* Fine... */
            } else {
                /* Bad... */
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting bypass type %s, got bypass type %s", line, action_str, client_app->bypass_type);
                return ZPATH_RESULT_ERR;
            }
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: Received unexpected bypass type %s", line, client_app->bypass_type);
            return ZPATH_RESULT_ERR;
        }
        if (hit) {
            if (ip_anchored != client_app->ip_anchored) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting ip_anchored=%d, got %d", line, (int)ip_anchored, (int)client_app->ip_anchored);
                return ZPATH_RESULT_ERR;
            }

            if(inspected != client_app->inspected) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting inspected=%d, got %d", line, (int) inspected, (int)client_app->inspected);
                return ZPATH_RESULT_ERR;
            }

            if (bypass >=0 && bypass != client_app->bypass) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting bypass=%d, got %d", line, (int)bypass, (int)client_app->bypass);
                return ZPATH_RESULT_ERR;
            }
            if (strcasecmp(double_encrypt_str, "no") == 0) {
                if (client_app->double_encrypt) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Expecting double encrypt %s, got yes", line, double_encrypt_str);
                    return ZPATH_RESULT_ERR;
                }
            } else if (strcasecmp(double_encrypt_str, "yes") == 0) {
                if (!client_app->double_encrypt) {
                    ZPATH_LOG(AL_ERROR, "Line %d: Expecting double encrypt %s, got no", line, double_encrypt_str);
                    return ZPATH_RESULT_ERR;
                }
            }
            if(match_style >=0 && match_style != client_app->match_style) {
                ZPATH_LOG(AL_ERROR, "Line %d: Expecting match_style=%d, got %d", line, (int)match_style, (int)client_app->match_style);
                return ZPATH_RESULT_ERR;
            }
        }
        return ZPATH_RESULT_NO_ERROR;
    } else if (strcasecmp(argv[1], "complete") == 0) {
        char *username;
        void *exists;
        username = get_option_string(argv, argc, used, "-user", NULL);

        if (!username) {
            ZPATH_LOG(AL_ERROR, "Line %d: need -user", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }

        exists = zhash_table_lookup(user_complete, username, strlen(username), NULL);
        if (!exists) {
            ZPATH_LOG(AL_ERROR, "Line %d: user %s not complete", line, username);
            return ZPATH_RESULT_ERR;
        }
        return ZPATH_RESULT_NO_ERROR;
    } else if (strcasecmp(argv[1], "timer") == 0) {
        int64_t customer_gid;
        char *fired;
        int check_fired = 0;
        int has_fired = 0;
        customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
        fired = get_option_string(argv, argc, used, "-fired", NULL);
        if (!customer_gid || !fired) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad arguments", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        if (strcasecmp(fired, "true") == 0) {
            check_fired = 1;
        } else if (strcasecmp(fired, "false") == 0) {
            check_fired = 0;
        } else if (strcasecmp(fired, "any") == 0) {
            /* Just clear the state... */
            ZPATH_LOG(AL_DEBUG, "Resetting fired...");
            zpn_broker_client_app_timer_since_last_call(customer_gid);
            ZPATH_LOG(AL_DEBUG, "Resetting fired... DONE");
            return ZPATH_RESULT_NO_ERROR;
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad arguments", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        ZPATH_LOG(AL_DEBUG, "Checking fired...");
        has_fired = zpn_broker_client_app_timer_since_last_call(customer_gid);
        ZPATH_LOG(AL_DEBUG, "Checking fired... DONE: fired = %d", has_fired);
        if (check_fired != has_fired) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad timer: Was expecting fired = %s", line, fired);
            return ZPATH_RESULT_ERR;
        }
    } else if (strcasecmp(argv[1], "dump") == 0) {
        char *username;
        username = get_option_string(argv, argc, used, "-user", NULL);
        if (!username) {
            ZPATH_LOG(AL_ERROR, "Line %d: Bad arguments", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        struct user_app_state *user = zhash_table_lookup(user_apps, username, strlen(username), NULL);
        if (!user) {
            ZPATH_LOG(AL_ERROR, "Line %d: User %s not found", line, username);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        int64_t key = 0;
        zhash_table_walk(user->domains, &key, dump_domain_walk, username);
    } else {
        ZPATH_LOG(AL_ERROR, "Line %d: Bad argument %s", line, argv[1]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_NO_ERROR;
}


int process_reg_verify_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    char *table = NULL;
    char *column = NULL;
    char *key = NULL;
    char *reg = NULL;
    char *id = NULL;
    int registered;
    int expect_none = 0;

    int rx_registered;
    char rx_column[1000];
    char rx_key[1000];
    int i;
    int64_t rx_request_id;


    memset(used, 0, sizeof(used[0])*argc);

    table = get_option_string(argv, argc, used, "-table", NULL);
    column = get_option_string(argv, argc, used, "-column", "");
    key = get_option_string(argv, argc, used, "-key", "");
    reg = get_option_string(argv, argc, used, "-register", NULL);
    id = get_option_string(argv, argc, used, "-id", NULL);

    if (!reg || !table) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing args", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    if (strcasecmp(reg, "yes") == 0) {
        registered = 1;
    } else if (strcasecmp(reg, "no") == 0) {
        registered = 0;
        if (id) {
            ZPATH_LOG(AL_ERROR, "Line %d: -id not allowed for deregistrations", line);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
    } else if (strcasecmp(reg, "none") == 0) {
        registered = 0;
        expect_none = 1;
    } else {
        ZPATH_LOG(AL_ERROR, "Line %d: bad '-register' option: %s", line, reg);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    res = wally_test_origin_get_registration(test_origin,
                                             table,
                                             &rx_request_id,
                                             &rx_registered,
                                             rx_column, sizeof(rx_column),
                                             rx_key, sizeof(rx_key));
    if (res == ZPATH_RESULT_NOT_FOUND) {
        if (expect_none) {
            /* We are done! */
            return ZPATH_RESULT_NO_ERROR;
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting registration, but there is none", line);
            return ZPATH_RESULT_ERR;
        }
    } else if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: Could not retrieve registration for table %s: %s", line, table, zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    } else {
        if (expect_none) {
            ZPATH_LOG(AL_ERROR, "Line %d: Expecting no registration, but there is one, table %s, column %s, key %s", line, table, rx_column, rx_key);
            return ZPATH_RESULT_ERR;
        }
    }

    if (rx_registered != registered) {
        ZPATH_LOG(AL_ERROR, "Line %d: registration vs deregistration mismatch. Expecting %s, got %s", line,
                  registered ? "registration" : "deregistration",
                  rx_registered ? "registration" : "deregistration");
        broken = 1;
    }
    if (strcasecmp(column, rx_column)) {
        ZPATH_LOG(AL_ERROR, "Line %d: (de)registration column mismatch, expecting <%s>, got <%s>", line, column, rx_column);
        broken = 1;
    }
    if (strcasecmp(key, rx_key)) {
        /* Might be numbers... */
        int64_t a, b;
        char *aa, *bb;
        a = strtoull(key, &aa, 0);
        b = strtoull(key, &bb, 0);
        if (aa && bb && (a == b)) {
            /* Integer match! */
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: (de)registration key mismatch, expecting <%s>, got <%s>", line, key, rx_key);
            broken = 1;
        }
    }
    if (broken) return ZPATH_RESULT_ERR;

    /* Save ID for later use in a response injection... */
    if (id) {
        int64_t *ptr = ZPE_CALLOC(sizeof(*ptr));
        *ptr = rx_request_id;
        zhash_table_store(ids, id, strlen(id), 0, ptr);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int process_respond_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    char *id = NULL;
    char *table_name = NULL;
    int64_t row_count;
    int64_t *req_id;
    int i;

    memset(used, 0, sizeof(used[0])*argc);

    id = get_option_string(argv, argc, used, "-id", NULL);
    table_name = get_option_string(argv,argc,used,"-table", NULL);
    row_count = get_option_int64(argv, argc, used, "-rows", 0);

    if (!id) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing args", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    req_id = zhash_table_lookup(ids, id, strlen(id), NULL);
    if (!req_id) {
        ZPATH_LOG(AL_ERROR, "Line %d: ID %s not found", line, id);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    res = wally_test_origin_inject_response(test_origin, *req_id, row_count,table_name);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: Could inject response", line);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}


static int add_one_object(char *filename, int line, char *object_str)
{
    int res;
    char *column_name;
    char *table_name;
    size_t i;
    struct argo_structure_description *d;
    struct argo_field_description *f;


    struct argo_object *obj = argo_deserialize_json(object_str, strlen(object_str));
    if (!obj) {
        ZPATH_LOG(AL_ERROR, "Line %d: File %s: Could Could not parse %s", line, filename, object_str);
        return ZPATH_RESULT_ERR;
    }
    d = argo_global.all_descriptions[obj->base_structure_index];
    table_name = d->type;

    ZPATH_LOG(AL_DEBUG, "Adding object table = %s, NULL column, object = %s", table_name, object_str);
    /* Add it for all columns and NULL column... */
    res = wally_test_origin_add_row(test_origin,
                                    table_name,
                                    NULL, 0,
                                    NULL, 0,
                                    obj);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: File %s: Could not add row to origin", line, filename);
        return ZPATH_RESULT_ERR;
    }

    for (i = 0; i < d->description_count; i++) {
        f = &(d->description[i]->public_description);
        if (f->is_index) {
            column_name = f->field_name;

            char *str_val;
            char int_str[100];
            int64_t int_val;
            enum argo_field_data_type f_type = f->argo_new_field_type;
            switch (f_type) {
            case argo_field_data_type_integer:
                res = argo_object_read_int_by_column_name(obj, column_name, &int_val);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "Line %d: File %s: Could not read integer for column %s", line, filename, column_name);
                    return ZPATH_RESULT_ERR;
                }
                snprintf(int_str, sizeof(int_str), "%ld", (long) int_val);
                str_val = int_str;
                break;
            case argo_field_data_type_string:
                res = argo_object_read_string_by_column_name(obj, column_name, &str_val);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "Line %d: File %s: Could not read string for column %s", line, filename, column_name);
                    return ZPATH_RESULT_ERR;
                }
                break;
            default:
                ZPATH_LOG(AL_ERROR, "Line %d: File %s: Object contains no field %s", line, filename, column_name);
                return ZPATH_RESULT_ERR;
                break;
            }
            ZPATH_LOG(AL_DEBUG, "Adding object table = %s, column = %s, value = %s, object = %s", table_name, column_name, str_val, object_str);
            res = wally_test_origin_add_row(test_origin,
                                            table_name,
                                            column_name, strlen(column_name),
                                            str_val, strlen(str_val),
                                            obj);
            if (res) {
                ZPATH_LOG(AL_ERROR, "Line %d: File %s: Could not add row to origin for column %s", line, filename, column_name);
                return ZPATH_RESULT_ERR;
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


int process_wally_load_file_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int i;

    char *file_name = NULL;

    memset(used, 0, sizeof(used[0])*argc);

    file_name = get_option_string(argv, argc, used, "-file", NULL);

    if (!file_name) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing args", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    /* Validate no extra arguments */
    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;


    /* Load file name full of objects into test wally */
    FILE *fp = fopen(file_name, "r");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Line %d: Could not open file %s", line, file_name);
        return ZPATH_RESULT_ERR;
    }
    char *s;
    char str[10000];
    while ((s = fgets(str, sizeof(str), fp))) {
        /* Skip whitespace */
        while (isspace(*s)) s++;

        /* If begins with '#', is a comment line, and skip. */
        if (*s == '#') {
            continue;
        }

        /* If line is empty, skip it */
        if (*s == '\0') {
            continue;
        }

        /* Trim trailing newlines */
        char *end = s + strlen(s);
        end--;
        while ((end > s) && (((*end) == '\n') || ((*end) == '\r'))) {
            *end = 0;
            end--;
        }

        res = add_one_object(file_name, line, s);
        if (res) {
            fclose(fp);
            fp = NULL;
            return res;
        }
    }
    if (fp) fclose(fp);
    return ZPATH_RESULT_NO_ERROR;
}

int process_load_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int used[MAX_ARGS];
    int broken = 0;

    int i;

    char *file_name = NULL;

    memset(used, 0, sizeof(used[0])*argc);

    file_name = get_option_string(argv, argc, used, "-file", NULL);

    if (!file_name) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing args", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    /* Validate no extra arguments */
    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    ZPATH_LOG(AL_DEBUG, "Processing sub-file %s", file_name);

    return process_file(file_name);
}


int inject(char *json_string)
{
    struct argo_object *object;
    int res;

    object = argo_deserialize_json(json_string, strlen(json_string));
    if (!object) {
        return ZPATH_RESULT_BAD_DATA;
    }
    ZPATH_LOG(AL_DEBUG, "Deserialized JSON");

    res = wally_test_origin_inject_row(test_origin, object);
    return res;
}


int init_fake_db(void)
{
    test_wally = wally_create("db_test", 0, NULL, NULL, NULL, NULL);
    if (!test_wally) return ZPN_RESULT_ERR;

    test_origin = wally_test_origin_create(test_wally, NULL);
    if (!test_origin) return ZPN_RESULT_ERR;

    test_origin_origin = wally_add_origin(test_wally,
                                          "test_wally",
                                          test_origin,
                                          wally_test_register_for_index,
                                          wally_test_deregister_for_index,
                                          wally_test_set_cookie,
                                          wally_test_get_status,
                                          wally_test_add_table,
                                          NULL,
                                          wally_test_dump_state,
                                          0); // Not writable);
    if (!test_origin_origin) return ZPN_RESULT_ERR;

    zpath_global_wally = test_wally;

    return ZPN_RESULT_NO_ERROR;
}

void init_zpath_table(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    int res;

    res = zpath_table_init(test_wally, test_origin_origin, NULL, 1);
    if (res) {
        fprintf(stderr, "Could not zpath_table_init\n");
        exit(1);
    }
}

void init_constellations(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    int res;
    res = zpath_constellation_init(test_wally, test_origin_origin, NULL);
    if (res) {
        fprintf(stderr, "Could not zpath_constellation_init\n");
        exit(1);
    }
    res = zpath_constellation_instance_init(test_wally, test_origin_origin, NULL);
    if (res) {
        fprintf(stderr, "Could not zpath_constellation_instance_init\n");
        exit(1);
    }
    res = zpath_customer_to_constellation_init(test_wally, test_origin_origin, NULL);
    if (res) {
        fprintf(stderr, "Could not zpath_customer_to_cosntellation_init\n");
        exit(1);
    }
}



void init_application_domain(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    int res;
    int64_t customer_id = (int64_t)int_cookie;
    res = zpn_application_init_1(test_wally, customer_id, 1, 1, 0, 1);
    if (res) {
        fprintf(stderr, "Could not zpn_application_init\n");
        exit(1);
    }

    res = zpn_application_domain_init_1(test_wally, customer_id, 0, 1);
    if (res) {
        fprintf(stderr, "Could not zpn_application_domain_init\n");
        exit(1);
    }

    res = zpn_application_group_application_mapping_init_1(test_wally, customer_id, 1, 0, 0, 1);
    if (res) {
        fprintf(stderr, "Could not zpn_application_group_application_mapping_init_1\n");
        exit(1);
    }

    res = zpn_application_group_init_1(test_wally, customer_id, 0 /* no fully load */, 0, 1);
    if (res) {
        fprintf(stderr, "Could not zpn_application_group_init_1\n");
        exit(1);
    }
}

int process_init_application_domain_line(char **argv, int argc, int line)
{
    int used[MAX_ARGS];
    int broken = 0;

    int64_t customer_gid = 0;

    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);

    if (!customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;
    zevent_base_call(init_base, init_application_domain, test_origin_origin, customer_gid);
    return 0;
}

// int zpn_policy_test_broker_partition_stats_obj_init(int64_t partition_gid, int create, void **obj)
// {
//     /* np ops */
//     return 0;
// }

void init_partition(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    // int res = 0;

    // ut_zpath_stats_collection = argo_log_create("statistics_log", NULL);
    // if (!ut_zpath_stats_collection) {
    //     ZPATH_LOG(AL_ERROR, "Could not initialize stats_log argo_log collection");
    //     return;
    // }

    // stats_file = argo_log_file_create(ut_zpath_stats_collection,
    //                                       ZPATH_LOCAL_STATS_LOG_FILE,
    //                                       ZPATH_LOCAL_STATS_LOG_FILE_SHORT,
    //                                       1024*1024*1024,
    //                                       argo_serialize_binary);

    // ZPATH_LOG(AL_NOTICE, "init_partition start ...");
    // res = zpath_partition_common_init(test_wally, test_origin_origin, NULL,(int64_t) 70575940719239898, zpn_policy_test_broker_partition_stats_obj_init);
    // if (res) {
    //     fprintf(stderr, "Could not zpath_partition_common_init\n");
    //     exit(1);
    // }
    // ZPATH_LOG(AL_NOTICE, "init_partition...Completed!\n");
}

void init_instances(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    int res = 0;
    ZPATH_LOG(AL_NOTICE, "init_instances start ...");

    res = zpath_instance_init_test(test_wally, test_origin_origin);
    if (res) {
        fprintf(stderr, "Could not zpath_instance_init\n");
        exit(1);
    }

    res = zpn_broker_load_init(test_wally, test_origin_origin, NULL);
    if (res) {
        fprintf(stderr, "Could not zpn_broker_load_init\n");
        exit(1);
    }
#if 0
    /* hardcode private broker gid as 2^56 for testing, this makes shard_index = 1 */
    res = zpn_private_broker_load_init_test(test_wally, 72057594037927936, NULL);
    if (res) {
        fprintf(stderr, "Could not zpn_private_broker_load_init\n");
        exit(1);
    }
# endif
    res = zpn_balance_redirect_init(test_wally, test_origin_origin, NULL, 12345);
    if (res) {
        fprintf(stderr, "Could not zpn_balance_redirect_init\n");
        exit(1);
    }
    ZPATH_LOG(AL_NOTICE, "init_instances completed ...");
}

static int zpn_private_broker_row_callback(void *cookie,
                                           struct wally_registrant *registrant,
                                           struct wally_table *table,
                                           struct argo_object *previous_row,
                                           struct argo_object *row,
                                           int64_t request_id)
{
    char dump[8000];
    if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_INFO, "pbroker row callback: %s", dump);
    }
    return ZPN_RESULT_NO_ERROR;
}

void init_zpn_private_broker_load (struct zevent_base *base,
                                   void *void_cookie,
                                   int64_t int_cookie,
                                   void *extra_cookie1,
                                   void *extra_cookie2,
                                   void *extra_cookie3,
                                   int64_t extra_int_cookie)
{
    int64_t pbroker_gid = int_cookie;
    int64_t customer_gid = extra_int_cookie;
    int complete = 0;

    ZPATH_LOG(AL_NOTICE, "init_zpn_private_broker_load start..., pbroker_gid = %"PRId64", customer_gid = %"PRId64"\n",
                         pbroker_gid, customer_gid);

    zpn_private_broker_table_init(test_wally, pbroker_gid, 0 /* no fully load */, 0, zpn_private_broker_row_callback);
    zpn_private_broker_table_load(customer_gid, pbroker_load_cb,  &complete, 0);

    zpn_pbroker_group_init(test_wally, pbroker_gid, NULL, 1, 0);
    zpn_pbroker_to_group_table_init(test_wally, pbroker_gid, NULL, 1, 0);

    zpn_trusted_network_init(test_wally, pbroker_gid, 0 /* no fully load */, 0);
    zpn_trusted_network_load(customer_gid, pbroker_load_cb,  &complete, 0);
    zpn_privatebrokergroup_trustednetwork_mapping_table_init(test_wally, pbroker_gid, 0);

    zpn_private_broker_load_init_internal(test_wally, pbroker_gid, 0 /* no fully load */, 0, zpn_balance_inst_load_update_pbroker, 1);
    zpn_private_broker_load_initialize(customer_gid, pbroker_load_cb, &complete, 0);

    while (!complete) {
        event_base_loop(script_ev_base, EVLOOP_NONBLOCK);
        usleep(1000);
    }
}
static int
validate_app_access_passed_or_failed()
{
    int res = 0;
    if (max_app_download_embedded_feature) {
        /* Current stats */
        ZPATH_LOG(AL_NOTICE, "Max_app_download_embedded_feature = %d, "
                             "Current platform = %s, User = %s, "
                             "Allowed app access count = %d, "
                             "Total app access tried = %d, Able to Access = %d, Not able to Access = %d\n",
                             max_app_download_embedded_feature,
                             ZPN_PLATFORM_TYPE_STR(current_platform), current_username,
                             max_app_download_embedded,
                             total_app_access,
                             found_app_download_embedded, not_found_app_download_embedded);

        if (current_platform == zpn_platform_type_windows || current_platform == zpn_platform_type_linux) {
            if (not_found_app_download_embedded > 0) {
                ZPATH_LOG(AL_ERROR, "App access failed for platform %s "
                                    "Number of apps not abled to access =  %d", ZPN_PLATFORM_TYPE_STR(current_platform),
                                    not_found_app_download_embedded);
                return ZPATH_RESULT_ERR;
            } else {
                ZPATH_LOG(AL_NOTICE, "App access successed for platform %s "
                                     "Number of apps accessed is %d", ZPN_PLATFORM_TYPE_STR(current_platform),
                                     found_app_download_embedded);
            }
        } else {
            /*
             * Covers this use case
             * 5 apps are configured, 2 gets downloaded
             * Try to access 5 apps, atmost 2 can be accessed
             */

            if (found_app_download_embedded == max_app_download_embedded) {
                ZPATH_LOG(AL_NOTICE, "App access successed for platform %s, "
                                     "Max access allowed = %d, Accessed apps = %d, Not able to access apps = %d \n",
                                     ZPN_PLATFORM_TYPE_STR(current_platform),
                                     max_app_download_embedded, found_app_download_embedded, not_found_app_download_embedded);
            } else {
                ZPATH_LOG(AL_ERROR, "App access failed for platform %s, "
                                     "Max access allowed = %d, Accessed apps = %d, Not able to access apps = %d \n",
                                     ZPN_PLATFORM_TYPE_STR(current_platform),
                                     max_app_download_embedded, found_app_download_embedded, not_found_app_download_embedded);
                return ZPATH_RESULT_ERR;
            }
        }
    }
    return res;
}

int process_one_line(char *one_line, int line, int64_t now_s)
{
    static char *argv[MAX_ARGS];
    int argc = MAX_ARGS;
    int i;
    int res = ZPATH_RESULT_NO_ERROR;

    argify(one_line, &(argv[0]), &argc);
    for (i = 0; i < argc; i++) {
        //ZPATH_LOG(AL_DEBUG, "  argv[%d] = %s", i, argv[i]);
    }
    if (!argc) return ZPATH_RESULT_NO_ERROR;
    if (argv[0][0] == '#') return ZPATH_RESULT_NO_ERROR;

    if (strcasecmp(argv[0], "add") == 0) {
        if (using_test_wally) {
            ZPATH_LOG(AL_ERROR, "Line %d: Using ADD when configured to use wally", line);
            res = ZPATH_RESULT_ERR;
            goto fail;
        }
        res = process_add_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if ((strcasecmp(argv[0], "get") == 0) || (strcasecmp(argv[0], "get_and_save") == 0)) {
        res = process_get_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "load_scope") == 0) {
        res = process_load_scope_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "user") == 0) {
        res = process_user_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "eval") == 0) {
        res = process_eval_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "check_build") == 0) {
        res = process_check_build_line(&(argv[0]), argc, line);
    } else if (strcasecmp(argv[0], "app_search") == 0) {
        res = process_app_search_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "enable_auto_tickle") == 0) {
        enable_tickle();
    } else if (strcasecmp(argv[0], "disable_auto_tickle") == 0) {
        disable_tickle();
    } else if (strcasecmp(argv[0], "tickle") == 0) {
        res = process_tickle_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "tickled") == 0) {
        res = process_tickled_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "load_policy") == 0) {
        res = process_load_policy_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "build_outstanding") == 0) {
        res = process_build_outstanding_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "tickle_cancel") == 0) {
        res = process_tickle_cancel_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "flush") == 0) {
        res = process_flush_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "client_app") == 0) {
        res = process_client_app_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "wally_policy") == 0) {
        using_test_wally = 1;
        res = test_policy_engine_use_wally(test_wally);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_table") == 0) {
        /* This needs to run on another thread, because it
         * does synchonous read of the database, and we need
         * to feed it initialization data. */
        zevent_base_call(init_base, init_zpath_table, test_origin_origin, 0);
    } else if (strcasecmp(argv[0], "zpn_private_broker_load") == 0) {
        res = process_zpn_private_broker_load(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_constellation") == 0) {
        zevent_base_call(init_base, init_constellations, test_origin_origin, 0);
    } else if (strcasecmp(argv[0], "zpath_instance") == 0) {
        zevent_base_call(init_base, init_instances, test_origin_origin, 0);
    } else if (strcasecmp(argv[0], "zpath_partition") == 0) {
        zevent_base_call(init_base, init_partition, test_origin_origin, 0);
    } else if (strcasecmp(argv[0], "zpath_partition_profile_activate") == 0) {
        res = process_partition_profile_activate(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_partition_profile_verify") == 0) {
        res = process_partition_profile_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_partition_profile_instance_verify") == 0) {
        res = process_partition_profile_instance_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_partition_profile_partition_verify") == 0) {
        res = process_partition_profile_partition_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    }  else if (strcasecmp(argv[0], "zpath_customer_partition_override_verify") == 0) {
        res = process_customer_partition_override_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpath_constellation_verify") == 0) {
        res = process_constellation_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "viable_brk_verify") == 0) {
        res = process_viable_brk_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpn_application_domain") == 0) {
        res = process_init_application_domain_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpn_tld_1_update") == 0) {
        res = process_zpn_tld_1_update_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "zpn_tld_1_verify") == 0) {
        res = process_zpn_tld_1_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "brk_balance_redir_verify") == 0) {
        res = process_brk_redir_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "brk_balance_redir_v2_verify") == 0) {
        res = process_brk_redir_v2_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "brk_balance_redir_v2_inspect") == 0) {
        res = process_brk_redir_v2_inspect_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "wally_reg_verify") == 0) {
        res = process_reg_verify_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "wally_respond") == 0) {
        res = process_respond_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "wally_autorespond") == 0) {
        res = wally_test_origin_autorespond(test_origin, 1);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "wally_load_file") == 0) {
        res = process_wally_load_file_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "modified_time") == 0) {
        if (argc != 2) {
            ZPATH_LOG(AL_ERROR, "Line %d: bad args", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto fail;
        }
        if (strcasecmp(argv[1], "off") == 0) {
            modified_time_s = 0;
        } else if (strcasecmp(argv[1], "now") == 0) {
            modified_time_s = now_s;
        } else {
            if (modified_time_s > 0) {
                modified_time_s += strtoll(argv[1], NULL, 0);
            } else {
                ZPATH_LOG(AL_ERROR, "Line %d: MODIFIED_TIME is OFF, must run 'MODIFIED_TIME NOW' first", line);
                res = ZPATH_RESULT_ERR;
                goto fail;
            }
        }
    } else if (strcasecmp(argv[0], "delay") == 0) {
        int64_t usec;
        if (argc != 2) {
            ZPATH_LOG(AL_ERROR, "Line %d: bad args", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto fail;
        }
        usec = strtoul(argv[1], NULL, 0);
        if (usec <= 0) {
            ZPATH_LOG(AL_ERROR, "Line %d: expect positive nonzero time", line);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto fail;
        }
        usleep(usec);
    } else if ((argv[0][0]=='0') &&
               (argv[0][1]=='x')) {
        res = process_read_data(&(argv[0]), argc, line);
        if (res) goto fail;
    } else if (strcasecmp(argv[0], "ENABLE_APP_SCALING_FEATURE_FLAG") == 0) {
        zpn_policy_enable_app_scaling_feature_test();
        fprintf(stderr, "Error enabling app scaling feature flag\n");
    } else if(strcasecmp(argv[0], "ENABLE_APP_MULTI_MATCH_FEATURE_FLAG") == 0) {
        zpn_enable_app_multi_match_feature_test();
        fprintf(stderr, "Enabling app multi match feature flag\n");
    } else if(strcasecmp(argv[0], "ACTIVATE_DR_MODE") == 0) {
        zpn_activate_dr_mode();
        fprintf(stderr, "Activate DR mode\n");
    } else if (strcasecmp(argv[0], "ENABLE_APP_DOWNLOAD_RESTRICT_EMBEDDED_FEATURE_FLAG") == 0) {
        zpn_policy_enable_app_download_restriction();
        max_app_download_embedded_feature = 1;
        fprintf(stderr, "Enabling app download restructions flag\n");
    } else if (strcasecmp(argv[0], "RESET_APP_DOWN_COUNTER") == 0) {
        res = validate_app_access_passed_or_failed();
        if (res) goto fail;
        found_app_download_embedded = 0;
        not_found_app_download_embedded = 0;
        total_app_access = 0;
        current_platform = 0;
        memset(current_username, 0, sizeof(current_username));
        fprintf(stderr, "Reset app download counters\n");
    } else if (strcasecmp(argv[0], "ET_25587_INIT") == 0) {
        et_25587_test_init();
        fprintf(stderr, "et_25587_init\n");
    } else if (strcasecmp(argv[0], "USE_SCOPE_INIT") == 0) {
        unit_test_scope_init();
        fprintf(stderr, "unit_test_scope_init\n");
    } else if (strcasecmp(argv[0], "DTA_TEST_INIT") == 0) {
        dta_test_init();
        fprintf(stderr, "unit_test_scope_init\n");
    } else if (strcasecmp(argv[0], "DTA_SKIP_DEFAULT_RULE_INIT") == 0) {
        dta_skip_default_rule_init();
        fprintf(stderr, "dta_skip_default_rule_init\n");
    } else if (strcasecmp(argv[0], "POLICY_EVALUATION_COMPARE") == 0) {
        policy_eval_comp_init();
        fprintf(stderr, "policy_evaluation_compare\n");
    }  else if (strcasecmp(argv[0], "POLICY_EVALUATION_COMPARE_PERF_CANCEL") == 0) {
        policy_eval_comp_perf_cancel();
        fprintf(stderr, "policy_evaluation_compare_perf_cancel\n");
    } else if (strcasecmp(argv[0], "ET_25587_CHECK_UNBLOCK") == 0) {
        et_25587_check_unblock();
        fprintf(stderr, "et_25587_check_unblock\n");
    } else if (strcasecmp(argv[0], "load") == 0) {
        res = process_load_line(&(argv[0]), argc, line);
        if (res) goto fail;
    } else {
        ZPATH_LOG(AL_ERROR, "Line %d: Bad line type %s", line, argv[0]);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto fail;
    }

 fail:
    return res;
}


/*
 * This routine leaks memory like a sieve, and I don't care
 *
 * It's pretty ugly, too.
 */
int process_file(char *filename)
{
    FILE *fp;
    static char one_line[MAX_ARGS*20];
    static char one_line_copy[MAX_ARGS*20];
    int res;
    int line = 0;
#ifndef PATH_MAX
#define PATH_MAX  4096
#endif
    char path[PATH_MAX];
    int64_t now_s = epoch_us()/1000000;

    fp = fopen(filename, "r");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Bad filename %s", filename);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    // Follow paths...
    if (!getcwd(path, sizeof(path))) {
        ZPATH_LOG(AL_ERROR, "FATAL: Cannot get executable working directory\n");
        exit(1);
    }
    char *dir = dirname(filename);
    if (!dir) {
        ZPATH_LOG(AL_ERROR, "FATAL: Cannot get script working directory\n");
        exit(1);
    }
    if (chdir(dir) != 0) {
        ZPATH_LOG(AL_ERROR, "FATAL: Cannot change to script's working directory\n");
        exit(1);
    }

    while (fgets(one_line, sizeof(one_line), fp)) {
        line++;
        char *e = one_line + strlen(one_line);
        e--;
        while ((e >= one_line) &&
               ((*e == '\n') || (*e == '\r'))) {
            *e = 0;
            e--;
        }
        ZPATH_LOG(AL_DEBUG, "Line %d = <" OUTPUT_GRN "%s" OUTPUT_RST ">", line, one_line);

        /* Special check for JSON injection. We don't want to argify it */
        if (strncasecmp(one_line, "wally_inject", strlen("wally_inject")) == 0) {
            if (modified_time_s != 0) {
                char *s = strstr(one_line, "}}");
                char *e = &one_line[0] + sizeof(one_line)-1;
                snprintf(s, e-s+1, ", \"modified_time\":%" PRId64 "}}", modified_time_s);
            }
            res = inject(&(one_line[strlen("wally_inject")]));
            if (res) goto fail;
        } else if (strncasecmp(one_line, "wally_load_object", strlen("wally_load_object")) == 0) {
            /* Find the beginning of the object. First character is '{'... */
            char *object_begin = strstr(one_line, "{");
            if (!object_begin) {
                ZPATH_LOG(AL_ERROR, "Line %d: no object specified", line);
                res = ZPATH_RESULT_BAD_ARGUMENT;
                goto fail;
            }
            res = add_one_object(filename, line, object_begin);
            if (res) {
                goto fail;
            }
        } else {
            if (strncasecmp(one_line, "waitfor", strlen("waitfor")) == 0) {
                /* Wait up to 1s for the requested result. */
                int64_t now_us = epoch_us();
                int64_t start_us = now_us;
                snprintf(one_line_copy, sizeof(one_line_copy), "%s", one_line);
                //ZPATH_LOG(AL_DEBUG, "Line %d = Waiting for <" OUTPUT_GRN "%s" OUTPUT_RST ">", line, &(one_line[strlen("waitfor")]));
                while ((res = process_one_line(&(one_line_copy[strlen("waitfor")]), line, now_s)) &&
                       ((now_us - start_us) < MAX_WAITFOR_US)) {
                    ZPATH_LOG(AL_NOTICE, "Line %d: Got bad result %s, has been %ldus, retry <" OUTPUT_YEL "%s" OUTPUT_RST ">",
                              line, zpath_result_string(res), (long) (now_us - start_us), &(one_line[strlen("waitfor")]));
                    snprintf(one_line_copy, sizeof(one_line_copy), "%s", one_line);
                    event_base_loop(script_ev_base, EVLOOP_NONBLOCK);
                    usleep(1000);
                    now_us = epoch_us();
                }
                if (res) {
                    ZPATH_LOG(AL_DEBUG, "Line %d = FAIL Waiting for <" OUTPUT_RED "%s" OUTPUT_RST ">", line, &(one_line[strlen("waitfor")]));
                } else {
                    ZPATH_LOG(AL_DEBUG, "Line %d = DONE Waiting for <" OUTPUT_GRN "%s" OUTPUT_RST ">", line, &(one_line[strlen("waitfor")]));
                }

            } else {
                res = process_one_line(one_line, line, now_s);
            }
            if (res) goto fail;
        }
        event_base_loop(script_ev_base, EVLOOP_NONBLOCK);
    }
    if (chdir(path) == -1) {
        ZPATH_LOG(AL_ERROR, "FATAL: Cannot change to executable working directory\n");
        exit(1);
    };
    return ZPATH_RESULT_NO_ERROR;

 fail:
    if (chdir(path) == -1) {
        ZPATH_LOG(AL_ERROR, "FATAL: Cannot change to executable working directory\n");
        exit(1);
    };
    return res;
}


int main(int argc, char *argv[])
{
    int res;

    argo_log_use_printf(1);

    users = zhash_table_alloc(NULL);
    user_apps = zhash_table_alloc(NULL);
    ids = zhash_table_alloc(NULL);
    user_complete = zhash_table_alloc(NULL);
    saml_hash = zhash_table_alloc(NULL);

    wally_debug =(
                  WALLY_DEBUG_RESULT_BIT |
                  WALLY_DEBUG_TABLE_BIT |
                  WALLY_DEBUG_REGISTRATION_BIT |
                  WALLY_DEBUG_ROW_BIT |
                  WALLY_DEBUG_POSTGRES_BIT |
                  WALLY_DEBUG_FOHH_CLIENT_ROW_BIT |
                  WALLY_DEBUG_POSTGRES_POLL_BIT |
                  WALLY_DEBUG_POSTGRES_FC_BIT |
                  WALLY_DEBUG_POSTGRES_CONN_BIT |
                  WALLY_DEBUG_POSTGRES_EVENT_BIT |
                  WALLY_DEBUG_ROW_DETAIL_BIT |
                  WALLY_DEBUG_WRITE_ROW_BIT |
                  WALLY_DEBUG_POSTGRES_WRITE_BIT |
                  WALLY_DEBUG_TEST_ORIGIN_BIT |
                  WALLY_DEBUG_INTEREST_CB_BIT |
                  0);
    zpn_debug_set(ZPN_DEBUG_CLIENT_IDX);
    zpn_debug_set(ZPN_DEBUG_MTN_IDX);
    zpn_debug_set(ZPN_DEBUG_ZPE_IDX);
    zpn_debug_set(ZPN_DEBUG_JIT_APPROVAL_IDX);
    zpn_debug_set(ZPN_DEBUG_ZPE_DETAIL_IDX);
    zpath_debug |= ZPATH_DEBUG_CONSTELLATION_BIT;

    if (evthread_use_pthreads() != 0) {
        fprintf(stderr, "Can't run pthreads\n");
        exit(1);
    }

    unit_test_init();

    zthread_init("Test", "version", "instance", NULL, NULL);
    zthread_disable_heartbeat_monitor(); // so that we can debug without getting killed
    zevent_init();

    init_policy_engine_test();
    zpe_init(1);
    zpe_set_deferred_tickle_us(20000);

    argo_library_init(1024);

    struct zevent_base *z_base;
    z_base = zevent_handler_create("app_thread", 16*1024*1024, 30);
    if (!z_base) {
        fprintf(stderr, "Could not create zevent...\n");
        exit(1);
    }
    zevent_add_to_class(z_base, ZEVENT_CLASS_APP_CALC);

    init_base = zevent_handler_create("init_thread", 16*1024*1024, 30);
    if (!init_base) {
        fprintf(stderr, "Could not create zevent...\n");
        exit(1);
    }

    zthread_register_self("main", 200);
    script_ev_base = event_base_new();
    zevent_attach(script_ev_base);

    zpn_rpc_init();
    /* DR mode is disabled during tests */
    zse_init(0);

    event_base_loop(script_ev_base, EVLOOP_NONBLOCK);

    res = init_fake_db();
    if (res) {
        fprintf(stderr, "Could not init fake db\n");
        exit(1);
    }

    /* Initialize with 200 ms defer, for speed reasons... */
    res = zpn_broker_client_apps_init(test_wally, 0, 200000, 0);
    if (res) {
        fprintf(stderr, "Could not init client_apps\n");
        exit(1);
    }

    char *process_file_name = NULL;


    if (argc == 2) {
        process_file_name = argv[1];
    } else {
        if (argc == 3) {
            if (strcmp(argv[1], "-quiet") == 0) {
                process_file_name = argv[2];
                argo_log_set_printf_allowed_bitmap(0x3f);
            } else if (strcmp(argv[2], "-quiet") == 0) {
                process_file_name = argv[1];
                argo_log_set_printf_allowed_bitmap(0x3f);
            } else {
                ZPATH_LOG(AL_ERROR, "Expecting exactly one argument- the file to process, or one extra '-quiet' argument");
                exit(1);
            }
        } else {
            ZPATH_LOG(AL_ERROR, "Expecting exactly one argument- the file to process, or one extra '-quiet' argument");
            exit(1);
        }
    }
    res = process_file(process_file_name);

    zhash_table_free(users);
    zhash_table_free(user_apps);
    zhash_table_free(ids);
    zhash_table_free(user_complete);
    zhash_table_free(saml_hash);

    if (res) {
        ZPATH_LOG(AL_ERROR, OUTPUT_RED "FATAL: Error: %s, processing file %s" OUTPUT_RST, zpath_result_string(res), argv[1]);
        exit(1);
    } else {
        ZPATH_LOG(AL_NOTICE, OUTPUT_GRN "All tests passed" OUTPUT_RST);
        exit(0);
    }
    /*
    exit(0);
    return 0;
    */
}

//ZPATH_PARTITION_PROFILE_ACTIVATE -version 1
int process_partition_profile_activate(char **argv, int argc, int line)
{
    int64_t version = 0;

    int res;
    int used[MAX_ARGS];
    int broken = 0;
    int i;

    memset(used, 0, sizeof(used[0])*argc);

    version = get_option_int64(argv, argc, used, "-version", 0);
    if (!version) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -version", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpath_partition_profile_activate(version);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_activate returned %s", line, zpath_result_string(res));
        return res;
    }
    return ZPATH_RESULT_NO_ERROR;
}

//ZPATH_PARTITION_PROFILE_VERIFY -gid 30000 -instance_count 2
int process_partition_profile_verify_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t gid = 0;
    int64_t inst_count = 0;

    struct zpath_partition_profile *profile;
    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    gid = get_option_int64(argv, argc, used, "-gid", 0);
    inst_count = get_option_int64(argv, argc, used, "-instance_count", 0);
    if (!gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -gid", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpath_partition_profile_get_profile(gid, &profile);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_profile returned %s", line, zpath_result_string(res));
        return res;
    }

    if (inst_count != profile->instance_count) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_profile: instance_count %ld, but expecting %ld", line, (long)profile->instance_count, (long)inst_count);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

//ZPATH_PARTITION_PROFILE_INSTANCE_VERIFY -profile_gid 30000 -count 2
int process_partition_profile_instance_verify_line(char **argv, int argc, int line)
{
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t profile_gid = 0;
    size_t count_exp = 0;

    struct zpath_partition_profile_instance *instances[10];
    size_t count = 10;

    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    profile_gid = get_option_int64(argv, argc, used, "-profile_gid", 0);
    count_exp = get_option_int64(argv, argc, used, "-count", 0);
    if (!profile_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -gid", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpath_partition_profile_get_instances(profile_gid, instances, &count);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_instances returned %s", line, zpath_result_string(res));
        return res;
    }

    if (count_exp != count) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_instances: instance_count %ld, but expecting %ld", line, (long)count, (long)count_exp);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

//ZPATH_PARTITION_PROFILE_PARTITION_VERIFY -profile_gid 30000 -count 2
int process_partition_profile_partition_verify_line(char **argv, int argc, int line)
{
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t profile_gid = 0;
    size_t count_exp = 0;

    struct zpath_partition_profile_partition *partitions[10];
    size_t count = 10;

    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    profile_gid = get_option_int64(argv, argc, used, "-profile_gid", 0);
    count_exp = get_option_int64(argv, argc, used, "-count", 0);
    if (!profile_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -gid", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpath_partition_profile_get_partitions(profile_gid, partitions, &count);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_partitions returned %s", line, zpath_result_string(res));
        return res;
    }

    if (count_exp != count) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_partition_profile_get_partitions: instance_count %ld, but expecting %ld", line, (long)count, (long)count_exp);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

//ZPATH_CUSTOMER_PARTITION_OVERRIDE_VERIFY -customer_gid 72057595111669761 -partition_gid 72057594038079098
int process_customer_partition_override_verify_line(char **argv, int argc, int line)
{
   // int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t partition_gid = 0;
    int64_t partition_gid_expect = 0;
    int64_t customer_gid = 0;

    //struct zpath_customer_partition_override row;
    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    partition_gid = get_option_int64(argv, argc, used, "-partition_gid", 0);
    if (!partition_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -partition_gid", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    customer_gid = get_option_int64(argv, argc, used, "-customer_gid", 0);
    if (!customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -customer_gid", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    partition_gid_expect = zpath_customer_partition_override_get_partition_gid(customer_gid);
    if (!partition_gid_expect) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_customer_partition_override_get_partition_gid returned %"PRId64, line, partition_gid);
        return ZPATH_RESULT_ERR;
    }
    if(partition_gid != partition_gid_expect) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_customer_partition_override_get_partition_gid returned %"PRId64", but expected %"PRId64,line, partition_gid_expect, partition_gid);
        return ZPATH_RESULT_NOT_FOUND;
    }
    ZPATH_LOG(AL_ERROR, "Line %d: zpath_customer_partition_override_get_partition_gid returned %"PRId64", matching the expected %"PRId64,line, partition_gid_expect, partition_gid);
    return ZPATH_RESULT_NO_ERROR;
}



int process_constellation_verify_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    char *contains = NULL;
    char *excludes = NULL;
    int64_t customer_gid = 0;

    int64_t instance_gids[10000];
    size_t instance_gid_count = 10000;
    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    contains = get_option_string(argv, argc, used, "-contains", NULL);
    excludes = get_option_string(argv, argc, used, "-excludes", NULL);
    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);

    if ((!contains && !excludes) || !customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -contains or -excludes, and -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpath_constellation_get_instance_gids(customer_gid,
                                                instance_gids,
                                                &instance_gid_count);
    if (res) {
        /* Should never error- it will just return 0 on 'miss' */
        ZPATH_LOG(AL_ERROR, "Line %d: zpath_constellation_get_instance_gids returned %s", line, zpath_result_string(res));
        return res;
    }

    ZPATH_LOG(AL_DEBUG, "zpath_constellation_get_instance_gids, customer_gid = %ld, returned instance_count = %ld", (long) customer_gid, (long) instance_gid_count);

    for (i = 0; i < instance_gid_count; i++) {
        ZPATH_LOG(AL_DEBUG, "Found instance %ld", (long)instance_gids[i]);
    }

    if (contains) {
        int64_t gid = strtoll(contains, NULL, 0);
        for (i = 0; i < instance_gid_count; i++) {
            if (instance_gids[i] == gid) break;
        }
        if (i == instance_gid_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpath_constellation_get_instance_gids: instance %ld not found", line, (long) gid);
            return ZPATH_RESULT_NOT_FOUND;
        }
    }
    if (excludes) {
        int64_t gid = strtoll(excludes, NULL, 0);
        for (i = 0; i < instance_gid_count; i++) {
            if (instance_gids[i] == gid) break;
        }
        if (i < instance_gid_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpath_constellation_get_instance_gids: instance %ld was found, but expected not to", line, (long) gid);
            return ZPATH_RESULT_ERR;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int process_zpn_tld_1_update_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int i;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t customer_gid = 0;
    char *domain = NULL;

    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
    domain = get_option_string(argv, argc, used, "-domain", NULL);
    if( !customer_gid|| !domain) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -customer or -domain", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpn_application_domain_changed(customer_gid, domain);

    return res;
}

int process_zpn_tld_1_verify_line(char **argv, int argc, int line)
{
    /* Gather all GIDs... */
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    char *contains = NULL;
    char *excludes = NULL;
    int64_t customer_gid = 0;
    char **tld_1 = NULL;
    int sz;
    int i;

    memset(used, 0, sizeof(used[0])*argc);

    contains = get_option_string(argv, argc, used, "-contains", NULL);
    excludes = get_option_string(argv, argc, used, "-excludes", NULL);
    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);

    if ((!contains && !excludes) || !customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -contains or -excludes, and -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpn_get_tld_1_by_gid(customer_gid, &tld_1, &sz);
    if (res) return res;

    ZPATH_LOG(AL_NOTICE, "customer %ld has tld_1 count: %d", (long)customer_gid, sz);
    for (i = 0; i < sz; i++) {
        ZPATH_LOG(AL_INFO, "customer %ld coantains tld_1 domain: %s", (long)customer_gid, tld_1[i]);
        if (contains && strcmp(contains, tld_1[i]) == 0) return 0;
        if (excludes && strcmp(excludes, tld_1[i]) == 0) return 1;
    }
    zpn_free_tld_1(tld_1, sz, 0);
    return contains? 1 : 0;
}

static int parse_balance_v2_req_args(char **argv, int argc, struct zpn_balance_redirect_request *req, int *used, int line)
{
    int err = 0;
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    int64_t feature_grace_dist = 0;
    int64_t feature_policy_redirect = 0;
    int64_t cc_enabled = 1;
    int64_t saml_enabled = 1;
    int64_t scim_enabled = 1;
    char *ctype = NULL;
    char *mtn = NULL;
    char *lat_str = NULL;
    char *lon_str = NULL;
    char *remote_cc = NULL;
    char *saml=NULL;
    char *scim = NULL;
    char *scim_group = NULL;
    struct zhash_table *string_table=NULL;
    struct zhash_table *saml_table=NULL;
    struct zhash_table *scim_table=NULL;
    int client_type = 0;

    int64_t mdc = 1;

    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
    scope_gid = get_option_int64(argv, argc, used, "-scope", customer_gid);
    ctype = get_option_string(argv, argc, used, "-client_type", NULL);
    mtn = get_option_string(argv, argc, used, "-mtn", NULL);
    lat_str   = get_option_string(argv, argc, used, "-latitude", NULL);
    lon_str   = get_option_string(argv, argc, used, "-longitude", NULL);
    remote_cc = get_option_string(argv, argc, used, "-country", NULL);
    feature_grace_dist = get_option_int64(argv, argc, used, "-grace_dist", feature_grace_dist);
    feature_policy_redirect = get_option_int64(argv, argc, used, "-policy_redirect", feature_policy_redirect);
    cc_enabled =  get_option_int64(argv, argc, used, "-cc_enabled", cc_enabled);
    saml_enabled =  get_option_int64(argv, argc, used, "-saml_enabled", saml_enabled);
    scim_enabled =  get_option_int64(argv, argc, used, "-scim_enabled", scim_enabled);
    saml = get_option_string(argv, argc, used, "-saml", NULL);
    scim = get_option_string(argv, argc, used, "-scim", NULL);
    scim_group = get_option_string(argv, argc, used, "-scim_group", NULL);

    mdc = get_option_int64(argv, argc, used, "-mdc", 1);

    if (!lat_str || !lon_str || !remote_cc || !customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -customer, -latitude, -logitude, -country", line);
        err = ZPATH_RESULT_BAD_ARGUMENT;
        goto ll_cleanup;
    }

    string_table = zhash_table_alloc(NULL);

    if(!string_table){
        ZPATH_LOG(AL_ERROR,"Unable to allocate memory for string_table");
        err = ZPATH_RESULT_ERR;
        goto ll_cleanup;
    }

    saml_table = zhash_table_alloc(NULL);

    if(!saml_table){
        ZPATH_LOG(AL_ERROR,"Unable to allocate memory for saml_table");
        err = ZPATH_RESULT_ERR;
        zhash_table_free(string_table);
        string_table = NULL;
        goto ll_cleanup;
    }

    scim_table = zhash_table_alloc(NULL);

    if(!scim_table){
        ZPATH_LOG(AL_ERROR,"Unable to allocate memory for scim_table");
        err = ZPATH_RESULT_ERR;
        zhash_table_free(string_table);
        string_table = NULL;
        zhash_table_free(saml_table);
        saml_table = NULL;
        goto ll_cleanup;
    }

    if(remote_cc){
        char tmp_cc[64];
        memset(tmp_cc,'\0',64);
        snprintf(tmp_cc, sizeof(tmp_cc), "COUNTRY_CODE|%s|true", remote_cc);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(string_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    if(ctype){
        char tmp_cc[64];
        memset(tmp_cc,'\0',64);
        snprintf(tmp_cc, sizeof(tmp_cc), "CLIENT_TYPE|id|%s", ctype);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(string_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    if(saml){
        char tmp_cc[64];
        memset(tmp_cc,'\0',64);
        snprintf(tmp_cc, sizeof(tmp_cc), "%s", saml);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(saml_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    if(scim){
        char tmp_cc[64];
        memset(tmp_cc,'\0',64);
        snprintf(tmp_cc, sizeof(tmp_cc), "%s", scim);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(scim_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    if(scim_group){
        char tmp_cc[64];
        memset(tmp_cc,'\0',64);
        snprintf(tmp_cc, sizeof(tmp_cc), "%s", scim_group);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(scim_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    client_type = ctype ? zpn_client_type_from_str(ctype):0;
    req->client_type = ctype ? zpn_client_type_from_str(ctype) : zpn_client_type_zapp;
    req->policy_req->is_client_capable = client_type ? zpn_client_static_config[client_type].broker_policy_redirect:0;
    req->customer_gid = customer_gid;
    req->scope_gid = scope_gid;
    req->remote_lat = strtod(lat_str, NULL);
    req->remote_lon = strtod(lon_str, NULL);
    req->flag.feature_grace_distance = feature_grace_dist;
    req->flag.feature_policy_redirect = feature_policy_redirect;
    req->policy_req->general_context_hash = string_table;
    req->policy_req->saml_hash = saml_table;
    req->policy_req->scim_hash = scim_table;
    req->policy_req->int_hash = NULL;
    req->policy_req->is_cc_enabled = cc_enabled;
    req->policy_req->is_saml_enabled = saml_enabled;
    req->policy_req->is_scim_enabled = scim_enabled;
    snprintf(req->remote_cc, CC_BUF_LEN, "%s", remote_cc);
    req->rbt = NULL; // TODO: take from param?
    req->trusted_networks = mtn ? debug_tokenize_trusted_networks(mtn) : NULL;
    req->multi_dc_redir_capable = mdc && zpn_client_static_config[req->client_type].fohh_redirect_pub_mdc;

ll_cleanup:
    ZPE_FREE(ctype);
    ZPE_FREE(mtn);
    ZPE_FREE(lat_str);
    ZPE_FREE(lon_str);
    ZPE_FREE(remote_cc);
    ZPE_FREE(saml);
    ZPE_FREE(scim);
    ZPE_FREE(scim_group);
    return err;
}

static void free_balance_v2_req_args(struct zpn_balance_redirect_request *req)
{
    if (req->trusted_networks) argo_object_release(req->trusted_networks);

    if (req->policy_req->scim_hash) {
        zhash_table_free(req->policy_req->scim_hash);
        req->policy_req->scim_hash = NULL;
    }
    if (req->policy_req->saml_hash) {
        zhash_table_free(req->policy_req->saml_hash);
        req->policy_req->saml_hash = NULL;
    }
    if (req->policy_req->general_context_hash) {
        zhash_table_free(req->policy_req->general_context_hash);
        req->policy_req->general_context_hash = NULL;
    }
}


/*
 * Support running zpn_balance_redirect and then inspect the state of a particular
 * instance (doesn't have to be one selected).
 * Can be used to examine and verify any member in the viable_instance structure.
 * TODO: add more fields to verify as needed.
 */
int process_brk_redir_v2_inspect_line(char **argv, int argc, int line)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int used[MAX_ARGS];
    int broken = 0;
    int i;

    int64_t inst_gid = 0;
    char *field[MAX_ARGS];
    int field_count = MAX_ARGS;
    char *value[MAX_ARGS];
    int value_count = MAX_ARGS;

    int broker_count = 0;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int brokers_type[ZPN_CLIENT_MAX_BROKERS] = {0};
    struct zpn_instance_info instance_info[ZPN_CLIENT_MAX_BROKERS] = {0};
    int instance_info_count = 0;
    int sni_suffix_count = 0;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];

    struct zpn_balance_redirect_request req = {0};
    struct balance_request_state *state = NULL;
    struct viable_instance *inst = NULL;
    struct zpn_balance_policy_req policy_req = {0};

    memset(used, 0, sizeof(used[0])*argc);
    req.policy_req = &policy_req;

    /* common params */
    res = parse_balance_v2_req_args(argv, argc, &req, used, line);
    if (res) goto ll_cleanup;

    /* params for instance state verification */
    inst_gid = get_option_int64(argv, argc, used, "-inst_gid", 0);
    get_option_strings(argv, argc, used, "-field", field, &field_count);
    get_option_strings(argv, argc, used, "-value", value, &value_count);

    if (!inst_gid || !field_count || !value_count || field_count != value_count) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -inst_gid, -field, -value", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto ll_cleanup;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) {
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto ll_cleanup;
    }

    req.broker_count = &broker_count;
    req.brokers = brokers;
    req.brokers_type = brokers_type;
    req.instance_info = &instance_info[0];
    req.instance_info_count = &instance_info_count;
    req.sni_suffix_count = &sni_suffix_count;
    req.sni_suffix = sni_suffixes;
    req.alt_cloud_supported = broker_alt_cloud_support_enabled;
    req.alt_cloud_aware = 0;
    req.exclude_self = 0;

    res = zpn_balance_redirect_internal(&req, NULL, NULL, &state);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpn_balance_redirect returned %s", line, zpath_result_string(res));
        goto ll_cleanup;
    }

    debug_print_balance_state(state, NULL, 1);

    /* Find and verify instance */
    assert(state);
    for (i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].gid == inst_gid) {
            inst = &state->viable_instances[i];
            break;
        }
    }

    if (!inst) {
        ZPATH_LOG(AL_ERROR, "Line %d: instance %"PRId64" not found", line, inst_gid);
        res = ZPATH_RESULT_ERR;
        goto ll_cleanup;
    }

    /* Field value verification of a viable_instance */
    for (i = 0; i < field_count; i++) {
        if (!strcasecmp(field[i], "load")) {
            uint32_t load = strtoul(value[i], NULL, 0);
            if (load != inst->effective_load) {
                ZPATH_LOG(AL_ERROR, "Line %d: field %s expecting %ul, got %ul", line, field[i], load, inst->effective_load);
                res = ZPATH_RESULT_ERR;
                goto ll_cleanup;
            }
        } else {
            ZPATH_LOG(AL_ERROR, "Line %d: field %s not supported - add it to process_brk_redir_v2_inspect_line?", line, field[i]);
            res = ZPATH_RESULT_BAD_ARGUMENT;
            goto ll_cleanup;
        }
    }
ll_cleanup:
    free_balance_v2_req_args(&req);

    if (state) {
        zpn_balance_redirect_free(state);
        ZPN_BALANCE_FREE(state);
    }
    for (i = 0; i < broker_count; i++) {
        if (brokers[i]) {
            ZPE_FREE(brokers[i]);
        }
    }
    for (i = 0; i < sni_suffix_count; i++) {
        if (sni_suffixes[i]) {
            ZPE_FREE(sni_suffixes[i]);
        }
    }
    return res;
}

/* verify zpn_balance_redirect() */
int process_brk_redir_v2_verify_line(char **argv, int argc, int line)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int used[MAX_ARGS];
    int broken = 0;
    size_t i, j;

    char *contains[MAX_ARGS];
    int contains_count = MAX_ARGS;
    char *contains_any[MAX_ARGS];
    int contains_any_count = MAX_ARGS;
    char *excludes[MAX_ARGS];
    int excludes_count = MAX_ARGS;

    int broker_count = 0;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int brokers_type[ZPN_CLIENT_MAX_BROKERS] = {0};

    struct zpn_instance_info instance_info[ZPN_CLIENT_MAX_BROKERS] = {0};
    int instance_info_count = 0;
    int sni_suffix_count = 0;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];

    struct zpn_balance_redirect_request req = {0};
    memset(used, 0, sizeof(used[0])*argc);
    struct zpn_balance_policy_req policy_req = {0};

    req.policy_req = &policy_req;
    req.policy_req->is_cc_enabled = 1;
    req.policy_req->is_saml_enabled = 1;
    req.policy_req->is_scim_enabled = 1;
    /* common params */
    res = parse_balance_v2_req_args(argv, argc, &req, used, line);
    if (res) goto ll_cleanup;

    /* params for redirect result verification */
    get_option_strings(argv, argc, used, "-contains", contains, &contains_count);
    get_option_strings(argv, argc, used, "-contains_any", contains_any, &contains_any_count);
    get_option_strings(argv, argc, used, "-excludes", excludes, &excludes_count);

    if (!contains_count && !contains_any_count && !excludes_count) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -contains|-contains_any|-excludes", line);
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto ll_cleanup;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) {
        res = ZPATH_RESULT_BAD_ARGUMENT;
        goto ll_cleanup;
    }

    req.broker_count = &broker_count;
    req.brokers = brokers;
    req.is_lp_enabled = zpath_get_logical_partition_feature_status();
    req.brokers_type = brokers_type;
    req.instance_info = &instance_info[0];
    req.instance_info_count = &instance_info_count;

    req.sni_suffix_count = &sni_suffix_count;
    req.sni_suffix = sni_suffixes;
    req.alt_cloud_supported = broker_alt_cloud_support_enabled;
    req.alt_cloud_aware = 0;
    req.exclude_self = 0;
    req.remote_ip = NULL;

    res = zpn_balance_redirect(&req);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpn_balance_redirect returned %s", line, zpath_result_string(res));
        goto ll_cleanup;
    }

    ZPATH_LOG(AL_DEBUG, "zpn_broker_balance_redirect, customer_gid = %ld, returned broker_count = %d",
                        (long) req.customer_gid, broker_count);

    for (i = 0; i < broker_count; i++) {
        ZPATH_LOG(AL_DEBUG, "Found brokers %s", brokers[i]);
    }

    for (j = 0; j < contains_count; j++) {
        for (i = 0; i < broker_count; i++) {
            if(0 == strcmp(brokers[i], contains[j])) break;
        }
        if (i == broker_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpn_balance_redirect: broker %s not found", line, contains[j]);
            res = ZPATH_RESULT_NOT_FOUND;
            goto ll_cleanup;
        }
    }
    if (contains_any_count) {
        int found_any_count = 0;
        for (j = 0; j < contains_any_count; j++) {
            for (i = 0; i < broker_count; i++) {
                if (0 == strcmp(brokers[i], contains_any[j])) {
                    found_any_count++;
                    break;
                }
            }
        }
        if (!found_any_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpn_balance_redirect: none of the expected brokers is found", line);
            res = ZPATH_RESULT_NOT_FOUND;
            goto ll_cleanup;
        }
        ZPATH_LOG(AL_NOTICE, "Line %d: zpn_balance_redirect: found %d out of the %d expected brokers", line, found_any_count, contains_any_count);
    }

    for (j = 0; j < excludes_count; j++) {
        for (i = 0; i < broker_count; i++) {
            if(0 == strcmp(brokers[i], excludes[j])) break;
        }
        if (i < broker_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpn_balance_redirect: broker %s was found, but expected not to", line, excludes[j]);
            res = ZPATH_RESULT_ERR;
            goto ll_cleanup;
        }
    }

ll_cleanup:
    free_balance_v2_req_args(&req);
    for (i = 0; i < broker_count; i++) {
        if (brokers[i]) {
            ZPE_FREE(brokers[i]);
        }
    }
    for (i = 0; i < sni_suffix_count; i++) {
        if (sni_suffixes[i]) {
            ZPE_FREE(sni_suffixes[i]);
        }
    }
    return res;
}

/* verify zpn_broker_balance_redirect() */
int process_brk_redir_verify_line(char **argv, int argc, int line)
{
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    char *contains = NULL;
    char *excludes = NULL;
    char *lat_str = NULL;
    char *lon_str = NULL;
    double remote_lat = 0.0;
    double remote_lon = 0.0;
    char *remote_cc = NULL;
    int broker_count = 0;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int brokers_type[ZPN_CLIENT_MAX_BROKERS] = {0};
    struct zpn_instance_info instance_info[ZPN_CLIENT_MAX_BROKERS] = {0};
    int instance_info_count = 0;
    int sni_suffix_count = 0;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];
    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);
    scope_gid = get_option_int64(argv, argc, used, "-scope", customer_gid);
    contains  = get_option_string(argv, argc, used, "-contains", NULL);
    excludes  = get_option_string(argv, argc, used, "-excludes", NULL);
    lat_str   = get_option_string(argv, argc, used, "-latitude", NULL);
    lon_str   = get_option_string(argv, argc, used, "-longitude", NULL);
    remote_cc = get_option_string(argv, argc, used, "-country", NULL);

    if ((!contains && !excludes) || !lat_str || !lon_str || !remote_cc || !customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: [-customer, -contains|-excludes, -latitude, -logitude, -country", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    remote_lat = strtod(lat_str, NULL);
    remote_lon = strtod(lon_str, NULL);

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zpn_broker_balance_redirect(customer_gid, scope_gid, NULL,
                                      NULL, remote_lat, remote_lon, remote_cc, NULL,
                                      &broker_count, &brokers[0], &brokers_type[0], &instance_info[0], &instance_info_count,
                                      &sni_suffix_count, &sni_suffixes[0],
                                      NULL, zpn_client_type_invalid, 0, 0, 0, 0, 0, "Unit Test");
    if (res) {
        ZPATH_LOG(AL_ERROR, "Line %d: zpn_broker_balance_redirect returned %s", line, zpath_result_string(res));
        return res;
    }

    ZPATH_LOG(AL_DEBUG, "zpn_broker_balance_redirect, customer_gid = %ld, returned broker_count = %d",
                        (long) customer_gid, broker_count);

    for (i = 0; i < broker_count; i++) {
        ZPATH_LOG(AL_DEBUG, "Found brokers %s", brokers[i]);
    }

    if (contains) {
        for (i = 0; i < broker_count; i++) {
            if(0 == strcmp(brokers[i], contains)) break;
        }
        if (i == broker_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpn_broker_balance_redirect: broker %s not found", line, contains);
            return ZPATH_RESULT_NOT_FOUND;
        }
    }
    if (excludes) {
        for (i = 0; i < broker_count; i++) {
            if(0 == strcmp(brokers[i], excludes)) break;
        }
        if (i < broker_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: zpn_broker_balance_redirect: broker %s was found, but expected not to", line, excludes);
            return ZPATH_RESULT_ERR;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* verify get_viable_brokers() */
int process_viable_brk_verify_line(char **argv, int argc, int line)
{
    int res;
    int used[MAX_ARGS];
    int broken = 0;
    char *scope_id = NULL;
    char *contains = NULL;
    char *excludes = NULL;
    size_t i;
    memset(used, 0, sizeof(used[0])*argc);

    scope_id = get_option_string(argv, argc, used, "-customer", NULL);
    contains  = get_option_string(argv, argc, used, "-contains", NULL);
    excludes  = get_option_string(argv, argc, used, "-excludes", NULL);

    int64_t scope_gid = scope_id ? strtoull(scope_id, NULL, 10) : 0;

    if ((!contains && !excludes) || !scope_gid) {

        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: [-scope, -contains|-excludes, -latitude, -logitude, -country", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    struct zpn_balance_redirect_request req = {0};
    struct balance_request_state state = {0};
    res = debug_run_and_analyze_common_return_state(NULL, NULL, scope_id, NULL, NULL, NULL, "US", NULL, NULL, NULL, NULL, 1, 1, DBG_EXEC_PHASE_INST_FILTER, 0, 1, 0, 0, 0, 0, 0, &req, &state);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Failed to get the list of viable brokers for scope_gid = %" PRId64 ", %s",
                scope_gid, zpn_error_description_string(res));
        goto ll_done;
    }

    ZPATH_LOG(AL_DEBUG, "zpn_broker_balance_redirect, scope_gid = %ld, returned broker_count = %ld",
                        (long) scope_gid, (long) state.viable_instance_count);
    for (i = 0; i < state.viable_instance_count; i++) {
        ZPATH_LOG(AL_DEBUG, "Found brokers %s", state.viable_instances[i].name);
    }

    if (contains) {
        for (i = 0; i < state.viable_instance_count; i++) {
            if(!state.viable_instances[i].exclude && 0 == strcmp(state.viable_instances[i].name, contains)) break;
        }
        if (i == state.viable_instance_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: get_viable_brokers: broker %s not found", line, contains);
            res = ZPATH_RESULT_NOT_FOUND;
            goto ll_done;
        }
    }
    if (excludes) {
        for (i = 0; i < state.viable_instance_count; i++) {
            if(!state.viable_instances[i].exclude && !strcmp(state.viable_instances[i].name, excludes)) break;
        }
        if (i < state.viable_instance_count) {
            ZPATH_LOG(AL_ERROR, "Line %d: get_viable_brokers: broker %s was found, but expected not to", line, excludes);
            res = ZPATH_RESULT_ERR;
            goto ll_done;
        }
    }

ll_done:
    /* Cleanup */
    zpn_balance_redirect_free(&state);
    if (req.trusted_networks)
        argo_object_release(req.trusted_networks);

    return res;
}

int process_zpn_private_broker_load(char **argv, int argc, int line)
{
    int res;
    int used[MAX_ARGS];
    int broken = 0;

    int64_t pbroker_gid = 0;
    int64_t customer_gid = 0;
    int64_t min_gid = 72057594037927936; /*2^56*/

    size_t i;

    memset(used, 0, sizeof(used[0])*argc);

    pbroker_gid = get_option_int64(argv, argc, used, "-pbroker", 0);
    customer_gid = get_option_int64(argv, argc, used, "-customer", 0);

    if (!pbroker_gid || !customer_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: Missing argument: -pbroker and -customer", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (pbroker_gid < min_gid || customer_gid < min_gid) {
        ZPATH_LOG(AL_ERROR, "Line %d: pbroker gid and customer gid must be equal to or larger than 72057594037927936 (2^56)", line);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    used[0] = 1;
    for (i = 0; i < argc; i++) {
        if (!used[i]) {
            broken = 1;
            ZPATH_LOG(AL_ERROR, "Line %d: Unused argument: %s", line, argv[i]);
        }
    }
    if (broken) return ZPATH_RESULT_BAD_ARGUMENT;

    res = zevent_base_big_call(init_base,
                               init_zpn_private_broker_load,
                               NULL,
                               pbroker_gid,
                               NULL,
                               NULL,
                               NULL,
                               customer_gid);

    if (res) ZPATH_LOG(AL_ERROR, "Line %d: Failed to init zpn_private_broker_load, ret=%d", line, res);
    return res;
}
