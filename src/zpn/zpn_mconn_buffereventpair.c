/*
 * zpn_mconn_buffereventpair.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include "zlibevent/zlibevent_bufferevent.h"
#include "zpn/zpn_mconn_buffereventpair.h"
#include "zpn/zpn_lib.h"

/*
 * Locking: it is assumed mtunnel is not locked at this point
 */
static void mconn_buffereventpair_receive(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;
    struct bufferevent *bev = mconn_b->bev;
    struct evbuffer *buf;
    size_t len;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn *mconn;

    __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);

    if (!bev) {
        ZPN_DEBUG_MCONN("%p: Already closed bev", mconn_b);
        return;
    }

    mconn = &(mconn_b->mconn);
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               int_cookie)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return;
    }

    //if (mconn_b->rx_paused) {
    //    goto exit;
    //}

    buf = bufferevent_get_input(bev);
    len = evbuffer_get_length(buf);

    if (len) {
        res = zpn_client_process_rx_data(&(mconn_b->mconn), buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

    if (mconn_b->mconn.peer && mconn_b->rx_fin_pending && !evbuffer_get_length(buf)) {
        ZPN_DEBUG_MCONN("Send out pending rx FIN after drained RX data");
        zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
        mconn_b->rx_fin_pending = 0;
    }

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }
}

static void bev_read_cb(struct bufferevent *bev, void *cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = 0;

    if (mconn_b->rx_paused) {
        return;
    }

    value = __sync_add_and_fetch_4(&(mconn_b->async_rx_count), 1);
    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
        return;
    }

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         mconn_buffereventpair_receive,
                         mconn_b,
                         incarnation) != FOHH_RESULT_NO_ERROR) {
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
    }
}

#define ZPN_MCONN_TCP_MAX_OUTSTANDING_BYTES_IN_WRITE_BUF (1 * 1024 * 1024) /* 1MB is good enough */
static int mconn_buffereventpair_transmit(struct zpn_mconn_buffereventpair *mconn_b, int need_lock, int64_t int_cookie)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int res = ZPN_RESULT_NO_ERROR;
    int enq_len = 0;

    if (mconn->global_owner) {
        if (need_lock && mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return res;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return res;
    }


    if (!mconn_b->bev) {
        /* Already closed? */
        goto exit;
    }

    if (mconn->global_owner_calls && (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               int_cookie))) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        goto exit;
    }

    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        if (!mconn_b->bev || mconn->fin_sent || mconn->drop_tx) {
            /* Already closed? Drop all the data */
            zpn_mconn_drain_transmit_buffer(mconn, &enq_len);
        } else {
            /*
             * Send out the data. Make sure we don't enqueue more than ZPN_MCONN_MAX_CLIENT_TX_DATA bytes of data
             * into the output buffer of bev
             */
            size_t input_len = zpn_mconn_get_transmit_buffer_len(mconn);
            size_t output_existing_buffered_data_len = evbuffer_get_length(bufferevent_get_output(mconn_b->bev));
            ssize_t output_available_space_len = ZPN_MCONN_TCP_MAX_OUTSTANDING_BYTES_IN_WRITE_BUF - output_existing_buffered_data_len;
            ssize_t output_len = (input_len < output_available_space_len) ? input_len : output_available_space_len;
            if (output_len <= 0) {
                ZPN_DEBUG_MCONN("%p: throttled data from client(%zd) output queue length(%zd)",
                                mconn_b, input_len, output_existing_buffered_data_len);
                goto done_sending_data_to_server;
            }

            enq_len = zpn_mconn_send_transmit_buffer(mconn, bufferevent_get_output(mconn_b->bev), output_len);

            mconn_b->mconn.bytes_to_client += enq_len;

            zpn_mconn_track_perf_egress(mconn);

done_sending_data_to_server:
            if (zpn_mconn_get_transmit_buffer_len(mconn)) {
                /* We didn't send out everything */
                mconn_b->tx_paused = 1;
                res = ZPN_RESULT_WOULD_BLOCK;
            }
        }
    }

    /*
     * The peer mconn have receveid FIN and we are done with transmitting all the data,
     * just forward FIN
     */
    if (mconn->client_needs_to_forward && !mconn->fin_sent &&
        (!zpn_mconn_transmit_buffer_exists(mconn) ||
         !zpn_mconn_get_transmit_buffer_len(mconn)) &&
        !evbuffer_get_length((bufferevent_get_output(mconn_b->bev)))) {
        struct bufferevent* partner = bufferevent_pair_get_partner(mconn_b->bev);
        if (partner) {
            bufferevent_trigger_event(partner, BEV_EVENT_EOF, BEV_OPT_DEFER_CALLBACKS );
        }
    }

    //if (mconn->client_needs_to_disconnect_local_owner && !mconn_b->is_connector_tun) {
        /* We are waiting to close the bufferevent once data is sent */
    //    struct bufferevent* partner = bufferevent_pair_get_partner(mconn_b->bev);
    //    if (partner) {
    //        zlibevent_bufferevent_free(partner);
    //    }
    //    zlibevent_bufferevent_free(mconn_b->bev);
    //    mconn_b->bev = NULL;
    //    res = ZPN_RESULT_NO_ERROR;
    //}

    if ((enq_len > 0) && mconn->peer) {
        ZPN_DEBUG_MCONN("window update, enq_len = %d", enq_len);
        zpn_mconn_client_window_update(mconn->peer, 0, (int)enq_len, 0);
    }

exit:
    if (need_lock && mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static void async_transmit(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;

    __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);

    if (mconn_b->mconn.global_owner_incarnation != int_cookie) {
        ZPN_DEBUG_MCONN("Incarnation changed, our incarnation = %ld, int_cookie = %ld",
                        (long)mconn_b->mconn.global_owner_incarnation, (long)int_cookie);
        return;
    }

    mconn_buffereventpair_transmit(mconn_b, 1, int_cookie);
}

static void bev_write_cb(struct bufferevent *bev, void *cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);

    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
        return;
    }

    mconn_b->tx_paused = 0;

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         async_transmit,
                         mconn_b,
                         incarnation) != FOHH_RESULT_NO_ERROR) {
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
    }
}

static void mconn_buffereventpair_event_process(struct zpn_mconn_buffereventpair *mconn_b, short style)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    struct bufferevent *bev = NULL;
    struct evbuffer *buf;
    size_t len;
    int bev_errno_save = EVUTIL_SOCKET_ERROR(); /* Save bev errno */

    char *b_reading = "";
    char *b_writing = "";
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";
    char *b_connected = "";

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    ZPN_DEBUG_MCONN("Connection: Received event %s%s%s%s%s%s, %s",
                    b_reading,
                    b_writing,
                    b_eof,
                    b_error,
                    b_timeout,
                    b_connected,
                    zpn_mconn_type_str(mconn->type));

    if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {

        if (mconn->global_owner) {
            if (mconn->global_owner_calls) {
                    (mconn->global_owner_calls->lock)(mconn,
                                                      mconn->self,
                                                      mconn->global_owner,
                                                      mconn->global_owner_key,
                                                      mconn->global_owner_key_length);
            }
        } else {
            return;
        }

        if (!mconn->global_owner) {
            /*
             * Global owner might be gone after we grab the lock, so check it again.
             * Bail if that's the case.
             */
            (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
            return;
        }

        bev = mconn_b->bev;
        if (!bev) {
            ZPN_DEBUG_MCONN("No bev, cannot process event");
            if (mconn->global_owner_calls) {
                (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
                return;
            }else {
                return;
            }
        }

        buf = bufferevent_get_input(bev);
        len = evbuffer_get_length(buf);

        if (mconn->global_owner_calls) {

            /* Only log if errno is set */
            if (bev_errno_save > 0) {

                /* Skip logging on: EAGAIN,EINPROGRESS + EOF */
                if ( ! (((bev_errno_save == EAGAIN) || (bev_errno_save == EINPROGRESS)) && (style & BEV_EVENT_EOF)) ) {
                    ZPN_DEBUG_MCONN("%s: Connection received events: %s%s%s%s%s%s, error: %s, queued input len: %d",
                            (mconn->global_owner_calls->id)(mconn,
                                                            mconn->self,
                                                            mconn->global_owner,
                                                            mconn->global_owner_key,
                                                            mconn->global_owner_key_length),
                            b_reading,
                            b_writing,
                            b_eof,
                            b_error,
                            b_timeout,
                            b_connected,
                            evutil_socket_error_to_string(bev_errno_save), (int)len);
                }
            }
        }

        if (len) {
            ZPN_DEBUG_MCONN("Got EOF or ERR but still %d bytes in socket", (int)len);
            mconn_b->rx_fin_pending = 1;
            bev_read_cb(bev, mconn_b);
        }

        mconn->fin_rcvd = 1;
        if (!mconn->fin_rcvd_us) mconn->fin_rcvd_us = epoch_us();

        if (!mconn->peer || (style & (BEV_EVENT_ERROR | BEV_EVENT_WRITING))) {
            ZPN_DEBUG_MCONN("mconn_bufferevent got EOF or ERROR, no peer");
            mconn->drop_tx = 1;
            mconn->fin_sent = 1;
        }

        if (mconn->peer && (!mconn_b->rx_fin_pending || mconn->drop_tx)) {
            ZPN_DEBUG_MCONN("mconn_bufferevent got EOF or ERROR, forward mtunnel_end, drop_tx = %d", mconn->drop_tx);
            zpn_mconn_forward_mtunnel_end(mconn->peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
        }

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        }

    } else {
        /* Connection initialized... Not really anything to do here. */
    }
}

static void async_event(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;
    short style = (short)int_cookie;

    __sync_sub_and_fetch_4(&(mconn_b->async_event_count), 1);
    mconn_buffereventpair_event_process(mconn_b, style);
}

static void bev_event_cb(struct bufferevent *bev, short style, void *cookie)
{
    struct zpn_mconn_buffereventpair *mconn_b = cookie;

    __sync_add_and_fetch_4(&(mconn_b->async_event_count), 1);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         async_event,
                         mconn_b,
                         style) != FOHH_RESULT_NO_ERROR) {
        __sync_sub_and_fetch_4(&(mconn_b->async_event_count), 1);
    }
}

static int zpn_mconn_buffereventpair_bind_cb(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length,
                                             int64_t *owner_incarnation)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;
    struct evbuffer *buf;
    size_t len;

    mconn_b->bev = owner;

    /* In case there is some data... */
    buf = bufferevent_get_input(mconn_b->bev);
    len = evbuffer_get_length(buf);

    if (len) {
        int res;

        res = zpn_client_process_rx_data(&(mconn_b->mconn), buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Bind cb process_rx data returned %s", zpn_result_string(res));
        }
    }

    bufferevent_setcb(mconn_b->bev, bev_read_cb, bev_write_cb, bev_event_cb, mconn_b);

    /* This enable should invoke a read callback if there is data
     * waiting for us on this bufferevent already */
    bufferevent_enable(mconn_b->bev, EV_READ|EV_WRITE);
    if (flow_control_enabled) {
        /*
         * write:
         *  No watermark is set, as we don't use bufferevent_write routine but manipulate the evbuffer directly. With
         *  this even if we have watermark, it is not effective.
         *
         * read:
         *  allow read callback into itasca even when there is 1 byte of data.
         *  stop reading from the network if the buffer has more than or equal to ZPN_MCONN_MAX_CLIENT_TX_DATA bytes
         */
        bufferevent_setwatermark(mconn_b->bev, EV_READ, 0, ZPN_MCONN_MAX_CLIENT_TX_DATA);
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_mconn_buffereventpair_unbind_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *owner,
                                               void *owner_key,
                                               size_t owner_key_length,
                                               int64_t owner_incarnation,
                                               int drop_buffered_data,
                                               int dont_propagate,
                                               const char *err)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;

    if (mconn_b->is_connector_tun) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn_b->bev) {
        if (zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn))) {
            ZPN_DEBUG_MCONN("Data hasn't been sent out yet, flushing bytes = %lu", zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn)));
            mconn_b->mconn.client_needs_to_disconnect_local_owner = 1;
        } else {
            zpn_mconn_forward_mtunnel_end(&(mconn_b->mconn), err, drop_buffered_data);
            struct bufferevent* partner = bufferevent_pair_get_partner(mconn_b->bev);
            if (partner) {
                zlibevent_bufferevent_free(partner);
            }
            zlibevent_bufferevent_free(mconn_b->bev);
            mconn_b->bev = NULL;
        }
    } else {
        mconn_b->mconn.fin_sent = 1;
        mconn_b->mconn.fin_rcvd = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}


static void zpn_mconn_buffereventpair_lock_cb(void *mconn_base,
                                              void *mconn_self,
                                              void *owner,
                                              void *owner_key,
                                              size_t owner_key_length)
{
}


static void zpn_mconn_buffereventpair_unlock_cb(void *mconn_base,
                                                void *mconn_self,
                                                void *owner,
                                                void *owner_key,
                                                size_t owner_key_length)
{
}

/*
 * Locking: it is assumed mtunnel is locked at this point
 */
static int zpn_mconn_buffereventpair_transmit_cb(void *mconn_base,
                                                 void *mconn_self,
                                                 void *owner,
                                                 void *owner_key,
                                                 size_t owner_key_length,
                                                 int64_t owner_incarnation,
                                                 int fohh_thread_id,
                                                 struct evbuffer *buf,
                                                 size_t buf_len)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;
    int res = ZPN_RESULT_NO_ERROR;


    /* XXX Implement Me- Backoff on large buffering. */
    if (mconn_b->tx_paused) {
        ZPN_DEBUG_MCONN("zpn_mconn_bufferevent tx paused");
        res = ZPN_RESULT_WOULD_BLOCK;
    } else {
        struct zpn_mconn *mconn = &(mconn_b->mconn);
        int64_t incarnation;
        int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);

        if (value > 2) {
            /* We already have thread call outstanding, no need to make call */
            __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
        } else {
            incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                   mconn->self,
                                                                   mconn->global_owner,
                                                                   mconn->global_owner_key,
                                                                   mconn->global_owner_key_length);
            if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                                 async_transmit,
                                 mconn_b,
                                 incarnation) != FOHH_RESULT_NO_ERROR) {
                __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
            }
        }
    }

    return res;
}


static int zpn_mconn_buffereventpair_pause_cb(void *mconn_base,
                                              void *mconn_self,
                                              void *owner,
                                              void *owner_key,
                                              size_t owner_key_length,
                                              int64_t owner_incarnation,
                                              int fohh_thread_id)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;

    mconn_b->rx_paused = 1;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_buffereventpair_resume_cb(void *mconn_base,
                                                void *mconn_self,
                                                void *owner,
                                                void *owner_key,
                                                size_t owner_key_length,
                                                int64_t owner_incarnation,
                                                int fohh_thread_id)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;

    if (mconn_b && mconn_b->bev) {

        struct zpn_mconn *mconn = &(mconn_b->mconn);
        int64_t incarnation;
        int value = __sync_add_and_fetch_4(&(mconn_b->async_rx_count), 1);

        mconn_b->rx_paused = 0;

        if (value > 2) {
            /* We already have thread call outstanding, no need to make call */
            __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
        } else {

            incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length);

            if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                                 mconn_buffereventpair_receive,
                                 mconn_b,
                                 incarnation) != FOHH_RESULT_NO_ERROR) {
                __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_buffereventpair_init(struct zpn_mconn_buffereventpair *mconn_buffereventpair,
                                   void *mconn_self,
                                   enum zpn_mconn_type type)
{
    mconn_buffereventpair->bev = NULL;
    mconn_buffereventpair->rx_paused = 0;
    mconn_buffereventpair->tx_paused = 0;
    mconn_buffereventpair->rx_bytes = 0;
    mconn_buffereventpair->tx_bytes = 0;
    mconn_buffereventpair->rx_fin_pending = 0;
    mconn_buffereventpair->tx_fin_pending = 0;
    mconn_buffereventpair->is_connector_tun = 0;
    mconn_buffereventpair->async_tx_count = 0;
    mconn_buffereventpair->async_rx_count = 0;

    return zpn_mconn_init(&(mconn_buffereventpair->mconn), mconn_self, type);
}

int zpn_mconn_buffereventpair_forward_tunnel_end_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    const char *err,
                                                    int32_t drop_data)
{
    struct zpn_mconn_buffereventpair *mconn_b = mconn_base;

    if (!mconn_b->bev) {
        ZPN_DEBUG_MCONN("No underline bev");
        mconn_b->mconn.fin_sent = 1;
        return ZPN_RESULT_NO_ERROR;
    }

    if (drop_data) {
        ZPN_DEBUG_MCONN("Need to drop all data");
        if (zpn_mconn_transmit_buffer_exists(&(mconn_b->mconn))) {
            size_t len = zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn));

            zpn_mconn_free_transmit_buffer(&(mconn_b->mconn));
            if (len && mconn_b->mconn.peer) {
                zpn_mconn_client_window_update(mconn_b->mconn.peer, 0, (int)len, 0);
            }
        }
    }

    if (!mconn_b->mconn.fin_sent) {
        mconn_b->mconn.client_needs_to_forward = 1;
        if (!mconn_b->tx_fin_pending) {
            struct zpn_mconn *mconn = &(mconn_b->mconn);
            int64_t incarnation;
            int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);

            if (value > 2) {
                /* We already have thread call outstanding, no need to make call */
                __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
            } else {

                incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length);

                if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                                 async_transmit,
                                 mconn_b,
                                 incarnation) != FOHH_RESULT_NO_ERROR) {
                    __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
                }
                mconn_b->tx_fin_pending = 1;
            }
        }
    }



    if (((mconn_b->mconn.fin_rcvd && !mconn_b->rx_fin_pending) || drop_data)  &&
        mconn_b->mconn.peer && !mconn_b->mconn.fin_refl) {

        ZPN_DEBUG_MCONN("Reflect mtunnel_end since we already reaceive one");
        if (drop_data) {
            /* Since we don't care about data, pretend we got FIN so we close it quickly */
            mconn_b->mconn.fin_rcvd = 1;
        }

        if (!mconn_b->is_connector_tun) {
            zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, drop_data);
            mconn_b->mconn.fin_refl = 1;
        } else {
            ZPN_DEBUG_MCONN("Is connector tun, no need to reflect");
        }
    } else {
        if (mconn_b->mconn.fin_refl) {
            ZPN_DEBUG_MCONN("Already reflected");
        } else if (!mconn_b->mconn.fin_rcvd) {
            ZPN_DEBUG_MCONN("Don't reflect, still waiting to receive EOF");
        } else {
            ZPN_DEBUG_MCONN("Don't reflect, no peer");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_buffereventpair_window_update_cb(void *mconn_base,
                                                void *mconn_self,
                                                void *owner,
                                                void *owner_key,
                                                size_t owner_key_length,
                                                int64_t owner_incarnation,
                                                int fohh_thread_id,
                                                int tx_len,
                                                int batch_win_upd)
{
    return;
}

void zpn_mconn_buffereventpair_stats_update_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *owner,
                                               void *owner_key,
                                               size_t owner_key_length,
                                               int64_t owner_incarnation,
                                               int fohh_thread_id,
                                               enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_buffereventpair_disable_read_cb(void *mconn_base,
                                              void *mconn_self,
                                              void *owner,
                                              void *owner_key,
                                              size_t owner_key_length,
                                              int64_t owner_incarnation,
                                              int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_buffereventpair_enable_read_cb(void *mconn_base,
                                              void *mconn_self,
                                              void *owner,
                                              void *owner_key,
                                              size_t owner_key_length,
                                              int64_t owner_incarnation,
                                              int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Callbacks exposed to mconn
 */
const struct zpn_mconn_local_owner_calls mconn_buffereventpair_calls = {
    zpn_mconn_buffereventpair_bind_cb,
    zpn_mconn_buffereventpair_unbind_cb,
    zpn_mconn_buffereventpair_lock_cb,
    zpn_mconn_buffereventpair_unlock_cb,
    zpn_mconn_buffereventpair_transmit_cb,
    zpn_mconn_buffereventpair_pause_cb,
    zpn_mconn_buffereventpair_resume_cb,
    zpn_mconn_buffereventpair_forward_tunnel_end_cb,
    zpn_mconn_buffereventpair_window_update_cb,
    zpn_mconn_buffereventpair_stats_update_cb,
    zpn_mconn_buffereventpair_disable_read_cb,
    zpn_mconn_buffereventpair_enable_read_cb
};


/* For UT */
struct zpn_mconn_local_owner_calls ut_mconn_buffereventpair_calls = {
    zpn_mconn_buffereventpair_bind_cb,
    zpn_mconn_buffereventpair_unbind_cb,
    zpn_mconn_buffereventpair_lock_cb,
    zpn_mconn_buffereventpair_unlock_cb,
    zpn_mconn_buffereventpair_transmit_cb,
    zpn_mconn_buffereventpair_pause_cb,
    zpn_mconn_buffereventpair_resume_cb
};

int zpn_mconn_buffereventpair_done(struct zpn_mconn_buffereventpair *mconn_buffereventpair)
{
    return zpn_mconn_done(&mconn_buffereventpair->mconn);
}

int zpn_mconn_buffereventpair_clean(struct zpn_mconn_buffereventpair *mconn_buffereventpair)
{
    int ret = 1;

    if (mconn_buffereventpair->bev) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean -- has bev");
        ret = 0;
    }

    if (mconn_buffereventpair->async_tx_count) {
        ZPN_DEBUG_MCONN("mconn buffereventpair not clean, async_tx_count = %ld", (long)mconn_buffereventpair->async_tx_count);
        return 0;
    }

    if (mconn_buffereventpair->async_rx_count) {
        ZPN_DEBUG_MCONN("mconn buffereventpair not clean, async_rx_count = %ld", (long)mconn_buffereventpair->async_rx_count);
        return 0;
    }

    if (mconn_buffereventpair->async_event_count) {
        ZPN_DEBUG_MCONN("mconn buffereventpair not clean, async_event_count = %ld", (long)mconn_buffereventpair->async_event_count);
        return 0;
    }

    if (!zpn_mconn_clean(&(mconn_buffereventpair->mconn))) {
        ZPN_DEBUG_MCONN("mconn buffereventpair not clean");
        ret = 0;
    }

    ZPN_DEBUG_MCONN("mconn buffereventpair clean");
    return ret;
}

void zpn_mconn_buffereventpair_internal_display(struct zpn_mconn_buffereventpair *mconn_buffereventpair)
{
    struct evbuffer *buf;

    zpn_mconn_internal_display(&(mconn_buffereventpair->mconn));
    ZPN_DEBUG_MCONN("rx_paused = %d, tx_paused = %d", mconn_buffereventpair->rx_paused, mconn_buffereventpair->tx_paused);
    ZPN_DEBUG_MCONN("rx_bytes = %ld, tx_bytes = %ld", (long)mconn_buffereventpair->rx_bytes, (long)mconn_buffereventpair->tx_bytes);
    if (mconn_buffereventpair->bev) {
        buf = bufferevent_get_input(mconn_buffereventpair->bev);
        ZPN_DEBUG_MCONN("input buffer len = %d", (int)evbuffer_get_length(buf));
        buf = bufferevent_get_output(mconn_buffereventpair->bev);
        ZPN_DEBUG_MCONN("output buffer len = %d", (int)evbuffer_get_length(buf));
    } else {
        ZPN_DEBUG_MCONN("No bev");
    }

    zpn_mconn_internal_display(&(mconn_buffereventpair->mconn));
}
