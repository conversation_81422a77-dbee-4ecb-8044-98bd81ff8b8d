/*
 * zpn_policy_engine_evaluate.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 */

/*
 * Policy engine evaluator. Evaluates policy built by zpn_policy_engine_build.
 */

#include "zpn/zpn_lib.h"
#include "zpath_lib/zpath_lib.h"
#include "zpn/zpn_policy_engine_build.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

/*
 * This feature define how we evaluate policy
 * 0: old way, 1: new way, 2: both and compare results
 */
int64_t g_policy_eval_type = 0;

const char *g_zpe_policy_type_stats_name[ZPE_POLICY_TYPE_COUNT] = {
#define XX(a, b) [a] = b,
    ZPE_POLICY_TYPE_XX(XX)
#undef XX
};

/*
 * do not set policy_eval_comp to non-zero for production
 * it is used for unit testing only
 */
int policy_eval_comp = 0;
int policy_eval_comp_perf = 0;
void policy_eval_comp_init()
{
    policy_eval_comp_perf = policy_eval_comp = 1;
    g_policy_eval_type = zpe_eval_both;
    zpn_debug_set(ZPN_DEBUG_ZPE_IDX);
    ZPN_LOG(AL_NOTICE, "policy_eval_type=%d", (int)g_policy_eval_type);
}
void policy_eval_comp_cancel()
{
    policy_eval_comp_perf = policy_eval_comp = 0;
    g_policy_eval_type = zpe_eval_old;
    ZPN_LOG(AL_NOTICE, "policy_eval_type=%d", (int)g_policy_eval_type);
}

void policy_eval_comp_perf_cancel()
{
    policy_eval_comp_perf = 0;
    ZPN_LOG(AL_NOTICE, "policy_eval_comp_perf=%d", policy_eval_comp_perf);
}

struct zpe_policy_eval_compare_stats zpe_policy_eval_comp_stats[ZTHREAD_MAX_THREADS] = {{0}};
struct zpe_policy_evaluate_stats zpe_policy_eval_stats[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS] = {{{0}}};
struct zpe_rule_evaluate_avg_time zpe_rule_eval_avg_time[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS] = {{{0}}};
struct zpe_rule_evaluate_avg_data_size zpe_rule_evaluate_avg_data_size[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS] = {{{0}}};
struct zpe_policy_mtunnel_eval_stats policy_mtunnel_eval_stats[ZTHREAD_MAX_THREADS] = {{0}};

static void operand2str(const struct zpe_operand *operand, char *printable, size_t printable_size)
{
    if (operand->is_gid) {
        snprintf(printable, printable_size, "%lu", (unsigned long) operand->gid);
    } else {
        snprintf(printable, printable_size, "%.*s", operand->data_len, (char*) operand->data);
    }
}

static int log_ret_val_for_operand_matches(struct zpe_operand *operand,
                                           int val)
{
    if (zpn_debug_get(ZPN_DEBUG_ZPE_DETAIL_IDX)) {
        char operand_as_str[256];
        operand2str(operand, operand_as_str, sizeof operand_as_str);
        ZPN_DEBUG_ZPE_DETAIL("      operand_matches(%s) returns %d", operand_as_str, val);;
    }
    return val;
}

static int operand_matches(struct zpe_operand *operand,
                           struct zhash_table *general_state,
                           struct zhash_table *saml_state,
                           struct zhash_table *scim_state,
                           struct zhash_table *int64_hash,
                           int debug_verbose) {
    if (operand->is_gid) {
        if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, evaluate gid operand: %"PRId64,  operand->gid);
        if (int64_hash) {
            if (zhash_table_lookup_with_hash(int64_hash, &(operand->gid), sizeof(operand->gid), operand->hash, NULL)) {
                if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, gid operand: %"PRId64" found in int_hash",  operand->gid);
                return log_ret_val_for_operand_matches(operand, 1);
            }
        }

        if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, gid operand: %"PRId64" not found",  operand->gid);
    } else {
        /* data_len - 1 since our data is all null terminated strings,
         * and we do not want to include the NULL character in the
         * hash lookup */
        if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, evaluate string operand: %s",  (char*)operand->data);
        if (general_state && zhash_table_lookup_with_hash(general_state, operand->data, operand->data_len - 1, operand->hash, NULL)) {
            if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, string operand: %s found in general_state",  (char*)operand->data);
            return log_ret_val_for_operand_matches(operand, 1);
        }
        if (saml_state && zhash_table_lookup_with_hash(saml_state, operand->data, operand->data_len - 1, operand->hash, NULL)) {
            if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, string operand: %s found in saml_state",  (char*)operand->data);
            return log_ret_val_for_operand_matches(operand, 1);
        }
        if (scim_state && zhash_table_lookup_with_hash(scim_state, operand->data, operand->data_len - 1, operand->hash, NULL)) {
            if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, string operand: %s found in scim_state",  (char*)operand->data);
            return log_ret_val_for_operand_matches(operand, 1);
        }
        if (debug_verbose) ZPN_LOG(AL_NOTICE, "POLICY_DEBUG, string operand: %s not found",  (char*)operand->data);
    }
    return log_ret_val_for_operand_matches(operand, 0);
}

static int condition_set_matches(struct zpe_condition_set *set,
                                 struct zhash_table *general_state,
                                 struct zhash_table *saml_state,
                                 struct zhash_table *scim_state,
                                 struct zhash_table *int64_hash,
                                 int debug_verbose) {
    int i;
    int result;

    if (set->operands_count == 0) {
        result = 1;
    } else {
        if (set->or_operands) {
            /* OR - any operand  matching accomplishes a match */
            result = 0;
            for (i = 0; i < set->operands_count; i++) {
                if (operand_matches(set->operands[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) {
                    ZPN_DEBUG_ZPE_DETAIL("    condition_set_matches: this is an OR condition set, and operand %d matches", i + 1);
                    result = 1;
                    break;
                }
            }
        } else {
            /* AND sets - any condition set not matching means a mismatch */
            result = 1;
            for (i = 0; i < set->operands_count; i++) {
                if (!operand_matches(set->operands[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) {
                    ZPN_DEBUG_ZPE_DETAIL("    condition_set_matches: this is an AND condition set, and operand %d does not match", i + 1);
                    result = 0;
                    break;
                }
            }
        }
    }
    if (set->negate) result ^= 1;
    ZPN_DEBUG_ZPE_DETAIL("    condition_set_matches: returning %d", result);
    if (debug_verbose) ZPN_LOG(AL_NOTICE, "-- POLICY_DEBUG, evaluate condition set, or_op=%d, negate=%d, operands_count=%d, result=%d",
                                          set->or_operands, set->negate, set->operands_count, result);
    return result;
}

static int rule_matches(struct zpe_rule *rule,
                        struct zhash_table *general_state,
                        struct zhash_table *saml_state,
                        struct zhash_table *scim_state,
                        struct zhash_table *int64_hash,
                        int debug_verbose) {
    int i;
    int result = 1;

    if (rule->zpe_condition_set_count == 0) goto done;
    if (rule->or_sets) {
        /* OR sets- any condition set matching accomplishes a match */
        for (i = 0; i < rule->zpe_condition_set_count; i++) {
            ZPN_DEBUG_ZPE_DETAIL("  rule_matches: Evaluating condition set %d", i + 1);
            if (condition_set_matches(rule->condition_sets[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) {
                ZPN_DEBUG_ZPE_DETAIL("  rule_matches: this is an OR rule and condition set %d returns 1; returning 1", i + 1);
                goto done;
            }
        }
        ZPN_DEBUG_ZPE_DETAIL("  rule_matches returning 0");
        result = 0;
        goto done;
    } else {
        /* AND sets - any condition set not matching means a mismatch */
        for (i = 0; i < rule->zpe_condition_set_count; i++) {
            if (!condition_set_matches(rule->condition_sets[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) {
                ZPN_DEBUG_ZPE_DETAIL("  rule_matches: this is an AND rule and condition set %d returns 0; returning 0", i + 1);
                result = 0;
                goto done;
            }
        }
        ZPN_DEBUG_ZPE_DETAIL("  rule_matches returning 1");
        goto done;
    }

done:
    if (debug_verbose) ZPN_LOG(AL_NOTICE, "++ POLICY_DEBUG, evaluate rule, gid=%"PRId64",or_sets=%d, condition_set_count=%d, result=%d",
                                          rule->gid, rule->or_sets, rule->zpe_condition_set_count, result);
    return result;
}

static int dump_int_hash_cb(void *cookie, void *object, void *data, size_t data_len)
{
    int64_t *gid = (int64_t *)data;
    char *dbg_str = (char *)cookie;
    ZPN_LOG(AL_NOTICE, "  %s:   %"PRId64, dbg_str, *gid);
    return ZPATH_RESULT_NO_ERROR;
}

static int dump_str_hash_cb(void *cookie, void *object, void *data, size_t data_len)
{
    char str[data_len+1];
    strncpy(str, (char *)data, data_len);  /* data may not be ended with \'0'*/
    str[data_len] = '\0';
    char *dbg_str = (char *)cookie;
    ZPN_LOG(AL_NOTICE, "  %s:   %s", dbg_str, str);
    return ZPATH_RESULT_NO_ERROR;
}

static int dump_app_to_rules_cb(void *cookie, void *object, void *key, size_t key_len)
{
    int i;
    int64_t  *app_gid = (int64_t *)key;
    struct zpe_policy_set *set = (struct zpe_policy_set *)cookie;
    struct zpe_mini_policy_set *m_policy = (struct zpe_mini_policy_set *)object;
    ZPN_DEBUG_ZPE("  **Policy Comparison: app_to_rules: app gid: %"PRId64", rule_count=%d", (*app_gid), m_policy->size);
    for (i = 0; i < m_policy->size; i++) {
        int idx = m_policy->rule_indexes[i];
        ZPN_DEBUG_ZPE( "  Policy Comparison: app_to_rules, app gid: %"PRId64", rule_order: %d, rule_gid: %"PRId64,
                     (*app_gid), idx+1, set->rules[idx]->gid);
    }
    return ZPATH_RESULT_NO_ERROR;
}
static void dump_policy_rules(struct zpe_policy_set_meta *meta,
                              struct zpe_policy_set *set,
                              struct zhash_table *app_to_rules,
                              struct zpe_mini_policy_set *rules_for_all)
{
    int64_t key = 0;
    int i;
    int64_t customer_gid = meta->customer_gid;
    int64_t scope_gid = meta->scope_gid;

    ZPN_DEBUG_ZPE("--Policy Comparison: dump rules, customer=%"PRId64", scope=%"PRId64", rule_count=%d",
                            customer_gid, scope_gid, set->zpe_rule_count);
    for (i = 0; i < set->zpe_rule_count; i++) {
        ZPN_DEBUG_ZPE("  Policy Comparison: rules, rule_order: %d, rule_gid: %"PRId64, i+1, set->rules[i]->gid);
    }

    ZPN_DEBUG_ZPE("--Policy Comparison: dump app_to_rules, customer=%"PRId64", scope=%"PRId64, customer_gid, scope_gid);
    zhash_table_walk(app_to_rules, &key, dump_app_to_rules_cb, set);

    ZPN_DEBUG_ZPE("--Policy Comparison: dump rules_for_all, customer=%"PRId64", scope=%"PRId64", rule_count=%d",
                            customer_gid, scope_gid, rules_for_all->size);
    for (i = 0; i < rules_for_all->size; i++) {
        int idx = rules_for_all->rule_indexes[i];
        ZPN_DEBUG_ZPE(" Policy Comparison: rules_for_all, rule_order: %d, rule_gid: %"PRId64, idx+1, set->rules[idx]->gid);
    }
}

/*
 * merge rules_for_an_app(app_to_rules.rule_indexes) and rules_for_all, and get the evaluated rule indexex in ascending order.
 * The values of rules_for_app and rules_for_all are rule indexes, and are both sorted in ascending order.
 * Return an array of index flag, if index marked as 1, the rule of that index should be evaluated.
 * e.g. if indexes is [0,0,1,0,1], the rule[2], rule[4] should be evaluated, rule[0], rule[1] and rule[3] should not be evaluated.
 * The caller should free the return array.
 */
static int8_t *get_rule_indexes(int64_t *app_gids,
                                int64_t app_gids_count,
                                struct zhash_table *app_to_rules,
                                struct zpe_mini_policy_set *rules_for_all,
                                int rule_count,
                                int *count_to_evaluate)
{
    int8_t *eval_indexes = NULL;
    int i = 0;
    int j = 0;
    int k = 0;

    (*count_to_evaluate) = 0;
    if (rule_count > 0) {
        eval_indexes = ZPE_CALLOC(rule_count*sizeof(int8_t));
        if (app_gids) {
            for (i = 0; i < app_gids_count; i++) {
                ZPN_DEBUG_ZPE("Get rules for app %"PRId64, app_gids[i]);
                struct zpe_mini_policy_set *rules_for_an_app = zhash_table_lookup(app_to_rules, &app_gids[i], sizeof(app_gids[i]), NULL);
                if (rules_for_an_app) {
                    for (j = 0; j < rules_for_an_app->size; j++) {
                        k = rules_for_an_app->rule_indexes[j];
                        if (eval_indexes[k] == 0) {
                            ZPN_DEBUG_ZPE("Set rule index for app %"PRId64", index=%d", app_gids[i], k);
                            eval_indexes[k] = 1;
                            (*count_to_evaluate)++;
                        }
                    }
                }
            }
        }

        if (rules_for_all) {
            for (j = 0; j < rules_for_all->size; j++) {
                k = rules_for_all->rule_indexes[j];
                if (eval_indexes[k] == 0) {
                    ZPN_DEBUG_ZPE("Set rule index for all apps, index=%d", k);
                    eval_indexes[k] = 1;
                    (*count_to_evaluate)++;
                }
            }
        }
    }

    return eval_indexes;
}

/* evaluate policy set for an application or an application group */
static struct zpe_rule *zpe_evaluate_new(int skip_default_rule,
                                         int debug_verbose,
                                         int64_t *app_gids,
                                         int64_t app_gids_count,
                                         struct zpe_policy_set *set,
                                         struct zhash_table *app_to_rules,
                                         struct zpe_mini_policy_set *rules_for_all,
                                         struct zhash_table *general_state,
                                         struct zhash_table *saml_state,
                                         struct zhash_table *scim_state,
                                         struct zhash_table *int64_hash,
                                         int *rules_evaluated,
                                         int *rule_order)
{
    int i = 0;
    int k = 0;
    int idx = -1;
    struct zpe_rule *rule = NULL;
    int count_to_evaluate = 0;
    int64_t app_gid = (app_gids && app_gids_count > 0) ? app_gids[0] : 0;
    int64_t customer_gid = (app_gid > 0) ? ZPATH_GID_GET_CUSTOMER_GID(app_gid) : 0;

    *rules_evaluated = 0;
    int8_t *indexes = get_rule_indexes(app_gids, app_gids_count, app_to_rules, rules_for_all, set->zpe_rule_count, &count_to_evaluate);
    ZPN_DEBUG_ZPE("Policy Comparison: new evaluate start. customer=%"PRId64", app_gid=%"PRId64", rule_count=%d", customer_gid, app_gid, count_to_evaluate);
    for (i = 0; i < set->zpe_rule_count && k < count_to_evaluate; i++) {
        if (indexes[i] == 0) continue;
        k++;
        ZPN_DEBUG_ZPE("Policy Comparison, customer=%"PRId64", app_gid=%"PRId64", new rule_order=%d, new rule_gid=%"PRId64,
                      customer_gid, app_gid, i+1, set->rules[i]->gid);
        if (skip_default_rule && i == set->zpe_rule_count-1) continue;
        (*rules_evaluated)++;
        if (rule_matches(set->rules[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) {
            idx = i;
            break;
        }
    }

    if (rule_order) *rule_order = idx + 1;
    rule = idx >= 0 ? set->rules[idx] : NULL;
    ZPN_DEBUG_ZPE("Policy Comparison: new evaluate end. customer=%"PRId64", app_gid=%"PRId64", rule_order=%d, rule_gid=%"PRId64", rules_evaluated=%d",
                  customer_gid, app_gid, idx+1, (rule?rule->gid:0), (*rules_evaluated));
    if (indexes) ZPE_FREE(indexes);
    return rule;
}

static struct zpe_rule *zpe_evaluate_old(int skip_default_rule,
                                         int debug_verbose,
                                         int64_t app_gid,
                                         struct zpe_policy_set *set,
                                         struct zhash_table *general_state,
                                         struct zhash_table *saml_state,
                                         struct zhash_table *scim_state,
                                         struct zhash_table *int64_hash,
                                         int *rules_evaluated,
                                         int *rule_order)
{
    int i;
    struct zpe_rule *rule = NULL;
    int z_rule_order = 0;

    *rules_evaluated = 0;
    /* For each rule in the policy set, see if it matches. */
    ZPN_DEBUG_ZPE("Policy Comparison: old evaluate start. app_gid=%"PRId64", rule_count=%d", app_gid, set->zpe_rule_count);
    int eval_count = (skip_default_rule && set->zpe_rule_count > 0) ? set->zpe_rule_count-1 : set->zpe_rule_count;
    for (i = 0; i < eval_count; i++) {
        ZPN_DEBUG_ZPE("Policy Comparison, old rule_order=%d, old rule_gid=%"PRId64, i+1, set->rules[i]->gid);
        (*rules_evaluated)++;
        if (rule_matches(set->rules[i], general_state, saml_state, scim_state, int64_hash, debug_verbose)) break;
    }

    if (i < eval_count) {
        z_rule_order = i + 1;
        rule = set->rules[i];
        ZPN_DEBUG_ZPE_DETAIL("zpe_evaluate_old: Rule %d matched", i + 1);
    } else {
        ZPN_DEBUG_ZPE_DETAIL("zpe_evaluate_old: No rule matched");
    }

    ZPN_DEBUG_ZPE("Policy Comparison: old evaluate end. app_gid=%"PRId64", rule_order=%d, rule_gid=%"PRId64,
                  app_gid, z_rule_order, (rule?rule->gid:0));

    if (rule_order) *rule_order = z_rule_order;
    return rule;
}

int zpe_evaluate(int skip_default_rule,
                 int debug_verbose,
                 int64_t *app_gids,
                 int64_t app_gids_count,
                 struct zpe_policy_built *policy_built,
                 struct zhash_table *general_state,
                 struct zhash_table *saml_state,
                 struct zhash_table *scim_state,
                 struct zhash_table *int64_hash,
                 const char * debug_str,
                 int *rules_evaluated,
                 int64_t *matched_rule,
                 enum zpe_access_action *matched_action,
                 char **matched_custom_msg,
                 int64_t *matched_reauth_timeout,
                 int64_t *matched_reauth_idle_timeout,
                 int64_t *matched_action_id)
{
    struct zpe_policy_set *set = NULL;
    struct zhash_table *app_to_rules = NULL;
    struct zpe_mini_policy_set *rules_for_all = NULL;
    int rules_evaluated_new = 0;
    int rules_evaluated_old = 0;
    struct zpe_rule *rule_new = NULL;
    struct zpe_rule *rule_old = NULL;
    int rule_order_new = 0;
    int rule_order_old = 0;
    struct zpe_rule *rule = NULL;
    int rule_order = 0;
    int64_t policy_eval_type = g_policy_eval_type;
    int64_t new_us = 0;
    int64_t old_us = 0;
    int re_evaluate = 0;
    int new_policy_available = 0;
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    enum zpe_policy_type policy_type = zpe_policy_type_deprecated;
    int max_debug_line_s_current = 0;
    static int max_debug_line_s_original = 0;
    static int reevaluation_count = 0;
    static int is_zpe_debug_on = 0;
    static int32_t eval_counter = 0;
    int64_t app_gid = (app_gids && app_gids_count > 0) ? app_gids[0] : 0;

    /* this function can be called in multiple threads concurrently, need the mutex to protect the static variables */
    static pthread_mutex_t local_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    if ((app_gids_count == 0) || !app_gids) policy_eval_type = zpe_eval_old;
    if (policy_built) {
        set = policy_built->policy_set;
        app_to_rules = policy_built->app_to_rules;
        rules_for_all = policy_built->rules_for_all;
        customer_gid = policy_built->policy_meta->customer_gid;
        scope_gid = policy_built->policy_meta->scope_gid;
        policy_type = policy_built->policy_meta->policy_type;
    }

    if (debug_verbose) {
        /*dump int_hash*/
        int64_t walk_key = 0;
        if (int64_hash) zhash_table_walk(int64_hash, &walk_key, dump_int_hash_cb, "POLICY_DEBUG, int64_hash: ");

        /*dump general_state*/
        walk_key = 0;
        if (general_state) zhash_table_walk(general_state, &walk_key, dump_str_hash_cb, "POLICY_DEBUG, general_state: ");

        /*dump saml_state*/
        walk_key = 0;
        if (saml_state) zhash_table_walk(saml_state, &walk_key, dump_str_hash_cb, "POLICY_DEBUG, saml_state: ");

        /*dump scim_state*/
        walk_key = 0;
        if (scim_state) zhash_table_walk(scim_state, &walk_key, dump_str_hash_cb, "POLICY_DEBUG, scim_state: ");
    }

    if (set == NULL || ((app_gids_count != 0) && app_to_rules == NULL)) {
        ZPN_LOG(AL_CRITICAL, "%s customer %"PRId64" scope %"PRId64" has a NULL policy set", debug_str, customer_gid, scope_gid);
        goto end;
    }


    int32_t eval_ct = (policy_eval_type == zpe_eval_both) ? __sync_fetch_and_add_4(&eval_counter, 1) : 0;
redo:

    if (policy_eval_type < zpe_eval_old || policy_eval_type > zpe_eval_both) {
        ZPN_LOG(AL_ERROR, "%s customer %"PRId64" scope %"PRId64"  has invalid policy_eval_type: %d, use default value:%d",
                          debug_str, customer_gid, scope_gid, (int)policy_eval_type, (int)DEFAULT_POLICY_EVAL_TYPE);
        policy_eval_type = DEFAULT_POLICY_EVAL_TYPE;
    }

    int eval_new = 0;
    if (policy_eval_type == zpe_eval_new) {
        eval_new = 1;
    } else if (policy_eval_type == zpe_eval_both) {
        /* evaluate new and old first in turn depending on the eval_counter */
        eval_new = (eval_ct%2 == 0) ? 1 : 0;
    }
    int i = 0;
    do {
        if (eval_new) {
            ZPN_DEBUG_ZPE("%s customer: %ld, scope %ld, new evaluation, policy_eval_type=%d, app_gid=%ld, app_gids_count=%"PRId64,
                          debug_str, (long)customer_gid, (long)scope_gid, (int)policy_eval_type, (long)app_gid, app_gids_count);
            new_us = epoch_us();

            rule_order_new = 0;
            // Do new policy evaluation for all the matched applications
            rule_new = zpe_evaluate_new(skip_default_rule,
                                        debug_verbose,
                                        app_gids,
                                        app_gids_count,
                                        set,
                                        app_to_rules,
                                        rules_for_all,
                                        general_state,
                                        saml_state,
                                        scim_state,
                                        int64_hash,
                                        &rules_evaluated_new,
                                        &rule_order_new);
            new_us = epoch_us() - new_us;
        } else {
            ZPN_DEBUG_ZPE("%s customer: %ld, scope %ld, old evaluation, policy_eval_type=%d, app_gid=%ld, app_gids_count=%"PRId64,
                          debug_str, (long)customer_gid, (long)scope_gid, (int)policy_eval_type, (long)app_gid, app_gids_count);
            old_us = epoch_us();
            rule_old = zpe_evaluate_old(skip_default_rule,
                                        debug_verbose,
                                        app_gid,
                                        set,
                                        general_state,
                                        saml_state,
                                        scim_state,
                                        int64_hash,
                                        &rules_evaluated_old,
                                        &rule_order_old);
            old_us = epoch_us() - old_us;
        }
        i++;
        eval_new = !eval_new;
    } while (policy_eval_type == zpe_eval_both && i < 2);

    struct zthread_info *self_zthread_info = zthread_self();
    int zthread_num = self_zthread_info->stack.thread_num;

    zpe_policy_eval_stats[policy_type][zthread_num].eval_count++;

    if (int64_hash)
        zpe_rule_evaluate_avg_data_size[policy_type][zthread_num].rule_evaluate_data_size_total +=
                int64_hash->element_data_size;
    if (general_state)
        zpe_rule_evaluate_avg_data_size[policy_type][zthread_num].rule_evaluate_data_size_total +=
                general_state->element_data_size;
    if (saml_state)
        zpe_rule_evaluate_avg_data_size[policy_type][zthread_num].rule_evaluate_data_size_total +=
                saml_state->element_data_size;
    if (scim_state)
        zpe_rule_evaluate_avg_data_size[policy_type][zthread_num].rule_evaluate_data_size_total +=
                scim_state->element_data_size;

    zpe_rule_evaluate_avg_data_size[policy_type][zthread_num].rule_evaluate_count++;

    if (policy_eval_type == zpe_eval_new) {
        rule = rule_new;
        rule_order = rule_order_new;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated_new += rules_evaluated_new;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated += rules_evaluated_new;
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_time_sum += new_us;
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_count += rules_evaluated_new;
    } else if (policy_eval_type == zpe_eval_old) {
        rule = rule_old;
        rule_order = rule_order_old;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated_old += rules_evaluated_old;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated += rules_evaluated_old;
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_time_sum += old_us;
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_count += rules_evaluated_old;
    } else {
        /*zpe_eval_both: compare old and new evaluations, if not match use old evaluation, otherwise use new evaluation */

        if (!re_evaluate) {
            /* overall total counts for this client since broker starts */
            zpe_policy_eval_comp_stats[zthread_num].eval_count++;
            zpe_policy_eval_comp_stats[zthread_num].rules_evaluated_new += rules_evaluated_new;
            zpe_policy_eval_comp_stats[zthread_num].rules_evaluated_old += rules_evaluated_old;
            zpe_policy_eval_comp_stats[zthread_num].rules_evaluated += (rules_evaluated_new + rules_evaluated_old);

        }
        /* zpe_policy_eval_comp_stats can be removed once we are get rid of
         * both new and old revaluation. Then below can also be removed.
         * Until then keep below.
         */
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated_new += rules_evaluated_new;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated_old += rules_evaluated_old;
        zpe_policy_eval_stats[policy_type][zthread_num].rules_evaluated += (rules_evaluated_new + rules_evaluated_old);
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_time_sum += (old_us + new_us);
        zpe_rule_eval_avg_time[policy_type][zthread_num].rule_evaluate_count += (rules_evaluated_old + rules_evaluated_new);

        if (rule_new == rule_old) {
            int64_t delta = old_us-new_us;
            int64_t rule_gid = rule_new ? rule_new->gid: 0;
            rule_order = rule_order_new;

            zpe_policy_eval_comp_stats[zthread_num].eval_enhance_us += delta;
            zpe_policy_eval_comp_stats[zthread_num].eval_old_us += old_us;

            ZPN_DEBUG_ZPE("Policy Performance Measurement: Policy Comparison,"
                          "%s customer=%"PRId64", scope=%"PRId64", policy_eval_type=%d, app_gid=%"PRId64", rule_gid=%"PRId64", rule_order=%d, policy_type=%s, "
                          "new_us=%"PRId64", old_us=%"PRId64", delta_us=%"PRId64", rules_evaluated_new=%d, rules_evaluated_old=%d",
			              debug_str, customer_gid, scope_gid, (int)policy_eval_type, app_gid,
                          rule_gid, rule_order, zpe_policy_type_string(policy_type),
                          new_us, old_us, delta, rules_evaluated_new, rules_evaluated_old);

            if (rules_evaluated_new > rules_evaluated_old) {
                zpe_policy_eval_comp_stats[zthread_num].eval_count_abnormal++;
                ZPN_LOG(AL_ERROR, "%s customer: %"PRId64", scope: %"PRId64", Policy Comparison warning: new policy evaluation is more expensive."
                                  "policy_eval_type=%d, app_gid=%"PRId64", rule_gid=%"PRId64", rule_order=%d, policy_type=%s, "
                                  "rules_evaluated_new=%d, rules_evaluated_old=%d",
                                  debug_str, customer_gid, scope_gid, (int)policy_eval_type, app_gid,
                                  rule_gid, rule_order, zpe_policy_type_string(policy_type),
                                  rules_evaluated_new, rules_evaluated_old);

                /* make the unit test fail. (policy_eval_comp should be set for unit test) */
                if (policy_eval_comp && policy_eval_comp_perf) return ZPATH_RESULT_ERR;
            }

            /* everything is OK, so use new evaluation results */
            *rules_evaluated = rules_evaluated_new;
            rule = rule_new;
        } else {
            int round = 1;
            char msg_str[32] = {0};
            if (!re_evaluate) {
                zpe_policy_eval_comp_stats[zthread_num].eval_count_fail_1++;
                snprintf(msg_str, sizeof(msg_str), "new_policy_available=unknown");
            } else {
                round++;
                zpe_policy_eval_comp_stats[zthread_num].eval_count_fail_2++;
                new_policy_available = is_new_policy_available(policy_built);

                /*
                 * If no new policy available (0), increment the counter, this is the mismatch we need to investigate.
                 * if new_policy_available is -1, it indicates an error happen when getting policy, we want to be alerted, so increment the counter too
                 */
                if (new_policy_available <= 0) zpe_policy_eval_comp_stats[zthread_num].eval_count_fail_wo_npol++;
                snprintf(msg_str, sizeof(msg_str), "new_policy_available=%d", new_policy_available);
            }
            ZPN_LOG(AL_ERROR, "Policy Comparison, %s customer: %"PRId64", scope: %"PRId64", Policy Evaluation mismatch round %d, %s, app_gid=%"PRId64", policy_type=%s"
                              " new rule_gid=%"PRId64", rule_order_new = %d, old rule_gid=%"PRId64", rule_order_old=%d, new msg=%s, old msg=%s",
                              debug_str, customer_gid, scope_gid, round, msg_str, app_gid, zpe_policy_type_string(policy_type) ,(rule_new ? rule_new->gid: 0), rule_order_new,
                              (rule_old ? rule_old->gid: 0), rule_order_old, rule_new ? rule_new->message: NULL,
                              rule_old ? rule_old->message: NULL);

            /* something wrong, use old evaluation rsults */
            *rules_evaluated = rules_evaluated_old;
            rule = rule_old;
            rule_order = rule_order_old;

            if (!re_evaluate) {
                pthread_mutex_lock(&local_lock);
                if (reevaluation_count == 0) {
                    max_debug_line_s_original = argo_log_get_max_text_per_line_per_s(argo_log_priority_debug);
                    if (max_debug_line_s_original < 1000) argo_log_reset_max_text_per_line_per_s(1000, argo_log_priority_debug);
                    is_zpe_debug_on = zpn_debug_get(ZPN_DEBUG_ZPE_IDX);
                    if (!is_zpe_debug_on) zpn_debug_set(ZPN_DEBUG_ZPE_IDX);
                }
                reevaluation_count++;
                int count = reevaluation_count;
                pthread_mutex_unlock(&local_lock);

                re_evaluate = 1;
                max_debug_line_s_current = argo_log_get_max_text_per_line_per_s(argo_log_priority_debug);
                ZPN_DEBUG_ZPE("Policy Comparison, re_evaluate for customer %"PRId64" scope %"PRId64" app %"PRId64
                              ", policy_type %s, max debug line rate %d/s, reevaluation_count %d",
                              customer_gid, scope_gid, app_gid, zpe_policy_type_string(policy_type), max_debug_line_s_current, (int)count);
                dump_policy_rules(policy_built->policy_meta, set, app_to_rules, rules_for_all);
                goto redo;
            }

            /*
             * make the unit test fail (policy_eval_comp should be set for unit test).
             * this make unit test fail so we know sth is wrong, but let production continue with old results
             */
            if (policy_eval_comp) {
                if (new_policy_available) {
                    return ZPATH_RESULT_BAD_STATE;
                } else {
                    return ZPATH_RESULT_BAD_DATA;
                }
            }
        }

        int recover = 0;
        if(re_evaluate) {
            pthread_mutex_lock(&local_lock);
            reevaluation_count--;
            if (reevaluation_count == 0 && max_debug_line_s_original > 0 && max_debug_line_s_original < 1000) {
                recover = 1;
                argo_log_reset_max_text_per_line_per_s(max_debug_line_s_original, argo_log_priority_debug);
                /* turn off the zpe debug if it was off before */
                if (!is_zpe_debug_on) zpn_debug_reset(ZPN_DEBUG_ZPE_IDX);
            }

            pthread_mutex_unlock(&local_lock);
        }
        if (recover) {
            max_debug_line_s_current = argo_log_get_max_text_per_line_per_s(argo_log_priority_debug);
            ZPN_LOG(AL_INFO, "Policy Comparison, recover max debug line rate to %d/s", max_debug_line_s_current);
        }
    }

end:

    if (matched_rule) *matched_rule = rule ? rule->gid: 0;
    if (matched_custom_msg) *matched_custom_msg = rule ? rule->message: NULL;

    if (matched_reauth_timeout) {

        if (rule) {
            if (rule->reauth_timeout < 0) {
                // fix -1 value in reauth_timeout. SET-681
                ZPN_DEBUG_ZPE("%s, negative value %" PRId64 " for customer: %" PRId64 "scope: %" PRId64 " rule: %" PRId64 "rule_order: %d",
                              debug_str, rule->reauth_timeout, customer_gid, scope_gid, rule->gid, rule_order);
                *matched_reauth_timeout = 0;
            } else {
                *matched_reauth_timeout = rule->reauth_timeout;
            }
        } else {
            *matched_reauth_timeout = 0;
        }
    }
    if (matched_reauth_idle_timeout) *matched_reauth_idle_timeout = rule ? rule->reauth_idle_timeout : 0;
    /* action_id is be used for saving CBI profile id when the customer has CBI enabled */
    if (matched_action_id) *matched_action_id = rule ? rule->action_id : 0;
    if (matched_action) *matched_action = rule ? rule->access_action : 0;

    if (debug_verbose || is_unit_test()) {
        ZPN_LOG(AL_NOTICE, "++ POLICY_DEBUG, evaluate policy, app_gid=%"PRId64", "
                            "policy_type=%s, matched rule_order=%d, action=%s, policy_eval_comp=%d, g_policy_eval_type=%"PRId64,
                            app_gid, zpe_policy_type_string(policy_type), rule_order,
                            zpe_access_action_string(rule ? rule->access_action : 0),
                            policy_eval_comp, g_policy_eval_type);
    }

    return rule ? ZPATH_RESULT_NO_ERROR : ZPATH_RESULT_NOT_FOUND;
}

int zpe_evaluate_scope(int64_t *app_gids,
                       int64_t app_gids_count,
                       struct zpe_policy_built *policy_built_default,
                       struct zpe_policy_built *policy_built,
                       struct zhash_table *general_state,
                       struct zhash_table *saml_state,
                       struct zhash_table *scim_state,
                       struct zhash_table *int64_hash,
                       const char * debug_str,
                       int *rules_evaluated,
                       int64_t *matched_rule,
                       enum zpe_access_action *matched_action,
                       char **matched_custom_msg,
                       int64_t *matched_reauth_timeout,
                       int64_t *matched_reauth_idle_timeout,
                       int64_t *matched_action_id,
                       int is_default_app)
{
    return zpe_evaluate_scope_internal(app_gids,
                                       app_gids_count,
                                       policy_built_default,
                                       policy_built,
                                       general_state,
                                       saml_state,
                                       scim_state,
                                       int64_hash,
                                       debug_str,
                                       rules_evaluated,
                                       matched_rule,
                                       matched_action,
                                       matched_custom_msg,
                                       matched_reauth_timeout,
                                       matched_reauth_idle_timeout,
                                       matched_action_id,
                                       is_default_app,
                                       0);          /* not debug verbose */
}


int zpe_evaluate_scope_internal(int64_t *app_gids,
                                int64_t app_gids_count,
                                struct zpe_policy_built *policy_built_default,
                                struct zpe_policy_built *policy_built,
                                struct zhash_table *general_state,
                                struct zhash_table *saml_state,
                                struct zhash_table *scim_state,
                                struct zhash_table *int64_hash,
                                const char * debug_str,
                                int *rules_evaluated,
                                int64_t *matched_rule,
                                enum zpe_access_action *matched_action,
                                char **matched_custom_msg,
                                int64_t *matched_reauth_timeout,
                                int64_t *matched_reauth_idle_timeout,
                                int64_t *matched_action_id,
                                int is_default_app,
                                int debug_verbose)  /* only set it for curl command */
{
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t app_gid = (app_gids_count > 0) ? app_gids[0] : 0;

    if (!policy_built_default) {
        ZPN_LOG(AL_CRITICAL, "%s NULL default policy build", debug_str);
        return ZPATH_RESULT_NOT_FOUND;
    }

    int skip_default_rule = (!is_unit_test() || is_dta_skip_default_rule())
                               && (policy_built->policy_meta->scope_gid != policy_built_default->policy_meta->scope_gid)
                               && (policy_built_default->policy_meta->policy_type == zpe_policy_type_bypass
                                 || policy_built_default->policy_meta->policy_type == zpe_policy_type_isolate
                                 || policy_built_default->policy_meta->policy_type == zpe_policy_type_reauth);

    ZPN_DEBUG_ZPE("%s, APP=%"PRId64", is default scope app %d, skip default rule %d, default policy type %d",
                   debug_str, app_gid, is_default_app, skip_default_rule, policy_built_default->policy_meta->policy_type);
    if(is_default_app) {
        res = zpe_evaluate(skip_default_rule,
                           debug_verbose,
                           app_gids,
                           app_gids_count,
                           policy_built_default,
                           general_state,
                           saml_state,
                           scim_state,
                           int64_hash,
                           debug_str,
                           rules_evaluated,
                           matched_rule,
                           matched_action,
                           matched_custom_msg,
                           matched_reauth_timeout,
                           matched_reauth_idle_timeout,
                           matched_action_id);
        if(res == ZPATH_RESULT_NO_ERROR)
            return res;
    }

    if (!is_scope_default(policy_built->policy_meta->scope_gid)){
        res = zpe_evaluate(0,
                           debug_verbose,
                           app_gids,
                           app_gids_count,
                           policy_built,
                           general_state,
                           saml_state,
                           scim_state,
                           int64_hash,
                           debug_str,
                           rules_evaluated,
                           matched_rule,
                           matched_action,
                           matched_custom_msg,
                           matched_reauth_timeout,
                           matched_reauth_idle_timeout,
                           matched_action_id);
    }

    return res;
}


int zpe_evaluate_all(struct zpe_policy_set *set,
                     struct zhash_table *general_state_hash,
                     struct zhash_table *saml_state_hash,
                     struct zhash_table *scim_state_hash,
                     struct zhash_table *int64_hash,
                     int64_t *matched_action_ids,
                     size_t *action_id_count)
{
    int i;
    int ix = 0;

    /* For each rule in the policy set, see if it matches. */
    for (i = 0; i < set->zpe_rule_count; i++) {
        if (rule_matches(set->rules[i], general_state_hash, saml_state_hash, scim_state_hash, int64_hash, 0)) {
            matched_action_ids[ix] = set->rules[i]->action_id;
            ix++;
            if (ix >= *action_id_count) break;
        }
    }
    *action_id_count = ix;
    if (ix == 0) {
        return ZPATH_RESULT_NOT_FOUND;
    }
    return ZPATH_RESULT_NO_ERROR;
}
