/*
 * zpn_broker_np.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
 */

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "wally/wally.h"
#include "np_lib/np.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_bgp_connector_session_config.h"

#include "zpn/zpn_ipars_rpc.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_client.h"
#include "admin_probe/np_command_probe.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_connectors.h"
#include "np_lib/np_connector_groups.h"
#include "zpn/zpn_broker_ipars.h"
#include "zpn/zpn_broker_ipars_request_queue.h"
#include "zpn/zpn_broker_np.h"
#include "zpn_policy_engine_private.h"
#include "zpn/zpn_broker_np_compiled.h"
#include "zpn/zpn_broker_np_client.h"
#include "zpn/zpn_broker_client_scim.h"
#include "zpn/zpn_scim_group.h"

static int initialized = 0;
static int64_t pinned_gateway_gid = 0;

struct zpn_broker_np_rpc_stats {                            /* _ARGO: object_definition */
    int64_t np_config_request_rx;                           /* _ARGO: integer */
    int64_t np_config_request_tx_success;                   /* _ARGO: integer */
    int64_t np_config_request_tx_fail;                      /* _ARGO: integer */
    int64_t np_config_rx;                                   /* _ARGO: integer */
    int64_t np_config_tx_success;                           /* _ARGO: integer */
    int64_t np_config_tx_fail;                              /* _ARGO: integer */
    int64_t np_gateway_select_rx;                           /* _ARGO: integer */
    int64_t np_gateway_select_tx_success;                   /* _ARGO: integer */
    int64_t np_gateway_select_tx_fail;                      /* _ARGO: integer */
    int64_t np_gateway_select_ack_rx;                       /* _ARGO: integer */
    int64_t np_gateway_select_ack_tx_success;               /* _ARGO: integer */
    int64_t np_gateway_select_ack_tx_fail;                  /* _ARGO: integer */
    int64_t np_config_update_rx;                            /* _ARGO: integer */
    int64_t np_config_update_tx_success;                    /* _ARGO: integer */
    int64_t np_config_update_tx_fail;                       /* _ARGO: integer */
    int64_t np_broker_keep_alive_rx;                        /* _ARGO: integer */
    int64_t np_client_app_tx_success;                       /* _ARGO: integer */
    int64_t np_client_app_tx_fail;                          /* _ARGO: integer */
    int64_t np_client_app_complete_tx_success;              /* _ARGO: integer */
    int64_t np_client_app_complete_tx_fail;                 /* _ARGO: integer */
    int64_t np_app_domain_tx_success;                       /* _ARGO: integer */
    int64_t np_app_domain_tx_fail;                          /* _ARGO: integer */
    int64_t np_app_domain_complete_tx_success;              /* _ARGO: integer */
    int64_t np_app_domain_complete_tx_fail;                 /* _ARGO: integer */
};
#include "zpn/zpn_broker_np_compiled_c.h"

static struct zpn_broker_np_rpc_stats rpc_stats = {0};
struct zpn_broker_np_ipars_transaction_stats np_ipars_xaction_stats = {0};
struct zpn_broker_np_client_transaction_stats np_client_xaction_stats = {0};

static struct argo_structure_description *zpn_broker_np_rpc_stats_description = NULL;
static struct argo_structure_description *zpn_broker_np_ipars_xaction_stats_description = NULL;
static struct argo_structure_description *zpn_broker_np_client_xaction_stats_description = NULL;

static struct fohh_connection *np_ipars_conn = NULL;    // Public broker only
static struct zpath_mutex np_ipars_conn_lock;           // Public broker only
static int64_t np_ipars_conn_incarnation = 0;           // Public broker only

#define NP_IPARS_CONN_LOCK      ZPATH_MUTEX_LOCK(&np_ipars_conn_lock, __FILE__, __LINE__)
#define NP_IPARS_CONN_UNLOCK    ZPATH_MUTEX_UNLOCK(&np_ipars_conn_lock, __FILE__, __LINE__)

static int is_initial_np_ipars_connection = 1;


int zpn_broker_np_log(const char *log_name, struct argo_structure_description *desc, void *structure_data)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (zpn_np_collection) {
        if ((res = argo_log_structure_immediate(zpn_np_collection,
                                                argo_log_priority_info,
                                                0,
                                                log_name,
                                                desc,
                                                structure_data))) {
            NP_LOG(AL_WARNING, "Could not log NP for log name: %s, %s", log_name, zpath_result_string(res));
        }
    }
    return res;
}

/* This function is called from zpn_np_rpc_tx_dump_tlv, zpn_np_rpc_tx_dump_fohh, zpn_np_rpc_rx_dump_tlv
 * Caller must ensure this is only called when debug bit is set */
static inline void zpn_np_rpc_debug_dump(struct argo_structure_description *description,
                                         const void *data,
                                         const char *direction,
                                         const char *conn)
{
    struct argo_object *object = argo_object_create(description, (void *)data);
    if (object) {
        char dump[4096];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s %s on %s", direction, dump, conn);
        }
        argo_object_release(object);
    }
}

static inline void zpn_np_rpc_tx_dump(struct argo_structure_description *description,
                                      const void *data,
                                      const char *conn)
{
    zpn_np_rpc_debug_dump(description, data, "Tx", conn);
}

static inline void zpn_np_rpc_tx_dump_fohh(struct argo_structure_description *description,
                                           const void *data,
                                           struct fohh_connection *f_conn)
{
    zpn_np_rpc_tx_dump(description, data, fohh_description(f_conn));
}

static inline void zpn_np_rpc_tx_dump_tlv(struct argo_structure_description *description,
                                          const void *data,
                                          struct zpn_tlv *tlv)
{
    zpn_np_rpc_tx_dump(description, data, zpn_tlv_description(tlv));
}

static inline void zpn_np_rpc_rx_dump(struct argo_structure_description *description,
                                      const void *data,
                                      const char *conn)
{
    zpn_np_rpc_debug_dump(description, data, "Rx", conn);
}

static inline void zpn_np_rpc_rx_dump_tlv(struct argo_structure_description *description,
                                          const void *data,
                                          struct zpn_tlv *tlv)
{
    zpn_np_rpc_rx_dump(description, data, zpn_tlv_description(tlv));
}


int zpn_send_np_config_request(struct fohh_connection *f_conn,
                               int64_t conn_incarnation,
                               const struct zpn_np_config_request *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!f_conn || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = fohh_argo_serialize(f_conn,
                                        zpn_np_config_request_description,
                                        (void *)data,
                                        conn_incarnation,
                                        fohh_queue_element_type_control);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_request_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config_request: res=%s f_conn=%s",
               zpn_result_string(res), fohh_description(f_conn));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_request_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_fohh(zpn_np_config_request_description, data, f_conn);
        }
    }

    return res;
}

int zpn_send_np_config_request_tlv(struct zpn_tlv *tlv,
                                   int64_t conn_incarnation,
                                   const struct zpn_np_config_request *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_config_request_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_control);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_request_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config_request: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_request_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_config_request_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_config(struct fohh_connection *f_conn,
                       int64_t conn_incarnation,
                       const struct zpn_np_config *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!f_conn || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = fohh_argo_serialize(f_conn,
                                        zpn_np_config_description,
                                        (void *)data,
                                        conn_incarnation,
                                        fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config: res=%s f_conn=%s",
               zpn_result_string(res), fohh_description(f_conn));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_fohh(zpn_np_config_description, data, f_conn);
        }
    }

    return res;
}

int zpn_send_np_config_tlv(struct zpn_tlv *tlv,
                           int64_t conn_incarnation,
                           const struct zpn_np_config *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_config_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_config_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_gateway_select(struct fohh_connection *f_conn,
                               int64_t conn_incarnation,
                               const struct zpn_np_gateway_select *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!f_conn || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = fohh_argo_serialize(f_conn,
                                        zpn_np_gateway_select_description,
                                        (void *)data,
                                        conn_incarnation,
                                        fohh_queue_element_type_control);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_gateway_select: res=%s f_conn=%s",
               zpn_result_string(res), fohh_description(f_conn));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_fohh(zpn_np_gateway_select_description, data, f_conn);
        }
    }

    return res;
}

int zpn_send_np_gateway_select_tlv(struct zpn_tlv *tlv,
                                   int64_t conn_incarnation,
                                   const struct zpn_np_gateway_select *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_gateway_select_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_control);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_gateway_select: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_gateway_select_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_gateway_select_ack(struct fohh_connection *f_conn,
                                   int64_t conn_incarnation,
                                   const struct zpn_np_gateway_select_ack *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!f_conn || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = fohh_argo_serialize(f_conn,
                                        zpn_np_gateway_select_ack_description,
                                        (void *)data,
                                        conn_incarnation,
                                        fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_ack_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_gateway_select_ack: res=%s f_conn=%s",
               zpn_result_string(res), fohh_description(f_conn));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_ack_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_fohh(zpn_np_gateway_select_ack_description, data, f_conn);
        }
    }

    return res;
}

int zpn_send_np_gateway_select_ack_tlv(struct zpn_tlv *tlv,
                                       int64_t conn_incarnation,
                                       const struct zpn_np_gateway_select_ack *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_gateway_select_ack_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_ack_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_gateway_select_ack: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_ack_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_gateway_select_ack_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_config_update(struct fohh_connection *f_conn,
                              int64_t conn_incarnation,
                              const struct zpn_np_config_update *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!f_conn || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = fohh_argo_serialize(f_conn,
                                        zpn_np_config_update_description,
                                        (void *)data,
                                        conn_incarnation,
                                        fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_update_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config_update: res=%s f_conn=%s",
               zpn_result_string(res), fohh_description(f_conn));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_update_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_fohh(zpn_np_config_update_description, data, f_conn);
        }
    }

    return res;
}

int zpn_send_np_config_update_tlv(struct zpn_tlv *tlv,
                                  int64_t conn_incarnation,
                                  const struct zpn_np_config_update *data)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_config_update_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_config_update_tx_fail, 1);
        NP_LOG(AL_NOTICE, "Failed to send zpn_np_config_update: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_config_update_tx_success, 1);
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_config_update_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_client_app(struct zpn_tlv *tlv,
                           int64_t conn_incarnation,
                           struct argo_object *object)
{
    struct zpn_np_client_app *data = object->base_structure_void;
    if (!data) {
        return ZPN_RESULT_ERR;;
    }
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_client_app_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_client_app_tx_fail, 1);
        NP_LOG(AL_ERROR, "Failed to send zpn_np_client_app: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_client_app_tx_success, 1);
        if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_client_app_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_client_app_complete(struct zpn_tlv *tlv,
                                    int64_t conn_incarnation,
                                    const char *error)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    struct zpn_np_client_app_complete data = {
        .error = error
    };

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_client_app_complete_description,
                                       &data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_client_app_complete_tx_fail, 1);
        NP_LOG(AL_ERROR, "Failed to send zpn_send_zpn_client_app_complete: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_client_app_complete_tx_success, 1);
        if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_client_app_complete_description, &data, tlv);
        }
    }

    return res;
}

int zpn_send_np_app_domain(struct zpn_tlv *tlv,
                           int64_t conn_incarnation,
                           struct argo_object *object)
{
    struct zpn_np_app_domain *data = object->base_structure_void;
    if (!data) {
        return ZPN_RESULT_ERR;;
    }
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv || !data) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_app_domain_description,
                                       (void *)data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_app_domain_tx_fail, 1);
        NP_LOG(AL_ERROR, "Failed to send zpn_np_app_domain: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_app_domain_tx_success, 1);
        if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_app_domain_description, data, tlv);
        }
    }

    return res;
}

int zpn_send_np_app_domain_complete(struct zpn_tlv *tlv,
                                    int64_t conn_incarnation,
                                    const char *error)
{
    if (!initialized) {
        return ZPN_RESULT_ERR;
    }

    if (!tlv) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    struct zpn_np_client_app_complete data = {
        .error = error
    };

    const int res = tlv_argo_serialize(tlv,
                                       zpn_np_app_domain_complete_description,
                                       &data,
                                       conn_incarnation,
                                       fohh_queue_element_type_mission_critical);

    if (res) {
        __sync_add_and_fetch_8(&rpc_stats.np_app_domain_complete_tx_fail, 1);
        NP_LOG(AL_ERROR, "Failed to send zpn_np_app_domain_complete: res=%s tlv=%s",
               zpn_result_string(res), zpn_tlv_description(tlv));
    } else {
        __sync_add_and_fetch_8(&rpc_stats.np_client_app_complete_tx_success, 1);
        if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
            zpn_np_rpc_tx_dump_tlv(zpn_np_app_domain_complete_description, &data, tlv);
        }
    }

    return res;
}

static void *zpn_np_check_object_type(struct argo_object *object, const char *expected_type)
{
    struct argo_structure_description *desc = argo_get_object_description(object);
    if (!desc) {
        NP_LOG(AL_ERROR, "Unknown object type");
        return NULL;
    }

    const char *actual_type = argo_description_get_type(desc);
    if (!actual_type) {
        NP_LOG(AL_ERROR, "No object type");
        return NULL;
    }

    if (strcmp(actual_type, expected_type) != 0) {
        NP_LOG(AL_ERROR, "Mismatched object type: expected=%s actual=%s", expected_type, actual_type);
        return NULL;
    }

    void *data = object->base_structure_void;
    if (!data) {
        NP_LOG(AL_ERROR, "Received %s msg successfully but no data", actual_type);
        return NULL;
    }

    return data;
}

struct zpn_np_config_request *zpn_recv_np_config_request(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_config_request *data = zpn_np_check_object_type(object, "zpn_np_config_request");
    if (data) {
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_rx_dump_tlv(zpn_np_config_request_description, data, tlv);
        }
        __sync_add_and_fetch_8(&rpc_stats.np_config_request_rx, 1);
    }

    return data;
}

struct zpn_np_config *zpn_recv_np_config(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_config *data = zpn_np_check_object_type(object, "zpn_np_config");
    if (data) {
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_rx_dump_tlv(zpn_np_config_description, data, tlv);
        }
        __sync_add_and_fetch_8(&rpc_stats.np_config_rx, 1);
    }

    return data;
}

struct zpn_np_gateway_select *zpn_recv_np_gateway_select(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_gateway_select *data = zpn_np_check_object_type(object, "zpn_np_gateway_select");
    if (!data) {
        NP_LOG(AL_ERROR, "Received zpn_np_gateways_select msg successfully but no data");
        return NULL;
    }

    if (IS_ZPN_DEBUG_NP()) {
        zpn_np_rpc_rx_dump_tlv(zpn_np_gateway_select_description, data, tlv);
    }
    __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_rx, 1);

    return data;
}

struct zpn_np_gateway_select_ack *zpn_recv_np_gateway_select_ack(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_gateway_select_ack *data = zpn_np_check_object_type(object, "zpn_np_gateway_select_ack");
    if (data) {
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_rx_dump_tlv(zpn_np_gateway_select_ack_description, data, tlv);
        }
        __sync_add_and_fetch_8(&rpc_stats.np_gateway_select_ack_rx, 1);
    }

    return data;
}

struct zpn_np_broker_keep_alive *zpn_recv_np_broker_keep_alive(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_broker_keep_alive *data = zpn_np_check_object_type(object, "zpn_np_broker_keep_alive");
    if (data) {
        if (IS_ZPN_DEBUG_NP_VERBOSE()) {
            zpn_np_rpc_rx_dump_tlv(zpn_np_broker_keep_alive_description, data, tlv);
        }
        __sync_add_and_fetch_8(&rpc_stats.np_broker_keep_alive_rx, 1);
    }

    return data;
}

struct zpn_np_config_update *zpn_recv_np_config_update(struct argo_object *object, struct zpn_tlv *tlv)
{
    struct zpn_np_config_update *data = zpn_np_check_object_type(object, "zpn_np_config_update");
    if (data) {
        if (IS_ZPN_DEBUG_NP()) {
            zpn_np_rpc_rx_dump_tlv(zpn_np_config_update_description, data, tlv);
        }
        __sync_add_and_fetch_8(&rpc_stats.np_config_update_rx, 1);
    }

    return data;
}


static int zpn_broker_np_dump_rpc_stats(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count __attribute__((unused)),
                                        void *cookie __attribute__((unused)))
{
    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        char json[4096];
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_broker_np_rpc_stats_description, &rpc_stats, json, sizeof(json), NULL, 1)) {
            ZDP("%s\n", json);
        }
    } else {
        ZDP("np_config_request_rx             : %" PRId64 "\n"
            "np_config_request_tx_success     : %" PRId64 "\n"
            "np_config_request_tx_fail        : %" PRId64 "\n"
            "np_config_rx                     : %" PRId64 "\n"
            "np_config_tx_success             : %" PRId64 "\n"
            "np_config_tx_fail                : %" PRId64 "\n"
            "np_gateway_select_rx             : %" PRId64 "\n"
            "np_gateway_select_tx_success     : %" PRId64 "\n"
            "np_gateway_select_tx_fail        : %" PRId64 "\n"
            "np_gateway_select_ack_rx         : %" PRId64 "\n"
            "np_gateway_select_ack_tx_success : %" PRId64 "\n"
            "np_gateway_select_ack_tx_fail    : %" PRId64 "\n"
            "np_config_update_rx              : %" PRId64 "\n"
            "np_config_update_tx_success      : %" PRId64 "\n"
            "np_config_update_tx_fail         : %" PRId64 "\n"
            "np_broker_keep_alive_rx          : %" PRId64 "\n"
            "np_client_app_tx_success         : %" PRId64 "\n"
            "np_client_app_tx_fail            : %" PRId64 "\n"
            "np_client_app_complete_tx_success: %" PRId64 "\n"
            "np_client_app_complete_tx_fail   : %" PRId64 "\n",
            rpc_stats.np_config_request_rx,
            rpc_stats.np_config_request_tx_success,
            rpc_stats.np_config_request_tx_fail,
            rpc_stats.np_config_rx,
            rpc_stats.np_config_tx_success,
            rpc_stats.np_config_tx_fail,
            rpc_stats.np_gateway_select_rx,
            rpc_stats.np_gateway_select_tx_success,
            rpc_stats.np_gateway_select_tx_fail,
            rpc_stats.np_gateway_select_ack_rx,
            rpc_stats.np_gateway_select_ack_tx_success,
            rpc_stats.np_gateway_select_ack_tx_fail,
            rpc_stats.np_config_update_rx,
            rpc_stats.np_config_update_tx_success,
            rpc_stats.np_config_update_tx_fail,
            rpc_stats.np_broker_keep_alive_rx,
            rpc_stats.np_client_app_tx_success,
            rpc_stats.np_client_app_tx_fail,
            rpc_stats.np_client_app_complete_tx_success,
            rpc_stats.np_client_app_complete_tx_fail
        );
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_dump_ipars_xaction_stats(struct zpath_debug_state *request_state,
                                                  const char **query_values,
                                                  int query_value_count __attribute__((unused)),
                                                  void *cookie __attribute__((unused)))
{
    struct zpn_broker_np_ipars_transaction_stats *stats = &np_ipars_xaction_stats;

    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        char json[4096];
        const int res = argo_structure_dump(zpn_broker_np_ipars_xaction_stats_description,
                                            stats, json, sizeof(json), NULL, 1);
        if (ARGO_RESULT_NO_ERROR == res) {
            ZDP("%s\n", json);
        }
    } else {
        ZDP("reservation_request                     : %" PRIu64 "\n"
            "reservation_release                     : %" PRIu64 "\n"
            "reservation_force_release               : %" PRIu64 "\n"
            "promotion_request                       : %" PRIu64 "\n"
            "refresh_request                         : %" PRIu64 "\n"
            "status_reservation_success              : %" PRIu64 "\n"
            "status_reservation_bad_request          : %" PRIu64 "\n"
            "status_reservation_no_gateway_available : %" PRIu64 "\n"
            "status_reservation_no_ip_available      : %" PRIu64 "\n"
            "status_reservation_service_not_available: %" PRIu64 "\n"
            "status_reservation_stale_connection     : %" PRIu64 "\n"
            "status_reservation_service_disabled     : %" PRIu64 "\n"
            "status_promotion_success                : %" PRIu64 "\n"
            "status_promotion_bad_request            : %" PRIu64 "\n"
            "status_promotion_ip_not_found           : %" PRIu64 "\n"
            "status_promotion_service_not_available  : %" PRIu64 "\n"
            "status_promotion_stale_connection       : %" PRIu64 "\n"
            "status_promotion_service_disabled       : %" PRIu64 "\n"
            "pending_reservation_requests            : %" PRIu64 "\n"
            "timedout_reservation_requests           : %" PRIu64 "\n"
            "dropped_reservation_requests            : %" PRIu64 "\n"
            "pending_promotion_requests              : %" PRIu64 "\n"
            "timedout_promotion_requests             : %" PRIu64 "\n"
            "dropped_promotion_requests              : %" PRIu64 "\n"
            "reservation_request_empty_user          : %" PRIu64 "\n"
            "ipars_fohh_connected                    : %" PRIu64 "\n"
            "ipars_fohh_disconnected                 : %" PRIu64 "\n"
            "num_long_term_ip                        : %" PRIu64 "\n",
            stats->reservation_request,
            stats->reservation_release,
            stats->reservation_force_release,
            stats->promotion_request,
            stats->refresh_request,
            stats->status_reservation_success,
            stats->status_reservation_bad_request,
            stats->status_reservation_no_gateway_available,
            stats->status_reservation_no_ip_available,
            stats->status_reservation_service_not_available,
            stats->status_reservation_stale_connection,
            stats->status_reservation_service_disabled,
            stats->status_promotion_success,
            stats->status_promotion_bad_request,
            stats->status_promotion_ip_not_found,
            stats->status_promotion_service_not_available,
            stats->status_promotion_stale_connection,
            stats->status_promotion_service_disabled,
            stats->pending_reservation_requests,
            stats->timedout_reservation_requests,
            stats->dropped_reservation_requests,
            stats->pending_promotion_requests,
            stats->timedout_promotion_requests,
            stats->dropped_promotion_requests,
            stats->reservation_request_empty_user,
            stats->ipars_fohh_connected,
            stats->ipars_fohh_disconnected,
            stats->num_long_term_ip
        );
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_dump_client_xaction_stats(struct zpath_debug_state *request_state,
                                                   const char **query_values,
                                                   int query_value_count __attribute__((unused)),
                                                   void *cookie __attribute__((unused)))
{
    struct zpn_broker_np_client_transaction_stats *stats = &np_client_xaction_stats;

    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        char json[4096];
        const int res = argo_structure_dump(zpn_broker_np_client_xaction_stats_description,
                                            stats, json, sizeof(json), NULL, 1);
        if (ARGO_RESULT_NO_ERROR == res) {
            ZDP("%s\n", json);
        }
    } else {
        ZDP(
            "config_request                     : %" PRIu64 "\n"
            "config_response                    : %" PRIu64 "\n"
            "gateway_select                     : %" PRIu64 "\n"
            "gateway_select_success             : %" PRIu64 "\n"
            "gateway_select_error               : %" PRIu64 "\n"
            "keep_alive                         : %" PRIu64 "\n"
            "config_update_reset                : %" PRIu64 "\n"
            "status_config_success              : %" PRIu64 "\n"
            "status_config_bad_request          : %" PRIu64 "\n"
            "status_config_forbidden            : %" PRIu64 "\n"
            "status_config_no_gateway_available : %" PRIu64 "\n"
            "status_config_no_ip_available      : %" PRIu64 "\n"
            "status_config_service_not_available: %" PRIu64 "\n"
            "status_config_stale_connection     : %" PRIu64 "\n"
            "status_config_service_disabled     : %" PRIu64 "\n"
            "status_config_not_supported        : %" PRIu64 "\n"
            "status_config_app_registration_fail: %" PRIu64 "\n"
            "keep_alive_timedout                : %" PRIu64 "\n",
            stats->config_request,
            stats->config_response,
            stats->gateway_select,
            stats->gateway_select_success,
            stats->gateway_select_error,
            stats->keep_alive,
            stats->config_update_reset,
            stats->status_config_success,
            stats->status_config_bad_request,
            stats->status_config_forbidden,
            stats->status_config_no_gateway_available,
            stats->status_config_no_ip_available,
            stats->status_config_service_not_available,
            stats->status_config_stale_connection,
            stats->status_config_service_disabled,
            stats->status_config_not_supported,
            stats->status_config_app_registration_fail,
            stats->keep_alive_timedout
        );
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Can be queried by specifying tunnel ID or customer GID, prints client info whenever a match is found*/
static int
zpn_broker_np_dump_clients(struct zpath_debug_state* request_state,
                           const char **query_values,
                           int query_value_count __attribute__((unused)),
                           void *cookie __attribute__((unused)))

{
    struct zpn_broker_connected_clients* connected_clients;
    struct zpn_broker_client_fohh_state* c_state;
    const char*                          tunnel_id = NULL;
    int64_t                              customer_gid = 0;
    struct zpn_broker_np_dump_clients_params params;
    memset(&params, 0, sizeof(params));

    if (query_values[0]) {
        tunnel_id = query_values[0];
    }
    if (query_values[1]) {
        customer_gid = strtol(query_values[1], NULL, 0);
    }
    if (query_values[2]) {
        params.long_term_gateway_gid = strtol(query_values[2], NULL, 0);
    }
    if (query_values[3]) {
        params.long_term_subnet_gid = strtol(query_values[3], NULL, 0);
    }
    if (query_values[4]) {
        params.long_term_ip_str = query_values[4];
    }

    connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));
    LIST_FOREACH(c_state, &(connected_clients->client_list), list_entry) {
        if (tunnel_id && (0 == strncmp(tunnel_id, c_state->tunnel_id, strnlen(c_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT)))) {
            /* Found a tunnel ID match, stop */
            zpn_broker_np_client_cstate_dump(request_state, c_state, &params);
            break;
        }
        if ((customer_gid && (c_state->customer_gid == customer_gid)) || (!tunnel_id && !customer_gid)) {
            /* Either no condition set, or customer gid matches. In that case we still need to continue the iteration*/
            zpn_broker_np_client_cstate_dump(request_state, c_state, &params);
        }
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_dump_client_small(void *cookie, void *value,
                                           void *key __attribute__((unused)),
                                           size_t key_len __attribute__((unused)))
{
    struct zpath_debug_state *request_state = cookie;
    struct zpn_broker_client_fohh_state *c_state = value;
    char ip[ARGO_INET_ADDRSTRLEN] = {0};
    char promoted_at[ARGO_LOG_GEN_TIME_STR_LEN] = {0};

    if (c_state->np.long_term_promoted_us) {
        argo_log_gen_time(c_state->np.long_term_promoted_us, promoted_at, sizeof(promoted_at), 0, 0);
    }

    ZDP("c_state=%p tunnel_id=%s customer_gid=%" PRId64 " gateway_gid=%" PRId64 " subnet_gid=%" PRId64 " ip=%s promoted_at=%s\n",
        c_state, c_state->tunnel_id, c_state->customer_gid, c_state->np.long_term_gateway_gid,
        c_state->np.long_term_subnet_gid, argo_inet_generate(ip, &c_state->np.long_term_ip), promoted_at);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_dump_client_subnets_hash_table(void *cookie, void *value, void *key,
                                                        size_t key_len __attribute__((unused)))
{
    struct zhash_table *table = value;
    if (table->element_count) {
        struct zpath_debug_state *request_state = cookie;
        ZDP("[subnet_gid=%" PRId64 " (%" PRIu64 " %s)]\n",
            *(int64_t *)key, table->element_count,
            table->element_count > 1 ? "clients" : "client");
        zhash_table_walk(value, NULL, zpn_broker_np_dump_client_small, cookie);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_dump_client_subnets(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count __attribute__((unused)),
                                             void *cookie __attribute__((unused)))
{
    int64_t subnet_gid = 0;
    if (query_values[0]) {
        subnet_gid = strtol(query_values[0], NULL, 0);
    }

    char current_us[ARGO_LOG_GEN_TIME_STR_LEN] = {0};
    argo_log_gen_time(epoch_us(), current_us, sizeof(current_us), 0, 1);
    ZDP("Executed at %s\n", current_us);

    struct zpn_broker_connected_clients *connected_clients = &broker.clients;
    pthread_mutex_lock(&connected_clients->lock);

    if (subnet_gid) {
        struct zhash_table *table = zhash_table_lookup(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), NULL);
        if (table) {
            zpn_broker_np_dump_client_subnets_hash_table(request_state, table, &subnet_gid, 0);
        } else {
            ZDP("No entries found for subnet_gid=%" PRId64, subnet_gid);
        }
    } else {
        zhash_table_walk(connected_clients->np_subnets, NULL, zpn_broker_np_dump_client_subnets_hash_table, request_state);
    }

    pthread_mutex_unlock(&connected_clients->lock);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_debug_pin_gateway_gid(struct zpath_debug_state *request_state,
                                               const char **query_values,
                                               int query_value_count,
                                               void *cookie)
{
    if (!query_values[0]) {
        ZDP("Error: Missing gid parameter\n");
        goto done;
    }

    const int64_t gateway_gid = strtoll(query_values[0], NULL, 0);
    if (gateway_gid < 0 || LLONG_MAX == gateway_gid) {
        ZDP("Error: Invalid gid\n");
        goto done;
    }

    ZDP("Update pinned_gateway_gid: was %" PRId64 ", now %" PRId64 "\n",
        pinned_gateway_gid, gateway_gid);

    pinned_gateway_gid = gateway_gid;

done:
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_broker_np_policy_status(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    if (!query_values[0]) {
        ZDP("Missing argument: customer\n");
    } else {
        int64_t customer_gid = strtol(query_values[0], NULL, 0);
        if (is_np_customer_connected(customer_gid)) {
            int enabled = np_policy_feature_is_enabled(customer_gid, NULL);
            ZDP("Customer %"PRId64" is %s to use NP policy enforcement\n", customer_gid, enabled? "enabled" : "not enabled");
        } else {
            ZDP("Customer %"PRId64" has not logged in\n", customer_gid);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * NP IPARS FOHH client
 */
static struct fohh_connection *zpn_broker_np_ipars_get_conn()
{
    struct fohh_connection *f_conn = NULL;

    NP_IPARS_CONN_LOCK;
    f_conn = np_ipars_conn;
    NP_IPARS_CONN_UNLOCK;

    if (!f_conn && !np_is_hard_disabled()) {
        NP_LOG(AL_WARNING, "No NP IPARS connection is established");
    }
    return f_conn;
}

static int zpn_broker_np_ipars_store_c_state_in_subnet_cache(struct zpn_broker_client_fohh_state *c_state)
{
    int res = ZPN_RESULT_NO_ERROR;
    int64_t subnet_gid = c_state->np.long_term_subnet_gid;
    struct zpn_broker_connected_clients *connected_clients = &broker.clients;

    pthread_mutex_lock(&connected_clients->lock);

    struct zhash_table *table = zhash_table_lookup(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), NULL);
    if (!table) {
        table = zhash_table_alloc(&np_allocator);
        if (!table) {
            NP_LOG(AL_ERROR, "Couldn't allocate hash table for c_state with subnet_gid=%" PRId64, subnet_gid);
            res = ZPN_RESULT_NO_MEMORY;
            goto done;
        }

        res = zhash_table_store(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), 0, table);
        if (res) {
            zhash_table_free(table);
            NP_LOG(AL_ERROR, "Couldn't add hash table for c_state with subnet_gid=%" PRId64, subnet_gid);
            goto done;
        }
    } else if (zhash_table_lookup(table, &c_state, sizeof(c_state), NULL) != NULL) {
        goto done;
    }

    res = zhash_table_store(table, &c_state, sizeof(c_state), 0, c_state);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add c_state to hash table: c_state=%p tunnel_id=%s subnet_gid=%" PRId64,
               c_state, c_state->tunnel_id, subnet_gid);
    } else {
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.num_long_term_ip, 1);
    }

done:
    pthread_mutex_unlock(&connected_clients->lock);
    return res;
}

static void zpn_broker_ipars_remove_c_state_from_subnet_cache(struct zpn_broker_client_fohh_state *c_state, int connected_clients_locked)
{
    const int64_t subnet_gid = c_state->np.long_term_subnet_gid;
    if (!subnet_gid) {
        return;
    }

    struct zpn_broker_connected_clients *connected_clients = &broker.clients;

    if (!connected_clients_locked) {
        pthread_mutex_lock(&connected_clients->lock);
    }

    struct zhash_table *table = zhash_table_lookup(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), NULL);
    if (table && zhash_table_remove(table, &c_state, sizeof(c_state), NULL) == ZHASH_RESULT_NO_ERROR) {
        __sync_fetch_and_sub_8(&np_ipars_xaction_stats.num_long_term_ip, 1);

        if (!table->element_count) {
            zhash_table_remove(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), NULL);
            zhash_table_free(table);
        }
    }

    if (!connected_clients_locked) {
        pthread_mutex_unlock(&connected_clients->lock);
    }
}

void zpn_broker_np_ipars_cleanup_np_ip(struct zpn_broker_client_fohh_state *c_state,
                                       int connected_clients_locked)
{
    zpn_broker_ipars_remove_c_state_from_subnet_cache(c_state, connected_clients_locked);
    memset(&c_state->np, 0, sizeof(c_state->np));
}

static inline void zpn_broker_np_ipars_xaction_reservation_status_inc(enum zpn_np_ipars_reservation_status_code status_code)
{
    uint64_t *field = NULL;
    switch (status_code) {
    case zpn_np_ipars_reservation_success:
        field = &np_ipars_xaction_stats.status_reservation_success;
        break;
    case zpn_np_ipars_reservation_error_bad_request:
        field = &np_ipars_xaction_stats.status_reservation_bad_request;
        break;
    case zpn_np_ipars_reservation_error_no_gateway_available:
        field = &np_ipars_xaction_stats.status_reservation_no_gateway_available;
        break;
    case zpn_np_ipars_reservation_error_no_ip_available:
        field = &np_ipars_xaction_stats.status_reservation_no_ip_available;
        break;
    case zpn_np_ipars_reservation_error_service_not_available:
        field = &np_ipars_xaction_stats.status_reservation_service_not_available;
        break;
    case zpn_np_ipars_reservation_error_stale_connection:
        field = &np_ipars_xaction_stats.status_reservation_stale_connection;
        break;
    case zpn_np_ipars_reservation_error_service_disabled:
        field = &np_ipars_xaction_stats.status_reservation_service_disabled;
        break;
    default:
        return;
    }

    __sync_fetch_and_add_8(field, 1);
}

static inline void zpn_broker_np_ipars_xaction_promotion_status_inc(enum zpn_np_ipars_promotion_status_code status_code)
{
    uint64_t *field = NULL;
    switch (status_code) {
    case zpn_np_ipars_promotion_success:
        field = &np_ipars_xaction_stats.status_promotion_success;
        break;
    case zpn_np_ipars_promotion_error_bad_request:
        field = &np_ipars_xaction_stats.status_promotion_bad_request;
        break;
    case zpn_np_ipars_promotion_error_ip_not_found:
        field = &np_ipars_xaction_stats.status_promotion_ip_not_found;
        break;
    case zpn_np_ipars_promotion_error_service_not_available:
        field = &np_ipars_xaction_stats.status_promotion_service_not_available;
        break;
    case zpn_np_ipars_promotion_error_stale_connection:
        field = &np_ipars_xaction_stats.status_promotion_stale_connection;
        break;
    case zpn_np_ipars_promotion_error_service_disabled:
        field = &np_ipars_xaction_stats.status_promotion_service_disabled;
        break;
    default:
        return;
    }

    __sync_fetch_and_add_8(field, 1);
}


typedef void (zpn_broker_np_ipars_process_response_cb)(struct zpn_broker_ipars_request_cookie *cookie);

static inline void zpn_broker_np_ipars_process_response_internal(struct zpn_broker_ipars_request_cookie *cookie,
                                                                 zpn_broker_np_ipars_process_response_cb *callback)
{
    // Now this function own the cookie.
    const struct zpn_broker_client_fohh_state *c_state = cookie->c_state;
    if (!c_state || c_state->incarnation != cookie->c_state_incarnation) {
        // The c_state was already gone.
    } else if (callback) {
        callback(cookie);
    }

    zpn_broker_ipars_request_free_cookie(cookie);
}

static void zpn_broker_np_ipars_process_response_on_thread(struct zevent_base *base __attribute__((unused)),
                                                           void *void_cookie,
                                                           int64_t int_cookie __attribute__((unused)),
                                                           void *extra_cookie1,
                                                           void *extra_cookie2 __attribute__((unused)),
                                                           void *extra_cookie3 __attribute__((unused)),
                                                           int64_t extra_int_cookie __attribute__((unused)))
{
    struct zpn_broker_ipars_request_cookie *cookie = void_cookie;
    if (!cookie) {
        NP_LOG(AL_NOTICE, "Missing cookie");
        return;
    }

    struct argo_object *object = cookie->response;
    zpn_broker_np_ipars_process_response_cb *callback = extra_cookie1;

    zpn_broker_np_ipars_process_response_internal(cookie, callback);
    // Ownership of cookie moved to zpn_broker_np_ipars_process_response_internal().

    if (object) {
        // The object was cloned by zpn_broker_np_ipars_process_response().
        argo_object_release(object);
    }
}

/*
 * zpn_broker_np_ipars_process_response() is the common interface to process a response which is tied to
 * a request by request_id.
 * Actual response handler is implemented by the given callback function.
 * This function does:
 *   1. Fetch zpn_broker_ipars_request_cookie object from pending request queue by the request_id.
 *   2. If the current thread is on the same thread which the c_state belongs to, invoke the callback.
 *   3. If the current thread is different from the c_state thread, defer the callback to the c_state thread.
 *
 * The callback doesn't need to worry about on which thread it gets invoked.
 * This framework guarantees the callback will be running on the c_state thread.
 */
static void zpn_broker_np_ipars_process_response(int64_t request_id,
                                                 struct argo_object *object,
                                                 zpn_broker_np_ipars_process_response_cb *callback)
{
    struct zpn_broker_ipars_request_cookie *cookie = zpn_broker_np_ipars_request_pop_by_id(request_id);
    if (!cookie) {
        // Probably already timed out.
        return;
    }

    const int fohh_thread_id = cookie->c_state_conn_thread_id;
    if (is_current_thread_the_same_fohh_thread(fohh_thread_id)) {
        cookie->response = object;
        zpn_broker_np_ipars_process_response_internal(cookie, callback);
        // Ownership of cookie moved to zpn_broker_np_ipars_process_response_internal().
        return;
    }

    const char *object_type = argo_object_get_type(object);
    ZPN_DEBUG_IPARS("Switching thread to fohh_thread_%d to process %s", fohh_thread_id, object_type);

    if ((cookie->response = argo_object_copy(object)) == NULL) {
        NP_LOG(AL_ERROR, "Failed to copy %s object: request_id=%" PRId64, object_type, request_id);
        zpn_broker_ipars_request_free_cookie(cookie);
        return;
    }

    const int res = fohh_thread_call_big_zevent(fohh_thread_id, zpn_broker_np_ipars_process_response_on_thread,
                                                cookie, 0, callback, NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to schedule a thread_call for processing %s: request_id=%" PRId64 ": %s",
               object_type, request_id, zpn_result_string(res));
        argo_object_release(cookie->response);
        zpn_broker_ipars_request_free_cookie(cookie);
    }
}


static void zpn_broker_np_client_xaction_stats_update(enum zpn_np_config_status_code status_code)
{
    uint64_t *field = NULL;
    struct zpn_broker_np_client_transaction_stats *stats = &np_client_xaction_stats;

    switch (status_code) {
    case zpn_np_config_success:
        field = &stats->status_config_success;
        break;
    case zpn_np_config_error_bad_request:
        field = &stats->status_config_bad_request;
        break;
    case zpn_np_config_error_forbidden:
        field = &stats->status_config_forbidden;
        break;
    case zpn_np_config_error_no_gateway_available:
        field = &stats->status_config_no_gateway_available;
        break;
    case zpn_np_config_error_no_ip_available:
        field = &stats->status_config_no_ip_available;
        break;
    case zpn_np_config_error_service_not_available:
        field = &stats->status_config_service_not_available;
        break;
    case zpn_np_config_error_stale_connection:
        field = &stats->status_config_stale_connection;
        break;
    case zpn_np_config_error_service_disabled:
        field = &stats->status_config_service_disabled;
        break;
    case zpn_np_config_error_not_supported:
        field = &stats->status_config_not_supported;
        break;
    case zpn_np_config_error_app_registration_fail:
        field = &stats->status_config_app_registration_fail;
        break;
    default:
        NP_LOG(AL_NOTICE, "Invalid status code: %d", status_code);
        return;
    }

    __sync_fetch_and_add_8(field, 1);
}

static inline void zpn_broker_np_config_request_error(struct zpn_tlv *tlv,
                                                      enum zpn_np_config_status_code status_code,
                                                      const char *error_message)
{
    const struct zpn_np_config config = {
        .gateways_count = 0,
        .status_code = status_code,
        .error = error_message ? : zpn_np_config_status_code_to_string(status_code)
    };

    NP_LOG(AL_NOTICE, "Rejecting zpn_np_config_request: %s", config.error);
    zpn_send_np_config_tlv(tlv, 0, &config);
    zpn_broker_np_client_xaction_stats_update(status_code);
    __sync_fetch_and_add_8(&np_client_xaction_stats.config_response, 1);
}

static struct argo_object *zpn_broker_np_create_gateway_config(int64_t gateway_gid, const struct argo_inet *client_ip)
{
    struct np_tenant_gateways *gateway = NULL;
    const int res = np_tenant_gateways_get_by_id_immediate(gateway_gid, &gateway);
    if (res || !gateway) {
        NP_LOG(AL_WARNING, "Could not load gateway config for gid=%" PRId64 ": %s", gateway_gid, wally_error_strings[res]);
        return NULL;
    }

    // Sanity check
    if (!gateway->public_key) {
        NP_LOG(AL_WARNING, "Gateway public key was not configured for gid=%" PRId64, gateway_gid);
        return NULL;
    }
    if (!gateway->listener_ip_address || !gateway->listener_ip_address->length) {
        NP_LOG(AL_WARNING, "Gateway listener IP address was not configured for gid=%" PRId64, gateway_gid);
        return NULL;
    }

    struct zpn_np_gateway_config config = {
        .gid = gateway_gid,
        .public_key = gateway->public_key,
        .key_expiry_s = gateway->public_key_expiry,
        .listen_ip = *gateway->listener_ip_address,
        .listen_port = gateway->listener_port,
        .mtu = 1420, // TODO: Make this configurable by customer later.
        .client_ip = *client_ip
    };

    return argo_object_create(zpn_np_gateway_config_description, &config);
}

// Running on c_state's fohh_thread
static void zpn_broker_np_ipars_process_reservation_response(struct zpn_broker_ipars_request_cookie *cookie)
{
    if (!cookie->response || !cookie->response->base_structure_void) {
        NP_LOG(AL_NOTICE, "No response is stored in cookie");
        return;
    }

    struct zpn_broker_client_fohh_state *c_state = cookie->c_state;
    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        return;
    }

    const struct zpn_np_ipars_reservation_response *data = cookie->response->base_structure_void;

    enum zpn_np_config_status_code status_code = zpn_np_config_success;
    switch (data->status_code) {
    case zpn_np_ipars_reservation_success:
        // IPARS won't send such an inconsistent response though, be defensive.
        if (!data->gateway_gids_count) {
            NP_LOG(AL_WARNING, "Received zpn_np_ipars_reservation_success but no gateway_gids");
            status_code = zpn_np_config_error_no_gateway_available;
        }
        if (!data->gateway_gids_count != !data->client_ips_count) {
            NP_LOG(AL_WARNING, "Received zpn_np_ipars_reservation_success but client_ips_count doesn't match gateway_gids_count");
            status_code = zpn_np_config_error_service_not_available;
        }
        break;
    case zpn_np_ipars_reservation_error_no_gateway_available:
        status_code = zpn_np_config_error_no_gateway_available;
        break;
    case zpn_np_ipars_reservation_error_no_ip_available:
        status_code = zpn_np_config_error_no_ip_available;
        break;
    case zpn_np_ipars_reservation_error_stale_connection:
        status_code = zpn_np_config_error_stale_connection;
        break;
    case zpn_np_ipars_reservation_error_service_disabled:
        status_code = zpn_np_config_error_service_disabled;
        break;
    default:
        status_code = zpn_np_config_error_service_not_available;
        break;
    }

    if (status_code != zpn_np_config_success) {
        zpn_broker_np_config_request_error(tlv, status_code, NULL);
        return;
    }

    struct zpn_np_config config = {
        .gateways_count = 0,
        .status_code = zpn_np_config_success,
        .error = NULL
    };

    int32_t i;
    struct argo_object *gateway = NULL;
    for (i = 0; i < data->gateway_gids_count; i++) {
        // TODO: Remove this block once testbed gets ready.
        if (pinned_gateway_gid != 0 && pinned_gateway_gid != data->gateway_gids[i]) {
            continue;
        }

        gateway = zpn_broker_np_create_gateway_config(data->gateway_gids[i], &data->client_ips[i]);
        if (gateway) {
            config.gateways[config.gateways_count++] = gateway;
        }
    }

    if (!config.gateways_count) {
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_no_gateway_available, "No gateway config found");
    } else if (zpn_broker_np_client_register_for_apps(c_state)) {
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_app_registration_fail, "NP app registration failed");
    } else {
        zpn_send_np_config_tlv(tlv, 0, &config);
        zpn_broker_np_client_xaction_stats_update(zpn_np_config_success);
        __sync_fetch_and_add_8(&np_client_xaction_stats.config_response, 1);
    }

    for (i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
}

static int zpn_broker_np_ipars_reservation_response_cb(void *argo_cookie_ptr __attribute__((unused)),
                                                       void *argo_structure_cookie_ptr,
                                                       struct argo_object *object)
{
    const struct zpn_np_ipars_reservation_response *data =
        zpn_recv_np_ipars_reservation_response(object, argo_structure_cookie_ptr);
    if (!data) {
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_broker_np_ipars_xaction_reservation_status_inc(data->status_code);
    zpn_broker_np_ipars_process_response(data->request_id, object, zpn_broker_np_ipars_process_reservation_response);
    return ZPN_RESULT_NO_ERROR;
}

static inline int zpn_broker_np_ipars_is_request_queue_full()
{
    const uint64_t pending_reservation_requests = __sync_fetch_and_add_8(&np_ipars_xaction_stats.pending_reservation_requests, 0);
    const uint64_t pending_promotion_requests = __sync_fetch_and_add_8(&np_ipars_xaction_stats.pending_promotion_requests, 0);
    return pending_reservation_requests + pending_promotion_requests > zpn_broker_ipars_get_max_pending_queue_size();
}

static inline int zpn_broker_np_ipars_match_refresh_request(const struct zpn_np_ipars_refresh_request *data,
                                                            struct zpn_broker_client_fohh_state *c_state)
{
    if (data->customer_gid != c_state->customer_gid) {
        return 0;
    }
    if (data->gateway_gid && data->gateway_gid != c_state->np.long_term_gateway_gid) {
        return 0;
    }
    if (data->subnet_gid && data->subnet_gid != c_state->np.long_term_subnet_gid) {
        return 0;
    }
    if (data->client_ip && data->client_ip->length && !argo_inet_is_same(data->client_ip, &c_state->np.long_term_ip)) {
        return 0;
    }

    return 1;
}

static inline void zpn_broker_np_send_config_update_reset(struct zpn_broker_client_fohh_state *c_state)
{
    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        return;
    }

    const struct zpn_np_config_update update = {
        .action = zpn_np_config_update_action_reset
    };

    __sync_fetch_and_add_8(&np_client_xaction_stats.config_update_reset, 1);
    zpn_send_np_config_update_tlv(tlv, 0, &update);
}

// Must be executed on c_state thread
static void zpn_broker_np_send_config_update_reset_on_thread(struct fohh_thread *thread __attribute__((unused)),
                                                             void *cookie,
                                                             int64_t incarnation)
{
    struct zpn_broker_client_fohh_state *c_state = cookie;
    if (c_state && c_state->incarnation == incarnation) {
        zpn_broker_np_send_config_update_reset(c_state);
    }
}

static void zpn_broker_np_ipars_process_reset_request(struct zpn_broker_client_fohh_state *c_state)
{
    const int fohh_thread_id = c_state->conn_thread_id;
    if (is_current_thread_the_same_fohh_thread(fohh_thread_id)) {
        zpn_broker_np_send_config_update_reset(c_state);
        return;
    }

    ZPN_DEBUG_NP_VERBOSE("Switching thread to fohh_thread_%d to send zpn_np_config_update_action_reset", fohh_thread_id);

    const int res = fohh_thread_call(fohh_thread_id, zpn_broker_np_send_config_update_reset_on_thread,
                                     c_state, c_state->incarnation);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to thread_call on zpn_np_config_update_action_reset: %s: tunnel_id=%s",
               zpn_result_string(res), c_state->tunnel_id);
    }
}

void zpn_broker_np_config_reset_request(struct zpn_broker_client_fohh_state *c_state)
{
    zpn_broker_np_ipars_process_reset_request(c_state);
}

// Must be executed on c_state thread
static inline void zpn_broker_np_ipars_process_refresh_request_each(struct zpn_broker_client_fohh_state *c_state, int connected_clients_locked)
{
    zpn_broker_np_send_config_update_reset(c_state);
    zpn_broker_np_ipars_cleanup_np_ip(c_state, connected_clients_locked);
}

// Must be executed on c_state thread
static void zpn_broker_np_ipars_process_refresh_request_on_thread(struct fohh_thread *thread __attribute__((unused)),
                                                                  void *cookie,
                                                                  int64_t incarnation)
{
    struct zpn_broker_client_fohh_state *c_state = cookie;

    if (c_state && c_state->incarnation == incarnation) {
        zpn_broker_np_ipars_process_refresh_request_each(c_state, 0);
    }
}

static int zpn_broker_np_ipars_process_refresh_request(void *cookie, void *value,
                                                       void *key __attribute__((unused)),
                                                       size_t key_len __attribute__((unused)))
{
    struct zpn_np_ipars_refresh_request *data = cookie;
    struct zpn_broker_client_fohh_state *c_state = value;

    if (zpn_broker_np_ipars_match_refresh_request(data, c_state)) {
        const int fohh_thread_id = c_state->conn_thread_id;
        if (is_current_thread_the_same_fohh_thread(fohh_thread_id)) {
            // As this function is called by zhash_table_walk() in zpn_broker_np_ipars_refresh_request_cb(),
            // we need to pass connected_clients_locked=1 to avoid dead lock.
            zpn_broker_np_ipars_process_refresh_request_each(c_state, 1);
            return ZPN_RESULT_NO_ERROR;
        }

        ZPN_DEBUG_NP_VERBOSE("Switching thread to fohh_thread_%d to process zpn_np_ipars_refresh_request", fohh_thread_id);

        const int res = fohh_thread_call(fohh_thread_id, zpn_broker_np_ipars_process_refresh_request_on_thread,
                                         c_state, c_state->incarnation);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to thread_call on zpn_np_ipars_process_refresh_request: %s: tunnel_id=%s",
                   zpn_result_string(res), c_state->tunnel_id);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_ipars_refresh_request_cb(void *argo_cookie_ptr __attribute__((unused)),
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct zpn_np_ipars_refresh_request *data =
        zpn_recv_np_ipars_refresh_request(object, argo_structure_cookie_ptr);
    if (!data) {
        goto done;
    }

    __sync_fetch_and_add_8(&np_ipars_xaction_stats.refresh_request, 1);

    const int64_t subnet_gid = data->subnet_gid;
    if (!subnet_gid) {
        NP_LOG(AL_NOTICE, "Received zpn_np_ipars_refresh_request with no subnet_gid");
        goto done;
    }

    struct zpn_broker_connected_clients *connected_clients = &broker.clients;
    pthread_mutex_lock(&connected_clients->lock);

    struct zhash_table *table = zhash_table_lookup(connected_clients->np_subnets, &subnet_gid, sizeof(subnet_gid), NULL);
    if (table) {
        zhash_table_walk(table, NULL, zpn_broker_np_ipars_process_refresh_request, data);
    }

    pthread_mutex_unlock(&connected_clients->lock);

done:
    return ZPN_RESULT_NO_ERROR;
}

// Running on c_state's fohh_thread in connection monitor
void zpn_broker_np_ipars_renew_long_term_ip(struct zpn_broker_client_fohh_state *c_state)
{
    if (!c_state ||
        !c_state->np.long_term_ip.length ||
        !c_state->np.ipars_conn_incarnation ||
        !np_ipars_conn_incarnation ||
        c_state->np.request_id_for_renew != 0) {
        // Not needed
        return;
    }

    // Check if long-term IP is expiring
    const int64_t expiry_hrs = zpn_broker_np_get_long_term_expiry_hrs(c_state->customer_gid);
    const int64_t ttl_us = c_state->np.long_term_promoted_us + HOUR_TO_US(expiry_hrs);
    char ip[ARGO_INET_ADDRSTRLEN] = {0};

    if (ttl_us < epoch_us()) {
        NP_LOG(AL_INFO, "Renewing long-term IP %s: gateway_gid=%" PRId64 " subnet_gid=%" PRId64 " tunnel_id=%s",
               argo_inet_generate(ip, &c_state->np.long_term_ip), c_state->np.long_term_gateway_gid,
               c_state->np.long_term_subnet_gid, c_state->tunnel_id);

    // Check if NP IPARS reconnection happened previously
    } else if (c_state->np.ipars_conn_incarnation != np_ipars_conn_incarnation) {
        ZPN_DEBUG_NP_VERBOSE("Resyncing long-term IP %s: gateway_gid=%" PRId64 " subnet_gid=%" PRId64 " tunnel_id=%s",
                             argo_inet_generate(ip, &c_state->np.long_term_ip), c_state->np.long_term_gateway_gid,
                             c_state->np.long_term_subnet_gid, c_state->tunnel_id);
    } else {
        // Not needed
        return;
    }

    struct fohh_connection *f_conn = zpn_broker_np_ipars_get_conn();
    if (!f_conn) {
        NP_LOG(AL_ERROR, "No connection to IPARS: tunnel_id=%s", c_state->tunnel_id);
        return;
    }

    if (zpn_broker_np_ipars_is_request_queue_full()) {
        NP_LOG(AL_ERROR, "IPARS request queue is occupied");
        return;
    }

    const struct zpn_np_ipars_promotion_request promotion_request = {
        .customer_gid = c_state->customer_gid,
        .broker_gid = zpn_broker_ipars_get_broker_gid(c_state),
        .request_id = zpn_broker_np_ipars_request_push(c_state, zpn_np_ipars_promotion_request_description),
        .connect_us = c_state->connect_us,
        .c_cname = c_state->cname,
        .client_ip = c_state->np.long_term_ip
    };

    const int res = zpn_send_np_ipars_promotion_request(f_conn, 0, &promotion_request);
    if (res) {
        c_state->np.request_id_for_renew = 0;
        NP_LOG(AL_ERROR, "Failed to send the promotion request to IPARS for renew action");
    } else {
        c_state->np.request_id_for_renew = promotion_request.request_id;
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.promotion_request, 1);
    }
}

static inline void zpn_broker_np_gateway_select_error(struct zpn_tlv *tlv, const char *error_message)
{
    NP_LOG(AL_NOTICE, "Rejecting zpn_np_gateway_select: %s", error_message);

    const struct zpn_np_gateway_select_ack ack = {
        .error = error_message,
        .keep_alive_interval_s = 0
    };

    __sync_fetch_and_add_8(&np_client_xaction_stats.gateway_select_error, 1);

    zpn_send_np_gateway_select_ack_tlv(tlv, 0, &ack);
}

// Running on c_state's fohh_thread
static void zpn_broker_np_ipars_process_promotion_response(struct zpn_broker_ipars_request_cookie *cookie)
{
    struct zpn_broker_client_fohh_state *c_state = cookie->c_state;
    const struct zpn_np_ipars_promotion_response *data = cookie->response->base_structure_void;

    if (c_state->np.request_id_for_renew == data->request_id) {
        // The promotion request was initiated by zpn_broker_np_ipars_renew_long_term_ip().
        c_state->np.request_id_for_renew = 0;
        switch (data->status_code) {
        case zpn_np_ipars_promotion_error_ip_not_found:
        case zpn_np_ipars_promotion_error_service_disabled:
            zpn_broker_np_send_config_update_reset(c_state);
            // Fall through
        case zpn_np_ipars_promotion_error_stale_connection:
            zpn_broker_np_ipars_cleanup_np_ip(c_state, 0);
            return;

        case zpn_np_ipars_promotion_success:
            c_state->np.long_term_promoted_us = data->promoted_us;
            c_state->np.ipars_conn_incarnation = np_ipars_conn_incarnation;
            return;

        case zpn_np_ipars_promotion_error_bad_request:
        case zpn_np_ipars_promotion_error_service_not_available:
        default:
            // Nothing to do
            return;
        }
    }

    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        return;
    }

    const char *error_message = NULL;
    switch (data->status_code) {
    case zpn_np_ipars_promotion_success:
        break;
    case zpn_np_ipars_promotion_error_ip_not_found:
        error_message = "Not found IP address to promote";
        break;
    case zpn_np_ipars_promotion_error_stale_connection:
        error_message = "Detected a statel connection";
        break;
    case zpn_np_ipars_promotion_error_service_disabled:
        error_message = "NP feature is disabled";
        break;
    default:
        error_message = "Internal IPARS error";
        break;
    }

    if (error_message) {
        zpn_broker_np_gateway_select_error(tlv, error_message);
        return;
    }

    const int16_t timeout_s = zpn_broker_np_get_keep_alive_timeout_s(c_state->customer_gid);
    c_state->np.keep_alive_timeout_s = timeout_s;
    c_state->np.keep_alive_expires_at = epoch_s() + timeout_s;
    c_state->np.long_term_subnet_gid = data->subnet_gid;
    c_state->np.long_term_promoted_us = data->promoted_us;
    c_state->np.ipars_conn_incarnation = np_ipars_conn_incarnation;

    const int res = zpn_broker_np_ipars_store_c_state_in_subnet_cache(c_state);
    if (res) {
        NP_LOG(AL_NOTICE, "Failed to store c_state (tunnel_id=%s) in the subnet hash table: %s",
               c_state->tunnel_id, zpn_result_string(res));
        zpn_broker_np_gateway_select_error(tlv, "Internal service edge error");
        return;
    }

    const struct zpn_np_gateway_select_ack ack = {
        .error = NULL,
        .keep_alive_interval_s = timeout_s / 4
    };

    __sync_fetch_and_add_8(&np_client_xaction_stats.gateway_select_success, 1);

    zpn_send_np_gateway_select_ack_tlv(tlv, 0, &ack);
}

static int zpn_broker_np_ipars_promotion_response_cb(void *argo_cookie_ptr __attribute__((unused)),
                                                     void *argo_structure_cookie_ptr,
                                                     struct argo_object *object)
{
    const struct zpn_np_ipars_promotion_response *data =
        zpn_recv_np_ipars_promotion_response(object, argo_structure_cookie_ptr);
    if (!data) {
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_broker_np_ipars_xaction_promotion_status_inc(data->status_code);
    zpn_broker_np_ipars_process_response(data->request_id, object, zpn_broker_np_ipars_process_promotion_response);
    return ZPN_RESULT_NO_ERROR;
}

static inline void zpn_broker_np_ipars_reservation_release_all(struct fohh_connection *f_conn)
{
    const struct zpn_np_ipars_reservation_release data = {
        .customer_gid = 0,
        .broker_gid = ZPN_BROKER_GET_GID(),
        .connect_us = 0,
        .client_ip = {0},
        .c_cname = NULL,
        .force = 0,
    };

    __sync_fetch_and_add_8(&np_ipars_xaction_stats.reservation_release, 1);
    zpn_send_np_ipars_reservation_release(f_conn, 0, &data);
}

static int zpn_broker_np_ipars_conn_cb(struct fohh_connection *f_conn,
                                       enum fohh_connection_state state,
                                       void *cookie __attribute__((unused)))
{
    if (state == fohh_connection_connected) {
        NP_LOG(AL_NOTICE, "Successfully connected to NP IPARS: %s", fohh_description(f_conn));
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.ipars_fohh_connected, 1);

        int res;
        struct argo_state *argo = fohh_argo_get_rx(f_conn);

        /* Register zpn_np_ipars_reservation_response_description */
        res = argo_register_structure(argo,
                                      zpn_np_ipars_reservation_response_description,
                                      zpn_broker_np_ipars_reservation_response_cb,
                                      f_conn);
        if (res) {
            NP_LOG(AL_ERROR,
                   "Could not register zpn_np_ipars_reservation_response_description for IPARS: f_conn=%s",
                   fohh_description(f_conn));
            return res;
        }

        /* Register zpn_np_ipars_promotion_response_description */
        res = argo_register_structure(argo,
                                      zpn_np_ipars_promotion_response_description,
                                      zpn_broker_np_ipars_promotion_response_cb,
                                      f_conn);
        if (res) {
            NP_LOG(AL_ERROR,
                   "Could not register zpn_np_ipars_promotion_response_description for IPARS: f_conn=%s",
                   fohh_description(f_conn));
            return res;
        }

        /* Register zpn_np_ipars_refresh_request_description */
        res = argo_register_structure(argo,
                                      zpn_np_ipars_refresh_request_description,
                                      zpn_broker_np_ipars_refresh_request_cb,
                                      f_conn);
        if (res) {
            NP_LOG(AL_ERROR,
                   "Could not register zpn_np_ipars_refresh_request_description for IPARS: f_conn=%s",
                   fohh_description(f_conn));
            return res;
        }

        if (is_initial_np_ipars_connection) {
            is_initial_np_ipars_connection = 0;
            zpn_broker_np_ipars_reservation_release_all(f_conn);
        }

        np_ipars_conn_incarnation = fohh_connection_incarnation(f_conn);

    } else if (state == fohh_connection_disconnected ||
               state == fohh_connection_deleted) {
        if (fohh_get_state(f_conn) == fohh_connection_connected) {
            np_ipars_conn_incarnation = 0;
            NP_LOG(AL_NOTICE, "Disconnected from NP IPARS: f_conn=%s", fohh_description(f_conn));
            __sync_fetch_and_add_8(&np_ipars_xaction_stats.ipars_fohh_disconnected, 1);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_ipars_client_create()
{
    int res = ZPN_RESULT_NO_ERROR;

    NP_IPARS_CONN_LOCK;
    if (np_ipars_conn) {
        goto done;
    }

    struct fohh_connection *f_conn;
    char hostname[255] = {0};
    snprintf(hostname, sizeof(hostname), "np-ipars.%s", ZPATH_LOCAL_CLOUD_NAME);

    f_conn = fohh_client_create(FOHH_WORKER_ZPN_IPARS,          // worker_pool
                                hostname,                       // stats_name
                                argo_serialize_json_no_newline, // encoding
                                fohh_connection_style_argo_tlv, // style
                                0,                              // quiet
                                NULL,                           // cookie
                                zpn_broker_np_ipars_conn_cb,    // callback
                                NULL,                           // tlv_callback,
                                NULL,                           // unblock_callback
                                NULL,                           // data_ack
                                hostname,                       // remote_host_name
                                hostname,                       // sni_service_name
                                NULL,                           // sni_suffix
                                htons(ZPN_IPARS_LISTEN_PORT),   // service_port_ne
                                NULL,                           // ssl_ctx
                                1,                              // use_ssl
                                15);                            // rx_data_timeout_s

    if (!f_conn) {
        NP_LOG(AL_CRITICAL, "Failed to create an NP IPARS client");
        res = ZPN_RESULT_NO_MEMORY;
        goto done;
    }

    fohh_set_sticky(f_conn, 1);
    np_ipars_conn = f_conn;

done:
    NP_IPARS_CONN_UNLOCK;
    return res;
}

static void zpn_broker_np_ipars_connection_shutdown(struct fohh_connection *f_conn)
{
    NP_LOG(AL_INFO, "Shutting down NP IPARS client...");
    fohh_connection_disconnect(f_conn, FOHH_CLOSE_REASON_OPS);
    fohh_connection_release(f_conn);
}

static void zpn_broker_np_ipars_connection_shutdown_on_thread(struct fohh_thread *thread __attribute__((unused)),
                                                              void *cookie,
                                                              int64_t int_cookie __attribute__((unused)))
{
    zpn_broker_np_ipars_connection_shutdown(cookie);
}

static void zpn_broker_np_ipars_client_destroy()
{
    NP_IPARS_CONN_LOCK;
    struct fohh_connection *f_conn = np_ipars_conn;
    np_ipars_conn = NULL;
    NP_IPARS_CONN_UNLOCK;

    if (!f_conn) {
        return;
    }

    zpn_broker_np_ipars_reservation_release_all(f_conn);

    if (!is_current_thread_fohh_thread()) {
        /* While this function is called in the broker_maintenance thread,
         * we cannot call fohh_connection_release() directly.
         */
        const int thr_id = fohh_connection_get_thread_id(f_conn);
        const int res = fohh_thread_call(thr_id, zpn_broker_np_ipars_connection_shutdown_on_thread, f_conn, 0);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to thread_call on connection_shutdown: %s", zpn_result_string(res));
        }
    } else {
        zpn_broker_np_ipars_connection_shutdown(f_conn);
    }
}

static int zpn_broker_np_ipars_client_init()
{
    int res = ZPN_RESULT_NO_ERROR;

    if ((res = zpn_ipars_rpc_init()) != ZPN_RESULT_NO_ERROR) {
        return res;
    }
    if ((res = zpn_broker_ipars_request_queue_init()) != ZPN_RESULT_NO_ERROR) {
        return res;
    }

    np_ipars_conn_lock = ZPATH_MUTEX_INIT;
    return zpn_broker_np_ipars_client_create();
}

static int zpn_broker_np_policy_check(const struct zpn_broker_client_fohh_state *c_state)
{
    return zpn_broker_is_np_scim_enabled(c_state, 1/*also update prev state*/);
}

void zpn_broker_np_ipars_release_and_cleanup_np_ip(struct zpn_broker_client_fohh_state *c_state,
                                                   int force, int connected_clients_locked)
{
    const int res = zpn_broker_np_ipars_send_reservation_release(c_state, force);
    if (res) {
        NP_LOG(AL_ERROR, "Could not send NP IPARS reservation release: %s: tunnel_id=%s",
               zpn_result_string(res), c_state->tunnel_id);
    } else {
        zpn_broker_np_ipars_cleanup_np_ip(c_state, connected_clients_locked);
    }
}

int zpn_np_broker_policy_evaluate(struct zpn_broker_client_fohh_state *c_state)
{
    enum zpe_access_action matched_action = zpe_access_action_none;
    int64_t matched_rule_id = 0;
    struct zhash_table *saml_hash = NULL;
    struct zhash_table *scim_hash = NULL;
    struct zpe_policy_built *policy_built = NULL;
    struct zpe_policy_built *policy_built_default = NULL;
    int rules_evaluated = 0;
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t scope_gid = c_state->scope_gid;
    int64_t customer_gid = c_state->customer_gid;

    if (zpn_client_static_config[c_state->client_type].scim_compatible) {
        if (c_state->scim_enabled && !zpn_broker_client_scim_user_active(c_state)) {
            ZPN_LOG(AL_INFO, "%s: policy rejected because user was deactivated", c_state->cname);
            return ZPN_RESULT_ERR;
        }
        scim_hash =  c_state->scim_enabled ? zpn_broker_client_scim_get_scim_state_hashed(c_state) : NULL;
    }

    if (zpn_client_static_config[c_state->client_type].uses_saml) {
        if(!c_state->idp_gid) {
            ZPN_LOG(AL_WARNING, "%s: No IDP GID for user?", c_state->cname);
            return ZPN_RESULT_ERR;
        }
        saml_hash = c_state->saml_enabled ? c_state->saml_hash : NULL;
    }

    ZPN_DEBUG_NP_VERBOSE("customer: %"PRId64 ", scope gid: %"PRId64" Policy type=%d",
                         customer_gid, scope_gid, zpe_policy_type_global_vpn);

    res = zpe_get_scope_policy_without_rebuild(scope_gid,
                                               zpe_policy_type_global_vpn,
                                               &policy_built_default,
                                               &policy_built,
                                               NULL);

    if (res) {
        NP_LOG(AL_NOTICE, "Could not get global vpn policy, scope:%"PRId64", res: %s\n", scope_gid, zpath_result_string(res));
        return res;
    }

    if (policy_built) {
        NP_LOG(AL_NOTICE, "customer: %"PRId64", Global VPN Policy evaluation", customer_gid);

        c_state->np.policy_version = policy_built->policy_version;
        c_state->np.posture_profile_version = c_state->posture_profile_version;
        c_state->np.scim_version = zpn_broker_client_get_scim_version(c_state);

        res = zpe_evaluate_scope(
                NULL,
                0,
                policy_built_default,
                policy_built,
                c_state->general_context_hash,
                saml_hash,
                scim_hash,
                NULL,
                NULL,
                &rules_evaluated,
                &matched_rule_id,
                &matched_action,
                NULL,
                NULL,
                NULL,
                NULL,
                1);

        if (c_state->np.policy_action == -1) c_state->np.policy_action = (res == ZPATH_RESULT_NO_ERROR && matched_action == zpe_access_action_allow)? 1 : 0;

        if (res == ZPATH_RESULT_NO_ERROR) {
            NP_LOG(AL_INFO, "Global VPN Policy Evaluation for client: %s matched rule = %ld"
                    " action %s", c_state->cname, (long)matched_rule_id, zpe_access_action_string(matched_action));
            if (matched_action == zpe_access_action_allow) {
                NP_LOG(AL_INFO, "Global VPN Policy matched was allowed for user %s", c_state->cname);
                return res;
            } else {
		        return ZPATH_RESULT_ERR;
            }
        } else {
            NP_LOG(AL_INFO, "Global VPN  Policy evaluation returned without match, not allowing this user %s", c_state->cname);
            res = ZPATH_RESULT_NOT_FOUND;
        }
    } else {
        NP_LOG(AL_INFO, "Global VPN Policy is not configured for customer %" PRId64, c_state->customer_gid);
        res = ZPATH_RESULT_NOT_FOUND;
    }

    return res;
}

int zpn_np_broker_policy_is_action_change(struct zpn_broker_client_fohh_state *c_state, int8_t *new_action)
{
    *new_action = !(zpn_np_broker_policy_evaluate(c_state));
    if (c_state->np.policy_action != *new_action) {
        ZPN_DEBUG_NP_VERBOSE("Policy Evaluation for client: %s changed, new action is %s", c_state->cname, zpe_access_action_string(*new_action));
        c_state->np.policy_action = *new_action;
        return 1;
    }

    ZPN_DEBUG_NP_VERBOSE("Policy Evaluation for client: %s has not changed, action is still %"PRId8, c_state->cname, c_state->np.policy_action);
    return 0;
}

int zpn_broker_np_config_request_cb(void *argo_cookie_ptr __attribute__((unused)),
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    if (!c_state) {
        NP_LOG(AL_ERROR, "Unexpected lack of c_state");
        goto done;
    }

    __sync_fetch_and_add_8(&np_client_xaction_stats.config_request, 1);

    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        goto done;
    }

    struct zpn_np_config_request *config_request = zpn_recv_np_config_request(object, tlv);
    if (!config_request) {
        goto done;
    }

    if (config_request->exclude_gateway_gids_count) {
        __sync_fetch_and_add_8(&np_client_xaction_stats.config_request_with_excl_gateway, 1);
    }

    if (ZPN_BROKER_IS_RBROKER()) {
        // If this is a redirect broker, just discard the request.
        goto done;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        // PSE doesn't process zpn_np_config_request in the first phase.
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_not_supported,
                                           "NP is not supported by PSE");
        goto done;
    }

    if (c_state->auth_type == zpn_tunnel_auth_private_broker) {
        // PSE is newer version which supports NP than broker.
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_not_supported,
                                           "NP capable PSE is not supported");
        goto done;
    }

    if (!c_state->auth_complete || !c_state->user_id) {
        // Ignore the call while user authentication hasn't completed.
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.reservation_request_empty_user, 1);
        NP_LOG(AL_NOTICE, "Ignored NP IPARS reservation request due to empty user: tunnel_id=%s", c_state->tunnel_id);
        goto done;
    }

    if (!c_state->capability_np || !zpn_client_static_config[c_state->client_type].np) {
        // Ignore the client which is not NP capable.
        goto done;
    }

    if (c_state->curr_connection_mode != zpn_client_conn_mode_active) {
        // Process the request only in LBB active mode.
        goto done;
    }

    if (!np_is_feature_enabled(c_state->customer_gid)) {
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_service_disabled,
                                           "NP feature is not enabled");
        goto done;
    }

    int np_policy_feature_change = 0;
    if (np_policy_feature_is_enabled(c_state->customer_gid, &np_policy_feature_change)) {
        if (zpn_np_broker_policy_evaluate(c_state)) {
            zpn_broker_np_config_request_error(tlv, zpn_np_config_error_forbidden,
                                               "Access denied for user");
            goto done;
        }
    } else {
        ZPN_DEBUG_NP_VERBOSE("NP Policy feature is disabled fallback to version one"); // debug log remove later
        if (!zpn_broker_np_policy_check(c_state)) {
            zpn_broker_np_config_request_error(tlv, zpn_np_config_error_forbidden,
                                               "No " ZPN_NETWORK_PRESENCE_SCIM_GROUP_NAME " found in SCIM group");
            goto done;
        }
    }

    struct fohh_connection *f_conn = zpn_broker_np_ipars_get_conn();
    if (!f_conn) {
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_service_not_available,
                                           "No connection to IPARS");
        goto done;
    }

    if (zpn_broker_np_ipars_is_request_queue_full()) {
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.dropped_reservation_requests, 1);
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_service_not_available,
                                           "IPARS request queue is occupied");
        goto done;
    }

    const struct zpn_np_ipars_reservation_request reservation_request = {
        .customer_gid = c_state->customer_gid,
        .exclude_gateway_gids = config_request->exclude_gateway_gids,
        .exclude_gateway_gids_count = config_request->exclude_gateway_gids_count,
        .broker_gid = zpn_broker_ipars_get_broker_gid(c_state),
        .request_id = zpn_broker_np_ipars_request_push(c_state, zpn_np_ipars_reservation_request_description),
        .connect_us = c_state->connect_us,
        .c_lat = c_state->log.c_lat,
        .c_lon = c_state->log.c_lon,
        .c_cc = c_state->log.cc,
        .c_cname = c_state->cname,
        .c_uid = c_state->user_id,
        .c_idp_gid = c_state->idp_gid,
        .c_pub_ip = &c_state->client_ip,
        .c_priv_ip = &c_state->log.priv_ip,
        .c_platform = zpn_broker_ipars_get_platform(c_state),
        .timeout_s = zpn_broker_ipars_request_get_timeout()
    };

    const int res = zpn_send_np_ipars_reservation_request(f_conn, 0, &reservation_request);
    if (res) {
        zpn_broker_np_config_request_error(tlv, zpn_np_config_error_service_not_available,
                                           "Failed to send the reservation request to IPARS");
    } else {
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.reservation_request, 1);
    }

done:
    return ARGO_RESULT_NO_ERROR;
}

int zpn_broker_np_gateway_select_cb(void *argo_cookie_ptr __attribute__((unused)),
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    if (!c_state) {
        NP_LOG(AL_ERROR, "Unexpected lack of c_state");
        goto done;
    }

    __sync_fetch_and_add_8(&np_client_xaction_stats.gateway_select, 1);

    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        goto done;
    }

    struct zpn_np_gateway_select *data = zpn_recv_np_gateway_select(object, tlv);
    if (!data) {
        goto done;
    }

    if (!np_is_feature_enabled(c_state->customer_gid)) {
        zpn_broker_np_gateway_select_error(tlv, "NP feature is not enabled");
        goto done;
    }

    if (!data->client_ip.length) {
        zpn_broker_np_gateway_select_error(tlv, "No client_ip is given");
        goto done;
    }

    struct fohh_connection *f_conn = zpn_broker_np_ipars_get_conn();
    if (!f_conn) {
        zpn_broker_np_gateway_select_error(tlv, "No connection to IPARS");
        goto done;
    }

    if (zpn_broker_np_ipars_is_request_queue_full()) {
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.dropped_promotion_requests, 1);
        zpn_broker_np_gateway_select_error(tlv, "IPARS request queue is occupied");
        goto done;
    }

    const struct zpn_np_ipars_promotion_request promotion_request = {
        .customer_gid = c_state->customer_gid,
        .broker_gid = zpn_broker_ipars_get_broker_gid(c_state),
        .request_id = zpn_broker_np_ipars_request_push(c_state, zpn_np_ipars_promotion_request_description),
        .connect_us = c_state->connect_us,
        .c_cname = c_state->cname,
        .client_ip = data->client_ip
    };

    const int res = zpn_send_np_ipars_promotion_request(f_conn, 0, &promotion_request);
    if (res) {
        zpn_broker_np_gateway_select_error(tlv, "Failed to send the promotion request to IPARS");
    } else {
        c_state->np.long_term_gateway_gid = data->gid;
        c_state->np.long_term_ip = data->client_ip;
        __sync_fetch_and_add_8(&np_ipars_xaction_stats.promotion_request, 1);
    }

done:
    return ARGO_RESULT_NO_ERROR;
}

int zpn_broker_np_broker_keep_alive_cb(void *argo_cookie_ptr __attribute__((unused)),
                                       void *argo_structure_cookie_ptr,
                                       struct argo_object *object)
{
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    if (!c_state) {
        NP_LOG(AL_ERROR, "Unexpected lack of c_state");
        goto done;
    }

    __sync_fetch_and_add_8(&np_client_xaction_stats.keep_alive, 1);

    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        NP_LOG(AL_ERROR, "TLV has no connection");
        goto done;
    }

    struct zpn_np_broker_keep_alive *data = zpn_recv_np_broker_keep_alive(object, tlv);
    if (!data) {
        goto done;
    }

    if (!c_state->np.long_term_subnet_gid) {
        ZPN_DEBUG_NP("Ignore keep-alive as np.long_term_subnet_gid is not set: tunnel_id=%s", c_state->tunnel_id);
        goto done;
    }

    int mismatched = 0;
    if (data->gateway_gid != c_state->np.long_term_gateway_gid) {
        NP_LOG(AL_NOTICE, "Received unknown gateway_gid=%" PRId64 " from NP Client while expecting %" PRId64,
               data->gateway_gid, c_state->np.long_term_gateway_gid);
        mismatched = 1;
    }

    if (!argo_inet_is_same(&data->client_ip, &c_state->np.long_term_ip)) {
        char client_ip[ARGO_INET_ADDRSTRLEN] = {0};
        char cached_ip[ARGO_INET_ADDRSTRLEN] = {0};
        NP_LOG(AL_NOTICE, "Received unknown client_ip=%s from NP Client while expecting %s",
               argo_inet_generate(client_ip, &data->client_ip),
               argo_inet_generate(cached_ip, &c_state->np.long_term_ip));
        mismatched = 1;
    }

    if (mismatched) {
        zpn_broker_np_ipars_release_and_cleanup_np_ip(c_state, 0, 0);
        goto done;
    }

    c_state->np.keep_alive_expires_at = epoch_s() + c_state->np.keep_alive_timeout_s;

    if (IS_ZPN_DEBUG_NP_VERBOSE()) {
        char expired_at[ARGO_LOG_GEN_TIME_STR_LEN] = {0};
        argo_log_gen_time(c_state->np.keep_alive_expires_at, expired_at, sizeof(expired_at), 0, 1);
        ZPN_DEBUG_NP_VERBOSE("NP client keep-alive timeout was extended to %s: tunnel_id=%s", expired_at, c_state->tunnel_id);
    }

done:
    return ARGO_RESULT_NO_ERROR;
}

int zpn_broker_np_ipars_send_reservation_release(struct zpn_broker_client_fohh_state *c_state, int force)
{
    if (!c_state) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (!c_state->np.long_term_ip.length) {
        // Nothing to do without IP.
        return ZPN_RESULT_NO_ERROR;
    }

    struct fohh_connection *f_conn = zpn_broker_np_ipars_get_conn();
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    const struct zpn_np_ipars_reservation_release reservation_release = {
        .customer_gid = c_state->customer_gid,
        .broker_gid = zpn_broker_ipars_get_broker_gid(c_state),
        .connect_us = c_state->connect_us,
        .client_ip = c_state->np.long_term_ip,
        .c_cname = c_state->cname,
        .force = force
    };

    const int res = zpn_send_np_ipars_reservation_release(f_conn, 0, &reservation_release);
    if (res == ZPN_RESULT_NO_ERROR) {
        force ? __sync_fetch_and_add_8(&np_ipars_xaction_stats.reservation_force_release, 1) :
                __sync_fetch_and_add_8(&np_ipars_xaction_stats.reservation_release, 1);
    }

    return res;
}

static int zpn_broker_np_debug_init(int log_stats)
{
    int res = ZPN_RESULT_NO_ERROR;

    zpn_broker_np_rpc_stats_description = argo_register_global_structure(ZPN_BROKER_NP_RPC_STATS_HELPER);
    zpn_broker_np_ipars_xaction_stats_description = argo_register_global_structure(ZPN_BROKER_NP_IPARS_TRANSACTION_STATS_HELPER);
    zpn_broker_np_client_xaction_stats_description = argo_register_global_structure(ZPN_BROKER_NP_CLIENT_TRANSACTION_STATS_HELPER);

    if (!zpn_broker_np_rpc_stats_description ||
        !zpn_broker_np_ipars_xaction_stats_description ||
        !zpn_broker_np_client_xaction_stats_description) {
        return ZPN_RESULT_ERR;
    }

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_broker_np_rpc_stats",
                                AL_INFO,
                                MINUTE_TO_US(1), /* 1 min interval */
                                zpn_broker_np_rpc_stats_description,
                                &rpc_stats,
                                1,
                                NULL,
                                NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_broker_np_ipars_xaction_stats",
                                AL_INFO,
                                MINUTE_TO_US(1), /* 1 min interval */
                                zpn_broker_np_ipars_xaction_stats_description,
                                &np_ipars_xaction_stats,
                                1,
                                NULL,
                                NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_broker_np_client_xaction_stats",
                                AL_INFO,
                                MINUTE_TO_US(1), /* 1 min interval */
                                zpn_broker_np_client_xaction_stats_description,
                                &np_client_xaction_stats,
                                1,
                                NULL,
                                NULL);

    res = zpath_debug_add_read_command("Dump c_state info of all the np clients",
                                  "/zpn/broker/np/cstate/dump",
                                  zpn_broker_np_dump_clients,
                                  NULL,
                                  "tunnel_id", "(optional) Tunnel ID of the user",
                                  "customer_gid", "(optional) Customer GID",
                                  "gateway_gid", "(optional) Long term gateway gid",
                                  "subnet_gid", "(optional) Long term subnet gid",
                                  "ip", "(optional) Long term ip",
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/np/cstate/dump: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump c_state::np info in np_subnets hash table",
                                  "/zpn/broker/np/cstate/np_subnets",
                                  zpn_broker_np_dump_client_subnets,
                                  NULL,
                                  "subnet_gid", "(optional) Long term subnet gid",
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/np/cstate/np_subnets: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump NP RPC stats",
                                  "/broker/np/dump/rpc_stats",
                                  zpn_broker_np_dump_rpc_stats,
                                  NULL,
                                  "json", "<optional> dump in JSON format",
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /broker/np/dump/rpc_stats: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump NP IPARS transaction stats",
                                  "/broker/np/dump/ipars_xaction_stats",
                                  zpn_broker_np_dump_ipars_xaction_stats,
                                  NULL,
                                  "json", "<optional> dump in JSON format",
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /broker/np/dump/ipars_xaction_stats: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump NP Client transaction stats",
                                  "/broker/np/dump/client_xaction_stats",
                                  zpn_broker_np_dump_client_xaction_stats,
                                  NULL,
                                  "json", "<optional> dump in JSON format",
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /broker/np/dump/client_xaction_stats: %s", zpn_result_string(res));
        return res;
    }

    if (zpn_broker_is_dev_environment()) {
        res = zpath_debug_add_write_command("DEV CLOUD ONLY - Pin a gateway_gid to respond zpn_np_config",
                                      "/broker/np/debug/pin_gateway_gid",
                                      zpn_broker_np_debug_pin_gateway_gid,
                                      NULL,
                                      "gid", "Gateway GID. 0 to disable pinning.",
                                      NULL);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to add debug command /broker/np/debug/pin_gateway_gid: %s", zpn_result_string(res));
            return res;
        }
    }

    res = zpath_debug_add_safe_read_command("Check NP Policy feature stats",
                                       "/zpn/broker/np/access_policy/status",
                                       zpn_broker_np_policy_status,
                                       NULL,
                                       "customer", "<required> customer_gid",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/np/access_policy/status: %s", zpn_result_string(res));
        return res;
    }

    NP_LOG(AL_NOTICE, "zpn_broker_np_debug_init complete");

    return res;
}

static void zpn_broker_np_instance_feature_status_toggled(int enabled)
{
    NP_LOG(AL_WARNING, "%s NP IPARS feature at broker level", enabled ? "Enabling" : "Disabling");
    zpn_broker_np_inc_feature_status_incarnation(0);

    if (enabled) {
        zpn_broker_np_ipars_client_create();
    } else {
        // Remove all pending request first
        zpn_broker_np_ipars_request_queue_destroy();
        // Release all IPs and disconnect from NP IPARS
        zpn_broker_np_ipars_client_destroy();
    }
}

static void zpn_broker_np_customer_feature_status_toggled(int64_t customer_gid, int enabled)
{
    NP_LOG(AL_NOTICE, "%s NP IPARS feature at customer level: customer_gid=%" PRId64,
           enabled ? "Enabling" : "Disabling", customer_gid);

    if (customer_gid) {
        zpn_broker_np_inc_feature_status_incarnation(customer_gid);
    }
}

/* check if NP feature is enabled and if it is toggled, notify ZCC and IPARS if it is toggled */
int zpn_broker_np_check_feature_status(struct zpn_broker_client_fohh_state *c_state, int *is_np_feature_toggled)
{
    int enabled = np_is_feature_enabled(c_state->customer_gid);
    int notify_zcc = 0;
    const uint16_t feature_status_incarnation = zpn_broker_np_get_feature_status_incarnation(c_state->customer_gid);
    if (c_state->np.feature_status_incarnation != feature_status_incarnation) {
        if (is_np_feature_toggled) *is_np_feature_toggled = 1;
        if (enabled) {
            if (!c_state->np.long_term_ip.length) {
                notify_zcc = 1;
            }
        } else {
            if (c_state->np.long_term_ip.length) {
                zpn_broker_np_ipars_release_and_cleanup_np_ip(c_state, 1, 0);
                notify_zcc = 1;
            }
            if (c_state->np_app_state) {
                zpn_broker_client_np_apps_done(c_state);
            }
        }

        if (notify_zcc) {
            zpn_broker_np_config_reset_request(c_state);
        }

        c_state->np.feature_status_incarnation = feature_status_incarnation;
    } else {
        if (is_np_feature_toggled) *is_np_feature_toggled = 0;
    }

    return enabled;
}

int zpn_broker_np_init()
{
    int res = ZPN_RESULT_NO_ERROR;

    if (initialized) {
        return res;
    }

    res = np_rpc_broker_init();
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np rpc");
        return res;
    }

    res = zpn_broker_np_debug_init(1);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np debug module");
        return res;
    }

    res = zpn_broker_np_client_init();
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np client module");
        return res;
    }

    res = np_tenant_gateways_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_tenant_gateways table");
        return res;
    }

    res = np_connectors_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_connectors table");
        return res;
    }

    res = np_connector_groups_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_connector_groups table");
        return res;
    }

    res = np_command_probe_init(NULL, 0, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_command_probe");
        return res;
    }

    res = np_bgp_connectors_config_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_bgp_connectors_config table");
       return res;
    }

    res = np_bgp_connectors_config_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_bgp_connectors_config table");
       return res;
    }

    res = np_bgp_gateways_config_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_bgp_gateways_config table");
       return res;
    }

    res = np_bgp_connector_session_config_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_bgp_connector_session_config table");
       return res;
    }

    res = np_connector_groups_lan_subnets_mapping_table_init(NULL, 0, NULL, NULL, 0);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np_connector_groups_lan_subnets_mapping table");
       return res;
    }

    res = zpn_broker_np_ipars_client_init();
    if (res) {
        NP_LOG(AL_ERROR, "Could not init NP IPARS client");
        return res;
    }

    np_set_all_disabled_config_monitor(zpn_broker_np_instance_feature_status_toggled);
    np_set_feature_config_monitor(zpn_broker_np_customer_feature_status_toggled);

    NP_LOG(AL_NOTICE, "Network Presence broker init complete");
    initialized = 1;

    return ZPN_RESULT_NO_ERROR;
}
