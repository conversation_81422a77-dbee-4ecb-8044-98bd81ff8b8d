/*
 * zpn_mconn_udp_tlv.c. Copyright (C) 2015 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include <fcntl.h>

#include "argo/argo_hash.h"
#include <arpa/inet.h>
#include <sys/socket.h>

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn_udp_tlv.h"
#include "zpn/zpn_client.h"

extern struct evbuffer *udp_packetize(char *buf, ssize_t len, uint16_t src, uint16_t dst);
extern struct evbuffer *udp_extract_packet(struct evbuffer *evbuf);


/***************************************************************************************
 * zpn_udp_tlv stuff
 ***************************************************************************************/

int zpn_udp_tlv_init(struct zpn_udp_tlv *udp_tlv)
{
    udp_tlv->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    udp_tlv->sockaddr = argo_hash_alloc(7, 1);
    if (!udp_tlv->sockaddr) return ZPN_RESULT_NO_MEMORY;

    LIST_INIT(&(udp_tlv->mconn_list));

    udp_tlv->udp_socket = 0;
    udp_tlv->ev = NULL;
    udp_tlv->fohh_thread_id = 0;

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_mconn_udp_tlv *zpn_mconn_udp_tlv_lookup(struct zpn_udp_tlv *udp_tlv, struct sockaddr_storage addr)
{
    struct zpn_mconn_udp_tlv *res;
    pthread_mutex_lock(&(udp_tlv->lock));
    res = argo_hash_lookup(udp_tlv->sockaddr,
                           &addr,
                           sizeof(addr),
                           NULL);
    pthread_mutex_unlock(&(udp_tlv->lock));
    return res;
}

/*
 * Event callback for zpn_udp_tlv
 */
static void zpn_udp_tlv_event_callback(evutil_socket_t fd, short int what, void *pargs)
{
    struct zpn_udp_tlv *udp_tlv = pargs;
    struct sockaddr_storage from_addr;
    unsigned int from_addr_len;
    char *data = NULL;
    int bytes = 0;
    char addr_buf[ARGO_INET_ADDRSTRLEN];
    struct evbuffer *evbuf = NULL;
    struct zpn_mconn_udp_tlv *mconn_udp_tlv;
    int res;

    data = FOHH_MALLOC_DONT_DUMP(MAX_UDP_PACKET);
    if (!data) return;

    memset((char *)&from_addr, 0, sizeof(struct sockaddr_storage));

    from_addr_len = sizeof(from_addr);

    if ((bytes = recvfrom(udp_tlv->udp_socket, data, MAX_UDP_PACKET, 0, (struct sockaddr *)&from_addr, &from_addr_len)) == -1) {
        goto exit;
    }

    inet_ntop(AF_INET, &(((struct sockaddr_in *)&from_addr)->sin_addr), addr_buf, ARGO_INET_ADDRSTRLEN);
    data[bytes < MAX_UDP_PACKET ? bytes : MAX_UDP_PACKET - 1] = 0;
    ZPN_DEBUG_MCONN("zpn_udp_tlv_event_callback(), fd is %d, received %d bytes, buffer is %s, from %s:%d\n",
                    (int)fd, bytes, data, addr_buf, sockaddr_get_port_he(&from_addr));

    mconn_udp_tlv = zpn_mconn_udp_tlv_lookup(udp_tlv, from_addr);
    if (mconn_udp_tlv) {
        ZPN_DEBUG_MCONN("Found mconn_udp_tlv for data, pass along the data");

        mconn_udp_tlv->last_rx_epoch_s = epoch_s();
        mconn_udp_tlv->data_arrived += bytes;

        evbuf = udp_packetize(data, bytes, mconn_udp_tlv->peer_port, mconn_udp_tlv->local_port);
        if (evbuf) {

#if 0   /* UDP framing test: add extra bytes */
            if (1) {
                char extra[10];

                memset(extra, 0, 10);
                evbuffer_add(evbuf, extra, 10);
                bytes += 10;
            }
#endif

            mconn_udp_tlv->callbacks ++;
            mconn_udp_tlv->data_to_peer_attemp += bytes;

            res = zpn_client_process_rx_data(&(mconn_udp_tlv->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            ZPN_DEBUG_MCONN("udp tlv receive, Cannot get packet");
        }

        goto exit;
    } else {
        ZPN_DEBUG_MCONN("Cannot find mconn_udp_tlv for data, create new one");
        (*udp_tlv->request_cb)(from_addr, udp_tlv->port_he, udp_tlv->fohh_thread_id);

        /*
         * Drop the packet for now, since we don't want to leave it in the
         * socket to block other incoming connections
         */
    }

#if 0
    /* XXX FIXME temporarily echo back the data */
    if (sendto(udp_tlv->udp_socket, data, bytes, 0, (struct sockaddr*)&from_addr, from_addr_len) == -1)
    {
        ZPN_DEBUG_MCONN("Cannot send data");
    } else {
        ZPN_DEBUG_MCONN("Sent back %d bytes\n", bytes);
    }
#endif

exit:
    FOHH_FREE_DONT_DUMP(data);
    /* Need to free the evbuf since we are done with it */
    if (evbuf) evbuffer_free(evbuf);

    return;
}

/*
 * Here we create and bind a socket to the given address and set up event callbacks
 */
int zpn_udp_tlv_listen(struct zpn_udp_tlv *udp_tlv,
                       struct argo_inet *inet,
                       uint16_t port_he,
                       int fohh_thread_id,
                       zpn_udp_tlv_request_cb *request_cb)
{
    int sflags;
    struct sockaddr_storage addr;
    struct event_base *base = fohh_get_thread_event_base(fohh_thread_id);
    char addr_buf[ARGO_INET_ADDRSTRLEN];
    socklen_t addr_len;

    udp_tlv->inet = *inet;
    udp_tlv->port_he = port_he;
    udp_tlv->fohh_thread_id = fohh_thread_id;
    udp_tlv->request_cb = request_cb;

    udp_tlv->udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (udp_tlv->udp_socket == -1) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate UDP socket - %s", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    sflags = fcntl(udp_tlv->udp_socket, F_GETFL, 0);

    /* Set flags in non-blocking mode */
    if (sflags< 0) {
        ZPN_DEBUG_MCONN("Cannot get socket flag");
        return ZPN_RESULT_ERR;
    }

    if (fcntl(udp_tlv->udp_socket, F_SETFL, sflags | O_NONBLOCK) < 0) {
        ZPN_DEBUG_MCONN("Cannot set socket flag");
        return ZPN_RESULT_ERR;
    }

    argo_inet_generate(addr_buf, inet);
    ZPN_DEBUG_MCONN("zpn_udp_tlv_listen(), listening on %s:%d", addr_buf, port_he);

    argo_inet_to_sockaddr(inet, (struct sockaddr*)&addr, &addr_len, htons(port_he));

    /* bind socket to port */
    if(bind(udp_tlv->udp_socket, (struct sockaddr*)&addr, sizeof(struct sockaddr_storage)) == -1) {
        ZPN_LOG(AL_WARNING, "Cannot bind socket");
        return ZPN_RESULT_ERR;
    }

    udp_tlv->ev = event_new(base, udp_tlv->udp_socket, EV_READ | EV_PERSIST, zpn_udp_tlv_event_callback, udp_tlv);
    event_add(udp_tlv->ev, NULL);

    return ZPN_RESULT_NO_ERROR;
}


/***************************************************************************************
 * zpn_mconn_udp_tlv stuff
 ***************************************************************************************/

int zpn_mconn_udp_tlv_init(struct zpn_mconn_udp_tlv *mconn_udp_tlv, void *mconn_self, enum zpn_mconn_type type)
{
    mconn_udp_tlv->local_port = 0;
    mconn_udp_tlv->peer_port = 0;
    mconn_udp_tlv->data_arrived = 0;
    mconn_udp_tlv->callbacks = 0;
    mconn_udp_tlv->buffer_under = 0;
    mconn_udp_tlv->buffer_over = 0;
    mconn_udp_tlv->data_to_peer_attemp = 0;
    mconn_udp_tlv->remote_paused = 0;
    mconn_udp_tlv->last_rx_epoch_s = epoch_s();
    mconn_udp_tlv->last_tx_epoch_s = epoch_s();
    return zpn_mconn_init(&(mconn_udp_tlv->mconn), mconn_self, type);
}

int zpn_mconn_udp_tlv_bind_cb(void *mconn_base,
                              void *mconn_self,
                              void *owner,
                              void *owner_key,
                              size_t owner_key_length,
                              int64_t *owner_incarnation)
{
    struct zpn_mconn_udp_tlv *mconn_udp_tlv = mconn_base;
    struct zpn_udp_tlv *udp_tlv = owner;
    int res;

    res = argo_hash_store(udp_tlv->sockaddr,
                          owner_key,
                          owner_key_length,
                          0,
                          mconn_udp_tlv);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not store: %s", zpn_result_string(res));
        return res;
    }

    LIST_INSERT_HEAD(&(udp_tlv->mconn_list), mconn_udp_tlv, mconn_list_entry);
    udp_tlv->list_size++;

    mconn_udp_tlv->peer_sockaddr = *((struct sockaddr_storage *)owner_key);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_udp_tlv_unbind_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int drop_buffered_data,
                                int dont_propagate,
                                const char *err)
{
    struct zpn_mconn_udp_tlv *mconn_udp_tlv = mconn_base;
    struct zpn_udp_tlv *udp_tlv = owner;
    int res;

    if (!dont_propagate) {
        res = zpn_mconn_forward_mtunnel_end(&(mconn_udp_tlv->mconn), err, drop_buffered_data);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
        }
    }

    res = argo_hash_remove(udp_tlv->sockaddr,
                           owner_key,
                           owner_key_length,
                           mconn_udp_tlv);

    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot find tag to remove from fohh tag hash: %s", zpn_result_string(res));
        return res;
    }

    LIST_REMOVE(mconn_udp_tlv, mconn_list_entry);
    udp_tlv->list_size--;

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_udp_tlv_lock_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length)
{
    struct zpn_udp_tlv *udp_tlv = owner;

    pthread_mutex_lock(&(udp_tlv->lock));
}

void zpn_mconn_udp_tlv_unlock_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length)
{
    struct zpn_udp_tlv *udp_tlv = owner;

    pthread_mutex_unlock(&(udp_tlv->lock));
}

int zpn_mconn_udp_tlv_transmit_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length,
                                  int64_t owner_incarnation,
                                  int fohh_thread_id,
                                  struct evbuffer *buf,
                                  size_t buf_len)
{
    struct zpn_mconn_udp_tlv *mconn_udp_tlv = mconn_base;
    struct zpn_udp_tlv *udp_tlv = owner;
    struct evbuffer *pktbuf = NULL;

    if (udp_tlv->udp_socket) {

        /* Drain all the packets, there may be one partial packet left */

        while (evbuffer_get_length(buf) &&
               ((pktbuf = udp_extract_packet(buf)) != NULL)) {

            unsigned char *buf = NULL;

            ZPN_DEBUG_MCONN("zpn_mconn_udp_tlv sending %d bytes", (int)evbuffer_get_length(pktbuf));

            mconn_udp_tlv->last_tx_epoch_s = epoch_s();

            buf = evbuffer_pullup(pktbuf, evbuffer_get_length(pktbuf));
            if (buf) {
                if (sendto(udp_tlv->udp_socket, buf, evbuffer_get_length(pktbuf), 0,
                       (struct sockaddr *)&mconn_udp_tlv->peer_sockaddr, sizeof(struct sockaddr))==-1) {
                    ZPN_LOG(AL_WARNING, "zpn_mconn_udp_bufferevent cannot send packet");
                }
            } else {
                ZPN_DEBUG_MCONN("Cannot pull up the packet evbuffer");
            }

            evbuffer_free(pktbuf);
        }
    } else {
        ZPN_DEBUG_MCONN("%p: Already closed", udp_tlv);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_udp_tlv_pause_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_udp_tlv_resume_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_udp_tlv_forward_tunnel_end_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length,
                                            int64_t owner_incarnation,
                                            const char *err,
                                            int32_t drop_data)
{
    struct zpn_mconn_udp_tlv *mconn_udp_tlv = mconn_base;

    ZPN_DEBUG_MCONN("zpn_mconn_udp_tlv_forward_tunnel_end_cb()");

    mconn_udp_tlv->mconn.fin_rcvd = 1;
    mconn_udp_tlv->mconn.fin_sent = 1;

    /* Reflect the tunnel end if not already */
    if (!mconn_udp_tlv->mconn.fin_refl) {
        zpn_mconn_forward_mtunnel_end(mconn_udp_tlv->mconn.peer, MT_CLOSED_TERMINATED, drop_data);
        mconn_udp_tlv->mconn.fin_refl = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_udp_tlv_window_update_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id,
                                        int tx_len,
                                        int batch_win_upd)
{
    return;
}

void zpn_mconn_udp_tlv_stats_update_cb(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length,
                                       int64_t owner_incarnation,
                                       int fohh_thread_id,
                                       enum zpn_mconn_stats stats_name)
{
    return;
}

int zpn_mconn_udp_tlv_disable_read_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
int zpn_mconn_udp_tlv_enable_read_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls zpn_mconn_udp_tlv_calls = {
    zpn_mconn_udp_tlv_bind_cb,
    zpn_mconn_udp_tlv_unbind_cb,
    zpn_mconn_udp_tlv_lock_cb,
    zpn_mconn_udp_tlv_unlock_cb,
    zpn_mconn_udp_tlv_transmit_cb,
    zpn_mconn_udp_tlv_pause_cb,
    zpn_mconn_udp_tlv_resume_cb,
    zpn_mconn_udp_tlv_forward_tunnel_end_cb,
    zpn_mconn_udp_tlv_window_update_cb,
    zpn_mconn_udp_tlv_stats_update_cb,
    zpn_mconn_udp_tlv_disable_read_cb,
    zpn_mconn_udp_tlv_enable_read_cb
};

int zpn_mconn_udp_tlv_done(struct zpn_mconn_udp_tlv *mconn_udp_tlv)
{
    uint32_t current_epoch_s = epoch_s();

    if (((current_epoch_s - mconn_udp_tlv->last_rx_epoch_s) > UDP_TIMEOUT_S) &&
        ((current_epoch_s - mconn_udp_tlv->last_tx_epoch_s) > UDP_TIMEOUT_S)) {

        if (mconn_udp_tlv->mconn.peer) {
            zpn_mconn_forward_mtunnel_end(mconn_udp_tlv->mconn.peer, MT_CLOSED_TERMINATED, 1);
        }

        mconn_udp_tlv->mconn.fin_rcvd = 1;
        mconn_udp_tlv->mconn.fin_sent = 1;

        return 1;
    }

    return 0;
}

void zpn_mconn_udp_tlv_internal_display(struct zpn_mconn_udp_tlv *mconn_udp_tlv)
{
    ZPN_DEBUG_MCONN("data_arrived = %ld, data_to_peer_attemp = %ld",
                    (long)mconn_udp_tlv->data_arrived, (long)mconn_udp_tlv->data_to_peer_attemp);
    zpn_mconn_internal_display(&(mconn_udp_tlv->mconn));
}

int zpn_mconn_udp_tlv_clean(struct zpn_mconn_udp_tlv *mconn_udp_tlv)
{
    return zpn_mconn_clean(&(mconn_udp_tlv->mconn));
}
