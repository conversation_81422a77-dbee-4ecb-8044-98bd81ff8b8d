/*
 * zpn_broker_mtunnel.h. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

/*
 * All the routines associated with operating on/processing mtunnels.
 *
 * Specifically, allocating, destroying, and state machine.
 */

#ifndef _ZPN_BROKER_MTUNNEL_H_
#define _ZPN_BROKER_MTUNNEL_H_

#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_broker_private.h"

/* Translate state... */
const char *mtunnel_state(enum zpn_broker_mtunnel_state state);

/*
 * Allocate and lock an mtunnel.
 *
 * Never asynchronous.
 *
 * o_dwgs : integer array of ZIA workload tag group gid
 * o_dwgs_count : Count of number of elements in the array
 */
struct zpn_broker_mtunnel *mtunnel_allocate_and_bucket_lock(const char *application_name,
                                                            const char *application_type,
                                                            const uint8_t *tunnel_bin,
                                                            const char *user_id,
                                                            struct zpn_broker_client_fohh_state *c_state,
                                                            int c_state_fohh_thread_id,
                                                            enum zpn_probe_type zpn_probe_type,
                                                            int64_t scope_gid,
                                                            enum zpn_client_type client_type,
                                                            int *bucket_id,
                                                            uint32_t *o_dwgs,
                                                            int o_dwgs_count);



/*
 * Lookup and lock an mtunnel.
 *
 * Never asynchronous.
 */
struct zpn_broker_mtunnel *mtunnel_lookup_and_bucket_lock(const char *mtunnel_id, uint64_t mtunnel_id_hash, int *bucket_id);

/*
 * Release lock on mtunnel.
 *
 * Never asynchronous.
 */
void mtunnel_bucket_unlock(int bucket_id);
void mtunnel_bucket_lock(struct zpn_broker_mtunnel *mtunnel, int *bucket_id);

#ifdef MTUNNEL_LOCKS_DEBUG

void _mtunnel_lock(struct zpn_broker_mtunnel *mtunnel);
void _mtunnel_unlock(struct zpn_broker_mtunnel *mtunnel);

#define mtunnel_lock(mtunnel)                                                           \
    ZPN_LOG(AL_NOTICE, "%s: locking [%s:%d]", mtunnel->mtunnel_id, __FILE__, __LINE__); \
    _mtunnel_lock(mtunnel)

#define mtunnel_unlock(mtunnel)                                                           \
    ZPN_LOG(AL_NOTICE, "%s: unlocking [%s:%d]", mtunnel->mtunnel_id, __FILE__, __LINE__); \
    _mtunnel_unlock(mtunnel)
#else

void mtunnel_lock(struct zpn_broker_mtunnel *mtunnel);
void mtunnel_unlock(struct zpn_broker_mtunnel *mtunnel);

#endif

/*
 * Destroy the locked tunnel.
 *
 * client_indication indicates that the client is telling the mtunnel
 * to close. (and thus we shouldn't tell the client it's closing)
 *
 * assistant_indication indicates that the assistant is telling the
 * mtunnel to close. (and thus we shouldn't tell the assistant it's
 * closing)
 *
 * Both indications can be zero.
 *
 * If 'err' is set, then the mtunnel will be closed with the given
 * error.
 */
int mtunnel_locked_destroy(struct zpn_broker_mtunnel *mtunnel,
                           int drop_buffered_data,
                           int client_indication,
                           int assistant_indication,
                           const char *err);

/*
 * Attach a locked mtunnel to a client. If this routine fails, the
 * mtunnel is marked for destruction.
 *
 * This routine also allocates a tag for the client to use.
 *
 * This routine assures that the client and assistant belong to the
 * same customer. (Just a re-verification. That should always be the
 * case, but it's worth being safe)
 *
 * Never asynchronous.
 */
int mtunnel_locked_attach_client(struct zpn_broker_mtunnel *mtunnel,
                                 struct fohh_connection *client_conn);
/*
 * Detach wants you to run state machine, eventually, on the mtunnel.
 */
int mtunnel_locked_detach_client(struct zpn_broker_mtunnel *mtunnel);

/*
 * Attach a locked mtunnel to an assistant. If this routine failes,
 * the mtunnel is marked for destruction.
 *
 * This routine assures that the client and assistant belong to the
 * same customer. (Just a re-verification. That should always be the
 * case, but it's worth being safe)
 *
 * Never asynchronous.
 */
int mtunnel_locked_attach_assistant(struct zpn_broker_mtunnel *mtunnel,
                                    struct fohh_connection *assistant_conn);
/*
 * Detach wants you to run state machine, eventually, on the mtunnel.
 */
int mtunnel_locked_detach_assistant(struct zpn_broker_mtunnel *mtunnel);

/*
 * Run state machine on a locked mtunnel. This will make the mtunnel
 * 'make what progress it can'. If an error occurs, the mtunnel is
 * marked for destruction.
 *
 * This state machine includes checking for timeouts, etc.
 *
 * Never asynchronous, but some operations that it performs will occur
 * asynchronously over time.
 */
void mtunnel_locked_state_machine(struct zpn_broker_mtunnel *mtunnel);

/*
 * This callback is designed to be called back from configuration completions.
 */
int zpn_broker_mtunnel_callback(void *response_callback_cookie,
                                struct wally_registrant *registrant,
                                struct wally_table *table,
                                int64_t request_id,
                                int row_count);

void zpn_broker_check_mtunnel();

/*
 * Locking: assume we have the lock of mtunnel
 * The mtunnel log is created(writen):
 *  1. when tunnel is fully connected, NOT Before
 *  2. After tunnel is fully connectd and before it is terminated
 *  3. When tunnel is terminated. This includes error termination, before tunnel is connected
 */
int zpn_broker_mtunnel_log(struct zpn_broker_mtunnel *mtunnel);

/*
 * Send mtunnel complete message(s) to client/assistant
 */
int zpn_broker_tunnel_complete_send(struct zpn_broker_mtunnel *mtunnel, const char *err_msg, const char* meta_tag);

void zpn_broker_c2c_fqdn_check_resume_mtunnel(const char*  mtunnel_id, int is_c2c_client, int is_rpc_fail, int64_t mtunnel_incarnation);

void zpn_broker_mtunnel_free_q_init();
int zpn_broker_mtunnel_init();

int zpn_broker_tune_track_application(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie);

int zpn_broker_tune_track_usr(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie);

int zpn_broker_tune_show(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count,
                         void *cookie);

int mtunnel_locked_send_broker_request(struct zpn_broker_mtunnel *mtunnel, int is_probe);


int zpn_broker_mtunnel_promote_locked(struct zpn_broker_mtunnel *mtunnel);

int zpn_broker_local_mtunnel_connection(struct zpn_broker_mtunnel *mtunnel, struct fohh_connection *assistant_f_conn);

struct zpn_broker_client_fohh_state *zpn_broker_mtunnel_client_c_state(struct zpn_broker_mtunnel *mtunnel);
struct zpn_broker_client_fohh_state *zpn_broker_mtunnel_assistant_c_state(struct zpn_broker_mtunnel *mtunnel);
struct zpn_tlv *zpn_broker_mtunnel_client_tlv(struct zpn_broker_mtunnel *mtunnel);
struct zpn_mconn *zpn_broker_mtunnel_client_mconn(struct zpn_broker_mtunnel *mtunnel);
struct zpn_tlv *zpn_broker_mtunnel_assistant_tlv(struct zpn_broker_mtunnel *mtunnel);
struct zpn_mconn *zpn_broker_mtunnel_assistant_mconn(struct zpn_broker_mtunnel *mtunnel);

int zpn_transaction_log_add_throttling_error(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie);


int zpn_transaction_log_remove_throttling_error(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie);

int zpn_transaction_log_show_throttling_error(struct zpath_debug_state *request_state,
                                              const char **query_values,
                                              int query_value_count,
                                              void *cookie);

int zpn_transaction_log_throttling(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie);


int zpn_transaction_log_init_throttling_error_code(void);


int zpn_transaction_log_init_throttling(void);


struct zpn_broker_mtunnel *zpn_broker_get_global_owner_tlv(struct zpn_tlv *tlv, int tag_id);
struct event_base *zpn_broker_client_ev_base(struct zpn_broker_mtunnel *mtunnel);

int zpn_broker_mtunnels_dump(struct zpath_debug_state* request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie);

int zpn_broker_user_risk_mtunnels_dump(struct zpath_debug_state* request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie);

struct zpn_broker_mtunnel *zpn_broker_mtunnel_alloc(enum zpn_client_type client_type);
void check_and_fix_mtunnel_log_locked(struct zpn_broker_mtunnel *mt);

int64_t zpn_broker_config_get_fohh_mconn_track_perf_stats_level(int64_t customer_id);

int64_t zpn_broker_config_get_fohh_connection_disable_read_config_flag(int64_t customer_id);
int64_t zpn_broker_config_mtunnel_fin_expire_us(int64_t customer_id);
void zpn_broker_config_get_fohh_connection_disable_read_config_params(int64_t customer_id,
                                                                      int64_t *client_tx_buff_low,
                                                                      int64_t *client_tx_buff_high,
                                                                      int64_t *client_rx_buff_high_water_time_max_s);


#endif /* _ZPN_BROKER_MTUNNEL_H_ */
