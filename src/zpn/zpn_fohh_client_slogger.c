/*
 * zpn_fohh_client_slogger.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

#include "argo/argo_hash.h"
#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_fohh_client_slogger.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

static int initialized = 0;
static zpath_mutex_t local_lock;
static struct argo_hash_table *slogger_by_id = NULL;
static struct zpn_fohh_client_slogger_mt_free_queue free_q;
static struct event *mt_timer = NULL;
static zfcs_get_cookie_details *rs_cookie_details_cb;

static void zpn_fohh_client_slogger_mt_free_q_init();
static struct zpn_fohh_client_slogger_mt *zpn_fohh_client_slogger_mt_allocate(void);
static void zpn_fohh_client_slogger_mt_reap(struct zpn_fohh_client_slogger_mt *mt);
static void zfcs_terminate_mtunnels(struct zpn_fohh_client_slogger *zfcs);

/***************************************************************************************************
 * Slogger support for broker redirect:
 * Feature flag: "config.feature.slogger.redirect_mode"
 * Values:
 *   SLOGGER_REDIRECT_MODE_DISABLED:    redirects not supported.
 *   SLOGGER_REDIRECT_MODE_FORCE:       only respond to forced redirects (e.g. broker shutting down).
 *   SLOGGER_REDIRECT_MODE_FULL:        support initial redirect and any subsequent redirects, regardless of redirect's attribute.
 */
#define SLOGGER_REDIRECT_MODE_DISABLED     0
#define SLOGGER_REDIRECT_MODE_FORCE        1
#define SLOGGER_REDIRECT_MODE_FULL         2

/* How long to hold initial request while we wait for redirect.
 * Only applicable for SLOGGER_REDIRECT_MODE_FULL mode. */
#define SLOGGER_REDIRECT_TIMEOUT_S     2
#define SLOGGER_REDIRECT_TIMEOUT_US    0

#define BUFFER_SIZE (1024*1024)

struct debug_walk_args {
    char *s; //start
    char *e; //end
};


static void zpn_fohh_client_slogger_status_reporting(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_fohh_client_slogger *zfcs = cookie;
    struct zfcs_cookie *c = NULL;

    struct zfcs_cookie *c2;
    struct zfcs_cookie_head head;

    int donot_call_again = 0;

    /* No callback? */
    if (!zfcs->status_cb) return;

    LIST_INIT(&head);

    ZPN_DEBUG_SIEM("%s Doing pending callbacks", ZFC_DBG(zfcs->zfc));

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);

    LIST_FOREACH(c, &(zfcs->cookie_list), list_entry) {
        c2 = ZPN_CALLOC(sizeof(*c2));
        if (!c2) {
            ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
            ZPN_LOG(AL_ERROR, "memory");
            return;
        }
        c2->cookie_rs = c->cookie_rs;
        c2->cookie_int = c->cookie_int;
        LIST_INSERT_HEAD(&head, c2, list_entry);
    }

    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

    while ((c = LIST_FIRST(&head))) {
        LIST_REMOVE(c, list_entry);
        (*zfcs->status_cb)(zfcs, c->cookie_rs, c->cookie_int, zfcs->status, NULL, &donot_call_again);
        ZPN_FREE(c);
    }

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    if (donot_call_again) {
        /*
         * f_conn is gone? We have informed siem and no need to do that anymore
         * free all the cookies
         */
        while ((c = LIST_FIRST(&(zfcs->cookie_list)))) {
            LIST_REMOVE(c, list_entry);
            ZPN_FREE(c);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
}

static void zpn_fohh_client_slogger_schedule_reporting(struct zpn_fohh_client_slogger *zfcs)
{
    if (zfcs && zfcs->zfc && zfcs->zfc->conn) {
        if (fohh_thread_call(fohh_connection_get_thread_id(zfcs->zfc->conn),
                             zpn_fohh_client_slogger_status_reporting,
                             zfcs,
                             fohh_connection_incarnation(zfcs->zfc->conn)) != FOHH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_slogger_status_reporting!");
        }
    }
}

static struct zpn_fohh_client_slogger *zpn_fohh_client_slogger_get(int64_t customer_id)
{
    struct zpn_fohh_client_slogger *slogger = NULL;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);

    if (slogger_by_id) {
        slogger = argo_hash_lookup(slogger_by_id, &customer_id, sizeof(customer_id), NULL);
    } else {
        slogger_by_id = argo_hash_alloc(8, 1);
        if (!slogger_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create slogger hash table");
        }
    }

    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);

    return slogger;
}

static int zpn_fohh_client_slogger_store(struct zpn_fohh_client_slogger *slogger, int64_t customer_id)
{
    int res = ZPN_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);

    if (!slogger_by_id) {
        slogger_by_id = argo_hash_alloc(8, 1);
        if (!slogger_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create slogger hash table");
            ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
            return ZPN_RESULT_NO_MEMORY;
        }
    }

    res = argo_hash_store(slogger_by_id, &customer_id, sizeof(customer_id), 0, slogger);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot store slogger to hash table, customer_id = %ld", (long)customer_id);
    }

    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
    return res;
}

static int zpn_fohh_client_slogger_remove(int64_t customer_id)
{
    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);

    if (slogger_by_id) {
        argo_hash_remove(slogger_by_id, &customer_id, sizeof(customer_id), NULL);
    }

    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_fohh_client_slogger_mt *get_mtunnel_by_tag_id(struct zpn_fohh_client_slogger *zfcs, int32_t tag_id)
{
    struct zpn_fohh_client_slogger_mt *mt;

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    mt = argo_hash_lookup(zfcs->mtunnel_by_id,
                          &tag_id,
                          sizeof(tag_id),
                          NULL);
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
    return mt;
}

static void zpn_slogger_resume_from_failed_redirect(struct zpn_fohh_client_slogger *zfcs)
{
    /* For the initial redirect do nothing */
    if (zfcs->initial_redir_received) {
        return;
    }

    /* cancel timeout */
    if (zfcs->redir_timeout_ev) {
        event_free(zfcs->redir_timeout_ev);
        zfcs->redir_timeout_ev = NULL;
    }

    zfcs->initial_redir_received = 1;
    zfcs->ignore_next_connection_drop = 0;

    switch (zfcs->status) {
    case zfc_ready:
        /* resume buffered mtunnel requests using the current broker. */
        zpn_fohh_client_slogger_schedule_reporting(zfcs);
        break;
    case zfc_not_connected:
        zfcs_terminate_mtunnels(zfcs);
        break;
    default:
        break;
    }
}

static int zpn_fohh_client_slogger_mt_end_message(void *cookie, void *object) {
    struct zpn_fohh_client_slogger *zfcs = cookie;
    struct zpn_fohh_client_slogger_mt *mt = object;
    int res = 1;
    if(zfcs && zfcs->zfc && zfcs->zfc->conn) {
        struct zpn_mtunnel_end mtunnel_end;
        mtunnel_end.drop_data = 0;
        mtunnel_end.mtunnel_id = mt->mtunnel_id;
        mtunnel_end.err_code = 0;
        mtunnel_end.error = SIEM_CONN_CLOSE_REDIRECT;
        mtunnel_end.tag_id = mt->broker_mconn.tag_id;

        while((res != 0) && (epoch_s() - zfcs->last_broker_redirect_time < 60)) {
            res = fohh_argo_serialize(zfcs->zfc->conn, zpn_mtunnel_end_description, &mtunnel_end, 0,
                                  fohh_queue_element_type_control);
        }

        if(epoch_s() - zfcs->last_broker_redirect_time >= 60) {
            return ZPN_RESULT_ERR;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_fohh_client_slogger_redirect(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct zpn_fohh_client_slogger *zfcs = argo_structure_cookie_ptr;
    struct fohh_connection *f_conn = zfcs->zfc->conn;
    struct zpn_broker_redirect *req = object->base_structure_void;
    int res;

    if (zpn_debug_get(ZPN_DEBUG_SIEM_IDX)) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfcs->zfc), dump);
        }
    }

    if (!req->broker_count) {
        ZPN_LOG(AL_WARNING, "%s: broker redirect message returned no broker", ZFC_DBG(zfcs->zfc));
        zpn_slogger_resume_from_failed_redirect(zfcs);
        return ZPN_RESULT_NO_ERROR;
    }

    switch (zfcs->slogger_redirect_mode) {
        case SLOGGER_REDIRECT_MODE_DISABLED:
        {
            /* no redirect */
            ZPN_LOG(AL_INFO, "%s: ignoring redirect message - redirect is disabled", ZFC_DBG(zfcs->zfc));
            zpn_slogger_resume_from_failed_redirect(zfcs);
            return ZPN_RESULT_NO_ERROR;
        }
        case SLOGGER_REDIRECT_MODE_FORCE:
        {
            if (!req->force_redirect) {
                ZPN_LOG(AL_INFO, "%s: ignoring redirect message - redirect is optional", ZFC_DBG(zfcs->zfc));
                zpn_slogger_resume_from_failed_redirect(zfcs);
                return ZPN_RESULT_NO_ERROR;
            }
            /* else do redirect */
            break;
        }
        case SLOGGER_REDIRECT_MODE_FULL:
        {
            /* do redirect */
            break;
        }
        default:
        {
            /* should not happen with sanitized config */
            ZPN_LOG(AL_ERROR, "Invalid zpn_slogger_redirect_mode_sanitized = %"PRId64,
                            zfcs->slogger_redirect_mode);
            zpn_slogger_resume_from_failed_redirect(zfcs);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    ZPN_LOG(AL_INFO, "Data broker redirect message customer_id for %ld at time %ld", (long) zfcs->customer_id, (long) epoch_us());
    zfcs->broker_redirect = 1;
    zfcs->last_broker_redirect_time = epoch_s();
    int64_t walk_key = 0;
    argo_hash_walk(zfcs->mtunnel_by_id,
                    &walk_key,
                    zpn_fohh_client_slogger_mt_end_message,
                    zfcs);
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

    res = fohh_redirect(f_conn, (const char **)req->brokers, req->broker_count, req->timestamp_s, NULL);

    if (res) {
        ZPN_LOG(AL_WARNING, "%s: fohh_redirect failed with %s", ZFC_DBG(zfcs->zfc), zpn_error_description_string(res));
        zpn_slogger_resume_from_failed_redirect(zfcs);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_SIEM("%s is redirected", ZFC_DBG(zfcs->zfc));

    /* cancel timeout */
    if (zfcs->redir_timeout_ev) {
        event_free(zfcs->redir_timeout_ev);
        zfcs->redir_timeout_ev = NULL;
    }

    if ((!zfcs->initial_redir_received) &&
        (0 != strcmp(req->reason, BRK_REDIRECT_REASON_BROKER_RESTART))) {
        /* fohh_redirect will cause the current connection to be dropped.
        * We will hold outstanding mtunnels when we received that event.
        * Note this works only for the first redirect. */
        zfcs->ignore_next_connection_drop = 1;
        zfcs->initial_redir_received = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_slogger_redir_timeout(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_slogger *zfcs = cookie;

    ZPN_LOG(AL_WARNING, "%s: initial broker redirect message timed out!", ZFC_DBG(zfcs->zfc));

    zpn_slogger_resume_from_failed_redirect(zfcs);
}

static int zpn_client_app_cb(void *argo_cookie_ptr,
                             void *argo_structure_cookie_ptr,
                             struct argo_object *object)
{
    struct zpn_client_app *req = (struct zpn_client_app *)object->base_structure_void;
    char dump[8000];

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (!req->deleted && !req->bypass) {
        ZPN_DEBUG_SIEM("Application: %s", req->app_domain);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_app_complete_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_fohh_client_slogger *zfcs = argo_structure_cookie_ptr;

    char dump[8000];
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfcs->zfc), dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_slogger_mt_terminate(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_fohh_client_slogger_mt *mt = cookie;

    if (mt) {

        ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

        if ((mt->incarnation == int_cookie) && !mt->cannot_call_siem) {

            mt->status = zfcs_mt_remote_disconnect;

            if (mt->status_cb) {

                /* We are done and have informed siem, so no need to call back to siem anymore */
                mt->cannot_call_siem = 1;

                ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

                if (mt->mtunnel_id) {
                    ZPN_DEBUG_MTUNNEL("%s: calling status callback to report remote disconnect", mt->mtunnel_id);
                } else {
                    ZPN_DEBUG_MTUNNEL("mtunnel_id already freed: calling status callback to report remote disconnect");
                }

                (*mt->status_cb)(mt, mt->cookie_rs, mt->cookie_int, mt->status);
                return;
            }
        }

        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
    }
}

/* MUST HOLD LOCK WHEN CALLED */
static int zpn_fohh_client_slogger_schedule_mt_terminate(void *cookie, void *object)
{
    struct zpn_fohh_client_slogger *zfcs = cookie;
    struct zpn_fohh_client_slogger_mt *mt = object;

    if (zfcs && zfcs->zfc && zfcs->zfc->conn && mt->status_cb && mt->cookie_rs && !(__sync_fetch_and_add_4(&mt->cannot_call_siem, 0))) {
        if (!mt->deleting) {
            mt->deleting = 1;
            if (fohh_thread_call(fohh_connection_get_thread_id(zfcs->zfc->conn),
                             zpn_fohh_client_slogger_mt_terminate,
                             mt,
                             mt->incarnation) != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_slogger_mt_status_reporting()!");
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mtunnel_request_ack_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_fohh_client_slogger *zfcs = argo_structure_cookie_ptr;
    struct zpn_mtunnel_request_ack *ack = object->base_structure_void;
    struct zpn_fohh_client_slogger_mt *mt;
    int res = ZPN_RESULT_NO_ERROR;

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
    }

    if (!ack->tag_id) {
        ZPN_LOG(AL_NOTICE, "Mtunnel request ack received without tag id");
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfcs, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Mtunnel request ack received without tunnel. It timed out?");
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    if (mt->deleting) {
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_ERROR;
    }

    if (ack->error) {
        ZPN_LOG(AL_NOTICE, "Tunnel request ack error %s : %s", ack->error ? ack->error : "error", ack->reason);
        mt->status = zfcs_mt_connect_error;
        mt->err = ZPN_STRDUP(ack->error, strlen(ack->error));
        mt->deleting = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        goto exit;
    }

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_NOTICE, "Tunnel request ack without mtunnel_id");
        mt->status = zfcs_mt_connect_error;
        mt->deleting = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        goto exit;
    }

    mt->mtunnel_id = ZPN_STRDUP(ack->mtunnel_id, strlen(ack->mtunnel_id));

    zpn_mconn_set_fohh_thread_id(&(mt->slogger_mconn.mconn), fohh_connection_get_thread_id(zfcs->zfc->conn));

    res = zpn_mconn_connect_peer(&(mt->slogger_mconn.mconn), &(mt->broker_mconn.mconn));
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Cannot connect peers", mt->mtunnel_id);
        mt->status = zfcs_mt_connect_error;
        mt->deleting = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        goto exit;
    }

    res = zpn_mconn_add_local_owner(&(mt->slogger_mconn.mconn),
                                    0,
                                    mt,
                                    NULL,
                                    0,
                                    &mconn_fohh_client_slogger_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Cannot add local owner for slogger_mconn", mt->mtunnel_id);
        mt->status = zfcs_mt_connect_error;
        mt->deleting = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        goto exit;
    }

    res = zpn_fohh_client_attach_mconn(zfcs->zfc, &(mt->broker_mconn), &mt->tag_id, sizeof(int32_t));
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Cannot attach mconn to zpn_fohh_client", mt->mtunnel_id);
        mt->status = zfcs_mt_connect_error;
        mt->deleting = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        goto exit;
    }

    ZPN_DEBUG_MTUNNEL("%s: Attached mconn to zpn_fohh_client", mt->mtunnel_id);
    mt->status = zfcs_mt_connected;
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

exit:

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    if (mt->status_cb && mt->cookie_rs && !mt->cannot_call_siem) {
        if (mt->status != zfcs_mt_connected) {
            /* Mtunnel req ack comes back with error, mt is done and
             * we have informed siem and no need to that any more.
             */
            mt->cannot_call_siem = 1;
        }
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        (*mt->status_cb)(mt, mt->cookie_rs, mt->cookie_int, mt->status);
    } else {
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
    }

    return res;
}

static int zpn_mtunnel_end_cb(void *argo_cookie_ptr,
                              void *argo_structure_cookie_ptr,
                              struct argo_object *object)
{
    struct zpn_fohh_client_slogger *zfcs = argo_structure_cookie_ptr;
    struct zpn_mtunnel_end *req = object->base_structure_void;
    const char *id = req->mtunnel_id ? req->mtunnel_id : "NULL";
    struct zpn_fohh_client_slogger_mt *mt = NULL;
    int need_deleted = 0;

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: customer %ld, Rx: %s", ZFC_DBG(zfcs->zfc), (long) zfcs->customer_id, dump);
    }

    ZPN_DEBUG_MCONN("%s: %s: Received tunnel end for tag_id = %d", ZFC_DBG(zfcs->zfc), id, req->tag_id);

    if (!req->tag_id) {
        ZPN_LOG(AL_ERROR, "%s: Expecting tag_id in mtunnel_end request", id);
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfcs, req->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "%s, Cannot find the mtunnel for tag_id = %d", id, req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    if (mt->deleting) {
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_ERROR;
    }
    mt->broker_mconn.mconn.fin_rcvd = 1;
    if (!mt->broker_mconn.mconn.fin_rcvd_us) mt->broker_mconn.mconn.fin_rcvd_us = epoch_us();
    if (req->drop_data) {
        mt->broker_mconn.mconn.drop_tx = req->drop_data;
    }
    mt->status = zfcs_mt_remote_disconnect;
    mt->deleting = 1;
    if (!mt->cannot_call_siem) {
        mt->cannot_call_siem = 1;
        need_deleted = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (need_deleted && mt->status_cb) {
        ZPN_DEBUG_MTUNNEL("%s: calling status callback to report remote disconnect", mt->mtunnel_id);
        (*mt->status_cb)(mt, mt->cookie_rs, mt->cookie_int, mt->status);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zfcs_terminate_mtunnels(struct zpn_fohh_client_slogger *zfcs)
{
    /* Now need to terminate all the mtunnels */
    int64_t walk_key = 0;
    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    argo_hash_walk(zfcs->mtunnel_by_id,
                    &walk_key,
                    zpn_fohh_client_slogger_schedule_mt_terminate,
                    zfcs);
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
}

static int zfcs_status_callback(struct zpn_fohh_client *zfc,
                                void *cookie_void,
                                int64_t cookie_int,
                                enum zfc_status status,
                                const char *error_string)
{
    struct argo_state *argo;
    struct zpn_fohh_client_slogger *zfcs = (struct zpn_fohh_client_slogger *)cookie_void;
    int res = ZPN_RESULT_NO_ERROR;

    ZPN_DEBUG_SIEM("%s: ZPN Fohh client status now %s. %s", ZFC_DBG(zfc), zfc_status_string(status), error_string ? error_string : "No Error");

    zfcs->status = status;

    if (status == zfc_authenticate) {
        argo = fohh_argo_get_rx(zfc->conn);

        /* Register zpn_client_app */
        if ((res = argo_register_structure(argo, zpn_client_app_description, zpn_client_app_cb, zfcs))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_client_app_complete */
        if ((res = argo_register_structure(argo, zpn_client_app_complete_description, zpn_client_app_complete_cb, zfcs))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_complete_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_request_ack */
        if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, zpn_mtunnel_request_ack_cb, zfcs))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_end */
        if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, zpn_mtunnel_end_cb, zfcs))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

    } else if (status == zfc_ready) {
        /* Register zpn_broker_redirect */
        argo = fohh_argo_get_rx(zfc->conn);
        if ((res = argo_register_structure(argo, zpn_broker_redirect_description, zpn_fohh_client_slogger_redirect, zfcs))) {
            ZPN_LOG(AL_ERROR, "%s: register zpn_broker_redirect_description failed with %s", ZFC_DBG(zfc), zpn_error_description_string(res));
            return res;
        }

        if (zfcs->slogger_redirect_mode == SLOGGER_REDIRECT_MODE_FULL && !zfcs->initial_redir_received) {
            /* Start initial redirect sequence by sending fohh_info and wait for response, or timeout. */
            if ((res = fohh_send_info(zfc->conn))) {
                ZPN_LOG(AL_ERROR, "%s: fohh_send_info failed with %s", ZFC_DBG(zfc), zpn_error_description_string(res));
                return res;
            }

            /* start a timer for the initial redirect sequence */
            if (!zfcs->redir_timeout_ev) {
                int thread_num = fohh_connection_get_thread_id(zfc->conn);
                struct event_base *base = fohh_get_thread_event_base(thread_num);
                struct timeval tv;
                zfcs->redir_timeout_ev = event_new(base,
                                            -1,
                                            0,  /* one-time event */
                                            zpn_fohh_client_slogger_redir_timeout,
                                            zfcs);

                tv.tv_sec = SLOGGER_REDIRECT_TIMEOUT_S;
                tv.tv_usec = SLOGGER_REDIRECT_TIMEOUT_US;
                if (event_add(zfcs->redir_timeout_ev, &tv)) {
                    ZPN_LOG(AL_ERROR, "%s: Could not activate redirect timeout", ZFC_DBG(zfc));
                    event_free(zfcs->redir_timeout_ev);
                    zfcs->redir_timeout_ev = NULL;
                }
            }
            ZPN_DEBUG_SIEM("%s: zfc_ready, waiting for redir message from broker...", ZFC_DBG(zfc));
        } else {
            zpn_fohh_client_slogger_schedule_reporting(zfcs);
        }

    } else if (status == zfc_not_connected) {
        if (zfcs->ignore_next_connection_drop) {
            /* Ignore once due to initial reconnect.
             * We'll get another one if the reconnect does not succeed. */
            ZPN_DEBUG_SIEM("%s: zfc_not_connected ignored once while waiting for redir message from broker...", ZFC_DBG(zfc));

            zfcs->ignore_next_connection_drop = 0;
            return ZPN_RESULT_NO_ERROR;
        }
        zfcs_terminate_mtunnels(zfcs);
        zpn_fohh_client_slogger_schedule_reporting(zfcs);

    } else {
        zpn_fohh_client_slogger_schedule_reporting(zfcs);
    }

    return ZPN_RESULT_NO_ERROR;
}

struct zpn_fohh_client_slogger *zpn_fohh_client_slogger_create(const char *broker_name,
                                                               const char *cloud_root_pem_filename,
                                                               const char *client_certificate_pem_filename,
                                                               const char *client_key_pem_filename,
                                                               zfcs_status_callback_f callback,
                                                               void *cookie_rs,
                                                               int64_t cookie_int,
                                                               const char *cloud_name,
                                                               int64_t customer_id)
{
    struct zpn_fohh_client_slogger *zfcs = NULL;
    struct zfcs_cookie *c = NULL;

    zfcs = zpn_fohh_client_slogger_get(customer_id);
    if (zfcs) {

        ZPN_DEBUG_SIEM("Have slogger, zfcs = %p, %s, customer_id = %ld", zfcs, ZFC_DBG(zfcs->zfc), (long)customer_id);

        c = ZPN_CALLOC(sizeof(struct zfcs_cookie));
        if (!c) {
            ZPN_LOG(AL_ERROR, "Cannot allocate cookie object for slogger");
            return NULL;
        } else {
            c->cookie_rs = cookie_rs;
            c->cookie_int = cookie_int;
            ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
            LIST_INSERT_HEAD(&(zfcs->cookie_list), c, list_entry);
            ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
        }

        zpn_fohh_client_slogger_schedule_reporting(zfcs);

        return zfcs;
    }

    ZPN_DEBUG_SIEM("%s: Allocate new slogger", broker_name);

    zfcs = ZPN_CALLOC(sizeof(struct zpn_fohh_client_slogger));
    if (zfcs) {
        int res = ZPN_RESULT_NO_ERROR;

        zfcs->lock = ZPATH_MUTEX_INIT;
        zfcs->status_cb = callback;
        zfcs->status = zfc_init;
        zfcs->customer_id = customer_id;
        zfcs->mtunnel_by_id = argo_hash_alloc(7, 1);
        zfcs->broker_redirect = 0;
        zfcs->last_broker_redirect_time = 0;
        zfcs->slogger_redirect_mode = zpath_config_override_get_config_int(SLOGGER_REDIRECT_FEATURE_OVERRIDE,
                                                                           &zfcs->slogger_redirect_mode,
                                                                           (int64_t)SLOGGER_REDIRECT_MODE_DEFAULT,
                                                                           zfcs->customer_id,
                                                                           ZPATH_INSTANCE_GID ? ZPATH_INSTANCE_GID : ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                           (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                           (int64_t)0);

        zfcs->zfc = zpn_fohh_client_init(zpn_client_type_slogger,
                                         zpn_tunnel_auth_cloud,
                                         NULL,
                                         NULL,
                                         broker_name,
                                         NULL,
                                         NULL,
                                         cloud_root_pem_filename,
                                         client_certificate_pem_filename,
                                         client_key_pem_filename,
                                         NULL,
                                         zfcs_status_callback,
                                         zfcs,
                                         0,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         cloud_name,
                                         customer_id,
                                         NULL,
                                         NULL,
                                         NULL,
                                         0,
                                         0);
        if (!zfcs->zfc) {
            ZPN_LOG(AL_ERROR, "Cannot create zfc");
            goto fail_free;
        }


        LIST_INIT(&(zfcs->cookie_list));

        /* Save the cookie to do callback when slogger is ready */
        c = ZPN_CALLOC(sizeof(struct zfcs_cookie));
        if (!c) {
            ZPN_LOG(AL_ERROR, "Cannot allocate cookie object for slogger");
            goto fail_free;
        } else {
            c->cookie_rs = cookie_rs;
            c->cookie_int = cookie_int;
            LIST_INSERT_HEAD(&(zfcs->cookie_list), c, list_entry);
        }

        res = zpn_fohh_client_slogger_store(zfcs, customer_id);
        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot store zfcs");
            goto fail_free;
        }

        return zfcs;
    }

fail_free:
    if (zfcs) {
        if (c) ZPN_FREE(c);
        if (zfcs->zfc) zpn_fohh_client_fini(zfcs->zfc);
        if (zfcs->mtunnel_by_id) argo_hash_free(zfcs->mtunnel_by_id);
        ZPN_FREE(zfcs);
    }

    return NULL;
}

void zpn_fohh_client_slogger_get_conn(struct zpn_fohh_client_slogger *zfcs, char *desc, size_t desc_len)
{
    if (!zfcs) {
        snprintf(desc, desc_len, "No zfcs");
        return;
    }
    if (!zfcs->zfc) {
        snprintf(desc, desc_len, "No zfcs->zfc");
        return;
    }
    zpn_fohh_client_get_description(zfcs->zfc, desc, desc_len);
}

void zpn_fohh_client_slogger_destroy(struct zpn_fohh_client_slogger *zfcs)
{
    if (zfcs) {
        ZPN_DEBUG_SIEM("Destroying slogger, zfcs = %p, %s, customer_id = %ld", zfcs, ZFC_DBG(zfcs->zfc), (long)zfcs->customer_id);

        zpn_fohh_client_slogger_remove(zfcs->customer_id);

        ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);

        if (zfcs->zfc) {
            zpn_fohh_client_fini(zfcs->zfc);
            zfcs->zfc = NULL;
        }

        /* All the mtunnels should be gone by now */
        if (zfcs->mtunnel_by_id) {
            argo_hash_free(zfcs->mtunnel_by_id);
            zfcs->mtunnel_by_id = NULL;
        }

        if (zfcs->redir_timeout_ev) {
            event_free(zfcs->redir_timeout_ev);
            zfcs->redir_timeout_ev = NULL;
        }

        ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

        ZPN_FREE(zfcs);
    }
}

static void zfcs_mt_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_slogger_mt *mt = NULL;
    struct zpn_fohh_client_slogger_mt *tmp_mt = NULL;

    /* Reap clean mt */
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    for (mt = TAILQ_FIRST(&(free_q.reap_mt_list)); mt != NULL; mt = tmp_mt) {
        tmp_mt = TAILQ_NEXT(mt, free_q_entry);

        /* If mt is clean, move from reap list to free list */
        if (zpn_mconn_fohh_tlv_clean(&mt->broker_mconn) && __sync_fetch_and_add_4(&mt->cannot_call_siem, 0)) {
            TAILQ_REMOVE(&(free_q.reap_mt_list), mt, free_q_entry);
            free_q.stats.reap_queue_count--;
            TAILQ_INSERT_TAIL(&(free_q.mt_list), mt, free_q_entry);
            free_q.stats.free_queue_count++;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

static int mtunnels_debug_walk_f(void *cookie, void *object)
{
    struct zpn_fohh_client_slogger_mt *mt = object;
    struct debug_walk_args *args          = cookie;

    if (mt && !mt->deleting && args) {
        struct zpn_mconn_fohh_tlv *broker_mconn = &(mt->broker_mconn);

        args->s += sxprintf(args->s, args->e, "%s: tag_id = %d, incarnation = %"PRId64", status = %s\n", mt->mtunnel_id, mt->tag_id, mt->incarnation, zfcs_mt_status_string(mt->status));
        if (rs_cookie_details_cb) {
            /* Print rx context details if it exists..*/
            args->s += rs_cookie_details_cb(mt->cookie_rs, args->s, args->e - args->s);
        }
        args->s += sxprintf(args->s, args->e, "\tclient transmit buffer current buffered len = %zu, bytes_to_client = %"PRId64", (mconn) bytes_to_peer = %"PRId64", (mconn) bytes_to_peer_attempt = %"PRId64", (mconn) bytes_from_peer = %"PRId64"\n",
                            zpn_mconn_get_transmit_buffer_len(&(broker_mconn->mconn)), broker_mconn->mconn.bytes_to_client, broker_mconn->mconn.bytes_to_peer, broker_mconn->mconn.bytes_to_peer_attempt, broker_mconn->mconn.bytes_from_peer);
        args->s += sxprintf(args->s, args->e, "\tFLAGS: fin_rcvd = %d, fin_sent = %d, needs_to_forward = %d, needs_to_disconnect = %d, to_client_paused = %d\n",
                            broker_mconn->mconn.fin_rcvd, broker_mconn->mconn.fin_sent, broker_mconn->mconn.client_needs_to_forward, broker_mconn->mconn.client_needs_to_disconnect_local_owner, broker_mconn->mconn.to_client_paused);
        args->s += sxprintf(args->s, args->e, "\tMTUNNEL FLOW CONTROL: total tx_data = %"PRId64", current tx_limit = %"PRId64", remote_tx_limit = %"PRId64", (rx by remote system) remote_rx_data = %"PRId64", (tx) fc_blocked = %d, remote_paused = %d\n",
                            broker_mconn->tx_data, broker_mconn->tx_limit, broker_mconn->remote_tx_limit, broker_mconn->remote_rx_data, broker_mconn->fc_blocked, broker_mconn->remote_paused);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dump_siem_all_mtunnel(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    if (!query_values[0])
        return ZPATH_RESULT_ERR;

    uint64_t cid = ZPATH_GID_GET_CUSTOMER_GID(strtoull(query_values[0], NULL, 10));

    struct zpn_fohh_client_slogger *zfcs = zpn_fohh_client_slogger_get(cid);

    if (zfcs) {
        int64_t walk_key = 0;
        char *buffer = ZPN_CALLOC(BUFFER_SIZE);
        if (buffer) {
            struct debug_walk_args args = {.s = buffer, .e = buffer + BUFFER_SIZE};

            ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
            if (zfcs->mtunnel_by_id)
                argo_hash_walk(zfcs->mtunnel_by_id, &walk_key, mtunnels_debug_walk_f, &args);
            ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

            ZDP("Slogger siem context dump for customer_id = %"PRIu64"\n%s", cid, buffer);
            ZPN_FREE(buffer);
        }
    } else {
        ZDP("No slogger client context found for customer_id = %"PRIu64"\n", cid);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dump_siem_mtunnel(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    struct zpn_fohh_client_slogger *zfcs = NULL;
    struct zpn_fohh_client_slogger_mt *mt = NULL;
    struct zpn_mconn_fohh_tlv *broker_mconn;
    uint64_t cid;
    uint32_t tag_id;

    if (!query_values[0] || !query_values[1]) {
        return ZPATH_RESULT_ERR;
    }

    cid = strtoull(query_values[0], NULL, 10);
    cid = ZPATH_GID_GET_CUSTOMER_GID(cid);
    tag_id = strtoul(query_values[1], NULL, 10);

    ZDP("Slogger debug siem customer_id = %ld, mtunneld = %d\n", (long)cid, tag_id);

    zfcs = zpn_fohh_client_slogger_get(cid);
    if (!zfcs) {
        ZDP("Cannot find slogger with customer id = %ld\n", (long)cid);
        return ZPATH_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfcs, tag_id);
    if (!mt) {
        ZDP("Cannot find mt with tag_id = %d\n", tag_id);
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("%s: tag_id = %d, incarnation = %ld, status = %s\n", mt->mtunnel_id, mt->tag_id, (long)mt->incarnation, zfcs_mt_status_string(mt->status));
    broker_mconn = &(mt->broker_mconn);
    ZDP("   client transmit buffer current buffered len = %ld, bytes_to_client = %ld, (mconn) bytes_to_peer = %ld, (mconn) bytes_to_peer_attempt = %ld, (mconn) bytes_from_peer = %ld\n",
        zpn_mconn_get_transmit_buffer_len(&(broker_mconn->mconn)),
        (long)broker_mconn->mconn.bytes_to_client, (long)broker_mconn->mconn.bytes_to_peer,
        (long)broker_mconn->mconn.bytes_to_peer_attempt, (long)broker_mconn->mconn.bytes_from_peer);
    ZDP("   FLAGS: fin_rcvd = %d, fin_sent = %d, needs_to_forward = %d, needs_to_disconnect = %d, to_client_paused = %d\n",
        broker_mconn->mconn.fin_rcvd, broker_mconn->mconn.fin_sent, broker_mconn->mconn.client_needs_to_forward,
        broker_mconn->mconn.client_needs_to_disconnect_local_owner, broker_mconn->mconn.to_client_paused);
    ZDP("   MTUNNEL FLOW CONTROL: total tx_data = %ld, current tx_limit = %ld, remote_tx_limit = %ld, (rx by remote system) remote_rx_data = %ld, (tx) fc_blocked = %d, remote_paused = %d\n",
        (long)broker_mconn->tx_data, (long)broker_mconn->tx_limit, (long)broker_mconn->remote_tx_limit,
        (long)broker_mconn->remote_rx_data,
        broker_mconn->fc_blocked, broker_mconn->remote_paused);


    return ZPN_RESULT_NO_ERROR;
}

void zpn_fohh_client_slogger_init(struct event_base *base, zfcs_get_cookie_details *cookie_cb)
{
    if (!initialized) {
        int res;

        local_lock = ZPATH_MUTEX_INIT;

        zpn_fohh_client_slogger_mt_free_q_init();

        mt_timer = event_new(base, -1, EV_PERSIST, zfcs_mt_timer_callback, NULL);
        if (mt_timer) {
            struct timeval tv;

            tv.tv_sec = 5;
            tv.tv_usec = 0;
            if (event_add(mt_timer, &tv)) {
                ZPN_LOG(AL_ERROR, "Slogger cannot activate mt timer");
            }
        } else {
            ZPN_LOG(AL_ERROR, "Slogger cannot create mt timer");
        }

        rs_cookie_details_cb = cookie_cb;

        res = zpath_debug_add_read_command("Dump SIEM debugging state",
                                      "/siem/dump_mt",
                                      zpn_dump_siem_mtunnel,
                                      NULL,
                                      "cid", "The SIEM customer GID",
                                      "tag_id", "The SIEM Mtunnel with tag ID to dump",
                                      NULL);
        if(res) {
            ZPN_LOG(AL_ERROR, "Could not add the debug interface /siem/dump_mt: %s", zpn_result_string(res));
        }

        res = zpath_debug_add_read_command("Dump SIEM debugging state - all MT for a customer",
                                      "/siem/dump_mt_all",
                                      zpn_dump_siem_all_mtunnel,
                                      NULL,
                                      "cid", "The SIEM customer GID",
                                      NULL);
        if(res) {
            ZPN_LOG(AL_ERROR, "Could not add the debug interface /siem/dump_mt_all: %s", zpn_result_string(res));
        }

        if (zpn_mconn_fohh_tlv_init_debug()) {
            ZPN_LOG(AL_ERROR, "Could not initialize debug command for fohh_tlv");
        }
        initialized = 1;
    }
}

static void zpn_fohh_client_slogger_unregister_ack(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_fohh_client_slogger *zfcs = cookie;
    struct zfcs_cookie *c = NULL;
    struct zfcs_cookie *tmp;

    struct zfcs_cookie_head head;

    int donot_call_again = 0;

    /* No callback? */
    if (!zfcs->status_cb) return;

    LIST_INIT(&head);

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);

    LIST_FOREACH_SAFE(c, &(zfcs->cookie_list), list_entry, tmp) {
        if (c->disconnect) {
            LIST_REMOVE(c, list_entry);
            LIST_INSERT_HEAD(&head, c, list_entry);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

    while ((c = LIST_FIRST(&head))) {
        LIST_REMOVE(c, list_entry);
        (*zfcs->status_cb)(zfcs, c->cookie_rs, c->cookie_int, zfc_not_connected, NULL, &donot_call_again);
        ZPN_FREE(c);
    }
}

/*
 * If return ZPN_RESULT_NO_ERROR, caller is free to do whatever.
 * If return ZPN_RESULT_ASYNCHRONOUS, caller should wait for callback to know if we are done.
 */
int zpn_fohh_client_slogger_unregister(struct zpn_fohh_client_slogger *zfcs, void *cookie_rs, int64_t cookie_int)
{
    struct zfcs_cookie *c = NULL;

    if (zfcs) {

        /* Find the corresponding cookie, and mark it */

        ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
        LIST_FOREACH(c, &(zfcs->cookie_list), list_entry) {
            if ((c->cookie_rs == cookie_rs) &&
                (c->cookie_int == cookie_int)) {
                c->disconnect = 1;
                break;
            }
        }
        ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

        /* Schedule unregister ack callback to siem to ackowledge we will not call back anymore */
        if (c) {
            if (zfcs->zfc && zfcs->zfc->conn) {
                if (fohh_thread_call(fohh_connection_get_thread_id(zfcs->zfc->conn),
                                     zpn_fohh_client_slogger_unregister_ack,
                                     zfcs,
                                     fohh_connection_incarnation(zfcs->zfc->conn)) != FOHH_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_slogger_unregister_ack!");
                } else {
                    return ZPN_RESULT_ASYNCHRONOUS;
                }
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


/*************************************************************************************************
 *
 *  zfcs_mt stuff
 *
 *************************************************************************************************/
const char *zfcs_mt_status_string(enum zpn_fohh_client_slogger_mt_status status)
{
    switch (status) {
    case zfcs_mt_connecting:
        return "zfcs_mt_connecting";
    case zfcs_mt_connected:
        return "zfcs_mt_connected";
    case zfcs_mt_remote_disconnect:
        return "zfcs_mt_remote_disconnect";
    case zfcs_mt_connect_error:
        return "zfcs_mt_connect_error";
    default:
        return "ERROR";
    }
}


/*************************************************************************************************
 *  zfcs_mt local owner
 */

int zpn_mconn_fohh_client_slogger_init(struct zpn_mconn_fohh_client_slogger *mconn_slogger, void *mconn_self)
{
    return zpn_mconn_init(&(mconn_slogger->mconn), mconn_self, mconn_fohh_tlv_c);
}

static int zpn_mconn_fohh_client_slogger_receive(struct zpn_mconn_fohh_client_slogger *mconn_slogger,
                                                 struct evbuffer *buf,
                                                 size_t len)
{
    struct zpn_mconn *mconn = &(mconn_slogger->mconn);
    int res = ZPN_RESULT_NO_ERROR;

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return res;
    }

    if (!mconn->global_owner) {
        goto exit;
    }

    if (!mconn_slogger->bound_to_siem) {
        res = ZPN_RESULT_WOULD_BLOCK;
        goto exit;
    }

    if (mconn_slogger->rx_paused) {
        res = ZPN_RESULT_WOULD_BLOCK;
        goto exit;
    }

    if (len) {
        res = zpn_client_process_rx_data(mconn, buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

exit:
    if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_slogger_bind_cb(void *mconn_base,
                                                 void *mconn_self,
                                                 void *owner,
                                                 void *owner_key,
                                                 size_t owner_key_length,
                                                 int64_t *owner_incarnation)
{
    struct zpn_mconn_fohh_client_slogger *slogger_mconn = mconn_base;

    slogger_mconn->bound_to_siem = 1;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_slogger_unbind_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int drop_buffered_data,
                                                   int dont_propagate,
                                                   const char *err)
{
    struct zpn_mconn_fohh_client_slogger *slogger_mconn = mconn_base;

    slogger_mconn->bound_to_siem = 0;

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_fohh_client_slogger_lock_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length)
{
}


static void zpn_mconn_fohh_client_slogger_unlock_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length)
{
}

static int zpn_mconn_fohh_client_slogger_transmit_cb(void *mconn_base,
                                                     void *mconn_self,
                                                     void *owner,
                                                     void *owner_key,
                                                     size_t owner_key_length,
                                                     int64_t owner_incarnation,
                                                     int fohh_thread_id,
                                                     struct evbuffer *buf,
                                                     size_t buf_len)
{
    struct zpn_mconn_fohh_client_slogger *slogger_mconn = mconn_base;
    struct zpn_mconn *mconn = &(slogger_mconn->mconn);
    struct zpn_fohh_client_slogger_mt *mt = mconn->global_owner;
    int res = ZPN_RESULT_NO_ERROR;
    int need_lock = 0;

    ZPN_DEBUG_MCONN("Send data. Len = %ld, buf_len = %ld", (long) evbuffer_get_length(buf), (long) buf_len);

    if (mconn->global_owner) {
        if (need_lock && mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return res;
    }

    if (!mconn->global_owner) {
        goto exit;
    }

    if (!slogger_mconn->bound_to_siem) {
        ZPN_DEBUG_MCONN("XXX, haven't connected yet");
        goto exit;
    }

    if (slogger_mconn->tx_paused) {
        ZPN_DEBUG_MCONN("XXX, tx_paused?");
        goto exit;
    }

    if (buf && buf_len) {
        mt = mconn->global_owner;

        if (mt && mt->consume_cb) {
            size_t pre_len, post_len;
            int enq_len;

            ZPATH_LOG(AL_CRITICAL, "Implement me: Need to verify that the connection isn't going away here- check mt->cannot_call_siem");

            pre_len = evbuffer_get_length(buf);
            res = (*mt->consume_cb)(mt, buf, buf_len, mt->cookie_rs, mt->cookie_int);
            post_len = evbuffer_get_length(buf);

            enq_len = pre_len - post_len;
            mconn->bytes_to_client += enq_len;
            if (enq_len && mconn->peer) {
                zpn_mconn_client_window_update(mconn->peer, 0, enq_len, 0);
            }
        } else {
            ZPN_DEBUG_MCONN("XXX, no mt or consume_cb?, mt = %p, consume_cb = %p", mt, mt->consume_cb);
        }
    }

exit:
    if (need_lock && mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_slogger_pause_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length,
                                                  int64_t owner_incarnation,
                                                  int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_slogger *mconn_slogger = mconn_base;

    mconn_slogger->rx_paused = 1;

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_slogger_resume_siem(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_fohh_client_slogger_mt *mt = cookie;
    if(mt) {
        ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
        int cannot_call_siem = mt->cannot_call_siem;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        if (mt->unblock_cb && !cannot_call_siem) {
            (*mt->unblock_cb)(mt, mt->cookie_rs, mt->cookie_int);
        }
    }

}

static int zpn_mconn_fohh_client_slogger_resume_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_slogger *mconn_slogger = mconn_base;
    struct zpn_mconn *mconn = &(mconn_slogger->mconn);
    struct zpn_fohh_client_slogger_mt *mt = mconn->global_owner;

    mconn_slogger->rx_paused = 0;

    if (mt) {
        struct zpn_fohh_client_slogger *zfcs = ((struct zpn_fohh_client_slogger_mt *)mt)->state;

        if (zfcs && zfcs->zfc && zfcs->zfc->conn) {
            if (fohh_thread_call(fohh_connection_get_thread_id(zfcs->zfc->conn),
                                 zpn_fohh_client_slogger_resume_siem,
                                 mt,
                                 0) != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_slogger_resume_siem!");
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_fohh_client_slogger_forward_tunnel_end_cb(void *mconn_base,
                                                        void *mconn_self,
                                                        void *owner,
                                                        void *owner_key,
                                                        size_t owner_key_length,
                                                        int64_t owner_incarnation,
                                                        const char *err,
                                                        int32_t drop_data)
{
    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_fohh_client_slogger_window_update_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id,
                                                    int tx_len,
                                                    int batch_win_upd)
{
    return;
}

void zpn_mconn_fohh_client_slogger_stats_update_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id,
                                                   enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_fohh_client_slogger_disable_read_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length,
                                                  int64_t owner_incarnation,
                                                  int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_slogger_enable_read_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length,
                                                  int64_t owner_incarnation,
                                                  int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls mconn_fohh_client_slogger_calls = {
    zpn_mconn_fohh_client_slogger_bind_cb,
    zpn_mconn_fohh_client_slogger_unbind_cb,
    zpn_mconn_fohh_client_slogger_lock_cb,
    zpn_mconn_fohh_client_slogger_unlock_cb,
    zpn_mconn_fohh_client_slogger_transmit_cb,
    zpn_mconn_fohh_client_slogger_pause_cb,
    zpn_mconn_fohh_client_slogger_resume_cb,
    zpn_mconn_fohh_client_slogger_forward_tunnel_end_cb,
    zpn_mconn_fohh_client_slogger_window_update_cb,
    zpn_mconn_fohh_client_slogger_stats_update_cb,
    zpn_mconn_fohh_client_slogger_disable_read_cb,
    zpn_mconn_fohh_client_slogger_enable_read_cb
};

/*************************************************************************************************
 *  zfcs_mt global owner
 */

static int global_bind_broker_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;
    struct zpn_fohh_client_slogger *zfcs = ((struct zpn_fohh_client_slogger_mt *)mt)->state;
    int res;

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    res = argo_hash_store(zfcs->mtunnel_by_id,
                          global_owner_key,
                          global_owner_key_length,
                          0,
                          mt);
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);
    *global_owner_incarnation = mt->incarnation;

    return res;
}

static int global_unbind_broker_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;
    struct zpn_fohh_client_slogger *zfcs = ((struct zpn_fohh_client_slogger_mt *)mt)->state;
    int res;

    ZPATH_MUTEX_LOCK(&(zfcs->lock), __FILE__, __LINE__);
    res = argo_hash_remove(zfcs->mtunnel_by_id,
                           global_owner_key,
                           global_owner_key_length,
                           NULL);
    ZPATH_MUTEX_UNLOCK(&(zfcs->lock), __FILE__, __LINE__);

    return res;
}

static int global_bind_slogger_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *global_owner,
                                  void *global_owner_key,
                                  size_t global_owner_key_length,
                                  int64_t *global_owner_incarnation)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    *global_owner_incarnation = mt->incarnation;
    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_slogger_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length,
                                    int64_t global_owner_incarnation,
                                    int drop_buffered_data,
                                    int dont_propagate,
                                    const char *err)
{
    return ZPN_RESULT_NO_ERROR;
}

static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error)
{
}

static int global_ip_proto_cb(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    return mt->ip_proto;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length)
{
    return 0;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    return (struct zpn_mconn *)mconn_base;
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    return mt->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    if (mt->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *global_owner,
                                  void *global_owner_key,
                                  size_t global_owner_key_length)
{
    struct zpn_fohh_client_slogger_mt *mt = mconn_self;

    return mt->mtunnel_id;
}

struct zpn_mconn_global_owner_calls zfcs_mt_global_call_set_broker = {
    global_bind_broker_cb,
    global_unbind_broker_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

struct zpn_mconn_global_owner_calls zfcs_mt_global_call_set_slogger = {
    global_bind_slogger_cb,
    global_unbind_slogger_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

struct zpn_fohh_client_slogger_mt *zpn_fohh_client_slogger_mt_create(struct zpn_fohh_client_slogger *zfcs,
                                                                     const char *hostname,
                                                                     const uint16_t port,
                                                                     const int ip_proto,
                                                                     int use_tls,
                                                                     zfcs_mt_status_callback_f *status_cb,
                                                                     zfcs_mt_consume_callback_f *consume_cb,
                                                                     zfcs_mt_unblock_callback_f *unblock_cb,
                                                                     void *cookie_rs,
                                                                     int64_t cookie_int,
                                                                     int64_t app_gid)
{
    struct zpn_fohh_client_slogger_mt *zfcs_mt = NULL;
    int32_t tag_id;
    int res;

    zfcs_mt = zpn_fohh_client_slogger_mt_allocate();
    if (zfcs_mt) {

        zfcs_mt->lock = ZPATH_MUTEX_INIT;
        zfcs_mt->hostname = ZPN_STRDUP(hostname, strlen(hostname));
        zfcs_mt->port = port;
        zfcs_mt->ip_proto = ip_proto;
        zfcs_mt->state = zfcs;
        tag_id = zpn_fohh_client_next_tag_id(zfcs->zfc);
        zfcs_mt->tag_id = tag_id;
        zfcs_mt->status = zfcs_mt_connecting;
        zfcs_mt->status_cb = status_cb;
        zfcs_mt->consume_cb = consume_cb;
        zfcs_mt->unblock_cb = unblock_cb;
        zfcs_mt->cookie_rs = cookie_rs;
        zfcs_mt->cookie_int = cookie_int;
        if (use_tls) {
            zfcs_mt->app_type = MTUNNEL_USE_TLS;
        } else {
            zfcs_mt->app_type = NULL;
        }
        zfcs_mt->app_gid = app_gid;

        zpn_mconn_fohh_tlv_init(&(zfcs_mt->broker_mconn), zfcs_mt, mconn_fohh_tlv_c);
        res = zpn_mconn_add_global_owner(&(zfcs_mt->broker_mconn.mconn),
                                         0,
                                         zfcs_mt,
                                         &(zfcs_mt->tag_id),
                                         sizeof(tag_id),
                                         &zfcs_mt_global_call_set_broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to add global owner");
        }

        zpn_mconn_fohh_client_slogger_init(&(zfcs_mt->slogger_mconn), zfcs_mt);
        res = zpn_mconn_add_global_owner(&(zfcs_mt->slogger_mconn.mconn),
                                         0,
                                         zfcs_mt,
                                         &(zfcs_mt->tag_id),
                                         sizeof(tag_id),
                                         &zfcs_mt_global_call_set_slogger);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to add global owner");
        }

        ZPN_LOG(AL_NOTICE, "%s customer %ld: Sending mtunnel request for %s:%d:%d with tag_id = %d.",
                ZFC_DBG(zfcs->zfc),
                (long) zfcs->customer_id,
                zfcs_mt->hostname,
                zfcs_mt->ip_proto,
                zfcs_mt->port,
                zfcs_mt->tag_id);
        res = zpn_send_zpn_mtunnel_request(&(zfcs->zfc->fohh_tlv_state.tlv),
                                           fohh_connection_incarnation(zfcs->zfc->conn),   /* fohh conn incarnation */
                                           zfcs_mt->tag_id,                                /* Tag id */
                                           zfcs_mt->hostname,                              /* application name */
                                           zfcs_mt->ip_proto,                              /* ip_proto */
                                           zfcs_mt->port,                                  /* port_he */
                                           0,                                              /* double_encrypt */
                                           0,                                              /* zpn_probe_type */
                                           0,
                                           zfcs_mt->app_type,
                                           0,
                                           zfcs_mt->app_gid,
                                           NULL,
                                           NULL);
        if (res) {
            ZPN_LOG(AL_NOTICE, "%s: Failed to send out mtunnel request", ZFC_DBG(zfcs->zfc));
        }

        ZPN_DEBUG_MTUNNEL("%s: XXX, create mt = %p", ZFC_DBG(zfcs->zfc), zfcs_mt);

        return zfcs_mt;
    }

    return NULL;
}

int zpn_fohh_client_slogger_mt_destroy(struct zpn_fohh_client_slogger_mt *zfcs_mt, char *err)
{
    int res = ZPN_RESULT_NO_ERROR;

    ZPN_DEBUG_MTUNNEL("%p: Terminating slogger mt %s", zfcs_mt, zfcs_mt->mtunnel_id?zfcs_mt->mtunnel_id:"");

    ZPATH_MUTEX_LOCK(&(zfcs_mt->lock), __FILE__, __LINE__);

    res = zpn_mconn_terminate(&(zfcs_mt->broker_mconn.mconn), 1, 0, err, NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Terminating slogger mconn returned %s", zpn_result_string(res));
    }

    zpn_fohh_client_slogger_schedule_mt_terminate(zfcs_mt->state, zfcs_mt);

    if (zfcs_mt->mtunnel_id) {
        ZPN_FREE(zfcs_mt->mtunnel_id);
        zfcs_mt->mtunnel_id = NULL;
    }

    if (zfcs_mt->err) {
        ZPN_FREE(zfcs_mt->err);
        zfcs_mt->err = NULL;
    }

    if (zfcs_mt->hostname) {
        ZPN_FREE(zfcs_mt->hostname);
        zfcs_mt->hostname = NULL;
    }

    ZPATH_MUTEX_UNLOCK(&(zfcs_mt->lock), __FILE__, __LINE__);

    zpn_fohh_client_slogger_mt_reap(zfcs_mt);
    return res;
}

void zpn_fohh_client_slogger_get_mconn(struct zpn_fohh_client_slogger_mt *zfcs_mt, char *desc, size_t desc_len)
{
    char *s = desc;
    char *e = s + desc_len;
    if (!zfcs_mt) {
        snprintf(desc, desc_len, "No Mtunnel");
        return;
    }
    if (zfcs_mt->mtunnel_id) {
        s += sxprintf(s, e, "mt=%s:%d", zfcs_mt->mtunnel_id, zfcs_mt->tag_id);
    } else {
        s += sxprintf(s, e, "mt=none:%d", zfcs_mt->tag_id);
    }
}

int zfcs_mt_consume(struct zpn_fohh_client_slogger_mt *mt, struct evbuffer *buf, size_t len, int64_t incarnation)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (buf && mt) {
        res = zpn_mconn_fohh_client_slogger_receive(&(mt->slogger_mconn), buf, len);
    }

    return res;
}

int zfcs_mt_unblock(struct zpn_fohh_client_slogger_mt *mt)
{
    /* Global owner can accept data again */
    mt->slogger_mconn.tx_paused = 0;
    zpn_mconn_resume_client(&(mt->broker_mconn.mconn), 1);
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_slogger_mt_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(free_q.mt_list));
    TAILQ_INIT(&(free_q.reap_mt_list));
}

static void zpn_fohh_client_slogger_mt_reap(struct zpn_fohh_client_slogger_mt *mt)
{
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(free_q.reap_mt_list), mt, free_q_entry);
    free_q.stats.reap_queue_count++;
    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

static struct zpn_fohh_client_slogger_mt *zpn_fohh_client_slogger_mt_allocate(void)
{
    struct zpn_fohh_client_slogger_mt *mt = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    if ((mt = TAILQ_FIRST(&(free_q.mt_list)))) {
        TAILQ_REMOVE(&(free_q.mt_list), mt, free_q_entry);
        free_q.stats.free_queue_count--;
        incarnation = mt->incarnation;
        incarnation++;
        memset(mt, 0, sizeof(struct zpn_fohh_client_slogger_mt));
        mt->incarnation = incarnation;
        ZPN_DEBUG_MTUNNEL("%p: RE-allocating slogger mt", mt);
    } else {
        mt = (struct zpn_fohh_client_slogger_mt *)ZPN_CALLOC(sizeof(struct zpn_fohh_client_slogger_mt));
        if (mt) {
            free_q.stats.allocations++;
            memset(mt, 0, sizeof(struct zpn_fohh_client_slogger_mt));
            mt->incarnation = 1;
            ZPN_DEBUG_MTUNNEL("%p: allocating slogger mt", mt);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);

    return mt;
}
