/*
 * zpn_broker_pbroker_stats.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 * Broker stats connection related stuff.
 */

#include <openssl/rand.h>
#include "base64/base64.h"
#include "fohh/fohh.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_debug.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_private.h"
#include "zpath_lib/zpath_et_service_endpoint.h"

#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_broker_pbroker_stats.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_lib.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_stats.h"


LIST_HEAD(stats_pbroker_head, stats_pbroker);

/* Stats connection connected private broker */
struct stats_pbroker {
    int64_t pb_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    int64_t g_pb_grp;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    struct event *timer;

    LIST_ENTRY(stats_pbroker) list_entry;
    int in_list;

    uint64_t rx_stats_upload;
    uint64_t monitor_count;
};

/* Structure to keep track of all private brokers connected to us through stats connections. */
struct stats_connected_pbrokers {
    struct zpath_mutex lock;
    struct stats_pbroker_head pbroker_list;
};

static struct stats_connected_pbrokers pbrokers_stats;

static int
zpn_broker_pbroker_stats_upload_to_zi_endpoint(struct argo_object *object, int64_t customer_gid)
{
    struct argo_object *obj_copy;

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PBROKER_STATS("Rx: %s", dump);
        }
    }

    if (zpn_broker_is_pbroker_zistats_upload_disabled_for_customer(customer_gid)) {
        ZPN_DEBUG_PRIVATE_BROKER("Received stats from private broker but zistats upload is disabled for customer %ld", (long)customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    obj_copy = argo_object_copy(object);
    argo_log_log_object_immediate(zpath_stats_collection, obj_copy);
    argo_object_release(obj_copy);

    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_broker_pbroker_stats_upload_to_ci_endpoint(struct argo_object    *object,
                                                 int64_t               customer_gid,
                                                 char                  *tunnel_id,
                                                 int64_t               pb_gid,
                                                 int64_t               pb_grp_gid,
                                                 int                   check_for_site)
{
    int res;
    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because customer_id was not configured");
        return ZPN_RESULT_ERR;
    }

    struct argo_log *log = object->base_structure_void;
    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    res = zpn_broker_pbroker_stats_upload_to_zi_endpoint(object, customer_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while uploading stats to zi endpoint for customer id: %ld, error: %s", (long) customer_gid, zpath_result_string(res));
    }

    enum zpath_customer_log_type log_type;
    if (0 == strncmp("pbroker_stats_comprehensive", log->l_name, sizeof("pbroker_stats_comprehensive"))) {
        log_type = zpath_customer_log_type_pb_comprehensive_stats;
        /* Stream comprehensive stats log to LSS */
        zpn_broker_siem_pbroker_comprehensive_stats(log->l_obj->base_structure_void,
                                                customer_gid,
                                                tunnel_id,
                                                pb_gid,
                                                pb_grp_gid,
                                                check_for_site);
    } else {
        log_type = zpath_customer_log_type_ci_stats;
    }

    /* We're sending comprehensive stats to a separate kafka topic */
    return zpath_service_endpoint_log_struct_config(customer_gid,
                                                    log->l_name,
                                                    log_type,
                                                    argo_get_object_description(log->l_obj),
                                                    log->l_inst,
                                                    pb_gid,
                                                    log->l_role,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    log->l_obj->base_structure_void); // internal calls creates the log object and captures this data and manages it
}


int zpn_broker_pbroker_stats_send_event_log_to_kafka(struct argo_object *object,
                                                     int64_t customer_gid,
                                                     int64_t pb_gid)
{
    struct argo_log *log = object->base_structure_void;
    struct zpn_event_stats *zpn_event_stats_data;
    int res;

    if (customer_gid == 0) {
        ZPN_LOG(AL_ERROR, "Could not send zpn_event log to  kafka pipeline because customer gid found NULL");
        return ZPN_RESULT_ERR;
    }

    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    res = zpath_service_endpoint_log_struct_config(customer_gid,
                                                   log->l_name,
                                                   zpath_customer_log_type_zpn_event,
                                                   argo_get_object_description(log->l_obj),
                                                   log->l_inst,
                                                   pb_gid,
                                                   log->l_role,
                                                   log->l_prio,
                                                   log->l_disp,
                                                   log->l_sys,
                                                   log->l_ctg,
                                                   log->l_obj->base_structure_void);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not forward zpn_event log to producer sent by %s: %s", log->l_inst, zpn_result_string(res));
        return res;
    }

    zpn_event_stats_data = zpn_event_get_stats_data_obj();
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_forwarded), 1);
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_total), 1);

    return ZPN_RESULT_NO_ERROR;
}

int
zpn_broker_pbroker_stats_upload(struct fohh_connection *f_conn,
                                  struct argo_object     *object,
                                  int64_t                customer_gid,
                                  char                   *tunnel_id,
                                  int64_t                pb_gid,
                                  int64_t                pb_grp_gid,
                                  int                    check_for_site)
{
    struct argo_log             *log = object->base_structure_void;
    int redirect_to_ci_endpoint;

    redirect_to_ci_endpoint = 0;
    if (0 == strncmp("pbroker_stats_", log->l_name, strlen("pbroker_stats_"))) {
        if (!customer_gid) {
            ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because customer gid found NULL, tunnel ID: %s", tunnel_id);
            return zpn_broker_pbroker_stats_upload_to_zi_endpoint(object, customer_gid);
        }

        if ((0 == strncmp("pbroker_stats_comprehensive", log->l_name, sizeof("pbroker_stats_comprehensive"))) ||
           (0 == strncmp("pbroker_stats_admin_probe", log->l_name, sizeof("pbroker_stats_admin_probe"))) ||
           (0 == strncmp("pbroker_stats_file_fetch", log->l_name, sizeof("pbroker_stats_file_fetch"))) ||
           (0 == strncmp("pbroker_stats_zdx_mtr", log->l_name, sizeof("pbroker_stats_zdx_mtr"))) ||
           (0 == strncmp("pbroker_stats_system_memory", log->l_name, sizeof("pbroker_stats_system_memory"))) ||
           (0 == strncmp("pbroker_stats_zdx_probes", log->l_name, sizeof("pbroker_stats_zdx_probes"))) ||
           (0 == strncmp("pbroker_stats_zdx_cache", log->l_name, sizeof("pbroker_stats_zdx_cache"))) ||
           (0 == strncmp("pbroker_stats_pbclient_debug", log->l_name, sizeof("pbroker_stats_pbclient_debug"))) ||
           (0 == strncmp("pbroker_stats_resiliency", log->l_name, sizeof("pbroker_stats_resiliency"))) ||
           (0 == strncmp("pbroker_stats_dns_dispatcher", log->l_name, sizeof("pbroker_stats_dns_dispatcher"))) ||
           (0 == strncmp("pbroker_stats_svcp", log->l_name, sizeof("pbroker_stats_svcp")))) {
            redirect_to_ci_endpoint = 1;
        } else {
            redirect_to_ci_endpoint = 0;
        }

        if (redirect_to_ci_endpoint) {
            return zpn_broker_pbroker_stats_upload_to_ci_endpoint(object,
                                                                    customer_gid,
                                                                    tunnel_id,
                                                                    pb_gid,
                                                                    pb_grp_gid,
                                                                    check_for_site);
        } else {
            return zpn_broker_pbroker_stats_upload_to_zi_endpoint(object, customer_gid);
        }
    }

    if ((0 == strncmp("zthread_rusage", log->l_otyp, strlen("zthread_rusage"))) ||
        (0 == strncmp("app_thread_stats", log->l_otyp, strlen("app_thread_stats"))) ||
        (0 == strncmp("zpe_policy_build_stats", log->l_otyp, strlen("zpe_policy_build_stats"))) ||
        (0 == strncmp("zpn_application_search_stats", log->l_otyp, strlen("zpn_application_search_stats"))) ||
        (0 == strncmp("zpe_policy_config_change_stats", log->l_otyp, strlen("zpe_policy_config_change_stats"))) ||
        (0 == strncmp("zpe_policy_evaluate_stats", log->l_otyp, strlen("zpe_policy_evaluate_stats"))) ||
        (0 == strncmp("zpn_c2c_fqdn_bypass_cache_stats", log->l_otyp, strlen("zpn_c2c_fqdn_bypass_cache_stats"))) ||
        (0 == strncmp("zpn_broker_mtunnel_type_stats", log->l_otyp, strlen("zpn_broker_mtunnel_type_stats"))) ||
        (0 == strncmp("zpn_broker_client_stats", log->l_otyp, strlen("zpn_broker_client_stats"))) ||
        (0 == strncmp("zpn_user_risk_stats", log->l_otyp, strlen("zpn_user_risk_stats"))) ||
        (0 == strncmp("zpn_aae_profile_conclusion_stats", log->l_otyp, strlen("zpn_aae_profile_conclusion_stats"))) ||
        (0 == strncmp("zpn_workload_tag_group_stats", log->l_otyp, strlen("zpn_workload_tag_group_stats"))) ||
        (0 == strncmp("zpn_broker_client_app_re_download_stats",log->l_otyp, strlen("zpn_broker_client_app_re_download_stats"))) ||
        (0 == strncmp("fohh_tracker_stats", log->l_otyp, strlen("fohh_tracker_stats"))) ||
        (0 == strncmp("wally_table_stats", log->l_otyp, strlen("wally_table_stats")))) {
        return zpn_broker_pbroker_stats_upload_to_ci_endpoint(object,
                                                              customer_gid,
                                                              tunnel_id,
                                                              pb_gid,
                                                              pb_grp_gid,
                                                              check_for_site);
    }

    /*
     * PSE sends zpn_event log over stats connection. Identify it and forward it to producer
     * on a separate kafka topic zpn_event.
     */
    if (0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) {
        return zpn_broker_pbroker_stats_send_event_log_to_kafka(object,
                                                                customer_gid,
                                                                pb_gid);
    }

    /* Receive zpn_event stats and send it to zi endpoint. */
    if ((0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) ||
        (0 == strncmp("zpath_debug_memory_allocator_stats", log->l_otyp, strlen("zpath_debug_memory_allocator_stats")))) {
        return zpn_broker_pbroker_stats_upload_to_zi_endpoint(object, customer_gid);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Stats connection interface */
int
zpn_broker_pbroker_stats_log_upload_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct fohh_connection      *f_conn = argo_structure_cookie_ptr;
    struct stats_pbroker        *pbroker;

    pbroker = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pbroker) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL for connection %s", fohh_description(f_conn));
        return zpn_broker_pbroker_stats_upload(f_conn, object, 0, NULL, 0, 0, 0);
    }
    pbroker->rx_stats_upload++;
    return zpn_broker_pbroker_stats_upload(f_conn,
                                            object,
                                            pbroker->customer_gid,
                                            pbroker->tunnel_id,
                                            pbroker->pb_gid_from_config,
                                            pbroker->g_pb_grp,
                                            0);
}

static int zpn_broker_pbroker_stats_conn_redirect(struct fohh_connection *f_conn)
{
    const int redirect_pbrokers = zpn_broker_pbroker_get_redirect_pbrokers_flag();
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_pbrokers, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct stats_pbroker *pbroker = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), pbroker->tunnel_id);

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    const int res = zpn_broker_pbroker_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    const int64_t peer_gid = fohh_peer_get_id(f_conn);
    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(peer_gid),
                                     ZPATH_GID_GET_CUSTOMER_GID(peer_gid),
                                     &peer_ip, &peer_site,
                                     pbroker->tunnel_id,
                                     redirect_pbrokers,
                                     zpn_broker_pbroker_get_redirect_pbrokers_reason(),
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_private_broker,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_stats_alt_cloud_redirects), 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

void zpn_broker_pbroker_stats_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_pbroker_stats_conn_redirect(f_conn);
}

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL_S seconds */
static void zpn_broker_pbroker_stats_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;
    struct stats_pbroker *pbroker = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pbroker) {
        ZPN_LOG(AL_NOTICE, "%s: Private broker stats conn monitor cb has no cookie", fohh_description(f_conn));
        return;
    }

    pbroker->monitor_count++;

    /* Drop connection when broker is shutting down. */
    if (zpn_broker_pbroker_get_disconnect_pbrokers_flag()) {
        const char *disconnect_reason = zpn_broker_pbroker_get_disconnect_pbrokers_reason();
        ZPN_LOG(AL_INFO, "%s: Disconnect pbroker stats connection due to %s",
                fohh_description(f_conn), disconnect_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        fohh_connection_delete(f_conn, disconnect_reason);
        return;
    }

    /* Drop connection when customer is disabled */
    if (pbroker->customer_gid != 0) {
        res = zpath_customer_get(pbroker->customer_gid,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), pbroker->customer_gid);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (zpn_broker_pbroker_get_redirect_pbrokers_flag() &&
        (pbroker->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0) {
        const char *redirect_reason = zpn_broker_pbroker_get_redirect_pbrokers_reason();
        ZPN_LOG(AL_INFO, "%s: Redirect pbroker stats connection due to %s",
                fohh_description(f_conn), redirect_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_pbroker_stats_conn_redirect(pbroker->f_conn);
    }
}

static void zpn_broker_pbroker_stats_destroy(struct stats_pbroker *pbroker/*!=NULL*/)
{
    struct fohh_connection *f_conn = pbroker->f_conn;

    if (f_conn) {
        ZPN_LOG(AL_NOTICE, "%s: Private broker stats connection DOWN, Private broker ID = %"PRId64,
                fohh_description(f_conn),
                pbroker->pb_gid_from_config);
    } else {
        ZPN_LOG(AL_NOTICE, "Stale private broker stats connection DOWN, Private broker ID = %"PRId64,
                pbroker->pb_gid_from_config);
    }

    if (pbroker->timer) {
        event_del(pbroker->timer);
        event_free(pbroker->timer);
    }

    /* Remove pbroker from connected proker_list */
    ZPATH_MUTEX_LOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);
    if (pbroker->in_list) {
        LIST_REMOVE(pbroker, list_entry);
        pbroker->in_list = 0;
    }
    ZPATH_MUTEX_UNLOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

    ZPN_FREE(pbroker);
}


int
zpn_broker_pbroker_stats_conn_callback(struct fohh_connection *connection,
                                        enum fohh_connection_state state,
                                        void *cookie)
{
    struct argo_state *argo;
    struct stats_pbroker *pbroker;
    int64_t pb_id;
    int res;
    int64_t customer_gid;
    struct zpath_customer *customer = NULL;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%s: Private broker stats connection is UP", fohh_description(connection));
        /* Allocate state for this connection, so we can track its stuff... */

        pb_id = fohh_peer_get_id(connection);
        if (!pb_id) {
            ZPN_LOG(AL_ERROR, "Private broker ID not found for connection %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        ZPN_DEBUG_PRIVATE_BROKER("%s: Broker received pb stats connection, Private broker ID = %"PRId64,
                            fohh_description(connection), pb_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);
        argo = fohh_argo_get_rx(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(pb_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), pb_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        /* Register private broker log upload. Privare broker's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_pbroker_stats_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register private broker log upload for connection %s", fohh_description(connection));
            return res;
        }

        pbroker = ZPN_CALLOC(sizeof(*pbroker));

        /* Generate an ID for this client */
        res = RAND_bytes(&(pbroker->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(pbroker);
            return res;
        }
        base64_encode_binary(pbroker->tunnel_id, pbroker->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        pbroker->in_list = 0;

        pbroker->pb_gid_from_config = pb_id;
        pbroker->f_conn = connection;
        pbroker->f_conn_incarnation = fohh_connection_incarnation(connection);

        pbroker->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_broker_pbroker_stats_conn_monitor_cb,
                                    connection);

        if (!pbroker->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(pbroker);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(pbroker->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add pbroker timer");
            event_free(pbroker->timer);
            ZPN_FREE(pbroker);
            return FOHH_RESULT_NO_MEMORY;
        }

        fohh_connection_set_dynamic_cookie(connection, pbroker);

        {
            // Get customer_gid & pb group gid for the pbroker
            struct zpn_private_broker_to_group *pb_grp = NULL;

            res = zpn_pbroker_to_group_get_by_private_broker_gid(pb_id, &pb_grp, 0, NULL, NULL, 0);
            if (pb_grp) {
                // We will always succeed here since this has been called during verification before connection is up
                pbroker->customer_gid = pb_grp->customer_gid;
                pbroker->g_pb_grp = pb_grp->private_broker_group_gid;
            } else {
                ZPN_LOG(AL_NOTICE, "Cannot find the pbroker with Private broker ID = %"PRId64, pb_id);
            }
        }

        /* Add pbroker to connected pbroker_list */
        ZPATH_MUTEX_LOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);
        LIST_INSERT_HEAD(&(pbrokers_stats.pbroker_list), pbroker, list_entry);
        pbroker->in_list = 1;
        ZPATH_MUTEX_UNLOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_stats(fohh_connection_get_thread_id(connection));

        ZPN_LOG(AL_NOTICE, "%s: Private broker stats connection UP, Private broker ID = %"PRId64,
                fohh_description(connection), pb_id);
    } else {
        if (connection->state == fohh_connection_connected) {
        	/* Connection probably went away... */
        	const char *reason = fohh_close_reason(connection);
        	ZPN_LOG(AL_NOTICE, "%s: Private broker stats connection DOWN: %s", fohh_description(connection), reason);

        	pbroker = fohh_connection_get_dynamic_cookie(connection);
        	if (pbroker) {
            	zpn_broker_pbroker_stats_destroy(pbroker);
            	fohh_connection_set_dynamic_cookie(connection, NULL);
            	zpn_fohh_worker_pbroker_disconnect_stats(fohh_connection_get_thread_id(connection));
            }
        }
    }
    return FOHH_RESULT_NO_ERROR;
}

int
zpn_broker_pbroker_stats_info(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct stats_pbroker* pb;
    struct stats_pbroker* tmp;
    uint64_t total_stats_connections = 0;
    uint64_t total_rx_stats_upload = 0;

    ZPATH_MUTEX_LOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

    LIST_FOREACH_SAFE(pb, &(pbrokers_stats.pbroker_list), list_entry, tmp) {
        total_stats_connections++;
        total_rx_stats_upload += pb->rx_stats_upload;
    }

    ZPATH_MUTEX_UNLOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

    ZDP("total number of stats connections: %"PRId64"\n", total_stats_connections);
    ZDP("total received stats upload: %"PRId64"\n", total_rx_stats_upload);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_broker_pbroker_stats_dump_all(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct stats_pbroker* pb;
    struct stats_pbroker* tmp;

    ZDP(" -- stats connections view -- \n");
    ZPATH_MUTEX_LOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

    LIST_FOREACH_SAFE(pb, &(pbrokers_stats.pbroker_list), list_entry, tmp) {
        ZDP("pb_gid_from_config                  : %"PRId64"\n", pb->pb_gid_from_config);
        ZDP("g_pb_grp                            : %"PRId64"\n", pb->g_pb_grp);
        ZDP("pbroker_customer_gid                : %"PRIu64"\n", (uint64_t)ZPATH_GID_GET_CUSTOMER_GID(pb->pb_gid_from_config));
        ZDP("f_conn                              : %s\n", fohh_description(pb->f_conn));
        ZDP("rx_log_stats_upload                 : %"PRId64"\n", pb->rx_stats_upload);

        ZDP("\n");
    }

    ZPATH_MUTEX_UNLOCK(&(pbrokers_stats.lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}


int zpn_broker_pbroker_stats_init()
{
    int res;

    pbrokers_stats.lock = ZPATH_MUTEX_INIT;

    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to private brokers",
                                  "/broker/pbroker/stats/info",
                                  zpn_broker_pbroker_stats_info,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while adding a debug command /broker/pbroker/stats/info, error: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to private brokers",
                                  "/broker/pbroker/stats/dump",
                                  zpn_broker_pbroker_stats_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while adding a debug command /broker/pbroker/stats/dump, error: %s", zpath_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}
