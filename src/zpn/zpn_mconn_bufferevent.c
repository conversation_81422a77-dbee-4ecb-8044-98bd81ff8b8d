/*
 * zpn_mconn_bufferevent.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include "zlibevent/zlibevent_bufferevent.h"
#include <netinet/in.h>
#include <netinet/tcp.h>

#include <openssl/err.h>

#include "zpn/zpn_mconn_bufferevent.h"
#include "zpn/zpn_lib.h"
#include "zpath_lib/zpath_config_override_keys.h"

int64_t zpn_mconn_inner_tunnel_window_update_status = 0;

int64_t zpn_allocator_libevent_out_queue_allowed_bytes = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT;

/*
 * Locking: it is assumed mtunnel is not locked at this point
 */
static void zpn_mconn_bufferevent_synthetic_response_internal(struct zevent_base *base,
                                                              void *void_cookie,
                                                              int64_t int_cookie,
                                                              void *extra_cookie1,
                                                              void *extra_cookie2,
                                                              void *extra_cookie3,
                                                              int64_t extra_int_cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = void_cookie;
    struct bufferevent *bev = mconn_b->bev;
    struct evbuffer *buf = extra_cookie1;
    size_t len = 0;
    int incarnation = int_cookie;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn *mconn;

    __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);

    if (!bev) {
//        ZPN_DEBUG_MCONN("%p: Already closed bev", mconn_b);
//        evbuffer_free(buf);
//        return;
        ZPN_DEBUG_MCONN("mconn_bufferevent_synthetic_response_internal no bev for end terminated mconn_b %p", mconn_b);
    }

    mconn = &(mconn_b->mconn);
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        if (buf) evbuffer_free(buf);
        return;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        if (buf) evbuffer_free(buf);
        return;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               incarnation)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        if (buf) evbuffer_free(buf);
        return;
    }

    //ZPN_LOG(AL_DEBUG, "Received Data: len = %ld", (long) len);

    len = evbuffer_get_length(buf);
    if (len) {
        res = zpn_client_process_rx_data(&(mconn_b->mconn), buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

    if (mconn_b->mconn.peer && mconn_b->rx_fin_pending && !evbuffer_get_length(buf)) {
        ZPN_DEBUG_MCONN("Send out pending rx FIN after drained RX data");
        zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
        mconn_b->rx_fin_pending = 0;
    }

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    if (buf) evbuffer_free(buf);
}

/*
 * Locking: it is assumed mtunnel is not locked at this point
 */
static void zpn_mconn_bufferevent_synthetic_request_internal(struct zevent_base *base,
                                                             void *void_cookie,
                                                             int64_t int_cookie,
                                                             void *extra_cookie1,
                                                             void *extra_cookie2,
                                                             void *extra_cookie3,
                                                             int64_t extra_int_cookie) {
    struct zpn_mconn_bufferevent *mconn_b = void_cookie;
    struct bufferevent *bev = mconn_b->bev;
    struct evbuffer *buf = extra_cookie1;
    size_t len = 0;
    int64_t incarnation = int_cookie;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn *mconn;

    __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);

    if (!bev) {
        ZPN_DEBUG_MCONN("%p: Already closed bev", mconn_b);
        if (buf) evbuffer_free(buf);
        return;
    }

    mconn = &(mconn_b->mconn);
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        if (buf) evbuffer_free(buf);
        return;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        if (buf) evbuffer_free(buf);
        return;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               incarnation)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        if (buf) evbuffer_free(buf);
        return;
    }

    //ZPN_LOG(AL_DEBUG, "Received Data: len = %ld", (long) len);

    len = evbuffer_get_length(buf);
    if (len) {
        res = zpn_mconn_send_data_to_client(&mconn_b->mconn, buf, evbuffer_get_length(buf), 0, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

    if (mconn_b->mconn.peer && mconn_b->rx_fin_pending && !evbuffer_get_length(buf)) {
        ZPN_DEBUG_MCONN("Send out pending rx FIN after drained RX data");
        zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
        mconn_b->rx_fin_pending = 0;
    }

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    if (buf) evbuffer_free(buf);
}

/*
 *
 * This API will inject the input evbuffer data into the zpn_mconn_bufferevent asynchronously
 * as inject when the corresponding fohh thread is ready.
 *
 * arguments:
 *       *mconn_b : the bufferevent mconn caller wants to inject data to.
 *       *buf     : the data in evbuffer structure format. this will be allocated by the caller.
 *
 * Note: this API will take care of freeing the *buf on success/failure cases.
 *
 */
void zpn_mconn_bufferevent_synthetic_response(struct zpn_mconn_bufferevent *mconn_b, struct evbuffer* buf)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = 0;

    if (mconn_b->rx_paused) {
        return;
    }

    value = __sync_add_and_fetch_4(&(mconn_b->async_rx_count), 1);
    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
        return;
    }

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call_big_zevent(mconn_b->mconn.fohh_thread_id,
                                    zpn_mconn_bufferevent_synthetic_response_internal,
                                    mconn_b,
                                    incarnation,
                                    buf,
                                    NULL) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for async_resume_receive!");
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
    }
}

/*
 *
 * This API will inject the input evbuffer data into the zpn_mconn_bufferevent asynchronously
 * as inject when the corresponding fohh thread is ready.
 *
 * arguments:
 *       *mconn_b : the bufferevent mconn caller wants to inject data to.
 *       *buf     : the data in evbuffer structure format. this will be allocated by the caller.
 *
 * Note: this API will take care of freeing the *buf on success/failure cases.
 *
 */
void zpn_mconn_bufferevent_synthetic_request(struct zpn_mconn_bufferevent *mconn_b, struct evbuffer* buf)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = 0;

    if (mconn_b->tx_paused) {
        if (buf) evbuffer_free(buf);
        return;
    }

    value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);
    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
        if(buf) evbuffer_free(buf);
        return;
    }

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call_big_zevent(mconn_b->mconn.fohh_thread_id,
                                    zpn_mconn_bufferevent_synthetic_request_internal,
                                    mconn_b,
                                    incarnation,
                                    buf,
                                    NULL) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for async_resume_receive!");
        if(buf) evbuffer_free(buf);
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
    }
}
#ifdef __linux__
void
zpn_mconn_bufferevent_set_quickack(struct zpn_mconn_bufferevent *buffevent_mconn) {
    if (!buffevent_mconn) {
        ZPN_DEBUG_MCONN("Mconn null in set quickack");
        return;
    }
    buffevent_mconn->quickack = 1;
}

int
zpn_mconn_bufferevent_get_quickack(struct zpn_mconn_bufferevent *buffevent_mconn) {
    if (!buffevent_mconn) {
        ZPN_DEBUG_MCONN("Mconn null in get quickack");
        return 0;
    }
    return buffevent_mconn->quickack;
}
#endif
/*
 * Locking: it is assumed mtunnel is not locked at this point
 */
static void mconn_bufferevent_receive(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;
    struct bufferevent *bev = mconn_b->bev;
    struct evbuffer *buf;
    size_t len;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_mconn *mconn;

    __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);

    if (!bev) {
        ZPN_DEBUG_MCONN("%p: Already closed bev", mconn_b);
        return;
    }

#ifdef __linux__
    int sock = bufferevent_getfd(bev);
    if (zpn_mconn_bufferevent_get_quickack(mconn_b)) {
            int tmp_val = 1;
            ZPN_DEBUG_MCONN("Set quickack for server in mconn %p %s\n", &(mconn_b->mconn), zpn_mconn_type_str(mconn_b->mconn.type));
            setsockopt(sock, IPPROTO_TCP, TCP_QUICKACK, (void *)&tmp_val, sizeof(tmp_val));
    }
#endif

    mconn = &(mconn_b->mconn);
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               int_cookie)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return;
    }

    //if (mconn_b->rx_paused) {
    //    goto exit;
    //}

    buf = bufferevent_get_input(bev);
    len = evbuffer_get_length(buf);

    //ZPN_LOG(AL_DEBUG, "Received Data: len = %ld", (long) len);

    if (len) {
        res = zpn_client_process_rx_data(&(mconn_b->mconn), buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

    if (mconn_b->mconn.peer && mconn_b->rx_fin_pending && !evbuffer_get_length(buf)) {
        ZPN_DEBUG_MCONN("Send out pending rx FIN after drained RX data");
        zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
        mconn_b->rx_fin_pending = 0;
    }

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }
}

static void bev_read_cb(struct bufferevent *bev, void *cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = 0;

    if (mconn_b->rx_paused) {
        return;
    }

    value = __sync_add_and_fetch_4(&(mconn_b->async_rx_count), 1);
    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
        return;
    }

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         mconn_bufferevent_receive,
                         mconn_b,
                         incarnation) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for async_resume_receive!");
        __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
    }
}

static int mconn_bufferevent_transmit(struct zpn_mconn_bufferevent *mconn_b, int need_lock, int64_t int_cookie)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int res = ZPN_RESULT_NO_ERROR;
    int enq_len = 0;
    int dropped_len = 0;

    if (mconn->global_owner) {
        if (need_lock && mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        } else if (need_lock) {
            return res;
        }
    } else {
        return res;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return res;
    }


    if (!mconn_b->bev) {
        /* Already closed? */
        goto exit;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               int_cookie)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        goto exit;
    }

    if (zpn_mconn_transmit_buffer_exists(mconn)) {
        if (!mconn_b->bev || mconn->fin_sent || mconn->drop_tx) {
            /* Already closed? Drop all the data */
            zpn_mconn_drain_transmit_buffer(mconn, &dropped_len);
            if (dropped_len > 0) {
                ZPN_DEBUG_MCONN("%p dropped data %d bytes due to mconn->bev present: %d, fin_sent: %d, drop_tx: %d", mconn, dropped_len, mconn_b->bev?1:0, mconn->fin_sent, mconn->drop_tx);
            }
        } else {
            /*
             * Send out the data. Make sure we don't enqueue more than ZPN_MCONN_MAX_CLIENT_TX_DATA bytes of data
             * into the output buffer of bev
             */
            //  adjust output_available_space_len dynamically based on
            size_t input_len = zpn_mconn_get_transmit_buffer_len(mconn);
            size_t output_existing_buffered_data_len = evbuffer_get_length(bufferevent_get_output(mconn_b->bev));

            int64_t mconn_tcp_max_outstanding_bytes_in_write_buff = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT;
            if (mconn->allocator_libevent_out_queue_is_enabled) {
                // note similar optimization could be done in zpn_mconn_bufferentpair.c extending scope of this feature for double encrypt, waf related features,
                // omitted at this time to limit scope with gradual changes
                mconn_tcp_max_outstanding_bytes_in_write_buff = __atomic_load_n(&zpn_allocator_libevent_out_queue_allowed_bytes, __ATOMIC_RELAXED);
            }
            ssize_t output_available_space_len = mconn_tcp_max_outstanding_bytes_in_write_buff - output_existing_buffered_data_len;
            ssize_t output_len = (input_len < output_available_space_len) ? input_len : output_available_space_len;
            if (output_len <= 0) {
                ZPN_DEBUG_MCONN("%p: throttled zpn_mconn_send_transmit_buffer data from client:%lu, output queue length:%lu, output available space:%ld, output_len:%ld, max output queue length:%"PRId64", is libevent q:%d",
                    mconn_b, input_len, output_existing_buffered_data_len, (long) output_available_space_len, (long) output_len, mconn_tcp_max_outstanding_bytes_in_write_buff,
                    mconn->allocator_libevent_out_queue_is_enabled);
                goto done_sending_data_to_server;
            }

            ZPN_DEBUG_MCONN("%p: before zpn_mconn_send_transmit_buffer data from client:%lu, output queue length:%lu, output available space:%ld, output_len:%ld, max output queue length:%"PRId64", is libevent q:%d",
                            mconn_b, input_len, output_existing_buffered_data_len, (long) output_available_space_len, (long) output_len, mconn_tcp_max_outstanding_bytes_in_write_buff,
                            mconn->allocator_libevent_out_queue_is_enabled);

            enq_len = zpn_mconn_send_transmit_buffer(mconn, bufferevent_get_output(mconn_b->bev), output_len);

            mconn_b->mconn.bytes_to_client += enq_len;

            zpn_mconn_track_perf_egress(mconn);

done_sending_data_to_server:
            if (zpn_mconn_get_transmit_buffer_len(mconn)) {
                /* We didn't send out everything */
                mconn_b->tx_paused = 1;
                mconn_b->pause_count++;
                mconn_b->last_pause_time_s = epoch_s();
                mconn_b->last_pause_time_monotime_s = monotime_s();
                res = ZPN_RESULT_WOULD_BLOCK;
            }
        }
    }

    /*
     * The peer mconn have receveid FIN and we are done with transmitting all the data,
     * just forward FIN
     */
    if (mconn->client_needs_to_forward && !mconn->fin_sent &&
        (!zpn_mconn_transmit_buffer_exists(mconn) || !zpn_mconn_get_transmit_buffer_len(mconn)) &&
        !evbuffer_get_length((bufferevent_get_output(mconn_b->bev)))) {

        int fd = bufferevent_getfd(mconn_b->bev);

        /* Send FIN */
        ZPN_DEBUG_MCONN("write_cb, %p: sending FIN, sock = %d", mconn_b, fd);
        shutdown(fd, SHUT_WR);
        mconn_b->mconn.fin_sent = 1;
        // unhold the flag to close mtunnel from assistant end
        mconn_b->mconn.hold_forward_mtunnel_end = 0;
        res = ZPN_RESULT_NO_ERROR;
    }

    if (mconn->client_needs_to_disconnect_local_owner && !mconn_b->is_connector_tun) {
        /* We are waiting to close the bufferevent once data is sent */
        ZPN_DEBUG_MCONN("%p: freeing bufferevent", mconn_b);
        zlibevent_bufferevent_free(mconn_b->bev);
        mconn_b->bev = NULL;
        res = ZPN_RESULT_NO_ERROR;
    }

    if (zpn_mconn_inner_tunnel_window_update_status && (mconn->type == mconn_bufferevent_tun_s)) {
        goto exit;
    }

    if (((enq_len > 0) || (dropped_len > 0)) && mconn->peer) {
        ZPN_DEBUG_MCONN("window update, enq_len = %d, dropped_len = %d", enq_len, dropped_len);
        zpn_mconn_client_window_update(mconn->peer, 0, (enq_len + dropped_len), 1);
    } else if(((enq_len > 0) || (dropped_len > 0)) && !mconn->peer) {
        ZPN_DEBUG_MCONN("%p TCP dequeue tx_buffer but not able to update peer enq_len %d, dropped_len %d", mconn, enq_len, dropped_len);
    }

exit:
    if (need_lock && mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static void async_transmit(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;

    __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);

    if (mconn_b->mconn.global_owner_incarnation != int_cookie) {
        ZPN_DEBUG_MCONN("Incarnation changed, our incarnation = %ld, int_cookie = %ld",
                        (long)mconn_b->mconn.global_owner_incarnation, (long)int_cookie);
        return;
    }

    mconn_bufferevent_transmit(mconn_b, 1, int_cookie);
}

static void bev_write_cb(struct bufferevent *bev, void *cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    int64_t incarnation;
    int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);
    int64_t cur_pause_time;

    if (value > 2) {
        /* We already have thread call outstanding, no need to make call */
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
        return;
    }


    if (mconn_b->tx_paused) {
        mconn_b->resume_count++;
        mconn_b->last_resume_time_s = epoch_s();
        cur_pause_time = mconn_b->last_resume_time_s - mconn_b->last_pause_time_s;
        if (cur_pause_time > mconn_b->max_pause_time_s) {
            mconn_b->max_pause_time_s = cur_pause_time;
        }
    }
    mconn_b->tx_paused = 0;

    incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                           mconn->self,
                                                           mconn->global_owner,
                                                           mconn->global_owner_key,
                                                           mconn->global_owner_key_length);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         async_transmit,
                         mconn_b,
                         incarnation) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for bev_write_cb!");
        __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
    }
}

static void mconn_bufferevent_event_process(struct zpn_mconn_bufferevent *mconn_b, short style)
{
    struct zpn_mconn *mconn = &(mconn_b->mconn);
    struct bufferevent *bev = NULL;
    struct evbuffer *buf;
    size_t len;
    int bev_errno_save = EVUTIL_SOCKET_ERROR(); /* Save bev errno */
    int rst_rcvd = 0;

    char *b_reading = "";
    char *b_writing = "";
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";
    char *b_connected = "";

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    ZPN_DEBUG_MCONN("Connection: Received event %s%s%s%s%s%s, %s",
                    b_reading,
                    b_writing,
                    b_eof,
                    b_error,
                    b_timeout,
                    b_connected,
                    zpn_mconn_type_str(mconn->type));

    if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {

        if (mconn->global_owner) {
            if (mconn->global_owner_calls) {
                (mconn->global_owner_calls->lock)(mconn,
                                                  mconn->self,
                                                  mconn->global_owner,
                                                  mconn->global_owner_key,
                                                  mconn->global_owner_key_length);
            } else {
                return;
            }
        } else {
            return;
        }

        if (!mconn->global_owner) {
            /*
             * Global owner might be gone after we grab the lock, so check it again.
             * Bail if that's the case.
             */
            (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
            return;
        }

        bev = mconn_b->bev;
        if (!bev) {
            ZPN_DEBUG_MCONN("No bev, cannot process event");
            (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
            return;
        }

        unsigned long erk;
        erk = bufferevent_get_openssl_error(bev);
        if (erk) {
            char dbg_buf[256];
            dbg_buf[0] = 0;
            ERR_error_string_n(erk, dbg_buf, sizeof(dbg_buf));
            ZPN_DEBUG_MTUNNEL("Connection received TLS error %s", dbg_buf);
            mconn_b->ssl_erk = erk;
        }

        buf = bufferevent_get_input(bev);
        len = evbuffer_get_length(buf);

        if (mconn->global_owner_calls) {

            /* Only log if errno is set */
            if (bev_errno_save > 0) {

                /* Skip logging on: EAGAIN,EINPROGRESS + EOF */
                if ( ! (((bev_errno_save == EAGAIN) || (bev_errno_save == EINPROGRESS)) && (style & BEV_EVENT_EOF)) ) {
                    ZPN_DEBUG_MCONN("%s: Connection received events: %s%s%s%s%s%s, error: %s, queued input len: %d",
                            (mconn->global_owner_calls->id)(mconn,
                                                            mconn->self,
                                                            mconn->global_owner,
                                                            mconn->global_owner_key,
                                                            mconn->global_owner_key_length),
                            b_reading,
                            b_writing,
                            b_eof,
                            b_error,
                            b_timeout,
                            b_connected,
                            evutil_socket_error_to_string(bev_errno_save), (int)len);
                }
            }
        }

        if (len) {
            ZPN_DEBUG_MCONN("Got EOF or ERR but still %d bytes in socket", (int)len);
            mconn_b->rx_fin_pending = 1;
            bev_read_cb(bev, mconn_b);
        }

        mconn->fin_rcvd = 1;
        if (!mconn->fin_rcvd_us) mconn->fin_rcvd_us = epoch_us();

        if (!mconn->peer || (style & (BEV_EVENT_ERROR | BEV_EVENT_WRITING))) {
            ZPN_DEBUG_MCONN("mconn_bufferevent got EOF or ERROR, no peer");
            mconn->drop_tx = 1;
            mconn->fin_sent = 1;
        }

        if((bev_errno_save == ECONNRESET) && (style & BEV_EVENT_ERROR)) {
            ZPN_DEBUG_MCONN("mconn_bufferevent got server RST event");
            rst_rcvd = 1;
        }

        if (!mconn->hold_forward_mtunnel_end) {
            if(mconn->peer && rst_rcvd) {
                ZPN_DEBUG_MCONN("%p: mconn_bufferevent got server RST event "
                                "send mtunnel_end to peer %p drop_tx = %d",
                                 mconn, mconn->peer, mconn->drop_tx);
                zpn_mconn_forward_mtunnel_end(mconn->peer, BRK_MT_RESET_FROM_SERVER, mconn->drop_tx);
            } else if (mconn->peer && (!mconn_b->rx_fin_pending || mconn->drop_tx)) {
                ZPN_DEBUG_MCONN("mconn_bufferevent got EOF or ERROR, forward mtunnel_end, drop_tx = %d", mconn->drop_tx);
                zpn_mconn_forward_mtunnel_end(mconn->peer, MT_CLOSED_TERMINATED, mconn->drop_tx);
            }
        }

#if 0   /* Let the timer to terminate and free mtunnel */
        /* Wipeout.... */
        if (mconn->fin_sent && mconn->global_owner && mconn->global_owner_calls->terminate) {
            (mconn->global_owner_calls->terminate)(mconn,
                                                   mconn->self,
                                                   mconn->global_owner,
                                                   mconn->global_owner_key,
                                                   mconn->global_owner_key_length,
                                                   "Terminated");
        }
#endif

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        }

    } else {
        /* Connection initialized... Not really anything to do here. */
    }
}

static void async_event(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;
    short style = (short)int_cookie;

    __sync_sub_and_fetch_4(&(mconn_b->async_event_count), 1);
    mconn_bufferevent_event_process(mconn_b, style);
}

static void bev_event_cb(struct bufferevent *bev, short style, void *cookie)
{
    struct zpn_mconn_bufferevent *mconn_b = cookie;

    __sync_add_and_fetch_4(&(mconn_b->async_event_count), 1);

    if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                         async_event,
                         mconn_b,
                         style) != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for bev_write_cb!");
        __sync_sub_and_fetch_4(&(mconn_b->async_event_count), 1);
    }
}

static int zpn_mconn_bufferevent_bind_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t *owner_incarnation)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;
    struct evbuffer *buf;
    size_t len;

    mconn_b->bev = owner;

    /* In case there is some data... */
    buf = bufferevent_get_input(mconn_b->bev);
    len = evbuffer_get_length(buf);

    if (len) {
        int res;

        res = zpn_client_process_rx_data(&(mconn_b->mconn), buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Bind cb process_rx data returned %s", zpn_result_string(res));
        }
    }

    bufferevent_setcb(mconn_b->bev, bev_read_cb, bev_write_cb, bev_event_cb, mconn_b);

    /* This enable should invoke a read callback if there is data
     * waiting for us on this bufferevent already */
    bufferevent_enable(mconn_b->bev, EV_READ|EV_WRITE);
    if (flow_control_enabled) {
        /*
         * write:
         *  No watermark is set, as we don't use bufferevent_write routine but manipulate the evbuffer directly. With
         *  this even if we have watermark, it is not effective.
         *
         * read:
         *  allow read callback into itasca even when there is 1 byte of data.
         *  stop reading from the network if the buffer has more than or equal to ZPN_MCONN_MAX_CLIENT_TX_DATA bytes
         */
        bufferevent_setwatermark(mconn_b->bev, EV_READ, 0, ZPN_MCONN_MAX_CLIENT_TX_DATA);
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_mconn_bufferevent_unbind_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           int drop_buffered_data,
                                           int dont_propagate,
                                           const char *err)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;

    if (mconn_b->is_connector_tun) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn_b->bev) {
        //struct evbuffer *out = bufferevent_get_output(mconn_b->bev);

        if (zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn))) {
            ZPN_DEBUG_MCONN("Data hasn't been sent out yet, flushing bytes = %ld", zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn)));
            mconn_b->mconn.client_needs_to_disconnect_local_owner = 1;
        } else {
            zpn_mconn_forward_mtunnel_end(&(mconn_b->mconn), err, drop_buffered_data);
            ZPN_DEBUG_MCONN("%p: freeing bufferevent", mconn_base);
            zlibevent_bufferevent_free(mconn_b->bev);
            mconn_b->bev = NULL;
        }
    } else {
        ZPN_LOG(AL_ERROR, "mconn unbind bufferevent- no bev");
        mconn_b->mconn.fin_sent = 1;
        mconn_b->mconn.fin_rcvd = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}


static void zpn_mconn_bufferevent_lock_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length)
{
}


static void zpn_mconn_bufferevent_unlock_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length)
{
}

/*
 * Locking: it is assumed mtunnel is locked at this point
 */
static int zpn_mconn_bufferevent_transmit_cb(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length,
                                             int64_t owner_incarnation,
                                             int fohh_thread_id,
                                             struct evbuffer *buf,
                                             size_t buf_len)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t now;

    ZPN_DEBUG_MCONN("Send data. Len = %ld, buf_len = %ld", (long) evbuffer_get_length(buf), (long) buf_len);

    /* XXX Implement Me- Backoff on large buffering. */
    if (mconn_b->tx_paused) {
        ZPN_DEBUG_MCONN("zpn_mconn_bufferevent tx paused");
        now = monotime_s();
        if (now >= mconn_b->last_pause_time_monotime_s + mconn_b->max_pause_time_interval) {
            struct zpn_mconn *mconn = &(mconn_b->mconn);
            //delete the mtunnel.
            ZPN_DEBUG_MCONN("terminating mtunnel with reason %s now =%"PRIu64" last_pause_time_s =%"PRIu64"",AST_MT_CONN_TO_SERVER_STUCK, now, mconn_b->last_pause_time_s);
            mconn->global_owner_calls->terminate(mconn,
                                                 mconn->self,
                                                 mconn->global_owner,
                                                 mconn->global_owner_key,
                                                 mconn->global_owner_key_length,
                                                 AST_MT_CONN_TO_SERVER_STUCK);
        }
        res = ZPN_RESULT_WOULD_BLOCK;
    } else {
        struct zpn_mconn *mconn = &(mconn_b->mconn);
        int64_t incarnation;
        int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);

        if (value > 2) {
            /* We already have thread call outstanding, no need to make call */
            __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
        } else {
            incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                   mconn->self,
                                                                   mconn->global_owner,
                                                                   mconn->global_owner_key,
                                                                   mconn->global_owner_key_length);
            if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                                 async_transmit,
                                 mconn_b,
                                 incarnation) != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for bev_write_cb!");
                __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
            }
        }
    }

    return res;
}


static int zpn_mconn_bufferevent_pause_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;

    mconn_b->rx_paused = 1;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_bufferevent_resume_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           int fohh_thread_id)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;

    if (mconn_b && mconn_b->bev) {

        struct zpn_mconn *mconn = &(mconn_b->mconn);
        int64_t incarnation;
        int value = __sync_add_and_fetch_4(&(mconn_b->async_rx_count), 1);

        mconn_b->rx_paused = 0;

        if (value > 2) {
            /* We already have thread call outstanding, no need to make call */
            __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
        } else {

            incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length);

            if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                             mconn_bufferevent_receive,
                             mconn_b,
                             incarnation) != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for async_resume_receive!");
                __sync_sub_and_fetch_4(&(mconn_b->async_rx_count), 1);
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_bufferevent_init(struct zpn_mconn_bufferevent *mconn_bufferevent,
                               void *mconn_self,
                               enum zpn_mconn_type type)
{
    mconn_bufferevent->bev = NULL;
    mconn_bufferevent->rx_paused = 0;
    mconn_bufferevent->tx_paused = 0;
    mconn_bufferevent->rx_bytes = 0;
    mconn_bufferevent->tx_bytes = 0;
    mconn_bufferevent->rx_fin_pending = 0;
    mconn_bufferevent->tx_fin_pending = 0;
    mconn_bufferevent->is_connector_tun = 0;
    mconn_bufferevent->async_tx_count = 0;
    mconn_bufferevent->async_rx_count = 0;
    mconn_bufferevent->ssl_erk = 0;

    return zpn_mconn_init(&(mconn_bufferevent->mconn), mconn_self, type);
}

int zpn_mconn_bufferevent_forward_tunnel_end_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length,
                                                  int64_t owner_incarnation,
                                                  const char *err,
                                                  int32_t drop_data)
{
    struct zpn_mconn_bufferevent *mconn_b = mconn_base;

    if (!mconn_b->bev) {
        ZPN_DEBUG_MCONN("No underline bev");
        mconn_b->mconn.fin_sent = 1;
        return ZPN_RESULT_NO_ERROR;
    }

    if (drop_data) {
        ZPN_DEBUG_MCONN("Need to drop all data");
        if (zpn_mconn_transmit_buffer_exists(&(mconn_b->mconn))) {
            size_t len = zpn_mconn_get_transmit_buffer_len(&(mconn_b->mconn));
            zpn_mconn_free_transmit_buffer(&(mconn_b->mconn));
            if (len && mconn_b->mconn.peer) {
                zpn_mconn_client_window_update(mconn_b->mconn.peer, 0, (int)len, 0);
            }
        }
    }

    if (!mconn_b->mconn.fin_sent) {
        mconn_b->mconn.client_needs_to_forward = 1;
        if (!mconn_b->tx_fin_pending) {
            struct zpn_mconn *mconn = &(mconn_b->mconn);
            int64_t incarnation;
            int value = __sync_add_and_fetch_4(&(mconn_b->async_tx_count), 1);

            if (value > 2) {
                /* We already have thread call outstanding, no need to make call */
                __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
            } else {

                incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length);

                if (fohh_thread_call(mconn_b->mconn.fohh_thread_id,
                                 async_transmit,
                                 mconn_b,
                                 incarnation) != FOHH_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for mconn_bufferevent_forward_tunnel_end!");
                    __sync_sub_and_fetch_4(&(mconn_b->async_tx_count), 1);
                }
                mconn_b->tx_fin_pending = 1;
            }
        }
    }

    if (((mconn_b->mconn.fin_rcvd && !mconn_b->rx_fin_pending) || drop_data)  &&
        mconn_b->mconn.peer && !mconn_b->mconn.fin_refl) {

        ZPN_DEBUG_MCONN("Reflect mtunnel_end since we already reaceive one");
        if (drop_data) {
            /* Since we don't care about data, pretend we got FIN so we close it quickly */
            mconn_b->mconn.fin_rcvd = 1;
        }

        if (!mconn_b->is_connector_tun) {
            zpn_mconn_forward_mtunnel_end(mconn_b->mconn.peer, MT_CLOSED_TERMINATED, drop_data);
            mconn_b->mconn.fin_refl = 1;
        } else {
            //ZPN_DEBUG_MCONN("Is connector tun, no need to reflect");
        }
    } else {
        if (mconn_b->mconn.fin_refl) {
            ZPN_DEBUG_MCONN("Already reflected");
        } else if (!mconn_b->mconn.fin_rcvd) {
            ZPN_DEBUG_MCONN("Don't reflect, still waiting to receive EOF");
        } else {
            ZPN_DEBUG_MCONN("Don't reflect, no peer");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_bufferevent_window_update_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length,
                                            int64_t owner_incarnation,
                                            int fohh_thread_id,
                                            int tx_len,
                                            int batch_win_upd)
{
    return;
}

void zpn_mconn_bufferevent_stats_update_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           int fohh_thread_id,
                                           enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_bufferevent_disable_read_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_bufferevent_enable_read_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Callbacks exposed to mconn
 */
const struct zpn_mconn_local_owner_calls mconn_bufferevent_calls = {
    zpn_mconn_bufferevent_bind_cb,
    zpn_mconn_bufferevent_unbind_cb,
    zpn_mconn_bufferevent_lock_cb,
    zpn_mconn_bufferevent_unlock_cb,
    zpn_mconn_bufferevent_transmit_cb,
    zpn_mconn_bufferevent_pause_cb,
    zpn_mconn_bufferevent_resume_cb,
    zpn_mconn_bufferevent_forward_tunnel_end_cb,
    zpn_mconn_bufferevent_window_update_cb,
    zpn_mconn_bufferevent_stats_update_cb,
    zpn_mconn_bufferevent_disable_read_cb,
    zpn_mconn_bufferevent_enable_read_cb
};


/* For UT */
struct zpn_mconn_local_owner_calls ut_mconn_bufferevent_calls = {
    zpn_mconn_bufferevent_bind_cb,
    zpn_mconn_bufferevent_unbind_cb,
    zpn_mconn_bufferevent_lock_cb,
    zpn_mconn_bufferevent_unlock_cb,
    zpn_mconn_bufferevent_transmit_cb,
    zpn_mconn_bufferevent_pause_cb,
    zpn_mconn_bufferevent_resume_cb
};

int zpn_mconn_bufferevent_done(struct zpn_mconn_bufferevent *mconn_bufferevent)
{
    return zpn_mconn_done(&mconn_bufferevent->mconn);
}

int zpn_mconn_bufferevent_stuck(struct zpn_mconn_bufferevent *mconn_b)
{
    uint64_t now;
    int is_stuck = 0;

    if (mconn_b->tx_paused) {
        now = monotime_s();
        if (now >= mconn_b->last_pause_time_monotime_s + mconn_b->max_pause_time_interval) {
            is_stuck = 1;
        }
    }

    return is_stuck;
}

int zpn_mconn_bufferevent_clean(struct zpn_mconn_bufferevent *mconn_bufferevent)
{
    int ret = 1;

    if (mconn_bufferevent->bev) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean -- has bev");
        ret = 0;
    }

    if (mconn_bufferevent->async_tx_count) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean, async_tx_count = %ld", (long)mconn_bufferevent->async_tx_count);
        return 0;
    }

    if (mconn_bufferevent->async_rx_count) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean, async_rx_count = %ld", (long)mconn_bufferevent->async_rx_count);
        return 0;
    }

    if (mconn_bufferevent->async_event_count) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean, async_event_count = %ld", (long)mconn_bufferevent->async_event_count);
        return 0;
    }

    if (!zpn_mconn_clean(&(mconn_bufferevent->mconn))) {
        ZPN_DEBUG_MCONN("mconn bufferevent not clean");
        ret = 0;
    }

    ZPN_DEBUG_MCONN("mconn bufferevent clean");
    return ret;
}

void zpn_mconn_bufferevent_internal_display(struct zpn_mconn_bufferevent *mconn_bufferevent)
{
    struct evbuffer *buf;

    zpn_mconn_internal_display(&(mconn_bufferevent->mconn));
    ZPN_DEBUG_MCONN("rx_paused = %d, tx_paused = %d", mconn_bufferevent->rx_paused, mconn_bufferevent->tx_paused);
    ZPN_DEBUG_MCONN("rx_bytes = %ld, tx_bytes = %ld", (long)mconn_bufferevent->rx_bytes, (long)mconn_bufferevent->tx_bytes);
    if (mconn_bufferevent->bev) {
        buf = bufferevent_get_input(mconn_bufferevent->bev);
        ZPN_DEBUG_MCONN("input buffer len = %d", (int)evbuffer_get_length(buf));
        buf = bufferevent_get_output(mconn_bufferevent->bev);
        ZPN_DEBUG_MCONN("output buffer len = %d", (int)evbuffer_get_length(buf));
    } else {
        ZPN_DEBUG_MCONN("No bev");
    }

    zpn_mconn_internal_display(&(mconn_bufferevent->mconn));
}
