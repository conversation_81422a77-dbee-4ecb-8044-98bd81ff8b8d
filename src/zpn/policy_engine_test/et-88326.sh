#!../zpn_policy_engine_test
#
# ET-88326.sh
#
# Inclusive applications for old and new policy evaluation algorithm
#
#

# Enable multi match flag
ENABLE_APP_MULTI_MATCH_FEATURE_FLAG

#compare old and new policy evaluation methods
POLICY_EVALUATION_COMPARE

#Cancel perf comparison for old and new evaluation method with multimatch
POLICY_EVALUATION_COMPARE_PERF_CANCEL

USE_SCOPE_INIT

#
# Start up an (empty) client for customer 73197005911883776
# This will trigger fetching config for customer 73197005911883776.
#
WALLY_POLICY
GET 73197005911883776 access ZPATH_RESULT_ASYNCHRONOUS
WALLY_REG_VERIFY -table zpn_rule -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule
WAITFOR WALLY_REG_VERIFY -table zpn_rule_condition_set -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule_condition_set
WAITFOR WALLY_REG_VERIFY -table zpn_rule_condition_operand -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule_condition_operand
# A deferred policy build occurs here which will trigger this registration.
WAITFOR WALLY_REG_VERIFY -table zpn_policy_set -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_policy_set
WAITFOR GET 73197005911883776 access ZPATH_RESULT_NO_ERROR

#
# Init zpath_table table. The delay is to let the multithreaded
# initialization complete.
ZPATH_TABLE
WAITFOR WALLY_REG_VERIFY -table zpath_table -register YES -id id1
# If you want, you could WALLY_INJECT initial zpath_table rows here.
WALLY_RESPOND -id id1 -rows 0 -table zpath_table

# initilize zpn_application and zpn_application_domain for customer 73197005911883776
ZPN_APPLICATION_DOMAIN -customer 73197005911883776

user Amy add CLIENT_TYPE|id|zpn_client_type_zapp

# Verify wally asked for customer's apps.
WAITFOR WALLY_REG_VERIFY -table zpn_application -register YES -column customer_gid -key 73197005911883776 -id id1
WAITFOR WALLY_REG_VERIFY -table zpn_application_domain -register YES -column customer_gid -key 73197005911883776 -id id1a

# Send response complete to wally, since we have no rows
WALLY_RESPOND -id id1 -rows 0 -table zpn_application
WALLY_RESPOND -id id1a -rows 0 -table zpn_application_domain

# Events between response and app_complete are on different threads,
# so wait a millisacond, here.
DELAY 1000

# Be sure no accidental registration came in again...
WALLY_REG_VERIFY -table zpn_application -register NONE

# ___________________________________________________________________________________________
# |App seg	|	domains 				    |	port 	|	style			|   Bypass type |
# +---------+-------------------------------------------+-------------------+---------------+
# |11 		|	map.corp.company.com 		|	443  	|	inclusive		|   NEVER       |
# |12 		|	.corp.company.com 		    |	443	    |	inclusive		|   NEVER       |
# |13		|	login.us.corp.company.com 	|	443	    |	inclusive		|   NEVER       |
# --------------------------------------------------------------------------+---------------+
#

WALLY_INJECT {"zpn_application":{"gid":11, "customer_gid":73197005911883776, "tcp_port_ranges":[443,443], "domain_names":["map.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":12, "customer_gid":73197005911883776, "tcp_port_ranges":[443,443], "domain_names":[".corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":13, "customer_gid":73197005911883776, "tcp_port_ranges":[443,443], "domain_names":["login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}

WALLY_INJECT {"zpn_application_domain":{"id":21, "customer_gid":73197005911883776, "application_gid":11, "domain_name":"map.corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":22, "customer_gid":73197005911883776, "application_gid":12, "domain_name":".corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":23, "customer_gid":73197005911883776, "application_gid":13, "domain_name":"login.us.corp.company.com", "deleted":0}}

WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 11 -id id1
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 12 -id id2
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 13 -id id3

WALLY_INJECT {"zpn_application_group_application_mapping":{"id":31, "application_id":11, "application_group_id":100, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":32, "application_id":12, "application_group_id":100, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":33, "application_id":13, "application_group_id":100, "customer_gid":73197005911883776}}

WALLY_RESPOND -id id1 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id2 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id3 -rows 1 -table zpn_application_group_application_mapping

WALLY_REG_VERIFY -table zpn_application_group -register YES -column gid -key 100 -id id4
WALLY_INJECT {"zpn_application_group":{"gid":100, "customer_gid":73197005911883776, "enabled":1}}
WALLY_RESPOND -id id4 -rows 1 -table zpn_application_group
DELAY 20000

# Add domains to diamond search
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain map.corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain .corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain login.us.corp.company.com
DELAY 20000

# Access policy (contains rule 1, 2, 3, 4, 5)
WALLY_INJECT {"zpn_policy_set":{"gid":10002, "customer_gid":73197005911883776, "global_set":1, "rule_ids":[1,2,3,4,5], "enabled":1}}

######### Test Case 1 ###############
# access policy:
# rule 1: (app11 or app12) and SIPA allow
# rule 2:  app12 and SIPA allow
# rule 3:  app13 allow
# rule 4:  SIPA allow
# rule 5: (app11 or app12) and machine_tunnel allow
#--------
# app_to_rule:
#  app11 => rule 1, 5
#  app12 => rule 1, 2, 5
#  app13 => rule 3
# rule_to_all: rule 4

#rule 1
WALLY_INJECT {"zpn_rule":{"gid":1, "customer_gid":73197005911883776, "name": "rule 1",  "custom_msg":"(app11 or app12) and SIPA allow", "rule_order":1, "policy_set_gid":10002, "condition_set_ids":[511, 512], "action_result":"ALLOW", "oper":"and"}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":511, "customer_gid":73197005911883776, "oper":"or", "rule_gid":1, "negated":0, "operand_ids":[611, 612], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":611, "condition_id":511, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"11", "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":612, "condition_id":511, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"12", "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":512, "customer_gid":73197005911883776, "oper":"or", "rule_gid":1, "negated":0, "operand_ids":[613], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":613, "condition_id":512, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_ip_anchoring", "deleted":0}}

#rule 2
WALLY_INJECT {"zpn_rule":{"gid":2, "customer_gid":73197005911883776, "name": "rule 2",  "custom_msg":"app12 and SIPA allow", "rule_order":2, "policy_set_gid":10002, "condition_set_ids":[521, 522], "action_result":"ALLOW", "oper":"and"}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":521, "customer_gid":73197005911883776, "oper":"or", "rule_gid":2, "negated":0, "operand_ids":[621], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":621, "condition_id":521, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"12", "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":522, "customer_gid":73197005911883776, "oper":"or", "rule_gid":2, "negated":0, "operand_ids":[622], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":622, "condition_id":522, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_ip_anchoring", "deleted":0}}

#rule 3
WALLY_INJECT {"zpn_rule":{"gid":3, "customer_gid":73197005911883776, "name": "rule 3",  "custom_msg":"app13 allow", "rule_order":3, "policy_set_gid":10002, "condition_set_ids":[531], "action_result":"ALLOW", "oper":"and"}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":531, "customer_gid":73197005911883776, "oper":"or", "rule_gid":3, "negated":0, "operand_ids":[631], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":631, "condition_id":531, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"13", "deleted":0}}

#rule 4
WALLY_INJECT {"zpn_rule":{"gid":4, "customer_gid":73197005911883776, "name": "rule 4",  "custom_msg":"SIPA allow", "rule_order":4, "policy_set_gid":10002, "condition_set_ids":[541], "action_result":"ALLOW", "oper":"and"}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":541, "customer_gid":73197005911883776, "oper":"or", "rule_gid":4, "negated":0, "operand_ids":[641], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":641, "condition_id":541, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_ip_anchoring", "deleted":0}}

#rule 5
WALLY_INJECT {"zpn_rule":{"gid":5, "customer_gid":73197005911883776, "name": "rule 5",  "custom_msg":"(app11 or app12) and machine_tunnel allow", "rule_order":5, "policy_set_gid":10002, "condition_set_ids":[551, 552], "action_result":"ALLOW", "oper":"and"}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":551, "customer_gid":73197005911883776, "oper":"or", "rule_gid":5, "negated":0, "operand_ids":[651, 652], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":651, "condition_id":551, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"11", "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":652, "condition_id":551, "customer_gid":73197005911883776, "object_type":"APP", "lhs":"id", "rhs":"12", "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_set":{"id":552, "customer_gid":73197005911883776, "oper":"or", "rule_gid":5, "negated":0, "operand_ids":[653], "deleted":0}}
WALLY_INJECT {"zpn_rule_condition_operand":{"id":653, "condition_id":552, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_machine_tunnel", "deleted":0}}

WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 yes
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 no
DELAY 2000

# evaluate rules in order: 1, 2, 4, 5 with No rule matched
eval -scope 73197005911883776 -customer 73197005911883776 -access -user Amy -verify-rule 0 -app 11 -eval_rule_count 4 -req_app map.corp.company.com:443

######### Test Case 2 ###############
# access policy:
# rule 1: (app11 or app12) and SIPA allow
# rule 2:  app12 and SIPA allow
# rule 3:  app13 allow
# rule 4:  SIPA allow
# rule 5: (app11 or app12) and zapp allow
#--------
# app_to_rule:
#  app11 => rule 1, 5
#  app12 => rule 1, 2, 5
#  app13 => rule 3
# rule_to_all: rule 4

WALLY_INJECT {"zpn_rule_condition_operand":{"id":653, "condition_id":552, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_zapp", "deleted":0}}
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 yes
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 no
DELAY 2000

# evaluate rules in order: 1, 2, 4, 5 with rule 5 matched
eval -scope 73197005911883776 -customer 73197005911883776 -access -user Amy -verify-rule 5 -app 11 -eval_rule_count 4 -req_app map.corp.company.com:443

######### Test Case 3 ###############
# access policy:
# rule 1: (app11 or app12) and SIPA allow
# rule 2:  app12 and zapp allow
# rule 3:  app13 allow
# rule 4:  SIPA allow
# rule 5: (app11 or app12) and zapp allow
#--------
# app_to_rule:
#  app11 => rule 1, 5
#  app12 => rule 1, 2, 5
#  app13 => rule 3
# rule_to_all: rule 4

WALLY_INJECT {"zpn_rule_condition_operand":{"id":622, "condition_id":522, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_zapp", "deleted":0}}
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 yes
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 no
DELAY 2000

# evaluate rules in order: 1, 2 with rule 2 matched
eval -scope 73197005911883776 -customer 73197005911883776 -access -user Amy -verify-rule 2 -app 11 -eval_rule_count 2 -req_app map.corp.company.com:443

######### Test Case 4 ###############
# access policy:
# rule 1: (app11 or app12) and SIPA allow
# rule 2:  app12 and zapp allow
# rule 3:  app13 allow
# rule 4:  SIPA allow
# rule 5: (app11 or app12) and machine_tunnel allow
#--------
# app_to_rule:
#  app11 => rule 1, 5
#  app12 => rule 1, 2, 5
#  app13 => rule 3
# rule_to_all: rule 4

WALLY_INJECT {"zpn_rule_condition_operand":{"id":653, "condition_id":552, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_machine_tunnel", "deleted":0}}
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 yes
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 no
DELAY 2000

# evaluate rules in order: 1, 2 with rule 2 matched
eval -scope 73197005911883776 -customer 73197005911883776 -access -user Amy -verify-rule 2 -app 11 -eval_rule_count 2 -req_app map.corp.company.com:443

######### Test Case 5 ###############
# access policy:
# rule 1: (app11 or app12) and zapp allow
# rule 2:  app12 and zapp allow
# rule 3:  app13 allow
# rule 4:  SIPA allow
# rule 5: (app11 or app12) and machine_tunnel allow
#--------
# app_to_rule:
#  app11 => rule 1, 5
#  app12 => rule 1, 2, 5
#  app13 => rule 3
# rule_to_all: rule 4

WALLY_INJECT {"zpn_rule_condition_operand":{"id":613, "condition_id":512, "customer_gid":73197005911883776, "object_type":"CLIENT_TYPE", "lhs":"id", "rhs":"zpn_client_type_zapp", "deleted":0}}
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 yes
WAITFOR BUILD_OUTSTANDING -scope 73197005911883776 no
DELAY 2000

# evaluate rules in order: 1 with rule 1 matched
eval -scope 73197005911883776 -customer 73197005911883776 -access -user Amy -verify-rule 1 -app 11 -eval_rule_count 1 -req_app map.corp.company.com:443
