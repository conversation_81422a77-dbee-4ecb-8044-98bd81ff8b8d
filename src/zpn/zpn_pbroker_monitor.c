#include <sys/stat.h>
#include <limits.h>
#include <stdio.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "zpn/zpn_broker.h"
#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_pbroker_monitor.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpn/zpn_private_broker_proxy.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpath_misc/zpath_version.h"
#include "zhw/zhw_id.h"
#include "zpn/pbroker_assert.h"
#include "zpn/zpn_system.h"
#include "zpath_lib/zpath_system.h"
#include "wally/wally.h"
#include "fohh/fohh_log.h"
#include "zpn/zpn_pbroker_data_connection_stats.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_site.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include <regex.h>

static struct zthread_info *tickle_me = NULL;
static struct zthread_info *malloc_tickle = NULL;
int is_pbroker_dev_environment = 0;

extern struct zpn_firedrill_stats g_pb_firedrill_stats_obj;
extern struct mmdb_stats query_stats;
int64_t prev_ts;
int mmdb_try;
int64_t prev_isp_cnt;
int64_t prev_geo_cnt;
static int64_t mt_schedule_rand_sec = 0;
static int mt_monitor_count = 0;
static int mt_monitor_first_schedule_time = 0, mt_is_first_schedule_completed = 0;
static int mt_monitor_second_schedule_time = 0, mt_is_second_schedule_completed = 0;

#define NUM_MMDB_TRY 4
#define ZPN_PB_FIREDRILL_TRANSIT_TIMER 300 // 5 mins for AUM to update the UI, prod value
//#define ZPN_PB_FIREDRILL_TRANSIT_TIMER 60

static void
pbroker_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie);

static void
pbroker_malloc_trim_timer_cb(evutil_socket_t sock, short flags, void *cookie);

int
pbroker_is_ready(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return ((gs->magic == PRIVATE_BROKER_GLOBAL_MAGIC) ? ZPN_RESULT_NO_ERROR : ZPN_RESULT_NOT_READY);

}

static int char_to_digit(char c) {
    if (!isdigit(c)) {
        return -1;
    }

    char str[2] = {0};
    str[0] = c;
    return atoi(str);
}

static int zpn_pse_malloc_trim_schedule_day_str_validator(const char *value_str){
    int valid = 1;

    if (!value_str) {
        valid = 0;
        return valid;
    }

    /*
    *   config.pse.malloc_trim.schedule.days
    *   Format :- String "0123456" # Sunday being '0', Monday being '1', ... and '6' being Saturday
    *   Multpile days can be configured to trigger malloc trim
    */
    int size = strlen(value_str);
    if (size > 7) {
        ZPN_LOG(AL_ERROR, "Max allowed len of %s is 7, but configured : %s len is greater \n" ,ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS, value_str);
        valid = 0;
        goto done;
    }

    for (int days_count = 0; days_count < size; days_count++) {
        int day = char_to_digit(value_str[days_count]);
        if (day < 0 || day > 6) {
            ZPN_LOG(AL_ERROR, "Invalid day %c configured in %s\n", value_str[days_count], ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS);
            valid = 0;
            goto done;
        }
    }

done:
    return valid;
}

static int zpn_pse_malloc_trim_schedule_time_str_validator(const char *value_str)
{
    regex_t reg_ex;
    const char *re_pattern = "^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";  /* Match HH:MM:SS in 24 hour time format, strictly */
    char re_error[128] = "";
    int ret;
    int valid = 1;

    if (!value_str) {
        valid = 0;
        return valid;
    }

    /* Compile regex */
    ret = regcomp(&reg_ex, re_pattern, REG_EXTENDED);
    if (ret) {
        regerror(ret, &reg_ex, re_error, sizeof(re_error));
        ZPATH_LOG(AL_ERROR, "regcomp failed with err: %s", re_error);
        valid = 0;
        goto done;
    }

    /* Match regex */
    ret = regexec(&reg_ex, value_str, 0, NULL, 0);

    valid = (ret == 0) ? 1 : 0;

    regfree(&reg_ex);

done:

    return valid;
}

static struct zpath_config_override_desc zpn_pbroker_monitor_descriptions[] = {
      {
        .key                = ZPN_PSE_MALLOC_TRIM_ENABLE,
        .desc               = "enable malloc_trim() on a schedule to trigger memory to be retuned back to Linux on Pse",
        .details            = "schedule format: Days Time rand_sec\n"
                              "Order of check - : private broker id, private broker grp id, customer id, global\n"
                              "default: 0 (i.e. disabled), allowed values are 0 = Disabled, 1 = Enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_systemwide,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = ZPN_PSE_MALLOC_TRIM_ENABLE_MIN,
        .int_range_hi       = ZPN_PSE_MALLOC_TRIM_ENABLE_MAX,
        .int_default        = DEFAULT_ZPN_PSE_MALLOC_TRIM_ENABLE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM
    },
    {
        .key                = ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS,
        .desc               = "Malloc trim schedule days controller",
        .details            = "String 0123456 # Sunday being '0', Monday being '1'... Saturday being '6'\n"
                              "Multiple days can be configured, Ex: String 01 # Malloc trim will be triggered on sunday & monday\n"
                              "Order of check - : private broker id, private broker grp id, customer id, global\n"
                              "default: '0' i.e sunday and Max len: 7",
        .val_type           = config_type_str,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(zpn_pse_malloc_trim_schedule_day_str_validator),
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME,
        .desc               = "Malloc trim first schedule time controller in HH:MM:SS format (24-hour clock), uses local time for each system",
        .details            = "Malloc trim first schedule time controller in HH:MM:SS format (24-hour clock), can be set between 00:00 to 23:59\n"
                              "Malloc trim will be triggered for the 1st time at this configured time in a day\n"
                              "Order of check - : private broker id, private broker grp id, customer id, global\n"
                              "default: enabled, 03:00:00 - local time for each system",
        .val_type           = config_type_str,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(zpn_pse_malloc_trim_schedule_time_str_validator),
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME,
        .desc               = "Malloc trim second schedule time controller in HH:MM:SS format (24-hour clock), uses local time for each system",
        .details            = "Malloc trim second schedule time controller in HH:MM:SS format (24-hour clock), can be set between 00:00 to 23:59\n"
                              "Malloc trim will be triggered for the 2nd time at this configured time in a day\n"
                              "Order of check - : private broker id, private broker grp id, customer id, global\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = DEFAULT_ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(zpn_pse_malloc_trim_schedule_time_str_validator),
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC,
        .desc               = "Malloc trim schedule random seconds controller",
        .details            = "Local system time randomize seconds between [ 0 to 15 mins ] as seconds\n"
                              "Order of check - : private broker id, private broker grp id, customer id, global\n"
                              "default: 900 secs, min:0 secs max:900 secs",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC_MIN,
        .int_range_hi       = ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC_MAX,
        .int_default        = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MALLOC_TRIM
    }
};

static int zpn_pbroker_monitor_config_override_desc_register(void){
    int res = ZPN_RESULT_NO_ERROR;

    // Component Private broker specific registration
    for (int i = 0; i < (sizeof(zpn_pbroker_monitor_descriptions) / sizeof(struct zpath_config_override_desc)); i++) {
        res = zpath_config_override_desc_register(&zpn_pbroker_monitor_descriptions[i]);

        if (res) {
            ZPN_LOG(AL_ERROR, "Unable to register zpn_pbroker_monitor config override[%d] for key: %s, err: %s",
                                i, zpn_pbroker_monitor_descriptions[i].key, zpath_result_string(res));
            return res;
        }
    }
    return res;
}

static int64_t zpn_pse_malloc_trim_enabled() {
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
    int64_t config_val = zpath_config_override_get_config_int(ZPN_PSE_MALLOC_TRIM_ENABLE,
                                                              &config_val,
                                                              DEFAULT_ZPN_PSE_MALLOC_TRIM_ENABLE,
                                                              ZPN_BROKER_GET_GID(),
                                                              ZPN_BROKER_GET_GROUP_GID(),
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);
    return config_val;
}

static char *get_malloc_trim_schedule_days_str(){
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
    char* day_config_val = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS;
    day_config_val = zpath_config_override_get_config_str(ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS,
                                                          &day_config_val,
                                                          DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS,
                                                          ZPN_BROKER_GET_GID(),
                                                          ZPN_BROKER_GET_GROUP_GID(),
                                                          customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);
    return day_config_val;
}

static char *get_malloc_trim_schedule_time_str(){
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
    char *time_config_val = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME;
    time_config_val = zpath_config_override_get_config_str(ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME,
                                                           &time_config_val,
                                                           DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME,
                                                           ZPN_BROKER_GET_GID(),
                                                           ZPN_BROKER_GET_GROUP_GID(),
                                                           customer_gid,
                                                           (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                           (int64_t)0);
    return time_config_val;
}

static char *get_malloc_trim_second_schedule_time_str(){
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
    char *time_config_val = DEFAULT_ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME;
    time_config_val = zpath_config_override_get_config_str(ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME,
                                                           &time_config_val,
                                                           DEFAULT_ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME,
                                                           ZPN_BROKER_GET_GID(),
                                                           ZPN_BROKER_GET_GROUP_GID(),
                                                           customer_gid,
                                                           (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                           (int64_t)0);
    return time_config_val;
}

static int64_t get_malloc_trim_schedule_rand_sec(){
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
    int64_t max_rand_sec_config_val = DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC;
    max_rand_sec_config_val = zpath_config_override_get_config_int(ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC,
                                                                   &max_rand_sec_config_val,
                                                                   DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC,
                                                                   ZPN_BROKER_GET_GID(),
                                                                   ZPN_BROKER_GET_GROUP_GID(),
                                                                   customer_gid,
                                                                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                   (int64_t)0);
    return max_rand_sec_config_val;
}

static void add_random_secs_to_schedule_time(int *sec, int *min, int *hour){
    int total_seconds = *sec + mt_schedule_rand_sec;
    *sec = total_seconds % 60;

    int additional_minutes = total_seconds / 60;
    int total_minutes = *min + additional_minutes;
    *min = total_minutes % 60;

    int additional_hours = total_minutes / 60;
    *hour = *hour + additional_hours;
}

static void
pbroker_malloc_trim_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int schedule_sec = 0, schedule_min = 0, schedule_hour = 0;
    int second_schedule_sec = 0, second_schedule_min = 0, second_schedule_hour = 0;
    int is_second_schedule_enabled = 0;
    if (malloc_tickle) zthread_heartbeat(malloc_tickle);

    if(!zpn_pse_malloc_trim_enabled()){
        return;
    }

    //increment the timers
    mt_monitor_count++;

    char* days_str = get_malloc_trim_schedule_days_str();
    char* schedule_time_str = get_malloc_trim_schedule_time_str();

    //parse the first scheduled times & add the random seconds
    if (sscanf(schedule_time_str, "%2d:%2d:%2d", &schedule_hour, &schedule_min, &schedule_sec) != 3) {
        ZPN_LOG(AL_ERROR, "Failed to parse %s, schedule_time_str:  %s\n", ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME, schedule_time_str);
        return;
    }
    add_random_secs_to_schedule_time(&schedule_sec, &schedule_min, &schedule_hour);

    //parse the second scheduled times & add the random seconds
    /*
    * To provide flexibility for our customers, we have made the second scheduled malloc trim time optional instead of enforcing it as mandatory.
    *
    * Implementation details:
    * - The second malloc trim time will be considered only if it is set to different time than first trim time.
    * - If the second trim time is default or configured to be the same as the first trim time, we will not schedule the second malloc trim.
    */
    char* second_schedule_time_str = get_malloc_trim_second_schedule_time_str();
    if (strcmp(second_schedule_time_str, DEFAULT_ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME) != 0) {
        if (sscanf(second_schedule_time_str, "%2d:%2d:%2d", &second_schedule_hour, &second_schedule_min, &second_schedule_sec) != 3) {
            ZPN_LOG(AL_ERROR, "Failed to parse %s, second_schedule_time_str: %s\n", ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME, second_schedule_time_str);
            return;
        }
        add_random_secs_to_schedule_time(&second_schedule_sec, &second_schedule_min, &second_schedule_hour);
        is_second_schedule_enabled = 1;
    }


    if (mt_monitor_count % ( ZPN_TUNNEL_MONITOR_MALLOC_TRIM_HOUR_INTERVAL_COUNT) == 0) {
        if (is_second_schedule_enabled) {
            ZPN_DEBUG_PRIVATE_BROKER("Malloc_trim schedule days: %s, 1st schedule time: %d:%d:%d local and 2nd schedule time: %d:%d:%d local\n",
                days_str,
                schedule_hour,schedule_min,schedule_sec,
                second_schedule_hour,second_schedule_min,second_schedule_sec);
        } else {
            ZPN_DEBUG_PRIVATE_BROKER("Malloc_trim schedule days: %s, 1st schedule time: %d:%d:%d local and 2nd schedule time is not configured\n",
                days_str,
                schedule_hour,schedule_min,schedule_sec);
        }
        mt_monitor_count = 0;
    }

    size_t days_str_len = strlen(days_str);
    for (int days_count = 0; days_count < days_str_len; days_count++) {

        //get the scheduled day
        int day = char_to_digit(days_str[days_count]);

        // Get the current time in struct tm format
        time_t rawtime;
        struct tm timeinfo;
        time(&rawtime);
        localtime_r(&rawtime,&timeinfo);

        if (timeinfo.tm_wday == day) {

            //increment timers
            mt_monitor_first_schedule_time++;
            if (is_second_schedule_enabled) {
                mt_monitor_second_schedule_time++;
            }

            //check for 1st schedule time in a day
            if (!mt_is_first_schedule_completed &&
                timeinfo.tm_hour == schedule_hour &&
                timeinfo.tm_min >= schedule_min &&
                timeinfo.tm_sec >= schedule_sec) {

#ifdef __linux__
                int64_t start_us = epoch_us();
                int ret = malloc_trim(0);
                int64_t end_us = epoch_us();

                if ((end_us - start_us) > (SYSTEM_MEM_API_WARN_TIME_SEC * US_PER_SEC))
                    ZPATH_LOG(AL_WARNING, "Malloc_trim for first schedule time is running long %" PRId64 " ms on Pse", (end_us - start_us)/US_PER_MSEC);

                if (!ret) {
                    ZPN_LOG(AL_ERROR, "Memory release for first schedule time failed on Pse\n");
                } else {
                    ZPN_LOG(AL_INFO, "Memory releasing for first schedule time is successful on Pse and took %" PRId64 " ms, start_us: %" PRId64 " end_us: %" PRId64 "\n", (end_us - start_us)/US_PER_MSEC, start_us, end_us);
                }
#endif
                mt_is_first_schedule_completed = 1;
                mt_monitor_first_schedule_time = 0;
            }

            //check for 2nd schedule time in a day if enabled
            if (is_second_schedule_enabled &&
                !mt_is_second_schedule_completed &&
                timeinfo.tm_hour == second_schedule_hour &&
                timeinfo.tm_min >= second_schedule_min &&
                timeinfo.tm_sec >= second_schedule_sec) {
#ifdef __linux__
                    int64_t start_us = epoch_us();
                    int ret = malloc_trim(0);
                    int64_t end_us = epoch_us();

                    if ((end_us - start_us) > (SYSTEM_MEM_API_WARN_TIME_SEC * US_PER_SEC))
                        ZPATH_LOG(AL_WARNING, "Malloc_trim for second schedule time is running long %" PRId64 " ms on Pse", (end_us - start_us)/US_PER_MSEC);

                    if (!ret) {
                        ZPN_LOG(AL_ERROR, "Memory release for second schedule time failed on Pse\n");
                    } else {
                        ZPN_LOG(AL_INFO, "Memory releasing for second schedule time is successful on Pse and took %" PRId64 " ms, start_us: %" PRId64 " end_us: %" PRId64 "\n", (end_us - start_us)/US_PER_MSEC, start_us, end_us);
                    }
#endif
                mt_is_second_schedule_completed = 1;
                mt_monitor_second_schedule_time = 0;
            }

            //reset the flags
            if (mt_monitor_first_schedule_time && (mt_monitor_first_schedule_time % ( ZPN_TUNNEL_MONITOR_MALLOC_TRIM_DAY_INTERVAL_COUNT) == 0)) {
                mt_is_first_schedule_completed = 0;
            }

            if (is_second_schedule_enabled && mt_monitor_second_schedule_time && (mt_monitor_second_schedule_time % ( ZPN_TUNNEL_MONITOR_MALLOC_TRIM_DAY_INTERVAL_COUNT) == 0)) {
                mt_is_second_schedule_completed = 0;
            }
        }
    }
}


static int
pbroker_malloc_trim_timer_init(struct event_base *base)
{
    int init_f = 0;
    struct timeval tv;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->pbroker_malloc_trim_timer = event_new(base,
                                      -1,
                                      EV_PERSIST,
                                      pbroker_malloc_trim_timer_cb,
                                      NULL);

    if (!gs->pbroker_malloc_trim_timer) {
        ZPN_LOG(AL_ERROR, "Could not create pbroker malloc trim timer");
        return ZPN_RESULT_ERR;
    }

    /* Make it show health once right away */
    pbroker_malloc_trim_timer_cb(-1, 0, &init_f);

    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
    tv.tv_usec = 0;
    if (event_add(gs->pbroker_malloc_trim_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate pbroker malloc trim timer");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void*
pbroker_malloc_trim_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *base = NULL;
    int res;

    malloc_tickle = zthread_arg;

    base = event_base_new();
    if (!base) {
        ZPN_LOG(AL_ERROR, "Could not create event_base: pbroker_malloc_trim_thread");
        goto fail;
    }

    res = pbroker_malloc_trim_timer_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize event_base, pbroker_malloc_trim_thread thread");
        goto fail;
    }

    zevent_base_dispatch(base);

    ZPN_LOG(AL_ERROR, "Not reachable, pbroker_malloc_trim_thread");
fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
    return NULL;
}

/*
* This thread will trigger malloc_trim(pad) with pad = 0 on a schedule to trigger memory to be retuned back to Linux periodically on Pse
* Feature enable/disable is controlled by:
  config.pse.malloc_trim.enable <= Supported only for Pse at instance/instance grp/customer/global level

Malloc trim schedule is controlled by:
config.pse.malloc_trim.schedule.days        <= String "0123456" # Sunday being '0', Monday being '1', ... and '6' being Saturday
config.pse.malloc_trim.schedule.time        <= String "03:00:00" # 3 AM Local Time (1st schedule time in a day)
config.pse.malloc_trim.second.schedule.time <= String "13:00:00" # 1 PM Local Time (Optional 2nd schedule time in a day)
config.pse.malloc_trim.schedule.rand_sec    <= Local system time randomize seconds between [ 0 -> 15*60 ] (i.e. 15 minutes as seconds, 0 also valid )

Scheduled time: time + rand_sec
*/

static int
pbroker_malloc_trim_thread_init(void)
{
    pthread_t thread;
    int res;

    //setting random secs once in lifetime of pse
    srand(time(NULL));              // Seed the random number generator with current time
    int64_t max_rand_sec = get_malloc_trim_schedule_rand_sec();
    mt_schedule_rand_sec = rand() % (max_rand_sec + 1);

    res = zthread_create(&thread,                       // thread
                        pbroker_malloc_trim_thread,     // thread func
                        NULL,                           // cookie
                        "pbroker_malloc_trim_thread",   // thread name
                        5*60,                           // max missing heartbeat_s
                        16 * 1024 * 1024,               // stack size bytes
                        60 * 1000 * 1000,               // user int
                        NULL);                          // user void

    if (res) return ZPN_RESULT_ERR;
    return ZPN_RESULT_NO_ERROR;
}

void*
pbroker_monitor_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *base = NULL;
    int res;

    tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        ZPN_LOG(AL_ERROR, "Could not create event_base: pbroker_monitor_thread");
        goto fail;
    }

    res = pbroker_monitor_timer_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize event_base, pbroker_monitor_thread thread");
        goto fail;
    }

    zevent_base_dispatch(base);

    ZPN_LOG(AL_ERROR, "Not reachable, pbroker_monitor_thread");
fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
    return NULL;
}

int
pbroker_monitor_thread_init(void)
{
    pthread_t thread;
    int res;
    res = zthread_create(&thread,                   // thread
                         pbroker_monitor_thread,    // thread func
                         NULL,                      // cookie
                         "pbroker_monitor_thread",  // thread name
                         3*60,                      // max missing heartbeat_s
                         16 * 1024 * 1024,          // stack size bytes
                         60 * 1000 * 1000,          // user int
                         NULL);                     // user void
    if (res) return ZPN_RESULT_ERR;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_pbroker_firedrill_config_fetch_callback(void *response_callback_cookie,
                                                struct wally_registrant *registrant,
                                                struct wally_table *table,
                                                int64_t request_id,
                                                int row_count)
{
    ZPN_LOG(AL_ERROR,"zpn_pbroker_firedrill_config_fetch_callback entered..");
    if(zpn_pbroker_firedrill_session_request()) {
        return ZPN_RESULT_ERR;
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_pbroker_firedrill_get_config(struct zpn_firedrill_site **firedrill_config)
{
    int res = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    res = zpn_firedrill_site_by_site_gid(gs->site_gid, gs->customer_id,
                                            zpn_pbroker_firedrill_config_fetch_callback,
                                            firedrill_config);
    if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64" res: %s",gs->site_gid, zpn_result_string(res));
        return res;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_ERROR, "firedrill config fetch asynchronous site_gid = %"PRId64" res: %s",gs->site_gid, zpn_result_string(res));
        return ZPN_RESULT_NO_ERROR;
    }
    return res;
}

int zpn_pbroker_firedrill_start(int64_t firedrill_interval)

{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if( (gs->firedrill_status == ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED ||
        gs->firedrill_status == ZPN_PRIVATE_BROKER_FIREDRILL_TRANSIT )&&
        firedrill_interval) {
        /* activate the timer */
        if(zpn_private_broker_firedrill_timer_activate(firedrill_interval)) {
            ZPN_LOG(AL_ERROR, "firedrill timer activation failed");
            return ZPN_RESULT_ERR;
        }
        /* switch the connections to pcc */
        zpn_private_broker_switch_to_pcc();
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_pbroker_firedrill_session_request()
{
    int res = 0;
    struct zpn_firedrill_site *firedrill_config = NULL;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if(!gs->site_gid) {
        ZPN_LOG(AL_ERROR, "gs->site_gid is NULL");
        return ZPN_RESULT_ERR;
    }

    res = zpn_pbroker_firedrill_get_config(&firedrill_config);
    if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_ERR;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }

    if(!firedrill_config) {
        ZPN_LOG(AL_ERROR, "firedrill config is NULL");
        return ZPN_RESULT_ERR;
    }

    if(zpn_pbroker_firedrill_start(firedrill_config->firedrill_interval_s)) {
        return ZPN_RESULT_ERR;
    }
    ZPN_LOG(AL_INFO, "firedrill activation successful for customer: %"PRId64" for interval: %"PRId64"",
        gs->customer_id, firedrill_config->firedrill_interval_s);

    return ZPN_RESULT_NO_ERROR;
}

void zpn_private_broker_firedrill_stop(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    if(gs->pbroker_firedrill_timer) {
        event_del(gs->pbroker_firedrill_timer);
    }


    if( ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED == gs->firedrill_status) {
        ZPN_LOG(AL_ERROR, "assistant not in firedrill mode\n");
        return;
   }
   gs->firedrill_status = ZPN_PRIVATE_BROKER_FIREDRILL_TRANSIT;

    ZPN_LOG(AL_INFO, "pb: firedrill interval completed, connect back to cloud");

    if(fohh_thread_call( fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MC), zpn_pbroker_mission_critical_conn_create, NULL, 0)) {
        ZPN_LOG(AL_CRITICAL, "Implement me");
    }
}

int
pbroker_monitor_timer_init(struct event_base *base)
{
    int init_f = 0;
    struct timeval tv;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    prev_ts = 0;
    mmdb_try = 0;
    prev_isp_cnt = 0;
    prev_geo_cnt = 0;

    gs->pbroker_monitor_timer = event_new(base,
                                      -1,
                                      EV_PERSIST,
                                      pbroker_monitor_timer_cb,
                                      NULL);
    if (!gs->pbroker_monitor_timer) {
        ZPN_LOG(AL_ERROR, "Could not create pbroker monitor timer");
        return ZPN_RESULT_ERR;
    }

    /* Make it show health once right away */
    pbroker_monitor_timer_cb(-1, 0, &init_f);

    tv.tv_sec = 60;
    tv.tv_usec = 0;
    if (event_add(gs->pbroker_monitor_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate pbroker monitor timer");
        return ZPN_RESULT_ERR;
    }

    /* create the firedrill timer */
    /* activate it in the row callback when it is trigerred through adminUI */
    gs->pbroker_firedrill_timer = event_new(base,
                                    -1,
                                    EV_TIMEOUT,
                                    zpn_private_broker_firedrill_stop,
                                    NULL);
    if (!gs->pbroker_firedrill_timer) {
        ZPN_LOG(AL_ERROR, "Could not create firedrill timer for private service edge");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Log the status of the config connections. Called periodically
 */
static void
pbroker_cfg_log_status(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *cfg_conn = NULL;

    if (gs->private_broker_state &&
        gs->private_broker_state->wally_fohh_client_handle &&
        gs->private_broker_state->wally_fohh_client_handle) {
        cfg_conn = wally_fohh_client_get_f_conn(gs->private_broker_state->wally_fohh_client_handle);
    }

    if (cfg_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(cfg_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(cfg_conn);

        if (fohh_get_state(cfg_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(cfg_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        PBROKER_LOG(AL_NOTICE, "Public Broker config connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(cfg_conn), fohh_description(cfg_conn),
                      fohh_get_uptime_str(cfg_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(cfg_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(cfg_conn),
                      tx_b, rx_b);
    } else {
        PBROKER_LOG(AL_NOTICE, "Public Broker config connection - NONE");
    }
}


/*
 * Log the status of the static config connections. Called periodically
 */
static void
pbroker_rcfg_log_status(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *cfg_conn = NULL;

    if (gs->private_broker_state &&
        gs->private_broker_state->static_wally_fohh_client_handle) {
        cfg_conn = wally_fohh_client_get_f_conn(gs->private_broker_state->static_wally_fohh_client_handle);
    }

    if (cfg_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(cfg_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(cfg_conn);

        if (fohh_get_state(cfg_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(cfg_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        PBROKER_LOG(AL_NOTICE, "Public Broker static config connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(cfg_conn), fohh_description(cfg_conn),
                      fohh_get_uptime_str(cfg_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(cfg_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                      fohh_conn_get_current_disconnect_duration_s(cfg_conn),
                      tx_b, rx_b);
    } else {
        PBROKER_LOG(AL_NOTICE, "Public Broker static config connection - NONE");
    }
}

void
pbroker_log_tx_log_status()
{
    fohh_log_send_log_status(argo_log_get_name(zpn_event_collection));
    fohh_log_send_log_status(argo_log_get_name(zpn_transaction_collection));
    fohh_log_send_log_status(argo_log_get_name(zpn_auth_collection));
    fohh_log_send_log_status(argo_log_get_name(zpn_ast_auth_collection));
    fohh_log_send_log_status(argo_log_get_name(zpn_dns_collection));
}

void pbroker_log_get_endpoint_data(struct fohh_log_endpoint_cns** cn, struct zpn_private_broker_global_state *gs)
{
    struct fohh_connection *fohh_conn = NULL;
    char fohh_desc_info[1024] = {'\0'};
    int i=0;
    char *peer_cn = NULL;

    *cn = (struct fohh_log_endpoint_cns*) ZPN_CALLOC(sizeof(struct fohh_log_endpoint_cns));
    (*cn)->num_cns = 0;
    (*cn)->cns = ZPN_CALLOC(sizeof(char*)*MAX_COLLECTIONS);

    //Event log
    fohh_conn = fohh_get_log_handle(zpn_event_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[event_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Transaction log
    fohh_conn = fohh_get_log_handle(zpn_transaction_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[transaction_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Auth log
    fohh_conn = fohh_get_log_handle(zpn_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Ast Auth log
    fohh_conn = fohh_get_log_handle(zpn_ast_auth_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[ast_auth_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Dns log
    fohh_conn = fohh_get_log_handle(zpn_dns_collection);
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[dns_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                           (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    //Stats log
    fohh_conn = gs->private_broker_state->broker_stats;
    peer_cn = fohh_peer_cn(fohh_conn);
    if(peer_cn){
        memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
        snprintf(fohh_desc_info, sizeof(fohh_desc_info), "[stats_log, %s, %"PRId64", %s]", peer_cn, fohh_get_uptime_s(fohh_conn),
                                            (fohh_get_state(fohh_conn) == fohh_connection_connected)?"up":"dn");
        (*cn)->cns[i++] = ZPN_STRDUP(fohh_desc_info, strlen(fohh_desc_info));
    }

    (*cn)->num_cns = i;
    return;
}

/*
 * Log the status of the control connections. Called periodically
 */
static void
pbroker_control_log_status(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *control_conn = NULL;

    if (gs->private_broker_state &&
        gs->private_broker_state->broker_control) {
        control_conn = gs->private_broker_state->broker_control;
    }

    if (control_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;
        uint8_t is_cloud_unreachable = 0;
        uint8_t is_resiliency_activated = 0;

        fohh_connection_max_rtt_to_string(control_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(control_conn);

        if (fohh_get_state(control_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(control_conn, & tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }
        if (fohh_conn_get_current_disconnect_duration_s(gs->private_broker_state->broker_control)
                >= zpn_pse_get_resiliency_cloud_down_interval_s() ) {
                is_cloud_unreachable = 1;
                if ( zpn_is_pse_resiliency_enabled() ) {
                    is_resiliency_activated = 1;
                }
        }

        PBROKER_LOG(AL_NOTICE, "Private Broker to Public Broker control connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64", Cloud unreachable: %u, Resiliency activated: %u",
                    fohh_state(control_conn), fohh_description(control_conn),
                    fohh_get_uptime_str(control_conn, uptime_str, sizeof(uptime_str)),
                    (fohh_get_state(control_conn) == fohh_connection_connected) ? max_rtt_us_str : "",
                    fohh_conn_get_current_disconnect_duration_s(control_conn),
                    tx_b, rx_b,is_cloud_unreachable,is_resiliency_activated);
    } else {
        PBROKER_LOG(AL_NOTICE, "Public Broker control connection - NONE");
    }
}

static void
pbroker_alt_cloud_status(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if(!gs->alt_cloud_feature_state_initialised) {
        PBROKER_LOG(AL_NOTICE, "alt-cloud-feature: uninitialised");
    } else {
        if(gs->alt_cloud_feature_state) {
            PBROKER_LOG(AL_NOTICE, "alt-cloud-feature: enabled, default-cloud-name: %s, redir-cloud-name: %s, listener-cloud-name: %s",
                                    gs->cfg_key_cloud,
                                    gs->redir_alt_cloud_name[0] ? gs->redir_alt_cloud_name : "None",
                                    gs->listener_alt_cloud_name[0] ? gs->listener_alt_cloud_name : "None"
                                    );
        } else {
            PBROKER_LOG(AL_NOTICE, "alt-cloud-feature: disabled");
        }
    }
}

static void
pbroker_stats_log_status(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *stats_conn = NULL;
    if (gs->private_broker_state &&
        gs->private_broker_state->broker_stats) {
        stats_conn = gs->private_broker_state->broker_stats;
    }

    if(stats_conn) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        uint64_t tx_b = 0;
        uint64_t rx_b = 0;
        uint64_t tx_o = 0;
        uint64_t rx_o = 0;
        uint64_t tx_raw_tlv = 0;
        uint64_t rx_raw_tlv = 0;

        fohh_connection_max_rtt_to_string(stats_conn, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(stats_conn);

        if (fohh_get_state(stats_conn) == fohh_connection_connected) {
            fohh_connection_get_stats(stats_conn, & tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
        }

        PBROKER_LOG(AL_NOTICE, "Private Broker to Public Broker Log(stats_log) connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"",
                      fohh_state(stats_conn), fohh_description(stats_conn),
                      fohh_get_uptime_str(stats_conn, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(stats_conn) == fohh_connection_connected)? max_rtt_us_str: "",
                      fohh_conn_get_current_disconnect_duration_s(stats_conn),
                      tx_b, rx_b);
    } else {
        ZPN_LOG(AL_NOTICE, "Public Broker Log(stats_log) connection - NONE");
    }
}

static void
pbroker_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int64_t         now_cloud_s;
    int64_t         time_remaining;
    static int64_t  pbroker_proc_smaps_sent_time = 0;
    int64_t         local_time_lags_cloud_time_delta_us;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_system_stats *sys_stats = &(gs->sys_stats);
    int             res;

    static int64_t          sys_disk_total_bytes;
    static int              meminfo_read = 0;
    char                    uptime_str[32];
    //struct timeval tv = {0};

    if (tickle_me) zthread_heartbeat(tickle_me);

    if (0 == gs->private_broker_id) {
        ZPN_LOG(AL_ERROR, "Pbroker gid null");
        return;
    }

    /* If there was change in alt_cloud_name, we should reboot PSE */
    if(gs->alt_cloud_name_changed) {
        ZPN_LOG(LOG_CRIT, "Listener alt_cloud_name changed, restarting PSE");
        sleep(2);
        exit(0);
    }

    if(gs->rdir_alt_cloud_name_changed) {
        ZPN_LOG(LOG_CRIT, "Redirect alt_cloud_name changed, restarting PSE");
        sleep(2);
        exit(0);
    }

    /* Reboot the PSE if the feature config val changes */
    if(gs->alt_cloud_feature_state_initialised && gs->alt_cloud_feature_state_changed) {
        ZPN_LOG(LOG_CRIT, "alt_cloud feature flag value changed to %" PRId64 " , restarting PSE", gs->alt_cloud_feature_state);
        sleep(2);
        exit(0);
    }

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_disk_free_bytes_for_non_root_user(&sys_stats->free_disk_bytes_for_non_root_user)) {
        ZPN_LOG(AL_INFO, "Could not get the disk usage info");
    }

#ifdef __linux__
    if (gs->cgroup_version) {
        //Containers
        zpn_pbroker_system_get_cpu_util_from_cgroups(sys_stats, gs->cgroup_version);

        if (zpath_system_get_system_and_process_memory_util_percentage_from_cgroups(&sys_stats->system_mem_util, &sys_stats->process_mem_util, gs->cgroup_version)) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            PBROKER_LOG(AL_INFO, "Could not get the Memory usage info");
        }

        if (zpn_system_get_memory_usage_info_from_cgroups(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem,
                                                          &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem,
                                                          &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem,
                                                          gs->cgroup_version) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            PBROKER_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    } else
#endif
    {
        //VMs
        zpn_pbroker_system_get_cpu_util(sys_stats);

        if (zpath_system_get_system_and_process_memory_util_percentage(&sys_stats->system_mem_util, &sys_stats->process_mem_util)) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            PBROKER_LOG(AL_INFO, "Could not get the Memory usage info");
        }

        if (zpn_system_get_memory_usage_info(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem, &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem, &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            PBROKER_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    }

    zpath_system_get_udp_socket_util(&sys_stats->udp4_port_util, &sys_stats->udp6_port_util);
    zpath_system_get_tcp_socket_util(&sys_stats->tcp4_port_util, &sys_stats->tcp6_port_util);

    zpath_system_get_fd_util(&sys_stats->sys_fd_util, &sys_stats->proc_fd_util);
    zpath_system_get_fd_in_use(&sys_stats->system_fd_in_use, &sys_stats->process_fd_in_use);
    zpath_system_get_tcp_socket_inuse(&sys_stats->num_system_tcpv4_socket_inuse, &sys_stats->num_system_tcpv6_socket_inuse);
    zpath_system_get_udp_socket_inuse(&sys_stats->num_system_udpv4_socket_inuse, &sys_stats->num_system_udpv6_socket_inuse);

    pbroker_state_get_uptime_str(uptime_str, sizeof(uptime_str));

    if (meminfo_read == 0) {
        zpath_system_get_disk_info(NULL, NULL, &sys_disk_total_bytes);
        meminfo_read = 1;
    }

    ZPN_LOG(AL_NOTICE, "-------- Status:ID=%ld:Name=%s:Ver=%s:Mem(System|Process)=%d%%|%d%%:"
                       "Mem_abs(MemTotal|MemFree|SwapTotal|SwapFree|SysUsed|ProcessUsed)= %" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB :"
                       "Disk (Avail|Total)=%.2fGB|%.2fGB:CPU(Util|Steal|Configured|Avail)=%d%%|%d%%|%d|%d:DispState Eval(Max|Avg)=%"PRIu64"us|%"PRIu64"us:Uptime=%s --------",
            (long)gs->private_broker_id,
            pbroker_get_configured_name(),
            ZPATH_VERSION,
            sys_stats->system_mem_util,
            sys_stats->process_mem_util,
            sys_stats->memtotal_abs_mem, sys_stats->memfree_abs_mem, sys_stats->swaptotal_abs_mem, sys_stats->swapfree_abs_mem, sys_stats->system_used_abs_mem, sys_stats->process_used_abs_mem,
            (double)sys_stats->free_disk_bytes_for_non_root_user / ZPN_SYSTEM_GB_TO_BYTES,
            (double)sys_disk_total_bytes / ZPN_SYSTEM_GB_TO_BYTES,
            sys_stats->cpu_util,
            sys_stats->cpu_steal_perc,
			zpn_private_broker_state_get_configured_cpus(),
			zpn_private_broker_state_get_available_cpus(),
            zpn_dispatcher_get_max_eval_time_us(),
            zpn_dispatcher_get_avg_eval_time_us(),
            uptime_str);

    /* cookie == NULL makes sure that we don't call this routine at the init time */
    if (cookie == NULL) {
        char* thread_cpu_usage_log = zthread_get_thread_cpu_usage_log();
        if (thread_cpu_usage_log != NULL) {
            ZPN_LOG(AL_NOTICE, "-------- PSE thread CPU usage: %s", thread_cpu_usage_log);
            ZPN_FREE(thread_cpu_usage_log);
        }
    }

    if (pbroker_get_swap_config()) {
        ZPN_LOG(AL_NOTICE, "Swap partition is found to be configured,"
			   " Zscaler does not recommend swap config as this could have impact on data transfer rate");
    }

    /* Certificate expiration */
    now_cloud_s = zpn_cloud_adjusted_epoch_s();
    time_remaining = gs->cert_validity_end_time_cloud_s - now_cloud_s;
    ZPN_SYSTEM_ASSERT(gs->cert_validity_end_time_cloud_s, 1, "unexpected code path, please report to customer support team");
    if (now_cloud_s > gs->cert_force_re_enroll_time_cloud_s) {
        ZPN_LOG(AL_CRITICAL, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds, restarting to re-enroll",
                time_remaining / 86400, (time_remaining % 86400) / 3600, ((time_remaining % 86400) % 3600) / 60,
                ((time_remaining % 86400) % 3600) % 60);
        sleep(1);
        exit(1);
    } else {
        ZPN_LOG(AL_NOTICE, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds",
                time_remaining / 86400, (time_remaining % 86400) / 3600, ((time_remaining % 86400) % 3600) / 60,
                ((time_remaining % 86400) % 3600) % 60);
    }

    if (gs->auto_upgrade_disabled) {
        ZPN_LOG(AL_NOTICE, "Auto Upgrade disabled, currently at version(%s)", ZPATH_VERSION);
    }

    if (fohh_proxy_hostname) {
        ZPN_LOG(AL_NOTICE, "Using proxy server %s:%"PRIu16, fohh_proxy_hostname, fohh_proxy_port);
    }

    pbroker_cfg_log_status();
    pbroker_rcfg_log_status();
    pbroker_control_log_status();
    pbroker_log_tx_log_status();
    pbroker_stats_log_status();
    zpn_pbroker_data_connection_stats_log_status();
    pbroker_alt_cloud_status();
    zpn_private_broker_features_fproxy_log_status();

    // TODO - Comming later
    //pbroker_data_log_status();
    //pbroker_rpc_log_status();

    /* Clock skew */
    local_time_lags_cloud_time_delta_us = zpn_private_broker_get_cloud_time_delta_us();
    if (local_time_lags_cloud_time_delta_us < 0) {
        ZPN_LOG(AL_NOTICE, "Time skew: local time is ahead of cloud time by %ld.%06lds",
                (long)(0 - local_time_lags_cloud_time_delta_us) / 1000000,
                (long)(0 - local_time_lags_cloud_time_delta_us) % 1000000);
    } else {
        ZPN_LOG(AL_NOTICE, "Time skew: local time lags cloud time by %ld.%06lds",
                (long)local_time_lags_cloud_time_delta_us / 1000000, (long)local_time_lags_cloud_time_delta_us % 1000000);
    }

    res = pbroker_get_num_hw_id_changed();
    if (res) {
        int i;
        int64_t *time_arr = pbroker_get_hw_id_changed_time_us();
        ZPN_DEBUG_ASSISTANT("Detected %d device(s) id changed, note that the next connector process restart will fail", res);
        for (i = 0; i < res; i++) {
            char time_str[128] = { 0 };
            int64_t changed_time_us = time_arr[i];
            argo_log_gen_time(changed_time_us, time_str, sizeof(time_str), 0, 1);
            ZPN_DEBUG_ASSISTANT("%d/%d device id changed around time: %s", i + 1, res, time_str);
        }
    }

    zpn_system_pbroker_monitor(sys_stats);

    //mmdb stats
    mmdb_try++;
    if(prev_ts && mmdb_try >= NUM_MMDB_TRY){
        int64_t curr_ts = epoch_us();
        double diff_ts = (double)((curr_ts - prev_ts)/(1000000.0f));
        int64_t diff_geo = query_stats.num_geo_queries - prev_geo_cnt;
        int64_t diff_isp = query_stats.num_isp_queries - prev_isp_cnt;
        query_stats.geo_query_rate = (double)diff_geo/diff_ts;
        query_stats.isp_query_rate = (double)diff_isp/diff_ts;
        prev_ts = curr_ts;
        prev_geo_cnt = query_stats.num_geo_queries;
        prev_isp_cnt = query_stats.num_isp_queries;
        mmdb_try = 0;
        ZPN_LOG(AL_NOTICE,"Isp_queries:%"PRIu64" isp_query_rate:%f  Geo_queries:%"PRIu64" Geo_query_rate:%f", diff_isp, query_stats.isp_query_rate, diff_geo, query_stats.geo_query_rate);
    }else if(!prev_ts){
        prev_ts = epoch_us();
        prev_geo_cnt = query_stats.num_geo_queries;
        prev_isp_cnt = query_stats.num_isp_queries;
    }

    /*
     * If we try to send stats or event log before the fohh is connected, it will get dropped (think fohh queue
     * flush) when it get connected. But smaps is a important baseline to miss. So lets make sure, we do it the first
     * time only when it is connected. Also, we are worried only about broker control connection here as that is
     * the the only one which is interested in the stats of the connector. Dirty hack, but for now..
     */
    if (pbroker_broker_control_is_connected()) {
        if ((now_cloud_s - pbroker_proc_smaps_sent_time) > PBROKER_CONTROL_TX_STATS_PROC_SMAPS_TIMEPERIOD_SEC) {
            pbroker_proc_smaps_sent_time = now_cloud_s;
#ifdef __linux__
            /*
             * Make sure CONFIG_PROC_PAGE_MONITOR is enabled in the linux kernel.
             */
            ARGO_STATS_LOG_FILE_CONTENTS(argo_log_get("statistics_log"), "proc_smaps", "/proc/self/smaps");
#endif
        }
    }
}

int
pbroker_stats_monitor_init(void)
{
    int res;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    pbroker_init_cert_validity_counters(FILENAME_CERT);

    res = zpn_system_init(ZPN_SYSTEM_THRESHOLD_TYPE_PBROKER,
                          zpn_private_broker_cloud_adjusted_epoch_us,
                          zpath_assert,
                          is_pbroker_dev_environment,
                          pbroker_is_ready,
                          pbroker_get_cpu_util,
                          pbroker_get_cpu_steal_perc,
                          pbroker_get_system_mem_util,
                          pbroker_get_process_mem_util,
                          zpn_private_broker_state_get_available_cpus,
                          gs->pb_start_time_m_s);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init system module");
        return res;
    }

    res = zpn_pbroker_monitor_config_override_desc_register();
    if(res) {
        ZPN_LOG(AL_ERROR, "Could not register pbroker_monitor config override");
        return res;
    }

    res = pbroker_malloc_trim_thread_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init pbroker_malloc_trim_thread_init");
        return res;
    }

    res = pbroker_monitor_thread_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init pbroker_monitor_thread_init");
        return res;
    }

    res = zpn_sys_stats_init_keys();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_sys_stats_init");
        return res;
    }

    return res;
}

uint16_t pbroker_get_cpu_util(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return (gs->sys_stats.cpu_util);
}

uint16_t pbroker_get_cpu_steal_perc(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return (gs->sys_stats.cpu_steal_perc);
}

int pbroker_get_system_mem_util(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return (gs->sys_stats.system_mem_util);
}

int pbroker_get_process_mem_util(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return (gs->sys_stats.process_mem_util);
}
