/*
 * zpn_private_broker_site.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include <stddef.h>
#include <stdio.h>
#include <stdatomic.h>
#include <pthread.h>
#include <arpa/inet.h>
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_site.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn/zpn_broker_client.h"

#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_pbroker_sitec_control.h"
#include "zpn/zpn_private_broker_site_config.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_rpc.h"

static const char* INVALID_OFFLINE_DOMAIN = (const char*)1;

extern struct zpn_firedrill_stats g_pb_firedrill_stats_obj;

#define DEFINE_SITE_FLIP_TRIGGER(X) \
    X(SITE_FLIP_BY_INIT_DONE, "PSE is ready"), \
    X(SITE_FLIP_BY_SITE_AVAILABILITY, "site availability"), \
    X(SITE_FLIP_BY_COMMAND, "required by command"), \
    X(SITE_FLIP_BY_SITE_GID_ZERO, "PSE is detached from site"), \
    X(SITE_FLIP_BY_SITE_GID_CHANGED, "PSE is attached to a new site"), \
    X(SITE_FLIP_BY_SITE_CONFIG_CHANGED, "ddil config is changed"), \

typedef enum site_flip_trigger_s {
#undef X
#define X(a, b)     a
    DEFINE_SITE_FLIP_TRIGGER(X)
} site_flip_trigger_t;

#define DEFINE_SITEC_PREFERENCE(X) \
    X(SITEC_PREFER_TO_DEFAULT, "default"), \
    X(SITEC_PREFER_TO_SITEC, "pcc/sitec (by curl)"), \
    X(SITEC_PREFER_TO_BROKER, "broker (by curl)"), \
    X(SITEC_PREFER_TO_BROKER_ONLY, "broker only (by curl)"), \

typedef enum sitec_preference_s {
#undef X
#define X(a, b)     a
    DEFINE_SITEC_PREFERENCE(X)
} sitec_preference_t;

static const char* sitec_preference_str[] = {
#undef X
#define X(a, b)     [a] = b
    DEFINE_SITEC_PREFERENCE(X)
};

#define DEFINE_ALLOW_C2SITE(X) \
    X(ALLOW_C2SITE_DEFAULT, "default"), \
    X(ALLOW_C2SITE_NO, "disallow (by curl)"), \
    X(ALLOW_C2SITE_YES, "allow (by curl)"), \

typedef enum allow_c2site_s {
#undef X
#define X(a, b)     a
    DEFINE_ALLOW_C2SITE(X)
} allow_c2site_t;

static const char* allow_c2site_str[] = {
#undef X
#define X(a, b)     [a] = b
    DEFINE_ALLOW_C2SITE(X)
};

typedef struct site_flip_event_record_s {
    sitec_preference_t sitec_preferred;
    allow_c2site_t allow_c2site;
    uint32_t sitec_reachable;
    uint32_t sitec_total;
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1];
    time_t timestamp;
    site_flip_trigger_t trigger;
} site_flip_event_record_t;

#define SITE_MAX_EVENT_RECORD   64
typedef struct site_flip_event_history_s {
    uint64_t times;             // how many events has ever been received
    int cur;                    // next record
    site_flip_event_record_t records[SITE_MAX_EVENT_RECORD];

} site_flip_event_history_t;

#define SITE_FLIP_RDLOCK()      ZPATH_RWLOCK_RDLOCK(&g_site_flip_lock, __FILE__, __LINE__)
#define SITE_FLIP_WRLOCK()      ZPATH_RWLOCK_WRLOCK(&g_site_flip_lock, __FILE__, __LINE__)
#define SITE_FLIP_UNLOCK()      ZPATH_RWLOCK_UNLOCK(&g_site_flip_lock, __FILE__, __LINE__)
static zpath_rwlock_t g_site_flip_lock;
static struct event *g_site_monitor_flip_ev_handler = NULL;

typedef struct site_flip_state_s {
    int site_is_active;
    sitec_preference_t sitec_preferred;
    allow_c2site_t allow_c2site;
    uint8_t probe_fail;
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1];
    site_flip_event_history_t event_history;
    struct fohh_connection *sitec_prober;
} site_flip_state_t;

static site_flip_state_t g_site_flip_state;

static _Atomic ZPN_ATOMIC_UINT32 site_pbctl_to_broker_status = 0;
static _Atomic ZPN_ATOMIC_UINT32 site_pbctl_to_sitec_status = 0;

#define SITE_CONN_RDLOCK()      ZPATH_RWLOCK_RDLOCK(&g_site_conn_lock, __FILE__, __LINE__)
#define SITE_CONN_WRLOCK()      ZPATH_RWLOCK_WRLOCK(&g_site_conn_lock, __FILE__, __LINE__)
#define SITE_CONN_UNLOCK()      ZPATH_RWLOCK_UNLOCK(&g_site_conn_lock, __FILE__, __LINE__)
static zpath_rwlock_t g_site_conn_lock;

typedef struct site_fohh_client_s {
    LIST_ENTRY(site_fohh_client_s) list_entry;
    struct fohh_connection *f_conn;
    const char *label;
    char remote_host_name[FOHH_MAX_NAMELEN];
    char sni_service_name[FOHH_MAX_NAMELEN];
} site_fohh_client_t;

static LIST_HEAD(site_fohh_clients_head, site_fohh_client_s) g_site_fohh_clients = {};

#define SITEC_CONTROL_RDLOCK()  ZPATH_RWLOCK_RDLOCK(&g_sitec_control_lock, __FILE__, __LINE__)
#define SITEC_CONTROL_WRLOCK()  ZPATH_RWLOCK_WRLOCK(&g_sitec_control_lock, __FILE__, __LINE__)
#define SITEC_CONTROL_UNLOCK()  ZPATH_RWLOCK_UNLOCK(&g_sitec_control_lock, __FILE__, __LINE__)
static zpath_rwlock_t g_sitec_control_lock;

#define SITE_PBLOAD_RDLOCK()    ZPATH_RWLOCK_RDLOCK(&g_site_pbload_lock, __FILE__, __LINE__)
#define SITE_PBLOAD_WRLOCK()    ZPATH_RWLOCK_WRLOCK(&g_site_pbload_lock, __FILE__, __LINE__)
#define SITE_PBLOAD_UNLOCK()    ZPATH_RWLOCK_UNLOCK(&g_site_pbload_lock, __FILE__, __LINE__)
static zpath_rwlock_t g_site_pbload_lock;
static struct event *g_site_monitor_pbload_ev_handler = NULL;
static struct event *g_site_monitor_pbload_conn_ev_handler = NULL;

#define DEFINE_SITEC_PBLOAD_ACTION(X) \
    X(SITEC_PBLOAD_ACTION_NOP, "nop"), \
    X(SITEC_PBLOAD_ACTION_ADD, "add"), \
    X(SITEC_PBLOAD_ACTION_REMOVE, "remove"), \
    X(SITEC_PBLOAD_ACTION_UPDATE, "update"), \

typedef enum sitec_pbload_action_s {
#undef X
#define X(a, b)     a
    DEFINE_SITEC_PBLOAD_ACTION(X)
} sitec_pbload_action_t;

static const char* sitec_pbload_action_str[] = {
#undef X
#define X(a, b)     [a] = b
    DEFINE_SITEC_PBLOAD_ACTION(X)
};

#define MAX_SITEC_NAME_LEN      128
typedef struct site_pbload_sitec_entry_s {
    LIST_ENTRY(site_pbload_sitec_entry_s) list_entry;
    int64_t sitec_gid;
    int64_t site_gid;
    char name[MAX_SITEC_NAME_LEN];
    struct fohh_connection *f_conn;
    uint8_t action;
} site_pbload_sitec_entry_t;

static LIST_HEAD(site_pbload_sitec_head, site_pbload_sitec_entry_s) g_site_pbload_sitec_list = {};
static _Atomic ZPN_ATOMIC_UINT32 site_pbload_conn_total = 0, site_pbload_conn_connected = 0;

#define REMEDIATION_BIT_FLAG_CHECK_SITE         0x01
#define REMEDIATION_BIT_FLAG_CHECK_DDIL_CFG     0x02
#define REMEDIATION_BIT_FLAG_CHECK_PBLOAD_CONN  0x04
static _Atomic ZPN_ATOMIC_UINT32 remediation_bit_flag = 0;
struct event *g_site_remediation_ev_handler = NULL;


static int zpn_private_broker_site_update_sitec_list();

static const char* get_sitec_preferred_string(sitec_preference_t sitec_preferred)
{
    return sitec_preferred == SITEC_PREFER_TO_DEFAULT
        ? (zpn_private_broker_get_sitec_preferred_with_lock() ? "pcc/sitec" : "broker")
        : sitec_preference_str[sitec_preferred];
}

static const char* get_allow_c2site_string(allow_c2site_t allow_c2site)
{
    return allow_c2site == ALLOW_C2SITE_DEFAULT
        ? (zpn_private_broker_get_allow_c2site_with_lock() ? "allow" : "disallow")
        : allow_c2site_str[allow_c2site];
}

static void get_pbload_connection_count(uint32_t *total, uint32_t *connected)
{
    ZPN_ATOMIC_LOAD(&site_pbload_conn_total, *total);
    ZPN_ATOMIC_LOAD(&site_pbload_conn_connected, *connected);
}

static int64_t get_sitec_group_by_sitec_gid(int64_t sitec_gid, struct zpn_site_controller_group **group)
{
    int res;
    struct zpn_site_controller_to_group *relation;
    size_t count;

    *group = NULL;

    count = 1;
    res = zpn_sitec_to_group_get_by_sitec_gid(sitec_gid, &relation, &count, 1, NULL, NULL, 0);
    if (res || !relation || relation->deleted) {
        return 0;
    }

    count = 1;
    res = zpn_sitec_group_get_by_gid(relation->site_controller_group_gid, group, &count, 0, NULL, NULL, 0);
    if (res || !(*group) || (*group)->deleted || !(*group)->enabled) {
        return 0;
    }

    return (*group)->site_gid;

}

static void zpn_private_broker_monitor_pbload_conn_periodically(evutil_socket_t sock, short flags, void *cookie)
{
    site_pbload_sitec_entry_t *cur, *tmp;

    uint32_t total = 0, connected = 0;

    SITE_PBLOAD_RDLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        if (cur->f_conn && fohh_get_state(cur->f_conn) == fohh_connection_connected) {
            connected++;
        }
        total++;
    }

    SITE_PBLOAD_UNLOCK();

    ZPN_ATOMIC_STORE(&site_pbload_conn_total, total);
    ZPN_ATOMIC_STORE(&site_pbload_conn_connected, connected);
}

static int zpn_private_broker_flip_fohh_servers(int enable_c2site, const char* domain_in_use, const char* offline_domain)
{
    struct fohh_generic_server *sni_server = zpn_broker_get_sni_server();
    char old_sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    char new_sni_str[ZPN_MAX_SNI_NAME_LEN + 1];

    snprintf(old_sni_str, sizeof(old_sni_str), "c2site.%s", domain_in_use);
    snprintf(new_sni_str, sizeof(new_sni_str), "c2site.%s", offline_domain);

    if (strncmp(old_sni_str, new_sni_str, ZPN_MAX_DOMAIN_NAME_LEN)) {
        if (fohh_generic_server_re_register(sni_server, old_sni_str, new_sni_str, 1) != FOHH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to re-register SNI from %s to %s", old_sni_str, new_sni_str);
            return ZPN_RESULT_ERR;
        }
        ZPN_LOG(AL_INFO, "Re-register SNI, from %s to %s", old_sni_str, new_sni_str);
    }

    int res = fohh_generic_server_set_domain_disabled(sni_server, new_sni_str, 1, !enable_c2site);
    if (res != FOHH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Failed to %s SNI '%s' for fohh server, result %d",
                enable_c2site ? "enable" : "disable",
                new_sni_str, res);
        return res;
    }

    if (offline_domain[0]) {
        ZPN_LOG(AL_DEBUG, "%s SNI '%s' for fohh server", enable_c2site ? "Enable" : "Disable", new_sni_str);
    }

    return res;
}

static void zpn_private_broker_flip_fohh_clients(const char* domain)
{
    site_fohh_client_t *client, *tmp;
    const char* domain_name = domain ? domain : "<original broker>";

    int res;
    int redirect_total = 0, redirect_succ = 0, redirect_fail = 0;

    SITE_CONN_WRLOCK();

    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        redirect_total++;

        ZPN_LOG(AL_INFO, "%s: flip %s to domain '%s'", fohh_description(client->f_conn),
                client->label, domain_name);

        res = fohh_connection_update_site_offline_domain(client->f_conn, (char*)domain);

        if (res == FOHH_RESULT_NO_ERROR) {
            redirect_succ++;
        } else {
            redirect_fail++;
            ZPN_LOG(AL_ERROR, "%s: failed to flip %s to new domain '%s'",
                fohh_description(client->f_conn), client->label, domain_name);
        }
    }

    SITE_CONN_UNLOCK();

    ZPN_LOG(AL_INFO, "flip fohh to new domain '%s': total %d, succ %d, failed %d",
            domain_name, redirect_total, redirect_succ, redirect_fail);
}

static int zpn_private_broker_site_probe_conn_callback(struct fohh_connection *connection,
                                                       enum fohh_connection_state state,
                                                       void *cookie)
{
    if (state == fohh_connection_connected) {
        SITE_FLIP_WRLOCK();
        g_site_flip_state.probe_fail = 0;
        SITE_FLIP_UNLOCK();

        ZPN_LOG(AL_NOTICE, "%s - probe connection to sitec is connected successfully", fohh_description(connection));
    } else {
        SITE_FLIP_WRLOCK();
        g_site_flip_state.probe_fail = 1;
        SITE_FLIP_UNLOCK();

        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: probe connection to sitec DOWN: %s", fohh_description(connection), reason);
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_private_broker_site_probe_unblock_callback(struct fohh_connection *connection,
                                                          enum fohh_queue_element_type element_type,
                                                          void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

struct fohh_connection*
zpn_private_broker_site_create_probe_connection(const char *offline_domain, void *cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *f_conn = NULL;

    if (!offline_domain[0]) {
        ZPN_LOG(AL_ERROR, "Offline domain is not set, can't create probe connection");
        return NULL;
    }

    char broker_name[256];
    char sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    char stats_name[268];

    if (snprintf(broker_name, sizeof(broker_name), "%s.%s",
                ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain) >= sizeof(broker_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating sitesp_name");
    }

    if (snprintf(sni_name, sizeof(sni_name), "%ld.pbctl.%s",
                (long) gs->private_broker_id, offline_domain) >= sizeof(sni_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating sni_name");
    }

    snprintf(stats_name, sizeof(stats_name), "brk-ctl-%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME);

    f_conn = fohh_client_create(FOHH_WORKER_ZPN_PBCTL,
                                stats_name,
                                argo_serialize_binary,
                                fohh_connection_style_argo,
                                0,
                                cookie,
                                zpn_private_broker_site_probe_conn_callback,
                                NULL,
                                zpn_private_broker_site_probe_unblock_callback,
                                NULL,
                                broker_name,
                                sni_name,
                                offline_domain,
                                htons(ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT),
                                gs->self_to_private_ssl_ctx,
                                1,
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S);

    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Failed to create probe connection to %s", broker_name);
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Successfully created probe connection to %s", broker_name);

    fohh_set_sticky(f_conn, 1);
    fohh_connection_set_default_sni(f_conn, (char*)offline_domain);

    return f_conn;
}

static int zpn_private_broker_site_pbload_conn_callback(struct fohh_connection *connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%s - pbload connection to sitec is connected successfully", fohh_description(connection));
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: pbload connection to sitec DOWN: %s", fohh_description(connection), reason);
    }

    if (g_site_monitor_flip_ev_handler) {
        // event_active(g_site_monitor_flip_ev_handler, SITE_FLIP_BY_SITE_AVAILABILITY, 0);
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_private_broker_site_pbload_unblock_callback(struct fohh_connection *connection,
                                                           enum fohh_queue_element_type element_type,
                                                           void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static struct fohh_connection*
zpn_private_broker_site_create_pbload_connection(int64_t sitec_gid, void* cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *f_conn = NULL;

    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1] = {};
    zpn_private_broker_get_offline_domain_with_lock(offline_domain, sizeof(offline_domain));

    if (!offline_domain[0]) {
        ZPN_LOG(AL_ERROR, "Offline domain is not set, can't create pbload connection");
        return NULL;
    }

    char sitesp_name[256];
    char sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    char stats_name[268];

    if (snprintf(sitesp_name, sizeof(sitesp_name), "bcpsp-%"PRId64".%s", sitec_gid, offline_domain) >= sizeof(sitesp_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating sitesp_name");
    }

    if (snprintf(sni_name, sizeof(sni_name), "%ld.pbload.%s", (long) gs->private_broker_id, offline_domain) >= sizeof(sni_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating sni_name");
    }

    snprintf(stats_name, sizeof(stats_name), "pbload-%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME);

    f_conn = fohh_client_create(FOHH_WORKER_ZPN_PBCTL,
                                stats_name,
                                argo_serialize_binary,
                                fohh_connection_style_argo,
                                0,
                                cookie,
                                zpn_private_broker_site_pbload_conn_callback,
                                NULL,
                                zpn_private_broker_site_pbload_unblock_callback,
                                NULL,
                                sitesp_name,
                                sni_name,
                                offline_domain,
                                htons(ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT),
                                gs->self_to_private_ssl_ctx,
                                1,
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S);

    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Failed to create pbload connection to %s", sitesp_name);
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Successfully created pbload connection to %s", sitesp_name);

    fohh_set_sticky(f_conn, 1);
    fohh_set_max_backoff(f_conn, ZPN_PRIVATE_BROKER_CONN_MAX_BACKOFF_TIME_S);
    fohh_connection_set_default_sni(f_conn, (char*)offline_domain);

    return f_conn;
}

static void zpn_private_broker_monitor_flip_callback(evutil_socket_t sock, short flags, void *cookie)
{
    site_flip_trigger_t trigger = flags;

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *sitec_control = NULL;

    uint32_t sitec_total, sitec_reachable;
    get_pbload_connection_count(&sitec_total, &sitec_reachable);

    int site_is_active_in_use = zpn_private_broker_get_site_is_active_with_lock();
    char domain_in_use[ZPN_MAX_DOMAIN_NAME_LEN + 1] = {};
    zpn_private_broker_get_offline_domain_with_lock(domain_in_use, sizeof(domain_in_use));

    int site_is_active;
    sitec_preference_t sitec_preferred;
    allow_c2site_t allow_c2site;
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1] = {};

    SITE_FLIP_RDLOCK();
    site_is_active = g_site_flip_state.site_is_active;
    sitec_preferred = g_site_flip_state.sitec_preferred;
    allow_c2site = g_site_flip_state.allow_c2site;
    snprintf(offline_domain, sizeof(offline_domain), "%s", g_site_flip_state.offline_domain);
    SITE_FLIP_UNLOCK();

    ZPN_LOG(AL_INFO, "trigger %d, site %s=>%s, prefer to %s, %s non enrolled user, reachable sitec %u/%u, domain: '%s' => '%s'",
            trigger,
            site_is_active_in_use ? "active" : "inactive", site_is_active ? "active" : "inactive",
            get_sitec_preferred_string(sitec_preferred),
            get_allow_c2site_string(allow_c2site),
            sitec_reachable, sitec_total,
            domain_in_use, offline_domain);

    int enable_c2site = allow_c2site == ALLOW_C2SITE_DEFAULT
        ? zpn_private_broker_get_allow_c2site_with_lock()
        : allow_c2site == ALLOW_C2SITE_YES;

    zpn_private_broker_set_site_is_active(site_is_active);

    zpn_private_broker_flip_fohh_servers(enable_c2site, domain_in_use, offline_domain);

    if (sitec_preferred == SITEC_PREFER_TO_BROKER_ONLY || !site_is_active || offline_domain[0] == 0) {
        SITEC_CONTROL_WRLOCK();
        sitec_control = (gs->private_broker_state) ? gs->private_broker_state->sitec_control : NULL;
        if (sitec_control) {
            gs->private_broker_state->sitec_control = NULL;
            zpn_private_broker_site_deregister_fohh(sitec_control);
            fohh_connection_delete_async(sitec_control,
                    fohh_connection_incarnation(sitec_control), "domain is emptry");
        }
        SITEC_CONTROL_UNLOCK();

        zpn_private_broker_flip_fohh_clients(NULL);
    } else {
        SITEC_CONTROL_WRLOCK();
        if (gs->private_broker_state) {
            sitec_control = gs->private_broker_state->sitec_control;
            if (!sitec_control) {
                zpn_private_broker_sitec_control_conn_init(gs->private_broker_state, gs->private_broker_id, offline_domain);
            }
        }
        SITEC_CONTROL_UNLOCK();

        zpn_private_broker_flip_fohh_clients(offline_domain);
    }

    zpn_private_broker_set_offline_domain_with_lock(offline_domain);

    if (site_is_active_in_use != site_is_active || strcmp(domain_in_use, offline_domain)) {
        zpn_private_broker_site_update_sitec_list();
    }

    SITE_FLIP_WRLOCK();

    site_flip_event_record_t *record = &g_site_flip_state.event_history.records[g_site_flip_state.event_history.cur];

    record->sitec_preferred = sitec_preferred;
    record->allow_c2site = allow_c2site;
    record->sitec_reachable = sitec_reachable;
    record->sitec_total = sitec_total;
    snprintf(record->offline_domain, sizeof(record->offline_domain), "%s", offline_domain);
    record->timestamp = time(NULL);
    record->trigger = trigger;

    g_site_flip_state.event_history.cur = (g_site_flip_state.event_history.cur + 1) % SITE_MAX_EVENT_RECORD;
    g_site_flip_state.event_history.times++;

    SITE_FLIP_UNLOCK();
}

static void zpn_private_broker_monitor_pbload_callback(evutil_socket_t sock, short flags, void *cookie)
{
    site_pbload_sitec_entry_t *cur, *tmp;

    SITE_PBLOAD_WRLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        if (cur->action == SITEC_PBLOAD_ACTION_REMOVE) {
            if (cur->f_conn) {
                fohh_connection_delete_async(cur->f_conn,
                        fohh_connection_incarnation(cur->f_conn), "sitec config delete");
                cur->f_conn = NULL;
            }
            LIST_REMOVE(cur, list_entry);
            PBROKER_FREE(cur);
            cur = NULL;
        } else if (cur->action == SITEC_PBLOAD_ACTION_UPDATE) {
            if (cur->f_conn) {
                fohh_connection_delete_async(cur->f_conn,
                        fohh_connection_incarnation(cur->f_conn), "sitec config update");
                cur->f_conn = NULL;
            }
            cur->action = SITEC_PBLOAD_ACTION_ADD;
        }
        if (cur && cur->action == SITEC_PBLOAD_ACTION_ADD) {
            if (cur->site_gid <= 0) {
                struct zpn_site_controller_group *group = NULL;
                cur->site_gid = get_sitec_group_by_sitec_gid(cur->sitec_gid, &group);
            }

            if (zpn_private_broker_get_site_gid_with_lock()
                    && zpn_private_broker_get_site_is_active_with_lock()
                    && zpn_private_broker_has_offline_domain()
                    && cur->site_gid > 0) {
                cur->f_conn = zpn_private_broker_site_create_pbload_connection(cur->sitec_gid, NULL);
                if (!cur->f_conn) {
                    atomic_fetch_or(&remediation_bit_flag, REMEDIATION_BIT_FLAG_CHECK_PBLOAD_CONN);
                    ZPN_LOG(AL_ERROR, "Failed to create pbload connection to %s", cur->name);
                } else {
                    cur->action = SITEC_PBLOAD_ACTION_NOP;
                }
            } else {
                atomic_fetch_or(&remediation_bit_flag, REMEDIATION_BIT_FLAG_CHECK_PBLOAD_CONN);
                ZPN_LOG(AL_WARNING, "site gid of sitec %"PRId64" is not found, skip creating pbload connection",
                        cur->sitec_gid);
            }
        }
    }
    SITE_PBLOAD_UNLOCK();
}

static int zpn_private_broker_handle_config_change(
        int site_is_active,                     // 0: inactive, 1: active, -1: ignore
        int sitec_preferred,                    // 0: prefer to broker, 1: prefer to sitec, -1: ignore
        const char* offline_domain,             // NULL or "": turn off offline mode, INVALID_OFFLINE_DOMAIN: ignore
        int64_t reenroll_period,                // -1: ignore
        int allow_c2site,                       // 0: disallow c2site, 1: allow c2site, -1: ignore
        int is_switchtime_enabled,              // -1: ignore
        int64_t max_allowed_downtime_s,         // -1: ignore
        int64_t max_allowed_switchtime_s,       // -1: ignore
        site_flip_trigger_t trigger)
{
    if (!offline_domain) {
        offline_domain = "";
    }

    PBROKER_SITE_CONFIG_RDLOCK();
    int site_is_active_in_use = zpn_private_broker_get_site_is_active();
    int sitec_preferred_in_use = zpn_private_broker_get_sitec_preferred();
    const char* domain_in_use = zpn_private_broker_get_offline_domain();
    int64_t reenroll_period_in_use = zpn_private_broker_get_reenroll_period();
    int allow_c2site_in_use = zpn_private_broker_get_allow_c2site();
    int is_switchtime_enabled_in_use = zpn_private_broker_get_is_switchtime_enabled();
    int64_t max_allowed_downtime_s_in_use = zpn_private_broker_get_max_allowed_downtime_s();
    int64_t max_allowed_switchtime_s_in_use = zpn_private_broker_get_max_allowed_switchtime_s();
    PBROKER_SITE_CONFIG_UNLOCK();

    if (site_is_active < 0) {
        site_is_active = site_is_active_in_use;
    }
    if (sitec_preferred < 0) {
        sitec_preferred = sitec_preferred_in_use;
    }
    if (offline_domain == INVALID_OFFLINE_DOMAIN) {
        offline_domain = domain_in_use;
    }
    if (reenroll_period < 0) {
        reenroll_period = reenroll_period_in_use;
    }
    if (allow_c2site < 0) {
        allow_c2site = allow_c2site_in_use;
    }
    if (is_switchtime_enabled < 0) {
        is_switchtime_enabled = is_switchtime_enabled_in_use;
    }

    if (max_allowed_downtime_s < 0) {
        max_allowed_downtime_s = max_allowed_downtime_s_in_use;
    }
    if (max_allowed_switchtime_s < 0) {
        max_allowed_switchtime_s = max_allowed_switchtime_s_in_use;
    }

    ZPN_LOG(AL_INFO, "Handle config change: site active %d=>%d, sitec perfer %d=>%d, domain '%s'=>'%s', reenroll_period %ld=>%ld, allow c2site %d=>%d, is switchtime enabled %d=>%d, max_allowed_downtime_s %ld=>%ld, max_allowed_switchtime_s %ld=>%ld",
            site_is_active_in_use, site_is_active,
            sitec_preferred_in_use, sitec_preferred,
            domain_in_use, offline_domain,
            (long)reenroll_period_in_use, (long)reenroll_period,
            allow_c2site_in_use, allow_c2site,
            is_switchtime_enabled_in_use, is_switchtime_enabled,
            (long)max_allowed_downtime_s_in_use, (long)max_allowed_downtime_s,
            (long)max_allowed_switchtime_s_in_use, (long)max_allowed_switchtime_s);

    // Some of the input arguments will actively trigger the callbacks,
    // while some ones are waiting for to be used passively.
    // For the later ones, we just save them.
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_private_broker_set_sitec_preferred(sitec_preferred);
    zpn_private_broker_set_allow_c2site(allow_c2site);
    zpn_private_broker_set_reenroll_period(reenroll_period);
    zpn_private_broker_set_is_switchtime_enabled(is_switchtime_enabled);
    zpn_private_broker_set_max_allowed_downtime_s(max_allowed_downtime_s);
    zpn_private_broker_set_max_allowed_switchtime_s(max_allowed_switchtime_s);
    PBROKER_SITE_CONFIG_UNLOCK();

    if (site_is_active_in_use != site_is_active
            || sitec_preferred_in_use != sitec_preferred
            || strcmp(domain_in_use, offline_domain)
            || reenroll_period_in_use != reenroll_period
            || max_allowed_downtime_s_in_use != max_allowed_downtime_s) {
        zpn_pbroker_site_config_t config;

        config.site_is_active = site_is_active;
        config.sitec_preferred = sitec_preferred;
        snprintf(config.offline_domain, sizeof(config.offline_domain), "%s", offline_domain);
        config.reenroll_period = reenroll_period;
        config.max_allowed_downtime_s = max_allowed_downtime_s;

        zpn_private_broker_site_config_save(&config);
    }

    SITE_FLIP_WRLOCK();

    g_site_flip_state.site_is_active = site_is_active;

    if (offline_domain[0]) {
        snprintf(g_site_flip_state.offline_domain, sizeof(g_site_flip_state.offline_domain), "%s", offline_domain);
    } else {
        g_site_flip_state.offline_domain[0] = 0;
    }

    SITE_FLIP_UNLOCK();

    if (g_site_monitor_flip_ev_handler) {
        event_active(g_site_monitor_flip_ev_handler, trigger, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_cfg_site_gid(int64_t site_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (site_gid == zpn_private_broker_get_site_gid_with_lock()) {
        ZPN_LOG(AL_DEBUG, "site gid is not changed, ignore: %ld", (long)site_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_LOG(AL_INFO, "Received site gid notify: site gid %ld=>%ld",
            (long)zpn_private_broker_get_site_gid_with_lock(), (long)site_gid);

    zpn_private_broker_set_site_gid_with_lock(site_gid);

    if (site_gid) {
        struct zpn_site *site;
        struct zpn_ddil_config *ddil_config;
        size_t row_count = 1;
        int res = zpn_site_get_by_gid(site_gid,
                &site,
                1,
                NULL,
                NULL,
                0);
        if (res != ZPN_RESULT_NO_ERROR) {
            atomic_fetch_or(&remediation_bit_flag, REMEDIATION_BIT_FLAG_CHECK_SITE);
            ZPN_LOG(AL_INFO, "Failed to get site table by site_gid %"PRId64, site_gid);
            goto done;
        }

        res = zpn_ddil_config_get_by_customer_gid(gs->customer_id,
             &ddil_config,
             &row_count,
             NULL,
             0,
             0);
        if (res != ZPN_RESULT_NO_ERROR) {
            atomic_fetch_or(&remediation_bit_flag, REMEDIATION_BIT_FLAG_CHECK_DDIL_CFG);
            ZPN_LOG(AL_INFO, "Failed to get ddil config error: %s", zpath_result_string(res));
            goto done;
        }

        zpn_private_broker_handle_config_change(
                    !site->deleted && site->enabled,
                    -1,
                    ddil_config->offline_domain,
                    site->reenroll_period,
                    ddil_config->new_user_support,
                    ddil_config->is_switchtime_enabled,
                    ddil_config->max_allowed_downtime_s,
                    ddil_config->max_allowed_switchtime_s,
                    SITE_FLIP_BY_SITE_GID_CHANGED);
    } else {
        // depart from site controller
        zpn_private_broker_handle_config_change(
                0,
                0,
                NULL,
                0,
                0,
                0,
                0,
                0,
                SITE_FLIP_BY_SITE_GID_ZERO);
    }

done:
    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_cfg_site_config(int valid, int64_t reenroll_period, int sitec_preferred)
{
    zpn_private_broker_handle_config_change(valid, sitec_preferred, INVALID_OFFLINE_DOMAIN, reenroll_period, -1, -1, -1, -1, SITE_FLIP_BY_SITE_CONFIG_CHANGED);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_cfg_ddil_config(struct zpn_ddil_config *ddil_config)
{

    return zpn_private_broker_handle_config_change(
            -1, -1, ddil_config->offline_domain, -1, ddil_config->new_user_support,
            ddil_config->is_switchtime_enabled,
            ddil_config->max_allowed_downtime_s, ddil_config->max_allowed_switchtime_s,
            SITE_FLIP_BY_SITE_CONFIG_CHANGED);
}

static int zpn_private_broker_site_update_sitec_list()
{
    site_pbload_sitec_entry_t *cur, *tmp;

    ZPN_LOG(AL_INFO, "Rebuild pbload connections to sitec list");

    SITE_PBLOAD_WRLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        cur->action = SITEC_PBLOAD_ACTION_UPDATE;
    }

    SITE_PBLOAD_UNLOCK();

    if (g_site_monitor_pbload_ev_handler) {
        event_active(g_site_monitor_pbload_ev_handler, 0, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_cfg_sitec_config(struct zpn_site_controller *sitec)
{
    site_pbload_sitec_entry_t *cur, *tmp;
    int found = 0;

    ZPN_LOG(AL_INFO, "Received sitec config notify: gid %"PRId64", name %s, deleted %ld, enabled %ld",
            sitec->gid, sitec->name, (long)sitec->deleted, (long)sitec->enabled);

    SITE_PBLOAD_WRLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        if (cur->sitec_gid == sitec->gid) {
            if (sitec->deleted || !sitec->enabled || !sitec->gid) {
                cur->action = SITEC_PBLOAD_ACTION_REMOVE;
                ZPN_LOG(AL_DEBUG, "Get sitec gid %"PRId64", name %s, to be deleted/disabled",
                        cur->sitec_gid, cur->name);
            } else if (strncmp(cur->name, sitec->name, MAX_SITEC_NAME_LEN)) {
                snprintf(cur->name, MAX_SITEC_NAME_LEN, "%s", sitec->name);
                cur->action = SITEC_PBLOAD_ACTION_UPDATE;
                ZPN_LOG(AL_DEBUG, "Get sitec gid %"PRId64", name %s, to be updated",
                        cur->sitec_gid, cur->name);
            } else {
                ZPN_LOG(AL_DEBUG, "Get sitec gid %"PRId64", name %s, nothing to do",
                        cur->sitec_gid, cur->name);
            }
            found = 1;
            break;
        }
    }

    if (!found && !sitec->deleted && sitec->enabled) {
        cur = PBROKER_MALLOC(sizeof(site_pbload_sitec_entry_t));
        if (cur) {
            cur->sitec_gid = sitec->gid;
            cur->site_gid = -1;
            snprintf(cur->name, MAX_SITEC_NAME_LEN, "%s", sitec->name);
            cur->f_conn = NULL;
            cur->action = SITEC_PBLOAD_ACTION_ADD;
            LIST_INSERT_HEAD(&g_site_pbload_sitec_list, cur, list_entry);
        } else {
            ZPN_LOG(AL_ERROR, "Failed to create sitec pbload entry for %s", sitec->name);
        }
    }

    SITE_PBLOAD_UNLOCK();

    if (g_site_monitor_pbload_ev_handler) {
        event_active(g_site_monitor_pbload_ev_handler, 0, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_cfg_sitec_group_config(struct zpn_site_controller_group *sitec_group)
{
    site_pbload_sitec_entry_t *cur, *tmp;
    struct zpn_site_controller_group *group = NULL;
    int64_t site_gid;
    int dirty = 0;

    SITE_PBLOAD_WRLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        site_gid = get_sitec_group_by_sitec_gid(cur->sitec_gid, &group);

        if (cur->site_gid != site_gid) {
            cur->site_gid = site_gid;
            cur->action = SITEC_PBLOAD_ACTION_UPDATE;
            dirty = 1;
        }
    }

    SITE_PBLOAD_UNLOCK();

    if (dirty && g_site_monitor_pbload_ev_handler) {
        event_active(g_site_monitor_pbload_ev_handler, 0, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_pbctl_to_broker_status(int connected)
{
    ZPN_ATOMIC_STORE(&site_pbctl_to_broker_status, connected);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_notify_pbctl_to_sitec_status(int connected)
{
    ZPN_ATOMIC_STORE(&site_pbctl_to_sitec_status, connected);

    return ZPN_RESULT_NO_ERROR;
}

void zpn_private_broker_site_xmit_pbinfo(struct zpn_private_broker_load *load, struct zpn_pbroker_status_report *pb_report)
{
    int res;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    site_pbload_sitec_entry_t *cur, *tmp;
    struct fohh_connection *sitec_control = NULL;

    SITE_PBLOAD_RDLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        if (!cur->f_conn) {
            continue;
        }
        res = fohh_argo_serialize(cur->f_conn,
                zpn_private_broker_load_description,
                load,
                0,
                fohh_queue_element_type_mission_critical);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send load to: %s, error %s",
                    fohh_description(cur->f_conn), zpn_result_string(res));
        } else {
            PBROKER_DEBUG_SITE("pbload is sent out to %s", fohh_description(cur->f_conn));
        }
    }

    SITE_PBLOAD_UNLOCK();

    SITEC_CONTROL_RDLOCK();
    sitec_control = (gs->private_broker_state) ? gs->private_broker_state->sitec_control : NULL;
    if (sitec_control) {
        /* Send status report via RPC */
        res = fohh_argo_serialize(sitec_control,
                                  zpn_pbroker_status_report_description,
                                  pb_report,
                                  0,
                                  fohh_queue_element_type_control);
        if (res) {
            PBROKER_DEBUG_SITE("Could not send pbroker status report to sitec: %s", zpn_result_string(res));
        } else {
            char dump[2000];
            argo_structure_dump(zpn_pbroker_status_report_description, pb_report, dump, sizeof(dump), NULL, 0);
            PBROKER_DEBUG_SITE("Sending zpn_pbroker_status_report: %s", dump);
        }
    }
    SITEC_CONTROL_UNLOCK();
}

static void zpn_private_broker_monitor_remediation_periodically(evutil_socket_t sock, short flags, void *cookie)
{
    uint64_t flag;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPN_ATOMIC_LOAD(&remediation_bit_flag, flag);

    if ((flag & REMEDIATION_BIT_FLAG_CHECK_SITE) || (flag & REMEDIATION_BIT_FLAG_CHECK_DDIL_CFG)) {
        int64_t site_gid = zpn_private_broker_get_site_gid_with_lock();
        struct zpn_site *site;
        struct zpn_ddil_config *ddil_config;
        size_t row_count = 1;
        int res = zpn_site_get_by_gid(site_gid,
                &site,
                1,
                NULL,
                NULL,
                0);
        if (res != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_INFO, "Failed to get site table by site_gid %"PRId64, site_gid);
            goto done;
        }

        res = zpn_ddil_config_get_by_customer_gid(gs->customer_id,
             &ddil_config,
             &row_count,
             NULL,
             0,
             0);
        if (res != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_INFO, "Failed to get ddil config error: %s", zpath_result_string(res));
            goto done;
        }

        if (flag & REMEDIATION_BIT_FLAG_CHECK_SITE) {
            atomic_fetch_and(&remediation_bit_flag, ~REMEDIATION_BIT_FLAG_CHECK_SITE);
        }

        if (flag & REMEDIATION_BIT_FLAG_CHECK_DDIL_CFG) {
            atomic_fetch_and(&remediation_bit_flag, ~REMEDIATION_BIT_FLAG_CHECK_DDIL_CFG);
        }

        zpn_private_broker_handle_config_change(
                    !site->deleted && site->enabled,
                    -1,
                    ddil_config->offline_domain,
                    site->reenroll_period,
                    ddil_config->new_user_support,
                    ddil_config->is_switchtime_enabled,
                    ddil_config->max_allowed_downtime_s,
                    ddil_config->max_allowed_switchtime_s,
                    SITE_FLIP_BY_SITE_GID_CHANGED);
    } else if (flag & REMEDIATION_BIT_FLAG_CHECK_PBLOAD_CONN) {
        atomic_fetch_and(&remediation_bit_flag, ~REMEDIATION_BIT_FLAG_CHECK_PBLOAD_CONN);
        if (g_site_monitor_pbload_ev_handler) {
            event_active(g_site_monitor_pbload_ev_handler, 0, 0);
        }
    }

done:
    return;
}

int zpn_private_broker_site_register_fohh(struct fohh_connection* connection, const char* label,
        const char *remote_host_name, const char *sni_service_name, const char * sni_suffix,
        int stick_to_sitec, int current_to_sitec)
{
    site_fohh_client_t *client, *tmp;

    SITE_CONN_WRLOCK();

    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        if (client->f_conn == connection) {
            SITE_CONN_UNLOCK();
            return ZPN_RESULT_ERR;
        }
    }

    /*
     * the max timeout to flip to broker is defined as 60s, so make sure the backoff is smaller
     * that it so that sitec connection has the chance to be retried.
     */
    fohh_set_max_backoff(connection, ZPN_PRIVATE_BROKER_CONN_MAX_BACKOFF_TIME_S);

    client = PBROKER_MALLOC(sizeof(site_fohh_client_t));
    client->f_conn = connection;
    client->label = label;
    snprintf(client->remote_host_name, sizeof(client->remote_host_name), "%s", remote_host_name);
    snprintf(client->sni_service_name, sizeof(client->sni_service_name), "%s", sni_service_name);
    LIST_INSERT_HEAD(&g_site_fohh_clients, client, list_entry);

    SITE_CONN_UNLOCK();

    PBROKER_SITE_CONFIG_RDLOCK();

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    const char* offline_domain = zpn_private_broker_get_offline_domain();

    fohh_connection_site_init(connection,
            offline_domain, ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME,
            client->remote_host_name, stick_to_sitec, 1, current_to_sitec,
            gs->self_to_private_ssl_ctx, gs->self_to_broker_ssl_ctx);

    PBROKER_SITE_CONFIG_UNLOCK();

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_deregister_fohh(struct fohh_connection* connection)
{
    site_fohh_client_t *client, *tmp;

    SITE_CONN_WRLOCK();

    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        if (client->f_conn == connection) {
            LIST_REMOVE(client, list_entry);
            PBROKER_FREE(client);
            SITE_CONN_UNLOCK();
            return ZPN_RESULT_NO_ERROR;
        }
    }

    SITE_CONN_UNLOCK();

    return ZPN_RESULT_ERR;
}

int zpn_private_broker_site_is_sitec_eligible(const char* offline_domain)
{
    return offline_domain && offline_domain[0] && zpn_private_broker_site_is_sitec_preferred();
}

int zpn_private_broker_site_is_sitec_preferred()
{
    SITE_FLIP_RDLOCK();
    sitec_preference_t sitec_preferred = g_site_flip_state.sitec_preferred;
    SITE_FLIP_UNLOCK();

    int ret = sitec_preferred == SITEC_PREFER_TO_DEFAULT
        ? zpn_private_broker_get_sitec_preferred_with_lock()
        : sitec_preferred == SITEC_PREFER_TO_SITEC;

    ret = ret && zpn_private_broker_get_site_is_active_with_lock();

    return ret;
}

int zpn_private_broker_site_sitec_is_reachable() {
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *pbctl = (gs->private_broker_state) ? gs->private_broker_state->sitec_control : NULL;

    int status = pbctl && fohh_get_state(pbctl) == fohh_connection_connected;
    status = status && g_site_flip_state.sitec_preferred != SITEC_PREFER_TO_BROKER_ONLY;

    return status;
}

int zpn_private_broker_site_broker_is_reachable()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct fohh_connection *pbctl = (gs->private_broker_state) ? gs->private_broker_state->broker_control : NULL;

    return pbctl && fohh_get_state(pbctl) == fohh_connection_connected;
}

static int zpn_private_broker_cmd_show_config(struct zpath_debug_state* request_state,
                                              const char **             query_values,
                                              int                       query_value_count,
                                              void*                     cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_ddil_config *ddil_config;
    struct zpn_site *site;
    size_t row_count = 1;
    int res;

    int64_t site_gid = zpn_private_broker_get_site_gid_with_lock();

    ZDP("%26s: %"PRIu64"\n", "customer_gid", gs->customer_id);
    ZDP("%26s: %"PRIu64"\n", "instance_gid", gs->private_broker_id);
    ZDP("%26s: %"PRIu64"\n", "group_gid", ZPN_BROKER_GET_GROUP_GID());
    ZDP("%26s: %"PRIu64"\n", "site_gid", site_gid);

    res = zpn_site_get_by_gid(site_gid,
            &site,
            1,
            NULL,
            NULL,
            0);
    if (res != ZPN_RESULT_NO_ERROR) {
        ZDP("Failed to get site table, error: %s\n", zpath_result_string(res));
    } else {
        ZDP("%26s: %"PRIu64" days\n", "reenroll_period", site->reenroll_period);
    }

    res = zpn_ddil_config_get_by_customer_gid(gs->customer_id,
            &ddil_config,
            &row_count,
            NULL,
            0,
            0);
    if (res != ZPN_RESULT_NO_ERROR) {
        ZDP("Failed to get ddil config, error: %s\n", zpath_result_string(res));
    } else {
        ZDP("%26s: %s\n", "offline_domain", ddil_config->offline_domain);
        ZDP("%26s: %d\n", "sitec_preferred", site->sitec_preferred);
        ZDP("%26s: %d\n", "new_user_support", ddil_config->new_user_support);
        ZDP("%26s: %d\n", "is_switchtime_enabled", ddil_config->is_switchtime_enabled);
        ZDP("%26s: %"PRIu64" seconds\n", "max_allowed_downtime_s", ddil_config->max_allowed_downtime_s);
        ZDP("%26s: %"PRIu64" seconds\n", "max_allowed_switchtime_s", ddil_config->max_allowed_switchtime_s);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_show_status(struct zpath_debug_state* request_state,
                                              const char **             query_values,
                                              int                       query_value_count,
                                              void*                     cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    uint32_t sitec_total, sitec_reachable;
    sitec_preference_t sitec_preferred;
    allow_c2site_t allow_c2site;

    SITE_FLIP_RDLOCK();
    sitec_preferred = g_site_flip_state.sitec_preferred;
    allow_c2site = g_site_flip_state.allow_c2site;
    SITE_FLIP_UNLOCK();

    get_pbload_connection_count(&sitec_total, &sitec_reachable);

    ZDP("                  site_gid: %"PRId64" (%s)\n",
                zpn_private_broker_get_site_gid(),
                zpn_private_broker_get_site_is_active_with_lock() ? "active" : "inactive");
    ZDP("           reenroll period: %"PRIu64" days\n", zpn_private_broker_get_reenroll_period_with_lock());
    ZDP("       pcc/sitec preferred: %s\n", get_sitec_preferred_string(sitec_preferred));
    ZDP("                switchtime: %s\n",
                zpn_private_broker_get_is_switchtime_enabled() ? "enabled" : "disabled");
    ZDP("      max allowed downtime: %"PRIu64" seconds\n", zpn_private_broker_get_max_allowed_downtime_s_with_lock());
    ZDP("    max allowed switchtime: %"PRIu64" seconds\n", zpn_private_broker_get_max_allowed_switchtime_s_with_lock());
    ZDP("            offline domain: %s\n", zpn_private_broker_get_offline_domain());
    ZDP("         non enrolled user: %s\n", get_allow_c2site_string(allow_c2site));
    ZDP(" nr of reachable pcc/sitec: %u/%u\n", sitec_reachable, sitec_total);

    struct fohh_connection *pbctl = gs->private_broker_state->broker_control;
    ZDP("%26s: %s\n", "pbctrl to broker", fohh_description(pbctl));
    ZDP("%26s  %-32s %s\n", "", fohh_state(pbctl), fohh_connection_get_sni(pbctl));

    site_fohh_client_t *pbctl_sitec = NULL, *client, *tmp;
    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        if (strcmp(client->label, "pbctl.sitec") == 0) {
            ZDP("%26s: %s\n", "pbctrl to pcc/sitec", fohh_description(client->f_conn));
            ZDP("%26s  %-32s %s\n", "", fohh_state(client->f_conn), fohh_connection_get_sni(client->f_conn));
            pbctl_sitec = client;
        }
    }
    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        if (pbctl_sitec != client) {
            ZDP("%26s: %s\n", client->label, fohh_description(client->f_conn));
            ZDP("%26s  %-32s %s\n", "", fohh_state(client->f_conn), fohh_connection_get_sni(client->f_conn));
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_join_site(struct zpath_debug_state* request_state,
                                            const char **             query_values,
                                            int                       query_value_count,
                                            void*                     cookie)
{
    char domain_in_use[ZPN_MAX_DOMAIN_NAME_LEN + 1] = {};
    zpn_private_broker_get_offline_domain_with_lock(domain_in_use, sizeof(domain_in_use));

    const char* target = query_values[0] ? query_values[0] : NULL;

    if (!target) {
        ZDP("Domain is not set, please specify the target domain by 'domain=<DOMAIN>' in query string\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (g_site_flip_state.sitec_preferred == SITEC_PREFER_TO_BROKER_ONLY) {
        ZDP("Allow attach to broker only, it will be executed only after it is enabled.\n");
    }

    if (strncmp(target, domain_in_use, ZPN_MAX_DOMAIN_NAME_LEN + 1)) {
        int res = zpn_private_broker_handle_config_change(-1, -1, target,
                -1, -1, -1, -1, -1, SITE_FLIP_BY_COMMAND);
        if (res == ZPN_RESULT_NO_ERROR) {
            ZDP("Connect to site '%s', executing in background.\n", target);
        } else {
            ZDP("Failed to execute command\n");
        }
    } else {
        ZDP("PSE is already connected to: '%s'\n", domain_in_use);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_change_sitec_preference(
                                    struct zpath_debug_state* request_state,
                                    const char **             query_values,
                                    int                       query_value_count,
                                    void*                     cookie)
{
    const char* preferred = query_values[0] ? query_values[0] : NULL;

    if (!preferred) {
        ZDP("Preference is not set, please specify it by 'preferred=<default/yes/no/never>' in query string\n");
        return ZPN_RESULT_NO_ERROR;
    }

    int action = 0;

    sitec_preference_t sitec_preferred;
    if (strcasecmp(preferred, "default") == 0) {
        sitec_preferred = SITEC_PREFER_TO_DEFAULT;
    } else if (strcasecmp(preferred, "yes") == 0) {
        sitec_preferred = SITEC_PREFER_TO_SITEC;
    } else if (strcasecmp(preferred, "no") == 0) {
        sitec_preferred = SITEC_PREFER_TO_BROKER;
    } else if (strcasecmp(preferred, "never") == 0) {
        sitec_preferred = SITEC_PREFER_TO_BROKER_ONLY;
    } else {
        ZDP("Unrecognized value %s, expect one of 'default', 'yes', 'no', and 'never'\n", preferred);
        return ZPN_RESULT_NO_ERROR;
    }

    SITE_FLIP_WRLOCK();

    if (g_site_flip_state.sitec_preferred == sitec_preferred) {
        ZDP("Attach mode is already set to %s.\n", preferred);
    } else {
        ZDP("Attach mode is %s, executing in background\n", get_sitec_preferred_string(sitec_preferred));
        g_site_flip_state.sitec_preferred = sitec_preferred;
        action = 1;
    }

    SITE_FLIP_UNLOCK();

    if (action && g_site_monitor_flip_ev_handler) {
        event_active(g_site_monitor_flip_ev_handler, SITE_FLIP_BY_COMMAND, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_cmd_change_new_user_support(
                                    struct zpath_debug_state* request_state,
                                    const char **             query_values,
                                    int                       query_value_count,
                                    void*                     cookie)
{
    const char* allowed = query_values[0] ? query_values[0] : NULL;

    if (!allowed) {
        ZDP("Allow new user is not set, please specify it by 'allow=<default/yes/no>' in query string\n");
        return ZPN_RESULT_NO_ERROR;
    }

    int action = 0;

    allow_c2site_t allow_c2site;
    if (strcasecmp(allowed, "default") == 0) {
        allow_c2site = ALLOW_C2SITE_DEFAULT;
    } else if (strcasecmp(allowed, "no") == 0) {
        allow_c2site = ALLOW_C2SITE_NO;
    } else if (strcasecmp(allowed, "yes") == 0) {
        allow_c2site = ALLOW_C2SITE_YES;
    } else {
        ZDP("Unrecognized value %s, expect one of 'default', 'yes', and 'no'\n", allowed);
        return ZPN_RESULT_NO_ERROR;
    }

    SITE_FLIP_WRLOCK();

    if (g_site_flip_state.allow_c2site == allow_c2site) {
        ZDP("Allow new user is already set to %s.\n", allowed);
    } else {
        ZDP("Allow new user is %s, executing in background\n", get_allow_c2site_string(allow_c2site));
        g_site_flip_state.allow_c2site = allow_c2site;
        action = 1;
    }

    SITE_FLIP_UNLOCK();

    if (action && g_site_monitor_flip_ev_handler) {
        event_active(g_site_monitor_flip_ev_handler, SITE_FLIP_BY_COMMAND, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_show_flip_stats(struct zpath_debug_state* request_state,
                                                 const char **             query_values,
                                                 int                       query_value_count,
                                                 void*                     cookie)
{
    site_fohh_client_t *client, *tmp;
    LIST_FOREACH_SAFE(client, &g_site_fohh_clients, list_entry, tmp) {
        ZDP("%26s: to_sitec %"PRIu64", to_broker %"PRIu64"\n", client->label,
                fohh_connection_get_nr_switch_to_sitec(client->f_conn),
                fohh_connection_get_nr_switch_to_broker(client->f_conn));
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_firedrill_stats(struct zpath_debug_state* request_state,
                                                    const char **             query_values,
                                                    int                       query_value_count,
                                                    void*                     cookie)
{
    struct zpn_firedrill_stats firedrill_stats;
    firedrill_stats.firedrill_cmdline_disable_count = __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_cmdline_disable_count, 0);
    firedrill_stats.firedrill_completed_count = __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_completed_count, 0);
    firedrill_stats.firedrill_triggered_count = __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_triggered_count, 0);
    firedrill_stats.firedrill_transit_count = __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_transit_count, 0);

    ZDP("Firedrill stats:\n");
    ZDP("Triggered : %d\n", firedrill_stats.firedrill_triggered_count);
    ZDP("Completed : %d\n", firedrill_stats.firedrill_completed_count);
    ZDP("Transit triggered: %d\n",firedrill_stats.firedrill_transit_count);
    ZDP("CmdLine disabled : %d\n", firedrill_stats.firedrill_cmdline_disable_count);

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_private_broker_firedrill_status(struct zpath_debug_state* request_state,
                                                const char **             query_values,
                                                int                       query_value_count,
                                                void*                     cookie)
{
    int firedrill_status = zpn_private_broker_get_firedrill_state();
    char *ptr;
    switch(firedrill_status) {
        case ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED:
            ptr = "disabled";
            break;
        case ZPN_PRIVATE_BROKER_FIREDRILL_ENABLED:
            ptr = "enabled";
            break;
        case ZPN_PRIVATE_BROKER_FIREDRILL_TRANSIT:
            ptr = "transit";
            break;
        default:
            ptr = "invalid";
            break;
    }
    ZDP("Firedrill status %s\n", ptr);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_cmd_show_pbload(struct zpath_debug_state* request_state,
                                              const char **             query_values,
                                              int                       query_value_count,
                                              void*                     cookie)
{
    site_pbload_sitec_entry_t *cur, *tmp;

    SITE_PBLOAD_RDLOCK();

    LIST_FOREACH_SAFE(cur, &g_site_pbload_sitec_list, list_entry, tmp) {
        if (!cur->f_conn) {
            continue;
        }
        ZDP("sitec gid %"PRId64", name %s, site gid %"PRId64", action %s, duration/backoff: %"PRId64"/%d\n",
                cur->sitec_gid, cur->name,
                cur->site_gid,
                sitec_pbload_action_str[cur->action],
                epoch_s() - cur->f_conn->last_connection_end_epoch_s, cur->f_conn->backoff);
        ZDP("  %s, %s\n",
                cur->f_conn ? fohh_description(cur->f_conn) : "<creating>",
                cur->f_conn ? fohh_state(cur->f_conn) : "<n/a>");
    }

    SITE_PBLOAD_UNLOCK();

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_init()
{
    int res = ZPN_RESULT_NO_ERROR;

    g_site_flip_lock = ZPATH_RWLOCK_INIT;
    g_sitec_control_lock = ZPATH_RWLOCK_INIT;
    g_site_pbload_lock = ZPATH_RWLOCK_INIT;
    g_site_conn_lock = ZPATH_RWLOCK_INIT;

    res = zpath_debug_add_read_command("show current configuration (from wally tables)",
                                  "/pbroker/site/config",
                                  zpn_private_broker_cmd_show_config,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for showing current configuration");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("show current offline status",
                                  "/pbroker/site/status",
                                  zpn_private_broker_cmd_show_status,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for showing current offline status");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_admin_command("request PSE to join specified site, for testing only",
                                  "/pbroker/site/join",
                                  zpn_private_broker_cmd_join_site,
                                  NULL,
                                  "domain", "the site domain to be joined",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for joining specified site");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_admin_command("change sitec preference, for testing only",
                                  "/pbroker/site/sitec",
                                  zpn_private_broker_cmd_change_sitec_preference,
                                  NULL,
                                  "preferred", "set to default/yes/no/never",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for changing sitec preference");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("override wally config - allow/disallow non enrolled user",
                                  "/pbroker/site/new_user_support",
                                  zpn_sitec_cmd_change_new_user_support,
                                  NULL,
                                  "allow", "set to default/yes/no",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for changing new user support");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("show site flip stats",
                                  "/pbroker/site/stats/flip",
                                  zpn_private_broker_cmd_show_flip_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for joining specified site");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("show current pbload connections",
                                  "/pbroker/site/pbload",
                                  zpn_private_broker_cmd_show_pbload,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for show pbload connections");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_admin_command("show firedrill stats",
                                  "/pbroker/site/firedrill_stats",
                                  zpn_private_broker_cmd_firedrill_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for firedrill stats");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_admin_command("show firedrill status",
                                  "/pbroker/site/firedrill_status",
                                  zpn_private_broker_firedrill_status,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Unable to add command for firedrill status");
        res = ZPN_RESULT_ERR;
        goto done;
    }

done:
    return res;
}

int zpn_private_broker_site_init_tasks(struct event_base *base)
{
    g_site_monitor_flip_ev_handler = event_new(base, -1, EV_PERSIST, zpn_private_broker_monitor_flip_callback, NULL);
    if (!g_site_monitor_flip_ev_handler) {
        ZPN_LOG(AL_CRITICAL, "Failed to create event handler for site flip monitor");
    }

    g_site_monitor_pbload_ev_handler = event_new(base, -1, EV_PERSIST, zpn_private_broker_monitor_pbload_callback, NULL);
    if (!g_site_monitor_pbload_ev_handler) {
        ZPN_LOG(AL_CRITICAL, "Failed to create event handler for site pbload monitor");
    }

    g_site_monitor_pbload_conn_ev_handler = event_new(base, -1, EV_PERSIST,
            zpn_private_broker_monitor_pbload_conn_periodically, NULL);

    if (!g_site_monitor_pbload_conn_ev_handler) {
        ZPN_LOG(AL_CRITICAL, "Failed to create timer for monitoring site pbload connections");
    } else {
        struct timeval tv;
        tv.tv_sec = 2;
        tv.tv_usec = 0;

        if (evtimer_add(g_site_monitor_pbload_conn_ev_handler, &tv) < 0) {
            ZPN_LOG(AL_CRITICAL, "Failed to add timer for monitoring site pbload connections");
            evtimer_del(g_site_monitor_pbload_conn_ev_handler);
            g_site_monitor_pbload_conn_ev_handler = NULL;
            return ZPN_RESULT_ERR;
        }
    }

    g_site_remediation_ev_handler = event_new(base, -1, EV_PERSIST,
            zpn_private_broker_monitor_remediation_periodically, NULL);

    if (!g_site_remediation_ev_handler) {
        ZPN_LOG(AL_CRITICAL, "Failed to create timer for site remediation monitor");
    } else {
        struct timeval tv;
        tv.tv_sec = 30;
        tv.tv_usec = 0;

        if (evtimer_add(g_site_remediation_ev_handler, &tv) < 0) {
            ZPN_LOG(AL_CRITICAL, "Failed to add timer for site remediation monitor");
            evtimer_del(g_site_remediation_ev_handler);
            g_site_remediation_ev_handler = NULL;
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_start()
{
    while (!g_site_monitor_flip_ev_handler || !g_site_monitor_pbload_ev_handler) {
        ZPN_LOG(AL_INFO, "Waiting for monitor handler to be valid ...");
        sleep(1);
    }

    event_active(g_site_monitor_flip_ev_handler, SITE_FLIP_BY_INIT_DONE, 0);
    event_active(g_site_monitor_pbload_ev_handler, 0, 0);

    return ZPN_RESULT_NO_ERROR;
}
