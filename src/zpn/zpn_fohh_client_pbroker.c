/*
 * zpn_fohh_client_pbroker.c. Copyright (C) 2019 Zscaler, Inc. All Rights Reserved.
 */

#include <sys/queue.h>
#include "argo/argo_hash.h"

#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"

#include "wally/wally.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_fohh_client_pbroker.h"

static int initialized = 0;
struct zpn_fohh_client_pbroker;
struct zpn_mconn_global_owner_calls zfc_pb_mt_global_call_set_broker;
struct zpn_mconn_global_owner_calls zfc_pb_mt_global_call_set_pbroker;
static struct event *mt_timer = NULL;

/* *********************************************************************************************
 * Global Resources protected by local_lock
 */
static zpath_mutex_t local_lock;
static struct argo_hash_table *pbroker_by_id = NULL;

struct zpn_fohh_client_pbroker_mt_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
    int64_t reap_queue_count;
};

struct zpn_fohh_client_pbroker_mt_free_queue {
    zpath_mutex_t lock;
    struct zpn_fohh_client_pbroker_mt_head_tailq reap_mt_list;
    struct zpn_fohh_client_pbroker_mt_head_tailq mt_list;
    struct zpn_fohh_client_pbroker_mt_queue_stats stats;
};

static struct zpn_fohh_client_pbroker_mt_free_queue free_q;

static void zpn_fohh_client_pbroker_destroy(struct zpn_fohh_client_pbroker *zfc_pb);
static void zpn_fohh_client_pbroker_mt_free_q_init();
static struct zpn_fohh_client_pbroker_mt *zpn_fohh_client_pbroker_mt_allocate(void);
static void zpn_fohh_client_pbroker_mt_reap(struct zpn_fohh_client_pbroker_mt *mt);

static int zpn_fohh_client_pbroker_send_all_app_queries(struct zpn_fohh_client_pbroker *zfc_pb);

const struct zpn_mconn_local_owner_calls mconn_fohh_client_pbroker_calls;

static int zpn_fohh_client_pbroker_mt_destroy(struct zpn_fohh_client_pbroker_mt *mt, char *err);

static void zpn_fohh_client_pbroker_mt_release_request(struct zpn_fohh_client_pbroker_mt *mt);
#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_mt_release_request_thread_call(struct zevent_base *zbase, void *cookie, int64_t int_cookie);
#else
static void zpn_fohh_client_pbroker_mt_release_request_thread_call(struct fohh_thread *thread, void *cookie, int64_t int_cookie);
#endif
static int zpn_fohh_client_pbroker_schedule_release_request(struct zpn_fohh_client_pbroker_mt *mt);
static int zpn_fohh_client_pbroker_mt_busy_to_idle_list(struct zpn_fohh_client_pbroker_mt *mt, int zfc_pb_locked);

#define ZFC_PB_MONITOR_TIMER_S        10                                  /* Connection monitor timer 10 sec */
#define ZFC_PB_MT_IDLE_TIMEOUT_US     (9*ZFC_PB_MONITOR_TIMER_S*1000000)  /* Idle mt timeout in 90 seconds */
#define ZFC_PB_IDLE_TIMEOUT_US        (60*30*1000000)                     /* Idle pbroker timeout in 30 minutes */
#define ZFC_PB_AUTH_TIMER_US          (100*1000)                          /* Auth monitor timer 100 ms */

/*************************************************************************************************
 *
 *  zfc_pb global pbroker stuff
 *
 *************************************************************************************************/
static void zpn_fohh_client_pbroker_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_pbroker *zfc_pb = cookie;
    struct zpn_fohh_client_pbroker_mt *mt;
    struct zpn_fohh_client_pbroker_mt *next_mt;
    struct zpn_fohh_client_pbroker_mt_head_tailq head;
    int64_t cur_time_us;
    int destroy_pbroker = 0;

    ZPN_LOG(AL_DEBUG, "%s for user %.*s: num_busy_mt = %d, num_idle_mt = %d, num_alloc_mt = %ld, num_reap_mt = %ld, num_free_mt = %ld",
            fohh_description(zfc_pb->zfc->conn), ZPATH_DEBUG_BYTES, zfc_pb->zfc->auth_token, zfc_pb->num_busy_mt, zfc_pb->num_idle_mt,
            (long)free_q.stats.allocations, (long)free_q.stats.reap_queue_count, (long)free_q.stats.free_queue_count);

    TAILQ_INIT(&head);

    cur_time_us = epoch_us();

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    if (zfc_pb->num_idle_mt > 0) {

        next_mt = TAILQ_FIRST(&(zfc_pb->idle_mt_list));

        while (next_mt) {
            struct zpn_fohh_client_pbroker_mt *mt = next_mt;

            next_mt = TAILQ_NEXT(next_mt, queue_entry);
            if (zpn_debug_get(ZPN_DEBUG_MCONN_IDX)) {
                ZPN_LOG(AL_DEBUG, "%s:%s:%s: idle mtunnel host = %s, proto = %d, port = %d, tls = %d",
                        mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, mt->key.host_name, mt->key.proto, mt->key.port, mt->key.tls);
            }

            if (cur_time_us - mt->idle_start_us > (ZFC_PB_MT_IDLE_TIMEOUT_US)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s:%s:%s: pbroker mt expired, remove, cur_time_us = %ld, idle_start_us = %ld",
                                         mt->dbg_str,
                                         mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, (long)cur_time_us, (long)mt->idle_start_us);
                argo_hash_remove(zfc_pb->idle_mt,
                                 &mt->key,
                                 sizeof(struct zfc_pb_mt_key),
                                 mt);
                TAILQ_REMOVE(&(zfc_pb->idle_mt_list), mt, queue_entry);
                zfc_pb->num_idle_mt--;
                TAILQ_INSERT_TAIL(&(head), mt, queue_entry);
            } else if ((mt->status == zfc_pb_mt_remote_disconnect) ||
                       (mt->status == zfc_pb_mt_connect_error) ||
                       (mt->status == zfc_pb_mt_request_error)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s:%s:%s pbroker mt is in %s state, should destroy",
                                         mt->dbg_str,
                                         mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, zfc_pb_mt_status_string(mt->status));
                argo_hash_remove(zfc_pb->idle_mt,
                                 &mt->key,
                                 sizeof(struct zfc_pb_mt_key),
                                 mt);
                TAILQ_REMOVE(&(zfc_pb->idle_mt_list), mt, queue_entry);
                zfc_pb->num_idle_mt--;
                TAILQ_INSERT_TAIL(&(head), mt, queue_entry);
            }
        }
    }

    if (!zfc_pb->num_busy_mt && !zfc_pb->num_idle_mt && !zfc_pb->idle_start_us) {
        zfc_pb->idle_start_us = cur_time_us;
        ZPN_DEBUG_PRIVATE_BROKER("No mt is allocated, start pbroker at %ld", (long)zfc_pb->idle_start_us);
    }
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    while ((mt = TAILQ_FIRST(&(head)))) {
        TAILQ_REMOVE(&(head), mt, queue_entry);
        zpn_fohh_client_pbroker_mt_destroy(mt, MT_CLOSED_TERMINATED);
    }

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    if (zfc_pb->idle_start_us) {
        ZPN_DEBUG_PRIVATE_BROKER("Pbroker %s for user %.*s has been idle for %ld seconds",
                          fohh_description(zfc_pb->zfc->conn),
                          ZPATH_DEBUG_BYTES, zfc_pb->zfc->auth_token,
                          (long)((cur_time_us - zfc_pb->idle_start_us) / 1000000));
        if ((cur_time_us - zfc_pb->idle_start_us) > ZFC_PB_IDLE_TIMEOUT_US) {
            ZPN_LOG(AL_INFO, "PBroker has been idle for more than %ld seconds, self destroy", (long)(ZFC_PB_IDLE_TIMEOUT_US / 1000000));
            ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);
            argo_hash_remove(pbroker_by_id, zfc_pb->zfc->auth_token, strlen(zfc_pb->zfc->auth_token), zfc_pb);
            ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
            destroy_pbroker = 1;
        }
    }
    //lock is acquired few lines above, it complains its possible the time we release the lock, we might not get the lock due to timing
    //ZPATH_MUTEXT_UNLOCK is a day1 implementation, keep it false positive.
    /* coverity[missing_lock: FALSE] */
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    if (destroy_pbroker) {
        zpn_fohh_client_pbroker_destroy(zfc_pb);
    }
}

/* Connection monitor timer add */
static void zpn_fohh_client_pbroker_add_monitor_timer(struct fohh_connection *connection,
                                                      struct zpn_fohh_client_pbroker *zfc_pb,
                                                      event_callback_fn monitor_timer_cb,
                                                      int64_t secs,
                                                      int64_t usecs)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    if (zfc_pb->monitor_ev) {
        event_free(zfc_pb->monitor_ev);
        zfc_pb->monitor_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    zfc_pb->monitor_ev = event_new(base,
                                 -1,
                                 EV_PERSIST,
                                 monitor_timer_cb,
                                 zfc_pb);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(zfc_pb->monitor_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate monitor timer");
        event_free(zfc_pb->monitor_ev);
        zfc_pb->monitor_ev = NULL;
    }
}

static void zpn_fohh_client_pbroker_auth_complete_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_pbroker *zfc_pb = cookie;

    ZPN_LOG(AL_DEBUG, "%s: Auth complete event callback", fohh_description(zfc_pb->zfc->conn));

    if (zfc_pb->conn_auth_complete_cb) {
        zfc_pb->conn_auth_complete_cb(zfc_pb);
    }
}

/* Connection auth complete timer add */
static void zpn_fohh_client_pbroker_add_auth_timer(struct fohh_connection *connection,
                                                   struct zpn_fohh_client_pbroker *zfc_pb,
                                                   event_callback_fn auth_complete_event_cb,
                                                   int64_t secs,
                                                   int64_t usecs)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    ZPN_DEBUG_PRIVATE_BROKER("%s: Add auth timer", ZFC_DBG(zfc_pb->zfc));

    if (zfc_pb->auth_ev) {
        event_free(zfc_pb->auth_ev);
        zfc_pb->auth_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    zfc_pb->auth_ev = event_new(base,
                                -1,
                                0, //EV_PERSIST,
                                auth_complete_event_cb,
                                zfc_pb);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(zfc_pb->auth_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate auth timer");
        event_free(zfc_pb->auth_ev);
        zfc_pb->auth_ev = NULL;
    }
}

/* Connection auth complete timer remove */
static void zpn_fohh_client_pbroker_remove_auth_timer(struct fohh_connection *connection,
                                                      struct zpn_fohh_client_pbroker *zfc_pb)
{
    ZPN_DEBUG_PRIVATE_BROKER("%s: Remove auth timer", ZFC_DBG(zfc_pb->zfc));

    /* Destroy timer, will cancel timeout */
    if (zfc_pb->auth_ev) {
        event_free(zfc_pb->auth_ev);
        zfc_pb->auth_ev = NULL;
    }
}


/* Enable helper event for triggering delayed auth callback */
void zpn_fohh_pb_client_enable_auth_complete_cb(struct zpn_fohh_client_pbroker *zfc_pb)
{

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    /* Set authentication callback event */
    zpn_fohh_client_pbroker_add_auth_timer(zfc_pb->zfc->conn,
                                           zfc_pb,
                                           zpn_fohh_client_pbroker_auth_complete_cb,
                                           0,
                                           ZFC_PB_AUTH_TIMER_US);

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
}

/* Disable helper event for triggering delayed auth callback */
void zpn_fohh_pb_client_disable_auth_complete_cb(struct zpn_fohh_client_pbroker *zfc_pb)
{

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    /* Clear authentication callback event */
    zpn_fohh_client_pbroker_remove_auth_timer(zfc_pb->zfc->conn, zfc_pb);

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
}

/* Delay running auth complete timer */
int zpn_fohh_client_pbroker_delay_auth_timer(struct zpn_fohh_client_pbroker *zfc_pb)
{
    struct timeval tv;
    int res = ZPN_RESULT_ERR;

    tv.tv_sec = 0;
    tv.tv_usec = ZFC_PB_AUTH_TIMER_US;

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    if (zfc_pb->auth_ev) {
        if (event_add(zfc_pb->auth_ev, &tv)) {
            ZPN_DEBUG_PRIVATE_BROKER("%s: Auth timer add delay error", ZFC_DBG(zfc_pb->zfc));
        } else {
            ZPN_DEBUG_PRIVATE_BROKER("%s: Delayed auth timer by %d us", ZFC_DBG(zfc_pb->zfc), ZFC_PB_AUTH_TIMER_US);
            res = ZPN_RESULT_NO_ERROR;
        }
    } else {
        ZPN_DEBUG_PRIVATE_BROKER("%s: Cannot delay auth timer, timer not active", ZFC_DBG(zfc_pb->zfc));
    }

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    return res;
}

struct zpn_fohh_client_pbroker_mt* zpn_fohh_client_pbroker_get_mtunnel_by_tag_id(struct zpn_fohh_client_pbroker *zfc_pb, int32_t tag_id)
{
    struct zpn_fohh_client_pbroker_mt *mt;

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    mt = argo_hash_lookup(zfc_pb->mtunnel_by_id,
                          &tag_id,
                          sizeof(tag_id),
                          NULL);
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    return mt;
}

static int zpn_pb_dns_app_client_check_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object,
                                          int is_app_check)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;
    struct zpn_dns_client_check *dns_check = NULL;
    struct zpn_app_client_check *app_check = NULL;
    char dump[8000];
    int i;

    if (is_app_check) {
        app_check = (struct zpn_app_client_check *)object->base_structure_void;
    } else {
        dns_check = (struct zpn_dns_client_check *)object->base_structure_void;
    }

    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX) || zpn_debug_get(ZPN_DEBUG_BROKER_DNS_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx %s check response from broker: %s",
                    is_app_check ? "app" : "DNS",
                    dump);
        }
    }

    ZPN_LOG(AL_DEBUG, "%s: %s Client Check %s", ZFC_DBG(zfc_pb->zfc),
            is_app_check ? "app" : "DNS",
            is_app_check ? app_check->name : dns_check->name);

    if (is_app_check) {
        for (i = 0; i < app_check->target_name_count; i++) {
            ZPN_LOG(AL_DEBUG, "Target[%d]: %s", i, app_check->target_name[i]);
        }
        if (zfc_pb->app_resp_cb) {
            ZPN_LOG(AL_DEBUG, "%s: relayed app check response to callback", ZFC_DBG(zfc_pb->zfc));
            zfc_pb->app_resp_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object);
        }
    } else {
        for (i = 0; i < dns_check->target_name_count; i++) {
            ZPN_LOG(AL_DEBUG, "Target[%d]: %s", i, dns_check->target_name[i]);
        }
        if (zfc_pb->dns_resp_cb) {
            ZPN_LOG(AL_DEBUG, "%s: relayed dns response to callback", ZFC_DBG(zfc_pb->zfc));
            zfc_pb->dns_resp_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pb_dns_client_check_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    return zpn_pb_dns_app_client_check_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object, 0);
}

static int zpn_pb_app_client_check_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    return zpn_pb_dns_app_client_check_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object, 1);
}

static int zpn_pb_posture_profile_ack_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;
    struct zpn_posture_profile_ack *posture_profile_ack = (struct zpn_posture_profile_ack *)object->base_structure_void;
    char dump[8000];

    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx postures ack from broker: %s", dump);
        }
    }

    ZPN_LOG(AL_DEBUG, "%s: Posture profile ack, id_str: %s, err: %s", ZFC_DBG(zfc_pb->zfc),
            posture_profile_ack->id_str, posture_profile_ack->error);

    if (zfc_pb->posture_profile_ack_cb) {
        ZPN_LOG(AL_DEBUG, "%s: relayed posture profile ack to callback", ZFC_DBG(zfc_pb->zfc));
        zfc_pb->posture_profile_ack_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object);
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_pb_client_app_cb(void *argo_cookie_ptr,
                             void *argo_structure_cookie_ptr,
                             struct argo_object *object)
{
    struct zpn_client_app *req = (struct zpn_client_app *)object->base_structure_void;
    char dump[8000];

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (!req->deleted && !req->bypass) {
        ZPN_LOG(AL_DEBUG, "Application: %s", req->app_domain);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pb_client_app_complete_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;

    char dump[8000];
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc_pb->zfc), dump);
        }
    }

    ZPN_LOG(AL_DEBUG, "%s: Got all applications", ZFC_DBG(zfc_pb->zfc));
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pb_mtunnel_request_ack_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;
    struct zpn_mtunnel_request_ack *ack = object->base_structure_void;
    struct zpn_fohh_client_pbroker_mt *mt;
    char err_str[1024];
    int res = ZPN_RESULT_NO_ERROR;
    int only_broker_mconn_attach = 1; /* This is how we are corrently using for PBroker */

    if (!ack->tag_id) {
        ZPN_LOG(AL_NOTICE, "Mtunnel request ack received without tag id");
        return ZPN_RESULT_NO_ERROR;
    }

    mt = zpn_fohh_client_pbroker_get_mtunnel_by_tag_id(zfc_pb, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Mtunnel pb request ack received without tunnel. It timed out? tag_id = %d", ack->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: Pbroker Rx mt ack from broker -> pbroker: %s", mt->dbg_str, dump);
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_NOTICE, "%s: Tunnel pb request ack without mtunnel_id", mt->dbg_str);
        mt->status = zfc_pb_mt_connect_error;
        goto exit;
    }

    mt->public_broker_mtunnel_id = ZPN_STRDUP(ack->mtunnel_id, strlen(ack->mtunnel_id));

    if (ack->error) {
        ZPN_LOG(AL_NOTICE, "%s: Tunnel request ack error %s : %s", mt->dbg_str, ack->error ? ack->error : "error", ack->reason);
        mt->status = zfc_pb_mt_connect_error;
        if (ack->reason) {
            snprintf(err_str, sizeof(err_str), "%s,%s", ack->error, ack->reason);
        } else {
            snprintf(err_str, sizeof(err_str), "%s", ack->error);
        }
        if (mt->err) ZPN_FREE(mt->err);
        mt->err = ZPN_STRDUP(err_str, strlen(err_str));
        goto exit;
    }

    if (only_broker_mconn_attach) {
        zpn_mconn_set_fohh_thread_id(&(mt->pbroker_mconn.mconn), fohh_connection_get_thread_id(zfc_pb->zfc->conn));

        res = zpn_fohh_client_attach_mconn(zfc_pb->zfc, &(mt->broker_mconn), &mt->tag_id, sizeof(int32_t));
        if (res) {
            ZPN_LOG(AL_ERROR, "%s:%s:%s: Cannot add local owner broker_mconn: %p for tag_id: %d for pbroker",
                    mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, &(mt->broker_mconn), mt->tag_id);
            mt->status = zfc_pb_mt_connect_error;
            goto exit;
        }

        ZPN_LOG(AL_NOTICE, "%s: Attached local owner to broker_mconn with pbroker tag_id: %d, broker_mconn: %p",
                mt->dbg_str, mt->tag_id, &(mt->broker_mconn));

        mt->status = zfc_pb_mt_connected;

        /* Inform client cb */
        if (zfc_pb->broker_mt_req_ack_cb) {
            ZPN_LOG(AL_DEBUG, "%s: PBroker client relay mt req ack from broker -> pbroker to callback", ZFC_DBG(zfc_pb->zfc));
            zfc_pb->broker_mt_req_ack_cb(argo_cookie_ptr, argo_structure_cookie_ptr, object);
        }

        goto exit;
    }

    /********************************************************************************************************/
    /* FIXME: Mansoor - We currently dont seem to need the following code, so cleanup after further testing */
    /********************************************************************************************************/

    res = zpn_fohh_client_attach_mconn(zfc_pb->zfc, &(mt->broker_mconn), &mt->tag_id, sizeof(int32_t));
    if (res) {
        ZPN_LOG(AL_ERROR, "%s:%s:%s Cannot attach mconn to zpn_fohh_client", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
        mt->status = zfc_pb_mt_connect_error;
        goto exit;
    }

    if (mt->key.tls) {
        int mconn_thread_id = fohh_connection_get_thread_id(zfc_pb->zfc->conn);

        mt->connector = (struct zpn_connector *)zpn_connector_tun_create(mt, zfc_pb->ssl_ctx_connector, NULL, mt->verify_cert, mt->verify_host);
        ZPN_DEBUG_MTUNNEL("%s:%s:%s Created TLS state. Verify cert = %d, Verify host = %d",
                          mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, mt->verify_cert, mt->verify_host);

        if (!mt->connector) {
            ZPN_LOG(AL_ERROR, "%s:%s:%s Cannot create tlv connector", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
            mt->status = zfc_pb_mt_connect_error;
            goto exit;
        }

        /* Client leg */
        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_c.mconn),
                                         1,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(mt->tag_id),
                                         &zfc_pb_mt_global_call_set_pbroker);

        /* Server leg */
        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_s.mconn),
                                         1,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(mt->tag_id),
                                         &zfc_pb_mt_global_call_set_broker);

        res  = zpn_connector_tun_connect(mconn_thread_id,
                                         (struct zpn_connector_tun *)mt->connector,
                                         &(mt->broker_mconn.mconn),
                                         1,
                                         0,
                                         mt->key.host_name);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: Server side connect returned: %s", mt->dbg_str, zpn_result_string(res));
            mt->status = zfc_pb_mt_connect_error;
            goto exit;
        }

        ZPN_DEBUG_SIEM_ALL("%s: Connected server side", mt->dbg_str);

        res  = zpn_connector_tun_connect(mconn_thread_id,
                                         (struct zpn_connector_tun *)mt->connector,
                                         &(mt->pbroker_mconn.mconn),
                                         0,
                                         mt->key.tls,
                                         mt->key.host_name);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: Client side connect returned: %s", mt->dbg_str, zpn_result_string(res));
            mt->status = zfc_pb_mt_connect_error;
            goto exit;
        }

        ZPN_DEBUG_SIEM_ALL("%s: Connected client side", mt->dbg_str);
    } else {
        res = zpn_mconn_connect_peer(&(mt->pbroker_mconn.mconn), &(mt->broker_mconn.mconn));
        if (res) {
            ZPN_LOG(AL_ERROR, "%s:%s:%s Cannot connect peers", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
            mt->status = zfc_pb_mt_connect_error;
            goto exit;
        }
    }

    zpn_mconn_set_fohh_thread_id(&(mt->pbroker_mconn.mconn), fohh_connection_get_thread_id(zfc_pb->zfc->conn));

    res = zpn_mconn_add_local_owner(&(mt->pbroker_mconn.mconn),
                                    0,
                                    mt,
                                    NULL,
                                    0,
                                    &mconn_fohh_client_pbroker_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s:%s:%s Cannot add local owner for pbroker", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
        mt->status = zfc_pb_mt_connect_error;
        goto exit;
    }

    ZPN_DEBUG_MTUNNEL("%s:%s:%s Attached mconn to zpn_fohh_client", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
    mt->status = zfc_pb_mt_connected;

exit:

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (mt->status == zfc_pb_mt_connect_error) {
            zpn_fohh_client_pbroker_mt_release_request(mt);
        } else {
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pb_mtunnel_end_cb(void *argo_cookie_ptr,
                              void *argo_structure_cookie_ptr,
                              struct argo_object *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;
    struct zpn_mtunnel_end *req = object->base_structure_void;
    const char *id = req->mtunnel_id ? req->mtunnel_id : "NULL";
    struct zpn_fohh_client_pbroker_mt *mt = NULL;
    int release_request = 0;

    ZPN_DEBUG_MCONN("%s: %s: Received tunnel end for tag_id = %d", ZFC_DBG(zfc_pb->zfc), id, req->tag_id);

    if (!req->tag_id) {
        ZPN_LOG(AL_ERROR, "%s: Expecting tag_id in mtunnel_end request", id);
        return ZPN_RESULT_NO_ERROR;
    }

    mt = zpn_fohh_client_pbroker_get_mtunnel_by_tag_id(zfc_pb, req->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "%s: Cannot find the mtunnel for tag_id = %d - mtunnel end", id, req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: Pbroker Rx from broker: %s", ZFC_DBG(zfc_pb->zfc), dump);
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    mt->broker_mconn.mconn.fin_rcvd = 1;
    if (!mt->broker_mconn.mconn.fin_rcvd_us) mt->broker_mconn.mconn.fin_rcvd_us = epoch_us();

    if (req->drop_data) {
        mt->broker_mconn.mconn.drop_tx = req->drop_data;
    }

    if (req->error) {
        if (mt->err) ZPN_FREE(mt->err);
        mt->err = ZPN_STRDUP(req->error, strlen(req->error));
    }

    if (mt->public_broker_mtunnel_id) {
        mt->status = zfc_pb_mt_remote_disconnect;
    } else {
        /*
         * This is the case where we get mtunnel_end instead of mtunnel_request_ack when
         * setting up mtunnel
         */
        mt->status = zfc_pb_mt_connect_error;
    }

    if (mt->broker_mconn.mconn.peer) {
        zpn_mconn_forward_mtunnel_end(&mt->pbroker_mconn.mconn, MT_CLOSED_TERMINATED, req->drop_data);
    } else {
        /* if mtunnel_end is returned before mtunnel is setup, call pbroker callback directly */
        release_request = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (release_request) {
        zpn_fohh_client_pbroker_mt_release_request(mt);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zfc_pb_mt_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_pbroker_mt *mt = NULL;
    struct zpn_fohh_client_pbroker_mt *tmp_mt = NULL;

    /* Reap clean mt */
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    for (mt = TAILQ_FIRST(&(free_q.reap_mt_list)); mt != NULL; mt = tmp_mt) {
        tmp_mt = TAILQ_NEXT(mt, queue_entry);

        /* If mt is clean, move from reap list to free list */
        if (zpn_mconn_fohh_tlv_clean(&mt->broker_mconn))  {
            TAILQ_REMOVE(&(free_q.reap_mt_list), mt, queue_entry);
            free_q.stats.reap_queue_count--;
            TAILQ_INSERT_TAIL(&(free_q.mt_list), mt, queue_entry);
            free_q.stats.free_queue_count++;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

int zpn_fohh_client_pbroker_init(void)
{
    if (!initialized) {
        struct event_base *base = fohh_get_thread_event_base(fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MAINTENANCE));

        local_lock = ZPATH_MUTEX_INIT;

        zpn_fohh_client_pbroker_mt_free_q_init();

        pbroker_by_id = argo_hash_alloc(8, 1);
        if (!pbroker_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot allocate pbroker_by_id hash in init");
            return ZPN_RESULT_NO_MEMORY;
        }

        mt_timer = event_new(base, -1, EV_PERSIST, zfc_pb_mt_timer_callback, NULL);
        if (mt_timer) {
            struct timeval tv;

            tv.tv_sec = 5;
            tv.tv_usec = 0;
            if (event_add(mt_timer, &tv)) {
                ZPN_LOG(AL_ERROR, "PBroker cannot activate mt timer");
            }
        } else {
            ZPN_LOG(AL_ERROR, "PBroker cannot create mt timer");
        }

#if 0
        /* Neither of these are required for pbroker since broker will
         * have already done them */
        if (zpath_debug_add_allocator(&zpn_allocator, "zpn")) {
            ZPN_LOG(AL_ERROR, "Could not add zpn allocator");
        }

        if (zpn_mconn_fohh_tlv_init_debug()) {
            ZPN_LOG(AL_ERROR, "Could not initialize debug command for fohh_tlv");
        }
#endif // 0

        initialized = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_pbroker_send_all_mt_requests(struct zpn_fohh_client_pbroker *zfc_pb)
{
    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    if (zfc_pb->status == zfc_ready) {
        struct zfc_pb_pending_mt_req *pmr = NULL;

        while ((pmr = TAILQ_FIRST(&(zfc_pb->pending_mt_reqs)))) {
            int res = ZPN_RESULT_NO_ERROR;
            struct zpn_fohh_client_pbroker_mt *mt = NULL;

            TAILQ_REMOVE(&(zfc_pb->pending_mt_reqs), pmr, queue_entry);
            mt = pmr->mt;

            ZPN_LOG(AL_NOTICE, "%s:%s: Pbroker Sending pb mtunnel request for %s:%d:%d with tag_id = %d.",
                    mt->dbg_str,
                    ZFC_DBG(zfc_pb->zfc),
                    mt->key.host_name,
                    mt->key.proto,
                    mt->key.port,
                    mt->tag_id);

            res = zpn_send_zpn_mtunnel_request(&(zfc_pb->zfc->fohh_tlv_state.tlv),
                                               fohh_connection_incarnation(zpn_mconn_fohh_tlv_get_conn(&(zfc_pb->zfc->fohh_tlv_state))),    /* fohh conn incarnation */
                                               mt->tag_id,                                                         /* Tag id */
                                               mt->key.host_name,                                                  /* application name */
                                               mt->key.proto,                                                      /* ip_proto */
                                               mt->key.port,                                                       /* port_he */
                                               0,                                                                  /* double_encrypt */
                                               0,                                                                  /* zpn_probe_type */
                                               mt->publish_gid,
                                               NULL,
                                               0,
                                               0,
                                               NULL,
                                               NULL);
            if (res) {
                ZPN_LOG(AL_NOTICE, "%s: Failed to send out mtunnel request", mt->dbg_str);
            }

            ZPN_FREE(pmr);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
}

#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_try_send_mt_requests(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_pbroker_try_send_mt_requests(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_pbroker *zfc_pb = cookie;

    if (zfc_pb->status == zfc_ready) {
        zpn_fohh_client_pbroker_send_all_mt_requests(zfc_pb);
    }
}

static int zpn_fonn_client_pbroker_schedule_mtunnel_req(struct zpn_fohh_client_pbroker *zfc_pb, struct zpn_fohh_client_pbroker_mt *mt)
{
    struct zfc_pb_pending_mt_req *pmr;
    int res;

    pmr = ZPN_CALLOC(sizeof(struct zfc_pb_pending_mt_req));
    if (!pmr) {
        ZPN_LOG(AL_NOTICE, "%s: Pbroker Cannot allocate pending mt req", mt->dbg_str);
        return ZPN_RESULT_NO_MEMORY;
    }

    pmr->mt = mt;

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(zfc_pb->pending_mt_reqs), pmr, queue_entry);
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    ZPN_DEBUG_MTUNNEL("%s: Pbroker Scheduled a mt_req", mt->dbg_str);

    if (zfc_pb->zfc && zfc_pb->zfc->conn) {
#if PBROKER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                      zpn_fohh_client_pbroker_try_send_mt_requests,
                                      zfc_pb,
                                      fohh_connection_incarnation(zfc_pb->zfc->conn));
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                               zpn_fohh_client_pbroker_try_send_mt_requests,
                               zfc_pb,
                               fohh_connection_incarnation(zfc_pb->zfc->conn));
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_pbroker_status_reporting!", mt->dbg_str);
        }
    } else {
        ZPN_DEBUG_MTUNNEL("%s: No zfc or zfv conn for scheduling a mt_req", mt->dbg_str);
    }

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_fohh_client_pbroker *zpn_fohh_client_pbroker_get_by_id(const char *auth_token)
{
    struct zpn_fohh_client_pbroker *pbroker = NULL;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);
    pbroker = argo_hash_lookup(pbroker_by_id, auth_token, strlen(auth_token), NULL);
    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);

    return pbroker;
}

static int zpn_fohh_client_pbroker_store(struct zpn_fohh_client_pbroker *pbroker, const char *auth_token)
{
    int res = ZPN_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);

    if (!pbroker_by_id) {
        pbroker_by_id = argo_hash_alloc(8, 1);
        if (!pbroker_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create pbroker hash table");
            ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
            return ZPN_RESULT_NO_MEMORY;
        }
    }

    res = argo_hash_store(pbroker_by_id, auth_token, strlen(auth_token), 0, pbroker);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot store pbroker to hash table, customer_id = %.*s", ZPATH_DEBUG_BYTES, auth_token);
    }

    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
    return res;
}

/* MUST HOLD LOCK WHEN CALLED */
static int zpn_fohh_client_pbroker_schedule_mt_terminate(void *cookie, void *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = cookie;
    struct zpn_fohh_client_pbroker_mt *mt = object;
    int res;

    ZPN_DEBUG_PBROKER("%s: zpn_fohh_client_pbroker_schedule_mt_terminate()", mt->dbg_str);

    mt->status = zfc_pb_mt_remote_disconnect;
    if (mt->err) ZPN_FREE(mt->err);
    if (zfc_pb->status == zfc_auth_fail) {
        mt->err = ZPN_STRDUP(PBROKER_FCONN_AUTH_FAIL, strlen(PBROKER_FCONN_AUTH_FAIL));
    } else {
        mt->err = ZPN_STRDUP(PBROKER_FCONN_GONE, strlen(PBROKER_FCONN_GONE));
    }

    if (zfc_pb && zfc_pb->zfc && zfc_pb->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (!mt->release_scheduled) {

#if PBROKER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                          zpn_fohh_client_pbroker_mt_release_request_thread_call,
                                          mt,
                                          mt->incarnation);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                   zpn_fohh_client_pbroker_mt_release_request_thread_call,
                                   mt,
                                   mt->incarnation);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_pbroker_mt_release_request_thread_call()!", mt->dbg_str);
            } else {
                mt->release_scheduled = 1;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pb_clientless_app_query_ack_cb(void *argo_cookie_ptr,
                                           void *argo_structure_cookie_ptr,
                                           struct argo_object *object)
{
    struct zpn_fohh_client_pbroker *zfc_pb = argo_structure_cookie_ptr;
    struct zpn_clientless_app_query_ack *query_ack = object->base_structure_void;
    struct zfc_pb_app_query *query = NULL;

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc_pb->zfc), dump);
    }

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    query = argo_hash_lookup(zfc_pb->query_by_id,
                             &(query_ack->query_id),
                             sizeof(query_ack->query_id),
                             NULL);
    if (query) {
        argo_hash_remove(zfc_pb->query_by_id,
                         &(query->query_id),
                         sizeof(query->query_id),
                         NULL);
        TAILQ_REMOVE(&zfc_pb->app_query_list, query, queue_entry);
    }

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    if (query) {
        if (zfc_pb->app_query_ack_cb) {
            (*zfc_pb->app_query_ack_cb)(query->query_id,
                                      zfc_pb->zfc->customer_id,
                                      zfc_pb->zfc->auth_token,
                                      query->host_name,
                                      query->proto,
                                      query->port,
                                      query->tls,
                                      query->publish_gid,
                                      query_ack->result,
                                      query_ack->reason);
        }
        if (query->host_name) ZPN_FREE(query->host_name);
        ZPN_FREE(query);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_pb_status_callback(struct zpn_fohh_client *zfc,
                                void *cookie_void,
                                int64_t cookie_int,
                                enum zfc_status status,
                                const char *error_string)
{
    struct zpn_fohh_client_pbroker *zfc_pb = (struct zpn_fohh_client_pbroker *)cookie_void;
    struct argo_state *argo = NULL;
    int res;
    int do_app_queries = 0;

    ZPN_DEBUG_PBROKER("%s: ZPN Fohh client pbroker status now %s. %s", ZFC_DBG(zfc), zfc_status_string(status), error_string ? error_string : "No Error");

    zfc_pb->status = status;

    if (status == zfc_authenticate) {
        argo = fohh_argo_get_rx(zfc->conn);

        /* Register zpn_client_app */
        if ((res = argo_register_structure(argo, zpn_client_app_description, zpn_pb_client_app_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_client_app_complete */
        if ((res = argo_register_structure(argo, zpn_client_app_complete_description, zpn_pb_client_app_complete_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_complete_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_request_ack */
        if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, zpn_pb_mtunnel_request_ack_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_end */
        if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, zpn_pb_mtunnel_end_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_clientless_app_query_ack */
        if ((res = argo_register_structure(argo, zpn_clientless_app_query_ack_description, zpn_pb_clientless_app_query_ack_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_clientless_app_query_ack_cb for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_dns_client_check */
        if ((res = argo_register_structure(argo, zpn_dns_client_check_description, zpn_pb_dns_client_check_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_client_check for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_app_client_check */
        if ((res = argo_register_structure(argo, zpn_app_client_check_description, zpn_pb_app_client_check_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_app_client_check for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_posture_profile */
        if ((res = argo_register_structure(argo, zpn_posture_profile_ack_description, zpn_pb_posture_profile_ack_cb, zfc_pb))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_posture_profile_ack_description for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Call user connection status callback */
        if (zfc_pb->conn_status_cb) {
            /* Connection callback */
            zfc_pb->conn_status_cb(argo, zfc_pb, status);
        }
    } else if (status == zfc_ready) {

        if (zfc_pb->conn_status_cb) {
            /* Connection callback */
            argo = fohh_argo_get_rx(zfc->conn);
            zfc_pb->conn_status_cb(argo, zfc_pb, status);
        }

        zpn_fohh_client_pbroker_send_all_mt_requests(zfc_pb);
        if (do_app_queries) {
            zpn_fohh_client_pbroker_send_all_app_queries(zfc_pb);
        }

        zpn_fohh_client_pbroker_add_monitor_timer(zfc_pb->zfc->conn,
                                                  zfc_pb,
                                                  zpn_fohh_client_pbroker_monitor_cb,
                                                  ZFC_PB_MONITOR_TIMER_S,
                                                  0);
    } else if ((status == zfc_not_connected) || (status == zfc_auth_fail)) {

        int64_t walk_key = 0;

        if (status == zfc_not_connected) {
            ZPN_DEBUG_PBROKER("ZFC disconnected");
        } else {
            ZPN_DEBUG_PBROKER("ZFC authentication failed");
        }

        /* Now need to terminate all the mtunnels */
        ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
        argo_hash_walk(zfc_pb->mtunnel_by_id,
                       &walk_key,
                       zpn_fohh_client_pbroker_schedule_mt_terminate,
                       zfc_pb);
        ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

        if (zfc_pb->conn_status_cb) {
            /* Connection callback */
            argo = fohh_argo_get_rx(zfc->conn);
            zfc_pb->conn_status_cb(argo, zfc_pb, status);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

struct zpn_fohh_client_pbroker *zpn_fohh_client_pbroker_get(const char *auth_token)
{
    struct zpn_fohh_client_pbroker *zfc_pb = NULL;

    zfc_pb = zpn_fohh_client_pbroker_get_by_id(auth_token);
    return zfc_pb;
}


struct zpn_fohh_client_pbroker *zpn_fohh_client_pbroker_create(enum zpn_client_type client_type,
                                                               enum zpn_tunnel_auth auth_type,
                                                               struct argo_object *auth_obj,
                                                               struct argo_structure_description *auth_obj_description,
                                                               const char *broker_name,
                                                               const char *cloud_root_pem_filename,
                                                               const char *client_certificate_pem_filename,
                                                               const char *client_key_pem_filename,
                                                               EVP_PKEY *client_key_mem,
                                                               const char *cloud_name,
                                                               int64_t customer_id,
                                                               const char *assertion_key,
                                                               const char *login_name,
                                                               struct argo_inet *public_ip,
                                                               struct argo_inet *private_ip,
                                                               char *customer_domain,
                                                               zfc_pb_app_query_ack_callback_f *app_query_ack_cb,
                                                               zfc_pb_dns_resp_f *dns_resp_cb,
                                                               zfc_pb_app_resp_f *app_resp_cb,
                                                               zfc_pb_posture_profile_ack_f *posture_profile_ack_cb,
                                                               zfc_pb_broker_mt_req_ack_f  *broker_mt_req_ack_cb,
                                                               zfc_pb_conn_status_cb_f *conn_status_cb,
                                                               zfc_pb_conn_auth_complete_cb_f *conn_auth_complete_cb,
                                                               void *owner_fwd_conn)
{
    struct zpn_fohh_client_pbroker *zfc_pb = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    if (!broker_name ||
        !customer_domain ||
        !client_certificate_pem_filename ||
        !client_key_pem_filename ||
        !login_name ||
        !cloud_name ||
        !auth_obj ||
        !auth_obj_description ||
        !cloud_root_pem_filename) return NULL;

    zfc_pb = zpn_fohh_client_pbroker_get_by_id(assertion_key);
    if (zfc_pb) {
        ZPN_DEBUG_PBROKER("Have pbroker , zfc_pb = %p, assertion_key = %s", zfc_pb, assertion_key);
        return zfc_pb;
    }

    ZPN_DEBUG_PRIVATE_BROKER("%s: Allocate new pbroker client for key: %s", broker_name, assertion_key);

    zfc_pb = ZPN_CALLOC(sizeof(struct zpn_fohh_client_pbroker));
    if (zfc_pb) {

        zfc_pb->zfc = zpn_fohh_client_init(client_type,
                                           auth_type,
                                           auth_obj,
                                           auth_obj_description,
                                           broker_name,
                                           customer_domain,
                                           NULL,
                                           cloud_root_pem_filename,
                                           client_certificate_pem_filename,
                                           client_key_pem_filename,
                                           client_key_mem,
                                           zfc_pb_status_callback,
                                           zfc_pb,
                                           0,
                                           NULL,
                                           NULL,
                                           NULL,
                                           login_name,
                                           NULL,
                                           NULL,
                                           cloud_name,
                                           customer_id,
                                           assertion_key,
                                           public_ip,
                                           private_ip,
                                           0,
                                           0);
        if (!zfc_pb->zfc) {
            ZPN_LOG(AL_ERROR, "Cannot create zfc");
            goto fail_free;
        }

        ZPN_LOG(AL_DEBUG, "%s: Created zfc pbroker -> broker connection", fohh_description(zfc_pb->zfc->conn));

        zfc_pb->lock = ZPATH_MUTEX_INIT;
        zfc_pb->status = zfc_init;

        zfc_pb->mtunnel_by_id = argo_hash_alloc(7, 1);
        if (!zfc_pb->mtunnel_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create mtunnel_by_id hash");
            goto fail_free;
        }

        TAILQ_INIT(&(zfc_pb->pending_mt_reqs));

        zfc_pb->busy_mt = argo_hash_alloc(7, 1);
        if (!zfc_pb->busy_mt) {
            ZPN_LOG(AL_ERROR, "Cannot create busy_mt hash");
            goto fail_free;
        }

        zfc_pb->idle_mt = argo_hash_alloc(7, 1);
        if (!zfc_pb->idle_mt) {
            ZPN_LOG(AL_ERROR, "Cannot create idle_mt hash");
            goto fail_free;
        }

        TAILQ_INIT(&(zfc_pb->idle_mt_list));

        zfc_pb->query_by_id = argo_hash_alloc(7, 1);
        if (!zfc_pb->query_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create query id hash");
            goto fail_free;
        }
        TAILQ_INIT(&(zfc_pb->app_query_list));

        zfc_pb->app_query_ack_cb = app_query_ack_cb;

        res = zpn_fohh_client_pbroker_store(zfc_pb, (char *)assertion_key);
        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot store zfc_pb");
            goto fail_free;
        }

        zfc_pb->ssl_ctx_connector = fohh_web_client_ssl_ctx_create();
        if (!zfc_pb->ssl_ctx_connector) {
            ZPN_LOG(AL_ERROR, "Cannot create SSL ctx");
            goto fail_free;
        }

        zfc_pb->ssl_ctx_connector_no_verify =  fohh_web_client_ssl_ctx_create_noverify();
        if (!zfc_pb->ssl_ctx_connector_no_verify) {
            ZPN_LOG(AL_ERROR, "Cannot create no verify SSL ctx");
            goto fail_free;
        }

        /* Set callbacks */
        zfc_pb->dns_resp_cb = dns_resp_cb;
        zfc_pb->app_resp_cb = app_resp_cb;
        zfc_pb->posture_profile_ack_cb = posture_profile_ack_cb;
        zfc_pb->broker_mt_req_ack_cb = broker_mt_req_ack_cb;
        zfc_pb->conn_status_cb = conn_status_cb;
        zfc_pb->conn_auth_complete_cb = conn_auth_complete_cb;

        /* Set owner fwd connection */
        zfc_pb->owner_fwd_conn = owner_fwd_conn;

        return zfc_pb;
    }

fail_free:
    if (zfc_pb) {
        if (zfc_pb->zfc) zpn_fohh_client_fini(zfc_pb->zfc);
        if (zfc_pb->mtunnel_by_id) argo_hash_free(zfc_pb->mtunnel_by_id);
        if (zfc_pb->busy_mt) argo_hash_free(zfc_pb->busy_mt);
        if (zfc_pb->idle_mt) argo_hash_free(zfc_pb->idle_mt);
        if (zfc_pb->query_by_id) argo_hash_free(zfc_pb->query_by_id);
        if (zfc_pb->ssl_ctx_connector) SSL_CTX_free(zfc_pb->ssl_ctx_connector);
        if (zfc_pb->ssl_ctx_connector_no_verify) SSL_CTX_free(zfc_pb->ssl_ctx_connector_no_verify);
        ZPN_FREE(zfc_pb);
    }

    return NULL;
}

static void zpn_fohh_client_pbroker_destroy(struct zpn_fohh_client_pbroker *zfc_pb)
{
    ZPN_DEBUG_PRIVATE_BROKER("zpn_fohh_client_pbroker_destroy()");

    /* Destroy any pending app queries */
    ZPN_LOG(AL_ERROR, "Implement me: destroy pending app queries");

    /* When we come here, there should be no mtunnels left */
    if (zfc_pb->zfc) zpn_fohh_client_fini(zfc_pb->zfc);
    if (zfc_pb->mtunnel_by_id) argo_hash_free(zfc_pb->mtunnel_by_id);
    if (zfc_pb->busy_mt) argo_hash_free(zfc_pb->busy_mt);
    if (zfc_pb->idle_mt) argo_hash_free(zfc_pb->idle_mt);
    if (zfc_pb->query_by_id) argo_hash_free(zfc_pb->query_by_id);
    if (zfc_pb->monitor_ev) event_free(zfc_pb->monitor_ev);
    if (zfc_pb->auth_ev) event_free(zfc_pb->auth_ev);
    if (zfc_pb->ssl_ctx_connector) SSL_CTX_free(zfc_pb->ssl_ctx_connector);
    if (zfc_pb->ssl_ctx_connector_no_verify) SSL_CTX_free(zfc_pb->ssl_ctx_connector_no_verify);

    ZPN_FREE(zfc_pb);
}

static int zpn_fohh_client_pbroker_send_all_app_queries(struct zpn_fohh_client_pbroker *zfc_pb)
{
    struct zfc_pb_app_query *query = NULL;
    struct zpn_clientless_app_query app_query;

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    TAILQ_FOREACH(query, &(zfc_pb->app_query_list), queue_entry) {
        if (query->sent) {
            break;
        }

        memset(&app_query, 0, sizeof(struct zpn_clientless_app_query));
        app_query.query_id = query->query_id;
        app_query.app_name = query->host_name;
        app_query.tcp_server_port = query->port;
        app_query.ip_protocol = query->proto;
        app_query.server_port = query->port;
        app_query.publish_gid = query->publish_gid;

        zpn_send_zpn_clientless_app_query(zfc_pb->zfc->conn,
                                          fohh_connection_incarnation(zfc_pb->zfc->conn),
                                          &app_query);
        query->sent = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_try_send_app_query(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_pbroker_try_send_app_query(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_pbroker *zfc_pb = cookie;

    if (zfc_pb->status == zfc_ready){
        zpn_fohh_client_pbroker_send_all_app_queries(zfc_pb);
    }
}

int zpn_fohh_pb_client_app_query(struct zpn_fohh_client_pbroker *zfc_pb,
                              int64_t query_id,
                              const char *hostname,
                              int proto,
                              uint16_t port,
                              int tls,
                              int64_t publish_gid)
{
    struct zfc_pb_app_query *query = NULL;
    int res;

    ZPN_DEBUG_PRIVATE_BROKER("zpn_fohh_client_app_query()");

    query = ZPN_CALLOC(sizeof(struct zfc_pb_app_query));
    if (!query) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate memory for app query");
        return ZPN_RESULT_NO_MEMORY;
    }

    query->query_id = query_id;
    query->host_name = ZPN_STRDUP(hostname, strlen(hostname));
    if (!query->host_name) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate memory for app query hostname");
        ZPN_FREE(query);
        return ZPN_RESULT_NO_MEMORY;
    }
    query->proto = proto;
    query->port = port;
    query->tls = tls;
    query->publish_gid = publish_gid;

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    res = argo_hash_store(zfc_pb->query_by_id,
                          &query->query_id,
                          sizeof(query->query_id),
                          0,
                          query);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Cannot store app query to hash");
        ZPN_FREE(query->host_name);
        ZPN_FREE(query);
        ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
        return res;
    }

    TAILQ_INSERT_HEAD(&(zfc_pb->app_query_list), query, queue_entry);

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    if (zfc_pb->zfc && zfc_pb->zfc->conn) {
#if PBROKER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                      zpn_fohh_client_pbroker_try_send_app_query,
                                      zfc_pb,
                                      fohh_connection_incarnation(zfc_pb->zfc->conn));
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                               zpn_fohh_client_pbroker_try_send_app_query,
                               zfc_pb,
                               fohh_connection_incarnation(zfc_pb->zfc->conn));
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_pbroker_try_send_app_query!");
        }
    } else {
        ZPN_DEBUG_PRIVATE_BROKER("No zfc or zfv conn for scheduling a app query");
    }

    return ZPN_RESULT_NO_ERROR;
}

/*************************************************************************************************
 *
 *  zfc_pb_mt stuff
 *
 *************************************************************************************************/

const char *zfc_pb_mt_status_string(enum zpn_fohh_client_pbroker_mt_status status)
{
    switch (status) {
    case zfc_pb_mt_connecting:
        return "zfc_pb_mt_connecting";
    case zfc_pb_mt_connected:
        return "zfc_pb_mt_connected";
    case zfc_pb_mt_remote_disconnect:
        return "zfc_pb_mt_remote_disconnect";
    case zfc_pb_mt_connect_error:
        return "zfc_pb_mt_connect_error";
    case zfc_pb_mt_release_request:
        return "zfc_pb_mt_release_request";
    case zfc_pb_mt_request_error:
        return "zfc_pb_mt_request_error";
    default:
        return "ERROR";
    }
}

/*************************************************************************************************
 *  zfc_pb_mt local owner
 */
int zpn_mconn_fohh_client_pbroker_init(struct zpn_mconn_fohh_client_pbroker *mconn_pbroker, void *mconn_self)
{
    return zpn_mconn_init(&(mconn_pbroker->mconn), mconn_self, mconn_fohh_tlv_c);
}

static int zpn_mconn_fohh_client_pbroker_receive(struct zpn_mconn_fohh_client_pbroker *mconn_pbroker,
                                                 struct evbuffer *buf,
                                                 size_t len)
{
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    int res = ZPN_RESULT_NO_ERROR;

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return res;
    }

    if (mconn_pbroker->rx_paused) {
        res = ZPN_RESULT_WOULD_BLOCK;
        goto exit;
    }

    if (len) {
        res = zpn_client_process_rx_data(mconn, buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

exit:
    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_pbroker_bind_cb(void *mconn_base,
                                                 void *mconn_self,
                                                 void *owner,
                                                 void *owner_key,
                                                 size_t owner_key_length,
                                                 int64_t *owner_incarnation)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_bind_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_pbroker_unbind_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int drop_buffered_data,
                                                   int dont_propagate,
                                                   const char *err)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_unbind_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_fohh_client_pbroker_lock_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_lock_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);
}


static void zpn_mconn_fohh_client_pbroker_unlock_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_unlock_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);
}

static int zpn_mconn_fohh_client_pbroker_transmit_cb(void *mconn_base,
                                                     void *mconn_self,
                                                     void *owner,
                                                     void *owner_key,
                                                     size_t owner_key_length,
                                                     int64_t owner_incarnation,
                                                     int fohh_thread_id,
                                                     struct evbuffer *buf,
                                                     size_t buf_len)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;
    int res = ZPN_RESULT_NO_ERROR;
    int need_lock = 0;

    ZPN_DEBUG_MCONN("%s: Send data. Len = %ld, buf_len = %ld", mt ? mt->dbg_str : "no mt", (long) evbuffer_get_length(buf), (long) buf_len);

    if (mconn->global_owner) {
        if (need_lock && mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return res;
    }

    if (!mt->busy) {
        ZPN_DEBUG_MCONN("%s: Nobody is expecting data from us", mt ? mt->dbg_str : "no mt");
        goto exit;
    }

    if (mconn_pbroker->tx_paused) {
        ZPN_DEBUG_MCONN("%s: tx_paused?", mt ? mt->dbg_str : "no mt");
        goto exit;
    }

    if (buf && buf_len) {
        mt = mconn->global_owner;

        if (mt && mt->consume_cb && mt->cookie_void && !mt->request_released) {
            size_t pre_len, post_len;
            int enq_len;

            pre_len = evbuffer_get_length(buf);
            res = (*mt->consume_cb)(mt, buf, buf_len, mt->cookie_void, mt->cookie_int);
            post_len = evbuffer_get_length(buf);

            enq_len = pre_len - post_len;
            mconn->bytes_to_client += enq_len;
            if (enq_len && mconn->peer) {
                zpn_mconn_client_window_update(mconn->peer, 0, enq_len, 0);
            }
        } else {
            ZPN_DEBUG_MCONN("No mt or consume_cb or cookie_void, released_cookie?, mt = %p, consume_cb = %p, cookie_void = %p, released_cookie = %d",
                            mt, mt->consume_cb, mt->cookie_void, mt->request_released);
        }
    }

exit:
    if (need_lock && mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_pbroker_pause_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_pause_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    if (mt->busy) {
        mconn_pbroker->rx_paused = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_resume_pbroker(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_pbroker_resume_pbroker(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_pbroker_mt *mt = cookie;

    if (mt && mt->unblock_cb && mt->cookie_void && !mt->request_released) {
        (*mt->unblock_cb)(mt, mt->cookie_void, mt->cookie_int);
    }
}

static int zpn_mconn_fohh_client_pbroker_resume_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;
    int res;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_resume_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    mconn_pbroker->rx_paused = 0;

    if (mt) {
        struct zpn_fohh_client_pbroker *zfc_pb = ((struct zpn_fohh_client_pbroker_mt *)mt)->state;

        /* Don't do anything if we are not being used by the Parser */
        if (!mt->busy) {
            return ZPN_RESULT_NO_ERROR;
        }

        if (zfc_pb && zfc_pb->zfc && zfc_pb->zfc->conn) {
#if PBROKER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                          zpn_fohh_client_pbroker_resume_pbroker,
                                          mt,
                                          0);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                   zpn_fohh_client_pbroker_resume_pbroker,
                                   mt,
                                   0);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_pbroker_resume_pbroker!");
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_pbroker_disable_read_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_pbroker_enable_read_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_fohh_client_pbroker_forward_tunnel_end_cb(void *mconn_base,
                                                        void *mconn_self,
                                                        void *owner,
                                                        void *owner_key,
                                                        size_t owner_key_length,
                                                        int64_t owner_incarnation,
                                                        const char *err,
                                                        int32_t drop_data)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_forward_tunnel_end_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    /*
     * There are two code paths to get here
     * 1. Through mtunnel received from f_conn connected to broker. In this case
     *    mt->status already set properly
     * 2. Through connector error for TLS connections. This is usually indicate
     *    TLS handshake failure. In this case, mt->status is still zfc_pb_mt_connected,
     *    we need to set it properly here
     */

    if (drop_data) {
        int need_deleted = 0;
        struct zpn_connector_tun *conn = (struct zpn_connector_tun *) mt->connector;
        struct zpn_mconn_bufferevent *peer_b = (struct zpn_mconn_bufferevent *)mconn->peer;
        char err_str[1024];

        /* Get any SSL related error string */
        err_str[0] = 0;

        if (conn && conn->ssl_status.err) {
            ZPN_DEBUG_MTUNNEL("%s:%s:%s Mtunnel TLS certificate failure was %s, where Subject=%s, Issuer=%s", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id,
                              conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);
            snprintf(err_str, sizeof(err_str), "%s, Certificate Error: %s, Subject=%s, Issuer=%s",
                     PBROKER_MT_TLS_SETUP_FAIL, conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);
        } else if (peer_b && peer_b->ssl_erk) {
            char dbg_buf[256];

            dbg_buf[0] = 0;
            ERR_error_string_n(peer_b->ssl_erk, dbg_buf, sizeof(dbg_buf));
            snprintf(err_str, sizeof(err_str), "%s,%s", PBROKER_MT_TLS_SETUP_FAIL, dbg_buf);
        }

        if (strlen(err_str)) {
            ZPN_DEBUG_MTUNNEL("%s:%s:%s Mtunnel setup failure due to TLS setup with remote server failed, err = %s", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, err);
            mt->status = zfc_pb_mt_connect_error;
            if (mt->err) ZPN_FREE(mt->err);
            mt->err = ZPN_STRDUP(err_str, strlen(err_str));
        }

        /* Force destroy, so call status_cb */
        mt->deleting = 1;
        if (!mt->request_released) {
            mt->request_released = 1;
            need_deleted = 1;
        }

        if (need_deleted && mt->status_cb && mt->cookie_void) {
            ZPN_DEBUG_MTUNNEL("%s:%s:%s calling status callback to report remote disconnect", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    } else {
        /*
         * Normal disconnect, no need to call status_cb. when request is done,
         * we will be called, mt will be moved to idle list and destroyed.
         */
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_fohh_client_pbroker_window_update_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id,
                                                    int tx_len,
                                                    int batch_win_upd)
{
    struct zpn_mconn_fohh_client_pbroker *mconn_pbroker = mconn_base;
    struct zpn_mconn *mconn = &(mconn_pbroker->mconn);
    struct zpn_fohh_client_pbroker_mt *mt = mconn->global_owner;
    int res;

    ZPN_DEBUG_MCONN("%s: zpn_mconn_fohh_client_pbroker_window_update_cb - mconn_pbroker: %p, mconn: %p",
                    mt ? mt->dbg_str : "no mt", mconn_pbroker, mconn);

    if (mt) {
        struct zpn_fohh_client_pbroker *zfc_pb = ((struct zpn_fohh_client_pbroker_mt *)mt)->state;

        /* Don't do anything if we are not being used by the Parser */
        if (!mt->busy) {
            return;
        }

        if (zfc_pb && zfc_pb->zfc && zfc_pb->zfc->conn) {
#if PBROKER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                          zpn_fohh_client_pbroker_resume_pbroker,
                                          mt,
                                          0);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                   zpn_fohh_client_pbroker_resume_pbroker,
                                   mt,
                                   0);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_pbroker_resume_pbroker!");
            }
        }
    }

    return;
}

void zpn_mconn_fohh_client_pbroker_stats_update_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id,
                                                    enum zpn_mconn_stats stats_name)
{
    return;
}

/* Pbroker leg calls */
const struct zpn_mconn_local_owner_calls mconn_fohh_client_pbroker_calls = {
    zpn_mconn_fohh_client_pbroker_bind_cb,
    zpn_mconn_fohh_client_pbroker_unbind_cb,
    zpn_mconn_fohh_client_pbroker_lock_cb,
    zpn_mconn_fohh_client_pbroker_unlock_cb,
    zpn_mconn_fohh_client_pbroker_transmit_cb,
    zpn_mconn_fohh_client_pbroker_pause_cb,
    zpn_mconn_fohh_client_pbroker_resume_cb,
    zpn_mconn_fohh_client_pbroker_forward_tunnel_end_cb,
    zpn_mconn_fohh_client_pbroker_window_update_cb,
    zpn_mconn_fohh_client_pbroker_stats_update_cb,
    zpn_mconn_fohh_client_pbroker_disable_read_cb,
    zpn_mconn_fohh_client_pbroker_enable_read_cb
};


/*************************************************************************************************
 *  zfc_pb_mt global owner
 */

static int global_bind_broker_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;
    struct zpn_fohh_client_pbroker *zfc_pb = ((struct zpn_fohh_client_pbroker_mt *)mt)->state;
    int res;

    ZPN_DEBUG_MCONN("%s: global_bind_broker_cb", mt ? mt->dbg_str : "no mt");

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    res = argo_hash_store(zfc_pb->mtunnel_by_id,
                          global_owner_key,
                          global_owner_key_length,
                          0,
                          mt);
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    return res;
}

static int global_unbind_broker_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;
    struct zpn_fohh_client_pbroker *zfc_pb = ((struct zpn_fohh_client_pbroker_mt *)mt)->state;
    int res;

    ZPN_DEBUG_MCONN("%s: global_unbind_broker_cb", mt ? mt->dbg_str : "no mt");

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);
    res = argo_hash_remove(zfc_pb->mtunnel_by_id,
                           global_owner_key,
                           global_owner_key_length,
                           NULL);
    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    return res;
}

static int global_bind_pbroker_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{

    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;
    ZPN_DEBUG_MCONN("%s: global_bind_pbroker_cb", mt ? mt->dbg_str : "no mt");

    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_pbroker_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{

    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;
    ZPN_DEBUG_MCONN("%s: global_unbind_pbroker_cb", mt ? mt->dbg_str : "no mt");

    return ZPN_RESULT_NO_ERROR;
}

static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_lock_cb", mt ? mt->dbg_str : "no mt");

    /********************************************************/
    /* Lock order zapp_mtunnel_lock -> pbroker_mtunnel_lock */
    /********************************************************/

    if (mt && mt->lock_cb) {
        /* Relay lock callback */
        ZPN_DEBUG_MCONN("%s: Relay to fwd mtunnel - lock callback", mt ? mt->dbg_str : "no mt");
        mt->lock_cb(mt, mt->cookie_void, mt->cookie_int);
    }

    ZPN_DEBUG_MCONN("%s: Locking self global lock", mt ? mt->dbg_str : "no mt");
    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    if (mt == NULL) {
        ZPN_DEBUG_MCONN("global_unlock_cb: no mt");
        return;
    }

    ZPN_DEBUG_MCONN("%s: global_unlock_cb", mt ? mt->dbg_str : "no mt");

    /********************************************************/
    /* Lock order zapp_mtunnel_lock -> pbroker_mtunnel_lock */
    /********************************************************/

    if (mt && mt->unlock_cb) {
        /* Relay lock callback */
        ZPN_DEBUG_MCONN("%s: Relay to fwd mtunnel - unlock callback", mt ? mt->dbg_str : "no mt");
        mt->unlock_cb(mt, mt->cookie_void, mt->cookie_int);
    }

    ZPN_DEBUG_MCONN("%s: Unlocking self global lock", mt ? mt->dbg_str : "no mt");
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_terminate_cb", mt ? mt->dbg_str : "no mt");
}

static int global_ip_proto_cb(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_ip_proto_cb", mt ? mt->dbg_str : "no mt");

    return mt->key.proto;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_double_encrypt_cb", mt ? mt->dbg_str : "no mt");

    return 0;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;
    struct zpn_mconn *outer_mconn = (struct zpn_mconn *)mconn_base;

    ZPN_DEBUG_MCONN("%s: global_outer_mconn_cb", mt ? mt->dbg_str : "no mt");

    if (mt->key.tls && mt->connector) {
        struct zpn_connector_tun *conn = (struct zpn_connector_tun *)mt->connector;

        if (mconn_base == &conn->mconn_c.mconn) {
            outer_mconn = &(mt->broker_mconn.mconn);
        } else if (mconn_base == &conn->mconn_s.mconn) {
            outer_mconn = &(mt->pbroker_mconn.mconn);
        }
    }

    return (struct zpn_mconn *)outer_mconn;
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_incarnation_cb", mt ? mt->dbg_str : "no mt");

    return mt->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;

    ZPN_DEBUG_MCONN("%s: global_validate_incarnation_cb", mt ? mt->dbg_str : "no mt");

    if (mt->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *global_owner,
                                  void *global_owner_key,
                                  size_t global_owner_key_length)
{
    struct zpn_fohh_client_pbroker_mt *mt = mconn_self;


    ZPN_DEBUG_MCONN("%s: global_mtunnel_id_cb", mt ? mt->dbg_str : "no mt");

    ZPN_LOG(AL_CRITICAL, "XXX public broker or private broker here?");

    return mt->private_broker_mtunnel_id;
}

struct zpn_mconn_global_owner_calls zfc_pb_mt_global_call_set_broker = {
    global_bind_broker_cb,
    global_unbind_broker_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

struct zpn_mconn_global_owner_calls zfc_pb_mt_global_call_set_pbroker = {
    global_bind_pbroker_cb,
    global_unbind_pbroker_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

/*************************************************************************************************
 *  zfc_pb_mt API
 */

static void zpn_fohh_client_pbroker_mt_set_key(struct zfc_pb_mt_key  *key,
                                              char *host_name,
                                              int proto,
                                              uint16_t port,
                                              int tls,
                                              int verify_cert,
                                              int32_t pbroker_conn_id)
{
    memset(key, 0, sizeof(struct zfc_pb_mt_key));

    snprintf(key->host_name, sizeof(key->host_name), "%s", host_name);
    key->proto = proto;
    key->port = port;
    key->tls = tls;
    key->verify_cert = verify_cert;
    key->pbroker_conn_id = pbroker_conn_id;
}

static int zpn_fohh_client_pbroker_mt_destroy(struct zpn_fohh_client_pbroker_mt *mt, char *err)
{
    int res = ZPN_RESULT_NO_ERROR;

    ZPN_DEBUG_MTUNNEL("%s: Terminating pbroker mt", mt->dbg_str);

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    res = zpn_mconn_terminate(&(mt->broker_mconn.mconn), 1, 0, err, NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Terminating pbroker broker mconn returned %s", mt->dbg_str, zpn_result_string(res));
    }

    if (mt->key.tls && mt->connector) {
        res = zpn_mconn_terminate(&(mt->pbroker_mconn.mconn), 1, 0, err, NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Terminating pbroker mconn returned %s", mt->dbg_str, zpn_result_string(res));
        }
    }

    if (mt->private_broker_mtunnel_id) {
        ZPN_FREE(mt->private_broker_mtunnel_id);
        mt->private_broker_mtunnel_id = NULL;
    }
    if (mt->public_broker_mtunnel_id) {
        ZPN_FREE(mt->public_broker_mtunnel_id);
        mt->public_broker_mtunnel_id = NULL;
    }
    if (mt->err) {
        ZPN_FREE(mt->err);
        mt->err = NULL;
    }
    if (mt->connector) zpn_connector_tun_destroy(mt->connector);

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    zpn_fohh_client_pbroker_mt_reap(mt);

    return res;
}

struct zpn_fohh_client_pbroker_mt *zpn_fohh_client_pbroker_mt_get(const char *dbg_str,
                                                                  struct zpn_fohh_client_pbroker *zfc_pb,
                                                                  char *host_name,
                                                                  int proto,
                                                                  uint16_t port,
                                                                  int tls,
                                                                  char *private_broker_mtunnel_id,
                                                                  zfc_pb_mt_status_callback_f *status_cb,
                                                                  zfc_pb_mt_consume_callback_f *consume_cb,
                                                                  zfc_pb_mt_unblock_callback_f *unblock_cb,
                                                                  zfc_pb_mt_trans_log_callback_f *trans_log_cb,
                                                                  zfc_pb_mt_lock_callback_f *lock_cb,
                                                                  zfc_pb_mt_unlock_callback_f *unlock_cb,
                                                                  void *cookie_void,
                                                                  int64_t cookie_int,
                                                                  int64_t publish_gid,
                                                                  int verify_cert,
                                                                  int32_t pbroker_conn_id)
{
    struct zpn_fohh_client_pbroker_mt *mt = NULL;
    struct zfc_pb_mt_key  key;
    struct zfc_pb_mt_key  key1;
    int res;

    ZPN_DEBUG_PBROKER("zpn_fohh_client_pbroker_mt_get(): %s, hostname=%s, proto=%d, port=%d, tls=%d, verify_cert=%d, pbroker_conn_id=%d",
                       dbg_str, host_name, proto, port, tls, verify_cert, pbroker_conn_id);

    if (!host_name) {
        ZPN_LOG(AL_INFO, "%s: Trying to get broker, but does not supply host_name", dbg_str);
        return NULL;
    }

    if (strlen(host_name) > (PBROKER_MAX_HOSTNAME_LEN - 1)) {
        ZPN_LOG(AL_INFO, "%s: Too big domain name = %s", dbg_str, host_name);
        return NULL;
    }

    zpn_fohh_client_pbroker_mt_set_key(&key, host_name, proto, port, tls, verify_cert, pbroker_conn_id);

    ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    /* We become busy, reset the idle start time */
    zfc_pb->idle_start_us = 0;

    mt = argo_hash_lookup(zfc_pb->idle_mt, &key, sizeof(struct zfc_pb_mt_key), NULL);
    if (mt && (mt->status == zfc_pb_mt_connected)) {
        /* Found an existing mtunnel in idle queue */
        argo_hash_remove(zfc_pb->idle_mt,
                         &mt->key,
                         sizeof(struct zfc_pb_mt_key),
                         mt);

        TAILQ_REMOVE(&zfc_pb->idle_mt_list, mt, queue_entry);

        zfc_pb->num_idle_mt--;

        res = argo_hash_store(zfc_pb->busy_mt,
                              &mt->key,
                              sizeof(struct zfc_pb_mt_key),
                              1,
                              mt);
        if (res == ARGO_RESULT_NO_MEMORY) {
            ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
            ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
            zpn_fohh_client_pbroker_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
            return NULL;
        }

        zfc_pb->num_busy_mt++;

        ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

        /* Set mt callbacks */
        mt->status_cb = status_cb;
        mt->consume_cb = consume_cb;
        mt->unblock_cb = unblock_cb;
        mt->lock_cb = lock_cb;
        mt->unlock_cb = unlock_cb;
        mt->trans_log_cb = trans_log_cb;

        /* Set other mt state */
        mt->cookie_void = cookie_void;
        mt->cookie_int = cookie_int;
        mt->publish_gid = publish_gid;
        mt->busy = 1;
        mt->idle_start_us = 0;
        mt->dbg_str = dbg_str;

        ZPN_DEBUG_PBROKER("%s:%s:%s Got mtunnel from idle queue, tag_id = %d", dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, mt->tag_id);

        return mt;
    }

    /* We cannot find mt with the correct pbroker_conn_id, try to find any non-nailed up idle mt to the app */
    if (pbroker_conn_id) {
        zpn_fohh_client_pbroker_mt_set_key(&key1, host_name, proto, port, tls, verify_cert, 0);
        mt = argo_hash_lookup(zfc_pb->idle_mt, &key1, sizeof(struct zfc_pb_mt_key), NULL);
        if (mt && (mt->status == zfc_pb_mt_connected)) {

            /* Found an existing mtunnel in idle queue, but it is not nailed up */
            argo_hash_remove(zfc_pb->idle_mt,
                             &mt->key,
                             sizeof(struct zfc_pb_mt_key),
                             mt);

            TAILQ_REMOVE(&zfc_pb->idle_mt_list, mt, queue_entry);

            zfc_pb->num_idle_mt--;

            /* Change it to new key */
            mt->key = key;

            res = argo_hash_store(zfc_pb->busy_mt,
                                  &mt->key,
                                  sizeof(struct zfc_pb_mt_key),
                                  1,
                                  mt);
            if (res == ARGO_RESULT_NO_MEMORY) {
                ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
                ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
                zpn_fohh_client_pbroker_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
                return NULL;
            }

            zfc_pb->num_busy_mt++;

            ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

            /* Set mt callbacks */
            mt->status_cb = status_cb;
            mt->consume_cb = consume_cb;
            mt->unblock_cb = unblock_cb;
            mt->lock_cb = lock_cb;
            mt->unlock_cb = unlock_cb;
            mt->trans_log_cb = trans_log_cb;

            /* Set other mt state */
            mt->cookie_void = cookie_void;
            mt->cookie_int = cookie_int;
            mt->publish_gid = publish_gid;
            mt->busy = 1;
            mt->idle_start_us = 0;
            mt->dbg_str = dbg_str;
            mt->pbroker_conn_id = pbroker_conn_id;

            ZPN_DEBUG_PBROKER("%s:%s:%s Got mtunnel from idle queue, tag_id = %d", dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id, mt->tag_id);

            return mt;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    /* We cannot find in idle queue, so need to allocate new ones */

    ZPN_DEBUG_PBROKER("%s: Cannot get mtunnel from idle queue, need to create one", dbg_str);
    mt = zpn_fohh_client_pbroker_mt_allocate();

    if (mt) {
        int32_t tag_id;

        ZPN_DEBUG_PBROKER("%s: Initialize created mt", dbg_str);

        mt->lock = ZPATH_MUTEX_INIT;

        zpn_fohh_client_pbroker_mt_set_key(&mt->key, host_name, proto, port, tls, verify_cert, pbroker_conn_id);

        tag_id = zpn_fohh_client_next_tag_id(zfc_pb->zfc);
        mt->tag_id = tag_id;
        mt->state = zfc_pb;
        mt->status = zfc_pb_mt_connecting;

        mt->private_broker_mtunnel_id = ZPN_STRDUP(private_broker_mtunnel_id, strlen(private_broker_mtunnel_id));

        /* Set mt callbacks */
        mt->status_cb = status_cb;
        mt->consume_cb = consume_cb;
        mt->unblock_cb = unblock_cb;
        mt->lock_cb = lock_cb;
        mt->unlock_cb = unlock_cb;
        mt->trans_log_cb = trans_log_cb;

        /* Set other mt state */
        mt->cookie_void = cookie_void;
        mt->cookie_int = cookie_int;
        mt->publish_gid = publish_gid;
        mt->verify_cert = verify_cert ? 1 : 0;
        mt->verify_host = mt->verify_cert;
        mt->busy = 1;
        mt->idle_start_us = 0;
        mt->dbg_str = dbg_str;
        mt->pbroker_conn_id = pbroker_conn_id;

        ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

        res = argo_hash_store(zfc_pb->busy_mt,
                              &mt->key,
                              sizeof(struct zfc_pb_mt_key),
                              1,
                              mt);
        if (res == ARGO_RESULT_NO_MEMORY) {
            ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
            ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
            zpn_fohh_client_pbroker_mt_reap(mt);
            return NULL;
        }

        zfc_pb->num_busy_mt++;

        ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

        ZPN_DEBUG_PBROKER("%s: Adding global owners", dbg_str);

        zpn_mconn_fohh_tlv_init(&(mt->broker_mconn), mt, mconn_fohh_tlv_c);
        res = zpn_mconn_add_global_owner(&(mt->broker_mconn.mconn),
                                         0,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(tag_id),
                                         &zfc_pb_mt_global_call_set_broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to add global owner", dbg_str);
        }

        zpn_mconn_fohh_client_pbroker_init(&(mt->pbroker_mconn), mt);
        res = zpn_mconn_add_global_owner(&(mt->pbroker_mconn.mconn),
                                         0,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(tag_id),
                                         &zfc_pb_mt_global_call_set_pbroker);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to add global owner", dbg_str);
        }

        res = zpn_fonn_client_pbroker_schedule_mtunnel_req(zfc_pb, mt);

        ZPN_DEBUG_MTUNNEL("%s: %s: create mt = %p, tag_id = %d", dbg_str, ZFC_DBG(zfc_pb->zfc), mt, mt->tag_id);

        return mt;
    }

    ZPN_LOG(AL_ERROR, "%s: Cannot allocate new mtunnel", dbg_str);

    return NULL;
}

static int zpn_fohh_client_pbroker_mt_busy_to_idle_list(struct zpn_fohh_client_pbroker_mt *mt, int zfc_pb_locked)
{
    struct zpn_fohh_client_pbroker *zfc_pb = mt->state;
    int res;

    ZPN_DEBUG_PBROKER("%s:%s:%s zpn_fohh_client_pbroker_mt_busy_to_idle_list()", mt->dbg_str, mt->private_broker_mtunnel_id, mt->public_broker_mtunnel_id);

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    mt->cookie_void = NULL;
    mt->cookie_int = 0;
    mt->idle_start_us = epoch_us();
    mt->deleting = 0;
    mt->request_released = 0;
    mt->request_done = 0;
    mt->release_scheduled = 0;
    mt->publish_gid = 0;
    mt->busy = 0;
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    /* Move from busy list to idle list */

    if (!zfc_pb_locked) ZPATH_MUTEX_LOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    TAILQ_INSERT_TAIL(&zfc_pb->idle_mt_list, mt, queue_entry);

    res = argo_hash_remove(zfc_pb->busy_mt,
                           &mt->key,
                           sizeof(struct zfc_pb_mt_key),
                           mt);
    if (res == ARGO_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_ERROR, "Releasing mtunnel not in busy hash?");
        if (!zfc_pb_locked) ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
        return res;
    }

    zfc_pb->num_busy_mt--;

    res = argo_hash_store(zfc_pb->idle_mt,
                          &mt->key,
                          sizeof(struct zfc_pb_mt_key),
                          1,
                          mt);
    if (res == ARGO_RESULT_NO_MEMORY) {
        ZPN_LOG(AL_ERROR, "Cannot store mtunnel in idle hash?");
        if (!zfc_pb_locked) ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);
        zpn_fohh_client_pbroker_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
        return res;
    }

    zfc_pb->num_idle_mt++;

    if (!zfc_pb_locked) ZPATH_MUTEX_UNLOCK(&(zfc_pb->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_fohh_client_pbroker_mt_release_request(struct zpn_fohh_client_pbroker_mt *mt)
{
    int move_to_idle = 0;

    ZPN_DEBUG_PBROKER("%s: zpn_fohh_client_pbroker_mt_release_request()", mt->dbg_str);

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    if (mt->status_cb && mt->cookie_void && !mt->request_released) {
        mt->request_released = 1;
        if (mt->request_done) {
            ZPN_DEBUG_PBROKER("%s: Move mtunnel to idle queue", mt->dbg_str);
            move_to_idle = 1;
        }
        if (mt->status == zfc_pb_mt_connected) {
            ZPN_DEBUG_PBROKER("%s: Close mtunnel", mt->dbg_str);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, zfc_pb_mt_release_request, mt->err);
        } else {
            ZPN_DEBUG_PBROKER("%s: mtunnel is closed, sending status = %d", mt->dbg_str, mt->status);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    } else {
        ZPN_DEBUG_PBROKER("%s: not need to release request: status_cb = %p, cookie_void = %p, released = %d", mt->dbg_str, mt->status_cb, mt->cookie_void, mt->request_released);
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (move_to_idle) {
        zpn_fohh_client_pbroker_mt_busy_to_idle_list(mt, 0);
    }
}

#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_mt_release_request_thread_call(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_pbroker_mt_release_request_thread_call(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_pbroker_mt *mt = cookie;

    zpn_fohh_client_pbroker_mt_release_request(mt);
}

/* Assume we have the lock of mt when calling this */
static int zpn_fohh_client_pbroker_schedule_release_request(struct zpn_fohh_client_pbroker_mt *mt)
{
    struct zpn_fohh_client_pbroker *zfc_pb = mt->state;
    int res;

    if (zfc_pb && zfc_pb->zfc && zfc_pb->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (!mt->release_scheduled) {
#if PBROKER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                          zpn_fohh_client_pbroker_mt_release_request_thread_call,
                                          mt,
                                          mt->incarnation);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                   zpn_fohh_client_pbroker_mt_release_request_thread_call,
                                   mt,
                                   mt->incarnation);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_pbroker_schedule_release_request_thread_call()!", mt->dbg_str);
            } else {
                mt->release_scheduled = 1;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_fohh_client_pbroker_mt_release(struct zpn_fohh_client_pbroker_mt *mt, int http_transaction_error)
{
    int move_to_idle = 0;

    ZPN_DEBUG_PBROKER("%s: zpn_fohh_client_pbroker_mt_release(), http_transaction_error = %d", mt->dbg_str, http_transaction_error);

    if (!mt) {
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    mt->request_done = 1;
    if (http_transaction_error) {
        mt->status = zfc_pb_mt_request_error;
    }
    if (!mt->request_released) {
        zpn_fohh_client_pbroker_schedule_release_request(mt);
    } else {
        move_to_idle = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (move_to_idle) {
        zpn_fohh_client_pbroker_mt_busy_to_idle_list(mt, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zfc_pb_mt_consume(struct zpn_fohh_client_pbroker_mt *mt, struct evbuffer *buf, size_t len, int64_t incarnation)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (buf && mt) {
        res = zpn_mconn_fohh_client_pbroker_receive(&(mt->pbroker_mconn), buf, len);
    }

    return res;
}

#if PBROKER_USE_ZEVENT
static void zpn_fohh_client_pbroker_mt_async_transmit(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_pbroker_mt_async_transmit(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_pbroker_mt *mt = cookie;

    ZPN_DEBUG_PBROKER("%s: zpn_fohh_client_pbroker_mt_async_transmit()", mt->dbg_str);

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    zpn_client_drain_tx_data(&(mt->pbroker_mconn.mconn));
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
}

static int zpn_fohh_client_pbroker_schedule_mtunnel_transmit(struct zpn_fohh_client_pbroker_mt *mt)
{
    struct zpn_fohh_client_pbroker *zfc_pb = mt->state;
    int res;

    if (zfc_pb && zfc_pb->zfc && zfc_pb->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
#if PBROKER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                                      zpn_fohh_client_pbroker_mt_async_transmit,
                                      mt,
                                      mt->incarnation);
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfc_pb->zfc->conn),
                               zpn_fohh_client_pbroker_mt_async_transmit,
                               mt,
                               mt->incarnation);
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_pbroker_mt_async_transmit()!", mt->dbg_str);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zfc_pb_mt_unblock(struct zpn_fohh_client_pbroker_mt *mt)
{
    /* Global owner can accept data again */
    if (mt) {
        zpn_fohh_client_pbroker_schedule_mtunnel_transmit(mt);
    }
    return ZPN_RESULT_NO_ERROR;
}

/*************************************************************************************************
 *  zfc_pb_mt Object Allocation/Free
 */
static void zpn_fohh_client_pbroker_mt_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(free_q.mt_list));
    TAILQ_INIT(&(free_q.reap_mt_list));
}

static void zpn_fohh_client_pbroker_mt_reap(struct zpn_fohh_client_pbroker_mt *mt)
{
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(free_q.reap_mt_list), mt, queue_entry);
    free_q.stats.reap_queue_count++;
    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

static struct zpn_fohh_client_pbroker_mt *zpn_fohh_client_pbroker_mt_allocate(void)
{
    struct zpn_fohh_client_pbroker_mt *mt = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    if ((mt = TAILQ_FIRST(&(free_q.mt_list)))) {
        TAILQ_REMOVE(&(free_q.mt_list), mt, queue_entry);
        free_q.stats.free_queue_count--;
        incarnation = mt->incarnation;
        incarnation++;
        if (mt->connector) ZPN_FREE(mt->connector);
        memset(mt, 0, sizeof(struct zpn_fohh_client_pbroker_mt));
        mt->incarnation = incarnation;
    } else {
        mt = (struct zpn_fohh_client_pbroker_mt *)ZPN_CALLOC(sizeof(struct zpn_fohh_client_pbroker_mt));
        if (mt) {
            free_q.stats.allocations++;
            memset(mt, 0, sizeof(struct zpn_fohh_client_pbroker_mt));
            mt->lock = ZPATH_MUTEX_INIT;
            mt->incarnation = 1;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);

    return mt;
}
