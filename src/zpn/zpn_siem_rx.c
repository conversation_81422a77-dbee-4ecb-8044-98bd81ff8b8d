/*
 * zpn_siem_rx.c. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 */

#include <event2/event.h>

#include "fohh/fohh.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_local.h"
#include "argo/argo_buf.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_rpc.h"
#include "zpn/zpn_siem_rx.h"

#include "lookup_lib/lookup_lib.h"
#include "zpn/zpn_fohh_client_slogger.h"
#include "zpn/zpn_siem_serialize.h"

/* Tables needed for lookup initialization: */
/* Zpath Tables */
#include "zpath_lib/zpath_category.h"
#include "zpath_lib/zpath_category_compiled.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_customer_compiled.h"
#include "zpath_lib/zpath_rule.h"
#include "zpath_lib/zpath_rule_compiled.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_instance_compiled.h"
/* ZPN Tables */
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_compiled.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_compiled.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application_server_compiled.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistant_group_compiled.h"
#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_assistant_table_compiled.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_policy_set_compiled.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_compiled.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_saml_attrs_compiled.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_compiled.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_compiled.h"
#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_posture_profile_compiled.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_trusted_network_compiled.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_private_broker_table_compiled.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_group_compiled.h"
#include "zpath_lib/zpath_service.h"
#include "zpath_lib/zpath_service_compiled.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_idp_cert_compiled.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_table_compiled.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_group_compiled.h"

#define MY_TEST    0

static char broker_name[255];

TAILQ_HEAD(ack_queue_head, ack_queue);

struct zhash_table *zpn_siem_debug_stats_table = NULL;
zpath_mutex_t zpn_siem_debug_stats_table_lock;

struct ack_queue {
    TAILQ_ENTRY(ack_queue) list;
    int64_t tx_timestamp;
    int64_t id;
};

struct rx_state {
    char *name;

    int64_t ref_count;

    int64_t siem_gid;
    int64_t siem_sequence;
    int     use_tls;
    struct zpn_siem_debug_stats *siem_stat;

    /* Connection from the broker. Incarnation is incarnation when we
     * were created */
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* Queue to keep track of acknowledgements we have outstanding. If
     * the SIEM doesn't provide acknowledgements, we fake an
     * acknowledgement after a given amount of time. */
    struct ack_queue_head acks;
    int64_t ack_count;
    struct event *ack_timer;
    /* The amount of time (microseconds) that we delay acks. This is
     * primarily used for ack-less SIEMS, but the code path is used
     * for all (just with a delay of 0) */
    int64_t ack_delay_us;

    unsigned deleting:1;
    unsigned ack_timer_running:1;
    unsigned self_ack:1;           // If set, we will acknowledge siem transmissions ourselves after some time.
    unsigned rx_disabled:1;

    /*
     * When we create a link to another module, we increment our
     * reference count.
     *
     * When a module tells us our reference is complete (connection
     * closed, mt closed, etc), we drop reference count. We also note
     * to ourselves whether or not we need to tell this module to
     * close (we no longer need to close this module- it told us it is
     * gone)
     *
     * When we need to tell a module to finish up (tell it to close
     * connection, etc), we simply mark that we have told the module
     * that it is being closed in order to keep repeats from
     * hapenning.
     *
     * This state is accomplished by:
     *
     * 'existence of module' (i.e. does *zfcs exist? If not, we don't
     * need to do any of this for that module)
     *
     * not_needed_XXX_close: We set this to indicate that we don't
     * need ever send a close to the module. (because we already did,
     * or were told by the module already)
     */
    unsigned not_needed_zfcs_close:1;  /* Associated with *zfcs */
    unsigned not_needed_mt_close:1;    /* Associated with *mt   */
    unsigned not_needed_fohh_close:1;  /* Associated with *fohh */

    /* Object queue waiting to transmit */
    struct zpn_siem_element_head objects;
    int64_t queue_count;
    int64_t queue_size;

    struct zpath_mutex lock;

    int64_t objects_received;
    int64_t objects_transmitted;
    int64_t objects_acknowledged;

    struct zpn_fohh_client_slogger *zfcs;
    struct zpn_fohh_client_slogger_mt *mt;
    int64_t mt_incarnation;

    char *siem_host;
    int32_t siem_port;
    int64_t customer_id;

    /* rs ref_count stats */
    int64_t timer_stats;
    int64_t fohh_stats;
    int64_t zfcs_stats;
    int64_t mt_stats;

    uint64_t conn_start_time;
};

enum rx_state_destroy_type {
    destroy_generic = 0,
    destroy_timer,
    destroy_fohh,
    destroy_zfcs,
    destroy_mt
};

static struct zpn_siem_conn_details_log g_siem_conn_stats;
static struct zpn_siem_debug_stats g_siem_debug_stats;

static int zpn_siem_out(struct rx_state *rs);
static void rs_destroy(struct rx_state *rs, enum rx_state_destroy_type dest);
static int zpn_siem_fohh_rx_conn_close(struct fohh_connection *f_conn);
static void update_name(struct rx_state *rs);

const struct zpn_siem_debug_stats *zpn_siem_get_global_debug_stats()
{
    return &(g_siem_debug_stats);
}

struct zpn_siem_conn_details_log *zpn_siem_get_global_conn_stats()
{
    return &(g_siem_conn_stats);
}

void zpn_siem_update_debug_stats(struct zpn_siem_debug_stats *value, enum debug_log_error_type error_type)
{
    if (value) {
        __sync_fetch_and_add_8(&(value->zpn_siem_error_type_count[error_type]), 1);
    }
    __sync_fetch_and_add_8(&(g_siem_debug_stats.zpn_siem_error_type_count[error_type]), 1);
}

struct zpn_siem_debug_stats * debug_stat_init_one(struct rx_state *rs, uint64_t siem_gid, uint64_t customer_id)
{
    char key[64] = {'\0'};
    snprintf(key, sizeof(key), "%"PRIu64, siem_gid);
    char zpn_siem_conn_source[256];
    char zpn_siem_conn_destination[256];
    zpn_fohh_client_slogger_get_conn(rs->zfcs, zpn_siem_conn_destination, sizeof(zpn_siem_conn_destination));
    snprintf(zpn_siem_conn_source, sizeof(zpn_siem_conn_source),
            "%s",
            rs->f_conn ? fohh_description(rs->f_conn) : "No_F_conn");
    ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
    struct zpn_siem_debug_stats *stats_counter = zhash_table_lookup(zpn_siem_debug_stats_table,
                                                                    key,
                                                                    strlen(key),
                                                                    NULL);
    if(stats_counter == NULL){
        stats_counter = SIEM_CALLOC(sizeof(*stats_counter));
        if(stats_counter){
            stats_counter->is_active              = 1;
            stats_counter->siem_gid               = siem_gid;
            stats_counter->customer_id            = customer_id;
            stats_counter->connection_start_time  = rs->conn_start_time;
            stats_counter->connection_end_time    = -1;
            stats_counter->connection_duration    = -1;
            stats_counter->log_received_count     = 0;
            stats_counter->log_dropped_count      = 0;
            stats_counter->log_transfered_count   = 0;
            stats_counter->connection_destination = ZPN_STRDUP(zpn_siem_conn_destination,
                                                               strlen(zpn_siem_conn_destination));
            stats_counter->connection_source      = ZPN_STRDUP(zpn_siem_conn_source,
                                                               strlen(zpn_siem_conn_source));
            zhash_table_store(zpn_siem_debug_stats_table, key, strlen(key), 0, stats_counter);
            ZPN_DEBUG_SIEM_ALL("%s: Stored %p in zpn_siem_debug_stats_table ", key, stats_counter);
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_connection_count, 1);
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_active_connection_count, 1);
        } else {
            ZPN_DEBUG_SIEM_ALL("%s: Failed to allocate siem_stat counter", key);
        }
    } else {
        if(stats_counter->is_active == 0){
            stats_counter->is_active             = 1;
            stats_counter->incarnation          += 1;
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_reconnection_count, 1);
            stats_counter->connection_start_time = rs->conn_start_time;
            stats_counter->connection_end_time   = -1;
            stats_counter->connection_duration   = -1;
            stats_counter->log_received_count    = 0;
            stats_counter->log_transfered_count  = 0;
            if(stats_counter->connection_destination != NULL){
                ZPN_FREE(stats_counter->connection_destination);
                stats_counter->connection_destination = NULL;
            }
            stats_counter->connection_destination = ZPN_STRDUP(zpn_siem_conn_destination,
                                                               strlen(zpn_siem_conn_destination));

            if(stats_counter->connection_source != NULL){
                ZPN_FREE(stats_counter->connection_source);
                stats_counter->connection_source = NULL;
            }
            stats_counter->connection_source = ZPN_STRDUP(zpn_siem_conn_source,
                                                          strlen(zpn_siem_conn_source));
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_active_connection_count, 1);
        }
        ZPN_DEBUG_SIEM_ALL("Key:%s Already present in zpn_siem_debug_stats_table", key);
    }
    ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
    return stats_counter;
}

void debug_stat_destroy(struct rx_state *rs, uint64_t logs_dropped_count)
{
    char key[64] = {'\0'};
    snprintf(key, sizeof(key), "%"PRIu64, rs->siem_gid);
    ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
    struct zpn_siem_debug_stats *stats_counter = zhash_table_lookup(zpn_siem_debug_stats_table,
                                                                    key,
                                                                    strlen(key),
                                                                    NULL);
    if (stats_counter != NULL) {
        /* Log into event_log for accounting */
        char buffer[4096] = {'\0'};
        char *s = buffer;
        char *e = s + sizeof(buffer);
        s += sxprintf(s, e, "Current RS stats for cust-id %"PRIu64" and siem-id %"PRIu64":",
                      stats_counter->customer_id,
                      stats_counter->siem_gid);
        for (int i = 0; i < zpn_siem_debug_log_error_type_max; i++)
            s += sxprintf(s, e, " %"PRIu64, stats_counter->zpn_siem_error_type_count[i]);

        s += sxprintf(s, e, " connection_start_time:%"PRIu64", connection_end_time:%"PRIu64" and connection_duration:%"PRIu64"\n",
                      stats_counter->connection_start_time,
                      stats_counter->connection_end_time,
                      stats_counter->connection_duration);
        s += sxprintf(s, e, " freeing connection_source:%s, connection_destination:%s\n",
                      stats_counter->connection_source,
                      stats_counter->connection_destination);
        ZPN_LOG(AL_NOTICE, "%s", buffer);

        if(stats_counter->connection_destination != NULL){
                ZPN_FREE(stats_counter->connection_destination);
                stats_counter->connection_destination = NULL;
        }
        if(stats_counter->connection_source != NULL){
                ZPN_FREE(stats_counter->connection_source);
                stats_counter->connection_source = NULL;
        }
        if(stats_counter->is_active == 1){
            stats_counter->is_active = 0;
            stats_counter->log_dropped_count += logs_dropped_count;
            __sync_fetch_and_sub_8(&g_siem_conn_stats.total_active_connection_count, 1);
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_log_dropped_count, 1);
        }
        stats_counter->connection_end_time = epoch_us();
        stats_counter->connection_duration = stats_counter->connection_end_time - stats_counter->connection_start_time;
    }
    ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
}

static int zpn_siem_mt_status_callback(struct zpn_fohh_client_slogger_mt *mt,
                                       void *cookie_void,
                                       int64_t cookie_int,
                                       enum zpn_fohh_client_slogger_mt_status status)
{
    struct rx_state *rs = cookie_void;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No rs");
        return ZPN_RESULT_ERR;
    }

    /* The repeated debug message is because I don't want to
     * update_name for the case where the mtunnel is being
     * destroyed. (you lose information in debug messages) */
    if (status == zfcs_mt_connected) {
        /* We can send data to broker now */
        ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);
        ZPATH_MUTEX_LOCK(&(rs->zfcs->lock), __FILE__, __LINE__);
        if(rs->zfcs->broker_redirect == 1) {
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_broker_redirection_count, 1);
            ZPN_LOG(AL_INFO, "%s: Data broker has been redirected and mtunnel has been established for the customer %ld", rs->name, (long)rs->zfcs->customer_id);
            rs->zfcs->broker_redirect = 0;
        }
        ZPATH_MUTEX_UNLOCK(&(rs->zfcs->lock), __FILE__, __LINE__);
        update_name(rs);
        ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger mtunnel status now %s. cookie_void = %p",
                           rs->name,
                           zfcs_mt_status_string(status), cookie_void);

        zpn_siem_out(rs);
        /* coverity[missing_lock: FALSE] */
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
    } else if (status == zfcs_mt_connecting) {
        ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);
        update_name(rs);
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
        ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger mtunnel status now %s. cookie_void = %p",
                           rs->name,
                           zfcs_mt_status_string(status), cookie_void);
        /* Do nothing */
    } else {
        /* We need to destroy connection */
        ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger mtunnel status now %s. cookie_void = %p",
                           rs->name,
                           zfcs_mt_status_string(status), cookie_void);
        rs_destroy(rs, destroy_mt);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_mt_consume_callback(struct zpn_fohh_client_slogger_mt *mt,
                                        struct evbuffer *buf,
                                        size_t len,
                                        void *cookie_void,
                                        int64_t cookie_int)
{
    struct rx_state *rs = cookie_void;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No rs");
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger mtunnel send us %d bytes of data", rs->name, (int)len);

#if MY_TEST
    {
        #define RESP_DATA_LEN    65535
        char resp_data[RESP_DATA_LEN];

        memset(resp_data, 0, RESP_DATA_LEN);
        if (buf && (len < RESP_DATA_LEN)) {
            evbuffer_remove(buf, resp_data, len);
        }

        ZPN_LOG(AL_DEBUG, "================= Received Response =================");
        ZPN_LOG(AL_DEBUG, "%s", resp_data);
    }
#endif

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_mt_unblock_callback(struct zpn_fohh_client_slogger_mt *mt, void *cookie_void, int64_t cookie_int)
{
    struct rx_state *rs = cookie_void;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No rs");
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger mtunnel tell us to resume sending", rs->name);

    /* We can send data to broker now */
    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);
    zpn_siem_out(rs);
    ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_zfcs_status_callback(struct zpn_fohh_client_slogger *zfcs,
                                         void *cookie_void,
                                         int64_t cookie_int,
                                         enum zfc_status status,
                                         const char *error_string,
                                         int *donot_call_again)
{
    struct rx_state *rs = (struct rx_state *)cookie_void;

    int need_destroy = 0;
    enum rx_state_destroy_type dt = destroy_generic;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No rs");
        return ZPN_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);

    if (
        (status == zfc_init) ||
        (status == zfc_ready) ||
        (status == zfc_authenticate)
        ) {
        update_name(rs);
    }

    ZPN_DEBUG_SIEM_ALL("%s: ZPN Fohh client slogger status now %s. %s, cookie = %p",
                       rs->name,
                       zfc_status_string(status), error_string ? error_string : "No Error", cookie_void);

    if (status == zfc_ready) {

        if (rs && rs->zfcs && !rs->mt) {
            rs->mt = zpn_fohh_client_slogger_mt_create(rs->zfcs,
                                                       rs->siem_host,
                                                       rs->siem_port,
                                                       IPPROTO_TCP,
                                                       rs->use_tls,
                                                       zpn_siem_mt_status_callback,
                                                       zpn_siem_mt_consume_callback,
                                                       zpn_siem_mt_unblock_callback,
                                                       rs,
                                                       0,
                                                       rs->siem_gid+2);
            if (rs->mt) {
                rs->mt_incarnation = rs->mt->incarnation;
                __sync_add_and_fetch_8(&(rs->ref_count), 1);
                __sync_add_and_fetch_8(&(rs->mt_stats), 1);
            } else {
                zpn_siem_update_debug_stats(rs->siem_stat, mtunnel_not_allocated);
                ZPN_LOG(AL_ERROR, "%s: Siem cannot allocate mtunnel", rs->name);
                /* We have created mt, so unregister with the slogger client */
                /* Use generic destroy because we don't want to drop
                * any ref count, since we didn't increase one for
                * this case... */
                need_destroy = 1;
                dt = destroy_generic;
            }
        } else {
            /* WTF??? */
        }
    } else if (status == zfc_not_connected) {
        /* Connection is gone, destroy zfcs ???? */
        /* SIEM cna do whatever with rs now, no more callback for rs */
        need_destroy = 1;
        dt = destroy_zfcs;
    } else {
        /* SIEM cna do whatever with rs now, no more callback for rs */
        need_destroy = 1;
        dt = destroy_zfcs;
    }

    ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    if (need_destroy) {
        *donot_call_again = 1;
        rs_destroy(rs, dt);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Must be called with lock held
 */
static void element_dequeue_and_free(struct rx_state *rs, struct zpn_siem_element *element)
{
    TAILQ_REMOVE(&(rs->objects), element, list);
    /* element is actually an arrived argo_object, so must be
     * released via argo... */
    rs->queue_count--;
    rs->queue_size -= zpn_siem_element_size(element);
    argo_structure_release(element);
}

/*
 * Must NOT be called with lock held.
 *
 * Love the consistency.
 *
 * Note: dest must be set of an external module is telling us its
 * state is destroyed. 'dest' tells us which of that state is being
 * destroyed.
 */
static void rs_destroy(struct rx_state *rs, enum rx_state_destroy_type dest)
{
    struct zpn_siem_element *element;
    struct ack_queue *ack;
    int64_t value;
    int64_t stats;

    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);

    rs->deleting = 1;

    if (rs->ref_count <= 0) {
        ZPN_LOG(AL_CRITICAL, "%s: Ref count already <= 0 (%ld)...", rs->name, (long) rs->ref_count);
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
        return;
    }

    switch(dest) {
    case destroy_generic:
        ZPN_LOG(AL_DEBUG, "%s: Ref count %ld (generic)", rs->name, (long)rs->ref_count);
        break;
    case destroy_timer:
        value = __sync_sub_and_fetch_8(&(rs->ref_count), 1);
        stats = __sync_sub_and_fetch_8(&(rs->timer_stats), 1);
        ZPN_LOG(AL_DEBUG, "%s: Ref count %ld->%ld (timer), stats:%ld", rs->name, (long)value + 1l, (long)value, (long)stats);
        break;
    case destroy_fohh:
        /* We do FOHH deletion here since FOHH callbacks are always
         * 'thread owned', and never expect a callback after calling
         * back themselves*/
        if (rs->f_conn) {
            fohh_connection_set_dynamic_cookie(rs->f_conn, NULL);
            rs->f_conn = NULL;
        }
        rs->not_needed_fohh_close = 1;
        value = __sync_sub_and_fetch_8(&(rs->ref_count), 1);
        stats = __sync_sub_and_fetch_8(&(rs->fohh_stats), 1);
        ZPN_LOG(AL_DEBUG, "%s: Ref count %ld->%ld (fohh), stats:%ld", rs->name, (long)value + 1l, (long)value, (long)stats);
        break;
    case destroy_zfcs:
        value = __sync_sub_and_fetch_8(&(rs->ref_count), 1);
        stats = __sync_sub_and_fetch_8(&(rs->zfcs_stats), 1);
        ZPN_LOG(AL_DEBUG, "%s: Ref count %ld->%ld (zfcs), stats:%ld", rs->name, (long)value + 1l, (long)value, (long)stats);
        break;
    case destroy_mt:
        value = __sync_sub_and_fetch_8(&(rs->ref_count), 1);
        stats = __sync_sub_and_fetch_8(&(rs->mt_stats), 1);
        ZPN_LOG(AL_DEBUG, "%s: Ref count %ld->%ld (mt), stats:%ld", rs->name, (long)value + 1l, (long)value, (long)stats);
        break;
    }

    /* Check our external modules to see if we need to tell any of them to close */
    if (!rs->not_needed_zfcs_close) {
        if (rs->zfcs) {
            ZPN_DEBUG_SIEM("%s: calling zpn_fohh_client_slogger_unregister", rs->name);
            zpn_fohh_client_slogger_unregister(rs->zfcs, rs, 0);
            rs->zfcs = NULL;
        }
        rs->not_needed_zfcs_close = 1;
    }
    if (!rs->not_needed_mt_close) {
        if (rs->mt) {
            ZPN_DEBUG_SIEM("%s: calling zpn_fohh_client_slogger_mt_destroy", rs->name);
            zpn_fohh_client_slogger_mt_destroy(rs->mt, NULL);
            rs->mt = NULL;
        }
        rs->not_needed_mt_close = 1;
    }

    if (!rs->not_needed_fohh_close) {
        if (rs->f_conn) {
            ZPN_DEBUG_SIEM("%s: calling fohh_connection_delete", rs->name);
            fohh_connection_delete_async(rs->f_conn, rs->f_conn_incarnation, SIEM_CONN_CLOSE);
        }
        rs->not_needed_fohh_close = 1;
    }

    /* Remove queued elements */
    int removed_elements = rs->queue_count;
    uint64_t removed_logs = (uint64_t)rs->queue_count;
    debug_stat_destroy(rs, removed_logs);
    while((element = TAILQ_FIRST(&(rs->objects)))) {
        ZPN_DEBUG_SIEM_ALL("%s: destroy: removing element %ld, count now %ld, size now %ld",
                           rs->name,
                           (long) element->id,
                           (long)(rs->queue_count - 1),
                           (long)(rs->queue_size - zpn_siem_element_size(element)));
        element_dequeue_and_free(rs, element);
    }
    if (rs->queue_count != 0) {
        ZPN_LOG(AL_WARNING, "%s: Queue count should be 0, is %ld", rs->name, (long) rs->queue_count);
    }
    if (rs->queue_size != 0) {
        ZPN_LOG(AL_WARNING, "%s: Queue size should be 0, is %ld", rs->name, (long) rs->queue_size);
    }

    /* Remove queued acks */
    int removed_acks = rs->ack_count;
    while ((ack = TAILQ_FIRST(&(rs->acks)))) {
        TAILQ_REMOVE(&(rs->acks), ack, list);
        rs->ack_count--;
        SIEM_FREE(ack);
    }
    if (rs->ack_count != 0) {
        ZPN_LOG(AL_WARNING, "%s: Ack queue size should be 0, is %ld", rs->name, (long) rs->ack_count);
    }

    if (removed_elements || removed_acks) {
        ZPN_LOG(AL_INFO, "%s: cleared connection with element_queue depth:%i and ack_queue depth:%i", rs->name, removed_elements, removed_acks);
    }

    ZPN_LOG(AL_DEBUG, "%s: destroy near end, ref count now %ld, dest:%d, timer_stats:%ld, fohh_stats:%ld, zfcs_stats:%ld, mt_stats:%ld",
            rs->name, (long) rs->ref_count, dest, (long) rs->timer_stats, (long) rs->fohh_stats, (long) rs->zfcs_stats, (long) rs->mt_stats);
    if (rs->ref_count) {
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
        return;
    }

    if (rs->ack_timer) {
        event_free(rs->ack_timer);
        rs->ack_timer = NULL;
    }

    ZPN_DEBUG_SIEM("%s: Freeing", rs->name);

    if (rs->name) SIEM_FREE(rs->name);
    if (rs->siem_host) SIEM_FREE(rs->siem_host);

    ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    SIEM_FREE(rs);

    return;
}


/*
 * Poke the ack queue to make sure it is running. Must be called with
 * rs lock held.
 *
 * Any error returned should be considered very bad- should destroy
 * rs.
 *
 * Must be called with rs lock held
 */
static int ack_poke(struct rx_state *rs)
{
    struct ack_queue *ack;
    int64_t next_timer_us = 0;
    int res;

    if (rs->ack_timer_running) return ZPN_RESULT_NO_ERROR;

    if (!rs->deleting)  {
        /* Normal operation: send acks for all entries that are
         * 'expired', and set a timer (if necessary) for
         * retransmit. Note- if we block on transmit, we just set a
         * 10ms timer and keep coming back */
        int64_t now = epoch_us();
        while ((ack = TAILQ_FIRST(&(rs->acks)))) {
            if ((ack->tx_timestamp + rs->ack_delay_us) <= now) {
                struct zpn_siem_element_ack tx_ack;
                tx_ack.id = ack->id;
                res = fohh_argo_serialize(rs->f_conn, zpn_siem_element_ack_description, &tx_ack, 0,
                                          fohh_queue_element_type_control);
                if (res) {
                    if (res == FOHH_RESULT_WOULD_BLOCK) {
                        /* Set timer for 10ms */
                        ZPN_DEBUG_SIEM_ALL("%s: Sending ack for %ld, would bock", rs->name, (long) ack->id);
                        next_timer_us = 10000;
                        break;
                    } else {
                        zpn_siem_update_debug_stats(rs->siem_stat, ack_failure);
                        ZPN_LOG(AL_ERROR, "%s: Sending ack id %ld returned error %s", rs->name, (long) tx_ack.id, fohh_result_string(res));
                        rs->deleting = 1;
                        return ZPN_RESULT_ERR;
                    }
                } else {
                    ZPN_DEBUG_SIEM_ALL("%s: Sent ack for %ld", rs->name, (long) ack->id);
                    TAILQ_REMOVE(&(rs->acks), ack, list);
                    SIEM_FREE(ack);
                    rs->ack_count--;
                    rs->objects_acknowledged++;
                }
            } else {
                next_timer_us = (ack->tx_timestamp + rs->ack_delay_us) - now;
                break;
            }
        }
        if (next_timer_us) {
            struct timeval tv;
            tv.tv_sec = next_timer_us / 1000000l;
            tv.tv_usec = next_timer_us % 1000000l;
            if (event_add(rs->ack_timer, &tv)) {
                zpn_siem_update_debug_stats(rs->siem_stat, no_timer_event);
                ZPN_LOG(AL_ERROR, "%s: Could not add timer event", rs->name);
                return ZPN_RESULT_ERR;
            }
            rs->ack_timer_running = 1;
            __sync_add_and_fetch_8(&(rs->ref_count), 1);
            __sync_add_and_fetch_8(&(rs->timer_stats), 1);
        }
    } else {
        ZPN_LOG(AL_DEBUG, "ACK_POKE while deleting...");
        return ZPN_RESULT_ERR;
    }
    return ZPN_RESULT_NO_ERROR;
}


static void ack_timer(evutil_socket_t sock, short flags, void *cookie)
{
    struct rx_state *rs = cookie;
    int res;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No RS");
        return;
    }

    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);

    rs->ack_timer_running = 0;

    if (!rs->deleting) {

        __sync_sub_and_fetch_8(&(rs->ref_count), 1);
        __sync_sub_and_fetch_8(&(rs->timer_stats), 1);

        res = ack_poke(rs);
        if (res) {
            ZPN_LOG(AL_NOTICE, "%s: ack_poke returned error: %s", rs->name, zpn_result_string(res));
        }

        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    } else {
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
        /* rs_destroy removes our ref count */
        rs_destroy(rs, destroy_timer);
    }
}

/* Expect lock held */
static int zpn_siem_ack_enqueue(struct rx_state *rs, int64_t id)
{
    struct ack_queue *ack = SIEM_CALLOC(sizeof(*ack));
    int res;

    if (!ack) {
        ZPN_LOG(AL_CRITICAL, "Memory");
        return ZPN_RESULT_NO_MEMORY;
    }
    ack->id = id;
    ack->tx_timestamp = epoch_us();

    TAILQ_INSERT_TAIL(&(rs->acks), ack, list);
    rs->ack_count++;
    ZPN_DEBUG_SIEM_ALL("%s: Queued ack %ld", rs->name, (long) id);
    res = ack_poke(rs);

    if (res) {
        ZPN_LOG(AL_NOTICE, "%s: ack_poke returned error: %s", rs->name, zpn_result_string(res));
    } else {
    }
    return res;
}

#if 0
static int lookup_f(int64_t gid,
                    char *buf,
                    size_t buf_len)
{
    snprintf(buf, buf_len, "Temporary_lookup(%ld)", (long) gid);
    return ZPN_RESULT_NO_ERROR;
}
#endif

/*
 * rs lock must be held when calling
 */
#if MY_TEST
static int my_test_write(struct rx_state *rs)
{
    /* Try to serialize, and transmit. */
    /* If transmit does not block, dequeue. (update stats) */
    /* If transmit blocked, TURN OFF FOHH RECEIVE (will still empty FOHH queue into our buffer) */
    /* If running without acknowledgements, do ack queueing */
    /* Note: If this routine consumes off the queue, it must release the objects. (duh) */

    if (rs->mt && (rs->mt->status == zfcs_mt_connected)) {
        const char *req = "GET / HTTP/1.1\r\nUser-Agent: curl/7.35.0\r\nHost: perf1.chanak.com\r\nAccept: */*\r\n\r\n";
        struct evbuffer *buf = evbuffer_new();

        if (buf) {
            int res;

            evbuffer_add(buf, req, strlen(req));
            res = zfcs_mt_consume(rs->mt, buf, evbuffer_get_length(buf), rs->mt_incarnation);
            if (res) {
                ZPN_LOG(AL_NOTICE, "Cannot send data???");
            }
        }

        ZPN_LOG(AL_DEBUG, "Sent siem data to Mtunnel.");
        /* Sending real object */
    }

    return ZPN_RESULT_NO_ERROR;
}
#endif

/*
 * Must be called with lock held
 */
static int zpn_siem_out(struct rx_state *rs)
{
    int res;
    struct zpn_siem *siem;
    struct zpn_siem_element *element;
    struct evbuffer *buf = evbuffer_new();

#if MY_TEST
    return my_test_write(rs);
#endif


    if (!buf) {
        ZPN_LOG(AL_CRITICAL, "Memory");
        return ZPN_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(buf);
    if (rs->deleting) {
        evbuffer_free(buf);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(rs->zfcs->lock), __FILE__, __LINE__);
    if(rs->zfcs->broker_redirect) {
        ZPATH_MUTEX_UNLOCK(&(rs->zfcs->lock), __FILE__, __LINE__);
        return ZPN_RESULT_WOULD_BLOCK;
    }
    ZPATH_MUTEX_UNLOCK(&(rs->zfcs->lock), __FILE__, __LINE__);

    res = zpn_siem_get(rs->siem_gid, &siem);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: SIEM %ld: lookup returned %s, have %ld objects queued", rs->name, (long) rs->siem_gid, zpn_result_string(res), (long) rs->queue_count);
        evbuffer_free(buf);
        return ZPN_RESULT_NO_ERROR;
    }
    // If siem is DISABLED
	if(!siem->enabled) {
		int elem_count = 0;
		ZPN_LOG(AL_DEBUG, "%s: SIEM [%ld] is disabled", rs->name, (long)siem->gid);
		while (!TAILQ_EMPTY(&rs->objects)) {
			element = TAILQ_FIRST(&rs->objects);
			element_dequeue_and_free(rs, element);
			elem_count ++;
		}
		ZPN_LOG(AL_DEBUG, "%s: Draining the queues...[%d] elements removed", rs->name, elem_count);
		evbuffer_free(buf);
		return ZPATH_RESULT_NO_ERROR;
	}

#define BUFFER_SIZE 256*1024

    /*
     * Loop: Pull one object, serialize it, and transmit. If
     * transmission success, then perhaps auto-ack (depending on
     * whether our output format does acknowledgement)
     */

    res = ZPN_RESULT_NO_ERROR;
    while ((element = TAILQ_FIRST(&(rs->objects)))) {
        /* Short circuit if we don't have output mtunnel */

        if (!rs->mt) {
            res = ZPN_RESULT_WOULD_BLOCK;
            if (!rs->rx_disabled) {
                rs->rx_disabled = 1;
                fohh_connection_disable_read(rs->f_conn);
            }
            ZPN_LOG(AL_NOTICE, "%s: SIEM %ld: Serialize element %ld blocking waiting for mtunnel", rs->name, (long) rs->siem_gid, (long) element->id);
            break;
        }

        struct argo_buf *buffer = argo_buf_create(0, NULL, NULL, BUFFER_SIZE); //256KB initial buffer
        if (!buffer) {
            ZPN_LOG(AL_WARNING, "%s: Siem Serialize failed, memory alloc failed: buffer-initialization", rs->name);
            return ZPN_RESULT_NO_MEMORY;
        }


        /* Step 1: Serialize to correct format */

        /* Remember: Buffer string is NULL terminated only in error (failure) case for local printing,
         * and no NULL byte in the end for success case (expected) as its destined for SIEM server */

        res = zpn_siem_serialize(siem, lookup_lib_lookup, element->object, &buffer);
        if (res) {
            zpn_siem_update_debug_stats(rs->siem_stat, siem_serialize_failed);
            size_t fail_buffer_length = argo_buf_size(buffer); //because NULL terminated in error case
            char *fail_buffer = SIEM_CALLOC(sizeof(char) * fail_buffer_length);
            if (fail_buffer) {
                argo_buf_copy_out(buffer, fail_buffer, fail_buffer_length, NULL);
                ZPN_LOG(AL_WARNING, "%s: Siem Serialize failed, output buffer became: %s", rs->name, fail_buffer);
                SIEM_FREE(fail_buffer);
            } else {
                ZPN_LOG(AL_WARNING, "%s: Siem Serialize failed, output buffer printing skipped due to mem", rs->name);
            }
            /* Failed to serialize the current log element. Drop it and continue with next element in the queue */
            argo_buf_free_all(buffer);
            element_dequeue_and_free(rs, element);
            continue;
        }

        size_t final_buffer_length = argo_buf_size(buffer);


        /* TO DO: Step 1.5: Generate linear buffer from argo buffer */
        char *out_buffer = SIEM_CALLOC(sizeof(char) * final_buffer_length);
        if (!out_buffer) {
            argo_buf_free_all(buffer);
            ZPN_LOG(AL_WARNING, "%s: Siem Serialize failed, memory alloc failed: linear-serialization", rs->name);
            return ZPN_RESULT_NO_MEMORY;
        }

        argo_buf_copy_out(buffer, out_buffer, final_buffer_length, NULL);
        argo_buf_free_all(buffer);
        buffer = NULL;

        evbuffer_add(buf, out_buffer, final_buffer_length);

        ZPN_DEBUG_SIEM_ALL("%s: Trying to really transmit: %.*s...",
                            rs->name, (int)(final_buffer_length > 30 ? 30 : final_buffer_length), out_buffer);
        SIEM_FREE(out_buffer);
        out_buffer = NULL;


        /* Step 2: Transmit. */
        res = zfcs_mt_consume(rs->mt, buf, evbuffer_get_length(buf), rs->mt_incarnation);

        if (res) {
            if (res == ZPN_RESULT_WOULD_BLOCK) {
                /* Block! */
                /* Turn off RX */
                if (!rs->rx_disabled) {
                    rs->rx_disabled = 1;
                    fohh_connection_disable_read(rs->f_conn);
                }
                zpn_siem_update_debug_stats(rs->siem_stat, zfcs_mt_consume_blocked);
                ZPN_LOG(AL_NOTICE, "%s: SIEM %ld: Serialize blocking on id = %ld while sending to mtunnel", rs->name, (long) rs->siem_gid, (long)element->id);
                break;
            } else {
                /* Some other error- rip it all down */
                zpn_siem_update_debug_stats(rs->siem_stat, zfcs_mt_consume_failed);
                ZPN_LOG(AL_NOTICE, "%s: zfcs_mt_consume failed: %s", rs->name, zpn_result_string(res));
                evbuffer_free(buf);
                return res;
            }
        } else {
            rs->objects_transmitted++;
        }

        /* Step 3: Auto-ack if appropriate. */
        if (element->ack_now) {
            struct zpn_siem_element_ack tx_ack;
            tx_ack.id = element->id;
            ZPN_DEBUG_SIEM_ALL("%s: Sending ack for %ld...", rs->name, (long) tx_ack.id);
            res = fohh_argo_serialize(rs->f_conn, zpn_siem_element_ack_description, &tx_ack, 0,
                                      fohh_queue_element_type_control);
            if (res) {
                if (res == FOHH_RESULT_WOULD_BLOCK) {
                    /* enqueing */
                    ZPN_DEBUG_SIEM_ALL("%s: Sending ack for %ld, would bock (wait for ~%lds)", rs->name,
                                       (long) tx_ack.id, (long) (rs->ack_delay_us / 1000000l));
                    zpn_siem_ack_enqueue(rs, element->id);
                    break;
                } else {
                    zpn_siem_update_debug_stats(rs->siem_stat, ack_failure);
                    ZPN_LOG(AL_ERROR, "%s: Sending ack id %ld returned error %s", rs->name, (long) tx_ack.id,
                            fohh_result_string(res));
                    rs->deleting = 1;
                    evbuffer_free(buf);
                    return ZPN_RESULT_ERR;
                }
            } else {
                ZPN_DEBUG_SIEM_ALL("%s: Sent ack for %ld", rs->name, (long) element->id);
                rs->objects_acknowledged++;
            }
        } else if (rs->self_ack) {
                zpn_siem_ack_enqueue(rs, element->id);
        }

        /* Step 4: Successful transmit, so remove and free element */
        element_dequeue_and_free(rs, element);
        char key[64] = {'\0'};
        snprintf(key, sizeof(key), "%"PRIu64, rs->siem_gid);
        ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
        struct zpn_siem_debug_stats *stats_counter = zhash_table_lookup(zpn_siem_debug_stats_table,
                                                                    key,
                                                                    strlen(key),
                                                                    NULL);
        if(rs->siem_stat != NULL && stats_counter != NULL && stats_counter->is_active){
            stats_counter->log_transfered_count += 1;
            __sync_fetch_and_add_8(&g_siem_conn_stats.total_log_transfered_count, 1);
        }
        ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
    }

    if (res == ZPN_RESULT_NO_ERROR && rs->rx_disabled) {
        ZPN_LOG(AL_NOTICE, "%s: SIEM %ld: Unblocking connection", rs->name, (long) rs->siem_gid);
        rs->rx_disabled = 0;
        fohh_connection_enable_read(rs->f_conn);
    }


    evbuffer_free(buf);

    return res;
}

/*
 * Makes debugging easier...  Must be called with lock held
 */
static void update_name(struct rx_state *rs)
{
    char new_name[768];
    char out_conn_desc[256];
    char mt_desc[256];
    struct zpn_siem *siem;
    int res;

    zpn_fohh_client_slogger_get_conn(rs->zfcs, out_conn_desc, sizeof(out_conn_desc));

    zpn_fohh_client_slogger_get_mconn(rs->mt, mt_desc, sizeof(mt_desc));

    /* First get the siem state, and update the name */
    if (rs->siem_gid) {
        res = zpn_siem_get(rs->siem_gid, &siem);
    } else {
        res = ZPN_RESULT_NOT_FOUND;
    }

    if (res) {
        /* No SIEM? */
        snprintf(new_name, sizeof(new_name),
                 "Siem:%ld:%ld:NotFound:"
                 "In:%s->"
                 "Out:%s->%s",
                 (long) rs->customer_id,
                 (long)(rs->siem_gid),
                 rs->f_conn ? fohh_description(rs->f_conn) : "No_F_conn",
                 out_conn_desc, mt_desc);
    } else {
        snprintf(new_name, sizeof(new_name),
                 "Siem:%s:%ld:%ld:%ld:%s:%ld="
                 "In:%s->"
                 "Out:%s->%s",
                 siem->name, (long) siem->customer_gid, (long) siem->gid, (long)siem->sequence, siem->siem_host, (long)siem->siem_port,
                 rs->f_conn ? fohh_description(rs->f_conn) : "No_F_conn",
                 out_conn_desc, mt_desc);
    }

    if (rs->name) SIEM_FREE(rs->name);
    rs->name = SIEM_STRDUP(new_name, strlen(new_name));
}

static int zpn_siem_get_ack_delay_timeout(int64_t cust_gid)
{
    /* in case some test code didn't initialize zpath_instance -
     * default to ZPATH_GLOBAL_CONFIG_OVERRIDE_GID */
    int64_t inst_gid = ZPATH_INSTANCE_GID ? ZPATH_INSTANCE_GID : ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(SLOGGER_ACK_DELAY_TIMEOUT_US_CONFIG_OVERRIDE,
                                                 &value,
                                                 SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT,
                                                 (int64_t)inst_gid,
                                                 (int64_t)cust_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);

    if (value != SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT) {
        ZPN_LOG(AL_DEBUG, "%" PRId64 ": applying custom ack delay of %" PRId64 " us", cust_gid, value);
    }

    if (value <= 0) {
        ZPN_LOG(AL_WARNING, "Config value %" PRId64 " for %" PRId64 " is not valid. Keeping deafult value of %d",
            value, cust_gid, SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT);
        value = SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT;
    }

    return value;
}

static void zpn_siem_output_init_config_override(struct rx_state *rs)
{
    int64_t special_ack_delay_timeout_us;

    special_ack_delay_timeout_us = zpn_siem_get_ack_delay_timeout(rs->customer_id);

    rs->ack_delay_us = special_ack_delay_timeout_us;
}

/*
 * Initialize the output state for this siem...
 *
 * Called with rs lock held
 */
static int zpn_siem_output_init(struct rx_state *rs)
{
    struct zpn_siem *siem;
    int res;

    /* First get the siem state, and update the name */
    res = zpn_siem_get(rs->siem_gid, &siem);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Could not find siem id %ld", rs->name, (long) rs->siem_gid);
        return res;
    }
    rs->siem_sequence =  siem->sequence;
    rs->use_tls = siem->use_tls;

    update_name(rs);

    ZPN_LOG(AL_DEBUG, "%s: Format string = %s", rs->name, siem->format);

    rs->self_ack = 1;
    rs->customer_id = ZPATH_GID_GET_CUSTOMER_GID(rs->siem_gid);

    zpn_siem_output_init_config_override(rs);

    if (!siem->siem_host) {
        return ZPN_RESULT_INSUFFICIENT_DATA;
    }

    rs->siem_host = ZPN_STRDUP(siem->siem_host, strlen(siem->siem_host));
    if (!rs->siem_host) {
        return ZPN_RESULT_NO_MEMORY;
    }

    rs->siem_port = siem->siem_port;

    ZPN_DEBUG_SIEM_ALL("%s: Create client tunnel for siem_host = %s, siem_port = %d, customer_id = %ld", rs->name,
                       siem->siem_host,
                       siem->siem_port,
                       (long)rs->customer_id);

#if MY_TEST
    rs->siem_host = ZPN_STRDUP("perf1.chanak.com", strlen("perf1.chanak.com"));
    rs->siem_port = 80;
    rs->customer_id = 217246525011525632;
#endif

    rs->zfcs = zpn_fohh_client_slogger_create(broker_name,
                                              ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                                              ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                                              ZPATH_LOCAL_PRIVATE_KEY_FILE,
                                              zpn_siem_zfcs_status_callback,
                                              rs,
                                              0,
                                              ZPATH_LOCAL_CLOUD_NAME,
                                              rs->customer_id);
    if (!rs->zfcs) {
        zpn_siem_update_debug_stats(rs->siem_stat, no_zpn_fohh_client_slogger);
        ZPN_LOG(AL_ERROR, "Could not create zpn_fohh_client_slogger");
        return ZPN_RESULT_NO_MEMORY;
    }

    __sync_add_and_fetch_8(&(rs->ref_count), 1);
    __sync_add_and_fetch_8(&(rs->zfcs_stats), 1);

    update_name(rs);
    ZPN_LOG(AL_DEBUG, "%s: Created zpn_fohh_client_slogger, ack_delay_timeout_us:%ld", rs->name, (long)rs->ack_delay_us);

    return ZPN_RESULT_NO_ERROR;
}

/* UNBLOCK CALLBACK FROM mtunnel! */
/* This routine has to simply ensure that fohh receive is turned on */
static int zpn_siem_element_cb(void *argo_cookie_ptr,
                               void *argo_structure_cookie_ptr,
                               struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct rx_state *rs = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_siem_element *element;
    int res;

    if (!rs) {
        ZPN_LOG(AL_ERROR, "No RS");
        return ZPN_RESULT_ERR;
    }

    rs->objects_received++;

#if 0
    if (zpn_debug_get(ZPN_DEBUG_SIEM_ALL_IDX)) {
        char dump[20000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Received element: %s", rs->name, dump);
        } else {
            ZPN_LOG(AL_DEBUG, "%s: Received element too big", rs->name);
        }
    }
#endif // 0


    /* Since we will want a copy of the object, make one. */
    object = argo_object_copy(object);
    if (!object) {
        zpn_siem_update_debug_stats(rs->siem_stat, failed_object_copy);
        ZPN_LOG(AL_ERROR, "%s: Could not copy object", rs->name);
        rs_destroy(rs, destroy_generic);
        return ZPN_RESULT_NO_ERROR;
    }


    /* Enqueue. */
    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);

    element = object->base_structure_void;

    ZPN_DEBUG_SIEM_ALL("%s: Received element %ld", rs->name, (long)element->id);

    if (rs->siem_gid != element->siem_gid) {
        /* Initialize output state */
        if (!rs->siem_gid) {
            rs->siem_gid = element->siem_gid;
            res = zpn_siem_output_init(rs);
            if (rs != NULL && rs->siem_gid && rs->customer_id) {
                rs->siem_stat = debug_stat_init_one(rs, rs->siem_gid, rs->customer_id);
            }
            if (res) {
                zpn_siem_update_debug_stats(rs->siem_stat, output_init_failed);
                ZPN_LOG(AL_ERROR, "%s: SIEM output init failed for siem gid %ld", rs->name, (long) element->siem_gid);
                argo_object_release(object);
                ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
                rs_destroy(rs, destroy_generic);
                return ZPN_RESULT_NO_ERROR;
            }
        } else {
            /* SIEM gid changed */
            zpn_siem_update_debug_stats(rs->siem_stat, rs_siem_gid_changed);
            ZPN_LOG(AL_ERROR, "%s: SIEM gid changed from %ld to %ld", rs->name, (long) rs->siem_gid, (long) element->siem_gid);
            argo_object_release(object);
            ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
            rs_destroy(rs, destroy_generic);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (rs->deleting) {
        argo_object_release(object);
        zpn_siem_update_debug_stats(rs->siem_stat, connection_going_away);
        ZPN_LOG(AL_ERROR, "%s: Received object on connection going away.", rs->name);
        ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);
        rs_destroy(rs, destroy_generic);
        return ZPN_RESULT_NO_ERROR;
    }

    TAILQ_INSERT_TAIL(&(rs->objects), element, list);

    char key[64] = {'\0'};
    snprintf(key, sizeof(key), "%"PRIu64, rs->siem_gid);
    ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
    struct zpn_siem_debug_stats *stats_counter = zhash_table_lookup(zpn_siem_debug_stats_table,
                                                                    key,
                                                                    strlen(key),
                                                                    NULL);
    if(rs->siem_stat != NULL && stats_counter != NULL && stats_counter->is_active){
        stats_counter->log_received_count += 1;
        __sync_fetch_and_add_8(&g_siem_conn_stats.total_log_received_count, 1);
    }
    ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);

    rs->queue_count++;
    rs->queue_size += zpn_siem_element_size(element);

    ZPN_DEBUG_SIEM_ALL("%s: Queued object id %ld, total objects queued = %ld, total queue size = %ld", rs->name, (long) element->id, (long) rs->queue_count, (long) rs->queue_size);

     res = zpn_siem_out(rs);

    ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    /* Don't let FOHH fail for outpub blocking */
    if (res) {
        if (res == ZPN_RESULT_WOULD_BLOCK) {
            res = ZPN_RESULT_NO_ERROR;
        } else {
            zpn_siem_update_debug_stats(rs->siem_stat, siem_out_failed);
            ZPN_LOG(AL_ERROR, "%s: siem_out returned %s", rs->name, zpn_result_string(res));
            rs_destroy(rs, destroy_generic);
            res = ZPN_RESULT_NO_ERROR;
        }
    }

    return res;
}

static int zpn_siem_fohh_rx_conn_new(struct fohh_connection *f_conn)
{
    struct rx_state *rs;
    struct argo_state *argo;
    int res;

    argo = fohh_argo_get_rx(f_conn);

    if ((res = argo_register_structure(argo, zpn_siem_element_description, zpn_siem_element_cb, f_conn))) {
        ZPN_LOG(AL_ERROR, "%s: Could not register zpn_siem_element for SIEM connection", fohh_description(f_conn));
        return res;
    }

    rs = SIEM_CALLOC(sizeof(*rs));
    if (!rs) {
        ZPN_LOG(AL_ERROR, "Memory");
        return ZPN_RESULT_NO_MEMORY;
    }
    if(!rs->conn_start_time){
        rs->conn_start_time = epoch_us();
    }
    rs->f_conn = f_conn;
    rs->f_conn_incarnation = fohh_connection_incarnation(f_conn);
    TAILQ_INIT(&(rs->objects));
    rs->lock = ZPATH_MUTEX_INIT;

    TAILQ_INIT(&(rs->acks));
    rs->ack_timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(f_conn)), -1, 0, ack_timer, rs);
    if (!rs->ack_timer) {
        ZPN_LOG(AL_ERROR, "Memory");
        SIEM_FREE(rs->name);
        SIEM_FREE(rs);
        return ZPN_RESULT_NO_MEMORY;
    }

    __sync_add_and_fetch_8(&(rs->ref_count), 1);
    __sync_add_and_fetch_8(&(rs->fohh_stats), 1);

    // Don't give the f_conn a handle until we are ready
    update_name(rs);
    fohh_connection_set_dynamic_cookie(f_conn, rs);


    return ZPN_RESULT_NO_ERROR;
}


static int zpn_siem_fohh_rx_conn_close(struct fohh_connection *f_conn)
{
    struct rx_state *rs = fohh_connection_get_dynamic_cookie(f_conn);

    if (!rs) {
        /* This can happen when fohh connection failed to establish,
         * callback might be called as if connection went away... */
        ZPN_LOG(AL_NOTICE, "%s: Received connection (tunnel) close before connection established", fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(rs->lock), __FILE__, __LINE__);
    ZPN_LOG(AL_NOTICE, "%s: %s: Received connection (tunnel) close", rs->name, fohh_description(f_conn));
    ZPATH_MUTEX_UNLOCK(&(rs->lock), __FILE__, __LINE__);

    /* Unlinking should happen immediately so that rs_destroy
     * doesn't send a connection_delete to fohh, since fohh is
     * telling us about the delete... */
    rs_destroy(rs, destroy_fohh);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_fohh_conn_callback(struct fohh_connection *connection,
                                       enum fohh_connection_state state,
                                       void *cookie)
{
    if (state == fohh_connection_connected) {
        return zpn_siem_fohh_rx_conn_new(connection);
    } else {
        return zpn_siem_fohh_rx_conn_close(connection);
    }
}

static int zpn_siem_fohh_unblock_callback(struct fohh_connection *connection,
                                          enum fohh_queue_element_type element_type,
                                          void *cookie)
{
    /* We are unlikely to get into this state... And we can drop acks,
     * so we won't worry about it yet. */
    ZPN_LOG(AL_WARNING, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}


static int print_rx_state_details(const void *cookie_rs, void *buffer_start, size_t buffer_len)
{
    const struct rx_state *rs = cookie_rs;
    char *s = buffer_start;

    int bytes_written = 0;

    if (rs && !rs->deleting && s && buffer_len) {
        bytes_written += sxprintf(s, s + buffer_len,
                                        "\tName: %s\n"
                                        "\tTime: Started %"PRId64"s ago at %"PRId64"\n"
                                        "\tRecv %"PRId64", Trans %"PRId64", Acked %"PRId64", Q %"PRId64"[%"PRId64" bytes], Ack-Q: %"PRId64", drops: %"PRId64"\n",
                                          rs->name ? rs->name : "", (int64_t)((epoch_us() - rs->conn_start_time)/1000000), rs->conn_start_time,
                                          rs->objects_received, rs->objects_transmitted, rs->objects_acknowledged,
                                          rs->queue_count, rs->queue_size, rs->ack_count, rs->siem_stat ? rs->siem_stat->log_dropped_count : 0);
    }

    return bytes_written;
}

static struct lookup_lib_init_table init_table[] = {
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_CATEGORY_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_CUSTOMER_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_RULE_HELPER,              0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APPLICATION_HELPER,         0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APPLICATION_GROUP_HELPER,   0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_APP_SERVER_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_ASSISTANT_GROUP_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_ASSISTANT_HELPER,           0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_POLICY_SET_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_RULE_HELPER,                0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SAML_ATTRS_HELPER,          0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SERVER_GROUP_HELPER,        0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_POSTURE_PROFILE_DB_HELPER,  0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_TRUSTED_NETWORK_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "friendly_name", ZPATH_INSTANCE_HELPER, 1, 0},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_IDP_HELPER,                 0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_PRIVATE_BROKER_HELPER,      0, 1},
	{GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_PRIVATE_BROKER_GROUP_HELPER,0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_IDP_CERT_HELPER,            0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPATH_SERVICE_HELPER,           0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SITE_CONTROLLER_HELPER,     0, 1},
    {GLOBALLY_UNIQUE_ID,      "gid",      "name", ZPN_SITE_CONTROLLER_GROUP_HELPER, 0, 1},
};


int zpn_siem_rx_listen(struct fohh_generic_server *sni_server, char *sni_str, char *sni_str_json)
{
    struct fohh_connection *f_conn;
    int res;
    static int initialized = 0;

    if (!initialized) {
        /* Start up the lookup service. */

        res = lookup_lib_add(init_table, sizeof(init_table) / sizeof(init_table[0]));
        if (res) {
            ZPN_LOG(AL_ERROR, "Could nit initialize lookup tables: %s", zpn_result_string(res));
            return res;
        }


        memset(broker_name, 0, sizeof(broker_name));
#if MY_TEST
        strncpy(broker_name, "broker.dev.zpath.net", strlen("broker.dev.zpath.net"));
#else
        snprintf(broker_name, sizeof(broker_name), "broker.%s", ZPATH_LOCAL_CLOUD_NAME);
#endif

        initialized = 1;
    }

    if (!sni_server || !sni_str || !sni_str_json) return ZPN_RESULT_ERR;

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_siem_fohh_conn_callback,
                                NULL,
                                zpn_siem_fohh_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1,    // int require_assistant_cert,
                                1,    // int use_ssl);
                                NULL, // ssl ctx callback
                                NULL, // verify callback
                                NULL,
                                1,    // Allow binary argo.
                                60*10);  // timeout- about 10 minutes. Nice to be bigger than the 5 minute default simple ack timeout
    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Could not create server for siem_rx");
        return ZPN_RESULT_ERR;
    }
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       0,
                                       FOHH_WORKER_ZPN_SLOGGER);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register siem_rx server");
        return res;
    }

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo_tlv, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_siem_fohh_conn_callback,
                                NULL,
                                zpn_siem_fohh_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1,    // int require_assistant_cert,
                                1,    // int use_ssl);
                                NULL, // ssl ctx callback
                                NULL, // verify callback
                                NULL,
                                1,    // Allow binary argo.
                                60*10);  // timeout- about 10 minutes. Nice to be bigger than the 5 minute default simple ack timeout
    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Could not create server for siem_rx_json");
        return ZPN_RESULT_ERR;
    }
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str_json,
                                       0,
                                       FOHH_WORKER_ZPN_SLOGGER);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register siem_rx_json server");
        return res;
    }

    zpn_fohh_client_slogger_init(fohh_get_thread_event_base(fohh_connection_get_thread_id(f_conn)), print_rx_state_details);

    return ZPN_RESULT_NO_ERROR;
}
