/*
 * zpn_mconn_bufferevent.h. Copyright (C) 2014 Zscaler Inc. All rights reserved.
 *
 * This is a wrapper over libevent's bufferevent for TCP. Today bufferevent is
 * supported only for TCP.
 */

#ifndef _ZPN_MCONN_BUFFEREVENT_H_
#define _ZPN_MCONN_BUFFEREVENT_H_

#include <sys/queue.h>

#include "zpn/zpn_mconn.h"

struct zpn_mconn_bufferevent {
    struct zpn_mconn mconn;		/* This has to be the first member */

    struct bufferevent *bev;

    uint32_t rx_paused;
    uint32_t tx_paused;

    uint64_t rx_bytes;
    uint64_t tx_bytes;

    int rx_fin_pending;
    int tx_fin_pending;

    int is_connector_tun;

    int async_tx_count;
    int async_rx_count;
    int async_event_count;

    unsigned long ssl_erk;

    int quickack;

    uint64_t pause_count;
    uint64_t resume_count;
    uint64_t last_pause_time_s;
    uint64_t last_resume_time_s;
    uint64_t max_pause_time_s;
    uint64_t last_pause_time_monotime_s;
    uint64_t max_pause_time_interval;
};


/*
 * Init for mconn_bufferevent.
 *
 * fohh_thread_id is the fohh thread upon which bev
 * lives. Furthermore, this call must be called within the context of
 * that thread.
 *
 * bev must me in a connected state. There can be tx/rx data queued on
 * the tx/rx evbuffers at the time of the call. The events for this
 * bufferevent will be changed when this routine is called. Ownership
 * of the bev will pass to this routine, for all time. The bufferevent
 * is assumed to be created WITHOUT locking and with deferred
 * callbacks, and definitely NOT close_on_free.
 *
 * TBD: is this really right, for BEV configuration expectation?
 */
int zpn_mconn_bufferevent_init(struct zpn_mconn_bufferevent *mconn_bufferevent,
                               void *mconn_self,
                               enum zpn_mconn_type type);

extern const struct zpn_mconn_local_owner_calls mconn_bufferevent_calls;

int zpn_mconn_bufferevent_stuck(struct zpn_mconn_bufferevent *mconn_b);
int zpn_mconn_bufferevent_done(struct zpn_mconn_bufferevent *mconn_bufferevent);
int zpn_mconn_bufferevent_clean(struct zpn_mconn_bufferevent *mconn_bufferevent);

void zpn_mconn_bufferevent_internal_display(struct zpn_mconn_bufferevent *mconn_bufferevent);

void zpn_mconn_bufferevent_set_quickack(struct zpn_mconn_bufferevent *buffevent_mconn);
/*
 *
 * This API will inject the input evbuffer data into the zpn_mconn_bufferevent asynchronously
 * as inject when the corresponding fohh thread is ready.
 *
 * arguments:
 *       *mconn_b : the bufferevent mconn caller wants to inject data to.
 *       *buf     : the data in evbuffer structure format. this will be allocated by the caller.
 *
 * Note: this API will take care of freeing the *buf on success/failure cases.
 *
 */
void zpn_mconn_bufferevent_synthetic_response(struct zpn_mconn_bufferevent *mconn_b, struct evbuffer* buf);

void zpn_mconn_bufferevent_synthetic_request(struct zpn_mconn_bufferevent *mconn_b, struct evbuffer* buf);

/* For UT */
extern struct zpn_mconn_local_owner_calls ut_mconn_bufferevent_calls;

/* Window update status of inner tunnel of mconn */
extern int64_t zpn_mconn_inner_tunnel_window_update_status;

extern int64_t zpn_allocator_libevent_out_queue_allowed_bytes;

#endif /* _ZPN_MCONN_BUFFEREVENT_H_ */
