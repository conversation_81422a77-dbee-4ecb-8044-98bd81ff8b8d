/*
 * zpn_firedrill_site.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_FIREDRILL_SITE_H_
#define _ZPN_FIREDRILL_SITE_H_

#include "argo/argo.h"
#include "wally/wally.h"
struct zpn_firedrill_site {             /* _ARGO: object_definition */
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int64_t deleted;                           /* _ARGO: integer, deleted */

    /* Real fields */
    int64_t id;                               /* _ARGO: integer, index, key */
    int64_t customer_gid;                      /* _ARGO: integer, index */
    int64_t scope_gid;                         /* _ARGO: integer, index */
    int64_t site_gid;                          /* _ARGO: integer, index */
    int64_t firedrill_interval;                /* _ARGO: integer */
    int64_t firedrill_interval_s;              /* _ARGO: integer , nodb */
    char *firedrill_interval_unit;             /* _ARGO: string */
};

extern struct argo_structure_description *zpn_firedrill_site_description;

int zpn_firedrill_site_init(struct wally *single_tenant_wally,
                        int64_t single_tenant_gid,
                        wally_row_callback_f *row_cb,
                        int single_tenant_fully_loaded,
                        int register_with_zpath_table);

int zpn_firedrill_site_get_by_customer_gid(int64_t customer_gid,
                                        struct zpn_firedrill_site **firedrill_site,
                                        size_t *row_count,
                                        wally_response_callback_f callback_f,
                                        void *callback_cookie,
                                        int64_t callback_id)
                                         __attribute__((weak));

int zpn_firedrill_site_by_site_gid(int64_t site_gid,
                                    int64_t customer_gid,
                                    wally_response_callback_f *callback_f,
                                    struct zpn_firedrill_site **firedrill_instance)
                                    __attribute__((weak));


void zpn_firedrill_site_row_fixup(struct argo_object *row);

int zpn_firedrill_site_row_callback(void *cookie,
                                       struct wally_registrant *registrant,
                                       struct wally_table *table,
                                       struct argo_object *previous_row,
                                       struct argo_object *row,
                                       int64_t request_id);

#endif /* _ZPN_FIREDRILL_SITE_H_ */
