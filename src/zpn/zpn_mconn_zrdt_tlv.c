/*
 * zpn_mconn_zrdt_tlv.h. Copyright (C) 2019 Zscaler Inc. All rights reserved.
 */

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>

#include <netinet/udp.h>

#include "zpath_lib/zpath_debug.h"
#include "argo/argo_hash.h"
#include "zrdt/zrdt.h"
#include "ztlv/zpn_tlv.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn/zpn_mconn_transmit_buffer.h"

char *zpn_mconn_zrdt_description(struct zpn_zrdt_tlv *zrdt_tlv)
{
    return zdtls_description(zrdt_conn_get_datagram_tx_cookie(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv)));
}

static struct zpn_mconn_zrdt_tlv *
zpn_mconn_zrdt_tlv_lookup_stream_id(struct zpn_zrdt_tlv *zrdt_tlv, int64_t stream_id, int64_t *incarnation)
{
    struct zpn_mconn_zrdt_tlv *res = NULL;

    /* Since tag_id is 32 bit, we probably not going to support stream_id greater than 32 bit integer */
    int32_t tag_id = (int32_t)stream_id;

    ZPATH_MUTEX_LOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
    if (zrdt_tlv->streams) {
        res = argo_hash_lookup(zrdt_tlv->streams,
                           &tag_id,
                           sizeof(tag_id),
                           NULL);
        if (res) {
            struct zpn_mconn *mconn = &(res->mconn);
            *incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                                mconn->self,
                                                                mconn->global_owner,
                                                                mconn->global_owner_key,
                                                                mconn->global_owner_key_length);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
    return res;
}

void zpn_tlv_clear_tlv_ps(struct zpn_zrdt_tlv_parse_state *tlv_ps)
{
    if (tlv_ps->tlv_read_buf) {
        evbuffer_free(tlv_ps->tlv_read_buf);
    }
}

void zpn_zrdt_tlv_clear_state(struct zpn_zrdt_tlv *zrdt_tlv)
{
    zpn_tlv_clear(&(zrdt_tlv->tlv));
    zrdt_tlv->rx_data = 0;
    zrdt_tlv->tx_data = 0;

    zrdt_tlv->rx_data_us = 0;
    zrdt_tlv->tx_data_us = 0;
    zrdt_tlv->enq_data_us = 0;

    zrdt_tlv->max_stream_id = 0;

    zpn_tlv_clear_tlv_ps(&(zrdt_tlv->tlv_ps));
}

void zpn_zrdt_tlv_set_conn(struct zpn_zrdt_tlv *zrdt_tlv, struct zrdt_conn *z_conn)
{
    zpn_tlv_set_conn(&(zrdt_tlv->tlv), zpn_zrdt_tlv, z_conn);
}

struct zrdt_conn *zpn_mconn_zrdt_tlv_get_conn(struct zpn_zrdt_tlv *zrdt_tlv)
{
    return (zrdt_tlv->tlv.conn.z_conn);
}

int zpn_zrdt_tlv_init(struct zpn_zrdt_tlv *zrdt_tlv, struct zrdt_conn *z_conn, int64_t tlv_incarnation)
{
    zrdt_tlv->tlv.type = zpn_zrdt_tlv;
    zpn_zrdt_tlv_clear_state(zrdt_tlv);
    zpn_tlv_init(&(zrdt_tlv->tlv), zpn_zrdt_tlv, z_conn, tlv_incarnation);

    zrdt_tlv->lock = ZPATH_MUTEX_INIT;
    if (zrdt_tlv->streams) argo_hash_free(zrdt_tlv->streams);
    zrdt_tlv->streams = argo_hash_alloc(7, 1);
    if (!zrdt_tlv->streams) return ZPN_RESULT_NO_MEMORY;
    LIST_INIT(&(zrdt_tlv->mconn_list));
    zrdt_conn_set_dynamic_cookie(z_conn, zrdt_tlv);
    zrdt_tlv->list_size = 0;

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_mconn_zrdt_tlv *zpn_zrdt_tlv_get_mconn(struct zpn_zrdt_tlv *zrdt_tlv)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = NULL;

    ZPATH_MUTEX_LOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
    mconn_zrdt_tlv = LIST_FIRST(&(zrdt_tlv->mconn_list));
    ZPATH_MUTEX_UNLOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);

    return mconn_zrdt_tlv;
}

int zpn_zrdt_tlv_destroy(struct zpn_zrdt_tlv *zrdt_tlv, const char *reason)
{
    int ret = ZPN_RESULT_NO_ERROR;
    int res;
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv;

    /*
     * This non-null reason error code have to eventually go away and the callers have to be specific on why it failed.
     */
    if (!reason) {
        reason = MT_CLOSED_DTLS_CONN_GONE;
    }

    while ((mconn_zrdt_tlv = zpn_zrdt_tlv_get_mconn(zrdt_tlv))) {
        struct zpn_mconn *mconn = &(mconn_zrdt_tlv->mconn);

        if (!mconn->global_owner) {
            /*
             * Not having a global owner should never occur. But
             * 'continue' here is a clear infinite loop. So we log and
             * attempt mconn termination regardless.
             */
            //continue;
            //ZPN_LOG(AL_CRITICAL, "%s: Mconn without global owner", zrdt_tlv->f_conn ? zpn_tconn_description(&(fohh_tlv->t_conn)) : "null f_conn");
        }

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }

        res = zpn_mconn_terminate(&(mconn_zrdt_tlv->mconn), 1, 0, reason, NULL);
        if (res) ret = res; /* Should this break the loop? Probably...? */

        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->unlock)(mconn,
                                                mconn->self,
                                                mconn->global_owner,
                                                mconn->global_owner_key,
                                                mconn->global_owner_key_length);
        }

    }

    ZPATH_MUTEX_LOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);

    argo_hash_free(zrdt_tlv->streams);
    if (zrdt_tlv->msg_stream) {
        struct zpn_zrdt_argo_state *argo = zrdt_get_msg_codec_state(zrdt_tlv->msg_stream);

        if (argo) {
            zrdt_set_msg_codec_state(zrdt_tlv->msg_stream, NULL, NULL);
            zpn_zrdt_free_argo_state(argo);
        }

        zrdt_stream_done(zrdt_tlv->msg_stream);
        zrdt_tlv->msg_stream = NULL;
    }

    zrdt_conn_destroy(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
    zrdt_tlv->streams = NULL;
    zpn_zrdt_tlv_clear_state(zrdt_tlv);

    ZPATH_MUTEX_UNLOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);

    return ret;
}

void *zpn_zrdt_tlv_get_global_owner(struct zpn_zrdt_tlv *zrdt_tlv,
                                    int64_t stream_id)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv;
    int64_t incarnation = 0;
    mconn_zrdt_tlv = zpn_mconn_zrdt_tlv_lookup_stream_id(zrdt_tlv, stream_id, &incarnation);
    if (mconn_zrdt_tlv) {
        return mconn_zrdt_tlv->mconn.self;
    } else {
        return NULL;
    }
}

void *zpn_zrdt_tlv_get_local_owner(struct zpn_zrdt_tlv *zrdt_tlv,
                                   int64_t stream_id)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = NULL;
    int64_t incarnation = 0;
    mconn_zrdt_tlv = zpn_mconn_zrdt_tlv_lookup_stream_id(zrdt_tlv, stream_id, &incarnation);
    return mconn_zrdt_tlv;
}

int zpn_mconn_zrdt_tlv_bind_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t *owner_incarnation)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = mconn_base;
    struct zpn_zrdt_tlv *zrdt_tlv = owner;
    int res;

    if (mconn_zrdt_tlv->stream_id > 0) {
        ZPN_LOG(AL_ERROR, "bind when already bound");
        return ZPN_RESULT_ERR;
    }

    if (!zrdt_tlv->streams) {
        ZPN_LOG(AL_ERROR, "Can't complete bind, zrdt connection likely destroyed");
        return ZPN_RESULT_ERR;
    }

    res = argo_hash_store(zrdt_tlv->streams,
                          owner_key,
                          owner_key_length,
                          0,
                          mconn_zrdt_tlv);

    if (res) {
        ZPN_LOG(AL_ERROR, "Could not store: %s", zpn_result_string(res));
        return res;
    }

    LIST_INSERT_HEAD(&(zrdt_tlv->mconn_list), mconn_zrdt_tlv, mconn_list_entry);
    zrdt_tlv->list_size++;

    mconn_zrdt_tlv->stream_id = *((int32_t *) owner_key);

    mconn_zrdt_tlv->zrdt_tlv = zrdt_tlv;

    if (owner_incarnation) {
        *owner_incarnation = zrdt_conn_incarnation(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_zrdt_tlv_unbind_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int drop_buffered_data,
                                 int dont_propagate,
                                 const char *err)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = mconn_base;
    struct zpn_zrdt_tlv *zrdt_tlv = owner;
    int res;

    // private broker seems to have an mtunnel with stream id 0(ET-35737), which is typically used only for RPC.
    // Since its ok to do the clean-up don't check for non-zero stream-id here.
    if (mconn_zrdt_tlv->stream) {
        zrdt_stream_done(mconn_zrdt_tlv->stream);
        mconn_zrdt_tlv->stream = NULL;
    } else {
        ZPN_DEBUG_MTUNNEL("unbind when no stream");
    }

    if (zrdt_conn_incarnation(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv)) != owner_incarnation) {
        /* Ooold request?? */
        ZPN_LOG(AL_ERROR, "Old incarnation");
        return ZPN_RESULT_ERR;
    }

    if (!dont_propagate) {
        res = zpn_mconn_forward_mtunnel_end(&(mconn_zrdt_tlv->mconn), err, drop_buffered_data);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
        }
    }

    res = argo_hash_remove(zrdt_tlv->streams,
                           owner_key,
                           owner_key_length,
                           mconn_zrdt_tlv);

    if (res) {
        ZPN_DEBUG_MCONN("Cannot find tag to remove from zrdt stream_id hash: %s", zpn_result_string(res));
        ZPN_DEBUG_MCONN("mconn_zrdt_tlv->stream_id = %d, stream_id = %d", mconn_zrdt_tlv->stream_id, *(int32_t *)owner_key);
        return res;
    }

    LIST_REMOVE(mconn_zrdt_tlv, mconn_list_entry);
    zrdt_tlv->list_size--;

    mconn_zrdt_tlv->stream_id = 0;
    mconn_zrdt_tlv->zrdt_tlv = NULL;
    zpn_tlv_clear_tlv_ps(&(mconn_zrdt_tlv->tlv_ps));

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_zrdt_tlv_lock_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length)
{
    struct zpn_zrdt_tlv *zrdt_tlv = owner;

    ZPATH_MUTEX_LOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
}


void zpn_mconn_zrdt_tlv_unlock_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length)
{
    struct zpn_zrdt_tlv *zrdt_tlv = owner;

    ZPATH_MUTEX_UNLOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
}

static int zpn_mconn_zrdt_tlv_stream_transmit(struct zpn_zrdt_tlv *zrdt_tlv,
                                              struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv,
                                              struct evbuffer *buf,
                                              size_t buf_len)
{
    struct zpn_mconn *mconn = &(mconn_zrdt_tlv->mconn);
    size_t sent_len = 0;
    int res;

    if (!buf || !buf_len) {
        return ZPN_RESULT_NO_ERROR;
    }

    res = zrdt_stream_transmit_evbuffer(mconn_zrdt_tlv->stream, buf, buf_len);

   ZPATH_MUTEX_LOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);
    if (res) {
        if (res == ZPATH_RESULT_WOULD_BLOCK) {
            size_t residual_len = evbuffer_get_length(buf);
            ZPN_DEBUG_MCONN("Stream transmit blocked");
            sent_len = buf_len - residual_len;
        } else {
            ZPN_LOG(AL_NOTICE, "FIX ME, stream transmit error");
        }
    } else {
        sent_len = buf_len;
    }

    mconn_zrdt_tlv->mconn.bytes_to_client += sent_len;
    zrdt_tlv->tx_data += sent_len;

    if (!mconn->tx_data_us_b) {
        mconn->tx_data_us_b = epoch_us();
    }
    mconn->tx_data_us = epoch_us();
    ZPATH_MUTEX_UNLOCK(&(zrdt_tlv->lock), __FILE__, __LINE__);

    /* Check to see if we need to resume peer */
    if (mconn->peer) {
        if (sent_len) {
            zpn_mconn_client_window_update(mconn->peer, 0, (int)sent_len, 0);
        }

        if (zpn_mconn_get_transmit_buffer_len(mconn) < ZPN_MCONN_MAX_CLIENT_TX_DATA) {
            res = zpn_mconn_resume_client(mconn->peer, 1);
            if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
                ZPN_LOG(AL_ERROR, "Cannot resume peer, res = %s", zpn_result_string(res));
                zpn_mconn_terminate(mconn->peer, 1, 0, MT_CLOSED_INTERNAL_ERROR, NULL);
            }
        }
    }

    zpn_fohh_worker_tx_data_zrdt(zrdt_tlv->fohh_thread_id, sent_len);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_zrdt_tlv_transmit_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length,
                                   int64_t owner_incarnation,
                                   int fohh_thread_id,
                                   struct evbuffer *buf,
                                   size_t buf_len)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = mconn_base;
    struct zpn_zrdt_tlv *zrdt_tlv = owner;

    if (!mconn_zrdt_tlv->stream) {
        ZPN_DEBUG_MCONN("No stream to send? maybe the stream is not ready yet");
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_mconn_zrdt_tlv_stream_transmit(zrdt_tlv, mconn_zrdt_tlv, buf, buf_len);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_zrdt_tlv_pause_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_zrdt_tlv_resume_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_zrdt_tlv_forward_tunnel_end_cb(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length,
                                             int64_t owner_incarnation,
                                             const char *err,
                                             int32_t drop_data)
{
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = mconn_base;
    struct zpn_zrdt_tlv *zrdt_tlv = owner;
    int res = ZPN_RESULT_NO_ERROR;

    if (!zrdt_all_data_sent(mconn_zrdt_tlv->stream)) {
        ZPN_DEBUG_MCONN("Underlying stream id = %ld has not sent out all the data yet", (long)mconn_zrdt_tlv->stream_id);
        mconn_zrdt_tlv->mconn.client_needs_to_forward = 1;
        mconn_zrdt_tlv->mconn.fin_sent_us = 0;
        return res;
    }

    res = zpn_send_zpn_mtunnel_end(&(zrdt_tlv->tlv),
                                   owner_incarnation,
                                   NULL,
                                   *((int32_t *)owner_key),
                                   err,
                                   drop_data);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
    } else {
        ZPN_DEBUG_MCONN("Forwarded mtunnel_end message to %s, drop_data = %d, to_client = %ld",
                        //zpn_tconn_description(&(fohh_tlv->t_conn)), drop_data, (long)mconn_fohh_tlv->mconn.bytes_to_client);
                        "ZRDT", drop_data, (long)mconn_zrdt_tlv->mconn.bytes_to_client);
    }

    /* We should set fin_sent even when mtunnel_end send failed, because in that case,
     * there is something wrong with the fohh connection, and we should kill the mtunnel
     * anyway.
     */
    mconn_zrdt_tlv->mconn.fin_sent = 1;
    if (drop_data) {
        mconn_zrdt_tlv->mconn.fin_sent_drop_data = 1;
        zpn_mconn_discard_client_data(&(mconn_zrdt_tlv->mconn), NULL);
    }

    return res;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_zrdt_tlv_window_update_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id,
                                         int tx_len,
                                         int batch_win_upd)
{
    /* We don't do flow control at ZPN level for ZRDT */
    return;
}

int zpn_mconn_zrdt_tlv_init(struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv, void *mconn_self, enum zpn_mconn_type type)
{
    mconn_zrdt_tlv->stream_id = 0;
    mconn_zrdt_tlv->data_arrived = 0;
    mconn_zrdt_tlv->stream = NULL;
    return zpn_mconn_init(&(mconn_zrdt_tlv->mconn), mconn_self, type);
}

void zpn_mconn_zrdt_tlv_stats_update_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t owner_incarnation,
                                    int fohh_thread_id,
                                    enum zpn_mconn_stats stats_name)
{
    return;
}
int zpn_mconn_zrdt_tlv_disable_read_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
int zpn_mconn_zrdt_tlv_enable_read_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls zpn_mconn_zrdt_tlv_calls = {
    zpn_mconn_zrdt_tlv_bind_cb,
    zpn_mconn_zrdt_tlv_unbind_cb,
    zpn_mconn_zrdt_tlv_lock_cb,
    zpn_mconn_zrdt_tlv_unlock_cb,
    zpn_mconn_zrdt_tlv_transmit_cb,
    zpn_mconn_zrdt_tlv_pause_cb,
    zpn_mconn_zrdt_tlv_resume_cb,
    zpn_mconn_zrdt_tlv_forward_tunnel_end_cb,
    zpn_mconn_zrdt_tlv_window_update_cb,
    zpn_mconn_zrdt_tlv_stats_update_cb,
    zpn_mconn_zrdt_tlv_disable_read_cb,
    zpn_mconn_zrdt_tlv_enable_read_cb
};

int zpn_mconn_zrdt_tlv_clean(struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv)
{
    if (!zpn_mconn_clean(&(mconn_zrdt_tlv->mconn))) {
        ZPN_DEBUG_MCONN("mconn zrdt tlv not clean");
        return 0;
    }
    if (mconn_zrdt_tlv->stream_id == 0) {
        ZPN_DEBUG_MCONN("mconn zrdt tlv clean");
        return 1;
    } else {
        ZPN_DEBUG_MCONN("mconn zrdt tlv not clean, stream_id = %ld", (long)mconn_zrdt_tlv->stream_id);
        return 0;
    }
}

static void zpn_mconn_zrdt_stream_state_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    if (!cookie1) {
        return;
    }

    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv = cookie1;
    if(mconn_zrdt_tlv->stream == NULL) {
        ZPN_DEBUG_MCONN("%p: Stream got destroyed", mconn_zrdt_tlv);
        return;
    }

    ZPN_DEBUG_MCONN("%p: stream: %p with stream id: %ld dump state",
                     mconn_zrdt_tlv,
                     mconn_zrdt_tlv->stream,
                     (long)mconn_zrdt_tlv->stream_id);
    zrdt_dump_stream_state(mconn_zrdt_tlv->stream);
}

void zpn_mconn_zrdt_tlv_internal_display(struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv)
{
    struct zevent_base *base;
    ZPN_DEBUG_MCONN("tag_id = %ld", (long)mconn_zrdt_tlv->stream_id);
    zpn_mconn_internal_display(&(mconn_zrdt_tlv->mconn));
    /*
     * dumping stream state touches the internal structure of the stream. To avoid data races it has to be done by the
     * assigned I/O thread. This avoids having to take the stream lock which is expensive.
     */
    if (mconn_zrdt_tlv->stream) {
        base = fohh_thread_id_zevent_base(mconn_zrdt_tlv->zrdt_tlv->fohh_thread_id);
        ZPN_DEBUG_MCONN("%p: for stream: %p with stream id %ld dump state from thread %d",
                         mconn_zrdt_tlv,
                         mconn_zrdt_tlv->stream,
                         (long)mconn_zrdt_tlv->stream_id,
                         mconn_zrdt_tlv->zrdt_tlv->fohh_thread_id);
        zevent_base_call(base, zpn_mconn_zrdt_stream_state_cb, mconn_zrdt_tlv, 0);
    }
}

int zpn_zrdt_tlv_data_callback(struct zrdt_conn *z_conn,
                               void *cookie,
                               int64_t stream_id,
                               struct zrdt_stream *stream)
{
    struct zpn_zrdt_tlv *zrdt_tlv = cookie;
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv;
    struct zpn_mconn *mconn;
    struct zpn_mconn *peer;
    int res = ZPN_RESULT_NO_ERROR;
    int64_t original_incarnation = 0;
    enum zrdt_stream_type stream_type;
    size_t data_length = 0;

    if (!zrdt_tlv) {
        ZPN_LOG(AL_ERROR, "No zrdt_tlv.");
        return ZPN_RESULT_ERR;
    }

    mconn_zrdt_tlv = zpn_mconn_zrdt_tlv_lookup_stream_id(zrdt_tlv, stream_id, &original_incarnation);
    if (!mconn_zrdt_tlv) {
        ZPN_DEBUG_MCONN("Data arrived on invalid stream_id = %ld, %s", (long)stream_id, zpn_mconn_zrdt_description(zrdt_tlv));

        if (stream_id > zrdt_tlv->max_stream_id) {
            ZPN_DEBUG_MCONN("ZRDT max stream_id = %ld, received stream_id = %ld, data arrived before mtunnel is setup",
                    (long)zrdt_tlv->max_stream_id, (long)stream_id);
        }

        /* Don't return error, or the full TLS connection will
         * close. This is likely just stale data thar arrived after we
         * closed this tag. */
        return ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_DEBUG_MCONN("%s: stream %p id %ld, data arrived", zpn_mconn_zrdt_description(zrdt_tlv), stream, (long)stream_id);
    }

    zrdt_tlv->rx_data_us = epoch_us();

    mconn = &(mconn_zrdt_tlv->mconn);

    /* FIXME: Should we handle UDP drop here??? */

    if (mconn->global_owner) {
        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        } else {
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!mconn->global_owner) {
        /*
         * Global owner might be gone after we grab the lock, so check it again.
         * Bail if that's the case.
         */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        return ZPN_RESULT_NO_ERROR;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               original_incarnation)) {
        /* mtunnel changed under us? move on to next mtunnel */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);

        return ZPN_RESULT_NO_ERROR;
    }

    peer = mconn->peer;
    if (peer) {

        stream_type = zrdt_stream_get_type(stream);

        if (stream_type == zrdt_stream_reliable_endpoint) {
            struct zbuffer *zbuf = zrdt_stream_get_rx_buffer(stream);

            if (zbuf) {
                struct evbuffer *data = zbuffer_get_evbuffer(zbuf);


                if (data) {
                    size_t residule_len;

                    data_length = evbuffer_get_length(data);

                    ZPN_DEBUG_MCONN("Stream_id = %ld, data arrived = %ld, total = %ld", (long)stream_id, (long)data_length, (long)zrdt_tlv->rx_data);

                    res = zpn_client_process_rx_data(&(mconn_zrdt_tlv->mconn), data, evbuffer_get_length(data), NULL, NULL);
                    /* Asynchronous result or UDP framing error are both okay, the
                     * tunnel will be fine */
                    if ((res == ZPN_RESULT_ASYNCHRONOUS) || (res == ZPN_RESULT_BAD_DATA)) {
                        res = ZPN_RESULT_NO_ERROR;
                    }

                    residule_len = evbuffer_get_length(data);
                    data_length -= residule_len;
                }
            } else {
                ZPN_DEBUG_MCONN("No rx buffer?");
            }
        } else if (stream_type == zrdt_stream_transit) {
            struct zpn_mconn_zrdt_tlv *peer_mconn_zrdt_tlv = (struct zpn_mconn_zrdt_tlv *)peer;
            struct zrdt_packet_tailq_head *pkts = NULL;
            struct zrdt_conn *peer_z_conn = NULL;
            uint64_t count;
            uint64_t bytes;

            ZPN_DEBUG_MCONN("read_cb() for transit stream");

            if (peer_mconn_zrdt_tlv->stream) {
                pkts = zrdt_stream_get_rx_packets(stream, &count, &bytes);
                if (pkts) {
                    ZPN_DEBUG_MCONN("has packet for transit stream");

                    mconn->bytes_to_peer += bytes;

                    if (!mconn->rx_data_us_b) {
                        mconn->rx_data_us_b = epoch_us();
                    }
                    mconn->rx_data_us = epoch_us();

                    peer->bytes_from_peer += bytes;
                    peer->bytes_to_client += bytes;
                    if (!peer->tx_data_us_b) {
                        peer->tx_data_us_b = epoch_us();
                    }
                    peer->tx_data_us = epoch_us();

                    data_length = bytes;

                    res = zrdt_stream_transmit_packet_queue(peer_mconn_zrdt_tlv->stream, pkts);
                    if (res) {
                        ZPN_LOG(AL_INFO, "Send packets failed?");
                    }

                    /* Update the transmite side */
                    if (peer_mconn_zrdt_tlv->zrdt_tlv) {
                        peer_mconn_zrdt_tlv->zrdt_tlv->tx_data = data_length;
                    }
                    peer_z_conn = zrdt_stream_get_rdt_conn(peer_mconn_zrdt_tlv->stream);
                    if (peer_z_conn) {
                        zpn_fohh_worker_tx_data_zrdt(zrdt_conn_get_thread_id(peer_z_conn), data_length);
                    }
                } else {
                    ZPN_DEBUG_MCONN("No packet to send?");
                }
            } else {
                ZPN_LOG(AL_INFO, "Peer doesn't have ZRDT conn");
            }
        } else if (stream_type == zrdt_stream_unreliable_endpoint) {
            struct evbuffer *data = evbuffer_new();

            if (data) {
                char *msg = NULL;
                int len = 0;
                int count = 0;

                while ((msg = zrdt_stream_get_message(stream, &len))) {
                    ZPN_DEBUG_MCONN("Got unreliable message, len = %d", len);
                    res = evbuffer_add(data, msg, len);
                    zrdt_msg_buf_buf_done(msg);
                    if (res == -1) {
                        ZPN_LOG(AL_NOTICE, "Failed to add data to evbuffer??");
                        break;
                    }
                    count ++;
                }

                data_length = evbuffer_get_length(data);

                if (data_length) {
                    ZPN_DEBUG_MCONN("Got %d unreliable messages with total length %ld bytes", count, data_length);
                    res = zpn_client_process_rx_data(&(mconn_zrdt_tlv->mconn), data, data_length, NULL, NULL);
                }

                evbuffer_drain(data, evbuffer_get_length(data));
                evbuffer_free(data);
            } else {
                ZPN_DEBUG_MCONN("Cannot allocate evbuffer for unreliable messages");
            }
        } else {
            ZPN_DEBUG_MCONN("read_cb() for buffer stream");
        }
    } else {
        ZPN_DEBUG_MCONN("No peer??");
    }

    if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    mconn_zrdt_tlv->data_arrived += data_length;
    zrdt_tlv->rx_data += data_length;
    zpn_fohh_worker_rx_data_zrdt(zrdt_tlv->fohh_thread_id, data_length);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_zrdt_tlv_stream_unblock_callback(struct zrdt_conn *z_conn,
                                         struct zrdt_stream *stream,
                                         int64_t stream_id,
                                         enum zrdt_stream_status status,
                                         void *cookie)
{
    struct zpn_zrdt_tlv *zrdt_tlv = cookie;
    struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv;
    struct zpn_mconn *mconn = NULL;
    int64_t incarnation = 0;

    if (!zrdt_tlv) {
        ZPN_LOG(AL_ERROR, "NULL zrdt_tlv for stream %p, id %ld", stream, (long)stream_id);
        return ZPN_RESULT_ERR;
    }

    mconn_zrdt_tlv = zpn_mconn_zrdt_tlv_lookup_stream_id(zrdt_tlv, stream_id, &incarnation);
    if (!mconn_zrdt_tlv) {
        ZPN_DEBUG_MCONN("NULL mconn zrdt_tlv for stream %p, id %ld", stream, (long)stream_id);
        return ZPN_RESULT_ERR;
    }

    if (!mconn_zrdt_tlv->stream) {
        ZPN_LOG(AL_ERROR, "zrdt tlv has no stream, %p, id %ld", stream, (long)stream_id);
        return ZPN_RESULT_ERR;
    }

    if (mconn_zrdt_tlv->stream != stream) {
        ZPN_LOG(AL_ERROR, "stream mismtach between callback and zconn zrdt tlv, %p, %p, %ld",
                          mconn_zrdt_tlv->stream, stream, (long)stream_id);
        return ZPN_RESULT_ERR;
    }

    mconn = &(mconn_zrdt_tlv->mconn);

    if (mconn->global_owner) {
        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_mconn_get_transmit_buffer_len(&(mconn_zrdt_tlv->mconn))) {
        zpn_mconn_zrdt_tlv_stream_transmit(zrdt_tlv,
                                           mconn_zrdt_tlv,
                                           zpn_mconn_get_transmit_buffer(mconn),
                                           zpn_mconn_get_transmit_buffer_len(&(mconn_zrdt_tlv->mconn)));
    } else if (mconn->client_needs_to_forward && zrdt_all_data_sent(mconn_zrdt_tlv->stream)) {
        /* We don't have anything to send, but have pending mtunnel_end */
        ZPN_DEBUG_MCONN("%p: Finished transmit data, send out pending mtunnel end message", mconn);
        zpn_mconn_forward_mtunnel_end(mconn, MT_CLOSED_TERMINATED, 0);
    } else {
        /* We don't have anything to send, just try to resume peer in case it is paused */
        if (mconn->peer) {
            zpn_mconn_resume_client(mconn->peer, 1);
        }
    }

    if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }
    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_zrdt_tlv_add_monitor_timer(struct zrdt_conn *z_conn,
                                          struct zpn_zrdt_tlv *zrdt_tlv,
                                          event_callback_fn monitor_timer_cb,
                                          int64_t secs,
                                          int64_t usecs)
{
    int thread_num = zrdt_tlv->fohh_thread_id;
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    if (zrdt_tlv->monitor_ev) {
        event_free(zrdt_tlv->monitor_ev);
        zrdt_tlv->monitor_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    zrdt_tlv->monitor_ev = event_new(base,
                                     -1,
                                     EV_PERSIST,
                                     monitor_timer_cb,
                                     zrdt_tlv);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(zrdt_tlv->monitor_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate monitor timer");
        event_free(zrdt_tlv->monitor_ev);
        zrdt_tlv->monitor_ev = NULL;
    }
}

void zpn_mconn_zrdt_tlv_remove_monitor_timer(struct zpn_zrdt_tlv *zrdt_tlv)
{
    if (zrdt_tlv->monitor_ev) {
        event_free(zrdt_tlv->monitor_ev);
        zrdt_tlv->monitor_ev = NULL;
    }
}

void zpn_mconn_zrdt_tlv_stream_touch(struct zpn_mconn_zrdt_tlv *mconn_zrdt_tlv)
{
    if (mconn_zrdt_tlv) {
        if (mconn_zrdt_tlv->stream) {
            zrdt_stream_touch(mconn_zrdt_tlv->stream);
        }
    }
}
