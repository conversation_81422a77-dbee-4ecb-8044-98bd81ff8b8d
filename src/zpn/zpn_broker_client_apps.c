/*
 * zpn_broker_client_apps.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include "zpath_lib/zpath_debug.h"
#include "diamond/diamond.h"

#include "zevent/zevent.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_broker_client_apps_build.h"
#include "zpn/zpn_broker_client_apps_merge.h"
#include "zpn/zpn_broker_client_apps_db.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_policy_engine_build.h"
#include "zpn/zpn_policy_engine.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpn/zpn_debug.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpn/zpn_jit_approval_policy.h"
#include "zpn/zpn_scope_engine.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_rule.h"
#include "zpath_lib/zpath_match_style.h"
#include "zpn/zpn_broker_client_private.h"
#include "zpn/zpn_policy_overrides.h"
#include "zpn/zpn_broker_ipars.h"

#define ZPN_BROKER_CLIENT_APPS_STATS_TIMER_US            (int64_t)(1ll * 60ll * 1000ll * 1000ll)

/*
 * Creating a new thread
 * to offload work for any misc app processes
 */
#define APP_MISC_THREAD       "apps_misc_thread"
#define APP_MISC_THREAD_POOL  "apps_misc_thread_pool"

struct zpn_broker_client_app_sent_stats {                    /* _ARGO: object_definition */
    /*
     * The enqueued and dequeued count of the sent applications for each new client during app download.
     * The difference is the count of applications left in the queue to be sent,
     * for successful the app download , the two counts should be the same (no objects left in the queue).
     */
    int64_t client_app_sent_cnt_in;                          /* _ARGO: integer */
    int64_t client_app_sent_cnt_out;                         /* _ARGO: integer */
};

/* This is client specific stats
 * however if we want platform specific
 * stats as well then that is also added
 * domain_sent_skip_cnt : For client specific
 * domain_sent_skip_win_cnt : ZCC flavor Windows
 * domain_sent_skip_mac_cnt : ZCC flavor MAC
 * domain_sent_skip_android_cnt : ZCC flavor Android
 */
struct zpn_broker_client_apps_app_scale_stats {         /* _ARGO: object_definition */
    int64_t domain_sent_skip_cnt;                       /* _ARGO: integer */
    int64_t domain_sent_skip_win_cnt;                   /* _ARGO: integer */
    int64_t domain_sent_skip_mac_cnt;                   /* _ARGO: integer */
    int64_t domain_sent_skip_android_cnt;               /* _ARGO: integer */
};

struct zpn_broker_client_app_re_download_stats {       /* _ARGO: object_definition */
    uint64_t total_app_re_download_time_us;             /* _ARGO: integer */
    uint64_t total_app_re_downloads;                    /* _ARGO: integer */
    uint64_t max_app_re_download_time_us;               /* _ARGO: integer */
    uint64_t min_app_re_download_time_us;               /* _ARGO: integer */
    uint64_t avg_app_re_download_time_us;               /* _ARGO: integer */
    uint64_t client_disconnect_app_download_timeout;    /* _ARGO: integer */
};

// Restrict App download config flag declaration
static struct zpath_config_override_desc zpn_broker_rest_app_down_descriptions[] = {
    {
        .key                = RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
        .desc               = "Enable or Disable restrict application download for iOS and android",
        .details            = "Not Set: Restrict app download for embedded device feature is disabled\n"
                              "0: Restrict app download for embedded device feature is disabled\n"
                              "1: Restrict app download for embedded device feature is enabled for customer (if set by customer gid) or global (if set for All)\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. Restrict app download for embedded device feature is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE_VAL_MIN,
        .int_range_hi       = RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE_VAL_MAX,
        .int_default        = DEFAULT_RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
        .feature_group      = FEATURE_GROUP_RESTRICT_APPLICATION_DOWNLOAD,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_IOS,
        .desc               = "Set the maximum number of applications downloadable for iOS devices when client connects",
        .details            = "If feature config.feature.restrict_app_download_embedded_devices is enabled\n"
                              "then for iOS devices this is the maximum allowed for application downloads\n"
                              "Order of check: customer gid, global\n"
                              "default: 5000",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = REST_APP_DOWNLOAD_EMBEDDED_VAL_MIN,
        .int_range_hi       = REST_APP_DOWNLOAD_EMBEDDED_VAL_MAX,
        .int_default        = DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE,
        .feature_group      = FEATURE_GROUP_RESTRICT_APPLICATION_DOWNLOAD,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_ANDROID,
        .desc               = "Set the maximum number of applications downloadable for Android devices when client connects",
        .details            = "If feature config.feature.restrict_app_download_embedded_devices is enabled\n"
                              "then for Android devices this is the maximum allowed for application downloads\n"
                              "Order of check: customer gid, global\n"
                              "default: 5000",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = REST_APP_DOWNLOAD_EMBEDDED_VAL_MIN,
        .int_range_hi       = REST_APP_DOWNLOAD_EMBEDDED_VAL_MAX,
        .int_default        = DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE,
        .feature_group      = FEATURE_GROUP_RESTRICT_APPLICATION_DOWNLOAD,
        .value_traits       = config_value_traits_normal
    }
};

/* this data goes to the stats.log */
static struct zpn_broker_client_app_sent_stats zpn_broker_app_sent_stats[FOHH_MAX_THREADS] = {{0}};
static struct zpn_broker_client_app_re_download_stats zpn_broker_app_re_download_stats[BROKER_APP_CALC_THREADS_DEFAULT] = {{0}};
static struct zpn_broker_client_apps_app_scale_stats zpn_broker_appscale_stats[zpn_client_type_total_count][BROKER_APP_CALC_THREADS_DEFAULT] = {{{0}}};

static void new_client_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie);
static void free_client_apps(void *element, void *cookie);
static void zpn_broker_client_apps_update_domain_sent_skip_stats(struct zpn_client_app_state *client);
static int skip_app_seg(struct zpn_application *old, struct zpn_client_app *new, struct zpn_client_app_state *client);

void zpn_app_multi_match_for_app_scaling(int64_t customer_gid);
void zpn_app_multi_match_for_app_download(int64_t customer_gid);

static int policy_ready = 1;
static int policy_not_ready = 0;

struct zpath_allocator zpn_broker_client_allocator = ZPATH_ALLOCATOR_INIT("zpn_broker_client");
struct zpath_allocator zpn_broker_client_app_cache_allocator = ZPATH_ALLOCATOR_INIT("zpn_broker_client_app_cache");

/* dummy data for app download keep alive */
static struct zpn_client_app *dummy_app = NULL;

static int zpn_broker_policy_re_eval_on_posture_change_is_disabled(void);

/* Appscaling change callbacks */
static void zpn_client_app_scaling_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);
static void zpn_client_app_scaling_android_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);
static void zpn_app_scaling_cc_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);
static void zpn_app_scaling_vdi_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);
#ifdef SIPA_APP_SCALE_CODE_ENABLED
void zpn_app_scaling_feature_flag_sipa_changed_cb(const int64_t *config_value, int64_t customer_gid);
#endif
static int zpn_broker_get_app_scaling_status_for_customer(int64_t customer_gid,
                                                          enum zpn_client_type client_type,
                                                          enum zpn_platform_type platform_type,
                                                          int broker_type);

/* App scaling hard disable flags */
struct app_scale_hard_disable_feature_flag_clients g_appscale_hard_disable_clients[zpn_client_type_total_count];
/*
 * For the policy test,
 * enable app scaling feature for 50k test cases
 */
int zpn_policy_app_scaling_flag_test = 0;
int zpn_policy_app_download_rest_test = 0;
void zpn_policy_enable_app_scaling_feature_test() { zpn_policy_app_scaling_flag_test = 1; }
void zpn_policy_enable_app_download_restriction() { zpn_policy_app_download_rest_test = 1; }

/*
 * do not set this to non-zero for for production
 * it is used for et-25587 unit testing only
 */
int et_25587_test = 0;
int et_25587_app_check = 0;
int64_t g_policy_fqdn_to_srv_ip_hard_disabled_feature_status = 0;
int64_t g_policy_workload_tag_grp_hard_disabled = CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT;
int64_t g_app_scaling_bypass_improvement_hard_disabled_feature_status = 0;
int64_t g_extranet_hard_disabled = CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT;
int64_t g_aae_profile_hard_disabled = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_DEFAULT;
int64_t g_policy_re_eval_on_scim_update_hard_disabled = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_DEFAULT;
int64_t g_policy_re_eval_on_posture_change_hard_disabled = CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_DEFAULT;
int64_t zevent_use_libevent_priority = 1;

void et_25587_test_init() { et_25587_test = 1; }
void et_25587_check_block()
{
    et_25587_app_check = 1;
    while (et_25587_app_check) {
        struct event_base *ev_base = zevent_event_base(zevent_self());
        event_base_loop(ev_base, EVLOOP_NONBLOCK);

        usleep(1);
    }
}
void et_25587_check_unblock()
{
    et_25587_app_check = 0;
}

/* State to indicate DR mode in effect */
static int g_dr_mode_is_active = 0;

/*
 * Enable to test application download when DR mode is active
 */
void zpn_activate_dr_mode() { g_dr_mode_is_active = 1; }
/*
 * if g_app_download_keep_alive is off, ZCC disconnects after a few second if no app downloaded.
 * otherwise, it will continue connected
 */
int64_t g_app_download_keep_alive = 0;
int64_t g_app_zia_inspection_idle_timeout_s = ZIA_INSPECTION_IDLE_TIMEOUT_S_DEFAULT;
/*
* ZIA inspection disable ( 1 disable  , 0 - (default)
*/
int64_t g_app_zia_inspection_disable = ZIA_INSPECTION_DISABLE_DEFAULT;

/*
 * Structure filled with domains for clients to process...
 */
struct client_domains {
    int reference_count;
    char **domains;
    size_t domains_count;  // Count of entries allocated.
    size_t domains_used;   // Count of entries used.
};

/*
 * Structure filled with app_gids for clients to process...
 */
struct client_app_gids {
    int reference_count;
    int64_t *app_gids;
    size_t app_gids_count;
    size_t app_gids_used;   // Count of entries used.
};


static int64_t client_update_push_us = DEFAULT_CLIENT_UPDATE_PUSH_US;
static int64_t single_tenant_customer_gid = 0;

/*
 * Accumulated state for a single app, in the context of a single
 * client. This is pretty ephemeral
 */
struct app_accumulate {                           /* _ARGO: object_definition */
    struct zpn_client_app_state *client;
    struct zpe_policy_built *bypass_policy_built;
    struct zpe_policy_built *access_policy_built;
    struct zpe_policy_built *bypass_policy_built_default;
    struct zpe_policy_built *access_policy_built_default;
    char domain_name[MAX_APPLICATION_DOMAIN_LEN];


    /* if deleted, do not send object */
    unsigned deleted_no_send:1;

    /* is policy allow to do accumulate or not */
    unsigned do_accumulate:1;

    /* In DR mode, if this is set and do_accumulate is 0 skip that domain */
    unsigned skip_non_dr_app_in_dr_mode:1;

    /* Pattern based domain */
    unsigned is_pattern_domain: 1;

    int udp_ports_array[MAX_PORTS_ARRAY];         /* _ARGO: integer */
    int tcp_ports_array[MAX_PORTS_ARRAY];         /* _ARGO: integer */

    /* These are NOT pair counts- these are array element counts */
    int udp_port_count;                           /* _ARGO: integer, quiet, count: udp_ports_array */
    int tcp_port_count;                           /* _ARGO: integer, quiet, count: tcp_ports_array */

    unsigned double_encrypt;                      /* _ARGO: integer */
    unsigned bypass;                              /* _ARGO: integer */
    unsigned bypass_reauth;                       /* _ARGO: integer */

    /* The following copies the enabled flag from the last app_gid evaluated. */
    unsigned enabled;                             /* _ARGO: integer */

    unsigned ip_anchored;                         /* _ARGO: integer */
    unsigned inspected;                           /* _ARGO: integer */
    unsigned bypass_on_reauth;                    /* _ARGO: integer */

    char bypass_type[BYPASS_TYPE_LEN];            /* _ARGO: string */
    char icmp_access_type[ICMP_ACCESS_TYPE_LEN];  /* _ARGO: string */

    char matched_domain[MAX_APPLICATION_DOMAIN_LEN];      /* _ARGO: string */

    /* app scaling for Forwarding Clients */
    struct argo_object **app_segments;            /* _ARGO: argo_object, quiet */
    size_t app_segments_count;                    /* _ARGO: integer, quiet, count: app_segments*/
};

struct timmer_parameter {
    struct customer_state *customer;
    int64_t scope_gid;
};

#include "zpn/zpn_broker_client_apps_compiled_c.h"

static struct argo_structure_description *app_accumulate_description;
static struct argo_structure_description* zpn_broker_client_app_sent_stats_description;
static struct argo_structure_description *zpn_broker_client_app_re_download_stats_description;
static struct argo_structure_description* zpn_broker_client_apps_app_scale_stats_description;

/***********************************************************************
 *
 * Customer management
 *
 * Please follow the following rules for using reference_counter strictly:
 *
 * (1) Do not call callbacks synchronously (separate synchronous and asynchronous calls)
 * (2) set reference_counter to 1 right after the object is created
 * (3) call object_hold() for each asynchronous callback with the object passed
 * (4) call object_hold() if the object is stored somewhere (i.e to list or table)
 * (5) call object_release if it's removed from list, table, etc.
 * (6) call object_release inside each callback with object passed
 * (7) do not call object_release in non-callback function, unless object is created there and is not needed any more
 */



static struct zhash_table *customers;
static zpath_mutex_t customers_lock;
static int64_t recalc_push_us = 2 * DEFAULT_RECALC_PUSH_US;

static struct zhash_table *generate_all_domains_table(struct customer_state *customer,
                                                      int no_domain_download,
                                                      int no_ip_download,
                                                      int client_appscale_flag);
static void add_hash_to_hash(struct zhash_table *target_hash, struct zhash_table *source_hash);
static int32_t client_release(struct zpn_client_app_state *client);
static int32_t client_hold(struct zpn_client_app_state *client);
static int32_t domains_hold(struct client_domains  *domains);
static int32_t app_gids_hold(struct client_app_gids *app_gids);
void customer_scope_update(int64_t customer_gid, int changed);


static inline int zpn_broker_is_libevent_priority_enabled()
{
    return zevent_use_libevent_priority;
}

#define CLIENT_ADD_BIG_ZEVENT(z_base, callback, void_cookie, int_cookie, cookie1, cookie2, cookie3, int_cookie1) \
    do { \
        if (zpn_broker_is_libevent_priority_enabled()) { \
            zevent_add_big_event((z_base), (callback), (void_cookie), (int_cookie), (cookie1), (cookie2), (cookie3), (int_cookie1)); \
        } else { \
            zevent_base_big_call((z_base), (callback), (void_cookie), (int_cookie), (cookie1), (cookie2), (cookie3), (int_cookie1)); \
        } \
    } while (0)

#define CLIENT_ADD_ZEVENT_WITH_PRIORITY(z_base, callback, void_cookie, int_cookie, priority) \
    do { \
        if (zpn_broker_is_libevent_priority_enabled()) { \
            zevent_add_event_with_priority((z_base), (callback), (void_cookie), (int_cookie), (priority)); \
        } else { \
            zevent_base_call_with_priority((z_base), (callback), (void_cookie), (int_cookie), (priority)); \
        } \
    } while (0)

// Helper macros to select the correct implementation based on the number of arguments
#define SELECT_IMPL(_1, _2, _3, _4, _5, NAME, ...) NAME

// Main macro that determines the number of arguments and calls the appropriate implementation
#define CLIENT_ADD_ZEVENT(...) SELECT_IMPL(__VA_ARGS__, CLIENT_ADD_ZEVENT_5, CLIENT_ADD_ZEVENT_4)(__VA_ARGS__)

#define CLIENT_ADD_ZEVENT_5(res, z_base, callback, void_cookie, int_cookie) \
    do { \
        if (zpn_broker_is_libevent_priority_enabled()) { \
          (res) = zevent_add_event(z_base, (callback), (void_cookie), (int_cookie)); \
        } else { \
           (res) = zevent_base_call(z_base, (callback), (void_cookie), (int_cookie)); \
        } \
    } while (0)

#define CLIENT_ADD_ZEVENT_4(z_base, callback, void_cookie, int_cookie) \
    do { \
        if (zpn_broker_is_libevent_priority_enabled()) { \
            zevent_add_event(z_base, (callback), (void_cookie), (int_cookie)); \
        } else { \
            zevent_base_call(z_base, (callback), (void_cookie), (int_cookie)); \
        } \
    } while (0)

static inline enum zpn_bypass_type zpn_broker_client_app_bypass_type_string_to_enum(const char *bypass_type, int64_t customer_gid)
{
    enum zpn_bypass_type type = NEVER;

    if (!bypass_type) {
        ZPN_LOG(AL_CRITICAL, "Customer : %"PRId64" zpn_broker_client_app_bypass_type_string_to_enum bypass_type is NULL, setting to NEVER", customer_gid);
        return type;
    }

    if (!strcasecmp(bypass_type, "NEVER")) {
        type = NEVER;
    } else if (!strcasecmp(bypass_type, "OFF_NET")) {
        type = OFF_NET;
    } else if (!strcasecmp(bypass_type, "ON_NET")) {
        type = ON_NET;
    } else if (!strcasecmp(bypass_type, "ALWAYS")) {
        type = ALWAYS;
    } else {
        ZPN_LOG(AL_ERROR, "Customer : %"PRId64" zpn_broker_client_app_bypass_type_string_to_enum unknown bypass_type: %s, returning NEVER", customer_gid, bypass_type);
    }
    return type;
}

static inline const char *zpn_broker_client_app_bypass_type_enum_to_string(enum zpn_bypass_type type, int64_t customer_gid)
{
    switch(type) {
    case NEVER:
        return "NEVER";
    case OFF_NET:
        return "OFF_NET";
    case ON_NET:
        return "ON_NET";
    case ALWAYS:
        return "ALWAYS";
    default:
        ZPN_LOG(AL_ERROR, "Customer : %"PRId64" zpn_broker_client_app_bypass_type_enum_to_string unknown bypass_type, returning NEVER", customer_gid);
        return "NEVER";
    }
}

static inline enum zpn_icmp_access_type zpn_broker_client_app_icmp_access_type_string_to_enum(const char *icmp_access_type, int64_t customer_gid)
{
    enum zpn_icmp_access_type type = NONE;

    if (!icmp_access_type) {
        ZPN_LOG(AL_CRITICAL, "Customer : %"PRId64" zpn_broker_client_icmp_access_type_string_to_enum icmp_access_type is NULL, setting to NONE", customer_gid);
        return type;
    }

    if (!strcasecmp(icmp_access_type, "NONE")) {
        type = NONE;
    } else if (!strcasecmp(icmp_access_type, "PING")) {
        type = PING;
    } else if (!strcasecmp(icmp_access_type, "PING_TRACEROUTING")) {
        type = PING_TRACEROUTING;
    } else {
        ZPN_LOG(AL_ERROR, "Customer : %"PRId64" zpn_broker_client_app_icmp_access_type_string_to_enum unknown icmp_access_type: %s, returning NONE", customer_gid, icmp_access_type);
    }
    return type;
}

static inline char *zpn_broker_client_app_icmp_access_type_enum_to_string(enum zpn_icmp_access_type type, int64_t customer_gid)
{
    switch(type) {
    case NONE:
        return "NONE";
    case PING:
        return "PING";
    case PING_TRACEROUTING:
        return "PING_TRACEROUTING";
    default:
        ZPN_LOG(AL_ERROR, "Customer : %"PRId64" zpn_broker_client_app_icmp_access_type_enum_to_string unknown icmp_access_type, returning NONE", customer_gid);
        return "NONE";
    }
}

/*
 * Get the application object based on application gid
 * Skip the application based on connection type
 */
static inline struct zpn_application *zpn_broker_client_apps_get_apps_by_gid(int collect_segments, int64_t app_gid, struct zpn_client_app_state *client, int *do_continue, int64_t customer_gid)
{
    struct zpn_application *app = NULL;
    int res = ZPN_RESULT_NO_ERROR;
    if (collect_segments) {
        res = zpn_broker_client_apps_get_app_by_gid(app_gid, &app);
        if (res || !app) {
            ZPN_LOG(AL_ERROR, "Error %s: Could not fetch app %" PRId64 " customer %"PRId64, zpath_result_string(res), app_gid, customer_gid);
            if (do_continue) {
                *do_continue = 1;
            }
        }

        // check if should not send this app
        if (client && skip_app_seg(app, NULL, client) && do_continue) {
            *do_continue = 1;
        }
    }
    return app;
}

static int zpn_broker_client_app_sent_stats_fill(void *cookie, int counter, void *structure_data)
{

    struct zpn_broker_client_app_sent_stats *stats = structure_data;
    int res;
    int max_fohh_thread = fohh_thread_count();
    res = argo_structure_array_add(zpn_broker_client_app_sent_stats_description, &zpn_broker_app_sent_stats[0],
                                   max_fohh_thread, stats, sizeof(struct zpn_broker_client_app_sent_stats));

    if (res) {
        ZPN_LOG(AL_ERROR, "unable to add zpn broker client sent stats");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_client_app_re_download_stats_fill(void *cookie __attribute__((unused)),
                                                        int counter __attribute__((unused)),
                                                        void *structure_data)
{

    struct zpn_broker_client_app_re_download_stats *stats = structure_data;
    int i = 0;

    stats->max_app_re_download_time_us = 0;
    stats->min_app_re_download_time_us = UINT64_MAX;

    for (i=0; i<BROKER_APP_CALC_THREADS_DEFAULT; i++) {
        stats->total_app_re_download_time_us += zpn_broker_app_re_download_stats[i].total_app_re_download_time_us;
        stats->total_app_re_downloads += zpn_broker_app_re_download_stats[i].total_app_re_downloads;
        stats->client_disconnect_app_download_timeout += zpn_broker_app_re_download_stats[i].client_disconnect_app_download_timeout;

        if (stats->max_app_re_download_time_us < zpn_broker_app_re_download_stats[i].max_app_re_download_time_us) {
            stats->max_app_re_download_time_us = zpn_broker_app_re_download_stats[i].max_app_re_download_time_us;
        }

        if (stats->min_app_re_download_time_us > zpn_broker_app_re_download_stats[i].min_app_re_download_time_us) {
            stats->min_app_re_download_time_us = zpn_broker_app_re_download_stats[i].min_app_re_download_time_us;
        }
    }

    if (stats->min_app_re_download_time_us == UINT64_MAX) {
        stats->min_app_re_download_time_us = 0;
    }

    if (stats->total_app_re_downloads) {
        stats->avg_app_re_download_time_us = (stats->total_app_re_download_time_us/stats->total_app_re_downloads);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_client_app_re_download_stats_dump(struct zpath_debug_state*  request_state,
                                               const char** query_values,
                                               int query_value_count __attribute__((unused)),
                                               void* cookie __attribute__((unused)))
{
    struct zpn_broker_client_app_re_download_stats stats = {0};

    zpn_broker_client_app_re_download_stats_fill(NULL, 0, &stats);
    ZDP("total_app_re_download_time_us          : %" PRId64 "\n"
        "total_app_re_downloads                 : %" PRId64 "\n"
        "max_app_re_download_time_us            : %" PRId64 "\n"
        "min_app_re_download_time_us            : %" PRId64 "\n"
        "avg_app_re_download_time_us            : %" PRId64 "\n"
        "client_disconnect_app_download_timeout : %" PRId64 "\n",
        stats.total_app_re_download_time_us,
        stats.total_app_re_downloads,
        stats.max_app_re_download_time_us,
        stats.min_app_re_download_time_us,
        stats.avg_app_re_download_time_us,
        stats.client_disconnect_app_download_timeout);

    return ZPN_RESULT_NO_ERROR;
}

/* Feature to restrict the app download for ios and android */
static int64_t zpn_broker_is_restrict_app_download_embedded_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = DEFAULT_RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE;

    if (zpn_policy_app_download_rest_test) return 1;
    if (is_unit_test() || ZPN_IS_SITEC()) return config_value;

    config_value = zpath_config_override_get_config_int(RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
                                                        &config_value,
                                                        DEFAULT_RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("Restrict app download for embedded devices config value for customer_id %"PRId64" is %"PRId64,
                  customer_gid,
                  config_value);

    return config_value?1:0;
}

/* Feature to restrict the app download for a customer */
int64_t zpn_broker_get_restrict_app_download_embedded_status_for_customer(int64_t customer_gid)
{
    struct customer_state *customer;

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (customer) {
        return customer->restrict_app_down_embedded_feature_status;
    }

    return zpn_broker_is_restrict_app_download_embedded_enabled_for_customer(customer_gid);
}

/* This func is for broker,
 * Get User Risk feature for customer
 */
static int64_t zpn_broker_is_user_risk_enabled_for_broker_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    if (!is_unit_test() && ZPN_BROKER_IS_PUBLIC()) {
        config_value = zpath_config_override_get_config_int(BROKER_CUSTOMER_USER_RISK_FEATURE_ENABLED,
                                                            &config_value,
                                                            DEFAULT_BROKER_CUSTOMER_USER_RISK_FEATURE,
                                                            customer_gid,
                                                            (int64_t)0);

        ZPN_DEBUG_USER_RISK("User Risk feature config value for customer_id %"PRId64" is %"PRId64,
                            customer_gid,
                            config_value);
    }

    return config_value;
}

/* This function is for broker,
 * Feature to get user risk for a customer
 */
int64_t zpn_broker_get_user_risk_status_for_broker_customer(int64_t customer_gid)
{
    struct customer_state *customer;

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (customer) {
        if (customer->cust_user_risk_status_in_broker != DEFAULT_BROKER_CUSTOMER_USER_RISK_FEATURE) {
            return customer->cust_user_risk_status_in_broker;
        }
    }

    return zpn_broker_is_user_risk_enabled_for_broker_customer(customer_gid);
}

/*
 * Is AAE is hard disabled
 */
int64_t zpn_broker_is_aae_profile_hard_disabled()
{
    return g_aae_profile_hard_disabled;
}

/* Disable the feature
 * DR mode, hard disabled, in unit tests
 */
int zpn_broker_aae_profile_is_disabled(void)
{
    if (is_unit_test() ||
        (ZPN_BROKER_IS_PRIVATE() &&
        g_dr_mode_is_active) ||
        zpn_broker_is_aae_profile_hard_disabled()) {
        return 1;
    }
    return 0;
}

int zpn_broker_get_aae_profile_status(int64_t customer_gid)
{
    int64_t config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_AAE_PROFILE_FEATURE,
                           &config_value,
                           CONFIG_FEATURE_AAE_PROFILE_FEATURE_DEFAULT,
                           customer_gid,
                           (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                           (int64_t)0);

    return config_value?1:0;
}

int zpn_broker_get_aae_profile_status_for_customer(int64_t customer_gid)
{
    if (zpn_broker_aae_profile_is_disabled()) {
        return 0;
    }

    struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (customer) {
        return ((customer->aae_profile_feature_status) ? 1 : 0);
    }

    return zpn_broker_get_aae_profile_status(customer_gid);
}

/*
 * Is Policy Re Eval on SCIM Update is hard disabled
 */
int64_t zpn_broker_is_policy_re_eval_on_scim_update_hard_disabled()
{
    return g_policy_re_eval_on_scim_update_hard_disabled;
}

/* Is Policy Reeval on SCIM update feature disabled
 * Return 1 if DR mode, hard disabled
 * Return !default value if unit tests
 */
static inline int zpn_broker_policy_re_eval_on_scim_update_is_disabled(void)
{
    if (is_unit_test()) {
        return !CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT;
    }

    if ((ZPN_BROKER_IS_PRIVATE() &&
        g_dr_mode_is_active) ||
        zpn_broker_is_policy_re_eval_on_scim_update_hard_disabled()) {
        return 1;
    }
    return 0;
}

static inline int zpn_broker_get_policy_re_eval_on_scim_update_status(int64_t customer_gid)
{
    // For unit tests, return default value
    if (is_unit_test()) {
        return CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT;
    }

    int64_t config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE,
                           &config_value,
                           CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT,
                           customer_gid,
                           (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                           (int64_t)0);

    return config_value?1:0;
}

int zpn_broker_is_policy_re_eval_on_scim_update_enabled(int64_t customer_gid)
{
    if (zpn_broker_policy_re_eval_on_scim_update_is_disabled()) {
        return 0;
    }

    struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (customer) {
        return ((customer->policy_re_eval_on_scim_update_feature_status) ? 1 : 0);
    }

    return zpn_broker_get_policy_re_eval_on_scim_update_status(customer_gid);
}

int64_t zpn_broker_is_extranet_hard_disabled()
{
    int64_t hard_disable_status = CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT;

    if (is_unit_test()) {
        return 0;
    }

    /* Disable the feature in DR mode */
    if (ZPN_BROKER_IS_PRIVATE() && g_dr_mode_is_active) {
        ZPN_DEBUG_EAS("For PSE, DR mode is ON, extranet feature is not allowed in DR mode");
        return 1;
    }

    hard_disable_status = zpath_config_override_get_config_int(CONFIG_FEATURE_EXTRANET_HARD_DISABLED,
                                                               &hard_disable_status,
                                                               CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT,
                                                               (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                               (int64_t)0);

    ZPN_DEBUG_EAS("Hard disable extranet value is %"PRId64, hard_disable_status);

    return (hard_disable_status)?1:0;
}

int64_t zpn_broker_is_extranet_enabled(int64_t customer_gid, int *pse_in_dr_mode)
{
    int64_t extranet_status;

    if (g_extranet_hard_disabled) {
        ZPN_DEBUG_EAS("Extranet is hard disabled, customer_gid: %"PRId64"", customer_gid);
        return 0;
    }

    /* Disable the feature in DR mode */
    if (ZPN_BROKER_IS_PRIVATE() && g_dr_mode_is_active) {
        if (pse_in_dr_mode) {
            *pse_in_dr_mode = 1;
        }
        ZPN_DEBUG_EAS("For PSE, DR mode is ON, extranet feature is not allowed in DR mode");
        return 0;
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        extranet_status = zpath_config_override_get_config_int(CONFIG_FEATURE_EXTRANET_BROKER_ENABLED,
                                                              &extranet_status,
                                                              CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_DEFAULT,
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);
    } else if (ZPN_BROKER_IS_PRIVATE()) {
        extranet_status = zpath_config_override_get_config_int(CONFIG_FEATURE_EXTRANET_PSE_ENABLED,
                                                              &extranet_status,
                                                              CONFIG_FEATURE_EXTRANET_PSE_ENABLED_DEFAULT,
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);
    } else {
        extranet_status = 0;
    }

    ZPN_DEBUG_EAS("Extranet feature is %s, customer_gid: %"PRId64"", (extranet_status) ? "enabled":"disabled", customer_gid);

    return (extranet_status)?1:0;
}

int64_t zpn_broker_is_workload_tag_grp_hard_disabled()
{
    int64_t hard_disable_status = CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT;

    if (is_unit_test()) {
        return 0;
    }

    /* Disable the feature in DR mode */
    if (ZPN_BROKER_IS_PRIVATE() && g_dr_mode_is_active) {
        ZPN_DEBUG_WORKLOAD_TAG_GRP("For PSE, DR mode is ON, Hard disable the workload tag grp feature");
        return 1;
    }
    if (ZPN_IS_SITEC()) {
        return CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT;
    }

    hard_disable_status = zpath_config_override_get_config_int(CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED,
                                                               &hard_disable_status,
                                                               CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT,
                                                               (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                               (int64_t)0);

    ZPN_DEBUG_WORKLOAD_TAG_GRP("Hard disable Workload tag grp value is %"PRId64, hard_disable_status);

    return (hard_disable_status)?1:0;
}

static int64_t zpn_broker_is_workload_tag_grp_enabled_for_cust(int64_t customer_gid)
{
    int64_t config_value = CONFIG_FEATURE_WORKLOAD_TAG_GRP_DEFAULT;

    if (is_unit_test() || ZPN_IS_SITEC()) return config_value;

    if (g_dr_mode_is_active && ZPN_BROKER_IS_PRIVATE()) {
        ZPN_DEBUG_WORKLOAD_TAG_GRP("For PSE, DR mode is ON, disabling Workload tag grp feature for customer gid %"PRId64,
        customer_gid);
        return 0;
    }

    config_value = zpath_config_override_get_config_int(CONFIG_WORKLOAD_TAG_GRP_FEATURE,
                   &config_value,
                   CONFIG_FEATURE_WORKLOAD_TAG_GRP_DEFAULT,
                   customer_gid,
                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                   (int64_t)0);
    ZPN_DEBUG_WORKLOAD_TAG_GRP("Workload tag grp feature value %"PRId64" for customer gid %"PRId64,
            config_value,
            customer_gid);

    return config_value?1:0;
}


int64_t zpn_broker_get_workload_tag_grp_status(int64_t customer_gid)
{
    return zpn_broker_get_workload_tag_grp_status_detailed(customer_gid, NULL, NULL);
}

int64_t zpn_broker_get_workload_tag_grp_status_detailed(int64_t customer_gid, int64_t *hard_disable_ret, int64_t *customer_config_ret)
{
    int64_t hard_disable = g_policy_workload_tag_grp_hard_disabled;
    int64_t customer_config = CONFIG_FEATURE_WORKLOAD_TAG_GRP_DEFAULT;

    struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (customer) {
        customer_config = customer->workload_tag_grp_cust_status;
    } else {
        customer_config = zpn_broker_is_workload_tag_grp_enabled_for_cust(customer_gid);
    }

    if (hard_disable_ret) {
        *hard_disable_ret = hard_disable;
    }
    if (customer_config_ret) {
        *customer_config_ret = customer_config;
    }

    if (hard_disable) {
        ZPN_DEBUG_WORKLOAD_TAG_GRP("Hard disabling workload tag grp feature");
        return 0;
    }

    ZPN_DEBUG_WORKLOAD_TAG_GRP("Workload tag grp feature value %"PRId64" for customer gid %"PRId64,
                                customer_config, customer_gid);

    return customer_config?1:0;
}

/*
 * Call this function only for embedded devices for example: android and ios
 */
static int64_t zpn_broker_get_max_app_download_val_for_embedded(int64_t customer_gid, enum zpn_platform_type platform_type)
{
    if (zpn_policy_app_download_rest_test) return DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_POLICY_TEST;

    const char *feature_flag;
    int64_t max_app_download = DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE;

    if (is_unit_test() || ZPN_IS_SITEC()) {
        return max_app_download;
    }

    if (platform_type != zpn_platform_type_ios && platform_type != zpn_platform_type_android) {
        ZPN_LOG(AL_ERROR, "Invalid platform value - ios and android allowed");
        return max_app_download;
    }

    if (platform_type == zpn_platform_type_ios) {
        feature_flag = ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_IOS;
    } else {
        feature_flag = ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_ANDROID;
    }

    /* coverity[overrun-buffer-val : FALSE] */
    max_app_download = zpath_config_override_get_config_int(feature_flag,
                                                            &max_app_download,
                                                            DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);

    ZPN_DEBUG_COR("Max app download for %s embedded devices for customer_id %"PRId64" is %"PRId64,
                  (platform_type == zpn_platform_type_ios) ? ZPN_PLATFORM_IOS : ZPN_PLATFORM_ANDROID,
                  customer_gid,
                  max_app_download);

    return max_app_download;
}

/* Check policy re-evalution on posture change config override for given customer. By default, it's disabled. */
static int zpn_broker_is_policy_re_eval_on_posture_change_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    if (is_unit_test()) return config_value;

    config_value = zpath_config_override_get_config_int(POLICY_RE_EVAL_ON_POSTURE_CHANGE,
                                                        &config_value,
                                                        DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("Policy re-eval on posture change feature config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

void zpn_broker_set_c2c_ip_feature_incarnation(struct zpn_broker_client_fohh_state *c_state)
{
    if(!c_state) {
        return;
    }
    struct customer_state *customer;
    customer = zhash_table_lookup(customers, &c_state->customer_gid, sizeof(c_state->customer_gid), NULL);
    if (customer) {
        c_state->c2c_ip.feature_status_incarnation = customer->c2c_ip_feature_status_incarnation;
    }
    return;
}

static int zpn_broker_get_c2c_ip_feature_status(int64_t customer_gid)
{
    int64_t config_value = C2C_IP_DEFAULT_CUSTOMER_FEATURE;

    if (is_unit_test()) return config_value;

    if (ZPN_BROKER_IS_PRIVATE() && g_dr_mode_is_active) {
        return C2C_IP_CUSTOMER_FEATURE_DISABLED;
    }

    config_value = zpath_config_override_get_config_int(C2C_IP_FEATURE_CUSTOMER,
                                                        &config_value,
                                                        C2C_IP_DEFAULT_CUSTOMER_FEATURE,
                                                        customer_gid,
                                                        (int64_t)0);

    ZPN_DEBUG_IPARS("c2c ip feature config value for customer_id %"PRId64" is %"PRId64" ",
                    customer_gid, config_value);

    return config_value;
}

enum zpn_broker_c2c_ip_feature_flag_action zpn_broker_c2c_ip_feature_check(struct zpn_broker_client_fohh_state *c_state,
                                                                           int *release_all_flag)
{
    if(!c_state) {
        return zpn_broker_c2c_ip_client_no_action;
    }

    struct customer_state *customer;
    customer = zhash_table_lookup(customers, &(c_state->customer_gid), sizeof(c_state->customer_gid), NULL);
    if (!customer) {
        // customer not found in cache check the gloabal values
        return zpn_broker_c2c_ip_client_check_global;
    }
    // Remove_later: We are not locking the customer as standard followed by other feature
    if (customer->c2c_ip_feature_status == C2C_IP_CUSTOMER_FEATURE_NOT_IN_DB) {
        // Feature flag is not set per customer, check global config
        return zpn_broker_c2c_ip_client_check_global;
    }

    if (customer->c2c_ip_feature_status_release_all == 1) {
        // Set the release_all flag, so very first client will send a release all by customer_gid
        *release_all_flag = 1;
        customer->c2c_ip_feature_status_release_all = 0;
    }

    if (c_state->c2c_ip.reserved_ip.length) {
        if (c_state->c2c_ip.feature_status_incarnation != customer->c2c_ip_feature_status_incarnation) {
            if (customer->c2c_ip_feature_status == 0) {
                // Cleanup if status is disabled
                return zpn_broker_c2c_ip_client_cleanup;
            } else {
                // Send renewal request if state is enable
                return zpn_broker_c2c_ip_client_renew;
            }
        }
    } else {
        // Feature is enabled but no IP is assigned yet
        if (customer->c2c_ip_feature_status) {
            return zpn_broker_c2c_ip_client_renew;
        }
    }
    return zpn_broker_c2c_ip_client_no_action;
}

/* Check svcp re evaluation frequency.*/
static int zpn_broker_get_svcp_re_eval_frequency_sec(int64_t customer_gid)
{
    int64_t config_value = DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC;

    if (is_unit_test()) return config_value;

    config_value = zpath_config_override_get_config_int(POLICY_SVCP_RE_EVAL_FREQUENCY_SEC,
                                                        &config_value,
                                                        DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR(POLICY_SVCP_RE_EVAL_FREQUENCY_SEC " for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value;
}

/* Checks svcp enable config override for given customer. By default, it's disabled. */
static int zpn_broker_is_policy_svcp_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = DEFAULT_POLICY_SVCP_ENABLE;

    if (is_unit_test()) return config_value;

    config_value = zpath_config_override_get_config_int(POLICY_SVCP_ENABLE,
                                                        &config_value,
                                                        DEFAULT_POLICY_SVCP_ENABLE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR(" SVCP feature enable config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

/* Check DNS TXT query support config override for given customer. By default, it's enabled. */
static int zpn_broker_is_dns_txt_query_support_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE;

    if (is_unit_test()) return config_value;

    config_value = zpath_config_override_get_config_int(DNS_TXT_QUERY_SUPPORT_FEATURE,
                                                        &config_value,
                                                        DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("DNS TXT query support feature config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

/* Checks FQDN_TO_SRV_IP enable config override for given customer. By default, it's disabled. */
static int zpn_broker_is_policy_fqdn_to_srv_ip_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE;

    if (is_unit_test()) return config_value;

    config_value = zpath_config_override_get_config_int(POLICY_FQDN_TO_SERV_IP_ENABLE,
                                                        &config_value,
                                                        DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("FQDN_TO_SRV_IP feature enable config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

/* Check step up auth support feature status for given customer. By default, it's disabled. */
static int zpn_broker_is_step_up_auth_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = ZPN_BROKER_STEP_UP_AUTH_FEATURE_DEFAULT;

    if (is_unit_test()) {
        return config_value;
    }

    config_value = zpath_config_override_get_config_int(ZPN_BROKER_STEP_UP_AUTH_FEATURE_ENABLED,
                                                        &config_value,
                                                        ZPN_BROKER_STEP_UP_AUTH_FEATURE_DEFAULT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("Step Up Auth support feature config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

static void increase_queue_size(struct zpn_client_app_state *client, int is_high_priority)
{
    ZPATH_MUTEX_LOCK(&(client->customer->stats_lock), __FILE__, __LINE__);
    if (is_high_priority) {
        client->customer->app_download_high_priority_queue_size++;
        client->stats.app_download_high_priority_queue_size_in = client->customer->app_download_high_priority_queue_size;
    } else {
        client->customer->app_download_low_priority_queue_size++;
        client->stats.app_download_low_priority_queue_size_in = client->customer->app_download_low_priority_queue_size;
    }
    ZPATH_MUTEX_UNLOCK(&(client->customer->stats_lock), __FILE__, __LINE__);
}

static void decrease_queue_size(struct zpn_client_app_state *client, int is_high_priority)
{
    ZPATH_MUTEX_LOCK(&(client->customer->stats_lock), __FILE__, __LINE__);
    if (is_high_priority) {
        client->stats.app_download_high_priority_queue_size_out = client->customer->app_download_high_priority_queue_size;
        client->customer->app_download_high_priority_queue_size--;
    } else {
        client->stats.app_download_low_priority_queue_size_out = client->customer->app_download_low_priority_queue_size;
        client->customer->app_download_low_priority_queue_size--;
    }
    ZPATH_MUTEX_UNLOCK(&(client->customer->stats_lock), __FILE__, __LINE__);
}

static void zbca_increment_app_scaling_cnt(struct zpn_client_app_state *client)
{
    if (zpn_broker_get_app_scaling_status_for_customer(client->customer_gid, client->client_type, client->platform_type, ZPN_BROKER_IS_PUBLIC() ? 1 : 0)) {
        if (client->no_domain_download) {
            client->customer->current_app_scaling_no_domain_download_clients++;
            client->customer->total_app_scaling_no_domain_download_clients++;
        }
        if (client->no_ip_download) {
            client->customer->current_app_scaling_no_ip_download_clients++;
            client->customer->total_app_scaling_no_ip_download_clients++;
        }
        if (client->no_domain_download_v2) {
            client->customer->current_app_scaling_no_domain_download_v2_clients++;
            client->customer->total_app_scaling_no_domain_download_v2_clients++;
        }
    }
}

static void zbca_decrement_app_scaling_cnt(struct zpn_client_app_state *client)
{
    if (zpn_broker_get_app_scaling_status_for_customer(client->customer_gid, client->client_type, client->platform_type, ZPN_BROKER_IS_PUBLIC() ? 1 : 0)) {
        if (client->no_domain_download) {
            client->customer->current_app_scaling_no_domain_download_clients--;
        }
        if (client->no_ip_download) {
            client->customer->current_app_scaling_no_ip_download_clients--;
        }
        if (client->no_domain_download_v2) {
            client->customer->current_app_scaling_no_domain_download_v2_clients--;
        }
    }
}

/* If android or ios and restrict app
 * download enabled then increment count
 */
static void zbca_increment_restrict_app_download_cnt(struct zpn_client_app_state *client)
{
    if ((zpn_platform_type_android == client->platform_type ||
         zpn_platform_type_ios == client->platform_type) &&
         client->customer->restrict_app_down_embedded_feature_status) {
        client->customer->current_restrict_app_download_clients++;
        client->customer->total_restrict_app_download_clients++;
    }
}

static void zbca_decrement_restrict_app_download_cnt(struct zpn_client_app_state *client)
{
    if ((zpn_platform_type_android == client->platform_type ||
         zpn_platform_type_ios == client->platform_type) &&
         client->customer->restrict_app_down_embedded_feature_status) {
        client->customer->current_restrict_app_download_clients--;
    }
}

/* Get app scaling status for a customer
 * If customer object exists then return value
 * from their else look in Wally
 */
int zpn_is_app_scaling_enabled(int64_t customer_gid)
{
    int res = 0;
    if (zpn_policy_app_scaling_flag_test) {
        res = 1;
    } else if (!is_unit_test() && !ZPN_IS_SITEC()) {
        struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (customer) {
            res = zpn_get_app_scaling_status(customer->customer_gid) ? 1 : 0;
        } else {
            res = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_zapp, zpn_platform_type_windows, 1);
        }
    }

    return res;
}

/*
 * Get appscaling hard disable feature
 * @client_type(i) : Client type
 * @platform_type(i) : Platform type
 * @broker (i) : Broker or PSE
 */
int zpn_get_app_scaling_hard_disabled_status(enum zpn_client_type client_type,
                                             enum zpn_platform_type platform_type,
                                             int broker_type)
{
    (void) platform_type;
    int config_value = 0;
    if (client_type == zpn_client_type_edge_connector) {
        if (broker_type) {
            config_value = (g_appscale_hard_disable_clients[zpn_client_type_edge_connector].broker_hard_disabled) ? 1 : 0;
        } else {
            config_value = (g_appscale_hard_disable_clients[zpn_client_type_edge_connector].pse_hard_disabled) ? 1 : 0;
        }
    } else if (client_type == zpn_client_type_vdi) {
        if (broker_type) {
            config_value = (g_appscale_hard_disable_clients[zpn_client_type_vdi].broker_hard_disabled) ? 1 : 0;
        } else {
            config_value = (g_appscale_hard_disable_clients[zpn_client_type_vdi].pse_hard_disabled) ? 1 : 0;
        }
    }
    return config_value;
}

/*
 * Get the value from config override for appscaling
 * @customer_gid (i) : Customer gid
 * @client_type (i) : Type of Client
 * @platform_type (i) : Platform type for ZCC clients
 * @broker (i) : Is it broker or PSE feature flag
 */
int zpn_broker_is_app_scaling_enabled_for_customer(int64_t customer_gid,
                                                   enum zpn_client_type client_type,
                                                   enum zpn_platform_type platform_type,
                                                   int broker_type)
{
    int64_t config_value = 0;
    int64_t default_value = 0;
    char feature_flag[64] = {0};

    if (zpn_policy_app_scaling_flag_test)
        return 1;

    if (is_unit_test() || ZPN_IS_SITEC() || zpn_get_app_scaling_hard_disabled_status(client_type, platform_type, broker_type)) return 0;

    if (zpn_lib_is_app_scale_zcc_clients(client_type)) {
        if (zpn_lib_is_app_scale_legacy_zcc(platform_type)) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", APP_SCALING_FEATURE);
            default_value = DEFAULT_APP_SCALING_FEATURE;
        } else if (platform_type == zpn_platform_type_android) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", ANDROID_APP_SCALING_FEATURE);
            default_value = DEFAULT_ANDROID_APP_SCALING_FEATURE;
        }
    } else if (client_type == zpn_client_type_edge_connector) {
        if (broker_type) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_CC_APP_SCALING_FEATURE);
            default_value = DEFAULT_BROKER_CC_APP_SCALING_FEATURE;
        } else {
            snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_CC_APP_SCALING_FEATURE);
            default_value = DEFAULT_PSE_CC_APP_SCALING_FEATURE;
        }
    } else if (client_type == zpn_client_type_vdi) {
        if (broker_type) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_VDI_APP_SCALING_FEATURE);
            default_value = DEFAULT_BROKER_VDI_APP_SCALING_FEATURE;
        } else {
            snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_VDI_APP_SCALING_FEATURE);
            default_value = DEFAULT_PSE_VDI_APP_SCALING_FEATURE;
        }
#ifdef SIPA_APP_SCALE_CODE_ENABLED
    } else if (client_type == zpn_client_type_ip_anchoring) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", SIPA_APP_SCALING_FEATURE);
        default_value = DEFAULT_SIPA_APP_SCALING_FEATURE;
#endif
    } else {
        ZPN_LOG(AL_NOTICE, "Appscaling feature invalid values: client %s, "
                "platform %s, broker %s, customer %"PRId64" returning 0",
                zpn_client_type_string(client_type),
                ZPN_PLATFORM_TYPE_STR(platform_type),
                (broker_type) ? "Public Broker" : "PSE",
                customer_gid);
        return 0;
    }

    config_value = zpath_config_override_get_config_int(feature_flag,
                                                        &config_value,
                                                        default_value,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("%s config value for customer_id %ld is %ld",
                  feature_flag,
                  (long)customer_gid,
                  (long)config_value);
    return config_value?1:0;
}

/*
 * Get the appscaling feature if customer object exists
 * else get from config override
 * @customer_gid (i) : Customer gid
 * @client_type (i) : Type of Client
 * @platform_type (i) : Platform type for ZCC clients
 * @broker (i) : Is it broker or PSE feature flag
 */
static int zpn_broker_get_app_scaling_status_for_customer(int64_t customer_gid,
                                                          enum zpn_client_type client_type,
                                                          enum zpn_platform_type platform_type,
                                                          int broker_type)
{
    int res = 0;

    if (zpn_policy_app_scaling_flag_test) {
        res = 1;
    } else if (!is_unit_test() && !ZPN_IS_SITEC() && !zpn_get_app_scaling_hard_disabled_status(client_type, platform_type, broker_type)) {
        struct customer_state *one_customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (one_customer) {
            if (zpn_lib_is_app_scale_zcc_clients(client_type)) {
                if (zpn_lib_is_app_scale_legacy_zcc(platform_type)) {
                    res = zpn_get_app_scaling_status(customer_gid) ? 1 : 0;
                } else if (platform_type == zpn_platform_type_android) {
                    res = (one_customer->android_app_scaling_feature_status) ? 1 : 0;
                }
            } else if (client_type == zpn_client_type_edge_connector) {
                if (broker_type) {
                    res = (one_customer->cc_broker_app_scaling_feature_status) ? 1 : 0;
                } else {
                    res = (one_customer->cc_pse_app_scaling_feature_status) ? 1 : 0;
                }
            } else if (client_type == zpn_client_type_vdi) {
                if (broker_type) {
                    res = (one_customer->vdi_broker_app_scaling_feature_status) ? 1 : 0;
                } else {
                    res = (one_customer->vdi_pse_app_scaling_feature_status) ? 1 : 0;
                }
#ifdef SIPA_APP_SCALE_CODE_ENABLED
            } else if (client_type == zpn_client_type_ip_anchoring) {
                res = (one_customer->sipa_app_scaling_feature_status) ? 1 : 0;
#endif
            }
        } else {
            res = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid,
                                                                 client_type,
                                                                 platform_type,
                                                                 broker_type);
        }
    }
    ZPN_DEBUG_COR("Client type: %s, Platform: %s, Customer gid: %"PRId64" for %s "
                  "appscaling config value is=%d",
                  zpn_client_type_string(client_type),
                  ZPN_PLATFORM_TYPE_STR(platform_type),
                  customer_gid,
                  ((broker_type) ? "Public Broker" : "PSE"),
                  res);
    return res;
}

int zpn_broker_get_app_scaling_flag_for_specific_client(struct zpn_client_app_state *client, struct zpn_broker_client_fohh_state *c_state)
{
    int config_value = 0;

    if (!client && !c_state) return config_value;

    int platform_type = (c_state) ? (c_state->platform_type) : (client->platform_type);
    int client_type = (c_state) ? (c_state->client_type) : (client->client_type);
    int64_t customer_gid = (c_state) ? (c_state->customer_gid) : (client->customer_gid);
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;

    config_value = zpn_is_app_scaling_enabled(customer_gid);
    if (config_value) {
        if (zpn_client_static_config[client_type].appscale && zpn_lib_is_app_scale_legacy_zcc(platform_type)) {
            /* If legacy then return the current value */
            return 1;
        } else if (zpn_client_static_config[client_type].appscale_v2) {
            return zpn_broker_get_app_scaling_status_for_customer(customer_gid,
                                                                  client_type,
                                                                  platform_type,
                                                                  broker_type);
        }
    } else {
        ZPN_DEBUG_CLIENT("app scaling for customer %" PRId64 " is not enabled: %s", customer_gid, APP_SCALING_FEATURE);
    }

    return 0;
}

/*
 * Add all the customer gids to the hash table, avoiding duplicates.
 */
static void add_customer_gids_to_hash(struct zhash_table *hash, struct customer_state *customer)
{
    /* Generate all app_ids direct from wally. */
    struct zpn_application *apps[100000];
    size_t apps_count = sizeof(apps) / sizeof(apps[0]);
    int res;
    res = zpn_broker_client_apps_get_apps_by_customer_gid(customer->customer_gid,
                                                          &(apps[0]),
                                                          &apps_count);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "Error fetching apps for customer %ld: %s", (long) customer->customer_gid, zpn_result_string(res));
        }
    } else {
        size_t i;
        for (i = 0; i < apps_count; i++) {
            zhash_table_store_not_exist(hash, &(apps[i]->gid), sizeof(apps[i]->gid), 0, hash);
        }
    }
}


static struct client_app_gids *generate_all_gids_table(struct customer_state *customer)
{
    /* Generate all app_ids direct from wally. */
    struct zpn_application *apps[100000];
    size_t apps_count = sizeof(apps) / sizeof(apps[0]);
    int res;
    struct client_app_gids *app_gids = NULL;
    res = zpn_broker_client_apps_get_apps_by_customer_gid(customer->customer_gid,
                                                          &(apps[0]),
                                                          &apps_count);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "Error fetching apps for customer %ld: %s", (long) customer->customer_gid, zpn_result_string(res));
        }
    } else {
        size_t i;
        app_gids = ZPN_BCA_CALLOC(sizeof(*app_gids));
        app_gids->app_gids = ZPN_BCA_MALLOC(sizeof(*(app_gids->app_gids)) * apps_count);
        app_gids->reference_count = 1;
        for (i = 0; i < apps_count; i++) {
            app_gids->app_gids[i] = apps[i]->gid;
        }
        app_gids->app_gids_count = apps_count;
        app_gids->app_gids_used = apps_count;
    }
    return app_gids;
}


static int domain_hash_walk(void *cookie,
                            void *object,
                            void *key,
                            size_t key_len)
{
    struct client_domains *domains = cookie;

    if (domains->domains_used >= domains->domains_count) {
        /* This might occur if domains are being added quickly, while
         * computing access. However, such changes should result in
         * rebuilding the info in the first place, so the error will
         * be temporary.  */
        ZPN_LOG(AL_ERROR, "Too many domains %ld vs %ld", (long)domains->domains_used, (long)domains->domains_count);
        return ZPATH_RESULT_ERR;
    }
    domains->domains[domains->domains_used] = ZPN_BCA_MALLOC(key_len + 1);
    snprintf(domains->domains[domains->domains_used], key_len + 1, "%.*s", (int)key_len, (char *)key);
    ZPN_DEBUG_MTN("  Domain_from_hash entry %d = %s", (int)domains->domains_used, domains->domains[domains->domains_used]);
    domains->domains_used++;
    return ZPATH_RESULT_NO_ERROR;
}

static int app_gid_hash_walk(void *cookie,
                             void *object,
                             void *key,
                             size_t key_len)
{
    struct client_app_gids *app_gids = cookie;

    if (key_len != sizeof(int64_t)) {
        ZPN_LOG(AL_ERROR, "Invalid size");
        return ZPATH_RESULT_ERR;
    }
    if (app_gids->app_gids_used >= app_gids->app_gids_count) {
        /* This might occur if app_gids are being added quickly, while
         * computing access. However, such changes should result in
         * rebuilding the info in the first place, so the error will
         * be temporary.  */
        ZPN_LOG(AL_ERROR, "Too many app_gids %ld vs %ld", (long)app_gids->app_gids_used, (long)app_gids->app_gids_count);
        return ZPATH_RESULT_ERR;
    }
    app_gids->app_gids[app_gids->app_gids_used] = *((int64_t *)key);
    app_gids->app_gids_used++;
    return ZPATH_RESULT_NO_ERROR;
}

static int32_t domains_hold(struct client_domains *domains)
{
    int32_t count = __sync_add_and_fetch_4(&(domains->reference_count), 1);
    if (count == 1) {
        ZPN_LOG(AL_CRITICAL, "Incremented domains reference count to 1");
    }

    return count;
}

static void domains_release(struct client_domains *domains)
{
    int ref = __sync_sub_and_fetch_4(&(domains->reference_count), 1);
    if (ref == 0) {
        while (domains->domains_used) {
            domains->domains_used--;
            ZPN_BCA_FREE(domains->domains[domains->domains_used]);
        }
        if (domains->domains) {
            ZPN_BCA_FREE(domains->domains);
        }
        ZPN_BCA_FREE(domains);
    }
}

static int32_t app_gids_hold(struct client_app_gids *app_gids)
{
    if (!app_gids) {
        ZPN_LOG(AL_CRITICAL, "Attempted to hold a null app_gid");
        return 0;
    }

    int32_t count = __sync_add_and_fetch_4(&(app_gids->reference_count), 1);
    if (count == 1) {
        ZPN_LOG(AL_CRITICAL, "Incremented app_gids reference count to 1");
    }
    return count;
}


static void app_gids_release(struct client_app_gids *app_gids)
{
    if (!app_gids) {
        return;
    }

    int ref = __sync_sub_and_fetch_4(&(app_gids->reference_count), 1);
    if (ref == 0) {
        if (app_gids->app_gids) {
            ZPN_BCA_FREE(app_gids->app_gids);
        }
        ZPN_BCA_FREE(app_gids);
    }
}

static int is_app_deleted(struct app_accumulate *accumulate)
{
    int deleted = 0;
    if (accumulate->enabled == 0) {
        deleted = 1;
    } else {
        if (!accumulate->udp_port_count && !accumulate->tcp_port_count) {
            /* No ports configured. This is deleted unless it is a special bypass type... */
            if ((strcasecmp(accumulate->bypass_type, "ON_NET") == 0) ||
                (strcasecmp(accumulate->bypass_type, "OFF_NET") == 0) ||
                (strcasecmp(accumulate->bypass_type, "ALWAYS") == 0)) {
                /* always download */
            } else if (accumulate->do_accumulate &&
                      ((strcasecmp(accumulate->icmp_access_type, "PING") == 0) ||
                       (strcasecmp(accumulate->icmp_access_type, "PING_TRACEROUTING") == 0))) {
                /* only download ICMP apps if do_accumulate */
            } else {
                deleted = 1;
            }
        }
    }

    return deleted;
}

/*
 * This routine takes the app GID specified, tests policy, and if the
 * policy is to intercept, adds the ports represented by this
 * application to this port set
 *
 * Evaluate bypass policy, which results in one of the following:
 *
 *    A. Miss: This is equivalent to 'intercept'
 *
 *    B. Hit: Intercept: Applications' ports are added to the set.
 *
 *    C. Hit: Bypass: Applications' ports are not added to the
 *            set. Mark the accumulation as 'bypassed' (This is used
 *            later- if the application is exclusively bypassed, then
 *            the application is still sent to the client- but to be
 *            bypassed.
 *
 *    D. Hit: No Download: Applications' ports are not added to the
 *            set. Do NOT mark the accumulation as 'bypassed'
 *
 *    E. Hit: Bypass on Reauth: Mark the application as 'bypass on
 *            reauth', but include the ports in the set.
 *
 *    F. Hit: Access Policy: Process access policy for this
 *            application, and:
 *
 *            i. If allow: Add the ports to the set.
 *
 *            ii. If deny: Do not add the ports to the set.
 *
 */
static int accumulate_ports_walk(void *cookie,
                                 void *object,
                                 void *key,
                                 size_t key_len,
                                 int *overlapping_ports_present,
                                 int *accumulated)
{
    struct app_accumulate *accumulate = cookie;
    struct zpn_client_app_state *client = accumulate->client;
    struct customer_state *customer = client->customer;
    struct zpn_application *app;
    struct zhash_table *int64_hash = NULL;
    int64_t app_gid = *((int64_t *)key);
    int res;
    int rules_evaluated = 0;
    int64_t matched_rule = 0;
    int64_t matched_rule2 = 0;
    enum zpe_access_action action;
    int do_accumulate = 1;
    if (accumulated) *accumulated = 0;
    struct zpn_rule *db_rule1 = NULL;
    struct zpn_rule *db_rule2 = NULL;
    size_t db_rule_count1 = 1;
    size_t db_rule_count2 = 1;
    char *rule_name1 = NULL;
    char *rule_name2 = NULL;

    /* if the caller already pass the object in, just use it. Otherwise we get it here */
    if (object) {
        app = object;
    } else {
        app = NULL;
        res = zpn_broker_client_apps_get_app_by_gid(app_gid,
                                                    &app);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not fetch app %ld", (long) app_gid);
            /* We don't return error so that we keep walking the
             * table. Better to do that than other options. */
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* Skip non DR mode Apps when in DR mode */
    if (g_dr_mode_is_active) {
        if (!app->use_in_dr_mode) {
            ZPN_LOG(AL_INFO, "DR mode: Skipped customer: %ld, domain: %s, app_gid: %ld is not enabled for DR",
                    (long)customer->customer_gid, app->domain_name, (long)app->gid);
            accumulate->skip_non_dr_app_in_dr_mode = 1;
            return ZPATH_RESULT_NO_ERROR;
        } else {
            ZPN_LOG(AL_DEBUG, "DR mode: Added customer: %ld, domain: %s, app_gid: %ld is enabled for DR",
                    (long)customer->customer_gid, app->domain_name, (long)app->gid);
            accumulate->skip_non_dr_app_in_dr_mode = 0;
        }
    }

    /* No Download for SIEM, always bypass it */
    if (app->config_space && strcmp(app->config_space, "SIEM") == 0) {
        ZPN_DEBUG_CLIENT("%s: App GID = %ld is configured as SIEM", client->debug_str, (long) app_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* If the app do not owned by or shared to the scope, do not download it */
    if(!g_dr_mode_is_active && !is_app_owned_or_shared(app, &(client->scope_gid), client->attr, client->machine_gid, 0)) {
        ZPN_DEBUG_CLIENT("%s: App GID = %ld is not owned by or shared to scope %ld, app_scope %ld, app_domain %s, app name %s",
                          client->debug_str, (long)app_gid, (long)client->scope_gid, (long)app->scope_gid, app->domain_name, app->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    int is_default_app = app->scope_gid == client->customer_gid;

    if (app->bypass_on_reauth) {
        accumulate->bypass_on_reauth = app->bypass_on_reauth;
    }

    /* Return whether or not this app is enabled... This isn't used
     * when truly accumulating, but it is used when querying 1-by-one
     * (segment mode) */
    if(app->enabled) {
        accumulate->enabled = app->enabled;
    } else {
        ZPN_DEBUG_CLIENT("%s: Application %ld is disabled", client->debug_str, (long) app_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (accumulate->deleted_no_send && !accumulate->enabled) {
        ZPN_LOG(AL_INFO, "customer %ld app %ld is deleted",
                          (long)accumulate->client->customer->customer_gid, (long)app_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (app->icmp_access_type) {
        if (strcmp(app->icmp_access_type, accumulate->icmp_access_type)) {
           if (strcmp(accumulate->icmp_access_type, "PING") && strcmp(accumulate->icmp_access_type, "PING_TRACEROUTING")) {
               snprintf(accumulate->icmp_access_type, sizeof(accumulate->icmp_access_type), "%s", app->icmp_access_type);
           }
        }
    } else {
        if (strcmp(accumulate->icmp_access_type, "PING") && strcmp(accumulate->icmp_access_type, "PING_TRACEROUTING")) {
            snprintf(accumulate->icmp_access_type, sizeof(accumulate->icmp_access_type), "%s", "NONE");
        }
    }
    ZPN_DEBUG_CLIENT("%s: App GID = %ld using ICMP Access Type %s", client->debug_str, (long) app_gid, app->icmp_access_type);

    /* Short circuit static app definitions with bypass type other than 'never' */
    if (app->bypass_type) {
        if (strcasecmp(app->bypass_type, "NEVER")) {
            /* Any bypass type other than NEVER overrides download policy directly */
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", app->bypass_type);
            ZPN_DEBUG_CLIENT("%s: App GID = %ld using override %s", client->debug_str, (long) app_gid, app->bypass_type);
            if (strcasecmp(app->bypass_type, "ALWAYS") == 0) {
                /* Don't accumulate ports if always bypassing... */
            } else {
                do_accumulate = 1;
                goto accumulate;
            }

            ZPN_APPLICATION_SET_INSPECTION_FLAGS(app->ip_anchored,
                                                 app->inspect_traffic_with_zia,
                                                 accumulate->ip_anchored,
                                                 accumulate->inspected);

            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* Okay, our key is the GID of an application. We use this gid to
     * look up the gid set (app gid + app_grp+gid) for the application in the customer
     * space. This gid set is a hash table of all the application
     * groups linked to the application, which can be used directly in
     * policy. */
    struct zhash_table *app_appgrp_gid_set = zhash_table_lookup(customer->app_gid_hashes, &app_gid, sizeof(app_gid), NULL);
    if (!app_appgrp_gid_set) {
        /* app_appgrp_gid_set being empty indicates that app_gid_hashes is
         * incomplete. We will get called in the future with new
         * data. For now we behave as though this app does not
         * exist. Note that the short circuit above is performed
         * regardless. (Backwards compatibility) */
        ZPN_DEBUG_CLIENT("%s: App GID = %ld skipping because it is not in hash", client->debug_str, (long) app_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    int64_hash = zhash_table_alloc(&zpn_allocator);
    if (client->client_aux_id > 0) {
        zhash_table_store(int64_hash, &(client->client_aux_id), sizeof(client->client_aux_id), 0, int64_hash);
    }

    add_hash_to_hash(int64_hash, app_appgrp_gid_set);

    if (zpn_client_static_config[client->client_type].location_policy) {
        if (client->loc_id) {
            zhash_table_store(int64_hash, &(client->loc_id), sizeof(client->loc_id), 0, int64_hash);
        }

        if (client->sub_loc_id) {
            zhash_table_store(int64_hash, &(client->sub_loc_id), sizeof(client->sub_loc_id), 0, int64_hash);
        }
    }

    /* We now have client general_context_hash and app+app_group gid hash, an
     * policy. We can evaluate. Evaluation can be 2-stage, which is
     * fine. */
    res = zpe_evaluate_scope(&app_gid,
                             1,
                             accumulate->bypass_policy_built_default,
                             accumulate->bypass_policy_built,
                             client->string_hash,
                             client->saml_string_hash,
                             client->scim_string_hash,
                             int64_hash,    /* int64_t data, e.g app gid, app group gis, machine group_gid ... */
                             client->debug_str,
                             &rules_evaluated,
                             &matched_rule,
                             &action,
                             NULL,
                             NULL,
                             NULL,
                             NULL,
                             is_default_app);
    client->stats.client_policy_rules_count += rules_evaluated;
    client->stats.client_policy_evaluate_calls++;
    switch(res) {
    case ZPATH_RESULT_NO_ERROR:
        if (zpn_debug_get(ZPN_DEBUG_MTN_IDX)) {
            zpn_rule_get_by_gid(matched_rule, &db_rule1, &db_rule_count1);
            rule_name1 = (db_rule1 != NULL) ? db_rule1->name : NULL;
        }
        switch (action) {
        case zpe_access_action_bypass:
            ZPN_DEBUG_MTN("%s: App GID = %ld App Name = %s evaluated as bypass, matched rule = %ld rule name = %s", client->debug_str, (long) app_gid, app->name, (long) matched_rule, rule_name1);
            do_accumulate = 0;
            accumulate->bypass = 1;
            client->stats.client_bypass_app_count++;
            break;
        default:
        case zpe_access_action_intercept:
            ZPN_DEBUG_MTN("%s: App GID = %ld App Name = %s evaluated as intercept, matched rule = %ld rule name = %s", client->debug_str, (long) app_gid, app->name, (long) matched_rule, rule_name1);
            break;
        case zpe_access_action_nodownload:
            ZPN_DEBUG_MTN("%s: App GID = %ld App Name = %s evaluated as nodownload, matched rule = %ld rule name = %s", client->debug_str, (long) app_gid, app->name, (long) matched_rule, rule_name1);
            do_accumulate = 0;
            break;
        case zpe_access_action_bypass_on_reauth:
            ZPN_DEBUG_MTN("%s: App GID = %ld App Name = %s evaluated as bypass_on_reauth, matched rule = %ld rule name = %s", client->debug_str, (long) app_gid, app->name, (long) matched_rule, rule_name1);
            accumulate->bypass_reauth = 1;
            break;
        case zpe_access_action_intercept_if_accessible:

            res = zpe_evaluate_scope(&app_gid,
                                     1,
                                     accumulate->access_policy_built_default,
                                     accumulate->access_policy_built,
                                     client->string_hash,
                                     client->saml_string_hash,
                                     client->scim_string_hash,
                                     int64_hash,    /* int64_t data, e.g app gids, app group gids, machine group_gids ... */
                                     client->debug_str,
                                     &rules_evaluated,
                                     &matched_rule2,
                                     &action,
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     is_default_app);
            client->stats.client_policy_rules_count += rules_evaluated;
            client->stats.client_policy_evaluate_calls++;
            if (res == ZPATH_RESULT_NO_ERROR) {
                if (action == zpe_access_action_approval_required) {
                    enum zpe_access_approval_status approval_status = zpe_access_approval_none;
                    int64_t approval_id = 0;
                    action = zpe_access_action_deny;
                    /* Access policy for this app is configured to 'Require Approval'
                     * Check approval table for active approvals
                     * If no approval is found, action would be zpe_access_action_deny */
                    if (is_pra_disabled(client->customer->customer_gid) || is_jit_disabled(client->customer->customer_gid)) {
                        res = ZPN_RESULT_NO_ERROR;
                    } else {
                        res = zpn_policy_check_approval(client->customer->customer_gid,
                                                    client->scope_gid,
                                                    client->saml_string_hash,
                                                    app_gid,
                                                    NULL, NULL, 0,
                                                    &approval_id, NULL,
                                                    &approval_status,
                                                    &action);
                        ZPN_DEBUG_MTN("%s: zpn_policy_check_approval for app %"PRId64"  app name = %s with 'require approval' access policy: approval id %"PRId64" status %s result %s",
                                      client->debug_str, app_gid, app->name, approval_id, zpe_access_approval_status_string(approval_status), zpn_result_string(res));
                    }
                }
                if (zpn_debug_get(ZPN_DEBUG_MTN_IDX)) {
                    zpn_rule_get_by_gid(matched_rule2, &db_rule2, &db_rule_count2);
                    rule_name2 = (db_rule2 != NULL) ? db_rule2->name : NULL;
                }
                switch (action) {
                case zpe_access_action_deny:
                    ZPN_DEBUG_MTN("%s: App GID = %ld App name = %s evaluated as access policy, matched rule %ld name = %s, access deny, matched rule %ld name = %s", client->debug_str, (long) app_gid, app->name, (long) matched_rule, rule_name1, (long) matched_rule2, rule_name2);
                    do_accumulate = 0;
                    client->stats.client_policy_deny_app_download_count++;
                    break;
                case zpe_access_action_allow:
                default:
                    ZPN_DEBUG_MTN("%s: App GID = %ld App name = %s evaluated as access policy, matched rule %ld name = %s, access allow, matched rule %ld name = %s", client->debug_str, (long) app_gid, app->name,(long) matched_rule, rule_name1, (long) matched_rule2, rule_name2);
                    break;
                }
            } else if (res == ZPATH_RESULT_NOT_FOUND) {
                ZPN_DEBUG_MTN("%s: App GID = %ld App name = %s evaluated as access policy, matched no rule, default deny", client->debug_str, (long) app_gid, app->name);
                do_accumulate = 0;
            } else {
                ZPN_DEBUG_MTN("%s: Unexpected", client->debug_str);
            }
            break;
        }
        break;
    default:
        /* Any other result is bad. We will still download in these
         * cases */
        ZPN_LOG(AL_ERROR, "Client policy evaluation returned %s", zpath_result_string(res));
        /* Fall through ! */
    case ZPATH_RESULT_NOT_FOUND:
        /* Defaults to action_intercept, which has do_accumulate set */
        ZPN_DEBUG_MTN("%s: App GID = %ld App name = %s evaluated as not found (no match), results in intercept", client->debug_str, (long) app_gid, app->name);
        break;
    }

    if(int64_hash) zhash_table_free(int64_hash);

accumulate:

    if (do_accumulate) {
        if (accumulated) *accumulated = 1;
        accumulate->do_accumulate = do_accumulate;
        ZPN_APPLICATION_SET_INSPECTION_FLAGS(app->ip_anchored,
                                             app->inspect_traffic_with_zia,
                                             accumulate->ip_anchored,
                                             accumulate->inspected);

        if (app->double_encrypt) accumulate->double_encrypt = 1;
        res = zpn_merge_ports(&(accumulate->udp_ports_array[0]), &(accumulate->udp_port_count), MAX_PORTS_ARRAY, app->udp_port_ranges, app->udp_port_ranges_count, overlapping_ports_present);
        if (res) {
            ZPN_LOG(AL_ERROR, "Merge UDP ports failed for app %ld", (long) app_gid);
        }
        res = zpn_merge_ports(&(accumulate->tcp_ports_array[0]), &(accumulate->tcp_port_count), MAX_PORTS_ARRAY, app->tcp_port_ranges, app->tcp_port_ranges_count, overlapping_ports_present);
        if (res) {
            ZPN_LOG(AL_ERROR, "Merge UDP ports failed for app %ld", (long) app_gid);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* Check if the specified arrays of ports are different. Returns 1 if
 * different, 0 if they are the same */
static int ports_diff(int32_t *ports_a, int ports_a_count, int32_t *ports_b, int ports_b_count)
{
    if (ports_a_count != ports_b_count) return 1;
    if (memcmp(ports_a, ports_b, ports_a_count * sizeof(*ports_a)) != 0) {
        return 1;
    }
    return 0;
}

/*
 * Receive one object to transmit for this client.
 */
static void client_app_rx_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie, void *extra_cookie1, void *extra_cookie2, void *extra_cookie3, int64_t extra_int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    struct argo_object *object_to_transmit = extra_cookie1;
    int64_t is_new_client = int_cookie;
    int64_t is_dummy_app = extra_int_cookie;
    int res;

    if (client->app_callback) {
        res = (client->app_callback)(object_to_transmit, client->char_cookie, client->int_cookie);
        if (res) {
            ZPN_LOG(AL_ERROR, "Client app_callback returned %s, %s", zpn_result_string(res), client->debug_str);
        } else {
            if(!is_dummy_app && is_new_client) {
                client->stats.latest_app_sent_us = epoch_us();
                client->stats.client_app_sent_cnt_out++;
                if (!client->is_cstate_done) zpn_broker_app_sent_stats[client->conn_thread_id].client_app_sent_cnt_out++;
                if (client->is_last_app_enqueued) {
                    if (client->stats.client_app_sent_cnt_in == client->stats.client_app_sent_cnt_out) {
                        zpn_client_tracker_end(client->tracker, client_track_app_sent, ZPN_RESULT_NO_ERROR);
                    }
                }
            }
        }
    } else {
        ZPN_LOG(AL_WARNING, "Client app_callback is not set %s", client->debug_str);
    }
    argo_object_release(object_to_transmit);
    client_release(client);
}

/*
 * Tell the client that it has a full set (so far)
 */
static void client_app_complete_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    int res;

    if (client->app_complete_cb_in_queue > client->stats.max_app_complete_cb_in_queue) {
        client->stats.max_app_complete_cb_in_queue = client->app_complete_cb_in_queue;
    }
    client->app_complete_cb_in_queue--;

    ZPN_DEBUG_MTN("%s: Client app complete callback", client->debug_str);

    if (client->complete_callback) {
        res = (client->complete_callback)(client->char_cookie, client->int_cookie);
        if (res) {
            ZPN_LOG(AL_ERROR, "Client complete_callback returned %s, %s", zpn_result_string(res), client->debug_str);
        }
    } else {
        ZPN_LOG(AL_WARNING, "Client complete_callback is not set %s", client->debug_str);
    }

    zpn_client_tracker_end(client->tracker, client_track_app_complete_cb, ZPN_RESULT_NO_ERROR);
    zpn_client_tracker_end(client->tracker, client_track_app, ZPN_RESULT_NO_ERROR);
    client_release(client);
}

/* do we need to send app with this domain to client ? */
static int is_domain_need_to_send(struct zpn_client_app_state *client, char *domain)
{
    int is_ip = is_valid_ip(domain);
    int is_app_scaling_enabled = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    int no_domain_download = (client->no_domain_download || client->no_domain_download_v2);
    int is_ip_skip = (is_ip && client->no_ip_download && is_app_scaling_enabled);
    int is_domain_skip = (!is_ip && no_domain_download && is_app_scaling_enabled);

    if (is_ip_skip || is_domain_skip) {
        if (is_domain_skip) zpn_broker_client_apps_update_domain_sent_skip_stats(client);
        return 0;
    } else {
        return 1;
    }
}

#define CATCH_ALL_IPV4_ADDR "0.0.0.0/0"
#define CATCH_ALL_IPV6_ADDR "::/0"

static inline int is_catch_all_ip(const char *domain) {
    return !strncmp(domain, CATCH_ALL_IPV4_ADDR, sizeof(CATCH_ALL_IPV4_ADDR) - 1) ||
            !strncmp(domain, CATCH_ALL_IPV6_ADDR, sizeof(CATCH_ALL_IPV6_ADDR) - 1);
}

static void dump_app(void *app, char *debug_str, char *str, int is_zpn_application)
{
    if (zpn_debug_get(ZPN_DEBUG_MTN_IDX)) {
        if (app) {
            char dump[10000];
            if (is_zpn_application) {
                argo_structure_dump(zpn_application_description, app, dump, sizeof(dump), NULL, 0);
            } else {
                argo_structure_dump(zpn_client_app_description, app, dump, sizeof(dump), NULL, 0);
            }
            ZPN_DEBUG_MTN("%s: %s %s", debug_str, str, dump);
        }
    }
}

static void dump_app_argo_object(void *app_argo_object, char *debug_str, char *str)
{
    if (zpn_debug_get(ZPN_DEBUG_MTN_IDX)) {
        if (app_argo_object) {
            char dump[10000];

            ZPN_DEBUG_MTN("%s: %s %s", debug_str, str, argo_object_dump_inline(app_argo_object, dump, sizeof(dump), 0));
        }
    }
}

static void dump_accumulate(void *accumulate, char *debug_str, char *str)
{
    if ((zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) && accumulate) {
        char dump[1000];
        argo_structure_dump(app_accumulate_description, accumulate, dump, sizeof(dump), NULL, 0);
        ZPN_DEBUG_CLIENT("%s: %s %s %s", debug_str, str,((struct app_accumulate *)accumulate)->domain_name, dump);
    }
}

/* if the client need to be skipped to send, return 1 */
static int skip_app_seg(struct zpn_application *old, struct zpn_client_app *new, struct zpn_client_app_state *client)
{
    if (client->client_type == zpn_client_type_ip_anchoring) {
        if ( ! ((old && (old->ip_anchored || old->inspect_traffic_with_zia || old->bypass || old->deleted)) || (new && (new->ip_anchored || new->inspected || new->bypass || new->deleted)))) {
            dump_app(new, client->debug_str, " skip new seg for SME: ", 0);
            return 1;
        }
    } else if (client->client_type == zpn_client_type_edge_connector || client->client_type == zpn_client_type_branch_connector ||
                client->client_type == zpn_client_type_vdi) {
        if ( ! ((old && (!old->ip_anchored  || old->deleted)) || (new && (!new->ip_anchored ||  new->deleted)))) {
            dump_app(new, client->debug_str, " skip new seg for ZCC/CC/BC: ", 0);
            return 1;
        }
    }

    return 0;
}

static int skip_app(struct zpn_client_app_cache *old, struct zpn_client_app *new, struct zpn_client_app_state *client)
{
    if (client->client_type == zpn_client_type_ip_anchoring) {
        if ( ! ((old && (old->ip_anchored || old->bypass || old->deleted)) || (new && (new->ip_anchored || new->bypass || new->deleted)))) {
            dump_app(new, client->debug_str, " skip new app for SME: ", 0);
            return 1;
        }
    } else if (client->client_type == zpn_client_type_edge_connector || client->client_type == zpn_client_type_branch_connector ||
                client->client_type == zpn_client_type_vdi) {
        if ( ! ((old && (!old->ip_anchored || old->deleted)) || (new && (!new->ip_anchored ||  new->deleted)))) {
            dump_app(new, client->debug_str, " skip new app for ZCC/CC/BC: ", 0);
            return 1;
        }
    }

    return 0;
}

static int send_app(struct zpn_client_app_state *client,
                    struct zpn_client_app *client_app,
                    int is_new_client,
                    int is_last_app)
{
    struct argo_object *object_to_send;
    int res = 1;
    if (is_domain_need_to_send(client, client_app->app_domain)) {
        object_to_send = argo_object_create(zpn_client_app_description, client_app);
        if (!object_to_send) {
            ZPN_BROKER_ASSERT_SOFT((NULL != object_to_send),
                                   "Could not create send object for app:%lld",
                                   (long)client_app->app_gid);
        } else {
            dump_app(client_app, client->debug_str, " send app seg: ", 0);
            /* It is created with a single reference count... We
             * let the receiver release that reference count */
            client_hold(client);
            if (is_new_client) {
                if (!client->is_first_app_enqueued) {
                    client->is_first_app_enqueued = 1;
                    zpn_client_tracker_start(client->tracker, client_track_app_sent);
                }
                if (!client->is_last_app_enqueued) client->is_last_app_enqueued = is_last_app;
                client->stats.client_app_sent_cnt_in++;
                if (!client->is_cstate_done) zpn_broker_app_sent_stats[client->conn_thread_id].client_app_sent_cnt_in++;
            }

            if (0 == zevent_base_big_call(client->client_fohh_thread, client_app_rx_cb, client,
                                          is_new_client, object_to_send, NULL, NULL, 0)) {
                res = 0;
            }
        }
    }
    return res;
}

/* if any domain of the app is sent, return 0, otherwise 1 */
static int send_app_all(struct zpn_client_app_state *client,
                        struct zpn_client_app *client_app,
                        struct zpn_application *z_app,
                        int is_new_client)
{
    int j;
    int res = 1;
    for (j = 0; j < z_app->domain_names_count; j++) {
        client_app->app_domain = z_app->domain_names[j];
        if (0 == send_app(client, client_app, is_new_client, j == z_app->domain_names_count-1)) res = 0;
    }
    return res;
}

/*
 * Free the application segments
 */
static void zpn_broker_client_apps_clear_app_segment_collection(struct argo_object *app_segments[], int64_t app_segments_count) {
    if (app_segments) {
        for (int i = 0; i < app_segments_count; i++) {
            argo_object_release(app_segments[i]);
        }
    }
}

/*
 * Free the accumulate object
 */
static void zpn_broker_client_apps_reset_accumulate_app_segments(struct app_accumulate *accumulate) {
    if (accumulate->app_segments)  {
        zpn_broker_client_apps_clear_app_segment_collection(accumulate->app_segments, accumulate->app_segments_count);
        ZPN_BCA_FREE(accumulate->app_segments);
    }
    accumulate->app_segments = NULL;
    accumulate->app_segments_count = 0;
}

/*
 * Initialize the Application with gid and TCP/UDP ports
 */
struct argo_object *zpn_broker_client_apps_copy_full_app_data(struct zpn_application *src) {
    if (!src) {
        return NULL;
    }

    struct zpn_app_client_check_full_app app_data = {0};

    app_data.gid = src->gid;

    if (src->udp_port_ranges_count > 0) {
        //give a handle so we can let argo_object copy the data
        app_data.udp_ports_array = src->udp_port_ranges;
        app_data.udp_ports_count = src->udp_port_ranges_count;
    }

    if (src->tcp_port_ranges_count > 0) {
        //give a handle so we can let argo_object copy the data
        app_data.tcp_ports_array = src->tcp_port_ranges;
        app_data.tcp_ports_count = src->tcp_port_ranges_count;
    }

    return argo_object_create(zpn_app_client_check_full_app_description, &app_data);
}

void init_accumulate(struct app_accumulate *accumulate)
{
    accumulate->bypass = 0;
    accumulate->bypass_reauth = 0;
    accumulate->bypass_type[0] = 0;
    accumulate->deleted_no_send = 0;
    accumulate->do_accumulate = 0;
    accumulate->double_encrypt = 0;
    accumulate->enabled = 0;
    accumulate->icmp_access_type[0] = 0;
    accumulate->ip_anchored = 0;
    accumulate->inspected = 0;
    accumulate->bypass_on_reauth = 0;
    accumulate->tcp_port_count = 0;
    accumulate->udp_port_count = 0;
    accumulate->skip_non_dr_app_in_dr_mode = 0;
    accumulate->is_pattern_domain = 0;
    zpn_broker_client_apps_reset_accumulate_app_segments(accumulate);
}

/*
 * Perform single client update when operating in segment update mode
 *
 * This code is split into three pieces (that should probably be made smaller...)
 *
 * For each GID:
 *
 * 1. Figure out how the app is modified by policy. (bypass types,
 *    ports, etc can all end up different)
 *
 * 2. Pull up the the app that we used for sending client_apps last time.
 *
 * 3. Generate a new app that we are using for sending client_apps this time.
 *
 * 4. Send updates based on how the app changed.
 *
 * 5. Remember the status of what we sent.
 *
 */
static void inform_one_client_segment(struct client_app_gids *app_gids,
                                      struct app_accumulate *accumulate,
                                      int is_new_client,
                                      int *is_app_sent)
{
    struct argo_object *old_app_object;
    struct zpn_application *old_app;
    struct zpn_application *new_app;
    struct zpn_application *new_app_from_config;
    struct zpn_application new_application;
    struct zpn_client_app client_app;
    struct zpn_client_app_state *client = accumulate->client;

    int res;
    size_t i;

    ZPN_DEBUG_MTN("%s: Inform_one_client (segment)", client->debug_str);

    if (!app_gids) {
        if (is_new_client) {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: empty app_gids", __LINE__);
        }
        return;
    }

    if (is_new_client && !app_gids->app_gids_used) {
        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: no app_gids used", __LINE__);
    }

    for (i = 0; i < app_gids->app_gids_used; i++) {
        int app_override = 0;
        int is_skipped = 0;
        old_app_object = NULL;
        old_app = NULL;

        ZPN_DEBUG_MTN("%s: Inform_one_client (segment): i = %zu, gid = %ld", client->debug_str, i, (long) app_gids->app_gids[i]);
        new_app_from_config = NULL;
        res = zpn_broker_client_apps_get_app_by_gid(app_gids->app_gids[i],
                                                        &new_app_from_config);
        if (res) new_app_from_config = NULL;

        /* Use the accumulate routine to get our ports, etc, just like
         * for domains. NOTE: accumulate_ports_walk performs policy
         * check. This is how we do policy based download here...
         * Unlike for domains, the walk routine itself is called just
         * the once for this app_gid and its groups. */
        init_accumulate(accumulate);

        accumulate_ports_walk(accumulate, new_app_from_config, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), NULL, NULL);

        if (accumulate->bypass_type[0] == 0) {
            if (accumulate->bypass) {
                snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "ALWAYS");
            } else if (accumulate->bypass_reauth) {
                snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "REAUTH");
            } else {
                snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "NEVER");
            }
        } else {
            app_override = 1;
        }

        /* Get the object we last sent to client. */
        old_app_object = zhash_table_lookup(client->apps_sent, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), NULL);
        if (old_app_object) {
            old_app = old_app_object->base_structure_void;
        }
        /*
         * Create an object representing what we are sending to the
         * client- but only if we think the client needs to
         * know... This builds both the application that will be saved
         * (new) as well as the structure of the client app that can
         * be sent.
         *
         * We build this from scratch because the behavior can be
         * different from the actual app based on policy (which was
         * evaluated as part of accumulate_ports_walk).
         *
         * We will remember and track this created (and eventually
         * sent) object rather than the actual application object.
         */
        /* we clear client_app here because we require a client_app
         * for deletion as well... */
        memset(&client_app, 0, sizeof(client_app));
        client_app.app_gid = app_gids->app_gids[i];
        new_app = NULL;
        if (accumulate->enabled && (1 || app_override || accumulate->udp_port_count || accumulate->tcp_port_count)) {
            if (!new_app_from_config) {
                res = zpn_broker_client_apps_get_app_by_gid(app_gids->app_gids[i],
                                                            &new_app_from_config);
            }
            if (res) {
                /* not found is fine... */
                new_app = NULL;
            } else {
                memset(&new_application, 0, sizeof(new_application));
                new_app = &new_application;
                if (!accumulate->udp_port_count &&
                    !accumulate->tcp_port_count) {
                    /* No ports configured. This is deleted unless it is a special bypass type... */
                    if ((strcasecmp(accumulate->bypass_type, "ON_NET") == 0) ||
                        (strcasecmp(accumulate->bypass_type, "OFF_NET") == 0) ||
                        (strcasecmp(accumulate->bypass_type, "ALWAYS") == 0)) {
                        /* always download */
                    } else if (accumulate->do_accumulate &&
                              ((strcasecmp(accumulate->icmp_access_type, "PING") == 0) ||
                               (strcasecmp(accumulate->icmp_access_type, "PING_TRACEROUTING") == 0))) {
                        /* only download ICMP apps if do_accumulate */
                    } else {
                        new_app = NULL;
                    }
                } else {
                    /* TCP ports... */
                    new_application.tcp_port_ranges = accumulate->tcp_ports_array;
                    new_application.tcp_port_ranges_count = accumulate->tcp_port_count;
                    client_app.tcp_port_ranges = accumulate->tcp_ports_array;
                    client_app.tcp_port_ranges_count = accumulate->tcp_port_count;

                    /* UDP ports... */
                    new_application.udp_port_ranges = accumulate->udp_ports_array;
                    new_application.udp_port_ranges_count = accumulate->udp_port_count;
                    client_app.udp_port_ranges = accumulate->udp_ports_array;
                    client_app.udp_port_ranges_count = accumulate->udp_port_count;
                }
                /* Double encryption config: */
                new_application.double_encrypt = accumulate->double_encrypt;
                client_app.double_encrypt = accumulate->double_encrypt;

                /* ip_anchoring config and inspection config: */
                ZPN_APPLICATION_SET_INSPECTION_FLAGS(accumulate->ip_anchored,
                                                     accumulate->inspected,
                                                     new_application.ip_anchored,
                                                     new_application.inspect_traffic_with_zia);
                ZPN_APPLICATION_SET_INSPECTION_FLAGS(accumulate->ip_anchored,
                                                     accumulate->inspected,
                                                     client_app.ip_anchored,
                                                     client_app.inspected);

                /* Bypass on reauth */
                new_application.bypass_on_reauth = accumulate->bypass_on_reauth;
                client_app.bypass_on_reauth = accumulate->bypass_on_reauth;

                /* Bypass type (text version, modern) */
                new_application.bypass_type = accumulate->bypass_type;
                client_app.bypass_type = accumulate->bypass_type;

                new_application.icmp_access_type = accumulate->icmp_access_type;
                client_app.icmp_access_type = accumulate->icmp_access_type;

                /* For backwards compatibility: */
                client_app.ingress_port_ranges = accumulate->tcp_ports_array;
                client_app.port_ranges_count = accumulate->tcp_port_count;

                /* Policy Style: inclusive or exclusive */
                new_application.m_style = new_app_from_config->m_style;
                client_app.match_style = new_app_from_config->m_style;

                if (strcmp(new_application.bypass_type, "NEVER")) {
                    new_application.bypass = 1;
                    client_app.bypass = 1;
                }
                new_application.domain_names = new_app_from_config->domain_names;
                new_application.domain_names_count = new_app_from_config->domain_names_count;
                new_application.gid = new_app_from_config->gid;
            }
        }

        dump_app(old_app, client->debug_str, " old_app: ", 1);
        dump_app(new_app, client->debug_str, " new_app: ", 1);

        // old_app is now null or valid.
        // new_app is now null or valid.

        /*
         * Send differntial updates for this app_gid!!!
         *
         * The cases:
         *
         * 1. No old object, but have new object:
         *
         *    For this case, we just send all new domains individually for this app_id. (and remember this new object)
         *
         * 2. Old object, but no new object:
         *
         *    For this case, we just send all old domains for deletion. (and delete the old object)
         *
         * 3. Old object and new object:
         *
         *    Walk domains for old object and new object. (Note:
         *    application domains are ordered in the array) Keep track
         *    of whether or not the old and new app are 'different':
         *    different is different in ports, double encryption,
         *    bypass type
         *
         *    If domain existed before and does not exist now, send a delete.
         *    If domain did not exist before and does now, send the new app.
         *    If domain existed before and after, and is different, send the app.
         *    If domain existed before and after, and is not different, do not send the app.
         *
         *    delete the old object, remember the new one.
         *
         * 4. No new or old object:
         *
         *    Do nothing.
         *
         */
        if (!old_app && new_app) {
            /* Send all domains with new client config. */
            client_app.deleted = 0;
            is_skipped = skip_app_seg(old_app, &client_app, client);
            if ( !is_skipped && 0 == send_app_all(client, &client_app, new_app, is_new_client)) {
                /* Remember what we have sent. */
                *is_app_sent = 1;
                struct argo_object *new_object = argo_object_create(zpn_application_description, new_app);
                zhash_table_store(client->apps_sent, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), 0, new_object);
            } else if(is_new_client) {
                snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                         "APP_DOWNLOAD %d: is_skipped=%d, app_gid=%"PRId64, __LINE__, is_skipped, client_app.app_gid);
            }
        } else if (old_app && !new_app) {
            /* Remove all domains we sent before, because they are gone. */
            client_app.deleted = 1;
            if ( 0 == send_app_all(client, &client_app, old_app, is_new_client)) {
                /* Remove the old object */
                *is_app_sent = 1;
                zhash_table_remove(client->apps_sent, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), old_app_object);
                /* Release the old object */
                argo_object_release(old_app_object);
            } else if(is_new_client) {
                snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                         "APP_DOWNLOAD %d: app_gid=%"PRId64, __LINE__, client_app.app_gid);
            }
        } else if (old_app && new_app) {
            /* The fun! */
            int old_walk = 0;
            int new_walk = 0;
            int any_difference = 0;
            while ((old_walk < old_app->domain_names_count) ||
                   (new_walk < new_app->domain_names_count)) {
                int cmp;
                /* cmp is used to determine whether to consume old
                 * walk or new walk next. If <0, then old_walk is
                 * consumed. If >0 then new_walk is consumed. If equal
                 * then they are equal */
                if (old_walk >= old_app->domain_names_count) {
                    cmp = 1;
                } else if (new_walk >= new_app->domain_names_count) {
                    cmp = -1;
                } else {
                    cmp = strcmp(old_app->domain_names[old_walk], new_app->domain_names[new_walk]);
                }
                if (cmp < 0) {
                    /* we have an old domain, and the old domain does not exist in the new set. Send delete. */
                    client_app.app_domain = old_app->domain_names[old_walk];
                    client_app.deleted = 1;
                    if (0 == send_app(client, &client_app, 0, 0)) {
                        *is_app_sent = 1;
                        any_difference = 1;
                    } else if(is_new_client) {
                        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                        "APP_DOWNLOAD %d: app_gid=%"PRId64, __LINE__, client_app.app_gid);
                    }
                    /* Go to next old_domain. */
                    old_walk++;
                } else if (cmp > 0) {
                    /* We have a new domain that didn't exist before... */
                    client_app.app_domain = new_app->domain_names[new_walk];
                    client_app.deleted = 0;
                    is_skipped = skip_app_seg(old_app, &client_app, client);
                    if (!is_skipped && 0 == send_app(client, &client_app, 0, 0)) {
                        *is_app_sent = 1;
                        any_difference = 1;
                    } else if(is_new_client) {
                        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                                 "APP_DOWNLOAD %d: is_skipped=%d, app_gid=%"PRId64, __LINE__, is_skipped, client_app.app_gid);
                    }

                    /* Go to next new_domain */
                    new_walk++;
                } else {
                    /* Same domain. Check if something changed... */
                    if (ports_diff(old_app->udp_port_ranges, old_app->udp_port_ranges_count, new_app->udp_port_ranges, new_app->udp_port_ranges_count) ||
                        ports_diff(old_app->tcp_port_ranges, old_app->tcp_port_ranges_count, new_app->tcp_port_ranges, new_app->tcp_port_ranges_count) ||
                        (old_app->double_encrypt != new_app->double_encrypt) ||
                        strcmp(old_app->bypass_type, new_app->bypass_type) ||
                        strcmp(old_app->icmp_access_type, new_app->icmp_access_type) ||
                        (old_app->bypass_on_reauth != new_app->bypass_on_reauth) ||
                        (old_app->ip_anchored != new_app->ip_anchored) ||
                        (old_app->m_style != new_app->m_style) ||
                        (old_app->inspect_traffic_with_zia != new_app->inspect_traffic_with_zia)) {
                        /* Something changed... send this app. */
                        client_app.app_domain = new_app->domain_names[new_walk];
                        client_app.deleted = 0;
                        is_skipped = skip_app_seg(old_app, &client_app, client);
                        if (!is_skipped && 0 == send_app(client, &client_app, 0, 0)) {
                            *is_app_sent = 1;
                            any_difference = 1;
                        } else if(is_new_client) {
                            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                                     "APP_DOWNLOAD %d: is_skipped=%d, app_gid=%"PRId64, __LINE__, is_skipped, client_app.app_gid);
                        }
                    } else {
                        /* Nothing changed, don't send this app. */
                    }
                    /* Go to next domain on both old/new */
                    old_walk++;
                    new_walk++;
                }
            } /* end of while loop */

            if (old_app_object && (any_difference || is_skipped)) {
                /* Out with the old, in with the new! */
                zhash_table_remove(client->apps_sent, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), old_app_object);
                argo_object_release(old_app_object);
            }
            if (any_difference) {
                struct argo_object *new_object = argo_object_create(zpn_application_description, new_app);
                zhash_table_store(client->apps_sent, &(app_gids->app_gids[i]), sizeof(app_gids->app_gids[i]), 0, new_object);
            }
        } else {
            /* Nothing to do */
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                     "APP_DOWNLOAD %d: nothing download", __LINE__);
        }
    } /* end of for loop for each app gid */
}

/*
 * Update one client about the domains it needs to (re)process.
 *
 * This call only occurs on client->calc_thread (thus the reduced locking)
 *
 * This routine consumes one reference to the domain list it was passed.
 *
 * This routine integrates into policy evaluation as follows:
 *
 * 1. For each application associated with a domain, run the
 *    accumulate_ports function, which actually calls policy
 *
 * 2. Once all ports are accumulated, we have:
 *
 *   A. Set of accumulated ports, from step 1
 *
 *   B. Bypass indication, from step 1
 *
 *   C. Bypass Reauth indication, from step 1
 *
 *   D. Original bypass configuration for the app
 *
 *      i. On net/Off net/Always/Never
 *
 * What we download to the client uses the following algorithm:
 *
 * If original bypass is 'on_net', 'off_net', or 'always', that
 * configuration supercedes all else, and the domain is sent
 * appropriately.
 *
 * If we have accumulated ports {
 *    If bypass_reauth {
 *       send ports, and tell client bypass_reauth
 *    } else {
 *       send ports
 *    }
 * } else {
 *    If bypass_indication {
 *       send bypass
 *    } else {
 *       send nothing (ignore domain / don't download)
 *    }
 * }
 */
static int exclusive_accumulate_per_domain(char *domain, struct app_accumulate *accumulate, int collect_segments)
{
    int app_override = 0;
    int res;
    struct zpn_client_app_state *client = accumulate->client;
    struct zpn_application_domain *relations[10000];
    size_t relations_count;

    /* Go through all the apps and see what ports this app is
     * advertising */
    init_accumulate(accumulate);

    /* The following walk gets the ports involved for each
     * application by checking policy, etc. (Yes,
     * accumulate_ports_walk does policy evaluation) */
    relations_count = sizeof(relations) / sizeof(relations[0]);
    res = zpn_broker_client_apps_get_by_domain(client->customer->customer_gid, domain, &(relations[0]), &relations_count);
    if (res == ZPATH_RESULT_NO_ERROR) {
        int j;

        if (collect_segments) {
            accumulate->app_segments_count = 0;
            accumulate->app_segments = ZPN_BCA_CALLOC(sizeof(struct zpn_app_client_check_full_app*) * relations_count); //alloc enough space for everything
        }

        for (j = 0; j < relations_count; j++) {
            int accumulated = 0;
            ZPN_DEBUG_CLIENT("%s: Running accumulate_ports_walk on customer %ld, domain %s, gid %ld, relation %d out of %d",
                             client->debug_str,
                             (long) client->customer->customer_gid,
                             domain,
                             (long) relations[j]->application_gid,
                             j, (int) relations_count);

            int do_continue = 0;
            struct zpn_application *app = zpn_broker_client_apps_get_apps_by_gid(collect_segments, relations[j]->application_gid, client, &do_continue, relations[j]->customer_gid);
            if (do_continue) {
                continue;
            }

            accumulate_ports_walk(accumulate,
                                  app,
                                  &(relations[j]->application_gid),
                                  sizeof(relations[j]->application_gid),
                                  NULL, // overlapping_ports_present
                                  &accumulated);

            if (collect_segments && accumulated && accumulate->app_segments) {
                accumulate->app_segments[accumulate->app_segments_count] = zpn_broker_client_apps_copy_full_app_data(app);
                if (accumulate->app_segments[accumulate->app_segments_count]) {
                    accumulate->app_segments_count++;  // only inc on successful attach
                } else {
                    ZPN_LOG(AL_ERROR,
                            "%" PRId64
                            " application failed to attach to accumulation 50k for forwarding clients may be "
                            "inaccurate customer %" PRId64, app->gid, app->customer_gid);
                }
            }
        }

        res = ZPATH_RESULT_NO_ERROR; // reset res back to no error once done looping
    }

    /* ET-32338, remove the line as it is producing too much data in the log, and can cause heartbeat expire
    //if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
    if (0) {
        char dump[1000];
        argo_structure_dump(app_accumulate_description,
                            accumulate,
                            dump,
                            sizeof(dump),
                            NULL,
                            1);
        ZPN_DEBUG_CLIENT("Customer %ld: domain = %s, accumulate = %s", (long) client->customer->customer_gid, domains->domains[i], dump);
    }
    */

    /* Only set bypass type if it was not overridden by a static app definition */
    if (accumulate->bypass_type[0] == 0) {
        if (accumulate->bypass) {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "ALWAYS");
        } else if (accumulate->bypass_reauth) {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "REAUTH");
        } else {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "NEVER");
        }
    } else {
        app_override = 1;
    }

    return app_override;
}

static int inclusive_accumulate_per_domain(char *domain,
                                           struct app_accumulate *accumulate,
                                           char **domain_results,
                                           size_t domain_results_count,
                                           struct zhash_table *accumulated_inclusive_domains,
                                           int collect_segments) {
    int app_override = 0;
    int res;
    struct zpn_client_app_state *client = accumulate->client;
    struct zpn_application_domain *relations[10000];
    size_t relations_count;
    int overlapping_ports = 0;
    int cache_accumulate = 1; //IOS and android app download, can do accumulate per domain twice and we need to return from cache
    size_t prev_count = 0;
    size_t curr_count = 0;
    int inc_counter = 0;
    struct argo_object **prev_obj = NULL;
    struct argo_object **curr_obj = NULL;

    /* Go through all the apps and see what ports this app is
     * advertising */
    init_accumulate(accumulate);

    /*
     * If domain is available in accumulated_inclusive_domains it mean
     * we have already accumulated port for given domain and its all
     * parent domains.
     *
     */
    struct app_accumulate *domain_accumulate = zhash_table_lookup(accumulated_inclusive_domains,
                                                                  domain, strnlen(domain , MAX_APPLICATION_DOMAIN_LEN), NULL);
    if (domain_accumulate) {
        memcpy(accumulate, domain_accumulate, sizeof(*accumulate));
        dump_accumulate(domain_accumulate, client->debug_str, " cached final accumulated domain: ");
        goto done_accumulation;
    }

    struct app_accumulate *prev_accumulate = NULL;
    for (int i = domain_results_count - 1; i >= 0; i--) {
        struct app_accumulate *current_accumulate = zhash_table_lookup(accumulated_inclusive_domains,
                                                                       domain_results[i],
                                                                       strnlen(domain_results[i], MAX_APPLICATION_DOMAIN_LEN),
                                                                       NULL);
        if (current_accumulate) {
            prev_accumulate = current_accumulate;
            continue;
        }

        cache_accumulate = 0;
        init_accumulate(accumulate);

        /* The following walk gets the ports involved for each
         * application by checking policy, etc. (Yes,
         * accumulate_ports_walk does policy evaluation) */
        relations_count = sizeof(relations) / sizeof(relations[0]);
        res = zpn_broker_client_apps_get_by_domain(client->customer->customer_gid, domain_results[i], &(relations[0]),
                                                   &relations_count);
        if(res) {
            ZPN_LOG(AL_WARNING, "%s: customer %"PRId64" Failed to get apps by domain for domain %s, skipping port accumulation",
                                client->debug_str, client->customer->customer_gid, domain_results[i]);
            continue;
        }

        overlapping_ports = 0;

        if (collect_segments) {
            if (prev_count > 0 && prev_obj) {
                curr_count = prev_count + relations_count;
            } else {
                curr_count = relations_count;
            }
            curr_obj = ZPN_BCA_CALLOC(sizeof(struct zpn_app_client_check_full_app*) * curr_count);
            if (prev_count > 0 && prev_obj) {
                for (inc_counter = 0; inc_counter < prev_count && prev_obj[inc_counter]; inc_counter++) {
                    curr_obj[inc_counter] = argo_object_copy(prev_obj[inc_counter]);
                    argo_object_release(prev_obj[inc_counter]);
                }
                ZPN_BCA_FREE(prev_obj);
            }
        }

        for (int j = 0; j < relations_count; j++) {
            int accumulated = 0;
            ZPN_DEBUG_CLIENT("%s: Running accumulate_ports_walk on customer %ld, domain %s, gid %ld, relation %d out of %d",
                              client->debug_str, (long)client->customer->customer_gid, domain_results[i],
                              (long)relations[j]->application_gid, j, (int)relations_count);

            int do_continue = 0;
            struct zpn_application *app = zpn_broker_client_apps_get_apps_by_gid(collect_segments, relations[j]->application_gid, client, &do_continue, relations[j]->customer_gid);
            if (do_continue) {
                continue;
            }

            accumulate_ports_walk(accumulate,
                                  app,
                                  &(relations[j]->application_gid),
                                  sizeof(relations[j]->application_gid),
                                  &overlapping_ports,
                                  &accumulated);
            if (overlapping_ports) {
                enum zpn_app_search_type search_type = (zpn_is_app_scaling_enabled(client->customer_gid)) ? zpn_app_scale_search : zpn_app_download_search;
                zpn_application_search_debug_stats_counter(search_type, zpn_app_overlapping_ports);
                ZPN_LOG(AL_INFO, "%s: customer %"PRId64" for domain %s, app %"PRId64" contains overlapping ports",
                                  client->debug_str, client->customer->customer_gid, domain_results[i], relations[j]->application_gid);
            }

            if (collect_segments && accumulated && curr_obj) {
                curr_obj[inc_counter] = zpn_broker_client_apps_copy_full_app_data(app);
                if (curr_obj[inc_counter]) {
                    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
                        char buf[16*1024];
                        if (argo_object_dump(curr_obj[inc_counter], buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                            ZPN_DEBUG_CLIENT("%s: Application Gid attached = %s", client->debug_str, buf);
                        }
                    }
                    inc_counter++;  // only inc on successful attach
                } else {
                    ZPN_LOG(AL_ERROR,
                            "%" PRId64
                            " application failed to attach to accumulation 50k for forwarding clients may be "
                            "inaccurate customer %"PRId64, app->gid, app->customer_gid);
                }
            }
        }

        if(g_dr_mode_is_active && (!accumulate->do_accumulate && accumulate->skip_non_dr_app_in_dr_mode)) {
            ZPN_DEBUG_CLIENT("%s: skipping non DR domain %s in DR mode", client->debug_str, domain_results[i]);
            if(prev_accumulate) {
                dump_accumulate(prev_accumulate, client->debug_str, " prev accumulated domain: ");
            }
            if(i == 0) {
                goto done_accumulation;
            } else {
                continue;
            }
        }

        if (accumulate->do_accumulate && prev_accumulate && prev_accumulate->do_accumulate) {
            res = zpn_merge_ports(&(accumulate->udp_ports_array[0]), &(accumulate->udp_port_count), MAX_PORTS_ARRAY,
                                  &(prev_accumulate->udp_ports_array[0]),
                                  prev_accumulate->udp_port_count, NULL);
            if (res) {
                ZPN_LOG(AL_ERROR, "Merge UDP ports failed for domain %s and parent domain %s", domain_results[i],
                                   prev_accumulate->domain_name);
            }
            res = zpn_merge_ports(&(accumulate->tcp_ports_array[0]), &(accumulate->tcp_port_count), MAX_PORTS_ARRAY,
                                  &(prev_accumulate->tcp_ports_array[0]),
                                  prev_accumulate->tcp_port_count, NULL);
            if (res) {
                ZPN_LOG(AL_ERROR, "Merge TCP ports failed for domain %s and parent domain %s", domain_results[i],
                                   prev_accumulate->domain_name);
            }
        }

        current_accumulate = ZPN_CALLOC(sizeof(*current_accumulate));

        memcpy(current_accumulate, accumulate, sizeof(*accumulate));
        /*
         * Let's set domain_name once we copy accumulate to current accumulate
         * otherwise it can get override with accumuate domain_name
         */
        snprintf(current_accumulate->domain_name, sizeof(current_accumulate->domain_name), "%s", domain_results[i]);
        dump_accumulate(current_accumulate, client->debug_str, " current accumulated domain: ");
        zhash_table_store(accumulated_inclusive_domains, domain_results[i], strnlen(domain_results[i], MAX_APPLICATION_DOMAIN_LEN), 0, current_accumulate);
        prev_accumulate = current_accumulate;
        if (collect_segments) {
            prev_count = curr_count;
            prev_obj = curr_obj;
        }
    }

    if (prev_accumulate && (domain_results_count > 1 || cache_accumulate)) {
        memcpy(accumulate, prev_accumulate, sizeof(*accumulate));
        dump_accumulate(prev_accumulate, client->debug_str, " final accumulated domain: ");
    }
    if (prev_count > 0 && prev_obj) {
        accumulate->app_segments = prev_obj;
        accumulate->app_segments_count = prev_count;
        if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
            for (int z = 0; z < accumulate->app_segments_count; z++) {
                char buf[16*1024];
                if (argo_object_dump(accumulate->app_segments[z], buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                    ZPN_DEBUG_CLIENT("%s: Attaching the AppGid = %s", client->debug_str, buf);
               }
            }
        }
    } else {
        accumulate->app_segments = NULL;
        accumulate->app_segments_count = 0;
    }
done_accumulation:
    /* Only set bypass type if it was not overridden by a static app definition */
    if (accumulate->bypass_type[0] == 0) {
        if (accumulate->bypass) {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "ALWAYS");
        } else if (accumulate->bypass_reauth) {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "REAUTH");
        } else {
            snprintf(accumulate->bypass_type, sizeof(accumulate->bypass_type), "%s", "NEVER");
        }
    } else {
        app_override = 1;
    }

    return app_override;
}

static int accumulate_per_domain(char *domain,
                                 struct app_accumulate *accumulate,
                                 struct zhash_table *accumulated_inclusive_domains,
                                 int search_pattern_domain,
                                 int collect_segments) {
    char *domain_results[100];
    size_t domain_results_count = 0;
    int res = ZPN_RESULT_NO_ERROR;
    int app_override = 0;
    int is_app_multi_match_enabled = 0;
    struct zpn_client_app_state *client = accumulate->client;
    int64_t scope_gid = client->scope_gid;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    int64_t start_us = 0;
    int64_t delta_us = 0;
    int do_inclusive = 0;
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_PERF_IDX)) start_us = epoch_us();

    /*
     * case-1: Non Multi-match enabled app segment and Non pattern based domain
     *  output of if statement: false (MM feature enabled: true && (is_pattern_domain: false || !is_domain_exclusive: false ))
     *  it will directly go for exclusive search
     *
     * case-2: Non pattern based app and Multi-match enabled app segment
     *  output of if statement: true (MM feature enabled: true && (is_pattern_domain: false || !is_domain_exclusive: true ))
     *  it will go inside if condition as its inclusive domain
     *
     * case-3: Pattern based domain and Multi-match enabled app segment
     * output of if statement: true (MM feature enabled: true && (is_pattern_domain: true || !is_domain_exclusive: false ))
     * it will go inside if condition as its inclusive domain
     *
     * case-4: Pattern based domain and Multi-match disabled app segment
     *    We don't allow this combination from the UI (in case if there is any change in that behavior in that case revisit this code)
     */
    if (is_app_multi_match_search(customer_gid, client->client_type) &&
        (accumulate->is_pattern_domain || !is_domain_exclusive(customer_gid, domain))) {
        domain_results_count = sizeof(domain_results) / sizeof(domain_results[0]);
        res = zpn_inclusive_domains_search(scope_gid, domain, domain_results, &domain_results_count, search_pattern_domain);
        ZPN_DEBUG_CLIENT("%s: domain = %s, parent domain count %lu, res %s",client->debug_str, domain, domain_results_count, zpn_result_string(res));
        is_app_multi_match_enabled = 1;

        enum zpn_app_search_type search_type = (zpn_is_app_scaling_enabled(client->customer_gid)) ? zpn_app_scale_search : zpn_app_download_search;
        zpn_application_search_stats_counter(search_type, domain_results_count);
    }

    if (is_app_multi_match_enabled && (res == ZPN_RESULT_NO_ERROR) && domain_results_count) {
        do_inclusive = 1;
        client->stats.client_inclusive_app_download_count++;
        app_override = inclusive_accumulate_per_domain(domain, accumulate, domain_results,
                                                       domain_results_count, accumulated_inclusive_domains, collect_segments);
    } else {
        app_override = exclusive_accumulate_per_domain(domain, accumulate, collect_segments);
    }

    if (start_us > 0) {
        delta_us = epoch_us() - start_us;
        ZPN_LOG(AL_NOTICE, "diamond_search performance: accumulate_per_domain, delta_us=%"PRId64", do_inclusive=%d, domain_results_count=%d",
                           delta_us, do_inclusive, (int)domain_results_count);
    }

    return app_override;
}

static inline void inform_one_client_not_segment_send_app(int is_new_client,
                                                          struct zpn_client_app_state *client,
                                                          struct argo_object *app_sent_object,
                                                          int is_last_app,
                                                          int *is_app_sent)
{
    /* It (the object) is created with a single
     * reference count... We let the receiver release
     * that reference count. However, we need to hold
     * a reference count to client state as well... */
    if (is_new_client) {
        if (!client->is_first_app_enqueued) {
            client->is_first_app_enqueued = 1;
            zpn_client_tracker_start(client->tracker, client_track_app_sent);
        }
        client->stats.client_app_sent_cnt_in++;
        if (!client->is_cstate_done) zpn_broker_app_sent_stats[client->conn_thread_id].client_app_sent_cnt_in++;
    }

    // If last app fill last_app_enqueued and also set "has_next" as 0
    if (is_last_app) {
        int col_index, res;

        if (!client->is_last_app_enqueued) {
            client->is_last_app_enqueued = 1;
        }

        // We are not failing if the argo update fails and rely on ZCC to handle this, as we don't want to miss the app update
        res = argo_object_get_column_index(app_sent_object, "has_next", &col_index);
        if (!res) {
            res = argo_object_write_int_by_column_index(app_sent_object, col_index, 0);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Customer : %"PRId64", argo_object_write_int_by_column_index failed to write has_next, res: %s", client->customer_gid, zpath_result_string(res));
            }
        } else {
            ZPN_LOG(AL_CRITICAL, "Customer : %"PRId64", argo_object_get_column_index failed to get col index for has_next, res: %s", client->customer_gid, zpath_result_string(res));
        }
    }
    dump_app_argo_object(app_sent_object, client->debug_str, " send app noseg: ");

    client_hold(client);
    zevent_base_big_call(client->client_fohh_thread, client_app_rx_cb, client,
                         is_new_client, app_sent_object, NULL, NULL, 0);
    *is_app_sent = 1;
}

static struct zpn_client_app_cache*
zpn_broker_client_create_app_cache(const struct zpn_client_app *app, int64_t customer_gid) {
    uint16_t j = 0;
    struct zpn_client_app_cache *app_cache = NULL;

    if (!app) {
        ZPN_LOG(AL_CRITICAL, "Customer : %"PRId64", zpn_broker_client_create_app_cache app is NULL", customer_gid);
        return NULL;
    }

    app_cache = ZPN_APP_CACHE_CALLOC(sizeof(struct zpn_client_app_cache));
    app_cache->magic = CLIENT_APP_CACHE_MAGIC;
    app_cache->bypass = (uint16_t)app->bypass;
    app_cache->bypass_on_reauth = (uint16_t)app->bypass_on_reauth;
    app_cache->deleted = (uint16_t)app->deleted;
    app_cache->ip_anchored = (uint16_t)app->ip_anchored;
    app_cache->match_style = (uint16_t)app->match_style;
    app_cache->inspected = (uint16_t)app->inspected;
    app_cache->double_encrypt = (uint16_t)app->double_encrypt;
    if (app->icmp_access_type) {
        app_cache->icmp_access_type = (uint16_t)zpn_broker_client_app_icmp_access_type_string_to_enum(app->icmp_access_type, customer_gid);
    }
    if (app->bypass_type) {
        app_cache->bypass_type = (uint16_t)zpn_broker_client_app_bypass_type_string_to_enum(app->bypass_type, customer_gid);
    }
    app_cache->tcp_port_ranges_count = (uint16_t)app->tcp_port_ranges_count;
    app_cache->udp_port_ranges_count = (uint16_t)app->udp_port_ranges_count;
    app_cache->tcp_port_ranges = ZPN_APP_CACHE_CALLOC(sizeof(*app_cache->tcp_port_ranges) * app_cache->tcp_port_ranges_count);
    for (j=0; j<app_cache->tcp_port_ranges_count; j++) {
        app_cache->tcp_port_ranges[j] = (uint16_t)app->tcp_port_ranges[j];
    }
    app_cache->udp_port_ranges = ZPN_APP_CACHE_CALLOC(sizeof(*app_cache->udp_port_ranges) * app_cache->udp_port_ranges_count);
    for (j=0; j<app_cache->udp_port_ranges_count; j++) {
        app_cache->udp_port_ranges[j] = (uint16_t)app->udp_port_ranges[j];
    }

    return app_cache;
}

static inline void
zpn_broker_client_app_cache_free(struct zpn_client_app_cache *app_cache) {
    if (!app_cache) {
        return;
    }
    ZPN_APP_CACHE_FREE_AND_NULL(app_cache->tcp_port_ranges);
    ZPN_APP_CACHE_FREE_AND_NULL(app_cache->udp_port_ranges);
    ZPN_APP_CACHE_FREE_AND_NULL(app_cache);
}

static void inform_one_client_not_segment(struct client_domains *domains,
                                          struct app_accumulate *accumulate,
                                          int                   is_new_client,
                                          int                   *is_app_sent,
                                          struct zhash_table    *accumulated_inclusive_domains)
{
    struct argo_object *app_sent_object = NULL;
    struct zpn_client_app_cache *app_sent = NULL;
    struct zpn_client_app new_app;
    struct argo_object *prev_app_sent_object = NULL;
    struct zpn_client_app_state *client = accumulate->client;
    int64_t restrict_app_download_embedded = 0;
    int64_t max_apps_sent = DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE;
    int app_override;
    int do_not_send;
    int loop_twice = 0;
    int different;
    int i;
    int64_t start_us = 0;
    int64_t delta_us = 0;
    int tcp_port_array[MAX_PORTS_ARRAY], udp_port_array[MAX_PORTS_ARRAY];
    int tcp_port_count = 0, udp_port_count = 0;

    if (!domains) {
        ZPN_LOG(AL_ERROR, "empty domains");
        if (is_new_client) {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: empty domains", __LINE__);
        }
        return;
    }

    restrict_app_download_embedded = client->customer->restrict_app_down_embedded_feature_status;
    if (restrict_app_download_embedded || zpn_policy_app_download_rest_test) {
        if (zpn_platform_type_android == client->platform_type) {
            max_apps_sent = client->customer->max_app_download_android_value;
        } else if (zpn_platform_type_ios == client->platform_type) {
            max_apps_sent = client->customer->max_app_download_ios_value;
        } else {
            // If any other platform, then reset the app download restriction
            restrict_app_download_embedded = 0;
        }
        ZPN_DEBUG_CLIENT("Client %s with gid %"PRId64" application download restriction is %s "
                         "for platform %s, Maximum allowed value is %"PRId64,
                         client->debug_str, client->customer_gid, restrict_app_download_embedded ? "enabled" : "disabled",
                         ZPN_PLATFORM_TYPE_STR(client->platform_type), restrict_app_download_embedded ? max_apps_sent : -1);
    }

    /* For each domain... */
    if (is_new_client && !domains->domains_used) {
        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: no domain used", __LINE__);
    }

    /*
     * Note: for embedded devices with platform ios and android
     * client type will be zapp and for zapp client type, one_client_not_segment func is called
     *
     * For the embedded devices, if the app download
     * restriction is enabled ET-46605 then
     * send only max allowed apps - this is tracked
     * with the hash table size
     *
     * This loop is running twice when restricted app download is enabled b/c
     * Lets say max app download is set to 2
     * At present there are 5 domains : D1, D2, D3, D4, D5
     *
     * First time client connects
     * Hash size before: 0, Parse through the list: D1 and D2 is send, Hash sz after = 2
     * D3, D4, D5 are not send, so marked as NS
     *
     * ---------------------------------------- D1 gets deleted
     * When list is parsed if items are encountered in below order as hash size is already 2
     * then there won't be any operation possible on D3, D4 and D5 in first parse
     *
     *  D3(NS), D4(NS), D5(NS), D1(D) send delete = 1, D2 (already downloaded) so hash size = 1
     *
     * after the delete = 1 for D1 is send to the client, hash has capacity of 1 more element, so
     * a second loop is needed to be runned so, 1 more element can be send.
     *
     * thats why a cnt = 1; do { cnt++; } while (app_rest_is_enabled && cnt < 2); is wrapped
     * around the list of all parsed domains
     */
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_PERF_IDX)) start_us = epoch_us();
    client->stats.client_total_app_count = domains->domains_used;
    do {
        ZPN_DEBUG_MTN("loop twice value %d, domains_used: %ld", loop_twice, domains->domains_used);
        for (i = 0; i < domains->domains_used; i++) {
            do_not_send = 0;
            ZPN_DEBUG_MTN("%s: Inform_one_client, domain = %s", client->debug_str, domains->domains[i]);

            if (!is_domain_need_to_send(client, domains->domains[i])) {
                continue;
            }
            /* pattern apps is not supported with app download */
            if (is_pattern_match_domain(domains->domains[i], strnlen(domains->domains[i], MAX_APPLICATION_DOMAIN_LEN))) {
                zpn_application_search_debug_stats_counter(zpn_app_download_search, zpn_pattern_apps_skipped);
                // Log this only once for the client
                if (!client->skip_pattern_domain) {
                    ZPN_LOG(AL_NOTICE, "Customer: %"PRId64", %s, Skip Pattern Apps with App Download for domain: %s, platform: %s, no_ip_download: %d, no_domain_download: %d, app scaling: %d, first_pass_complete: %d",
                            client->customer_gid, client->tunnel_id, domains->domains[i], ZPN_PLATFORM_TYPE_STR(client->platform_type), client->no_ip_download, client->no_domain_download, zpn_is_app_scaling_enabled(client->customer_gid), client->first_pass_complete);
                    client->skip_pattern_domain = 1;
                }
                continue;
            }

            if (is_catch_all_ip(domains->domains[i])) {
                zpn_application_search_debug_stats_counter(zpn_app_download_search, zpn_catch_all_ip_apps_skipped);
                ZPN_LOG(AL_NOTICE, "Customer: %"PRId64", %s, Skip Catch All IP App with App Download for domain: %s, platform: %s, no_ip_download: %d, no_domain_download: %d, app scaling: %d, first_pass_complete: %d",
                        client->customer_gid, client->tunnel_id, domains->domains[i], ZPN_PLATFORM_TYPE_STR(client->platform_type), client->no_ip_download, client->no_domain_download, zpn_is_app_scaling_enabled(client->customer_gid), client->first_pass_complete);
                continue;
            }
            app_override = accumulate_per_domain(domains->domains[i], accumulate, accumulated_inclusive_domains, PATTERN_DOMAIN_SEARCH_DISABLED, 0);

            /* We set different if the built up app is different from the
             * downloaded app */
            different = 0;

            /* Check if we currently have this app downloaded */
            app_sent = zhash_table_lookup(client->apps_sent, domains->domains[i], strlen(domains->domains[i]), NULL);
            if (app_sent) {
                ZPN_BROKER_ASSERT_HARD((app_sent->magic == CLIENT_APP_CACHE_MAGIC), "App cache is corrupted for client: %s, domain: %s, for customer: %"PRId64, client->tunnel_id, domains->domains[i], client->customer_gid);
                /* memcmp is faster than comparing indiviual ranges, ok to increase fn stack size and do local copy */
                tcp_port_count = app_sent->tcp_port_ranges_count;
                udp_port_count = app_sent->udp_port_ranges_count;
                int j = 0;
                memset(tcp_port_array, 0, sizeof(tcp_port_array));
                memset(udp_port_array, 0, sizeof(udp_port_array));
                for (j = 0; j < tcp_port_count; j++) {
                    tcp_port_array[j] = app_sent->tcp_port_ranges[j];
                }
                for (j = 0; j < udp_port_count; j++) {
                    udp_port_array[j] = app_sent->udp_port_ranges[j];
                }
                if (ports_diff(accumulate->udp_ports_array, accumulate->udp_port_count, udp_port_array, udp_port_count) ||
                    ports_diff(accumulate->tcp_ports_array, accumulate->tcp_port_count, tcp_port_array, tcp_port_count) ||
                    (accumulate->double_encrypt != app_sent->double_encrypt) ||
                    (accumulate->ip_anchored != app_sent->ip_anchored) ||
                    (zpn_client_static_config[client->client_type].zia_inspection && (accumulate->inspected != app_sent->inspected)) ||
                    (accumulate->bypass_on_reauth != app_sent->bypass_on_reauth) ||
                    strcmp(accumulate->bypass_type, zpn_broker_client_app_bypass_type_enum_to_string(app_sent->bypass_type, client->customer_gid)) ||
                    (accumulate->enabled == 0) ||
                    strcmp(accumulate->icmp_access_type, zpn_broker_client_app_icmp_access_type_enum_to_string(app_sent->icmp_access_type, client->customer_gid))) {
                    different = 1;
                }
            } else {
                /* We didn't send this app before */
                if (accumulate->udp_port_count ||
                    accumulate->tcp_port_count ||
                    app_override ||
                    accumulate->bypass ||
                    (strcasecmp(accumulate->icmp_access_type, "PING") == 0) ||
                    (strcasecmp(accumulate->icmp_access_type, "PING_TRACEROUTING") == 0)) {
                    different = 1;
                }
            }
            if (different) {
                ZPN_DEBUG_MTN("%s: Inform_one_client, domain = %s, different! Send it!", client->debug_str, domains->domains[i]);

                /* Need to send new stuff. Either deleted or updated */
                memset(&new_app, 0, sizeof(new_app));
                new_app.app_domain = domains->domains[i];
                new_app.deleted = is_app_deleted(accumulate);
                if (accumulate->enabled) {
                    if (accumulate->udp_port_count || accumulate->tcp_port_count) {
                        /* TCP ports... */
                        new_app.tcp_port_ranges = accumulate->tcp_ports_array;
                        new_app.tcp_port_ranges_count = accumulate->tcp_port_count;
                        /* UDP ports... */
                        new_app.udp_port_ranges = accumulate->udp_ports_array;
                        new_app.udp_port_ranges_count = accumulate->udp_port_count;
                    }
                    /* Double encryption config: */
                    new_app.double_encrypt = accumulate->double_encrypt;

                    /* inspected config: */
                    ZPN_APPLICATION_SET_INSPECTION_FLAGS(accumulate->ip_anchored,
                                                         accumulate->inspected,
                                                         new_app.ip_anchored,
                                                         new_app.inspected);

                    new_app.bypass_on_reauth = accumulate->bypass_on_reauth;
                    /* Bypass type (text version, modern) */
                    new_app.bypass_type = accumulate->bypass_type;

                    new_app.icmp_access_type = accumulate->icmp_access_type;

                    /* For backwards compatibility: */
                    new_app.ingress_port_ranges = accumulate->tcp_ports_array;
                    new_app.port_ranges_count = accumulate->tcp_port_count;
                    if (strcmp(new_app.bypass_type, "NEVER")) {
                        new_app.bypass = 1;
                    }
                }
                new_app.has_next = 1;
                if (skip_app(app_sent, &new_app, client)) {
                    if (app_sent) {
                        dump_app(&new_app, client->debug_str, " skip new app noseg: ", 0);
                        zhash_table_remove(client->apps_sent, domains->domains[i], strlen(domains->domains[i]), NULL);
                        zpn_broker_client_app_cache_free(app_sent);
                    }
                    continue;
                }

                /* If we were deleted, and there wasn't such a domain
                 * sent, we will simply not send this one either.. */
                if (new_app.deleted && !app_sent) {
                    /* Do nothing- already not there */
                    dump_app(&new_app, client->debug_str, " skip new app deleted noseg: ", 0);
                } else {
                    if (app_sent) {
                        dump_app(&new_app, client->debug_str, " new app deleted noseg: ", 0);
                        zhash_table_remove(client->apps_sent, domains->domains[i], strlen(domains->domains[i]), NULL);
                        zpn_broker_client_app_cache_free(app_sent);
                    }
                    app_sent_object = argo_object_create(zpn_client_app_description, &new_app);
                    if (!app_sent_object) {
                        ZPN_LOG(AL_CRITICAL, "Could not create object");
                    } else {
                        /* Keep track of what we have sent to the client,
                         * but only if not deleted */
                        if (!new_app.deleted) {
                           /*
                            * ET-46605  There are two use case when we want to
                            * download this application
                            * 1. When there is no feature flag set for restrict app download
                            *    then !restrict_app_download_embedded will always be true
                            * 2. When we have the feature flag set i.e restrict_app_download_embedded = 1
                            *    then check goes to the second || condition
                            *    Max allowed download is checked
                            */
                            if (!restrict_app_download_embedded ||
                                zhash_table_get_size(client->apps_sent) < max_apps_sent) {
                                dump_app(&new_app, client->debug_str, " new app created noseg: ", 0);
                                app_sent = zpn_broker_client_create_app_cache(&new_app, client->customer_gid);
                                zhash_table_store(client->apps_sent, domains->domains[i], strlen(domains->domains[i]), 0, app_sent);
                            } else {
                                do_not_send = 1;

                                // Barrett: ET-62726
                                // Since we are not sending, and we are not going to cache it
                                // We will just let it go
                                #if 0
                                argo_object_release(app_sent_object);
                                app_sent_object = NULL;
                                #endif
                            }
                        }
                        if (!do_not_send) {
                            if (prev_app_sent_object) {
                                inform_one_client_not_segment_send_app(is_new_client, client, prev_app_sent_object, 0, is_app_sent);
                            }
                            prev_app_sent_object = app_sent_object;
                        }
                    }
                }
            } else {
                ZPN_DEBUG_MTN("%s: Inform_one_client, domain = %s, not different!", client->debug_str, domains->domains[i]);
            }
        }
        loop_twice++;
    } while (restrict_app_download_embedded && (zhash_table_get_size(client->apps_sent) < max_apps_sent) && (loop_twice < 2)); // loop twice only if max apps are not downloaded

    if (prev_app_sent_object) {
        ZPN_DEBUG_MTN("%s: Inform_one_client, last app noseg!", client->debug_str);
        inform_one_client_not_segment_send_app(is_new_client, client, prev_app_sent_object, 1, is_app_sent);
    }

    if (client->stats.app_re_download_time_us) {
        //increment stats for re-download case
        //These will be 0 for new clients
        client->stats.app_re_download_time_us = epoch_us() - client->stats.app_re_download_time_us;
        zpn_broker_app_re_download_stats[client->app_thread_id].total_app_re_download_time_us += client->stats.app_re_download_time_us;
        zpn_broker_app_re_download_stats[client->app_thread_id].total_app_re_downloads++;
        if (zpn_broker_app_re_download_stats[client->app_thread_id].max_app_re_download_time_us < client->stats.app_re_download_time_us) {
            zpn_broker_app_re_download_stats[client->app_thread_id].max_app_re_download_time_us = client->stats.app_re_download_time_us;
        }

        if (zpn_broker_app_re_download_stats[client->app_thread_id].min_app_re_download_time_us > client->stats.app_re_download_time_us) {
            zpn_broker_app_re_download_stats[client->app_thread_id].min_app_re_download_time_us = client->stats.app_re_download_time_us;
        }
        client->stats.app_re_download_time_us = 0;
    }

    if (start_us > 0) {
        delta_us = epoch_us() - start_us;
        ZPN_LOG(AL_NOTICE, "diamond_search performance: inform_one_client_not_segment, delta_us=%"PRId64", domains_used=%d, loop_twice=%d, debug_str=%s",
                           delta_us, (int)domains->domains_used, loop_twice, client->debug_str);
    }
}

/* send dummy app to keep ZCC alive */
static void send_dummy_app(struct zpn_client_app_state *client)
{
    struct argo_object *dummy_app_sent_object = argo_object_create(zpn_client_app_description, dummy_app);
    client_hold(client);
    zevent_base_big_call(client->client_fohh_thread, client_app_rx_cb, client, 0,
                         dummy_app_sent_object, NULL, NULL, 1);
    ZPN_DEBUG_CLIENT("%s app dowload keep alive message is sent", client->debug_str);
}

static void free_inclusive_accumulated_domain(void *element, void *cookie)
{
    struct app_accumulate *domain_accumulated = element;
    struct zpn_client_app_state *client = cookie;
    ZPN_DEBUG_MTN("%s: Releasing inclusive accumulated domain %s", client->debug_str, domain_accumulated->domain_name);
    ZPN_FREE(domain_accumulated);
}

/*
 * Update one client about the domains it needs to (re)process.
 *
 * This call only occurs on client->calc_thread (thus the reduced locking)
 *
 * This routine consumes one reference to the domain list it was passed.
 *
 * This routine integrates into policy evaluation as follows:
 *
 * 1. For each application associated with a domain, run the
 *    accumulate_ports function, which actually calls policy
 *
 * 2. Once all ports are accumulated, we have:
 *
 *   A. Set of accumulated ports, from step 1
 *
 *   B. Bypass indication, from step 1
 *
 *   C. Bypass Reauth indication, from step 1
 *
 *   D. Original bypass configuration for the app
 *
 *      i. On net/Off net/Always/Never
 *
 * What we download to the client uses the following algorithm:
 *
 * If original bypass is 'on_net', 'off_net', or 'always', that
 * configuration supercedes all else, and the domain is sent
 * appropriately.
 *
 * If we have accumulated ports {
 *    If bypass_reauth {
 *       send ports, and tell client bypass_reauth
 *    } else {
 *       send ports
 *    }
 * } else {
 *    If bypass_indication {
 *       send bypass
 *    } else {
 *       send nothing (ignore domain / don't download)
 *    }
 * }
 */
static int inform_one_client(struct zpn_client_app_state *client,
                             struct client_domains       *domains,
                             struct client_app_gids      *app_gids,
                             int                          is_new_client)
{
    int res;
    int is_app_sent = 0;
    struct app_accumulate accumulate = {0};
    struct zhash_table *accumulated_inclusive_domains = NULL;

    ZPN_DEBUG_MTN("%s: Inform_one_client, scope gid %ld", client->debug_str, (long)client->scope_gid);

    if (client->deleted) {
        /* This is just a small optimization. If the optimization is
         * missed, we just do more work that will get thrown away */
        ZPN_LOG(AL_DEBUG, "%s: Inform_one_client (deleted - skipping)", client->debug_str);
        if (is_new_client) {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: deleted 2", __LINE__);
        }
        return ZPN_RESULT_ERR;
    }

    /*
     * If appscale is enabled and no_ip_download capability is set
     * no need to download
     */
    int no_domain_download = (client->no_domain_download || client->no_domain_download_v2);
    if (client->no_ip_download &&
        no_domain_download &&
        zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL)) {
        goto end;
    }

    client->stats.client_policy_evaluate_start_us = epoch_us();

    /* Accumulate needs client state because it is processing policy
     * as part of its function. It therefore also needs the policy
     * sets it may need for processing */
    accumulate.client = client;

    res = zpe_get_scope_policy_without_rebuild(client->scope_gid,
                                               zpe_policy_type_bypass,
                                               &(accumulate.bypass_policy_built_default),
                                               &(accumulate.bypass_policy_built),
                                               NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to get default scope policy %ld", (long)client->scope_gid);
        if (is_new_client) {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                     "APP_DOWNLOAD %d: get bypass policy fail", __LINE__);
        }
        return res;
    }

    res = zpe_get_scope_policy_without_rebuild(client->scope_gid,
                                               zpe_policy_type_access,
                                               &(accumulate.access_policy_built_default),
                                               &(accumulate.access_policy_built),
                                               NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to get scope policy %ld", (long)client->scope_gid);
        if (is_new_client) {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                     "APP_DOWNLOAD %d: get access policy fail", __LINE__);
        }
        return res;
    }

    ZPATH_MUTEX_LOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
    if (client->segment_download) {
        inform_one_client_segment(app_gids, &accumulate, is_new_client, &is_app_sent);
    } else {
        accumulated_inclusive_domains = zhash_table_alloc(&zpn_allocator);
        if(accumulated_inclusive_domains == NULL) {
            ZPN_LOG(AL_ERROR, "Failed to allocate memory for inclusive domain table for customer %"PRId64", scope %"PRId64" ",
                               client->customer->customer_gid, client->scope_gid);
            ZPATH_MUTEX_UNLOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
            return ZPN_RESULT_NO_MEMORY;
        }
        inform_one_client_not_segment(domains, &accumulate, is_new_client, &is_app_sent, accumulated_inclusive_domains);
        zhash_table_free_and_call(accumulated_inclusive_domains, free_inclusive_accumulated_domain, client);
    }
    ZPATH_MUTEX_UNLOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
    client->stats.client_policy_evaluate_complete_us = epoch_us();

end:

    /* If this is our first time informing this client, let the client
     * know we are done for now */
    if (!client->first_pass_complete && !client->customer->initializing_testing) {
        client->stats.app_download_debug_str[sizeof(client->stats.app_download_debug_str)-1] = '\0';
        size_t len = strlen(client->stats.app_download_debug_str);
        if (len) {
            char *start = client->stats.app_download_debug_str + len;
            size_t sz = sizeof(client->stats.app_download_debug_str) - len;
            snprintf(start, sz, ", is_app_sent=%d", is_app_sent);
        } else {
            snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                     "APP_DOWNLOAD %d: is_app_sent=%d", __LINE__, is_app_sent);
        }
        client->first_pass_complete = 1;

        zpn_client_tracker_start(client->tracker, client_track_app_complete_cb);

        client_hold(client);
        zevent_base_call(client->client_fohh_thread, client_app_complete_cb, client, 0);
        client->app_complete_cb_in_queue++;
    }

    zthread_heartbeat(NULL);

    return ZPN_RESULT_NO_ERROR;
}


/*
 * do not put customer->lock inside this function, because this may be called from
 * a long list loop protected by the same customer lock, if it holds too long, this may cause
 * heartbeat
 */
static void inform_one_client_cb(struct zevent_base *base,
                                 void               *void_cookie,
                                 int64_t            int_cookie,
                                 void               *extra_cookie1,
                                 void               *extra_cookie2,
                                 void               *extra_cookie3,
                                 int64_t            extra_int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    struct client_domains *domains = extra_cookie1;
    struct client_app_gids *app_gids = extra_cookie2;
    int is_new_client = int_cookie;
    int res;

    client->stats.app_thread_low_priority_dequeue_us = epoch_us();
    base->low_priority_queue_wait_time += client->stats.app_thread_low_priority_dequeue_us - client->stats.app_thread_low_priority_enqueue_us;
    decrease_queue_size(client, 0);

    if (client->inform_one_cb_in_queue > client->stats.max_inform_one_cb_in_queue) {
        client->stats.max_inform_one_cb_in_queue = client->inform_one_cb_in_queue;
    }
    client->inform_one_cb_in_queue--;


    res = inform_one_client(client, domains, app_gids, is_new_client);
    if (!res && et_25587_test) {
        ZPN_DEBUG_MTN("et_25587 inform_one_client wait for app check to complete: %s", client->debug_str);
        et_25587_check_block();
        ZPN_DEBUG_MTN("et_25587 DEQUE: inform_one_client app check complete: %s", client->debug_str);
    }

    if (domains) domains_release(domains);
    if (app_gids) app_gids_release(app_gids);
    client_release(client);
}

static struct client_domains *domains_from_hash(struct zhash_table *table)
{
    struct client_domains *domains;
    int64_t key = 0;
    int res;

    ZPN_DEBUG_MTN("Generating domains from hash");

    domains = ZPN_BCA_CALLOC(sizeof(*domains));
    domains->reference_count = 1;
    if (table) {
        domains->domains_count = zhash_table_get_size(table);
        domains->domains = ZPN_BCA_MALLOC(sizeof(*(domains->domains)) * domains->domains_count);

        res = zhash_table_walk(table,
                               &key,
                               domain_hash_walk,
                               domains);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Too many domains %ld vs %ld", (long)domains->domains_used, (long)domains->domains_count);
            /* We will still process as many as we could... */
        }
    }
    return domains;
}

static struct client_app_gids *gids_from_hash(struct zhash_table *table)
{
    struct client_app_gids *app_gids;
    int64_t key = 0;
    int res;

    ZPN_DEBUG_MTN("Generating gids from hash");

    app_gids = ZPN_BCA_CALLOC(sizeof(*app_gids));
    app_gids->reference_count = 1;
    if (table) {
        app_gids->app_gids_count = zhash_table_get_size(table);
        app_gids->app_gids = ZPN_BCA_MALLOC(sizeof(*(app_gids->app_gids)) * app_gids->app_gids_count);

        res = zhash_table_walk(table,
                               &key,
                               app_gid_hash_walk,
                               app_gids);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Too many app_gids %ld vs %ld", (long)app_gids->app_gids_used, (long)app_gids->app_gids_count);
            /* We will still process as many as we could... */
        }
    }
    return app_gids;
}

static void free_gid_set_deferred(void *c1, void *c2)
{
    struct zhash_table *hash_table = c1;
    app_gid_gids_free(hash_table);
}

struct free_sent_apps_cookie {
    char *debug_str;
    int   segment_download;
};

static void free_sent_apps(void *element, void *cookie)
{
    struct free_sent_apps_cookie *tmp_free_sent_apps_cookie = cookie;
    char                         *debug_str = tmp_free_sent_apps_cookie->debug_str;
    int                           segment_download = tmp_free_sent_apps_cookie->segment_download;

    if (segment_download) {
        struct argo_object *object = element;
        struct zpn_application *app = object->base_structure_void;
        ZPN_DEBUG_MTN("%s: Releasing app %"PRId64, debug_str, app->gid);
        argo_object_release(object);
    } else {
        struct zpn_client_app_cache *app_cache = element;
        zpn_broker_client_app_cache_free(app_cache);
    }
}



static void free_sent_apps_asynch(struct zevent_base *base,
                                  void               *void_cookie,
                                  int64_t            int_cookie,
                                  void               *extra_cookie1,
                                  void               *extra_cookie2,
                                  void               *extra_cookie3,
                                  int64_t            extra_int_cookie)
{
    struct free_sent_apps_cookie tmp_free_sent_app_cookie;

    tmp_free_sent_app_cookie.debug_str = extra_cookie1;
    tmp_free_sent_app_cookie.segment_download = (int)int_cookie;

    if (!tmp_free_sent_app_cookie.segment_download) {
        ZPN_DEBUG_MTN("%s: Releasing app for non-segment based download",
                      tmp_free_sent_app_cookie.debug_str);
    }
    zhash_table_free_and_call(void_cookie, free_sent_apps, &tmp_free_sent_app_cookie);
    ZPN_FREE((char*)extra_cookie1); // free the debug_str
}

static void inform_all_clients_after_app_scaling_disable(struct zevent_base *zbase, void *cookie, int64_t int1_cookie, void *cplat_cookie1, void *extra_cookie2, void *extra_cookie3, int64_t int2_cookie)
{
    struct customer_state *customer = cookie;
    struct client_domains *domains;
    struct client_app_gids *app_gids;
    struct zpn_client_app_state *client;
    struct zhash_table *tmp_table;
    struct zpn_lib_appscale_client_cookie *cplat_cookie = cplat_cookie1;

    ZPN_DEBUG_CLIENT("disable app scaling for Customer gid %" PRId64, customer->customer_gid);

    app_gids = generate_all_gids_table(customer);
    if (!app_gids) {
        ZPN_LOG(AL_INFO, "%"PRId64" attempted to disable app scaling for customer with no apps on current broker - nothing to do", customer->customer_gid);
        return;
    }

    int app_scale = zpn_is_app_scaling_enabled(customer->customer_gid);
    tmp_table = generate_all_domains_table(customer, 0, 0, app_scale);
    if (!tmp_table) {
        ZPN_LOG(AL_WARNING, "%"PRId64" attempted to disable app scaling table creation failed giving up", customer->customer_gid);
        app_gids_release(app_gids);
        return;
    }

    domains = domains_from_hash(tmp_table);
    zhash_table_free(tmp_table);

    if (!domains) {
        ZPN_LOG(AL_WARNING, "%"PRId64" attempted to disable app scaling domains list creation failed giving up", customer->customer_gid);
        app_gids_release(app_gids);
        return;
    }


    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    TAILQ_FOREACH(client, &(customer->clients), list) {

        /* Function of notifying the client for appscaling is disabled
         * only matters for the clients which has send us either the no_domain_download or no_ip_download
         * capability hence this check
         */
        int no_domain_download = (client->no_domain_download || client->no_domain_download_v2);
        if ((0 == no_domain_download) && (0 == client->no_ip_download)) {
            continue;
        }

        /*
         * Per client appscaling feature flag is added for example android
         * If android feature flag changes, then we need to notify only
         * android clients of this change, hence this check
         */
        if (0 == zpn_broker_client_apps_send_to_specific_client(cplat_cookie, client, NULL)) {
            continue;
        }
        ZPN_LOG(AL_DEBUG, "Customer %ld: inform_all_clients: Call client %s",
                          (long)customer->customer_gid, client->debug_str);

        if (et_25587_test) {
            ZPN_DEBUG_MTN("et_25587 ENQUE: Inform_one_client %s", client->debug_str);
        }

        /*
         * Flush the apps_sent table, the client->apps_sent count may be large, we need to flush it
         * asynchrously to avoid heartbeat in case there are too many clients
         */
        ZPATH_MUTEX_LOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
        struct zhash_table *apps_to_free = client->apps_sent;
        client->apps_sent = zhash_table_alloc(&zpn_allocator);
        ZPATH_MUTEX_UNLOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
        char *debug_str = ZPN_STRDUP(client->debug_str, strlen(client->debug_str));
        CLIENT_ADD_BIG_ZEVENT(customer->calc_thread, free_sent_apps_asynch, apps_to_free, client->segment_download, debug_str, NULL, NULL, 0);
        increase_queue_size(client, 0);
        client_hold(client);
        domains_hold(domains);
        app_gids_hold(app_gids);
        client->stats.app_thread_low_priority_enqueue_us = epoch_us();
        CLIENT_ADD_BIG_ZEVENT(client->calc_thread, inform_one_client_cb, client, 0, domains, app_gids, NULL, 0);
        client->inform_one_cb_in_queue++;
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    domains_release(domains);
    app_gids_release(app_gids);
    ZPN_BCA_FREE(cplat_cookie);
}

/*
 * Let all clients of this customer about all the domains that have been accumulated
 *
 * ALWAYS called with customer->lock held.
 */
static void inform_all_clients(struct customer_state *customer, int64_t scope_gid)
{
    struct client_domains *domains;
    struct client_app_gids *app_gids;
    struct zpn_client_app_state *client;
    struct zhash_table *new_gid_sets;
    int incomplete = 0;

    /*
     * If we are waiting for either app initializing or policy
     * initializing, we defer this call until those complete
     */
    if (customer->initializing_apps) return;
    if (!g_dr_mode_is_active && scope_gid && zhash_table_lookup(customer->scope_set, &scope_gid, sizeof(scope_gid), 0) == &policy_not_ready){
        return;
    }

    new_gid_sets = app_gid_gids_generate(customer->customer_gid, &incomplete);
    if (incomplete) {
        app_gid_gids_free(new_gid_sets);
    } else {
        zevent_defer(free_gid_set_deferred, customer->app_gid_hashes, NULL, 30l*1000l*1000l);
        customer->app_gid_hashes = new_gid_sets;
    }

    /*
     * Create the set of domains that is being added. We do this by
     * adding all the domains to a reference counted set. We send
     * this set to each client, reference counted once per
     * client. Once the client is done with the set, it releases it.
     *
     * If there has been a policy change, we make sure to add all
     * domains the customer CURRENTLY has to the set that has
     * changed. (The set that has changed can include deleted
     * entries!!!)
     */
    if (customer->policy_change || customer->inform_all_domains) {
        struct zhash_table *tmp_table;
        ZPN_DEBUG_MTN("Customer %ld: inform_all_clients (all domains)", (long)customer->customer_gid);

        /* Mark up all the domains... */
        int app_scale = zpn_is_app_scaling_enabled(customer->customer_gid);
        tmp_table = generate_all_domains_table(customer, 0, 0, app_scale);
        add_hash_to_hash(customer->domain_accumulate, tmp_table);
        zhash_table_free(tmp_table);

        /* Mark up all the GIDs... */
        add_customer_gids_to_hash(customer->gid_accumulate, customer);

        customer->policy_change = 0;
        customer->inform_all_domains = 0;
    } else {
        ZPN_DEBUG_MTN("Customer %ld: inform_all_clients (accumulated domains)", (long)customer->customer_gid);
    }
    domains = domains_from_hash(customer->domain_accumulate);
    app_gids = gids_from_hash(customer->gid_accumulate);

    TAILQ_FOREACH(client, &(customer->clients), list) {
        if (!g_dr_mode_is_active && scope_gid && client->scope_gid != scope_gid) continue;
        if (!g_dr_mode_is_active && zhash_table_lookup(customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0) == &policy_not_ready) {
            continue;
        }

        ZPN_LOG(AL_DEBUG, "Customer %ld: inform_all_clients: Call client %s", (long)customer->customer_gid, client->debug_str);

        if (et_25587_test) {
            ZPN_DEBUG_MTN("et_25587 ENQUE: Inform_one_client %s", client->debug_str);
        }

        client_hold(client);
        if (client->first_pass_complete) {
            increase_queue_size(client, 0);
            client->stats.app_thread_low_priority_enqueue_us = epoch_us();
            client->stats.app_re_download_time_us = epoch_us();
            domains_hold(domains);
            app_gids_hold(app_gids);
            CLIENT_ADD_BIG_ZEVENT(client->calc_thread, inform_one_client_cb, client, 0, domains, app_gids, NULL, 0);
            client->inform_one_cb_in_queue++;
        } else {
            increase_queue_size(client, 1);
            client->stats.app_thread_high_priority_enqueue_us = epoch_us();
            client->stats.app_re_download_time_us = 0;

            zpn_client_tracker_start(client->tracker, client_track_app);
            zpn_client_tracker_start(client->tracker, client_track_app_thread_xfer);

            CLIENT_ADD_ZEVENT_WITH_PRIORITY(client->calc_thread, new_client_cb, client, 0, QUEUE_PRIORITY_HIGH);
            client->new_client_cb_in_queue++;
            int64_t delta = client->re_init_start_us ? epoch_us() - client->re_init_start_us : 0;
            ZPN_LOG(AL_INFO, "client %s has been re-enqueued, delay_us=%"PRId64, client->debug_str, delta);
        }
    }

    /* Free/clear out domain update hash */
    zhash_table_free(customer->domain_accumulate);
    customer->domain_accumulate = zhash_table_alloc(&zpn_broker_client_allocator);
    zhash_table_free(customer->gid_accumulate);
    customer->gid_accumulate = zhash_table_alloc(&zpn_broker_client_allocator);

    domains_release(domains);
    app_gids_release(app_gids);
}

static void customer_register_callback_on_thread(struct zevent_base *event_base,
                                                 void *void_cookie,
                                                 int64_t int_cookie)
{
    struct customer_state *customer = void_cookie;
    int64_t row_count = int_cookie;

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    ZPN_DEBUG_MTN("%ld: CUSTOMER_ASYNC register callback, async %p %d->%d rows = %ld", (long)customer->customer_gid, &(customer->async_count), customer->async_count, customer->async_count - 1, (long)row_count);

    __sync_sub_and_fetch_4(&(customer->async_count), 1);
    if (customer->async_count == 0) {
        /* Customer config load is complete. If this is the FIRST time
         * we hit '0', we trigger all our clients */
        if (customer->initializing_apps) {
            ZPN_DEBUG_MTN("%ld: CUSTOMER_ASYNC register callback, async now zero, setting initializing to zero",
                          (long)customer->customer_gid);
            customer->initializing_apps = 0;
            customer->policy_change = 1; // Will force building all apps
            customer->initializing_connection = 1;
            inform_all_clients(customer, 0);
        } else {
            ZPN_DEBUG_MTN("%ld: CUSTOMER_ASYNC register callback, initializing_apps already 0",
                          (long)customer->customer_gid);
        }

    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

static int customer_register_callback(void *response_callback_cookie,
                                      struct wally_registrant *registrant,
                                      struct wally_table *table,
                                      int64_t request_id,
                                      int row_count)
{
    struct customer_state *customer = response_callback_cookie;
    int res;

    CLIENT_ADD_ZEVENT(res,
                      customer->calc_thread,
                      customer_register_callback_on_thread,
                      customer,
                      row_count);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error: %s", zpn_result_string(res));
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void get_policy_defer(void *cookie1, void *cookie2, int64_t int_cookie)
{
    struct customer_state *customer = cookie1;
    int64_t scope_gid = int_cookie;
    int res;

    /* don't worry about default scope, if this is not a default scope,
     * the default scope policy should have been loaded before customized scope */
    res = zpe_get_policy_without_rebuild(scope_gid,
                                         zpe_policy_type_bypass,
                                         NULL,
                                         NULL,
                                         NULL);
    if (res) {
        if (res == ZPATH_RESULT_NOT_FOUND) {
            /* Expected, often at startup when scope_ready() has not complete policy fetch */
            /* defer 50 msec and retry */
            ZPN_DEBUG_ZPE("%ld %ld: no policy found, type=%d, will retry",
                         (long)customer->customer_gid, (long)scope_gid, (int)zpe_policy_type_bypass);
            zevent_big_defer(get_policy_defer, customer, NULL, scope_gid, ZPN_CUSTOMER_READY_DEFER_US);
        } else {
            ZPN_LOG(AL_CRITICAL, "Customer %ld Scope %ld: Error fetching policy: %s",
                         (long)customer->customer_gid, (long)scope_gid, zpath_result_string(res));
        }
    } else {
        ZPN_LOG(AL_INFO, "customer %ld scope %ld get_policy_defer success", (long)customer->customer_gid, (long)customer->customer_gid);
        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        zhash_table_remove(customer->scope_set, &(scope_gid), sizeof(scope_gid), &policy_not_ready);
        zhash_table_store(customer->scope_set, &(scope_gid), sizeof(scope_gid), 0, &policy_ready);
        customer->policy_change = 1;
        inform_all_clients(customer, scope_gid);

        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    }
}

int64_t zpn_client_app_state_get_scope_gid(void *arg) {
    struct zpn_client_app_state *client = arg;
    return client->scope_gid;
}

char * zpn_client_app_state_get_tunnel_id(void *arg) {
    struct zpn_client_app_state *client = arg;
    return client->tunnel_id;
}

void zpn_client_app_state_release(void *arg) {
    struct zpn_client_app_state *client = arg;
    client_release(client);
}

static void zpn_broker_c2c_ip_feature_update_cb(const int64_t *config_value, int64_t customer_gid)
{
    struct customer_state *customer;
    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZPN_LOG(AL_INFO,"Customer gid %"PRId64" not found in the cache", customer_gid);
    } else {
        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        customer->c2c_ip_feature_status_incarnation++;
        if (*config_value == 0) {
            //Feature is disabled set release all message flag
            customer->c2c_ip_feature_status_release_all = 1;
        }
        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    }
}

static struct customer_state *get_customer_state(int64_t customer_gid, void *cookie)
{
    struct customer_state *customer;
    int need_registering = 0;

    if (customer_gid == 0) {
        ZPN_LOG(AL_ERROR, "Customer_gid == 0");
    }

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZPATH_MUTEX_LOCK(&customers_lock, __FILE__, __LINE__);
        /* Repeat lookup because it could have been added before we got lock */
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_MTN("%ld: State initialize", (long) customer_gid);

            /* if we cannot add callbacks for customer state in zpn_application.c, just error out */
            int res = zpn_add_app_multi_match_callback_funcs(customer_gid, zpn_app_multi_match_for_app_scaling, zpn_app_multi_match_for_app_download);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Failed to add app_multi_match callbacks, customer=%"PRId64, customer_gid);
                ZPATH_MUTEX_UNLOCK(&customers_lock, __FILE__, __LINE__);
                return NULL;
            }

            customer = ZPN_BCA_CALLOC(sizeof(*customer));
            if (customer) {
                customer->lock = ZPATH_MUTEX_INIT;
                customer->stats_lock = ZPATH_MUTEX_INIT;
                customer->customer_gid = customer_gid;
                TAILQ_INIT(&(customer->clients));
                customer->domain_accumulate = zhash_table_alloc(&zpn_broker_client_allocator);
                customer->gid_accumulate = zhash_table_alloc(&zpn_broker_client_allocator);
                customer->app_gid_hashes = zhash_table_alloc(&zpn_broker_client_allocator);
                customer->calc_thread = zevent_get_for_class(ZEVENT_CLASS_APP_CALC);
                customer->scope_set = zhash_table_alloc(&zpn_broker_client_allocator);
                customer->initializing_apps = 1;
                customer->config_update_cb = NULL;
                customer->agg_domain_cb = NULL;
                if (!ZPN_IS_SITEC()) {
                    customer->aae_profile_feature_status = zpn_broker_get_aae_profile_status(customer_gid);
                    customer->policy_re_eval_on_scim_update_feature_status = zpn_broker_get_policy_re_eval_on_scim_update_status(customer_gid);
                    customer->app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_zapp, zpn_platform_type_windows, 1);
                    customer->android_app_scaling_feature_status = customer->android_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_zapp, zpn_platform_type_android, 1);
/*** Disable sipa code until it is supported ***/
#ifdef SIPA_APP_SCALE_CODE_ENABLED
                    customer->sipa_app_scaling_feature_status = customer->sipa_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_ip_anchoring, 0, 1);
#endif
                    customer->c2c_ip_feature_status = zpn_broker_get_c2c_ip_feature_status(customer_gid);
                    customer->dns_txt_query_support_status = zpn_broker_is_dns_txt_query_support_enabled_for_customer(customer_gid);
                    customer->policy_fqdn_to_srv_ip_feature_status = zpn_broker_is_policy_fqdn_to_srv_ip_enabled_for_customer(customer_gid);
                    customer->policy_re_eval_on_posture_chg_feature_status = zpn_broker_is_policy_re_eval_on_posture_change_enabled_for_customer(customer_gid);
                    customer->policy_svcp_re_eval_freq_sec = zpn_broker_get_svcp_re_eval_frequency_sec(customer_gid);
                    customer->policy_svcp_enabled_feature_status = zpn_broker_is_policy_svcp_enabled_for_customer(customer_gid);
                    customer->restrict_app_down_embedded_feature_status = zpn_broker_is_restrict_app_download_embedded_enabled_for_customer(customer_gid);
                    customer->step_up_auth_feature_status = zpn_broker_is_step_up_auth_enabled_for_customer(customer_gid);
                }
                if (ZPN_BROKER_IS_PUBLIC()) {
                    customer->cc_broker_app_scaling_feature_status = customer->cc_broker_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 1);
                    customer->vdi_broker_app_scaling_feature_status = customer->vdi_broker_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_vdi, 0, 1);
                }
                if (ZPN_BROKER_IS_PRIVATE()) {
                    customer->cc_pse_app_scaling_feature_status = customer->cc_pse_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 0);
                    customer->vdi_pse_app_scaling_feature_status = customer->vdi_pse_app_scaling_feature_status_cur = zpn_broker_is_app_scaling_enabled_for_customer(customer_gid, zpn_client_type_vdi, 0, 0);
                }
                customer->inform_all_domains = 0;
                customer->cust_user_risk_status_in_broker = zpn_broker_is_user_risk_enabled_for_broker_customer(customer_gid);
                customer->workload_tag_grp_cust_status = zpn_broker_is_workload_tag_grp_enabled_for_cust(customer_gid);
                customer->current_restrict_app_download_clients = 0;
                customer->total_restrict_app_download_clients = 0;
                customer->max_app_download_ios_value = zpn_broker_get_max_app_download_val_for_embedded(customer_gid, zpn_platform_type_ios);
                customer->max_app_download_android_value = zpn_broker_get_max_app_download_val_for_embedded(customer_gid, zpn_platform_type_android);
                customer->c2c_ip_feature_status_incarnation = 0;
                customer->c2c_ip_feature_status_release_all = 0;

                if (!is_unit_test()) {
                    if (!ZPN_IS_SITEC()) {
                        zpn_register_app_scaling_flag(customer->customer_gid);

                        zpath_config_override_monitor_int(ANDROID_APP_SCALING_FEATURE,
                                                        &(customer->android_app_scaling_feature_status),
                                                        zpn_client_app_scaling_android_feature_flag_changed_cb,
                                                        DEFAULT_ANDROID_APP_SCALING_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

/*** Disable sipa code until it is supported ***/
#ifdef SIPA_APP_SCALE_CODE_ENABLED
                        // SIPA app-scaling, offloads work to other threads
                        zpath_config_override_monitor_int(SIPA_APP_SCALING_FEATURE,
                                                        &(customer->sipa_app_scaling_feature_status),
                                                        zpn_app_scaling_feature_flag_sipa_changed_cb,
                                                        DEFAULT_SIPA_APP_SCALING_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
#endif

                        zpath_config_override_monitor_int(RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
                                                        &(customer->restrict_app_down_embedded_feature_status),
                                                        NULL,
                                                        DEFAULT_RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_IOS,
                                                        &(customer->max_app_download_ios_value),
                                                        NULL,
                                                        DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_ANDROID,
                                                        &(customer->max_app_download_android_value),
                                                        NULL,
                                                        DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(POLICY_RE_EVAL_ON_POSTURE_CHANGE,
                                                        &(customer->policy_re_eval_on_posture_chg_feature_status),
                                                        NULL,
                                                        DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(POLICY_SVCP_RE_EVAL_FREQUENCY_SEC,
                                                        &(customer->policy_svcp_re_eval_freq_sec),
                                                        NULL,
                                                        DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(POLICY_SVCP_ENABLE,
                                                        &(customer->policy_svcp_enabled_feature_status),
                                                        NULL,
                                                        DEFAULT_POLICY_SVCP_ENABLE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(DNS_TXT_QUERY_SUPPORT_FEATURE,
                                                        &(customer->dns_txt_query_support_status),
                                                        NULL,
                                                        DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(POLICY_FQDN_TO_SERV_IP_ENABLE,
                                                        &(customer->policy_fqdn_to_srv_ip_feature_status),
                                                        NULL,
                                                        DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(ZPN_BROKER_STEP_UP_AUTH_FEATURE_ENABLED,
                                                        &(customer->step_up_auth_feature_status),
                                                        NULL,
                                                        ZPN_BROKER_STEP_UP_AUTH_FEATURE_DEFAULT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(APP_SCALING_BYPASS_IMPROVEMENT_FEATURE,
                                                        &(customer->app_scaling_bypass_improvement_feature_status),
                                                        NULL,
                                                        DEFAULT_APP_SCALING_BYPASS_IMPROVEMENT_FEATURE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

                        zpath_config_override_monitor_int(CONFIG_FEATURE_AAE_PROFILE_FEATURE,
                                                        &(customer->aae_profile_feature_status),
                                                        NULL,
                                                        CONFIG_FEATURE_AAE_PROFILE_FEATURE_DEFAULT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
                        zpath_config_override_monitor_int(CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE,
                                                        &(customer->policy_re_eval_on_scim_update_feature_status),
                                                        NULL,
                                                        CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
                    }

                    if (ZPN_BROKER_IS_PUBLIC()) {
                        /* User risk feature for broker requires monitoring
                         * ONLY for customer gid
                         * Check for reference: ET-57955, ET-59832
                         */
                        zpath_config_override_monitor_int(BROKER_CUSTOMER_USER_RISK_FEATURE_ENABLED,
                                                          &(customer->cust_user_risk_status_in_broker),
                                                          NULL,
                                                          DEFAULT_BROKER_CUSTOMER_USER_RISK_FEATURE,
                                                          customer_gid,
                                                          (int64_t)0); // DO NOT ADD GLOBAL_CONFIG_OVERRIDE_GID

                        zpath_config_override_monitor_int(BROKER_CC_APP_SCALING_FEATURE,
                                                          &(customer->cc_broker_app_scaling_feature_status),
                                                          zpn_app_scaling_cc_feature_flag_changed_cb,
                                                          DEFAULT_BROKER_CC_APP_SCALING_FEATURE,
                                                          customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);

                        zpath_config_override_monitor_int(BROKER_VDI_APP_SCALING_FEATURE,
                                                          &(customer->vdi_broker_app_scaling_feature_status),
                                                          zpn_app_scaling_vdi_feature_flag_changed_cb,
                                                          DEFAULT_BROKER_VDI_APP_SCALING_FEATURE,
                                                          customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);
                    }

                    if (!ZPN_IS_SITEC() && !(ZPN_BROKER_IS_PRIVATE() && g_dr_mode_is_active)) {
                        zpath_config_override_monitor_int(C2C_IP_FEATURE_CUSTOMER,
                                                          &(customer->c2c_ip_feature_status),
                                                          zpn_broker_c2c_ip_feature_update_cb,
                                                          C2C_IP_DEFAULT_CUSTOMER_FEATURE,
                                                          customer_gid,
                                                          (int64_t)0);
                        ZPN_LOG(AL_NOTICE, "c2c_ip_feature flag monit is set for customer %" PRId64, customer_gid);

                        zpath_config_override_monitor_int(CONFIG_WORKLOAD_TAG_GRP_FEATURE,
                                                        &(customer->workload_tag_grp_cust_status),
                                                        NULL,
                                                        CONFIG_FEATURE_WORKLOAD_TAG_GRP_DEFAULT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
                    }
                    if (ZPN_BROKER_IS_PRIVATE()) {
                        zpath_config_override_monitor_int(PSE_CC_APP_SCALING_FEATURE,
                                                          &(customer->cc_pse_app_scaling_feature_status),
                                                          zpn_app_scaling_cc_feature_flag_changed_cb,
                                                          DEFAULT_PSE_CC_APP_SCALING_FEATURE,
                                                          customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);

                        zpath_config_override_monitor_int(PSE_VDI_APP_SCALING_FEATURE,
                                                          &(customer->vdi_pse_app_scaling_feature_status),
                                                          zpn_app_scaling_vdi_feature_flag_changed_cb,
                                                          DEFAULT_PSE_VDI_APP_SCALING_FEATURE,
                                                          customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);
                    }
                }
                /* We don't want to register with our lock held, as
                 * the callbacks may happen synchronously. */
                need_registering = 1;

                zhash_table_store(customers, &customer_gid, sizeof(customer_gid), 0, customer);
            }
            ZPN_DEBUG_MTN("Customer:%ld State initialized successfully", (long) customer_gid);
        }
        ZPATH_MUTEX_UNLOCK(&customers_lock, __FILE__, __LINE__);
    }

    if (need_registering) {
        /* Register for all apps for the customer. This is done to
         * make our full domain/app/group map */
        int res;
        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        ZPN_DEBUG_MTN("%ld: CUSTOMER_ASYNC Registering for apps %d->%d", (long) customer_gid, customer->async_count, customer->async_count + 1);
        customer->async_count++;
        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
        res = zpn_broker_client_apps_app_register(customer_gid,
                                                  customer_register_callback,
                                                  customer);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                ZPN_DEBUG_CLIENT("Customer %ld registered apps %d->%d",
                                 (long) customer_gid, customer->async_count - 1, customer->async_count);
            } else {
                ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
                ZPN_LOG(AL_CRITICAL, "%ld: CUSTOMER_ASYNC could not register for apps %d->%d: %s",
                        (long) customer_gid, customer->async_count, customer->async_count - 1, zpath_result_string(res));
                customer->async_count--;
                ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
            }
        } else {
            ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
            ZPN_LOG(AL_CRITICAL, "%ld: CUSTOMER_ASYNC registered apps %d->%d",
                    (long) customer_gid, customer->async_count, customer->async_count - 1);
            customer->async_count--;
            ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
        }

        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        ZPN_LOG(AL_DEBUG, "%ld: Registering for app domains %d->%d", (long) customer_gid, customer->async_count, customer->async_count + 1);
        customer->async_count++;
        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
        res = zpn_broker_client_apps_app_domain_register(customer_gid,
                                                         customer_register_callback,
                                                         customer);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                ZPN_DEBUG_CLIENT("Customer %ld registered app domains %d->%d",
                                 (long) customer_gid, customer->async_count - 1, customer->async_count);
            } else {
                ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
                ZPN_LOG(AL_CRITICAL, "Customer %ld could not register for app domains %d->%d: %s",
                        (long) customer_gid, customer->async_count, customer->async_count - 1, zpath_result_string(res));
                customer->async_count--;
                ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
            }
        } else {
            ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
            ZPN_LOG(AL_CRITICAL, "Customer %ld registered app domains %d->%d",
                    (long) customer_gid, customer->async_count, customer->async_count - 1);
            customer->async_count--;
            ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
        }
    }

    if (cookie) {
        struct zpn_client_app_state *client = cookie;
        if (!zhash_table_lookup(customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0)) {
            ZPN_DEBUG_MTN("%ld %ld: Retrieving policies (prefetch)", (long)customer_gid, (long)client->scope_gid);
            ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
            zhash_table_store(customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0, &policy_not_ready);
            ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
            zevent_big_defer(get_policy_defer, customer, NULL, client->scope_gid, 0);
        }
    }

    return customer;
}

static void inform_all_clients_after_multi_match_toggled(struct zevent_base *zbase, void *cookie, int64_t incarnation)
{
    struct customer_state *customer = cookie;
    struct client_domains *domains;
    struct client_app_gids *app_gids;
    struct zpn_client_app_state *client;
    struct zhash_table *tmp_table;

    app_gids = generate_all_gids_table(customer);
    int app_scale = zpn_is_app_scaling_enabled(customer->customer_gid);
    tmp_table = generate_all_domains_table(customer, 0, 0, app_scale);
    domains = domains_from_hash(tmp_table);
    zhash_table_free(tmp_table);

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    TAILQ_FOREACH(client, &(customer->clients), list) {

        int no_domain_download_val = (client->no_domain_download || client->no_domain_download_v2);
        int app_scale_client_config = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
        if (!client->no_ip_download && !no_domain_download_val && app_scale_client_config) {
            ZPN_DEBUG_CLIENT("MULTI-MATCH, Customer %"PRId64", skip app download for client %s", customer->customer_gid, client->debug_str);
            continue;
        }

        ZPN_DEBUG_CLIENT("MULTI-MATCH, Customer %"PRId64", process app download for client %s", customer->customer_gid, client->debug_str);

        if (et_25587_test) {
            ZPN_DEBUG_MTN("et_25587 ENQUE: Inform_one_client %s", client->debug_str);
        }

        increase_queue_size(client, 0);
        client_hold(client);
        domains_hold(domains);
        app_gids_hold(app_gids);
        client->stats.app_thread_low_priority_enqueue_us = epoch_us();
        CLIENT_ADD_BIG_ZEVENT(client->calc_thread, inform_one_client_cb, client, 0, domains, app_gids, NULL, 0);
        client->inform_one_cb_in_queue++;
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    domains_release(domains);
    app_gids_release(app_gids);
}

void zpn_app_multi_match_for_app_scaling(int64_t customer_gid)
{
    struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZPN_DEBUG_CLIENT("MULTI-MATCH, Customer gid %"PRId64" not found, skipping notification", customer_gid);
        return;
    }

    if (zpn_is_app_scaling_enabled(customer->customer_gid)) {
        if (customer->config_update_cb) {
            /* notify clients to drop app_dns_check cache if they support app_scaling */
            (customer->config_update_cb)(customer_gid, NULL);
            ZPN_DEBUG_CLIENT("MULTI-MATCH, send zpn_client_config_updated message to the customer %"PRId64, customer_gid);
        } else {
            ZPN_LOG(AL_WARNING, "MULTI-MATCH, config_update_cb not set, customer=%"PRId64, customer_gid);
        }
    } else {
        ZPN_DEBUG_CLIENT("MULTI-MATCH, app_scaling is not enabled to customer %"PRId64, customer_gid);
    }
}

void zpn_app_multi_match_for_app_download(int64_t customer_gid)
{
    struct customer_state *customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZPN_DEBUG_CLIENT("MULTI-MATCH, Customer gid %"PRId64" not found, skipping notification", customer_gid);
        return;
    }

    /*
     * now re-download applications to the clients if they do not support app_scaling or if the
     * app scaling feature is not enabled
     */
     ZPN_DEBUG_CLIENT("MULTI-MATCH, re-download applications for the customer %"PRId64, customer_gid);
     CLIENT_ADD_ZEVENT(customer->calc_thread, inform_all_clients_after_multi_match_toggled, customer, 0);
}

void zpn_add_app_scaling_callback_funcs(int64_t customer_gid, void *config_cb, void *agg_domain_cb)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    /* coverity[thread2_checks_field_early : FALSE] */
    if (!customer->config_update_cb && config_cb) {
        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        if (!customer->config_update_cb) {
            customer->config_update_cb = config_cb;
            ZPN_LOG(AL_NOTICE, "Customer %ld config_update_cb is set", (long)customer->customer_gid);
        }
        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    }

    if (!customer->agg_domain_cb && agg_domain_cb) {
        ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
        if (!customer->agg_domain_cb) {
            customer->agg_domain_cb = agg_domain_cb;
            ZPN_LOG(AL_NOTICE, "Customer %ld agg_domain_cb is set", (long)customer->customer_gid);
        }
        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    }
}

void customer_timer(int sock, short flags, void *cookie)
{
    struct timmer_parameter *timmer_param = cookie;
    struct customer_state *customer = timmer_param->customer;
    int64_t scope_gid = timmer_param->scope_gid;
    ZPN_BCA_FREE(timmer_param);

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    ZPN_DEBUG_MTN("Customer %ld: App timer fired", (long) customer->customer_gid);

    customer->timeout_occurred = 1;

    if (customer->timer) {
        event_free(customer->timer);
        customer->timer = NULL;
    }

    /* if it is default scope, we notify all clients because default scope data apply to all other scopes*/
    if (customer->customer_gid == scope_gid) scope_gid = 0;
    inform_all_clients(customer, scope_gid);

    /* do not send config_update for first connection time */
    if (customer->initializing_connection == 0) {
        if (customer->config_update_cb) {
            (customer->config_update_cb)(customer->customer_gid, NULL);
        } else {
            ZPN_LOG(AL_WARNING, "Customer %ld config_update_cb is not set", (long)customer->customer_gid);
        }
    } else {
        customer->initializing_connection = 0;
    }


    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

/*
 * Must have lock held when called
 */
static void refresh_timer(struct customer_state *customer, int64_t scope_gid)
{
    struct timeval tv;
    struct timmer_parameter *timmer_param;

    if (customer->initializing_apps) {
        ZPN_DEBUG_MTN("Customer %ld: App timer defer (initializing)", (long) customer->customer_gid);
        return;
    }

    if (!customer->timer) {
        timmer_param = ZPN_BCA_CALLOC(sizeof(*timmer_param));
        timmer_param->customer = customer;
        timmer_param->scope_gid = scope_gid;

        customer->timer = event_new(zevent_event_base(customer->calc_thread), -1, 0, customer_timer, timmer_param);
        ZPN_DEBUG_MTN("Customer %ld: App timer create", (long) customer->customer_gid);
    } else {
        ZPN_DEBUG_MTN("Customer %ld: App timer push", (long) customer->customer_gid);
    }

    /* This will schedule or reschedule the timer into the future */
    tv.tv_usec = recalc_push_us % 1000000;
    tv.tv_sec = recalc_push_us / 1000000;
    event_add(customer->timer, &tv);
}

/*
 * Update current state for this domain + app_gid
 *
 * This updates both hash tables for the customer.
 */
static void app_domain_update(struct customer_state *customer, char *domain_name, int inform_all_domains)
{
    size_t domain_name_len = strlen(domain_name);
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    // Start printing this log after 30 min from broker uptime
    if (g_app_debug_log) {
        ZPN_DEBUG_APPLICATION("Customer %ld, domain update for domain %s", (long) customer->customer_gid, domain_name);
    }

    if (!customer->inform_all_domains) {
        customer->inform_all_domains = inform_all_domains;
    }
    /* If we don't have a timer, we need to create one. */
    refresh_timer(customer, 0);

    /* Remember that this domain has been touched. */
    if (!zhash_table_lookup(customer->domain_accumulate, domain_name, domain_name_len, NULL)) {
        zhash_table_store(customer->domain_accumulate, domain_name, domain_name_len, 0, customer);
    }

    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

static void app_gid_update(struct customer_state *customer, int64_t app_gid, int inform_all_domains)
{
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    // Start printing this log after 30 min from broker uptime
    if (g_app_debug_log) {
        ZPN_LOG(AL_DEBUG, "Customer %ld, app_gid_update for gid %ld",
                (long) customer->customer_gid, (long) app_gid);
    }

    if (!customer->inform_all_domains) {
        customer->inform_all_domains = inform_all_domains;
    }
    /* If we don't have a timer, we need to create one. */
    refresh_timer(customer, 0);

    /* Remember that this domain has been touched. */
    if (!zhash_table_lookup(customer->gid_accumulate, &app_gid, sizeof(app_gid), NULL)) {
        zhash_table_store(customer->gid_accumulate, &app_gid, sizeof(app_gid), 0, customer);
    }

    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

/*
 * In case of exclusive app update (i.e.old and new app both are exclusive or new app is exclusive)
 * function will return 1 otherwise 0
 */
static int is_exclusive_app_update(struct zpn_application *old_app, struct zpn_application *new_app)
{
    if(old_app && new_app) {
        old_app->m_style =  zpath_match_style_string_to_enum(old_app->match_style);
        if(new_app->m_style == zpn_match_style_exclusive && old_app->m_style == zpn_match_style_exclusive) {
                return 1;
        }
    } else if(new_app && (new_app->m_style == zpn_match_style_exclusive)) {
        return 1;
    }

    return 0;
}

void application_row_callback_deferred(void *void_cookie1, void *void_cookie2)
{
    struct argo_object *row = void_cookie1;
    struct argo_object *old_row = void_cookie2;
    struct zpn_application *app = row->base_structure_void;
    struct customer_state *customer;
    int64_t customer_gid = app->customer_gid;
    int res;
    int i;

    customer = get_customer_state(customer_gid, NULL);

    ZPN_DEBUG_MTN("Customer %ld: Application row arrived, app gid = %ld", (long) customer_gid, (long) app->gid);

    /* Make sure we're registered for the appropriate relation (so we
     * can fetch group membership of apps) */
    if (app->deleted) {
        /* We leave registrations in place, since they don't really hurt and there aren't really that many */
    } else {
        /* If not registered, register. Otherwise ignore */
        if (zpn_broker_client_apps_relation_is_registered(app->gid) == ZPATH_RESULT_NOT_FOUND) {
            res = zpn_broker_client_apps_relation_register(app->gid,
                                                           customer_register_callback, // wally_response_callback_f *response_callback,
                                                           customer); // void *response_callback_cookie
            if (res) {
                if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                    ZPN_LOG(AL_ERROR, "Could not register for row");
                } else {
                    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
                    ZPN_DEBUG_MTN("%ld: CUSTOMER_ASYNC register for app relation %d->%d",
                            (long) customer_gid, customer->async_count, customer->async_count + 1);
                    customer->async_count++;
                    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
                }
            }
        }
    }

    /* We remove all old domains if they are there, and re-add any currently existing ones */
    int inform_all_domains = 0;
    int is_app_multi_match_enabled = is_app_multi_match_feature_enabled(customer_gid);
    if (is_app_multi_match_enabled) {
        inform_all_domains = 1;
        app->m_style = zpath_match_style_string_to_enum(app->match_style);
    }
    if (old_row) {
        ZPN_DEBUG_MTN("Customer %ld: Application row arrived, app gid = %ld, processing old row!", (long) customer_gid, (long) app->gid);
        struct zpn_application *old_application = old_row->base_structure_void;
        if(is_app_multi_match_enabled && is_exclusive_app_update(old_application, app)) {
            inform_all_domains = 0;
        }
        for (i = 0; i < old_application->domain_names_count; i++) {
            app_domain_update(customer, old_application->domain_names[i], inform_all_domains);
        }
        app_gid_update(customer, old_application->gid, inform_all_domains);
        argo_object_release(old_row);
    } else if (is_app_multi_match_enabled && is_exclusive_app_update(NULL, app)) {
        inform_all_domains = 0;
    }
    for (i = 0; i < app->domain_names_count; i++) {
        app_domain_update(customer, app->domain_names[i], inform_all_domains);
    }
    app_gid_update(customer, app->gid, inform_all_domains);

    argo_object_release(row);
}

static int application_row_callback(void *response_callback_cookie,
                                    struct wally_registrant *registrant,
                                    struct wally_table *table,
                                    struct argo_object *previous_row,
                                    struct argo_object *row,
                                    int64_t request_id)
{
    /*
     * It is important to watch for domains removed from
     * applications. The only way we see those is if we track domains
     * from the previous object for this row.
     */
    zpn_application_row_fixup(row);
    argo_object_hold(row);
    if (previous_row) argo_object_hold(previous_row);
    zevent_defer(application_row_callback_deferred, row, previous_row, 0);
    return ZPATH_RESULT_NO_ERROR;
}


static void relation_row_callback_deferred(void *cookie1, void *cookie2)
{
    /* IMPORTANT: Don't forget to release row */
    struct argo_object *row = cookie1;
    struct zpn_application *app;
    struct zpn_application_group_application_mapping *relation = row->base_structure_void;
    struct customer_state *customer = get_customer_state(relation->customer_gid, NULL);
    int res;
    int i;

    if (relation->deleted) {
        /* We leave registrations in place, since they don't really hurt and there aren't really that many */
    } else {
        /* If not registered, register. Otherwise ignore */
        if (zpn_broker_client_apps_group_is_registered(relation->application_group_id) == ZPATH_RESULT_NOT_FOUND) {
            res = zpn_broker_client_apps_group_register(relation->application_group_id,
                                                        NULL, // wally_response_callback_f *response_callback,
                                                        NULL); // void *response_callback_cookie
            if ((res != ZPATH_RESULT_NO_ERROR) &&
                (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                ZPN_LOG(AL_ERROR, "Could not register for row");
            }
        }
    }

    int is_app_multi_match_enabled = is_app_multi_match_feature_enabled(relation->customer_gid);
    /* Always update the app attached to this group if it exists, in all cases */
    /* coverity[callee_ptr_arith : FALSE] */
    res = zpn_broker_client_apps_get_app_by_gid(relation->application_id,
                                                &app);
    if (res == ZPN_RESULT_NO_ERROR) {
        for (i = 0; i < app->domain_names_count; i++) {
            app_domain_update(customer, app->domain_names[i], is_app_multi_match_enabled);
        }
        app_gid_update(customer, app->gid, is_app_multi_match_enabled);
    }
    argo_object_release(row);
}

static int relation_row_callback(void *response_callback_cookie,
                                 struct wally_registrant *registrant,
                                 struct wally_table *table,
                                 struct argo_object *previous_row,
                                 struct argo_object *row,
                                 int64_t request_id)
{
    struct zpn_application_group_application_mapping *relation = row->base_structure_void;
    if (!relation->customer_gid) {
        relation->customer_gid = ZPATH_GID_GET_CUSTOMER_GID(relation->application_id);
    }
    argo_object_hold(row);
    zevent_defer(relation_row_callback_deferred, row, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;

}

static void group_row_callback_deferred(void *cookie1, void *cookie2)
{
    struct argo_object *row = cookie1;
    struct zpn_application *app;
    struct zpn_application_group_application_mapping *relations[20000];
    size_t relations_count = sizeof(relations) / sizeof(relations[0]);
    struct zpn_application_group *group = row->base_structure_void;
    int res;
    size_t i;
    int j;
    int64_t customer_gid = group->customer_gid;
    struct customer_state *customer = get_customer_state(customer_gid, NULL);

    int is_app_multi_match_enabled = is_app_multi_match_feature_enabled(customer_gid);

    /* All we need to do here is, for each relation, for each app,
     * make sure we have a domain update going... Yes, we need */
    res = zpn_broker_client_apps_get_relation_by_group_gid(group->gid,
                                                           &(relations[0]),
                                                           &relations_count);
    if (res == ZPATH_RESULT_NO_ERROR) {
        for (i = 0; i < relations_count; i++) {
            /* coverity[callee_ptr_arith : FALSE] */
            res = zpn_broker_client_apps_get_app_by_gid(relations[i]->application_id,
                                                        &app);
            /* coverity[check_after_deref : FALSE] */
            if (res == ZPN_RESULT_NO_ERROR) {
                for (j = 0; j < app->domain_names_count; j++) {
                    app_domain_update(customer, app->domain_names[j], is_app_multi_match_enabled);
                }
                if (app) app_gid_update(customer, app->gid, is_app_multi_match_enabled);
            }
        }
    }
    argo_object_release(row);
}

static int group_row_callback(void *response_callback_cookie,
                              struct wally_registrant *registrant,
                              struct wally_table *table,
                              struct argo_object *previous_row,
                              struct argo_object *row,
                              int64_t request_id)
{
    argo_object_hold(row);
    zevent_defer(group_row_callback_deferred, row, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

static void domain_row_callback_deferred(void *cookie1, void *cookie2)
{
    struct argo_object *row = cookie1;
    struct zpn_application_domain *app_domain = row->base_structure_void;
    struct customer_state *customer = get_customer_state(app_domain->customer_gid, NULL);

    int is_app_multi_match_enabled = is_app_multi_match_feature_enabled(app_domain->customer_gid);
    app_domain_update(customer, app_domain->domain_name, is_app_multi_match_enabled);
    app_gid_update(customer, app_domain->application_gid, is_app_multi_match_enabled);
    argo_object_release(row);
}

static int domain_row_callback(void *response_callback_cookie,
                               struct wally_registrant *registrant,
                               struct wally_table *table,
                               struct argo_object *previous_row,
                               struct argo_object *row,
                               int64_t request_id)
{
    argo_object_hold(row);
    zevent_defer(domain_row_callback_deferred, row, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_client_app_customer_policy_update(int64_t scope_gid)
{
    int64_t customer_gid = get_customer_from_scope(scope_gid);
    if (!customer_gid) return ZPATH_RESULT_BAD_ARGUMENT;
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    customer->policy_change = 1;
    refresh_timer(customer, scope_gid);
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_client_app_timer_since_last_call(int64_t customer_gid)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    int ret;
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    ret = customer->timeout_occurred;
    customer->timeout_occurred = 0;
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    return ret;
}


static void policy_changed(int64_t scope_gid)
{
    zpn_broker_client_app_customer_policy_update(scope_gid);
}

static int zpn_application_debug_generate_gids(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    int64_t customer_gid = single_tenant_customer_gid;
    struct zhash_table *app_appgrp_gids = NULL;
    int incomplete = 0;
    if (!customer_gid) {
        if (!query_value_count || !query_values[0]) {
            ZDP("Argument customer is required.");
            return ZPATH_RESULT_ERR;
        }
        char *end_ptr;
        customer_gid  = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
            if (!customer_gid) {
                ZDP("Bad customer ID or Customer domain not provided\n");
                return ZPATH_RESULT_ERR;
            }
        }
        ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    }

    app_appgrp_gids = app_gid_gids_generate(customer_gid, &incomplete);
    if (!app_appgrp_gids) {
        ZDP("Unable to generate app gids for customer %ld, incomplete=%d\n", (long)customer_gid, incomplete);
        return ZPATH_RESULT_ERR;
    }
    ZDP("Generated app gids for customer %ld, count=%ld, incomplete=%d\n", (long)customer_gid, (long)zhash_table_get_size(app_appgrp_gids), incomplete);
    zhash_table_free(app_appgrp_gids);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_application_debug_init_test(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    int64_t customer_gid = 0;
    struct customer_state *customer = NULL;

    if (!query_values[0]) {
        ZDP("Missing argument: customer\n");
        return ZPATH_RESULT_ERR;
    } else {
        customer_gid = strtoul(query_values[0], NULL, 0);
        if (!customer_gid) {
            ZDP("Bad customer gid.");
            return ZPATH_RESULT_ERR;
        }
    }

    customer = get_customer_state(customer_gid, NULL);

    /* This is for testing, only allow set it once to avoid DoS attack */
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    if (!customer->initializing_testing_set) {
        customer->initializing_testing = 1;
        customer->initializing_testing_set = 1;
        ZDP("customer %ld initializing_testing=%d\n", (long)customer_gid, customer->initializing_testing);
    } else {
        ZDP("customer %ld initializing_testing was set once already, not allowed anymore\n", (long)customer_gid);
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * This CURL command is to check given the customer gid if
 * app scaling  (50k application segments) feature
 * is enabled or disabled for a customer
 * Usage: : curl "localhost:8000/zpn/broker/client/check/appscaling/status?customer=<customer-GID/domain-name>"
 */
static int
zpn_broker_client_check_app_scaling_status(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    int64_t customer_gid;
    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\n");
        ZDP("Usage : curl \"localhost:8000/zpn/broker/client/check/appscaling/status?customer=<customer-GID/domain-name>\"\n");
        goto DONE;
    }

    if (!customer_gid) {
        ZDP("Bad customer ID or customer domain not provided\n");
        goto DONE;
    }
    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);

    int enabled = zpn_is_app_scaling_enabled(customer_gid);
    ZDP("Config  %s value is %s\n\n", APP_SCALING_FEATURE, enabled ? "ENABLED" : "DISABLED");
    enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_zapp, zpn_platform_type_android, 1);
    ZDP("Config  %s value is %s\n\n", ANDROID_APP_SCALING_FEATURE, enabled ? "ENABLED" : "DISABLED");

    enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_edge_connector, 0, 1);
    ZDP("Config %s value %s\n", BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 1);
    ZDP("Config %s value %s\n", BROKER_CC_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_edge_connector, 0, 0);
    ZDP("Config %s value %s\n", PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 0);
    ZDP("Config %s value %s\n\n", PSE_CC_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");

    enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_vdi, 0, 1);
    ZDP("Config %s value %s\n", BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_vdi, 0, 1);
    ZDP("Config %s value %s\n", BROKER_VDI_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_vdi, 0, 0);
    ZDP("Config %s value %s\n", PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
    enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_vdi, 0, 0);
    ZDP("Config %s value %s\n\n", PSE_VDI_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");

DONE:
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Given the customer name or id, check if
 * Restricted application download feature is
 * enabled for this customer or not
 */
static int
zpn_broker_client_restrict_app_download_customer(struct zpath_debug_state *request_state,
                                               const char **query_values,
                                               int query_value_count,
                                               void *cookie)
{
    int64_t customer_gid = 0;

    (void) query_value_count;
    (void) cookie;

    if ((!query_values[0] && !query_values[1]) ||
        (query_values[0] && query_values[1])) {
        ZDP("Provide either customer gid or customer name\nUsage "
            "curl localhost:8000/zpn/broker/client/restrictAppDownload/enabled?customer_gid=<customer gid> OR \n"
            "curl localhost:8000/zpn/broker/client/restrictAppDownload/enabled?customer_name=<customer name> \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 0);
        if (!customer_gid) {
            ZDP("Invalid customer gid\n");
            return ZPN_RESULT_NO_ERROR;
        } else {
            /* Find if this is a valid customer GID */
            int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
            if (!shard_index) {
                ZDP("Invalid customer gid %"PRId64" \n", customer_gid);
                return ZPN_RESULT_NO_ERROR;
            }
        }
    }

    if (query_values[1]) {
        customer_gid = zpath_domain_lookup_get_id(query_values[1], strlen(query_values[1]));
        if (!customer_gid) {
            ZDP("Invalid customer name\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    int64_t enabled = zpn_broker_get_restrict_app_download_embedded_status_for_customer(customer_gid);
    if (enabled) {
        ZDP("Restrict app download feature is enabled for ZCC platform iOS and android\n");
    } else {
        ZDP("Restrict app download feature is disabled\n");
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Given the customer id, Get accumulated count of
 * number of clients which had app scaling
 * feature enabled for this customer
 * Note: This is accumulated count, not current count
 */
static int
zpn_broker_client_check_app_scaling_count(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    int64_t customer_gid = 0;
    struct customer_state *customer = NULL;
    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\nUsage "
            "curl localhost:8000/zpn/broker/client/check/appscaling/count?customer=customername\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!customer_gid) {
        ZDP("Bad customer ID or customer domain not provided\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZDP("No clients of this customer is connected\n");
    } else {
        int enabled = zpn_is_app_scaling_enabled(customer->customer_gid);
        ZDP("Config %s value %s\n\n", APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_zapp, zpn_platform_type_android, 1);
        ZDP("Config  %s value is %s\n\n", ANDROID_APP_SCALING_FEATURE, enabled ? "ENABLED" : "DISABLED");

        enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_edge_connector, 0, 1);
        ZDP("Config %s value %s\n", BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 1);
        ZDP("Config %s value %s\n", BROKER_CC_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_edge_connector, 0, 0);
        ZDP("Config %s value %s\n", PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_edge_connector, 0, 0);
        ZDP("Config %s value %s\n\n", PSE_CC_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");

        enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_vdi, 0, 1);
        ZDP("Config %s value %s\n", BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_vdi, 0, 1);
        ZDP("Config %s value %s\n", BROKER_VDI_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_get_app_scaling_hard_disabled_status(zpn_client_type_vdi, 0, 0);
        ZDP("Config %s value %s\n", PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE, enabled ? "ENABLED": "DISABLED");
        enabled = zpn_broker_get_app_scaling_status_for_customer(customer_gid, zpn_client_type_vdi, 0, 0);
        ZDP("Config %s value %s\n\n", PSE_VDI_APP_SCALING_FEATURE, enabled ? "ENABLED": "DISABLED");
        ZDP("Current clients with no domain download capability = %"PRId64"\n"
            "Current clients with no domain download V2 capability = %"PRId64"\n"
            "Current clients with no ip download capability = %"PRId64"\n"
            "Total clients with no domain download capability = %"PRId64"\n"
            "Total clients with no domain download V2 capability = %"PRId64"\n"
            "Total clients with no ip download capability = %"PRId64"\n",
            customer->current_app_scaling_no_domain_download_clients,
            customer->current_app_scaling_no_domain_download_v2_clients,
            customer->current_app_scaling_no_ip_download_clients,
            customer->total_app_scaling_no_domain_download_clients,
            customer->total_app_scaling_no_domain_download_v2_clients,
            customer->total_app_scaling_no_ip_download_clients);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dump_domains(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    int64_t customer_gid = 0;
    int no_domain_download = 0;
    int no_ip_download = 0;
    int max_count = 0;
    struct customer_state *customer = NULL;
    struct zhash_table *join = NULL;
    struct client_domains *domains = NULL;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\nUsage "
            "curl localhost:8000/zpn/domains/dump?customer=<customer name or gid>\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!customer_gid) {
        ZDP("Bad customer ID or customer domain not provided\n");
        return ZPN_RESULT_NO_ERROR;
    }
    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZDP("No clients of this customer is connected\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[1]) no_domain_download = strtol(query_values[1], NULL, 0);
    if (query_values[2]) no_ip_download = strtol(query_values[2], NULL, 0);
    if (query_values[3]) max_count = strtol(query_values[3], NULL, 0);

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    join = generate_all_domains_table(customer, no_domain_download, no_ip_download, 1);
    if (join) {
        add_hash_to_hash(join, customer->domain_accumulate);
        domains = domains_from_hash(join);
        ZDP("Domain list for customer: %"PRId64", domain count: %lu\n", customer_gid, (domains ? domains->domains_used : 0));
        ZDP("-----------------------------------------\n");
        if (domains) {
            int i = 0;
            int count = (max_count > 0 && max_count < domains->domains_used) ? max_count : domains->domains_used;
            for (i = 0; i < count; i++) {
                ZDP("%s\n", domains->domains[i]);
            }
            domains_release(domains);
        }
        ZDP("-----------------------------------------\n");
        zhash_table_free(join);
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;

}

/*
 * Given the customer id, Get accumulated count of
 * number of clients which had restricted app download
 * feature enabled for this customer
 * Note: This is accumulated count, not current count
 */
static int
zpn_broker_client_restrict_app_download_count(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    int64_t enabled = 0;
    int64_t customer_gid = 0;
    struct customer_state *customer = NULL;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\nUsage "
            "curl localhost:8000/zpn/broker/client/restrictAppDownload/count?customer=customername\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!customer_gid) {
        ZDP("Bad customer ID or customer domain not provided\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);

    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZDP("No clients of this customer is connected\n");
    } else {
        enabled = customer->restrict_app_down_embedded_feature_status;
        if (enabled) {
            ZDP("Current clients having restrict app download enabled = %"PRId64"\n", customer->current_restrict_app_download_clients);
            ZDP("Total clients having restrict app download enabled so far = %"PRId64"\n", customer->total_restrict_app_download_clients);
        } else {
            ZDP("Restrict app download feature is disabled\n");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This thread is created to offload the work
 * from config override thread
 * and to notify all the customers in the cloud
 * for change of appscale feature flag,
 * name is kept as APP_MISC and can be used for other tasks as well
 */
static int create_apps_misc_thread_event_base()
{
    static pthread_mutex_t local_apps_misc_lock = PTHREAD_MUTEX_INITIALIZER;
    static struct zevent_base *zbase = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    pthread_mutex_lock(&local_apps_misc_lock);
    if (!zbase) {
        zbase = zevent_handler_create(APP_MISC_THREAD, 16*1024*1024, 30);
        if (zbase) {
            zevent_add_to_class(zbase, APP_MISC_THREAD_POOL);
        } else {
            res = ZPN_RESULT_ERR;
        }
    }

    pthread_mutex_unlock(&local_apps_misc_lock);
    return res;
}

/*
 * 2ms busy loop
 *
 */
static void ut_event_deferred(void *cookie1, void *cookie2)
{
    int64_t start_us = monotime_us();
    while (1) {
        int64_t current_us = monotime_us();
        if (current_us - start_us >= 2000) {
            break;
        }
    }
}

/*
 * to simulate and verify ET-82526 this command is developed
 */
static int
zpn_broker_client_simulate_large_domain_update(struct zpath_debug_state *request_state,
                                               const char **query_values,
                                               int query_value_count,
                                               void *cookie)
{

    int64_t event_cnt = 0;
    if (query_values[0]) {
        event_cnt = strtoul(query_values[0], NULL, 0);
        if (!event_cnt) {
            ZDP("Invalid Event count");
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        ZDP("Event count is required!\nUsage "
            "curl localhost:8000/zpn/broker/simulate/domainUpdate?event_cnt=<num of events>\n");
        return ZPN_RESULT_NO_ERROR;
    }

    /*
     * this will enqueue event_cnt into evbase
     * each event will take 2ms to finish
     */
    for (int i = 0; i < event_cnt; i++) {
        zevent_defer(ut_event_deferred, NULL, NULL, 0);
    }
    return ZPN_RESULT_NO_ERROR;
}

int app_re_download_stats_init() {
    int res, i;
    struct zpn_broker_client_app_re_download_stats *app_re_download_stats;
    zpn_broker_client_app_re_download_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_APP_RE_DOWNLOAD_STATS_HELPER);

    for (i=0; i<BROKER_APP_CALC_THREADS_DEFAULT; i++) {
        zpn_broker_app_re_download_stats[i].min_app_re_download_time_us = UINT64_MAX;
    }

    app_re_download_stats = ZPN_BCA_CALLOC(sizeof(*app_re_download_stats));
    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                     "zpn_broker_client_app_re_download_stats",
                                     AL_INFO,
                                     60*1000*1000,    /* 1 minute */
                                     zpn_broker_client_app_re_download_stats_description,
                                     app_re_download_stats,
                                     1,
                                     zpn_broker_client_app_re_download_stats_fill,
                                     NULL)) {
        ZPN_LOG(AL_CRITICAL, "Could not register broker app re_download stats - statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump app redownload stats",
                                  "/zpn/broker/client/app_redownload_stats",
                                  zpn_broker_client_app_re_download_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/restrictAppDownload/count");
    }

    return res;
}

static int
zpn_broker_client_policy_re_eval_on_posture_chg_status(struct zpath_debug_state *request_state,
                                                       const char **query_values,
                                                       int query_value_count,
                                                       void *cookie)
{
    int64_t customer_gid = 0;
    const char *customer_str = query_values[0];

    if (!customer_str) {
        ZDP("Require argument customer (customer_gid)");
        return ZPATH_RESULT_ERR;
    }

    customer_gid = strtoul(customer_str, NULL, 0);
    if (customer_gid == 0) {
        ZDP("Invalid customer_gid: %"PRId64, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    struct customer_state *customer = get_customer_state(customer_gid, NULL);

    int64_t is_enabled = zpn_broker_policy_re_eval_on_posture_change_is_enabled(customer_gid);
    int64_t is_hard_disabled = zpn_broker_policy_re_eval_on_posture_change_is_disabled();
    int64_t is_feature_flag_enabled = customer->policy_re_eval_on_posture_chg_feature_status;
    ZDP("Policy re-eval on Posture change is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_enabled == 0)?"disabled":"enabled"), customer_gid, is_enabled);
    ZDP("Policy re-eval on Posture change %s hard disabled for all customers with value: %"PRId64"\n",  is_hard_disabled? "is" : "is not", is_hard_disabled);
    ZDP("Policy re-eval on Posture change feature flag is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_feature_flag_enabled == 0)?"disabled":"enabled"), customer_gid, is_feature_flag_enabled);

    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_broker_app_thread_info(struct zpath_debug_state *request_state,
                           const char **query_values,
                           int query_value_count,
                           void *cookie)
{
    ZDP("App thread libevent priority is %s \n", ((zpn_broker_is_libevent_priority_enabled() == 0) ? "disabled" : "enabled"));
    ZDP("Total App threads in thread pool %d\n", zpn_broker_app_calc_threads_count_get());

    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_broker_client_appscale_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_broker_client_apps_app_scale_stats *stats = structure_data;
    int client_type = *((int *)cookie);
    int res = ZPN_RESULT_NO_ERROR;

    if (client_type <  zpn_client_type_invalid || client_type > zpn_client_type_total_count) {
        ZPN_LOG(AL_ERROR, "Unable to add app thread app scale stats");
        return ZPN_RESULT_ERR;
    }

    res = argo_structure_array_add(zpn_broker_client_apps_app_scale_stats_description, &zpn_broker_appscale_stats[client_type],
                                   BROKER_APP_CALC_THREADS_DEFAULT, stats, sizeof(struct zpn_broker_client_apps_app_scale_stats));

    if (res) {
        ZPN_LOG(AL_ERROR, "argo_structure_array_add failed for zpn_broker_client_apps_app_scale_stats with error %s", zpath_result_string(res));
    }
    return res;
}

/* Display app thread appscaling stats */
static int
zpn_broker_client_apps_appscaling_stats_dump(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    char jsonout[10000];
    int client_type_start = zpn_client_type_invalid + 1;
    int client_type_end = zpn_client_type_total_count - 1;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_broker_client_apps_app_scale_stats cumulative_stats;

    if (query_values[0]) {
        int type = atoi(query_values[0]);
        if (type < client_type_start || type > client_type_end) {
            ZDP("Invalid entry, allowed entries are\n");
            for (int i = client_type_start; i < client_type_end; i++) {
                ZDP("%d = %s\n", i, zpn_client_type_string(i));
            }
            return ZPN_RESULT_NO_ERROR;
        }
        client_type_start = type;
        client_type_end = type;
    }

    for (int ctype = client_type_start; ctype <= client_type_end; ctype++) {
        memset(&cumulative_stats, 0, sizeof(cumulative_stats));
        memset(jsonout, 0, sizeof(jsonout));

        ZDP("===== Client Type : %s ========\n", zpn_client_type_string(ctype));
        res = argo_structure_array_add(zpn_broker_client_apps_app_scale_stats_description,
                                       &zpn_broker_appscale_stats[ctype],
                                       BROKER_APP_CALC_THREADS_DEFAULT,
                                       &cumulative_stats,
                                       sizeof(cumulative_stats));
        if (res != ARGO_RESULT_NO_ERROR) {
            ZDP("argo_structure_array_add failed for zpn_broker_client_apps_app_scale_stats with error %s\n", zpath_result_string(res));
            return ARGO_RESULT_NO_ERROR;
        }

        res = argo_structure_dump(zpn_broker_client_apps_app_scale_stats_description,
                                  &cumulative_stats, jsonout, sizeof(jsonout),
                                  NULL, 1);
        if (res != ARGO_RESULT_NO_ERROR) {
            ZDP("argo_structure_dump failed for zpn_broker_client_apps_app_scale_stats with error %s\n", zpath_result_string(res));
            return ARGO_RESULT_NO_ERROR;
        }
        ZDP("%s\n", jsonout);
    }
    return res;
}

/* If lone_customer_gid is set, then only initialized for single
 * customer access */
int zpn_broker_client_apps_init(struct wally *lone_wally, int64_t lone_customer_gid, int64_t push_us, int dr_mode_is_active)
{
    int res;
    struct zpn_broker_client_app_sent_stats *app_sent_stats;
    static struct zpn_broker_client_apps_app_scale_stats appscale_stats[zpn_client_type_total_count] = {{0}};
    static int client_type_cookie[zpn_client_type_total_count] = {0};

    app_accumulate_description = argo_register_global_structure(APP_ACCUMULATE_HELPER);
    zpn_broker_client_app_sent_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_APP_SENT_STATS_HELPER);
    zpn_broker_client_apps_app_scale_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_APPS_APP_SCALE_STATS_HELPER);

    recalc_push_us = push_us;
    g_dr_mode_is_active = dr_mode_is_active;

    customers = zhash_table_alloc(&zpn_broker_client_allocator);
    customers_lock = ZPATH_MUTEX_INIT;

    zpn_set_app_scaling_update_callback(zpn_client_app_scaling_feature_flag_changed_cb);

    if (!is_unit_test()){
        app_sent_stats = ZPN_BCA_CALLOC(sizeof(*app_sent_stats));
        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "zpn_broker_client_app_sent_stats",
                                         AL_INFO,
                                         60*1000*1000,    /* 1 minute */
                                         zpn_broker_client_app_sent_stats_description,
                                         app_sent_stats,
                                         1,
                                         zpn_broker_client_app_sent_stats_fill,
                                         NULL)) {
            ZPN_LOG(AL_CRITICAL, "Could not register broker app sent stats - statistics_log not initialized?");
            return ZPN_RESULT_ERR;
        }

        res = app_re_download_stats_init();
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not initialize app re download stats");
        }

        for (int i = 1; i < zpn_client_type_total_count; i++) {
            client_type_cookie[i] = i;
            if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                             zpn_client_type_string(i),
                                             AL_INFO,
                                             MINUTE_TO_US(1),    /* 1 minute */
                                             zpn_broker_client_apps_app_scale_stats_description,
                                             &appscale_stats[i],
                                             1,
                                             zpn_broker_client_appscale_stats_fill,
                                             &client_type_cookie[i])) {
                ZPN_LOG(AL_CRITICAL, "Could not register app scale stats - statistics_log not initialized?");
                return ZPN_RESULT_ERR;
            }
        }
    }

    res = zpn_broker_client_apps_db_init(lone_wally,
                                         lone_customer_gid,
                                         application_row_callback,
                                         relation_row_callback,
                                         group_row_callback,
                                         domain_row_callback);

    if (res) return res;

    zse_register_for_change(customer_scope_update);

    zpe_register_for_change(policy_changed);

    res = zpath_debug_add_write_command("Perform application lookup, matching all possible matches",
                                  "/zpn/application/init_test",
                                  zpn_application_debug_init_test,
                                  NULL,
                                  "customer", "GID of customer on whose behalf we will test for init",
                                  NULL);

    if (!lone_customer_gid) {
        res = zpath_debug_add_read_command("Perform application lookup, matching all possible matches",
                                    "/zpn/application/gids",
                                    zpn_application_debug_generate_gids,
                                    NULL,
                                    "customer", "Domain or GID of customer on whose behalf we will fetch apps",
                                    NULL);
    } else {
        single_tenant_customer_gid = lone_customer_gid;
        res = zpath_debug_add_read_command("Perform application lookup, matching all possible matches",
                                    "/zpn/application/gids",
                                    zpn_application_debug_generate_gids,
                                    NULL,
                                    NULL);
    }
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/application/gids");
        return res;
    }

    res = zpath_debug_add_read_command("Check if app scaling feature is enabled or disabled for a customer",
                                  "/zpn/broker/client/check/appscaling/status",
                                  zpn_broker_client_check_app_scaling_status,
                                  NULL,
                                  "customer", "Required. Domain name or customer GID",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/client/check/appscaling/status");
        return res;
    }

    res = zpath_debug_add_read_command("Check count of client for a customer with app_scaling enabled and no_domain_download/no_ip_download capability",
                                  "/zpn/broker/client/check/appscaling/count",
                                  zpn_broker_client_check_app_scaling_count,
                                  NULL,
                                  "customer", "Required. Domain name or customer GID",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/client/check/appscaling/count");
        return res;
    }

    res = zpath_debug_add_read_command("Check if restricted app download is enabled for customer",
                                  "/zpn/broker/client/restrictAppDownload/enabled",
                                  zpn_broker_client_restrict_app_download_customer,
                                  NULL,
                                  "customer_gid", "Optional. GID of customer",
                                  "customer_name", "Optional. Domain of customer",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/restrictAppDownload/enabled");
        return res;
    }

    res = zpath_debug_add_read_command("Accumulated number of clients for a particular customer who has enabled restrict download feature",
                                  "/zpn/broker/client/restrictAppDownload/count",
                                  zpn_broker_client_restrict_app_download_count,
                                  NULL,
                                  "customer", "Required. Domain or GID of customer",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/restrictAppDownload/count");
        return res;
    }

    res = zpath_debug_add_read_command("Domain list for a particular customer",
                                  "/zpn/domains/dump",
                                  zpn_dump_domains,
                                  NULL,
                                  "customer", "Required. Domain or GID of customer",
                                  "no_domain_download", "optional, domain based hosts are excluded, default 0",
                                  "no_ip_download", "optional, IP based hosts are excluded, default 0",
                                  "max_count", "optional, max count to display, default will display all",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/restrictAppDownload/count");
        return res;
    }

    if (zpn_broker_is_dev_environment()) {
        res = zpath_debug_add_write_command("Simulate large domain update",
                                    "/zpn/broker/simulate/domainUpdate",
                                    zpn_broker_client_simulate_large_domain_update,
                                    NULL,
                                    "event_cnt","Number of event to be enqueued",
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/simulate/domainUpdate");
            return res;
        }
    }

    res = zpath_debug_add_safe_read_command("check Policy re-eval on posture change feature flags status for a customer",
                                            "/zpn/broker/client/policy_re_eval_on_posture_chg/status",
                                            zpn_broker_client_policy_re_eval_on_posture_chg_status,
                                            NULL,
                                            "customer_gid", "Required. Customer id",
                                            NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/client/policy_re_eval_on_posture_chg/status");
        return res;
    }

    res = zpath_debug_add_safe_read_command("Provides app thread info",
                                            "/zpn/broker/app_thread/info",
                                            zpn_broker_app_thread_info,
                                            NULL,
                                            NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /zpn/broker/app_thread/info");
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump app thread appscale stats",
                                            "/zpn/broker/app_thread/appscaling/stats",
                                            zpn_broker_client_apps_appscaling_stats_dump,
                                            NULL,
                                            "client_type", "Client type stats to display",
                                            NULL,
                                            NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command /zpn/broker/app_thread/appscaling/stats");
        return res;
    }

    // Register for Restrict Application download flag, skip this for unit tests
    // skip it if we are initializing sitec.
    if (!is_unit_test() && !ZPN_IS_SITEC()) {
        int len = sizeof(zpn_broker_rest_app_down_descriptions) / sizeof(struct zpath_config_override_desc);
        for (int z = 0; z < len; z++) {
            res = zpath_config_override_desc_register(&zpn_broker_rest_app_down_descriptions[z]);
            if (res != ZPATH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Unable to register zpn_broker_rest_app_down_descriptions[%d] for "
                        "key: %s, error: %s", z, zpn_broker_rest_app_down_descriptions[z].key, zpath_result_string(res));
                return res;
            }
        }
    }

    /* init dummy data */
    dummy_app = ZPN_BCA_CALLOC(sizeof(*dummy_app));
    dummy_app->deleted = 1;
    dummy_app->app_gid = 123456789;
    dummy_app->app_domain = ZPN_BCA_STRDUP("qwertyuiopasdf.com", strlen("qwertyuiopasdf.com"));
    dummy_app->icmp_access_type = ZPN_BCA_STRDUP("PING", strlen("PING"));

    /* Create apps misc thread */
    res = create_apps_misc_thread_event_base();
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to create thread %s, error: %s", APP_MISC_THREAD, zpn_result_string(res));
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * @customer : Customer object
 * @no_domain_download : This can be legacy no_domain_download, or no_domain_download_v2 value if new client
 * @no_ip_download : is not yet used by the client, and is likely to be 0
 * @simulate_app_scaling : to 1 for safe curl command, do not use it for production code
 * @client_appscale_flag : Set the appscale config override value for clients
 */
static struct zhash_table *generate_all_domains_table(struct customer_state *customer,
                                                      int no_domain_download,
                                                      int no_ip_download,
                                                      int client_appscale_flag)
{
    struct zpn_application **apps;
    size_t apps_count;
    size_t i, j;
    struct zhash_table *tmp_table = NULL;
    int res;


    tmp_table = zhash_table_alloc(&zpn_broker_client_allocator);

    apps = ZPN_BCA_MALLOC(sizeof(*apps) * MAX_CLIENT_APPS);
    apps_count = MAX_CLIENT_APPS;

    res = zpn_broker_client_apps_get_apps_by_customer_gid(customer->customer_gid,
                                                          apps,
                                                          &apps_count);
    if (res) {
        if (res == ZPATH_RESULT_NOT_FOUND) {
            /* Empty set is fine. */
        } else {
            /* This is equivalent to empty set, and bad... */
            ZPN_LOG(AL_ERROR, "Customer %ld could not generate domains list: %s", (long)customer->customer_gid, zpath_result_string(res));
        }
    } else {
        /* This is where it gets fun. For each app, for each domain, add it to hash. */
        for (i = 0; i < apps_count; i++) {
            struct zpn_application *app = apps[i];
            for (j = 0; j < app->domain_names_count; j++) {
                char *domain = app->domain_names[j];
                size_t domain_len = strlen(domain);

                if (client_appscale_flag) {
                    int is_ip = is_valid_ip(domain);
                    if (no_domain_download && !is_ip) continue; /* do not download domain based app */
                    if (no_ip_download && is_ip) continue;      /* do not download ip based app */
                }

                if (!zhash_table_lookup(tmp_table, domain, domain_len, NULL)) {
                    /* We can use external key storage in zhash table,
                     * since this table is used during this function
                     * only */
                    zhash_table_store(tmp_table, domain, domain_len, 1, tmp_table);
                }
            }
        }
    }
    ZPN_BCA_FREE(apps);

    return tmp_table;
}

static int merge_walk(void *cookie,
                      void *object,
                      void *key,
                      size_t key_len)
{
    struct zhash_table *dest = cookie;
    if (!zhash_table_lookup(dest, key, key_len, NULL)) {
        zhash_table_store(dest, key, key_len, 0, dest);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void add_hash_to_hash(struct zhash_table *target_hash, struct zhash_table *source_hash)
{
    int64_t key = 0;
    zhash_table_walk(source_hash, &key, merge_walk, target_hash);
}

/*
 * do not put customer->lock inside this function, because this may be called from
 * a long list loop protected by the same customer lock, if it holds too long, this may cause
 * heartbeat
 */
static void new_client_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    struct customer_state *customer = client->customer;
    struct client_domains *domains = NULL;
    struct client_app_gids *app_gids = NULL;
    struct zhash_table *tmp_table;

    snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD %d: start", __LINE__);

    struct zthread_info *zth_info = zthread_self();
    if (zth_info) {
        char thread_info[256];
        int thread_num = 0;
        snprintf(thread_info, sizeof(thread_info), "%s", zth_info->stack.thread_name);
        sscanf(thread_info, "app_thread_%d", &thread_num);
        int app_thread_count = zpn_broker_app_calc_threads_count_get();
        if (thread_num >= 0 && thread_num < app_thread_count) {
            client->app_thread_id = thread_num;
        }
    }

    client->stats.app_thread_high_priority_dequeue_us = epoch_us();
    client->stats.app_thread_high_priority_wait_us = client->stats.app_thread_high_priority_dequeue_us - client->stats.app_thread_high_priority_enqueue_us;
    base->high_priority_queue_wait_time += client->stats.app_thread_high_priority_wait_us;

    if (client->stats.app_thread_high_priority_wait_us > SECOND_TO_US(10)) {
        ZPN_LOG(AL_INFO, "Customer: %"PRId64" tunnel: %s high priority wait time greater than 10s. wait time: %"PRId64"us",
                          client->customer_gid, client->tunnel_id, client->stats.app_thread_high_priority_wait_us);
        base->high_priority_queue_wait_time_gt_10s++;
    } else if (client->stats.app_thread_high_priority_wait_us > SECOND_TO_US(5)) {
        ZPN_LOG(AL_INFO, "Customer: %"PRId64" tunnel: %s high priority wait time greater than 5s. wait time: %"PRId64"us",
                          client->customer_gid, client->tunnel_id, client->stats.app_thread_high_priority_wait_us);
        base->high_priority_queue_wait_time_gt_5s++;
    } else if (client->stats.app_thread_high_priority_wait_us > SECOND_TO_US(2)) {
        ZPN_LOG(AL_INFO, "Customer: %"PRId64" tunnel: %s high priority wait time greater than 2s. wait time: %"PRId64"us",
                          client->customer_gid, client->tunnel_id, client->stats.app_thread_high_priority_wait_us);
        base->high_priority_queue_wait_time_gt_2s++;
    }

    decrease_queue_size(client, 1);

    if (client->new_client_cb_in_queue > client->stats.max_new_client_cb_in_queue) {
        client->stats.max_new_client_cb_in_queue = client->new_client_cb_in_queue;
    }
    client->new_client_cb_in_queue--;

    if (client->deleted) {
        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str), "APP_DOWNLOAD: %d deleted 1", __LINE__);
        client_release(client);
        return;
    }

    /* If we're still initializing, then apps will be transported as
     * normal - all clients getting all initial apps */
    zpn_client_tracker_end(client->tracker, client_track_app_thread_xfer, 0);
    zpn_client_tracker_start(client->tracker, client_track_app_customer_init);
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    if (customer->initializing_apps ||
        (!g_dr_mode_is_active && (zhash_table_lookup(customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0) == &policy_not_ready))) {
        ZPN_DEBUG_MTN("%s: New client process: Still initializing: apps=%d", client->debug_str, customer->initializing_apps);

        if (!client->re_init_start_us) client->re_init_start_us = epoch_us();

        ZPN_LOG(AL_INFO, "%s: New client process: Still initializing: apps=%d, re_init_start_us=%"PRId64,
                         client->debug_str, customer->initializing_apps, client->re_init_start_us);

        ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
        /* Need to release client here, as we were given a reference
         * as part of being asynchronous */

        snprintf(client->stats.app_download_debug_str, sizeof(client->stats.app_download_debug_str),
                 "APP_DOWNLOAD %d: initializing %d ", __LINE__, customer->initializing_apps);
        client_release(client);
        return;
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
    zpn_client_tracker_end(client->tracker, client_track_app_customer_init, ZPN_RESULT_NO_ERROR);

    zpn_client_tracker_start(client->tracker, client_track_app_inform_client);
    /* if either IP download or domain download */
    int no_domain_download = (client->no_domain_download || client->no_domain_download_v2);
    int client_appscale_config = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);

    if (!client->no_ip_download ||
        !no_domain_download ||
        !client_appscale_config) {
        if (client->segment_download) {
            /* Generate all app_ids direct from wally. */
            app_gids = generate_all_gids_table(customer);
        } else {
            ZPN_DEBUG_MTN("%s: New client process: Generate all_domains", client->debug_str);
            tmp_table = generate_all_domains_table(customer, no_domain_download, client->no_ip_download, client_appscale_config);
            domains = domains_from_hash(tmp_table);
            zhash_table_free(tmp_table);
        }
    }
    inform_one_client(client, domains, app_gids, 1);
    zpn_client_tracker_end(client->tracker, client_track_app_inform_client, ZPN_RESULT_NO_ERROR);

    if (et_25587_test) {
        ZPN_DEBUG_MTN("et_25587 new_client wait for app check to complete: %s", client->debug_str);
        et_25587_check_block();
        ZPN_DEBUG_MTN("et_25587 DEQUE: new_client app check complete: %s", client->debug_str);
    }

    if(domains) domains_release(domains);
    if(app_gids) app_gids_release(app_gids);
    client_release(client);
}

/*
 * If segment_download is set, then the download to client is (and
 * tracks) by application segment GID. (Edge connector clients want
 * this)
 *
 * If segment_download is not set, then the download to client is (and
 * tracks) by domain name. (All other clients want this)
 *
 * This function initializes the client objects based on the parameters and makes a zevent call for the new_client function passing this client object
 *
 * i : input parameter
 * o : output parameter
 *
 * @c_state (i) : a client connection
 * @segment_download (i) : download by application segment or not
 * @app_callback (i) : Function which is invoked for app callback
 * @complete_callback (i) : Function which is invoked when callback is complete
 */
struct zpn_client_app_state *zpn_broker_client_apps_create_client_app_state(struct zpn_broker_client_fohh_state *c_state,
                                                            int segment_download,
                                                            zpn_broker_client_app_callback_f *app_callback,
                                                            zpn_broker_client_app_complete_callback_f *complete_callback)
{
    struct zpn_client_app_state *client;
    char str[200];

    client = ZPN_BCA_CALLOC(sizeof(*client));

    /* initilize reference counter to 1 */
    client->reference_count = 1;

    /* for debug in gdb only, do NOT use it */
    client->c_state_do_not_use = (void *)c_state;

    client->apps_sent_lock = ZPATH_MUTEX_INIT;
    snprintf(str, sizeof(str), "%s:%ld", c_state->tunnel_id, (long) c_state->customer_gid);
    client->debug_str = ZPN_BCA_STRDUP(str, strlen(str));
    client->tracker = &(c_state->tracker);
    client->tracker->tag = client->debug_str;
    ZPN_DEBUG_MTN("%s: Client app fetch started", client->debug_str);

    client->platform_type = c_state->platform_type;
    client_hold(client);
    c_state->app_state = client;
    c_state->app_stats = &(client->stats);

    client->customer_gid = c_state->customer_gid;
    client->scope_gid = c_state->scope_gid;
    client->machine_gid = c_state->machine_gid;
    client->conn_thread_id = c_state->conn_thread_id;
    snprintf(client->attr, sizeof(client->attr), "%s", c_state->attr);
    client->string_hash = c_state->general_context_hash;
    client->saml_string_hash = c_state->saml_hash;
    client->scim_string_hash = c_state->scim_hash;

    client->client_type = c_state->client_type;
    client->client_aux_id = c_state->client_aux_id;

    if (zpn_client_static_config[client->client_type].location_policy) {
        if (c_state->o_location_id) client->loc_id = c_state->o_location_id;
        if (c_state->o_sub_location_id) client->sub_loc_id = c_state->o_sub_location_id;
    }

    client->app_callback = app_callback;
    client->complete_callback = complete_callback;
    client->char_cookie = ZPN_BCA_STRDUP(c_state->tunnel_id, strlen(c_state->tunnel_id));
    client->int_cookie = c_state->incarnation;
    client->client_fohh_thread = zevent_self();
    client->calc_thread = zevent_get_for_class(ZEVENT_CLASS_APP_CALC);
    client->app_thread_id = 0;
    if (segment_download) client->segment_download = 1;
    client->no_ip_download = c_state->capability_no_ip_download;
    client->no_domain_download = c_state->capability_no_domain_download;
    client->no_domain_download_v2 = c_state->capability_no_domain_download_v2;
    snprintf(client->tunnel_id, sizeof(client->tunnel_id), "%s", c_state->tunnel_id);

    /*
     * c_state cannot be used in different thread, discard the pointer since we do not need it anymore,
     * this avoid c_state being passed to different thread later by accident
     */
    c_state = NULL;

    if (!client->client_fohh_thread) {
        ZPN_LOG(AL_CRITICAL, "%s: Cannot get thread: client thread", client->debug_str);
        ZPN_BCA_FREE(client->debug_str);
        ZPN_BCA_FREE(client->char_cookie);
        ZPATH_MUTEX_DESTROY(&(client->apps_sent_lock), _FILE_, _LINE_);
        ZPN_BCA_FREE(client);
        return NULL;
    }
    if (!client->calc_thread) {
        ZPN_LOG(AL_CRITICAL, "%s: Cannot get thread: calc thread", client->debug_str);
        ZPN_BCA_FREE(client->debug_str);
        ZPN_BCA_FREE(client->char_cookie);
        ZPATH_MUTEX_DESTROY(&(client->apps_sent_lock), _FILE_, _LINE_);
        ZPN_BCA_FREE(client);
        return NULL;
    }
    client->customer = get_customer_state(client->customer_gid, client);
    ZPATH_MUTEX_LOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
    client->apps_sent = zhash_table_alloc(&zpn_broker_client_allocator);
    ZPATH_MUTEX_UNLOCK(&(client->apps_sent_lock), __FILE__, __LINE__);

    /* Attach self to customer, so we get updates. Then have the
     * customer process me... */
    ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);
    zbca_increment_app_scaling_cnt(client);
    zbca_increment_restrict_app_download_cnt(client);
    client_hold(client);
    TAILQ_INSERT_TAIL(&(client->customer->clients), client, list);
    ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);

    return client;

}

int zpn_broker_client_apps_start(struct zpn_broker_client_fohh_state *c_state,
                                 int segment_download,
                                 zpn_broker_client_app_callback_f *app_callback,
                                 zpn_broker_client_app_complete_callback_f *complete_callback)
{
    struct zpn_client_app_state *client = zpn_broker_client_apps_create_client_app_state(c_state,
                                                                  segment_download,
                                                                  app_callback,
                                                                  complete_callback);
    if (!client) return ZPATH_RESULT_ERR;

    if (g_app_download_keep_alive && client->client_type == zpn_client_type_zapp ) {
        send_dummy_app(client);
    }

    /* call with high priority for new connection */
    increase_queue_size(client, 1);
    client_hold(client);
    client->stats.app_thread_high_priority_enqueue_us = epoch_us();

    zpn_client_tracker_start(client->tracker, client_track_app);
    zpn_client_tracker_start(client->tracker, client_track_app_thread_xfer);

    CLIENT_ADD_ZEVENT_WITH_PRIORITY(client->calc_thread, new_client_cb, client, 0, QUEUE_PRIORITY_HIGH);
    client->new_client_cb_in_queue++;

    if (et_25587_test) {
        ZPN_DEBUG_MTN("et_25587 ENQUE: new_client %s", client->debug_str);
    }

    client_release(client);
    return ZPATH_RESULT_ASYNCHRONOUS;
}

static void client_config_update(struct zpn_client_app_state *client)
{
    struct client_domains *domains = NULL;
    struct client_app_gids *app_gids = NULL;
    struct zhash_table *join;

    ZPN_DEBUG_MTN("%s: Config update", client->debug_str);

    client->stats.app_update_start_us = epoch_us();
    ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);

    int no_domain_download = (client->no_domain_download || client->no_domain_download_v2);
    int client_appscale_config = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);

    /* if either IP download or domain download */
    if (!client->no_ip_download ||
        !no_domain_download ||
        !client_appscale_config) {
        if (client->segment_download) {
            /* We only need the customer gids here. Any other outstanding GIDs
             * that are changing (deleted) will be updated on defer change
             * timers firing. */
            app_gids = generate_all_gids_table(client->customer);
        } else {
            join = generate_all_domains_table(client->customer, no_domain_download, client->no_ip_download, client_appscale_config);
            add_hash_to_hash(join, client->customer->domain_accumulate);
            domains = domains_from_hash(join);
            zhash_table_free(join);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);

    client->stats.app_update_calls++;
    client->stats.app_update_complete_us = epoch_us();

    inform_one_client(client, domains, app_gids, 0);
    if(domains) domains_release(domains);
    if(app_gids) app_gids_release(app_gids);
}

void tickle_inform_one_client(struct zpn_client_app_state *client)
{
    ZPN_DEBUG_MTN("%s: customer scope update for scope gid %ld", client->debug_str, (long)client->scope_gid);
    client_hold(client);
    if(client->domains) domains_hold(client->domains);
    if(client->app_gids) app_gids_hold(client->app_gids);
    increase_queue_size(client, 0);
    CLIENT_ADD_BIG_ZEVENT(client->calc_thread, inform_one_client_cb, client, 0, client->domains, client->app_gids, NULL, 0);
}

static int customer_scope_update_cb(void *response_callback_cookie,
                                    struct wally_registrant *registrant,
                                    struct wally_table *table,
                                    int64_t request_id,
                                    int row_count)
{
    struct zpn_client_app_state *client = response_callback_cookie;
    ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);
    zhash_table_remove(client->customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), &policy_not_ready);
    zhash_table_store(client->customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0, &policy_ready);
    tickle_inform_one_client(client);
    ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);
    if(client->domains) domains_release(client->domains);
    if(client->app_gids) app_gids_release(client->app_gids);
    client_release(client);
    return ZPATH_RESULT_NO_ERROR;
}

void customer_scope_update(int64_t customer_gid, int changed)
{
    int res;
    struct zpn_client_app_state *client;
    struct client_domains *domains = NULL;
    struct client_app_gids *app_gids = NULL;
    struct zhash_table *join;

    if(g_dr_mode_is_active) {
        return;
    }

    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    app_gids = generate_all_gids_table(customer);
    int app_scale = zpn_is_app_scaling_enabled(customer->customer_gid);
    join = generate_all_domains_table(customer, 0, 0, app_scale);
    add_hash_to_hash(join, customer->domain_accumulate);
    domains = domains_from_hash(join);
    zhash_table_free(join);

    TAILQ_FOREACH(client, &(customer->clients), list) {

        /*
         * if scope is changed(deleted, or toggled for enabled, we reevaluate for all clients.
         * Otherwise, we only reevaluate for those attr=>scope mapping is changed
         */
        if (!is_unit_test() || is_dta_test()) {
            struct zse_scope_data *scope = NULL;
            if (client->client_type == zpn_client_type_machine_tunnel) {
                scope = get_scope_by_machine_gid(client->machine_gid);
            } else {
                scope = get_scope_by_attr(customer_gid, client->attr);
            }
            if (scope) {
                if(!scope->enabled) {
                    client->scope_gid = customer_gid;
                } else if (scope->scope_gid != client->scope_gid){
                    client->scope_gid = scope->scope_gid;
                } else if(!changed) {
                    continue;
                }
            } else {
                if (client->scope_gid != customer_gid){
                    /* This scope is deleted, the user turns to belong to default scope */
                    client->scope_gid = customer_gid;
                } else if(!changed) {
                    continue;
                }
            }
        }

        client->domains = domains;
        client->app_gids = app_gids;

        if (zhash_table_lookup(customer->scope_set, &client->scope_gid, sizeof(client->scope_gid), 0) == &policy_not_ready){
            /* this will be handled in get_policy_defer */
            continue;
        } else if (zhash_table_lookup(customer->scope_set, &client->scope_gid, sizeof(client->scope_gid), 0) == 0) {
            /* This scope has not connect before, load its policy first */
            if(client->domains) domains_hold(client->domains);
            if(client->app_gids) app_gids_hold(client->app_gids);
            client_hold(client);
            res = zpn_scope_ready_1(client->scope_gid, customer_scope_update_cb, client, 0);
            if (res) {
                if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                    ZPN_LOG(AL_CRITICAL, "Cannot get scope ready %ld", (long)client->scope_gid);
                }
                continue;
            } else {
                /* the callback will not be added to wally_callback_queue, so release the objects */
                if(client->domains) domains_release(client->domains);
                if(client->app_gids) app_gids_release(client->app_gids);
                zhash_table_store(customer->scope_set, &(client->scope_gid), sizeof(client->scope_gid), 0, &policy_ready);
                client_release(client);
            }
        }

        tickle_inform_one_client(client);
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    if(domains) domains_release(domains);
    if(app_gids) app_gids_release(app_gids);

}

void client_update_timer_cb(int sock, short flags, void *cookie)
{
    struct zpn_client_app_state *client = cookie;

    ZPN_DEBUG_MTN("Client %s: update timer fired", client->debug_str);

    if (client->timer) {
        event_free(client->timer);
        client->timer = NULL;
    }

    if (!client->deleted) {
        client_config_update(client);
    } else {
        ZPN_DEBUG_MTN("Client timer: client: %s deleted, skip update", client->debug_str);
    }

    client_release(client);
}

static void refresh_client_timer_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    struct timeval tv;

    if (client->refresh_timer_cb_in_queue > client->stats.max_refresh_timer_cb_in_queue) {
        client->stats.max_refresh_timer_cb_in_queue = client->refresh_timer_cb_in_queue;
    }
    client->refresh_timer_cb_in_queue--;

    if (!client->timer) {
        client_hold(client);
        client->timer = event_new(zevent_event_base(client->calc_thread), -1, 0, client_update_timer_cb, client);
        client->stats.client_policy_timer_push_count = 0;
        ZPN_DEBUG_MTN("Client refresh: %s: Client update timer create", client->debug_str);
    } else {
        ZPN_DEBUG_MTN("Client refresh: %s: Client update timer push by %ld ms",
                      client->debug_str,
                      (long) (client_update_push_us / 1000));
    }

    /* This will schedule or reschedule the timer into the future */
    tv.tv_usec = client_update_push_us % 1000000;
    tv.tv_sec = client_update_push_us / 1000000;

    event_add(client->timer, &tv);
    client->stats.client_policy_timer_push_count++;

    client_release(client);
}

int zpn_broker_client_apps_update(struct zpn_client_app_state *client,
                                  struct zhash_table *string_hash,
                                  struct zhash_table *saml_hash,
                                  struct zhash_table *scim_hash)
{
    /* Update hash first!! No locking needed because it is an atomic
     * hash change */
    client->string_hash = string_hash;
    client->saml_string_hash = saml_hash;
    client->scim_string_hash = scim_hash;

    /* Do an async call on the calc thread, and schedule a timer for the following purposes:
     * 1. Reduce time holding the user thread (fohh thread) and offload heavy-duty
     *    app/policy computation to a separate thread.
     * 2. Allow a slight delay such that if a swarm of updates coming next to each other,
     *    they have an opportunity to get coallesced into a single action on the app thread.
     *
     * Ref: ET-34016.
     */
    client_hold(client);
    CLIENT_ADD_ZEVENT(client->calc_thread, refresh_client_timer_cb, client, 0);
    client->refresh_timer_cb_in_queue++;

    return ZPATH_RESULT_NO_ERROR;
}

int64_t zpn_broker_client_app_get_scope_gid(void *cookie)
{
    struct zpn_client_app_state *client = cookie;
    return client->scope_gid;
}

static void free_client_apps(void *element, void *cookie)
{
    struct zpn_client_app_state *client = cookie;
    if (client->segment_download) {
        struct argo_object *object = element;
        struct zpn_application *app = object->base_structure_void;
        ZPN_DEBUG_MTN("%s: Releasing app %"PRId64, client->debug_str, app->gid);
        argo_object_release(object);
    } else {
        struct zpn_client_app_cache *app_cache = element;
        zpn_broker_client_app_cache_free(app_cache);
    }
   return;
}


/* Simple reference count drop. Returns the new reference count
 * (indicating the client still exists)
 * Do not call this within client->apps_sent_lock */
static int32_t client_release(struct zpn_client_app_state *client)
{
    int32_t count = __sync_sub_and_fetch_4(&(client->reference_count), 1);
    ZPN_DEBUG_CLIENT("%s client reference count = %d", client->debug_str, count);

    /* If this suppose to be the last client to be released, the non-zero count may indicate a memeory leak */
    /* i.e. we have more client_hold() than client_release() */
    if (!count) {
        if (!client->segment_download) {
            ZPN_DEBUG_MTN("%s: Releasing app for non-segment based download", client->debug_str);
        }
        zhash_table_free_and_call(client->apps_sent, free_client_apps, client);
        ZPN_LOG(AL_DEBUG, "%s: Freed", client->debug_str);
        ZPN_BCA_FREE(client->debug_str);
        ZPN_BCA_FREE(client->char_cookie);
        ZPATH_MUTEX_DESTROY(&(client->apps_sent_lock), _FILE_, _LINE_);
        ZPN_BCA_FREE(client);
    }
    return count;
}

static int32_t client_hold(struct zpn_client_app_state *client)
{
    int32_t count = __sync_add_and_fetch_4(&(client->reference_count), 1);
    if (count == 1) {
        ZPN_LOG(AL_CRITICAL, "Incremented client reference count to 1");
    }
    return count;
}

int32_t get_client_ref_count(void *cookie)
{
    struct zpn_client_app_state *client = cookie;
    return client ? __sync_or_and_fetch_4(&(client->reference_count), 0) : 0;
}

void set_enforced_disconnection_count(void *cookie)
{
    struct zpn_client_app_state *client = cookie;
    if (client) {
        zpn_broker_app_re_download_stats[client->app_thread_id].client_disconnect_app_download_timeout++;
        ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);
        client->customer->enforced_client_disconnection_count++;
        client->stats.app_process_termination_count = client->customer->enforced_client_disconnection_count;
        ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);
    }
}

void reset_init_testing(void *cookie)
{
    struct zpn_client_app_state *client = cookie;
    if (client) {
        ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);
        client->customer->initializing_testing = 0;
        ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);
    }

}

/*
 * Removes client from callback queues, etc and marks it deleted to
 * short-circuit in-progress evaluations and callbacks
 */
static void client_delete_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_client_app_state *client = void_cookie;
    struct customer_state *customer = client->customer;

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    zbca_decrement_app_scaling_cnt(client);
    zbca_decrement_restrict_app_download_cnt(client);

    TAILQ_REMOVE(&(customer->clients), client, list);
    client->deleted = 1;
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    /* decrease reference_count because we increase it when insert client to customer->clients */
    client_release(client);

    /* decrease reference_count because we increase it when dispatch the callbackclient_delete_cb */
    client_release(client);
}

int zpn_broker_client_apps_done(struct zpn_broker_client_fohh_state *c_state)
{
    struct zpn_client_app_state *client = c_state->app_state;

    ZPN_DEBUG_MTN("%s: Done requested", client->debug_str);

    /* c_state do not need client any more, so release it here */
    client->is_cstate_done = 1;

    client_hold(client);
    CLIENT_ADD_ZEVENT(client->calc_thread, client_delete_cb, client, 0);


    c_state->app_state = NULL;
    c_state->app_stats = NULL;

    /*
     * These fields are now bad. We can remove them directly here
     * since the state is guaranteed valid. The rest of the code
     * accommodates this change
     * ET-43734
     *
     * It may take some time for this base_call to actually occur-
     * there can be a large backlog of clients when systems
     * start/restart. Thus it may take a long time to get to the point
     * where this base_call is processed, and only some time after
     * that wwere these hashes originally removed. During that time,
     * the c_state could have gone away, causing a memory access
     * problem.
     *
     * It is important after this point that we clear out any
     * references to basically stale data. In fact, we should mark
     * this client synchronously as "going away" such that we can
     * short-circuit any app calculations that are being
     * performed. But those optimizations may/may not be
     * needed/done.
     */
    client->string_hash = NULL;
    client->saml_string_hash = NULL;
    client->scim_string_hash = NULL;

    client_release(client);
    return ZPATH_RESULT_NO_ERROR;
}

int64_t zpn_broker_client_apps_sent_count(struct zpn_client_app_state *client) {
    int64_t res = 0;
    res = (client && client->apps_sent) ? client->apps_sent->element_count : 0;
    return res;
}

void accumulate_free(struct zpn_app_client_check *client_check)
{
    if (client_check) {
        if (client_check->accumulate) {
            ZPN_BCA_FREE(client_check->accumulate);
        }

        if (client_check->app_segments) {
            zpn_broker_client_apps_clear_app_segment_collection(client_check->app_segments, client_check->app_segments_count);
            ZPN_BCA_FREE(client_check->app_segments);
            client_check->app_segments = NULL;
            client_check->app_segments_count = 0;
        }
        memset(client_check, 0, sizeof(*client_check));
    }
}

int zpn_broker_client_fill_app_client_check_with_app(struct zpn_broker_client_fohh_state *c_state,
                                                      struct zpn_app_client_check *client_check)
{
    int res;
    int debugging_enabled = 0;
    int64_t delta_us = 0;
    char *p;
    struct app_accumulate *accumulate;
    char *application_name;
    struct zpn_client_app_state *client = c_state->app_state;
    struct zhash_table *accumulated_inclusive_domains = NULL;

    if (!client) {
        ZPN_LOG(AL_ERROR, "customer %ld Failed to get zpn_client_app_state", (long)c_state->customer_gid);
        return ZPATH_RESULT_ERR;
    } else if(client->deleted) {
        ZPN_LOG(AL_ERROR, "customer %ld zpn_client_app_state is deleted", (long)c_state->customer_gid);
        return ZPATH_RESULT_ERR;
    }

    /* release it after client_check has been sent out */
    accumulate = ZPN_BCA_CALLOC(sizeof(*accumulate));
    if (!accumulate) {
        ZPN_LOG(AL_CRITICAL, "customer %ld Failed to allocate accumulate", (long)c_state->customer_gid);
        return ZPATH_RESULT_ERR;
    }

    init_accumulate(accumulate);

    accumulate->deleted_no_send = 1;

    /* Accumulate needs client state because it is processing policy
     * as part of its function. It therefore also needs the policy
     * sets it may need for processing */
    accumulate->client = client;

    if(!g_dr_mode_is_active) {
        res = zpe_get_scope_policy_without_rebuild(client->scope_gid,
                                                zpe_policy_type_bypass,
                                                &(accumulate->bypass_policy_built_default),
                                                &(accumulate->bypass_policy_built),
                                                NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to get default scope policy %ld", (long)client->scope_gid);
            ZPN_BCA_FREE(accumulate);
            return res;
        }

        res = zpe_get_scope_policy_without_rebuild(client->scope_gid,
                                                zpe_policy_type_access,
                                                &(accumulate->access_policy_built_default),
                                                &(accumulate->access_policy_built),
                                                NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to get scope policy %ld", (long)client->scope_gid);
            ZPN_BCA_FREE(accumulate);
            return res;
        }
    }

    application_name = ZPN_BCA_STRDUP(client_check->name, strlen(client_check->name));
    /* strip CIDR */
    p = application_name;
    while(*p != '\0' && *p != '/') p++;
    *p = '\0';


    if(g_dr_mode_is_active) {
        /* get most matched domain for application name based on customer_gid */
        res = zpn_application_search_by_customer(client->customer_gid,
                                                 application_name,
                                                 strnlen(application_name, MAX_DOMAIN_LEN_SIZE),
                                                 accumulate->matched_domain,
                                                 sizeof(accumulate->matched_domain),
                                                 NULL);
    } else {
        /* get most matched domain for application name based on scope gid */
        res = zpn_application_search_by_scope(client->scope_gid,
                                              application_name,
                                              strnlen(application_name, MAX_DOMAIN_LEN_SIZE),
                                              accumulate->matched_domain,
                                              sizeof(accumulate->matched_domain),
                                              NULL);
    }

    if (res) {
        ZPN_LOG(AL_ERROR, "Customer %ld could not find most matched domain for application %s",
                          (long) client->customer->customer_gid, application_name);
        ZPN_BCA_FREE(accumulate);
        ZPN_BCA_FREE(application_name);
        return ZPATH_RESULT_ERR;
    }

    accumulated_inclusive_domains = zhash_table_alloc(&zpn_allocator);
    if(accumulated_inclusive_domains == NULL) {
        ZPN_LOG(AL_ERROR, "Failed to allocate memory for inclusive domain table for customer %"PRId64", scope %"PRId64" ",
                           client->customer->customer_gid, client->scope_gid);
        ZPN_FREE(accumulate);
        ZPN_FREE(application_name);
        return ZPN_RESULT_NO_MEMORY;
    }

    ZPN_DEBUG_CLIENT("%s: Matched domain %s", client->debug_str, accumulate->matched_domain);
    if (is_app_pattern_match_feature_enabled(client->customer_gid) &&
        is_pattern_match_domain(accumulate->matched_domain, strnlen(accumulate->matched_domain, MAX_APPLICATION_DOMAIN_LEN))) {
        snprintf(accumulate->matched_domain, sizeof(accumulate->matched_domain), "%s", application_name);
        accumulate->is_pattern_domain = 1;
        ZPN_DEBUG_CLIENT("%s: For port accumulate using client's domain %s", client->debug_str, accumulate->matched_domain);
    }

    debugging_enabled = zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX);
    if (debugging_enabled) {
        delta_us = epoch_us();
    }

    /* accumulate response */
    int collect_segments = (c_state->capability_no_domain_download || c_state->capability_no_domain_download_v2) &&
                           zpn_client_static_config[c_state->client_type].needs_full_app_data_on_app_check;

    accumulate_per_domain(accumulate->matched_domain,
                          accumulate,
                          accumulated_inclusive_domains,
                          PATTERN_DOMAIN_SEARCH_ENABLED,
                          collect_segments);

    if (debugging_enabled) {
        delta_us = epoch_us() - delta_us;
        ZPN_DEBUG_APPLICATION("Customer:%"PRId64", tunnel_id: %s, Domain: %s accumulate time=%"PRId64" usec",
                               client->customer_gid, client->tunnel_id, application_name, delta_us);
    }

    zhash_table_free_and_call(accumulated_inclusive_domains, free_inclusive_accumulated_domain, client);

    if(is_app_deleted(accumulate)) {
        ZPN_LOG(AL_INFO, "Customer %ld application %s is deleted",
                          (long) client->customer->customer_gid, application_name);
        zpn_broker_client_apps_reset_accumulate_app_segments(accumulate);
        ZPN_BCA_FREE(accumulate);
        ZPN_BCA_FREE(application_name);
        return ZPATH_RESULT_ERR;
    }

    /* fill output */
    client_check->accumulate = accumulate;

    client_check->ingress_port_ranges   = accumulate->tcp_ports_array;
    client_check->port_ranges_count     = accumulate->tcp_port_count;
    client_check->tcp_port_ranges       = accumulate->tcp_ports_array;
    client_check->tcp_port_ranges_count = accumulate->tcp_port_count;
    client_check->udp_port_ranges       = accumulate->udp_ports_array;
    client_check->udp_port_ranges_count = accumulate->udp_port_count;
    client_check->bypass_type           = accumulate->bypass_type;
    if (client_check->bypass_type && strcmp(client_check->bypass_type, "NEVER")) client_check->bypass = 1;
    client_check->icmp_access_type      = accumulate->icmp_access_type;
    ZPN_APPLICATION_SET_INSPECTION_FLAGS(accumulate->ip_anchored,
                                         accumulate->inspected,
                                         client_check->ip_anchored,
                                         client_check->inspected);
    client_check->bypass_on_reauth      = accumulate->bypass_on_reauth;
    client_check->double_encrypt        = accumulate->double_encrypt;
    client_check->app_domain            = accumulate->matched_domain;

    client_check->app_segments          = accumulate->app_segments;
    client_check->app_segments_count    = accumulate->app_segments_count;
    // Hand ownership to the client check who can free it
    accumulate->app_segments = NULL;
    accumulate->app_segments_count = 0;

    ZPN_BCA_FREE(application_name);
    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_bca_send_agg_domain_app_scaling_flag_changed(struct customer_state *customer, int value, enum zpn_client_type client, enum zpn_platform_type platform)
{
    int res;
    char **tld_1 = NULL;
    int sz = 0;
    int64_t timer_fired_us = epoch_us();

    if (customer->agg_domain_cb) {
        if (0 == value) {
            res = (customer->agg_domain_cb)(customer->customer_gid, NULL, 0, 1, timer_fired_us, client, platform);
            if (res) {
                ZPN_LOG(AL_ERROR, "App Scaling feature flag changed for customer %"PRId64", "
                        "failed to notify clients, error %s\n", customer->customer_gid,
                        zpn_result_string(res));
                return;
            }
            struct zpn_lib_appscale_client_cookie *cplat_cookie = ZPN_BCA_CALLOC(sizeof(*cplat_cookie));
            zpn_broker_client_appscale_set_platform_clients(cplat_cookie, client, platform);
            CLIENT_ADD_BIG_ZEVENT(customer->calc_thread,
                                  inform_all_clients_after_app_scaling_disable,
                                  customer,
                                  0,
                                  cplat_cookie,
                                  NULL,
                                  NULL,
                                  0);
        } else {
            res = zpn_get_tld_1_by_gid(customer->customer_gid, &tld_1, &sz);
            if (res) {
                ZPN_LOG(AL_ERROR, "App Scaling feature flag changed for customer %"PRId64", "
                        "failed to get tld_1, error %s\n", customer->customer_gid,
                        zpn_result_string(res));
                return;
            }

            res = (customer->agg_domain_cb)(customer->customer_gid, tld_1, sz, 0, timer_fired_us, client, platform);
            if (res) {
                ZPN_LOG(AL_ERROR, "App Scaling feature flag changed for customer %"PRId64", "
                        "failed to notify clients, error %s\n", customer->customer_gid,
                        zpn_result_string(res));
            }
            zpn_free_tld_1(tld_1, sz, 0);
       }
    } else {
       ZPN_LOG(AL_WARNING, "Customer %ld agg_domain_cb is not set", (long)customer->customer_gid);
    }
}

static int zpn_bca_app_scaling_customer_config_has_changed(struct customer_state *customer) {
    int64_t config_value = customer->app_scaling_feature_status_cur;
    //Updated cur to whatever is currently in the DB so that it properly reflects the current config
    zpath_config_override_get_config_int(APP_SCALING_FEATURE, &(customer->app_scaling_feature_status_cur), DEFAULT_APP_SCALING_FEATURE, customer->customer_gid, ZPATH_GLOBAL_CONFIG_OVERRIDE_GID, 0);
    //compare the current config to the previous config and return if they mismatch to mark that it 'has_changed'
    return config_value != customer->app_scaling_feature_status_cur;
}

static int zpn_bca_app_scaling_changed_customer_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct customer_state *customer = object;
    if (!customer) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (zpn_bca_app_scaling_customer_config_has_changed(customer)) {
        ZPN_LOG(AL_INFO, "%"PRId64": %s has changed to %"PRId64, customer->customer_gid, APP_SCALING_FEATURE, customer->app_scaling_feature_status_cur);
        //Applying the new config only if it has changed for this customer to minimize churn
        zpn_bca_send_agg_domain_app_scaling_flag_changed(customer, (customer->app_scaling_feature_status_cur ? 1 : 0), zpn_client_type_total_count, zpn_platform_type_total_count);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_bca_android_app_scaling_customer_config_has_changed(struct customer_state *customer) {

    int app_scale_flag = zpn_is_app_scaling_enabled(customer->customer_gid);

    /* If app-scale feature flag is disabled, then no-op for subsequent feature flag change */
    if (0 == app_scale_flag) {
        return 0;
    }

    int64_t config_value = customer->android_app_scaling_feature_status_cur;

    //Updated cur to whatever is currently in the DB so that it properly reflects the current config
    zpath_config_override_get_config_int(ANDROID_APP_SCALING_FEATURE, &(customer->android_app_scaling_feature_status_cur), DEFAULT_ANDROID_APP_SCALING_FEATURE, customer->customer_gid, ZPATH_GLOBAL_CONFIG_OVERRIDE_GID, 0);

    //compare the current config to the previous config and return if they mismatch to mark that it 'has_changed'
    return config_value != customer->android_app_scaling_feature_status_cur;
}

static int zpn_bca_android_app_scaling_changed_customer_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct customer_state *customer = object;
    if (!customer) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (zpn_bca_android_app_scaling_customer_config_has_changed(customer)) {
        ZPN_LOG(AL_INFO, "Android feature flag value %"PRId64": %s has changed to %"PRId64, customer->customer_gid, ANDROID_APP_SCALING_FEATURE, customer->android_app_scaling_feature_status_cur);

        //Applying the new config only if it has changed for this customer to minimize churn
        zpn_bca_send_agg_domain_app_scaling_flag_changed(customer, customer->android_app_scaling_feature_status_cur, zpn_client_type_zapp, zpn_platform_type_android);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_broker_client_apps_android_app_scale_changed_for_all_customers(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    int64_t key = 0;

    if (zhash_table_get_size(customers)) {
        zhash_table_walk(customers, &key, zpn_bca_android_app_scaling_changed_customer_walk, NULL);
    }
}

static void zpn_client_app_scaling_android_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid)
{
    struct customer_state *customer;

    if (1 == customer_gid ||
        0 == customer_gid) {
        zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL), zpn_broker_client_apps_android_app_scale_changed_for_all_customers, NULL, 0);
    } else {
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_CLIENT("Customer gid %"PRId64" not found, skipping notification", customer_gid);
            return;
        }
        if (zpn_bca_android_app_scaling_customer_config_has_changed(customer)) {
            ZPN_LOG(AL_INFO, "Android feature flag value %"PRId64": %s has changed to %"PRId64, customer->customer_gid, ANDROID_APP_SCALING_FEATURE, customer->android_app_scaling_feature_status_cur);

            zpn_bca_send_agg_domain_app_scaling_flag_changed(customer, customer->android_app_scaling_feature_status_cur, zpn_client_type_zapp, zpn_platform_type_android);
        }
    }
}

/**** CC Broker App scale feature flag change ****/

/**
 * Checks if the ZPA customer configuration has changed for CC
 *
 * This function compares the current customer configuration with the previous
 * one to determine if any changes have been made that affect CC clients on Broker.
 *
 * @param customer The current customer state.
 * @return 1 if the configuration has changed, 0 otherwise.
 */
static int zpn_is_app_scaling_cc_customer_config_changed(struct customer_state *customer) {
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;
    char feature_flag[64];
    int64_t default_val;

    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_CC_APP_SCALING_FEATURE);
        default_val = DEFAULT_BROKER_CC_APP_SCALING_FEATURE;
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_CC_APP_SCALING_FEATURE);
        default_val = DEFAULT_PSE_CC_APP_SCALING_FEATURE;
    }

    int app_scale_flag = zpn_is_app_scaling_enabled(customer->customer_gid);
    /* If app-scale feature flag is disabled, then no-op for subsequent feature flag change */
    if (0 == app_scale_flag) {
        ZPN_DEBUG_CLIENT("Appscaling parent feature flag %s is disabled for customer %"PRId64" "
                         "hence not acting on child appscaling flag %s updates",
                         APP_SCALING_FEATURE, customer->customer_gid,
                         feature_flag);
        return 0;
    }

    int64_t prev_config_value = broker_type ?
                                customer->cc_broker_app_scaling_feature_status_cur :
                                customer->cc_pse_app_scaling_feature_status_cur;

    if (zpn_get_app_scaling_hard_disabled_status(zpn_client_type_edge_connector, 0, broker_type)) {
        if (broker_type) {
            customer->cc_broker_app_scaling_feature_status_cur = 0;
        } else {
            customer->cc_pse_app_scaling_feature_status_cur = 0;
        }
    } else {
        // set current value
        zpath_config_override_get_config_int(feature_flag,
                                         broker_type ? (&(customer->cc_broker_app_scaling_feature_status_cur)) : (&(customer->cc_pse_app_scaling_feature_status_cur)),
                                         default_val,
                                         customer->customer_gid,
                                         ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         0);
    }

    int64_t compare_val = broker_type ? customer->cc_broker_app_scaling_feature_status_cur : customer->cc_pse_app_scaling_feature_status_cur;
    // compare the current config to the previous config and return if they mismatch to mark that it 'has_changed'
    ZPN_DEBUG_COR("Customer gid %" PRId64 " %s %" PRId64 "-%" PRId64 " %s",
                  customer->customer_gid,
                  feature_flag,
                  prev_config_value,
                  compare_val,
                  prev_config_value != compare_val ? "updating" : " not updating");

    return prev_config_value != compare_val;
}

static int zpn_cc_app_scaling_changed_customer_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct customer_state *customer = object;
    if (!customer) {
        return ZPATH_RESULT_NO_ERROR;
    }

    char feature_flag[64];
    int64_t curr_val = 0;
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;

    if (zpn_is_app_scaling_cc_customer_config_changed(customer)) {
        if (broker_type) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_CC_APP_SCALING_FEATURE);
            curr_val = customer->cc_broker_app_scaling_feature_status_cur;
        } else {
            snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_CC_APP_SCALING_FEATURE);
            curr_val = customer->cc_pse_app_scaling_feature_status_cur;
        }
        ZPN_LOG(AL_INFO,
                "Customer: %"PRId64", CC feature flag: %s has changed to %" PRId64,
                customer->customer_gid,
                feature_flag,
                curr_val);

        // Applying the new config only if it has changed for this customer to minimize churn
        zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                         curr_val ? 1 : 0,
                                                         zpn_client_type_edge_connector,
                                                         zpn_platform_type_invalid);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void cc_app_scale_changed_for_all_customers(struct zevent_base *base,
                                                   void *void_cookie,
                                                   int64_t int_cookie) {
    int64_t key = 0;

    if (zhash_table_get_size(customers)) {
        zhash_table_walk(customers, &key, zpn_cc_app_scaling_changed_customer_walk, NULL);
    }
}

void zpn_app_scaling_cc_hard_disable_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid)
{
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;
    char feature_flag[64];
    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE);
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE);
    }
    ZPN_LOG(AL_INFO,
            "Feature flag '%s' customer=%" PRId64 " has changed to %" PRId64,
            feature_flag,
            customer_gid,
            *config_value);

    zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL),
                     cc_app_scale_changed_for_all_customers,
                     NULL,
                     0);
}

/**
 * Callback function triggered when the CC Broker or PSE application scaling feature flag is changed.
 *
 * @param config_value The new value of the feature flag.
 * @param customer_gid The GID of the customer whose feature flag has changed.
 */
static void zpn_app_scaling_cc_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid) {
    struct customer_state *customer = NULL;
    char feature_flag[64];
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;

    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_CC_APP_SCALING_FEATURE);
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_CC_APP_SCALING_FEATURE);
    }

    // doesnt happen often, ok to be logged
    ZPN_LOG(AL_INFO,
            "Feature flag '%s' customer=%" PRId64 " has changed to %" PRId64,
            feature_flag,
            customer_gid,
            *config_value);

    if (1 == customer_gid || 0 == customer_gid) {
        zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL),
                         cc_app_scale_changed_for_all_customers,
                         NULL,
                         0);
    } else {
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_COR("Customer gid %" PRId64 " not found, skipping notification", customer_gid);
            return;
        }
        if (zpn_is_app_scaling_cc_customer_config_changed(customer)) {
            int64_t changed_val = broker_type ? customer->cc_broker_app_scaling_feature_status : customer->cc_pse_app_scaling_feature_status;
            zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                             (changed_val ? 1 : 0),
                                                             zpn_client_type_edge_connector,
                                                             zpn_platform_type_invalid);
        } else {
            ZPN_DEBUG_COR(
                    "Customer gid %" PRId64 " not updated,'%s' no change", customer_gid, feature_flag);
        }
    }
}

/**** VDI App scale feature flag change ****/

/**
 * Checks if the ZPA customer configuration has changed for VDI
 *
 * This function compares the current customer configuration with the previous
 * one to determine if any changes have been made that affect VDI clients on Broker.
 *
 * @param customer The current customer state.
 * @return 1 if the configuration has changed, 0 otherwise.
 */
int zpn_is_app_scaling_vdi_customer_config_changed(struct customer_state *customer) {
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;
    char feature_flag[64];
    int64_t default_val;

    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_VDI_APP_SCALING_FEATURE);
        default_val = DEFAULT_BROKER_VDI_APP_SCALING_FEATURE;
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_VDI_APP_SCALING_FEATURE);
        default_val = DEFAULT_PSE_VDI_APP_SCALING_FEATURE;
    }


    int app_scale_flag = zpn_is_app_scaling_enabled(customer->customer_gid);
    /* If app-scale feature flag is disabled, then no-op for subsequent feature flag change */
    if (0 == app_scale_flag) {
        ZPN_DEBUG_CLIENT("Appscaling parent feature flag %s is disabled for customer %"PRId64" "
                         "hence not acting on child appscaling flag %s updates",
                         APP_SCALING_FEATURE, customer->customer_gid,
                         feature_flag);
        return 0;
    }

    int64_t prev_config_value = broker_type ?
                                customer->vdi_broker_app_scaling_feature_status_cur :
                                customer->vdi_pse_app_scaling_feature_status_cur;

    if (zpn_get_app_scaling_hard_disabled_status(zpn_client_type_vdi, 0, broker_type)) {
        if (broker_type) {
            customer->vdi_broker_app_scaling_feature_status_cur = 0;
        } else {
            customer->vdi_pse_app_scaling_feature_status_cur = 0;
        }
    } else {

        // set current value
        zpath_config_override_get_config_int(feature_flag,
                                         broker_type ? (&(customer->vdi_broker_app_scaling_feature_status_cur)) : (&(customer->vdi_pse_app_scaling_feature_status_cur)),
                                         default_val,
                                         customer->customer_gid,
                                         ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         0);
    }

    int64_t compare_val = broker_type ? customer->vdi_broker_app_scaling_feature_status_cur : customer->vdi_pse_app_scaling_feature_status_cur;
    // compare the current config to the previous config and return if they mismatch to mark that it 'has_changed'
    ZPN_DEBUG_COR("Customer gid %" PRId64 " %s %" PRId64 "-%" PRId64 " %s",
                  customer->customer_gid,
                  feature_flag,
                  prev_config_value,
                  compare_val,
                  prev_config_value != compare_val ? "updating" : " not updating");

    return prev_config_value != compare_val;
}

static int zpn_vdi_app_scaling_changed_customer_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct customer_state *customer = object;
    if (!customer) {
        return ZPATH_RESULT_NO_ERROR;
    }

    char feature_flag[64];
    int64_t curr_val = 0;
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;

    if (zpn_is_app_scaling_vdi_customer_config_changed(customer)) {
        if (broker_type) {
            snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_VDI_APP_SCALING_FEATURE);
            curr_val = customer->vdi_broker_app_scaling_feature_status_cur;
        } else {
            snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_VDI_APP_SCALING_FEATURE);
            curr_val = customer->vdi_pse_app_scaling_feature_status_cur;
        }
        ZPN_LOG(AL_INFO,
                "VDI Broker feature flag value %" PRId64 ": %s has changed to %" PRId64,
                customer->customer_gid,
                feature_flag,
                curr_val);

        // Applying the new config only if it has changed for this customer to minimize churn
        zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                         curr_val ? 1 : 0,
                                                         zpn_client_type_vdi,
                                                         zpn_platform_type_invalid);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void vdi_app_scale_changed_for_all_customers(struct zevent_base *base,
                                                    void *void_cookie,
                                                    int64_t int_cookie) {
    int64_t key = 0;

    if (zhash_table_get_size(customers)) {
        zhash_table_walk(customers, &key, zpn_vdi_app_scaling_changed_customer_walk, NULL);
    }
}

void zpn_app_scaling_vdi_hard_disable_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid)
{
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;
    char feature_flag[64];
    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE);
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE);
    }
    ZPN_LOG(AL_INFO,
            "Feature flag '%s' customer=%" PRId64 " has changed to %" PRId64,
            feature_flag,
            customer_gid,
            *config_value);

    zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL),
                     vdi_app_scale_changed_for_all_customers,
                     NULL,
                     0);
}

/**
 * Callback function triggered when the VDI Broker or PSE application scaling feature flag is changed.
 *
 * @param config_value The new value of the feature flag.
 * @param customer_gid The GID of the customer whose feature flag has changed.
 */
static void zpn_app_scaling_vdi_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid) {
    struct customer_state *customer = NULL;
    char feature_flag[64];
    int broker_type = ZPN_BROKER_IS_PUBLIC() ? 1 : 0;

    if (broker_type) {
        snprintf(feature_flag, sizeof(feature_flag), "%s", BROKER_VDI_APP_SCALING_FEATURE);
    } else {
        snprintf(feature_flag, sizeof(feature_flag), "%s", PSE_VDI_APP_SCALING_FEATURE);
    }

    // doesnt happen often, ok to be logged
    ZPN_LOG(AL_INFO,
            "Feature flag '%s' customer=%" PRId64 " has changed to %" PRId64,
            feature_flag,
            customer_gid,
            *config_value);

    if (1 == customer_gid || 0 == customer_gid) {
        zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL),
                         vdi_app_scale_changed_for_all_customers,
                         NULL,
                         0);
    } else {
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_COR("Customer gid %" PRId64 " not found, skipping notification", customer_gid);
            return;
        }
        if (zpn_is_app_scaling_vdi_customer_config_changed(customer)) {
            int64_t changed_val = broker_type ? customer->vdi_broker_app_scaling_feature_status : customer->vdi_pse_app_scaling_feature_status;
            zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                             (changed_val ? 1 : 0),
                                                             zpn_client_type_vdi,
                                                             zpn_platform_type_invalid);
        } else {
            ZPN_DEBUG_COR(
                    "Customer gid %" PRId64 " not updated,'%s' no change", customer_gid, feature_flag);
        }
    }
}

/*** Disable sipa code until it is supported ***/
#ifdef SIPA_APP_SCALE_CODE_ENABLED

/**
 * Checks if the ZPA customer configuration has changed for SIPA.
 *
 * This function compares { the current customer configuration with the previous
 * one to determine if any changes have been made that affect SIPA.
 *
 * @param customer The current customer state.
 * @return 1 if the configuration has changed, 0 otherwise.
 */
int zpn_is_app_scaling_sipa_customer_config_changed(struct customer_state *customer) {

    int app_scale_flag = zpn_is_app_scaling_enabled(customer->customer_gid);

    /* If app-scale feature flag is disabled, then no-op for subsequent feature flag change */
    if (0 == app_scale_flag) {
        ZPN_DEBUG_CLIENT("Customer gid %"PRId64" not updated due to scale flag", customer->customer_gid);
        return 0;
    }

    int64_t prev_config_value = customer->sipa_app_scaling_feature_status_cur;

    // set current value
    zpath_config_override_get_config_int(SIPA_APP_SCALING_FEATURE,
                                         &(customer->sipa_app_scaling_feature_status_cur),
                                         DEFAULT_SIPA_APP_SCALING_FEATURE,
                                         customer->customer_gid,
                                         ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         0);

    // compare the current config to the previous config and return if they mismatch to mark that it 'has_changed'
    ZPN_DEBUG_COR("Customer gid %" PRId64 " %s %" PRId64 "-%" PRId64 " %s",
                     customer->customer_gid,
                     SIPA_APP_SCALING_FEATURE,
                     prev_config_value,
                     customer->sipa_app_scaling_feature_status_cur,
                     prev_config_value != customer->sipa_app_scaling_feature_status_cur ? "updating" : " not updating");

    return prev_config_value != customer->sipa_app_scaling_feature_status_cur;
}

static int zpn_sipa_app_scaling_changed_customer_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct customer_state *customer = object;
    if (!customer) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (zpn_is_app_scaling_sipa_customer_config_changed(customer)) {
        ZPN_LOG(AL_INFO,
                "SIPA feature flag value %" PRId64 ": %s has changed to %" PRId64,
                customer->customer_gid,
                SIPA_APP_SCALING_FEATURE,
                customer->android_app_scaling_feature_status_cur);

        // Applying the new config only if it has changed for this customer to minimize churn
        zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                         customer->sipa_app_scaling_feature_status,
                                                         zpn_client_type_ip_anchoring,
                                                         zpn_platform_type_invalid);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void sipa_app_scale_changed_for_all_customers(struct zevent_base *base,
                                                                            void *void_cookie,
                                                                            int64_t int_cookie) {
    int64_t key = 0;

    if (zhash_table_get_size(customers)) {
        zhash_table_walk(customers, &key, zpn_sipa_app_scaling_changed_customer_walk, NULL);
    }
}

/**
 * Callback function triggered when the application scaling feature flag is changed.
 *
 * @param config_value The new value of the feature flag.
 * @param customer_gid The GID of the customer whose feature flag has changed.
 */
void zpn_app_scaling_feature_flag_sipa_changed_cb(const int64_t *config_value, int64_t customer_gid) {
    struct customer_state *customer = NULL;

    // doesnt happen often, ok to be logged
    ZPN_LOG(AL_INFO,
            "Feature flag '%s' customer=%" PRId64 " has changed to %" PRId64,
            SIPA_APP_SCALING_FEATURE,
            customer_gid,
            *config_value);

    if (1 == customer_gid || 0 == customer_gid) {
        zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL),
                         sipa_app_scale_changed_for_all_customers,
                         NULL,
                         0);
    } else {
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_COR("Customer gid %" PRId64 " not found, skipping notification", customer_gid);
            return;
        }
        if (zpn_is_app_scaling_sipa_customer_config_changed(customer)) {
            zpn_bca_send_agg_domain_app_scaling_flag_changed(customer,
                                                             customer->sipa_app_scaling_feature_status,
                                                             zpn_client_type_ip_anchoring,
                                                             zpn_platform_type_invalid);
        } else {
            ZPN_DEBUG_COR(
                    "Customer gid %" PRId64 " not updated,'%s' no change", customer_gid, SIPA_APP_SCALING_FEATURE);
        }
    }
}

#endif

static void zpn_broker_client_apps_app_scale_changed_for_all_customers(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    int64_t key = 0;

    if (zhash_table_get_size(customers)) {
        zhash_table_walk(customers, &key, zpn_bca_app_scaling_changed_customer_walk, NULL);
    }
}

/*
 * This function informs all the clients for
 * change in app scaling feature flag
 * @*config_value : Config value location with the current value
 * @customer_gid : Customer gid for which the value changed,
 *                 if 1 means flag changed globally and inform all the customers
 */
static void zpn_client_app_scaling_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid)
{
    struct customer_state *customer;

    if (1 == customer_gid ||
        0 == customer_gid) {
        zevent_base_call(zevent_get_for_class(APP_MISC_THREAD_POOL), zpn_broker_client_apps_app_scale_changed_for_all_customers, NULL, 0);
    } else {
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            ZPN_DEBUG_CLIENT("Customer gid %"PRId64" not found, skipping notification", customer_gid);
            return;
        }
        if (zpn_bca_app_scaling_customer_config_has_changed(customer)) {
            ZPN_LOG(AL_INFO, "%"PRId64": %s has changed to %"PRId64, customer->customer_gid, APP_SCALING_FEATURE, customer->app_scaling_feature_status_cur);
            zpn_bca_send_agg_domain_app_scaling_flag_changed(customer, customer->app_scaling_feature_status_cur, zpn_client_type_total_count, zpn_platform_type_total_count);
        }
    }
}

#define S(x) x == NULL ? "" : x

static int dump_app_seg_cb(void *cookie,
                           void *pPretty,
                           void *counter,
                           void *cookie4,
                           void *cookie5,
                           void *object,
                           void *key,
                           size_t key_len)
{

    struct zpath_debug_state *request_state = cookie;
    struct argo_object *app_object = object;
    struct zpn_application *app;
    int k;
    int i = ++(*(int *)counter);
    int res = 0;

    if (*(int *)pPretty == 2) {
        struct zpn_application *app = (struct zpn_application *)app_object->base_structure_void;
        ZDP("%3d. bypass='%-6s' ip_anchored=%d dbl_enc=%d icmp=%s  domains=[", i, S(app->bypass_type), app->ip_anchored,
            app->double_encrypt, S(app->icmp_access_type));

        for (k = 0; k < app->domain_names_count; k++) {
            ZDP("'%s',", S(app->domain_names[k]));
        }

        ZDP("]");

        if (app->tcp_port_ranges_count > 0) {
            ZDP(" tcp_port_ranges=[");
            for (k = 0; k < app->tcp_port_ranges_count; k++) {
                ZDP("%d%c", app->tcp_port_ranges[k], k % 2 ? ',' : '-');
            }
            ZDP("]");
        }
        ZDP("\n");
    } else {
        char dump[10000];
        app = app_object->base_structure_void;
        res = argo_structure_dump(zpn_application_description, app, dump, sizeof(dump), NULL, 0);
        if (!res) ZDP("%s\n", dump);
        argo_structure_dump(zpn_application_description, app_object->base_structure_void, dump, sizeof(dump), NULL,
                            *(int *)pPretty);
        ZDP("%s\n", dump);
    }

    return res;
}

void convert_app_cache_to_app_client(struct zpn_client_app *app_client, struct zpn_client_app_cache *app_cache)
{
    int i = 0;

    app_client->bypass = app_cache->bypass;
    app_client->ip_anchored = app_cache->ip_anchored;
    app_client->inspected = app_cache->inspected;
    app_client->bypass_on_reauth = app_cache->bypass_on_reauth;
    app_client->match_style = app_cache->match_style;
    app_client->double_encrypt = app_cache->double_encrypt;
    app_client->deleted = app_cache->deleted;
    app_client->bypass_type = zpn_broker_client_app_bypass_type_enum_to_string(app_cache->bypass_type, 0);
    app_client->icmp_access_type = zpn_broker_client_app_icmp_access_type_enum_to_string(app_cache->icmp_access_type, 0);
    app_client->tcp_port_ranges_count = app_cache->tcp_port_ranges_count;
    app_client->udp_port_ranges_count = app_cache->udp_port_ranges_count;

    app_client->tcp_port_ranges = ZPN_BCA_CALLOC(sizeof(*app_client->tcp_port_ranges) * app_client->tcp_port_ranges_count);
    for (i = 0; i < app_client->tcp_port_ranges_count; i++){
        app_client->tcp_port_ranges[i] = app_cache->tcp_port_ranges[i];
    }

    app_client->udp_port_ranges = ZPN_BCA_CALLOC(sizeof(*app_client->udp_port_ranges) * app_client->udp_port_ranges_count);
    for (i = 0; i < app_client->udp_port_ranges_count; i++){
        app_client->udp_port_ranges[i] = app_cache->udp_port_ranges[i];
    }
}

static int dump_app_noseg_cb(void *cookie,
                             void *pPretty,
                             void *counter,
                             void *cookie4,
                             void *cookie5,
                             void *object,
                             void *key,
                             size_t key_len)
{

    struct zpath_debug_state *request_state = cookie;
    struct zpn_client_app app_client_temp;
    struct zpn_client_app_cache *app_sent = object;
    int k;
    int i = ++(*(int *)counter);

    if (*(int *)pPretty == 2) {
        ZDP("%3d. bypass='%-6s' ip_anchored=%d inspected=%d dbl_enc=%d icmp=%s domain='%s'",
                i, zpn_broker_client_app_bypass_type_enum_to_string(app_sent->bypass_type, 0),
                app_sent->ip_anchored, app_sent->inspected, app_sent->double_encrypt,
                zpn_broker_client_app_icmp_access_type_enum_to_string(app_sent->icmp_access_type, 0), (char *)key);

        if (app_sent->tcp_port_ranges_count > 0) {
            ZDP(" tcp_port_ranges=[");
            for (k = 0; k < app_sent->tcp_port_ranges_count; k++) {
                ZDP("%d%c", app_sent->tcp_port_ranges[k], k % 2 ? ',' : '-');
            }
            ZDP("]");
        }

        if (app_sent->udp_port_ranges_count > 0) {
            ZDP(" udp_port_ranges=[");
            for (k = 0; k < app_sent->udp_port_ranges_count; k++) {
                ZDP("%d%c", app_sent->udp_port_ranges[k], k % 2 ? ',' : '-');
            }
            ZDP("]");
        }
        ZDP("\n");
    } else {
        char dump[10000];
        memset(&app_client_temp, 0, sizeof(app_client_temp));
        app_client_temp.app_domain = (char *)key;
        convert_app_cache_to_app_client(&app_client_temp, app_sent);
        struct argo_object *app_sent_object = argo_object_create(zpn_client_app_description, &app_client_temp);
        ZDP("%s\n",argo_object_dump_inline(app_sent_object, dump, sizeof(dump), 0));
        ZPN_BCA_FREE(app_client_temp.tcp_port_ranges);
        ZPN_BCA_FREE(app_client_temp.udp_port_ranges);
        argo_object_release(app_sent_object);
    }

    return ZPN_RESULT_NO_ERROR;
}

int dump_downloaded_app(struct zpath_debug_state *request_state, struct zpn_client_app_state *client, const char* tunnel_id, int* pretty)
{
    if (!client) {
        ZDP("The connection for tunnel_id %s has been closed or client doesnt download apps\n", tunnel_id);
    } else {
        /* This is called in a different thread for debug, we want to hold client untill it is done */
        client_hold(client);
        if (strcmp(client->tunnel_id, tunnel_id) == 0) {
            int counter = 0;
            ZPATH_MUTEX_LOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
            if(client->segment_download) {
                ZDP("---- dump applications with segment\n");
                zhash_table_walk2(client->apps_sent, NULL, dump_app_seg_cb, request_state, pretty, &counter, NULL, NULL);
            } else {
                ZDP("---- dump applications with no segment\n");
                zhash_table_walk2(client->apps_sent, NULL, dump_app_noseg_cb, request_state, pretty, &counter, NULL, NULL);
            }
            ZPATH_MUTEX_UNLOCK(&(client->apps_sent_lock), __FILE__, __LINE__);
        } else {
            ZDP("Cannot find client for tunnel id %s, the connection may be closed\n", tunnel_id);
        }
        client_release(client);
    }
    return 0;
}

/*
 * Is Policy Re Eval on posture change is hard disabled
 */
int64_t zpn_broker_is_policy_re_eval_on_posture_change_hard_disabled()
{
    return g_policy_re_eval_on_posture_change_hard_disabled;
}


/* Is Policy Reeval on posture change feature disabled
 * Return 1 if DR mode, hard disabled
 * Return !default value if unit tests
 */
static int zpn_broker_policy_re_eval_on_posture_change_is_disabled(void)
{
    if (is_unit_test()) {
        return !DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE;
    }

    if ((ZPN_BROKER_IS_PRIVATE() &&
        g_dr_mode_is_active) ||
        zpn_broker_is_policy_re_eval_on_posture_change_hard_disabled()) {
        return 1;
    }
    return 0;
}

int zpn_broker_policy_re_eval_on_posture_change_is_enabled(int64_t customer_gid)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    int status = 0;

    if (zpn_broker_policy_re_eval_on_posture_change_is_disabled()) {
        return 0;
    }

    status = customer->policy_re_eval_on_posture_chg_feature_status;
    ZPN_DEBUG_COR("Policy re-eval on posture change feature config value for customer_id %"PRId64" is %d",
                  customer_gid, status);

    return status ? 1 : 0;
}

int64_t zpn_broker_get_svcp_re_eval_duration(int64_t customer_gid)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    int64_t value = 0;

    if(customer){
        value = customer->policy_svcp_re_eval_freq_sec;
        ZPN_DEBUG_COR("Policy svcp re-eval frequency for customer_id %"PRId64" is %"PRId64"",
                  customer_gid, value);
    }else{
        ZPN_LOG(AL_ERROR, "Customer state NULL for customer_id %"PRId64"", customer_gid);
    }

    return value;
}

int zpn_broker_policy_svcp_is_enabled(int64_t customer_gid)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);
    int status = 0;

    status = customer->policy_svcp_enabled_feature_status;
    ZPN_DEBUG_COR("Policy SVCP feature config value for customer_id %"PRId64" is %d",
                  customer_gid, status);

    return status ? 1 : 0;
}

int zpn_broker_step_up_auth_is_enabled(int64_t customer_gid)
{
    int status = 0;
    struct customer_state *customer = get_customer_state(customer_gid, NULL);

    if (customer == NULL) {
        return status;
    }

    status = customer->step_up_auth_feature_status;
    ZPN_DEBUG_COR("Step up auth feature config value for customer_id %"PRId64" is %d",
                  customer_gid, status);

    return status ? 1 : 0;
}

int zpn_broker_dns_txt_query_support_is_enabled(int64_t customer_gid)
{
    struct customer_state *customer = get_customer_state(customer_gid, NULL);;
    int status = 0;

    status = customer->dns_txt_query_support_status;
    ZPN_DEBUG_COR("DNS TXT query support feature config value for customer_id %"PRId64" is %d",
                  customer_gid, status);

    return status ? 1 : 0;
}

int zpn_broker_policy_fqdn_to_srv_ip_enabled(int64_t customer_gid)
{
    struct customer_state  *customer = get_customer_state(customer_gid, NULL);
    int                     status = 0;
    int                     feature_status = 0;
    int                     hard_disabled_feature_status = 0;

    feature_status = customer->policy_fqdn_to_srv_ip_feature_status;
    hard_disabled_feature_status = g_policy_fqdn_to_srv_ip_hard_disabled_feature_status;

    ZPN_DEBUG_COR("Policy FQDN_TO_SRV_IP feature flag value for customer_id %"PRId64" is feature_config:%d hard_disable:%d",
                  customer_gid, feature_status, hard_disabled_feature_status);

    status = (!hard_disabled_feature_status && feature_status);

    return status;
}

int zpn_broker_app_scaling_bypass_improvement_enabled(int64_t customer_gid)
{
    const struct customer_state  *customer = get_customer_state(customer_gid, NULL);
    int                           status = 0;
    int64_t                       feature_status = 0;
    int64_t                       hard_disabled_feature_status = 0;

    feature_status = customer->app_scaling_bypass_improvement_feature_status;
    hard_disabled_feature_status = g_app_scaling_bypass_improvement_hard_disabled_feature_status;

    ZPN_DEBUG_COR("App scaling bypass improvement feature flag value for customer_id %"PRId64" is feature_config:%"PRId64" hard_disable:%"PRId64"",
                  customer_gid, feature_status, hard_disabled_feature_status);

    status = (!hard_disabled_feature_status && feature_status);

    return status;
}

/*
 * This function is used in conjunction with
 * zpn_broker_client_appscale_set_platform_clients
 *
 * For example: If android appscale feature flag flips then
 * only android clients needs to be informed
 * Func: zpn_broker_client_appscale_set_platform_clients set the platform/client accordingly
 * for zpn_lib_appscale_client_cookie, and when parsing through all clients
 * of the customer zpn_broker_client_apps_send_to_specific_client check if
 * current client is android then only send that RPC else skip
 */
int zpn_broker_client_apps_send_to_specific_client(struct zpn_lib_appscale_client_cookie *cookie,
                                                   struct zpn_client_app_state *client,
                                                   struct zpn_broker_client_fohh_state *c_state)
{
    if (!cookie) return 0;
    if (!client && !c_state) return 0;

    int platform_type = (c_state) ? (c_state->platform_type) : (client->platform_type);
    int client_type = (c_state) ? (c_state->client_type) : (client->client_type);

    if ((cookie->platforms[platform_type] == 1) &&
        (cookie->clients[client_type] == 1)) {
        return 1;
#ifdef SIPA_APP_SCALE_CODE_ENABLED
    } else if (client_type == zpn_client_type_ip_anchoring &&
               cookie->clients[client_type] == 1) {
        return 1;
#endif
    } else if (client_type == zpn_client_type_edge_connector &&
               cookie->clients[client_type] == 1) {
        return 1;
    } else if (client_type == zpn_client_type_vdi &&
               cookie->clients[client_type] == 1) {
        return 1;
    }

    return 0;
}

/* This utility is to trigger
 * for which platform/clients we are performing the operation
 * For Appscale feature flag is for all platform/clients
 * For Android feature flag is for ZCC Android
 * Extend for BC/CC/SIPA in future
 */
void zpn_broker_client_appscale_set_platform_clients(struct zpn_lib_appscale_client_cookie *pclient,
                                                     enum zpn_client_type client,
                                                     enum zpn_platform_type platform)
{
    if (pclient) {
        if ((platform == zpn_platform_type_total_count) && (client == zpn_client_type_total_count)) {
            // update all clients and platforms
            pclient->platforms[zpn_platform_type_windows] = 1;
            pclient->platforms[zpn_platform_type_mac] = 1;
            pclient->platforms[zpn_platform_type_android] = 1;

            pclient->clients[zpn_client_type_zapp] = 1;
            pclient->clients[zpn_client_type_zapp_partner] = 1;
            pclient->clients[zpn_client_type_machine_tunnel] = 1;

#ifdef SIPA_APP_SCALE_CODE_ENABLED
            pclient->clients[zpn_client_type_ip_anchoring] = 1;
#endif
            pclient->clients[zpn_client_type_edge_connector] = 1;
            pclient->clients[zpn_client_type_vdi] = 1;

        } else if ((platform == zpn_platform_type_android) && (client == zpn_client_type_zapp || client == zpn_client_type_zapp_partner || client == zpn_client_type_machine_tunnel)) {

            pclient->platforms[zpn_platform_type_android] = 1;

            pclient->clients[zpn_client_type_zapp] = 1;
            pclient->clients[zpn_client_type_zapp_partner] = 1;
            pclient->clients[zpn_client_type_machine_tunnel] = 1;

#ifdef SIPA_APP_SCALE_CODE_ENABLED
        } else if (client == zpn_client_type_ip_anchoring) {
            pclient->clients[zpn_client_type_ip_anchoring] = 1;
#endif
        } else if (client == zpn_client_type_edge_connector) {
            pclient->clients[zpn_client_type_edge_connector] = 1;
        } else if (client == zpn_client_type_vdi) {
            pclient->clients[zpn_client_type_vdi] = 1;
        }
    }
}

static void zpn_broker_client_apps_update_domain_sent_skip_stats(struct zpn_client_app_state *client)
{
    if (client) {
        zpn_broker_appscale_stats[client->client_type][client->app_thread_id].domain_sent_skip_cnt++;
        if (zpn_lib_is_app_scale_zcc_clients(client->client_type)) {
            if (client->platform_type == zpn_platform_type_windows) {
                zpn_broker_appscale_stats[client->client_type][client->app_thread_id].domain_sent_skip_win_cnt++;
            } else if (client->platform_type == zpn_platform_type_mac) {
                zpn_broker_appscale_stats[client->client_type][client->app_thread_id].domain_sent_skip_mac_cnt++;
            } else if (client->platform_type == zpn_platform_type_android) {
                zpn_broker_appscale_stats[client->client_type][client->app_thread_id].domain_sent_skip_android_cnt++;
            }
        }
    }
}
