
/*
 * zpn_broker_mtunnel.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */


#include <sys/queue.h>
#include <openssl/rand.h>

#include <arpa/inet.h>
#include "base64/base64.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_location.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_instance.h"
#include "zsaml/zsaml.h"
#include "zpn_zdx/zpn_zdx_http.h"
#include "zpn_zdx/zpn_zdx_webprobe_rate_limit.h"
#include "zpath_lib/zpath_partition_common.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_mtunnel_stats.h"
#include "zpn/zpn_broker_policy.h"
#include "zpn/zpn_rule_to_server_group.h"
#include "zpn/zpn_rule_to_assistant_group.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_location_group_to_location.h"
#include "zpn/zpn_rule_to_location.h"
#include "zpn/zpn_rule_to_location_group.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_broker_client_path_cache.h"
#include "zpn/zpn_broker_client_path_cache_cfg.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_broker_transit.h"
#include "zpn/zpn_broker_client_scim.h"
#include "zpn/zpn_broker_client_user_risk.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_scope.h"
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_jit_approval_policy.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_partition_stats.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_scope_engine.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_shared_customer_domain.h"
#include "zpn/zpn_broker_client_neg_path_cache.h"
#include <sys/param.h>
#include "zpn/zpn_siem_tx.h"
#include "zpn/zins_inspection/zins_inspection.h"
#include "zpn/zins_inspection/zins_inspection_private.h"
#include "zpn/zpn_broker_natural.h"
#include "zpn/zpn_broker_natural_compiled.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_eas/zpn_eas.h"
#include "zpn/zpn_broker_stepup_auth.h"

#include "zpn/zpn_broker_dispatch_c2c_client_check.h"
#include "zpn/zpn_c2c_client_registration.h"

extern int64_t g_app_zia_inspection_disable;
extern int g_hard_close;


#define  ZPN_BROKER_MTUNNEL_MAX_AST_GRP_SZ  20
#define  MAX_BUFFERED_GROUPS  300
#define  MTUNNEL_DEFAULT_IDLE_TIMEOUT_S     (2 * 3600)

void init_mtunnel_stats_ptr(struct generic_mtunnel_stats *generic_stats, enum zpn_client_type client_type);
int zpn_broker_app_route_info_local_cb(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_app_route_info *msg);

static inline int64_t is_zpn_zdx_https_webprobe_disabled(int64_t cst_id)
{
    // Check if the feature is disabled globally
    int64_t is_https_disabled = 0;

    is_https_disabled = zpath_config_override_get_config_int(ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED,
                                                             &is_https_disabled,
                                                             ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED_DEFAULT,
                                                             (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                             (int64_t)0);

    /* If hard_disable is 1, then https is disabled for the entire cloud */

    if (is_https_disabled != ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED_DEFAULT) {
        return is_https_disabled;
    }

    int64_t is_https_enabled = 0;

    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        is_https_enabled = zpath_config_override_get_config_int(ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED,
                                                                &is_https_enabled,
                                                                ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT,
                                                                cst_id,
                                                                root_customer_gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
    } else {
        is_https_enabled = zpath_config_override_get_config_int(ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED,
                                                                &is_https_enabled,
                                                                ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT,
                                                                cst_id,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
    }

    return !is_https_enabled;

}

// below two variables are defined for testing only
static int64_t zpn_broker_test_mtunnels_limit_customer_gid = -1;
static int64_t zpn_broker_test_mtunnels_limit_value = -1;

static int64_t zpn_broker_get_max_mtunnels_per_user(int64_t customer_gid)
{
    if (customer_gid == zpn_broker_test_mtunnels_limit_customer_gid) {
        return zpn_broker_test_mtunnels_limit_value;
    }

    int64_t mtunnel_limit = -1;

    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        mtunnel_limit = zpath_config_override_get_config_int(
                BROKER_MAX_MTUNNEL_PER_USER,
                &mtunnel_limit,
                BROKER_MAX_MTUNNEL_PER_USER_DEFAULT,
                customer_gid,
                root_customer_gid,
                (int64_t)0);
    }

    return mtunnel_limit;
}

static int zpn_broker_exceed_mtunnel_limit_check(struct zpn_broker_client_fohh_state *c_state,
        int64_t *mtunnel_number, int64_t *mtunnel_limit)
{
    int64_t customer_id = c_state->customer_gid;
    *mtunnel_number = c_state_get_mtunnel_count(c_state);

    // refresh the limit settings per concurrent 1000 mtunnels in case it is changed
    if (c_state->max_num_mtunnel == 0 || *mtunnel_number % 1000 == 0 ) {
        c_state->max_num_mtunnel = zpn_broker_get_max_mtunnels_per_user(customer_id);
    }

    if (c_state->max_num_mtunnel < 0) {
        // not configured, take it as unlimited, i.e. not exceed the limit
        return 0;
    }

    *mtunnel_limit = c_state->max_num_mtunnel;

    return *mtunnel_number >= *mtunnel_limit;
}

#define TRANSACTION_LOG_PERIOD            300          /* 5 min in seconds */
#define REAPED_LIST_LOG_INTERVAL_US       600000000L  /* 10 min in usec */


int64_t track_g_app = 0;
int64_t track_g_usr = 0;

int config_log_repeats = 1;

extern char *geoip_db_file;

static struct zpn_broker_mtunnel_free_queue  free_q;

struct zpn_pbroker_mtunnel_stats mstats;

static int zpn_transaction_log_throttling_on = 1;

/* Store keys as strings + null terminator in hash */
static struct zhash_table* transaction_log_throttling_errors_hash = NULL;

/* Err codes response buf size */
#define WALK_ERR_OUTPUT_BUF_LEN     2048

static const char *mtunnel_states[] = {
    "zbms_request_received",
    "zbms_authenticated",
    "zbms_probe_sent",
    "zbms_zia_inspection_start",
    "zbms_zia_inspection_ready",
    "zbms_zia_inspection_bypass",
    "zbms_dispatch_sent",
    "zbms_assistant_ack_sent", // this is zbms_assistant_bound, not sure why the discrepancy, keeping it as is
    "zbms_complete",
    "zbms_reaping",
    "zbms_free",
};

static struct {
    int64_t number_of_bind_ack_msgs_to_be_dropped;
} ut_hook;

struct argo_structure_description*  natural_transaction_throttle_logs_stats_description = NULL;

void zpn_broker_mtunnel_free(struct zpn_broker_mtunnel *mtunnel);
static int zpn_mtunnel_peer_assistant_mconn(struct zpn_broker_mtunnel *mt, int check_peer);
static int zpn_broker_mtunnel_c2c_regex_match_check(struct zpn_broker_mtunnel *mtunnel);

const char *mtunnel_state(enum zpn_broker_mtunnel_state state) {
    return mtunnel_states[state];
}

static int zpn_transaction_log_add_throttling_error_code(const char *error_str)
{
    int res;

    char *error_str2 = (char *)error_str; /* This is ok because internally the has will make a copy as data is not external */

    /* We store and match the error string null + null terminator because we need to access them as null terminated strings during walk */

    res = zhash_table_store(transaction_log_throttling_errors_hash, error_str2, strlen(error_str2) + 1, 0,
                            &zpn_transaction_log_throttling_on);

    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to set log throttling for this error code: '%s'\n", error_str2);
    }

    return res;
}

static int zpn_transaction_log_remove_throttling_error_code(const char *error_str)
{
    int res;


    /* We store and match the error string null + null terminator */
    res = zhash_table_remove(transaction_log_throttling_errors_hash, error_str, strlen(error_str) + 1,
                             &zpn_transaction_log_throttling_on);

    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to set log throttling for this error code: '%s'\n", error_str);
    }

    return res;
}


static int zpn_transaction_log_exists_throttling_error_code(const char *error_str, int *err_code_throttling_on)
{
    int res = ZPATH_RESULT_NO_ERROR;
    void *data = NULL;


    if (!err_code_throttling_on) {
        ZPN_LOG(AL_ERROR, "Invalid null throttle flag arg");
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    data = zhash_table_lookup(transaction_log_throttling_errors_hash, error_str, strlen(error_str) + 1, NULL);
    if (!data) {
        return ZPN_RESULT_NOT_FOUND;
    }

    *err_code_throttling_on = *((int *)data);

    return res;
}

int walk_err_code_data(void *cookie,
                       void *data,
                       size_t len)
{
    char *error_str = (char *)data;
    char *output_buf = (char *)cookie;
    size_t dest_buf_len;
    size_t dest_str_len;

    if (error_str && output_buf) {
        dest_buf_len = WALK_ERR_OUTPUT_BUF_LEN;
        dest_str_len = strlen(output_buf);

        /* Add error string */
        strncat(output_buf, error_str, dest_buf_len - dest_str_len - 1);

        /* Re adjust and add newline */
        dest_str_len = strlen(output_buf);

        strncat(output_buf, "\n", dest_buf_len - dest_str_len - 1);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_transaction_get_enabled_throttling_error_codes(char *output_buf)
{
    int res = ZPATH_RESULT_NO_ERROR;

    /* Empty ? */
    if (zhash_table_get_size(transaction_log_throttling_errors_hash) == 0) {
        return res;
    }

    /* Walk and collect throttled errors */
    res = zhash_table_walk_data(transaction_log_throttling_errors_hash, walk_err_code_data, output_buf);

    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to iterate log throttling error codes\n");
    }

    return res;
}

int zpn_transaction_log_add_throttling_error(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    int res;
    int exists = 0;
    char output_buf[WALK_ERR_OUTPUT_BUF_LEN] = "";

    if (query_value_count != 1) {
        ZDP("Require a single error parameter\n");
        return ZPN_RESULT_NO_ERROR;
    }
    if (query_values[0]) {

        res = zpn_transaction_log_exists_throttling_error_code(query_values[0], &exists);
        if (res == ZPATH_RESULT_NO_ERROR) {
            ZDP("Unable to add err code: %s, already exists\n", query_values[0]);
            return ZPN_RESULT_NO_ERROR;
        }

        res = zpn_transaction_log_add_throttling_error_code(query_values[0]);
        if (res) {
            ZDP("Unable to add error code: %s\n", query_values[0]);
        }

        res = zpn_transaction_get_enabled_throttling_error_codes(output_buf);
        if (res) {
            ZDP("Unable to show error codes\n");
        } else if (strlen(output_buf) > 0) {
            ZDP("%s", output_buf);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_transaction_log_remove_throttling_error(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    int res;
    char output_buf[WALK_ERR_OUTPUT_BUF_LEN] = "";

    if (query_value_count != 1) {
        ZDP("Require a single error parameter\n");
        return ZPN_RESULT_NO_ERROR;
    }
    if (query_values[0]) {
        res = zpn_transaction_log_remove_throttling_error_code(query_values[0]);
        if (res) {
            ZDP("Unable to remove error code: %s\n", query_values[0]);
        }

        res = zpn_transaction_get_enabled_throttling_error_codes(output_buf);
        if (res) {
            ZDP("Unable to show error codes\n");
        } else  if (strlen(output_buf) > 0) {
            ZDP("%s", output_buf);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_transaction_log_show_throttling_error(struct zpath_debug_state *request_state,
                                                const char **query_values,
                                                int query_value_count,
                                                void *cookie)
{
    int res;
    char output_buf[WALK_ERR_OUTPUT_BUF_LEN] = "";

    if (query_value_count != 0) {
        ZDP("Require no parameters\n");
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_transaction_get_enabled_throttling_error_codes(output_buf);
    if (res) {
        ZDP("Unable to show error codes\n");
    } else if (strlen(output_buf) > 0) {
        ZDP("%s", output_buf);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_transaction_log_init_throttling_error_code(void)
{
    int res = ZPATH_RESULT_NO_ERROR;

    if (!transaction_log_throttling_errors_hash) {
        transaction_log_throttling_errors_hash = zhash_table_alloc(&zpn_allocator);
        if (!transaction_log_throttling_errors_hash) {
            ZPN_LOG(AL_DEBUG, "Failed to allocate memory for throttling error hash");
            return ZPN_RESULT_NO_MEMORY;
        }
    }

    return res;
}

int zpn_transaction_log_init_throttling(void)
{
    int res = ZPN_RESULT_NO_ERROR;
    int i = 0;
    static char *throttled_errors[] = { "BRK_MT_SETUP_FAIL_SAML_EXPIRED", NULL };

    res = zpn_transaction_log_init_throttling_error_code();
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to init zpn_transaction_log_init_throttling_error_code: %s", zpath_result_string(res));
        return res;
    }

    while (throttled_errors[i] != NULL) {
        res = zpn_transaction_log_add_throttling_error_code(throttled_errors[i]);
        if (res) {
            ZPN_LOG(AL_ERROR, "add throttle error code failed, code: %s, reason: %s", throttled_errors[i], zpath_result_string(res));
            return res;
        }
        i++;
    }

    res = ZPN_RESULT_NO_ERROR;

    return res;
}

int zpn_transaction_log_throttling(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie)
{
    ZPN_LOG(AL_DEBUG, "Toggling log throttling state");
    if (zpn_transaction_log_throttling_on) {
        zpn_transaction_log_throttling_on = 0;
        ZDP("Transaction log throttling was on, turning it off\n");
    } else {
        zpn_transaction_log_throttling_on = 1;
        ZDP("Transaction log throttling was off, turning it on\n");
    }
    return ZPN_RESULT_NO_ERROR;
}

static int global_bind_client_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    *global_owner_incarnation = mtunnel->incarnation;

    return ZPN_RESULT_NO_ERROR;
}


static int global_unbind_client_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;
    int res = ZPN_RESULT_NO_ERROR;

    if (!mtunnel) return res;

    if (err && !mtunnel->err) mtunnel->err = ZPN_STRDUP(err, strlen(err));

    /* We are still guaranteed to have valid c_state at this point,
     * for logging. And the mtunnel has been unbound for a reason, so
     * we will have the appropriate reason code */
    mtunnel->log.action = "close";
    if (!mtunnel->log.end_us) {
        mtunnel->log.end_us = epoch_us();
    }

    if (1 == mtunnel->zia_inspection) {
        struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
        if (c_state && c_state->client_type == zpn_client_type_zia_inspection) {
            struct sme_conn *zi_conn = (struct sme_conn *)c_state->zins_cookie;
            ZPN_DEBUG_ZINS("reaping %s %p", mtunnel->tunnel_id, zi_conn);
            if (zi_conn) {
                __sync_fetch_and_add_4(&zi_conn->num_of_current_mtunnels, -1);
            }
        }
    }

    zpn_broker_mtunnel_log(mtunnel);
    mtunnel->no_more_logs = 1;

    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        if (!mtunnel->client_tlv.mconn.local_owner) {
            mtunnel->_client_f_conn = NULL;
        }
    } else if (mtunnel->client_tlv_type == zpn_zrdt_tlv) {
        if (!mtunnel->client_tlv_zrdt.mconn.local_owner) {
            mtunnel->client_z_conn = NULL;
        }
    } else {
        /* Bad tlv type ??*/
    }

    return res;
}


static int global_bind_assistant_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length,
                                    int64_t *global_owner_incarnation)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    *global_owner_incarnation = mtunnel->incarnation;

    return ZPN_RESULT_NO_ERROR;
}


static int global_unbind_assistant_cb(void *mconn_base,
                                      void *mconn_self,
                                      void *global_owner,
                                      void *global_owner_key,
                                      size_t global_owner_key_length,
                                      int64_t global_owner_incarnation,
                                      int drop_buffered_data,
                                      int dont_propagate,
                                      const char *err)
{
    int res = ZPN_RESULT_NO_ERROR;

    return res;
}


static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    mtunnel_lock(mtunnel);
}


static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    mtunnel_unlock(mtunnel);
}

static int global_ip_proto_cb(void *mconn_base,
                              void *mconn_self,
                              void *global_owner,
                              void *global_owner_key,
                              size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = global_owner;

    return mtunnel->ip_protocol;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = global_owner;

    return mtunnel->double_encrypt;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    return (struct zpn_mconn *)mconn_base;
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    return mtunnel->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    if (mtunnel->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    return mtunnel->mtunnel_id;
}

static struct zpn_mconn *global_get_peer_client_mconn(void *mconn_base,
                                                      void *mconn_self,
                                                      void *global_owner,
                                                      void *global_owner_key,
                                                      size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    if (!mtunnel) {
        return NULL;
    }

    return zpn_broker_mtunnel_client_mconn(mtunnel);
}

static struct zpn_mconn *global_get_peer_assistant_mconn(void *mconn_base,
                                                         void *mconn_self,
                                                         void *global_owner,
                                                         void *global_owner_key,
                                                         size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    if (!mtunnel) {
        return NULL;
    }

    return zpn_broker_mtunnel_assistant_mconn(mtunnel);
}

static int64_t global_get_customer_gid(void *mconn_base,
                                       void *mconn_self,
                                       void *global_owner,
                                       void *global_owner_key,
                                       size_t global_owner_key_length)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;

    if (!mtunnel) {
        return 0;
    }

    return mtunnel->customer_id;
}

int mtunnel_request_ack(struct zpn_broker_mtunnel *mtunnel,
                        int64_t conn_incarnation,
                        int32_t tag,
                        const char *tunnel_id,
                        const char *error,
                        const char *reason)
{
    struct zpn_tlv *tlv = zpn_broker_mtunnel_client_tlv(mtunnel);
    struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    int64_t reauth_s = mtunnel->reauth_timeout;

    if (tlv) {
        char output[100] = {0};
        ZPN_DEBUG_MTUNNEL("zpn_send_zpn_mtunnel_request_ack: sending reauth timeout:%" PRIu64
                          " for mtunnel:%s reason='%s' error='%s'",
                          reauth_s, mtunnel->mtunnel_id, reason ?: "", error ?: "");
        ZPN_DEBUG_ZINS("'%s': send_zpn_mtunnel_request_ack: server-5-tuple='%s'", mtunnel->mtunnel_id,
                       zins_mtunnel_server_5_tuple_locked(mtunnel, output, sizeof(output)));
        if (c_state && zpn_client_static_config[c_state->client_type].gets_ips) {
            return zpn_send_zpn_mtunnel_request_ack(tlv,
                                                conn_incarnation,
                                                reauth_s,
                                                tag,
                                                mtunnel->log.a_port,
                                                mtunnel->log.s_port,
                                                &(mtunnel->log.a_ip),
                                                &(mtunnel->log.s_ip),
                                                mtunnel->mtunnel_id,
                                                error,
                                                reason,
                                                mtunnel->required_stepup_auth_level_id);
        } else {
            return zpn_send_zpn_mtunnel_request_ack(tlv,
                                                conn_incarnation,
                                                reauth_s,
                                                tag,
                                                0,
                                                0,
                                                NULL,
                                                NULL,
                                                mtunnel->mtunnel_id,
                                                error,
                                                reason,
                                                mtunnel->required_stepup_auth_level_id);
        }
    } else {
        ZPN_DEBUG_MTUNNEL("Client connection gone, cannot send mtunnel_request_ack back");
    }
    ZPN_LOG(AL_ERROR, "Unexpected lack of f_conn");
    return ZPATH_RESULT_ERR;
}


static void mtunnel_async_terminate_callback(struct fohh_thread *thread, void *term_cookie, int64_t int_cookie)
{
    struct terminate_cookie *cookie = term_cookie;
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int bucket_id = -1;

    if (!cookie) {
        ZPN_LOG(AL_CRITICAL, "Async terminate with NULL cookie");
        return;
    }

    if (!cookie->mtunnel_id) {
        ZPN_LOG(AL_CRITICAL, "Async terminate with NULL mtunnel_id");
        return;
    }

    ZPN_DEBUG_MTUNNEL("global_mtunnel_terminate_callback(), mtunnel_id = %s", cookie->mtunnel_id);

    mtunnel_id_len = strlen(cookie->mtunnel_id);
    mtunnel_id_hash = CityHash64(cookie->mtunnel_id, mtunnel_id_len);
    mtunnel = mtunnel_lookup_and_bucket_lock(cookie->mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (mtunnel) {
        /* We are the original requesting broker, send mtunnel_req_ack back to client */
        ZPN_LOG(AL_NOTICE, "%s: Async terminate for mtunnel, state = %s",
                mtunnel->mtunnel_id, mtunnel_state(mtunnel->state));

        /* FIXME: Should we send out message to other modules connected to us? */

        mtunnel_lock(mtunnel);

        mtunnel_locked_destroy(mtunnel, 0, 0, 1, cookie->error);
        mtunnel_locked_state_machine(mtunnel);

        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    } else {
        /* mtunnel probably has been deleted already. Just ignore the call */
    }

    ZPN_FREE(cookie->mtunnel_id);
    if (cookie->error) ZPN_FREE(cookie->error);
    ZPN_FREE(cookie);
}

/*
 * Locking: it is assumed we have the lock to mtunnel at this point
 */
static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error)
{
    struct zpn_broker_mtunnel *mtunnel = mconn_self;
    struct terminate_cookie *cookie;
    int i;

    if (!mtunnel) {
        ZPN_LOG(AL_CRITICAL, "Terminating NULL mtunnel!");
        return;
    }

    ZPN_BROKER_ASSERT_HARD(mtunnel->mtunnel_id != NULL, "mtunnel_id is NULL!");

    if (mtunnel->termination_started) {
        ZPN_LOG(AL_WARNING, "%s: Terminating already started.", mtunnel->mtunnel_id);
        return;
    }

    cookie = ZPN_CALLOC(sizeof(struct terminate_cookie));
    if (!cookie) {
        ZPN_LOG(AL_CRITICAL, "%s: No more memory for terminate cookie!", mtunnel->mtunnel_id);
        return;
    }

    cookie->mtunnel_id = ZPN_STRDUP(mtunnel->mtunnel_id, strlen(mtunnel->mtunnel_id));
    if (!cookie->mtunnel_id) {
        ZPN_LOG(AL_CRITICAL, "No more memory for mtunnel_id!");
        ZPN_FREE(cookie);
        return;
    }

    if (error) {
        cookie->error = ZPN_STRDUP(error, strlen(error));
        if (!cookie->error) {
            ZPN_LOG(AL_CRITICAL, "%s: No more memory for error!", mtunnel->mtunnel_id);
            ZPN_FREE(cookie->mtunnel_id);
            ZPN_FREE(cookie);
            return;
        }
    }

    /* Loop through all the threads until we find one that will accept our call */
    for (i = 0; i < fohh_thread_count(); i++) {
        if (fohh_thread_call(i,
                             mtunnel_async_terminate_callback,
                             cookie,
                             0) == FOHH_RESULT_NO_ERROR) {
            mtunnel->termination_started = 1;
            return;
        }
    }

    /* Erk... All threads have probably gone? Nothing matters anymore ..., whatever */
    ZPN_LOG(AL_CRITICAL, "%s: Cannot do thread call", mtunnel->mtunnel_id);

    // zpn free takes care of null pointer check
    ZPN_FREE(cookie->mtunnel_id);
    ZPN_FREE(cookie->error);
    ZPN_FREE(cookie);
}
// non static for ut
struct zpn_mconn_global_owner_calls client_global_call_set = {
    global_bind_client_cb,
    global_unbind_client_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id,
    global_get_peer_assistant_mconn,
    global_get_customer_gid
};
struct zpn_mconn_global_owner_calls assistant_global_call_set = {
    global_bind_assistant_cb,
    global_unbind_assistant_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id,
    global_get_peer_client_mconn,
    global_get_customer_gid
};


/* Peer client with assistant_mconn */
static int zpn_mtunnel_peer_assistant_mconn(struct zpn_broker_mtunnel *mt, int check_peer)
{
    int res = ZPN_RESULT_NO_ERROR;

    /* Check only if asked to do so */
    if (check_peer) {
        mtunnel_lock(mt);
        /* Check if client_tlv is already peered with something */
        if (mt->client_tlv.mconn.peer == &(mt->assistant_tlv.mconn)) {
            /* Already peered with assistant_tlv */
            ZPN_LOG(AL_DEBUG, "%s: c_mt->client_tlv.mconn: %p is already peered with assistant_tlv.mconn: %p",
                    mt->mtunnel_id, &(mt->client_tlv.mconn), mt->client_tlv.mconn.peer);
            res = ZPN_RESULT_NO_ERROR;
        } else if (mt->client_tlv.mconn.peer) {
            /* Peered with some other mconn ? */
            ZPN_LOG(AL_DEBUG, "%s: c_mt->client_tlv.mconn: %p is already peered with unknown peer: %p, current assistant_tlv.mconn: %p",
                    mt->mtunnel_id, &(mt->client_tlv.mconn), mt->client_tlv.mconn.peer, &(mt->assistant_tlv.mconn));
            res = ZPN_RESULT_BAD_STATE;
        }
        mtunnel_unlock(mt);
    }

    if (!res) {

        /* Not already peered, peer now */
        mtunnel_lock(mt);
        ZPN_LOG(AL_DEBUG, "%s: Peering client_tlv.mconn: %p with assistant_tlv mconn: %p", mt->mtunnel_id, &(mt->client_tlv.mconn), &(mt->assistant_tlv.mconn));
        res = zpn_mconn_connect_peer(&(mt->client_tlv.mconn), &(mt->assistant_tlv.mconn));
        if (res) {
            ZPN_LOG(AL_DEBUG, "%s: Unable to peer client_tlv.mconn: %p with assistant_tlv mconn: %p, res: %s",
                    mt->mtunnel_id, &(mt->client_tlv.mconn), &(mt->assistant_tlv.mconn), zpath_result_string(res));
            mtunnel_unlock(mt);
            return res;
        }
        mtunnel_unlock(mt);

        if (!res) {
            ZPN_LOG(AL_DEBUG, "%s: Adding assistant tlv global owner", mt->mtunnel_id);

            /* This will internally acquire lock via lock callback */
            res = zpn_mconn_add_global_owner(&(mt->assistant_tlv.mconn),
                                             0,
                                             mt,
                                             NULL,
                                             0,
                                             &assistant_global_call_set);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Could not bind client global owner.", mt->mtunnel_id);
            }
        }
    }

    return res;
}

static struct zpn_zdx_cookie *get_zpn_zdx_cookie(void) {
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)ZPN_CALLOC(sizeof(struct zpn_zdx_cookie));
    memset(zdx_cookie, 0, sizeof(*zdx_cookie));
    return zdx_cookie;
}

static void reset_zpn_zdx_cookie(void *cookie) {
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)cookie;
    if (zdx_cookie) ZPN_FREE(zdx_cookie);
    zdx_cookie = NULL;
}

static void zpn_reset_http_parse_state(char* transform_name, void* processor_cookie)
{
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)processor_cookie;
    zpn_free_http_parse_state(zdx_cookie->ps);
    zdx_cookie->ps = NULL;
}


static void setup_http_config_inspection_pipeline(struct zpn_mconn* mconn, void* cookie)
{
    zpn_mconn_setup_pipeline(mconn, "zdx mtr config request processing");

    struct zpn_zdx_cookie *zdx_cookie = cookie;
    zdx_cookie->ps = zpn_get_http_parse_state();
    zdx_cookie->ps->data = &(zdx_cookie->mtr);
    zdx_cookie->ps->probe_info_data = &(zdx_cookie->probe_info);
    zpn_mconn_add_transform_to_pipeline(mconn, zpn_process_zdx_http_request, zdx_cookie, "ZDX_CONFIG_PARSER", zpn_reset_http_parse_state);
}

static void setup_http_config_response_pipeline(struct zpn_mconn* mconn, void* cookie)
{
    zpn_mconn_setup_pipeline(mconn, "zdx mtr config response forwarding");
}

/*
 * Debug interface for setting/unsetting UT behavior - drop bind ack msg. This is used to simulate the case of connector
 * doesn't receive the bind ack message on its side and observe the effects.
 */
static int
zpn_broker_assistant_mtunnel_ut_drop_bind_ack_msgs(struct zpath_debug_state*    request_state,
                                                   const char**                 query_values,
                                                   int                          query_value_count,
                                                   void*                        cookie)
{
    if (query_values[0]) {
        ut_hook.number_of_bind_ack_msgs_to_be_dropped = strtoll(query_values[0], NULL, 10);
    } else {
        ut_hook.number_of_bind_ack_msgs_to_be_dropped = 1;
    }
    ZDP("Set to drop %lld bind ack messages\n", (long long)ut_hook.number_of_bind_ack_msgs_to_be_dropped);

    return ZPATH_RESULT_NO_ERROR;
}


struct zpn_broker_mtunnel *mtunnel_allocate_and_bucket_lock(const char *application_name,
                                                            const char *application_type,
                                                            const uint8_t *tunnel_bin,
                                                            const char *user_id,
                                                            struct zpn_broker_client_fohh_state *c_state,
                                                            int c_state_fohh_thread_id,
                                                            enum zpn_probe_type zpn_probe_type,
                                                            int64_t scope_gid,
                                                            enum zpn_client_type client_type,
                                                            int *bucket_id,
                                                            uint32_t *o_dwgs,
                                                            int o_dwgs_count)
{
    int64_t customer_id = c_state->customer_gid;

    struct zpn_broker_mtunnel *mtunnel;
    struct zpn_broker_bucket *bucket = NULL;
    uint8_t mtunnel_bin[ZPN_MTUNNEL_ID_BYTES];
    int res;

    int64_t mtunnel_limit = 0, mtunnel_number = 0;
    if (ZPN_BROKER_IS_PUBLIC() && zpn_broker_exceed_mtunnel_limit_check(c_state, &mtunnel_number, &mtunnel_limit)) {
        ZPN_LOG(AL_WARNING, "Number of requesting for mtunnel %"PRId64" exceeds the limit %"PRId64,
                mtunnel_number, mtunnel_limit);
        return NULL;
    }

    mtunnel = zpn_broker_mtunnel_alloc(client_type);
    if (mtunnel) {
        mtunnel->c_state_fohh_thread_id = c_state_fohh_thread_id;
        mtunnel->req_app_name = ZPN_STRDUP(application_name, strlen(application_name));
        mtunnel->application_domain  = ZPN_CALLOC(MAX_APPLICATION_DOMAIN_LEN);
        mtunnel->req_app_type = zpn_app_type_from_str(application_type);
        /*
         * ET-85800: The static analyzer is flagging this because it's only seeing the 5 explicit initializations
         * without understanding the full context of how ZMT_COUNT is defined & how C handles designated initializers.
         * Therefore, this is likely a false positive because the array is likely properly sized through ZMT_COUNT
         * definition & Uninitialized elements will be NULL pointers, which is a valid state. The access pattern is
         * intentional and safe as long as ZMT_COUNT is defined correctly
         */
        /* coverity[overrun-buffer-val : FALSE] */
        mtunnel->log.app_type = zmt_string[mtunnel->req_app_type];

        // Count our probe types
        zpn_broker_mtunnel_stats_inc_created_probe_type_counters(zpn_probe_type);

        // set the log_s time, so it doesnt log until it is connected
        mtunnel->log_s = epoch_s();
        mtunnel->start_us = monotime_us();

        if (user_id) {
            snprintf(mtunnel->user_id, sizeof(mtunnel->user_id), "%s", user_id);
            mtunnel->log.c_uid = mtunnel->user_id;
        }

        /* Log ZCC client platform in the transaction log */
        if (mtunnel->client_type == zpn_client_type_zapp && c_state->platform_type != zpn_platform_type_invalid) {
            mtunnel->log.platform = ZPN_PLATFORM_TYPE_STR(c_state->platform_type);
        } else {
            mtunnel->log.platform = NULL;
        }

        /* Store a copy of the  c_state->auth object in the mtunnel, and increase the objects ref count*/
        if (c_state->auth_request_x) {
            mtunnel->auth_request_x = c_state->auth_request_x;
            argo_object_hold(c_state->auth_request_x);
        } else {
            mtunnel->auth_request_x = NULL;
        }

        mtunnel->o_user_id[0] = 0;

        mtunnel->customer_id = customer_id;
        if (!mtunnel->req_app_name) goto failmessage;
        mtunnel->tunnel_id = ZPN_CALLOC(ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
        mtunnel->mtunnel_id = ZPN_CALLOC(ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
        if (!mtunnel->tunnel_id || !mtunnel->mtunnel_id) goto failmessage;



        /* mtunnel ID is combination of tunnel ID + more random bytes, both base64, delimited by a comma */
        res = RAND_bytes((unsigned char *)&mtunnel_bin, ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_LOG(AL_CRITICAL, "Crypto Random Generator not seeded!");
            goto fail;
        }
        base64_encode_binary(&(mtunnel->mtunnel_id[0]), tunnel_bin, ZPN_TUNNEL_ID_BYTES);
        int ix = base64_encoded_size(ZPN_TUNNEL_ID_BYTES);
        mtunnel->mtunnel_id[ix] = ',';
        base64_encode_binary(&(mtunnel->mtunnel_id[ix + 1]), mtunnel_bin, ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);

        memcpy(mtunnel->tunnel_id, mtunnel->mtunnel_id, ix);

        /* Our hash is actually a cityhash of the STRING version of the tunnel_id. */
        mtunnel->mtunnel_id_hash = CityHash64((const char *)mtunnel->mtunnel_id, (size_t) ZPN_MTUNNEL_ID_BYTES_TEXT);

        mtunnel->broker_id = ZPN_BROKER_GET_GID();
        mtunnel->broker_group_id = ZPN_BROKER_GET_GROUP_GID();
        mtunnel->log.startrx_us = epoch_us();

        *bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
        bucket = &(broker.buckets[*bucket_id]);

        pthread_mutex_lock(&(bucket->lock));

        res = argo_hash_store_with_hash(bucket->mtunnel_by_id,
                                        mtunnel->mtunnel_id,
                                        ZPN_MTUNNEL_ID_BYTES_TEXT,
                                        mtunnel->mtunnel_id_hash,
                                        1,
                                        mtunnel);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not store mtunnel in bucket hash table");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }
        TAILQ_INSERT_HEAD(&(bucket->mtunnel_list), mtunnel, bucket_list);

        mtunnel->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
        mtunnel->termination_started = 0;

        res = zpn_mconn_fohh_tlv_init(&(mtunnel->client_tlv), mtunnel, mconn_fohh_tlv_c);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initing fohh_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* client_tlv_mconn = &(mtunnel->client_tlv.mconn);
            client_tlv_mconn->setup_pipeline_cb = setup_http_config_response_pipeline;
        }

        res = zpn_mconn_zrdt_tlv_init(&(mtunnel->client_tlv_zrdt), mtunnel, mconn_zrdt_tlv_c);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initing client zrdt_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* client_tlv_zrdt_mconn = &(mtunnel->client_tlv_zrdt.mconn);
            client_tlv_zrdt_mconn->setup_pipeline_cb = setup_http_config_response_pipeline;
        }

        res = zpn_mconn_fohh_tlv_init(&(mtunnel->assistant_tlv), mtunnel, mconn_fohh_tlv_s);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initing fohh_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        //for zdx we will share the pipeline cookie regardless of tunnel type since they are the same
        void *pipeline_cookie = NULL;
        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* server_tlv_mconn = &(mtunnel->assistant_tlv.mconn);
            server_tlv_mconn->setup_pipeline_cb = setup_http_config_inspection_pipeline;
            if (!pipeline_cookie) {
                pipeline_cookie = get_zpn_zdx_cookie();
            }
            server_tlv_mconn->pipeline_cookie = pipeline_cookie;
        }

        res = zpn_mconn_zrdt_tlv_init(&(mtunnel->assistant_tlv_zrdt), mtunnel, mconn_zrdt_tlv_s);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initing assistatnt zrdt_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* server_zrdt_tlv_mconn = &(mtunnel->assistant_tlv_zrdt.mconn);
            server_zrdt_tlv_mconn->setup_pipeline_cb = setup_http_config_inspection_pipeline;
            if (!pipeline_cookie) {
                pipeline_cookie = get_zpn_zdx_cookie();
            }
            server_zrdt_tlv_mconn->pipeline_cookie = pipeline_cookie;
        }

        /* Move this to zpn_broker_assistant.c because we don't know which tlv to peer
         * until we know the type of connection at the connector side.
         */
#if 0
        res = zpn_mconn_connect_peer(&(mtunnel->client_tlv.mconn), &(mtunnel->assistant_tlv.mconn));
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not connect peer mconns.");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }
#endif

        res = zpn_mconn_add_global_owner(&(mtunnel->client_tlv.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &client_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner.");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->client_tlv_zrdt.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &client_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner for zrdt.");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->assistant_tlv.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &assistant_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner.");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->assistant_tlv_zrdt.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &assistant_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind assistant global owner for zrdt.");
            pthread_mutex_unlock(&(bucket->lock));
            goto fail;
        }

        //ZPN_DEBUG_BROKER("%s: Allocated for application %s, type %s", mtunnel->mtunnel_id, application_name, application_type ? application_type : "NULL");

        mtunnel->o_dwgs_count = o_dwgs_count;
        if (o_dwgs && (o_dwgs_count > 0)) {
            mtunnel->o_dwgs = ZPN_CALLOC(o_dwgs_count * sizeof(uint32_t));
            for (int k = 0; k < o_dwgs_count; k++) {
                mtunnel->o_dwgs[k] = o_dwgs[k];
            }
        }

        mtunnel->session_termination_on_reauth = is_session_termination_on_reauth_enabled(scope_gid);
        ZPN_DEBUG_CUSTOMER_CONFIG("customer=%ld, session_termination_on_reauth=%d",
                                  (long)mtunnel->customer_id, mtunnel->session_termination_on_reauth);

        mtunnel->log.action = "open";
        mtunnel->scope_gid = scope_gid;

        /* Don't log it when mtunnel is first created */
    }

    //coverity[missing_unlock] : not a bug
    return mtunnel;

 failmessage:
    ZPN_LOG(AL_CRITICAL, "Probable allocation failure creating mtunnel");
 fail:
    if (mtunnel) {
        if( bucket && argo_hash_remove_with_hash(bucket->mtunnel_by_id,
                                        mtunnel->mtunnel_id,
                                        ZPN_MTUNNEL_ID_BYTES_TEXT,
                                        mtunnel->mtunnel_id_hash,
                                        NULL)) {
            ZPN_LOG(AL_WARNING, "%s: Could not remove from bucket hash", mtunnel->mtunnel_id);
        }
        ZPN_FREE_AND_NULL(mtunnel->req_app_name);
        ZPN_FREE_AND_NULL(mtunnel->application_domain);
        ZPN_FREE_AND_NULL(mtunnel->db_app_name);
        ZPN_FREE_AND_NULL(mtunnel->mtunnel_id);
        ZPN_FREE_AND_NULL(mtunnel->tunnel_id);
        ZPN_FREE_AND_NULL(mtunnel->err);
        ZPN_FREE_AND_NULL(mtunnel->o_dwgs);
        ZPN_DEBUG_ZINS("free mtunnel: mtunnel_allocate_and_bucket_lock");
        zpn_broker_mtunnel_free(mtunnel);
    }
    return NULL;
}


struct zpn_broker_mtunnel *mtunnel_lookup_and_bucket_lock(const char *mtunnel_id, uint64_t mtunnel_id_hash, int *bucket_id)
{
    struct zpn_broker_mtunnel *mtunnel;
    struct zpn_broker_bucket *bucket;
    size_t len;

    if (!mtunnel_id) {
        ZPN_LOG(AL_CRITICAL, "Request for mtunnel lookup without mtunnel_id");
        return NULL;
    }

    len = strlen(mtunnel_id);
    *bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel_id_hash);
    bucket = &(broker.buckets[*bucket_id]);

    pthread_mutex_lock(&(bucket->lock));

    mtunnel = argo_hash_lookup_with_hash(bucket->mtunnel_by_id,
                                         mtunnel_id,
                                         len,
                                         mtunnel_id_hash,
                                         NULL);
    if (!mtunnel) {
        pthread_mutex_unlock(&(bucket->lock));
    } else {
        if (mtunnel->state >= zbms_reaping) {
            ZPN_DEBUG_BROKER("%s: Lookup: found, but reaping.", mtunnel_id);
            pthread_mutex_unlock(&(bucket->lock));
            mtunnel = NULL;
        } else {
            //ZPN_DEBUG_BROKER("%s: Lookup: found", mtunnel_id);
        }
    }
    //coverity[missing_unlock] : not a bug
    return mtunnel;
}

void mtunnel_bucket_lock(struct zpn_broker_mtunnel *mtunnel, int *bucket_id)
{
    struct zpn_broker_bucket *bucket;

    if (mtunnel) {
        *bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
        bucket = &(broker.buckets[*bucket_id]);
        pthread_mutex_lock(&(bucket->lock));
    } else {
        ZPN_LOG(AL_WARNING, "Locking NULL tunnel bucket");
    }
}

void mtunnel_bucket_unlock(int bucket_id)
{
    if (bucket_id != -1) {
        struct zpn_broker_bucket *bucket;
        bucket = &(broker.buckets[bucket_id]);
        pthread_mutex_unlock(&(bucket->lock));
    } else {
        ZPN_LOG(AL_ERROR, "Invalid bucket_id -1 ");
    }
}

#ifdef MTUNNEL_LOCKS_DEBUG
void _mtunnel_lock(struct zpn_broker_mtunnel *mtunnel)
#else
void mtunnel_lock(struct zpn_broker_mtunnel *mtunnel)
#endif
{
    if (mtunnel) {
        pthread_mutex_lock(&(mtunnel->lock));
    } else {
        ZPN_LOG(AL_WARNING, "Locking NULL tunnel");
    }
}

#ifdef MTUNNEL_LOCKS_DEBUG
void _mtunnel_unlock(struct zpn_broker_mtunnel *mtunnel)
#else
void mtunnel_unlock(struct zpn_broker_mtunnel *mtunnel)
#endif
{
    if (mtunnel) {
        pthread_mutex_unlock(&(mtunnel->lock));
    } else {
        ZPN_LOG(AL_WARNING, "Unlocking NULL tunnel");
    }
}

/*
 * This is called by the libevent finalize API, which guarantees the execution of this funtion after the event callback is complete,
 * if its running concurrently. Not doing this will lead to deadlock if the mtunnel_destory gets called while the mtunnel_end timer
 * callback is executing
 * mtunnel destory holds the tunnel lock and tries to free the event which waits for the callback to complete
 * callback function waits on the mtunnel lock to execute
 */
static void mtunnel_end_cb_cookie_final_free(struct event *ev, void *arg) {
    struct zpn_mtunnel_end_thread_cb_cookie *cb_cookie;
    cb_cookie = arg;

    if (!cb_cookie) {
        return;
    }

    if (cb_cookie->tunnel_end.mtunnel_id) {
        ZPN_FREE((void *)cb_cookie->tunnel_end.mtunnel_id);
    }
    if (cb_cookie->tunnel_end.error) {
        ZPN_FREE((void *)cb_cookie->tunnel_end.error);
    }
    event_del(cb_cookie->timer);
    cb_cookie->timer = NULL;
    ZPN_FREE_SLOW(cb_cookie);
}

void zpn_free_disp_learn_mode_timer(struct event * ev, void * arg)
{
    //This a NO-OP place holder function as event_free_finalize require a callback
}

/*
 * Destroy the locked tunnel.
 *
 * client_indication indicates that the client is telling the mtunnel
 * to close. (and thus we shouldn't tell the client it's closing)
 *
 * assistant_indication indicates that the assistant is telling the
 * mtunnel to close. (and thus we shouldn't tell the assistant it's
 * closing)
 *
 * Both indications can be zero.
 *
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 *
 * If 'err' is set, then the mtunnel will be closed with the given
 * error.
 */
int mtunnel_locked_destroy(struct zpn_broker_mtunnel *mtunnel,
                           int drop_buffered_data,
                           int client_indication,
                           int assistant_indication,
                           const char *err)
{
    int res;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    int peer_connected = 0;

    int64_t client_mconn_uncounted_dropped_bytes = 0;

    if (!mtunnel) {
        ZPN_LOG(AL_WARNING, "Destroying NULL tunnel");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (mtunnel->state >= zbms_reaping) {
        ZPN_DEBUG_ZINS("bucket: destroyed on tag=%d st=%s", mtunnel->log.c_tag, mtunnel_state(mtunnel->state));
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
        if (c_state && c_state->is_lp_enabled) {
            incr_num_mtunnel_destroy_part_stats(c_state);
        } else if(!c_state) {
            ZPN_DEBUG_MTUNNEL("No client f_conn c_state");
        }
    }

    zpn_broker_mtunnel_stats_inc_closed_probe_type_counters(mtunnel->zpn_probe_type, (mtunnel->err && mtunnel->err[0] != '\0'));

    if (mtunnel->insp_profile_gid) {
        zpn_broker_mtunnel_stats_inc_closed_policy_eval_web_inspection();
        if (ZPN_IS_WAF_INSPECTION_PERFORMED(mtunnel->insp_status)) {
            zpn_broker_mtunnel_stats_inc_closed_performed_web_inspection();
        }
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);
    if (client_mconn->peer || assistant_mconn->peer) {
        peer_connected = 1;
    } else {
        mtunnel->log.peer_disconnect_before_term = 1;
    }
    mtunnel->log.assistant_indicate_end = assistant_indication;

    if (mtunnel->timer) {
        event_free_finalize(0, mtunnel->timer, zpn_free_disp_learn_mode_timer);
        mtunnel->timer = NULL;
    }

    if (mtunnel->timeout_retry_disp_learn_mode_timer) {
        event_free_finalize(0, mtunnel->timeout_retry_disp_learn_mode_timer, zpn_free_disp_learn_mode_timer);
        mtunnel->timeout_retry_disp_learn_mode_timer = NULL;
    }

    if (mtunnel->mtunnel_end_client) {
        event_free_finalize(0, mtunnel->mtunnel_end_client->timer, mtunnel_end_cb_cookie_final_free);
        mtunnel->mtunnel_end_client = NULL;
    }
    if (mtunnel->mtunnel_end_client_dropdata) {
        event_free_finalize(0, mtunnel->mtunnel_end_client_dropdata->timer, mtunnel_end_cb_cookie_final_free);
        mtunnel->mtunnel_end_client_dropdata = NULL;
    }
    if (mtunnel->mtunnel_end_assistant) {
        event_free_finalize(0, mtunnel->mtunnel_end_assistant->timer, mtunnel_end_cb_cookie_final_free);
        mtunnel->mtunnel_end_assistant = NULL;
    }
    if (mtunnel->mtunnel_end_assistant_dropdata) {
        event_free_finalize(0, mtunnel->mtunnel_end_assistant_dropdata->timer, mtunnel_end_cb_cookie_final_free);
        mtunnel->mtunnel_end_assistant_dropdata = NULL;
    }

    if (err && !mtunnel->err) mtunnel->err = ZPN_STRDUP(err, strlen(err));

    if (assistant_indication) {
        res = zpn_mconn_terminate(assistant_mconn,
                                  drop_buffered_data,
                                  1,
                                  err,
                                  NULL);
        if (!peer_connected) {
            res = zpn_mconn_terminate(client_mconn,
                                      drop_buffered_data,
                                      1,
                                      err,
                                      &client_mconn_uncounted_dropped_bytes);
            if (client_mconn_uncounted_dropped_bytes) {
                ZPN_LOG(AL_WARNING, "%s mtunnel termination for customer %"PRId64" , uncounted_dropped_bytes %"PRId64" ", mtunnel->mtunnel_id, mtunnel->customer_id, client_mconn_uncounted_dropped_bytes);
            }
        }
    } else {
        res = zpn_mconn_terminate(client_mconn,
                                  drop_buffered_data,
                                  0,
                                  err,
                                  &client_mconn_uncounted_dropped_bytes);
        if (!peer_connected) {

            if (client_mconn_uncounted_dropped_bytes) {
                /*
                * we have dropped bytes from client mconn and wasnt able to update assistant_mconn
                * the main goal for this call is to update fohh_tlv->peer_tx_data correctly
                */
                zpn_mconn_client_window_update(assistant_mconn, 0, (int)client_mconn_uncounted_dropped_bytes, 0);
                ZPN_DEBUG_MTUNNEL("%s mtunnel termination, peer not counted but able to count dropped bytes %"PRId64" ", mtunnel->mtunnel_id, client_mconn_uncounted_dropped_bytes);
            }
            res = zpn_mconn_terminate(assistant_mconn,
                                      drop_buffered_data,
                                      1,
                                      err,
                                      NULL);
        }
    }

    mtunnel->state = zbms_reaping;

    if (res) {
        ZPN_LOG(AL_ERROR, "%s: destroy failed: %s", mtunnel->mtunnel_id, zpn_result_string(res));
    }

    return res;
}


static void mtunnel_async_callback(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_broker_mtunnel *mtunnel = cookie;
    int bucket_id = -1;

    mtunnel_bucket_lock(mtunnel, &bucket_id);
    mtunnel_lock(mtunnel);
    ZPN_DEBUG_MTUNNEL("%s: Async count %d -> %d", mtunnel->mtunnel_id, mtunnel->async_count, mtunnel->async_count - 1);
    mtunnel->async_count--;
    if (mtunnel->async_count == 0) {
        mtunnel_locked_state_machine(mtunnel);
    }
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
}

int zpn_broker_mtunnel_callback(void *response_callback_cookie,
                                struct wally_registrant *registrant,
                                struct wally_table *table,
                                int64_t request_id,
                                int row_count)
{
    int i;

    /* Loop through all the threads until we find one that will accept our call */
    for (i = 0; i < fohh_thread_count(); i++) {
        if (fohh_thread_call(i, mtunnel_async_callback, response_callback_cookie, 0) == FOHH_RESULT_NO_ERROR) {
            return ZPN_RESULT_NO_ERROR;
        }
    }

    /* Erk... All threads have probably gone? Nothing matters anymore ..., whatever */
    ZPN_LOG(AL_CRITICAL, "Cannot do thread call");

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Locking: it is assumed both bucket and mtunnel are locked at this point
 *
 * This is a LOT simpler now that auth is handled as part of a
 * real authentication request. This is basically 'make sure we
 * aren't outside our auth window'
 */
static int zpn_broker_mtunnel_authenticate(struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_broker_client_fohh_state *c_state;

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);

    if (!c_state) {
        ZPN_LOG(AL_CRITICAL, "No client f_conn c_state");
        return ZPN_RESULT_ERR;
    }

    mtunnel->log.saml_not_before = c_state->saml_not_before;
    mtunnel->log.saml_not_on_or_after = c_state->saml_not_on_or_after;

    if (!c_state->auth_complete) {
        return ZPN_RESULT_ERR;
    }

    if (c_state->user_id) {
        snprintf(mtunnel->user_id, sizeof(mtunnel->user_id), "%s", c_state->user_id);
    } else {
        mtunnel->user_id[0] = 0;
    }

    mtunnel->log.c_pub_ip = c_state->log.pub_ip;
    mtunnel->log.c_priv_ip = c_state->log.priv_ip;

    if (geoip_db_file) {
        char str[ARGO_INET_ADDRSTRLEN];
        /* Update lat, lon, country code */
        if (!mtunnel->log.c_cc) {

            double lat, lon;

            // TODO: Consider reusing lookup results in c_state.
            if (zpath_geoip_lookup_double(&mtunnel->log.c_pub_ip, &lat, &lon, &mtunnel->c_cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_MTUNNEL("%s: Client IP = %s, lat = %f, lon = %f, cc = %s",
                                mtunnel->mtunnel_id,
                                argo_inet_generate(str, &(mtunnel->log.c_pub_ip)), lat, lon, mtunnel->c_cc);
                mtunnel->log.c_lat = lat;
                mtunnel->log.c_lon = lon;
                mtunnel->log.c_cc = &mtunnel->c_cc[0];
            } else {
                if(!argo_inet_is_private(&mtunnel->log.c_pub_ip)){
                    ZPN_LOG(AL_WARNING, "geoip lookup failed for client ip = %s", argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                }else{
                    ZPN_DEBUG_MTUNNEL("geoip lookup failed for client ip = %s", argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                }
                mtunnel->log.c_lat = 0;
                mtunnel->log.c_lon = 0;
                mtunnel->log.c_cc = NULL;
            }

            /* In many cases city info may not exist, so only perform lookup together with cc lookup */
            if (!mtunnel->log.c_city) {
                if (zpath_geoip_lookup_city(&mtunnel->log.c_pub_ip, &mtunnel->c_city[0], sizeof(mtunnel->c_city)) == ZPATH_RESULT_NO_ERROR) {
                    ZPN_DEBUG_MTUNNEL("%s: Client IP = %s, city = %s",
                                mtunnel->mtunnel_id,
                                argo_inet_generate(str, &(mtunnel->log.c_pub_ip)), mtunnel->c_city);
                    mtunnel->log.c_city = &mtunnel->c_city[0];
                } else {
                    ZPN_DEBUG_MTUNNEL("%s: City lookup failed for client ip = %s", mtunnel->mtunnel_id, argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                    mtunnel->log.c_city = NULL;
                }
            }
        }
    } else {
        ZPN_DEBUG_MTUNNEL("geoip_db_file is NULL");
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Attach mtunnel to local assistant for mtunnel - Must be called without holding mtunnel lock */
int zpn_broker_local_mtunnel_connection(struct zpn_broker_mtunnel *mtunnel, struct fohh_connection *assistant_f_conn)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Peer assistant tlv */
        res = zpn_mtunnel_peer_assistant_mconn(mtunnel, 1);
        if (res) {
            ZPN_DEBUG_MTUNNEL("%s: Peer assistant tlv for mtunnel for local dispatch: %s failed", mtunnel->mtunnel_id, fohh_description(assistant_f_conn));
            return ZPN_RESULT_ERR;
        } else {
            ZPN_DEBUG_MTUNNEL("%s: Peer assistant tlv for mtunnel for local dispatch: %s success", mtunnel->mtunnel_id, fohh_description(assistant_f_conn));
        }
    }

    return res;
}

/*
 * Locking: It is assumed bucket and mtunnel are both locked at this point.
 */
int zpn_broker_tunnel_complete_send(struct zpn_broker_mtunnel *mtunnel, const char *err_msg, const char* meta_tag)
{
    struct zpn_tlv *assistant_tlv = zpn_broker_mtunnel_assistant_tlv(mtunnel);
    struct zpn_tlv *client_tlv = zpn_broker_mtunnel_client_tlv(mtunnel);
    int res;

    if (ut_hook.number_of_bind_ack_msgs_to_be_dropped) {
        __sync_sub_and_fetch_8(&ut_hook.number_of_bind_ack_msgs_to_be_dropped, 1);
        ZPN_LOG(AL_NOTICE, "%s: dropped bind ack per UT config, still (%lld) bind ack messages set to be dropped",
                      mtunnel->mtunnel_id, (long long)ut_hook.number_of_bind_ack_msgs_to_be_dropped);
        return ZPN_RESULT_NO_ERROR;
    }

    if (assistant_tlv && !mtunnel->assistant_ack_sent) {
        struct zpn_mtunnel_bind_ack data;
        memset(&data, 0, sizeof(data));

        data.mtunnel_id = mtunnel->mtunnel_id;
        data.tag_id = mtunnel->log.a_tag;
        data.error = (err_msg == NULL) ? NULL : err_msg;
        data.ubrk_tx_us = zpn_cloud_adjusted_epoch_us();
        if (mtunnel->insp_profile_gid) {
            zpn_broker_mtunnel_stats_inc_policy_eval_web_inspection();
        }
        res = zpn_send_zpn_mtunnel_bind_ack_struct(assistant_tlv, 0, &data);
        if (res == ZPN_RESULT_WOULD_BLOCK) {
            /* Just chill. */
            return ZPN_RESULT_ASYNCHRONOUS;
        } else if (res) {
            ZPN_LOG(AL_ERROR, "%s: Could not send assistant ack", mtunnel->mtunnel_id);
            return ZPN_RESULT_ERR;
        } else {
            if (zpn_meta_transaction_collection) {
                argo_log_structure_immediate(zpn_meta_transaction_collection,
                                             argo_log_priority_info,
                                             0,
                                             "BindAck",
                                             zpn_mtunnel_bind_ack_description,
                                             &data);
            }
            ZPN_DEBUG_MTUNNEL("%s: assistant ack sent to %s", mtunnel->mtunnel_id, zpn_tlv_description(assistant_tlv));
            mtunnel->assistant_ack_sent = 1;
        }
    } else {
        ZPN_DEBUG_MTUNNEL("%s: bind_ack not sent assistant_tlv=%s %s", mtunnel->mtunnel_id,
                          assistant_tlv ? zpn_tlv_description(assistant_tlv) : "<no_tlv>",
                          mtunnel->assistant_ack_sent ? "ack_sent_already" : "");
    }

    if (client_tlv && !err_msg && mtunnel->assistant_ack_sent &&
        !mtunnel->client_ack_sent) {
        int64_t c_incarnation = zpn_tlv_conn_incarnation(client_tlv);
        res = mtunnel_request_ack(mtunnel,
                                  c_incarnation,
                                  mtunnel->log.c_tag,
                                  mtunnel->mtunnel_id,
                                  NULL,
                                  NULL);
        if (res == ZPN_RESULT_WOULD_BLOCK) {
            /* Just chill. Timeouts will pick it up later. */
            ZPN_LOG(AL_WARNING, "%s: overrun? Could not send client ack", mtunnel->mtunnel_id);
            return ZPN_RESULT_ASYNCHRONOUS;
        } else if (res) {
            ZPN_LOG(AL_ERROR, "%s: Could not send client ack", mtunnel->mtunnel_id);
            return ZPN_RESULT_ERR;
        } else {
            ZPN_DEBUG_MTUNNEL("%s: client ack sent to %s", mtunnel->mtunnel_id, zpn_tlv_description(client_tlv));
            mtunnel->client_ack_sent = 1;
        }
    }

    //ZPN_DEBUG_MTUNNEL("%s: SETUP COMPLETE.", mtunnel->mtunnel_id);

    return ZPN_RESULT_NO_ERROR;
}


static void
mtunnel_send_request_on_cstate_thread(struct zevent_base* zbase,
                                      void*               cookie1,
                                      int64_t             int_cookie1,
                                      void*               cookie2,
                                      void*               cookie3,
                                      void*               cookie4,
                                      int64_t             int_cookie2)
{
    struct zpn_broker_mtunnel*  mtunnel;
    int                         is_probe;
    int                         mtunnel_incarnation;

    mtunnel = cookie1;
    is_probe = int_cookie1;
    mtunnel_incarnation = int_cookie2;
    int bucket_id = -1;
    mtunnel_bucket_lock(mtunnel, &bucket_id);
    mtunnel_lock(mtunnel);
    if (mtunnel->incarnation == mtunnel_incarnation) {
        mtunnel_locked_send_broker_request(mtunnel, is_probe);
    }
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
}


void zpn_broker_c2c_fqdn_check_resume_mtunnel(const char*  mtunnel_id, int is_c2c_client, int is_c2c_disable_local_route, int64_t mtunnel_incarnation)
{
    struct zpn_broker_mtunnel *mtunnel = NULL;
    struct zpn_broker_client_fohh_state*    c_state;
     struct zpn_tlv                          *client_tlv = NULL;
    struct zevent_base*                     zbase;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int bucket_id = -1;

    if (mtunnel_id == NULL) {
        ZPN_LOG(AL_NOTICE, "zpn_broker_c2c_fqdn_check_resume_mtunnel: Mtunnel_id is NULL");
        return;
    }

    mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = mtunnel_lookup_and_bucket_lock(mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_NOTICE, "%s: Mtunnel_end received for mtunnel that isn't found", mtunnel_id);
        return;
    }

    mtunnel_lock(mtunnel);

    if (mtunnel->incarnation != mtunnel_incarnation) {
        ZPN_LOG(AL_ERROR, "%s: Mtunnel incarnation mismatch. Requested: %"PRId64", Current: %"PRId64"",
                                                 mtunnel_id, mtunnel_incarnation, mtunnel->incarnation);
        goto done;
    }

    mtunnel->flag.is_c2c_bypass_ld = is_c2c_client;
    if (is_c2c_disable_local_route) {
        mtunnel->flag.is_c2c_terminate_access_on_fail = 1;
    }

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    client_tlv = zpn_broker_mtunnel_client_tlv(mtunnel);
    if (!c_state || (zpn_tlv_sanity_check(client_tlv) != ZPN_RESULT_NO_ERROR)) {
        ZPN_LOG(AL_WARNING, "%s: No client state", mtunnel->mtunnel_id);
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, MT_CLOSED_INTERNAL_ERROR);
        mtunnel_locked_state_machine(mtunnel);
        goto done;
    }

    if (is_current_thread_fohh_thread() && fohh_get_current_thread_id() == zpn_tlv_get_thread_id(client_tlv)) {
        mtunnel_locked_send_broker_request(mtunnel, 0);
        goto done;
    }

    zbase = zpn_broker_client_get_cstate_zevent_base(c_state);
    zevent_base_big_call(zbase, mtunnel_send_request_on_cstate_thread, mtunnel, 0, NULL, NULL, NULL,
                         mtunnel->incarnation);

done:
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
    return;
}

/*
 * MAY or MAY NOT be called from another thread. We want to send the dispatcher request from cstate thread context.
 * If this is called from another thread, switch to cstate thread. If this is called from cstate thread itself,
 * don't do context switching - send the request inline.
 *
 * We want only the fohh thread on which cstate is pinned to send the broker request. This will make it easier to
 * implement lockless actions in few cases.
 *
 * Lets send a message to the right thread to process this. If we can't send the message for whatever reason, don't
 * delete the mtunnel, let it get handled when the timer runs the next time.
 */
static void
mtunnel_locked_send_broker_request_from_another_thread(struct zpn_broker_mtunnel*  mtunnel,
                                                       int                         is_probe)
{
    struct zpn_broker_client_fohh_state*    c_state;
    struct zpn_tlv                          *client_tlv = NULL;
    struct zevent_base*                     zbase;

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    client_tlv = zpn_broker_mtunnel_client_tlv(mtunnel);
    if (!c_state || (zpn_tlv_sanity_check(client_tlv) != ZPN_RESULT_NO_ERROR)) {
        ZPN_LOG(AL_WARNING, "%s: No client state", mtunnel->mtunnel_id);
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, MT_CLOSED_INTERNAL_ERROR);
        mtunnel_locked_state_machine(mtunnel);
        goto done;
    }

    if (is_current_thread_fohh_thread() && (fohh_get_current_thread_id() == zpn_tlv_get_thread_id(client_tlv))) {
        mtunnel_locked_send_broker_request(mtunnel, is_probe);
        goto done;
    }

    /* If we don't have an application_id, we need to look it up... */
    if (!mtunnel->application_id) {
        if (zpn_is_dr_mode_active()) {
            mtunnel->application_id = zpn_application_domain_search_by_customer(c_state->customer_gid, mtunnel->req_app_name,
                                                                                strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                mtunnel->application_domain, &mtunnel->application_domain_id, NULL,
                                                                                mtunnel->ip_protocol, mtunnel->client_port,
                                                                                zpn_mtunnel_search, c_state->client_type);
        } else {
            mtunnel->application_id = zpn_application_domain_search(c_state->scope_gid, mtunnel->req_app_name,
                                                                    strnlen(mtunnel->req_app_name, MAX_DOMAIN_LEN_SIZE),
                                                                    mtunnel->application_domain,  &mtunnel->application_domain_id, NULL,
                                                                    mtunnel->ip_protocol, mtunnel->client_port,
                                                                    zpn_mtunnel_search, c_state->client_type);
        }

        if (!mtunnel->application_id) {
            ZPN_LOG(AL_WARNING, "%s: Could not find app for %s", mtunnel->mtunnel_id, mtunnel->req_app_name);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_APP_NOT_FOUND);
            mtunnel_locked_state_machine(mtunnel);
            goto done;
        }
        mtunnel->log.g_app = mtunnel->application_id;
    }

    if(mtunnel->flag.ptag_enabled && !mtunnel->application_domain_id) {
        if (zpn_is_dr_mode_active()) {
            mtunnel->application_domain_id = zpn_application_configured_domain_search_by_customer(c_state->customer_gid,
                                                                                                  mtunnel->req_app_name,
                                                                                                  strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                                  mtunnel->application_domain, NULL,
                                                                                                  mtunnel->ip_protocol, mtunnel->client_port);
        } else {
            mtunnel->application_domain_id = zpn_application_configured_domain_search(c_state->scope_gid,
                                                                                      mtunnel->req_app_name,
                                                                                      strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                      mtunnel->application_domain, NULL,
                                                                                      mtunnel->ip_protocol, mtunnel->client_port);
        }
    }

    zbase = zpn_broker_client_get_cstate_zevent_base(c_state);
    zevent_base_big_call(zbase, mtunnel_send_request_on_cstate_thread, mtunnel, is_probe, NULL, NULL, NULL,
                         mtunnel->incarnation);

done:
    return;
}

/*
 * If returns error, mtunnel will have been destroyed with reason
 * set. Does not set state.
 *
 * is_probe is an indication that this is a probing request. (state is
 * tracked a bit differently this way)
 *
 * Try to send BrkRq message to connector directly if possible (if we have the cache and if the sending path
 * succeeded, otherwise send it to dispatcher)
 */
int mtunnel_locked_send_broker_request(struct zpn_broker_mtunnel *mtunnel, int is_probe)
{
    ZPN_DEBUG_MTUNNEL("%s : starting %" PRId64 " %s, is_probe: %d", mtunnel->mtunnel_id, mtunnel->application_id, mtunnel->req_app_name, is_probe);

    struct zpn_broker_request req;
    struct zpn_broker_client_fohh_state *c_state;
    int res = ZPN_RESULT_ERR;
    int64_t now_us;
    int cache_fetch_status;
    struct zpn_tlv *asst_conn;
    struct zpn_tlv *client_tlv = NULL;
    char error_str[ZPN_NEG_PATH_CACHE_ERROR_STR_LEN_MAX];
    int no_disp_ready = 0;
    int64_t last_disp_id = 0;
    char *previous_error_str = NULL;
    int64_t neg_path_cache_ttl_us;
    int result;

    if (!mtunnel->mtunnel_id) {
        ZPN_LOG(AL_WARNING, "Cannot send broker request. Invalid mtunnel");
        return ZPN_RESULT_ERR;
    }

    if (!mtunnel->flag.c2c_regex_match && zpn_broker_mtunnel_c2c_regex_match_check(mtunnel)) {
        int is_c2c_fqdn = 0;
        int64_t cache_entry_expiry = 0;

        mtunnel->flag.c2c_regex_match = 1;
        ZPN_DEBUG_MTUNNEL("%s: C2C regex match found for fqdn:%s", mtunnel->mtunnel_id, mtunnel->req_app_name);

        res = zpn_broker_fqdn_c2c_check_query(mtunnel->customer_id, mtunnel->tunnel_id, mtunnel->mtunnel_id, mtunnel->req_app_name,
                                 strnlen(mtunnel->req_app_name, ZPN_C2C_FQDN_LEN_MAX), &is_c2c_fqdn, &cache_entry_expiry, zpn_request_type_normal, mtunnel->incarnation);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_MTUNNEL("%s: C2C fqdn:%s check async:%d", mtunnel->mtunnel_id, mtunnel->req_app_name, mtunnel->flag.c2c_regex_match);
            return res;
        } else if (res == ZPN_RESULT_NO_ERROR) {
            mtunnel->flag.is_c2c_bypass_ld = is_c2c_fqdn;
            ZPN_DEBUG_MTUNNEL("%s: C2C fqdn:%s Result success fwd_to_broker:%d", mtunnel->mtunnel_id, mtunnel->req_app_name, mtunnel->flag.is_c2c_bypass_ld);
        } else {
            // Error checking c2c Cache
            ZPN_DEBUG_MTUNNEL("%s: C2C fqdn:%s Result error:%s", mtunnel->mtunnel_id, mtunnel->req_app_name, zpn_result_string(res));
            if (is_c2c_ldbypass_local_route_config_enabled(mtunnel->req_app_name) == 0) {
                mtunnel->flag.is_c2c_terminate_access_on_fail = 1;
            }
        }
    }

    ZPN_DEBUG_MTUNNEL("%s: fqdn:%s Regex_match:%d is_c2c_fqdn :%d is_terminate_access_on_c2c_check_fail:%d", mtunnel->mtunnel_id, mtunnel->req_app_name,
                                         mtunnel->flag.c2c_regex_match, mtunnel->flag.is_c2c_bypass_ld, mtunnel->flag.is_c2c_terminate_access_on_fail);

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    if (!c_state) {
        ZPN_LOG(AL_WARNING, "%s: No client state", mtunnel->mtunnel_id);
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, MT_CLOSED_INTERNAL_ERROR);
        mtunnel_locked_state_machine(mtunnel);
        return ZPN_RESULT_ERR;
    }

    /* If we don't have an application_id/application_domain_id, we need to look it up... */
    if (!mtunnel->application_id) {
        if (zpn_is_dr_mode_active()) {
            mtunnel->application_id = zpn_application_domain_search_by_customer(c_state->customer_gid, mtunnel->req_app_name,
                                                                                strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                mtunnel->application_domain, &mtunnel->application_domain_id, NULL,
                                                                                mtunnel->ip_protocol, mtunnel->client_port,
                                                                                zpn_mtunnel_search, c_state->client_type);
        } else {
            mtunnel->application_id = zpn_application_domain_search(c_state->scope_gid, mtunnel->req_app_name,
                                                                    strnlen(mtunnel->req_app_name, MAX_DOMAIN_LEN_SIZE),
                                                                    mtunnel->application_domain,  &mtunnel->application_domain_id, NULL,
                                                                    mtunnel->ip_protocol, mtunnel->client_port,
                                                                    zpn_mtunnel_search, c_state->client_type);
        }

        if (!mtunnel->application_id) {
            ZPN_LOG(AL_WARNING, "%s: Could not find app for %s", mtunnel->mtunnel_id, mtunnel->req_app_name);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_APP_NOT_FOUND);
            mtunnel_locked_state_machine(mtunnel);
            goto done;
        }
        mtunnel->log.g_app = mtunnel->application_id;
    }

    /* There are cases in flow where application_gid is not populated via call to API zpn_application_domain_search,
     * In such cases we will need this as we will have application_gid but not application_domain_gid
     * However, Protocol Tagging feature requires configured app_domain and app_domain_id
     */
    if(mtunnel->flag.ptag_enabled && !mtunnel->application_domain_id) {
        if (zpn_is_dr_mode_active()) {
            mtunnel->application_domain_id = zpn_application_configured_domain_search_by_customer(c_state->customer_gid,
                                                                                                  mtunnel->req_app_name,
                                                                                                  strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                                  mtunnel->application_domain, NULL,
                                                                                                  mtunnel->ip_protocol, mtunnel->client_port);
        } else {
            mtunnel->application_domain_id = zpn_application_configured_domain_search(c_state->scope_gid,
                                                                                      mtunnel->req_app_name,
                                                                                      strnlen(mtunnel->req_app_name,MAX_DOMAIN_LEN_SIZE),
                                                                                      mtunnel->application_domain, NULL,
                                                                                      mtunnel->ip_protocol, mtunnel->client_port);
        }
    }

    if (ZPN_BROKER_IS_PRIVATE() && mtunnel->flag.is_c2c_terminate_access_on_fail) {
        ZPN_LOG(AL_WARNING, "%s: App name: %s, Terminating mtunnel as remote C2C check failed", mtunnel->mtunnel_id, mtunnel->req_app_name);
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_TIMEOUT);
        mtunnel_locked_state_machine(mtunnel);
        goto done;
    }

    memset(&req, 0, sizeof(req));
    req.mtunnel_id = mtunnel->mtunnel_id;
    req.g_app = mtunnel->application_id;

    req.g_brk = ZPN_BROKER_GET_GID();
    req.g_microtenant = mtunnel->scope_gid;

    client_tlv = zpn_broker_mtunnel_client_tlv(mtunnel);

    if (client_tlv && zpn_client_static_config[c_state->client_type].cn_username) {
        if (mtunnel->o_user_id[0] != 0) {
            /* ES-1466, ET-51866
             * We noticed some imbalanced traffic on SIPA, hence we want to use origin
             * user id here (whenever it's available), so that dispatcher can do load
             * balance on the real user id.
             */
            req.c_uid = &(mtunnel->o_user_id[0]);
        } else {
            req.c_uid = zpn_tlv_peer_cn(client_tlv);

            // It is technically possible for fohh_peer_cn to return NULL
            // This seems like it would cause issues later for the connection
            // However, since we never relied on this value being set during this routine
            // We will instead initiate a fallback to the previous method of setting c_uid
            // The cost of this is a degredation in load balancing but keeps our specialized clients
            // running instead of failing in this new condition
            // The alternative is to kill our mtunnel anc_pub_ipd return an error
            if (!req.c_uid) {
                ZPN_LOG(AL_WARNING, "%s: Could not assign c_uid to request for type %s client",
                        mtunnel->mtunnel_id,
                        zpn_client_static_config[c_state->client_type].name);
                // Fallback to previous naming
                req.c_uid = &(mtunnel->user_id[0]);
            }
        }
    } else {
        req.c_uid = &(mtunnel->user_id[0]);
    }


    req.c_pub_ip = &(mtunnel->client_ip);
    req.c_priv_ip = &(mtunnel->log.c_priv_ip);
    req.brk_name = zpn_broker_get_instance_full_name();
    req.c_port = mtunnel->client_port;
    req.o_port = mtunnel->log.o_sport;
    req.domain = mtunnel->req_app_name;

    /*
     * We need app_domain to compare with matched domain in zpn_inspection table to decide if
     * waf is disabled for this domain.
     */
    req.app_domain = mtunnel->application_domain;
    req.g_app_domain = mtunnel->application_domain_id;

    req.app_type = zmt_string[mtunnel->req_app_type];
    req.double_encrypt = mtunnel->double_encrypt;
    req.zpn_probe_type = mtunnel->zpn_probe_type;
    req.ip_protocol = mtunnel->ip_protocol;
    req.g_ins_app = mtunnel->insp_appl_gid;
    req.g_ins_profile = mtunnel->insp_profile_gid;
    req.g_ins_rule = mtunnel->insp_rule_gid;
    req.redispatch_reason = zpn_broker_dispatch_get_redispatch_reason(mtunnel->last_disp_error);
    if (req.g_ins_profile) {
        req.g_ins_type = zpn_waf_inspt_all;
    } else {
        req.g_ins_type = zpn_waf_inspt_none;
    }
    struct zpn_mconn *mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    req.icmp_access_type = mconn->icmp_access_type;
    if (mtunnel->assistant_group_gid_count) {
        req.filter_ast_grp = mtunnel->assistant_group_gid;
        req.filter_ast_grp_count = mtunnel->assistant_group_gid_count;
    }
    if (mtunnel->extranet_locs_count) {
        req.filter_locs = mtunnel->extranet_locs;
        req.filter_locs_count = mtunnel->extranet_locs_count;
    }

    if (mtunnel->log.g_app_grps_count) {
        req.g_apps = mtunnel->log.g_apps;
        req.g_apps_count = mtunnel->log.g_apps_count;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        req.b_lat = g_broker_common_cfg->private_broker.latitude;
        req.b_lon = g_broker_common_cfg->private_broker.longitude;
        // TODO: Revisit if there is ability to lookup client location on PB.
        req.c_lat = g_broker_common_cfg->private_broker.latitude;
        req.c_lon = g_broker_common_cfg->private_broker.longitude;
    } else {
        req.b_lat = zpath_instance_global_state.current_config->latitude;
        req.b_lon = zpath_instance_global_state.current_config->longitude;
        // TODO: Use client's location if available for PB data channels (public PB case).
        req.c_lat = mtunnel->log.c_lat;
        req.c_lon = mtunnel->log.c_lon;
        req.c_cc = mtunnel->c_cc;
    }
    if (is_probe) {
        req.req_type = ZPN_BRK_REQ_TYPE_USE_STICKY;
#if 0  //FQDN_TO_SERVER_IP_HEALTH_APPS (ET-64854)
    } else {
        if (zpn_broker_policy_fqdn_to_srv_ip_enabled(c_state->customer_gid)) {
            req.s_ip = mtunnel->log.s_ip;
            req.g_ast = mtunnel->log.g_ast;
            req.req_type = ZPN_BRK_REQ_TYPE_USE_STICKY;
            ZPN_DEBUG_MTUNNEL("%s : state: %s, req_type: %s", mtunnel->mtunnel_id, mtunnel_state(mtunnel->state), req.req_type);
        }
#endif
    }

    // Do a better job on load balancing LSS traffic.
    if (zpn_client_static_config[c_state->client_type].ignore_cache) {
        req.ignore_cache = 1;
    }

    now_us = zpn_cloud_adjusted_epoch_us();
    req.ubrk_tx_us = now_us;
    req.allow_all_xport = mtunnel->allow_all_xport;

    cache_fetch_status = ZPN_RESULT_ERR;
    int cache_key_type = zpn_broker_client_path_cfg_get_key_type(c_state->customer_gid);
    /* Double hop happens when: g_brk != g_bfw, we should not use cached path in case of double hop */
    if ((!mtunnel->flag.donot_send_directly_to_connector) && (!zpn_broker_is_asst_databroker_resilience_enabled(c_state->customer_gid))) {
        asst_conn = NULL;
        if (cache_key_type && mtunnel->assistant_group_gid_count) {
            //Generally for SIPA/BC/CC, server_group_gid can be received in mtunnel_request
            //Assistant groups are looked up in mtunnel_locked_fill_assistant_groups
            //Use it in path cache key only if feature flag is enabled and the ast grp gid is non-zero
            ZPN_DEBUG_PATH_CACHE("%s: Checking path cache, cache_key_type: %d, assistant_group_gid_count: %d",
                                c_state->tunnel_id, cache_key_type, mtunnel->assistant_group_gid_count);
            mtunnel->flag.ast_grp_used_in_cache_key = 1;
            int i;
            for (i = 0; (i < ZPN_MAX_ASSISTANT_GROUP_GIDS) && (i < mtunnel->assistant_group_gid_count); i++) {
                ZPN_DEBUG_PATH_CACHE("%s: Checking path cache, using ast_grp_gid: %"PRId64"",
                                    c_state->tunnel_id, mtunnel->assistant_group_gid[i]);
                cache_fetch_status = zpn_broker_client_path_cache_get_valid(c_state,
                                                mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]),
                                                mtunnel->assistant_group_gid[i], req.domain, mtunnel->req_app_type, req.c_port,
                                                req.ip_protocol, &mtunnel->dispatcher_id, &req.g_ast, &req.g_ast_grp,
                                                &req.g_app_grp, &req.g_aps, &req.g_srv_grp, &req.s_ip, &asst_conn, 1);
                if (ZPN_RESULT_NO_ERROR == cache_fetch_status) {
                    ZPN_DEBUG_PATH_CACHE("%s: Found path cache, with ast_grp_gid: %"PRId64"",
                                        c_state->tunnel_id, mtunnel->assistant_group_gid[i]);
                    break;
                }
            }
        } else {
            ZPN_DEBUG_PATH_CACHE("%s: Checking path cache, without using ast_grp ",
                                 c_state->tunnel_id);
            mtunnel->flag.ast_grp_used_in_cache_key = 0;
            cache_fetch_status = zpn_broker_client_path_cache_get_valid(c_state,
                                            mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]),
                                            0, req.domain, mtunnel->req_app_type, req.c_port,
                                            req.ip_protocol, &mtunnel->dispatcher_id, &req.g_ast, &req.g_ast_grp,
                                            &req.g_app_grp, &req.g_aps, &req.g_srv_grp, &req.s_ip, &asst_conn, 1);
        }
    } else {
        ZPN_DEBUG_PATH_CACHE("%s: Not checking path cache, flag: %d, resilience: %d",
                            c_state->tunnel_id, mtunnel->flag.donot_send_directly_to_connector,
                            zpn_broker_is_asst_databroker_resilience_enabled(c_state->customer_gid));
	}

    if (ZPN_RESULT_NO_ERROR == cache_fetch_status) {
        ZPN_DEBUG_PATH_CACHE("%s: path cache found, bypassing dispatcher and doing BrkRq directly with connector(%lld)",
                             c_state->tunnel_id, (long long)req.g_ast);
        req.g_dsp = mtunnel->dispatcher_id;
        req.dsp_tx_us = now_us;
        req.bfw_us = now_us;
        req.dsp_bypassed = 1;
        req.g_bfw = ZPN_BROKER_GET_GID();
        mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_CACHE_HIT;
        req.path_decision = mtunnel->path_decision;
        mtunnel->brk_req_seq_num++;
        req.seq_num = mtunnel->brk_req_seq_num;
        if (ZPN_BROKER_IS_PRIVATE()) {
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
            ZPN_ATOMIC_FETCH_ADD8(&(dispatch_stats->total_cache_dispatch_hit_count), 1);
        }

        res = zpn_send_zpn_broker_request_struct_on_zpn_tlv(asst_conn, 0, &req);
        if (ZPN_RESULT_NO_ERROR == res) {
            mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_AST;
            mtunnel->log.brk_req_utoa_tx_us = now_us;
            if (mtunnel->policy_start_us) {
                mtunnel->log.policy_us += mtunnel->log.brk_req_utoa_tx_us - mtunnel->policy_start_us;
            }
            if (ZPN_BROKER_IS_PUBLIC() && mtunnel->last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                mtunnel->flag.timeout_redispatch_to_diff_dc = 1;
            }
            goto done;
        } else {
            mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_AST_FAIL;
        }
    } else {
        ZPN_DEBUG_PATH_CACHE("%s: path cache not found", c_state->tunnel_id);
        if (ZPN_BROKER_IS_PRIVATE()) {
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
            ZPN_ATOMIC_FETCH_ADD8(&(dispatch_stats->total_cache_dispatch_miss_count), 1);
        }
    }

    /* Look for negative path cache. */
    if (ZPN_BROKER_IS_PUBLIC() &&
        zpn_broker_client_neg_path_cache_enabled(c_state->customer_gid)) {
        res = zpn_broker_client_neg_path_cache_get(req.g_app, req.domain, req.c_port, req.ip_protocol, &mtunnel->dispatcher_id,
                                                   error_str, sizeof(error_str));
        if (res == ZPN_RESULT_NO_ERROR) {
            ZPN_DEBUG_NEG_PATH_CACHE("%s: Found entry in negative path cache, closing  mtunnel with %s error",
                                     mtunnel->mtunnel_id, error_str);
            mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_NEG_CACHE_HIT;
            req.path_decision = mtunnel->path_decision;
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, error_str);
            mtunnel_locked_state_machine(mtunnel);
            return res;
        }
    }

    mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_CACHE_MISS;
    req.path_decision = mtunnel->path_decision;
    req.connector_close_to_app = mtunnel->connector_close_to_app;

    mtunnel->dispatch_count++;

    if (mtunnel->dispatch_count > ZPN_MTUNNEL_DISPATCH_MAX) {
        mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_TOO_MANY_RETRY;
        ZPN_LOG(AL_WARNING, "%s: Exceeded dispatch count", mtunnel->mtunnel_id);
        if (ZPN_BROKER_IS_PRIVATE()) {
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
            ZPN_ATOMIC_FETCH_ADD8(&(dispatch_stats->total_max_dispatch_exceeded_count), 1);
        }
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REPEATED_DISPATCH);
        mtunnel_locked_state_machine(mtunnel);
        return ZPN_RESULT_ERR;
    } else {
        mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP;
        req.path_decision = mtunnel->path_decision;
        mtunnel->brk_req_seq_num++;
        req.seq_num = mtunnel->brk_req_seq_num;

        /* EAS, add extranet data to broker_request*/
        if (eas_prepare_broker_request_locked(mtunnel, &req)) {
            // need to close this mtunnel
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, MT_CLOSED_INTERNAL_ERROR);
            mtunnel_locked_state_machine(mtunnel);
            return ZPN_RESULT_ERR;
        }

        if (ZPN_BROKER_IS_PRIVATE()) {
            if (mtunnel->log.extranet) {
                // need to send the broker request to global dispatcher
                // the local dispatcher doesnt have ZIA location registrations
                // need to be called after this routine is completed

                struct zevent_base *base = fohh_thread_id_zevent_base(fohh_get_current_thread_id());
                struct argo_object *obj = argo_object_create(zpn_broker_request_description, &req);
                if (!obj) {
                    ZPN_LOG(AL_ERROR, "Could not create zpn_broker_request_description argo object");
                    res = ZPN_RESULT_NO_MEMORY;
                } else {
                    res = zevent_base_call(base, eas_pb_forward_broker_request, obj, 0);
                    if (res) {
                        argo_object_release(obj);
                    }
                }
            } else {
                res = zpn_broker_dispatch_send_request_local(&req, mtunnel->flag.c2c_regex_match,
                                                             mtunnel->flag.is_c2c_bypass_ld);
            }
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
            ZPN_ATOMIC_FETCH_ADD8(&(dispatch_stats->total_cloud_dispatch_count), 1);

        } else {
            if (mtunnel->last_disp_error == zpn_err_brk_req_app_in_learn_mode && mtunnel->timer_disp_id) {
                /* This was fired by timer. */
                last_disp_id = mtunnel->timer_disp_id;
            } else {
                last_disp_id = mtunnel->dispatcher_id;
            }
            req.last_g_dsp = last_disp_id;

            res = zpn_broker_dispatch_send_request(&req, mtunnel->last_disp_error, last_disp_id, &no_disp_ready);
        }

        /* Log the dispatcher id */
        if (mtunnel->last_disp_error == zpn_err_brk_req_no_connector_available ||
            mtunnel->last_disp_error == zpn_err_brk_req_invalid_domain ||
            mtunnel->last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
            mtunnel->redispatch_to_diff_dc_disp_id = req.g_dsp;
        }
        mtunnel->dispatcher_id = req.g_dsp;
        if (is_probe) {
            mtunnel->log.probetx_us = epoch_us();
        } else {
            if (ZPN_BROKER_IS_PUBLIC() && mtunnel->last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                mtunnel->log.redisptx_us = epoch_us();
            } else {
                mtunnel->log.disptx_us = epoch_us();
                if (mtunnel->policy_start_us) {
                    mtunnel->log.policy_us += mtunnel->log.disptx_us - mtunnel->policy_start_us;
                }
            }
        }

        if (res) {
            /* Probably _WOULD_BLOCK. We'll reject it. */
            if (ZPN_BROKER_IS_PRIVATE()) {
                struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
                struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
                ZPN_ATOMIC_FETCH_ADD8(&(dispatch_stats->total_cloud_dispatch_fail_count), 1);
            }
            mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_SEND_FAILED;
            if (ZPN_BROKER_IS_PUBLIC() &&
                mtunnel->last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                ZPN_LOG(AL_ERROR, "%s: Could not transmit zpn_broker_request to dispatcher in different DC (%s)",
                        mtunnel->mtunnel_id, zpn_result_string(res));
                mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_SEND_FAILED;
                struct zpn_broker_dispatcher_stats *broker_dispatcher_stats = zpn_broker_dispatcher_stats_data_obj_get();
                __sync_add_and_fetch_8(&(broker_dispatcher_stats->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc), 1);
                struct zpn_broker_dispatcher_stats *brk_dsp_stats = zpn_broker_dispatcher_get_stats(last_disp_id);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc), 1);
                }
                /*
                 * Don't close mtunnel if we couldn't redispatch to different DC in case of timeout.
                 * Let's wait for reply from first dispatcher or mtunnel will anyway timeout in 30 sec.
                 */
                goto done;
            }
            if (ZPN_BROKER_IS_PUBLIC() &&
                no_disp_ready &&
                (mtunnel->last_disp_error == zpn_err_brk_req_no_connector_available ||
                 mtunnel->last_disp_error == zpn_err_brk_req_invalid_domain)) {
                ZPN_LOG(AL_ERROR, "%s: Could not re-dispatch - no dispatcher ready in different DC (%s)", mtunnel->mtunnel_id, zpn_result_string(res));
                struct zpn_broker_dispatcher_stats *broker_dispatcher_stats = zpn_broker_dispatcher_stats_data_obj_get();
                __sync_add_and_fetch_8(&(broker_dispatcher_stats->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc), 1);
                struct zpn_broker_dispatcher_stats *brk_dsp_stats = zpn_broker_dispatcher_get_stats(last_disp_id);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc), 1);
                }
                mtunnel->dispatcher_id = mtunnel->last_dispatcher_id;

                if (mtunnel->last_disp_error == zpn_err_brk_req_no_connector_available) {
                    previous_error_str = ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE;
                } else {
                    previous_error_str = ZPN_ERR_BRK_REQ_INVALID_DOMAIN;
                }

                /* Negative path cache it if primary dispatcher had asked us to do it. */
                if (mtunnel->flag.neg_path_cache_on_brk &&
                    zpn_broker_client_neg_path_cache_enabled(mtunnel->customer_id)) {
                    neg_path_cache_ttl_us = zpn_broker_client_neg_path_cache_ttl_us(mtunnel->customer_id);
                    result = zpn_broker_client_neg_path_cache_set(req.g_app,
                                                                  req.domain,
                                                                  req.c_port,
                                                                  req.ip_protocol,
                                                                  mtunnel->last_dispatcher_id,
                                                                  previous_error_str,
                                                                  neg_path_cache_ttl_us);
                    if (result) {
                        ZPN_LOG(AL_ERROR, "Could not set negative path cache for g_app(%"PRId64"), domain(%s), c_port(%d), "
                                "ip_protocol(%d), g_dsp(%"PRId64"), error_str(%s), ttl_us(%"PRId64"): %s",
                                req.g_app, req.domain, req.c_port, req.ip_protocol, mtunnel->last_dispatcher_id,
                                previous_error_str, neg_path_cache_ttl_us, zpn_result_string(result));
                    }
                }

                /*
                 * If no dispatcher is ready in different DC for re-dispatch, close the mtunnel with previous error.
                 * This can happen if we've only one DC for dispatchers like in beta cloud. In such case, we need to
                 * close the mtunnel with previous error, else it'll cause actual error visibility loss.
                 */
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, previous_error_str);
            } else {
                ZPN_LOG(AL_ERROR, "%s: Rejected- could not transmit to dispatcher (%s)", mtunnel->mtunnel_id, zpn_result_string(res));
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_CANNOT_SEND_TO_DISPATCHER);
            }
            mtunnel_locked_state_machine(mtunnel);
        } else {
            if (ZPN_BROKER_IS_PUBLIC() && mtunnel->last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                mtunnel->flag.timeout_redispatch_to_diff_dc = 1;
            }
            if (!mtunnel->flag.donot_send_directly_to_connector && zpn_broker_client_path_cache_is_ready() && (0 == req.ignore_cache)) {

                if (cache_key_type && mtunnel->assistant_group_gid_count) {
                    //Generally for SIPA/BC/CC, server_group_gid can be received in mtunnel_request
                    //Assistant groups are looked up in mtunnel_locked_fill_assistant_groups
                    //Use it in path cache key only if feature flag is enabled and the ast grp gid is non-zero
                    mtunnel->flag.ast_grp_used_in_cache_key = 1;
                    int i;
                    for (i = 0; (i < ZPN_MAX_ASSISTANT_GROUP_GIDS) && (i < mtunnel->assistant_group_gid_count); i++) {
                        zpn_broker_client_path_cache_create(c_state, req.g_app, req.g_dsp,
                                                        mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]),
                                                        mtunnel->assistant_group_gid[i], req.domain, mtunnel->req_app_type, req.c_port,
                                                        req.ip_protocol);
                    }
                } else {
                    mtunnel->flag.ast_grp_used_in_cache_key = 0;
                    zpn_broker_client_path_cache_create(c_state, req.g_app, req.g_dsp,
                                                    mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]),
                                                    0, req.domain, mtunnel->req_app_type, req.c_port, req.ip_protocol);
                }
            }
        }
    }

done:
    if (ZPN_RESULT_NO_ERROR == res) {
#if 0 //FQDN_TO_SERVER_IP_HEALTH_APPS (ET-64854)
        if (is_probe) {
            mtunnel->state = zbms_probe_sent;
        } else {
#endif
            /* Don't change mtunnel state if we're doing parallel re-dispatch because of timeout. */
            if (mtunnel->last_disp_error != zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                mtunnel->state = zbms_dispatch_sent;
            }

        ZPN_DEBUG_MTUNNEL("%s : state set to %s", mtunnel->mtunnel_id, mtunnel_state(mtunnel->state));
#if 0 //FQDN_TO_SERVER_IP_HEALTH_APPS (ET-64854)
       }
#endif
    }
    return res;
}

static int int64_cmp(const void *a, const void *b)
{
    const int64_t *ia = (const int64_t *)a; // casting pointer types
    const int64_t *ib = (const int64_t *)b;
    if (*ia < *ib) return -1;
    if (*ia > *ib) return  1;
    return 0;
}

/* Locations are obtained from rule id
 * 1. Rule -> Locs
 * 2. Rule -> Loc Grps
 *   i) Loc Groups to Locs
 */
static int mtunnel_locked_fill_locs_frm_rule(struct zpn_broker_mtunnel *mtunnel)
{
    /* If there is no rule gid, then return */
    if (!mtunnel->rule_gid) {
        return ZPN_RESULT_NO_ERROR;
    }

    int64_t buffered_extranet_locs[MAX_BUFFERED_GROUPS];
    size_t buffered_extranet_locs_pos = 0;
    int res = ZPN_RESULT_NO_ERROR;

    /* Now get zpn_rule_to_location */
    struct zpn_rule_to_location *rule_to_loc[ZPN_MAX_LOCS_FOR_RULE] = {NULL};
    size_t rule_to_loc_count = ZPN_MAX_LOCS_FOR_RULE;
    res = zpn_rule_to_loc_get_by_rule_id(mtunnel->rule_gid,
                                         &(rule_to_loc[0]),
                                         &rule_to_loc_count,
                                         zpn_broker_mtunnel_callback,
                                         mtunnel,
                                         0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        mtunnel->async_count++;
    } else if (res && res != ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_WARNING, "Tunnelid %s, Customergid %"PRId64", Rule gid %"PRId64": "
           "error %s fetching the rule to locs",
            mtunnel->tunnel_id, mtunnel->customer_id, mtunnel->rule_gid,
            zpath_result_string(res));
        return res;
    } else if (res == ZPN_RESULT_NOT_FOUND) {
        /* Just log */
        ZPN_DEBUG_EAS("mtunnel id %s, customer gid %"PRId64": no rule to "
                "location configured for the rule gid %"PRId64,
                mtunnel->tunnel_id, mtunnel->customer_id,
                mtunnel->rule_gid);
    } else {
        for (size_t n = 0; n < rule_to_loc_count && buffered_extranet_locs_pos < MAX_BUFFERED_GROUPS; n++) {
            if (mtunnel->zpn_er_id == rule_to_loc[n]->zpn_er_id) {
                buffered_extranet_locs[buffered_extranet_locs_pos++] = rule_to_loc[n]->zpn_location_id;
            } else {
                ZPN_DEBUG_EAS("%s: c_gid %" PRId64
                              "location %" PRId64 "  partner id not matching %" PRId64
                              "/%" PRId64,
                              mtunnel->tunnel_id,
                              mtunnel->customer_id,
                              rule_to_loc[n]->zpn_location_id,
                              mtunnel->zpn_er_id,
                              rule_to_loc[n]->zpn_er_id);
            }
        }
    }

    /* Now get zpn_rule_to_location_group */
    struct zpn_rule_to_location_group *rule_to_loc_grp[ZPN_MAX_LOC_GRP_FOR_RULE] = {NULL};
    size_t rule_to_loc_grp_count = ZPN_MAX_LOC_GRP_FOR_RULE;
    res = zpn_rule_to_loc_group_get_by_rule_id(mtunnel->rule_gid,
                                               &(rule_to_loc_grp[0]),
                                               &rule_to_loc_grp_count,
                                               zpn_broker_mtunnel_callback,
                                               mtunnel,
                                               0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        mtunnel->async_count++;
    } else if (res && res != ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_WARNING, "Tunnelid %s, Customergid %"PRId64", Rule gid %"PRId64": "
           "error %s fetching the rule to loc groups",
            mtunnel->tunnel_id, mtunnel->customer_id, mtunnel->rule_gid,
            zpath_result_string(res));
        return res;
    } else if (res == ZPN_RESULT_NOT_FOUND) {
        /* Just log */
        ZPN_DEBUG_EAS("mtunnel id %s, customer gid %"PRId64": no rule to "
                "location group configured for the rule gid %"PRId64,
                mtunnel->tunnel_id, mtunnel->customer_id,
                mtunnel->rule_gid);
    } else {
        for (size_t p = 0; p < rule_to_loc_grp_count; p++) {
            /* For each location group find the list of locations */
            if (mtunnel->zpn_er_id != rule_to_loc_grp[p]->zpn_er_id) {
                continue;
            }
            size_t loc_grp_to_locs_cnt = ZPN_MAX_LOC_GRP_TO_LOC;
            struct zpn_location_group_to_location *loc_grp_to_locs[ZPN_MAX_LOC_GRP_TO_LOC] = {NULL};
            res = zpn_loc_group_to_loc_get_by_loc_group_id(rule_to_loc_grp[p]->zpn_location_group_id,
                                                           &(loc_grp_to_locs[0]),
                                                           &loc_grp_to_locs_cnt,
                                                           zpn_broker_mtunnel_callback,
                                                           mtunnel,
                                                           0);
           if (res == ZPN_RESULT_ASYNCHRONOUS) {
               mtunnel->async_count++;
           } else if (res && res != ZPN_RESULT_NOT_FOUND) {
               ZPN_LOG(AL_WARNING, "Tunnelid %s, Customergid %"PRId64", Location gid %"PRId64": "
                      "error %s fetching the loc groups to location",
                       mtunnel->tunnel_id, mtunnel->customer_id, rule_to_loc_grp[p]->zpn_location_group_id,
                       zpath_result_string(res));
               return res;
           } else if (res == ZPN_RESULT_NOT_FOUND) {
               /* Just log */
               ZPN_DEBUG_EAS("mtunnel id %s, customer gid %"PRId64": no "
                       "location group to location configured for the location gid %"PRId64,
                       mtunnel->tunnel_id, mtunnel->customer_id,
                       rule_to_loc_grp[p]->zpn_location_group_id);
           } else {
               for (size_t w = 0; w < loc_grp_to_locs_cnt && buffered_extranet_locs_pos < MAX_BUFFERED_GROUPS; w++) {
                       buffered_extranet_locs[buffered_extranet_locs_pos++] = loc_grp_to_locs[w]->zpn_location_id;
               }
           }
        }
    }
    if (mtunnel->async_count) return ZPN_RESULT_ASYNCHRONOUS;

    if (buffered_extranet_locs_pos < 1) {
        ZPN_DEBUG_EAS("Mtunnel id %s, customer gid %"PRId64" gotten this Rule id %"PRId64" does not have any extranet locations",
                       mtunnel->tunnel_id, mtunnel->customer_id, mtunnel->rule_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    size_t r = 0;
    size_t s = 0;

    /* Allocate fixed number of memory for the extranet_locs */
    int64_t tmp_extranet_locs[ZPN_MAX_EXTRANET_LOCS];
    qsort(buffered_extranet_locs, buffered_extranet_locs_pos, sizeof(buffered_extranet_locs[0]), int64_cmp);
    for (r = 0, s = 0; r < buffered_extranet_locs_pos && s < ZPN_MAX_EXTRANET_LOCS; r++, s++) {
        tmp_extranet_locs[s] = buffered_extranet_locs[r];
        /* Skip duplicates */
        while (((r + 1) < buffered_extranet_locs_pos) && (tmp_extranet_locs[s] == buffered_extranet_locs[r+1])) r++;
    }

    mtunnel->extranet_locs_count = s;
    if (mtunnel->extranet_locs) {
        ZPN_FREE_AND_NULL(mtunnel->extranet_locs);
    }
    if (mtunnel->extranet_locs_count > 0) {
        mtunnel->extranet_locs = ZPN_CALLOC(sizeof(int64_t) * mtunnel->extranet_locs_count);
        for (int m = 0; m < mtunnel->extranet_locs_count; m++) {
            mtunnel->extranet_locs[m] = tmp_extranet_locs[m];
        }
    }

    if (r < buffered_extranet_locs_pos && mtunnel->extranet_locs_count == ZPN_MAX_EXTRANET_LOCS) {
        ZPN_LOG(AL_WARNING, "Tunnel id %s, customer id %"PRId64": may not have attached "
               "all unique extranet locs to request because there were too many, "
               "Found %zu, allowed %d", mtunnel->tunnel_id, mtunnel->customer_id,
               r, ZPN_MAX_EXTRANET_LOCS);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Fill the mtunnel assistant groups based on connector policy tables */
static int mtunnel_locked_fill_rule_assistant_groups(struct zpn_broker_mtunnel *mtunnel) {

    if (mtunnel->extranet_enabled) {
        return mtunnel_locked_fill_locs_frm_rule(mtunnel);
    }

    if (!mtunnel->rule_gid) {
        // We don't have a rule so we don't modify the assistant_group_gids
        // This isn't an error the rest of the system can handle itself and not modifying
        // the state is safe + the right thing to do
        return ZPN_RESULT_NO_ERROR;
    }

    int64_t buffered_connector_group_gids[MAX_BUFFERED_GROUPS];
    size_t buffered_connector_group_pos = 0;

    int res;
    int found_server_groups = 0;
    int attached_values = 0;
    size_t total_found = mtunnel->assistant_group_gid_count;

    // grab any existing assistant groups and throw them in our set
    if (mtunnel->assistant_group_gid_count) {
        int i;
        for(i=0; i < mtunnel->assistant_group_gid_count; i++) {
            buffered_connector_group_gids[buffered_connector_group_pos] = mtunnel->assistant_group_gid[i];
            buffered_connector_group_pos++;
        }
    }



    // Get rows from zpn_rule_to_server_groups

    struct zpn_rule_to_server_group *server_groups[MAX_SERVER_GROUPS_FOR_RULE];
    size_t server_groups_count = MAX_SERVER_GROUPS_FOR_RULE;
    res = zpn_rule_to_server_group_by_rule_gid(mtunnel->rule_gid,
                                               &(server_groups[0]),
                                               &server_groups_count,
                                               zpn_broker_mtunnel_callback,
                                               mtunnel,
                                               0);

    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        mtunnel->async_count++;
    } else if (res && res != ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_WARNING, "%s: error fetching server groups for user %s", mtunnel->tunnel_id, zpath_result_string(res));
        return res;
    } else {
        // Get rows from zpn_server_groups_assistant_groups
        found_server_groups = (server_groups_count > 0);
        size_t i;
        for (i = 0; i < server_groups_count; i++) {
            size_t relations_count = 1000;
            struct zpn_server_group_assistant_group *server_group_assistants[1000];
            res = zpn_server_group_assistant_group_get_by_server_group(server_groups[i]->server_group_gid,
                                                                       &(server_group_assistants[0]),
                                                                       &relations_count,
                                                                       zpn_broker_mtunnel_callback,
                                                                       mtunnel,
                                                                       0);

            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                mtunnel->async_count++;
                continue;
            } else if (res && res != ZPN_RESULT_NOT_FOUND) {
                ZPN_LOG(AL_WARNING,
                        "%s: error fetching server group assistant group matching for user %s",
                        mtunnel->tunnel_id,
                        zpath_result_string(res));
                return res;
            } else if (res == ZPN_RESULT_NOT_FOUND) {
                ZPN_LOG(AL_INFO, "%s: no assistant group for configured server group on policy: server group id: %ld",
                        mtunnel->tunnel_id,
                        (long) server_groups[i]->server_group_gid);
            } else {
                total_found += relations_count;
                size_t j;
                for (j = 0; j < relations_count && buffered_connector_group_pos < MAX_BUFFERED_GROUPS; j++) {
                    attached_values = 1;
                    buffered_connector_group_gids[buffered_connector_group_pos] = server_group_assistants[j]->assistant_group_id;
                    buffered_connector_group_pos++;
                }
            }
        }
    }

    // Get rows from zpn_rule_to_assistant_groups
    size_t rule_assistant_groups_count = MAX_ASSISTANT_GROUPS_FOR_RULE;
    struct zpn_rule_to_assistant_group *rule_assistant_groups[MAX_ASSISTANT_GROUPS_FOR_RULE];
    res = zpn_rule_to_assistant_group_by_rule_gid(mtunnel->rule_gid,
                                                  &(rule_assistant_groups[0]),
                                                  &rule_assistant_groups_count,
                                                  zpn_broker_mtunnel_callback,
                                                  mtunnel,
                                                  0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        mtunnel->async_count++;
    } else if (res && res != ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_WARNING,
                "%s: error fetching assistant group for rule %s",
                mtunnel->tunnel_id,
                zpath_result_string(res));
        return res;
    } else {
        total_found += rule_assistant_groups_count;
        size_t i;
        for (i = 0; i < rule_assistant_groups_count && buffered_connector_group_pos < MAX_BUFFERED_GROUPS; i++) {
            buffered_connector_group_gids[buffered_connector_group_pos] = rule_assistant_groups[i]->assistant_group_gid;
            buffered_connector_group_pos++;
        }
    }

    if (mtunnel->async_count) return ZPN_RESULT_ASYNCHRONOUS;

    if (total_found > MAX_BUFFERED_GROUPS) {
        ZPN_LOG(AL_WARNING, "%s: may not be able to attach all unique connector groups to mtunnel because there were too many max:%d found:%ld", mtunnel->tunnel_id, MAX_BUFFERED_GROUPS, total_found);
    }

    size_t i;
    size_t j;
    qsort(buffered_connector_group_gids, buffered_connector_group_pos, sizeof(buffered_connector_group_gids[0]), int64_cmp);
    for (i = 0, j = 0; i < buffered_connector_group_pos && j < ZPN_MAX_ASSISTANT_GROUP_GIDS; i++, j++) {
        mtunnel->assistant_group_gid[j] = buffered_connector_group_gids[i];
        /* Skip duplicates */
        while (((i + 1) < buffered_connector_group_pos) && (mtunnel->assistant_group_gid[j] == buffered_connector_group_gids[i+1])) i++;
    }
    mtunnel->assistant_group_gid_count = j;

    if (i < buffered_connector_group_pos && mtunnel->assistant_group_gid_count == ZPN_MAX_ASSISTANT_GROUP_GIDS) {
        ZPN_LOG(AL_WARNING, "%s: may not have attached all unique groups to request because there were too many", mtunnel->tunnel_id);
    }

    /*
     * Detect the following scenario:
     * Customer configured only server groups
     * None of the server groups were properly configured
     * We were unable to attach any connector groups to the request because of misconfiguration so error
     *
     * Misconfiguration here means server group has no assigned connector groups so lookup fails during mapping table
     *
     * This allows a partial configuration to succeed though such as:
     * 1) configuration that has misconfigured server group and some connector groups
     * 2) configuration that has multiple server groups but not all are misconfigured
     * 3) combination of 1+2
     */
    if (found_server_groups && !attached_values) {
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Fill the mtunnel assistant groups based on any server groups that
 * may exist in the mtunnel */
static int mtunnel_locked_fill_assistant_groups(struct zpn_broker_mtunnel *mtunnel)
{
    /* For extranet apps, ignore the server group list send by the client */
    if (mtunnel->extranet_enabled) {
        return ZPN_RESULT_NO_ERROR;
    }

    int64_t s_gid;
    int res;

    mtunnel->assistant_group_gid_count = 0;
    if (mtunnel->server_group_gid_count == 0) {
        //ZPN_LOG(AL_DEBUG, "%s: Fill assistant groups- No GIDs to check", mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_ERROR;
    }

    for (int i = 0; i < mtunnel->server_group_gid_count; i++) {
        s_gid = mtunnel->server_group_gid[i];
        if (ZPATH_GID_GET_CUSTOMER_GID(s_gid) == mtunnel->customer_id) {
            struct zpn_server_group *s_grp;
            //ZPN_LOG(AL_DEBUG, "%s: Fill assistant groups- Checking s_gid = %ld", mtunnel->mtunnel_id, (long) s_gid);
            res = zpn_server_group_get_async(s_gid,
                                             &s_grp,
                                             zpn_broker_mtunnel_callback,
                                             mtunnel,
                                             0);
            if (res) {
                if (res == ZPN_RESULT_ASYNCHRONOUS) {
                    //ZPN_LOG(AL_DEBUG, "%s: Fill assistant groups- server group fetch- async %d->%d", mtunnel->mtunnel_id, mtunnel->async_count, mtunnel->async_count + 1);
                    mtunnel->async_count++;
                } else {
                    ZPN_LOG(AL_WARNING, "%s: Error retrieving server group %ld: %s", mtunnel->mtunnel_id, (long) s_gid, zpn_result_string(res));
                }
            } else {
                if (s_grp->enabled) {
                    struct zpn_server_group_assistant_group *relations[1000];
                    size_t relations_count = 1000;
                    res = zpn_server_group_assistant_group_get_by_server_group(s_gid,
                                                                               &(relations[0]),
                                                                               &relations_count,
                                                                               zpn_broker_mtunnel_callback,
                                                                               mtunnel,
                                                                               0);
                    if (res) {
                        if (res == ZPN_RESULT_ASYNCHRONOUS) {
                            //ZPN_LOG(AL_DEBUG, "%s: Fill assistant groups- relation fetch- async %d->%d", mtunnel->mtunnel_id, mtunnel->async_count, mtunnel->async_count + 1);
                            mtunnel->async_count++;
                        } else {
                            ZPN_LOG(AL_WARNING, "%s: Error retrieving group relations, current server group = %ld", mtunnel->mtunnel_id, (long) s_gid);
                        }
                    } else {
                        for (size_t j = 0; j < relations_count; j++) {
                            //ZPN_LOG(AL_DEBUG, "%s: Fill assistant groups- Filling s_gid = %ld, app_grp_gid = %ld", mtunnel->mtunnel_id, (long) s_gid, (long)relations[j]->assistant_group_id);
                            if (mtunnel->assistant_group_gid_count < ZPN_MAX_ASSISTANT_GROUP_GIDS) {
                                mtunnel->assistant_group_gid[mtunnel->assistant_group_gid_count] = relations[j]->assistant_group_id;
                                mtunnel->assistant_group_gid_count++;
                            } else {
                                ZPN_LOG(AL_WARNING, "%s: Exceeded asstant group count on request, current server group = %ld", mtunnel->mtunnel_id, (long) s_gid);
                            }

                        }
                    }
                }
            }
        } else {
            ZPN_LOG(AL_WARNING, "%s: Received server group gid = %ld outside of customer %ld", mtunnel->mtunnel_id, (long) s_gid, (long) mtunnel->customer_id);
        }
    }
    if (mtunnel->async_count) return ZPN_RESULT_ASYNCHRONOUS;

    if (mtunnel->assistant_group_gid_count == 0) {
        /* No enabled assistant groups found- rather than letting this
         * connection go through whatever 'works', we will return an
         * error and fail the connection */
        return ZPN_RESULT_NOT_FOUND;
    }

    /* Now we will sort them and remove duplicates */
    qsort(mtunnel->assistant_group_gid, mtunnel->assistant_group_gid_count, sizeof(mtunnel->assistant_group_gid[0]), int64_cmp);
    int i;
    int j;
    for (i = 0, j = 0; i < mtunnel->assistant_group_gid_count; i++, j++) {
        /* This might reassign to same value if no duplicates, but that is fine. */
        mtunnel->assistant_group_gid[j] = mtunnel->assistant_group_gid[i];
        /* Skip duplicates */
        while (((i + 1) < mtunnel->assistant_group_gid_count) && (mtunnel->assistant_group_gid[i] == mtunnel->assistant_group_gid[i + 1])) i++;
    }
    mtunnel->assistant_group_gid_count = j;
    return ZPN_RESULT_NO_ERROR;
}

static int mtunnel_locked_get_inspt_gid_by_domain(struct zpn_broker_mtunnel *mtunnel)
{
    int64_t app_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(mtunnel->application_id);
    int res;
    int32_t dst_port = mtunnel->client_port;

    /* In case of auto app protection or when adp enabled, no ports are configured per
     * inspection table. The protocol traffic is detected from received packet.
     */
    if ((mtunnel->flag.adp_enabled) || (mtunnel->flag.auto_app_protect_enabled)) {
        dst_port = 0;
        ZPN_DEBUG_RULE("mtunnel %s : app protection %d or adp_enabled %d for customer %ld with insp_appl_gid %ld, getting inspection data using dstport 0",
                       mtunnel->mtunnel_id, mtunnel->flag.auto_app_protect_enabled,
                       mtunnel->flag.adp_enabled, (long)app_customer_gid, (long)mtunnel->insp_appl_gid);
    }
    res = zpn_inspection_application_get_gid(app_customer_gid, mtunnel->req_app_name, mtunnel->application_domain, dst_port,
                                             &mtunnel->insp_appl_gid, zpn_broker_mtunnel_callback, mtunnel, 0);

    if (res) {
        if (ZPN_RESULT_ASYNCHRONOUS == res) {
            ZPN_DEBUG_RULE(
                           "mtunnel %s: fetching inspection application for customer %ld returned async (count %d -> %d).",
                           mtunnel->mtunnel_id, (long)app_customer_gid, mtunnel->async_count, mtunnel->async_count + 1);
            mtunnel->async_count++;
        } else {
            if (ZPN_RESULT_NOT_FOUND != res) {
                ZPN_DEBUG_RULE("%s: Customer %ld: get_inspection_application_by_domain %s port %d returned %s",
                               mtunnel->mtunnel_id, (long)app_customer_gid, mtunnel->req_app_name, mtunnel->client_port,
                               zpn_result_string(res));
            }
            /* add WAF feature check */
            res = mtunnel->async_count ? ZPN_RESULT_ASYNCHRONOUS : ZPN_RESULT_NO_ERROR; /* do not fail */
        }
    } else {
        ZPN_DEBUG_RULE(
                "Mtunnel %s: WAF policy evaluation completed successfully: "
                "Inspection[APP-Id, Rule, Profile]:=[%ld, %ld, %ld]",
                mtunnel->mtunnel_id, (long)mtunnel->insp_appl_gid, (long)mtunnel->insp_rule_gid,
                (long)mtunnel->insp_profile_gid);
        if (0 == mtunnel->insp_appl_gid) {
            mtunnel->insp_profile_gid = 0; /* marking for connector inspection application is disabled */
        }
        res = mtunnel->async_count ? ZPN_RESULT_ASYNCHRONOUS : ZPN_RESULT_NO_ERROR;
    }
    return res;
}

/* Verify if the Protocol tagging is enabled based on the client type */
static int zpn_broker_mtunnel_is_ptag_enabled(struct zpn_broker_mtunnel *mtunnel)
{
    if (!mtunnel)
        return 0;

    /* disable for PRA */
    if (mtunnel->flag.is_pra_session)
        return 0;

    if (is_scope_default(mtunnel->scope_gid) && mtunnel->flag.waf_enabled &&
            zpn_broker_is_waf_ptag_enabled(mtunnel->broker_id, mtunnel->customer_id) &&
            zpn_client_static_config[mtunnel->client_type].ptag) {
        return 1;
    }
    return 0;
}

static int zpn_broker_mtunnel_c2c_regex_match_check(struct zpn_broker_mtunnel *mtunnel) {

    if (!ZPN_BROKER_IS_PRIVATE()) {
        return 0;
    }

    if (!is_c2c_ldbypass_feature_enabled(mtunnel->customer_id)) {
        // C2C LD Bypass 2.0 feature isn't enabled.
        return 0;
    }

    if (zpn_broker_dispatch_is_c2c_local_route_exist(mtunnel->customer_id, mtunnel->req_app_name)) {
        ZPN_DEBUG_MTUNNEL("C2C Local route exist for requested fqdn: %s .Don't hold mtunnel %s", mtunnel->req_app_name, mtunnel->mtunnel_id);
        return 0;
    }

    if (zpn_broker_dispatch_c2c_regex_match_check_req(mtunnel->mtunnel_id, mtunnel->req_app_name)) {
        // Regex match found
        return 1;
    }
    return 0;
}

/*
 * Send any messages that need to be sent...
 *
 * Time out anything that needs to be timed out...
 *
 * Locking: It is assumed both bucket and mtunnel are locked at this point.
 */
void mtunnel_locked_state_machine(struct zpn_broker_mtunnel *mtunnel)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char *msg = NULL;
    struct zpn_tlv *client_tlv = NULL;
    struct zpn_broker_client_fohh_state *c_state = NULL;

    if (!mtunnel) {
        ZPN_LOG(AL_CRITICAL, "Null mtunnel");
        return;
    }
    ZPN_DEBUG_MTUNNEL("%s: State Machine Entry, state = %s", mtunnel->mtunnel_id, mtunnel_state(mtunnel->state));

    client_tlv = zpn_broker_mtunnel_client_tlv(mtunnel);

    /* Generic timeout check */
    switch (mtunnel->state) {
    case zbms_probe_sent:
        // Do nothing.
        break;
    case zbms_request_received:
        /* Perform authentication- get a user ID */
        /* This should generally be quick once the client connection
         * has been authenticated once. */
        /* For now, we just do a user lookup. We do this based on the
         * certificate- the CN of the certificate is a user ID. */
        res = zpn_broker_mtunnel_authenticate(mtunnel);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_MTUNNEL("%s: Waiting for asynchronous response- authentication verification", mtunnel->mtunnel_id);
            break;
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_NOTICE, "%s: User not found", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_USER_NOT_FOUND);
            mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (res == ZPN_RESULT_EXPIRED) {
            ZPN_LOG(AL_NOTICE, "%s: Saml assertion expired", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_SAML_EXPIRED);
            mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (res) {
            ZPN_LOG(AL_WARNING, "%s: Error: %s", mtunnel->mtunnel_id, zpath_result_string(res));
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, zpath_result_string(res));
            mtunnel_locked_state_machine(mtunnel);
            break;
        }
        ZPN_DEBUG_MTUNNEL("%s: Associated with user %s", mtunnel->mtunnel_id, mtunnel->user_id);
        /* Else: Success... */

#if 0 //FQDN_TO_SERVER_IP_HEALTH_APPS (ET-64854)
        c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
        /* Now, check configuration. If we are configured to do
         * dispatch probing, do that now, before policy check. */
        if (c_state && zpn_broker_policy_fqdn_to_srv_ip_enabled(c_state->customer_gid)) {
            int cache_fetch_status = ZPN_RESULT_ERR;
            int64_t dispatcher_id, g_ast, g_ast_grp, g_app_grp, g_aps, g_srv_grp;
            struct argo_inet s_ip;

            /* Get broker cache value for server ip before probing. */
            if (!mtunnel->flag.donot_send_directly_to_connector) {
                struct zpn_tlv *asst_conn = NULL;

                int cache_key_type = zpn_broker_client_path_cfg_get_key_type(c_state->customer_gid);
                if (cache_key_type && mtunnel->assistant_group_gid_count) {
                    mtunnel->flag.ast_grp_used_in_cache_key = 1;
                }
                cache_fetch_status = zpn_broker_client_path_cache_get_valid(c_state,
                                                                            mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]), 0,
                                                                            mtunnel->req_app_name, mtunnel->req_app_type, mtunnel->client_port, mtunnel->ip_protocol,
                                                                            &dispatcher_id, &g_ast, &g_ast_grp,
                                                                            &g_app_grp, &g_aps, &g_srv_grp, &s_ip, &asst_conn, 1);
            }

            /* If there is a valid broker cache, use the same else send probe. */
            if (cache_fetch_status != ZPN_RESULT_NO_ERROR) {
                /* Perform a probe to the dispatcher to determine where it will send this request */
                mtunnel_locked_send_broker_request_from_another_thread(mtunnel, 1);
                break;
            } else {
                mtunnel->log.s_ip =  s_ip;
                mtunnel->log.g_ast = g_ast;
            }
        }
#endif
        mtunnel->state = zbms_authenticated;
        mtunnel->policy_start_us = epoch_us();
        mtunnel->last_disp_error = zpn_err_brk_req_no_error;

        /* FALL THROUGH!!! */
    case zbms_authenticated:
        res = mtunnel_locked_fill_assistant_groups(mtunnel);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_MTUNNEL("%s: Waiting for asynchronous responses- fill assistant groups", mtunnel->mtunnel_id);
            break;
        } else if (res) {
            if (mtunnel_request_ack(mtunnel,
                                    0,
                                    mtunnel->log.c_tag,
                                    mtunnel->mtunnel_id,
                                    BRK_MT_SETUP_FAIL_NO_ASSISTANT_GROUPS,
                                    msg)) {
                ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                        mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
            }
            ZPN_DEBUG_MTUNNEL("%s: Rejected- No Assistant Groups", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_NO_ASSISTANT_GROUPS);
            break;
        }

        /* Deny access if user is deactivated or not synced via SCIM */
        if (!c_state) c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
        if (c_state && zpn_client_static_config[c_state->client_type].scim_compatible &&
                !zpn_broker_client_scim_user_active(c_state)) {
            ZPN_DEBUG_MTUNNEL("%s: mtunnel access rejected because user was deactivated or not synced via scim",
                    mtunnel->mtunnel_id);
            if (zpn_broker_client_scim_done(c_state) != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_INFO, "%s: mtunnel access request rejected due to unknown scim user state, auth_complete:%d",
                        mtunnel->mtunnel_id, c_state->auth_complete);
            }
            if (mtunnel_request_ack(mtunnel,
                                    0,
                                    mtunnel->log.c_tag,
                                    mtunnel->mtunnel_id,
                                    BRK_MT_SETUP_FAIL_SCIM_INACTIVE,
                                    msg)) {
                ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
            }
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_SCIM_INACTIVE);
            break;
        }
        if (is_pra_disabled(mtunnel->customer_id) || is_jit_disabled(mtunnel->customer_id)) {
            mtunnel->flag.jit_disabled = 1;
        }

        /* if WAF_ENABLED set mtunnel bit WAF enabled */
        /* WAF should not never be invoked for ZDX created control messages */
        if (mtunnel->zpn_probe_type == zpn_probe_type_default) {
            mtunnel->flag.waf_enabled = zpn_broker_is_waf_enabled(mtunnel->broker_id, mtunnel->customer_id) ? 1 : 0;
            mtunnel->flag.ptag_enabled = zpn_broker_mtunnel_is_ptag_enabled(mtunnel);
        }

        /* Only in DR mode skip policy evaluation on PB */
        int do_policy_or_insp = 0;

        if (ZPN_BROKER_IS_PUBLIC() || !zpn_is_dr_mode_active()) {
            do_policy_or_insp = 1;
        } else {
            res = mtunnel_fill_required_fields_for_dr(mtunnel, &msg);
            if (res != ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_MTUNNEL("%s: DR mode: error while filling required field %s", mtunnel->mtunnel_id, zpath_result_string(res));
            }
        }

        mtunnel->log.mtunnel_req_policy_check_start_us = epoch_us();
        if (do_policy_or_insp) {
            res = mtunnel_locked_policy_check(mtunnel, &msg);
        } else {
            res = ZPN_RESULT_NO_ERROR;
            ZPN_LOG(AL_DEBUG, "%s: DR mode: Skip policy evaluation for PB", mtunnel->mtunnel_id);
        }

        /* Mtunnel req time not calcualed and res is not asynchronous */
        if (!mtunnel->log.mtunnel_req_policy_check_time_us &&
            (mtunnel->log.mtunnel_req_policy_check_start_us != 0) &&
            (res != ZPN_RESULT_ASYNCHRONOUS)) {
            mtunnel->log.mtunnel_req_policy_check_time_us = epoch_us() - mtunnel->log.mtunnel_req_policy_check_start_us;
            int zthread_num = fohh_thread_id_get_zthread_num(mtunnel->c_state_fohh_thread_id);
            if (zthread_num >= 0) {
                policy_mtunnel_eval_stats[zthread_num].total_mtunnel_policy_eval_time_us += mtunnel->log.mtunnel_req_policy_check_time_us;
                policy_mtunnel_eval_stats[zthread_num].num_mtunnels++;
                if (mtunnel->log.mtunnel_req_policy_check_time_us > policy_mtunnel_eval_stats[zthread_num].max_mtunnel_policy_eval_time_us) {
                    policy_mtunnel_eval_stats[zthread_num].max_mtunnel_policy_eval_time_us = mtunnel->log.mtunnel_req_policy_check_time_us;
                }
            }
        }

        if (do_policy_or_insp) {
            if (mtunnel->insp_profile_gid && (ZPN_RESULT_NO_ERROR == res || ZPN_RESULT_ASYNCHRONOUS == res)) {
                /* inspection application GID lookup: by customer & domain:port */
                res = mtunnel_locked_get_inspt_gid_by_domain(mtunnel);
            }
        } else {
            res = ZPN_RESULT_NO_ERROR;
            ZPN_LOG(AL_DEBUG, "%s: Skip inspection in DR mode for PB", mtunnel->mtunnel_id);
        }

        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_MTUNNEL("%s: Waiting for asynchronous responses- policy check", mtunnel->mtunnel_id);
            break;
        } else if (res != ZPN_RESULT_NO_ERROR) {
            if(res == ZPN_RESULT_EXPIRED) {
                ZPN_DEBUG_MTUNNEL("%s: sending auth expired for %s client", mtunnel->mtunnel_id, zpn_client_type_string(c_state->client_type));
                char *error_str =
                    (c_state && (c_state->client_type == zpn_client_type_vdi)) ? BRK_MT_SETUP_FAIL_LAST_AUTH_EXPIRED : BRK_MT_SETUP_FAIL_REAUTH_EXPIRED;

                if (mtunnel_request_ack(mtunnel,
                                        0,
                                        mtunnel->log.c_tag,
                                        mtunnel->mtunnel_id,
                                        error_str,
                                        msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                            mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }

                ZPN_DEBUG_MTUNNEL("%s: Rejected- REAUTH policy %"PRId64"", mtunnel->mtunnel_id, mtunnel->rule_gid);
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, error_str);
            } else if (res == ZPN_RESULT_ACCESS_DENIED) {
                ZPN_DEBUG_MTUNNEL("%s: Rejected- Access Denied", mtunnel->mtunnel_id);
                if (mtunnel_request_ack(mtunnel,
                                        0,
                                        mtunnel->log.c_tag,
                                        mtunnel->mtunnel_id,
                                        BRK_MT_SETUP_FAIL_ACCESS_DENIED,
                                        msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_ACCESS_DENIED);
            } else if (res == ZPN_RESULT_AL_AUTH_EXPIRED) {
                ZPN_DEBUG_MTUNNEL("stepup_auth: stepup_auth_level:{ id:%s name:%s expiry:%"PRId64"} expired for customer:%"PRId64" scope:%"PRId64" tunnel:%s",
                                    mtunnel->required_stepup_auth_level_id,
                                    mtunnel->required_stepup_auth_level_name,
                                    mtunnel->required_stepup_auth_level_expiry_s,
                                    mtunnel->customer_id, mtunnel->scope_gid, mtunnel->mtunnel_id);
                if (mtunnel_request_ack(mtunnel,
                                        0,
                                        mtunnel->log.c_tag,
                                        mtunnel->mtunnel_id,
                                        BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL,
                                        msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }
                ZPN_FREE(msg); //Only applicable for stepup auth related errors
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL);
            } else if (res == ZPN_RESULT_AL_AUTH_REQUIRED) {
                ZPN_DEBUG_MTUNNEL("stepup_auth: stepup_auth_level:{id:%s name:%s} required for customer:%"PRId64" scope:%"PRId64" tunnel:%s",
                                    mtunnel->required_stepup_auth_level_id,
                                    mtunnel->required_stepup_auth_level_name,
                                    mtunnel->customer_id, mtunnel->scope_gid, mtunnel->mtunnel_id);
                if (mtunnel_request_ack(mtunnel,
                                        0,
                                        mtunnel->log.c_tag,
                                        mtunnel->mtunnel_id,
                                        BRK_MT_SETUP_FAIL_REAUTH_WITH_AL,
                                        msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }
                ZPN_FREE(msg); //Only applicable for stepup auth related errors
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_WITH_AL);
            } else {
                /* Policy failure- reject it. */
                if (mtunnel->approval_id) {
                    ZPN_DEBUG_MTUNNEL("%s: Rejected- JIT approval %"PRId64, mtunnel->mtunnel_id, mtunnel->approval_id);
                    if (mtunnel_request_ack(mtunnel,
                                            0,
                                            mtunnel->log.c_tag,
                                            mtunnel->mtunnel_id,
                                            BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL,
                                            msg)) {
                        ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                        mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                    }
                    mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL);
                } else if (mtunnel->rule_gid) {
                    ZPN_DEBUG_MTUNNEL("%s: Rejected- policy %ld", mtunnel->mtunnel_id, (long) mtunnel->rule_gid);
                    if (mtunnel_request_ack(mtunnel,
                                            0,
                                            mtunnel->log.c_tag,
                                            mtunnel->mtunnel_id,
                                            BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY,
                                            msg)) {
                        ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                        mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                    }
                    mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY);
                } else {
                    if (res == ZPN_RESULT_BAD_ARGUMENT) {
                        /* Bad argument usually means we didn't do inclusive learning right */
                        if (mtunnel_request_ack(mtunnel,
                                                0,
                                                mtunnel->log.c_tag,
                                                mtunnel->mtunnel_id,
                                                BRK_MT_SETUP_FAIL_NO_PROBE_DATA,
                                                NULL)) {
                            ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                        }
                    } else {
                        if (mtunnel_request_ack(mtunnel,
                                                0,
                                                mtunnel->log.c_tag,
                                                mtunnel->mtunnel_id,
                                                BRK_MT_SETUP_FAIL_NO_POLICY_FOUND,
                                                NULL)) {
                            ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                        }
                    }
                    ZPN_DEBUG_MTUNNEL("%s: Rejected- no policy match, default deny, res = %s", mtunnel->mtunnel_id, zpn_result_string(res));
                    /* NOTE: We use exactly ONE reason code for policy
                     * rejection. The transaction log contains a rule that
                     * matched causing the policy rejection, or does not,
                     * which indicates that no policy matches */
                    mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_NO_POLICY_FOUND);
                }
            }
            mtunnel_locked_state_machine(mtunnel);
            break;
        }
        ZPN_DEBUG_MTUNNEL("%s: Allowed, matched rule %ld %s (%ld)", mtunnel->mtunnel_id, (long) mtunnel->rule_gid,
                          (0==mtunnel->insp_profile_gid)?"without inspection":"and inspection enabled",
                          (long)mtunnel->insp_profile_gid);
        //if policy allowed the action and it is a web probe make sure we have capacity
        if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe ||
            mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https ||
            mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_deprecated) {

            if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https && is_zpn_zdx_https_webprobe_disabled(mtunnel->customer_id)) {
                ZPN_LOG(AL_DEBUG, "%s: ZDX HTTPs is disabled",mtunnel->mtunnel_id);
                zpn_broker_mtunnel_stats_inc_closed_webprobe_https_disabled();
                if (mtunnel_request_ack(mtunnel,
                                    0,
                                    mtunnel->log.c_tag,
                                    mtunnel->mtunnel_id,
                                    BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED,
                                    msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED);
            }

            if (zpn_zdx_webprobe_rate_limit_exceeds_capacity(mtunnel->customer_id, mtunnel->application_id)) {
                ZPN_LOG(AL_DEBUG, "%s: ratelimiting application connection from %"PRId64" for application %"PRId64,
                        mtunnel->mtunnel_id,
                        mtunnel->customer_id,
                        mtunnel->application_id);
                if (mtunnel_request_ack(mtunnel,
                                    0,
                                    mtunnel->log.c_tag,
                                    mtunnel->mtunnel_id,
                                    BRK_MT_SETUP_FAIL_WEBPROBE_RATE_LIMITED,
                                    msg)) {
                    ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                    mtunnel->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                }
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_WEBPROBE_RATE_LIMITED);
            }
        }

        res = mtunnel_locked_fill_rule_assistant_groups(mtunnel);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_MTUNNEL("%s: Waiting for asynchronous responses- connector policy", mtunnel->mtunnel_id);
            break;
        } else if (res) {
            ZPN_LOG(AL_WARNING,
                    "%s: failed to attach server groups from valid policy... failing the connection",
                    mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_CONNECTOR_GROUPS_MISSING);
        }

        // If no explicit connector specified and inclusive match, choose most specific application gid to send to dispatcher.
        if (!mtunnel->assistant_group_gid_count && mtunnel->most_specific_application_id && (mtunnel->most_specific_application_id != mtunnel->application_id)) {
            mtunnel->flag.specific_app_and_matched_app_different = 1;
            mtunnel->application_id = mtunnel->most_specific_application_id;
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_specific_and_matched_app_differ);
        }

        /* Check DTLS configuration */
        int allow_all_xport = zpn_broker_is_dtls_enabled_on_broker();

        zpn_broker_get_app_buffer_parameters();

        /* For now we don't control dtls at the per app level */
#if 0
        struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
        if ((c_state->client_type == zpn_client_type_zapp) ||
            (c_state->client_type == zpn_client_type_exporter)) {
            allow_all_xport = zpn_broker_is_dtls_enabled_for_app(mtunnel->application_id,
                                                   mtunnel->app_group_id,
                                                   c_state->cert_id,
                                                   mtunnel->customer_id);
        } else if ((c_state->client_type == zpn_client_type_slogger) ||
                   (c_state->client_type == zpn_client_type_private_broker) ||
                   (c_state->client_type == zpn_client_type_ip_anchoring) ||
                   (c_state->client_type == zpn_client_type_exporter_noauth)) {
            allow_all_xport = zpn_broker_is_dtls_enabled_for_app(mtunnel->application_id,
                                                   mtunnel->app_group_id,
                                                   0,
                                                   mtunnel->customer_id);
        } else if (c_state->client_type == zpn_client_type_edge_connector) {
            allow_all_xport = zpn_broker_is_dtls_enabled_for_app(mtunnel->application_id,
                                                   mtunnel->app_group_id,
                                                   c_state->znf_gid,
                                                   mtunnel->customer_id);
        }
#endif

        if (allow_all_xport) {
            ZPN_DEBUG_MTUNNEL("%s: dtls is enabled", mtunnel->mtunnel_id);
            mtunnel->allow_all_xport = 1;
        }

        if (mtunnel->application_id == track_g_app) {
            mtunnel->client_tlv.mconn.track = 1;
            mtunnel->assistant_tlv.mconn.track = 1;
        }

        // track performance on mtunnel mconns
        // behavior - applicable for new mtunnels only - during mtunnel transition change
        // don't want to change mconns and alive mtunnel
        int64_t track_perf_stats_level = zpn_broker_config_get_fohh_mconn_track_perf_stats_level(mtunnel->customer_id);
        if (track_perf_stats_level) {
            mtunnel->client_tlv.mconn.is_mconn_track_perf_stats_enabled = 1;
            mtunnel->assistant_tlv.mconn.is_mconn_track_perf_stats_enabled = 1;
            mtunnel->mconn_track_perf_stats_level = (int) track_perf_stats_level;
        } else {
            mtunnel->client_tlv.mconn.is_mconn_track_perf_stats_enabled = 0;
            mtunnel->assistant_tlv.mconn.is_mconn_track_perf_stats_enabled = 0;
            mtunnel->mconn_track_perf_stats_level = 0;
        }

        int64_t fohh_connection_disable_read_config_flag = zpn_broker_config_get_fohh_connection_disable_read_config_flag(mtunnel->customer_id);
        if (fohh_connection_disable_read_config_flag) {
            int64_t client_tx_buff_high;
            int64_t client_tx_buff_low;
            int64_t client_tx_buff_high_time_max_us;
            zpn_broker_config_get_fohh_connection_disable_read_config_params(mtunnel->customer_id, &client_tx_buff_low,
                                                                             &client_tx_buff_high, &client_tx_buff_high_time_max_us);

            // set both client and assistant mconn parameters since this feature is triggered on assistant mconn
            mtunnel->client_tlv.mconn.fohh_connection_disable_read_config_flag = 1;
            mtunnel->client_tlv.mconn.disable_read_client_tx_buff_high = client_tx_buff_high;
            mtunnel->client_tlv.mconn.disable_read_client_tx_buff_low = client_tx_buff_low;
            mtunnel->client_tlv.mconn.disable_read_client_tx_buff_high_allow_time_max_us = client_tx_buff_high_time_max_us;

            mtunnel->assistant_tlv.mconn.fohh_connection_disable_read_config_flag = 1;
            mtunnel->assistant_tlv.mconn.disable_read_client_tx_buff_high = client_tx_buff_high;
            mtunnel->assistant_tlv.mconn.disable_read_client_tx_buff_low = client_tx_buff_low;
            mtunnel->assistant_tlv.mconn.disable_read_client_tx_buff_high_allow_time_max_us = client_tx_buff_high_time_max_us;

        } else {
            mtunnel->client_tlv.mconn.fohh_connection_disable_read_config_flag = 0;
            mtunnel->assistant_tlv.mconn.fohh_connection_disable_read_config_flag = 0;
        }

        /* configuration for fin expire time */
        int64_t fin_expire_us = zpn_broker_config_mtunnel_fin_expire_us(mtunnel->customer_id);
        if (fin_expire_us) {
            mtunnel->client_tlv.mconn.config_fin_expire_us = fin_expire_us;
            mtunnel->assistant_tlv.mconn.config_fin_expire_us = fin_expire_us;
        } else {
            mtunnel->client_tlv.mconn.config_fin_expire_us = SECOND_TO_US(ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT);
            mtunnel->assistant_tlv.mconn.config_fin_expire_us = SECOND_TO_US(ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT);
        }

        // zia inspection check , per app and per global flag.
        // error code might be check in earlier validation
        // check if this is ZIA TERM mtunnel, do not do inspection
        if (1 == mtunnel->zia_inspection  && !mtunnel->zia_inspection_attempted && mtunnel->log.zia_inspection_status_code == NULL) {
             int64_t is_disabled = __atomic_load_n(&g_app_zia_inspection_disable, __ATOMIC_RELAXED);

            if (!ZPN_BROKER_IS_PRIVATE() && (0 == is_disabled)) {
                // open mtunnel to zia
                res = zins_create_new_inspection_locked(mtunnel);
                if (res) {
                    // system reasons
                    ZPN_ERROR_ZINS("%s: ZIA Inspection mtunnel FAIL_CLOSE error='%s' '%s'",
                                   mtunnel->mtunnel_id,
                                   zpn_result_string(res),
                                   client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");

                    __sync_fetch_and_add_8(&g_inspection_stats.total_failed_close, 1);

                    if (ZPN_RESULT_NO_ERROR != (res = mtunnel_request_ack(mtunnel,
                                                                          0,
                                                                          mtunnel->log.c_tag,
                                                                          mtunnel->mtunnel_id,
                                                                          mtunnel->log.zia_inspection_status_code
                                                                                  ?: BRK_MT_ZIA_INSPECTION_INIT_FAIL,
                                                                          msg))) {
                        ZPN_LOG(AL_WARNING, "%s: Could not queue mtunnel_request_ack back to %s '%s'",
                                mtunnel->mtunnel_id,
                                client_tlv ? zpn_tlv_description(client_tlv) : "no client connection",
                                zpn_result_string(res));
                    }
                    mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_ZIA_INSPECTION_INIT_FAIL);
                    mtunnel_locked_state_machine(mtunnel);
                    break;
                }

                // do not sent broker request here
                mtunnel->state = zbms_zia_inspection_start;
                break;
            } else {
                if (is_disabled) {
                    mtunnel->log.zia_inspection_status_code = BRK_MT_ZIA_INSPECTION_DISABLED_BY_CONFIG;
                }

                ZPN_DEBUG_ZINS("%s: zia_inspection set for app, status='%s' disabled=%"PRId64, mtunnel->mtunnel_id,
                             mtunnel->log.zia_inspection_status_code ?: "n.a.", is_disabled);


            }
            // marked for inspection, but it didnt happen for internal reason: state, memory
            // continue as normal
            if (mtunnel->log.zia_inspection_bypassed == 0) {
                mtunnel->log.zia_inspection_bypassed = 1;
                __sync_fetch_and_add_8(&g_inspection_stats.total_failed_open, 1);
            }
        }
        // regular, non-zia-inspection mtunnels here
        mtunnel_locked_send_broker_request_from_another_thread(mtunnel, 0);
        break;
    case zbms_zia_inspection_start:
         /* Chill. */
        break;
    case zbms_zia_inspection_ready: {
        // zia inspection skips zbms_authenticated state straight to broker request
        int64_t zia_ctrl_end_us = monotime_us();
        if (mtunnel->zia_ctrl_start_us) {
            mtunnel->log.zia_ctrl_path_ms = (zia_ctrl_end_us - mtunnel->zia_ctrl_start_us) / 1000;
        }
        mtunnel_locked_send_broker_request_from_another_thread(mtunnel, 0);
        break;
    }
    case zbms_zia_inspection_bypass: {
        if (mtunnel->log.zia_inspection_bypassed == 0) {
            mtunnel->log.zia_inspection_bypassed = 1;
            __atomic_add_fetch(&g_inspection_stats.total_failed_open, 1, __ATOMIC_RELAXED);
        }

        // zia inspection was attempted, but now it has to be bypassed
        // the appcon local owner needs to removed, so the mtunnel can bind to app connector
        int64_t zia_ctrl_end_us = monotime_us();
        if (mtunnel->zia_ctrl_start_us) {
            mtunnel->log.zia_ctrl_path_ms = (zia_ctrl_end_us - mtunnel->zia_ctrl_start_us) / 1000;
        }

        if (g_hard_close) {
            ZPN_DEBUG_ZINS("%s: changing FAIL_OPEN to FAIL_CLOSE by /zpn/broker/zia/control", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, mtunnel->log.zia_inspection_status_code);
            mtunnel_locked_state_machine(mtunnel);
            break;
        }
        // remove assistant owner
        res = zpn_mconn_remove_local_owner(&(mtunnel->assistant_tlv.mconn), 0, 1, 1, NULL);
        if (res) {
            ZPN_LOG(AL_WARNING, "%s: zpn_mconn_remove_local_owner failed %s", mtunnel->mtunnel_id,
                    zpn_result_string(res));
        }
        mtunnel->assistant_tlv.mconn.local_owner_incarnation = 0;
        mtunnel->assistant_ack_sent = 0;

        // continue with mtunnel as if there is no inspection
        mtunnel_locked_send_broker_request_from_another_thread(mtunnel, 0);
        break;
    }
    case zbms_dispatch_sent:
        /* Ack assistant. */
        /* Fall through if acking assistant was successful! */

        if (mtunnel->is_fqdn_to_serv_ip_policy_check_async) {
            ZPN_DEBUG_MTUNNEL("%s: Customer gid:%" PRId64 " fqdn to server ip policy re-evaluation progress...",
                                     mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id));

            res = zpn_broker_mtunnel_re_eval_policy_check_for_srv_ip(mtunnel);
            if (res == ZPN_RESULT_ACCESS_DENIED) {
                ZPN_DEBUG_MTUNNEL("%s: Customer_gid:%" PRId64 " Access denied (fqdn to server ip) rule:%" PRId64 " fqdn prev_rule:%" PRId64 " ",
                                     mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id),
                                     mtunnel->rule_gid, mtunnel->log.g_fqdn_to_server_ip_first_eval_rul);
                zpn_broker_client_mtunnel_ack_err_for_fqdn_serv_ip(mtunnel);
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY);
                mtunnel_locked_state_machine(mtunnel);
                break;
            } else if (res == ZPN_RESULT_NO_ERROR) {
                zpn_broker_mtunnel_complete_bind(mtunnel);
                mtunnel_locked_state_machine(mtunnel);
            }

        }
        break;
    case zbms_assistant_bound:
        ZPN_LOG(AL_CRITICAL, "%s: This code path has been removed", mtunnel->mtunnel_id);

    case zbms_complete:
        /* Chill. */
        break;

    case zbms_reaping:
        /* Resend end messages until they're successful. */
        if (0 == mtunnel->async_count) {

            struct zpn_broker_bucket *bucket;
            int bucket_id;

            if (!mtunnel->log.end_us) {
                mtunnel->log.end_us = epoch_us();
            }
            if (strncmp(mtunnel->log.action, "deny", strlen("deny"))) {
                mtunnel->log.action = "close";
            }

            /* Delink from client... */
            /* Delink from assistant... */

            /* Remove from bucket hash table... */
            bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
            bucket = &(broker.buckets[bucket_id]);

            if (mtunnel->mtunnel_id && argo_hash_remove_with_hash(bucket->mtunnel_by_id,
                                           mtunnel->mtunnel_id,
                                           ZPN_MTUNNEL_ID_BYTES_TEXT,
                                           mtunnel->mtunnel_id_hash,
                                           NULL)) {
                /* The mtunnel should have have been destructed, do not put it in bucket->reaped_list*/
                ZPN_LOG(AL_WARNING, "%s: Could not remove from bucket hash", mtunnel->mtunnel_id);
            } else {
                /* Remove ourselves from bucket list, add to reaped list. */
                TAILQ_REMOVE(&(bucket->mtunnel_list), mtunnel, bucket_list);
                TAILQ_INSERT_TAIL(&(bucket->reaped_list), mtunnel, bucket_list);
                if (ZPN_BROKER_IS_PRIVATE()) {
                    enum zpn_client_type client_type = mtunnel->client_type;
                    struct generic_mtunnel_stats gstats;
                    init_mtunnel_stats_ptr(&gstats, client_type);
                    ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_reaped_in, 1);
                    ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_reaped_in, 1);
                }
            }
            mtunnel->incarnation++;
            mtunnel->state = zbms_free;

            mtunnel->_client_f_conn = NULL;
            mtunnel->client_z_conn = NULL;


        }
        break;
    case zbms_free:
        /* Uh, scream. Loudly. */
        ZPN_LOG(AL_CRITICAL, "Running state machine on free mtunnel");
        break;
    default:
        /* Uh, scream. Loudly. */
        ZPN_LOG(AL_CRITICAL, "Unrecognized state machine state");
        break;
    }
}

/*
 * Locking: It is assumed bucket and mtunnel are both locked at this point
 */
int zpn_broker_mtunnel_clean(struct zpn_broker_mtunnel *mt)
{
    ZPN_DEBUG_MCONN("-- Client Side --");

    if (mt->client_tlv_type == zpn_fohh_tlv) {
        if (zpn_broker_mtunnel_client_f_conn(mt)) return 0;
        if (!zpn_mconn_fohh_tlv_clean(&mt->client_tlv)) return 0;
    } else {
        if (!zpn_mconn_zrdt_tlv_clean(&mt->client_tlv_zrdt)) return 0;
    }

    ZPN_DEBUG_MCONN("-- Assistant Side --");

    if (mt->assistant_tlv_type == zpn_fohh_tlv) {
        if (!zpn_mconn_fohh_tlv_clean(&mt->assistant_tlv)) return 0;
    } else {
        if (!zpn_mconn_zrdt_tlv_clean(&mt->assistant_tlv_zrdt)) return 0;
    }

    if (mt->zpn_probe_type == zpn_probe_type_zdx_mtr) {

        struct zpn_mconn* server_tlv_mconn = &(mt->assistant_tlv.mconn);
        struct zpn_mconn* server_zrdt_tlv_mconn = &(mt->assistant_tlv_zrdt.mconn);


        if (server_tlv_mconn->pipeline_cookie != server_zrdt_tlv_mconn->pipeline_cookie) {
            reset_zpn_zdx_cookie(server_zrdt_tlv_mconn->pipeline_cookie);
        }

        reset_zpn_zdx_cookie(server_tlv_mconn->pipeline_cookie);

        server_tlv_mconn->pipeline_cookie = NULL;
        server_zrdt_tlv_mconn->pipeline_cookie = NULL;
    }

    return 1;
}

int zpn_broker_mtunnel_idle_timeout(struct zpn_broker_mtunnel *mt, int64_t now_us)
{
    // The default value of `Idle Connection Timeout` is -1, i.e. UNDEFINED
    // In this case, we define a default value so that the idle mtunnel can be freed after enough time

    int64_t idle_timeout = mt->idle_timeout < 0 ? MTUNNEL_DEFAULT_IDLE_TIMEOUT_S : mt->idle_timeout;
    int64_t idletime_us = (idle_timeout * 1000000);
    struct zpn_mconn *client_mconn = zpn_broker_mtunnel_client_mconn(mt);
    struct zpn_mconn *assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mt);

    /* Check to see if we have idle timed out */
    if ((idle_timeout > 0) &&
        (mt->log.bindrx_us > 0) &&
        (now_us > (client_mconn->rx_data_us + idletime_us)) &&
        (now_us > (client_mconn->tx_data_us + idletime_us)) &&
        (now_us > (assistant_mconn->rx_data_us + idletime_us)) &&
        (now_us > (assistant_mconn->tx_data_us + idletime_us)) &&
        (now_us > (mt->log.bindrx_us + idletime_us))) {

        ZPN_DEBUG_MTUNNEL("%s: idle_timeout [%lld] bindrx_us [%lld] c_rx_us [%lld] c_tx_us [%lld] a_rx_us [%lld] a_tx_us [%lld]",
                          mt->mtunnel_id,
                          (long long int) idle_timeout,
                          (long long int) mt->log.bindrx_us,
                          (long long int) client_mconn->rx_data_us,
                          (long long int) client_mconn->tx_data_us,
                          (long long int) assistant_mconn->rx_data_us,
                          (long long int) assistant_mconn->tx_data_us);
        /* We haven't seen any data for a while, so time it out */
        ZPN_DEBUG_MTUNNEL("%s: Broker mtunnel idle timeout", mt->mtunnel_id);
        return 1;
    }

    return 0;
}

int zpn_broker_mtunnel_expired(struct zpn_broker_mtunnel *mt, int64_t now_s)
{
    /* Need to fetch client c_state, if possible */
    const struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mt);
    int64_t reauth_delta_remaining_s = 0;
    int64_t resiliency_reauth_s = 0;
    if (!c_state) return 0;

    /* If no saml, no reauth required */
    if (!c_state->saml_not_before) return 0;

    /* If no reauth timeout, it can't expire */
    if (!mt->reauth_timeout) return 0;

    if ( ZPN_BROKER_IS_PRIVATE() &&
            zpn_pse_control_connection_is_cloud_unreachable() &&
            zpn_is_pse_resiliency_enabled() ) {

        resiliency_reauth_s = c_state_get_resiliency_reauth_interval(c_state);
        reauth_delta_remaining_s = (c_state->saml_not_before + mt->reauth_timeout + resiliency_reauth_s ) - now_s;
        ZPN_DEBUG_MTUNNEL("%s: Mtunnel expiry extended - resiliency timeout:%" PRId64
            ",saml timeout:%" PRId64 ",rauth timeout:%" PRId64 " .", mt->tunnel_id,resiliency_reauth_s,
             c_state->saml_not_before,mt->reauth_timeout);
    } else {
        reauth_delta_remaining_s = (c_state->saml_not_before + mt->reauth_timeout) - now_s;
    }

    if (reauth_delta_remaining_s < 0) {
        /* Expired */
        return 1;
    } else {
        /* Still time remaining */
        return 0;
    }
}

static int zpn_broker_mtunnel_pse_to_broker_conn_changed(struct zpn_broker_mtunnel *mt)
{
    int broker_conn_changed = 0;

    /* Check only non-c2c promoted mtunnels */
    if (mt->promoted_to_public && !mt->log.c2c) {
        struct zpn_pb_client *pb_client = zpn_pb_client_get_pb_client_from_mtunnel(mt);
        int64_t pb_client_broker_incarnation;

        /* This was a promoted mtunnel, now it does not have a pb_client anymore, it lost its upstream broker connection instance or ZCC disconnected */
        if (!pb_client) {
            ZPN_DEBUG_MTUNNEL("%s: No pb_client is associated with promoted mtunnel, cleanup mtunnel", mt->mtunnel_id);
            broker_conn_changed = 1;
            goto done;
        } else {

            /* This should catch mtunnels that currently have the PSE -> Broker conn disconnected */
            if (!zpn_pb_connected(pb_client)) {
                ZPN_DEBUG_MTUNNEL("%s: pb_client is disconnected for promoted mtunnel, cleanup mtunnel", mt->mtunnel_id);
                broker_conn_changed = 1;
            }
        }

        /* This should catch mtunnels that have re-connected from PSE -> Broker, and clear mtunnels promoted earlier */
        pb_client_broker_incarnation = pb_client_broker_conn_incarnation(pb_client);
        if (mt->pse_to_broker_incarnation != pb_client_broker_incarnation) {
            ZPN_DEBUG_MTUNNEL("%s: Public broker connection instance changed for promoted mtunnel, mt_broker_inst: %ld, pb_client_broker_inst: %ld, cleanup mtunnel",
                              mt->mtunnel_id, (long)mt->pse_to_broker_incarnation, (long)pb_client_broker_incarnation);
            broker_conn_changed = 1;
        }
    }

done:

    return broker_conn_changed;
}

static int zpn_broker_mtunnel_stepup_auth_level_expired(struct zpn_broker_mtunnel *mt)
{
    struct stepup_auth_level_htbl_obj *auth_level = NULL;
    struct zpn_broker_client_fohh_state *c_state = NULL;
    char *level_id = NULL;
    struct zhash_table *table = NULL;
    zpath_rwlock_t *lock = NULL;

    if (!mt || !mt->current_stepup_auth_level_gid) {
        return 0;
    }

    /*
     * It's possible we may have received an update to the expiry of the AL (This happens if
     * the parent AL authentication has happened, and so the child AL expiry will be reset, and
     * will be sent via SAML assertion). This update info resides in our cache (cstate hash table),
     * and so we need to fetch it.
     *
     * Also, since this is active mtunnel (current AL = required AL), we can use
     * 'mt->required_stepup_auth_level_id' to perform lookup in the cache.
     */

    c_state = zpn_broker_mtunnel_client_c_state(mt);
    if (!c_state) {
        /* Since we could not fetch c_state, the connection might have gone away .. */
        return 0;
    }

    level_id = mt->required_stepup_auth_level_id;
    if(!level_id) {
        ZPN_DEBUG_MTUNNEL("stepup_auth: Null auth level id stored in mtunnel. tunnel:%s", mt->tunnel_id);
        return 0;
    }
    table = c_state->stepup_auth_level_htbl;
    lock = &(c_state->stepup_auth_level_htbl_lock);

    ZPATH_RWLOCK_WRLOCK(lock, __FILE__, __LINE__);
    auth_level = zhash_table_lookup(table, level_id, strnlen(level_id, ZPN_STEPUP_AUTH_LEVEL_LEVELID_MAX_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(lock, __FILE__, __LINE__);

    if (auth_level) {
        /* Update expiry timestamp of current AL */
        mt->current_stepup_auth_level_expiry_s = auth_level->expiry_s;
    }

    if (epoch_s() > mt->current_stepup_auth_level_expiry_s) {
        return 1;
    }

    return 0;
}

static int zpn_broker_mtunnel_jit_expired(struct zpn_broker_mtunnel *mt, int64_t now_s)
{
    /* If JIT just-in-time approval does not exist for this mtunnel, it cannot expire */
    if (!mt->jit_timeout) return 0;

    /* Current UTC time is greater than the absolute UTC end timestamp jit_timeout.
     * This indicates session has expired due to time set by JIT approval */
    if (now_s > mt->jit_timeout) return 1;

    return 0;
}

static void mtunnel_reevaluate(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_broker_mtunnel *mtunnel              = void_cookie;
    struct zpn_broker_client_fohh_state *c_state    = NULL;
    int res;
    struct zpn_broker_bucket *bucket;
    int bucket_id;

    // Skip policy reevaluate if pbroker runs in DR mode
    if (ZPN_BROKER_IS_PRIVATE() && zpn_is_dr_mode_active()) {
        return;
    }

    bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
    bucket = &(broker.buckets[bucket_id]);

    pthread_mutex_lock(&(bucket->lock));
    mtunnel_lock(mtunnel);
    if (mtunnel->incarnation != int_cookie) {
        mtunnel_unlock(mtunnel);
        pthread_mutex_unlock(&(bucket->lock));
        return;
    }

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);

    /* refresh auth object in mtunnel, if the auth object in c_state has changed due to re-auth */
    if (c_state && mtunnel->auth_request_x != c_state->auth_request_x) {
        // release the old auth object if any
        if (mtunnel->auth_request_x) {
            argo_object_release(mtunnel->auth_request_x);
            mtunnel->auth_request_x = NULL;
        }

        // save the new auth object and increse its ref count
        if (c_state->auth_request_x) {
            mtunnel->auth_request_x = c_state->auth_request_x;
            argo_object_hold(c_state->auth_request_x);
        }
    }

    /* Kill if user deactivated or not synced via SCIM */
    if (c_state && zpn_client_static_config[c_state->client_type].scim_compatible &&
            !zpn_broker_client_scim_user_active(c_state)) {
        ZPN_LOG(AL_WARNING, "%s: mtunnel rejected. User was deactivated or not synced via scim",
                mtunnel->mtunnel_id);
        if (zpn_broker_client_scim_done(c_state) != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_INFO, "%s: mtunnel_reevaluate:scim user state unknown, auth_complete:%d",
                    mtunnel->mtunnel_id, c_state->auth_complete);
        }
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_SCIM_INACTIVE);
        mtunnel_locked_state_machine(mtunnel);
    }
    else {
        res = mtunnel_locked_policy_check(mtunnel, NULL);
        if (res == ZPN_RESULT_ERR) {
            /* Not allowed- policy denied- Kill it */
            if (mtunnel->approval_id) {
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL);
            } else {
                mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY);
            }
            mtunnel_locked_state_machine(mtunnel);
        } else if (res == ZPN_RESULT_NOT_FOUND) {
            /* App gone, kill it */
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_NO_POLICY_FOUND);
            mtunnel_locked_state_machine(mtunnel);
        } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
            /* Let it get re-triggered for re-evaluation */
            mtunnel->reevaluation_in_progress = 0;
        } else if (res == ZPN_RESULT_EXPIRED) {
            /* Expired, kill it. */
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_EXPIRED);
            mtunnel_locked_state_machine(mtunnel);
        } else if (res == ZPN_RESULT_AL_AUTH_REQUIRED) {
            ZPN_DEBUG_MTUNNEL("stepup_auth: mtunnel re-evaluate done. customer:%"PRId64" scope:%"PRId64" tunnel:%s "
                              "required_auth_level:{ gid:%"PRId64" name:%s level_id:%s } result: ZPN_RESULT_AL_AUTH_REQUIRED",
                               mtunnel->customer_id, mtunnel->scope_gid, mtunnel->tunnel_id,
                               mtunnel->required_stepup_auth_level_gid,
                               mtunnel->required_stepup_auth_level_name,
                               mtunnel->required_stepup_auth_level_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_WITH_AL);
            mtunnel_locked_state_machine(mtunnel);
        } else if (res == ZPN_RESULT_AL_AUTH_EXPIRED) {
            ZPN_DEBUG_MTUNNEL("stepup_auth: mtunnel re-evaluate done. customer:%"PRId64" scope:%"PRId64" tunnel:%s "
                              "required_auth_level:{ gid:%"PRId64" name:%s level_id:%s expiry:%"PRId64"} result: ZPN_RESULT_AL_AUTH_EXPIRED",
                               mtunnel->customer_id, mtunnel->scope_gid, mtunnel->tunnel_id,
                               mtunnel->required_stepup_auth_level_gid,
                               mtunnel->required_stepup_auth_level_name,
                               mtunnel->required_stepup_auth_level_id,
                               mtunnel->required_stepup_auth_level_expiry_s);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL);
            mtunnel_locked_state_machine(mtunnel);
        } else if (res) {
            /* All other errors, kill it. */
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_NO_POLICY_FOUND);
            mtunnel_locked_state_machine(mtunnel);
        } else {
            /* success. Let re-evaluation occur */
            mtunnel->reevaluation_in_progress = 0;
        }
    }
    mtunnel_unlock(mtunnel);
    pthread_mutex_unlock(&(bucket->lock));
}

/* Mtunnel re-evaluate happens
 * either if the user risk changed
 * or session termination on re-auth is enabled for
 * a scope id
 */
static int zpn_broker_mtunnel_test_reevaluate(struct zpn_broker_mtunnel *mt)
{
    int res;
    int reevaluate_mtunnel = 0;

    if (mt->reevaluation_in_progress) return 0;

    struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mt);
    if (!c_state) return 0;

    int64_t customer_gid = c_state->customer_gid;
    if (!customer_gid) return 0;

    /* Check if user risk changed */
    int64_t user_risk_version = zpn_broker_client_get_cstate_user_risk_version(c_state);
    int64_t scope_gid = c_state->scope_gid;
    if (!scope_gid) return 0;

    int64_t current_posture_version = c_state->posture_profile_version;
    int64_t current_sv_posture_version = c_state->sv_posture_profile_version;
    int64_t current_aae_conclusion_version = zpn_broker_client_scim_get_aae_conclusion_version(c_state);
    int64_t current_scim_version = zpn_broker_client_get_scim_version(c_state);
    struct zpe_policy_built *access_policy_built = NULL;
    struct zpe_policy_built *reauth_policy_built = NULL;
    struct zpe_policy_built *access_policy_built_default = NULL;
    struct zpe_policy_built *reauth_policy_built_default = NULL;
    int64_t customer_application_version = 0;
    int64_t scope_version = get_scope_version(customer_gid);

    if ((res = zpe_get_scope_policy_without_rebuild(scope_gid,
                                               zpe_policy_type_access,
                                               &access_policy_built_default,
                                               &access_policy_built,
                                               NULL))) {
        /* Could not get policy, which should basically not happen. */
        ZPN_LOG(AL_ERROR, "%s: customer %ld: Could not get access policy: %s", mt->mtunnel_id, (long) customer_gid, zpath_result_string(res));
        return 0;
    }
    if ((res = zpe_get_scope_policy_without_rebuild(scope_gid,
                                                   zpe_policy_type_reauth,
                                                   &reauth_policy_built_default,
                                                   &reauth_policy_built,
                                                   NULL))) {
        /* Could not get policy, which should basically not happen. */
        ZPN_LOG(AL_ERROR, "%s: customer %ld: Could not get reauth policy: %s", mt->mtunnel_id, (long) customer_gid, zpath_result_string(res));
        return 0;
    }
    customer_application_version = zpn_application_version_get(zpn_application_customer_state_get(customer_gid));

    if (mt->user_risk_version != user_risk_version) {
        reevaluate_mtunnel = 1;
        ZPN_DEBUG_USER_RISK("User risk feature mtunnel id %s: mtunnel dispatching to reevaluate policy, user risk version changed. Old : %"PRId64", New : %"PRId64"",
                            mt->mtunnel_id,
                            mt->user_risk_version,
                            user_risk_version);
        mt->user_risk_version = user_risk_version;
        zpn_broker_mtunnel_stats_inc_user_risk_version_change_policy_reevaluate();
    } else if (mt->application_version != customer_application_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, application version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->application_version,
                           customer_application_version);
        zpn_broker_mtunnel_stats_inc_app_version_change_policy_reevaluate();
    } else if(mt->access_rule_version_default != access_policy_built_default->policy_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, default access policy version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->access_rule_version,
                           access_policy_built_default->policy_version);
        zpn_broker_mtunnel_stats_inc_default_access_rule_change_policy_reevaluate();
    } else if(mt->reauth_rule_version_default != reauth_policy_built_default->policy_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, default re-auth policy version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->reauth_rule_version,
                           reauth_policy_built_default->policy_version);
        zpn_broker_mtunnel_stats_inc_default_reauth_rule_change_policy_reevaluate();
    } else if(mt->access_rule_version != access_policy_built->policy_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, scope access policy version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->access_rule_version,
                           access_policy_built->policy_version);
        zpn_broker_mtunnel_stats_inc_scope_access_rule_change_policy_reevaluate();
    } else if(mt->reauth_rule_version != reauth_policy_built->policy_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, scope re-auth policy version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->reauth_rule_version,
                           reauth_policy_built->policy_version);
        zpn_broker_mtunnel_stats_inc_scope_reauth_rule_change_policy_reevaluate();
    } else if(customer_gid != scope_gid && mt->scope_version != scope_version) {
        reevaluate_mtunnel = 1;
        ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, scope version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->scope_version,
                           scope_version);
        mt->scope_version = scope_version;
        zpn_broker_mtunnel_stats_inc_scope_version_change_policy_reevaluate();
    } else if (mt->aae_conclusion_version != current_aae_conclusion_version) {
        reevaluate_mtunnel = 1;
        ZPN_DEBUG_AAE("AAE profile feature mtunnel id %s: mtunnel dispatching to reevaluate policy, AAE conclusion version changed. Old : %"PRId64", New : %"PRId64"",
                      mt->mtunnel_id, mt->aae_conclusion_version, current_aae_conclusion_version);
        mt->aae_conclusion_version = current_aae_conclusion_version;
        zpn_broker_mtunnel_stats_inc_aae_conclusion_version_change_policy_reevaluate();
    } else if (zpn_broker_is_policy_re_eval_on_scim_update_enabled(customer_gid) && mt->scim_version != current_scim_version) {
        reevaluate_mtunnel = 1;
        ZPATH_DEBUG_SCIM("mtunnel id %s: mtunnel dispatching to reevaluate policy, SCIM version changed. Old : %"PRId64", New : %"PRId64"",
                            mt->mtunnel_id,
                            mt->scim_version,
                            current_scim_version);
        mt->scim_version = current_scim_version;
        zpn_broker_mtunnel_stats_inc_scim_version_change_policy_reevaluate();
    }
    /* ATTENTION: Below Condition should be the last.
     * Below block of code will always be TRUE if the config is enabled so KEEP this as a LAST else if and ADD NEW conditions before this.
     */
    else if(zpn_broker_policy_re_eval_on_posture_change_is_enabled(customer_gid)) {
        if(mt->posture_profile_version != current_posture_version){
            reevaluate_mtunnel = 1;
            ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, client posture profile version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->posture_profile_version,
                           current_posture_version);
            mt->posture_profile_version = current_posture_version;
            zpn_broker_mtunnel_stats_inc_posture_version_change_policy_reevaluate();
        } else if (mt->sv_posture_profile_version != current_sv_posture_version){
            reevaluate_mtunnel = 1;
            ZPN_LOG(AL_DEBUG, "%s: mtunnel dispatching to reevaluate policy, server validated posture profile version changed. Old : %"PRId64", New : %"PRId64"",
                           mt->mtunnel_id,
                           mt->sv_posture_profile_version,
                           current_sv_posture_version);
            mt->sv_posture_profile_version = current_sv_posture_version;
            zpn_broker_mtunnel_stats_inc_sv_posture_version_change_policy_reevaluate();
        }
    }

    if (reevaluate_mtunnel) {
        /* Ask client thread to reevaluate policy */
        mt->reevaluation_in_progress = 1;
        zpn_broker_mtunnel_stats_inc_total_policy_reevaluate();
        zevent_base_call(c_state->client_thread, mtunnel_reevaluate, mt, mt->incarnation);
        return 1;
    }
    return 0;
}

int zpn_broker_mtunnel_done(struct zpn_broker_mtunnel *mt)
{
    struct zpn_mconn *client_mconn = zpn_broker_mtunnel_client_mconn(mt);
    struct zpn_mconn *assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mt);

    if (zpn_mconn_done(client_mconn) && zpn_mconn_done(assistant_mconn)) {
        zpn_broker_mtunnnel_stats_inc_mconn_done_both();
        return 1;
    } else if (zpn_mconn_fin_expired(client_mconn) || zpn_mconn_fin_expired(assistant_mconn)) {
        ZPN_DEBUG_MTUNNEL("%s: Broker mtunnel expired after receiving FIN", mt->mtunnel_id);
        zpn_broker_mtunnnel_stats_inc_mconn_done_client();
        return 1;
    } else if (zpn_mconn_fin_expired(&mt->assistant_tlv.mconn)) {
        ZPN_DEBUG_MTUNNEL("%s: Broker mtunnel expired after receiving FIN from connector", mt->mtunnel_id);
        zpn_broker_mtunnnel_stats_inc_mconn_done_assistant();
        return 1;
    } else if ((mt->ip_protocol == IPPROTO_ICMP || mt->ip_protocol == IPPROTO_ICMPV6) && (assistant_mconn->fin_rcvd  || client_mconn->fin_rcvd)) {
        if (assistant_mconn->fin_rcvd) {
            ZPN_DEBUG_MTUNNEL("%s: Broker ICMP mtunnel done after receiving FIN from connector", mt->mtunnel_id);
            zpn_broker_mtunnnel_stats_inc_mconn_done_assistant();
        } else {
            ZPN_DEBUG_MTUNNEL("%s: Broker ICMP mtunnel done after receiving FIN from client", mt->mtunnel_id);
            zpn_broker_mtunnnel_stats_inc_mconn_done_client();
        }
        return 1;
    }

    return 0;
}

// not static, for unit test
void zpn_broker_mtunnel_internal_display_zdf(struct zpath_debug_state *request_state,
                                             int is_assist,
                                             struct zpn_mconn_fohh_tlv *mconn_fohh_tlv) {
    ZDP("    %s: %s=%-12p %s=%-12p flow_control=%d cbs=%d  bytes_to_client=%" PRId64 " bytes_to_peer=%" PRId64
       " bytes_to_peer_attempt=%"PRId64"\n",
        is_assist ? "A" : "C",
        is_assist ? "peer " : "mconn",
        is_assist ? mconn_fohh_tlv->mconn.peer : &mconn_fohh_tlv->mconn,
        is_assist ? "mconn" : "peer ",
        is_assist ? &mconn_fohh_tlv->mconn : mconn_fohh_tlv->mconn.peer,
        mconn_fohh_tlv->remote_fc_status,
        mconn_fohh_tlv->callbacks,
        mconn_fohh_tlv->mconn.bytes_to_client,
        mconn_fohh_tlv->mconn.bytes_to_peer,
        mconn_fohh_tlv->mconn.bytes_to_peer_attempt);
}

void zpn_broker_mtunnel_internal_display(struct zpn_broker_mtunnel *mt)
{
    if (zpn_debug_get(ZPN_DEBUG_MCONN_IDX)) {
        ZPN_DEBUG_MCONN("mtunnel_id = %s", mt->mtunnel_id);

        ZPN_DEBUG_MCONN("client_mconn -----");
        if (mt->client_tlv_type == zpn_fohh_tlv) {
            zpn_mconn_fohh_tlv_internal_display(&(mt->client_tlv));
        } else {
            zpn_mconn_zrdt_tlv_internal_display(&(mt->client_tlv_zrdt));
        }

        ZPN_DEBUG_MCONN("assistant_mconn -----");
        if (mt->assistant_tlv_type == zpn_fohh_tlv) {
            zpn_mconn_fohh_tlv_internal_display(&(mt->assistant_tlv));
        } else {
            zpn_mconn_zrdt_tlv_internal_display(&(mt->assistant_tlv_zrdt));
        }
    }
}

void zpn_broker_mtunnel_show_track_time(struct zpn_broker_mtunnel *mt)
{
    if (mt->client_tlv.mconn.track && mt->assistant_tlv.mconn.track) {
        ZPN_LOG(AL_DEBUG, "%s: clt-tx-rx = %d, clt rx-tx = %d, ast-tx-rx = %d, ast-rx-tx = %d",
                          mt->mtunnel_id,
                          mt->client_tlv.mconn.tx_rx_total/ZPN_MCONN_TRACK_ARRAY_SIZE,
                          mt->client_tlv.mconn.rx_tx_total/ZPN_MCONN_TRACK_ARRAY_SIZE,
                          mt->assistant_tlv.mconn.tx_rx_total/ZPN_MCONN_TRACK_ARRAY_SIZE,
                          mt->assistant_tlv.mconn.rx_tx_total/ZPN_MCONN_TRACK_ARRAY_SIZE);
    }
}

static void zpn_broker_mtunnel_touch_stream(struct zpn_broker_mtunnel *mt)
{
    if (mt->client_tlv_type == zpn_zrdt_tlv) {
        zpn_mconn_zrdt_tlv_stream_touch(&(mt->client_tlv_zrdt));
    }

    if (mt->assistant_tlv_type == zpn_zrdt_tlv) {
        zpn_mconn_zrdt_tlv_stream_touch(&(mt->assistant_tlv_zrdt));
    }
}
/*
* in c2c transit scenario , the mtunnel log is not fully populated until transit_request_ack is recieved
* in ZIA Inspection,
* in case of timeouts or other errors, the mtunnel log should be updated
*/
void check_and_fix_mtunnel_log_locked(struct zpn_broker_mtunnel *mt) {
    if (!mt->log.c2c && !mt->zia_term) {
        return;
    }
    if (0 == mt->log.orig_start_rx_time_us) {
        mt->log.orig_start_rx_time_us = mt->log.startrx_us;
    }

    if (NULL == mt->log.brks) {
        /* Brokers */
        mt->log.brks = ZPN_CALLOC(sizeof(*(mt->log.brks)));
        if (mt->log.brks) {
            mt->log.brks[0] = ZPN_BROKER_GET_GID();
            mt->log.brks_count = 1;
        }
    }

    if (NULL == mt->log.start_rx_times_us) {
        mt->log.start_rx_times_us = ZPN_CALLOC(sizeof(*(mt->log.start_rx_times_us)));
        if (mt->log.start_rx_times_us) {
            mt->log.start_rx_times_us[0] = mt->log.startrx_us;
            mt->log.start_rx_times_us_count = 1;
        }
    }

    if (NULL == mt->log.end_times_us) {
        mt->log.end_times_us = ZPN_CALLOC(sizeof(*(mt->log.end_times_us)));
        if (mt->log.end_times_us) {
            mt->log.end_times_us[0] = epoch_us();
            mt->log.end_times_us_count = 1;
        }
    } else if (mt->log.end_times_us_count == 2) {
        mt->log.end_times_us[0] = mt->log.end_times_us[1] = epoch_us();
    }

    if (NULL == mt->log.self_hop_type) {
        const char *hop_name = get_mtunnel_hop_name(zpn_trans_hop_type_term);
        mt->log.self_hop_type = ZPN_STRDUP(hop_name, strlen(hop_name));
    }
}

/*
 * Returns 1 if timeout re-dispatch to different DC is needed, else 0.
 * Mtunnel is assumed to be locked at this point.
 */
int zpn_broker_mtunnel_is_timeout_redispatch_needed(struct zpn_broker_mtunnel *mtunnel,
                                                    int64_t *brk_req_timeout_us,
                                                    int64_t curr_us)
{
    if (ZPN_BROKER_IS_PUBLIC() && // Timeout re-dispatch is only for public broker
        mtunnel->zpn_broker_mtunnel_type != zbmt_transit_c2c && // Don't re-dispatch transit mtunnel
        mtunnel->state == zbms_dispatch_sent && // Mtunnel is in zbms_dispatch_sent state
        mtunnel->flag.response_received_from_disp == 0 && // No response received from dispatcher so far
        curr_us - mtunnel->log.disptx_us > (*brk_req_timeout_us = zpn_broker_dispatch_get_brk_req_timeout_with_jitter_us(mtunnel->customer_id)) && // Timeout interval is exceeded (by default 2 sec + added jitter)
        (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_IN_DIFF_DC) == 0 && // This mtunnel was not re-dispatched to diff DC previously because of split brain retry
        (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) == 0 && //This mtunnel was not re-dispatched to diff DC previously because of timeout
        (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_AST) == 0 && // This mtunnel didn't send brk req directly to AC because of positive path cache hit
        zpn_broker_dispatch_is_timeout_redispatch_to_diff_dc_enabled(mtunnel->customer_id)) { // Timeout re-dispatch feature is enabled
        return 1;
    }

    return 0;
}

void zpn_broker_check_mtunnel()
{
    static int i = 0;  /* Tracks the broker bucket currently being evaluated */
    int j;

    int64_t curr_us = epoch_us();
    struct zpn_tlv *client_tlv;
    int64_t reevaluations_launched = 0;
    int64_t mtunnels_visited = 0;

    int reaped_count = 0;
    int reaped_null_count = 0;
    int64_t reaped_cur_us = 0;

    int pbroker_mtunnel_stats_collected = 0;
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;

    for (j = 0; (j < ZPN_BROKER_BUCKETS) && (j < MTUNNEL_HOUSEKEEPING_MAX_BUCKETS_PER_INTERVAL); j++, i = (i + 1) % ZPN_BROKER_BUCKETS) {
        struct zpn_broker_bucket *bucket = &(broker.buckets[i]);
        struct zpn_broker_mtunnel *mt;
        struct zpn_broker_mtunnel *tmp_mt;
        int64_t now_us = epoch_us();
        int64_t now_s = now_us / 1000000;

        if (j && mtunnels_visited > MTUNNEL_HOUSEKEEPING_CHECKS_PER_INTERVAL) {
            /* If we have processed at least one bucket, and if we
             * have exceeded the desired housekeeping checks per
             * interval, then stop */
            break;
        }

        pthread_mutex_lock(&(bucket->lock));

        /*
         * We do this only once per invocation of zpn_broker_check_tunnel() and that too under the protection of a lock.
         * However, this may still not guarantee that the peak value being derived is accurate.
         *
         * Note: The peak value of the active mtunnel count below is not accurate and is an approximation, but it would
         *       give us a good indicator over time of how the peak is moving. This is a side-effect if actively avoiding
         *       the use of any additionl locks and avoiding the performance impact
         */
        if(ZPN_BROKER_IS_PRIVATE() && !pbroker_mtunnel_stats_collected) {
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            struct zpn_private_broker_mtunnel_stats *mtstats = &gs->private_broker_state->mtunnel_stats;

            if(mtstats->total_mtunnels_active > mtstats->approx_mtunnels_peak_active) {
                ZPN_ATOMIC_STORE(&mtstats->approx_mtunnels_peak_active, mtstats->total_mtunnels_active);
            }

            pbroker_mtunnel_stats_collected = 1;
        }

        for (mt = TAILQ_FIRST(&(bucket->mtunnel_list)); mt != NULL; mt = tmp_mt)
        {
            mtunnels_visited++;

            tmp_mt = TAILQ_NEXT(mt, bucket_list);

            mtunnel_lock(mt);

            struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mt);
            if (!c_state) {
                ZPN_DEBUG_MTUNNEL("%s: mtunnel failed to get c_state", mt->mtunnel_id);
            }

            zpn_broker_mtunnel_internal_display(mt);

            zpn_broker_mtunnel_show_track_time(mt);

            zpn_broker_mtunnel_touch_stream(mt);

	    /* 1. Just-In-Time approval based mtunnels need session termination
	     * and require reauth based global knob to be set.
         * 2. If in probe_sent state, do not reevaluate as policy evaluation
         * is still pending.*/
            if (mt->session_termination_on_reauth) {
#if 0 //FQDN_TO_SERVER_IP_HEALTH_APPS (ET-64854)
                    && (mt->state != zbms_probe_sent)
#endif
                /* Check if configuration has changed for this mtunnel. If
                 * so, we need to request reprocessing policy. */
                if (zpn_broker_mtunnel_test_reevaluate(mt)) {
                    /* It was scheduled for re-evaluation */
                    reevaluations_launched++;
                    if (reevaluations_launched > MTUNNEL_HOUSEKEEPING_RE_EVALUATE_PER_INTERVAL) {
                        // Lame way to break loop
                        j = ZPN_BROKER_BUCKETS;
                        mtunnel_unlock(mt);
                        break;
                    }
                }
            }

            /* check scope every 100msec at least */
            if (curr_us - mt->last_scope_check_us > 100000l) {
                mt->last_scope_check_us = curr_us;

                /*
                 * if c_state->app_state is not set yet, just skip checking scope. If it is set, the scope_gid must be set too
                 * We track scope_gid back from c_state->app_state, because it is more upto current: if there is
                 * scope change for a client, it is notified and updated in c_state->app_state, but not in c_state
                 */
                if (c_state && c_state->app_state) {
                    int64_t new_scope_gid = zpn_broker_client_app_get_scope_gid(c_state->app_state);

                    if(new_scope_gid != c_state->scope_gid) {
                        /*
                         * Scope gid has changed. First update c_state with the latest scope_gid.
                         * Then re-create and send the search domain list to the client
                         */
                        c_state->scope_gid = new_scope_gid;

                        int res = zpn_broker_client_send_shared_domain_complete(c_state, NULL);
                        if (res) {
                            ZPN_LOG(AL_ERROR, "tunnel:%s customer:%"PRId64" scope:%"PRId64" client-type:%s zpn_broker_client_send_shared_domain_complete() failed. Error:%s",
                                               c_state->tunnel_id, c_state->customer_gid, c_state->scope_gid, c_state->client_type_str, zpn_result_string(res));
                        }
                    }

                    if (!is_valid_scope(c_state->scope_gid) || (c_state->scope_gid != mt->scope_gid)) {
                        ZPN_DEBUG_MTUNNEL("%s: Mtunnel scope invalid, should free it for scope gid %ld, mtunnel scope gid %ld",
                                          mt->mtunnel_id, (long)c_state->scope_gid, (long) mt->scope_gid);
                        check_and_fix_mtunnel_log_locked(mt);
                        mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED);
                        mtunnel_locked_state_machine(mt);
                        mtunnel_unlock(mt);
                        break;
                    }
                }
            }

            int64_t brk_req_timeout_us;
            if (zpn_broker_mtunnel_is_timeout_redispatch_needed(mt, &brk_req_timeout_us, curr_us)) {
                /*
                 * Re-dispatch zpn_broker_request to different DC dispatcher if
                 * doesn't receive response from first dispatcher within 2 sec.
                 */
                mt->last_disp_error = zpn_err_brk_req_timeout_redispatch_to_diff_dc;
                mt->brk_req_redispatch_to_diff_dc_reason = zpn_err_brk_req_timeout_redispatch_to_diff_dc;
                mt->last_dispatcher_id = mt->dispatcher_id;
                mt->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY;
                brk_dsp_stats = zpn_broker_dispatcher_stats_data_obj_get();
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout), 1);
                brk_dsp_stats = zpn_broker_dispatcher_get_stats(mt->last_dispatcher_id);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout), 1);
                }
                ZPN_LOG(AL_NOTICE, "%s: Did not receive response from %"PRId64" dispatcher within %"PRId64" us, re-dispatching brk req to diff DC",
                        mt->mtunnel_id, mt->dispatcher_id, brk_req_timeout_us);
                mtunnel_locked_send_broker_request_from_another_thread(mt, 0);
            }

            if (ZPN_BROKER_IS_PUBLIC() &&
                mt->flag.timeout_redispatch_to_diff_dc &&
                mt->flag.response_received_from_disp == 0 &&
                mt->flag.second_disp_timeout_stats_updated == 0 &&
                curr_us - mt->log.redisptx_us > zpn_broker_dispatch_get_brk_req_timeout_us(mt->customer_id)) {
                /*
                 * Primary dispatcher didn't respond within 2 sec, so we re-dispatched to another dispatcher in diff DC,
                 * second dispatcher also didn't respond within 2 sec.
                 */
                brk_dsp_stats = zpn_broker_dispatcher_stats_data_obj_get();
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_second_disp_timeout), 1);
                brk_dsp_stats = zpn_broker_dispatcher_get_stats(mt->redispatch_to_diff_dc_disp_id);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_second_disp_timeout), 1);
                }
                mt->flag.second_disp_timeout_stats_updated = 1;
            }

            if (mt->state < zbms_complete) {
                if ((curr_us - mt->log.startrx_us) > ZPN_MCONN_TIMEOUT_BRK) {
                    ZPN_DEBUG_MTUNNEL("%s: Broker mtunnel expired", mt->mtunnel_id);

                    if (ZPN_BROKER_IS_PUBLIC() &&
                        mt->flag.timeout_redispatch_to_diff_dc &&
                        mt->flag.response_received_from_disp == 0) {
                        /* We had re-dispatched to diff DC dispatcher because of timeout but none of them responded. */
                        brk_dsp_stats = zpn_broker_dispatcher_stats_data_obj_get();
                        __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_no_response), 1);
                        /* Increment no response counter for primary dispatcher. */
                        brk_dsp_stats = zpn_broker_dispatcher_get_stats(mt->last_dispatcher_id);
                        if (brk_dsp_stats) {
                            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_no_response), 1);
                        }
                        /* Increment no response counter for secondary dispatcher. */
                        brk_dsp_stats = zpn_broker_dispatcher_get_stats(mt->redispatch_to_diff_dc_disp_id);
                        if (brk_dsp_stats) {
                            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_no_response), 1);
                        }
                    }

                    client_tlv = zpn_broker_mtunnel_client_tlv(mt);
                    if (mtunnel_request_ack(mt,
                                            0,
                                            mt->log.c_tag,
                                            mt->mtunnel_id,
                                            "Broker mtunnel setup timeout",
                                            NULL)) {
                        ZPN_LOG(AL_WARNING, "%s: overrun? Could not queue error message back to %s",
                                mt->mtunnel_id, client_tlv ? zpn_tlv_description(client_tlv) : "no client connection");
                    }

                    check_and_fix_mtunnel_log_locked(mt);
                    mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_SETUP_TIMEOUT);
                    mtunnel_locked_state_machine(mt);

                    ZPN_DEBUG_MTUNNEL("%s: Mtunnel END. MTUNNEL GONE", mt->mtunnel_id);
                }
            } else if (zpn_broker_mtunnel_idle_timeout(mt, now_us)) {
                ZPN_DEBUG_MTUNNEL("%s: Mtunnel idle timed out, should free it", mt->mtunnel_id);
                check_and_fix_mtunnel_log_locked(mt);
                mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED_IDLE_TIMEOUT);
                mtunnel_locked_state_machine(mt);

            } else if (zpn_broker_mtunnel_done(mt)) {
                ZPN_DEBUG_MTUNNEL("%s: Mtunnel terminated on both ends, should free it", mt->mtunnel_id);
                if(mt->err) {
                    mtunnel_locked_destroy(mt, 1, 0, 0, mt->err);
                } else {
                    mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED);
                }
                mtunnel_locked_state_machine(mt);

            } else if (zpn_broker_mtunnel_expired(mt, now_s)) {
                if(mt->session_termination_on_reauth) {
                    ZPN_DEBUG_MTUNNEL("%s: Mtunnel terminated- reauthentication required", mt->tunnel_id);
                    check_and_fix_mtunnel_log_locked(mt);
                    mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_EXPIRED);
                    mtunnel_locked_state_machine(mt);
                    //active_conn--;
                }
            } else if (zpn_broker_mtunnel_jit_expired(mt, now_s)) {
                ZPN_DEBUG_MTUNNEL("%s: Mtunnel terminated- Just-In-Time JIT approval expired", mt->tunnel_id);
                check_and_fix_mtunnel_log_locked(mt);
                mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED_APPROVAL_TIMEOUT);
                mtunnel_locked_state_machine(mt);
            } else if (zpn_broker_mtunnel_pse_to_broker_conn_changed(mt)) {
                __sync_fetch_and_add_8(&(mstats.mt_promote_terminated_count), 1);
                ZPN_DEBUG_MTUNNEL("%s: Mtunnel terminated- Public broker for PSE changed", mt->tunnel_id);
                check_and_fix_mtunnel_log_locked(mt);
                mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED_PSE_BROKER_CHANGED);
                mtunnel_locked_state_machine(mt);
            } else if (mt->current_stepup_auth_level_gid) {
                 if (mt->session_termination_on_reauth && !zpn_broker_is_step_up_auth_enabled(c_state)) {
                    ZPN_DEBUG_MTUNNEL("stepup_auth: mtunnel terminated because stepup auth has been disabled or PSE is in DR mode. "
                                      "customer:%"PRId64" scope:%"PRId64" tunnel:%s}",
                                      mt->customer_id, mt->scope_gid, mt->tunnel_id);
                    check_and_fix_mtunnel_log_locked(mt);
                    mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_TERMINATED);
                    mtunnel_locked_state_machine(mt);
                } else if (zpn_broker_mtunnel_stepup_auth_level_expired(mt)) {
                    __sync_add_and_fetch(&zpn_stepup_chk_stats.num_stepup_mtunnel_al_expired, 1);
                    ZPN_DEBUG_MTUNNEL("stepup_auth: mtunnel terminated due to auth level expiry. customer:%"PRId64" scope:%"PRId64" "
                                      "tunnel:%s auth_level:{ id:'%s' gid:%"PRId64" name:'%s' expiry:%"PRId64" }",
                                      mt->customer_id, mt->scope_gid, mt->tunnel_id,
                                      mt->required_stepup_auth_level_id,
                                      mt->current_stepup_auth_level_gid,
                                      mt->current_stepup_auth_level_name,
                                      mt->current_stepup_auth_level_expiry_s);
                    check_and_fix_mtunnel_log_locked(mt);
                    mtunnel_locked_destroy(mt, 1, 0, 0, BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL);
                    mtunnel_locked_state_machine(mt);
                }
            }

            if (mt->zia_inspection == 1) {
                zins_mtunnel_monitor_locked(mt);
            }

            /* It's been a while since last time we logged the mtunnel, log it again */
            if (now_s - mt->log_s >= TRANSACTION_LOG_PERIOD) {
                zpn_broker_mtunnel_log(mt);
            }
            mtunnel_unlock(mt);
        }

        reaped_count = 0;
        reaped_null_count = 0;
        if (bucket->reaped_start_us == 0) bucket->reaped_start_us = epoch_us();
        for (mt = TAILQ_FIRST(&(bucket->reaped_list)); mt != NULL; mt = tmp_mt)
        {
            int free_mt = 0;

            mtunnel_lock(mt);

#if 0
            int count = 0;
            if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
                struct zpn_broker_mtunnel *tmp_2 = mt;
                while (tmp_2) {
                    tmp_2 = TAILQ_NEXT(tmp_2, bucket_list);
                    count++;
                }

                if (count > 2) {
                    ZPN_LOG(AL_DEBUG, "bucket index=%4d reaper_list_cnt=%d tag=%d", i, count, mt->log.c_tag);
                }
            }
#endif
            reaped_count++;
            if (!mt->mtunnel_id) reaped_null_count++;

            zpn_broker_mtunnel_touch_stream(mt);

            tmp_mt = TAILQ_NEXT(mt, bucket_list);

            if (zpn_broker_mtunnel_clean(mt)) {

                int fohh_thread_id;

                TAILQ_REMOVE(&(bucket->reaped_list), mt, bucket_list);
                /* Move this log to right after we put mt on reap list and before NULL the fohh_conn in mt */
                //zpn_broker_mtunnel_log(mt);
                if (ZPN_BROKER_IS_PRIVATE()) {
                    enum zpn_client_type client_type = mt->client_type;
                    struct generic_mtunnel_stats gstats;
                    init_mtunnel_stats_ptr(&gstats, client_type);
                    ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_reaped_out, 1);
                    ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_reaped_out, 1);
                }
                free_mt = 1;

                if (mt->timer) {
                    event_free_finalize(0, mt->timer, zpn_free_disp_learn_mode_timer);
                    mt->timer = NULL;
                }

                if (zpn_mconn_get_fohh_thread_id(&(mt->client_tlv.mconn), &fohh_thread_id) != ZPN_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_NOTICE, "No fohh thread id for mconn");
                    fohh_thread_id = 0;
                }

                zpn_fohh_worker_mtunnel_destroy(fohh_thread_id,
                                                zpn_client_type_from_str(mt->log.client_type),
                                                mt->log.c_service_port,
                                                mt->log.c_rxbytes,
                                                mt->log.c_txbytes,
                                                mt->log.bindrx_us > 0);

                zpn_broker_client_free_self_mtunnel_hop_logs(mt);

                if (mt->req_app_name) {
                    ZPN_FREE(mt->req_app_name);
                    mt->req_app_name = NULL;
                }
                if (mt->application_domain) {
                    ZPN_FREE(mt->application_domain);
                    mt->application_domain = NULL;
                }
                if (mt->db_app_name) {
                    ZPN_FREE(mt->db_app_name);
                    mt->db_app_name = NULL;
                }
                if (mt->mtunnel_id) {
                    ZPN_FREE(mt->mtunnel_id);
                    mt->mtunnel_id = NULL;
                }
                if (mt->tunnel_id) {
                    ZPN_FREE(mt->tunnel_id);
                    mt->tunnel_id = NULL;
                }
                if (mt->err) {
                    ZPN_FREE(mt->err);
                    mt->err = NULL;
                }
                if (mt->o_dwgs) {
                    ZPN_FREE(mt->o_dwgs);
                    mt->o_dwgs = NULL;
                    mt->o_dwgs_count = 0;
                }
                if (mt->log.g_apps) {
                    ZPN_FREE(mt->log.g_apps);
                    mt->log.g_apps = NULL;
                    mt->log.g_apps_count = 0;
                }
                if (mt->log.g_app_grps) {
                    ZPN_FREE(mt->log.g_app_grps);
                    mt->log.g_app_grps = NULL;
                    mt->log.g_app_grps_count = 0;
                }

                if (mt->log.gids) {
                    ZPN_FREE(mt->log.gids);
                    mt->log.gids = NULL;
                    mt->log.gids_count = 0;
                }

                if (mt->log.slogger_info) {
                    ZPN_FREE(mt->log.slogger_info);
                    mt->log.slogger_info = NULL;
                }

                if (mt->log.g_dsp_list) {
                    ZPN_FREE(mt->log.g_dsp_list);
                    mt->log.g_dsp_list = NULL;
                    mt->log.g_dsp_list_count = 0;
                }

                if (mt->log.filter_ast_grp_str_array) {
                    for (int k = 0; k < mt->log.filter_ast_grp_str_count; k++)
                    {
                        if (mt->log.filter_ast_grp_str_array[k]) {
                            ZPN_FREE(mt->log.filter_ast_grp_str_array[k]);
                            mt->log.filter_ast_grp_str_array[k] = NULL;
                        }
                    }
                    ZPN_FREE(mt->log.filter_ast_grp_str_array);
                    mt->log.filter_ast_grp_str_array = NULL;
                }

                if (mt->log.filter_extranet_locs) {
                    for (int k = 0; k < mt->log.filter_extranet_locs_count; k++)
                    {
                        if (mt->log.filter_extranet_locs[k]) {
                            ZPN_FREE(mt->log.filter_extranet_locs[k]);
                            mt->log.filter_extranet_locs[k] = NULL;
                        }
                    }
                    ZPN_FREE(mt->log.filter_extranet_locs);
                    mt->log.filter_extranet_locs = NULL;
                }

                if (mt->log.log_features) {
                    int i;
                    for (i = 0; i < mt->log.log_features_count; ++i) {
                        ZPN_FREE(mt->log.log_features[i]);
                    }
                    ZPN_FREE(mt->log.log_features);
                }

                if (mt->log.wtag_grp_found) {
                    ZPN_FREE_AND_NULL(mt->log.wtag_grp_found);
                    mt->log.wtag_grp_found_rcvd = 0;
                }

                if (mt->log.wtag_grp_not_found) {
                    ZPN_FREE_AND_NULL(mt->log.wtag_grp_not_found);
                    mt->log.wtag_grp_not_found_rcvd = 0;
                }

                if (mt->public_cloud) {
                    ZPN_FREE(mt->public_cloud);
                    mt->public_cloud = NULL;
                }

                if (mt->private_cloud) {
                    ZPN_FREE(mt->private_cloud);
                    mt->private_cloud = NULL;
                }

                if (mt->region_id) {
                    ZPN_FREE(mt->region_id);
                    mt->region_id = NULL;
                }

                if (mt->a_cc) {
                    ZPN_FREE(mt->a_cc);
                    mt->a_cc = NULL;
                }

                if (mt->a_city) {
                    ZPN_FREE(mt->a_city);
                    mt->a_city = NULL;
                }

                if (mt->a_sc) {
                    ZPN_FREE(mt->a_sc);
                    mt->a_sc = NULL;
                }

                if (mt->c2c_uid_peer) {
                    ZPN_FREE(mt->c2c_uid_peer);
                    mt->c2c_uid_peer = NULL;
                }
                if (mt->brk_name) {
                    ZPN_FREE(mt->brk_name);
                    mt->brk_name = NULL;
                }
                if (mt->mtunnel_id_parent) {
                    ZPN_FREE(mt->mtunnel_id_parent);
                    mt->mtunnel_id_parent = NULL;
                }
                if (mt->extranet_locs) {
                    ZPN_FREE_AND_NULL(mt->extranet_locs);
                }
                if (mt->log.c_pra_console_user) {
                    ZPN_FREE(mt->log.c_pra_console_user);
                    mt->log.c_pra_console_user = NULL;
                }
                if (mt->log.c_pra_file_transfer_list) {
                    ZPN_FREE(mt->log.c_pra_file_transfer_list);
                    mt->log.c_pra_file_transfer_list = NULL;
                }
                if (mt->log.c_pra_console_conn_type) {
                    ZPN_FREE(mt->log.c_pra_console_conn_type);
                    mt->log.c_pra_console_conn_type = NULL;
                }
                if (mt->log.c_pra_error_string) {
                    ZPN_FREE(mt->log.c_pra_error_string);
                    mt->log.c_pra_error_string = NULL;
                }
                if (mt->log.c_pra_sess_type) {
                    ZPN_FREE(mt->log.c_pra_sess_type);
                    mt->log.c_pra_sess_type = NULL;
                }
                if (mt->log.c_pra_conn_id) {
                    ZPN_FREE(mt->log.c_pra_conn_id);
                    mt->log.c_pra_conn_id = NULL;
                }
                if (mt->log.o_identity_name) {
                    ZPN_FREE(mt->log.o_identity_name);
                    mt->log.o_identity_name = NULL;
                }
                if (mt->log.c_pra_session_recording) {
                    ZPN_FREE(mt->log.c_pra_session_recording);
                    mt->log.c_pra_session_recording = NULL;
                }

                if (mt->log.c_pra_shared_users_list) {
                    ZPN_FREE(mt->log.c_pra_shared_users_list);
                    mt->log.c_pra_shared_users_list = NULL;
                }
                if (mt->log.c_pra_shared_mode) {
                    ZPN_FREE(mt->log.c_pra_shared_mode);
                    mt->log.c_pra_shared_mode = NULL;
                }
                if (mt->log.c_pra_credential_id) {
                    ZPN_FREE(mt->log.c_pra_credential_id);
                    mt->log.c_pra_credential_id = NULL;
                }
                if (mt->log.c_pra_credential_pool_id) {
                    ZPN_FREE(mt->log.c_pra_credential_pool_id);
                    mt->log.c_pra_credential_pool_id = NULL;
                }
                if (mt->log.slogger_info) {
                    ZPN_FREE(mt->log.slogger_info);
                    mt->log.slogger_info = NULL;
                }
                if (mt->origin_mtunnel_id) {
                    ZPN_FREE(mt->origin_mtunnel_id);
                    mt->origin_mtunnel_id = NULL;
                }

                if (mt->log.zia_cloud) {
                    ZPN_FREE_AND_NULL(mt->log.zia_cloud);
                }

                if(mt->required_stepup_auth_level_name) {
                    ZPN_FREE_AND_NULL(mt->required_stepup_auth_level_name);
                }

                if(mt->required_stepup_auth_level_id) {
                    ZPN_FREE_AND_NULL(mt->required_stepup_auth_level_id);
                }

                if(mt->current_stepup_auth_level_name) {
                    ZPN_FREE_AND_NULL(mt->current_stepup_auth_level_name);
                }

                if(mt->log.gprofile_gids) {
                    ZPN_FREE_AND_NULL(mt->log.gprofile_gids);
                }
                zpn_broker_client_free_aae_mtunnel_log(&mt->log.aae_conclusion_user_profiles, mt->log.aae_conclusion_user_profile_count,
                                                       &mt->log.aae_conclusion_device_profiles, mt->log.aae_conclusion_device_profile_count);
            }
            mtunnel_unlock(mt);

            if (free_mt) {
                ZPN_DEBUG_ZINS("free mtunnel:");
                if(ZPN_BROKER_IS_PRIVATE()) {
                    struct zpn_broker_client_fohh_state *c_state = zpn_broker_mtunnel_client_c_state(mt);
                    if (!c_state) {
                        ZPN_DEBUG_MTUNNEL("%s: mtunnel failed to get c_state", mt->mtunnel_id);
                        zpn_broker_mtunnel_free(mt);
                    } else {
                        zpn_broker_mtunnel_free(mt);
                    }
                } else {
                    zpn_broker_mtunnel_free(mt);
                }
            }
        }

        if (reaped_count > bucket->reaped_max_count) bucket->reaped_max_count = reaped_count;
        if (reaped_null_count > bucket->reaped_max_null_count) bucket->reaped_max_null_count = reaped_null_count;
        reaped_cur_us = epoch_us();
        if (reaped_cur_us - bucket->reaped_start_us >= REAPED_LIST_LOG_INTERVAL_US) {
            ZPN_LOG(AL_DEBUG, "bucket index= %d, reaped_max_count=%ld, reaped_max_null_count=%ld, ts_usec %ld - %ld",
                             i, (long)bucket->reaped_max_count, (long)bucket->reaped_max_null_count,
                             (long)bucket->reaped_start_us, (long)reaped_cur_us);
            bucket->reaped_start_us = reaped_cur_us;
            bucket->reaped_max_count = 0;
            bucket->reaped_max_null_count = 0;
        }
        #if 0
        if (reaped_count > 0) {
            ZPN_DEBUG_MTUNNEL( "bucket index=%4d, mt-visited=%5" PRId64 ", reaped=%d  reaped-null=%d alloc = %ld, free_q_cnt = %ld", i, mtunnels_visited,
                    reaped_count, reaped_null_count, (long)free_q.stats.allocations, (long)free_q.stats.free_queue_count);
        }
        #endif

        pthread_mutex_unlock(&(bucket->lock));
    }

    /* This is no longer accurate since we do small sets of iteration rather than large sets. */
    /*
    ZPN_DEBUG_MTUNNEL("Broker Mtunnels: active_conn = %d, free_conn = %d, freed = %d, alloc = %ld, free_q_cnt = %ld",
                      active_conn, free_conn, freed, (long)free_q.stats.allocations, (long)free_q.stats.free_queue_count);
    */
}

static void zpn_broker_mtunnel_dump_internal(struct zpath_debug_state* request_state, struct zpn_broker_mtunnel *mt)
{
    struct zpn_mconn *client_mconn = zpn_broker_mtunnel_client_mconn(mt);
    struct zpn_mconn *asst_mconn = zpn_broker_mtunnel_assistant_mconn(mt);
    size_t client_mconn_tx_len = zpn_mconn_get_transmit_buffer_len(client_mconn);
    size_t client_mconn_rx_len = client_mconn->client_rx_data ? evbuffer_get_length(client_mconn->client_rx_data) : 0;;
    size_t asst_mconn_tx_len = zpn_mconn_get_transmit_buffer_len(asst_mconn);
    size_t asst_mconn_rx_len = asst_mconn->client_rx_data ? evbuffer_get_length(asst_mconn->client_rx_data) : 0;

    if (client_mconn_tx_len > (ZPN_MCONN_MAX_CLIENT_TX_DATA * 2) ||
        client_mconn_rx_len > (ZPN_MCONN_MAX_CLIENT_TX_DATA * 2) ||
        asst_mconn_tx_len > (ZPN_MCONN_MAX_CLIENT_TX_DATA * 2) ||
        asst_mconn_rx_len > (ZPN_MCONN_MAX_CLIENT_TX_DATA * 2) ) {
        zpath_debug_cb_printf_response(request_state, "mtunnel_id = %s, client_mconn_tx_len = %ld, client_mconn_rx_len = %ld, asst_mconn_tx_len = %ld, asst_mconn_rx_len = %ld\n",
                                       mt->mtunnel_id, (long)client_mconn_tx_len, (long)client_mconn_rx_len, (long)asst_mconn_tx_len, (long)asst_mconn_rx_len);
    }
}

static void zpn_broker_mtunnel_dump_user_risk(struct zpath_debug_state* request_state, struct zpn_broker_mtunnel *mt)
{
    zpath_debug_cb_printf_response(request_state,
                                  "mtunnel_id = %s, "
                                  "mtunnel user risk version = %"PRId64" \n",
                                  mt->mtunnel_id, mt->user_risk_version);
}

static void
zpn_broker_mtunnels_common_cb(struct zpath_debug_state* request_state,
                              const char **query_values,
                              int is_user_risk_stats)
{
    int i;
    int active_conn = 0;
    int free_conn = 0;
    int promoted_count = 0;

    const char *tunnel_id = NULL;
    int all = query_values[1] ? 1 : 0;

    if (query_values[0]) {
        tunnel_id = query_values[0];
    }

    zpath_debug_cb_printf_response(request_state,
                                   "Dumping mtunnel internal state - tunnel - %s\n",
                                    (tunnel_id != NULL) ? tunnel_id : "all" );

    for (i = 0; i < ZPN_BROKER_BUCKETS; i++)
    {
        struct zpn_broker_bucket *bucket = &(broker.buckets[i]);
        struct zpn_broker_mtunnel *mt;
        struct zpn_broker_mtunnel *tmp_mt;

        pthread_mutex_lock(&(bucket->lock));

        for (mt = TAILQ_FIRST(&(bucket->mtunnel_list)); mt != NULL; mt = tmp_mt)
        {
                tmp_mt = TAILQ_NEXT(mt, bucket_list);
                mtunnel_lock(mt);
                if (!tunnel_id || (mt->tunnel_id && (strcmp(mt->tunnel_id, tunnel_id) == 0))) {
                    if (is_user_risk_stats) {
                        zpn_broker_mtunnel_dump_user_risk(request_state, mt);
                    } else {
                        zpn_broker_mtunnel_dump_internal(request_state, mt);
                    }
                    active_conn++;

                    if (mt->promoted_to_public) {
                        promoted_count++;
                    }

                    if (all) {
                        ZDP("%3d. '%s':'%s'  app='%s'"
                            " thread=%-3d tags(C/A)=%d/%d err='%s' ask_sent(C/A)=%d/%d zia=%s:'%s':%d:%d state=%s\n",
                            active_conn,
                            mt->mtunnel_id,
                            mtunnel_state(mt->state),
                            mt->req_app_name ?: "",
                            mt->c_state_fohh_thread_id,
                            mt->client_tlv.tag_id,
                            mt->assistant_tlv.tag_id,
                            mt->err ?: "",
                            mt->client_ack_sent,
                            mt->assistant_ack_sent,
                            mt->zia_inspection ? "insp" : "",
                            mt->origin_mtunnel_id ?: "",
                            mt->zia_term,
                            mt->zia_origin,
                            mtunnel_state(mt->state));

                        if (mt->client_tlv_type == zpn_fohh_tlv &&
                            mt->assistant_tlv_type == zpn_fohh_tlv) {
                            // show mcon stats
                            zpn_broker_mtunnel_internal_display_zdf(request_state, 0, &(mt->client_tlv));
                            zpn_broker_mtunnel_internal_display_zdf(request_state, 1, &(mt->assistant_tlv));
                        }
                    }
                }
                mtunnel_unlock(mt);
        }

        for (mt = TAILQ_FIRST(&(bucket->reaped_list)); mt != NULL; mt = tmp_mt)
        {
                tmp_mt = TAILQ_NEXT(mt, bucket_list);
                mtunnel_lock(mt);
                if (!tunnel_id || (mt->tunnel_id && (strcmp(mt->tunnel_id, tunnel_id) == 0))) {
                    if (is_user_risk_stats) {
                        zpn_broker_mtunnel_dump_user_risk(request_state, mt);
                    } else {
                        zpn_broker_mtunnel_dump_internal(request_state, mt);
                    }
                    free_conn++;
                }
                mtunnel_unlock(mt);
        }

        pthread_mutex_unlock(&(bucket->lock));
    }

    if (is_user_risk_stats) {
        zpath_debug_cb_printf_response(request_state, "Broker Mtunnels: active_conn = %d, promoted_mt_count: %d, free_conn = %d\n",
                      active_conn, promoted_count, free_conn);

    } else {
        zpath_debug_cb_printf_response(request_state, "Broker Mtunnels: active_conn = %d, promoted_mt_count: %d, free_conn = %d, alloc = %ld, free_q_cnt = %ld\n",
                      active_conn, promoted_count, free_conn, (long)free_q.stats.allocations, (long)free_q.stats.free_queue_count);
    }
}

int zpn_broker_mtunnels_dump(struct zpath_debug_state* request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    zpn_broker_mtunnels_common_cb(request_state,
                                  query_values,
                                  0);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_user_risk_mtunnels_dump(struct zpath_debug_state* request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    zpn_broker_mtunnels_common_cb(request_state,
                                  query_values,
                                  1);
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mtunnel_update_qbr_fields(struct zpn_broker_mtunnel *mtunnel)
{
    /*
     * Populate public cloud and region ID information only if private cloud is not configured.
     * Additionally, both public cloud and region ID information must be available;
     * otherwise, neither will be reported.
     */
    if (zpn_broker_assistant_is_qbr_insights_feature_enabled(mtunnel->customer_id)) {
        if (mtunnel->private_cloud || (!mtunnel->public_cloud && mtunnel->region_id) || (mtunnel->public_cloud && !mtunnel->region_id)) {
            mtunnel->log.g_ast_public_cloud = mtunnel->log.g_ast_region_id = NULL;
            mtunnel->log.g_ast_private_cloud = mtunnel->private_cloud;
        } else {
            mtunnel->log.g_ast_public_cloud = mtunnel->public_cloud;
            mtunnel->log.g_ast_region_id = mtunnel->region_id;
            mtunnel->log.g_ast_private_cloud = NULL;
        }

        mtunnel->log.g_ast_country_code = mtunnel->a_cc;
        mtunnel->log.g_ast_city = (mtunnel->log.g_ast_country_code) ? mtunnel->a_city : NULL;
        mtunnel->log.g_ast_subdivision_code = (mtunnel->log.g_ast_country_code && mtunnel->log.g_ast_city) ? mtunnel->a_sc : NULL;
    } else {
        mtunnel->log.g_ast_public_cloud = mtunnel->log.g_ast_region_id = mtunnel->log.g_ast_private_cloud = mtunnel->log.g_ast_country_code = mtunnel->log.g_ast_city = mtunnel->log.g_ast_subdivision_code = NULL;
    }
}

/*
 * Locking: assume we have the lock of mtunnel
 * The mtunnel log is created(writen):
 *  1. when tunnel is fully connected, NOT Before
 *  2. After tunnel is fully connectd and before it is terminated
 *  3. When tunnel is terminated. This includes error termination, before tunnel is connected
 */
int zpn_broker_mtunnel_log(struct zpn_broker_mtunnel *mtunnel)
{
    int res;
    double lat, lon;
    char str[ARGO_INET_ADDRSTRLEN];

    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    enum zpn_trans_hop_type hop_type;
    struct zpn_application *app;
    //
    int should_output_log = 1;

    if (mtunnel->no_more_logs) return ZPN_RESULT_NO_ERROR;

    if (!mtunnel->log.tunnel_id) mtunnel->log.tunnel_id = mtunnel->tunnel_id;
    if (!mtunnel->log.mtunnel_id) mtunnel->log.mtunnel_id = mtunnel->mtunnel_id;

    mtunnel->log_s = epoch_s();
    mtunnel->log.log_date = mtunnel->log_s;

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    // check if tunnel is fully connected
    should_output_log = client_mconn && assistant_mconn && client_mconn->peer && assistant_mconn->peer;

    if (mtunnel->err) {
        // if there is an error, log
        should_output_log = 1;
    }
    else if( mtunnel->log.action == NULL) {
        // if action is not set
        should_output_log = 0;
    }

    if( should_output_log == 0) {
        ZPN_DEBUG_MTUNNEL("%s tunnel not connect yet", mtunnel->tunnel_id );
    }

    if (mtunnel->zpn_broker_mtunnel_type != zbmt_transit_c2c) {
        mtunnel->log.domain = mtunnel->req_app_name;
        mtunnel->log.g_rul = mtunnel->rule_gid;
        mtunnel->log.g_app_grp = mtunnel->app_group_id;
        mtunnel->log.g_auth_rul = mtunnel->reauth_rule_gid;
    }

    mtunnel->log.g_apprl_id = mtunnel->approval_id;
    mtunnel->log.g_app = mtunnel->application_id;
    mtunnel->log.g_ast = mtunnel->assistant_id;
    mtunnel->log.g_ast_grp = mtunnel->assistant_group_id;
    mtunnel->log.g_srv_grp = mtunnel->server_group_id;
    mtunnel->log.g_brk = mtunnel->broker_id;
    mtunnel->log.g_bfw = mtunnel->fwd_broker_id;
    mtunnel->log.g_cst = mtunnel->customer_id;
    mtunnel->log.g_aps = mtunnel->application_server_id;
    mtunnel->log.connector_close_to_app = mtunnel->connector_close_to_app;
    mtunnel->log.posture_profile_version = mtunnel->posture_profile_version;
    mtunnel->log.app_match_style = zpath_match_style_enum_to_string(mtunnel->match_style);
    mtunnel->log.sv_posture_profile_version = mtunnel->sv_posture_profile_version;

    mtunnel->log.required_stepup_auth_level_expiry_s = mtunnel->required_stepup_auth_level_expiry_s;
    mtunnel->log.required_stepup_auth_level_gid = mtunnel->required_stepup_auth_level_gid;
    mtunnel->log.current_stepup_auth_level_expiry_s = mtunnel->current_stepup_auth_level_expiry_s;
    mtunnel->log.current_stepup_auth_level_gid = mtunnel->current_stepup_auth_level_gid;

    if (!mtunnel->log.g_dsp) {
        if (!mtunnel->flag.timeout_redispatch_to_diff_dc) {
            mtunnel->log.g_dsp = mtunnel->dispatcher_id;
        } else {
            if (!mtunnel->flag.response_received_from_disp) {
                /* We re-dispatched to different DC dispatcher because of timeout but none of them responded back. */
                mtunnel->log.g_dsp = mtunnel->redispatch_to_diff_dc_disp_id;
            }
        }
    }
    if (mtunnel->log.g_dsp_list) {
        ZPN_FREE(mtunnel->log.g_dsp_list);
    }
    mtunnel->log.g_dsp_list = NULL;
    mtunnel->log.g_dsp_list_count = 0;

    /*
     * Log both disp id if - first disp returned NO_CONNECTOR_AVAILABLE or INVALID_DOMAIN or didn't
     * respond within 2 sec and we re-dispatched the brk req to some other disp in different DC.
     */
    if (mtunnel->last_dispatcher_id) {
        mtunnel->log.g_dsp_list = ZPN_CALLOC(2 * (sizeof(*(mtunnel->log.g_dsp_list))));
        mtunnel->log.g_dsp_list[0] = mtunnel->last_dispatcher_id;
        mtunnel->log.g_dsp_list[1] = mtunnel->redispatch_to_diff_dc_disp_id;
        mtunnel->log.g_dsp_list_count = 2;
    } else {
        mtunnel->log.g_dsp_list = ZPN_CALLOC(1 * (sizeof(*(mtunnel->log.g_dsp_list))));
        mtunnel->log.g_dsp_list[0] = mtunnel->dispatcher_id;
        mtunnel->log.g_dsp_list_count = 1;
    }

    /* Update path_decision string in txn log as per latest mtunnel path_decision bits. */
    zpn_tx_path_decision_get_str(mtunnel->path_decision, mtunnel->log.path_decision, sizeof(mtunnel->log.path_decision));

    if(zpn_application_get_by_id_immediate(mtunnel->application_id, &app) == ZPATH_RESULT_NO_ERROR) {
        mtunnel->log.g_app_microtenant = is_scope_default(app->scope_gid) ? 0 : app->scope_gid;
    }

    if (mtunnel->zpn_broker_mtunnel_type == zbmt_transit_c2c) {
        // swaping the orginator and terminating uid per PM request for 1st phase, in two broker case
        // single broker case is already correct
        mtunnel->log.c_uid = mtunnel->c2c_uid_peer;
        mtunnel->log.c2c_uid_peer = mtunnel->user_id;
        mtunnel->log.c_priv_ip = mtunnel->c2c_priv_ip;
    } else {
        if (mtunnel->client_type == zpn_client_type_ip_anchoring) {
            // for SIPA , check if user/location name is available , replace the c_uid
            if (mtunnel->log.o_identity_name) {
                mtunnel->log.c_uid = mtunnel->log.o_identity_name;
            }
        } else {
            mtunnel->log.c_uid = mtunnel->user_id;
            if (mtunnel->c2c_uid_peer) {
                mtunnel->log.c2c_uid_peer = mtunnel->c2c_uid_peer;
            }
        }
    }

    // add flow_ctrl
    mtunnel->log.flow_ctrl[0] = client_mconn->to_client_paused_count;
    mtunnel->log.flow_ctrl[1] = client_mconn->from_client_paused_count;
    mtunnel->log.flow_ctrl[2] = client_mconn->to_client_resume_count;
    mtunnel->log.flow_ctrl[3] = client_mconn->from_client_resume_count;
    mtunnel->log.flow_ctrl[4] = client_mconn->to_client_pause_timed_out_count;
    mtunnel->log.flow_ctrl[5] = assistant_mconn->to_client_paused_count;
    mtunnel->log.flow_ctrl[6] = assistant_mconn->from_client_paused_count;
    mtunnel->log.flow_ctrl[7] = assistant_mconn->to_client_resume_count;
    mtunnel->log.flow_ctrl[8] = assistant_mconn->from_client_resume_count;
    mtunnel->log.flow_ctrl[9] = assistant_mconn->to_client_pause_timed_out_count;

    zpn_mtunnel_update_qbr_fields(mtunnel);

    // zia inspection
    mtunnel->log.zia_inspection = mtunnel->zia_inspection;
#if USE_INSP_RES
    ZPN_DEBUG_ZINS( "%s zia_results: %d", mtunnel->mtunnel_id, mtunnel->zia_results);
    if (mtunnel->zia_results) {
        mtunnel->log.zia_inspection_results = 1;
    }
#endif

    // TODO: Consider reusing lookup results in c_state.
    if (geoip_db_file) {
        if (!mtunnel->log.c_cc) {
            if (zpath_geoip_lookup_double(&mtunnel->log.c_pub_ip, &lat, &lon, &mtunnel->c_cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_MTUNNEL("Client IP = %s, lat = %f, lon = %f, cc = %s",
                                argo_inet_generate(str, &(mtunnel->log.c_pub_ip)), lat, lon, mtunnel->c_cc);
                mtunnel->log.c_lat = lat;
                mtunnel->log.c_lon = lon;
                mtunnel->log.c_cc = &mtunnel->c_cc[0];
            } else {
                if(!argo_inet_is_private(&mtunnel->log.c_pub_ip)){
                    ZPN_LOG(AL_WARNING, "geoip lookup failed for client ip = %s", argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                }else{
                    ZPN_DEBUG_MTUNNEL("geoip lookup failed for client ip = %s", argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                }
                mtunnel->log.c_lat = 0;
                mtunnel->log.c_lon = 0;
                mtunnel->log.c_cc = NULL;
            }

            /* In many cases city info may not exist, so only perform lookup together with cc lookup */
            if (!mtunnel->log.c_city) {
                if (zpath_geoip_lookup_city(&mtunnel->log.c_pub_ip, &mtunnel->c_city[0], sizeof(mtunnel->c_city)) == ZPATH_RESULT_NO_ERROR) {
                    ZPN_DEBUG_MTUNNEL("%s: Client IP = %s, city = %s",
                                    mtunnel->mtunnel_id,
                                    argo_inet_generate(str, &(mtunnel->log.c_pub_ip)), mtunnel->c_city);
                    mtunnel->log.c_city = &mtunnel->c_city[0];
                } else {
                    ZPN_DEBUG_MTUNNEL("%s: City lookup failed for client ip = %s", mtunnel->mtunnel_id, argo_inet_generate(str, &mtunnel->log.c_pub_ip));
                    mtunnel->log.c_city = NULL;
                }
            }
        }
    }

    mtunnel->log.c_rx_us_b = client_mconn->rx_data_us_b;
    mtunnel->log.c_tx_us_b = client_mconn->tx_data_us_b;
    mtunnel->log.c_rx_us = client_mconn->rx_data_us;
    mtunnel->log.c_tx_us = client_mconn->tx_data_us;
    mtunnel->log.a_rx_us_b = assistant_mconn->rx_data_us_b;
    mtunnel->log.a_tx_us_b = assistant_mconn->tx_data_us_b;
    mtunnel->log.a_rx_us = assistant_mconn->rx_data_us;
    mtunnel->log.a_tx_us = assistant_mconn->tx_data_us;
    mtunnel->log.c_rxdelta = client_mconn->bytes_to_peer - mtunnel->log.c_rxbytes;
    mtunnel->log.c_rxbytes = client_mconn->bytes_to_peer;
    mtunnel->log.c_txdelta = client_mconn->bytes_to_client - mtunnel->log.c_txbytes;
    mtunnel->log.c_txbytes = client_mconn->bytes_to_client;
    mtunnel->log.a_rxdelta = assistant_mconn->bytes_to_peer - mtunnel->log.a_rxbytes;
    mtunnel->log.a_rxbytes = assistant_mconn->bytes_to_peer;
    mtunnel->log.a_txdelta = assistant_mconn->bytes_to_client - mtunnel->log.a_txbytes;
    mtunnel->log.a_txbytes = assistant_mconn->bytes_to_client;

    mtunnel->log.c_tx_end_us = client_mconn->fin_sent_us;
    mtunnel->log.c_rx_end_us = client_mconn->fin_rcvd_us;
    mtunnel->log.a_tx_end_us = assistant_mconn->fin_sent_us;
    mtunnel->log.a_rx_end_us = assistant_mconn->fin_rcvd_us;

    mtunnel->log.c_client_rx_data_len[0] = client_mconn->client_rx_data_len[0];
    mtunnel->log.c_client_rx_data_len[1] = client_mconn->client_rx_data_len[1];
    mtunnel->log.c_client_rx_data_len[2] = client_mconn->client_rx_data_len[2];
    mtunnel->log.c_client_rx_data_len[3] = client_mconn->client_rx_data_len[3];
    mtunnel->log.a_client_rx_data_len[0] = assistant_mconn->client_rx_data_len[0];
    mtunnel->log.a_client_rx_data_len[1] = assistant_mconn->client_rx_data_len[1];
    mtunnel->log.a_client_rx_data_len[2] = assistant_mconn->client_rx_data_len[2];
    mtunnel->log.a_client_rx_data_len[3] = assistant_mconn->client_rx_data_len[3];

    mtunnel->log.c_tx_buf_len[0] = client_mconn->tx_buf_len[0];
    mtunnel->log.c_tx_buf_len[1] = client_mconn->tx_buf_len[1];
    mtunnel->log.c_tx_buf_len[2] = client_mconn->tx_buf_len[2];
    mtunnel->log.c_tx_buf_len[3] = client_mconn->tx_buf_len[3];
    mtunnel->log.a_tx_buf_len[0] = assistant_mconn->tx_buf_len[0];
    mtunnel->log.a_tx_buf_len[1] = assistant_mconn->tx_buf_len[1];
    mtunnel->log.a_tx_buf_len[2] = assistant_mconn->tx_buf_len[2];
    mtunnel->log.a_tx_buf_len[3] = assistant_mconn->tx_buf_len[3];

    // track performance stats
    int64_t track_perf_stats_level = zpn_broker_config_get_fohh_mconn_track_perf_stats_level(mtunnel->customer_id);
    if ((track_perf_stats_level > 0) && (track_perf_stats_level <= HIGH_ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL)) {

        if (track_perf_stats_level >= 1) {
            // 1 level of track performance stats
            mtunnel->log.c_tx_peer_rx_data_hist[0] = client_mconn->tx_peer_rx_data_hist[0];
            mtunnel->log.c_tx_peer_rx_data_hist[1] = client_mconn->tx_peer_rx_data_hist[1];
            mtunnel->log.c_tx_peer_rx_data_hist[2] = client_mconn->tx_peer_rx_data_hist[2];
            mtunnel->log.c_tx_peer_rx_data_hist[3] = client_mconn->tx_peer_rx_data_hist[3];
            mtunnel->log.c_tx_peer_rx_data_hist[4] = client_mconn->tx_peer_rx_data_hist[4];

            mtunnel->log.a_tx_peer_rx_data_hist[0] = assistant_mconn->tx_peer_rx_data_hist[0];
            mtunnel->log.a_tx_peer_rx_data_hist[1] = assistant_mconn->tx_peer_rx_data_hist[1];
            mtunnel->log.a_tx_peer_rx_data_hist[2] = assistant_mconn->tx_peer_rx_data_hist[2];
            mtunnel->log.a_tx_peer_rx_data_hist[3] = assistant_mconn->tx_peer_rx_data_hist[3];
            mtunnel->log.a_tx_peer_rx_data_hist[4] = assistant_mconn->tx_peer_rx_data_hist[4];
        }
        if (track_perf_stats_level >= 2) {
            // 2  level of track performance stats
            mtunnel->log.c_rx_diff_rx_data_hist[0] = client_mconn->rx_diff_rx_data_hist[0];
            mtunnel->log.c_rx_diff_rx_data_hist[1] = client_mconn->rx_diff_rx_data_hist[1];
            mtunnel->log.c_rx_diff_rx_data_hist[2] = client_mconn->rx_diff_rx_data_hist[2];
            mtunnel->log.c_rx_diff_rx_data_hist[3] = client_mconn->rx_diff_rx_data_hist[3];
            mtunnel->log.c_rx_diff_rx_data_hist[4] = client_mconn->rx_diff_rx_data_hist[4];

            mtunnel->log.c_tx_peer_rx_data_max_us = client_mconn->tx_peer_rx_data_max_us;
            mtunnel->log.c_tx_peer_rx_data_max_epoch_us = client_mconn->tx_peer_rx_data_max_epoch_us;

            mtunnel->log.a_rx_diff_rx_data_hist[0] = assistant_mconn->rx_diff_rx_data_hist[0];
            mtunnel->log.a_rx_diff_rx_data_hist[1] = assistant_mconn->rx_diff_rx_data_hist[1];
            mtunnel->log.a_rx_diff_rx_data_hist[2] = assistant_mconn->rx_diff_rx_data_hist[2];
            mtunnel->log.a_rx_diff_rx_data_hist[3] = assistant_mconn->rx_diff_rx_data_hist[3];
            mtunnel->log.a_rx_diff_rx_data_hist[4] = assistant_mconn->rx_diff_rx_data_hist[4];

            mtunnel->log.a_tx_peer_rx_data_max_us = assistant_mconn->tx_peer_rx_data_max_us;

            mtunnel->log.a_tx_peer_rx_data_max_epoch_us = assistant_mconn->tx_peer_rx_data_max_epoch_us;
        }
        if (track_perf_stats_level >= 3) {
            // 3  level of track performance stats
            mtunnel->log.c_tx_peer_rx_data_max_cnt = client_mconn->tx_peer_rx_data_max_cnt;
            mtunnel->log.c_tx_peer_rx_data_max_unblock_cnt = client_mconn->tx_peer_rx_data_max_unblock_cnt;

            mtunnel->log.c_tx_data_unblock_max_us = client_mconn->tx_data_unblock_max_us;
            mtunnel->log.c_tx_data_unblock_tot_us = client_mconn->tx_data_unblock_tot_us;
            mtunnel->log.c_tx_data_unblock_max_cnt = client_mconn->tx_data_unblock_max_cnt;

            mtunnel->log.a_tx_peer_rx_data_max_cnt = assistant_mconn->tx_peer_rx_data_max_cnt;
            mtunnel->log.a_tx_peer_rx_data_max_unblock_cnt = assistant_mconn->tx_peer_rx_data_max_unblock_cnt;

            mtunnel->log.a_tx_data_unblock_max_us = assistant_mconn->tx_data_unblock_max_us;
            mtunnel->log.a_tx_data_unblock_tot_us = assistant_mconn->tx_data_unblock_tot_us;
            mtunnel->log.a_tx_data_unblock_max_cnt = assistant_mconn->tx_data_unblock_max_cnt;
        }
    }
    // reset track performance counters
    __atomic_store_n(&client_mconn->tx_peer_rx_data_max_us, 0, __ATOMIC_RELAXED);
    __atomic_store_n(&assistant_mconn->tx_peer_rx_data_max_us, 0, __ATOMIC_RELAXED);
    __atomic_store_n(&client_mconn->tx_data_unblock_max_us, 0, __ATOMIC_RELAXED);
    __atomic_store_n(&assistant_mconn->tx_data_unblock_max_us, 0, __ATOMIC_RELAXED);

    mtunnel->log.c_notx_bytes = client_mconn->dropped_untransmitted_bytes;
    mtunnel->log.a_notx_bytes = assistant_mconn->dropped_untransmitted_bytes;

    mtunnel->log.c_bad_udp = client_mconn->packets_malformed_udp;
    mtunnel->log.a_bad_udp = assistant_mconn->packets_malformed_udp;

    mtunnel->log.c_bad_icmp = client_mconn->icmp_malformed_pkt_drops;
    mtunnel->log.a_bad_icmp = assistant_mconn->icmp_malformed_pkt_drops;

    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        mtunnel->log.c_fc_tx_max = mtunnel->client_tlv.tx_limit;
        mtunnel->log.c_fc_rx_max = mtunnel->client_tlv.remote_tx_limit;
        mtunnel->log.c_fc_tx_us = mtunnel->client_tlv.last_wnd_tx_update_us;
        mtunnel->log.c_fc_rx_us = mtunnel->client_tlv.last_wnd_rx_update_us;
    }

    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        mtunnel->log.a_fc_tx_max = mtunnel->assistant_tlv.tx_limit;
        mtunnel->log.a_fc_rx_max = mtunnel->assistant_tlv.remote_tx_limit;
        mtunnel->log.a_fc_tx_us = mtunnel->assistant_tlv.last_wnd_tx_update_us;
        mtunnel->log.a_fc_rx_us = mtunnel->assistant_tlv.last_wnd_rx_update_us;;
    }

    mtunnel->log.c_txpause = client_mconn->to_client_paused_count;
    mtunnel->log.c_rxpause = client_mconn->from_client_paused_count;
    mtunnel->log.c_txresume = client_mconn->to_client_resume_count;
    mtunnel->log.c_rxresume = client_mconn->from_client_resume_count;
    mtunnel->log.c_pauseto = client_mconn->to_client_pause_timed_out_count;
    mtunnel->log.c_txpause_time_max_us = client_mconn->to_client_pause_time_max_us;
    mtunnel->log.c_txpause_time_max_epoch_us = client_mconn->to_client_pause_time_max_epoch_us;
    mtunnel->log.c_txpause_time_total_us = client_mconn->to_client_pause_time_total_us;
    mtunnel->log.c_rxpause_time_max_us = client_mconn->from_client_pause_time_max_us;
    mtunnel->log.c_rxpause_time_max_epoch_us = client_mconn->from_client_pause_time_max_epoch_us;
    mtunnel->log.c_rxpause_time_total_us = client_mconn->from_client_pause_time_total_us;

    mtunnel->log.a_txpause = assistant_mconn->to_client_paused_count;
    mtunnel->log.a_rxpause = assistant_mconn->from_client_paused_count;
    mtunnel->log.a_txresume = assistant_mconn->to_client_resume_count;
    mtunnel->log.a_rxresume = assistant_mconn->from_client_resume_count;
    mtunnel->log.a_pauseto = assistant_mconn->to_client_pause_timed_out_count;
    mtunnel->log.a_txpause_time_max_us = assistant_mconn->to_client_pause_time_max_us;
    mtunnel->log.a_txpause_time_max_epoch_us = assistant_mconn->to_client_pause_time_max_epoch_us;
    mtunnel->log.a_txpause_time_total_us = assistant_mconn->to_client_pause_time_total_us;
    mtunnel->log.a_rxpause_time_max_us = assistant_mconn->from_client_pause_time_max_us;
    mtunnel->log.a_rxpause_time_max_epoch_us = assistant_mconn->from_client_pause_time_max_epoch_us;
    mtunnel->log.a_rxpause_time_total_us = assistant_mconn->from_client_pause_time_total_us;

    mtunnel->log.c_disable_read_client_count = client_mconn->disable_read_client_count;
    mtunnel->log.c_enable_read_client_count = client_mconn->enable_read_client_count;
    mtunnel->log.c_disable_read_client_time_max_us = client_mconn->disable_read_client_time_max_us;
    mtunnel->log.c_disable_read_client_time_max_epoch_us = client_mconn->disable_read_client_time_max_epoch_us;
    mtunnel->log.c_disable_read_client_time_total_us = client_mconn->disable_read_client_time_total_us;

    if (mtunnel->err) {
        mtunnel->log.reason = mtunnel->err;
    } else if (mtunnel->log.action && (!strcmp(mtunnel->log.action, "open") || !strcmp(mtunnel->log.action, "active"))) {
        mtunnel->log.reason = OPEN_OR_ACTIVE_CONNECTION;
    } else {
        ZPN_BROKER_ASSERT_SOFT((NULL != mtunnel->log.reason), "%s: Connection does not have a status reason field set", mtunnel->mtunnel_id);
    }

    mtunnel->log.double_encrypt = mtunnel->double_encrypt;
    mtunnel->log.zpn_probe_type = mtunnel->zpn_probe_type;
    mtunnel->log.ip_proto = mtunnel->ip_protocol;

    mtunnel->log.icmp_access_type = client_mconn->icmp_access_type;

    mtunnel->log.async_count = mtunnel->async_count;

    mtunnel->log.drop_udp_framed_data = client_mconn->drop_udp_framed_data;
    mtunnel->log.c_bytes_dropped_udp = client_mconn->bytes_dropped_udp;
    mtunnel->log.a_bytes_dropped_udp = assistant_mconn->bytes_dropped_udp;

    mtunnel->log.allow_all_xport = mtunnel->allow_all_xport;

    if (mtunnel->zpn_broker_mtunnel_type != zbmt_transit_c2c) {
        // for zbmt_transit_c2c the c_tlv_type is provided from origin
        mtunnel->log.c_tlv_type = zpn_tlv_type_str(mtunnel->client_tlv_type);
    }

    // c2c uses a_tlv_type as recieving client tlv. Txn UX should not show it in Assistant section
    mtunnel->log.a_tlv_type = zpn_tlv_type_str(mtunnel->assistant_tlv_type);

#if 0
    if (mtunnel->ip_protocol == IPPROTO_ICMP) {
        char s_ipstr[INET6_ADDRSTRLEN];
        char a_ipstr[INET6_ADDRSTRLEN];

        strcpy(s_ipstr, inet_ntoa(assistant_mconn->icmp_state.s_ip));
        argo_string_to_inet(s_ipstr, &mtunnel->log.s_ip);

        strcpy(a_ipstr, inet_ntoa(assistant_mconn->icmp_state.a_ip));
        argo_string_to_inet(a_ipstr, &mtunnel->log.a_ip);
    }
#endif

    /* Do SLA calculation. */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        const char* s_close = "close";
        if(!mtunnel->log.action) {
            ZPN_LOG(AL_ERROR, "Invalid mtunnel log action field : null");
            return ZPN_RESULT_BAD_ARGUMENT;
        }
        if (!strncmp(mtunnel->log.action, s_close,strlen(s_close))) {
            /* SEE zpn_rpc.h header file for logic/rationale! */
            if (mtunnel->log.startrx_us) {
                if (mtunnel->log.rt_info_rx_us) {
                    mtunnel->log.sla_valid = 1;
                    mtunnel->log.sla_us = (mtunnel->log.rt_info_rx_us - mtunnel->log.startrx_us) - mtunnel->log.learn_us;
                } else if (mtunnel->flag.brk_req_using_path_cache_succeeded) {
                    mtunnel->log.sla_us = (mtunnel->log.brk_req_utoa_tx_us - mtunnel->log.startrx_us);
                    mtunnel->log.sla_valid = 1;
                } else if (mtunnel->log.reason && strncmp(mtunnel->log.reason, "AST", 3)) {
                    if (mtunnel->log.c_tx_end_us) {
                        mtunnel->log.sla_valid = 1;
                        mtunnel->log.sla_us = (mtunnel->log.c_tx_end_us - mtunnel->log.startrx_us) - mtunnel->log.learn_us;
                    }
                }
            }
            int current_thread_id = mtunnel->client_tlv.mconn.fohh_thread_id;
            if (mtunnel->log.sla_valid) {
                // replace negative value with 0
                if( mtunnel->log.sla_us < 0 ) {
                    mtunnel->log.sla_us = 0;
                }

                /* Do some accounting */
                uint64_t sla_ms = mtunnel->log.sla_us / 1000;
                int bucket = histogram_log2_bucket(sla_ms, ZPN_FOHH_WORKER_SLA_STATS_TOTAL_BUCKETS);
                int64_t *bucket_array = &(zpn_fohh_workers[current_thread_id].sla_stats.sla_0_0);
                __sync_fetch_and_add_8(&(bucket_array[bucket]), 1);
            } else {
                __sync_fetch_and_add_8(&(zpn_fohh_workers[current_thread_id].sla_stats.sla_nc), 1);
            }
        }
    }

    /* Log any assistant groups by which it might have been filtered */
    if (mtunnel->log.filter_ast_grp_str_array) {
        for (int i = 0; i < mtunnel->log.filter_ast_grp_str_count; i++) {
            if (mtunnel->log.filter_ast_grp_str_array[i]) {
                ZPN_FREE(mtunnel->log.filter_ast_grp_str_array[i]);
                mtunnel->log.filter_ast_grp_str_array[i] = NULL;
            }
        }
        ZPN_FREE(mtunnel->log.filter_ast_grp_str_array);
        mtunnel->log.filter_ast_grp_str_array = NULL;
    }

    if (mtunnel->log.filter_extranet_locs) {
        for (int i = 0; i < mtunnel->log.filter_extranet_locs_count; i++) {
            if (mtunnel->log.filter_extranet_locs[i]) {
                ZPN_FREE(mtunnel->log.filter_extranet_locs[i]);
                mtunnel->log.filter_extranet_locs[i] = NULL;
            }
        }
        ZPN_FREE(mtunnel->log.filter_extranet_locs);
        mtunnel->log.filter_extranet_locs = NULL;
    }

    /* Deprecated filter_ast_grp and filter_ast_grp_count,
     * Use
     * filter_ast_grp_str_array and filter_ast_grp_str_count
     * instead
     */
    mtunnel->log.filter_ast_grp = mtunnel->assistant_group_gid;
    mtunnel->log.filter_ast_grp_count = mtunnel->assistant_group_gid_count;

    mtunnel->log.filter_ast_grp_str_count = mtunnel->assistant_group_gid_count;
    mtunnel->log.filter_ast_grp_str_array = ZPN_CALLOC(sizeof(char *) * mtunnel->log.filter_ast_grp_str_count);
    for (int i = 0; i < mtunnel->log.filter_ast_grp_str_count; i++) {
        /*
         * int64_t max characters are 18 and uint64_t max characters is 19
         * +1 for null termination
         * using 20 here for the max value
         */
        mtunnel->log.filter_ast_grp_str_array[i] = ZPN_CALLOC(ZPN_BROKER_MTUNNEL_MAX_AST_GRP_SZ * sizeof(char));
        snprintf(mtunnel->log.filter_ast_grp_str_array[i], ZPN_BROKER_MTUNNEL_MAX_AST_GRP_SZ, "%"PRId64, mtunnel->assistant_group_gid[i]);
    }

    mtunnel->log.filter_extranet_locs_count = mtunnel->extranet_locs_count;
    if (mtunnel->log.filter_extranet_locs_count > 0) {
        mtunnel->log.filter_extranet_locs = ZPN_CALLOC(sizeof(char *) * mtunnel->log.filter_extranet_locs_count);
        if (!mtunnel->log.filter_extranet_locs) {
            ZPN_LOG(AL_CRITICAL,"Failed to allocate memory: for filter extranet locs");
            return ZPN_RESULT_NO_MEMORY;
        }
        for (int i = 0; i < mtunnel->log.filter_extranet_locs_count; i++) {
            /*
             * int64_t max characters are 18 and uint64_t max characters is 19
             * +1 for null termination
             * using 20 here for the max value
             */
            mtunnel->log.filter_extranet_locs[i] = ZPN_CALLOC(ZPN_BROKER_MTUNNEL_MAX_AST_GRP_SZ * sizeof(char));
            snprintf(mtunnel->log.filter_extranet_locs[i], ZPN_BROKER_MTUNNEL_MAX_AST_GRP_SZ, "%"PRId64, mtunnel->extranet_locs[i]);
        }
    }

    /* Final hop, if we are last hop and not fwd to broker */
    if ((mtunnel->log.self_hop == (mtunnel->log.hops_count - 1)) && !mtunnel->log.fwd_to_next_hop) {
        hop_type = zpn_trans_hop_type_term;
    } else {
        /* Dont overwrite the case of transit */
        hop_type = mtunnel->log.self_hop == zpn_trans_hop_type_transit?zpn_trans_hop_type_transit:zpn_trans_hop_type_origin ;
    }

    /* Final update to hops before logging */

    res = zpn_broker_client_update_self_hop_log_from_self_mtunnel_log(mtunnel, hop_type);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unable to update current mtunnel log", mtunnel->mtunnel_id);
    }


    if (mtunnel->log.gids) {
        /* This will exist if we are re-logging the mtunnel, which is
         * common for long lived connections */
        ZPN_FREE(mtunnel->log.gids);
    }
    mtunnel->log.gids = NULL;
    mtunnel->log.gids_count = 0;

    if (mtunnel->zpn_broker_mtunnel_type == zbmt_transit_c2c) {
        c_state = zpn_broker_mtunnel_assistant_c_state(mtunnel);
    } else {
        c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    }

    /* get the partitions list from instance id and fill them in the auth log */
    if (c_state && c_state->is_lp_enabled) {

        int64_t partition_gids[ZPATH_MAX_PARTITIONS];
        size_t count = ZPATH_MAX_PARTITIONS;
        mtunnel->log.g_part_gid_count = 0;

        res = zpath_partition_fill_log(mtunnel->log.g_brk, mtunnel->log.g_cst, partition_gids, &count, &(mtunnel->log.g_cst_part_gid));
        if(res || !count) {
            mtunnel->log.g_inst_part_gids = NULL;
            mtunnel->log.g_part_gid_count = 0;
            if(res) {
                ZPN_LOG(AL_ERROR, "populating partitions for this instance failed with error %d", res);
            }
        } else {
            mtunnel->log.g_inst_part_gids = ZPN_MALLOC(sizeof(int64_t) * count);
            if(!mtunnel->log.g_inst_part_gids) {
                ZPN_LOG(AL_CRITICAL,"memory allocation failed");
                return ZPN_RESULT_NO_MEMORY;
            }
            for (size_t iter = 0; iter < count; iter++) {
                 mtunnel->log.g_inst_part_gids[iter] = partition_gids[iter];
            }
            mtunnel->log.g_part_gid_count = (int16_t) count;
        }
    }

    mtunnel->log.hostname = "";
    if (mtunnel->auth_request_x) {
        const char *type = argo_object_get_type(mtunnel->auth_request_x);
        if (type) {
            if (!strcmp(type, "zpn_client_authenticate")) {
                struct zpn_client_authenticate *auth = mtunnel->auth_request_x->base_structure_void;
                if (auth && auth->hostname) mtunnel->log.hostname = auth->hostname;
            } else if (!strcmp(type, "zpn_machine_tunnel_client_authenticate")) {
                struct zpn_machine_tunnel_client_authenticate *auth = mtunnel->auth_request_x->base_structure_void;
                if (auth && auth->hostname) mtunnel->log.hostname = auth->hostname;
            }
        }
    }

    zpn_broker_client_free_aae_mtunnel_log(&mtunnel->log.aae_conclusion_user_profiles, mtunnel->log.aae_conclusion_user_profile_count,
                                           &mtunnel->log.aae_conclusion_device_profiles, mtunnel->log.aae_conclusion_device_profile_count);
    zpn_broker_client_fill_aae_mtunnel_log(c_state, &mtunnel->log.aae_conclusion_user_profiles, &mtunnel->log.aae_conclusion_user_profile_count,
                                           &mtunnel->log.aae_conclusion_device_profiles, &mtunnel->log.aae_conclusion_device_profile_count);
    if (ZPN_BROKER_IS_PRIVATE()) {
        mtunnel->log.g_pbrk = g_broker_common_cfg->private_broker.broker_id;
        size_t ii = 0;
        int64_t siem_ids[ZPN_MAX_SIEM_IDS];
        size_t siem_ids_count = ZPN_MAX_SIEM_IDS;
        if (c_state) {
            zpn_broker_siem_get_siem_gids_trans_log(c_state, &(mtunnel->log), siem_ids, &siem_ids_count);
            if (siem_ids_count) {
                mtunnel->log.gids = ZPN_CALLOC(siem_ids_count * (sizeof(*(mtunnel->log.gids))));
                for (ii = 0; ii < siem_ids_count; ii++) {
                    mtunnel->log.gids[ii] = siem_ids[ii];
                }
                mtunnel->log.gids_count = siem_ids_count;
            }
        }
    }

    /* Increment stats counter before log throttling */
    if (mtunnel->err) {
        zpn_broker_mtunnel_stats_inc_error_counter(mtunnel->err);
    }

    int* log_throttling_on = NULL;
    if (mtunnel->err) {
        log_throttling_on = zhash_table_lookup(transaction_log_throttling_errors_hash, mtunnel->err, strlen(mtunnel->err) + 1, NULL);
    }

    if (log_throttling_on && *log_throttling_on) {
        if (c_state) {
            res = zpn_domain_log_hash_alloc(c_state);
            if (res != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_DEBUG, "%s: Unable to allocate log hash for domain: %s, user: %s", mtunnel->mtunnel_id, mtunnel->log.domain, c_state->log.c_uid);
                return res;
            }
            if (c_state->domain_log_hash) {
                // Check if a transaction log has been generated for this domain and customer
                // in the last 1 minute. If no, generate a log and add the domain to this hash.
                // The hash is maintained per c_state and is flushed every minute.
                //
                int* domain_logged;

                zpn_c_state_lock(c_state);

                domain_logged = zhash_table_lookup(c_state->domain_log_hash, mtunnel->log.domain , strlen(mtunnel->log.domain) + 1, NULL);
                if (domain_logged) {
                    ZPN_DEBUG_MTUNNEL("%s: Skipping transaction log for domain = %s, user = %s, transaction log throttling on for error %s",
                                      mtunnel->mtunnel_id, mtunnel->log.domain, c_state->log.c_uid, mtunnel->err);
                    zpn_c_state_unlock(c_state);
                    return ZPATH_RESULT_NO_ERROR;
                } else {
                    char *log_domain = (char *)mtunnel->log.domain; /* This is ok because internally hash makes a copy */
                    res = zhash_table_store(c_state->domain_log_hash, log_domain, strlen(log_domain) + 1, 0, &zpn_transaction_log_throttling_on);
                    if (res) {
                        ZPN_LOG(AL_DEBUG, "%s: Failed to store domain name in hash, err: %s, domain: %s, user: %s",
                                mtunnel->mtunnel_id, zpath_result_string(res), mtunnel->log.domain, c_state->log.c_uid);
                        zpn_c_state_unlock(c_state);
                        return res;
                    }
                }

                zpn_c_state_unlock(c_state);
            }
        }
    }

    if (zpn_transaction_collection) {
        if ((res = argo_log_structure_immediate(zpn_transaction_collection,
                                                argo_log_priority_info,
                                                0,
                                                "transaction",
                                                zpn_trans_log_description,
                                                &(mtunnel->log)))) {
            ZPN_LOG(AL_WARNING, "%s: Could not log transaction: %s", mtunnel->mtunnel_id, zpath_result_string(res));
        }
    }

    /* Private broker does not log directly. */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        return ZPATH_RESULT_NO_ERROR;
    }

    /* client state could be NULL here (ex: if this transaction was rate-limited...which is OK
     * SIEM policy is always obtained using customer_id, and here it is inside mtunnel->log->g_cst.
     * Even if SIEM policy lookup were to fail, that is fine...just this log wont get sent to customer
     */
    zpn_broker_siem_trans_log(c_state, &(mtunnel->log));

    /* Will be called only for Public broker */
    int throttle_check_result = ZPN_NATURAL_SEND_TRANS_LOG;
    if(c_state){
        zpn_c_state_lock(c_state);
        throttle_check_result = zpn_natural_log_throttle_check(mtunnel,c_state);
        zpn_c_state_unlock(c_state);
    }
    if(throttle_check_result == ZPN_NATURAL_SEND_TRANS_LOG)
    {
        for (int repeat = 0; repeat < config_log_repeats; repeat++) {
        res = zpath_customer_log_struct(mtunnel->customer_id,
                                        zpath_customer_log_type_zpn_transaction,
                                        "transaction",
                                        NULL,
                                        NULL,
                                        NULL,
                                        NULL,
                                        zpn_trans_log_description,
                                        &(mtunnel->log));

        if (res) {
            ZPN_LOG(AL_WARNING, "zpn broker mtunnel log fail");
            }
        }
    }
    if(mtunnel->log.g_inst_part_gids && c_state && c_state->is_lp_enabled) {
        ZPN_FREE(mtunnel->log.g_inst_part_gids);
        mtunnel->log.g_inst_part_gids = NULL;
        mtunnel->log.g_part_gid_count = 0;
    }
    if(mtunnel->log.slogger_info){
        ZPN_FREE(mtunnel->log.slogger_info);
        mtunnel->log.slogger_info = NULL;
    }
    return res;
}

static int
zpn_broker_mtunnel_show_mtunnels_limit(struct zpath_debug_state*  request_state,
                                       const char **              query_values,
                                       int                        query_value_count,
                                       void*                      cookie)
{
    int64_t customer_gid = query_values[0] ? strtoll(query_values[0], NULL, 10) : 0;
    int64_t limit = zpn_broker_get_max_mtunnels_per_user(customer_gid);

    ZDP("Customer GID %"PRId64": mtunnels limit %"PRId64"\n", customer_gid, limit);

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_broker_mtunnel_set_test_mtunnels_limit(struct zpath_debug_state*  request_state,
                                           const char **              query_values,
                                           int                        query_value_count,
                                           void*                      cookie)
{
    if (!query_values[0]) {
        ZDP("Need 'customer_gid' as an argument\n");
        return ZPATH_RESULT_ERR;
    }
    if (!query_values[1]) {
        ZDP("Need 'limit' as an argument\n");
        return ZPATH_RESULT_ERR;
    }

    zpn_broker_test_mtunnels_limit_customer_gid = strtoll(query_values[0], NULL, 10);
    zpn_broker_test_mtunnels_limit_value = strtoll(query_values[1], NULL, 10);

    ZDP("Customer GID %"PRId64": set mtunnels limit to %"PRId64"\n",
            zpn_broker_test_mtunnels_limit_customer_gid,
            zpn_broker_test_mtunnels_limit_value);
    ZDP("REMEMBER to clear the limit after the testing\n");

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_broker_mtunnel_clear_test_mtunnels_limit(struct zpath_debug_state*  request_state,
                                            const char **              query_values,
                                            int                        query_value_count,
                                            void*                      cookie)
{
    zpn_broker_test_mtunnels_limit_customer_gid = -1;
    zpn_broker_test_mtunnels_limit_value = -1;

    ZDP("mtunnels limit is cleared\n");

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_pbroker_mtunnel_dump_promoted_stats(struct zpath_debug_state*  request_state,
                                const char **              query_values,
                                int                        query_value_count,
                                void*                      cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_pbroker_mtunnel_stats_description,
                                                    &mstats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_broker_mtunnel_log_repeats(struct zpath_debug_state*  request_state,
                               const char **              query_values,
                               int                        query_value_count,
                               void*                      cookie)
{
    int new_value;

    if (!query_values[0]) {
        ZDP("Need 'count' as an argument for setting log repeats\n");
        return ZPATH_RESULT_ERR;
    }

    new_value = strtol(query_values[0], NULL, 0);
    if (new_value < 0) {
        ZDP("Count >= 0 please\n");
        return ZPATH_RESULT_ERR;
    }
    config_log_repeats = new_value;;
    ZDP("Logging every transaction log %d times\n", config_log_repeats);

    return ZPATH_RESULT_NO_ERROR;
}

#define ZPN_BROKER_MTUNNEL_ONE_DAY_IN_SECONDS (24 * 3600)
#define ZPN_BROKER_MTUNNEL_ONE_HOUR_IN_SECONDS (60 * 60)
#define ZPN_BROKER_MTUNNEL_ONE_MIN_IN_SECONDS 60
#define ZPN_BROKER_MTUNNEL_MAX_DAY_STR_LEN 21
#define ZPN_BROKER_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN 5
#define ZPN_BROKER_MTUNNEL_MAX_UPTIME_BUF_LEN 33
static void
zpn_broker_mtunnel_dump_mtunnel(struct zpath_debug_state*  request_state,
                                const char*                prefix_str,
                                struct zpn_broker_mtunnel* mt)
{
    int64_t current_us = monotime_us();

    int64_t day = 0;
    int64_t hour = 0;
    int64_t min = 0;
    int64_t sec = 0;
    char    day_str[ZPN_BROKER_MTUNNEL_MAX_DAY_STR_LEN];
    char    hour_str[ZPN_BROKER_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    min_str[ZPN_BROKER_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[ZPN_BROKER_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    uptime_str[ZPN_BROKER_MTUNNEL_MAX_UPTIME_BUF_LEN];

    sec =((current_us - mt->start_us) / 1000000);
    day = sec/ZPN_BROKER_MTUNNEL_ONE_DAY_IN_SECONDS;
    sec %= ZPN_BROKER_MTUNNEL_ONE_DAY_IN_SECONDS;

    hour = sec/ZPN_BROKER_MTUNNEL_ONE_HOUR_IN_SECONDS;
    sec %= ZPN_BROKER_MTUNNEL_ONE_HOUR_IN_SECONDS;

    min = sec/ZPN_BROKER_MTUNNEL_ONE_MIN_IN_SECONDS;
    sec %= ZPN_BROKER_MTUNNEL_ONE_MIN_IN_SECONDS;

    snprintf_nowarn(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf_nowarn(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf_nowarn(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf_nowarn(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf_nowarn(uptime_str, ZPN_BROKER_MTUNNEL_MAX_UPTIME_BUF_LEN, "%s%s%s%s", (day==0)? "": day_str,
                                                                            (hour==0 && day==0)? "": hour_str,
                                                                            (day==0 && hour==0 && min==0)? "": min_str,
                                                                            (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    ZDP("%s %s uptime %s", prefix_str,  mt->mtunnel_id, uptime_str);
}

static int
zpn_broker_mtunnel_dump_mtunnels(struct zpath_debug_state*  request_state,
                                 const char **              query_values,
                                 int                        query_value_count,
                                 void*                      cookie)
{

    int bucket_iter;

    for (bucket_iter = 0; bucket_iter < ZPN_BROKER_BUCKETS; bucket_iter++) {
        struct zpn_broker_bucket *bucket = &(broker.buckets[bucket_iter]);
        struct zpn_broker_mtunnel *mt;

        pthread_mutex_lock(&(bucket->lock));

        for (mt = TAILQ_FIRST(&(bucket->mtunnel_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            mtunnel_lock(mt);
            zpn_broker_mtunnel_dump_mtunnel(request_state, "Active Mtunnel", mt);
            mtunnel_unlock(mt);
        }

        for (mt = TAILQ_FIRST(&(bucket->reaped_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            mtunnel_lock(mt);
            zpn_broker_mtunnel_dump_mtunnel(request_state, "Reaped Mtunnel", mt);
            mtunnel_unlock(mt);
        }

        pthread_mutex_unlock(&(bucket->lock));
    }

    return ZPN_RESULT_NO_ERROR;
}


int
zpn_broker_mtunnel_init()
{
    int res;

    res = zpn_broker_mtunnel_stats_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to init broker mtunnel stats module");
        goto done;
    }

    if (!(natural_transaction_throttle_logs_stats_description = argo_register_global_structure(NATURAL_TRANSACTION_THROTTLE_LOGS_STATS_HELPER))) {
        ZPN_LOG(AL_NOTICE, "Unable to create natural log throttle stats desc");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("show the limit of mtunnels per user",
                                  "/zpn/broker/mtunnels_limit",
                                  zpn_broker_mtunnel_show_mtunnels_limit,
                                  NULL,
                                  "customer_gid", "Customer GID",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/broker/mtunnels_limit to debug: %s", zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    if (zpn_broker_is_dev_environment()) {
        res = zpath_debug_add_write_command("set the limit of mtunnels for one customer, override previous setting, for testing only",
                                    "/zpn/broker/mtunnels_limit/test/set",
                                    zpn_broker_mtunnel_set_test_mtunnels_limit,
                                    NULL,
                                    "customer_gid", "Customer GID",
                                    "limit", "the mtunnels limit",
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register /zpn/broker/mtunnels_limit/test/set: %s", zpn_result_string(res));
            res = ZPN_RESULT_ERR;
            goto done;
        }

        res = zpath_debug_add_write_command("clear the limit of mtunnels which is set for testing",
                                    "/zpn/broker/mtunnels_limit/test/clear",
                                    zpn_broker_mtunnel_clear_test_mtunnels_limit,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register /zpn/broker/mtunnels_limit/test/clear: %s", zpn_result_string(res));
            res = ZPN_RESULT_ERR;
            goto done;
        }
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zpath_debug_add_read_command("dump the stats of mtunnel on the pbroker",
                                    "/pbroker/mtunnel/promoted/stats",
                                    zpn_pbroker_mtunnel_dump_promoted_stats,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register /pbroker/mtunnel/promoted/stats to debug: %s",
                    zpn_result_string(res));
            res = ZPN_RESULT_ERR;
            goto done;
        }
    }

    res = zpath_debug_add_write_command("Send duplicate transaction logs",
                                  "/broker/log/transaction/repeats",
                                  zpn_broker_mtunnel_log_repeats,
                                  NULL,
                                  "count", "The number of times to repeat transmit a transaction log",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /broker/log/transaction/repeats to debug interface: %s",
                zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }


    if (zpn_broker_is_dev_environment()) {
        res = zpath_debug_add_admin_command("Unit Test: Drop a certain number of bind ack messages to connector",
                                    "/broker/assistant/mtunnel/ut/drop_bind_ack_msgs",
                                    zpn_broker_assistant_mtunnel_ut_drop_bind_ack_msgs,
                                    NULL,
                                    "count", "Number of bind ack messages to drop",
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Unable to register /broker/assistant/mtunnel/ut/drop_bind_ack_msgs");
            goto done;
        }

        res = zpath_debug_add_write_command("Send duplicate transaction logs",
                                      "/broker/assistant/mtunnel/dump/mtunnels",
                                      zpn_broker_mtunnel_dump_mtunnels,
                                      NULL,
                                      NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register /broker/log/transaction/repeats to debug interface: %s",
                              zpn_result_string(res));
            res = ZPN_RESULT_ERR;
            goto done;

        }
    }

done:
    return res;

}

void zpn_broker_mtunnel_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    TAILQ_INIT(&(free_q.mt_list));
}

void init_mtunnel_stats_ptr(struct generic_mtunnel_stats *generic_stats, enum zpn_client_type client_type)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* Initialize the common pointers */
    generic_stats->total_mtunnels_created = &gs->private_broker_state->mtunnel_stats.total_mtunnels_created;
    generic_stats->total_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.total_mtunnels_freed;
    generic_stats->total_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.total_mtunnels_reaped_in;
    generic_stats->total_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.total_mtunnels_reaped_out;
    generic_stats->total_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.total_mtunnels_init_failed;
    generic_stats->total_mtunnels_active = &gs->private_broker_state->mtunnel_stats.total_mtunnels_active;
    generic_stats->approx_mtunnels_peak_active = &gs->private_broker_state->mtunnel_stats.approx_mtunnels_peak_active;
    generic_stats->total_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.total_mtunnels_c2c_regex_bypass;

    switch(client_type) {
    case zpn_client_type_zapp:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.zcc_mtunnels_c2c_regex_bypass;
        break;

    case zpn_client_type_machine_tunnel:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.mt_mtunnels_c2c_regex_bypass;
        break;

    case zpn_client_type_zapp_partner:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.zp_mtunnels_c2c_regex_bypass;
        break;

    case zpn_client_type_edge_connector:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.ec_mtunnels_c2c_regex_bypass;
        break;

    case zpn_client_type_vdi:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.vdi_mtunnels_c2c_regex_bypass;
        break;

    case zpn_client_type_branch_connector:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.bc_mtunnels_c2c_regex_bypass;
        break;

    default:
        generic_stats->client_mtunnels_created = &gs->private_broker_state->mtunnel_stats.other_mtunnels_created;
        generic_stats->client_mtunnels_freed = &gs->private_broker_state->mtunnel_stats.other_mtunnels_freed;
        generic_stats->client_mtunnels_reaped_in = &gs->private_broker_state->mtunnel_stats.other_mtunnels_reaped_in;
        generic_stats->client_mtunnels_reaped_out = &gs->private_broker_state->mtunnel_stats.other_mtunnels_reaped_out;
        generic_stats->client_mtunnels_init_failed = &gs->private_broker_state->mtunnel_stats.other_mtunnels_init_failed;
        generic_stats->client_mtunnels_active = &gs->private_broker_state->mtunnel_stats.other_mtunnels_active;
        generic_stats->client_mtunnels_c2c_regex_bypass = &gs->private_broker_state->mtunnel_stats.other_mtunnels_c2c_regex_bypass;
        break;
    }
}

struct zpn_broker_mtunnel *zpn_broker_mtunnel_alloc(enum zpn_client_type client_type)
{
    struct zpn_broker_mtunnel *mtunnel = NULL;
    int64_t incarnation = 0;

    pthread_mutex_lock(&(free_q.lock));

    if ((mtunnel = TAILQ_FIRST(&(free_q.mt_list)))) {
        TAILQ_REMOVE(&(free_q.mt_list), mtunnel, bucket_list);
        free_q.stats.free_queue_count--;
        incarnation = mtunnel->incarnation;
        incarnation++;
        memset(mtunnel, 0, sizeof(struct zpn_broker_mtunnel));
        mtunnel->incarnation = incarnation;
        mtunnel->zpn_broker_mtunnel_type = zbmt_normal;
    } else {
        mtunnel = (struct zpn_broker_mtunnel *)ZPN_CALLOC(sizeof(struct zpn_broker_mtunnel));
        if (mtunnel) {
            free_q.stats.allocations++;
            memset(mtunnel, 0, sizeof(struct zpn_broker_mtunnel));
            mtunnel->incarnation = 1;
            mtunnel->zpn_broker_mtunnel_type = zbmt_normal;
        }
    }

    pthread_mutex_unlock(&(free_q.lock));
    if(mtunnel) mtunnel->client_type = client_type;

    if (ZPN_BROKER_IS_PRIVATE()) {
        struct generic_mtunnel_stats gstats;
        init_mtunnel_stats_ptr(&gstats, client_type);

        if(mtunnel) {
            /* Aggregate mtunnel stats */
            ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_created, 1);
            ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_active, 1);

            /* Client-type specific mtunnels stats */
            ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_created, 1);
            ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_active, 1);
        } else {
            ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_init_failed, 1);
            ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_init_failed, 1);
        }
    }

    return mtunnel;
}


void zpn_broker_mtunnel_free(struct zpn_broker_mtunnel *mtunnel)

{
    enum zpn_client_type client_type = mtunnel->client_type;
    mtunnel->client_type = zpn_client_type_invalid;

    if (mtunnel->auth_request_x) {
        argo_object_release(mtunnel->auth_request_x);
        mtunnel->auth_request_x = NULL;
    }

    pthread_mutex_lock(&(free_q.lock));
    TAILQ_INSERT_TAIL(&(free_q.mt_list), mtunnel, bucket_list);
    free_q.stats.free_queue_count++;
    pthread_mutex_unlock(&(free_q.lock));
    if (ZPN_BROKER_IS_PRIVATE()) {
        struct generic_mtunnel_stats gstats;
        init_mtunnel_stats_ptr(&gstats, client_type);

        ZPN_ATOMIC_FETCH_ADD8(gstats.total_mtunnels_freed, 1);
        ZPN_ATOMIC_FETCH_SUB8(gstats.total_mtunnels_active, 1);

        ZPN_ATOMIC_FETCH_ADD8(gstats.client_mtunnels_freed, 1);
        ZPN_ATOMIC_FETCH_SUB8(gstats.client_mtunnels_active, 1);
    }
}

int zpn_broker_tune_track_application(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    track_g_app = strtoll(query_values[0], NULL, 0);
    zpath_debug_cb_printf_response(request_state, "Setting tracking application gid = %ld\n", (long)track_g_app);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_tune_track_usr(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    track_g_usr = strtoll(query_values[0], NULL, 0);
    zpath_debug_cb_printf_response(request_state, "Setting tracking user gid = %ld\n", (long)track_g_usr);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_tune_show(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count,
                         void *cookie)
{
    zpath_debug_cb_printf_response(request_state, "Current tracking application gid = %ld\n", (long)track_g_app);
    zpath_debug_cb_printf_response(request_state, "Current tracking user gid = %ld\n", (long)track_g_usr);

    return ZPATH_RESULT_NO_ERROR;
}

struct zpn_broker_client_fohh_state *zpn_broker_mtunnel_assistant_c_state(struct zpn_broker_mtunnel *mtunnel) {
    struct zpn_broker_client_fohh_state *c_state = NULL;

    if (mtunnel->assistant_tlv_type == zpn_fohh_tlv) {

        if (mtunnel->assistant_f_conn && mtunnel->assistant_tlv.mconn.local_owner_incarnation ==
                                                 fohh_connection_incarnation(mtunnel->assistant_f_conn)) {
            INLINE_VOID_INFERENCE(c_state, fohh_connection_get_dynamic_cookie(mtunnel->assistant_f_conn));
        }

    } else {
        struct zrdt_conn *z_conn = mtunnel->assistant_z_conn;

        if (z_conn) {
            INLINE_VOID_INFERENCE(c_state, zrdt_conn_get_dynamic_cookie(z_conn));
        }
    }

    return c_state;
}

struct zpn_broker_client_fohh_state *zpn_broker_mtunnel_client_c_state(struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_broker_client_fohh_state *c_state = NULL;

    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = zpn_broker_mtunnel_client_f_conn(mtunnel);

        if (f_conn) {
            INLINE_VOID_INFERENCE(c_state, fohh_connection_get_dynamic_cookie(f_conn));
        }
    } else {
        struct zrdt_conn *z_conn = mtunnel->client_z_conn;

        if (z_conn) {
            INLINE_VOID_INFERENCE(c_state, zrdt_conn_get_dynamic_cookie(z_conn));
        }
    }

    return c_state;
}

struct zpn_tlv *zpn_broker_mtunnel_client_tlv(struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_mconn *mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    int64_t incarnation;

    return zpn_mconn_get_local_owner(mconn, &incarnation);
}

struct zpn_mconn *zpn_broker_mtunnel_client_mconn(struct zpn_broker_mtunnel *mtunnel)
{
    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        return &(mtunnel->client_tlv.mconn);
    } else {
        return &(mtunnel->client_tlv_zrdt.mconn);
    }
}

struct zpn_tlv *zpn_broker_mtunnel_assistant_tlv(struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_broker_assistant_fohh_state *a_state;

    if (mtunnel->assistant_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = mtunnel->assistant_f_conn;

        a_state = fohh_connection_get_dynamic_cookie(f_conn);
        return &(a_state->tlv_state.tlv);
    } else {
        struct zrdt_conn *z_conn = mtunnel->assistant_z_conn;

        a_state = zrdt_conn_get_dynamic_cookie(z_conn);
        return &(a_state->zrdt_tlv_state.tlv);
    }
}

struct zpn_mconn *zpn_broker_mtunnel_assistant_mconn(struct zpn_broker_mtunnel *mtunnel)
{
    if (mtunnel->assistant_tlv_type == zpn_fohh_tlv) {
        return &(mtunnel->assistant_tlv.mconn);
    } else {
        return &(mtunnel->assistant_tlv_zrdt.mconn);
    }
}

struct fohh_connection *zpn_broker_mtunnel_client_f_conn(struct zpn_broker_mtunnel *mtunnel)
{
    if (!mtunnel->_client_f_conn) {
        return NULL;
    }

    if (mtunnel->client_tlv.mconn.local_owner_incarnation != fohh_connection_incarnation(mtunnel->_client_f_conn)) {
        /* Connection changed under us */
        return NULL;
    }

    return mtunnel->_client_f_conn;
}

struct zrdt_conn *zpn_broker_mtunnel_client_z_conn(struct zpn_broker_mtunnel *mtunnel)
{
    if (!mtunnel->client_z_conn) {
        return NULL;
    }

    if (mtunnel->client_tlv_zrdt.mconn.local_owner_incarnation != zrdt_conn_incarnation(mtunnel->client_z_conn)) {
        /* Connection changed under us */
        return NULL;
    }

    return mtunnel->client_z_conn;
}

struct zpn_broker_mtunnel *zpn_broker_get_global_owner_tlv(struct zpn_tlv *tlv, int tag_id)
{
    if (tlv->type == zpn_fohh_tlv) {
        struct zpn_fohh_tlv *fohh_tlv = (struct zpn_fohh_tlv *)tlv;
        return zpn_fohh_tlv_get_global_owner(fohh_tlv, tag_id);
    } else if (tlv->type == zpn_zrdt_tlv) {
        struct zpn_zrdt_tlv *zrdt_tlv = (struct zpn_zrdt_tlv *)tlv;
        return zpn_zrdt_tlv_get_global_owner(zrdt_tlv, tag_id);
    } else {
        return NULL;
    }
}

struct event_base *zpn_broker_client_ev_base(struct zpn_broker_mtunnel *mtunnel)
{
    struct event_base* client_ev_base;

    if (mtunnel->client_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection* client_f_conn;
        client_f_conn = zpn_broker_mtunnel_client_f_conn(mtunnel);
        if (!client_f_conn) return NULL;
        client_ev_base = fohh_connection_event_base(client_f_conn);
    } else {
        struct zrdt_conn *client_z_conn;
        struct zdtls_session *z_sess;
        client_z_conn = zpn_broker_mtunnel_client_z_conn(mtunnel);
        if (!client_z_conn) return NULL;
        z_sess = zrdt_conn_get_datagram_tx_cookie(client_z_conn);
        if (!z_sess) return NULL;
        client_ev_base = zdtls_session_get_event_base(z_sess);
    }

    return client_ev_base;
}

/*
 * create mtunnel for c2c mtunnel
 */
struct zpn_broker_mtunnel *mtunnel_allocate_transit_and_bucket_lock(const struct zpn_broker_request *req,
                                                                    struct zpn_broker_client_fohh_state *c_state,
                                                                    int *bucket_id) {
    size_t mtunnel_id_len;
    char *mtunnel_err = NULL;
    const char *connection_dbg_str = "";
    struct zpn_broker_mtunnel *mtunnel = NULL;
    struct zpn_broker_bucket *bucket;
    const char *application_name = req->domain;
    const char *application_type = req->app_type;
    const uint8_t *tunnel_bin = &(c_state->tunnel_id_bin[0]);
    const char *user_id = &(c_state->user_id[0]);
    int64_t customer_id = c_state->customer_gid;
    int c_state_fohh_thread_id = c_state_get_fohh_thread_id(c_state);
    uint8_t mtunnel_bin[ZPN_MTUNNEL_ID_BYTES];

    int res;

    if (!req->mtunnel_id) {
        ZPN_LOG(AL_WARNING, "Received broker request without mtunnel id from %s", connection_dbg_str);
        mtunnel_err = AST_MT_SETUP_INVALID_REQUEST;
        goto DONE;
    }

    mtunnel_id_len = strlen(req->mtunnel_id);
    if (mtunnel_id_len < ZPN_MTUNNEL_ID_BYTES_TEXT_MIN) {
        ZPN_LOG(AL_WARNING, "%s: Bad length from %s", req->mtunnel_id, connection_dbg_str);
        mtunnel_err = AST_MT_SETUP_INVALID_REQUEST;
        goto DONE;
    }

    int64_t mtunnel_limit = 0, mtunnel_number = 0;
    if (ZPN_BROKER_IS_PUBLIC() && zpn_broker_exceed_mtunnel_limit_check(c_state, &mtunnel_number, &mtunnel_limit)) {
        ZPN_LOG(AL_WARNING, "Number of requesting for transit mtunnel %"PRId64" exceeds the limit %"PRId64,
                mtunnel_number, mtunnel_limit);
        mtunnel_err = AST_MT_SETUP_ERR_EXCEED_LIMIT;
        goto DONE;
    }

    // allocate tunnel
    mtunnel = zpn_broker_mtunnel_alloc(zpn_client_type_invalid);

    if (mtunnel) {
        mtunnel->zpn_broker_mtunnel_type = zbmt_transit_c2c;
        mtunnel->log.c2c = 1;

        mtunnel->c_state_fohh_thread_id = c_state_fohh_thread_id;
        mtunnel->req_app_name = ZPN_STRDUP(application_name, strlen(application_name));
        mtunnel->req_app_type = zpn_app_type_from_str(application_type);
        /*
         * ET-85814: The static analyzer is flagging this because it's only seeing the 5 explicit initializations
         * without understanding the full context of how ZMT_COUNT is defined & how C handles designated initializers.
         * Therefore, this is likely a false positive because the array is likely properly sized through ZMT_COUNT
         * definition & Uninitialized elements will be NULL pointers, which is a valid state. The access pattern is
         * intentional and safe as long as ZMT_COUNT is defined correctly
         */
        /* coverity[overrun-buffer-val : FALSE] */
        mtunnel->log.app_type = zmt_string[mtunnel->req_app_type];

        // store the parent mtunnel to map it back in broker_request_ack and transit requests
        /*
         * ET-85162: The mtunnel is getting allocated in this function itself, hence no two threads
         * could potentially be updating this mtunnel simultaneously. Hence, we don't really need a
         * lock here.
         */
        /* coverity[missing_lock: FALSE] */
        mtunnel->mtunnel_id_parent = ZPN_STRDUP(req->mtunnel_id, strlen(req->mtunnel_id));

        // set the log_s time, so it doesnt log until it is connected
        mtunnel->log_s = epoch_s();
        mtunnel->start_us = monotime_us();

        if (user_id) {
            snprintf(mtunnel->user_id, sizeof(mtunnel->user_id), "%s", user_id);
            mtunnel->log.c_uid = mtunnel->user_id;
        } else {
            mtunnel->user_id[0] = 0;
        }
        mtunnel->o_user_id[0] = 0;

        mtunnel->customer_id = customer_id;
        if (!mtunnel->req_app_name)
            goto DONE_MSG;

        mtunnel->tunnel_id = ZPN_CALLOC(ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
        mtunnel->mtunnel_id = ZPN_CALLOC(ZPN_MTUNNEL_ID_BYTES_TEXT + 1);

        if (!mtunnel->tunnel_id || !mtunnel->mtunnel_id)
            goto DONE_MSG;

        /* mtunnel ID is combination of tunnel ID + more random bytes, both base64, delimited by a comma */
        res = RAND_bytes((unsigned char *)&mtunnel_bin, ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_LOG(AL_CRITICAL, "Crypto Random Generator not seeded!");
            goto DONE;
        }
        base64_encode_binary(&(mtunnel->mtunnel_id[0]), tunnel_bin, ZPN_TUNNEL_ID_BYTES);
        int ix = base64_encoded_size(ZPN_TUNNEL_ID_BYTES);
        mtunnel->mtunnel_id[ix] = ',';
        base64_encode_binary(&(mtunnel->mtunnel_id[ix + 1]), mtunnel_bin, ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);

        memcpy(mtunnel->tunnel_id, mtunnel->mtunnel_id, ix);

        /* Our hash is actually a cityhash of the STRING version of the tunnel_id. */
        mtunnel->mtunnel_id_hash = CityHash64((const char *)mtunnel->mtunnel_id, strlen(mtunnel->mtunnel_id));

        mtunnel->broker_id = ZPN_BROKER_GET_GID();
        mtunnel->broker_group_id = ZPN_BROKER_GET_GROUP_GID();
        mtunnel->log.startrx_us = epoch_us();

        *bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
        bucket = &(broker.buckets[*bucket_id]);

        pthread_mutex_lock(&(bucket->lock));

        res = argo_hash_store_with_hash(bucket->mtunnel_by_id,
                                        mtunnel->mtunnel_id,
                                        strlen(mtunnel->mtunnel_id),
                                        mtunnel->mtunnel_id_hash,
                                        ARGO_HASH_EXTERNAL_DATA,
                                        mtunnel);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not store mtunnel in bucket hash table");

            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }
        TAILQ_INSERT_HEAD(&(bucket->mtunnel_list), mtunnel, bucket_list);

        mtunnel->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
        mtunnel->termination_started = 0;

        res = zpn_mconn_fohh_tlv_init(&(mtunnel->client_tlv), mtunnel, mconn_fohh_tlv_c);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initiating client fohh_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_zrdt_tlv_init(&(mtunnel->client_tlv_zrdt), mtunnel, mconn_zrdt_tlv_c);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initiating client zrdt_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_fohh_tlv_init(&(mtunnel->assistant_tlv), mtunnel, mconn_fohh_tlv_s);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initiating assitant fohh_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_zrdt_tlv_init(&(mtunnel->assistant_tlv_zrdt), mtunnel, mconn_zrdt_tlv_s);
        if (res) {
            ZPN_LOG(AL_ERROR, "error initiating assistatnt zrdt_tlv mconn");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->client_tlv.mconn), 0, mtunnel, NULL, 0, &client_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner.");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->client_tlv_zrdt.mconn), 0, mtunnel, NULL, 0,
                                         &client_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner for zrdt.");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->assistant_tlv.mconn), 0, mtunnel, NULL, 0,
                                         &assistant_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind assistant global owner.");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->assistant_tlv_zrdt.mconn), 0, mtunnel, NULL, 0,
                                         &assistant_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not bind assistant global owner for zrdt.");
            pthread_mutex_unlock(&(bucket->lock));
            goto DONE;
        }

        mtunnel->log.action = "open";
    }

    return mtunnel;

DONE_MSG:
    ZPN_LOG(AL_CRITICAL, "Probable allocation failure creating mtunnel");
DONE:
    if (mtunnel) {
        ZPN_FREE_AND_NULL(mtunnel->req_app_name);
        ZPN_FREE_AND_NULL(mtunnel->db_app_name);
        ZPN_FREE_AND_NULL(mtunnel->mtunnel_id);
        ZPN_FREE_AND_NULL(mtunnel->tunnel_id);
        ZPN_FREE_AND_NULL(mtunnel->err);
        ZPN_FREE_AND_NULL(mtunnel->o_dwgs);

        ZPN_DEBUG_ZINS("free mtunnel: mtunnel_allocate_transit_and_bucket_lock");
        zpn_broker_mtunnel_free(mtunnel);
    }
    /* Send broker_request_ack to report error */
    if (mtunnel_err) {
        ZPN_LOG(AL_ERROR, "transit_mtunnel_create error %s", mtunnel_err);
    }

    return NULL;
}

int64_t zpn_broker_config_get_fohh_connection_disable_read_config_flag(int64_t customer_id)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
                                                            &config_value,
                                                            ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_id,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("fohh connection read disable (block) feature config for customer %ld, gid %ld, root gid %ld is %ld",
                          (long)customer_id,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)root_customer_gid,
                          (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
                                                            &config_value,
                                                            ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("fohh connection read disable (block) feature config for %ld pse, grp gid %ld customer gid %ld is %ld",
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)g_broker_common_cfg->private_broker.pb_group_id,
                          (long)customer_gid,
                          (long)config_value);
    }
    return config_value;
}

void zpn_broker_config_get_fohh_connection_disable_read_config_params(int64_t customer_id, int64_t *client_tx_buff_low, int64_t *client_tx_buff_high, int64_t *client_rx_buff_high_water_time_max_s)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
                                                            &config_value,
                                                            ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_id,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        *client_tx_buff_high = config_value;

        config_value = zpath_config_override_get_config_int(ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
                                                            &config_value,
                                                            ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_id,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        *client_tx_buff_low = config_value;

        config_value = zpath_config_override_get_config_int(ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
                                                    &config_value,
                                                    ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT,
                                                    zpath_instance_global_state.current_config->gid,
                                                    customer_id,
                                                    root_customer_gid,
                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                    (int64_t)0);
        *client_rx_buff_high_water_time_max_s = config_value;

        ZPN_DEBUG_MTUNNEL("fohh connection read disable (block) feature config for customer %ld, gid %ld, root gid %ld is %ld",
                          (long)customer_id,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)root_customer_gid,
                          (long)config_value);
    } else {

        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);

        config_value = zpath_config_override_get_config_int(ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
                                                    &config_value,
                                                    ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT,
                                                    g_broker_common_cfg->private_broker.broker_id,
                                                    g_broker_common_cfg->private_broker.pb_group_id,
                                                    customer_gid,
                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                    (int64_t)0);
        *client_tx_buff_high = config_value;

        config_value = zpath_config_override_get_config_int(ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
                                            &config_value,
                                            ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT,
                                            g_broker_common_cfg->private_broker.broker_id,
                                            g_broker_common_cfg->private_broker.pb_group_id,
                                            customer_gid,
                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                            (int64_t)0);
        *client_tx_buff_low = config_value;

        config_value = zpath_config_override_get_config_int(ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
                                    &config_value,
                                    ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT,
                                    g_broker_common_cfg->private_broker.broker_id,
                                    g_broker_common_cfg->private_broker.pb_group_id,
                                    customer_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);
        *client_rx_buff_high_water_time_max_s = config_value;

        ZPN_DEBUG_MTUNNEL("fohh connection read disable (block) feature config for %ld pse, grp gid %ld customer gid %ld is %ld",
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)g_broker_common_cfg->private_broker.pb_group_id,
                          (long)customer_gid,
                          (long)config_value);
    }
    return;
}

int64_t zpn_broker_config_mtunnel_fin_expire_us(int64_t customer_id)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S,
                                                            &config_value,
                                                            ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_id,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("mtunnel fin expire config for customer %ld, gid %ld, root gid %ld is %ld",
                          (long)customer_id,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)root_customer_gid,
                          (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S,
                                                            &config_value,
                                                            ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("mtunnel fin expire config for %ld pse, customer gid %ld is %ld",
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)customer_gid,
                          (long)config_value);
    }
    config_value = SECOND_TO_US(config_value);
    return config_value;
}
