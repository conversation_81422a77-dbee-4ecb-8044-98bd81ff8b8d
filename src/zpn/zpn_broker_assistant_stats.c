/*
 * zpn_broker_assistant_stats.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 * Broker stats connection related stuff.
 */

#include <openssl/rand.h>
#include "base64/base64.h"
#include "fohh/fohh.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_debug.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_private.h"
#include "zpath_lib/zpath_et_service_endpoint.h"

#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_broker_assistant_stats.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_lib.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_stats.h"


LIST_HEAD(stats_assistant_head, stats_assistant);

/*
 * Stats connection connected assistant
 *
 */
struct stats_assistant {
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* Timer for authentication reports. */
    struct event *timer;

    struct zpn_ast_auth_log auth_log;

    int64_t g_ast_grp;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;

    LIST_ENTRY(stats_assistant) list_entry;
    int in_list;

    uint64_t rx_stats_upload;
    uint64_t monitor_count;

    enum connector_type type;
};

/* Structure to keep track of all assistants connected to us through stats connections. */
struct stats_connected_assistants {
    pthread_mutex_t lock;

    struct stats_assistant_head assistant_list;
};
static struct stats_connected_assistants assistants_stats;

static void
zpn_broker_assistant_stats_conn_auth_log(struct stats_assistant *asst/*!=NULL*/,
                                         enum fohh_connection_state state)
{
    const int res = zpn_broker_assistant_auth_log(asst->f_conn,
                                                  &(asst->auth_log),
                                                  asst->assistant_gid_from_config,
                                                  ZPN_ASSISTANT_BROKER_STATS,
                                                  state);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Assistant stats log connection auth log fail. Assistant ID = %"PRId64,
                fohh_description(asst->f_conn), asst->assistant_gid_from_config);
    }
}

static void
zpn_broker_assistant_stats_conn_redirect(struct fohh_connection *f_conn)
{
    const int redirect_assistants = zpn_broker_assistant_get_redirect_assistants_flag();

    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_assistants, NULL)) {
        return;
    }

    struct stats_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), asst->tunnel_id);

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    zpn_broker_assistant_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);

    zpn_broker_balance_conn_redirect(f_conn,
                                     asst->customer_gid,
                                     asst->scope_gid,
                                     &peer_ip, &peer_site,
                                     asst->tunnel_id,
                                     redirect_assistants,
                                     zpn_broker_assistant_get_redirect_assistants_reason(),
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_assistant,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_stats_alt_cloud_redirects), 1);
    }
}

void zpn_broker_assistant_stats_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_assistant_stats_conn_redirect(f_conn);
}

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL_S seconds */
static void
zpn_broker_assistant_stats_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct stats_assistant *asst = cookie;

    if (asst) {
        asst->monitor_count++;

        /* Drop connection when broker is shutting down. */
        if (zpn_broker_assistant_get_disconnect_assistants_flag()) {
            const char *disconnect_reason = zpn_broker_assistant_get_disconnect_assistants_reason();
            ZPN_LOG(AL_INFO, "%s: Disconnect assistant stats connection due to %s",
                    fohh_description(asst->f_conn),
                    disconnect_reason ? disconnect_reason : FOHH_CLOSE_REASON_UPGRADE);
            fohh_connection_delete(asst->f_conn, disconnect_reason);
            return;
        }

        if (asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), fohh_peer_get_id(asst->f_conn), asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (zpn_broker_assistant_get_redirect_assistants_flag() &&
            (asst->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0) {
            const char *redirect_reason = zpn_broker_assistant_get_redirect_assistants_reason();
            ZPN_LOG(AL_INFO, "%s: Redirect assistant stats connection due to %s",
                    fohh_description(asst->f_conn),
                    redirect_reason ? redirect_reason : FOHH_CLOSE_REASON_UPGRADE);
            zpn_broker_assistant_stats_conn_redirect(asst->f_conn);
        }

        if ((asst->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
            zpn_broker_assistant_stats_conn_auth_log(asst, fohh_get_state(asst->f_conn));
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Assistant stats conn monitor cb has no cookie");
    }
}

static void
zpn_broker_assistant_stats_destroy(struct stats_assistant *asst/*!=NULL*/)
{
    struct fohh_connection *f_conn = asst->f_conn;
    if (f_conn) {
        ZPN_LOG(AL_NOTICE, "%s: Assistant stats connection DOWN. Assistant ID = %"PRId64,
                fohh_description(f_conn), asst->assistant_gid_from_config);
    } else {
        ZPN_LOG(AL_NOTICE, "Stale assistant stats connection DOWN. Assistant ID = %"PRId64,
                asst->assistant_gid_from_config);
    }

    if (asst->timer) {
        event_free(asst->timer);
        asst->timer = NULL;
    }

    zpn_broker_assistant_stats_conn_auth_log(asst, fohh_connection_disconnected);

    /* Remove asst from connected assistant_list */
    pthread_mutex_lock(&(assistants_stats.lock));
    if (asst->in_list) {
        LIST_REMOVE(asst, list_entry);
        asst->in_list = 0;
    }
    pthread_mutex_unlock(&(assistants_stats.lock));

    if (asst->auth_log.gids) {
        ZPN_FREE(asst->auth_log.gids);
        asst->auth_log.gids = NULL;
        asst->auth_log.gids_count = 0;
    }
    if (asst->auth_log.slogger_info ){
        ZPN_FREE(asst->auth_log.slogger_info);
        asst->auth_log.slogger_info = NULL;
    }
    if (asst->auth_log.dft_rt_intf) {
        ZPN_FREE(asst->auth_log.dft_rt_intf);
        asst->auth_log.dft_rt_intf = NULL;
    }

    if (asst->auth_log.cc) {
        ZPN_FREE(asst->auth_log.cc);
        asst->auth_log.cc = NULL;
    }
    ZPN_FREE(asst);

    if (f_conn) {
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
    }

    if (f_conn) {
        zpn_fohh_worker_assistant_disconnect_stats(fohh_connection_get_thread_id(f_conn));
    }
}

int
zpn_broker_assistant_stats_conn_callback(struct fohh_connection *connection,
                                         enum fohh_connection_state state,
                                         void *cookie)
{
    struct argo_state *argo;
    struct stats_assistant *asst;

    if (state == fohh_connection_connected) {
        int res;
        int64_t asst_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        /* Allocate state for this connection, so we can track its stuff... */
        asst_id = fohh_peer_get_id(connection);
        if (!asst_id) return FOHH_RESULT_ERR;

        ZPN_DEBUG_ASSISTANT("%s: Broker received assistant stats connection, Assistant ID = %"PRId64,
                            fohh_description(connection), asst_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_ASSISTANT("%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);

        /* Register assistant log upload. Connector's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_assistant_stats_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register assistant log upload for connection %s", fohh_description(connection));
            return res;
        }

        struct timeval tv;
        asst = ZPN_CALLOC(sizeof(*asst));
        if (!asst) {
            ZPN_LOG(AL_ERROR, "Memory");
            return FOHH_RESULT_NO_MEMORY;
        }

        /* Generate an ID for this client */
        res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(asst);
            return res;
        }
        base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        asst->auth_log.tunnel_id = asst->tunnel_id;
        asst->auth_log.status = ZPN_STATUS_AUTHENTICATED;
        asst->in_list = 0;

        asst->assistant_gid_from_config = asst_id;
        asst->f_conn = connection;
        asst->f_conn_incarnation = fohh_connection_incarnation(connection);
        fohh_connection_set_dynamic_cookie(connection, asst);

        {
            /* Fetch geoIP and gid from assistant group. Note- these were
             * prefetched during authentication, so should be
             * available directly */
            struct zpn_assistantgroup_assistant_relation *ag_relation;
            size_t count = 1;
            res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                         &ag_relation,
                                                                         &count,
                                                                         NULL,
                                                                         NULL,
                                                                         0);
            if (res) {
                ZPN_LOG(AL_WARNING, "%s: Error fetching assistantgroup_assistant_relation. Assistant ID = %"PRId64": %s",
                        fohh_peer_cn(connection), asst_id, zpn_result_string(res));
            } else {
                asst->g_ast_grp = ag_relation->assistant_group_id;
                asst->auth_log.g_ast_grp = ag_relation->assistant_group_id;
            }
        }

        {
            /* Get customer_gid for the assistant */
            struct zpn_assistant *assistant = NULL;

            res = zpn_assistant_get_by_id(asst_id, &assistant, NULL, NULL, 0);
            if (assistant) {
                /* We will always succeed here since this has been called during verification before connection is up */
                asst->customer_gid = assistant->customer_gid;
                asst->auth_log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
                asst->type = assistant->connector_type;
            } else {
                ZPN_LOG(AL_NOTICE, "Cannot find the assistant with Assistant ID = %"PRId64, asst_id);
            }
        }

        asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                -1,
                                EV_PERSIST,
                                zpn_broker_assistant_stats_conn_monitor_cb,
                                asst);
        if (!asst->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;;
        tv.tv_usec = 0;
        if (event_add(asst->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add assistant timer");
            event_free(asst->timer);
            ZPN_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        /* Add asst to connected assistant_list */
        pthread_mutex_lock(&(assistants_stats.lock));
        LIST_INSERT_HEAD(&(assistants_stats.assistant_list), asst, list_entry);
        asst->in_list = 1;
        pthread_mutex_unlock(&(assistants_stats.lock));

        zpn_fohh_worker_assistant_connect_stats(fohh_connection_get_thread_id(connection));

        ZPN_LOG(AL_NOTICE, "%s: Assistant stats connection UP, Assistant ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), asst_id, asst->customer_gid);
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: Assistant stats connection DOWN due to %s",
                fohh_description(connection), reason);

        asst = fohh_connection_get_dynamic_cookie(connection);
        if (asst) {
            asst->auth_log.close_reason = reason;
            asst->auth_log.status = ZPN_STATUS_DISCONNECTED;
            zpn_broker_assistant_stats_destroy(asst);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_stats_unblock_callback(struct fohh_connection *connection,
                                            enum fohh_queue_element_type element_type,
                                            void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "%s: Assistant stats connection unblock callback", fohh_description(connection));
    return FOHH_RESULT_NO_ERROR;
}

static int
zpn_broker_assistant_stats_upload_to_zi_endpoint(struct argo_object *object, int64_t customer_gid)
{
    struct argo_object *obj_copy;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (zpn_broker_is_asst_zistats_upload_disabled_for_customer(customer_gid)) {
        ZPN_DEBUG_ASSISTANT("Received stats from connector but zistats upload is disabled for customer %"PRId64, customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    obj_copy = argo_object_copy(object);
    argo_log_log_object_immediate(zpath_stats_collection, obj_copy);
    argo_object_release(obj_copy);

    return ZPN_RESULT_NO_ERROR;

}

static int
zpn_broker_assistant_stats_upload_to_ci_endpoint(struct argo_object    *object,
                                                 int64_t               customer_gid,
                                                 char                  *tunnel_id,
                                                 int64_t               ast_gid,
                                                 int64_t               ast_grp_gid,
                                                 int                   check_for_site,
                                                 enum connector_type   type)
{
    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because customer_id was not configured");
        return ZPN_RESULT_ERR;
    }

    struct argo_log *log = object->base_structure_void;
    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    zpn_broker_assistant_stats_upload_to_zi_endpoint(object, customer_gid);

    enum zpath_customer_log_type log_type;
    if (0 == strncmp("zpn_np_nw_comp_stats", log->l_name, sizeof("zpn_np_nw_comp_stats"))) {
        log_type = zpath_customer_log_type_zpn_np_nw_comp_stats;
    } else if (0 == strncmp("assistant_stats_comprehensive", log->l_name, sizeof("assistant_stats_comprehensive"))) {

        if (type == np_connector) {
            log_type = zpath_customer_log_type_np_comprehensive_stats;
        } else {
            log_type = zpath_customer_log_type_comprehensive_stats;
            /* Stream comprehensive stats log to LSS */
            zpn_broker_siem_ast_comprehensive_stats(log->l_obj->base_structure_void,
                                                    customer_gid,
                                                    tunnel_id,
                                                    ast_gid,
                                                    ast_grp_gid,
                                                    check_for_site);
            }
    } else {
        log_type = zpath_customer_log_type_ci_stats;
    }
    /* We're sending comprehensive stats to a separate kafka topic */
    return zpath_service_endpoint_log_struct_config(customer_gid,
                                                    log->l_name,
                                                    log_type,
                                                    argo_get_object_description(log->l_obj),
                                                    log->l_inst,
                                                    ast_gid,
                                                    log->l_role,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    log->l_obj->base_structure_void); // internal calls creates the log object and captures this data and manages it
}

int zpn_broker_assistant_stats_send_event_log_to_kafka(struct argo_object *object,
                                                       int64_t customer_gid,
                                                       int64_t ast_gid)
{
    struct argo_log *log = object->base_structure_void;
    struct zpn_event_stats *zpn_event_stats_data;
    int res;

    if (customer_gid == 0) {
        ZPN_LOG(AL_ERROR, "Could not send zpn_event log to kafka pipeline because customer_gid is NULL");
        return ZPN_RESULT_ERR;
    }

    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    res = zpath_service_endpoint_log_struct_config(customer_gid,
                                                   log->l_name,
                                                   zpath_customer_log_type_zpn_event,
                                                   argo_get_object_description(log->l_obj),
                                                   log->l_inst,
                                                   ast_gid,
                                                   log->l_role,
                                                   log->l_prio,
                                                   log->l_disp,
                                                   log->l_sys,
                                                   log->l_ctg,
                                                   log->l_obj->base_structure_void);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not forward zpn_event log to producer sent by %s: %s", log->l_inst, zpn_result_string(res));
        return res;
    }

    zpn_event_stats_data = zpn_event_get_stats_data_obj();
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_forwarded), 1);
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_total), 1);

    return ZPN_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_stats_upload(struct fohh_connection *f_conn,
                                  struct argo_object     *object,
                                  int64_t                customer_gid,
                                  char                   *tunnel_id,
                                  int64_t                ast_gid,
                                  int64_t                ast_grp_gid,
                                  int                    check_for_site,
                                  enum connector_type    type)
{
    struct argo_log             *log = object->base_structure_void;
    struct argo_object          *obj_structure = log->l_obj;
    struct argo_log_text        *log_text = obj_structure->base_structure_void;
    int                         redirect_to_ci_endpoint;

    redirect_to_ci_endpoint = 0;
    if (0 == strncmp("assistant_stats_", log->l_name, strlen("assistant_stats_"))) {
        if (!customer_gid) {
            ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL");
            return zpn_broker_assistant_stats_upload_to_zi_endpoint(object, customer_gid);
        }
        // Redirect mtunnel stats to an endpoint
        if (0 == strncmp("assistant_stats_data", log->l_name, sizeof("assistant_stats_data"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_data_mtunnel_global", log->l_name, sizeof("assistant_stats_data_mtunnel_global"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_disk", log->l_name, sizeof("assistant_stats_system_disk"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_cpu", log->l_name, sizeof("assistant_stats_system_cpu"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_memory", log->l_name, sizeof("assistant_stats_system_memory"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_fd", log->l_name, sizeof("assistant_stats_system_fd"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_sock", log->l_name, sizeof("assistant_stats_system_sock"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_app", log->l_name, sizeof("assistant_stats_app"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_system_inventory", log->l_name, sizeof("assistant_stats_system_inventory"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_comprehensive", log->l_name, sizeof("assistant_stats_comprehensive"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_dns", log->l_name, sizeof("assistant_stats_dns"))) {
            redirect_to_ci_endpoint = 1;
        } else if (0 == strncmp("assistant_stats_admin_probe", log->l_name, sizeof("assistant_stats_admin_probe"))) {
            redirect_to_ci_endpoint = 1;
        } else {
            redirect_to_ci_endpoint = 0;
        }

        if (redirect_to_ci_endpoint) {
            return zpn_broker_assistant_stats_upload_to_ci_endpoint(object,
                                                                    customer_gid,
                                                                    tunnel_id,
                                                                    ast_gid,
                                                                    ast_grp_gid,
                                                                    check_for_site,
                                                                    type);
        } else {
            return zpn_broker_assistant_stats_upload_to_zi_endpoint(object, customer_gid);
        }
    }

    // NP nw comprehensive stats
    if (0 == strncmp("zpn_np_nw_comp_stats", log->l_name, sizeof("zpn_np_nw_comp_stats"))) {
        return zpn_broker_assistant_stats_upload_to_ci_endpoint(object,
                                                                customer_gid,
                                                                tunnel_id,
                                                                ast_gid,
                                                                ast_grp_gid,
                                                                check_for_site,
                                                                type);
    }

    /*
     * Stats logged in assistant which are zpn library specific. Note : It would be nice if even these stats change
     * to be prefixed with assistant_stats_*
     */
    if ((0 == strncmp("zthread_rusage", log->l_otyp, strlen("zthread_rusage"))) ||
        (0 == strncmp("zpath_debug_memory_allocator_stats", log->l_otyp, strlen ("zpath_debug_memory_allocator_stats"))) ||
        (0 == strncmp("zpath_debug_mallinfo", log->l_otyp, strlen("zpath_debug_mallinfo"))) ||
        (0 == strncmp("zpath_system_sysinfo", log->l_otyp, strlen("zpath_system_sysinfo"))) ||
        (0 == strncmp("proc_smaps", log->l_name, sizeof("proc_smaps"))) ||
        (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) ||
        (0 == strncmp("zpn_event_assistant_stats", log->l_name, sizeof("zpn_event_assistant_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_error_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_error_stats"))) ||
        (0 == strncmp("zpn_zdx_mtr_stats", log->l_name, sizeof("zpn_zdx_mtr_stats"))) ||
        (0 == strncmp("zpn_zdx_cache_stats", log->l_name, sizeof("zpn_zdx_cache_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_stats", log->l_name, sizeof("zpn_zdx_probe_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_telemetry_stats", log->l_name, sizeof("zpn_zdx_probe_telemetry_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_error_stats", log->l_name, sizeof("zpn_zdx_probe_error_stats"))) ||
        (0 == strncmp("zpn_fohh_worker_assistant_stats", log->l_name, sizeof("zpn_fohh_worker_assistant_stats"))) ||
        (0 == strncmp("zhealth_probe_lib_raw_sock_stats", log->l_name, sizeof("zhealth_probe_lib_raw_sock_stats"))) ||
        (0 == strncmp("zhealth_probe_lib_udp_raw_sock_stats", log->l_name, sizeof("zhealth_probe_lib_udp_raw_sock_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_https_ptls_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_https_ptls_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_https_ptls_err_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_https_ptls_err_stats"))) ||
        (0 == strncmp("fohh_connection_cipher_stats", log->l_otyp, sizeof("fohh_connection_cipher_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_http_response_error_stats", log->l_name, sizeof("zpn_zdx_probe_http_response_error_stats"))) ||
        (0 == strncmp("fohh_connection_aggregated_hop_latency_stats", log->l_otyp, sizeof("fohh_connection_aggregated_hop_latency_stats"))) ||
        (0 == strncmp("zpn_np_route_comp_stats", log->l_name, sizeof("zpn_np_route_comp_stats"))) ||
        (0 == strncmp("zpn_np_cumulative_bgp_peer_stats", log->l_name, sizeof("zpn_np_cumulative_bgp_peer_stats"))) ||
        (0 == strncmp("zpn_np_cumulative_bgp_route_stats", log->l_name, sizeof("zpn_np_cumulative_bgp_route_stats"))) ||
        (0 == strncmp("zpn_np_frr_svc_stats", log->l_name, sizeof("zpn_np_frr_svc_route_stats"))) ||
        (0 == strncmp("fohh_connection_aggregated_pipeline_latency_stats", log->l_otyp, sizeof("fohh_connection_aggregated_pipeline_latency_stats")))) {

        /*
         * I have seen smaps around 32KB. This means that argo_object_dump() down this path will not work for proc_smaps
         * as it was dumping only 8KB of memory. I don't want to change that allocation, and instead decided to
         * not print it - not that important.
         */
        return zpn_broker_assistant_stats_upload_to_zi_endpoint(object, customer_gid);
    }

    /*
     * Assistant sends zpn_event log over stats connection. Identify it and forward it to producer
     * on a separate kafka topic 'zpn_event'.
     */
    if (0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) {
        return zpn_broker_assistant_stats_send_event_log_to_kafka(object,
                                                                  customer_gid,
                                                                  ast_gid);
    }

    /*
     * When this line is hit, it means we received an unrecognized stats log.
     * we will not forward it to any remote endpoint and will drop it.
     */
    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    /*
     * drop any log request without priority string. This is not expected, but any logs generated by
     * connector by using argo_log_register_structure(), is going to have NULL priority. We will hit this case if
     * logs generated by argo_log_register_structure() is sent to broker and it is not yet equipped to handle it.
     */
    if (NULL == log->l_prio) {
        ZPN_LOG(AL_ERROR, "%s: Do not know how to handle this log type(%s) name(%s), so dropping it",
                fohh_description(f_conn), log->l_otyp, log->l_name);
        return ZPN_RESULT_NO_ERROR;
    }

    /* Log to local - for backward compatibility only. The older connectors still send log which hits here */
    argo_log_structure_immediate(zpn_event_collection,
                                 argo_log_rfc_priority_string_to_priority(log->l_prio),
                                 0,
                                 fohh_peer_cn(f_conn),
                                 global_argo_log_text_desc,
                                 log_text);

    /*
     * FIXME:revisit 2020 Dec!
     * Ideally we should have done the below assert below. But for backward compatibility lets accept the logs here.
     * Once all the connectors are upgraded past this point, we should bring this assert back here.
     * ZPN_BROKER_ASSERT_HARD(0, "event logs are sent to wrong endpoint");
     */

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Stats connection interface (new connectors, after drop8)
 */
int
zpn_broker_assistant_stats_log_upload_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct fohh_connection      *f_conn = argo_structure_cookie_ptr;
    struct stats_assistant      *asst;

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (!asst) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL");
        return zpn_broker_assistant_stats_upload(f_conn, object, 0, NULL, 0, 0, 0, 0);
    }
    asst->rx_stats_upload++;
    return zpn_broker_assistant_stats_upload(f_conn,
                                             object,
                                             asst->customer_gid,
                                             asst->tunnel_id,
                                             asst->assistant_gid_from_config,
                                             asst->g_ast_grp,
                                             0,
                                             asst->type);
}

int
zpn_broker_assistant_stats_info(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct stats_assistant* asst;
    struct stats_assistant* tmp;
    uint64_t total_stats_connections = 0;
    uint64_t total_rx_stats_upload = 0;

    pthread_mutex_lock(&(assistants_stats.lock));

    LIST_FOREACH_SAFE(asst, &(assistants_stats.assistant_list), list_entry, tmp) {
        total_stats_connections++;
        total_rx_stats_upload += asst->rx_stats_upload;
    }

    pthread_mutex_unlock(&(assistants_stats.lock));

    ZDP("total number of stats connections: %"PRId64"\n", total_stats_connections);
    ZDP("total received stats upload: %"PRId64"\n", total_rx_stats_upload);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_stats_dump_all(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct stats_assistant* asst;
    struct stats_assistant* tmp;

    ZDP(" -- stats connections view -- \n");
    pthread_mutex_lock(&(assistants_stats.lock));

    LIST_FOREACH_SAFE(asst, &(assistants_stats.assistant_list), list_entry, tmp) {
        ZDP("assistant_gid_from_config           : %"PRId64"\n", asst->assistant_gid_from_config);
        ZDP("assistant_customer_gid              : %"PRIu64"\n", (uint64_t)ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config));
        ZDP("f_conn                              : %s\n", fohh_description(asst->f_conn));
        ZDP("rx_log_stats_upload                 : %"PRId64"\n", asst->rx_stats_upload);

        ZDP("\n");
    }

    pthread_mutex_unlock(&(assistants_stats.lock));

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_stats_init()
{
    int res;

    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to connectors",
                                  "/broker/assistant/stats/info",
                                  zpn_broker_assistant_stats_info,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to connectors",
                                  "/broker/assistant/stats/dump",
                                  zpn_broker_assistant_stats_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}
