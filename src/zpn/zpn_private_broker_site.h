/*
 * zpn_private_broker_site.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ZPN_PRIVATE_BROKER_SITE_H_
#define _ZPN_PRIVATE_BROKER_SITE_H_

#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_table.h"

struct fohh_connection;
struct zpn_site_controller;
struct zpn_private_broker_load;

int zpn_private_broker_site_notify_cfg_site_gid(int64_t site_gid);
int zpn_private_broker_site_notify_cfg_site_config(int valid, int64_t reenroll_period, int sitec_preferred);
int zpn_private_broker_site_notify_cfg_ddil_config(struct zpn_ddil_config *ddil_config);
int zpn_private_broker_site_notify_cfg_sitec_config(struct zpn_site_controller *sitec);
int zpn_private_broker_site_notify_cfg_sitec_group_config(struct zpn_site_controller_group *sitec_group);
int zpn_private_broker_site_notify_pbctl_to_broker_status(int connected);
int zpn_private_broker_site_notify_pbctl_to_sitec_status(int connected);

void zpn_private_broker_site_xmit_pbinfo(struct zpn_private_broker_load *load, struct zpn_pbroker_status_report *pb_report);

int zpn_private_broker_site_register_fohh(struct fohh_connection* connection, const char* label,
        const char *remote_host_name, const char *sni_service_name, const char * sni_suffix,
        int stick_to_sitec, int current_to_sitec);
int zpn_private_broker_site_deregister_fohh(struct fohh_connection* connection);

int zpn_private_broker_site_init();
int zpn_private_broker_site_init_tasks(struct event_base *base);
int zpn_private_broker_site_start();

int zpn_private_broker_site_is_sitec_eligible(const char* offline_domain);
int zpn_private_broker_site_is_sitec_preferred();
int zpn_private_broker_site_sitec_is_reachable();
int zpn_private_broker_site_broker_is_reachable();


#endif // _ZPN_PRIVATE_BROKER_SITE_H_
