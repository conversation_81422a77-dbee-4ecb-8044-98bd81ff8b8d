/*
 * zpn_mconn_icmp.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_debug.h"

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include "fohh/fohh.h"
#include <fcntl.h>
#include <errno.h>
#include <sys/socket.h>
#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>

#include "fohh/fohh_private.h"
#include "zpn/zpn_mconn_icmp.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_lib.h"
#include "zhealth/zhealth_probe_lib.h"
#include "zpn/zpn_mconn_icmp_util.h"
#include "zpath_misc/zpath_misc.h"

static struct argo_structure_description* zpn_mconn_icmp_stats_description;

static
struct zpn_mconn_icmp_stats {                                              /* _ARGO: object_definition */
    int64_t icmp_mconn_icmpinfo_alloc;                                     /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_free;                                      /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_alloc;                            /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_free;                             /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_payload_alloc;                             /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_payload_free;                              /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_payload_hdrincl_alloc;                     /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_payload_hdrincl_free;                      /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_cookie1_alloc;                    /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_cookie1_free;                     /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_cookie2_alloc;                    /* _ARGO: integer */
    int64_t icmp_mconn_icmpinfo_response_cookie2_free;                     /* _ARGO: integer */
    int64_t icmp_mconn_failcookie_alloc;                                   /* _ARGO: integer */
    int64_t icmp_mconn_failcookie_free;                                    /* _ARGO: integer */
    int64_t icmp_mconn_reqcookie_alloc;                                    /* _ARGO: integer */
    int64_t icmp_mconn_reqcookie_free;                                     /* _ARGO: integer */
    int64_t icmp_mconn_free_icmpinfo_invalid_icmpinfo;                     /* _ARGO: integer */
    int64_t icmp_mconn_icmp_failure_cb_invalid_icmpinfo;                   /* _ARGO: integer */
    int64_t icmp_mconn_icmp_response_cb_invalid_icmpinfo;                  /* _ARGO: integer */
    int64_t icmp_mconn_failure_cb_no_global_owner_to_lock;                 /* _ARGO: integer */
    int64_t icmp_mconn_failure_cb_mconn_already_gone;                      /* _ARGO: integer */
    int64_t icmp_mconn_failure_cb_no_global_owner_to_unlock;               /* _ARGO: integer */
    int64_t icmp_mconn_response_cb_no_global_owner_to_lock;                /* _ARGO: integer */
    int64_t icmp_mconn_response_cb_no_global_owner_to_unlock;              /* _ARGO: integer */
}stats;

#include "zpn/zpn_mconn_icmp_compiled_c.h"

/*
 * IMPORTANT
 * We do not free cookies here because cookies are self referential and not always consistantly owned by the icmp_info
 * instead it is up to the users of the icmpinfo here to properly free any cookies that they may have set in their
 * path
 */
static void zpn_mconn_icmp_info_free(struct zhealth_icmp_info *icmpinfo) {
    if (!icmpinfo) {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_free_icmpinfo_invalid_icmpinfo), 1);
        return;
    }

    if (icmpinfo->icmp_payload) {
        ZPN_FREE(icmpinfo->icmp_payload);
        icmpinfo->icmp_payload = NULL;
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_payload_free), 1);
    }

    if (icmpinfo->response) {
        ZPN_FREE(icmpinfo->response);
        icmpinfo->response = NULL;
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_response_free), 1);
    }

    if (icmpinfo->icmp_payload_hdrincl) {
        ZPN_FREE(icmpinfo->icmp_payload_hdrincl);
        icmpinfo->icmp_payload_hdrincl = NULL;
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_payload_hdrincl_free), 1);
    }

    zhealth_probe_lib_probe_info_shutdown_probe(&(icmpinfo->probe_info));
    zhealth_probe_lib_close_zhealth_owned_socket_async(&(icmpinfo->probe_info));

    ZPN_FREE(icmpinfo);
    __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_free), 1);
}

int zpn_mconn_icmp_init(struct zpn_mconn_icmp *mconn_icmp, void *mconn_self, enum zpn_mconn_type type)
{
    return zpn_mconn_icmp_common_init(mconn_icmp, mconn_self, type);
}

static int zpn_mconn_icmp_bind_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length,
                                  int64_t *owner_incarnation)
{
    return zpn_mconn_icmp_common_bind(mconn_base, owner);
}

static int zpn_mconn_icmp_unbind_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t owner_incarnation,
                                    int drop_buffered_data,
                                    int dont_propagate,
                                    const char *err)
{
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_icmp_lock_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length)
{
    return;
}


static void zpn_mconn_icmp_unlock_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length)
{
    return;
}

int zpn_mconn_icmp_failure_cb(struct zhealth_icmp_info *icmp_info, int64_t pending_us)
{
    if (!icmp_info) {
        ZPN_LOG(AL_ERROR, "ICMP mconn failure cb - no icmp_info, returning");
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmp_failure_cb_invalid_icmpinfo), 1);
        return ZPN_RESULT_ERR;
    }

    struct zpn_mconn_icmpfail_cookie *icmpfail_cookie = icmp_info->failure_cookie;
    struct zhealth_icmp_info *icmpinfo = icmpfail_cookie->icmpinfo;
    struct zpn_mconn_icmp *mconn_icmp = icmpfail_cookie->mconn_icmp;
    struct zpn_mconn *mconn = &(mconn_icmp->mconn);

    /*
     * free the icmp info first and then access the mconn structure later with lock.
     * its okay we lose the mtunnel, but the icmp related data need to be freed anyways.
     */

    if (icmpfail_cookie->req_cookie) {
        ZPN_FREE(icmpfail_cookie->req_cookie);
        icmpfail_cookie->req_cookie = NULL;
        __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
    }

    ZPN_FREE(icmpfail_cookie);
    __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);

    zpn_mconn_icmp_info_free(icmpinfo);

    if (!mconn) {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_failure_cb_mconn_already_gone), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_failure_cb_no_global_owner_to_lock), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    mconn->icmp_timeout_failure_drops++;

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_failure_cb_no_global_owner_to_unlock), 1);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_response_cb(struct zhealth_icmp_info *icmpinfo, int icmp_response_type, int icmp_response_code) {
    if (!icmpinfo) {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmp_response_cb_invalid_icmpinfo), 1);
        return ZPN_RESULT_ERR;
    }

    struct zpn_mconn_icmpreq_cookie *icmpreq_cookie = icmpinfo->response_cookie1;
    struct zpn_mconn_icmp *mconn_icmp = icmpinfo->response_cookie2;
    struct zpn_mconn *mconn = &(mconn_icmp->mconn);

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_response_cb_no_global_owner_to_lock), 1);
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_MCONN("Received ICMP response on: %s", icmpinfo->rx.dest_ip);

    mconn_icmp->last_rx_epoch_s = epoch_s();
    mconn_icmp->rx_bytes += icmpinfo->response_len;
    mconn_icmp->mconn.icmp_rx_packets++;

    if (icmpinfo->response_len > (ZPN_MCONN_MTU - 100)) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        goto exit;
    }

    if (!mconn->peer) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        mconn_icmp->mconn.icmp_internal_err_drops++;
        goto exit;
    }

    if (((icmp_response_type == ICMP_TIMXCEED && icmp_response_code == ICMP_TIMXCEED_INTRANS) ||
        (icmp_response_type == ICMP_UNREACH && icmp_response_code == ICMP_UNREACH_NEEDFRAG)) &&
        ((mconn->peer->icmp_access_type != ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING) &&
         (mconn->peer->icmp_access_type != ZPN_MCONN_ICMP_ACCESS_TYPE_PING))) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        mconn_icmp->mconn.icmp_access_err_drops++;
        goto exit;
    }

    ZPN_DEBUG_MCONN("Received ICMP payload of len: %zu", icmpinfo->response_len);
    struct evbuffer *evbuf = zpn_mconn_icmp_packetize(icmpinfo->response, icmpinfo->response_len,
                                                      icmp_response_type, icmp_response_code,
                                                      icmpreq_cookie->req_id,
                                                      icmpreq_cookie->req_seq,
                                                      icmpreq_cookie->ip_src,
                                                      icmpreq_cookie->ip_id);
    if (!evbuf) {
        goto exit;
    }

    int res = zpn_client_process_rx_data(&(mconn_icmp->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
    if (res) {
        ZPN_DEBUG_MCONN("Process_rx data for ICMP returned %s", zpn_result_string(res));
    }

    evbuffer_free(evbuf);

exit:

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmp_mconn_response_cb_no_global_owner_to_unlock), 1);
    }

    if (icmpreq_cookie) {
        if (icmpreq_cookie->failure_cookie) {
            ZPN_FREE(icmpreq_cookie->failure_cookie);
            icmpreq_cookie->failure_cookie = NULL;
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
        }

        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
    }

    zpn_mconn_icmp_info_free(icmpinfo);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_icmp_transmit_cb(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      int64_t owner_incarnation,
                                      int fohh_thread_id,
                                      struct evbuffer *buf,
                                      size_t buf_len)
{
    struct zpn_mconn_icmp *mconn_icmp = mconn_base;

    mconn_icmp->last_tx_epoch_s = epoch_s();

    int icmp_buf_len = evbuffer_get_length(buf);
    uint8_t *icmp_linear_buf = evbuffer_pullup(buf, icmp_buf_len);
    if (!icmp_linear_buf) {
        return ZPN_RESULT_ERR;
    }

    /* Only supporting
     * IPv4 version and src/dest as IPv4 address
     * ping <IPv4 address>
     * ping <FQDN> where FQDN is IPv4 address
     * else error out
     */
    struct ip *iph = (struct ip *)(icmp_linear_buf);
    int iph_len = iph->ip_hl << 2;

    if (iph->ip_v != 4) {
        ZPN_LOG(AL_ERROR, "IPv4 header not received in ICMPv4 transmit cb");
        return ZPN_RESULT_ERR;
    }

    struct icmp *icmp_hdr = (struct icmp *)(icmp_linear_buf + iph_len);
    struct zpn_mconn_icmpreq_cookie *icmpreq_cookie =
           ZPN_CALLOC(sizeof(struct zpn_mconn_icmpreq_cookie));
    if (!icmpreq_cookie) {
        return ZPN_RESULT_ERR;
    }
    __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_alloc), 1);

    icmpreq_cookie->req_id = icmp_hdr->icmp_hun.ih_idseq.icd_id;
    icmpreq_cookie->req_seq = icmp_hdr->icmp_hun.ih_idseq.icd_seq;
    icmpreq_cookie->ip_src = iph->ip_src;
    icmpreq_cookie->ip_id = iph->ip_id;

    struct zhealth_icmp_info *icmpinfo = (struct zhealth_icmp_info *)
                                          ZPN_CALLOC(sizeof(struct zhealth_icmp_info));
    if (!icmpinfo) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
        return ZPN_RESULT_ERR;
    }

    __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_alloc), 1);

    icmpinfo->response_cookie1 = icmpreq_cookie;
    icmpinfo->response_cookie2 = mconn_base;
    icmpinfo->response_cb = zpn_mconn_icmp_response_cb;

    icmpinfo->probe_info.max_ttl = iph->ip_ttl;
    icmpinfo->ip_tos = iph->ip_tos;
    snprintf_nowarn(icmpinfo->probe_info.dest_ip, sizeof(icmpinfo->probe_info.dest_ip), "%s", mconn_icmp->dst_ipaddr);

    struct zpn_mconn_icmpfail_cookie *icmpfail_cookie =
           ZPN_CALLOC(sizeof(struct zpn_mconn_icmpfail_cookie));
    if (!icmpfail_cookie) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
        zpn_mconn_icmp_info_free(icmpinfo);
        return ZPN_RESULT_ERR;
    }

    __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_alloc), 1);

    icmpfail_cookie->mconn_icmp = mconn_icmp;
    icmpfail_cookie->req_cookie = icmpreq_cookie;
    icmpfail_cookie->icmpinfo = icmpinfo;

    icmpreq_cookie->failure_cookie = icmpfail_cookie;
    icmpinfo->failure_cookie = icmpfail_cookie;
    icmpinfo->failure_cb = zpn_mconn_icmp_failure_cb;

    uint16_t ip_tlen = ntohs(iph->ip_len);

    if ((!((iph->ip_off) & htons(IP_DF))) &&
        (!((iph->ip_off) & htons(IP_MF)))&&
        (!((iph->ip_off) & htons(IP_OFFMASK)))) { /* For Big MTU - Frags packed up by ZCC */
        icmpinfo->response_len = ip_tlen + 68;

        icmpinfo->response = ZPN_CALLOC(icmpinfo->response_len);
        if (!icmpinfo->response) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_response_alloc), 1);

        int icmp_payload_len = (ip_tlen - iph_len - ICMP_HDR_LEN);
        uint8_t *icmp_payload = (uint8_t *)ZPN_MALLOC(icmp_payload_len);
        if (!icmp_payload) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_payload_alloc), 1);

        memcpy(icmp_payload, (icmp_linear_buf + iph_len + ICMP_HDR_LEN), icmp_payload_len);

        icmpinfo->icmp_payload = icmp_payload;
        icmpinfo->icmp_payload_len = icmp_payload_len;

        if (zhealth_icmp_gen_seqid(icmpinfo) != ARGO_RESULT_NO_ERROR) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }

        zhealth_send_icmp_probe(icmpinfo);
    } else { /* For supporting "Tracepathing" */
        icmpinfo->response_len = MAX_ICMP_PACKET;

        icmpinfo->response = ZPN_CALLOC(icmpinfo->response_len);
        if (!icmpinfo->response) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }

        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_response_alloc), 1);

        int ret;
        struct argo_inet inet_ip;
        socklen_t sock_len;

        ret = argo_string_to_inet("0.0.0.0", &inet_ip);
        if (ret != ARGO_RESULT_NO_ERROR) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }

        struct sockaddr_storage sock_addr;
        argo_inet_to_sockaddr(&inet_ip, (struct sockaddr *)&sock_addr, &sock_len, 0);

        iph->ip_src = ((struct sockaddr_in *)(&sock_addr))->sin_addr;

        memset(&inet_ip, 0, sizeof(struct argo_inet));

        ret = argo_string_to_inet(icmpinfo->probe_info.dest_ip, &inet_ip);
        if (ret != ARGO_RESULT_NO_ERROR) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }

        memset(&sock_addr, 0, sizeof(struct sockaddr_storage));

        argo_inet_to_sockaddr(&inet_ip, (struct sockaddr *)&sock_addr, &sock_len, 0);
        iph->ip_dst = ((struct sockaddr_in *)(&sock_addr))->sin_addr;

        iph->ip_sum = 0;

        if (zhealth_icmp_gen_seqid(icmpinfo) != ARGO_RESULT_NO_ERROR) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }

        if (!((iph->ip_off) & htons(IP_OFFMASK))) {
            icmp_hdr->icmp_hun.ih_idseq.icd_id = htons(icmpinfo->id);
            icmp_hdr->icmp_hun.ih_idseq.icd_seq = htons(icmpinfo->seq);

            icmp_hdr->icmp_cksum = 0;
            icmp_hdr->icmp_cksum = zpn_mconn_icmp_common_csum((unsigned short *)icmp_hdr,
                                        (ip_tlen - iph_len));
        }

        uint8_t *icmp_pkt = (uint8_t *)ZPN_MALLOC(ip_tlen);
        if (!icmp_pkt) {
            ZPN_FREE(icmpreq_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_reqcookie_free), 1);
            ZPN_FREE(icmpfail_cookie);
            __sync_add_and_fetch_8(&(stats.icmp_mconn_failcookie_free), 1);
            zpn_mconn_icmp_info_free(icmpinfo);
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch_8(&(stats.icmp_mconn_icmpinfo_payload_hdrincl_alloc), 1);

        memcpy(icmp_pkt, icmp_linear_buf, ip_tlen);

        icmpinfo->icmp_payload_hdrincl = icmp_pkt;
        icmpinfo->icmp_payload_hdrincl_len = ip_tlen;


        // TODO: May add ICMP_UNREACH/ICMP_UNREACH_NEEDFRAG later when
        // sendto returns EMSGSIZE & callbacks (Think of Tx/Rx thread)
        // will help in "Tracepathing"
        zhealth_send_icmp_direct(icmpinfo);
    }

    evbuffer_drain(buf, ip_tlen);
    mconn_icmp->mconn.bytes_to_client += ip_tlen;
    mconn_icmp->mconn.icmp_tx_packets++;
    mconn_icmp->tx_bytes += ip_tlen;

    if (mconn_icmp->mconn.peer) {
        zpn_mconn_client_window_update(mconn_icmp->mconn.peer, 0, ip_tlen, 1);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_icmp_pause_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length,
                                   int64_t owner_incarnation,
                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_icmp_resume_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t owner_incarnation,
                                    int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_forward_tunnel_end_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         const char *err,
                                         int32_t drop_data)
{
    return zpn_mconn_icmp_common_forward_tunnel_end(mconn_base, drop_data);
}

void zpn_mconn_icmp_window_update_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length,
                                     int64_t owner_incarnation,
                                     int fohh_thread_id,
                                     int tx_len,
                                     int batch_win_upd)
{
    return;
}

void zpn_mconn_icmp_stats_update_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t owner_incarnation,
                                    int fohh_thread_id,
                                    enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_icmp_disable_read_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length,
                                   int64_t owner_incarnation,
                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
static int zpn_mconn_icmp_enable_read_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length,
                                   int64_t owner_incarnation,
                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls mconn_icmp_calls = {
    zpn_mconn_icmp_bind_cb,
    zpn_mconn_icmp_unbind_cb,
    zpn_mconn_icmp_lock_cb,
    zpn_mconn_icmp_unlock_cb,
    zpn_mconn_icmp_transmit_cb,
    zpn_mconn_icmp_pause_cb,
    zpn_mconn_icmp_resume_cb,
    zpn_mconn_icmp_forward_tunnel_end_cb,
    zpn_mconn_icmp_window_update_cb,
    zpn_mconn_icmp_stats_update_cb,
    zpn_mconn_icmp_disable_read_cb,
    zpn_mconn_icmp_enable_read_cb
};

int zpn_mconn_icmp_done(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_done(mconn_icmp);
}

int zpn_mconn_icmp_clean(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_clean(mconn_icmp);
}

void zpn_mconn_icmp_internal_display(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_internal_display(mconn_icmp);
}

static int
zpn_mconn_icmp_dump_stats(struct zpath_debug_state*  request_state,
                           const char**               query_values,
                           int                        query_value_count,
                           void*                      cookie)
{
    char        jsonout[50000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(zpn_mconn_icmp_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_debug_init()
{
    int res;

    if (!(zpn_mconn_icmp_stats_description = argo_register_global_structure(ZPN_MCONN_ICMP_STATS_HELPER))) {
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of zpn_mconn_icmp",
                                  "/zpn/zpn_mconn_icmp/dump/stats",
                                  zpn_mconn_icmp_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}
