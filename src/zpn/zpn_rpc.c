/*
 * zpn_rpc.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#include "argo/argo.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_rpc_compiled.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_misc/zpath_platform.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn/zpn_file_fetch.h"
#include "zpath_lib/et_translate.h"
#include "zpn/zpn_system_compiled.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_lib/zpath_instance.h"
#include "np_connector/np_connector_stats.h"

#include "zpn_zdx/zpn_zdx_webprobe_cache.h"
#include "zpn_zdx/zpn_zdx_webprobe_cache_https_ptls.h"
#include "zpn_zdx/zpn_zdx_webprobe_cache_compiled.h"
#include "zpn_zdx/zpn_zdx_webprobe_cache_https_ptls_compiled.h"

struct argo_structure_description *zpn_version_description;
struct argo_structure_description *zpn_version_ack_description;

struct argo_structure_description *zpn_client_authenticate_description;
struct argo_structure_description *zpn_client_authenticate_ack_description;
struct argo_structure_description *zpn_machine_tunnel_client_authenticate_description;
struct argo_structure_description *zpn_machine_tunnel_client_authenticate_ack_description;
struct argo_structure_description *zpn_trusted_client_authenticate_description;
struct argo_structure_description *zpn_trusted_client_authenticate_ack_description;
struct argo_structure_description *zpn_exporter_client_authenticate_description;
struct argo_structure_description *zpn_exporter_client_authenticate_ack_description;
struct argo_structure_description *zpn_bi_client_authenticate_description;
struct argo_structure_description *zpn_bi_client_authenticate_ack_description;
struct argo_structure_description *zpn_pbroker_client_authenticate_description;
struct argo_structure_description *zpn_pbroker_client_authenticate_ack_description;
struct argo_structure_description *zpn_pbroker_client_switch_to_cloud_description;
struct argo_structure_description *zpn_ec_client_authenticate_description;
struct argo_structure_description *zpn_ec_client_authenticate_ack_description;
struct argo_structure_description *zpn_vdi_client_authenticate_description;
struct argo_structure_description *zpn_vdi_client_authenticate_ack_description;
struct argo_structure_description *zpn_ia_client_authenticate_description;
struct argo_structure_description *zpn_ia_client_authenticate_ack_description;
struct argo_structure_description *zpn_client_connection_upgrade_description;
struct argo_structure_description *zpn_client_connection_upgrade_response_description;
struct argo_structure_description *zpn_broker_client_latency_probe_stats_description;
struct argo_structure_description *zpn_broker_vdi_auth_stats_description;
struct argo_structure_description *zpn_svcp_stats_description;
struct argo_structure_description *zpn_stepup_auth_stats_description;

struct argo_structure_description *zpn_zins_client_authenticate_description;
struct argo_structure_description *zpn_zins_client_authenticate_ack_description;
struct argo_structure_description *zpn_zia_identity_description;
struct argo_structure_description *zpn_broker_redirect_description;
struct argo_structure_description *zpn_instance_info_description;
struct argo_structure_description *zpn_client_app_description;
struct argo_structure_description *zpn_client_app_complete_description;
struct argo_structure_description *zpn_client_search_domain_description;
struct argo_structure_description *zpn_client_search_domain_complete_description;
struct argo_structure_description *zpn_client_search_domain_all_description;

struct argo_structure_description *zpn_client_config_updated_description;
struct argo_structure_description *zpn_client_aggregated_domains_description;

struct argo_structure_description *zpn_posture_profile_start_description;
struct argo_structure_description *zpn_posture_profile_description;
struct argo_structure_description *zpn_posture_profile_ack_description;
struct argo_structure_description *zpn_server_validated_cert_posture_check_request_description;
struct argo_structure_description *zpn_server_validated_cert_posture_check_response_payload_description;
struct argo_structure_description *zpn_server_validated_cert_posture_check_response_description;
struct argo_structure_description *zpn_server_validated_cert_posture_check_done_description;
struct argo_structure_description *zpn_svcp_pb_posture_profile_description;

struct argo_structure_description *zpn_trusted_networks_description;
struct argo_structure_description *zpn_trusted_networks_ack_description;

struct argo_structure_description *zpn_mtunnel_request_description;
struct argo_structure_description *zpn_mtunnel_request_int_description;
struct argo_structure_description *zpn_mtunnel_request_ack_description;
struct argo_structure_description *zpn_mtunnel_bind_description;
struct argo_structure_description *zpn_mtunnel_bind_ack_description;
struct argo_structure_description *zpn_mtunnel_tag_pause_description;
struct argo_structure_description *zpn_mtunnel_tag_resume_description;
struct argo_structure_description *zpn_mtunnel_end_description;
struct argo_structure_description *zpn_broker_request_description;
struct argo_structure_description *zpn_broker_request_ack_description;
struct argo_structure_description *zpn_app_route_info_description;
struct argo_structure_description *zpn_health_report_description;
struct argo_structure_description *zpn_app_route_registration_description;
struct argo_structure_description *zpn_app_route_log_description;
struct argo_structure_description *zpn_app_route_discovery_description;
struct argo_structure_description *zpn_trans_log_description;
struct argo_structure_description *zpn_health_log_description;
struct argo_structure_description *zpn_auth_log_description;
struct argo_structure_description *zpn_audit_log_description;
struct argo_structure_description *zpn_ast_auth_log_description;
struct argo_structure_description *zpn_sys_auth_log_description;
struct argo_structure_description *zpn_sitec_auth_log_description;
struct argo_structure_description *zpn_ast_auth_report_description;
struct argo_structure_description *zpn_tcp_info_report_description;
struct argo_structure_description *zpn_zrdt_info_report_description;
struct argo_structure_description *zpn_asst_environment_report_description;
struct argo_structure_description *zpn_pbroker_environment_report_description;
struct argo_structure_description *zpn_asst_active_control_connection_description;
struct argo_structure_description *zpn_dns_client_check_description;
struct argo_structure_description *zpn_app_client_check_description;
struct argo_structure_description *zpn_app_client_check_full_app_description;
struct argo_structure_description *zpn_dns_dispatch_check_description;
struct argo_structure_description *zpn_broker_dispatcher_c2c_app_check_description;
struct argo_structure_description *zpn_dns_assistant_check_description;
struct argo_structure_description *zpn_assistant_log_control_description;
struct argo_structure_description *zpn_assistant_stats_control_description;
struct argo_structure_description *zpn_assistant_restart_description;
struct argo_structure_description *zpn_assistant_status_report_description;
struct argo_structure_description *zpn_assistant_pvt_key_control_description;
struct argo_structure_description *zpn_assistant_gen_cert_control_description;
struct argo_structure_description *zpn_assistant_app_cert_key_control_description;
struct argo_structure_description *zpn_asst_state_description;
struct argo_structure_description *zpn_asst_restart_reason_description;
struct argo_structure_description *zpn_pbroker_status_report_description;
struct argo_structure_description *zpn_fohh_tlv_window_update_description;
struct argo_structure_description *zpn_fohh_tlv_window_update_batch_description;
struct argo_structure_description *zpn_dispatcher_status_description;
struct argo_structure_description *zpn_broker_info_description;
struct argo_structure_description *zpn_http_trans_log_description;
struct argo_structure_description *zpn_clientless_app_query_description;
struct argo_structure_description *zpn_clientless_app_query_ack_description;

struct argo_structure_description *zpn_pbroker_log_control_description;
struct argo_structure_description *zpn_pbroker_stats_control_description;
struct argo_structure_description *zpn_pbroker_stats_upload_description;
struct argo_structure_description *zpn_pbroker_trans_log_description;
struct argo_structure_description *zpn_private_broker_system_stats_description;
struct argo_structure_description *zpn_assistant_system_stats_description;
struct argo_structure_description *zpn_sitec_system_stats_description;
struct argo_structure_description *zpn_system_inventory_description;
struct argo_structure_description *zpn_assistant_data_stats_description;
struct argo_structure_description *zpn_assistant_app_stats_description;
struct argo_structure_description *zpn_assistant_rpc_stats_description;
struct argo_structure_description *zpn_assistant_scache_stats_description;
struct argo_structure_description *zpn_assistant_data_mtunnel_global_stats_description;
struct argo_structure_description *zpn_assistant_data_mtunnel_stats_description;
struct argo_structure_description *zpn_assistant_dns_stats_description;
struct argo_structure_description *zpn_broker_client_path_cache_stats_description;
struct argo_structure_description *zpn_broker_client_neg_path_cache_stats_description;
struct argo_structure_description *zpn_assistant_ncache_stats_description;

struct argo_structure_description *zpn_client_broker_app_registration_description;
struct argo_structure_description *zpn_client_private_broker_app_registration_description;
struct argo_structure_description *zpn_client_app_registration_notification_description;
struct argo_structure_description *zpn_broker_dispatcher_app_registration_description;
struct argo_structure_description *zpn_transit_req_description;
struct argo_structure_description *zpn_mtunnel_stats_description;

struct argo_structure_description *zpn_assistant_comprehensive_stats_description;
struct argo_structure_description *zpn_auth_state_log_description;
struct argo_structure_description *zpn_pbroker_file_stats_description;
struct argo_structure_description *zpn_pbroker_dns_dispatcher_stats_description;
struct argo_structure_description *zpn_pse_resiliency_stats_description;
struct argo_structure_description *zpn_pbroker_comprehensive_stats_description;
struct argo_structure_description *zpn_sitec_comprehensive_stats_description;
struct argo_structure_description *zpn_sitec_siem_log_stats_description;
struct argo_structure_description *zpn_sitec_siem_log_stats_per_conn_description;

struct argo_structure_description *zpn_file_fetch_key_description;
struct argo_structure_description *zpn_decrypt_key_request_description;
struct argo_structure_description *zpn_decrypt_key_response_description;

struct argo_structure_description *zpn_assistant_monitor_stats_description;
struct argo_structure_description *zpn_assistant_fproxy_stats_description;
struct argo_structure_description *zpn_a2pb_authentication_description;
struct argo_structure_description *zpn_private_broker_fproxy_stats_description;
struct argo_structure_description *zpn_a2pb_authentication_ack_description;
struct argo_structure_description *zpn_exporter_log_data_description;
struct argo_structure_description *zpn_exporter_data_ack_description;
struct argo_structure_description *zpn_exporter_log_data_ack_description;
struct argo_structure_description *zpn_assistant_zvm_stats_description;
struct argo_structure_description *zpn_assistant_upgrade_stats_description;
struct argo_structure_description *zpn_private_broker_upgrade_stats_description;
struct argo_structure_description *zpn_sitec_upgrade_stats_description;
struct argo_structure_description *np_connector_wireguard_stats_description;

struct argo_structure_description *zpn_private_broker_dr_stats_description;
struct argo_structure_description *zpn_assistant_dr_stats_description;
struct argo_structure_description *zpn_pbroker_mtunnel_stats_description;
struct argo_structure_description *zpn_pbclient_debug_stats_description;

struct argo_structure_description *zpn_exporter_pra_guac_proxy_data_description;

struct argo_structure_description *zpn_sitec_log_control_description;
struct argo_structure_description *zpn_sitec_stats_control_description;
struct argo_structure_description *zpn_sitec_status_report_description;
struct argo_structure_description *zpn_sitec_environment_report_description;
struct argo_structure_description *zpn_firedrill_stats_description;
struct argo_structure_description *zpn_sitec_firedrill_exit_description;
struct argo_structure_description *zpn_sitec_fproxy_stats_description;
struct argo_structure_description *zpn_client_state_description;
struct argo_structure_description *client_state_description;

struct argo_structure_description *zpn_client_state_description;
struct argo_structure_description *client_state_description;

struct argo_structure_description *zpn_mtunnel_data_mconn_stats_description;

struct argo_structure_description *zpn_zia_instance_info_description;
struct argo_structure_description *zpn_zia_instance_info_ack_description;
struct argo_structure_description *zpn_zia_location_update_description;
struct argo_structure_description *zpn_zia_location_update_ack_description;
struct argo_structure_description *zpn_zia_health_description;
struct argo_structure_description *zpn_zia_instance_data_description;
struct argo_structure_description *zpn_zia_instance_data_ack_description;

struct argo_structure_description *zpn_broker_dispatcher_zia_location_registration_description;
struct argo_structure_description *zpn_managed_chrome_payload_description;

struct argo_structure_description *zpn_add_proxy_description;
struct argo_structure_description *zpn_add_proxy_ack_description;
struct argo_structure_description *zpn_delete_proxy_description;

struct argo_structure_description *zpn_zdx_webprobe_cache_stats_description;
struct argo_structure_description *zpn_zdx_webprobe_cache_error_stats_description;
struct argo_structure_description *zpn_zdx_webprobe_cache_https_ptls_stats_description;
struct argo_structure_description *zpn_zdx_webprobe_cache_https_ptls_err_stats_description;
struct argo_structure_description *zms_flowlog_description;

struct argo_structure_description *zpn_broker_mission_critical_description;
struct argo_structure_description *zpn_broker_mission_critical_description_resp;

int zpn_rpc_init(void)
{
    /* Yeah, small race condition on double-init */
    static int initialized = 0;
    if (initialized) return ZPN_RESULT_NO_ERROR;

    if (zpath_system_stats_init()) {
        ZPN_LOG(AL_ERROR, "Could not initialize system stats");
        return ZPN_RESULT_ERR;
    }

    zpn_version_description = argo_register_global_structure(ZPN_VERSION_HELPER);
    zpn_version_ack_description = argo_register_global_structure(ZPN_VERSION_ACK_HELPER);
    zpn_client_authenticate_description = argo_register_global_structure(ZPN_CLIENT_AUTHENTICATE_HELPER);
    zpn_client_authenticate_ack_description = argo_register_global_structure(ZPN_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_machine_tunnel_client_authenticate_description = argo_register_global_structure(ZPN_MACHINE_TUNNEL_CLIENT_AUTHENTICATE_HELPER);
    zpn_machine_tunnel_client_authenticate_ack_description = argo_register_global_structure(ZPN_MACHINE_TUNNEL_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_trusted_client_authenticate_description = argo_register_global_structure(ZPN_TRUSTED_CLIENT_AUTHENTICATE_HELPER);
    zpn_trusted_client_authenticate_ack_description = argo_register_global_structure(ZPN_TRUSTED_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_exporter_client_authenticate_description = argo_register_global_structure(ZPN_EXPORTER_CLIENT_AUTHENTICATE_HELPER);
    zpn_exporter_client_authenticate_ack_description = argo_register_global_structure(ZPN_EXPORTER_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_bi_client_authenticate_description = argo_register_global_structure(ZPN_BI_CLIENT_AUTHENTICATE_HELPER);
    zpn_bi_client_authenticate_ack_description = argo_register_global_structure(ZPN_BI_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_pbroker_client_authenticate_description = argo_register_global_structure(ZPN_PBROKER_CLIENT_AUTHENTICATE_HELPER);
    zpn_pbroker_client_authenticate_ack_description = argo_register_global_structure(ZPN_PBROKER_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_pbroker_client_switch_to_cloud_description = argo_register_global_structure(ZPN_PBROKER_CLIENT_SWITCH_TO_CLOUD_HELPER);
    zpn_ec_client_authenticate_description = argo_register_global_structure(ZPN_EC_CLIENT_AUTHENTICATE_HELPER);
    zpn_vdi_client_authenticate_description = argo_register_global_structure(ZPN_VDI_CLIENT_AUTHENTICATE_HELPER);
    zpn_ec_client_authenticate_ack_description = argo_register_global_structure(ZPN_EC_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_vdi_client_authenticate_ack_description = argo_register_global_structure(ZPN_VDI_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_ia_client_authenticate_description = argo_register_global_structure(ZPN_IA_CLIENT_AUTHENTICATE_HELPER);
    zpn_ia_client_authenticate_ack_description = argo_register_global_structure(ZPN_IA_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_client_connection_upgrade_description = argo_register_global_structure(ZPN_CLIENT_CONNECTION_UPGRADE_HELPER);
    zpn_client_connection_upgrade_response_description = argo_register_global_structure(ZPN_CLIENT_CONNECTION_UPGRADE_RESPONSE_HELPER);

    zpn_zins_client_authenticate_description = argo_register_global_structure(ZPN_ZINS_CLIENT_AUTHENTICATE_HELPER);
    zpn_zins_client_authenticate_ack_description = argo_register_global_structure(ZPN_ZINS_CLIENT_AUTHENTICATE_ACK_HELPER);
    zpn_zia_identity_description = argo_register_global_structure(ZPN_ZIA_IDENTITY_HELPER);

    zpn_broker_redirect_description = argo_register_global_structure(ZPN_BROKER_REDIRECT_HELPER);
    zpn_instance_info_description = argo_register_global_structure(ZPN_INSTANCE_INFO_HELPER);
    zpn_client_app_description = argo_register_global_structure(ZPN_CLIENT_APP_HELPER);
    zpn_client_app_complete_description = argo_register_global_structure(ZPN_CLIENT_APP_COMPLETE_HELPER);
    zpn_client_search_domain_description = argo_register_global_structure(ZPN_CLIENT_SEARCH_DOMAIN_HELPER);
    zpn_client_search_domain_complete_description = argo_register_global_structure(ZPN_CLIENT_SEARCH_DOMAIN_COMPLETE_HELPER);
    zpn_client_search_domain_all_description = argo_register_global_structure(ZPN_CLIENT_SEARCH_DOMAIN_ALL_HELPER);
    zpn_client_config_updated_description = argo_register_global_structure(ZPN_CLIENT_CONFIG_UPDATED_HELPER);
    zpn_client_aggregated_domains_description = argo_register_global_structure(ZPN_CLIENT_AGGREGATED_DOMAINS_HELPER);
    zpn_posture_profile_start_description = argo_register_global_structure(ZPN_POSTURE_PROFILE_START_HELPER);
    zpn_posture_profile_description = argo_register_global_structure(ZPN_POSTURE_PROFILE_HELPER);
    zpn_posture_profile_ack_description = argo_register_global_structure(ZPN_POSTURE_PROFILE_ACK_HELPER);
    zpn_server_validated_cert_posture_check_request_description = argo_register_global_structure(ZPN_SERVER_VALIDATED_CERT_POSTURE_CHECK_REQUEST_HELPER);
    zpn_server_validated_cert_posture_check_response_payload_description = argo_register_global_structure(ZPN_SERVER_VALIDATED_CERT_POSTURE_CHECK_RESPONSE_PAYLOAD_HELPER);
    zpn_server_validated_cert_posture_check_response_description = argo_register_global_structure(ZPN_SERVER_VALIDATED_CERT_POSTURE_CHECK_RESPONSE_HELPER);
    zpn_server_validated_cert_posture_check_done_description = argo_register_global_structure(ZPN_SERVER_VALIDATED_CERT_POSTURE_CHECK_DONE_HELPER);
    zpn_svcp_pb_posture_profile_description = argo_register_global_structure(ZPN_SVCP_PB_POSTURE_PROFILE_HELPER);
    zpn_trusted_networks_description = argo_register_global_structure(ZPN_TRUSTED_NETWORKS_HELPER);
    zpn_trusted_networks_ack_description = argo_register_global_structure(ZPN_TRUSTED_NETWORKS_ACK_HELPER);
    zpn_mtunnel_request_description = argo_register_global_structure(ZPN_MTUNNEL_REQUEST_HELPER);
    zpn_mtunnel_request_int_description = argo_register_global_structure(ZPN_MTUNNEL_REQUEST_INT_HELPER);
    zpn_mtunnel_request_ack_description = argo_register_global_structure(ZPN_MTUNNEL_REQUEST_ACK_HELPER);
    zpn_mtunnel_bind_description = argo_register_global_structure(ZPN_MTUNNEL_BIND_HELPER);
    zpn_mtunnel_bind_ack_description = argo_register_global_structure(ZPN_MTUNNEL_BIND_ACK_HELPER);
    zpn_mtunnel_end_description = argo_register_global_structure(ZPN_MTUNNEL_END_HELPER);
    zpn_mtunnel_tag_pause_description = argo_register_global_structure(ZPN_MTUNNEL_TAG_PAUSE_HELPER);
    zpn_mtunnel_tag_resume_description = argo_register_global_structure(ZPN_MTUNNEL_TAG_RESUME_HELPER);
    zpn_broker_request_description = argo_register_global_structure(ZPN_BROKER_REQUEST_HELPER);
    zpn_broker_request_ack_description = argo_register_global_structure(ZPN_BROKER_REQUEST_ACK_HELPER);
    zpn_app_route_info_description = argo_register_global_structure(ZPN_APP_ROUTE_INFO_HELPER);
    zpn_health_report_description = argo_register_global_structure(ZPN_HEALTH_REPORT_HELPER);
    zpn_app_route_registration_description = argo_register_global_structure(ZPN_APP_ROUTE_REGISTRATION_HELPER);
    zpn_app_route_log_description = argo_register_global_structure(ZPN_APP_ROUTE_LOG_HELPER);
    zpn_app_route_discovery_description = argo_register_global_structure(ZPN_APP_ROUTE_DISCOVERY_HELPER);
    zpn_trans_log_description = argo_register_global_structure(ZPN_TRANS_LOG_HELPER);
    zpn_health_log_description = argo_register_global_structure(ZPN_HEALTH_LOG_HELPER);
    zpn_auth_log_description = argo_register_global_structure(ZPN_AUTH_LOG_HELPER);
    zpn_audit_log_description = argo_register_global_structure(ZPN_AUDIT_LOG_HELPER);
    zpn_ast_auth_log_description = argo_register_global_structure(ZPN_AST_AUTH_LOG_HELPER);
    zpn_sys_auth_log_description = argo_register_global_structure(ZPN_SYS_AUTH_LOG_HELPER);
    zpn_sitec_auth_log_description = argo_register_global_structure(ZPN_SITEC_AUTH_LOG_HELPER);
    zpn_ast_auth_report_description = argo_register_global_structure(ZPN_AST_AUTH_REPORT_HELPER);
    zpn_tcp_info_report_description = argo_register_global_structure(ZPN_TCP_INFO_REPORT_HELPER);
    zpn_zrdt_info_report_description = argo_register_global_structure(ZPN_ZRDT_INFO_REPORT_HELPER);
    zpn_asst_environment_report_description = argo_register_global_structure(ZPN_ASST_ENVIRONMENT_REPORT_HELPER);
    zpn_pbroker_environment_report_description = argo_register_global_structure(ZPN_PBROKER_ENVIRONMENT_REPORT_HELPER);
    zpn_asst_active_control_connection_description = argo_register_global_structure(ZPN_ASST_ACTIVE_CONTROL_CONNECTION_HELPER);
    zpn_dns_client_check_description = argo_register_global_structure(ZPN_DNS_CLIENT_CHECK_HELPER);
    zpn_app_client_check_description = argo_register_global_structure(ZPN_APP_CLIENT_CHECK_HELPER);
    zpn_app_client_check_full_app_description = argo_register_global_structure(ZPN_APP_CLIENT_CHECK_FULL_APP_HELPER);
    zpn_dns_dispatch_check_description = argo_register_global_structure(ZPN_DNS_DISPATCH_CHECK_HELPER);
    zpn_broker_dispatcher_c2c_app_check_description = argo_register_global_structure(ZPN_BROKER_DISPATCHER_C2C_APP_CHECK_HELPER);
    zpn_dns_assistant_check_description = argo_register_global_structure(ZPN_DNS_ASSISTANT_CHECK_HELPER);
    zpn_assistant_log_control_description = argo_register_global_structure(ZPN_ASSISTANT_LOG_CONTROL_HELPER);
    zpn_assistant_stats_control_description = argo_register_global_structure(ZPN_ASSISTANT_STATS_CONTROL_HELPER);
    zpn_assistant_restart_description = argo_register_global_structure(ZPN_ASSISTANT_RESTART_HELPER);
    zpn_assistant_status_report_description = argo_register_global_structure(ZPN_ASSISTANT_STATUS_REPORT_HELPER);
    zpn_assistant_pvt_key_control_description = argo_register_global_structure(ZPN_ASSISTANT_PVT_KEY_CONTROL_HELPER);
    zpn_assistant_gen_cert_control_description = argo_register_global_structure(ZPN_ASSISTANT_GEN_CERT_CONTROL_HELPER);
    zpn_assistant_app_cert_key_control_description = argo_register_global_structure(ZPN_ASSISTANT_APP_CERT_KEY_CONTROL_HELPER);
    zpn_asst_state_description = argo_register_global_structure(ZPN_ASST_STATE_HELPER);
    zpn_asst_restart_reason_description = argo_register_global_structure(ZPN_ASST_RESTART_REASON_HELPER);
    zpn_pbroker_status_report_description = argo_register_global_structure(ZPN_PBROKER_STATUS_REPORT_HELPER);
    zpn_fohh_tlv_window_update_description = argo_register_global_structure(ZPN_FOHH_TLV_WINDOW_UPDATE_HELPER);
    zpn_fohh_tlv_window_update_batch_description = argo_register_global_structure(ZPN_FOHH_TLV_WINDOW_UPDATE_BATCH_HELPER);
    zpn_dispatcher_status_description = argo_register_global_structure(ZPN_DISPATCHER_STATUS_HELPER);
    zpn_broker_info_description = argo_register_global_structure(ZPN_BROKER_INFO_HELPER);
    zpn_http_trans_log_description = argo_register_global_structure(ZPN_HTTP_TRANS_LOG_HELPER);
    zpn_clientless_app_query_description = argo_register_global_structure(ZPN_CLIENTLESS_APP_QUERY_HELPER);
    zpn_clientless_app_query_ack_description = argo_register_global_structure(ZPN_CLIENTLESS_APP_QUERY_ACK_HELPER);
    zpn_pbroker_stats_control_description = argo_register_global_structure(ZPN_PBROKER_STATS_CONTROL_HELPER);
    zpn_pbroker_log_control_description = argo_register_global_structure(ZPN_PBROKER_LOG_CONTROL_HELPER);
    zpn_pbroker_trans_log_description = argo_register_global_structure(ZPN_PBROKER_TRANS_LOG_HELPER);
    zpn_private_broker_system_stats_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_SYSTEM_STATS_HELPER);
    zpn_assistant_system_stats_description = argo_register_global_structure(ZPN_ASSISTANT_SYSTEM_STATS_HELPER);
    zpn_sitec_system_stats_description = argo_register_global_structure(ZPN_SITEC_SYSTEM_STATS_HELPER);
    zpn_assistant_data_stats_description = argo_register_global_structure(ZPN_ASSISTANT_DATA_STATS_HELPER);
    zpn_assistant_rpc_stats_description = argo_register_global_structure(ZPN_ASSISTANT_RPC_STATS_HELPER);
    zpn_assistant_scache_stats_description = argo_register_global_structure(ZPN_ASSISTANT_SCACHE_STATS_HELPER);
    zpn_assistant_app_stats_description = argo_register_global_structure(ZPN_ASSISTANT_APP_STATS_HELPER);
    zpn_assistant_data_mtunnel_global_stats_description = argo_register_global_structure(ZPN_ASSISTANT_DATA_MTUNNEL_GLOBAL_STATS_HELPER);
    zpn_assistant_data_mtunnel_stats_description = argo_register_global_structure(ZPN_ASSISTANT_DATA_MTUNNEL_STATS_HELPER);
    zpn_assistant_dns_stats_description = argo_register_global_structure(ZPN_ASSISTANT_DNS_STATS_HELPER);
    zpn_broker_client_path_cache_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_PATH_CACHE_STATS_HELPER);
    zpn_broker_client_neg_path_cache_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_NEG_PATH_CACHE_STATS_HELPER);
    zpn_broker_client_latency_probe_stats_description = argo_register_global_structure(ZPN_BROKER_CLIENT_LATENCY_PROBE_STATS_HELPER);
    zpn_broker_vdi_auth_stats_description = argo_register_global_structure(ZPN_BROKER_VDI_AUTHENTICATION_STATS_HELPER);
    zpn_svcp_stats_description = argo_register_global_structure(ZPN_SVCP_STATS_HELPER);
    zpn_stepup_auth_stats_description = argo_register_global_structure(ZPN_STEPUP_AUTH_STATS_HELPER);


    zpn_client_broker_app_registration_description = argo_register_global_structure(ZPN_CLIENT_BROKER_APP_REGISTRATION_HELPER);
    zpn_client_private_broker_app_registration_description = argo_register_global_structure(ZPN_CLIENT_PRIVATE_BROKER_APP_REGISTRATION_HELPER);
    zpn_client_app_registration_notification_description = argo_register_global_structure(ZPN_CLIENT_APP_REGISTRATION_NOTIFICATION_HELPER);
    zpn_broker_dispatcher_app_registration_description = argo_register_global_structure(ZPN_BROKER_DISPATCHER_APP_REGISTRATION_HELPER);
    zpn_transit_req_description = argo_register_global_structure(ZPN_TRANSIT_REQ_HELPER);
    zpn_mtunnel_stats_description = argo_register_global_structure(ZPN_MTUNNEL_STATS_HELPER);

    zpn_assistant_comprehensive_stats_description = argo_register_global_structure(ZPN_ASSISTANT_COMPREHENSIVE_STATS_HELPER);
    zpn_pbroker_comprehensive_stats_description = argo_register_global_structure(ZPN_PBROKER_COMPREHENSIVE_STATS_HELPER);
    zpn_sitec_comprehensive_stats_description = argo_register_global_structure(ZPN_SITEC_COMPREHENSIVE_STATS_HELPER);
    zpn_pbroker_file_stats_description = argo_register_global_structure(ZPN_PBROKER_FILE_STATS_HELPER);
    zpn_pbroker_dns_dispatcher_stats_description = argo_register_global_structure(ZPN_PBROKER_DNS_DISPATCHER_STATS_HELPER);
    zpn_pse_resiliency_stats_description = argo_register_global_structure(ZPN_PSE_RESILIENCY_STATS_HELPER);
    zpn_system_inventory_description = argo_register_global_structure(ZPN_SYSTEM_INVENTORY_STATS_HELPER);
    zpn_assistant_ncache_stats_description = argo_register_global_structure(ZPN_ASSISTANT_NCACHE_STATS_HELPER);
    zpn_auth_state_log_description = argo_register_global_structure(ZPN_AUTH_STATE_LOG_HELPER);

    zpn_file_fetch_key_description = argo_register_global_structure(ZPN_FILE_FETCH_KEY_HELPER);
    zpn_decrypt_key_request_description = argo_register_global_structure(ZPN_DECRYPT_KEY_REQUEST_HELPER);
    zpn_decrypt_key_response_description = argo_register_global_structure(ZPN_DECRYPT_KEY_RESPONSE_HELPER);

    zpn_assistant_monitor_stats_description = argo_register_global_structure(ZPN_ASSISTANT_MONITOR_STATS_HELPER);
    zpn_assistant_fproxy_stats_description = argo_register_global_structure(ZPN_ASSISTANT_FPROXY_STATS_HELPER);
    zpn_exporter_log_data_description = argo_register_global_structure(ZPN_EXPORTER_LOG_DATA_HELPER);
    zpn_exporter_data_ack_description = argo_register_global_structure(ZPN_EXPORTER_DATA_ACK_HELPER);
    zpn_exporter_log_data_ack_description = argo_register_global_structure(ZPN_EXPORTER_LOG_DATA_ACK_HELPER);


    zpn_a2pb_authentication_description = argo_register_global_structure(ZPN_A2PB_AUTHENTICATION_HELPER);
    zpn_a2pb_authentication_ack_description = argo_register_global_structure(ZPN_A2PB_AUTHENTICATION_ACK_HELPER);
    zpn_assistant_zvm_stats_description = argo_register_global_structure(ZPN_ASSISTANT_ZVM_STATS_HELPER);
    zpn_assistant_upgrade_stats_description = argo_register_global_structure(ZPN_ASSISTANT_UPGRADE_STATS_HELPER);
    np_connector_wireguard_stats_description = argo_register_global_structure(NP_CONNECTOR_WIREGUARD_STATS_HELPER);

    zpn_private_broker_dr_stats_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_DR_STATS_HELPER);
    zpn_assistant_dr_stats_description = argo_register_global_structure(ZPN_ASSISTANT_DR_STATS_HELPER);
    zpn_pbroker_mtunnel_stats_description = argo_register_global_structure(ZPN_PBROKER_MTUNNEL_STATS_HELPER);
    zpn_private_broker_fproxy_stats_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_FPROXY_STATS_HELPER);
    zpn_pbclient_debug_stats_description = argo_register_global_structure(ZPN_PBCLIENT_DEBUG_STATS_HELPER);
    zpn_exporter_pra_guac_proxy_data_description = argo_register_global_structure(ZPN_EXPORTER_PRA_GUAC_PROXY_DATA_HELPER);
    zpn_client_state_description = argo_register_global_structure(ZPN_CLIENT_STATE_HELPER);
    client_state_description = argo_register_global_structure(CLIENT_STATE_HELPER);
    zpn_private_broker_upgrade_stats_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_UPGRADE_STATS_HELPER);

    zpn_sitec_stats_control_description = argo_register_global_structure(ZPN_SITEC_STATS_CONTROL_HELPER);
    zpn_sitec_log_control_description = argo_register_global_structure(ZPN_SITEC_LOG_CONTROL_HELPER);
    zpn_sitec_status_report_description = argo_register_global_structure(ZPN_SITEC_STATUS_REPORT_HELPER);
    zpn_sitec_environment_report_description = argo_register_global_structure(ZPN_SITEC_ENVIRONMENT_REPORT_HELPER);
    zpn_sitec_firedrill_exit_description = argo_register_global_structure(ZPN_SITEC_FIREDRILL_EXIT_HELPER);
    zpn_firedrill_stats_description = argo_register_global_structure(ZPN_FIREDRILL_STATS_HELPER);
    zpn_sitec_siem_log_stats_description = argo_register_global_structure(ZPN_SITEC_SIEM_LOG_STATS_HELPER);
    zpn_sitec_siem_log_stats_per_conn_description = argo_register_global_structure(ZPN_SITEC_SIEM_LOG_STATS_PER_CONN_HELPER);
    zpn_sitec_fproxy_stats_description = argo_register_global_structure(ZPN_SITEC_FPROXY_STATS_HELPER);
    zpn_sitec_upgrade_stats_description = argo_register_global_structure(ZPN_SITEC_UPGRADE_STATS_HELPER);

    zpn_mtunnel_data_mconn_stats_description = argo_register_global_structure(ZPN_MTUNNEL_DATA_MCONN_STATS_HELPER);

    zpn_zia_instance_info_description = argo_register_global_structure(ZPN_ZIA_INSTANCE_INFO_HELPER);
    zpn_zia_instance_info_ack_description = argo_register_global_structure(ZPN_ZIA_INSTANCE_INFO_ACK_HELPER);
    zpn_zia_location_update_description = argo_register_global_structure(ZPN_ZIA_LOCATION_UPDATE_HELPER);
    zpn_zia_location_update_ack_description = argo_register_global_structure(ZPN_ZIA_LOCATION_UPDATE_ACK_HELPER);
    zpn_zia_health_description = argo_register_global_structure(ZPN_ZIA_HEALTH_HELPER);

    zpn_zia_instance_data_description = argo_register_global_structure(ZPN_ZIA_INSTANCE_DATA_HELPER);
    zpn_zia_instance_data_ack_description = argo_register_global_structure(ZPN_ZIA_INSTANCE_DATA_ACK_HELPER);
    zpn_broker_dispatcher_zia_location_registration_description = argo_register_global_structure(ZPN_BROKER_DISPATCHER_ZIA_LOCATION_REGISTRATION_HELPER);
    zpn_managed_chrome_payload_description = argo_register_global_structure(ZPN_MANAGED_CHROME_PAYLOAD_HELPER);

    zpn_add_proxy_description = argo_register_global_structure(ZPN_ADD_PROXY_HELPER);
    zpn_add_proxy_ack_description = argo_register_global_structure(ZPN_ADD_PROXY_ACK_HELPER);
    zpn_delete_proxy_description = argo_register_global_structure(ZPN_DELETE_PROXY_HELPER);

    zpn_zdx_webprobe_cache_stats_description = argo_register_global_structure(ZPN_ZDX_WEBPROBE_CACHE_STATS_HELPER);
    zpn_zdx_webprobe_cache_error_stats_description = argo_register_global_structure(ZPN_ZDX_WEBPROBE_CACHE_ERROR_STATS_HELPER);
    zpn_zdx_webprobe_cache_https_ptls_stats_description = argo_register_global_structure(ZPN_ZDX_WEBPROBE_CACHE_HTTPS_PTLS_STATS_HELPER);
    zpn_zdx_webprobe_cache_https_ptls_err_stats_description = argo_register_global_structure(ZPN_ZDX_WEBPROBE_CACHE_HTTPS_PTLS_ERR_STATS_HELPER);
    zms_flowlog_description = argo_register_global_structure(ZMS_FLOWLOG_HELPER);

    zpn_broker_mission_critical_description = argo_register_global_structure(ZPN_BROKER_MISSION_CRITICAL_HELPER);
    zpn_broker_mission_critical_description_resp = argo_register_global_structure(ZPN_BROKER_MISSION_CRITICAL_RESP_HELPER);

    if (!zpn_version_description ||
        !zpn_version_ack_description ||
        !zpn_client_authenticate_description ||
        !zpn_client_authenticate_ack_description ||
        !zpn_machine_tunnel_client_authenticate_description ||
        !zpn_machine_tunnel_client_authenticate_ack_description ||
        !zpn_trusted_client_authenticate_description ||
        !zpn_trusted_client_authenticate_ack_description ||
        !zpn_exporter_client_authenticate_description ||
        !zpn_exporter_client_authenticate_ack_description ||
        !zpn_bi_client_authenticate_description ||
        !zpn_bi_client_authenticate_ack_description ||
        !zpn_pbroker_client_authenticate_description ||
        !zpn_pbroker_client_authenticate_ack_description ||
        !zpn_pbroker_client_switch_to_cloud_description ||
        !zpn_ec_client_authenticate_description ||
        !zpn_vdi_client_authenticate_description ||
        !zpn_ec_client_authenticate_ack_description ||
        !zpn_vdi_client_authenticate_ack_description ||
        !zpn_ia_client_authenticate_description ||
        !zpn_ia_client_authenticate_ack_description ||
        !zpn_broker_redirect_description ||
        !zpn_instance_info_description ||
        !zpn_client_app_description ||
        !zpn_client_app_complete_description ||
        !zpn_client_search_domain_description ||
        !zpn_client_search_domain_complete_description ||
        !zpn_client_search_domain_all_description ||
        !zpn_client_config_updated_description ||
        !zpn_client_aggregated_domains_description ||
        !zpn_posture_profile_start_description ||
        !zpn_posture_profile_description ||
        !zpn_posture_profile_ack_description ||
        !zpn_server_validated_cert_posture_check_request_description ||
        !zpn_server_validated_cert_posture_check_response_payload_description ||
        !zpn_server_validated_cert_posture_check_response_description ||
        !zpn_server_validated_cert_posture_check_done_description ||
        !zpn_trusted_networks_description ||
        !zpn_trusted_networks_ack_description ||
        !zpn_mtunnel_request_description ||
        !zpn_mtunnel_request_int_description ||
        !zpn_mtunnel_request_ack_description ||
        !zpn_mtunnel_bind_description ||
        !zpn_mtunnel_bind_ack_description ||
        !zpn_mtunnel_end_description ||
        !zpn_mtunnel_tag_pause_description ||
        !zpn_mtunnel_tag_resume_description ||
        !zpn_broker_request_description ||
        !zpn_broker_request_ack_description ||
        !zpn_app_route_info_description ||
        !zpn_health_report_description ||
        !zpn_app_route_registration_description ||
        !zpn_app_route_log_description ||
        !zpn_app_route_discovery_description ||
        !zpn_trans_log_description ||
        !zpn_health_log_description ||
        !zpn_auth_log_description ||
        !zpn_audit_log_description ||
        !zpn_ast_auth_log_description ||
        !zpn_sys_auth_log_description ||
        !zpn_sitec_auth_log_description ||
        !zpn_ast_auth_report_description ||
        !zpn_tcp_info_report_description ||
        !zpn_zrdt_info_report_description ||
        !zpn_asst_environment_report_description ||
        !zpn_pbroker_environment_report_description ||
        !zpn_asst_active_control_connection_description ||
        !zpn_dns_client_check_description ||
        !zpn_app_client_check_description ||
        !zpn_app_client_check_full_app_description ||
        !zpn_dns_dispatch_check_description ||
        !zpn_broker_dispatcher_c2c_app_check_description ||
        !zpn_dns_assistant_check_description ||
        !zpn_assistant_log_control_description ||
        !zpn_assistant_stats_control_description ||
        !zpn_assistant_restart_description ||
        !zpn_assistant_status_report_description ||
        !zpn_assistant_pvt_key_control_description ||
        !zpn_assistant_gen_cert_control_description||
        !zpn_assistant_app_cert_key_control_description||
        !zpn_asst_state_description ||
        !zpn_asst_restart_reason_description ||
        !zpn_pbroker_status_report_description ||
        !zpn_fohh_tlv_window_update_description ||
        !zpn_fohh_tlv_window_update_batch_description ||
        !zpn_dispatcher_status_description ||
        !zpn_broker_info_description ||
        !zpn_http_trans_log_description ||
        !zpn_clientless_app_query_description ||
        !zpn_clientless_app_query_ack_description ||
        !zpn_pbroker_stats_control_description ||
        !zpn_pbroker_log_control_description ||
        !zpn_pbroker_trans_log_description ||
        !zpn_private_broker_system_stats_description ||
        !zpn_assistant_system_stats_description ||
        !zpn_sitec_system_stats_description ||
        !zpn_assistant_data_stats_description ||
        !zpn_assistant_rpc_stats_description ||
        !zpn_assistant_scache_stats_description ||
        !zpn_assistant_app_stats_description ||
        !zpn_assistant_data_mtunnel_global_stats_description ||
        !zpn_assistant_data_mtunnel_stats_description ||
        !zpn_client_connection_upgrade_description ||
        !zpn_client_connection_upgrade_response_description ||
        !zpn_broker_client_latency_probe_stats_description ||
        !zpn_broker_vdi_auth_stats_description ||
        !zpn_svcp_stats_description ||
        !zpn_stepup_auth_stats_description ||
        !zpn_broker_client_path_cache_stats_description||
        !zpn_broker_client_neg_path_cache_stats_description ||
        !zpn_client_broker_app_registration_description ||
        !zpn_client_private_broker_app_registration_description ||
        !zpn_client_app_registration_notification_description ||
        !zpn_broker_dispatcher_app_registration_description ||
        !zpn_transit_req_description ||
        !zpn_mtunnel_stats_description ||
        !zpn_assistant_comprehensive_stats_description ||
        !zpn_pbroker_comprehensive_stats_description ||
        !zpn_sitec_comprehensive_stats_description ||
        !zpn_pbroker_file_stats_description ||
        !zpn_pbroker_dns_dispatcher_stats_description ||
        !zpn_pse_resiliency_stats_description ||
        !zpn_assistant_dns_stats_description ||
        !zpn_system_inventory_description ||
        !zpn_assistant_ncache_stats_description ||
        !zpn_auth_state_log_description ||
        !zpn_a2pb_authentication_description ||
        !zpn_a2pb_authentication_ack_description ||
        !zpn_assistant_zvm_stats_description ||
        !zpn_assistant_upgrade_stats_description ||
        !zpn_private_broker_upgrade_stats_description ||
        !zpn_sitec_upgrade_stats_description ||
        !np_connector_wireguard_stats_description ||

        !zpn_file_fetch_key_description ||
        !zpn_decrypt_key_request_description ||
        !zpn_decrypt_key_response_description ||

        !zpn_assistant_monitor_stats_description ||
        !zpn_assistant_fproxy_stats_description ||
        !zpn_private_broker_fproxy_stats_description ||
        !zpn_exporter_log_data_description ||
        !zpn_exporter_data_ack_description ||
        !zpn_exporter_log_data_ack_description ||
        !zpn_private_broker_dr_stats_description ||
        !zpn_assistant_dr_stats_description ||
        !zpn_pbroker_mtunnel_stats_description ||
        !zpn_pbclient_debug_stats_description ||
        !zpn_exporter_pra_guac_proxy_data_description ||
        !zpn_sitec_stats_control_description ||
        !zpn_sitec_log_control_description ||
        !zpn_sitec_status_report_description ||
        !zpn_sitec_environment_report_description ||
        !zpn_sitec_firedrill_exit_description ||
        !zpn_firedrill_stats_description ||
        !zpn_sitec_siem_log_stats_description ||
        !zpn_sitec_siem_log_stats_per_conn_description ||
        !zpn_sitec_fproxy_stats_description ||
        !zpn_client_state_description ||
        !client_state_description ||
        !zpn_mtunnel_data_mconn_stats_description ||
        !zpn_zia_instance_info_description ||
        !zpn_zia_instance_info_ack_description ||
        !zpn_zia_location_update_description ||
        !zpn_zia_location_update_ack_description ||
        !zpn_zia_health_description ||
        !zpn_zia_instance_data_description ||
        !zpn_zia_instance_data_ack_description ||
        !zpn_broker_dispatcher_zia_location_registration_description ||
        !zpn_managed_chrome_payload_description ||
        !zpn_add_proxy_description ||
        !zpn_add_proxy_ack_description ||
        !zpn_delete_proxy_description ||
        !zpn_zdx_webprobe_cache_stats_description ||
        !zpn_zdx_webprobe_cache_error_stats_description ||
        !zpn_zdx_webprobe_cache_https_ptls_stats_description ||
        !zpn_zdx_webprobe_cache_https_ptls_err_stats_description ||
        !zms_flowlog_description ||
        !zpn_broker_mission_critical_description ||
        !zpn_broker_mission_critical_description_resp ||
        0) {
        return ZPN_RESULT_ERR;
    }
    initialized = 1;
    return ZPN_RESULT_NO_ERROR;
}

int tlv_argo_serialize(struct zpn_tlv *tlv,
                       struct argo_structure_description *description,
                       void *data,
                       int64_t conn_incarnation,
                       enum fohh_queue_element_type elem_type)
{
    if (!tlv) {
        ZPN_DEBUG_RPC("Cannot serialize, NULL tlv");
        return ZPN_RESULT_ERR;
    }

    if (tlv->type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = tlv->conn.f_conn;

        if (!f_conn) {
            ZPN_DEBUG_RPC("Cannot serialize, NULL f_conn");
            return ZPN_RESULT_ERR;
        }

        if (conn_incarnation && (conn_incarnation != fohh_connection_incarnation(f_conn))) {
            return ZPN_RESULT_NO_ERROR;
        }

        return fohh_argo_serialize(f_conn, description, data, conn_incarnation, elem_type);
    } else if (tlv->type == zpn_zrdt_tlv) {
        struct zrdt_conn *z_conn = tlv->conn.z_conn;

        if (!z_conn) {
            ZPN_DEBUG_RPC("Cannot serialize, NULL z_conn");
            return ZPN_RESULT_ERR;
        }

        if (conn_incarnation && (conn_incarnation != zrdt_conn_incarnation(z_conn))) {
            return ZPN_RESULT_NO_ERROR;
        }

        return zpn_zrdt_argo_serialize(z_conn, description, data, conn_incarnation, elem_type);
    } else {
        ZPN_LOG(AL_NOTICE, "Unknow tlv type: %d", tlv->type);
        return ZPN_RESULT_ERR;
    }
}

int tlv_argo_serialize_object(struct zpn_tlv *tlv,
                              struct argo_object *object,
                              int64_t conn_incarnation,
                              enum fohh_queue_element_type elem_type)
{
    if (!tlv) {
        ZPN_DEBUG_RPC("Cannot serialize, NULL tlv");
        return ZPN_RESULT_ERR;
    }

    if (tlv->type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = tlv->conn.f_conn;

        if (!f_conn) {
            ZPN_DEBUG_RPC("Cannot serialize, NULL f_conn");
            return ZPN_RESULT_ERR;
        }

        if (conn_incarnation && (conn_incarnation != fohh_connection_incarnation(f_conn))) {
            return ZPN_RESULT_NO_ERROR;
        }

        return fohh_argo_serialize_object(f_conn, object, conn_incarnation, elem_type);
    } else if (tlv->type == zpn_zrdt_tlv) {
        struct zrdt_conn *z_conn = tlv->conn.z_conn;

        if (!z_conn) {
            ZPN_DEBUG_RPC("Cannot serialize, NULL z_conn");
            return ZPN_RESULT_ERR;
        }

        if (conn_incarnation && (conn_incarnation != zrdt_conn_incarnation(z_conn))) {
            return ZPN_RESULT_NO_ERROR;
        }

        return zpn_zrdt_argo_serialize_object(z_conn, object, conn_incarnation, elem_type);
    } else {
        ZPN_LOG(AL_NOTICE, "Unknow tlv type: %d", tlv->type);
        return ZPN_RESULT_ERR;
    }
}

int zpn_send_zpn_version(struct zpn_tlv *tlv,
                         int64_t conn_incarnation,
                         int32_t version_major,
                         int32_t version_minor)
{
    struct zpn_version data;
    memset(&data, 0, sizeof(data));

    data.version_major = version_major;
    data.version_minor = version_minor;
    data.ver_str = ZPATH_VERSION;

    return tlv_argo_serialize(tlv, zpn_version_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_version_ack(struct zpn_tlv *tlv,
                             int64_t conn_incarnation,
                             const char *error)
{
    struct zpn_version_ack data;
    memset(&data, 0, sizeof(data));

    data.error = error;
    return tlv_argo_serialize(tlv, zpn_version_ack_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_client_state(struct zpn_tlv *tlv,
                              int64_t conn_incarnation,
                              const char *client_state)
{
    struct zpn_client_state data;
    memset(&data, 0, sizeof(data));

    data.client_state = client_state;
    return tlv_argo_serialize(tlv, zpn_client_state_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_client_authenticate(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     int64_t id,
                                     const char *saml_assertion_xml,
                                     const char *hw_serial_id,
                                     const char *cpu_serial_id,
                                     const char *storage_serial_id,
                                     const char *login_name,
                                     const char *platform,
                                     char *primary_login_domain,
                                     const char *client_type,
                                     const char **capabilities,
                                     int capabilities_count,
                                     enum zpn_client_conn_mode role)
{
    struct zpn_client_authenticate data;
    memset(&data, 0, sizeof(data));

    data.id = id;
    data.saml_assertion_xml = saml_assertion_xml;
    data.hw_serial_id = hw_serial_id;
    data.cpu_serial_id = cpu_serial_id;
    data.storage_serial_id = storage_serial_id;
    data.login_name = login_name;
    data.platform = platform;
    data.primary_login_domain = primary_login_domain;
    data.client_type = client_type;
    data.capabilities = capabilities;
    data.capabilities_count = capabilities_count;
    data.connection_mode = role;

    return tlv_argo_serialize(tlv, zpn_client_authenticate_description, &data, conn_incarnation, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_machine_tunnel_client_authenticate(struct zpn_tlv *tlv,
                                                    int64_t conn_incarnation,
                                                    int64_t id,
                                                    const char *hw_serial_id,
                                                    const char *cpu_serial_id,
                                                    const char *storage_serial_id,
                                                    struct argo_inet *priv_ip,
                                                    const char *hostname,
                                                    const char *platform)
{
    struct zpn_machine_tunnel_client_authenticate data;
    memset(&data, 0, sizeof(data));

    data.id = id;
    data.hw_serial_id = hw_serial_id;
    data.cpu_serial_id = cpu_serial_id;
    data.storage_serial_id = storage_serial_id;
    data.priv_ip = priv_ip;
    data.hostname = hostname;
    data.platform = platform;

    return tlv_argo_serialize(tlv, zpn_machine_tunnel_client_authenticate_description, &data, conn_incarnation, fohh_queue_element_type_mission_critical);
}
int zpn_send_zpn_authenticate_obj(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct argo_object *obj,
                                  struct argo_structure_description *obj_description)


{
    void  *data = obj->base_structure_void;

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, obj_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_upgrade_request_response(struct zpn_tlv *tlv,
                                                int request_id,
                                                int res,
                                                const char *resp_str)
{
    struct zpn_client_connection_upgrade_response upg_resp;

    memset(&upg_resp, 0, sizeof(upg_resp));

    upg_resp.error = res;
    upg_resp.error_msg = resp_str;
    upg_resp.request_id = request_id;

    return tlv_argo_serialize(tlv, zpn_client_connection_upgrade_response_description, &upg_resp, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_authenticate_ack(struct zpn_tlv *tlv,
                                         int64_t conn_incarnation,
                                         int64_t id,
                                         int64_t saml_not_before,
                                         const char *tunnel_id,
                                         const char *current_broker,
                                         int64_t broker_type,
                                         const char *error,
                                         struct argo_inet *pub_ip)
{
    struct zpn_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    data.id = id;
    data.saml_not_before = saml_not_before;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.broker_type = broker_type;
    data.error = error;
    data.pub_ip = pub_ip;

    return tlv_argo_serialize(tlv, zpn_client_authenticate_ack_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_machine_tunnel_client_authenticate_ack(struct zpn_tlv *tlv,
                                                        int64_t conn_incarnation,
                                                        int64_t id,
                                                        const char *tunnel_id,
                                                        const char *current_broker,
                                                        int64_t broker_type,
                                                        const char *error)
{
    struct zpn_machine_tunnel_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.error = error;
    data.error_dup = error;
    data.broker_type = broker_type;

    return tlv_argo_serialize(tlv, zpn_machine_tunnel_client_authenticate_ack_description,
                              &data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_trusted_client_authenticate(struct fohh_connection *f_conn,
                                             int64_t f_conn_incarnation,
                                             int64_t id,
                                             int64_t customer_gid)
{
    struct zpn_trusted_client_authenticate data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.customer_gid = customer_gid;

    return fohh_argo_serialize(f_conn, zpn_trusted_client_authenticate_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_trusted_client_authenticate_ack(struct zpn_tlv *tlv,
                                                 int64_t conn_incarnation,
                                                 int64_t id,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 const char *error)
{
    struct zpn_trusted_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.error = error;

    return tlv_argo_serialize(tlv, zpn_trusted_client_authenticate_ack_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_exporter_client_authenticate(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              int64_t id,
                                              int64_t customer_gid,
                                              const char *assertion_base64,
                                              struct argo_inet *public_ip,
                                              struct argo_inet *private_ip,
                                              int64_t pra_scope_gid,
                                              int is_pra_third_party_login)
{
    struct zpn_exporter_client_authenticate data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.customer_gid = customer_gid;
    data.assertion_base64 = assertion_base64;
    data.public_ip = public_ip;
    data.private_ip = private_ip;
    if (pra_scope_gid) {
        data.pra_scope_gid = pra_scope_gid;
    }
    if (is_pra_third_party_login) {
        data.is_pra_third_party_login = is_pra_third_party_login;
    }

    return fohh_argo_serialize(f_conn, zpn_exporter_client_authenticate_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_exporter_client_authenticate_ack(struct zpn_tlv *tlv,
                                                 int64_t conn_incarnation,
                                                 int64_t id,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 const char *error)
{
    struct zpn_exporter_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.error = error;

    return tlv_argo_serialize(tlv, zpn_exporter_client_authenticate_ack_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_bi_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        const char *assertion_base64,
                                        struct argo_inet *public_ip,
                                        struct argo_inet *private_ip)
{
    struct zpn_bi_client_authenticate data;
    memset(&data, 0, sizeof(data));

    data.id = id;
    data.assertion_base64 = assertion_base64;
    data.public_ip = public_ip;
    data.private_ip = private_ip;

    return tlv_argo_serialize(tlv, zpn_bi_client_authenticate_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_bi_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            const char *error)
{
    struct zpn_bi_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.error = error;

    return tlv_argo_serialize(tlv, zpn_bi_client_authenticate_ack_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_pbroker_client_authenticate(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              int64_t id,
                                              const char *assertion_base64,
                                              struct argo_inet *private_ip) {
    struct zpn_pbroker_client_authenticate data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.assertion_base64 = assertion_base64;
    //data.private_ip = private_ip;

    return fohh_argo_serialize(f_conn, zpn_pbroker_client_authenticate_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_pbroker_client_authenticate_ack(struct fohh_connection *f_conn,
                                                  int64_t f_conn_incarnation,
                                                  int64_t id,
                                                  const char *tunnel_id,
                                                  const char *current_broker,
                                                  const char *error) {
    struct zpn_exporter_client_authenticate_ack data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.current_broker = current_broker;
    data.error = error;

    return fohh_argo_serialize(f_conn, zpn_pbroker_client_authenticate_ack_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_vdi_client_authenticate(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            int64_t orgid,
                                            const char *cloud_name,
                                            const char **capabilities,
                                            int capabilities_count,
                                            struct argo_inet *private_ip,
                                            const char *platform,
                                            const char *user_name,
                                            int64_t last_auth_time) {
    struct zpn_vdi_client_authenticate data;
    memset(&data, 0, sizeof(data));

    data.id = id;
    data.orgid = orgid;
    data.cloud_name = cloud_name;
    data.private_ip = private_ip;
    data.capabilities = capabilities;
    data.capabilities_count = capabilities_count;
    data.platform = platform;
    data.username = user_name;
    data.last_auth_time = last_auth_time;

    return tlv_argo_serialize(tlv, zpn_vdi_client_authenticate_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_pbroker_client_switch_to_cloud(struct fohh_connection *f_conn,
                                                int64_t f_conn_incarnation,
                                                int8_t is_cloud_reachable,
                                                int64_t num_mtunnels) {
    struct zpn_pbroker_client_switch_to_cloud data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.is_cloud_reachable = is_cloud_reachable;
    data.num_mtunnels = num_mtunnels;

    return fohh_argo_serialize(f_conn, zpn_pbroker_client_switch_to_cloud_description, &data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_ec_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        int64_t orgid,
                                        int64_t o_location_id,
                                        const char *cloud_name,
                                        const char **capabilities,
                                        int capabilities_count,
                                        struct argo_inet *private_ip,
                                        const char *platform) {
    struct zpn_ec_client_authenticate data;
    memset(&data, 0, sizeof(data));

    data.id = id;
    data.orgid = orgid;
    data.o_location_id = o_location_id;
    data.cloud_name = cloud_name;
    data.private_ip = private_ip;
    data.capabilities = capabilities;
    data.capabilities_count = capabilities_count;
    data.platform = platform;

    return tlv_argo_serialize(tlv, zpn_ec_client_authenticate_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

void zpn_populate_zpn_vdi_client_authenticate_ack(struct zpn_vdi_client_authenticate_ack *data,
                                                        int64_t id,
                                                        const char *tunnel_id,
                                                        const char *current_broker,
                                                        int64_t broker_type,
                                                        const char *error) {
    memset(data, 0, sizeof(*data));
    data->id = id;
    data->tunnel_id = tunnel_id;
    data->current_broker = current_broker;
    data->broker_type = broker_type;
    data->error = error;
}

int zpn_send_zpn_vdi_client_authenticate_ack(struct zpn_tlv *tlv,
                                                int64_t conn_incarnation,
                                                int64_t id,
                                                const char *tunnel_id,
                                                const char *current_broker,
                                                int64_t broker_type,
                                                const char *error) {
    struct zpn_vdi_client_authenticate_ack data;
    zpn_populate_zpn_vdi_client_authenticate_ack(&data, id, tunnel_id, current_broker, broker_type, error);
    return tlv_argo_serialize(tlv, zpn_vdi_client_authenticate_ack_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

void zpn_populate_zpn_ec_client_authenticate_ack(struct zpn_ec_client_authenticate_ack *data,
                                                 int64_t id,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 int64_t broker_type,
                                                 const char *error) {
    memset(data, 0, sizeof(*data));

    data->id = id;
    data->tunnel_id = tunnel_id;
    data->current_broker = current_broker;
    data->broker_type = broker_type;
    data->error = error;
}

int zpn_send_zpn_ec_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            int64_t broker_type,
                                            const char *error) {
    struct zpn_ec_client_authenticate_ack data;

    zpn_populate_zpn_ec_client_authenticate_ack(&data, id, tunnel_id, current_broker, broker_type, error);

    return tlv_argo_serialize(tlv, zpn_ec_client_authenticate_ack_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_ia_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        const char *cloud_name,
                                        int64_t orgid,
                                        int64_t customer_gid,
                                        struct argo_inet *private_ip,
                                        const char **capabilities,
                                        int capabilities_count) {
    struct zpn_ia_client_authenticate data;
    memset(&data, 0, sizeof(data));
    data.id = id;
    data.cloud_name = cloud_name;
    data.orgid = orgid;
    data.customer_gid = customer_gid;
    data.private_ip = private_ip;
    data.capabilities = capabilities;
    data.capabilities_count = capabilities_count;

    return tlv_argo_serialize(tlv, zpn_ia_client_authenticate_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}

void zpn_populate_zpn_ia_client_authenticate_ack(struct zpn_ia_client_authenticate_ack *data,
                                                 int64_t id,
                                                 int64_t customer_gid,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 int64_t broker_type,
                                                 const char *error) {
    memset(data, 0, sizeof(*data));
    data->id = id;
    data->customer_gid = customer_gid;
    data->tunnel_id = tunnel_id;
    data->current_broker = current_broker;
    data->broker_type = broker_type;
    data->error = error;
}

int zpn_send_zpn_ia_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            int64_t customer_gid,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            int64_t broker_type,
                                            const char *error) {
    struct zpn_ia_client_authenticate_ack data;

    zpn_populate_zpn_ia_client_authenticate_ack(&data, id, customer_gid, tunnel_id, current_broker, broker_type, error);

    return tlv_argo_serialize(tlv, zpn_ia_client_authenticate_ack_description, &data, 0,
                              fohh_queue_element_type_mission_critical);
}



int zpn_send_zpn_client_app(struct zpn_tlv *tlv,
                            int64_t conn_incarnation,
                            struct argo_object *object,
                            char *debug_str)
{
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char buf[20000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_CLIENT("%s: Send client app: %s", debug_str, buf);
        }
    }
    return tlv_argo_serialize_object(tlv, object, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_app_complete(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     const char *error)
{
    struct zpn_client_app_complete data;
    memset(&data, 0, sizeof(data));

    data.error = error;

    return tlv_argo_serialize(tlv, zpn_client_app_complete_description, &data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_search_domain(struct zpn_tlv *tlv,
                                      int64_t conn_incarnation,
                                      const char *domain,
                                      int capture,
                                      int deleted)
{
    struct zpn_client_search_domain data;
    memset(&data, 0, sizeof(data));

    data.domain = domain;
    data.deleted = deleted;
    data.capture = capture;

    return tlv_argo_serialize(tlv, zpn_client_search_domain_description, &data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_search_domain_complete(struct zpn_tlv *tlv,
                                               int64_t conn_incarnation,
                                               char **domains,
                                               int *capture,
                                               int domains_count,
                                               const char *error)
{
    struct zpn_client_search_domain_complete data;
    memset(&data, 0, sizeof(data));

    data.error = error;
    data.all_domains = domains;
    data.capture = capture;
    data.all_domains_count = domains_count;
    data.capture_count = domains_count;

    return tlv_argo_serialize(tlv, zpn_client_search_domain_complete_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_client_search_domain_all(struct zpn_tlv *tlv,
                                          int64_t conn_incarnation,
                                          char **domains,
                                          int *capture,
                                          int domains_count)
{
    struct zpn_client_search_domain_all data;
    memset(&data, 0, sizeof(data));

    data.all_domains = domains;
    data.capture = capture;
    data.all_domains_count = domains_count;
    data.capture_count = domains_count;

    return tlv_argo_serialize(tlv, zpn_client_search_domain_all_description, &data, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_tlv_config_updated(struct zpn_tlv *tlv, const char* error)
{
    struct zpn_client_config_updated data;
    memset(&data, 0, sizeof(data));

    data.error = error;

    return tlv_argo_serialize(tlv, zpn_client_config_updated_description, &data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_tlv_aggregated_domains(struct zpn_tlv *tlv,
                                        char **domain_list,
                                        int domain_cnt)
{
    struct zpn_client_aggregated_domains ag_domains;

    ag_domains.include_domain_list_cnt = domain_cnt;
    ag_domains.include_domain_list = domain_list;

    return tlv_argo_serialize(tlv, zpn_client_aggregated_domains_description,
           &ag_domains, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_posture_profile_ack(struct zpn_tlv *tlv,
                                     int64_t f_conn_incarnation,
                                     const char *id_str,
                                     const char *error)
{
    struct zpn_posture_profile_ack data;
    memset(&data, 0, sizeof(data));

    data.id_str = id_str;
    data.error = error;

    return tlv_argo_serialize(tlv, zpn_posture_profile_ack_description, &data, 0, fohh_queue_element_type_mission_critical);
}


int zpn_send_exporter_log_data(struct zpn_tlv *tlv,
                               int32_t tag_id,
                               char *console_user,
                               enum zpn_console_credential_type console_cred_type,
                               int64_t capabilities_policy_id,
                               char *file_transfer_list,
                               int64_t cred_policy_id,
                               char *guac_error_string,
                               char *console_conn_type,
                               int32_t is_pra_session,
                               char *session_recording,
                               char *pra_conn_id,
                               char *shared_users_list,
                               char *shared_mode,
                               char *user_email,
                               int32_t event_type,
                               char *credential_id,
                               char *credential_pool_id)
{
    struct zpn_exporter_log_data data;
    memset(&data, 0, sizeof(data));
    data.tag_id = tag_id;
    if (console_user) {
        data.console_user = console_user;
    }

    data.console_cred_type = console_cred_type;

    if (capabilities_policy_id) {
        data.capabilities_policy_id = capabilities_policy_id;
    }
    if (file_transfer_list) {
        data.file_transfer_list = file_transfer_list;
    }
    if (cred_policy_id) {
        data.cred_policy_id = cred_policy_id;
    }
    if (guac_error_string) {
        data.guac_error_string = guac_error_string;
    }
    if (console_conn_type) {
        data.console_conn_type = console_conn_type;
    }
    if (is_pra_session) {
        data.is_pra_session = is_pra_session;
    }
    if (session_recording) {
        data.session_recording = session_recording;
    }
    if (pra_conn_id) {
        data.pra_conn_id = pra_conn_id;
    }
    if (shared_users_list) {
        data.shared_users_list = shared_users_list;
    }
    if (shared_mode) {
        data.shared_mode = shared_mode;
    }
    if (user_email) {
        data.shared_user_email = user_email;
    }
    if (event_type) {
        data.shared_user_event = event_type;
    }
    if (credential_id) {
        data.credential_id = credential_id;
    }
    if (credential_pool_id) {
        data.credential_pool_id = credential_pool_id;
    }

    return tlv_argo_serialize(tlv, zpn_exporter_log_data_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_exporter_log_data_ack(struct zpn_tlv *tlv,
                                   int32_t tag_id,
                                   const char *error)
{
    struct zpn_exporter_log_data_ack data;
    memset(&data, 0, sizeof(data));
    data.tag_id = tag_id;
    data.error = error;

    return tlv_argo_serialize(tlv, zpn_exporter_log_data_ack_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_request(struct zpn_tlv *tlv,
                                 int64_t conn_incarnation,
                                 int32_t tag_id,
                                 char *app_name,
                                 uint16_t ip_protocol,
                                 int32_t server_port,
                                 uint32_t double_encrypt,
                                 enum zpn_probe_type zpn_probe_type,
                                 int64_t publish_gid,
                                 char *app_type,
                                 int64_t o_location_id,
                                 int64_t app_gid,
                                 const struct zpn_managed_chrome_payload *gposture,
                                 const struct zpn_managed_browser_profiles *gprofiles)
{
    struct zpn_mtunnel_request data;
    memset(&data, 0, sizeof(data));

    data.tag_id = tag_id;
    data.app_name = app_name;
    data.tcp_server_port = server_port;
    data.ip_protocol = ip_protocol;
    data.server_port = server_port;
    data.double_encrypt = double_encrypt;
    data.zpn_probe_type = zpn_probe_type;
    data.publish_gid = publish_gid;
    if (app_type) {
        data.app_type = app_type;
    }
    data.o_location_id = o_location_id;
    data.app_gid = app_gid;

    if (gposture) {
        data.g_success = gposture->success;
        data.g_browser_version = gposture->browser_version;
        data.g_key_trust_level = gposture->key_trust_level;
        data.g_operating_system = gposture->operating_system;
        data.g_disk_encryption = gposture->disk_encryption;
        data.g_os_firewall = gposture->os_firewall;
        data.g_screen_lock_secured = gposture->screen_lock_secured;
        data.g_safe_browsing_protection_level = gposture->safe_browsing_protection_level;
        data.g_crowd_strike_agent = gposture->crowd_strike_agent;
        data.gd_cookie_domain_id = gposture->gd_cookie_domain_id;
    }

    if(gprofiles) {
        data.gprofile_gids = gprofiles->gprofile_gids;
        data.gprofile_gid_count = gprofiles->gprofile_gid_count;
        data.managed_browser_payload_version = gprofiles->managed_browser_payload_version;
    }

    return tlv_argo_serialize(tlv, zpn_mtunnel_request_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_request_zia(struct zpn_tlv *tlv,
                                    int64_t conn_incarnation,
                                    int32_t tag_id,
                                    char *app_name,
                                    uint16_t ip_protocol,
                                    int32_t server_port,
                                    int64_t o_user_id,
                                    int64_t o_location_id,
                                    int64_t o_plocation_id,
                                    struct argo_inet *o_sip,
                                    struct argo_inet *o_dip,
                                    int32_t o_sport,
                                    int32_t o_dport,
                                    char *o_identity_name) {
    struct zpn_mtunnel_request data;
    memset(&data, 0, sizeof(data));

    data.tag_id = tag_id;
    data.app_name = app_name;
    data.tcp_server_port = server_port;
    data.ip_protocol = ip_protocol;
    data.server_port = server_port;

    data.o_user_id = o_user_id;
    data.o_location_id = o_location_id;
    data.o_plocation_id = o_plocation_id;

    data.o_sip = o_sip;

    data.o_dip = o_dip;
    data.o_sport = o_sport;
    data.o_dport = o_dport;
    data.o_identity_name = o_identity_name;

    return tlv_argo_serialize(tlv, zpn_mtunnel_request_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_request_int(struct fohh_connection *f_conn,
                                     int64_t f_conn_incarnation,
                                     int32_t tag_id,
                                     char *app_name,
                                     uint16_t ip_protocol,
                                     int32_t server_port,
                                     uint32_t double_encrypt,
                                     uint32_t zpn_probe_type,
                                     int64_t publish_gid)
{
    struct zpn_mtunnel_request_int data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.tag_id = tag_id;
    data.app_name = app_name;
    data.tcp_server_port = server_port;
    data.ip_protocol = ip_protocol;
    data.server_port = server_port;
    data.double_encrypt = double_encrypt;
    data.zpn_probe_type = zpn_probe_type;
    data.publish_gid = publish_gid;

    return fohh_argo_serialize(f_conn, zpn_mtunnel_request_int_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_request_ack(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     int64_t reauth_timeout_s,
                                     int32_t tag_id,
                                     int a_port,
                                     int s_port,
                                     struct argo_inet *a_ip,
                                     struct argo_inet *s_ip,
                                     const char *mtunnel_id,
                                     const char *error,
									 const char *reason,
                                     const char *stepup_al_id)
{
    struct zpn_mtunnel_request_ack data;
    memset(&data, 0, sizeof(data));

    data.reauth_timeout_s = reauth_timeout_s;
    data.tag_id = tag_id;
    data.a_port = a_port;
    data.s_port = s_port;
    data.a_ip = a_ip;
    data.s_ip = s_ip;
    data.mtunnel_id = mtunnel_id;
    data.error = error;
    data.err_code = et_translate_get_code(error);
    data.reason = reason;
    data.stepup_auth_level_id = stepup_al_id;
    return tlv_argo_serialize(tlv, zpn_mtunnel_request_ack_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_bind(struct zpn_tlv *tlv,
                              int64_t conn_incarnation,
                              const char *mtunnel_id,
                              int32_t server_us,
                              int64_t fwd_broker_id,
                              int64_t fwd_broker_us,
                              int64_t g_aps,
                              struct argo_inet brk_req_s_inet,
                              struct argo_inet s_inet,
                              uint16_t s_port,
                              struct argo_inet a_inet,
                              uint16_t a_port,
                              int64_t  brk_req_dsp_tx_us,
                              int64_t  ast_tx_us,
                              int64_t  brk_req_ast_rx_us,
                              int64_t  g_app,
                              int64_t  g_app_grp,
                              int64_t  g_ast_grp,
                              int64_t  g_srv_grp,
                              int64_t  g_dsp,
                              uint64_t path_decision,
                              int8_t   dsp_bypassed,
                              uint8_t  insp_status,
                              uint64_t ssl_err)
{
    struct zpn_mtunnel_bind data;
    memset(&data, 0, sizeof(data));

    data.mtunnel_id = mtunnel_id;
    data.server_us = server_us;
    data.g_aps = g_aps;
    data.g_bfw = fwd_broker_id;
    data.brk_req_dsp_tx_us = brk_req_dsp_tx_us;
    data.bfw_us = fwd_broker_us;
    data.brk_req_ast_rx_us = brk_req_ast_rx_us;
    data.ast_tx_us = ast_tx_us;
    data.brk_req_s_inet = brk_req_s_inet;
    data.s_inet = s_inet;
    data.s_port = s_port;
    data.a_inet = a_inet;
    data.a_port = a_port;
    data.g_app = g_app;
    data.g_ast_grp = g_ast_grp;
    data.g_app_grp = g_app_grp;
    data.g_srv_grp = g_srv_grp;
    data.g_dsp = g_dsp;
    data.path_decision = path_decision;
    data.dsp_bypassed = dsp_bypassed;
    data.insp_status = insp_status;
    data.ssl_err = ssl_err;

    return tlv_argo_serialize(tlv, zpn_mtunnel_bind_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_bind_ack_struct(struct zpn_tlv *tlv,
                                         int64_t conn_incarnation,
                                         struct zpn_mtunnel_bind_ack *ack)
{
    return tlv_argo_serialize(tlv, zpn_mtunnel_bind_ack_description, ack, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_bind_ack(struct zpn_tlv *tlv,
                                  int64_t conn_incarnation,
                                  const char *mtunnel_id,
                                  int32_t tag_id,
                                  const char *error,
				  int64_t brk_tx_us)
{
    struct zpn_mtunnel_bind_ack data;
    memset(&data, 0, sizeof(data));

    if (conn_incarnation && (conn_incarnation != zpn_tlv_conn_incarnation(tlv))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.mtunnel_id = mtunnel_id;
    data.tag_id = tag_id;
    data.error = error;
    data.ubrk_tx_us = brk_tx_us;

    return tlv_argo_serialize(tlv, zpn_mtunnel_bind_ack_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_end(struct zpn_tlv *tlv,
                             int64_t conn_incarnation,
                             const char *mtunnel_id,
                             int32_t tag_id,
                             const char *error,
                             int32_t drop_data)
{
    struct zpn_mtunnel_end data;
    memset(&data, 0, sizeof(data));

    data.mtunnel_id = mtunnel_id;
    data.tag_id = tag_id;
    data.error = error;
    data.err_code = et_translate_get_code(error);
    data.drop_data = drop_data;

    return tlv_argo_serialize(tlv, zpn_mtunnel_end_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_client_connection_upgrade(struct zpn_tlv *tlv,
                                           int64_t conn_incarnation,
                                           int id,
                                           int curr_conn_mode,
                                           int new_conn_mode)
{
    struct zpn_client_connection_upgrade data;
    memset(&data, 0, sizeof(data));

    data.current_connection_mode = curr_conn_mode;
    data.new_connection_mode = new_conn_mode;
    data.request_id = id;

    return tlv_argo_serialize(tlv, zpn_client_connection_upgrade_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_tag_pause(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int32_t tag_id)
{
    struct zpn_mtunnel_tag_pause data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    fohh_increment_pause_stats(f_conn);

    data.tag_id = tag_id;

    return fohh_argo_serialize(f_conn, zpn_mtunnel_tag_pause_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_tag_resume(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int32_t tag_id)
{
    struct zpn_mtunnel_tag_resume data;
    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }
    fohh_increment_resume_stats(f_conn);
    data.tag_id = tag_id;

    return fohh_argo_serialize(f_conn, zpn_mtunnel_tag_resume_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_broker_request_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_broker_request *req)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }
    return fohh_argo_serialize(f_conn, zpn_broker_request_description, req, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_broker_request_struct_on_zpn_tlv(struct zpn_tlv *tlv,
                                                  int64_t tlv_incarnation,
                                                  struct zpn_broker_request *req)
{
    if (tlv_incarnation && (tlv_incarnation != zpn_tlv_incarnation(tlv))) {
        return ZPN_RESULT_NO_ERROR;
    }
    return tlv_argo_serialize(tlv, zpn_broker_request_description, req, 0, fohh_queue_element_type_control);
}
int zpn_send_zpn_broker_request_ack_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_broker_request_ack *ack)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }
    return fohh_argo_serialize(f_conn, zpn_broker_request_ack_description, ack, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_broker_request_ack_struct_on_zpn_tlv(struct zpn_tlv* tlv,
                                                        int64_t conn_incarnation,
                                                        struct zpn_broker_request_ack *ack)
{
    if (conn_incarnation && (conn_incarnation != zpn_tlv_incarnation(tlv))) {
        return ZPN_RESULT_NO_ERROR;
    }
    return tlv_argo_serialize(tlv, zpn_broker_request_ack_description, ack, 0, fohh_queue_element_type_control);
}


int zpn_send_zpn_health_report_struct(struct fohh_connection *f_conn,
                                      int64_t f_conn_incarnation,
                                      struct zpn_health_report *rep)

{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_health_report_description, rep, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_ast_auth_report_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_ast_auth_report *rep)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_ast_auth_report_description, rep, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_app_route_registration_struct(struct fohh_connection *f_conn,
                                               int64_t f_conn_incarnation,
                                               struct zpn_app_route_registration *rep)

{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_app_route_registration_description, rep, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_app_route_discovery_struct(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            struct zpn_app_route_discovery *rep)

{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_app_route_discovery_description, rep, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_app_route_info_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_app_route_info *rep)

{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_app_route_info_description, rep, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_tcp_info_report(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct fohh_connection *f_conn_info,
                                 const char *platform_detail,
                                 const char *runtime_os)
{
    struct zpn_tcp_info_report data;
    struct fohh_tcp_info info;
    char geoip_version[MAX_VERSION_LEN] = {'\0'};
    char isp_version[MAX_VERSION_LEN] = {'\0'};
    int res = FOHH_RESULT_NO_ERROR;

    memset(&data, 0, sizeof(data));
    memset(&info, 0, sizeof(info));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    /* not returning in case of failure, as its a get call and info is initialised to 0 */
    res = fohh_connection_get_tcp_info(f_conn_info, &info);
    if (res) {
        ZPN_LOG(AL_ERROR, "fohh_connection_get_tcp_info failed: %s", zpn_result_string(res));
    }
    fohh_connection_address(f_conn_info, NULL, &(data.priv_ip));
    if(version_file_exists(FILENAME_GEOIP_ENC))
        read_version(FILENAME_GEOIP_ENC, geoip_version);
    if(version_file_exists(FILENAME_ISP_ENC))
        read_version(FILENAME_ISP_ENC, isp_version);

    if((geoip_version[0]=='\0') || !strcmp(geoip_version,VERSION_NONE)){
        geoip_version[0] = 0;
    }

    if((isp_version[0]=='\0') || !strcmp(isp_version,VERSION_NONE)){
        isp_version[0] = 0;
    }

    data.version = ZPATH_VERSION;
    data.platform = ZPATH_PLATFORM_NAMEVERSION;
    data.platform_arch = ZPATH_PLATFORM_ARCH;
    data.platform_detail = platform_detail;
    data.runtime_os = runtime_os;
    data.tcpi_snd_mss = info.tcpi_snd_mss;
    data.tcpi_rcv_mss = info.tcpi_rcv_mss;
    data.tcpi_rtt = info.tcpi_rtt;
    data.tcpi_rttvar = info.tcpi_rttvar;
    data.tcpi_snd_cwnd = info.tcpi_snd_cwnd;
    data.tcpi_advmss = info.tcpi_advmss;
    data.tcpi_reordering = info.tcpi_reordering;
    data.tcpi_rcv_rtt = info.tcpi_rcv_rtt;
    data.tcpi_rcv_space = info.tcpi_rcv_space;
    data.tcpi_total_retrans = info.tcpi_total_retrans;
    data.tcpi_thru_put = info.tcpi_thru_put;
    data.geoip_version = geoip_version;
    data.isp_version = isp_version;
    data.static_wally_enabled = 1;

    return fohh_argo_serialize(f_conn, zpn_tcp_info_report_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_zrdt_info_report(struct zpn_tlv *tlv,
                                  int64_t conn_incarnation,
                                  struct zrdt_conn *z_conn)
{
    struct zpn_zrdt_info_report data;

    data.version = ZPATH_VERSION;
    data.platform = ZPATH_PLATFORM_NAMEVERSION;
    if (z_conn) {
         zdtls_connection_address(zrdt_conn_get_datagram_tx_cookie(z_conn), NULL, &(data.priv_ip));
    }
    return tlv_argo_serialize(tlv, zpn_zrdt_info_report_description, &data, conn_incarnation, fohh_queue_element_type_control);
}

int zpn_send_zpn_asst_environment_report(struct fohh_connection*            f_conn,
                                         int64_t                            f_conn_incarnation,
                                         struct zpn_asst_environment_report *data)
{

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_asst_environment_report_description, data, 0,

                               fohh_queue_element_type_control);
}

int zpn_send_zpn_pbroker_environment_report(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            const char *sarge_version) {
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_pbroker_environment_report rep;
    rep.sarge_version = sarge_version;

    return fohh_argo_serialize(f_conn, zpn_pbroker_environment_report_description, &rep, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_sitec_environment_report(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            const char *sarge_version) {
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_sitec_environment_report rep;
    rep.sarge_version = sarge_version;

    return fohh_argo_serialize(f_conn, zpn_sitec_environment_report_description, &rep, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_sitec_firedrill_exit(struct fohh_connection *f_conn,
                                            int firedrill_exit) {


    struct zpn_sitec_firedrill_exit rep;
    rep.firedrill_exit = firedrill_exit;
    return fohh_argo_serialize(f_conn, zpn_sitec_firedrill_exit_description, &rep, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_asst_active_control_connection(struct fohh_connection*    f_conn,
                                                int64_t                    f_conn_incarnation)
{
    struct zpn_asst_active_control_connection data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_asst_active_control_connection_description, &data, 0,
                               fohh_queue_element_type_control);
}


#if 0
int zpn_send_zpn_dns_client_check(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  int64_t id,
                                  char *name,
                                  const char **target_name,
                                  int32_t *target_port,
                                  int32_t *target_priority,
                                  int32_t *target_weight,
                                  int target_count,
                                  const char **cnames,
                                  int cnames_count,
                                  char *type,
                                  char *error)
{
    struct zpn_dns_client_check data;
    int i;

    memset(&data, 0, sizeof(data));

    if (target_count > ZPN_DNS_MAX_TARGETS) return ZPN_RESULT_ERR_TOO_LARGE;
    if (cnames_count > ZPN_DNS_MAX_CNAMES) return ZPN_RESULT_ERR_TOO_LARGE;

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.name = name;
    data.type = type;
    data.error = error;

    for (i = 0; i < target_count; i++) {
        data.target_name[i] = target_name[i];
        data.target_port[i] = target_port[i];
        data.target_priority[i] = target_priority[i];
        data.target_weight[i] = target_weight[i];
    }
    data.target_name_count = target_count;
    data.target_port_count = target_count;
    data.target_priority_count = target_count;
    data.target_weight_count = target_count;

    for (i = 0; i < cnames_count; i++) {
        data.cnames[i] = cnames[i];
    }
    data.cnames_count = cnames_count;

    return fohh_argo_serialize(f_conn, zpn_dns_client_check_description, &data, 0);
}
#endif

int zpn_send_zpn_dns_client_check(struct zpn_tlv *tlv,
                                  int64_t f_conn_incarnation,
                                  struct zpn_dns_client_check *data)
{
    return tlv_argo_serialize(tlv, zpn_dns_client_check_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_dns_client_check_f_conn(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         void *data,
                                         int is_app_check)
{
    struct argo_structure_description *description = is_app_check ? zpn_app_client_check_description : zpn_dns_client_check_description;

    return fohh_argo_serialize(f_conn, description, data, f_conn_incarnation, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_app_client_check(struct zpn_tlv *tlv,
                               int64_t f_conn_incarnation,
                               struct zpn_app_client_check *data)
{
    return tlv_argo_serialize(tlv, zpn_app_client_check_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_trusted_networks(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct zpn_trusted_networks *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_trusted_networks_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_trusted_networks_ack(struct zpn_tlv *tlv,
                                      int64_t conn_incarnation,
                                      int64_t id)
{
    struct zpn_trusted_networks_ack ack;

    memset(&ack, 0, sizeof(ack));
    ack.id = id;

    return tlv_argo_serialize(tlv, zpn_trusted_networks_ack_description, &ack, 0, fohh_queue_element_type_mission_critical);
}


int zpn_send_zdx_probe_legs_info_tlv(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     struct zpn_zdx_probe_legs_info *info)
{
    return tlv_argo_serialize(tlv, zpn_zdx_probe_legs_info_description, info, 0, fohh_queue_element_type_control);
}

/* Only used by zdx test server */
int zpn_send_zdx_client_authenticate_ack(struct fohh_connection *f_conn,
                                         int64_t conn_incarnation,
                                         struct zpn_client_authenticate_ack *ack)
{
    return fohh_argo_serialize(f_conn, zpn_client_authenticate_ack_description, ack, 0, fohh_queue_element_type_control);
}



int zpn_send_zpn_dns_dispatch_check(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int64_t id,
                                    char *tunnel_id,
                                    int64_t g_brk,
                                    int64_t g_app,
                                    char *name,
                                    const char **target_name,
                                    int32_t *target_port,
                                    int32_t *target_priority,
                                    int32_t *target_weight,
                                    int target_count,
                                    const char **cnames,
                                    int cnames_count,
                                    char *type,
                                    char *error)
{
    struct zpn_dns_dispatch_check data;
    int i;

    memset(&data, 0, sizeof(data));

    if (target_count > ZPN_DNS_MAX_TARGETS) return ZPN_RESULT_ERR_TOO_LARGE;
    if (cnames_count > ZPN_DNS_MAX_CNAMES) return ZPN_RESULT_ERR_TOO_LARGE;

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.id = id;
    data.tunnel_id = tunnel_id;
    data.g_brk = g_brk;
    data.g_app = g_app;
    data.name = name;
    data.type = type;
    data.error = error;

    for (i = 0; i < target_count; i++) {
        data.target_name[i] = target_name[i];
        data.target_port[i] = target_port[i];
        data.target_priority[i] = target_priority[i];
        data.target_weight[i] = target_weight[i];
    }
    data.target_name_count = target_count;
    data.target_port_count = target_count;
    data.target_priority_count = target_count;
    data.target_weight_count = target_count;

    for (i = 0; i < cnames_count; i++) {
        data.cnames[i] = cnames[i];
    }
    data.cnames_count = cnames_count;

    return fohh_argo_serialize(f_conn, zpn_dns_dispatch_check_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_dns_dispatch_check_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_dns_dispatch_check *msg)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_dns_dispatch_check_description, msg, 0, fohh_queue_element_type_control);
}

int zpn_send_dispatch_fqdn_c2c_check_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_broker_dispatcher_c2c_app_check *msg)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_broker_dispatcher_c2c_app_check_description, msg, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_dns_assistant_check(struct fohh_connection *f_conn,
                                     int64_t f_conn_incarnation,
                                     int64_t g_ast,
                                     int64_t g_app,
                                     int64_t g_dsp,
                                     char *name,
                                     char *type,
                                     char *error,
                                     int64_t dsp_tx_us,
                                     int64_t cbrk_to_ast_tx_us)
{
    struct zpn_dns_assistant_check data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_ast = g_ast;
    data.g_app = g_app;
    data.name = name;
    data.type = type;
    data.error = error;
    data.g_dsp = g_dsp;
    data.dsp_tx_us = dsp_tx_us;
    data.cbrk_to_ast_tx_us = cbrk_to_ast_tx_us;

    return fohh_argo_serialize(f_conn, zpn_dns_assistant_check_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_broker_mission_critical_req(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    struct zpn_broker_mission_critical *msg)
{
    ZPN_LOG(AL_ERROR, "firedrill sending mission critical message request");
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if(!msg->site_gid) {
        ZPN_LOG(AL_ERROR, "firedrill: site gid cannot be zero");
        return ZPN_RESULT_CANT_WRITE;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }
    return fohh_argo_serialize(f_conn, zpn_broker_mission_critical_description, msg, 0, fohh_queue_element_type_mission_critical);
}

int zpn_broker_mission_critical_ack(struct fohh_connection *f_conn,
                                        int64_t f_conn_incarnation,
                                        struct zpn_broker_mission_critical_resp *msg)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }
    return fohh_argo_serialize(f_conn, zpn_broker_mission_critical_description_resp, msg, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_dns_assistant_check_struct(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            struct zpn_dns_assistant_check *msg)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_dns_assistant_check_description, msg, 0, fohh_queue_element_type_control);
}

int zpn_send_eas_dns_assistant_check(struct zpn_tlv *tlv,
                                     int64_t tlv_incarnation,
                                     struct zpn_dns_assistant_check *dns_req)
{
    if (tlv_incarnation && (tlv_incarnation != zpn_tlv_incarnation(tlv))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return tlv_argo_serialize(tlv, zpn_dns_assistant_check_description, dns_req, 0,
                              fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_log_control(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       int64_t g_ast,
                                       int type,
                                       int upload,
                                       uint64_t flag)
{
    struct zpn_assistant_log_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_ast = g_ast;
    data.type = type;
    data.upload = upload;
    data.flag = flag;

    return fohh_argo_serialize(f_conn, zpn_assistant_log_control_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_stats_control(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       int64_t g_ast,
                                       int upload)
{
    struct zpn_assistant_stats_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_ast = g_ast;
    data.upload = upload;

    return fohh_argo_serialize(f_conn, zpn_assistant_stats_control_description, &data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_restart(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int64_t g_ast)
{
    struct zpn_assistant_restart data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_ast = g_ast;

    return fohh_argo_serialize(f_conn, zpn_assistant_restart_description, &data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_enc_pvt_key(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         struct zpn_assistant_pvt_key_control *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_assistant_pvt_key_control_description, data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_cert_req(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         struct zpn_assistant_gen_cert_control  *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_assistant_gen_cert_control_description, data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_assistant_status_report(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         struct zpn_assistant_status_report *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_assistant_status_report_description, data, 0,
                               fohh_queue_element_type_control);
}


int zpn_send_zpn_asst_state(struct zpn_tlv *tlv,
                            int64_t tlv_incarnation,
                            struct zpn_asst_state *data)
{
    if (tlv_incarnation && (tlv_incarnation != zpn_tlv_incarnation(tlv))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return tlv_argo_serialize(tlv, zpn_asst_state_description, data, 0,
                               fohh_queue_element_type_control);
}


int zpn_send_zpn_asst_state_on_fohh(struct fohh_connection *f_conn,
                                    int64_t fohh_incarnation,
                                    struct zpn_asst_state *data)
{
    if (fohh_incarnation && (fohh_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_asst_state_description, data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_asst_restart_reason_on_fohh(struct fohh_connection *f_conn,
                                             int64_t fohh_incarnation,
                                             int64_t assistant_id,
                                             const char *restart_reason)
{
    if (fohh_incarnation && (fohh_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_asst_restart_reason data;
    memset(&data, 0, sizeof(data));

    data.assistant_id = assistant_id;
    data.report_epoch_s = epoch_s();
    snprintf(data.reason, sizeof(data.reason), "%s", restart_reason);

    return fohh_argo_serialize(f_conn, zpn_asst_restart_reason_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_fohh_window_update(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int32_t tag_id,
                                    int64_t tx_limit,
                                    int64_t rx_data)
{
    struct zpn_fohh_tlv_window_update data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }
    fohh_increment_window_update_stats(f_conn, tag_id);

    data.tag_id = tag_id;
    data.tx_limit = tx_limit;
    data.rx_data = rx_data;

    return fohh_argo_serialize(f_conn, zpn_fohh_tlv_window_update_description, &data, 0,
                               fohh_queue_element_type_control);
}


int zpn_send_zpn_fohh_window_batch_update(struct fohh_connection *f_conn,
                                          struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch)
{
    fohh_increment_window_update_stats(f_conn, -1);
    return fohh_argo_serialize(f_conn, zpn_fohh_tlv_window_update_batch_description, zpn_fohh_tlv_window_update_batch, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_broker_redirect_struct(struct zpn_tlv *tlv,
                                        struct zpn_broker_redirect *redirect)
{
    return tlv_argo_serialize(tlv, zpn_broker_redirect_description, redirect, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_dispatcher_status(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation)
{
    struct zpn_dispatcher_status data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    /* Leave all fields zero on request */

    return fohh_argo_serialize(f_conn, zpn_dispatcher_status_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_dispatcher_status_struct(struct fohh_connection *f_conn,
                                          int64_t f_conn_incarnation,
                                          struct zpn_dispatcher_status *status) {
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        return ZPN_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_dispatcher_status_description, status, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_broker_info(struct fohh_connection *f_conn,
                             int64_t f_conn_incarnation,
                             int64_t gid,
                             const char *name,
                             uint8_t channel_id) {
    struct zpn_broker_info data;

    data.g_brk = gid;
    data.name = name;
    data.channel_id = channel_id;

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_broker_info_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_zpn_clientless_app_query(struct fohh_connection *f_conn,
                                      int64_t f_conn_incarnation,
                                      struct zpn_clientless_app_query *query)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_clientless_app_query_description, query, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_clientless_app_query_ack(struct fohh_connection *f_conn,
                                          int64_t f_conn_incarnation,
                                          struct zpn_clientless_app_query_ack *query_ack)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_clientless_app_query_ack_description, query_ack, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_posture_profile_start(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_posture_profile_start *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn)))
    {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_posture_profile_start_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_posture_profile(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct zpn_posture_profile *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn)))
    {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_posture_profile_description, data, 0, fohh_queue_element_type_mission_critical);
}


int zpn_send_zpn_posture_profile_start_tlv(struct zpn_tlv *tlv,
                                           int64_t f_conn_incarnation,
                                           struct zpn_posture_profile_start *data)
{
    return tlv_argo_serialize(tlv, zpn_posture_profile_start_description, data, 0, fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_posture_profile_tlv(struct zpn_tlv *tlv,
                                     int64_t f_conn_incarnation,
                                     struct zpn_posture_profile *data)
{
    return tlv_argo_serialize(tlv, zpn_posture_profile_description, data, 0, fohh_queue_element_type_mission_critical);
}

/* PBroker send log control */
int zpn_send_pbroker_log_control(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 int64_t g_pbroker,
                                 int type,
                                 int upload,
                                 uint64_t flag)
{
    struct zpn_pbroker_log_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_pbroker = g_pbroker;
    data.type = type;
    data.upload = upload;
    data.flag = flag;

    return fohh_argo_serialize(f_conn, zpn_pbroker_log_control_description, &data, 0, fohh_queue_element_type_control);
}

/* Pbroker send stats control */
int zpn_send_pbroker_stats_control(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int64_t g_pbroker,
                                   int upload)
{
    struct zpn_pbroker_stats_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.g_pbroker = g_pbroker;
    data.upload = upload;

    return fohh_argo_serialize(f_conn, zpn_pbroker_stats_control_description, &data, 0,
                               fohh_queue_element_type_control);
}

/* sitec send log control */
int zpn_send_sitec_log_control(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 int64_t sitec_gid,
                                 int type,
                                 int upload,
                                 uint64_t flag)
{
    struct zpn_sitec_log_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.sitec_gid = sitec_gid;
    data.type = type;
    data.upload = upload;
    data.flag = flag;

    return fohh_argo_serialize(f_conn, zpn_sitec_log_control_description, &data, 0, fohh_queue_element_type_control);
}

/* sitec send stats control */
int zpn_send_sitec_stats_control(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int64_t sitec_gid,
                                   int upload)
{
    struct zpn_sitec_stats_control data;

    memset(&data, 0, sizeof(data));

    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    data.sitec_gid = sitec_gid;
    data.upload = upload;

    return fohh_argo_serialize(f_conn, zpn_sitec_stats_control_description, &data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_file_fetch_key(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_file_fetch_key *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_file_fetch_key_description, data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_decrypt_key_request(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct zpn_decrypt_key_request *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_decrypt_key_request_description, data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_decrypt_key_response(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct zpn_decrypt_key_response *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }

    return fohh_argo_serialize(f_conn, zpn_decrypt_key_response_description, data, 0,
                               fohh_queue_element_type_control);
}

int zpn_send_zpn_client_broker_app_registration(struct zpn_tlv *tlv,
                                                int64_t conn_incarnation,
                                                const char *client_fqdn) {
    struct zpn_client_broker_app_registration data;

    memset(&data, 0, sizeof(data));
    data.client_fqdn = client_fqdn;

    data.timestamp_us = epoch_us();

    return tlv_argo_serialize(tlv, zpn_client_broker_app_registration_description, &data, conn_incarnation,
                              fohh_queue_element_type_control);
}

int zpn_send_zpn_client_app_registration_notification(struct zpn_tlv *tlv,
                                                      int64_t conn_incarnation,
                                                      const char *client_fqdn,
                                                      struct argo_inet *client_ip,
                                                      const char *location_hint,
                                                      const char *error_code)
{
    struct zpn_client_app_registration_notification data;

    memset(&data, 0, sizeof(data));
    data.client_fqdn = client_fqdn ? : "";
    data.error_code = error_code;
    if (client_ip && client_ip->length) {
        data.client_ip = client_ip;
        data.location_hint = location_hint;
    }

    if (zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
        struct argo_object *obj = argo_object_create(zpn_client_app_registration_notification_description, (void *)&data);
        if (obj) {
            char buf[1024];
            if (argo_object_dump(obj, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                ZPN_LOG(AL_DEBUG, "Sending c2c app reg notification to client: %s", buf);
            }
            argo_object_release(obj);
        }
    }

    return tlv_argo_serialize(tlv, zpn_client_app_registration_notification_description, &data, conn_incarnation,
                              fohh_queue_element_type_mission_critical);
}

int zpn_send_zpn_broker_dispatcher_app_registration(struct zpn_tlv *tlv,
                                                    int64_t conn_incarnation,
                                                    struct zpn_broker_dispatcher_app_registration *data) {
    return tlv_argo_serialize(tlv, zpn_broker_dispatcher_app_registration_description, data, conn_incarnation,
                              fohh_queue_element_type_control);
}

int zpn_send_zpn_transit_req(struct zpn_tlv *tlv, int64_t conn_incarnation, struct zpn_transit_req *data){
    return tlv_argo_serialize(tlv, zpn_transit_req_description, data, conn_incarnation,
                              fohh_queue_element_type_control);
}

int zpn_send_zpn_mtunnel_stats(struct zpn_tlv *tlv, int64_t conn_incarnation, struct zpn_mtunnel_stats *data){
    return tlv_argo_serialize(tlv, zpn_mtunnel_stats_description, data, conn_incarnation,
                              fohh_queue_element_type_control);
}

int zpn_send_exporter_guac_proxy_data(struct zpn_tlv *tlv,
                                      int32_t tag_id,
                                      char *guac_conn_id,
                                      char *sess_user,
                                      int32_t event,
                                      char *user_email,
                                      int64_t capabilities_policy_id)
{
    struct zpn_exporter_pra_guac_proxy_data data;
    memset(&data, 0, sizeof(data));
    data.pra_conn_id = guac_conn_id;
    data.sess_user = sess_user;
    data.tag_id = tag_id;
    data.event_type = event;
    data.user_email = user_email;
    data.capabilities_policy_id = capabilities_policy_id;
    ZPN_LOG(AL_INFO, "SESS_PROC sending PRA con id %s", guac_conn_id);
    return tlv_argo_serialize(tlv, zpn_exporter_pra_guac_proxy_data_description, &data, 0, fohh_queue_element_type_control);
}

int zpn_send_exporter_guac_proxy_data_ack(struct zpn_tlv *tlv,
                                   int32_t tag_id,
                                   const char *error,
                                   int32_t cmd_type,
                                   int64_t cmd_data)
{
    struct zpn_exporter_data_ack data;
    memset(&data, 0, sizeof(data));
    data.tag_id = tag_id;
    data.error = error;
    data.cmd_type = cmd_type;
    data.cmd_data = cmd_data;
    return tlv_argo_serialize(tlv, zpn_exporter_data_ack_description, &data, 0, fohh_queue_element_type_control);
}


int zpn_send_zpn_mtunnel_data_mconn_stats(struct fohh_connection *f_conn,
                                                  int64_t f_conn_incarnation,
                                                  struct zpn_mtunnel_data_mconn_stats  *data)
{
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        return ZPN_RESULT_NO_ERROR;
    }
    return fohh_argo_serialize(f_conn, zpn_mtunnel_data_mconn_stats_description, data, 0, fohh_queue_element_type_control);
}
