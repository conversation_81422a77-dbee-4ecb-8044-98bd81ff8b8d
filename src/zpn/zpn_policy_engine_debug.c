/*
 * zpn_policy_engine_debug.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved
 */

#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_customer.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_customer.h"
#include "zpn/zpn_scope_ready.h"

#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_engine_build.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_broker_client_apps_build.h"

static int domain_search(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count,
                         void *cookie)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    int return_inclusive_only = 0;

    const char *domain = NULL;

    void *domain_lookup_results[100];
    size_t domain_lookup_results_count = sizeof(domain_lookup_results) / sizeof(domain_lookup_results[0]);

    int res;
    size_t i;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }
    if (!scope_gid) {
        ZDP("Bad scope ID or not provided\n");
        goto done;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);


    /* Get the domain + port + protocol */
    if (!query_values[1]) {
        ZDP("Require domain\n");
        goto done;
    }
    domain = query_values[1];

    if (query_values[2]) {
        return_inclusive_only = strtoul(query_values[2], NULL, 0);
    }

    ZDP("Dump domains, customer %ld scope %ld to domain <%s>, inclusive_only = %d\n",
       (long) customer_gid, (long) scope_gid, domain, return_inclusive_only);

    res = zpath_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpath_customer_ready: %s\n", zpath_result_string(res));
        goto done;
    }

    res = zpn_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpn_customer_ready: %s\n", zpath_result_string(res));
        goto done;
    }

    res = zpn_scope_ready(scope_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpn_scope_ready: %s\n", zpath_result_string(res));
        goto done;
    }

    res = zpn_application_customer_register(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on customer_register: %s\n", zpath_result_string(res));
        goto done;
    }

    domain_lookup_results_count = zpn_domain_search_all(scope_gid,
                                                        domain,
                                                        strnlen(domain, MAX_APPLICATION_DOMAIN_LEN),
                                                        return_inclusive_only,
                                                        NULL,
                                                        &domain_lookup_results[0],
                                                        NULL,
                                                        domain_lookup_results_count,
                                                        PATTERN_DOMAIN_SEARCH_ENABLED);

    for (i = 0; i < domain_lookup_results_count; i++) {
        ZDP("%s\n", (char*)(domain_lookup_results[i]));
    }

 done:
    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int do_application_search(int64_t scope_gid,
                                 const char *domain,
                                 int port,
                                 int protocol,
                                 int64_t *app_gids,
                                 size_t  *app_gids_count,
                                 int64_t *app_groups,
                                 size_t  *app_groups_count,
                                 int64_t *most_specific_app_gid,
                                 char *msg,
                                 size_t msg_sz)
{
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    int is_inclusive = 1;
    int is_wildcard = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    res = zpath_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        snprintf(msg, msg_sz, "Waiting on zpath_customer_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        snprintf(msg, msg_sz, "Waiting on zpn_customer_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_scope_ready(scope_gid, NULL, NULL, 0);
    if (res) {
        snprintf(msg, msg_sz,"Waiting on zpn_scope_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_application_customer_register(customer_gid, NULL, NULL, 0);
    if (res) {
        snprintf(msg, msg_sz, "Waiting on customer_register: %s", zpath_result_string(res));
        goto done;
    }

    /* Now have all arguments set. This mimics, but is not identical to policy lookup. */
    res = zpn_application_search_all(customer_gid,
                                     domain,
                                     strnlen(domain, MAX_APPLICATION_DOMAIN_LEN),
                                     protocol,
                                     port,
                                     0, // Don't filter by assistant.
                                     &is_inclusive,
                                     app_gids,
                                     app_gids_count,
                                     app_groups,
                                     app_groups_count,
                                     &is_wildcard,
                                     most_specific_app_gid,
                                     NULL,
                                     NULL,
                                     0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        snprintf(msg, msg_sz," Fetching all applications went asynchronous\n");
        goto done;
    }
    if (res == ZPN_RESULT_NOT_FOUND) {
        snprintf(msg, msg_sz, "No application found\n");
        goto done;
    } else if (res) {
        snprintf(msg, msg_sz, "application search error: %s\n", zpath_result_string(res));
        goto done;
    }

 done:
    return res;
}

static int application_search(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    const char *app = NULL;
    int port = 443;
    int protocol = 6;
    char msg[256] = {0};

    int64_t app_gids[ZPN_MAX_APP_GIDS];
    size_t app_gids_count = sizeof(app_gids) / sizeof(app_gids[0]);

    int64_t app_groups[ZPN_MAX_APP_GIDS];
    size_t app_groups_count = sizeof(app_groups) / sizeof(app_groups[0]);

    int64_t most_specific_app_gid;

    int res;
    size_t i;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }
    if (!scope_gid) {
        ZDP("Bad scope ID or not provided\n");
        goto done;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);

    /* Get the domain + port + protocol */
    if (!query_values[1]) {
        ZDP("Require domain or ip\n");
        goto done;
    }
    app = query_values[1];

    if (query_values[2]) {
        port = strtoul(query_values[2], NULL, 0);
    }

    if (query_values[3]) {
        protocol = strtoul(query_values[3], NULL, 0);
    }

    res = do_application_search(scope_gid, app, port, protocol,
                                app_gids, &app_gids_count, app_groups, &app_groups_count, &most_specific_app_gid ,msg, sizeof(msg));
    if (res) {
        ZDP("%s\n", msg);
        goto done;
    }

    ZDP("Testing application search, customer %ld scope %ld to app <%s>, port = %d, protocol = %d\n",
       (long) customer_gid, (long) scope_gid, app, port, protocol);

    ZDP("  App <%s>:%d:%d found app_gids:", app, protocol, port);
    for (i = 0; i < app_gids_count; i++) {
        struct zpn_application *app = NULL;
        res = zpn_application_get_by_id_immediate(app_gids[i], &app);
        if (res) {
            ZDP(" %ld(%s)", (long) app_gids[i], zpath_result_string(res));
        } else {
            ZDP(" %ld(%s)", (long) app_gids[i], app->name);
        }
    }
    ZDP("\n");
    ZDP("  App <%s>:%d:%d Most specific app_gid: %"PRId64"\n", app, protocol, port, most_specific_app_gid);

    ZDP("  App <%s>:%d:%d found app_grp_gids:", app, protocol, port);
    for (i = 0; i < app_groups_count; i++) {
        struct zpn_application_group *grp = NULL;
        res = zpn_application_group_get_by_id_immediate(app_groups[i], &grp);
        if (res) {
            ZDP(" %ld(%s)", (long) app_groups[i], zpath_result_string(res));
        } else {
            ZDP(" %ld(%s)", (long) app_groups[i], grp->name);
        }
    }
    ZDP("\n");

 done:
    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}


static int policy_test(struct zpath_debug_state *request_state,
                       const char **query_values,
                       int query_value_count,
                       void *cookie)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    int64_t app_id = 0;
    struct zpn_application *app_to_evaluate = NULL;
    struct zhash_table *int_hash = NULL;
    struct zhash_table *string_hash = NULL;
    const char *domain = NULL;
    int port = 443;
    int protocol = 6;
    enum zpe_policy_type p_type = zpe_policy_type_access;
    char msg[256] = {0};

    int64_t app_gids[ZPN_MAX_APP_GIDS];
    size_t app_gids_count = sizeof(app_gids) / sizeof(app_gids[0]);

    int64_t app_groups[ZPN_MAX_APP_GIDS];
    size_t app_groups_count = sizeof(app_groups) / sizeof(app_groups[0]);

    int64_t most_specific_app_gid;

    int res;
    size_t i;

    char policy_dump[10000];

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }
    if (!scope_gid) {
        ZDP("Bad scope ID or not provided\n");
        goto done;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);

    if (query_values[7]) {
        if (strcmp(query_values[7], "access") == 0) {
            p_type = zpe_policy_type_access;
        } else if (strcmp(query_values[7], "reauth") == 0) {
            p_type = zpe_policy_type_reauth;
        } else if (strcmp(query_values[7], "siem") == 0) {
            p_type = zpe_policy_type_siem;
        } else if (strcmp(query_values[7], "bypass") == 0) {
            p_type = zpe_policy_type_bypass;
        } else if (strcmp(query_values[7], "redirect") == 0) {
            p_type = zpe_policy_type_broker_redirect;
        } else {
            ZDP("Bad policy type <%s>\n", query_values[7]);
            goto done;
        }
    }

    /* Get the domain + port + protocol */
    if (!query_values[2]) {
        ZDP("Require domain or ip\n");
        goto done;
    }
    domain = query_values[2];
    if (query_values[3]) {
        port = strtoul(query_values[3], NULL, 0);
    }
    if (query_values[4]) {
        protocol = strtoul(query_values[4], NULL, 0);
    }

    ZDP("Testing policy, customer %ld scope %ld to domain <%s>, port = %d, protocol = %d, policy type = %s\n",
       (long) customer_gid, (long) scope_gid, domain, port, protocol, zpe_policy_type_string(p_type));

    res = zpath_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpath_customer_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_customer_ready(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpn_customer_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_scope_ready(scope_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on zpn_scope_ready: %s", zpath_result_string(res));
        goto done;
    }

    res = zpn_application_customer_register(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Waiting on customer_register: %s", zpath_result_string(res));
        goto done;
    }

    zpe_dump_policy_state(scope_gid, policy_dump, sizeof(policy_dump));
    ZDP("Policy state:\n%s", policy_dump);

    /* Add any strings to string hash, as necessary */
    string_hash = zhash_table_alloc(&zpe_allocator);
    int_hash = zhash_table_alloc(&zpe_allocator);
    const char *walk = query_values[1];
    while (walk) {
        char str[1000];
        size_t len;
        const char *next = strstr(walk, ",");
        if (next) {
            len = (next - walk) - 1;
        } else {
            len = strlen(walk);
        }
        snprintf(str, sizeof(str), "%.*s", (int) len, walk);
        zhash_table_store(string_hash, str, len, 0, string_hash);
        ZDP("   Including string hash entry = <%s>\n", str);
        if (next) {
            walk = next + 1;
        } else {
            break;
        }
    }

    res = do_application_search(scope_gid, domain, port, protocol,
                                app_gids, &app_gids_count, app_groups, &app_groups_count, &most_specific_app_gid, msg, sizeof(msg));
    if (res) {
        ZDP("%s\n", msg);
        goto done;
    }

    app_id = app_gids[0];
    res = zpn_application_get_by_id(app_id,
                                    &app_to_evaluate,
                                    NULL,
                                    NULL,
                                    0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        ZDP("Application fetch asynchronous\n");
        goto done;
    } else if (res ||
               zpn_application_is_invalid_domain_names(app_to_evaluate)) {
        ZDP("Could not get application for application id %ld, app :%p, domain_names_count : %d, res: %s\n",
             (long) app_id,
             (app_to_evaluate ? app_to_evaluate: 0),
             (app_to_evaluate ? app_to_evaluate->domain_names_count: 0),
             zpn_result_string(res));
        goto done;
    }

    if(app_to_evaluate && !is_app_owned_or_shared(app_to_evaluate, &scope_gid, NULL, 0, 0)) {
        ZDP("App GID = %ld is not owned by or shared to scope %ld", (long)app_id, (long)scope_gid);
        goto done;
    }

    ZDP("  Domain <%s>:%d:%d found app_gids:", domain, protocol, port);
    for (i = 0; i < app_gids_count; i++) {
        struct zpn_application *app;
        res = zpn_application_get_by_id_immediate(app_gids[i], &app);
        if (res) {
            ZDP(" %ld(%s)", (long) app_gids[i], zpath_result_string(res));
        } else {
            ZDP(" %ld(%s)", (long) app_gids[i], app->name);
        }
        zhash_table_store(int_hash, &(app_gids[i]), sizeof(app_gids[i]), 0, string_hash);
    }
    ZDP("\n");
    ZDP("  Domain <%s>:%d:%d Most specific app_gid: %"PRId64"\n", domain, protocol, port, most_specific_app_gid);
    ZDP("  Domain <%s>:%d:%d found app_grp_gids:", domain, protocol, port);
    for (i = 0; i < app_groups_count; i++) {
        struct zpn_application_group *grp;
        res = zpn_application_group_get_by_id_immediate(app_groups[i], &grp);
        if (res) {
            ZDP(" %ld(%s)", (long) app_groups[i], zpath_result_string(res));
        } else {
            ZDP(" %ld(%s)", (long) app_groups[i], grp->name);
        }
        zhash_table_store(int_hash, &(app_groups[i]), sizeof(app_groups[i]), 0, string_hash);
    }
    ZDP("\n");

    /* Get a policy and run it... */
    struct zpe_policy_built *policy_built = NULL;
    struct zpe_policy_built *policy_built_default = NULL;
    struct zpe_policy_built_timestamp policy_ts = {0};
    int being_built = 0;

    res = zpe_get_scope_policy_without_rebuild(scope_gid,
                                               p_type,
                                               &policy_built_default,
                                               &policy_built,
                                               &policy_ts);
    if (res) {
        ZDP("Getting current policy returned: %s", zpath_result_string(res));
        goto done;
    }

    int64_t now_us = epoch_us();

    ZDP("Using policy rule set %ld, sequence %ld, last tickle %ld(%.3f), being built concurrent: %s"
        "last_rebuild_started_us: %ld, last_rebuild_finished_us: %ld, last_rebuild_time_us: %ld",
        (long) policy_built->policy_meta->gid,
        (long) policy_built->policy_meta->sequence,
        (long) policy_ts.last_refresh_indication_us,
        (double) (now_us - policy_ts.last_refresh_indication_us) / 1000000.0,
        being_built ? "yes" : "no",
        (long)policy_ts.last_rebuild_started_us,
        (long)policy_ts.last_rebuild_finished_us,
        (long)policy_ts.last_rebuild_time_us);

    int rules_evaluated = 0;
    int64_t matched_rule_gid = 0;
    enum zpe_access_action matched_action;
    char *matched_custom_msg = NULL;
    int64_t matched_reauth_timeout = 0;
    int64_t matched_reauth_idle_timeout = 0;
    int64_t matched_action_id = 0;
    int default_app = 0;
    if(app_to_evaluate != NULL && app_to_evaluate->scope_gid == app_to_evaluate->customer_gid){
        default_app = 1;
    }else if(p_type == zpe_policy_type_broker_redirect && customer_gid==scope_gid){
        default_app = 1;
    }

    res = zpe_evaluate_scope(((p_type==zpe_policy_type_broker_redirect) ? NULL : app_gids),
                             ((p_type==zpe_policy_type_broker_redirect) ? 0 : app_gids_count),
                             policy_built_default,
                             policy_built,
                             string_hash,
                             NULL,
                             NULL,
                             int_hash,
                             (p_type==zpe_policy_type_broker_redirect?"Redirect policy":domain),
                             &rules_evaluated,
                             &matched_rule_gid,
                             &matched_action,
                             &matched_custom_msg,
                             &matched_reauth_timeout,
                             &matched_reauth_idle_timeout,
                             &matched_action_id,
                             default_app);

    if (res == ZPATH_RESULT_NOT_FOUND) {
        ZDP("No policy found matching criteria. %d rules evaluated.\n", rules_evaluated);
        goto done;
    }

    ZDP("Evaluated %d rules, Matched rule id %ld, action = %s, custom message = %s, reauth_timeout = %ld, idle_timeout = %ld, action_id = %ld\n",
        rules_evaluated,
        (long) matched_rule_gid,
        zpe_access_action_string(matched_action),
        matched_custom_msg ? matched_custom_msg : "N/A",
        (long) matched_reauth_timeout,
        (long) matched_reauth_idle_timeout,
        (long) matched_action_id);

 done:
    if (string_hash) zhash_table_free(string_hash);
    if (int_hash) zhash_table_free(int_hash);

    return ZPATH_RESULT_NO_ERROR;
}

static int policy_test_build(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    int64_t scope_gid = 0;
    enum zpe_policy_type p_type = zpe_policy_type_access;
    int64_t rule_count = 0;
    int res;
    int i;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }
    if (!scope_gid) {
        ZDP("scope(scope gid),'type'(access|reauth|siem|bypass|redirect) or 'rules'(max rule counts to dispaly) is not provided\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (query_values[1]) {
        if (strcmp(query_values[1], "access") == 0) {
            p_type = zpe_policy_type_access;
        } else if (strcmp(query_values[1], "reauth") == 0) {
            p_type = zpe_policy_type_reauth;
        } else if (strcmp(query_values[1], "siem") == 0) {
            p_type = zpe_policy_type_siem;
        } else if (strcmp(query_values[1], "bypass") == 0) {
            p_type = zpe_policy_type_bypass;
        } else if (strcmp(query_values[1], "redirect") == 0) {
            p_type = zpe_policy_type_broker_redirect;
        } else {
            ZDP("Bad policy type (access|reauth|siem|bypass|redirect) <%s>\n", query_values[1]);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    if (query_values[2]) {
        rule_count = strtoul(query_values[2], NULL, 0);
    }

    int async_count = 0;

    struct zpe_policy_set *set = NULL;
    struct zpe_policy_set_meta *set_meta = NULL;
    struct zhash_table *app_to_rules = NULL;
    struct zpe_mini_policy_set *rules_for_all = NULL;
    struct zhash_table *grp_to_apps = zhash_table_alloc(&zpe_allocator);

    res = zpe_generate_set_from_config(scope_gid,
                                       p_type,
                                       grp_to_apps,
                                       &async_count,
                                       &set,
                                       &set_meta,
                                       &app_to_rules,
                                       &rules_for_all,
                                       NULL,
                                       NULL,
                                       0);
    if (res) {
        ZDP("Generate set for scope %ld returned %s, async_count = %d\n", (long) scope_gid, zpath_result_string(res), async_count);
        goto done;
    }

    ZDP("Generate set for scope %ld, rules = %d, bytes = %d, gid = %ld, sequence = %ld\n",
        (long) scope_gid,
        set->zpe_rule_count,
        set->total_bytes,
        (long) set_meta->gid,
        (long) set_meta->sequence);

    if (set->zpe_rule_count < rule_count) rule_count = set->zpe_rule_count;
    if (rule_count > 0) {
        ZDP("rule order: \n");
        ZDP("------------------------------------------\n");
    }
    for (i = 0; i < rule_count; i++) {
        ZDP("%ld    %s\n", (long)set->rules[i]->gid, set->rules[i]->message?set->rules[i]->message:"");
    }
    ZDP("------------------------------------------\n");

done:
    zpe_free_grp_to_apps(grp_to_apps);
    ZPE_FREE(set);
    ZPE_FREE(set_meta);
    if (app_to_rules) {
        zhash_table_free(app_to_rules);
    }
    if (rules_for_all) {
        ZPE_FREE(rules_for_all->rule_indexes);
        ZPE_FREE(rules_for_all);
    }

    return ZPATH_RESULT_NO_ERROR;
}

struct policy_dump_helper {
    struct zpe_policy_built *policy_built;
    struct zpath_debug_state *request_state;
};

static int app_to_rules_cb(void *cookie, void *object, void *key, size_t key_len)
{
    int i;
    int64_t  *gid = (int64_t *)key;
    struct zpe_mini_policy_set *m_policy = (struct zpe_mini_policy_set *)object;
    struct policy_dump_helper *helper = (struct policy_dump_helper *)cookie;
    struct zpath_debug_state *request_state = helper->request_state;
    struct zpe_policy_built *policy_built = helper->policy_built;
    ZDP(" -- app gid: %"PRId64", rule_count=%d\n", (*gid), m_policy->size);

    for (i = 0; i < m_policy->size; i++) {
        int index = m_policy->rule_indexes[i];
        ZDP( "    app_gid: %"PRId64" ==> rule_order: %d, \trule_gid: %"PRId64"\n", (*gid), index+1, policy_built->policy_set->rules[index]->gid);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int policy_dump(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    enum zpe_policy_type p_type = zpe_policy_type_access;
    int res;
    int i;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }

    if (!scope_gid) {
        ZDP("Missing argument: require scope, for default scope, use customer_gid as scope_gid\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);

    if (query_values[1]) {
        if (strcmp(query_values[1], "access") == 0) {
            p_type = zpe_policy_type_access;
        } else if (strcmp(query_values[1], "reauth") == 0) {
            p_type = zpe_policy_type_reauth;
        } else if (strcmp(query_values[1], "siem") == 0) {
            p_type = zpe_policy_type_siem;
        } else if (strcmp(query_values[1], "bypass") == 0) {
            p_type = zpe_policy_type_bypass;
        } else if (strcmp(query_values[1], "redirect") == 0) {
            p_type = zpe_policy_type_broker_redirect;
        } else if (strcmp(query_values[1], "isolate") == 0) {
            p_type = zpe_policy_type_isolate;
        } else if (strcmp(query_values[1], "inspection") == 0) {
            p_type = zpe_policy_type_inspection;
        } else if (strcmp(query_values[1], "priv_cap") == 0) {
            p_type = zpe_policy_type_priv_capabilities;
        } else if (strcmp(query_values[1], "cred_map") == 0) {
            p_type = zpe_policy_type_cred_map;
        } else if (strcmp(query_values[1], "csp") == 0) {
            p_type = zpe_policy_type_csp_policy;
        } else if (strcmp(query_values[1], "priv_portal") == 0) {
            p_type = zpe_policy_type_priv_portal_policy;
        } else {
            ZDP("Bad policy type (access|reauth|siem|bypass|redirect|isolate|inspection|priv_cap|cred_map|csp|priv_portal) <%s>\n", query_values[1]);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    struct zpe_policy_built *policy_built = NULL;
    struct zpe_policy_built_timestamp policy_ts = {0};
    res = zpe_get_policy_without_rebuild(scope_gid,
                                         p_type,
                                         &policy_built,
                                         NULL,
                                         &policy_ts);
    if (res || policy_built->policy_set->zpe_rule_count == 0) {
        ZDP("Policy for customer: %"PRId64", scope: %"PRId64" is not built yet\n", customer_gid, scope_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    int64_t key = 0;
    struct policy_dump_helper helper = {0};
    helper.policy_built = policy_built;
    helper.request_state = request_state;

    ZDP("last_rebuild_started_us=%"PRId64", last_rebuild_finished_us=%"PRId64"\n", policy_ts.last_rebuild_started_us, policy_ts.last_rebuild_finished_us);
    ZDP("last_rebuild_time_us = %"PRId64"\n", policy_ts.last_rebuild_time_us);
    ZDP("------------------------------------------\n");
    ZDP("**** rule order: \n");
    ZDP("------------------------------------------\n");
    for (i = 0; i < policy_built->policy_set->zpe_rule_count; i++) {
        ZDP("%d\t%"PRId64"\n", i+1, policy_built->policy_set->rules[i]->gid);
    }
    ZDP("------------------------------------------\n\n");
    ZDP("**** app_to_rules: \n");
    ZDP("------------------------------------------\n");
    zhash_table_walk(policy_built->app_to_rules, &key, app_to_rules_cb, &helper);
    ZDP("------------------------------------------\n\n");
    ZDP("**** rules_for_all, rule_count=%d: \n", policy_built->rules_for_all->size);
    ZDP("------------------------------------------\n");
    for (i = 0; i < policy_built->rules_for_all->size; i++) {
        int index = policy_built->rules_for_all->rule_indexes[i];
        ZDP("rule_order: %d\t rule_gid: %"PRId64"\n", index+1, policy_built->policy_set->rules[index]->gid);
    }
    ZDP("------------------------------------------\n\n");

    return ZPATH_RESULT_NO_ERROR;
}

static const char *postfix[] = {"st", "nd", "rd", "th"};
static const char *order_postfix(int n){
    return (n==1)?postfix[0]:((n==2)?postfix[1]:((n==3)?postfix[2]:postfix[3]));
}

static int policy_rule_dump(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    int rule_order = 0;
    enum zpe_policy_type p_type = zpe_policy_type_access;
    int res;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }

    if (!scope_gid) {
        ZDP("Missing argument: require scope, for default scope, use customer_gid as scope_gid\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);

    if (query_values[1]) {
        if (strcmp(query_values[1], "access") == 0) {
            p_type = zpe_policy_type_access;
        } else if (strcmp(query_values[1], "reauth") == 0) {
            p_type = zpe_policy_type_reauth;
        } else if (strcmp(query_values[1], "siem") == 0) {
            p_type = zpe_policy_type_siem;
        } else if (strcmp(query_values[1], "bypass") == 0) {
            p_type = zpe_policy_type_bypass;
        } else if (strcmp(query_values[1], "isolate") == 0) {
            p_type = zpe_policy_type_isolate;
        } else if (strcmp(query_values[1], "inspection") == 0) {
            p_type = zpe_policy_type_inspection;
        } else if (strcmp(query_values[1], "priv_cap") == 0) {
            p_type = zpe_policy_type_priv_capabilities;
        } else if (strcmp(query_values[1], "cred_map") == 0) {
            p_type = zpe_policy_type_cred_map;
        } else if (strcmp(query_values[1], "csp") == 0) {
            p_type = zpe_policy_type_csp_policy;
        } else if (strcmp(query_values[1], "redirect") == 0) {
            p_type = zpe_policy_type_broker_redirect;
        } else if (strcmp(query_values[1], "priv_portal") == 0) {
            p_type = zpe_policy_type_priv_portal_policy;
        }  else if (strcmp(query_values[1], "vpn") == 0) {
            p_type = zpe_policy_type_global_vpn;
        } else {
            ZDP("Bad policy type (access|reauth|siem|bypass|isolate|inspection|priv_cap|cred_map|csp|redirect|priv_portal|vpn) <%s>\n", query_values[1]);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    if (query_values[2]) {
        rule_order = strtol(query_values[2], NULL, 0);
    }

    if (rule_order < 1) {
        ZDP("Missing or invalid argument: rule_order (the order of the policy rule), the rule_order must be greater than or equal to 1\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    struct zpe_policy_built *policy_built = NULL;
    struct zpe_policy_built_timestamp policy_ts = {0};
    res = zpe_get_policy_without_rebuild(scope_gid,
                                         p_type,
                                         &policy_built,
                                         NULL,
                                         &policy_ts);
    if (res || policy_built->policy_set->zpe_rule_count == 0) {
        ZDP("Policy for customer: %"PRId64", scope: %"PRId64" is not built yet\n", customer_gid, scope_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("last_rebuild_started_us=%"PRId64", last_rebuild_finished_us=%"PRId64"\n",
             policy_ts.last_rebuild_started_us, policy_ts.last_rebuild_finished_us);
    ZDP("last_rebuild_time_us = %"PRId64"\n", policy_ts.last_rebuild_time_us);
    ZDP("------------------------------------------\n");
    if (rule_order > policy_built->policy_set->zpe_rule_count) {
        ZDP("rule_order is too large, rule count = %d\n", policy_built->policy_set->zpe_rule_count);
    } else {
        int idx = rule_order - 1;
        int i = 0;
        struct zpe_rule *rule = policy_built->policy_set->rules[idx];
        ZDP("Detail information for %d%s rule, rule gid=%"PRId64", operation=%s, condition_sets count=%d\n",
            rule_order, order_postfix(rule_order), rule->gid, (rule->or_sets?"OR":"AND"), rule->zpe_condition_set_count);
        ZDP("------------------------------------------\n");
        for (i = 0; i < rule->zpe_condition_set_count; i++) {
            int j = 0;
            struct zpe_condition_set *set = rule->condition_sets[i];
            ZDP( " -- %d%s condition set: operation=%s, negative=%d, operands count=%d\n",
                 i+1, order_postfix(i+1), (set->or_operands?"OR":"AND"), set->negate, set->operands_count);
            for (j = 0; j < set->operands_count; j++) {
                struct zpe_operand *operand = set->operands[j];
                if (operand->is_gid) {
                    ZDP("      %d%s operand: gid=%"PRId64"\n", j+1, order_postfix(j+1), operand->gid);
                } else {
                    ZDP("      %d%s operand: data=%s\n", j+1, order_postfix(j+1), (char*)operand->data);
                }
            }
        }
    }
    ZDP("------------------------------------------\n\n");

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * curl localhost:8000/broker/policy/test/customer_tickle?scope=<scope_gid>
 */
static int policy_test_customer_tickle(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count __attribute__((unused)),
                                       void *cookie __attribute__((unused)))
{
    int64_t scope_gid = 0;

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }

    if (!scope_gid) {
        ZDP("Invalid scope value, for default scope, use customer_gid as scope_gid\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    ut_dispatch_tickle(scope_gid);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * curl localhost:8000/broker/policy/debug/stats?scope=<scope_gid>
 */
static int policy_dump_debug_stats(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count __attribute__((unused)),
                                   void *cookie __attribute__((unused)))
{
    int64_t scope_gid = 0;
    char policy_dump[10000];

    if (query_values[0]) {
        scope_gid = strtoul(query_values[0], NULL, 0);
    }

    if (!scope_gid) {
        ZDP("Invalid scope value, for default scope, use customer_gid as scope_gid\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    zpe_dump_policy_state(scope_gid, policy_dump, sizeof(policy_dump));
    ZDP("Policy state:\n%s", policy_dump);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_policy_engine_debug_init(void)
{
    static int initialized = 0;
    int res;

    if (initialized) {
        ZPN_LOG(AL_CRITICAL, "Already initialized");
        return ZPATH_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Test domain seach for an application access request",
                                  "/broker/domain/search",
                                  domain_search,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will evaluate policy (use customer_gid for default scope)",
                                  /* 1 */"domain", "Domain or IP being requested (required)",
                                  /* 2 */"inclusive_only", "if non 0, only return inclusive domains (default 0)",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    res = zpath_debug_add_read_command("Test application search for an application access request",
                                  "/broker/apps/search",
                                  application_search,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will evaluate policy (use customer_gid for default scope)",
                                  /* 1 */"app", "Domain or IP being requested (required)",
                                  /* 2 */"port", "Port used for access. Defaults 443",
                                  /* 3 */"protocol", "Protocol used for access. Defaults 6",
                                  /* 4 */"assistant", "Result limit to this assistant_gid(connector).(Optional)",
                                  /* 5 */"server_ip", "Indicate the IP of the server the connector used (Optional)",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    res = zpath_debug_add_write_command("Test processing policy for a client of a scope for a customer",
                                  "/broker/policy/test",
                                  policy_test,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will evaluate policy (use customer_gid for default scope)",
                                  /* 1 */"strings", "Comma separated value delimited set of strings for client's characteristics for policy, such as:\n"
                                  "                         \"IDP|217246660303022599,TRUSTED_NETWORK|1cd35d99-e509-49c6-a0fe-a1e1f754ac3e|true\"\n"
                                  "                         Strings are of the following forms:\n"
                                  "                            <IDP_GID>|SAML|<ATTR_NAME>|<ATTR_VALUE>\n"
                                  "                            <IDP_GID>|SCIM|<ATTR_ID>|<ATTR_VALUE>\n"
                                  "                            <IDP_GID>|SCIM_GROUP|<SCIM_GROUP_GID>\n"
                                  "                            POSTURE|<UUID>|<true|false>\n"
                                  "                            CLIENT_TYPE|id|<client_type>\n"
                                  "                            TRUSTED_NETWORK|<UUID>|<true|false>\n"
                                  "                            IDP|<IDP_GID>",
                                  /* 2 */"domain", "Domain or IP being requested (required)",
                                  /* 3 */"port", "Port used for access. Defaults 443",
                                  /* 4 */"protocol", "Protocol used for access. Defaults 6",
                                  /* 5 */"assistant", "Result limit to this assistant_gid(connector) (Optional)",
                                  /* 6 */"server_ip", "Indicate the IP of the server the connector used (Optional)",
                                  /* 7 */"type", "Indicate policy_type to use. access, reauth, siem, or bypass. Defaults access",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    res = zpath_debug_add_write_command("Test building current config of policy for a scope. When asynchronous, may need to be called multiple times",
                                  "/broker/policy/build",
                                  policy_test_build,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will fetch apps",
                                  /* 1 */"type", "Indicate policy_type to use. access, reauth, siem, bypass, redirect, isolate, inspection, priv_cap, cred_map, or csp, priv_portal. Defaults access",
                                  /* 2 */"rules", "count of rules to display",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    res = zpath_debug_add_read_command("Dump a built policy",
                                  "/broker/policy/dump",
                                  policy_dump,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will dump the policy, for default scope, use customer_gid as scope_gid",
                                  /* 1 */"type", "Indicate policy_type to use. access, reauth, siem, or bypass. Defaults access",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    res = zpath_debug_add_read_command("Dump a built policy rule",
                                  "/broker/policy/rule/dump",
                                  policy_rule_dump,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will dump the policy, for default scope, use customer_gid as scope_gid",
                                  /* 1 */"type", "Indicate policy_type to use. access, reauth, siem, or bypass. Defaults access",
                                  /* 2 */"order", "rule order",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add debug command");
        return res;
    }

    if (zpath_is_dev_environment()) {
        res = zpath_debug_add_write_command("Test customer tickle policy rebuild",
                                      "/broker/policy/test/customer_tickle",
                                      policy_test_customer_tickle,
                                      NULL,
                                      /* 0 */"scope", "GID of scope on whose behalf we will tickle policy rebuild, for default scope, use customer_gid as scope_gid",
                                      NULL);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not add /broker/policy/test/customer_tickle debug command");
            return res;
        }
    }

    res = zpath_debug_add_read_command("Dump policy debug stats",
                                  "/broker/policy/debug/stats",
                                  policy_dump_debug_stats,
                                  NULL,
                                  /* 0 */"scope", "GID of scope on whose behalf we will tickle policy rebuild, for default scope, use customer_gid as scope_gid",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not add /broker/policy/debug/stats debug command");
        return res;
    }

    initialized = 1;

    return ZPATH_RESULT_NO_ERROR;
}
