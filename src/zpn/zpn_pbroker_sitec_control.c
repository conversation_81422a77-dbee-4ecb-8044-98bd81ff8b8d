#include "zpn/zpn_broker.h"

#include "zpn/zpn_pbroker_sitec_control.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_private_broker_util.h"

struct zpn_firedrill_stats g_pb_firedrill_stats_obj = {0};

static int private_broker_site_firedrill_disable_cb(void *argo_cookie_ptr,
                                                       void *argo_structure_cookie_ptr,
                                                       struct argo_object *object)
{
    struct zpn_sitec_firedrill_exit *firedrill_obj = object->base_structure_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPN_LOG(AL_ERROR, "received firedrill exit cmd from pcc");

    if(firedrill_obj->firedrill_exit && gs->firedrill_status) {
        /* disconnect from sitec and connect to cloud */
        if(gs->pbroker_firedrill_timer) {
            event_del(gs->pbroker_firedrill_timer);
        }
        zpn_private_broker_switch_to_cloud();
        gs->firedrill_status = ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED;
        __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_cmdline_disable_count, 1);
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* Pbroker sitec control connection callback */
static int zpn_pbroker_sitec_control_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie)
{
    int res = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_connected_broker *conn_brk;

    if (state == fohh_connection_connected) {

        ZPN_LOG(AL_NOTICE, "%s - Control channel successfully connected to Site Controller", fohh_description(connection));

        conn_brk = ZPN_CALLOC(sizeof(*conn_brk));
        if (!conn_brk) {
            ZPN_LOG(AL_ERROR, "Unable to alloc connected_broker");
            return ZPN_RESULT_NO_MEMORY;
        }

        conn_brk->f_conn = connection;
        conn_brk->f_conn_incarnation = fohh_connection_incarnation(connection);
        // TODO: Initialize conn_brk further.

        {   /* Lock scope */
            pthread_mutex_lock(&gs->lock);
            fohh_connection_set_dynamic_cookie(connection, conn_brk);
            pthread_mutex_unlock(&gs->lock);
        }

        /* Send our IP (which is likely private) to our peer.  */
        zpn_send_zpn_tcp_info_report(connection, fohh_connection_incarnation(connection), connection, zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);

        ZPN_DEBUG_PRIVATE_BROKER("Private Broker connection %s, Platform Detail is %s, Runtime OS is %s",
                fohh_description(connection),
                zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);

        zpn_send_zpn_pbroker_environment_report(connection, 0, gs->sarge_version);

        /* Send in our very first load status. Load will also be reported periodically */
        zpn_broker_private_broker_load_monitor(zpn_private_broker_site_xmit_pbinfo);

        zpn_private_broker_site_notify_pbctl_to_sitec_status(1);

        res = argo_register_structure(fohh_argo_get_rx(connection),
                                      zpn_sitec_firedrill_exit_description,
                                      private_broker_site_firedrill_disable_cb,
                                      connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register for firedrill rpc %s", fohh_description(connection));
            return res;
        }
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: PBroker sitec control connection DOWN: %s",
                fohh_description(connection),
                reason);
        {
            /* Lock scope */
            pthread_mutex_lock(&gs->lock);
            conn_brk = fohh_connection_get_dynamic_cookie(connection);
            if (conn_brk) {
                /* Clear out connection */
                fohh_connection_set_dynamic_cookie(connection, NULL);
                ZPN_FREE(conn_brk);
            }
            pthread_mutex_unlock(&gs->lock);
            zpn_private_broker_site_notify_pbctl_to_sitec_status(0);
            ZPN_DEBUG_PRIVATE_BROKER("%s - Disconnected from sitec", fohh_description(connection));
        }
    }

    return FOHH_RESULT_NO_ERROR;

}

static int zpn_pbroker_sitec_control_unblock_callback(struct fohh_connection *connection,
                                                       enum fohh_queue_element_type element_type,
                                                       void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

/* Private broker control connection to broker intit */
int zpn_private_broker_sitec_control_conn_init(struct zpn_private_broker_state *private_broker_state,
                                               int64_t private_broker_id,
                                               const char *offline_domain)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    char broker_name[256];
	char sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    char broker_stats_name[268];

    if (!offline_domain || !offline_domain[0] || !zpn_private_broker_get_site_is_active_with_lock()) {
        if (zpn_private_broker_get_site_gid_with_lock()) {
            ZPN_LOG(AL_INFO, "Offline domain is not specified, could not create pbctl to sitec at present");
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain);
    snprintf(sni_name, sizeof(sni_name), "%ld.pbctl.%s", (long) private_broker_id, offline_domain);
    snprintf(broker_stats_name, sizeof(broker_stats_name), "brk-ctl-%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME);

    private_broker_state->sitec_control = fohh_client_create(FOHH_WORKER_ZPN_PBCTL,
                                                             broker_stats_name,
                                                             argo_serialize_binary,
                                                             fohh_connection_style_argo,
                                                             0,
                                                             private_broker_state,
                                                             zpn_pbroker_sitec_control_conn_callback,
                                                             NULL,
                                                             zpn_pbroker_sitec_control_unblock_callback,
                                                             NULL,
                                                             broker_name,
                                                             sni_name,
                                                             offline_domain,
                                                             htons(ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT),
                                                             gs->self_to_private_ssl_ctx,
                                                             1,
                                                             ZPN_PBROKER_BROKER_RX_TIMEOUT_S);

    if (!private_broker_state->sitec_control) {
        /* memory allocation failed */
        ZPN_LOG(AL_ERROR, "Could not initialize private broker control connection to sitec");
        return ZPATH_RESULT_ERR;
    }

    zpn_private_broker_site_register_fohh(private_broker_state->sitec_control, "pbctl.sitec",
            broker_name, sni_name, offline_domain, 1, 1);

    fohh_set_sticky(private_broker_state->sitec_control, 1);

    fohh_connection_monitor_sanity(private_broker_state->sitec_control,
                                   zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                                   ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_set_max_backoff(private_broker_state->sitec_control, ZPN_PRIVATE_BROKER_CONN_MAX_BACKOFF_TIME_S);

    fohh_connection_set_default_sni(private_broker_state->sitec_control, (char*)offline_domain);

    return ZPATH_RESULT_NO_ERROR;
}
