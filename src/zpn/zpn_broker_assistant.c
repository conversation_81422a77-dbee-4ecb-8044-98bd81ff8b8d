/*
 * zpn_broker_assistant.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#include <openssl/rand.h>
#include "base64/base64.h"
#include "fohh/fohh.h"
#include "wally/wally.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_filter_table.h"

#include "argo/argo_buf.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_debug.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_private.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpn_zdx/zpn_zdx_http.h"
#include "zpn_zdx/zpn_zdx_lib.h"
#include <netinet/in.h>
#include <netinet/tcp.h>

#include "admin_probe/admin_probe_rpc.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_broker_assistant_stats.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_mtunnel_stats.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_tx.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_broker_client_path_cache.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_policy.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_broker_transit.h"
#include "zpn/zpn_broker_cryptosvc.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_log_compiled.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_data.h"
#include "zpn_event/zpn_event_stats.h"
#include "zpn_waf/zpn_app_inspection_log.h"
#include "zpn_waf/zpn_app_inspection_log_compiled.h"
#include "zpn/zpn_pbroker_data_connection_stats.h"
#include "zpn/zpn_scope.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "np_lib/np_rpc.h"
#include "zpn/zpn_broker_proxy.h"

#define ZPN_GLOBAL_INSPECTION_CONFIG_GID 1
#ifdef ZPN_TESTING
#include "test_misc/testing_macros.h"
#include "zpn/gtests/zpn_broker_maintanence_auth_log_tests/test_headers/zpn_broker_maintanence_auth_log_tests_weak_headers.h"
static void zpn_broker_assistant_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
static int zpn_broker_assistant_control_conn_callback(struct fohh_connection *connection,
                                           enum fohh_connection_state state,
                                           void *cookie);

void zpath_wrapper_zpn_broker_assistant_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie) {
    zpn_broker_assistant_control_conn_monitor_cb(sock, flags, cookie);
}
int zpath_wrapper_zpn_broker_assistant_control_conn_callback(struct fohh_connection *connection,
                                               enum fohh_connection_state state,
                                               void *cookie) {
    return zpn_broker_assistant_control_conn_callback(connection, state, cookie);
}

#else
#include "test_misc/production_macros.h"
#endif // ZPN_TESTING

extern struct argo_structure_description *zpn_broker_mission_critical_description;

#define A2PB_AUTH_MESSAGE_LEN                            512

enum A2PBAuth {
    AUTH_NOT_SEEN = 0,
    AUTH_UNSUCCESSFUL = 1,
    AUTH_SUCCESSFUL = 2
};

enum config_conn_type {
    config_conn_type_config = 0,
    config_conn_type_override = 1,
    config_conn_type_np = 2
};

struct argo_structure_description *zpn_ast_waf_http_exchanges_log_description = NULL;
struct argo_structure_description *zpn_ast_waf_http_exchanges_api_log_description = NULL;
struct argo_structure_description *zpn_ast_ptag_log_description = NULL;
struct argo_structure_description *zpn_ast_app_inspection_log_description = NULL;
struct argo_structure_description *zpn_ast_krb_inspection_log_description = NULL;
struct argo_structure_description *zpn_ast_ldap_inspection_log_description = NULL;
struct argo_structure_description *zpn_ast_smb_inspection_log_description = NULL;
int64_t g_send_ast_auth_report_every_1_min = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT;

struct zpn_broker_assistant_fohh_state_free_queue {
    zpath_mutex_t                                          lock;
    struct zpn_broker_assistant_fohh_state_tailq           list;
    struct zpn_broker_fohh_state_free_queue_stats          stats;
};

struct zpn_broker_assistant_fohh_state_active_queue {
    zpath_mutex_t                                          lock;
    struct zpn_broker_assistant_fohh_state_tailq           list;
    struct zhash_table*                                    tbl;
};

static struct {
    int64_t number_of_assistant_get_conn_to_return_err;
} ut_hook;

static pthread_mutex_t asst_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

extern char *geoip_db_file;

static struct zpn_broker_assistant_fohh_state_active_queue active_q;
static struct zpn_broker_assistant_fohh_state_free_queue  free_q;
static struct zpn_broker_assistant_fohh_state *zpn_broker_a_state_alloc(void);
static void zpn_broker_a_state_free(struct zpn_broker_assistant_fohh_state *a_state);

static void zpn_broker_a_state_active_q_add(struct zpn_broker_assistant_fohh_state *a_state);
static void zpn_broker_a_state_active_q_del(struct zpn_broker_assistant_fohh_state *a_state);

static int zpn_broker_assistant_ctx_callback_zrdt(struct zdtls_session *zd_sess,
                                                   char *sni,
                                                   const char *sni_suffix,
                                                   SSL_CTX **ssl_ctx);
static int zpn_broker_assistant_verify_callback_zrdt(struct zrdt_conn *z_conn);
static int zpn_broker_assistant_verify_callback_callback_zrdt(void *response_callback_cookie,
                                                           struct wally_registrant *registrant,
                                                           struct wally_table *table,
                                                           int64_t request_id,
                                                           int row_count);
static int zpn_broker_assistant_data_dump_all(struct zpath_debug_state *request_state,
                                              const char **query_values,
                                              int query_value_count,
                                              void *cookie);

static void zpn_broker_assistant_log_conn_redirect(struct fohh_connection *f_conn, void *cookie);
static int zpn_assistant_version_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object);

/* book-keeping for all connector SNIs (used during clean up) */
#define MAX_ZPN_BROKER_ASSISTANT_SNIS 10
int zpn_broker_assistant_snis_count = 0;
char* zpn_broker_assistant_snis[MAX_ZPN_BROKER_ASSISTANT_SNIS];
int zpn_broker_assistant_sni_flags[MAX_ZPN_BROKER_ASSISTANT_SNIS];
static void zpn_broker_assistant_add_sni(char* sni_str, int wildcard);
static void zpn_broker_assistant_update_sni(char* old_sni_str, char *new_sni_str, int wildcard);
static int zpn_broker_assistant_validate_version(const int64_t assistant_gid,
                                                    wally_response_callback_f callback_f,
                                                    void *callback_cookie,
                                                    int64_t callback_id);
static int disconnect_assistants = 0;
static const char* disconnect_assistants_reason = NULL;
static int redirect_assistants = 0;
static const char* redirect_assistants_reason = NULL;

/*
 * connected_assistant is ALWAYS in the CN hash table.
 *
 * But only in the ID hash table once we retrieve the real ID of the
 * assistant from configuration.
 */

LIST_HEAD(connected_assistant_head, connected_assistant);

/*
 * Control connected assistant (not data connection or config connection)
 *
 * delta_from_prev_auth_log - We accumulate the stats from connector after the point that last auth log is sent.
 */
struct connected_assistant {
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* Timer for authentication reports. */
    struct event *timer;
    int first_auth_sent;
    int last_np_state_seen;

    char *version;
    char *sarge_version;

    /* New fields for QBR */
    char *public_cloud;
    char *private_cloud;
    char *region_id;

    int version_major;
    int version_minor;

    unsigned tcp_info_ready:1;
    unsigned status_report_ready:1;

    struct zpn_ast_auth_log auth_log;

    enum connector_type connector_type;
    struct {
        int64_t     delta_mtunnel_count;
    } delta_from_prev_auth_log;
    int64_t g_ast_grp;

    int log_upload;
    uint64_t debug_flag;

    int stats_upload;

    struct argo_inet a_ip;
    char a_cc[CC_STR_LEN + 1];             /* Need to NULL terminate */
    double a_lat;
    double a_lon;

    char *dft_rt_intf;
    char *platform;
    char *platform_detail;
    char *runtime_os;
    char *platform_arch;
    char *platform_version;
    char *frr_version;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;

    LIST_ENTRY(connected_assistant) list_entry;
    int in_list;

    int disabled;
    struct zpn_broker_assistant_capability  capability;
    int32_t udp4_port_util;
    int32_t udp6_port_util;
    int32_t tcp4_port_util;
    int32_t tcp6_port_util;
    int32_t sys_fd_util;
    int32_t proc_fd_util;

    /* Values can be "test", "on" or "off" */
    char dr_running_mode[DR_RUNNING_MODE_STATUS_LEN];
    enum A2PBAuth dr_auth_status;

    /* Represents the status of scope check for the assistant */
    enum A2PBAuth scope_auth_status;

    /*
     * FIXME:Ramesh
     * 1. Now we started receiving the control messages on data connections as well. But the below stats capture only
     * the control connections. We should probably move this stats to a seperate object and link it from
     * connected_object as well as a_state and update there; or some other way.
     */
    uint64_t tx_auth_report_failed;
    uint64_t tx_auth_report_success;
    uint64_t rx_tcp_info_report;
    uint64_t rx_environment_report;
    uint64_t rx_health_report;
    uint64_t rx_app_route_registration;
    uint64_t rx_broker_request_ack;
    uint64_t rx_dns_assistant_check;
    uint64_t rx_log_stats_upload;
    uint64_t rx_log_control;
    uint64_t rx_stats_control;
    uint64_t rx_status_report;
    uint64_t rx_state;
    uint64_t rx_active_connection;
    uint64_t rx_waf_cert_prv_key_req;
    uint64_t rx_waf_cert_gen_req;
    uint64_t active_connection_switched_in;
    uint64_t active_connection_switched_out;
    uint64_t active_control_connection_currently:1;
    uint64_t monitor_count;

    int is_redirect_sent;
};

/*
 * Structure to keep track of all assistants connected to us through control connections. In case of public broker
 * there can be only one control connection from assistant. In case of pbroker, there can be multiple control
 * connections from a connector. This means assistant_list and active_assistant_by_id_hash are disjoint sets in case
 * of pbroker but same in case of broker. active_assistant_by_id_hash have only the control connections which are
 * selected as 'active' control connection.
 */
struct control_connected_assistants {
    pthread_mutex_t lock;

    struct argo_hash_table *active_assistant_by_id_hash;
    struct connected_assistant_head assistant_list;
};

static struct control_connected_assistants assistants_control;

struct connected_assistant_config_fohh_state {
    int is_override;
    enum config_conn_type config_connection_type;
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    struct event *timer;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;
    uint64_t monitor_count;
};

static struct wally_fohh_server *wally_servers[ZPATH_MAX_SHARDS];

static struct wally_fohh_server *wally_server_global;

static struct wally_fohh_server *np_wally_servers[ZPATH_MAX_SHARDS];

static int zpn_tcp_info_report_control_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object);
static int zpn_broker_assistant_environment_report_cb(void *argo_cookie_ptr,
                                                      void *argo_structure_cookie_ptr,
                                                      struct argo_object *object);
static void zpn_broker_assistant_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
static void zpn_broker_assistant_control_conn_auth_log_send(struct connected_assistant *asst,
                                                            enum fohh_connection_state state);

struct zpn_tlv *a_state_get_tlv(struct zpn_broker_assistant_fohh_state *a_state)
{
    if (a_state->tlv_type == zpn_fohh_tlv) {
        return &(a_state->tlv_state.tlv);
    } else {
        return &(a_state->zrdt_tlv_state.tlv);
    }
}

/*
 * This function will be called to update broker's knowledge on each connected(control connection) conenctor's lat/lon
 *
 * Currently this gets called when:
 * 1. soon after control connection is made
 * 2. every 1 min control connector status report callback
 * 3. in control connection monitor cb timer
 *
 * Note that currently, we are not keeping track of lat/lon info for log/config/data connections with connector.
 *
 * Currently, UI forces each conenctor group must be configured with a lat/lon value.
 * so its unlikely that lat/lon value to be 0 unless there is a bug in UI.
 */
static int
zpn_broker_assistant_update_asst_lat_lon(struct connected_assistant *asst)
{
    struct zpn_assistant_group *group;
    int                        res;

    res = zpn_assistant_group_get_by_gid(asst->g_ast_grp,
                                         &group,
                                         NULL,
                                         NULL,
                                         0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (res) {
        return ZPN_RESULT_ERR;
    } else {
        asst->a_lat = group->latitude;
        asst->a_lon = group->longitude;
        asst->auth_log.a_lat = group->latitude;
        asst->auth_log.a_lon = group->longitude;
        if (group->country_code) {
            if (asst->auth_log.cc && strcmp(asst->auth_log.cc, group->country_code)) {
                ZPN_FREE(asst->auth_log.cc);
                asst->auth_log.cc = NULL;
            }

            if (NULL == asst->auth_log.cc) {
                asst->auth_log.cc = ZPN_STRDUP(group->country_code, strlen(group->country_code));
            }
        }
    }

    if (geoip_db_file && (asst->a_lat == 0) && (asst->a_lon == 0)) {
            fohh_connection_address(asst->f_conn, &asst->a_ip, NULL);
            char str[ARGO_INET_ADDRSTRLEN];

            if (zpath_geoip_lookup_double(&asst->a_ip, &asst->a_lat, &asst->a_lon, &asst->a_cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_ASSISTANT("Assistant IP = %s, lat = %f, lon = %f, cc = %s",
                                    argo_inet_generate(str, &(asst->a_ip)), asst->a_lat, asst->a_lon, asst->a_cc);

                asst->auth_log.a_lat = asst->a_lat;
                asst->auth_log.a_lon = asst->a_lon;
                if (asst->auth_log.cc && strcmp(asst->a_cc, asst->auth_log.cc)){
                    ZPN_FREE(asst->auth_log.cc);
                    asst->auth_log.cc = NULL;
                }

                if (NULL == asst->auth_log.cc) {
                    asst->auth_log.cc = ZPN_STRDUP(asst->a_cc, strlen(asst->a_cc));
                }
            }
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Function: zpn_broker_assistant_is_brk_send_pending_mtunnel_end_enabled
*  Fetch the config override value for CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END
*  or CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END
*/
int zpn_broker_assistant_is_brk_send_pending_mtunnel_end_enabled(int64_t customer_gid)
{
    int64_t config_value = 0;
    if (ZPN_BROKER_IS_PUBLIC()) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
                                                            &config_value,
                                                            DEFAULT_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    } else {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END,
                                                            &config_value,
                                                            DEFAULT_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    }
    return config_value ? 1 : 0;
}

int zpn_broker_assistant_is_qbr_insights_feature_enabled(int64_t customer_gid)
{
    int64_t config_value = 0;
    if (ZPN_BROKER_IS_PUBLIC()) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_QBR_INSIGHTS_FEATURE,
                                                            &config_value,
                                                            CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    } else {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_PSE_QBR_INSIGHTS_FEATURE,
                                                            &config_value,
                                                            CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    }
    return config_value ? 1 : 0;
}

static void
zpn_broker_assistant_free_capability(struct zpn_broker_assistant_capability* capability)
{
    int iter;

    for (iter = 0; iter < capability->capabilities_count; iter++) {
        ZPN_FREE(capability->capability_str[iter]);
    }
    ZPN_FREE(capability->capability_str);
}

static void
zpn_broker_assistant_init_capability(struct zpn_broker_assistant_capability* out_capability)
{
    out_capability->capability_str = ZPN_CALLOC(ZPN_ASST_MAX_NUMBER_OF_CAPABILITIES * sizeof(char*));
    out_capability->capabilities_count = 0;
    out_capability->capability_pathing_from_ubrk_enabled = 0;
    out_capability->capability_sticky_cache_enabled = 0;
}


static void
zpn_broker_assistant_store_capabilities(struct zpn_broker_assistant_capability* out_capability,
                                        const char**                            in_capability_str_arr,
                                        int                                     in_capability_count)
{
    int capability_sticky_cache_enabled = 0;
    int capability_pathing_from_ubrk_enabled = 0;

    int iter;
    for (iter = 0; iter < out_capability->capabilities_count; iter++) {
        ZPN_FREE(out_capability->capability_str[iter]);
    }
    for (iter = 0; (iter < in_capability_count && iter < ZPN_ASST_MAX_NUMBER_OF_CAPABILITIES); iter++) {
        /* coverity[suspicious_sizeof : FALSE] */
        out_capability->capability_str[iter] = ZPN_STRDUP(in_capability_str_arr[iter],
                                                          sizeof(in_capability_str_arr[iter]));
        if (0 == strncasecmp(in_capability_str_arr[iter], ZPN_ASST_CAPABILITY_STR_ASC, sizeof(ZPN_ASST_CAPABILITY_STR_ASC))) {
            capability_sticky_cache_enabled = 1;
        } else if (0 == strncasecmp(in_capability_str_arr[iter], ZPN_ASST_CAPABILITY_STR_APU, sizeof(ZPN_ASST_CAPABILITY_STR_APU))) {
            capability_pathing_from_ubrk_enabled = 1;
        }
    }

    out_capability->capability_sticky_cache_enabled = capability_sticky_cache_enabled;
    out_capability->capability_pathing_from_ubrk_enabled = capability_pathing_from_ubrk_enabled;
    out_capability->capabilities_count = in_capability_count;
}


static int zpn_broker_init_connected_assistants()
{
    assistants_control.lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    assistants_control.active_assistant_by_id_hash = argo_hash_alloc(7, 1);
    if (!assistants_control.active_assistant_by_id_hash) {
        ZPN_LOG(AL_ERROR, "Cannot allocate hash table for control connected assistants");
        return ZPN_RESULT_NO_MEMORY;
    }
    LIST_INIT(&(assistants_control.assistant_list));
    return ZPN_RESULT_NO_ERROR;
}


static struct connected_assistant *zpn_broker_find_connected_active_assistant(int64_t asst_id)
{
    struct connected_assistant *asst;

    if (!assistants_control.active_assistant_by_id_hash) {
        ZPN_LOG(AL_DEBUG, "Looking up connected active assistant before connected_assistant is initialized");
        return NULL;
    }

    pthread_mutex_lock(&(assistants_control.lock));
    asst = argo_hash_lookup(assistants_control.active_assistant_by_id_hash,
                            &asst_id,
                            sizeof(asst_id),
                            NULL);
    pthread_mutex_unlock(&(assistants_control.lock));
    return asst;
}

static int zpn_broker_add_connected_active_assistant(int64_t asst_id, struct connected_assistant *asst, int locked)
{
    int res;
    if (!locked) pthread_mutex_lock(&(assistants_control.lock));
    res = argo_hash_store(assistants_control.active_assistant_by_id_hash,
                          &asst_id,
                          sizeof(asst_id),
                          0,
                          asst);
    asst->active_connection_switched_in++;
    asst->active_control_connection_currently = 1;
    if (!locked) pthread_mutex_unlock(&(assistants_control.lock));
    return res;
}

static int zpn_broker_remove_connected_active_assistant(struct connected_assistant *asst, int locked)
{
    int res = ZPN_RESULT_ERR;

    if (!asst) {
        return res;
    }

    ZPN_LOG(AL_NOTICE, "Clean-up the disconnected Assistant (%"PRId64") (%s:%s) conn_type:%s",
            asst->assistant_gid_from_config, fohh_description(asst->f_conn), fohh_state(asst->f_conn),
            asst->active_control_connection_currently ? "ACTIVE" : "STANDBY");

    if (ZPN_BROKER_IS_PRIVATE() && !asst->active_control_connection_currently) {
        // In private broker, there will be only one assistant active control channel and this channel is not active.
        return ZPN_RESULT_NO_ERROR;
    }

    if (!locked) pthread_mutex_lock(&(assistants_control.lock));
    res = argo_hash_remove(assistants_control.active_assistant_by_id_hash,
                           &(asst->assistant_gid_from_config),
                           sizeof(asst->assistant_gid_from_config),
                           asst);
    if (asst->active_control_connection_currently) {
        asst->active_connection_switched_out++;
        asst->active_control_connection_currently = 0;
    }
    if (!locked) pthread_mutex_unlock(&(assistants_control.lock));

    return res;
}

/*
 * This is called when a assistant is connected from FOHH pov. In public broker, the assistant becomes active control
 * channel immediately. In private broker, the assistant will send another message if it wants this connection to
 * behave as the active control channel.
 */
static void zpn_broker_assistant_control_add_connected_assistant(int64_t asst_id, struct connected_assistant *asst)
{
    pthread_mutex_lock(&(assistants_control.lock));
    LIST_INSERT_HEAD(&(assistants_control.assistant_list), asst, list_entry);
    asst->in_list = 1;
    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) {
        zpn_broker_add_connected_active_assistant(asst_id, asst, 1);
    }
    pthread_mutex_unlock(&(assistants_control.lock));
}

static int zpn_broker_assistant_control_remove_connected_assistant(struct connected_assistant *asst)
{
    int res;

    if (ZPN_BROKER_IS_PRIVATE() && asst && asst->active_control_connection_currently) {
        zpn_pbroker_ast_control_connection_stats_update(asst->f_conn, 0);
    }

    pthread_mutex_lock(&(assistants_control.lock));
    res = zpn_broker_remove_connected_active_assistant(asst, 1);
    if (asst->in_list) {
        LIST_REMOVE(asst, list_entry);
        asst->in_list = 0;
    }
    pthread_mutex_unlock(&(assistants_control.lock));

    return res;
}

int zpn_broker_update_connected_assistants_to_dispatcher(struct zpn_broker_dispatcher *dispatcher,
                                                         struct fohh_connection *f_conn) {
    struct connected_assistant *asst, *tmp;

    ZPN_DEBUG_AUTH("Sending all connected assistant auth report to dispatcher because it just came up");

    pthread_mutex_lock(&asst_lock);
    pthread_mutex_lock(&(assistants_control.lock));

    LIST_FOREACH_SAFE(asst, &(assistants_control.assistant_list), list_entry, tmp) {
        if (!zpn_broker_dispatcher_pool_name_match(asst->customer_gid, dispatcher->pool_name))
            continue;

        struct zpn_ast_auth_report rep;
        memset(&rep, 0, sizeof(struct zpn_ast_auth_report));
        rep.g_cst = ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config);
        rep.g_ast = asst->assistant_gid_from_config;
        rep.g_brk = broker_instance_id;
        rep.g_ast_grp = asst->g_ast_grp;
        rep.a_lat = asst->a_lat;
        rep.a_lon = asst->a_lon;
        rep.auth_us = asst->auth_log.auth_us;
        rep.deauth_us = asst->auth_log.deauth_us;
        rep.status = asst->auth_log.status;
        rep.capabilities = asst->capability.capability_str;
        rep.capabilities_count = asst->capability.capabilities_count;
        rep.cpu = asst->auth_log.cpu_util;
        rep.mem = asst->auth_log.mem_util;
        rep.target = asst->auth_log.current_target_count;
        rep.mtunnel = asst->auth_log.total_mtunnel_count;
        rep.udp4_port_util = asst->udp4_port_util;
        rep.udp6_port_util = asst->udp6_port_util;
        rep.tcp4_port_util = asst->tcp4_port_util;
        rep.tcp6_port_util = asst->tcp6_port_util;
        rep.sys_fd_util = asst->sys_fd_util;
        rep.proc_fd_util = asst->proc_fd_util;
        rep.disabled = asst->disabled;

        if (zpn_send_zpn_ast_auth_report_struct(f_conn, 0, &rep) == ZPN_RESULT_NO_ERROR) {
            ZPN_DEBUG_AUTH("Sent ast auth report for connected assistant %ld a_lat(%lf) a_lon(%lf) done to %s",
                             (long)asst->assistant_gid_from_config, rep.a_lat, rep.a_lon, fohh_peer_cn(f_conn));
        } else {
            ZPN_DEBUG_AUTH("Cannot sent ast auth report for connected assistant %ld to %s",
                             (long)asst->assistant_gid_from_config,
                             fohh_peer_cn(f_conn));
            break;
        }
    }

    pthread_mutex_unlock(&(assistants_control.lock));
    pthread_mutex_unlock(&asst_lock);

    return ZPN_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_control_dump_all(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    struct connected_assistant* asst;
    struct connected_assistant* tmp;
    char                        ipaddr[64];
    int                         iter;

    ZDP(" -- control connections view -- \n");
    pthread_mutex_lock(&asst_lock);
    pthread_mutex_lock(&(assistants_control.lock));

    LIST_FOREACH_SAFE(asst, &(assistants_control.assistant_list), list_entry, tmp) {
        ZDP("assistant_gid_from_config           : %"PRId64"\n", asst->assistant_gid_from_config);
        ZDP("assistant_customer_gid              : %"PRIu64"\n", (uint64_t)ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config));
        ZDP("assistant_scope_gid                 : %"PRId64"\n", (int64_t)(asst->scope_gid));
        ZDP("f_conn                              : %s\n", fohh_description(asst->f_conn));
        ZDP("f_conn_incarnation                  : %"PRId64"\n", asst->f_conn_incarnation);
        ZDP("fist_auth_sent                      : %d\n", asst->first_auth_sent);
        ZDP("version                             : %s\n", asst->version);
        ZDP("sarge version                       : %s\n", asst->sarge_version);
        ZDP("version_major                       : %d\n", asst->version_major);
        ZDP("version_minor                       : %d\n", asst->version_minor);
        ZDP("public cloud                        : %s\n", asst->public_cloud);
        ZDP("private cloud                       : %s\n", asst->private_cloud);
        ZDP("region id                           : %s\n", asst->region_id);
        ZDP("assitant_grp                        : %"PRId64"\n", asst->g_ast_grp);
        ZDP("log_upload                          : %d\n", asst->log_upload);
        ZDP("debug_flag                          : %"PRIu64"\n", asst->debug_flag);
        ZDP("stats_upload                        : %d\n", asst->stats_upload);
        ZDP("a_ip                                : %s\n", argo_inet_generate(ipaddr, &asst->a_ip));
        ZDP("a_cc                                : %s\n", asst->a_cc);
        ZDP("a_lat                               : %f\n", asst->a_lat);
        ZDP("a_lon                               : %f\n", asst->a_lon);
        ZDP("dft_rt_intf                         : %s\n", asst->dft_rt_intf);
        ZDP("platform                            : %s\n", asst->platform);
        ZDP("platform detail                     : %s\n", asst->platform_detail);
        ZDP("Runtime OS                          : %s\n", asst->runtime_os);
        ZDP("tunnel_id                           : %s\n", asst->tunnel_id);
        ZDP("capability_pathing_from_ubrk_enabled: %d\n", asst->capability.capability_pathing_from_ubrk_enabled);
        ZDP("capability_sticky_cache_enabled     : %d\n", asst->capability.capability_sticky_cache_enabled);
        for (iter = 0; (iter < asst->capability.capabilities_count && iter < ZPN_ASST_MAX_NUMBER_OF_CAPABILITIES); iter++) {
            ZDP("capability str                       : %s\n", asst->capability.capability_str[iter]);
        }
        ZDP("capabilities_count                  : %d\n", asst->capability.capabilities_count);
        ZDP("disabled                            : %d\n", asst->disabled);
        ZDP("tx_auth_report_failed               : %"PRId64"\n", asst->tx_auth_report_failed);
        ZDP("tx_auth_report_success              : %"PRId64"\n", asst->tx_auth_report_success);
        ZDP("rx_tcp_info_report                  : %"PRId64"\n", asst->rx_tcp_info_report);
        ZDP("rx_environment_report               : %"PRId64"\n", asst->rx_environment_report);
        ZDP("rx_tcp_info_report                  : %"PRId64"\n", asst->rx_tcp_info_report);
        ZDP("rx_environment_report               : %"PRId64"\n", asst->rx_environment_report);
        ZDP("rx_health_report                    : %"PRId64"\n", asst->rx_health_report);
        ZDP("rx_app_route_registration           : %"PRId64"\n", asst->rx_app_route_registration);
        ZDP("rx_broker_request_ack               : %"PRId64"\n", asst->rx_broker_request_ack);
        ZDP("rx_dns_assistant_check              : %"PRId64"\n", asst->rx_dns_assistant_check);
        ZDP("rx_log_stats_upload                 : %"PRId64"\n", asst->rx_log_stats_upload);
        ZDP("rx_log_control                      : %"PRId64"\n", asst->rx_log_control);
        ZDP("rx_stats_control                    : %"PRId64"\n", asst->rx_stats_control);
        ZDP("rx_status_report                    : %"PRId64"\n", asst->rx_status_report);
        ZDP("rx_active_connection                : %"PRId64"\n", asst->rx_active_connection);
        ZDP("rx_waf_cert_prv_key_req             : %"PRId64"\n", asst->rx_waf_cert_prv_key_req);
        ZDP("rx_waf_cert_gen_req                 : %"PRId64"\n", asst->rx_waf_cert_gen_req);
        ZDP("active_connection_switched_in       : %"PRId64"\n", asst->active_connection_switched_in);
        ZDP("active_connection_switched_out      : %"PRId64"\n", asst->active_connection_switched_out);
        ZDP("active_control_connection_currently : %d\n", asst->active_control_connection_currently);
        ZDP("platform detail                     : %s\n", asst->platform_arch);
        ZDP("platform version                    : %s\n", asst->platform_version);
        ZDP("frr version                         : %s\n", asst->frr_version);

        ZDP("\n");
    }

    pthread_mutex_unlock(&(assistants_control.lock));
    pthread_mutex_unlock(&asst_lock);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_control_dump_all_short(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    struct connected_assistant* asst;
    struct connected_assistant* tmp;

    ZDP(" -- control connections view -- \n");
    pthread_mutex_lock(&asst_lock);
    pthread_mutex_lock(&(assistants_control.lock));

    uint64_t total_status_report_received = 0;
    uint64_t total_health_report_received = 0;
    uint64_t total_route_registration_received = 0;
    uint64_t total_asst_state_received = 0;
    uint64_t total_control_connections = 0;
    uint64_t total_auth_sent_success = 0;
    uint64_t total_auth_sent_failed = 0;

    LIST_FOREACH_SAFE(asst, &(assistants_control.assistant_list), list_entry, tmp) {
        ZDP("assistant_gid_from_config           : %"PRId64"\n", asst->assistant_gid_from_config);
        ZDP("assistant_customer_gid              : %"PRIu64"\n", (uint64_t)ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config));
        ZDP("zpn_assistant_status_report_received: %"PRId64"\n", asst->rx_status_report);
        ZDP("zpn_health_report_received          : %"PRId64"\n", asst->rx_health_report);
        ZDP("zpn_app_route_registration_received : %"PRId64"\n", asst->rx_app_route_registration);
        ZDP("zpn_asst_state_received             : %"PRId64"\n", asst->rx_state);
        ZDP("zpn_tcp_info_report_received        : %"PRId64"\n", asst->rx_tcp_info_report);
        ZDP("tx_auth_report_failed               : %"PRId64"\n", asst->tx_auth_report_failed);
        ZDP("tx_auth_report_success              : %"PRId64"\n", asst->tx_auth_report_success);

        total_status_report_received += asst->rx_status_report;
        total_health_report_received += asst->rx_health_report;
        total_route_registration_received += asst->rx_app_route_registration;
        total_asst_state_received += asst->rx_state;
        total_auth_sent_success += asst->tx_auth_report_success;
        total_auth_sent_failed += asst->tx_auth_report_failed;
        total_control_connections++;

        ZDP("\n");
    }

    ZDP("---------------------------------------------------------------------   \n");
    ZDP("total_status_report_received              : %"PRId64"\n", total_status_report_received);
    ZDP("total_health_report_received              : %"PRId64"\n", total_health_report_received);
    ZDP("total_route_registration_received         : %"PRId64"\n", total_route_registration_received);
    ZDP("total_asst_state_received                 : %"PRId64"\n", total_asst_state_received);
    ZDP("total_control_connections                 : %"PRId64"\n", total_control_connections);
    ZDP("total_auth_sent_success                   : %"PRId64"\n", total_auth_sent_success);
    ZDP("total_auth_sent_failed                    : %"PRId64"\n", total_auth_sent_failed);


    pthread_mutex_unlock(&(assistants_control.lock));
    pthread_mutex_unlock(&asst_lock);

    return ZPATH_RESULT_NO_ERROR;

}

static int zpn_broker_health_log(struct connected_assistant *asst,
                                 struct zpn_health_report *rep)
{
    rep->ba_rtt = fohh_connection_get_app_rtt_us(asst->f_conn);

    if (zpn_health_collection) {
        argo_log_structure_immediate(zpn_health_collection,
                                     argo_log_priority_info,
                                     0,
                                     "asst_health",
                                     zpn_health_log_description,
                                     rep);
    }

    /* Private broker does not log direct to customer */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        return ZPATH_RESULT_NO_ERROR;
    }

    return zpath_customer_log_struct(rep->g_cst,
                                     zpath_customer_log_type_zpn_health,
                                     "asst_health",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_health_log_description,
                                     rep);
}

static int zpn_broker_app_report_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_app_route_registration *rep = object->base_structure_void;
    int res;
    struct connected_assistant *asst;
    int64_t cust_id;
    int32_t diff;

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);

    if (!asst) {
        ZPN_LOG(AL_WARNING, "Received assistant health monitor from assistant with no authentication state: %s (state may just be arriving slowly)", fohh_description(f_conn));
        /* We don't return an error here in order to give configuration time to arrive. */
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_NO_ERROR;
    }

    if (ZPN_BROKER_IS_PRIVATE() && ((asst->dr_auth_status == AUTH_UNSUCCESSFUL) || (asst->scope_auth_status == AUTH_UNSUCCESSFUL)))
    {
        if(asst->dr_auth_status == AUTH_UNSUCCESSFUL) {
            ZPN_LOG(AL_DEBUG, "Authentication is not completed yet due to DR state mismatch: %s", fohh_description(f_conn));
        }

        /* Disabling this log message, since it's too frequent */
#if 0
        if(asst->scope_auth_status == AUTH_UNSUCCESSFUL) {
            ZPN_LOG(AL_DEBUG, "Authentication is not completed yet due to scope check failure: %s", fohh_description(f_conn));
        }
#endif
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_NO_ERROR;
    }

    asst->rx_app_route_registration++;
    cust_id = ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config);
    if (cust_id != ZPATH_GID_GET_CUSTOMER_GID(rep->g_app)) {
        ZPN_LOG(AL_WARNING, "Mismatched health report ownership, asst_gid_from_config = %ld, app_id = %ld, asst_id = %ld: %s",
                (long)asst->assistant_gid_from_config,
                (long)rep->g_app,
                (long)rep->g_ast,
                fohh_description(f_conn));
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_ERR;
    }
    pthread_mutex_unlock(&asst_lock);

    rep->g_brk = ZPN_BROKER_GET_GID();

    diff = rep->expires - rep->report;
    rep->report = epoch_s() - (fohh_connection_get_app_rtt_us(f_conn)/2000000);
    rep->expires = rep->report + diff;
    rep->g_cst = cust_id;

    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_broker_dispatch_send_app_local(rep);
    } else {
        res = zpn_broker_dispatch_send_app(rep);
    }

    if (res) {
        ZPN_LOG(AL_WARNING, "Could not send health report for application %ld: %s", (long) rep->g_app, zpn_result_string(res));
    }

    if (zpn_health_collection) {
        argo_log_structure_immediate(zpn_health_collection,
                                     argo_log_priority_info,
                                     0,
                                     "app_route_log",
                                     zpn_app_route_log_description,
                                     rep);
    }

    if (g_broker_common_cfg->instance_type != ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        if ((res = zpath_customer_log_struct(rep->g_cst,
                                             zpath_customer_log_type_zpn_health,
                                             "app_route_log",
                                             NULL,
                                             NULL,
                                             NULL,
                                             NULL,
                                             zpn_app_route_log_description,
                                             rep))) {
            ZPN_LOG(AL_WARNING, "Could not send app registration report to health subsystem: %s", zpn_result_string(res));
        }
    }

    return ZPN_RESULT_NO_ERROR;
}



static int zpn_broker_health_report_cb(void *argo_cookie_ptr,
                                       void *argo_structure_cookie_ptr,
                                       struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_health_report *rep = object->base_structure_void;
    int res;
    struct connected_assistant *asst;
    int64_t cust_id;
    int32_t diff;

#if 0
    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }
#endif

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);

    if (!asst) {
        ZPN_LOG(AL_WARNING, "Received assistant health monitor from assistant with no authentication state: %s (state may just be arriving slowly)", fohh_description(f_conn));
        /* We don't return an error here in order to give configuration time to arrive. */
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_NO_ERROR;
    }

    if (ZPN_BROKER_IS_PRIVATE() && ((asst->dr_auth_status == AUTH_UNSUCCESSFUL) || (asst->scope_auth_status == AUTH_UNSUCCESSFUL)))
    {
        if(asst->dr_auth_status == AUTH_UNSUCCESSFUL) {
            ZPN_LOG(AL_DEBUG, "Authentication is not completed yet due to DR state mismatch: %s", fohh_description(f_conn));
        }

        /* Disabling this log message, since it's too frequent */
#if 0
        if(asst->scope_auth_status == AUTH_UNSUCCESSFUL) {
            ZPN_LOG(AL_DEBUG, "Authentication is not completed yet due to scope check failure: %s", fohh_description(f_conn));
        }
#endif
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_NO_ERROR;
    }

    asst->rx_health_report++;
    cust_id = ZPATH_GID_GET_CUSTOMER_GID(asst->assistant_gid_from_config);
    if ((cust_id != ZPATH_GID_GET_CUSTOMER_GID(rep->g_app)) ||
        ((rep->g_aps) && (cust_id != ZPATH_GID_GET_CUSTOMER_GID(rep->g_aps)))) {
        ZPN_LOG(AL_WARNING, "Mismatched health report ownership, asst_gid_from_config = %ld, app_id = %ld, app_srv_id = %ld, asst_id = %ld: %s",
                (long)asst->assistant_gid_from_config,
                (long)rep->g_app,
                (long)rep->g_aps,
                (long)rep->g_ast,
                fohh_description(f_conn));
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_ERR;
    }

    // Update assistant group too, in case wally lookup failed somehow.
    if (!asst->g_ast_grp) {
        asst->g_ast_grp = rep->g_ast_grp;
        asst->auth_log.g_ast_grp = rep->g_ast_grp;
    }

    pthread_mutex_unlock(&asst_lock);

    if (rep->app_type == NULL) rep->app_type = "name";
    if (rep->ip_protocol == 0) rep->ip_protocol = 6;

    rep->g_brk = ZPN_BROKER_GET_GID();

    diff = rep->expires - rep->report;
    rep->report = epoch_s() - (fohh_connection_get_app_rtt_us(f_conn)/2000000);
    rep->expires = rep->report + diff;
    rep->g_cst = cust_id;
    rep->g_microtenant = is_scope_default(asst->scope_gid) ? 0 : asst->scope_gid;

    int fohh_thread_id = fohh_connection_get_thread_id(f_conn);
    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_broker_dispatch_send_health_local(fohh_thread_id, rep);
    } else {
        res = zpn_broker_dispatch_send_health(fohh_thread_id, rep);
    }

    if (res) {
        ZPN_LOG(AL_WARNING, "Could not send health report for application %ld: %s", (long) rep->g_app, zpn_result_string(res));
    }


    if (rep->g_app_grp == rep->g_srv_grp) {
        /* We're letting this data get dispatched, but not health
         * logged. The dispatcher can deal with weird application
         * groups, but the health logging system cannot */
        ZPN_LOG(AL_WARNING, "%ld: Server and App groups identical- discarding health. (%ld)", (long) rep->g_ast, (long) rep->g_app_grp);
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_broker_health_log(asst, rep);

    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_WARNING, "zpn broker health log fail: %s", zpn_result_string(res));
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_update_appc_location_info(struct argo_inet *ip,
                                          struct zpn_ast_auth_log *log)
{
    double lat = 0;
    double lon = 0;
    char str[ARGO_INET_ADDRSTRLEN] = {0};
    char a_cc[CC_BUF_LEN] = {0};
    char a_city[CITY_BUF_LEN] = {0};
    char a_sc[SC_BUF_LEN] = {0};

    if (zpn_broker_assistant_is_qbr_insights_feature_enabled(log->g_cst)) {
        if (geoip_db_file) {
            if (! log->g_ast_country_code) {
                if (zpath_geoip_lookup_double(ip, &lat, &lon, &a_cc[0]) == ZPATH_RESULT_NO_ERROR) {
                    ZPN_DEBUG_ASSISTANT("Assistant IP = %s, lat = %f, lon = %f, cc = %s",
                                    argo_inet_generate(str, ip), lat, lon, a_cc);
                } else {
                    if (!argo_inet_is_private(ip)) {
                        ZPN_LOG(AL_WARNING, "geoip lookup failed for assistant ip = %s", argo_inet_generate(str, ip));
                    } else {
                        ZPN_DEBUG_ASSISTANT("geoip lookup failed for assistant ip = %s", argo_inet_generate(str, ip));
                    }
                }
                log->g_ast_country_code = (a_cc[0] == '\0') ? NULL : ZPN_STRDUP(a_cc, strlen(a_cc));
            }

            if (! log->g_ast_city) {
                if (zpath_geoip_lookup_city(ip, a_city, sizeof(a_city)) == ZPATH_RESULT_NO_ERROR) {
                    ZPN_DEBUG_ASSISTANT("Assistant IP = %s, city = %s",
                                    argo_inet_generate(str, ip), a_city);
                } else {
                    ZPN_DEBUG_ASSISTANT("City lookup failed for assistant ip = %s", argo_inet_generate(str, ip));
                }
                log->g_ast_city = (a_city[0] == '\0') ? NULL : ZPN_STRDUP(a_city, strlen(a_city));
            }

            if (! log->g_ast_subdivision_code) {
                if (zpath_geoip_lookup_subdivision_code(ip, a_sc, sizeof(a_sc)) == ZPATH_RESULT_NO_ERROR) {
                    ZPN_DEBUG_ASSISTANT("Assistant IP = %s, subdivision code = %s",
                                    argo_inet_generate(str, ip), a_sc);
                } else {
                    ZPN_DEBUG_ASSISTANT("Subdivision lookup failed for assistant ip = %s", argo_inet_generate(str, ip));
                }
                log->g_ast_subdivision_code = (a_sc[0] == '\0') ? NULL : ZPN_STRDUP(a_sc, strlen(a_sc));
            }
        }
    } else {
        if (log->g_ast_country_code) ZPN_FREE(log->g_ast_country_code);
        if (log->g_ast_city) ZPN_FREE(log->g_ast_city);
        if (log->g_ast_subdivision_code) ZPN_FREE(log->g_ast_subdivision_code);
        log->g_ast_city = log->g_ast_country_code = log->g_ast_subdivision_code = NULL;
    }
}

int
zpn_broker_assistant_auth_log(struct fohh_connection *f_conn,
                              struct zpn_ast_auth_log *log,
                              int64_t asst_id,
                              char *type,
                              enum fohh_connection_state state)
{
    struct fohh_tcp_info info;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    int64_t assistant_grp_id = 0;
    size_t count = 1;
    int res = 0;
    res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                 &ag_relation,
                                                                 &count,
                                                                 NULL,
                                                                 NULL,
                                                                 0);
    if (res) {
        ZPN_LOG(AL_WARNING, "Error fetching assistantgroup_assistant_relation. Assistant ID = %"PRId64": %s",
                         asst_id, zpn_result_string(res));
    } else {
        assistant_grp_id = ag_relation->assistant_group_id;
    }

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
    log->g_ast = asst_id;
    log->g_ast_grp = assistant_grp_id;
    log->type = type;
    log->g_brk = ZPN_BROKER_GET_GID();
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_connector);
    if (ZPN_BROKER_IS_PRIVATE()) {
        log->g_pbrk = g_broker_common_cfg->private_broker.broker_id;
    }
    if (f_conn) {
        if (!log->pub_ip.length) {
            uint16_t pub_port_ne;
            fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
            log->pub_port = ntohs(pub_port_ne);
        }

        if (strcmp(type, ZPN_ASSISTANT_BROKER_DATA) == 0 || strcmp(type, ZPN_ASSISTANT_BROKER_CONTROL) == 0) {
            zpn_update_appc_location_info(&(log->pub_ip), log);
        }

        memset(&info, 0, sizeof(info));
        if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
            log->tcpi_snd_mss = info.tcpi_snd_mss;
            log->tcpi_rcv_mss = info.tcpi_rcv_mss;
            log->tcpi_rtt = info.tcpi_rtt;
            log->tcpi_rttvar = info.tcpi_rttvar;
            log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
            log->tcpi_advmss = info.tcpi_advmss;
            log->tcpi_reordering = info.tcpi_reordering;
            log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
            log->tcpi_rcv_space = info.tcpi_rcv_space;
            log->tcpi_total_retrans = info.tcpi_total_retrans;
            log->tcpi_thru_put = info.tcpi_thru_put;
            log->tcpi_unacked = info.tcpi_unacked;
            log->tcpi_sacked = info.tcpi_sacked;
            log->tcpi_lost = info.tcpi_lost;
            log->tcpi_fackets = info.tcpi_fackets;

            log->tcpi_last_data_sent = info.tcpi_last_data_sent;
            log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
            log->tcpi_last_data_recv = info.tcpi_last_data_recv;
            log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
            log->tcpi_bytes_acked = info.tcpi_bytes_acked;
            log->tcpi_bytes_received = info.tcpi_bytes_received;
            log->tcpi_segs_out = info.tcpi_segs_out;
            log->tcpi_segs_in = info.tcpi_segs_in;

            if ((info.tcpi_rtt / 1000) > ZPN_ASSISTANT_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
                ZPN_LOG(AL_NOTICE, "%s: Assistant connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, ZPN_ASSISTANT_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
            }
        }

        fohh_connection_get_stats(f_conn,
                                  &(log->transmit_bytes),
                                  &(log->receive_bytes),
                                  &(log->transmit_objects),
                                  &(log->receive_objects),
                                  &(log->transmit_raw_tlv),
                                  &(log->receive_raw_tlv));
        /* Skip quiet app connections */
        if (!fohh_connection_is_quiet(f_conn)) {
            log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
            if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
                log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
            } else {
                ZPN_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
            }
            if (fohh_histogram_get_delta_app_rtt_counts_array(f_conn, log->delta_app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
                log->delta_app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
            } else {
                ZPN_LOG(AL_DEBUG, "%s: Unable to get delta app rtt histogram", fohh_description(f_conn));
            }
            if (fohh_histogram_update_prev_app_rtt_hostogram(f_conn) != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_DEBUG, "%s: Unable to update previous app rtt histogram", fohh_description(f_conn));
            }
        }

        /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
        log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
        }

        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
        }

        fohh_connection_reset_max_rtt(f_conn);

        /* This Log type will be present in case of fohh log connection
           it will be null for data */
        log->log_type = fohh_connection_get_log_collection_name(f_conn);
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    if (log->gids) {
        ZPN_FREE(log->gids);
        log->gids = NULL;
        log->gids_count = 0;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        int64_t siem_ids[ZPN_MAX_SIEM_IDS];
        size_t siem_ids_count = ZPN_MAX_SIEM_IDS;
        zpn_broker_siem_get_siem_gids_ast_auth_log(log, siem_ids, &siem_ids_count);
        if (siem_ids_count) {
            log->gids = ZPN_CALLOC(siem_ids_count * sizeof(*(log->gids)));
            for (size_t ii = 0; ii < siem_ids_count; ii++) {
                log->gids[ii] = siem_ids[ii];
            }
            log->gids_count = siem_ids_count;
        }
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);

    /* Log to local for debug purpose if enabled */
    if (ZPN_BROKER_IS_PUBLIC() && zpn_auth_collection) {
        argo_log_structure_immediate(zpn_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "auth_log",
                                     zpn_ast_auth_log_description,
                                     log);
    }

    if (ZPN_BROKER_IS_PRIVATE() && zpn_ast_auth_collection) {
        argo_log_structure_immediate(zpn_ast_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "ast_auth_log",
                                     zpn_ast_auth_log_description,
                                     log);

    }


    /* Private broker does not log directly. */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        if (log->gids) {
            ZPN_FREE(log->gids);
            log->gids = NULL;
            log->gids_count = 0;
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    zpn_broker_siem_ast_auth_log(log);

    res = zpath_customer_log_struct(log->g_cst,
                                     zpath_customer_log_type_zpn_ast_auth,
                                     "asst_auth",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_ast_auth_log_description,
                                     log);
    if ( log->slogger_info){
        ZPN_FREE(log->slogger_info);
        log->slogger_info = NULL;
    }
    return res;
}

static int zpn_broker_assistant_auth_log_zrdt(struct zrdt_conn *z_conn,
                                              struct zpn_ast_auth_log *log,
                                              int64_t asst_id,
                                              char *type,
                                              enum zrdt_conn_status state)
{
    struct zrdt_conn_stats cs;
    int i;

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
    log->g_ast = asst_id;
    log->type = type;
    log->g_brk = ZPN_BROKER_GET_GID();
    if (ZPN_BROKER_IS_PRIVATE()) {
        log->g_pbrk = g_broker_common_cfg->private_broker.broker_id;
    }

    if (state == zrdt_conn_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_zrdt_tlv);

    if (z_conn) {
        if (!log->pub_ip.length) {
            uint16_t pub_port_ne;
            zdtls_connection_address_and_port(zrdt_conn_get_datagram_tx_cookie(z_conn), &(log->pub_ip), &pub_port_ne, NULL, NULL);
            log->pub_port = ntohs(pub_port_ne);
        }
    }

    if (strcmp(type, ZPN_ASSISTANT_BROKER_DATA) == 0) {
        zpn_update_appc_location_info(&(log->pub_ip), log);
    }

    memset(&cs, 0, sizeof(struct zrdt_conn_stats));
    zrdt_get_conn_stats(z_conn, &cs);
    log->streams_created = cs.streams_created;
    log->streams_destroyed = cs.streams_destroyed;
    log->packets_sent = cs.packets_sent;
    log->packets_recv = cs.packets_recv;
    log->peer_loss = cs.peer_loss;
    log->local_loss = cs.local_loss;
    for (i = 0; i < ZRDT_CONN_HISTO_SIZE; i++) {
        log->zrdt_rtt[i] = cs.rtt_histo[i];
    }
    log->zrdt_rtt_count = ZRDT_CONN_HISTO_SIZE;

    /* Log to local for debug purpose if enabled */
    if (ZPN_BROKER_IS_PUBLIC() && zpn_auth_collection) {
        argo_log_structure_immediate(zpn_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "auth_log",
                                     zpn_ast_auth_log_description,
                                     log);
    }

    if (ZPN_BROKER_IS_PRIVATE() && zpn_ast_auth_collection) {
        argo_log_structure_immediate(zpn_ast_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "ast_auth_log",
                                     zpn_ast_auth_log_description,
                                     log);

    }

    /* Private broker does not log directly. */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        return ZPATH_RESULT_NO_ERROR;
    }

    zpn_broker_siem_ast_auth_log(log);

    int res = zpath_customer_log_struct(log->g_cst,
                                     zpath_customer_log_type_zpn_ast_auth,
                                     "asst_auth",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_ast_auth_log_description,
                                     log);
    if ( log->slogger_info){
        ZPN_FREE(log->slogger_info);
        log->slogger_info = NULL;
    }
    return res;
}

static int
zpn_broker_assistant_auth_report_to_dispatcher(struct connected_assistant *asst)
{
    int64_t asst_id = asst->assistant_gid_from_config;
    struct zpn_ast_auth_report rep;
    int res;

    memset(&rep, 0, sizeof(struct zpn_ast_auth_report));
    rep.g_cst = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
    rep.g_ast = asst_id;
    rep.g_brk = ZPN_BROKER_GET_GID();
    rep.g_ast_grp = asst->g_ast_grp;
    rep.a_lat = asst->a_lat;
    rep.a_lon = asst->a_lon;
    rep.auth_us = asst->auth_log.auth_us;
    rep.deauth_us = asst->auth_log.deauth_us;
    rep.status = asst->auth_log.status;
    ZPN_BROKER_ASSERT_SOFT((NULL != rep.status), "auth report to dispatcher status field is NULL, dispatcher may not be handling it");
    rep.capabilities = asst->capability.capability_str;
    rep.capabilities_count = asst->capability.capabilities_count;
    rep.cpu = asst->auth_log.cpu_util;
    rep.mem = asst->auth_log.mem_util;
    rep.target = asst->auth_log.current_target_count;
    rep.mtunnel = asst->auth_log.total_mtunnel_count;
    rep.udp4_port_util = asst->udp4_port_util;
    rep.udp6_port_util = asst->udp6_port_util;
    rep.tcp4_port_util = asst->tcp4_port_util;
    rep.tcp6_port_util = asst->tcp6_port_util;
    rep.sys_fd_util = asst->sys_fd_util;
    rep.proc_fd_util = asst->proc_fd_util;
    rep.disabled = asst->disabled;

    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_broker_dispatch_send_auth_report_local(&rep);
    } else {
        res = zpn_broker_dispatch_send_auth_report_all(&rep);
    }

    if (res) {
        asst->tx_auth_report_failed++;
        ZPN_LOG(AL_WARNING, "Assistant(%lld) auth report to dispatchers failed", (long long)asst->assistant_gid_from_config);
    } else {
        asst->tx_auth_report_success++;
    }

    return res;
}

static void zpn_broker_assistant_update_qbr_fields(struct zpn_broker_mtunnel *mtunnel, struct zpn_broker_assistant_fohh_state *a_state)
{
    if (mtunnel->public_cloud) {
        ZPN_FREE(mtunnel->public_cloud);
        mtunnel->public_cloud = NULL;
    }

    if (mtunnel->private_cloud) {
        ZPN_FREE(mtunnel->private_cloud);
        mtunnel->private_cloud = NULL;
    }

    if (mtunnel->region_id) {
        ZPN_FREE(mtunnel->region_id);
        mtunnel->region_id = NULL;
    }

    if (mtunnel->a_cc) {
        ZPN_FREE(mtunnel->a_cc);
        mtunnel->a_cc = NULL;
    }

    if (mtunnel->a_city) {
        ZPN_FREE(mtunnel->a_city);
        mtunnel->a_city = NULL;
    }

    if (mtunnel->a_sc) {
        ZPN_FREE(mtunnel->a_sc);
        mtunnel->a_sc = NULL;
    }

    if (zpn_broker_assistant_is_qbr_insights_feature_enabled(ZPATH_GID_GET_CUSTOMER_GID(a_state->assitant_gid))) {
        if (a_state->public_cloud) {
            mtunnel->public_cloud = ZPN_STRDUP(a_state->public_cloud, strlen(a_state->public_cloud));
        }

        if (a_state->private_cloud) {
            mtunnel->private_cloud = ZPN_STRDUP(a_state->private_cloud, strlen(a_state->private_cloud));
        }

        if (a_state->region_id) {
            mtunnel->region_id = ZPN_STRDUP(a_state->region_id, strlen(a_state->region_id));
        }

        if (a_state->log.g_ast_country_code) {
            mtunnel->a_cc = ZPN_STRDUP(a_state->log.g_ast_country_code, strlen(a_state->log.g_ast_country_code));
        }

        if (a_state->log.g_ast_city) {
            mtunnel->a_city = ZPN_STRDUP(a_state->log.g_ast_city, strlen(a_state->log.g_ast_city));
        }

        if (a_state->log.g_ast_subdivision_code) {
            mtunnel->a_sc = ZPN_STRDUP(a_state->log.g_ast_subdivision_code, strlen(a_state->log.g_ast_subdivision_code));
        }
    }
}

/*
 * Brokers receive broker request acks on failure. But they can be an
 * intermediary for the message delivery, or it can be for them
 * directly.
 *
 * Note that control brokers & data brokers receive this message. So extra caution when trying to use the cookie as
 * it will be different object type in both the cases.
 *
 * Note that broker will retry sending zpn_broker_request message in some cases. So this zpn_broker_request_ack
 * may be corresponding to an older zpn_broker_request message. So we should check if ack's seq_num matches that of
 * mtunnel's sequence number, otherwise decide that it is stale and discard this message.
 *
 * Invalidate the path cache if this is user's broker and this user broker sent the BrkRq directly to the connector.
 * Exception is AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND case which indicates that connector could connect to the
 * application, but having trouble reaching the user's broker (so intermediatary problem). The side effect is that
 * we are preventing the dispatcher from choosing another connector. But with today's connector choosing algorithm,
 * a dispatcher have no way of knowing if a connector is having trouble reaching a user's broker, so there is no change
 * in behavior. It is important to not blow up the cache for AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND because a interim
 * congestion (which can happen a lot of times) can wash off the entire cache related to that connector. Note that
 * we are talking about congestion and not connection break - If there is a connection break(user's broker<->connector)
 * and if a new transaction comes in, the cache is washed off even before sending BrkRq message.
 */
static int zpn_broker_request_ack_cb(struct argo_object *object, struct zpn_broker_assistant_fohh_state *a_state)
{
    struct zpn_broker_request_ack *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int res;
    int bucket_id = -1;

    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "BrkRqAck",
                                     zpn_broker_request_ack_description,
                                     req);
    }

    if (req->g_brk != ZPN_BROKER_GET_GID()) {
        /* We are not the original requesting broker, forward to Dispatcher */
        ZPN_DEBUG_MTUNNEL("%s: Broker Req Ack originated from other broker %ld", req->mtunnel_id, (long)req->g_brk);

        /*
         * We don't care if this message is lost due to congestion. The orginating broker will
         * eventually time out the mtunnel
         */
        if (ZPN_BROKER_IS_PRIVATE()) {
            ZPN_BROKER_ASSERT_SOFT(0,
                    "Private broker is not supposed to forward zpn_broker_request_ack, g_brk=%ld", (long) req->g_brk);
        } else {
            zpn_broker_dispatch_send_request_ack(req);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "Expecting mtunnel_id or tag_id in mtunnel_end request");
        return ZPN_RESULT_ERR;
    }

    mtunnel_id_len = strlen(req->mtunnel_id);
    mtunnel_id_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
    mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (mtunnel) {
        struct zpn_tlv *tlv = zpn_broker_mtunnel_client_tlv(mtunnel);

#if 1
        /*
         * FIXME:Ramesh:
         * 1. We are testing req->seq_num to be zero to account for older dispatchers in the field which reset the
         * seq_num to zero. In that case, lets make it a loose check and pretend as if we don't care. Dispatcher code
         * fixed this around oct/2020, so if you see this code around oct/2021, please remove req->seq_num check as it
         * is expected to be non-zero.
         * 2. After the UT phase, add a soft assert to to help the broker cry out loud in case dev environment
         * dispatcher is misbehaving.
         */
        if (0 == req->seq_num) {
            ZPN_BROKER_ASSERT_SOFT(0, "%s: dispatcher(%lld) is sending broker request ack with seq_num=0, not expected",
                                   req->mtunnel_id, req->g_dsp);
        }
        mtunnel_lock(mtunnel);
        if (req->seq_num &&
            req->seq_num != mtunnel->brk_req_seq_num &&
            (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) == 0) {
            /* Re-dispatch to diff DC due to timeout case is an exception here - seq num can mismatch in that case. */
            ZPN_LOG(AL_NOTICE, "%s: discarding brk_req_ack, as seq num mismatch (%d vs %d)",
                    req->mtunnel_id, req->seq_num, mtunnel->brk_req_seq_num);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            goto done;
        }
#endif

        /* We are the original requesting broker, send mtunnel_req_ack back to client */
        ZPN_DEBUG_MTUNNEL("%s: Broker Req Ack for mtunnel, state = %s",
                          req->mtunnel_id, mtunnel_state(mtunnel->state));

        if (req->dsp_bypassed && req->error && (0 != strcmp(req->error, AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND))) {
            /*
             * put it back in auth state, so that the request is retried again. set the flag to not send the request
             * again to connector
             */
            mtunnel->flag.donot_send_directly_to_connector = 1;
            mtunnel->state = zbms_authenticated;
            zpn_broker_client_path_cache_invalidate_from_another_thread(mtunnel->tunnel_id, mtunnel->c_state_fohh_thread_id,
                        mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]), req->g_ast_grp,
                        mtunnel->req_app_name, mtunnel->req_app_type, mtunnel->client_port, mtunnel->ip_protocol,
                        mtunnel->flag.ast_grp_used_in_cache_key);
            mtunnel_locked_state_machine(mtunnel);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            goto done;
        }

        if (ZPN_BROKER_IS_PUBLIC() && mtunnel->flag.timeout_redispatch_to_diff_dc) {
            if (req->error && strcmp(req->error, AST_MT_SETUP_ERR_DUP_MT_ID) == 0) {
                /* We had sent zpn_broker_request to 2 dispatchers, so old AC can complain about the duplicate mtunnel id - ignore. */
                ZPN_DEBUG_MTUNNEL("%s: Received %s error from %"PRId64" AC, could be because we sent 2 brk req due to timeout and both landed on same AC - ignoring",
                                  mtunnel->mtunnel_id, req->error, req->g_ast);
                mtunnel_unlock(mtunnel);
                mtunnel_bucket_unlock(bucket_id);
                goto done;
            } else if (mtunnel->state > zbms_dispatch_sent) {
                /*
                 * We had re-dispatched to diff DC due to timeout and now the mtunnel is already complete,
                 * no need to process this error coming from AC now.
                 */
                ZPN_DEBUG_MTUNNEL("%s: Received %s error from AC but mtunnel is already complete - ignoring",
                                  mtunnel->mtunnel_id, req->error);
                mtunnel_unlock(mtunnel);
                mtunnel_bucket_unlock(bucket_id);
                goto done;
            }
        }

        mtunnel->assistant_id = req->g_ast;
        mtunnel->assistant_group_id = req->g_ast_grp;

        if (a_state) {
            zpn_broker_assistant_update_qbr_fields(mtunnel, a_state);
        }

        mtunnel->server_group_id = req->g_srv_grp;
        mtunnel->log.action = "close";
        mtunnel->log.a_ip = req->a_inet;
        mtunnel->log.a_port = req->a_port;
        mtunnel->fwd_broker_id = req->g_bfw;
        if (req->s_inet.length) {
            mtunnel->log.s_ip = req->s_inet;
            mtunnel->log.s_port = req->s_port;
        }
        mtunnel->log.bind_ast_tx_us = req->ast_bind_tx_us;
        mtunnel->log.brk_req_ack_ast_tx_us = req->ast_tx_us;
        mtunnel->log.brk_req_ack_cbrk_tx_us = req->bfw_tx_us;
        mtunnel->log.brk_req_ack_dsp_tx_us = req->dsp_tx_us;
        mtunnel->log.brk_req_ack_rx_us = epoch_us();
        mtunnel->log.server_us = req->server_us;
        mtunnel->path_decision |= req->path_decision;
        zpn_tx_path_decision_get_str(mtunnel->path_decision, mtunnel->log.path_decision,
                                     sizeof(mtunnel->log.path_decision));
        mtunnel->insp_status = req->insp_status;
        mtunnel->ssl_err = req->ssl_err;
        zpn_broker_mtunnel_stats_update_inspection_counter(mtunnel->insp_status);
        zpn_tx_insp_status_get_str(mtunnel->insp_status, mtunnel->ssl_err, mtunnel->log.insp_status_str);
        if (tlv) {
            res = mtunnel_request_ack(mtunnel,
                                      zpn_tlv_conn_incarnation(tlv),
                                      mtunnel->log.c_tag,
                                      mtunnel->mtunnel_id,
                                      req->error,
                                      NULL);
            if (res != FOHH_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "%s: mtunnel request ack error: tag_id: %d, error: %s, ret: %s",
                        mtunnel->mtunnel_id, mtunnel->log.c_tag, mtunnel->err ? mtunnel->err : "no error",
                        zpath_result_string(res));
            }
        } else {
            ZPN_DEBUG_MTUNNEL("%s: Received broker request ack, but no client state?", mtunnel->mtunnel_id);
        }

        ZPN_DEBUG_MTUNNEL("%s: Closing mtunnel from assistant side- still must send close to client.", mtunnel->mtunnel_id);

        mtunnel->log.g_dsp = req->g_dsp;
        mtunnel_locked_destroy(mtunnel, 0, 0, 1, req->error);
        mtunnel_locked_state_machine(mtunnel);
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    } else {
        /* mtunnel probably has been deleted already. Just ignore the ack */
    }

done:
    return ZPN_RESULT_NO_ERROR;
}


static int zpn_broker_request_ack_cb_from_control_conn(void *argo_cookie_ptr,
                                                       void *argo_structure_cookie_ptr,
                                                       struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_assistant *asst;
    pthread_mutex_lock(&asst_lock);
    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_broker_request_ack++;
    }
    pthread_mutex_unlock(&asst_lock);

    return zpn_broker_request_ack_cb(object, NULL);
}


static int zpn_broker_request_ack_cb_from_data_conn(void *argo_cookie_ptr,
                                                    void *argo_structure_cookie_ptr,
                                                    struct argo_object *object)
{
    return zpn_broker_request_ack_cb(object, argo_structure_cookie_ptr);
}


static void resend_dns_check(void *void_cookie, int64_t int_cookie)
{
    struct argo_object *copy = void_cookie;
    struct zpn_dns_assistant_check *check_in = copy->base_structure_void;

    ZPN_DEBUG_BROKER_DNS("Sending delayed errored client check from ast %ld, app %ld, name %s, type %s, error %s",
                         (long) check_in->g_ast,
                         (long) check_in->g_app,
                         check_in->name,
                         check_in->type,
                         check_in->error);

    check_in->cbrk_to_dsp_tx_us = zpn_cloud_adjusted_epoch_us();
    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_broker_dispatch_send_dns_reply_local(check_in);
    } else {
        if (zpn_broker_is_dns_assistant_check_unicast_enabled() && check_in->g_dsp) {
            zpn_broker_dispatch_send_dispatcher(check_in->g_dsp, copy);
        } else {
            zpn_broker_dispatch_send_pool(ZPATH_GID_GET_CUSTOMER_GID(check_in->g_app), copy);
        }
    }
    argo_object_release(copy);
}


static int64_t zpn_broker_assistant_get_dns_check_error_delay_us(int64_t assistant_gid, int64_t customer_gid)
{
    int64_t assistant_group_gid = 0;
    struct zpn_assistantgroup_assistant_relation *ag_relation = NULL;
    size_t count = 1;

    zpn_assistantgroup_assistant_relation_get_by_assistant(assistant_gid,
                                                           &ag_relation,
                                                           &count,
                                                           NULL,
                                                           NULL,
                                                           0);
    if (ag_relation) {
        assistant_group_gid = ag_relation->assistant_group_id;
    }

    return zpn_broker_get_dns_check_response_delay_us(assistant_gid, assistant_group_gid, customer_gid);
}

static int zpn_dns_assistant_check_from_assistant_cb(void *argo_cookie_ptr,
                                                     void *argo_structure_cookie_ptr,
                                                     struct argo_object *object)
{
    struct zpn_dns_assistant_check *check_in = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct argo_object *copy;
    char dump[8000];
    int64_t g_cst;
    int res;
    struct connected_assistant *asst;
    int64_t dns_error_delay_us;
    int i;
    int ipv4_addr_present = 0;
    int ipv6_addr_present = 0;

    pthread_mutex_lock(&asst_lock);
    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_dns_assistant_check++;
    }
    pthread_mutex_unlock(&asst_lock);

    if ((!check_in->g_ast) ||
        (!check_in->g_app) ||
        (!check_in->name) ||
        (!check_in->type)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_WARNING, "%s: Rx incomplete zpn_dns_assistant_check: %s", fohh_peer_cn(f_conn), dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    /* Verify the requested app/assistant are consistent */
    g_cst = ZPATH_GID_GET_CUSTOMER_GID(check_in->g_ast);
    if (g_cst != ZPATH_GID_GET_CUSTOMER_GID(check_in->g_app)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "%s: Rx mismatch g_ast/g_app: %s", fohh_peer_cn(f_conn), dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    /* Log it first, then nuke the returned IPs (first get any necessary meta info) so that
     * they don't get send all over the place. We also note the customer (then clear it) so
     * that it doesn't get sent to dispatcher (bandwidth savings) */
    check_in->g_cst = ZPATH_GID_GET_CUSTOMER_GID(check_in->g_ast);

    /* Grab presence of IPv4 and IPv6 address in list for use in strict DNS checking */
    for (i = 0; i < check_in->ips_count; i++) {
        if (check_in->ips[i].length == 4)
            ipv4_addr_present++;
        else if (check_in->ips[i].length == 16)
            ipv6_addr_present++;
    }

    if (ipv4_addr_present) check_in->has_a = 1;
    if (ipv6_addr_present) check_in->has_aaaa = 1;

    res = zpn_broker_dns_log_assistant(check_in, "dns_assistant");
    if (res) {
        ZPN_LOG(AL_WARNING, "Could not log assistant dns check");
    }

    check_in->ips_count = 0;
    check_in->g_cst = 0;

    copy = argo_object_copy(object);
    if (!copy) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "%s: Rx, but cannot copy object: %s", fohh_peer_cn(f_conn), dump);
        }
        return ZPN_RESULT_NO_ERROR;
    } else {
        if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)){
            if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
            }
        }
    }


    dns_error_delay_us = zpn_broker_assistant_get_dns_check_error_delay_us(check_in->g_ast, ZPATH_GID_GET_CUSTOMER_GID(check_in->g_ast));

    // TODO: We should probably do better than this.
    if (check_in->error) {
        ZPN_DEBUG_BROKER_DNS("Delaying errored client check from ast %ld, app %ld, name %s, type %s, error %s",
                             (long) check_in->g_ast,
                             (long) check_in->g_app,
                             check_in->name,
                             check_in->type,
                             check_in->error);
        if (ztimer_wheel_add(timer_wheel,
                             dns_error_delay_us,
                             resend_dns_check,
                             copy,
                             0)) {
            ZPN_LOG(AL_ERROR, "Could not set timer for delaying errored client check from ast %ld, app %ld, name %s, type %s, error %s",
                    (long) check_in->g_ast,
                    (long) check_in->g_app,
                    check_in->name,
                    check_in->type,
                    check_in->error);
            argo_object_release(copy);
        }
    } else {
        if (ZPN_BROKER_IS_PRIVATE()) {
            zpn_broker_dispatch_send_dns_reply_local(copy->base_structure_void);
        } else {
            if (zpn_broker_is_dns_assistant_check_unicast_enabled() && check_in->g_dsp) {
                /* Send to the dispatcher that requested it... */
                zpn_broker_dispatch_send_dispatcher(check_in->g_dsp, copy);
            } else {
                /* Send to all dispatchers... */
                zpn_broker_dispatch_send_pool(ZPATH_GID_GET_CUSTOMER_GID(check_in->g_app), copy);
            }
        }
        argo_object_release(copy);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Control connection interface (old connectors, before drop8)
 * FIXME: remove this interface after all connectors have upgraded to 21.147.x
 */
static int zpn_broker_assistant_control_log_upload_cb(void *argo_cookie_ptr,
                                                      void *argo_structure_cookie_ptr,
                                                      struct argo_object *object)
{
    struct fohh_connection*     f_conn = argo_structure_cookie_ptr;
    struct connected_assistant* asst;

    pthread_mutex_lock(&asst_lock);
    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (!asst) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL");
        return zpn_broker_assistant_stats_upload(f_conn, object, 0, NULL, 0, 0, 0, 0);
    }
    asst->rx_log_stats_upload++;
    pthread_mutex_unlock(&asst_lock);
    return zpn_broker_assistant_stats_upload(f_conn,
                                             object,
                                             asst->customer_gid,
                                             asst->tunnel_id,
                                             asst->assistant_gid_from_config,
                                             asst->g_ast_grp,
                                             0,
                                             0);
}


static int zpn_broker_assistant_log_control_cb(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_assistant_log_control *req = object->base_structure_void;
    struct connected_assistant *asst;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);

    if (asst) {
        asst->rx_log_control++;
        asst->log_upload = req->upload;
        asst->debug_flag = req->flag;
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_rx_enc_pvt_key_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_assistant_pvt_key_control *req = object->base_structure_void;

    if ((NULL == argo_structure_cookie_ptr) || (NULL == req) || (0 == req->cert_id) ||
        (NULL == req->enc_or_dec_pvt_key)) {
        ZPN_LOG(AL_ERROR, "Broker revd pvt key decryption req with bad parameters");
        return ZPN_RESULT_ERR;
    }
    struct waf_cert_domain *waf_cert_domain = ZPN_CALLOC(sizeof(*waf_cert_domain));
    if (NULL == waf_cert_domain) {
        ZPN_LOG(AL_ERROR, "Broker failed mem alloc for pvt key req for cert %ld", (long)req->cert_id);
        return ZPN_RESULT_ERR;
    }
    pthread_mutex_lock(&(asst_lock));
    struct connected_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_waf_cert_prv_key_req++;
    }
    pthread_mutex_unlock(&(asst_lock));
    waf_cert_domain->cert_id = req->cert_id;
    waf_cert_domain->f_conn = argo_structure_cookie_ptr;
    snprintf(waf_cert_domain->enc_pvt_key, KEY_BUFF_SIZE, "%s", req->enc_or_dec_pvt_key);

    zpn_broker_cryptosvc_get_decrypted_key(waf_cert_domain);
    return ZPN_RESULT_NO_ERROR;
}

extern int g_auto_cert_gen_rcvd;

static int zpn_broker_assistant_rx_gen_cert_cb(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    const struct zpn_assistant_gen_cert_control *req = object->base_structure_void;

    if (!g_auto_cert_gen_rcvd) {
        /* This is the first Cert Gen Req from connector to this Control Broker
         * Initialize the fohh connection from Control Broker to Cert Gen Service timer routine */
        ZPN_LOG(AL_NOTICE, "[AUTO_CERT] Broker received first generate cert req");
        g_auto_cert_gen_rcvd = 1;
    }


    if ((NULL == argo_structure_cookie_ptr) || (NULL == req)) {
        ZPN_LOG(AL_ERROR, "[AUTO_CERT] Broker received generate cert req with invalid data argo=%s req=%s",
                (argo_structure_cookie_ptr)?"valid":"Null", (req)?"valid":"null");
        return ZPN_RESULT_ERR;
    }

    if ((0 == req->app_gid) || (NULL == req->cn)) {
        ZPN_LOG(AL_ERROR, "[AUTO_CERT] Broker received generate cert req with bad parameters app_id=%ld cn=%s",
                (long)req->app_gid, (req->cn)?req->cn:"null");
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_LOGGING("[AUTO_CERT] Received #### Connector cert gen request app_id %"PRId64"", (int64_t)req->app_gid);

    struct waf_cert_gen_req *waf_cert_req = ZPN_CALLOC(sizeof(*waf_cert_req));
    if (NULL == waf_cert_req) {
        ZPN_LOG(AL_ERROR, "[AUTO_CERT] Broker failed mem alloc for certificate gen req for app_id %ld", (long)req->app_gid);
        return ZPN_RESULT_ERR;
    }
    pthread_mutex_lock(&asst_lock);
    struct connected_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_waf_cert_gen_req++;
    }
    pthread_mutex_unlock(&asst_lock);
    waf_cert_req->app_gid = req->app_gid;
    waf_cert_req->f_conn = argo_structure_cookie_ptr;
    waf_cert_req->cust_gid = req->cust_gid;
    snprintf(waf_cert_req->cn, 256, "%s", req->cn);

    zpn_broker_cryptosvc_get_cert_n_key(waf_cert_req);
    return ZPN_RESULT_NO_ERROR;
}


static int zpn_broker_assistant_stats_control_cb(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_assistant_stats_control *req = object->base_structure_void;
    struct connected_assistant *asst;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);

    if (asst) {
        asst->rx_stats_control++;
        asst->stats_upload = req->upload;
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_status_report_cb(void *argo_cookie_ptr,
                                                 void *argo_structure_cookie_ptr,
                                                 struct argo_object *object)
{
    int send_first_auth_report = 0;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_assistant_status_report *req = object->base_structure_void;
    struct connected_assistant *asst;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_status_report++;
        asst->auth_log.cpu_util = req->cpu_util;
        asst->auth_log.mem_util = req->mem_util;
        asst->auth_log.service_count = req->service_count;
        asst->proc_fd_util = req->proc_fd_util;
        asst->sys_fd_util = req->sys_fd_util;
        asst->udp4_port_util = req->udp4_port_util;
        asst->udp6_port_util = req->udp6_port_util;
        asst->tcp4_port_util = req->tcp4_port_util;
        asst->tcp6_port_util = req->tcp6_port_util;

        asst->delta_from_prev_auth_log.delta_mtunnel_count += req->delta_mtunnel_count;
        asst->auth_log.current_target_count = req->current_target_count;
        asst->auth_log.total_mtunnel_count = req->total_mtunnel_count;
        asst->auth_log.last_os_upgrade_time = req->last_os_upgrade_time;
        asst->auth_log.last_sarge_upgrade_time = req->last_sarge_upgrade_time;

        if (asst->auth_log.dft_rt_intf) {
            ZPN_FREE(asst->auth_log.dft_rt_intf);
        }
        if (req->dft_rt_intf) {
            asst->auth_log.dft_rt_intf = ZPN_STRDUP(req->dft_rt_intf, strlen(req->dft_rt_intf));
        } else {
            asst->auth_log.dft_rt_intf = NULL;
        }
        asst->auth_log.dft_rt_gw = req->dft_rt_gw;
        asst->auth_log.resolver = req->resolver;
        asst->auth_log.sys_uptime_s = req->sys_uptime_s;
        asst->auth_log.ast_uptime_s = req->ast_uptime_s;
        asst->auth_log.intf_count = req->intf_count;
        asst->auth_log.intf_rb = req->intf_rb;
        asst->auth_log.intf_rp = req->intf_rp;
        asst->auth_log.intf_re = req->intf_re;
        asst->auth_log.intf_rd = req->intf_rd;
        asst->auth_log.intf_tb = req->intf_tb;
        asst->auth_log.intf_tp = req->intf_tp;
        asst->auth_log.intf_te = req->intf_te;
        asst->auth_log.intf_td = req->intf_td;

        asst->auth_log.delta_intf_rb += req->intf_rb;
        asst->auth_log.delta_intf_rp += req->intf_rp;
        asst->auth_log.delta_intf_re += req->intf_re;
        asst->auth_log.delta_intf_rd += req->intf_rd;
        asst->auth_log.delta_intf_tb += req->intf_tb;
        asst->auth_log.delta_intf_tp += req->intf_tp;
        asst->auth_log.delta_intf_te += req->intf_te;
        asst->auth_log.delta_intf_td += req->intf_td;

        asst->auth_log.total_intf_b = req->total_intf_b;
        asst->auth_log.total_intf_p = req->total_intf_p;
        asst->auth_log.total_intf_e = req->total_intf_e;
        asst->auth_log.total_intf_d = req->total_intf_d;
        asst->auth_log.delta_total_intf_b += req->delta_total_intf_b;
        asst->auth_log.delta_total_intf_p += req->delta_total_intf_p;
        asst->auth_log.delta_total_intf_e += req->delta_total_intf_e;
        asst->auth_log.delta_total_intf_d += req->delta_total_intf_d;
        zpn_broker_assistant_update_asst_lat_lon(asst);

        if (req->platform) {
            if (asst->platform) ZPN_FREE(asst->platform);
            asst->platform = ZPN_STRDUP(req->platform, strlen(req->platform));
        }

        if (req->platform_detail) {
            if (asst->platform_detail) ZPN_FREE(asst->platform_detail);
            asst->platform_detail = ZPN_STRDUP(req->platform_detail, strlen(req->platform_detail));
        }

        if (req->runtime_os) {
            if (asst->runtime_os) ZPN_FREE(asst->runtime_os);
            asst->runtime_os = ZPN_STRDUP(req->runtime_os, strnlen(req->runtime_os, FOHH_MAX_NAMELEN));
        }
        if (req->platform_arch) {
            if (asst->platform_arch) ZPN_FREE(asst->platform_arch);
            asst->platform_arch = ZPN_STRDUP(req->platform_arch, strlen(req->platform_arch));
        }
        if (req->platform_version) {
            if (asst->platform_version) ZPN_FREE(asst->platform_version);
            asst->platform_version = ZPN_STRDUP(req->platform_version, strlen(req->platform_version));
        }

        if (req->frr_version) {
            if (asst->frr_version) ZPN_FREE(asst->frr_version);
            asst->frr_version = ZPN_STRDUP(req->frr_version, strlen(req->frr_version));
        }

        asst->auth_log.platform = asst->platform;
        asst->auth_log.platform_detail = asst->platform_detail;
        asst->auth_log.runtime_os = asst->runtime_os;
        asst->auth_log.platform_arch = asst->platform_arch;
        asst->auth_log.platform_version = asst->platform_version;

        if (req->connector_type == np_connector) {
            asst->connector_type = np_connector;
            asst->auth_log.connector_type = np_connector;
            asst->auth_log.np_connector_gid = req->np_connector_gid;
            asst->auth_log.np_connector_gid_str = req->np_connector_gid;
            asst->auth_log.np_connector_state = req->np_state;
            asst->auth_log.frr_version = asst->frr_version;
        }

        snprintf(asst->auth_log.log_broker_cn, sizeof(asst->auth_log.log_broker_cn), "%s", req->log_broker_cn);
        snprintf(asst->auth_log.ovd_broker_cn, sizeof(asst->auth_log.ovd_broker_cn), "%s", req->ovd_broker_cn);
        asst->status_report_ready = 1;

        /* Send the first auth log only when we have all the required info */
        if (!asst->first_auth_sent && asst->status_report_ready && asst->tcp_info_ready) {
            zpn_broker_assistant_control_conn_auth_log_send(asst, fohh_connection_connected);
            if (!g_send_ast_auth_report_every_1_min) {
                /*
                 * Not sending auth_report to dispatcher from here because coverity didn't like waiting while holding a lock
                 */
                send_first_auth_report = 1;
            }
            if (asst->connector_type == np_connector) {
                asst->last_np_state_seen = asst->auth_log.np_connector_state;
            }
        } else if (ZPN_BROKER_IS_PUBLIC() &&
                   (asst->first_auth_sent) &&
                   (asst->connector_type == np_connector) &&
                   (asst->auth_log.np_connector_state != asst->last_np_state_seen)) {

            zpn_broker_assistant_control_conn_auth_log_send(asst, fohh_connection_connected);
            ZPN_DEBUG_ASSISTANT("NP connector %"PRId64" state change from %d to %d ", asst->auth_log.np_connector_gid, asst->last_np_state_seen, asst->auth_log.np_connector_state);
            asst->last_np_state_seen = asst->auth_log.np_connector_state;
        }
    }

    pthread_mutex_unlock(&(asst_lock));

    /*
     * Calling this function outside the lock as it might sleep
     * Also, send ast_auth_report to dispatcher every 1 min(which is arrival of
     * every status_report from assistant) if config override value is enabled or
     * if this is the first auth_report being sent
     */
    if ((send_first_auth_report || g_send_ast_auth_report_every_1_min) && asst && asst->status_report_ready && asst->tcp_info_ready) {
        zpn_broker_assistant_auth_report_to_dispatcher(asst);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_control_state_cb(void *argo_cookie_ptr,
                                                 void *argo_structure_cookie_ptr,
                                                 struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_asst_state *req = object->base_structure_void;
    struct connected_assistant *asst;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_state++;
        asst->disabled = req->disabled;
        zpn_broker_assistant_store_capabilities(&asst->capability, req->capabilities, req->capabilities_count);

        int is_qbr_insights_feature_enabled = zpn_broker_assistant_is_qbr_insights_feature_enabled(asst->customer_gid);

        if (req->public_cloud && is_qbr_insights_feature_enabled) {
            if (!asst->public_cloud || strcmp(asst->public_cloud, req->public_cloud)) {
                if (asst->public_cloud) {
                    ZPN_FREE(asst->public_cloud);
                }
                asst->public_cloud = ZPN_STRDUP(req->public_cloud, strlen(req->public_cloud));
                asst->auth_log.g_ast_public_cloud = asst->public_cloud;
            }
        } else {
            if (asst->public_cloud) {
                ZPN_FREE(asst->public_cloud);
            }
            asst->auth_log.g_ast_public_cloud = asst->public_cloud = NULL;
        }

        if (req->private_cloud && is_qbr_insights_feature_enabled) {
            if (!asst->private_cloud || strcmp(asst->private_cloud, req->private_cloud)) {
                if (asst->private_cloud) {
                    ZPN_FREE(asst->private_cloud);
                }
                asst->private_cloud = ZPN_STRDUP(req->private_cloud, strlen(req->private_cloud));
                asst->auth_log.g_ast_private_cloud = asst->private_cloud;
            }
        } else {
            if (asst->private_cloud) {
                ZPN_FREE(asst->private_cloud);
            }
            asst->auth_log.g_ast_private_cloud = asst->private_cloud = NULL;
        }

        if (req->region_id && is_qbr_insights_feature_enabled) {
            if (!asst->region_id || strcmp(asst->region_id, req->region_id)) {
                if (asst->region_id) {
                    ZPN_FREE(asst->region_id);
                }
                asst->region_id = ZPN_STRDUP(req->region_id, strlen(req->region_id));
                asst->auth_log.g_ast_region_id = asst->region_id;
            }
        } else {
            if (asst->region_id) {
                ZPN_FREE(asst->region_id);
            }
            asst->auth_log.g_ast_region_id = asst->region_id = NULL;
        }
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_restart_reason_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_asst_restart_reason *stat = object->base_structure_void;
    struct connected_assistant *asst;

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    pthread_mutex_lock(&(asst_lock));
    asst = zpn_broker_find_connected_active_assistant(stat->assistant_id);
    if (asst) {
        asst->auth_log.report_restart_epoch_s = stat->report_epoch_s;
        snprintf(asst->auth_log.restart_reason, sizeof(asst->auth_log.restart_reason), "%s", stat->reason);
    }

    pthread_mutex_unlock(&(asst_lock));
    return ZPN_RESULT_NO_ERROR;
}

int zpn_send_a2pb_authenticate_ack(struct fohh_connection *f_conn, int auth_status, char *message)
{
    struct zpn_a2pb_authentication_ack data;
    data.auth_status = auth_status;
    data.message = message;
    return fohh_argo_serialize(f_conn, zpn_a2pb_authentication_ack_description, &data, 0, fohh_queue_element_type_mission_critical);
}

#define FORMAT_SCOPE_AUTH_LOG_MESSAGE(msg, msg_len_remaining, str) \
    do { \
        int ret = snprintf(msg, msg_len_remaining, str); \
        if(ret) { \
            msg_len_remaining -= ret; \
        } \
    } while(0);

static int
zpn_a2pb_scope_auth(struct connected_assistant *asst, char *msg, int msg_max_len)
{
    int res = ZPN_RESULT_NO_ERROR;
    int scope_auth_status       = 0;
    int pbroker_scope_enabled   = 0;
    int assistant_scope_enabled = 0;
    int is_default_pBroker      = is_scope_default(g_broker_common_cfg->private_broker.scope_id);
    int is_default_connector    = is_scope_default(asst->scope_gid);
    int is_dta_enabled          = zpn_get_delegated_admin_status(asst->customer_gid);
    int is_matching_scope       = (asst->scope_gid == g_broker_common_cfg->private_broker.scope_id);

    char log[1024];
    int  log_len = 1024;

    if(!is_dta_enabled) {
        /*
         * If DTA is not enabled, we should not check for status of any scope.
         * Just simply check if both of the scopes belong to default scope. If so,
         * mark the auth as successful. Else fail the auth.
         */

        if(is_default_pBroker && is_default_connector) {
            scope_auth_status = 1;
        } else {
            FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "DTA feature is disabled, setting a2pb auth as failed");
        }
    } else {

        /* Check status of pbroker scope state, if it does not belong to default scope */
        if(!is_default_pBroker) {
            res = zpn_is_scope_enabled(g_broker_common_cfg->private_broker.scope_id, &pbroker_scope_enabled);
            if(res != ZPN_RESULT_NO_ERROR) {
                FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "Failed to fetch scope status for pbroker scope during a2pb authentication");
            } else if(!pbroker_scope_enabled) {
                FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "PBroker scope is disabled state during a2pb authentication");
                res = ZPN_RESULT_ERR;
            }
        }

        /* Check status of assistant scope state, if it does not belong to default scope */
        if(!is_default_connector && res == ZPN_RESULT_NO_ERROR) {
            res = zpn_is_scope_enabled(asst->scope_gid, &assistant_scope_enabled);
            if(res != ZPN_RESULT_NO_ERROR) {
                FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "Failed to fetch scope status for assistant scope during a2pb authentication");
            } else if(!assistant_scope_enabled) {
                FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "Assistant scope is disabled state during a2pb authentication");
                res = ZPN_RESULT_ERR;
            }
        }

        if(res == ZPN_RESULT_NO_ERROR) {
            /* We were successfully able to fetch the scopes of connector and pbroker and they are in enabled state */
            if (is_default_pBroker || is_default_connector ||  is_matching_scope) {
                scope_auth_status = 1;
            } else {
                FORMAT_SCOPE_AUTH_LOG_MESSAGE(log, log_len, "Assistant and pbroker scopes do not match, failing a2pb scope auth");
            }
        }
    }

    if(scope_auth_status) {
        ZPN_LOG(AL_DEBUG, "Scope check successful in a2pb authentication. dta_enabled(%d) "
                          "pbroker(gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d) "
                          "assistant(gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d)",
                          is_dta_enabled,
                          g_broker_common_cfg->private_broker.broker_id,
                          g_broker_common_cfg->private_broker.scope_id,
                          pbroker_scope_enabled,
                          asst->assistant_gid_from_config,
                          asst->scope_gid,
                          assistant_scope_enabled);
        strncpy(msg, "Authentication successfull, DR mode matches and scope criteria matches!", msg_max_len);
    } else {
        ZPN_LOG(AL_DEBUG, "Scope check failed during a2pb autentication. Error(%s) dta_enabled(%d) "
                          "pbroker(gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d) "
                          "assistant(gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d)",
                          log,
                          is_dta_enabled,
                          g_broker_common_cfg->private_broker.broker_id,
                          g_broker_common_cfg->private_broker.scope_id,
                          pbroker_scope_enabled,
                          asst->assistant_gid_from_config,
                          asst->scope_gid,
                          assistant_scope_enabled);
        strncpy(msg, "Authentication successfull, DR mode matches but scope criteria does not match!", msg_max_len);
    }

    if(scope_auth_status) {
        asst->scope_auth_status = AUTH_SUCCESSFUL;
    } else {
        asst->scope_auth_status = AUTH_UNSUCCESSFUL;
    }

    return scope_auth_status;
}

/*
 * Expected only in pbroker environment. A connector will authenticate
 * itself with pbroker first
 */
static int zpn_a2pb_authentication_cb(void*                 argo_cookie_ptr,
                                      void*                 argo_structure_cookie_ptr,
                                      struct argo_object*   object)
{
    struct zpn_a2pb_authentication*             auth;
    struct fohh_connection*                     f_conn;
    struct connected_assistant*                 asst;
    int64_t                                     asst_gid;
    char                                        message[A2PB_AUTH_MESSAGE_LEN];
    int                                         auth_status = 0;
    struct zpn_private_broker_global_state*     gs;

    auth = object->base_structure_void;
    f_conn = argo_structure_cookie_ptr;
    gs = zpn_get_private_broker_global_state();

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (NULL == asst) {
        ZPN_LOG(AL_DEBUG, "Could not get connected assistant info, connection: %s", fohh_description(f_conn));
        strcpy(message, "Assistant info not found");
        goto done;
    }

    asst->dr_auth_status = AUTH_UNSUCCESSFUL;

    /*
     * Set default value for scope auth, before commencing scope criteria checks.
     * When DR mode is active, DTA checks are not enforced, and so the auth status should be set to 'Successful'
     */
    if(!zpn_is_dr_mode_active()) {
        asst->scope_auth_status = AUTH_UNSUCCESSFUL;
    } else {
        asst->scope_auth_status = AUTH_SUCCESSFUL;
    }

    asst_gid = fohh_peer_get_id(f_conn);
    if (!asst_gid) {
        ZPN_LOG(AL_DEBUG, "Could not get connected assistant gid, connection: %s", fohh_description(f_conn));
        strcpy(message, "Assistant gid not found");
        goto done;
    }

    if (!strcmp(auth->dr_running_mode, "on") ||
        !strcmp(auth->dr_running_mode, "off") ||
        !strcmp(auth->dr_running_mode, "test")) {
        snprintf(asst->dr_running_mode, sizeof(asst->dr_running_mode), "%s", auth->dr_running_mode);

        if (!strcmp(gs->private_broker_state->dr_running_mode, asst->dr_running_mode)) {
            ZPN_LOG(AL_INFO, "DR mode match, connector's DR mode: %s, Pbroker's DR mode: %s, connection: %s",
                asst->dr_running_mode, gs->private_broker_state->dr_running_mode, fohh_description(f_conn));

            asst->dr_auth_status = AUTH_SUCCESSFUL;
            auth_status = 1;

            if(!zpn_is_dr_mode_active()) {
                auth_status = zpn_a2pb_scope_auth(asst, message, A2PB_AUTH_MESSAGE_LEN);
                goto done;
            } else {
                strcpy(message, "Authentication successfull, DR mode matches !");
                goto done;
            }
        } else {
            ZPN_LOG(AL_WARNING, "DR mode mismatched, connector's DR mode: %s, Pbroker's DR mode: %s, connection: %s",
                                asst->dr_running_mode, gs->private_broker_state->dr_running_mode, fohh_description(f_conn));
            strcpy(message, "Authentication unsuccessfull, DR mode mismatched !");
            goto done;
        }
    } else {
        ZPN_LOG(AL_ERROR, "DR mode for assistant with gid(%"PRId64") is unknown", asst_gid);
        strcpy(message, "Unknown DR mode");
    }

done:
    zpn_send_a2pb_authenticate_ack(f_conn, auth_status, message);
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Expected only in pbroker environment. A connector will have multiple control connections to a pbroker. Only one will
 * be active connection, others will just be standby to take over the communication if the active is not healthy. This
 * message is which flips the switch of a connection to active connection. When the message hits the pbroker, we don't
 * expect any of the other connection to be active. The previous active would be torn down. But just in case there
 * exists an active it will be removed (thinking of a case where connector sees a active connection is down, but pbroker
 * thought that the active is still alive - just respect the connector's decision and move on).
 */
static int zpn_broker_assistant_active_connection_cb(void*                 argo_cookie_ptr,
                                                     void*                 argo_structure_cookie_ptr,
                                                     struct argo_object*   object)
{
    struct fohh_connection*                     f_conn;
    struct connected_assistant*                 asst;
    struct connected_assistant*                 asst_old;
    int64_t                                     asst_gid;

    f_conn = argo_structure_cookie_ptr;

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (NULL == asst) {
        goto done;
    }
    asst_gid = fohh_peer_get_id(f_conn);
    if (!asst_gid) {
        goto done;
    }

    asst_old = zpn_broker_find_connected_active_assistant(asst_gid);
    if (asst_old) {
        ZPN_LOG(AL_NOTICE, "Assistant control connection to gid(%"PRId64") is switching from (%s:%s) to (%s:%s)",
                asst_gid, fohh_description(asst_old->f_conn), fohh_state(asst_old->f_conn),
                fohh_description(asst->f_conn), fohh_state(asst->f_conn));
        if (ZPN_BROKER_IS_PRIVATE() && asst_old->active_control_connection_currently) {
            // Update assistant control connection stats for old connection.
            zpn_pbroker_ast_control_connection_stats_update(asst_old->f_conn, 0);
        }
        zpn_broker_remove_connected_active_assistant(asst_old, 0);
    } else {
        ZPN_LOG(AL_NOTICE, "Assistant control connection to gid(%"PRId64") is switching to (%s:%s)",
                asst_gid, fohh_description(asst->f_conn), fohh_state(asst->f_conn));
    }

    asst->rx_active_connection++;
    zpn_broker_add_connected_active_assistant(asst_gid, asst, 0);

    // Assistant active control connection is up. Update assistant control connection stats.
    if (ZPN_BROKER_IS_PRIVATE()) zpn_pbroker_ast_control_connection_stats_update(f_conn, 1);

done:
    return ZPN_RESULT_NO_ERROR;
}


static void
zpn_broker_assistant_control_conn_auth_log_send(struct connected_assistant *asst,
                                                enum fohh_connection_state state)
{
    int res;

    if (NULL == asst) return;

    (asst->first_auth_sent) ? (asst->auth_log.first_log = 0) : (asst->auth_log.first_log = 1);

    asst->auth_log.delta_mtunnel_count = asst->delta_from_prev_auth_log.delta_mtunnel_count;
    bzero(&asst->delta_from_prev_auth_log, sizeof(asst->delta_from_prev_auth_log));

    res = zpn_broker_assistant_auth_log(asst->f_conn,
                                        &(asst->auth_log),
                                        asst->assistant_gid_from_config,
                                        ZPN_BROKER_IS_PRIVATE() ? ZPN_ASSISTANT_PBROKER_CONTROL : ZPN_ASSISTANT_BROKER_CONTROL,
                                        state);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Could not generate control auth log", fohh_peer_cn(asst->f_conn));
    } else {
        asst->first_auth_sent = 1;
    }

    /* Resetting all the deltas in assistant interface stats */
    asst->auth_log.delta_intf_rb = 0;
    asst->auth_log.delta_intf_rp = 0;
    asst->auth_log.delta_intf_re = 0;
    asst->auth_log.delta_intf_rd = 0;
    asst->auth_log.delta_intf_tb = 0;
    asst->auth_log.delta_intf_tp = 0;
    asst->auth_log.delta_intf_te = 0;
    asst->auth_log.delta_intf_td = 0;
    asst->auth_log.delta_total_intf_b = 0;
    asst->auth_log.delta_total_intf_p = 0;
    asst->auth_log.delta_total_intf_e = 0;
    asst->auth_log.delta_total_intf_d = 0;
}

/*
 * Check if config override for broker to send assistant control connection disconnect event to
 * zpn_event topic is enabled.
 * Check level: Assistant -> Assistant Group -> Customer -> Broker -> Root Customer -> Global(1)
 * By default enabled.
 */
int zpn_broker_assistant_is_send_ast_disconnect_event_enabled(int64_t assistant_gid,
                                                              int64_t assistant_group_gid,
                                                              int64_t customer_gid)
{
    int64_t config_value = 1;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_int(CONFIG_SEND_AST_DISCONNECT_EVENT,
                                                        &config_value,
                                                        CONFIG_SEND_AST_DISCONNECT_EVENT_DEFAULT,
                                                        assistant_gid,
                                                        assistant_group_gid,
                                                        customer_gid,
                                                        zpath_instance_global_state.current_config->gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value ? 1 : 0;
}

void zpn_broker_assistant_control_destroy_with_asst_lock(struct connected_assistant *asst)
{
    struct zpn_health_report rep;
    struct zpn_event_system_control_connection_disconnect event_data;
    struct zpn_event_broker_stats *zpn_event_broker_stats_data;
    int res;

    if (!asst) return;

    memset(&rep, 0, sizeof(rep));

    struct fohh_connection *f_conn = asst->f_conn;
    if (f_conn) {
        ZPN_LOG(AL_NOTICE, "%s: Assistant control connection DOWN, Assistant ID = %"PRId64,
                fohh_description(f_conn), asst->assistant_gid_from_config);

        /* Fill up event data for assistant control connection disconnect and send it to zpn_event topic. */
        if (ZPN_BROKER_IS_PUBLIC() &&
            zpn_broker_assistant_is_send_ast_disconnect_event_enabled(asst->assistant_gid_from_config, asst->g_ast_grp, asst->customer_gid) &&
            !zpn_broker_assistant_get_disconnect_assistants_flag() &&
            !zpn_broker_assistant_get_redirect_assistants_flag() &&
            !f_conn->redirect_list_sent_to_client) {
            /* Don't send event if broker is disconnecting/redirecting assistants because broker is going
             * to shut down or if redirect list is sent to assistant to connect to some other broker. In
             * this case, assistant can connect to some other broker and customer doesn't need to get a
             * notification for this. */
            event_data.system_id = asst->assistant_gid_from_config;
            event_data.system_group_id = asst->g_ast_grp;
            event_data.g_cst = asst->customer_gid;
            event_data.g_microtenant = asst->auth_log.g_microtenant;
            event_data.message = "App connector control connection with broker got disconnected";
            res = zpn_event_send(asst->customer_gid,
                                 zpn_event_priority_low,
                                 zpn_event_disposition_bad,
                                 zpn_event_log_name_system_control_connection_disconnected,
                                 zpn_event_system_assistant,
                                 zpn_event_category_connectivity_and_upgrade,
                                 zpn_event_system_control_connection_disconnect_desc,
                                 &event_data);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not send event data for assistant %ld control connection disconnect",
                        (long) asst->assistant_gid_from_config);
            } else {
                zpn_event_broker_stats_data = zpn_event_get_broker_stats_data_obj();
                __sync_add_and_fetch_8(&(zpn_event_broker_stats_data->num_event_ast_control_connection_disconnect), 1);
            }
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Stale assistant control connection DOWN, Assistant ID = %"PRId64,
                asst->assistant_gid_from_config);
    }

    if (asst->timer) {
        event_free(asst->timer);
        asst->timer = NULL;
    }

    zpn_broker_assistant_control_conn_auth_log_send(asst, fohh_connection_disconnected);
    zpn_broker_assistant_auth_report_to_dispatcher(asst);

    rep.g_ast = asst->assistant_gid_from_config;
    res = zpn_broker_assistant_control_remove_connected_assistant(asst);
    if (res) {
        ZPN_LOG(AL_WARNING, "Cannot remove assistant ID state?");
    }
    if (asst->version) ZPN_FREE(asst->version);
    if (asst->sarge_version) ZPN_FREE(asst->sarge_version);
    if (asst->public_cloud) {
        ZPN_FREE(asst->public_cloud);
        asst->public_cloud = NULL;
    }
    if (asst->private_cloud) {
        ZPN_FREE(asst->private_cloud);
        asst->private_cloud = NULL;
    }
    if (asst->region_id) {
        ZPN_FREE(asst->region_id);
        asst->region_id = NULL;
    }
    if (asst->auth_log.g_ast_country_code) {
        ZPN_FREE(asst->auth_log.g_ast_country_code);
        asst->auth_log.g_ast_country_code = NULL;
    }
    if (asst->auth_log.g_ast_city) {
        ZPN_FREE(asst->auth_log.g_ast_city);
        asst->auth_log.g_ast_city = NULL;
    }
    if (asst->auth_log.g_ast_subdivision_code) {
        ZPN_FREE(asst->auth_log.g_ast_subdivision_code);
        asst->auth_log.g_ast_subdivision_code = NULL;
    }
    if (asst->dft_rt_intf) ZPN_FREE(asst->dft_rt_intf);
    if (asst->platform) ZPN_FREE(asst->platform);
    if (asst->platform_detail) ZPN_FREE(asst->platform_detail);
    if (asst->platform_arch) ZPN_FREE(asst->platform_arch);
    if (asst->platform_version) ZPN_FREE(asst->platform_version);
    if (asst->frr_version) ZPN_FREE(asst->frr_version);
    if (asst->runtime_os) ZPN_FREE(asst->runtime_os);
    if (asst->auth_log.gids) {
        ZPN_FREE(asst->auth_log.gids);
        asst->auth_log.gids = NULL;
        asst->auth_log.gids_count = 0;
    }

    if (asst->auth_log.dft_rt_intf) {
        ZPN_FREE(asst->auth_log.dft_rt_intf);
        asst->auth_log.dft_rt_intf = NULL;
    }

    if (asst->auth_log.cc) {
        ZPN_FREE(asst->auth_log.cc);
        asst->auth_log.cc = NULL;
    }
    if (asst->auth_log.slogger_info){
        ZPN_FREE(asst->auth_log.slogger_info);
        asst->auth_log.slogger_info = NULL;
    }
    zpn_broker_assistant_free_capability(&asst->capability);
    ZPN_FREE(asst);

    if (f_conn) {
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
    }

    if (f_conn) {
        zpn_fohh_worker_assistant_disconnect_control(fohh_connection_get_thread_id(f_conn));
    }
}

STATIC_INLINE void zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(struct connected_assistant *asst)
{
    zpn_broker_assistant_control_destroy_with_asst_lock(asst);
}

static int zpn_broker_assistant_admin_probe_status_report_cb(void*                 argo_cookie_ptr,
                                                             void*                 argo_structure_cookie_ptr,
                                                             struct argo_object*   object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_command_probe_status *report = object->base_structure_void;

    if (!report || !f_conn) {
        ZPN_LOG(AL_ERROR, "Command Probe : argo callback argument is not valid");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Command Probe : Rx: %s", dump);
        }
    }

    if (admin_probe_collection) {
        argo_log_structure_immediate(admin_probe_collection,
                                     argo_log_priority_info,
                                     0,
                                     "status_report",
                                     zpn_command_probe_status_description,
                                     report);
    }


    ZPN_LOG(AL_NOTICE, "Command Probe %s : zpn_command_probe_status report from connector", report->command_uuid? report->command_uuid: "");

    return zpath_customer_log_struct(report->customer_gid,
                                     zpath_customer_log_type_admin_probe,
                                     "command_probe",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_command_probe_status_description,
                                     report);
}

static int zpn_broker_assistant_np_admin_probe_status_report_cb(void*                 argo_cookie_ptr,
                                                             void*                 argo_structure_cookie_ptr,
                                                             struct argo_object*   object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct np_command_probe_status *report = object->base_structure_void;

    if (!report || !f_conn) {
        ZPN_LOG(AL_ERROR, "Command Probe : argo callback argument is not valid");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Command Probe : Rx: %s", dump);
        }
    }

    if (admin_probe_collection) {
        argo_log_structure_immediate(admin_probe_collection,
                                     argo_log_priority_info,
                                     0,
                                     "status_report",
                                     np_command_probe_status_description,
                                     report);
    }


    ZPN_LOG(AL_NOTICE, "Command Probe %s : np_command_probe_status report from connector", report->command_uuid? report->command_uuid: "");

    return zpath_customer_log_struct(report->customer_gid,
                                     zpath_customer_log_type_admin_probe,
                                     "command_probe",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     np_command_probe_status_description,
                                     report);
}

/* Get scope_gid for the assistant from 'zpn_assistant' table */
static int zpn_broker_assistant_get_scope_gid(int64_t asst_id, int64_t *scope_gid)
{
    struct zpn_assistant *assistant = NULL;
    int result = ZPN_RESULT_NO_ERROR;

    result = zpn_assistant_get_by_id(asst_id, &assistant, NULL, NULL, 0);
    if (result || !assistant) {
        ZPN_LOG(AL_ERROR, "Cannot find the assistant with Assistant ID = %" PRId64, asst_id);
        return ZPN_RESULT_ERR;
    }

    *scope_gid = assistant->scope_gid;
    return result;
}


static int zpn_broker_assistant_mission_critical_conn_callback(struct fohh_connection *connection,
                                                                enum fohh_connection_state state,
                                                                void *cookie)
{
    int res = 0;
    /* As of now, this channel will be used for firedrill data fetching */
    ZPN_LOG(AL_NOTICE, "%ld: %s: assitant mission critical connection connected", (long)fohh_peer_get_id(connection), fohh_description(connection));

    if (state == fohh_connection_connected) {
        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_broker_mission_critical_description, zpn_broker_mission_critical_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_sitec_status_report for private broker for %s", fohh_description(connection));
            return res;
        }

    } else{
        /* disconnect and update stats, what else ? */
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: Assistant amc connection DOWN due to %s", fohh_description(connection), reason);

    }
    return res;
}

static int
zpn_broker_assistant_control_conn_callback(struct fohh_connection *connection,
                                           enum fohh_connection_state state,
                                           void *cookie)
{
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct argo_state *argo;
    struct connected_assistant *asst;
    struct zpn_assistant *assistant = NULL;
    int64_t asst_id;
    int res;

    if (state == fohh_connection_connected) {

        /* Allocate state for this connection, so we can track its stuff... */
        asst_id = fohh_peer_get_id(connection);
        if (!asst_id) return FOHH_RESULT_ERR;

        ZPN_DEBUG_ASSISTANT("%s: Broker received assistant control connection, Assistant ID = %"PRId64,
                            fohh_description(connection), asst_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
        if (ZPN_BROKER_IS_PUBLIC() && customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_ASSISTANT("%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);
        if ((res = argo_register_structure(argo, zpn_health_report_description, zpn_broker_health_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_health_report for connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_app_route_registration_description, zpn_broker_app_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_app_route_registration for connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_broker_request_ack */
        if ((res = argo_register_structure(argo, zpn_broker_request_ack_description, zpn_broker_request_ack_cb_from_control_conn, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_dns_assistant_check */
        if ((res = argo_register_structure(argo, zpn_dns_assistant_check_description, zpn_dns_assistant_check_from_assistant_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_assistant_check for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register assistant log upload. Connector's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_assistant_control_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register assistant log upload for connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_assistant_log_control */
        if ((res = argo_register_structure(argo, zpn_assistant_log_control_description, zpn_broker_assistant_log_control_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_log_control for broker connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_assistant_stats_control */
        if ((res = argo_register_structure(argo, zpn_assistant_stats_control_description, zpn_broker_assistant_stats_control_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_stats_control for broker connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_assistant_pvt_key_control */
        if ((res = argo_register_structure(argo, zpn_assistant_pvt_key_control_description, zpn_broker_assistant_rx_enc_pvt_key_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_pvt_key_control for broker connection %s", fohh_description(connection));
            return res;
       }

        /* Register zpn_assistant_gen_cert_control*/
        if ((res = argo_register_structure(argo, zpn_assistant_gen_cert_control_description, zpn_broker_assistant_rx_gen_cert_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_assistant_gen_cert_control for broker connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_assistant_status_report */
        if ((res = argo_register_structure(argo, zpn_assistant_status_report_description, zpn_broker_assistant_status_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_status_report for broker connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_asst_state_description, zpn_broker_assistant_control_state_cb,
                                           connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_asst_state for broker connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_asst_restart_reason_description, zpn_broker_assistant_restart_reason_cb,
                                           connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_asst_restart_reason for broker connection %s", fohh_description(connection));
            return res;
        }

        if (ZPN_INSTANCE_TYPE_PRIVATE_BROKER == g_broker_common_cfg->instance_type) {
            if ((res = argo_register_structure(argo, zpn_asst_active_control_connection_description,
                                               zpn_broker_assistant_active_connection_cb, connection) )) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_asst_active_control_connection for connection %s",
                        fohh_description(connection));
                return res;
            }
            if ((res = argo_register_structure(argo, zpn_a2pb_authentication_description,
                                               zpn_a2pb_authentication_cb, connection) )) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_a2pb_authentication for connection %s",
                        fohh_description(connection));
                return res;
            }
        }

        if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) {
            if ((res = argo_register_structure(argo, zpn_command_probe_status_description,
                                               zpn_broker_assistant_admin_probe_status_report_cb, connection) )) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_command_probe_status for connection %s",
                        fohh_description(connection));
                return res;
            }
            if ((res = argo_register_structure(argo, np_command_probe_status_description,
                                               zpn_broker_assistant_np_admin_probe_status_report_cb, connection) )) {
                ZPN_LOG(AL_ERROR, "Could not register np_command_probe_status for connection %s",
                        fohh_description(connection));
                return res;
            }
            /* Register zpn_version */
            if ((res = argo_register_structure(argo, zpn_version_description, zpn_assistant_version_cb, connection))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_version for connection for %s", fohh_description(connection));
                return res;
            }
        }



        pthread_mutex_lock(&asst_lock);

        /*
         * Public brokers accept only one control connection from the connector. This is because FOHH picks up only
         * one IP out of co2br resolution and connects to it. In pbroker environment, the connector connects to all
         * the IP/hostname in private broker load table and this pbroker can get more than one connections from the
         * connector.
         */
        if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) {
            asst = zpn_broker_find_connected_active_assistant(asst_id);
            if (asst) {
                /* Need to drop this state from its f_conn... */
                ZPN_LOG(AL_WARNING, "Replacing assistant configuration for asst %ld cn=%s",
                        (long) asst_id,
                        fohh_peer_cn(connection));

                res = zpn_broker_assistant_control_remove_connected_assistant(asst);
                if (res) {
                    ZPN_LOG(AL_WARNING, "Cannot remove assistant ID state?");
                }

                fohh_connection_delete_async(asst->f_conn, asst->f_conn_incarnation, ZPN_BRK_ASST_CONN_CLOSE);
            }
        }

        asst = ZPN_CALLOC(sizeof(*asst));

        /* Generate an ID for this client */
        res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(asst);
            pthread_mutex_unlock(&asst_lock);
            return res;
        }
        base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        asst->auth_log.tunnel_id = asst->tunnel_id;
        asst->auth_log.status = ZPN_STATUS_AUTHENTICATED;
        asst->in_list = 0;
        asst->auth_log.auth_us = epoch_us();

        asst->assistant_gid_from_config = asst_id;
        asst->f_conn = connection;
        asst->f_conn_incarnation = fohh_connection_incarnation(connection);
        fohh_connection_set_dynamic_cookie(connection, asst);

        {
            /* Fetch geoIP and gid from assistant group. Note- these were
             * prefetched during authentication, so should be
             * available directly */
            struct zpn_assistantgroup_assistant_relation *ag_relation;
            size_t count = 1;
            res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                         &ag_relation,
                                                                         &count,
                                                                         NULL,
                                                                         NULL,
                                                                         0);
            if (res) {
                ZPN_LOG(AL_WARNING, "%s: Error fetching assistantgroup_assistant_relation: %ld: %s", fohh_peer_cn(connection), (long) asst_id, zpn_result_string(res));
            } else {
                asst->g_ast_grp = ag_relation->assistant_group_id;
                asst->auth_log.g_ast_grp = ag_relation->assistant_group_id;

                if (zpn_broker_assistant_update_asst_lat_lon(asst)){
                    ZPN_LOG(AL_WARNING, "Error fetching assistant_group: %ld: %s", (long) asst->g_ast_grp, zpn_result_string(res));
                }

                if ((asst->a_lat == 0) && (asst->a_lon == 0) && !geoip_db_file) {
                    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) {
                        ZPN_LOG(AL_ERROR, "geoip_db_file is NULL");
                    }
                }
            }
        }

        /*
         * Fetch assistant from zpn_assistant table. Note:- this is
         * prefetched during authentication, so should be
         * available directly
         */
        res = zpn_assistant_get_by_id(asst_id,
                                      &assistant,
                                      NULL,
                                      NULL,
                                      0);
        if (res) {
            ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) asst_id,
                                 zpn_result_string(res));
        }

        if(res != ZPN_RESULT_NO_ERROR) {
            fohh_connection_set_dynamic_cookie(connection, NULL);
            ZPN_FREE(asst);
            pthread_mutex_unlock(&asst_lock);
            return res;
        } else {
            asst->customer_gid = assistant->customer_gid;
            asst->scope_gid    = assistant->scope_gid;
            asst->auth_log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
        }

        asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                -1,
                                EV_PERSIST,
                                zpn_broker_assistant_control_conn_monitor_cb,
                                asst);
        if (!asst->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(asst);
            pthread_mutex_unlock(&asst_lock);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(asst->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add assistant timer");
            event_free(asst->timer);
            ZPN_FREE(asst);
            pthread_mutex_unlock(&asst_lock);
            return FOHH_RESULT_NO_MEMORY;
        }

        zpn_broker_assistant_init_capability(&asst->capability);

        /*
         * Note that once we add connector to connected list, another thread can send that data to outside world
         * (think dispatcher). So call this after all the attributes are set in asst object
         */
        zpn_broker_assistant_control_add_connected_assistant(asst_id, asst);
        pthread_mutex_unlock(&asst_lock);

        zpn_application_customer_register(ZPATH_GID_GET_CUSTOMER_GID(asst_id), NULL, NULL, 0);

        if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, zpn_tcp_info_report_control_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_asst_environment_report_description,
                                                            zpn_broker_assistant_environment_report_cb, connection) )) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_asst_environment_report for connection %s",
                    fohh_description(connection));
            return res;
        }

        zpn_fohh_worker_assistant_connect_control(fohh_connection_get_thread_id(connection));

        asst->log_upload = 0;

        ZPN_LOG(AL_NOTICE, "%s: Assistant control connection UP, Assistant ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), asst_id, asst->customer_gid);

    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: Assistant control connection DOWN due to %s",
                fohh_description(connection), reason);

        pthread_mutex_lock(&asst_lock);
        asst = fohh_connection_get_dynamic_cookie(connection);
        if (asst) {
            if (zpn_is_broker_auth_log_redirect_status_feature_enabled() && asst->is_redirect_sent) {
                ZPN_LOG(AL_DEBUG, "%s: Overriding close_reason to %s", fohh_description(connection), FOHH_CLOSE_REASON_BROKER_REDIRECT);
                asst->auth_log.close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
                asst->is_redirect_sent = 0;
            } else {
                asst->auth_log.close_reason = reason;
            }

            asst->auth_log.status = ZPN_STATUS_DISCONNECTED;
            zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(asst);
        }
        pthread_mutex_unlock(&asst_lock);
    }

    return FOHH_RESULT_NO_ERROR;
}

void zpn_broker_assistant_peer_geoip_lookup(struct fohh_connection *f_conn,
                                            struct argo_inet *peer_ip,
                                            struct site *peer_site)
{
    if (!f_conn || !peer_ip || !peer_site) {
        return;
    }

    /* Fill in with default data for peer's IP. We do this in
     * particular to pick up peer_cc. The lat/lon will be overridden
     * with the assistant_group lookup in the following
     * code. Eventually we will use the group lookup exclusively, but
     * right now the assistant group does not include country code. */
    fohh_connection_address(f_conn, peer_ip, NULL);
    memset(peer_site, 0, sizeof(*peer_site));
    zpath_geoip_lookup_double(peer_ip, &peer_site->lat, &peer_site->lon, peer_site->cc);

    /* Fetch the assistant group so we can get the configured
     * lat/lon/cc. These lookups were cached when the assistant
     * authenticated. If these lookups fail, that is okay (but very
     * highly unusual- authentication should not have succeeded). The
     * broker will just use its own location for redirecting */
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    int res = zpn_assistantgroup_assistant_relation_get_by_assistant(fohh_peer_get_id(f_conn),
                                                                     &ag_relation,
                                                                     &count,
                                                                     NULL,
                                                                     NULL,
                                                                     0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: assistantgroup_assistant_relation: %ld: %s",
                fohh_description(f_conn),
                (long) fohh_peer_get_id(f_conn),
                zpn_result_string(res));
        return; // Respect zpath_geoip_lookup_double()
    }

    /* Now that we have an assistantgroup, fetch that too. */
    struct zpn_assistant_group *group;
    res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                         &group,
                                         NULL,
                                         NULL,
                                         0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: assistant_group fetch for gid = %ld: %s",
                fohh_description(f_conn),
                (long) ag_relation->assistant_group_id,
                zpn_result_string(res));
        return; // Respect zpath_geoip_lookup_double()
    }

    /* Use the lat/lon/cc of the assistant group. The assistant group has no country code, so we cheat and use the cc of the  */
    peer_site->lat = group->latitude;
    peer_site->lon = group->longitude;
    if (group->country_code) {
        snprintf(peer_site->cc, sizeof(peer_site->cc), "%s", group->country_code);
    }
}

static void zpn_broker_assistant_conn_redirect(struct fohh_connection *f_conn)
{

    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_assistants, NULL)) {
        return;
    }

    const struct connected_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), asst->tunnel_id);

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    zpn_broker_assistant_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);

    zpn_broker_balance_conn_redirect(f_conn,
                                     asst->customer_gid,
                                     asst->scope_gid,
                                     &peer_ip, &peer_site,
                                     asst->tunnel_id,
                                     redirect_assistants,
                                     redirect_assistants_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_assistant,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_ctrl_alt_cloud_redirects), 1);
    }
}

static void zpn_broker_assistant_config_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_assistants, NULL)) {
        return;
    }

    struct connected_assistant_config_fohh_state *asst = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), asst->tunnel_id);

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    zpn_broker_assistant_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);

    zpn_broker_balance_conn_redirect(f_conn,
                                     asst->customer_gid,
                                     asst->scope_gid,
                                     &peer_ip, &peer_site,
                                     asst->tunnel_id,
                                     redirect_assistants,
                                     redirect_assistants_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_assistant,
                                     0);

    if (is_redirect_to_alt_cloud) {
        switch (asst->config_connection_type)
        {
            case config_conn_type_config:
                __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_cfg_alt_cloud_redirects), 1);
                break;
            case config_conn_type_override:
                __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_ovd_alt_cloud_redirects), 1);
                break;
            case config_conn_type_np:
                __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_np_cfg_alt_cloud_redirects), 1);
                break;
        }
    }
}

static void zpn_broker_assistant_cfg_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_assistant_config_conn_redirect(f_conn);
}

static void zpn_broker_assistant_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_assistant_conn_redirect(f_conn);
}

static void zpn_broker_assistant_mtunnel_end_thread_cb(int sock, short flags, void *cookie)
{
    struct zpn_mtunnel_end_thread_cb_cookie *cb_cookie = cookie;
    struct zpn_broker_assistant_fohh_state *a_state;
    struct zpn_mtunnel_end *req;
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    struct zpn_tlv *tlv;
    int bucket_id = -1;

    if (!cb_cookie) {
        return;
    }

    req = &(cb_cookie->tunnel_end);
    if (!req->mtunnel_id && !req->tag_id) {
        ZPN_LOG(AL_ERROR, "Expecting mtunnel_id or tag_id in mtunnel_end request");
        return;
    }

    if (!cb_cookie->timer) {
        return;
    }

    const char *id = req->mtunnel_id ? req->mtunnel_id : "NULL";

    if (req->mtunnel_id) {
        mtunnel_id_len = strlen(req->mtunnel_id);
        mtunnel_id_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
        mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_id_hash, &bucket_id);
        if (!mtunnel) {
            ZPN_LOG(AL_NOTICE, "%s: Mtunnel_end received for mtunnel that isn't found", req->mtunnel_id);
            return;
        }
    } else {
        a_state = cb_cookie->a_state;
        if (!a_state || a_state->incarnation != cb_cookie->a_state_incarnation) {
            return;
        }

        tlv = a_state_get_tlv(a_state);
        if (tlv->type != zpn_zrdt_tlv) {
            return;
        }
        mtunnel = zpn_broker_get_global_owner_tlv(tlv, req->tag_id);
        if (!mtunnel) {
            //ZPN_DEBUG_MTUNNEL("Mtunnel_end received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
            return;
        }

       if (mtunnel->state >= zbms_reaping) {
            /* It is already being freed, so return */
            return;
        }
        mtunnel_bucket_lock(mtunnel, &bucket_id);
    }

    ZPN_DEBUG_MTUNNEL("%s: %s: Rx from assistant mtunnel end: %d, err = %s, drop = %d, data_arrived = %ld",
                      mtunnel->mtunnel_id,
                      id,
                      req->tag_id,
                      req->error ? req->error : "<null>",
                      req->drop_data,
                      (long)mtunnel->assistant_tlv.data_arrived);

    mtunnel_lock(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    if (mtunnel->assistant_tlv_type == zpn_zrdt_tlv) {
        if (epoch_us() - assistant_mconn->rx_data_us < ZRDT_MTUNNEL_END_GRACE_PERIOD && cb_cookie->defer_count < ZRDT_MAX_MTUNNEL_END_DEFER) {
            struct timeval tv;
            tv.tv_sec = ZRDT_MTUNNEL_END_DEFER_TIME;
            tv.tv_usec = 0;

            cb_cookie->defer_count += 1;
            event_add(cb_cookie->timer, &tv);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            ZPN_DEBUG_MTUNNEL("%s: mtunnel_end over zrdt, assistant timer, pkt recevied during grace period, defer count %d", mtunnel->mtunnel_id, cb_cookie->defer_count);
            return;
        } else if (cb_cookie->defer_count == ZRDT_MAX_MTUNNEL_END_DEFER) {
            ZPN_DEBUG_MTUNNEL("%s: mtunnel_end over zrdt, assistant timer, max defer count reached", mtunnel->mtunnel_id);
        }
    }

    assistant_mconn->fin_rcvd = 1;
    if (!assistant_mconn->fin_rcvd_us) assistant_mconn->fin_rcvd_us = epoch_us();
    if (req->drop_data) {
        assistant_mconn->drop_tx = req->drop_data;
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    zpn_mconn_forward_mtunnel_end(client_mconn, BRK_MT_CLOSED_FROM_ASSISTANT, req->drop_data);

    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return;
}

static void set_assistant_mtunnel_end_timer(struct event_base *base,
                                            struct zpn_broker_mtunnel *mtunnel,
                                            struct zpn_mtunnel_end *req,
                                            struct zpn_broker_assistant_fohh_state *a_state) {
    struct zpn_mtunnel_end_thread_cb_cookie *cb_cookie;
    if (!req->drop_data) {
        if (!mtunnel->mtunnel_end_assistant) {
            mtunnel->mtunnel_end_assistant = ZPN_CALLOC(sizeof(*mtunnel->mtunnel_end_assistant));
            cb_cookie = mtunnel->mtunnel_end_assistant;
            cb_cookie->timer = event_new(base, -1, 0, NULL, NULL);
        } else {
            cb_cookie = mtunnel->mtunnel_end_assistant;
            event_del(cb_cookie->timer);
        }
    } else {
        if (!mtunnel->mtunnel_end_assistant_dropdata) {
            mtunnel->mtunnel_end_assistant_dropdata = ZPN_CALLOC(sizeof(*mtunnel->mtunnel_end_assistant_dropdata));
            cb_cookie = mtunnel->mtunnel_end_assistant_dropdata;
            cb_cookie->timer = event_new(base, -1, 0, NULL, NULL);
        } else {
            cb_cookie = mtunnel->mtunnel_end_assistant_dropdata;
            event_del(cb_cookie->timer);
        }
    }

    if (req->mtunnel_id) {
        cb_cookie->tunnel_end.mtunnel_id = ZPN_STRDUP(req->mtunnel_id, strlen(req->mtunnel_id));
    }
    if (req->error) {
        cb_cookie->tunnel_end.error = ZPN_STRDUP(req->error, strlen(req->error));
    }
    cb_cookie->tunnel_end.drop_data = req->drop_data;
    cb_cookie->tunnel_end.tag_id = req->tag_id;
    cb_cookie->tunnel_end.err_code = req->err_code;

    cb_cookie->a_state = a_state;
    cb_cookie->a_state_incarnation = a_state->incarnation;
    cb_cookie->defer_count = 1;
    event_assign(cb_cookie->timer, base, -1, EV_FINALIZE, zpn_broker_assistant_mtunnel_end_thread_cb, cb_cookie);

    struct timeval tv;
    tv.tv_sec = ZRDT_MTUNNEL_END_DEFER_TIME;
    tv.tv_usec = 0;
    event_add(cb_cookie->timer, &tv);
    return;
}

static int zpn_broker_mtunnel_end_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object)
{
    struct zpn_mtunnel_end *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    int bucket_id = -1;

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "Expecting non-null assistant state on mtunnel_end");
        return ZPN_RESULT_NO_ERROR;
    }
    tlv = a_state_get_tlv(a_state);

    const char *id = req->mtunnel_id ? req->mtunnel_id : "NULL";

    if (!req->mtunnel_id && !req->tag_id) {
        ZPN_LOG(AL_ERROR, "Expecting mtunnel_id or tag_id in mtunnel_end request");
        return ZPN_RESULT_ERR;
    }

    if (req->mtunnel_id) {
        mtunnel_id_len = strlen(req->mtunnel_id);
        mtunnel_id_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
        mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_id_hash, &bucket_id);
        if (!mtunnel) {
            ZPN_LOG(AL_NOTICE, "%s: Mtunnel_end received for mtunnel that isn't found", req->mtunnel_id);
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        mtunnel = zpn_broker_get_global_owner_tlv(tlv, req->tag_id);
        if (!mtunnel) {
            //ZPN_DEBUG_MTUNNEL("Mtunnel_end received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }

        if (mtunnel->state >= zbms_reaping) {
            /* It is already being freed, so return */
            return ZPN_RESULT_NO_ERROR;
        }
        mtunnel_bucket_lock(mtunnel, &bucket_id);
    }

    ZPN_DEBUG_MTUNNEL("%s: %s: Rx from assistant mtunnel end: %d, err = %s, drop = %d, data_arrived = %ld",
                      mtunnel->mtunnel_id,
                      id,
                      req->tag_id,
                      req->error ? req->error : "<null>",
                      req->drop_data,
                      (long)mtunnel->assistant_tlv.data_arrived);

    mtunnel_lock(mtunnel);

    if (mtunnel->assistant_tlv_type == zpn_zrdt_tlv) {
        set_assistant_mtunnel_end_timer(fohh_get_thread_event_base(zpn_tlv_get_thread_id(tlv)), mtunnel, req, a_state);
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
        ZPN_DEBUG_MTUNNEL("%s: mtunnel_end over zrdt, handling deferred to assistant timer", mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_ERROR;
    }

    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);
    assistant_mconn->fin_rcvd = 1;
    if (!assistant_mconn->fin_rcvd_us) assistant_mconn->fin_rcvd_us = epoch_us();
    if (req->drop_data) {
        assistant_mconn->drop_tx = req->drop_data;
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    if(req->error && !strcmp(req->error, BRK_MT_RESET_FROM_SERVER)) {
        ZPN_DEBUG_MTUNNEL("%s: Received RST from assistant forward mtunnel_end to peer %p mconn",
                           mtunnel->mtunnel_id, client_mconn);
        zpn_mconn_forward_mtunnel_end(client_mconn, BRK_MT_RESET_FROM_SERVER, req->drop_data);
    } else {
        zpn_mconn_forward_mtunnel_end(client_mconn, BRK_MT_CLOSED_FROM_ASSISTANT, req->drop_data);
    }

    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_zdx_fill_mtunnel_pipeline_cookie(struct zpn_broker_mtunnel *mtunnel, struct zpn_tlv *tlv) {

    uint16_t downstream_port_ne;

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        if (mtunnel->assistant_tlv_type == zpn_fohh_tlv) {
            struct zpn_mconn* server_tlv_mconn = &(mtunnel->assistant_tlv.mconn);
            struct zpn_zdx_cookie *zdx_cookie = server_tlv_mconn->pipeline_cookie;

            fohh_connection_address_and_port(mtunnel->assistant_f_conn,
                                             &(zdx_cookie->probe_info.downstream_ip), &downstream_port_ne,
                                             NULL, NULL);
            zdx_cookie->probe_info.downstream_port = ntohs(downstream_port_ne);
            zdx_cookie->probe_info.downstream_system_type = zpn_zdx_system_type_connector;
            zdx_cookie->probe_info.argo_structure_cookie_ptr = tlv;
            zdx_cookie->probe_info.conn_incarnation = zpn_tlv_incarnation(tlv);
        } else {
            struct zpn_mconn* server_zrdt_tlv_mconn = &(mtunnel->assistant_tlv_zrdt.mconn);
            struct zpn_zdx_cookie *zdx_zrdt_cookie = server_zrdt_tlv_mconn->pipeline_cookie;

            zdtls_connection_address_and_port(zrdt_conn_get_datagram_tx_cookie(mtunnel->assistant_z_conn),
                                              &(zdx_zrdt_cookie->probe_info.downstream_ip), &downstream_port_ne,
                                              NULL, NULL);

            zdx_zrdt_cookie->probe_info.downstream_port = ntohs(downstream_port_ne);
            zdx_zrdt_cookie->probe_info.downstream_system_type = zpn_zdx_system_type_connector;
            zdx_zrdt_cookie->probe_info.argo_structure_cookie_ptr = tlv;
            zdx_zrdt_cookie->probe_info.conn_incarnation = zpn_tlv_incarnation(tlv);
        }
    }
}

struct zpn_broker_assistant_fohh_state *zpn_broker_mtunnel_assistant_state(struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_broker_assistant_fohh_state *a_state;

    if (mtunnel->assistant_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = mtunnel->assistant_f_conn;

        a_state = fohh_connection_get_dynamic_cookie(f_conn);
    } else {
        struct zrdt_conn *z_conn = mtunnel->assistant_z_conn;

        a_state = zrdt_conn_get_dynamic_cookie(z_conn);
    }
    return a_state;
}

int zpn_broker_mtunnel_re_eval_policy_check_for_srv_ip(struct zpn_broker_mtunnel *mtunnel) {
    int res = ZPN_RESULT_NO_ERROR;
    int64_t policy_re_eval_delta_us = 0;

    if (ZPN_BROKER_IS_PRIVATE() && zpn_is_dr_mode_active()) {
        /* Policies are skipped during DR mode */
        return ZPN_RESULT_NO_ERROR;
    }

    if (mtunnel && mtunnel->fqdn_to_serv_ip) {

        res = mtunnel_locked_policy_check(mtunnel, NULL);
        ZPN_DEBUG_MTUNNEL("%s: customer_id: %" PRId64 "fqdn to server ip policy re-evaluation result:%s",
                        mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id), zpn_result_string(res));
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            mtunnel->is_fqdn_to_serv_ip_policy_check_async = 1;
            zpn_broker_mtunnel_stats_fqdn_to_srv_ip_update_counter(zpn_fqdn_to_srv_ip_counter_async);
            return res;
        } else {
            /* Calculate policy re-eval time and update mtunnel_req_policy_check_time_us with cumulative value adding the time taken for this new evaluation also */
            policy_re_eval_delta_us = epoch_us() - mtunnel->fqdn_to_serv_ip_policy_reval_start_us;
            mtunnel->log.mtunnel_req_policy_check_time_us += policy_re_eval_delta_us;

            ZPN_DEBUG_MTUNNEL("%s: customer_id: %" PRId64 "fqdn to server ip policy re-evaluation time:%" PRId64" ",
                        mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id), policy_re_eval_delta_us);

            mtunnel->is_fqdn_to_serv_ip_policy_check_async = 0;
            if (res == ZPN_RESULT_ERR || res == ZPN_RESULT_ACCESS_DENIED) {
                /* Send BIND ack with an error to AC */
                zpn_broker_tunnel_complete_send(mtunnel, BRK_MT_SETUP_BIND_REJECTED_BY_IP_POLICY, NULL);
                zpn_broker_mtunnel_stats_fqdn_to_srv_ip_update_counter(zpn_fqdn_to_srv_ip_counter_reject);
                return ZPN_RESULT_ACCESS_DENIED;
            }
        }
        zpn_broker_mtunnel_stats_fqdn_to_srv_ip_update_counter(zpn_fqdn_to_srv_ip_counter_success);
    }
    return res;
}

int zpn_broker_mtunnel_complete_bind(struct zpn_broker_mtunnel *mtunnel)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_broker_assistant_fohh_state *a_state = NULL;
    enum zrdt_stream_type atype = zrdt_stream_buffer;
    enum zrdt_stream_type ctype = zrdt_stream_buffer;
    struct zpn_mconn *client_mconn = NULL;
    struct zpn_mconn *assistant_mconn = NULL;

    a_state = zpn_broker_mtunnel_assistant_state(mtunnel);
    struct zpn_tlv *tlv = a_state_get_tlv(a_state);

    /*
     * NOTE: We must send tunnel complete messages to client/assistant
     * before we add our local owner, because adding local owner
     * dequeues.
     */
    res = zpn_broker_tunnel_complete_send(mtunnel, NULL, NULL);
    if (res) {
        if (res == ZPN_RESULT_WOULD_BLOCK) {
            ZPN_LOG(AL_CRITICAL, "Implement me- tunnel_complete_send blocked");
        }
        ZPN_LOG(AL_ERROR, "%s: tunnel_complete_send failed: %s", mtunnel->mtunnel_id, zpn_result_string(res));
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_CANNOT_SEND_MT_COMPLETE);
        return ZPN_RESULT_NO_ERROR;
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    /* If we have RDT connection, create a stream */
    if (a_state->tlv_type == zpn_zrdt_tlv) {

        mtunnel->assistant_tlv_type = zpn_zrdt_tlv;

        if (mtunnel->client_tlv_type == zpn_zrdt_tlv) {
            /* Client side has RDT */
            if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
                if (mtunnel->ip_protocol == IPPROTO_UDP || mtunnel->ip_protocol == IPPROTO_ICMP) {
                    ctype = zrdt_stream_unreliable_endpoint;
                    atype = zrdt_stream_unreliable_endpoint;
                } else {
                    ctype = zrdt_stream_reliable_endpoint;
                    atype = zrdt_stream_reliable_endpoint;
                }
            } else {
                atype = zrdt_stream_transit;
                ctype = zrdt_stream_transit;
            }
        } else {
            /* Client side is not RDT */
            if (mtunnel->ip_protocol == IPPROTO_UDP || mtunnel->ip_protocol == IPPROTO_ICMP) {
                atype = zrdt_stream_unreliable_endpoint;
            } else {
                atype = zrdt_stream_reliable_endpoint;
            }
        }

        res = zrdt_stream_create(zpn_mconn_zrdt_tlv_get_conn(&(a_state->zrdt_tlv_state)),
                                 &(mtunnel->assistant_tlv_zrdt.stream),
                                 mtunnel->log.a_tag,
                                 atype,
                                 zpn_zrdt_read_cb,
                                 zpn_zrdt_write_cb,
                                 zpn_zrdt_event_cb,
                                 &(a_state->zrdt_tlv_state));
        if (res) {
            ZPN_LOG(AL_INFO, "%s: Could not create stream", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_TO_CREATE_STREAM);
            return ZPN_RESULT_NO_ERROR;
        }

        ZPN_DEBUG_MTUNNEL("Created zrdt broker-assistant stream %p (id %d, type %d) for mtunnel %s over dtls conn %s, ", mtunnel->assistant_tlv_zrdt.stream, mtunnel->log.a_tag, atype, mtunnel->mtunnel_id, zpn_mconn_zrdt_description(&(a_state->zrdt_tlv_state)));
        zrdt_set_stream_cookie(mtunnel->assistant_tlv_zrdt.stream, mtunnel, mtunnel->incarnation);
    } else {
        /* Assistant side doesn't have RDT */
        if (mtunnel->client_tlv_zrdt.stream) {
            /* Client side has RDT */
            if (mtunnel->ip_protocol == IPPROTO_UDP || mtunnel->ip_protocol == IPPROTO_ICMP) {
                ctype = zrdt_stream_unreliable_endpoint;
            } else {
                ctype = zrdt_stream_reliable_endpoint;
            }
        }
    }

    if (mtunnel->client_tlv_type == zpn_zrdt_tlv) {
        if (mtunnel->client_tlv_zrdt.stream && (ctype != zrdt_stream_buffer) &&
            zrdt_stream_get_type(mtunnel->client_tlv_zrdt.stream) == zrdt_stream_buffer) {
            (void) zrdt_stream_set_type(mtunnel->client_tlv_zrdt.stream, ctype);
        }
    }

    /* Save the max_tag_id */
    if (mtunnel->log.a_tag > a_state->tlv_state.max_tag_id) {
        a_state->tlv_state.max_tag_id = mtunnel->log.a_tag;
    }

    if (a_state->tlv_type == zpn_zrdt_tlv) {
        mtunnel->assistant_tlv_type = zpn_zrdt_tlv;
        res = zpn_mconn_add_local_owner(&(mtunnel->assistant_tlv_zrdt.mconn),
                                        0,
                                        &(a_state->zrdt_tlv_state),
                                        &(mtunnel->log.a_tag),
                                        sizeof(mtunnel->log.a_tag),
                                        &zpn_mconn_zrdt_tlv_calls);
    } else {
        mtunnel->assistant_tlv_type = zpn_fohh_tlv;
        res = zpn_mconn_add_local_owner(&(mtunnel->assistant_tlv.mconn),
                                        0,
                                        &(a_state->tlv_state),
                                        &(mtunnel->log.a_tag),
                                        sizeof(mtunnel->log.a_tag),
                                        &zpn_mconn_fohh_tlv_calls);
    }
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: local owner bind failed: %s", mtunnel->mtunnel_id, zpn_result_string(res));
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_BIND_TO_AST_LOCAL_OWNER);
        return ZPN_RESULT_NO_ERROR;
    }

    /* For mtr probes prentend remote side has disabled flow control. This is required because the mtr mtunnel payload is
     * consumed by the assistant and not forwarded; thus not generating window updates for the mtunnel or the outer tunnel.
     * TDB: include web probes as well when caching is enabled for web probes
     *
     * above zpn_mconn_add_local_owner() has inherit fohh_tlv->remote_fc_status for mtunnel->assistant_tlv.remote_fc_status
     * now we set the mconn level tlv (assistant_tlv) again for this specific mconn
     */
    if (broker_mtr_flow_control_disabled && (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr
                                          || mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe
                                          || mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https)) {
        mtunnel->assistant_tlv.remote_fc_status = flow_ctrl_disabled;
    } else {
        mtunnel->assistant_tlv.remote_fc_status = a_state->tlv_state.remote_fc_status;
    }

    /*
     * FIXME: why is customer_id filled in again. It is filled in the first time in mtunnel_allocate_and_bucket_lock()
     */
    mtunnel->customer_id = ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id);

    ZPN_DEBUG_MTUNNEL("%s: Bound to assistant, Tag %d, %s", mtunnel->mtunnel_id, (int) mtunnel->log.a_tag, zpn_tlv_description(tlv));

    mtunnel->state = zbms_complete;
    mtunnel->log.tunnel_us = epoch_us() - mtunnel->log.startrx_us;
    zpn_broker_mtunnel_log(mtunnel);

    /* Mark it 'active' for the NEXT log. First log will be "open" */
    mtunnel->log.action = "active";

    /*
     * If the client mconn have received FIN earlier, then we will forward the FIN
    */
    if ((mtunnel->assistant_tlv_type != zpn_zrdt_tlv) &&
        (assistant_mconn->client_needs_to_forward) &&
        zpn_broker_assistant_is_brk_send_pending_mtunnel_end_enabled(mtunnel->customer_id)) {
        ZPN_LOG(AL_NOTICE,"%s: tag= %d We have pending mtunnel_end received from client, send out the message now", mtunnel->mtunnel_id, mtunnel->log.a_tag);
        zpn_mconn_forward_mtunnel_end(assistant_mconn, BRK_MT_CLOSED_FROM_CLIENT, client_mconn->drop_tx);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Received bind message from connector. This bind could be the result of either BrkRq sent to dispatcher or sent
 * directly to the connector. We can distinguish this by seeing if zpn_mtunnel_bind->dsp_bypassed field is set or not.
 * Lets update the path cache only if the BrkRq is passed via dispatcher. Broker is caching the decision made by
 * dispatcher and it makes sense to cache only the info that dispatcher gave to us. At the same time, we will
 * blow up the cache if the transaction failed as well.
 */
static int zpn_broker_mtunnel_bind_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_mtunnel_bind *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    uint64_t mtunnel_hash;
    size_t mtunnel_id_len;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    int res;
    int bucket_id = -1;

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "Bind request without a_state");
        return ZPN_RESULT_ERR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char dump[8000] = {0};
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "zpn_broker_mtunnel_bind_cb Rx: %s", dump);
        }
    }

    struct zpn_tlv *tlv = a_state_get_tlv(a_state);
    if (zpn_tlv_sanity_check(tlv) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "bind_cb without a_state connection?");
        return ZPN_RESULT_ERR;
    }

    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "Bind",
                                     zpn_mtunnel_bind_description,
                                     req);
    }

    if (!req->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "Bind request received from %s without mtunnel_id", zpn_tlv_description(tlv));
        return ZPN_RESULT_ERR;
    }

    mtunnel_id_len = strlen(req->mtunnel_id);
    mtunnel_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
    mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_ERROR, "%s: Bind request received from %s, tunnel not found.", req->mtunnel_id, zpn_tlv_description(tlv));
        return ZPN_RESULT_NO_ERROR;
    }

    /* Local connection hook - Not we are not holding mtunnel lock !*/
    //zpn_broker_local_mtunnel_connection(mtunnel, f_conn);

    mtunnel_lock(mtunnel);

    if (ZPN_BROKER_IS_PUBLIC() &&
        mtunnel->flag.timeout_redispatch_to_diff_dc &&
        mtunnel->state > zbms_dispatch_sent) {
        /*
         * We had re-dispatched zpn_broker_request to different DC dispatcher, so we can
         * receive 2 bind requests. Don't close the mtunnel for this - just discard the
         * later bind request.
         */
        ZPN_LOG(AL_INFO, "%s: Bind req received from %s due to %"PRId64" dispatcher, but not in zbms_dispatch_sent state, in %s, likely because of re-dispatch to diff DC, ignoring",
                mtunnel->mtunnel_id, zpn_tlv_description(tlv), req->g_dsp, mtunnel_state(mtunnel->state));
        struct zpn_broker_dispatcher_stats *broker_dispatcher_stats = zpn_broker_dispatcher_stats_data_obj_get();
        __sync_add_and_fetch_8(&(broker_dispatcher_stats->zpn_brk_req_redispatch_timeout_bind_discard), 1);
        struct zpn_broker_dispatcher_stats *brk_dsp_stats = zpn_broker_dispatcher_get_stats(req->g_dsp);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_bind_discard), 1);
        }
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel->assistant_id = a_state->assitant_gid;
    mtunnel->assistant_group_id = a_state->assistant_group_gid;

    if (a_state->capability.capability_pathing_from_ubrk_enabled) {
        if (req->dsp_bypassed) {
            mtunnel->flag.brk_req_using_path_cache_succeeded = 1;
        } else {
            mtunnel->flag.brk_req_using_path_cache_succeeded = 0;
            zpn_broker_client_path_cache_update_from_another_thread(mtunnel->tunnel_id, mtunnel->c_state_fohh_thread_id,
                        mtunnel->o_user_id[0] ? &(mtunnel->o_user_id[0]) : &(mtunnel->user_id[0]),
                        mtunnel->req_app_name, mtunnel->req_app_type, mtunnel->client_port, mtunnel->ip_protocol,
                        req->g_app_grp, mtunnel->assistant_id, req->g_ast_grp, req->g_aps, req->g_srv_grp, 0,
                        req->brk_req_s_inet, a_state->tunnel_id, a_state->incarnation, mtunnel->flag.ast_grp_used_in_cache_key);
        }
    }

    if (mtunnel->state != zbms_dispatch_sent) {
        ZPN_LOG(AL_ERROR, "%s: Bind request received from %s, but not in zbms_dispatch_sent, am in %s",
                req->mtunnel_id,
                zpn_tlv_description(tlv),
                mtunnel_state(mtunnel->state));
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_BIND_RECV_IN_BAD_STATE);
        mtunnel_locked_state_machine(mtunnel);
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
        return ZPN_RESULT_NO_ERROR;
    }

    /* Allocate a tag to use... */
    mtunnel->log.a_tag = __sync_add_and_fetch_4(&(a_state->last_used_tag_id), 1);

    /* Record server_is from assistant */
    mtunnel->log.server_us = req->server_us;
    mtunnel->fwd_broker_id = req->g_bfw;
    mtunnel->log.bindrx_us = epoch_us();
    mtunnel->log.disprx_us = req->bfw_us;
    mtunnel->log.s_ip = req->s_inet;
    mtunnel->log.s_port = req->s_port;
    mtunnel->log.a_ip = req->a_inet;
    mtunnel->log.a_port = req->a_port;
    mtunnel->log.brk_req_dsp_tx_us = req->brk_req_dsp_tx_us;
    mtunnel->log.brk_req_bfw_tx_us = req->bfw_us;
    mtunnel->log.brk_req_ast_rx_us = req->brk_req_ast_rx_us;
    mtunnel->log.bind_ast_tx_us = req->ast_tx_us;
    mtunnel->path_decision |= req->path_decision;
    mtunnel->server_group_id = req->g_srv_grp;
    mtunnel->application_server_id = req->g_aps;
    /* We had sent brk req to 2 dispatchers, fill the correct dispatcher id in txn log due to which this bind came. */
    mtunnel->log.g_dsp = req->g_dsp;

    zpn_broker_assistant_update_qbr_fields(mtunnel, a_state);

    int is_ip = is_valid_ip(mtunnel->req_app_name);

    /* Set fqdn_to_serv_ip flag to 1 only if feature is enabled and requested app is fqdn based */
    if (zpn_broker_policy_fqdn_to_srv_ip_enabled(ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id)) && !is_ip) {
        mtunnel->fqdn_to_serv_ip = 1;
        ZPN_DEBUG_MTUNNEL("%s: fqdn to server ip flag is set TRUE for app:%s", req->mtunnel_id, mtunnel->req_app_name);
    }
    mtunnel->is_fqdn_to_serv_ip_policy_check_async = 0;
    mtunnel->log.g_ast = mtunnel->assistant_id;

    zpn_tx_path_decision_get_str(mtunnel->path_decision, mtunnel->log.path_decision, sizeof(mtunnel->log.path_decision));

    mtunnel->insp_status = req->insp_status;
    mtunnel->ssl_err = req->ssl_err;
    zpn_broker_mtunnel_stats_update_inspection_counter(mtunnel->insp_status);
    zpn_tx_insp_status_get_str(mtunnel->insp_status, mtunnel->ssl_err, mtunnel->log.insp_status_str);

    if (mtunnel->log.disprx_us) {
        if (mtunnel->log.disptx_us) {
            mtunnel->log.disp_us = mtunnel->log.disprx_us - mtunnel->log.disptx_us;
        }
        mtunnel->log.bind_us = mtunnel->log.bindrx_us - mtunnel->log.disprx_us;
    } else if (mtunnel->flag.brk_req_using_path_cache_succeeded) {
        mtunnel->log.disp_us = 0;
        mtunnel->log.bind_us = mtunnel->log.bindrx_us - mtunnel->log.brk_req_utoa_tx_us;
    }

    if (a_state->tlv_type == zpn_zrdt_tlv) {
        mtunnel->assistant_tlv_type = zpn_zrdt_tlv;
        mtunnel->assistant_z_conn = zpn_mconn_zrdt_tlv_get_conn(&(a_state->zrdt_tlv_state));
    } else {
        mtunnel->assistant_tlv_type = zpn_fohh_tlv;
        mtunnel->assistant_f_conn = zpn_mconn_fohh_tlv_get_conn(&(a_state->tlv_state));
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);
    zpn_mconn_connect_peer(client_mconn, assistant_mconn);
    zpn_client_drain_tx_data(assistant_mconn);

    zpn_mconn_set_fohh_thread_id(zpn_broker_mtunnel_assistant_mconn(mtunnel),
                                 zpn_tlv_get_thread_id(tlv));

    zpn_zdx_fill_mtunnel_pipeline_cookie(mtunnel, tlv);

    if (mtunnel->fqdn_to_serv_ip) {
        mtunnel->fqdn_to_serv_ip_policy_reval_start_us = epoch_us();
    }

    res = zpn_broker_mtunnel_re_eval_policy_check_for_srv_ip(mtunnel);
    if (res == ZPN_RESULT_ACCESS_DENIED) {
        ZPN_DEBUG_MTUNNEL("%s: Customer id:%" PRId64 " Bind req : Access denied (fqdn to server ip) rule:%" PRId64 " fqdn prev_rule:%" PRId64 " ",
                                 mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id),
                                 mtunnel->rule_gid, mtunnel->log.g_fqdn_to_server_ip_first_eval_rul);
        zpn_broker_client_mtunnel_ack_err_for_fqdn_serv_ip(mtunnel);
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY);
    } else if (res == ZPN_RESULT_NO_ERROR) {
        zpn_broker_mtunnel_complete_bind(mtunnel);
    }

    /* Update re-dispatch to different DC app access success stats. */
    if (ZPN_BROKER_IS_PUBLIC() && mtunnel->state == zbms_complete) {
        zpn_broker_dispatcher_stats_app_access_success_update(mtunnel, req->g_dsp);
    }

    mtunnel_locked_state_machine(mtunnel);

    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
    return ZPN_RESULT_NO_ERROR;
}

void zpn_broker_client_mtunnel_ack_err_for_fqdn_serv_ip(struct zpn_broker_mtunnel *mtunnel)
{
    int res = 0;
    struct zpn_tlv *tlv = zpn_broker_mtunnel_client_tlv(mtunnel);

    if (tlv) {
        ZPN_DEBUG_MTUNNEL("%s: Customer id:%" PRId64 " Sending broker client mtunnel ack for error:%s",
                                mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id),
                                 BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY);
        res = mtunnel_request_ack(mtunnel,
                                  zpn_tlv_conn_incarnation(tlv),
                                  mtunnel->log.c_tag,
                                  mtunnel->mtunnel_id,
                                  BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY,
                                  NULL);
        if (res != FOHH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "%s: Customer id:%" PRId64 " mtunnel request ack error tag_id: %d, error: %s, ret: %s",
                    mtunnel->mtunnel_id, ZPATH_GID_GET_CUSTOMER_GID(mtunnel->assistant_id), mtunnel->log.c_tag,
                     BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY, zpath_result_string(res));
        }
    } else {
        ZPN_DEBUG_MTUNNEL("%s: Received broker request ack, but no client state", mtunnel->mtunnel_id);
    }
}

static int zpn_broker_assistant_mtunnel_tag_pause_cb(void *argo_cookie_ptr,
                                                     void *argo_structure_cookie_ptr,
                                                     struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    struct zpn_mtunnel_tag_pause *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;

    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        char dump[800];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "Tag pause without a_state");
        return ZPN_RESULT_ERR;
    }

    tlv = a_state_get_tlv(a_state);

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do callback for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    if (a_state->tlv_state.remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_CRITICAL, "mtunnel_pause without tag_id");
        return ZPN_RESULT_ERR;
    }

    mtunnel = zpn_fohh_tlv_get_global_owner(&(a_state->tlv_state), req->tag_id);
    if (!mtunnel) {
        ZPN_LOG(AL_NOTICE, "Mtunnel_pause received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("Pausing mtunnel");

    mtunnel_lock(mtunnel);
    zpn_mconn_to_client_pause(&(mtunnel->client_tlv.mconn));
    mtunnel_unlock(mtunnel);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_mtunnel_tag_resume_cb(void *argo_cookie_ptr,
                                                      void *argo_structure_cookie_ptr,
                                                      struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    struct zpn_mtunnel_tag_resume *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;

    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        char dump[800];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "Tag resume without a_state");
        return ZPN_RESULT_ERR;
    }

    tlv = a_state_get_tlv(a_state);

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do callback for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    if (a_state->tlv_state.remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_CRITICAL, "mtunnel_pause without tag_id");
        return ZPN_RESULT_ERR;
    }

    mtunnel = zpn_fohh_tlv_get_global_owner(&(a_state->tlv_state), req->tag_id);
    if (!mtunnel) {
        ZPN_LOG(AL_NOTICE, "Mtunnel_pause received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_MCONN("Resuming mtunnel");

    mtunnel_lock(mtunnel);
    zpn_mconn_to_client_resume(&(mtunnel->client_tlv.mconn));
    mtunnel_unlock(mtunnel);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_tcp_info_report_control_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_tcp_info_report *req = object->base_structure_void;
    char dump[800];

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    if (asst) {
        asst->rx_tcp_info_report++;
        asst->auth_log.priv_ip = req->priv_ip;

        if (req->version) {
            if (asst->version) {
                ZPN_FREE(asst->version);
            }
            asst->version = ZPN_STRDUP(req->version, strlen(req->version));
            asst->auth_log.version = asst->version;
            asst->version_major = 0;
            asst->version_minor = 0;
            if (sscanf(asst->version, "%d.%d", &(asst->version_major), &(asst->version_minor)) != 2) {
                ZPN_LOG(AL_WARNING, "Could not pull assistant version from %s; assistant %ld", asst->version, (long)asst->assistant_gid_from_config);
            }
        } else {
            asst->version_major = 0;
            asst->version_minor = 0;
        }
        asst->tcp_info_ready = 1;

        /* Send the first auth log only when we have all the required info */
        if (!asst->first_auth_sent && asst->status_report_ready && asst->tcp_info_ready) {
            zpn_broker_assistant_control_conn_auth_log_send(asst, fohh_connection_connected);
            zpn_broker_assistant_auth_report_to_dispatcher(asst);
        }
    } else {
        ZPN_LOG(AL_ERROR, "Could not find assistant for %s", fohh_peer_cn(f_conn));
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * receive the details about connector's environment and store it in the auth log. This means every time the auth log
 * is sent by the broker to mgmt API, we will send the connector's environment too.
 */
static int zpn_broker_assistant_environment_report_cb(void*                 argo_cookie_ptr,
                                                      void*                 argo_structure_cookie_ptr,
                                                      struct argo_object*   object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_assistant *asst = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_asst_environment_report *msg = object->base_structure_void;
    if (NULL == asst) {
        goto done;
    }
    asst->rx_environment_report++;
    if (asst->sarge_version) {
        ZPN_FREE(asst->sarge_version);
    }
    asst->sarge_version = ZPN_STRDUP(msg->sarge_version, strlen(msg->sarge_version));
    asst->auth_log.sarge_version = asst->sarge_version;

done:
    return ZPN_RESULT_NO_ERROR;
}


static int zpn_broker_assistant_tcp_info_report_cb(void *argo_cookie_ptr,
                                                   void *argo_structure_cookie_ptr,
                                                   struct argo_object *object)
{
    // Ignoring TCP state sent from assistant, for the moment.
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tcp_info_report *req = object->base_structure_void;

    if (a_state) {
        if (req->version) {
            if (a_state->version) {
                ZPN_FREE(a_state->version);
            }
            a_state->version = ZPN_STRDUP(req->version, strlen(req->version));
            a_state->log.version = a_state->version;
        }
        a_state->log.priv_ip = req->priv_ip;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_zrdt_info_report_cb(void *argo_cookie_ptr,
                                                   void *argo_structure_cookie_ptr,
                                                   struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_zrdt_info_report *req = object->base_structure_void;

    if (a_state) {
        if (req->version) {
            if (a_state->version) {
                ZPN_FREE(a_state->version);
            }
            a_state->version = ZPN_STRDUP(req->version, strlen(req->version));
            a_state->log.version = a_state->version;
        }
        a_state->log.priv_ip = req->priv_ip;
    }
    return ZPN_RESULT_NO_ERROR;
}

#define _PSE_AC_FC_STATS_ENABLE_ 1
#ifdef _PSE_AC_FC_STATS_ENABLE_
static void zpn_broker_assistant_data_conn_monitor_log(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_tlv *fohh_tlv = cookie;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
    struct zpn_broker_assistant_fohh_state *a_state = fohh_connection_get_dynamic_cookie(f_conn);
    char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
    char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
    char                                log_str[4000] = { '\0' };
    char *start;
    char *end;
    time_t                              t;
    struct tm                          lt;
    char                                tm_str[128] = "";

    start = log_str;
    end = log_str + sizeof(log_str);

    if (a_state->tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_tlv fohh_tlv2;
        struct fohh_connection  *f_conn2;

        if (NULL == zpn_mconn_fohh_tlv_get_conn(&(a_state->tlv_state))) {
            return;
        }

        /* Attention!! tlv_state is not deep copied here.. Do it as appropriate */
        ZPATH_MUTEX_LOCK(&(a_state->tlv_state.lock), __FILE__, __LINE__);
        fohh_tlv2 = a_state->tlv_state;
        f_conn2 = zpn_mconn_fohh_tlv_get_conn(&a_state->tlv_state);
        struct fohh_connection_stats *fconn_stats = &f_conn->stats;

        fohh_connection_max_rtt_to_string(f_conn2, max_rtt_us_str, sizeof(max_rtt_us_str));

        start += sxprintf(start, end, "Private Broker assistant data connection (FOHH),%"PRId64":%s", a_state->assitant_gid,
                          fohh_description(f_conn2));

        sxprintf(start, end, ", uptime %s%s, "
                 " fohh_thread %d, "
                 " to assistant %ld bytes, "
                 " from assistant %ld bytes. "
                 "Detail[rx %ld, "
                 "peer_tx %ld, tx %ld, tx_drop %ld, tx_limit %ld, tx_limit_update %ld, r_rx %ld, r_tx_limit %ld, "
                 "tx_wnd_update %ld, rx_wnd_update %ld, tx_blocked %ld, enq %ld, deq %ld , data_write_blocked %ld, "
                 "data_write_blocked_fohh %ld, data_write_blocked_evbuf %ld, fohh_fc_blocked %ld, fohh_fc_blocked_time %ld, fohh_fc_block_max_time %ld, "
                 "fohh_fc_block_tot_time %ld, window_update_delta_time %ld, max_window_update_delta_time %ld, "
                 "hop_latency %ld, max_hop_latency %ld, fohh_pipeline_latency %lu, fohh_pipeline_latency_max %lu, "
                 "rx_udp_data_dropped_frame_error = %ld, rx_udp_data_dropped_tx_buf_full = %ld, rx_icmp_error_data_dropped = %ld, rx_icmp6_error_data_dropped = %ld, "
                 "bufferevent_in_len = %"PRIu64", bufferevent_in_len_max = %"PRIu64", tlv_read_buf_len = %"PRIu64", tlv_read_buf_len_max = %"PRIu64", "
                 "bufferevent_out_len = %"PRIu64", bufferevent_out_len_max = %"PRIu64", raw_tlv_buffer_len = %"PRIu64", raw_tlv_buffer_len_max = %"PRIu64", "
                 "raw_tlv_buffer_enq = %"PRIu64", raw_tlv_buffer_deq = %"PRIu64"]",
                 zpn_tlv_get_uptime_str(&(fohh_tlv2.tlv), uptime_str, sizeof(uptime_str)),
                 (fohh_get_state(zpn_mconn_fohh_tlv_get_conn(&fohh_tlv2)) == fohh_connection_connected) ? max_rtt_us_str : "",
                 fohh_connection_get_thread_id(f_conn),
                 (long)fohh_tlv2.tx_data,
                 (long)fohh_tlv2.rx_data,
                 (long)fohh_tlv2.rx_data,
                 (long)fohh_tlv2.peer_tx_data,
                 (long)fohh_tlv2.tx_data,
                 (long)fohh_tlv2.tx_data_drop,
                 (long)fohh_tlv2.tx_limit,
                 (long)fohh_tlv2.tx_limit_update_us,
                 (long)fohh_tlv2.remote_rx_data,
                 (long)fohh_tlv2.remote_tx_limit,
                 (long)fohh_tlv2.remote_rx_data_change_us,
                 (long)fohh_tlv2.last_wnd_update_us,
                 (long)fohh_tlv2.fc_blocked_timestamp,
                 (long)fohh_tlv2.enq_bytes,
                 (long)fohh_tlv2.deq_bytes,
                 (long)fohh_tlv2.fohh_data_write_blocked,
                 (long)fohh_tlv2.fohh_data_write_fohh_blocked,
                 (long)fohh_tlv2.fohh_data_write_evbuf_blocked,
                 (long)fohh_tlv2.fohh_fc_blocked,
                 (long)fohh_tlv2.fohh_fc_blocked_time,
                 (long)fohh_tlv2.max_fohh_fc_blocked_time,
                 (long)fohh_tlv2.tot_fohh_fc_blocked_time,
                 (long)fohh_tlv2.window_update_delta_time,
                 (long)fohh_tlv2.max_window_update_delta_time,
                 (long)fohh_tlv2.hop_latency,
                 (long)fohh_tlv2.max_hop_latency,
                 (unsigned long)fohh_tlv2.pipeline_latency[latency_inspection_stage_last],
                 (unsigned long)fohh_tlv2.pipeline_latency_max,
                 (long)fohh_tlv->rx_udp_data_dropped_frame_error, (long)fohh_tlv->rx_udp_data_dropped_tx_buf_full,
                 (long)fohh_tlv->rx_icmp_error_data_dropped, (long)fohh_tlv->rx_icmp6_error_data_dropped,
                 fconn_stats->bufferevent_in_len, fconn_stats->bufferevent_in_len_max, fconn_stats->tlv_read_buf_len,
                 fconn_stats->tlv_read_buf_len_max, fconn_stats->bufferevent_out_len, fconn_stats->bufferevent_out_len_max,
                 fconn_stats->raw_tlv_buffer_len, fconn_stats->raw_tlv_buffer_len_max, fconn_stats->raw_tlv_buffer_enq,
                 fconn_stats->raw_tlv_buffer_deq);
        ZPATH_MUTEX_UNLOCK(&(a_state->tlv_state.lock), __FILE__, __LINE__);
    } else {

        struct zpn_zrdt_tlv  *zrdt_tlv;
        struct zrdt_conn_stats conn_stats;

        if (NULL == zpn_mconn_zrdt_tlv_get_conn(&(a_state->zrdt_tlv_state))) {
            return;
        }

        zrdt_tlv = &(a_state->zrdt_tlv_state);
        zrdt_get_conn_stats(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv), &conn_stats);

        snprintf(max_rtt_us_str, sizeof(max_rtt_us_str), ", rtt: %"PRIu64" us", conn_stats.max_rtt_us);

        start += sxprintf(start, end, "Private Broker assistant data connection (DTLS),%" PRId64 ":%s", a_state->assitant_gid,
                          zpn_tlv_description(&(zrdt_tlv->tlv)));

        sxprintf(start, end, ", uptime %s%s, active_mtunnels %" PRId64 ", "
                 "bytes, from broker %" PRId64 " bytes. Detail[rx_pkt %ld, "
                 "tx_pkt %ld, lloss %ld, rloss = %ld]",
                 zpn_tlv_get_uptime_str(&(zrdt_tlv->tlv), uptime_str, sizeof(uptime_str)),
                 max_rtt_us_str,
                 zrdt_tlv->tx_data,
                 zrdt_tlv->rx_data,
                 (long)conn_stats.packets_recv,
                 (long)conn_stats.packets_sent,
                 (long)conn_stats.local_loss,
                 (long)conn_stats.peer_loss);
    }

    time(&t);
    localtime_r(&t, &lt);
    strftime(tm_str, sizeof(tm_str), "%c", &lt);

    ZPN_LOG(AL_NOTICE, "[%s]: FC:%s", tm_str, log_str);

}
#endif

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL seconds */
static void zpn_broker_assistant_data_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_fohh_tlv *fohh_tlv = cookie;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);
    struct zpn_broker_assistant_fohh_state *a_state = fohh_connection_get_dynamic_cookie(f_conn);
    int res;

    if (a_state) {
        a_state->monitor_count++;

        if ((a_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
            zpn_mconn_fohh_tlv_fc_monitor(fohh_tlv, 1);
        }

        /* Drop connection when broker is shutting down. */
        if (disconnect_assistants) {
            ZPN_LOG(AL_INFO, "%s: Disconnect assistant data connection due to %s",
                    fohh_description(f_conn),
                    disconnect_assistants_reason ? disconnect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
            fohh_connection_delete(f_conn, disconnect_assistants_reason);
            return;
        }

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(a_state->assitant_gid);
        if (ZPN_BROKER_IS_PUBLIC() && customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), a_state->assitant_gid, customer_gid);
                /* Kill the connection */
                fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        // Drop assistants on disabled PB.
        if (ZPN_BROKER_IS_PRIVATE() && !g_broker_common_cfg->private_broker.enabled) {
            fohh_connection_delete(f_conn, ZPN_ERR_AUTH_SERVICE_DISABLED);
            return;
        }

        if (fohh_tlv->fc_blocked_timestamp_initial) {
            if (epoch_us() - fohh_tlv->fc_blocked_timestamp_initial > FOHH_TLV_BLOCKED_RESET_US) {
                ZPN_LOG(AL_ERROR, "Reset connection to %s, as it is flow control blocked for more than %ld seconds",
                        fohh_description(f_conn), (long)FOHH_TLV_BLOCKED_RESET_US/1000000);
                /* Kill the connection */
                fohh_connection_delete(f_conn, FOHH_CLOSE_REASON_BRK_DATA_CONN_FLOW_CONTROL);
                return;
            }
        }

        if ((a_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
            res = zpn_broker_assistant_auth_log(f_conn,
                                                &(a_state->log),
                                                a_state->assitant_gid,
                                                ZPN_ASSISTANT_BROKER_DATA,
                                                fohh_get_state(f_conn));
            if (res) {
                ZPN_LOG(AL_WARNING, "%s: Could not generate data auth log", fohh_peer_cn(f_conn));
            }
        }


#ifdef _PSE_AC_FC_STATS_ENABLE_

        if (ZPN_BROKER_IS_PRIVATE()) {

            if ((a_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {

                /* Trigger fc monitor log every 4 mins */
                zpn_broker_assistant_data_conn_monitor_log(sock, flags, cookie);
            }
#endif

        }
    } else {
        ZPN_LOG(AL_NOTICE, "Assistant data conn monitor cb has no a_state");
    }
}

static void zpn_broker_assistant_data_conn_zrdt_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_zrdt_tlv *zrdt_tlv = cookie;
    struct zpn_broker_assistant_fohh_state *a_state = zrdt_conn_get_dynamic_cookie(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
    int res;

    if (a_state) {
        a_state->monitor_count++;

        /* Drop connection when broker is shutting down. */
        if (disconnect_assistants) {
            struct zdtls_session *z_sess = zrdt_conn_get_datagram_tx_cookie(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
            if (z_sess) {
                ZPN_LOG(AL_INFO, "%s: disconnect assistant data connection due to %s",
                            zpn_tlv_peer_cn(&(zrdt_tlv->tlv)),
                            disconnect_assistants_reason ? disconnect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
                res = zdtls_session_close(z_sess);
            }
            return;
        }

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(a_state->assitant_gid);
        if (ZPN_BROKER_IS_PUBLIC() && customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                /* Kill the connection */
                struct zdtls_session *z_sess = zrdt_conn_get_datagram_tx_cookie(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
                if (z_sess) {
                    ZPN_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                                    zdtls_description(z_sess), a_state->assitant_gid, customer_gid);
                    res = zdtls_session_close(z_sess);
                }
                return;
            }
        }

        // Drop assistants on disabled PB.
        if (ZPN_BROKER_IS_PRIVATE() && !g_broker_common_cfg->private_broker.enabled) {
            struct zdtls_session *z_sess;

            z_sess = zrdt_conn_get_datagram_tx_cookie(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv));
            if (z_sess) {
                res = zdtls_session_close(z_sess);
            }
            return;
        }

        if (a_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT == 0) {
            res = zpn_broker_assistant_auth_log_zrdt(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv),
                                                    &(a_state->log),
                                                    a_state->assitant_gid,
                                                    ZPN_ASSISTANT_BROKER_DATA,
                                                    zrdt_conn_get_status(zpn_mconn_zrdt_tlv_get_conn(zrdt_tlv)));
            if (res) {
                ZPN_LOG(AL_WARNING, "%s: Could not generate data auth log", zpn_tlv_peer_cn(&(zrdt_tlv->tlv)));
            }
        }
    } else {
        ZPN_LOG(AL_NOTICE, "zrdt conn monitor cb has no a_state");
    }
}

static void zpn_broker_assistant_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct connected_assistant *asst = cookie;

    if (asst) {
        asst->monitor_count++;

        /* Drop connection when broker is shutting down. */
        if (disconnect_assistants) {
            ZPN_LOG(AL_INFO, "%s: Disconnect assistant control connection due to %s",
                    fohh_description(asst->f_conn),
                    disconnect_assistants_reason ? disconnect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
            fohh_connection_delete(asst->f_conn, disconnect_assistants_reason);
            return;
        }

        if (ZPN_BROKER_IS_PUBLIC() && asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), fohh_peer_get_id(asst->f_conn), asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (redirect_assistants &&
            (asst->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0) {
            ZPN_LOG(AL_INFO, "%s: Redirect assistant control connection due to %s",
                    fohh_description(asst->f_conn),
                    redirect_assistants_reason ? redirect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
            zpn_broker_assistant_conn_redirect(asst->f_conn);

            if (zpn_is_broker_auth_log_redirect_status_feature_enabled() && zpn_is_redirect_reason_maintanence(redirect_assistants_reason)) {
                asst->is_redirect_sent = 1;
            }
        }

        /*
         * Here we check the state of connected assistant's scope.
         * If the scope is disabled, we set the auth_status to UNSUCCESSFUL, else we set it to SUCCESSFUL.
         *
         * If this instance is a public broker, we do nothing. We allow the control connection to exist,
         * and rely on rejection of app access during policy evaluation. The disabled scope's user is anyways
         * demoted to default scope and thus will be unable to access the disabled scope's app
         *
         * We do not need to check for DTA feature being enabled or not here, because of the same reason as above.
         * If DTA is disabled, then all assistants will also be disabled and so, the scope specific users will
         * transition to default scope and will be prevented at the policy evaluation stage when trying to
         * access scope specific application.
         *
         * Ideal case would be if, when we mark the auth state as unsuccessful, we should also delete/invalidate
         * the app routing information and app health information from the embedded dispatcher. This is maybe
         * something we can look at in the next iteration. For now this should be ok.
         */
        if(ZPN_BROKER_IS_PRIVATE() && !zpn_is_dr_mode_active() && !is_scope_default(asst->scope_gid)) {

            int is_scope_enabled = 0;
            int result = ZPN_RESULT_NO_ERROR;
            enum A2PBAuth old_status = asst->scope_auth_status;

            result = zpn_is_scope_enabled(asst->scope_gid, &is_scope_enabled);
            if((result != ZPN_RESULT_NO_ERROR) || !is_scope_enabled ||
                    (!is_scope_default(g_broker_common_cfg->private_broker.scope_id) && (asst->scope_gid != g_broker_common_cfg->private_broker.scope_id))) {
                asst->scope_auth_status = AUTH_UNSUCCESSFUL;
            } else {
                asst->scope_auth_status = AUTH_SUCCESSFUL;
            }

            if(asst->scope_auth_status != old_status) {
                /*
                 * We log a message only when there is a change in status. This will limit the number of log messages being generated.
                 */

                ZPN_LOG(AL_DEBUG, "a2pb scope auth status changed. old_status:\"%s\" new_status:\"%s\". customer_gid:%"PRId64" pbroker[gid:%"PRId64" scope_gid:%"PRId64"] "
                                  "assistant[gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d]",
                                  (old_status == AUTH_SUCCESSFUL) ? "auth-successful" : "auth-unsuccessful",
                                  (asst->scope_auth_status == AUTH_SUCCESSFUL) ? "auth-successful" : "auth-unsuccessful",
                                  ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.scope_id),
                                  g_broker_common_cfg->private_broker.broker_id,
                                  g_broker_common_cfg->private_broker.scope_id,
                                  asst->scope_gid,
                                  asst->assistant_gid_from_config,
                                  is_scope_enabled);
            }
        }

        // Drop assistants on disabled PB.
        if (ZPN_BROKER_IS_PRIVATE() && !g_broker_common_cfg->private_broker.enabled) {
            fohh_connection_delete(asst->f_conn, ZPN_ERR_AUTH_SERVICE_DISABLED);
            return;
        }

        if (((asst->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) &&
            (asst->status_report_ready && asst->tcp_info_ready)) {
            zpn_broker_assistant_update_asst_lat_lon(asst);
            zpn_broker_assistant_control_conn_auth_log_send(asst, fohh_get_state(asst->f_conn));
            /*
             * send zpn_ast_auth_report to dispatcher from here which is every 4 min
             * when config override value is disabled
             */
            if (!g_send_ast_auth_report_every_1_min) {
                zpn_broker_assistant_auth_report_to_dispatcher(asst);
            }
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Assistant control conn monitor cb has no c_state");
    }
}

static void zpn_broker_assistant_config_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct connected_assistant_config_fohh_state *asst = cookie;
    char conn_name_str[20];

    if (asst) {

        switch (asst->config_connection_type)
        {
            case config_conn_type_config:
                snprintf(conn_name_str, sizeof(conn_name_str), "%s", "config");
                break;
            case config_conn_type_override:
                snprintf(conn_name_str, sizeof(conn_name_str), "%s", "config override");
                break;
            case config_conn_type_np:
                snprintf(conn_name_str, sizeof(conn_name_str), "%s", "np config");
                break;
        }
        asst->monitor_count++;
        /* Drop connection when broker is shutting down. */
        if (disconnect_assistants) {
            ZPN_LOG(AL_INFO, "%s: Disconnect assistant %s connection due to %s",
                    fohh_description(asst->f_conn),
                    conn_name_str,
                    disconnect_assistants_reason ? disconnect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
            fohh_connection_delete(asst->f_conn, disconnect_assistants_reason);
            return;
        }

        if (asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), fohh_peer_get_id(asst->f_conn), asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (redirect_assistants &&
            (asst->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0) {
            ZPN_LOG(AL_INFO, "%s: Redirect assistant %s connection due to %s",
                    fohh_description(asst->f_conn),
                    conn_name_str,
                    redirect_assistants_reason ? redirect_assistants_reason : FOHH_CLOSE_REASON_UPGRADE);
            zpn_broker_assistant_config_conn_redirect(asst->f_conn);
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Assistant config conn monitor cb has no state");
    }
}

static int zpn_broker_assistant_fohh_tlv_window_update_cb(void *argo_cookie_ptr,
                                                          void *argo_structure_cookie_ptr,
                                                          struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    struct zpn_fohh_tlv *fohh_tlv;
    struct zpn_fohh_tlv_window_update *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    char dump[800];

    int64_t now_us = epoch_us();

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "TLV window update without a_state");
        return ZPN_RESULT_ERR;
    }
    tlv = a_state_get_tlv(a_state);
    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", zpn_tlv_description(tlv), dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    fohh_tlv = &a_state->tlv_state;
    ZPN_DEBUG_BROKER_ASSISTANT("Window update received %s with tag_id: %d tx_limit: %"PRId64" rx_data: %"PRId64,
                               zpn_tlv_description(tlv), req->tag_id, req->tx_limit, req->rx_data);
    if (!req->tag_id) {
        /* For overall FOHH connection */
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        if (req->tx_limit > fohh_tlv->tx_limit) {
            fohh_tlv->tx_limit = req->tx_limit;
            fohh_tlv->tx_limit_update_us = now_us;
        }
        if (fohh_tlv->remote_rx_data != req->rx_data) {
            fohh_tlv->remote_rx_data = req->rx_data;
            fohh_tlv->remote_rx_data_change_us = now_us;
        }
        if (req->tx_limit) {
            fohh_tlv->remote_fc_status = flow_ctrl_enabled;
        } else {
            fohh_tlv->remote_fc_status = flow_ctrl_disabled;
        }
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        zpn_fohh_tlv_unblock_cb(fohh_tlv);
    } else {
        /* For an mconn inside FOHH connection */
        mtunnel = zpn_fohh_tlv_get_global_owner(&(a_state->tlv_state), req->tag_id);
        if (!mtunnel) {
            ZPN_LOG(AL_INFO, "Window update received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }
        /* If we decided to disable flow control locally for this mconn ignore the window update from the peer*/
        mtunnel_lock(mtunnel);
        if (mtunnel->assistant_tlv.remote_fc_status == flow_ctrl_disabled) {
            mtunnel_unlock(mtunnel);
            return ZPN_RESULT_NO_ERROR;
        }

        mtunnel->assistant_tlv.remote_fc_status = flow_ctrl_enabled;
        mtunnel->assistant_tlv.tx_limit = req->tx_limit;
        mtunnel->assistant_tlv.last_wnd_rx_update_us = now_us;
        if (mtunnel->assistant_tlv.remote_rx_data != req->rx_data) {
            mtunnel->assistant_tlv.remote_rx_data = req->rx_data;
            mtunnel->assistant_tlv.remote_rx_data_change_us = now_us;
        }
        zpn_client_drain_tx_data(&(mtunnel->assistant_tlv.mconn));
        mtunnel_unlock(mtunnel);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_fohh_tlv_window_update_batch_cb(void *argo_cookie_ptr,
                                                                void *argo_structure_cookie_ptr,
                                                                struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    struct zpn_fohh_tlv *fohh_tlv;
    struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    char dump[800];
    int64_t now_us = epoch_us();

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "Batch TLV window update without a_state");
        return ZPN_RESULT_ERR;
    }

    tlv = a_state_get_tlv(a_state);

    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", zpn_tlv_description(tlv), dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only do window update for fohh tlv connection */
        return ZPN_RESULT_NO_ERROR;
    }

    fohh_tlv = &a_state->tlv_state;
    ZPN_DEBUG_BROKER_ASSISTANT("Batch window update; tag count: %d", zpn_fohh_tlv_window_update_batch->tag_id_count);
    for (int idx = 0; idx < zpn_fohh_tlv_window_update_batch->tag_id_count; idx++) {
      ZPN_DEBUG_BROKER_ASSISTANT("Batch window update received for tag_id: %d; tx_limit: %"PRId64" rx_data: %"PRId64,
                                 zpn_fohh_tlv_window_update_batch->tag_id[idx],
                                 zpn_fohh_tlv_window_update_batch->tx_limit[idx],
                                 zpn_fohh_tlv_window_update_batch->rx_data[idx]);
      if (!zpn_fohh_tlv_window_update_batch->tag_id[idx]) {
          ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx] > fohh_tlv->tx_limit) {
            fohh_tlv->tx_limit = zpn_fohh_tlv_window_update_batch->tx_limit[idx];
            fohh_tlv->tx_limit_update_us = now_us;
          }
          if (fohh_tlv->remote_rx_data != zpn_fohh_tlv_window_update_batch->rx_data[idx]) {
            fohh_tlv->remote_rx_data = zpn_fohh_tlv_window_update_batch->rx_data[idx];
            fohh_tlv->remote_rx_data_change_us = now_us;
          }
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx]) {
            fohh_tlv->remote_fc_status = flow_ctrl_enabled;
          } else {
            fohh_tlv->remote_fc_status = flow_ctrl_disabled;
          }
          ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
          zpn_fohh_tlv_unblock_cb(fohh_tlv);
      } else {
          mtunnel = zpn_fohh_tlv_get_global_owner(&(a_state->tlv_state), zpn_fohh_tlv_window_update_batch->tag_id[idx]);
          if (!mtunnel) {
            ZPN_LOG(AL_INFO, "Window update batch received for mtunnel tag_id = %d that isn't found",
                    (int)zpn_fohh_tlv_window_update_batch->tag_id[idx]);
            continue;;
          }
          /* If we decided to disable flow control locally for this mconn ignore the window update from the peer*/
          mtunnel_lock(mtunnel);
          if (mtunnel->assistant_tlv.remote_fc_status == flow_ctrl_disabled) {
            mtunnel_unlock(mtunnel);
            continue;
          }
          mtunnel->assistant_tlv.remote_fc_status = flow_ctrl_enabled;
          mtunnel->assistant_tlv.tx_limit = zpn_fohh_tlv_window_update_batch->tx_limit[idx];
          mtunnel->assistant_tlv.last_wnd_rx_update_us = now_us;
          if (mtunnel->assistant_tlv.remote_rx_data != zpn_fohh_tlv_window_update_batch->rx_data[idx]) {
            mtunnel->assistant_tlv.remote_rx_data = zpn_fohh_tlv_window_update_batch->rx_data[idx];
            mtunnel->assistant_tlv.remote_rx_data_change_us = now_us;
          }
          zpn_client_drain_tx_data(&(mtunnel->assistant_tlv.mconn));
          mtunnel_unlock(mtunnel);
      }
    } // for
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_data_state_cb(void *argo_cookie_ptr,
                                              void *argo_structure_cookie_ptr,
                                              struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_asst_state *req = object->base_structure_void;
    char dump[800];
    struct zpn_tlv *tlv;

    tlv = a_state_get_tlv(a_state);

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", zpn_tlv_description(tlv),dump);
        }
    }

    zpn_broker_assistant_store_capabilities(&a_state->capability, req->capabilities, req->capabilities_count);
    a_state->disabled = req->disabled;

    int is_qbr_insights_feature_enabled = zpn_broker_assistant_is_qbr_insights_feature_enabled(ZPATH_GID_GET_CUSTOMER_GID(a_state->assitant_gid));

    if (req->public_cloud && is_qbr_insights_feature_enabled) {
        if (!a_state->public_cloud || strcmp(a_state->public_cloud, req->public_cloud)) {
            if (a_state->public_cloud) {
                ZPN_FREE(a_state->public_cloud);
            }
            a_state->public_cloud = ZPN_STRDUP(req->public_cloud, strlen(req->public_cloud));
            a_state->log.g_ast_public_cloud = a_state->public_cloud;
        }
    } else {
        if (a_state->public_cloud) {
            ZPN_FREE(a_state->public_cloud);
        }
        a_state->log.g_ast_public_cloud = a_state->public_cloud = NULL;
    }

    if (req->private_cloud && is_qbr_insights_feature_enabled) {
        if (!a_state->private_cloud || strcmp(a_state->private_cloud, req->private_cloud)) {
            if (a_state->private_cloud) {
                ZPN_FREE(a_state->private_cloud);
            }
            a_state->private_cloud = ZPN_STRDUP(req->private_cloud, strlen(req->private_cloud));
            a_state->log.g_ast_private_cloud = a_state->private_cloud;
        }
    } else {
        if (a_state->private_cloud) {
            ZPN_FREE(a_state->private_cloud);
        }
        a_state->log.g_ast_private_cloud = a_state->private_cloud = NULL;
    }

    if (req->region_id && is_qbr_insights_feature_enabled) {
        if (!a_state->region_id || strcmp(a_state->region_id, req->region_id)) {
            if (a_state->region_id) {
                ZPN_FREE(a_state->region_id);
            }
            a_state->region_id = ZPN_STRDUP(req->region_id, strlen(req->region_id));
            a_state->log.g_ast_region_id = a_state->region_id;
        }
    } else {
        if (a_state->region_id) {
            ZPN_FREE(a_state->region_id);
        }
        a_state->log.g_ast_region_id = a_state->region_id = NULL;
    }

    zpn_update_appc_location_info(&(a_state->log.pub_ip), &(a_state->log));

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_data_mconn_stats_update_cb(void *argo_cookie_ptr,
                                                                   void *argo_structure_cookie_ptr,
                                                                   struct argo_object *object)
{
    struct zpn_broker_assistant_fohh_state *a_state = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv;
    struct zpn_mtunnel_data_mconn_stats *req = object->base_structure_void;
    struct zpn_broker_mtunnel *mtunnel;
    char dump[800];

    ZPN_DEBUG_BROKER_ASSISTANT("track perf data_mconn_stats_update_cb received for mtunnel tag_id = %d ", (int)req->tag_id);

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "track perf data_mconn_stats_update_cb update without a_state");
        return ZPN_RESULT_ERR;
    }

    tlv = a_state_get_tlv(a_state);

    if (zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", zpn_tlv_description(tlv),dump);
        }
    }

    if (tlv->type != zpn_fohh_tlv) {
        /* Only for fohh connection */
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->tag_id) {
        /* For overall FOHH connection - not applicable */
    } else {
        /* For an mconn inside FOHH connection */
        mtunnel = zpn_fohh_tlv_get_global_owner(&(a_state->tlv_state), req->tag_id);
        if (!mtunnel) {
            ZPN_LOG(AL_ERROR, "track perf data_mconn_stats_update_cb update received for mtunnel tag_id = %d that isn't found", (int)req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }

        int mconn_track_perf_stats_level = mtunnel->mconn_track_perf_stats_level;
        if(mconn_track_perf_stats_level) {

            mtunnel_lock(mtunnel);

            struct zpn_trans_log *log = &(mtunnel->log);

            if(mconn_track_perf_stats_level>=1) {
                //update asst to server stats
                log->a_s_tx_peer_rx_data_hist[0] = req->as_tx_peer_rx_data_hist[0];
                log->a_s_tx_peer_rx_data_hist[1] = req->as_tx_peer_rx_data_hist[1];
                log->a_s_tx_peer_rx_data_hist[2] = req->as_tx_peer_rx_data_hist[2];
                log->a_s_tx_peer_rx_data_hist[3] = req->as_tx_peer_rx_data_hist[3];
                log->a_s_tx_peer_rx_data_hist[4] = req->as_tx_peer_rx_data_hist[4];

                log->a_s_tx_peer_rx_data_max_us = req->as_tx_peer_rx_data_max_us;
                log->a_s_tx_peer_rx_data_max_epoch_us = req->as_tx_peer_rx_data_max_epoch_us;

                // update ast to broker stats
                log->a_b_tx_peer_rx_data_hist[0] = req->ab_tx_peer_rx_data_hist[0];
                log->a_b_tx_peer_rx_data_hist[1] = req->ab_tx_peer_rx_data_hist[1];
                log->a_b_tx_peer_rx_data_hist[2] = req->ab_tx_peer_rx_data_hist[2];
                log->a_b_tx_peer_rx_data_hist[3] = req->ab_tx_peer_rx_data_hist[3];
                log->a_b_tx_peer_rx_data_hist[4] = req->ab_tx_peer_rx_data_hist[4];

                log->a_b_tx_peer_rx_data_max_us = req->ab_tx_peer_rx_data_max_us;
                log->a_b_tx_peer_rx_data_max_epoch_us = req->ab_tx_peer_rx_data_max_epoch_us;
            }

            if(mconn_track_perf_stats_level>=2) {
                //update asst to server stats
                log->a_s_rx_diff_rx_data_hist[0] = req->as_rx_diff_rx_data_hist[0];
                log->a_s_rx_diff_rx_data_hist[1] = req->as_rx_diff_rx_data_hist[1];
                log->a_s_rx_diff_rx_data_hist[2] = req->as_rx_diff_rx_data_hist[2];
                log->a_s_rx_diff_rx_data_hist[3] = req->as_rx_diff_rx_data_hist[3];
                log->a_s_rx_diff_rx_data_hist[4] = req->as_rx_diff_rx_data_hist[4];

                // update ast to broker stats
                log->a_b_rx_diff_rx_data_hist[0] = req->ab_rx_diff_rx_data_hist[0];
                log->a_b_rx_diff_rx_data_hist[1] = req->ab_rx_diff_rx_data_hist[1];
                log->a_b_rx_diff_rx_data_hist[2] = req->ab_rx_diff_rx_data_hist[2];
                log->a_b_rx_diff_rx_data_hist[3] = req->ab_rx_diff_rx_data_hist[3];
                log->a_b_rx_diff_rx_data_hist[4] = req->ab_rx_diff_rx_data_hist[4];
            }

            if(mconn_track_perf_stats_level>=3) {
                // update ast to broker stats
                log->a_b_tx_peer_rx_data_max_cnt = req->ab_tx_peer_rx_data_max_cnt;
                log->a_b_tx_peer_rx_data_max_unblock_cnt = req->ab_tx_peer_rx_data_max_unblock_cnt;
                log->a_b_tx_data_unblock_max_us = req->ab_tx_data_unblock_max_us;
                log->a_b_tx_data_unblock_max_cnt = req->ab_tx_data_unblock_max_cnt;
                log->a_b_tx_data_unblock_tot_us = req->ab_tx_data_unblock_tot_us;
             }

            mtunnel_unlock(mtunnel);
        }
        ZPN_DEBUG_BROKER_ASSISTANT("track perf data_mconn_stats_update_cb track_perf: %d update rx for mtunnel tag_id = %d with as_tx_peer_rx_data_max_us=%d , ab_tx_peer_rx_data_max_us=%d ",
                                   mconn_track_perf_stats_level, (int)req->tag_id,
                                   req->as_tx_peer_rx_data_max_us, req->ab_tx_peer_rx_data_max_us);

    }

    return ZPN_RESULT_NO_ERROR;
}

static void data_conn_version_callback(struct fohh_connection *connection,
                                       void *cookie)
{
    struct zpn_broker_assistant_fohh_state *a_state = fohh_connection_get_dynamic_cookie(connection);

    if (!a_state) {
        return;
    }

    zpn_send_zpn_fohh_window_update(connection,
                                    fohh_connection_incarnation(connection),
                                    0,
                                    flow_control_enabled ? fohh_tlv_window_size : 0,
                                    0);
    a_state->tlv_state.last_wnd_update_us = epoch_us();
    a_state->tlv_state.remote_tx_limit = fohh_tlv_window_size;
}

static int zpn_broker_assistant_register_rpc_callbacks(struct zpn_broker_assistant_fohh_state *a_state)
{
    struct argo_state *argo;
    struct zpn_tlv *tlv = a_state_get_tlv(a_state);
    int res;

    if (a_state->tlv_type == zpn_fohh_tlv) {
        argo = fohh_argo_get_rx(zpn_mconn_fohh_tlv_get_conn(&(a_state->tlv_state)));
    } else {
        struct zpn_zrdt_argo_state *argo_state;

        argo_state = zrdt_get_msg_codec_state(a_state->zrdt_tlv_state.msg_stream);
        argo = argo_state->rx_argo;
    }

    /* Register zpn_bind_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_bind_description, zpn_broker_mtunnel_bind_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_bind for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_broker_request_ack */
    if ((res = argo_register_structure(argo, zpn_broker_request_ack_description, zpn_broker_request_ack_cb_from_data_conn, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_mtunnel_end */
    if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, zpn_broker_mtunnel_end_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_end for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_mtunnel_tag_pause_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_pause_description, zpn_broker_assistant_mtunnel_tag_pause_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_pause for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_mtunnel_tag_resume_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_resume_description, zpn_broker_assistant_mtunnel_tag_resume_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_resume for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_tcp_info_report */
    if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, zpn_broker_assistant_tcp_info_report_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_zrdt_info_report */
    if ((res = argo_register_structure(argo, zpn_zrdt_info_report_description, zpn_broker_assistant_zrdt_info_report_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_zrdt_info_report for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    /* Register zpn_fohh_tlv_window_update */
    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_description, zpn_broker_assistant_fohh_tlv_window_update_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_fohh_tlv_window_update for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_batch_description, zpn_broker_assistant_fohh_tlv_window_update_batch_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_fohh_tlv_window_update batch for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_asst_state_description, zpn_broker_assistant_data_state_cb, a_state) )) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_asst_state for broker data connection %s",
                zpn_tlv_description(tlv));
        return res;
    }

    /* Register  */
    if ((res = argo_register_structure(argo, zpn_mtunnel_data_mconn_stats_description, zpn_broker_assistant_data_mconn_stats_update_cb, a_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_data_mconn_stats_description for broker connection %s", zpn_tlv_description(tlv));
        return res;
    }
    if(!ZPN_BROKER_IS_PRIVATE()) {
        if ((res = argo_register_structure(argo, zpn_add_proxy_description, zpn_broker_add_proxy_cb, a_state))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_asst_state for proxy broker data connection %s",
                    zpn_tlv_description(tlv));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_delete_proxy_description, zpn_broker_delete_proxy_cb, a_state))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_asst_state for deleting proxy broker data connection %s",
                    zpn_tlv_description(tlv));
            return res;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_data_state_init(struct zpn_broker_assistant_fohh_state *a_state, enum zpn_tlv_type tlv_type)
{
    int res = ZPN_RESULT_NO_ERROR;

    a_state->tlv_type = tlv_type;
        /* Generate an ID for this client */
    res = RAND_bytes(&(a_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_LOG(AL_CRITICAL, "Crypto Random Generator not seeded!");
        return res;
    }
    base64_encode_binary(a_state->tunnel_id, a_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    a_state->log.tunnel_id = a_state->tunnel_id;
    return res;
}
#ifdef __linux__
static int zpn_broker_quickack_from_zpn_assistant_table(struct fohh_connection *conn, int read, int64_t assistant_group_id)
{
    int res;
    struct zpn_assistant_group *group;

    ZPN_DEBUG_BROKER_ASSISTANT("Fetch for zpn_assistant_group connection %s value %ld", fohh_description(conn), (long)assistant_group_id);
    res = zpn_assistant_group_get_by_gid(assistant_group_id, &group, NULL, NULL, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_BROKER_ASSISTANT("Asynchronous assistant_group fetch for gid = %ld",
                                (long)assistant_group_id);
        } else {
            ZPN_DEBUG_BROKER_ASSISTANT("Error fetching assistant_group: %ld: %s", (long)assistant_group_id,
                    zpn_result_string(res));
        }
    } else {
        if (read) {
            ZPN_DEBUG_BROKER_ASSISTANT("Quickack read enabled from zpn_assistant_group connection %s value %d",
                                 fohh_description(conn), group->tcp_quickack_read_assistant);
            return group->tcp_quickack_read_assistant;
        } else {
            ZPN_DEBUG_BROKER_ASSISTANT("Quickack enabled from zpn_assistant_group connection %s value %d",
                                fohh_description(conn), group->tcp_quickack_assistant);
            return group->tcp_quickack_assistant;
        }
    }
    return 0;
}
#endif

static int zpn_broker_quickack_enable (struct fohh_connection *conn, uint64_t assistant_group_id)
{
    uint32_t feature_bit = 0;
#ifdef __linux__
    int64_t customer_gid = 0;
    int64_t assistant_gid;

    assistant_gid = fohh_peer_get_id(conn);
    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(assistant_gid);

    if (zpn_broker_quickack_from_zpn_assistant_table(conn, 0, assistant_group_id)
        || zpn_broker_is_quickack_enabled_for_assistant(assistant_gid, assistant_group_id, customer_gid)) {
        int tmp_val = 1;
        if(!setsockopt(conn->sock, IPPROTO_TCP, TCP_QUICKACK, (void *)&tmp_val, sizeof(tmp_val))) {
            feature_bit |= TCP_QUICKACK_CONFIG;
            ZPN_DEBUG_BROKER_ASSISTANT("Quickack enabled for at broker listen/assistant accept %ld connection %s %ld",
                                 customer_gid, fohh_description(conn), (long)assistant_group_id);
        }
    }
    if (zpn_broker_quickack_from_zpn_assistant_table(conn, 1, assistant_group_id)
        || zpn_broker_is_quickack_read_enabled_for_assistant(assistant_gid, assistant_group_id, customer_gid)) {
        ZPN_DEBUG_BROKER_ASSISTANT("Quickack read enabled for broker listen/assistant accept %ld connection %s %ld",
                             customer_gid, fohh_description(conn), (long)assistant_group_id);
        fohh_peer_set_quickack_read_config(conn);
	feature_bit |= TCP_QUICKACK_CONFIG_READ;
    }
#endif
    return feature_bit;
}

static int zpn_broker_assistant_data_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie)
{
    struct zpn_broker_assistant_fohh_state *a_state = fohh_connection_get_dynamic_cookie(connection);
    int res;
    uint32_t feature;
    struct zpn_ast_auth_log *log;
    struct zpn_assistant *assistant = NULL;

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t asst_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        if (a_state) {
            /* Some old state was not released... It's probably better
             * to lose this memory rather than try to free it. We
             * don't know what module it was for. */
            ZPN_LOG(AL_WARNING, "Stale data was on connection");
        }

        /* Drop connection when customer is disabled */
        asst_id = fohh_peer_get_id(connection);
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
        if (ZPN_BROKER_IS_PUBLIC() && customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_ASSISTANT("%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        a_state = zpn_broker_a_state_alloc();
        if (!a_state) {
            return FOHH_RESULT_NO_MEMORY;
        }

        res = zpn_fohh_tlv_init(&(a_state->tlv_state), connection, a_state->incarnation);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: tlv_init failed: %s", fohh_description(connection), zpn_result_string(res));
            zpn_broker_a_state_free(a_state);
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_assistant_data_state_init(a_state, zpn_fohh_tlv);
        if (!res) {
            ZPN_LOG(AL_CRITICAL, "a_state init failed!");
            zpn_broker_a_state_free(a_state);
            return res;
        }

        if(zpn_fohh_flow_control_enhancement_enabled(customer_gid)) {
            ZPN_DEBUG_ASSISTANT("Broker assistant fohh flow control enhacement enabled %ld", (long) customer_gid);
            fohh_set_fohh_fc_enhacement_config(connection);
        }

        if(zpn_broker_syn_app_rtt_enabled(asst_id, a_state->assistant_group_gid, customer_gid)) {
          ZPN_DEBUG_ASSISTANT("Broker assistant syn status msg enabled agid %ld, aggid %ld, cgid %ld",
                             (long) asst_id, (long) a_state->assistant_group_gid, (long) customer_gid);
          fohh_set_syn_status_msg_config(connection);
	    }
        if(zpn_broker_pipeline_latency_trace_enabled(asst_id, a_state->assistant_group_gid, customer_gid)) {
          ZPN_DEBUG_ASSISTANT("Broker assistant pipeline latency trace enabled agid %ld, aggid %ld, cgid %ld",
                             (long) asst_id, (long) a_state->assistant_group_gid, (long) customer_gid);
          fohh_set_pipeline_latency_trace_config(connection);
        }

        a_state->assitant_gid = fohh_peer_get_id(connection);
        if (ZPN_BROKER_IS_PRIVATE()) zpn_pbroker_data_connection_stats_update_ast_state_hash(a_state, 1);

        if (a_state->assitant_gid) {
            struct zpn_assistantgroup_assistant_relation *ag_relation = NULL;
            size_t count = 1;

            zpn_assistantgroup_assistant_relation_get_by_assistant(a_state->assitant_gid,
                                                                   &ag_relation,
                                                                   &count,
                                                                   NULL,
                                                                   NULL,
                                                                   0);
            if (ag_relation) {
                a_state->assistant_group_gid = ag_relation->assistant_group_id;
            }

            /*
            * Fetch assistant from zpn_assistant table. Note:- this is
            * prefetched during authentication, so should be
            * available directly
            */
            res = zpn_assistant_get_by_id(a_state->assitant_gid,
                                          &assistant,
                                          NULL,
                                          NULL,
                                          0);
            if (res) {
                ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) a_state->assitant_gid,
                                     zpn_result_string(res));
            } else {
                a_state->log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
            }

        }
        ZPN_DEBUG_BROKER_ASSISTANT("The assistant group id %ld", (long)a_state->assistant_group_gid);
        if(zpn_broker_batched_mconn_window_updates_enabled(asst_id, a_state->assistant_group_gid, customer_gid)) {
          ZPN_DEBUG_ASSISTANT("Broker assistant batched window updates enabled agid %ld, aggid %ld, cgid %ld",
                              (long) asst_id, (long) a_state->assistant_group_gid, (long) customer_gid);
          fohh_set_batched_mconn_window_updates_config(connection);
        }

        if(zpn_broker_syn_app_rtt_enabled(asst_id, a_state->assistant_group_gid, customer_gid)) {
          ZPN_DEBUG_ASSISTANT("Broker assistant syn status msg enabled agid %ld, aggid %ld, cgid %ld, gid %ld",
                             (long) asst_id, (long) a_state->assistant_group_gid, (long) customer_gid,
                             (long) (ZPN_BROKER_GET_GID()));
          fohh_set_syn_status_msg_config(connection);
	    }
        if(zpn_broker_pipeline_latency_trace_enabled(asst_id, a_state->assistant_group_gid, customer_gid)) {
          ZPN_DEBUG_ASSISTANT("Broker assistant pipeline latency trace enabled agid %ld, aggid %ld, cgid %ld, gid %ld",
                             (long) asst_id, (long) a_state->assistant_group_gid, (long) customer_gid,
                             (long) (ZPN_BROKER_GET_GID()));
          fohh_set_pipeline_latency_trace_config(connection);
        }
        /* Check if the libevent low write watermark needs to be set */
        if(zpn_broker_is_low_write_watermark_enabled_for_customer(customer_gid)) {
            fohh_peer_set_libevent_low_write_watermark_config(connection);
        }
        feature = zpn_broker_quickack_enable(connection, a_state->assistant_group_gid);

        log = &(a_state->log);

        zpn_broker_add_quickack_to_authlog(log->features, feature);
        /* Save the state... */
        fohh_connection_set_dynamic_cookie(connection, a_state);

        zpn_broker_a_state_active_q_add(a_state);

        zpn_broker_assistant_register_rpc_callbacks(a_state);

        ZPN_LOG(AL_NOTICE, "%s: Assistant data connection UP", fohh_description(connection));

        /* Any other received message is an error. */
        zpn_mconn_fohh_tlv_add_drain_timer(connection, &(a_state->tlv_state));

        zpn_mconn_fohh_tlv_add_monitor_timer(connection, &(a_state->tlv_state), zpn_broker_assistant_data_conn_monitor_cb, ZPN_TUNNEL_MONITOR_INTERVAL_S, 0);

        zpn_broker_get_app_buffer_parameters();

        /* Log the successful control connection setup */
        res = zpn_broker_assistant_auth_log(connection,
                                            &(a_state->log),
                                            a_state->assitant_gid,
                                            ZPN_ASSISTANT_BROKER_DATA,
                                            state);
        if (res) {
            ZPN_LOG(AL_WARNING, "Assistant data connection auth log fail");
        }

        zpn_fohh_worker_assistant_connect_data_fohh(fohh_connection_get_thread_id(connection));

        /* Send initial window update to our peer */
        zpn_send_zpn_fohh_window_update(connection,
                                        fohh_connection_incarnation(connection),
                                        0,
                                        flow_control_enabled ? fohh_tlv_window_size : 0,
                                        0);
        a_state->tlv_state.last_wnd_update_us = epoch_us();
        a_state->tlv_state.remote_tx_limit = fohh_tlv_window_size;


        fohh_enable_direct_write(connection);

        char *cname = fohh_peer_cn(connection);
        if (cname) {
            if (a_state->cname) {
                ZPN_FREE(a_state->cname);
                a_state->cname = NULL;
            }
            a_state->cname = ZPN_STRDUP(cname, strlen(cname));
        } else {
            ZPN_LOG(AL_INFO, "Peer %s has no cname?", fohh_description(connection));
        }

        /* Turn off autotune between broker and connector */
        // fohh_set_autotune(connection, 1, 0.85, 0.9, 5, 16*1024);
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: Assistant data connection DOWN due to %s",
                fohh_description(connection), reason);


        if (a_state) {
            if (ZPN_BROKER_IS_PRIVATE()) zpn_pbroker_data_connection_stats_update_ast_state_hash(a_state, 0);

            a_state->log.close_reason = reason;

            res = zpn_broker_assistant_auth_log(connection,
                                                &(a_state->log),
                                                a_state->assitant_gid,
                                                ZPN_ASSISTANT_BROKER_DATA,
                                                state);

            if (res) {
                ZPN_LOG(AL_WARNING, "Assistant data connection auth log fail");
            }

            res = zpn_fohh_tlv_destroy(&(a_state->tlv_state), MT_CLOSED_TLS_CONN_GONE_AST_CLOSED);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Could not destroy tlv state", fohh_description(connection));
            }

            if (a_state->version) {
                ZPN_FREE(a_state->version);
                a_state->version = NULL;
            }
            if (a_state->cname) {
                ZPN_FREE(a_state->cname);
                a_state->cname = NULL;
            }
            if (a_state->public_cloud) {
                ZPN_FREE(a_state->public_cloud);
                a_state->public_cloud = NULL;
            }
            if (a_state->private_cloud) {
                ZPN_FREE(a_state->private_cloud);
                a_state->private_cloud = NULL;
            }
            if (a_state->region_id) {
                ZPN_FREE(a_state->region_id);
                a_state->region_id = NULL;
            }
            if (a_state->log.g_ast_country_code) {
                ZPN_FREE(a_state->log.g_ast_country_code);
                a_state->log.g_ast_country_code = NULL;
            }
            if (a_state->log.g_ast_city) {
                ZPN_FREE(a_state->log.g_ast_city);
                a_state->log.g_ast_city = NULL;
            }
            if (a_state->log.g_ast_subdivision_code) {
                ZPN_FREE(a_state->log.g_ast_subdivision_code);
                a_state->log.g_ast_subdivision_code = NULL;
            }
            zpn_broker_a_state_active_q_del(a_state);
            zpn_broker_a_state_free(a_state);
            fohh_connection_set_dynamic_cookie(connection, NULL);

            zpn_fohh_worker_assistant_disconnect_data_fohh(fohh_connection_get_thread_id(connection), reason);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}


static int zpn_broker_assistant_create_config_conn_cookie(struct fohh_connection *f_conn,
                                                          int64_t asst_gid,
                                                          enum config_conn_type type)
{
    struct connected_assistant_config_fohh_state *asst = ZPN_CALLOC(sizeof(*asst));
    int result;

    /* Generate an ID for this client */
    const int res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(asst);
        return FOHH_RESULT_ERR;
    }
    base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

    asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(f_conn)),
                            -1,
                            EV_PERSIST,
                            zpn_broker_assistant_config_conn_monitor_cb,
                            asst);
    if (!asst->timer) {
        ZPN_LOG(AL_CRITICAL, "Memory");
        ZPN_FREE(asst);
        return FOHH_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;;
    tv.tv_usec = 0;
    if (event_add(asst->timer, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not add assistant timer");
        event_free(asst->timer);
        asst->timer = NULL;
        ZPN_FREE(asst);
        return FOHH_RESULT_NO_MEMORY;
    }

    if (type == config_conn_type_override) asst->is_override = 1;
    asst->config_connection_type = type;
    asst->assistant_gid_from_config = asst_gid;
    asst->f_conn = f_conn;
    asst->f_conn_incarnation = fohh_connection_incarnation(f_conn);
    asst->customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_gid);

    /*
     * For config connections we dont need to make sure the pBroker and the assistant have the same scope gid (as of now).
     */
    result = zpn_broker_assistant_get_scope_gid(asst_gid, &asst->scope_gid);
    if(result != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(LOG_ERR, "Failed to fetch scope_gid for assistant %"PRId64"\n", asst_gid);
        event_free(asst->timer);
        asst->timer = NULL;
        ZPN_FREE(asst);
        return FOHH_RESULT_ERR;
    }

    fohh_connection_set_dynamic_cookie(f_conn, asst);
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_broker_assistant_destroy_config_conn_cookie(struct fohh_connection *f_conn)
{
    struct connected_assistant_config_fohh_state *asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        if (asst->timer) {
            event_free(asst->timer);
        }
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        ZPN_FREE(asst);
    }
}

static int zpn_broker_assistant_config_override_conn_callback(struct fohh_connection *connection,
                                                              enum fohh_connection_state state,
                                                              void *cookie)
{
    static char *filter_tables[] = {
        "zpath_config_override",
        "zpn_inspection_config_data",
        "zpn_inspection_zsdefined_control",
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int res;
        int64_t gid = fohh_peer_get_id(connection);
        if (!gid) {
            ZPN_LOG(AL_ERROR, "%s: Broker received incorrect assistant config override connection, Assistant ID = %"PRId64,
                    fohh_description(connection), gid);
            return FOHH_RESULT_ERR;
        }

        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int64_t global_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
        int64_t global_insp_gid = ZPN_GLOBAL_INSPECTION_CONFIG_GID;
        struct filter_entry entries[] = {
                                         {"zpath_config_override",      "customer_gid",       &customer_gid, sizeof(int64_t)},
                                         {"zpath_config_override",      "customer_gid",       &global_gid, sizeof(int64_t)},
                                         {"zpn_inspection_config_data", "customer_gid",       &customer_gid, sizeof(int64_t)},
                                         {"zpn_inspection_config_data", "customer_gid",       &global_insp_gid, sizeof(int64_t)}
                                        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(wally_server_global,
                                                connection,
                                                NULL,
                                                0,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                customer_gid,
                                                global_gid,
                                                table_filter,
                                                NULL,
                                                zpn_broker_assistant_config_override_conn_callback)) {
            ZPN_LOG(AL_ERROR, "%s: Could not hand off FOHH to wally for assistant override", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }
        ZPN_LOG(AL_NOTICE, "%s: Assistant config override connection UP", fohh_description(connection));

        res = zpn_broker_assistant_create_config_conn_cookie(connection, gid, config_conn_type_override);
        if (res != FOHH_RESULT_NO_ERROR) {
            return res;
        }

        zpn_fohh_worker_assistant_connect_override(fohh_connection_get_thread_id(connection));
    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Assistant config override connection DOWN", fohh_description(connection));

            zpn_broker_assistant_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_assistant_disconnect_override(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_np_config_conn_callback(struct fohh_connection *connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    static char *filter_tables[] = {
        "np_connectors",
        "np_tenant_gateways",
        "np_client_subnets",
        "np_connector_groups",
        "np_command_probe",
        "np_lan_subnets",
        "np_connector_groups_lan_subnets_mapping",
        "np_bgp_connectors_config",
        "np_bgp_gateways_config",
        "np_bgp_connector_session_config"
    };

    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int res;
        int64_t customer_gid = 0;
        int64_t gid = fohh_peer_get_id(connection);
        int shard = ZPATH_SHARD_FROM_GID(gid);

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "%s: Broker received np config connection, Assistant ID = %"PRId64", shard = %d (Out of range)",
                    fohh_description(connection), gid, shard);
            return FOHH_RESULT_ERR;
        }

        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);

        if (!wally_fohh_create_fohh_from_client(np_wally_servers[shard],
                                                connection,
                                                NULL,
                                                0,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                customer_gid,
                                                0, //pass_key?
                                                NULL,
                                                NULL,
                                                zpn_broker_assistant_np_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "%s: Could not hand off FOHH to wally for np config", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }
        ZPN_LOG(AL_NOTICE, "%s: Assistant np config connection UP", fohh_description(connection));

        res = zpn_broker_assistant_create_config_conn_cookie(connection, gid, config_conn_type_np);
        if (res != FOHH_RESULT_NO_ERROR) {
            return res;
        }

        zpn_fohh_worker_assistant_connect_np_config(fohh_connection_get_thread_id(connection));
    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Assistant np config connection DOWN", fohh_description(connection));

            zpn_broker_assistant_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_assistant_disconnect_np_config(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_config_conn_callback(struct fohh_connection *connection,
                                                     enum fohh_connection_state state,
                                                     void *cookie)
{
    static char *filter_tables[] = {
        "zpath_customer",
        "zpn_app_group_relation",
        "zpn_app_server",
        "zpn_application",
        "zpn_application_group",
        "zpn_application_group_application_mapping",
        "zpn_assistant",
        "zpn_assistant_group",
        "zpn_assistant_version",
        "zpn_assistantgroup_assistant_relation",
        "zpn_command_probe",
        "zpn_client_setting",
        "zpn_server_group",
        "zpn_server_group_assistant_group",
        "zpn_servergroup_server_relation",
        "zpn_private_broker_load",
        "zpn_private_broker",
        "zpn_private_broker_group",
        "zpn_private_broker_to_group",
        "zpn_inspection_application",
        "zpn_public_cert",
        "zpn_inspection_profile",
        "zpn_inspection_profile_to_control",
        "zpn_inspection_prof_to_zsdefined_ctrl",
        "zpn_sub_module_upgrade",
        "zpn_site",
        "zpn_ddil_config",
        "zpn_firedrill_site"
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int res;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;
        int64_t gid = fohh_peer_get_id(connection);
        int shard = ZPATH_SHARD_FROM_GID(gid);

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "%s: Broker received assistant config connection, Assistant ID = %"PRId64", shard = %d (Out of range)",
                    fohh_description(connection), gid, shard);
            return FOHH_RESULT_ERR;
        }

        if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) {
            struct argo_state *argo = fohh_argo_get_rx(connection);
            /* Register zpn_version */
            if ((res = argo_register_structure(argo, zpn_version_description, zpn_assistant_version_cb, connection))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_version for connection for %s", fohh_description(connection));
                return res;
            }
        }
        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_ASSISTANT("%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        if (!wally_fohh_create_fohh_from_client(wally_servers[shard],
                                                connection,
                                                NULL,
                                                0,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                NULL,
                                                NULL,
                                                zpn_broker_assistant_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "%s: Could not hand off FOHH to wally for assistant config", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }
        ZPN_LOG(AL_NOTICE, "%s: Assistant config connection UP", fohh_description(connection));

        res = zpn_broker_assistant_create_config_conn_cookie(connection, gid, config_conn_type_config);
        if (res != FOHH_RESULT_NO_ERROR) {
            return res;
        }

        zpn_fohh_worker_assistant_connect_config(fohh_connection_get_thread_id(connection));
    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Assistant config connection DOWN", fohh_description(connection));

            zpn_broker_assistant_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_assistant_disconnect_config(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_unblock_callback(struct fohh_connection *connection,
                                                 enum fohh_queue_element_type element_type,
                                                 void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_data_unblock_callback(struct fohh_connection *connection,
                                                      enum fohh_queue_element_type element_type,
                                                      void *cookie)
{
    struct zpn_broker_assistant_fohh_state *a_state = fohh_connection_get_dynamic_cookie(connection);

    /*
     * commenting below code to make it maitanance-proof
     */
    //if (fohh_queue_element_type_data != element_type) {
    //  return ZPN_RESULT_NO_ERROR;
    //}

    if (!a_state) {
        ZPN_LOG(AL_ERROR, "%s: Data unblock without a_state", fohh_description(connection));
        return ZPN_RESULT_ERR;
    }

    return zpn_fohh_tlv_unblock_cb(&(a_state->tlv_state));
}

static int zpn_broker_assistant_config_unblock_callback(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_broker_assistant_log_connection_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct fohh_connection *connection = cookie;

    if (zpn_broker_assistant_get_redirect_assistants_flag()) {
        const char *redirect_reason = zpn_broker_assistant_get_redirect_assistants_reason();
        ZPN_LOG(AL_INFO, "%s: Redirect assistant log connection due to %s",
                fohh_description(connection),
                redirect_reason ? redirect_reason : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_assistant_log_conn_redirect(connection, log_rx_get_app_info(connection));
    }
}

static void zpn_broker_assistant_log_connection_periodic_logging_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct fohh_connection *connection = cookie;
    struct zpn_ast_auth_log log;
    struct zpn_assistant *assistant = NULL;
    memset(&log, 0, sizeof(log));

    int res;

    res = zpn_assistant_get_by_id(fohh_peer_get_id(connection),
                                  &assistant,
                                  NULL,
                                  NULL,
                                  0);
    if (res) {
        ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) fohh_peer_get_id(connection),
                             zpn_result_string(res));
    } else {
        log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
    }

    if (connection->log_type && (0 == strcmp(connection->log_type, FOHH_LOG_INSPECTION_LOG))) {
        res = zpn_broker_assistant_auth_log(connection,
                                            &log,
                                            fohh_peer_get_id(connection),
                                            ZPN_ASSISTANT_BROKER_LOG_INSPECTION,
                                            fohh_get_state(connection));
    } else {
        res = zpn_broker_assistant_auth_log(connection,
                                            &log,
                                            fohh_peer_get_id(connection),
                                            ZPN_ASSISTANT_BROKER_LOG,
                                            fohh_get_state(connection));
    }

    if (res) {
        ZPN_LOG(AL_WARNING, "Assistant log connection auth log fail");
    }
}

static int zpn_broker_assistant_config_override_unblock_callback(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}


int zpn_broker_assistant_get_conn(int64_t assistant_id,
                                  struct fohh_connection **f_conn,
                                  int64_t *f_conn_incarnation)
{
    struct connected_assistant *asst;

    if (ut_hook.number_of_assistant_get_conn_to_return_err) {
        __sync_sub_and_fetch_8(&ut_hook.number_of_assistant_get_conn_to_return_err, 1);
        ASSISTANT_LOG(AL_NOTICE, "return err from assistant_get_conn function as per UT config, still (%"PRId64") return err on assistant_get_conn",
                      ut_hook.number_of_assistant_get_conn_to_return_err);
        return ZPN_RESULT_ERR;
    }

    pthread_mutex_lock(&asst_lock);
    asst = zpn_broker_find_connected_active_assistant(assistant_id);
    if (!asst) {
        pthread_mutex_unlock(&asst_lock);
        (*f_conn) = NULL;
        ZPN_LOG(AL_NOTICE, "Request for assistant id %ld, but not found", (long) assistant_id);
        return ZPN_RESULT_NOT_FOUND;
    }

    (*f_conn) = asst->f_conn;
    (*f_conn_incarnation) = asst->f_conn_incarnation;
    pthread_mutex_unlock(&asst_lock);
    return ZPN_RESULT_NO_ERROR;
}

/*
 * 1. Do a curl 127.0.0.1:9000/assistant/app/dump/apps in connector to get the input required for this command.
 * 2. Now in broker do something like this,
 * curl '127.0.0.1:8000/zpn/broker/assistant/dns_check?g_ast=217246660303022926&g_app=217246660303022315&name=money.cnn.com&type=A'
 */
int zpn_broker_assistant_send_dns_check_dbg(struct zpath_debug_state*    request_state,
                                            const char**                 query_values,
                                            int                          query_value_count,
                                            void*                        cookie)
{
    struct zpn_dns_assistant_check  dns_check;

    memset(&dns_check, 0, sizeof(dns_check));

    if (!query_values[0] ||
        !query_values[1] ||
        !query_values[2] ||
        !query_values[3]) {
        ZDP("Missing argument: one or more of: g_ast, g_app, name, type\n");
    } else {
        dns_check.g_ast = strtol(query_values[0], NULL, 10);
        dns_check.g_app = strtol(query_values[1], NULL, 10);
        dns_check.name = (char *)query_values[2];
        dns_check.type = (char *)query_values[3];
        if (query_values[4]) dns_check.g_dsp = strtol(query_values[4], NULL, 10);
        dns_check.target_name_count = 0;
        dns_check.target_port_count = 0;
        dns_check.target_priority_count = 0;
        dns_check.target_weight_count = 0;
        dns_check.cnames_count = 0;
        dns_check.dsp_tx_us = epoch_us();
        zpn_broker_assistant_send_dns_check(&dns_check);
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpn_broker_assistant_send_dns_check(struct zpn_dns_assistant_check *check_in)
{
    struct connected_assistant *asst;
    int res;

    if (!ZPN_DNS_CHECK_TARGETS_EQUAL(check_in)) {
        ZPN_LOG(AL_ERROR, "Bad check counts");
        return ZPN_RESULT_ERR;
    }

    pthread_mutex_lock(&asst_lock);

    asst = zpn_broker_find_connected_active_assistant(check_in->g_ast);
    if (!asst) {
        pthread_mutex_unlock(&asst_lock);
        return ZPN_RESULT_NOT_FOUND;
    }

    res = zpn_send_zpn_dns_assistant_check(asst->f_conn,
                                           asst->f_conn_incarnation,
                                           check_in->g_ast,
                                           check_in->g_app,
                                           check_in->g_dsp,
                                           check_in->name,
                                           check_in->type,
                                           NULL,
                                           check_in->dsp_tx_us,
                                           zpn_cloud_adjusted_epoch_us());
    pthread_mutex_unlock(&asst_lock);
    return res;
}

/*
 * Callback that occurs after all the certificates have been fetched
 * from storage.
 *
 * We just queue up reprocessing of the connection, which will trigger
 * creation of the cert context again.
 */
static int zpn_broker_assistant_ctx_callback_callback(void *response_callback_cookie,
                                                      struct wally_registrant *registrant,
                                                      struct wally_table *table,
                                                      int64_t request_id,
                                                      int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_assistant_fohh_incarnation_mismatch), 1);
        return WALLY_RESULT_NO_ERROR;
    }

    fohh_connection_process(f_conn, request_id);
    return WALLY_RESULT_NO_ERROR;
}

/*
 * When the connection wants SSL context, this gets called. SNI is
 * assumed to have domain of customer.
 */
static int zpn_broker_assistant_ctx_callback_common(void *conn_cookie,
                                                    const char *sni,
                                                    const char *sni_suffix,
                                                    SSL_CTX **ssl_ctx,
                                                    enum zpn_tlv_type tlv_type,
                                                    const char *conn_description)
{
    char sub_sni[256];
    size_t sni_suffix_len;
    size_t sni_len;
    int res;
    const char *use_sni = NULL;
    int64_t customer_id = 0;
    int64_t assistant_id;
    struct zpn_assistant *assistant = NULL;
    struct zpath_customer *customer = NULL;
    int is_dtls = (tlv_type == zpn_fohh_tlv) ? 0 : 1;
    wally_response_callback_f *callback_f = (tlv_type == zpn_fohh_tlv) ? zpn_broker_assistant_ctx_callback_callback : NULL;
    void *callback_cookie = (tlv_type == zpn_fohh_tlv) ? conn_cookie : NULL;
    int64_t cookie_int = (tlv_type == zpn_fohh_tlv) ? fohh_connection_incarnation(conn_cookie) : 0;

    if (ZPN_BROKER_IS_PRIVATE() && !g_broker_common_cfg->private_broker.enabled) {
        ZPN_LOG(AL_INFO, "Dropping %s, Service Edge is disabled", conn_description);
        return ZPN_RESULT_ERR;
    }

    if (isdigit(sni[0])) {
        /*
         * V2 style connector SNI: <assistant_id>.<broker_gid><actl|adata|acfg>.<cloud name>
         *
         * Note: Modern customer provision is domain as
         * CUSTOMER_GID.zpa-customer.com. Thus we check if the GID
         * passed is a customer_GID
         */
        assistant_id = strtoll(sni, NULL, 0);
        if ((assistant_id < 0x0100000000000000ll) ||
            assistant_id == ZPATH_GID_GET_CUSTOMER_GID(assistant_id)) {
            /* Assume it is V1 style enrollment if the ID isn't big enough to include shard. */
            assistant_id = 0;
            ZPN_DEBUG_ASSISTANT("%s - sni: %s, sni_suffix: %s - V1 style connector enrollment", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
        } else {

            ZPN_DEBUG_ASSISTANT("%s - sni: %s, sni_suffix: %s, assistant_id: %ld - V2 style connector enrollment",
                                conn_description, sni, sni_suffix ? sni_suffix : "NULL", (long)assistant_id);

            res = zpn_assistant_get_by_id(assistant_id,
                                          &assistant,
                                          callback_f,
                                          callback_cookie,
                                          cookie_int);
            if (res) {
                if (res == ZPN_RESULT_ASYNCHRONOUS) {
                    ZPN_DEBUG_ASSISTANT("Asynchronous assistant fetch for gid = %ld", (long)assistant_id);
                } else {
                    ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long)assistant_id, zpn_result_string(res));
                }
                return res;
            } else {
                customer_id = assistant->customer_gid;
                ZPN_DEBUG_ASSISTANT("%s: Assistant %ld fetched, cert_id: %ld, customer_gid: %ld",
                                    conn_description, (long)assistant_id, (long) assistant->cert_id, (long) customer_id);

                res = zpath_customer_get(customer_id, &customer, callback_f, callback_cookie, cookie_int);
                if (res) {
                    if (res == ZPN_RESULT_ASYNCHRONOUS) {
                        ZPN_DEBUG_ASSISTANT("Asynchronous customer fetch for gid = %ld", (long)customer_id);
                    } else {
                        ZPN_LOG(AL_WARNING, "Error fetching customer %ld: %s", (long)customer_id, zpn_result_string(res));
                    }
                    return res;
                }
                use_sni = customer->domain_name;
                ZPN_DEBUG_ASSISTANT("Customer domain: %s, customer_gid: %ld", use_sni, (long)customer_id);
            }
        }
    } else {
        ZPN_DEBUG_ASSISTANT("%s - sni: %s, sni_suffix: %s - V1 style connector enrollmentco", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
    }

    if (!sni_suffix) {
        use_sni = sni;
    } else if (!use_sni) {
        /* V1 style connector SNI: <domain>.<acfg|adata|actl>.<cloud name> */
        sni_suffix_len = strlen(sni_suffix) + 1;
        sni_len = strlen(sni);

        if (sni_len <= sni_suffix_len) {
            ZPN_LOG(AL_ERROR, "Expecting SNI longer than root. Sni = %s", sni);
            return ZPN_RESULT_ERR;
        }

        snprintf(sub_sni, sizeof(sub_sni), "%.*s", (int)(sni_len - sni_suffix_len), sni);
        use_sni = sub_sni;
    }

    res = zpn_signing_cert_get_verify_ctx(ssl_ctx,
                                          &customer_id,
                                          use_sni,
                                          0,
                                          callback_f,
                                          conn_cookie,
                                          cookie_int,
                                          is_dtls);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_ASSISTANT("Asynchronous context request for sni = %s", sni);
        } else {
            ZPN_LOG(AL_WARNING, "Error context request for sni = %s: %s", sni, zpn_result_string(res));
        }
    } else {
        ZPN_DEBUG_ASSISTANT("%s: SNI = %s has context", conn_description, sni);

        if (tlv_type == zpn_zrdt_tlv) {
            if (!zpn_broker_is_dtls_enabled_on_broker()) {
                /* DTLS not enabled for this customer */
                ZPN_LOG(AL_INFO, "SSL ctx fetch failed because dtls is not enabled");
                *ssl_ctx = NULL;
                res = ZPN_RESULT_ERR;
            }
        }
    }
    return res;
}

static int zpn_broker_assistant_ctx_callback_fohh(struct fohh_connection *f_conn,
                                                  const char *sni,
                                                  const char *sni_suffix,
                                                  SSL_CTX **ssl_ctx)
{
    return zpn_broker_assistant_ctx_callback_common(f_conn, sni, sni_suffix, ssl_ctx, zpn_fohh_tlv, fohh_description(f_conn));

}

static int zpn_broker_assistant_verify_callback_common(struct zpn_tlv *tlv, int verify_scope)
{
    int res=0;
    int64_t assistant_gid_derived_from_cert;
    int64_t assistant_gid_fetched_from_wally;
    char *cn = zpn_tlv_peer_cn(tlv);
    X509 *cert;
    struct zpn_assistant *assistant;
    int ip_acl_iter;
    struct argo_inet asst_addr;
    int ip_acl_validation_failed;
    char    *cn_iter;
    wally_response_callback_f *callback_f = (tlv->type == zpn_fohh_tlv) ? zpn_broker_assistant_ctx_callback_callback : zpn_broker_assistant_verify_callback_callback_zrdt;
    void *callback_cookie = (tlv->type == zpn_fohh_tlv) ? (void *)(tlv->conn.f_conn) : (void *)(tlv->conn.z_conn);
    int64_t request_id = (tlv->type == zpn_fohh_tlv) ? fohh_connection_incarnation(tlv->conn.f_conn) : zrdt_conn_incarnation(tlv->conn.z_conn);

    if (!cn) {
        ZPN_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    assistant_gid_derived_from_cert = 0;
    for (cn_iter = &(cn[0]); *cn_iter; cn_iter++) {
        if (isdigit(*cn_iter)) {
            assistant_gid_derived_from_cert = strtoll(cn_iter, &cn_iter, 0);
            break;
        }
    }
    if (0 == assistant_gid_derived_from_cert) {
        ZPN_LOG(AL_WARNING, "Could not derive assistant gid from cname(%s)", cn);
        return ZPN_RESULT_ERR;
    }

    res=zpn_broker_assistant_validate_version(assistant_gid_derived_from_cert,
                                                callback_f,
                                                callback_cookie,
                                                request_id);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Could not validate version for assistant : %s", zpn_result_string(res));
        }
        return res;
    }

    cert = zpn_tlv_peer_cert(tlv);

    if (!cert) {
        ZPN_LOG(AL_WARNING, "No peer cert??");
        return ZPN_RESULT_ERR;
    }

    res = zpn_issuedcert_verify(ZPATH_GID_GET_CUSTOMER_GID(assistant_gid_derived_from_cert),
                                cert,
                                cn,
                                "ASSISTANT",
                                &assistant_gid_fetched_from_wally,
                                callback_f,
                                callback_cookie,
                                request_id);
    X509_free(cert);

    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_ASSISTANT("Asynchronous verification request for CN = %s", cn);
        } else {
            ZPN_LOG(AL_WARNING, "Error verifying request for CN = %s, = %s", cn, zpn_result_string(res));
        }
        return res;

    } else {
        if (assistant_gid_fetched_from_wally != assistant_gid_derived_from_cert) {
            ZPN_LOG(AL_WARNING, "Error verifying request for CN = %s, gid mismatch(%ld vs %ld)", cn,
                    (long)assistant_gid_derived_from_cert, (long)assistant_gid_fetched_from_wally);
            return ZPN_RESULT_ERR;
        }
        ZPN_DEBUG_ASSISTANT("Verified cert for CN = %s", cn);
    }
    zpn_tlv_peer_set_id(tlv, assistant_gid_derived_from_cert);

    res = zpn_assistant_get_by_id(assistant_gid_derived_from_cert,
                                  &assistant,
                                  callback_f,
                                  callback_cookie,
                                  request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_ASSISTANT("Asynchronous assistant fetch for gid = %ld", (long) assistant_gid_derived_from_cert);
        } else {
            ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) assistant_gid_derived_from_cert,
                    zpn_result_string(res));
        }
        return res;
    } else {
        ZPN_DEBUG_ASSISTANT("%s: Assistant cert verified, assistant %ld fetched.", cn, (long) assistant_gid_derived_from_cert);
    }

    zpn_tlv_address(tlv, &asst_addr, NULL);

    /*
     * Here we are prefetching the assistant group relation and
     * assistant group, because we can do it asynchronously. When the
     * control connection callback occurs, this data can be fetched
     * synchronously. (The control connection callback must be
     * synchronous)
     */
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    res = zpn_assistantgroup_assistant_relation_get_by_assistant(assistant_gid_derived_from_cert,
                                                                 &ag_relation,
                                                                 &count,
                                                                 callback_f,
                                                                 callback_cookie,
                                                                 request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_ASSISTANT("Asynchronous assistantgroup_assistant_relation fetch for gid = %ld", (long) assistant_gid_derived_from_cert);
        } else {
            ZPN_LOG(AL_WARNING, "Error fetching assistantgroup_assistant_relation: %ld: %s", (long) assistant_gid_derived_from_cert, zpn_result_string(res));
            /* We don't error here because the relation may simply be changing, etc */
            res = ZPN_RESULT_NO_ERROR;
        }
        return res;
    } else {
        /* Now that we have an assistantgroup, fetch that too. */
        struct zpn_assistant_group *group;
        res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                             &group,
                                             callback_f,
                                             callback_cookie,
                                             request_id);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                ZPN_DEBUG_ASSISTANT("Asynchronous assistant_group fetch for gid = %ld", (long) ag_relation->assistant_group_id);
            } else {
                ZPN_LOG(AL_WARNING, "Error fetching assistant_group: %ld: %s", (long) ag_relation->assistant_group_id, zpn_result_string(res));
                /* We don't error here because the relation may simply be changing, etc */
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            /*
             * IP ACL is configured at connector group level.
             * So either customer do not configure them
             * or will have to configure IP ACL's for all the connectors in the group
             */
            if (0 == group->ip_acl_count) {
                ip_acl_validation_failed = 0;
            } else {
                ip_acl_validation_failed = 1;
            }
            ip_acl_iter = 0;
            while (ip_acl_iter != group->ip_acl_count) {
                if (argo_inet_is_contained(&group->ip_acl[ip_acl_iter], &asst_addr)) {
                    ip_acl_validation_failed = 0;
                    break;
                }
                ip_acl_iter++;
            }

            if (ip_acl_validation_failed) {
                char asst_addr_str[ARGO_INET_ADDRSTRLEN];
                ZPN_LOG(AL_ERROR, "Connector's %ld IP(%s) didn't match ACL "
                        "defined in Connector Group %ld - "
                        "terminating the connector",
                        (long)assistant_gid_derived_from_cert,
                        argo_inet_generate(asst_addr_str, &asst_addr),
                        (long)ag_relation->assistant_group_id);
                return ZPN_RESULT_ACCESS_DENIED;
            }
        }
    }

    return res;
}

static int zpn_broker_assistant_verify_callback_fohh(struct fohh_connection *f_conn)
{
    struct zpn_tlv tlv;

    zpn_tlv_init(&tlv, zpn_fohh_tlv, f_conn, 0);
    return zpn_broker_assistant_verify_callback_common(&tlv, 0);
}

static int zpn_broker_assistant_verify_control_conn_callback_fohh(struct fohh_connection *f_conn)
{
    struct zpn_tlv tlv;

    zpn_tlv_init(&tlv, zpn_fohh_tlv, f_conn, 0);
    return zpn_broker_assistant_verify_callback_common(&tlv, 0);
}

/*
 * Debug interface for setting/unsetting UT behavior - drop bind msg. This is used to simulate the case of broker
 * dropping the bind message on its side and observe the effects.
 */
static int
zpn_broker_assistant_return_err_in_assistant_get_conn(struct zpath_debug_state*    request_state,
                                        const char**                 query_values,
                                        int                          query_value_count,
                                        void*                        cookie)
{
    if (query_values[0]) {
        ut_hook.number_of_assistant_get_conn_to_return_err = strtoll(query_values[0], NULL, 10);
    } else {
        ut_hook.number_of_assistant_get_conn_to_return_err = 1;
    }
    ZDP("Set to %"PRId64" return err from assistant_get_conn function\n", ut_hook.number_of_assistant_get_conn_to_return_err);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_broker_assistant_init(enum Zpn_Instance_Type broker_personality)
{
    int res;

    res = zpath_debug_add_read_command("Dump all the control connections to connectors",
                                  "/broker/assistant/control/dump",
                                  zpn_broker_assistant_control_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump all the control connections to connectors",
                                  "/broker/assistant/control/dump/short",
                                  zpn_broker_assistant_control_dump_all_short,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump all the data connections to connectors",
                                  "/broker/assistant/data/dump",
                                  zpn_broker_assistant_data_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Unit Test: mock cases where control broker can not find the control connection to the specified assistant",
                                  "/broker/assistant/ut/assistant_get_conn_err",
                                  zpn_broker_assistant_return_err_in_assistant_get_conn,
                                  NULL,
                                  "count", "Number of times to return err from zpn_broker_assistant_get_conn function",
                                  NULL);
    if (res) {
        return res;
    }

    if(broker_personality == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        res = zpn_broker_assistant_stats_init();
        if (res) {
            return res;
        }
        zpath_config_override_monitor_int(BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN,
                                          &g_send_ast_auth_report_every_1_min,
                                          NULL,
                                          (int64_t)BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid),
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        zpath_config_override_monitor_int(BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN,
                                          &g_send_ast_auth_report_every_1_min,
                                          NULL,
                                          (int64_t)BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id),
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int new_stream_cb(struct zrdt_conn *conn,
                         struct zrdt_stream *stream,
                         uint64_t stream_id,
                         enum zrdt_stream_status status,
                         void *cookie)
{
    struct zpn_broker_assistant_fohh_state *a_state;
    int res;

    ZPN_DEBUG_MTUNNEL("New Stream callback. stream_id = %ld, status = %s, description = %s",
                       (long) stream_id, zrdt_stream_status_string(status),
                       zdtls_description(zrdt_conn_get_datagram_tx_cookie(conn)));

    a_state = zrdt_conn_get_dynamic_cookie(conn);
    if (!a_state) {
        ZPN_LOG(AL_NOTICE, "new stream coming without a_state??");
        return ZPN_RESULT_ERR;
    }

    if (stream_id == 0) {
        a_state->zrdt_tlv_state.msg_stream = stream;

        /* This is control stream */
        res = zrdt_stream_set_type(stream, zrdt_stream_reliable_endpoint);
        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot set stream type");
            return ZPN_RESULT_ERR;
        }

        res = zpn_zrdt_init_argo_state(stream, argo_serialize_json_no_newline, 1);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize argo state for msg_stream");
            return ZPN_RESULT_ERR;
        }

        zpn_broker_assistant_register_rpc_callbacks(a_state);
    } else {
        struct zpn_broker_mtunnel *mtunnel;

        mtunnel = zpn_zrdt_tlv_get_global_owner(&(a_state->zrdt_tlv_state), stream_id);
        if (!mtunnel) {
            ZPN_DEBUG_MTUNNEL("New stream with id %ld has no corresponding mtunnel", (long)stream_id);
            return ZPN_RESULT_NO_ERROR;
        }
        zrdt_set_stream_cookie(stream, mtunnel, mtunnel->incarnation);
    }

    /* This is data stream */
    res = zrdt_stream_assign_callbacks(stream, zpn_zrdt_read_cb, zpn_zrdt_write_cb, zpn_zrdt_event_cb, &(a_state->zrdt_tlv_state));
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot set callbacks");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_valid_stream_cb(struct zrdt_conn *conn,
                                                struct zrdt_stream *stream,
                                                uint64_t stream_id,
                                                int64_t cookie_int,
                                                void *cookie)
{
    int64_t original_incarnation = cookie_int;
    struct zpn_broker_mtunnel *mtunnel = cookie;

    ZPN_DEBUG_MTUNNEL("verify stream id %ld has mconn/mtunnel associated with it before deleting stream", (long)stream_id);

    if(mtunnel == NULL) {
        ZPN_DEBUG_MTUNNEL("%ld: Invalid mtunnel. proceed with stream deletion", (long)stream_id);
        return 0;
    }

    if (mtunnel->incarnation != original_incarnation) {
        ZPN_DEBUG_MTUNNEL("%ld: mtunnel incarnation mismatch. proceed with stream deletion", (long)stream_id);
        return 0;
    }

    if(mtunnel->state < zbms_free) {
        ZPN_BROKER_ASSERT_SOFT(0, "steam %ld has mconn/mtunnel %s", (long)stream_id, mtunnel->mtunnel_id);
        return 1;
    }

    return 0;
}

static int zpn_broker_assistant_search_customer_id(char *cn_given, int64_t *customer_id)
{
    char *cn = cn_given;
    char downcase[256];

    if (!cn) {
        ZPN_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    /* Find @ of e-mail address */
    while ((*cn) && ((*cn) != '@')) cn++;
    if ((*cn) != '@') {
        /* CN for assistant could be in form of <assistant_gid>.ast.<cloud_name> */
        cn = cn_given;
        while ((*cn) && ((*cn) != '.')) {
            if(!isdigit(*cn)) {
                ZPN_LOG(AL_WARNING, "Peer CN not in the expected form: %s", cn_given);
                return ZPN_RESULT_ERR;
            }
            cn++;
        }
        *customer_id = strtoll(cn_given, &cn, 10);
        return ZPN_RESULT_NO_ERROR;
    }
    cn++;

    /* Need to downcase the domain in a seperate buffer, otherwise CN
     * lookup will fail when fetching certificate */
    snprintf(downcase, sizeof(downcase), "%s", cn);
    zpath_downcase(downcase);

    *customer_id = zpath_domain_lookup_search_id(downcase, strlen(downcase));

    if (!*customer_id) {
        ZPN_LOG(AL_WARNING, "No customer ID for peer domain = %s from cn = %s", downcase, cn_given);
        return ZPN_RESULT_NOT_FOUND;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_assistant_zdtls_session_callback(struct zdtls_session *zd_sess,
                                                       void *thread_cookie,
                                                       enum zdtls_session_status status,
                                                       const char *reason,
                                                       void *cookie,
                                                       int64_t int_cookie,
                                                       void *tlv_cookie)
{
    struct zrdt_conn *zrdt_conn = NULL;
    struct fohh_thread *f_thread = (struct fohh_thread *)thread_cookie;
    int thread_id = fohh_thread_get_id(f_thread);
    struct zpn_broker_assistant_fohh_state *a_state = NULL;
    int res;

    ZPN_DEBUG_ASSISTANT("Received assistant side dtls session callback, status = %s", zdtls_session_status(status));

    if (status == zdtls_session_open) {
        // dtls session has been established with the connector. If the path to this assistant gets an additional encap
        // (ex: IPSec because of going through ZIA SME) mtu has to be adjusted. This is controlled by the config override
        // DTLS_FEATURE_MTU
        char *cname;
        int mtu = DEFAULT_STREAM_MTU;
        int64_t assistant_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        cname = zdtls_get_peer_cn(zd_sess);
        if (cname) {
            char *cn;
            for (cn = cname; *cn; cn++) {
                if (isdigit(*cn)) {
                    assistant_id = strtoll(cn, &cn, 0);
                    break;
                }
            }
            if (assistant_id) {
                mtu = zpn_broker_dtls_mtu(assistant_id);
                if (mtu != DEFAULT_STREAM_MTU) {
                    ZPN_DEBUG_ASSISTANT("config override: mtu %d for dtls connection to assistant %ld", mtu, (long)assistant_id);
                }
            }
        }

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(assistant_id);
        if (ZPN_BROKER_IS_PUBLIC() && customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_ASSISTANT("%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            zdtls_description(zd_sess), assistant_id, customer_gid);
                return;
            }
        }

        res = zrdt_conn_create(&zrdt_conn,
                               mtu,
                               (zrdt_datagram_tx_f *) zdtls_session_transmit,
                               zd_sess,
                               int_cookie,      // Is it a client connection or not?
                               new_stream_cb,
                               NULL,            // c_state,
                               zdtls_session_get_event_base(zd_sess));
        if (res) {
            ZPN_LOG(AL_NOTICE, "Could not create new connection?");
            res = zdtls_session_close(zd_sess);
            if (res) {
                ZPN_LOG(AL_NOTICE, "Could not handle session_close on session failure");
            }

            return;
        }

        ZPN_DEBUG_ASSISTANT("Created RDT connection");

        a_state = zpn_broker_a_state_alloc();
        if (!a_state) {
            ZPN_LOG(AL_ERROR, "Cannot allocate a_state");
            zrdt_conn_destroy(zrdt_conn);
            return;
        }

        a_state->tlv_type = zpn_zrdt_tlv;

        res = zpn_zrdt_tlv_init(&(a_state->zrdt_tlv_state), zrdt_conn, a_state->incarnation);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: tlv_state init failed: %s", zdtls_description(zd_sess), zpn_result_string(res));
            zpn_broker_a_state_free(a_state);
            return;
        }

        res = zpn_broker_assistant_data_state_init(a_state, zpn_zrdt_tlv);
        if (!res) {
            ZPN_LOG(AL_CRITICAL, "a_state init failed!");
            zpn_broker_a_state_free(a_state);
            return;
        }

        cname = zdtls_get_peer_cn(zd_sess);
        if (cname) {
            if (a_state->cname) {
                ZPN_FREE(a_state->cname);
                a_state->cname = NULL;
            }
            a_state->cname = ZPN_STRDUP(cname, strlen(cname));
        } else {
            ZPN_LOG(AL_INFO, "Peer %s has no cname?", zdtls_description(zd_sess));
        }
        zpn_broker_assistant_search_customer_id(cname, &a_state->assitant_gid);
        if (ZPN_BROKER_IS_PRIVATE()) zpn_pbroker_data_connection_stats_update_ast_state_hash(a_state, 1);

        /* Save the state... */
        zrdt_conn_set_dynamic_cookie(zrdt_conn, a_state);

        a_state->zrdt_tlv_state.fohh_thread_id = thread_id;

        zrdt_conn_set_thread_id(zrdt_conn, thread_id);

        if(a_state->assitant_gid) {
            struct zpn_assistant *assistant = NULL;
            /*
            * Fetch assistant from zpn_assistant table. Note:- this is
            * prefetched during authentication, so should be
            * available directly
            */
            res = zpn_assistant_get_by_id(a_state->assitant_gid,
                                          &assistant,
                                          NULL,
                                          NULL,
                                          0);
            if (res) {
                ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) a_state->assitant_gid,
                                     zpn_result_string(res));
            } else {
                a_state->log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
            }
        }

        ZPN_LOG(AL_NOTICE, "%s: Assistant data connection UP", zdtls_description(zd_sess));

        zpn_mconn_zrdt_tlv_add_monitor_timer(zrdt_conn,
                                             &(a_state->zrdt_tlv_state),
                                             zpn_broker_assistant_data_conn_zrdt_monitor_cb,
                                             ZPN_TUNNEL_MONITOR_INTERVAL_S,
                                             0);
#if 0
        res = zdtls_session_set_rx_callback(zd_sess, zpn_zrdt_zdtls_data_callback, zrdt_conn, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not set rx_callback from dtls");
        }
#endif

        res = zdtls_session_reset_connection_callback(zd_sess, zpn_broker_assistant_zdtls_session_callback, zrdt_conn, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not reset sess_callback from dtls");
        }

        zpn_broker_a_state_active_q_add(a_state);

        zrdt_conn_set_verify_callback(zrdt_conn, zpn_broker_assistant_verify_callback_zrdt);
        zrdt_conn_verify(zrdt_conn);
        zrdt_conn_set_stream_validity_check_callback(zrdt_conn, zpn_broker_assistant_valid_stream_cb);

    } else if (status == zdtls_session_closing) {
        /* Closing */

    } else {
        zrdt_conn = (struct zrdt_conn *)cookie;

        if (!zrdt_conn) {
            ZPN_LOG(AL_NOTICE, "Got assistant DTLS data connection closed call, but we never connected");
            return;
        }

        ZPN_LOG(AL_NOTICE, "%s: Assistant data connection DOWN due to %s",
                zdtls_description(zrdt_conn_get_datagram_tx_cookie(zrdt_conn)),
                reason ? reason : "Unknown");

        zpn_fohh_worker_assistant_disconnect_data_zrdt(zrdt_conn_get_thread_id(zrdt_conn));

        a_state = zrdt_conn_get_dynamic_cookie(zrdt_conn);
        if (a_state) {
            if (ZPN_BROKER_IS_PRIVATE()) zpn_pbroker_data_connection_stats_update_ast_state_hash(a_state, 0);

            zpn_mconn_zrdt_tlv_remove_monitor_timer(&(a_state->zrdt_tlv_state));

            a_state->log.close_reason = reason;

            res = zpn_broker_assistant_auth_log_zrdt(zrdt_conn,
                                                     &(a_state->log),
                                                     a_state->assitant_gid,
                                                     ZPN_ASSISTANT_BROKER_DATA,
                                                     zrdt_conn_disconnected);

            if (res) {
                ZPN_LOG(AL_WARNING, "Assistant data connection auth log fail");
            }

            if (a_state->version) {
                ZPN_FREE(a_state->version);
                a_state->version = NULL;
            }

            if (a_state->cname) {
                ZPN_FREE(a_state->cname);
                a_state->cname = NULL;
            }

            if (a_state->public_cloud) {
                ZPN_FREE(a_state->public_cloud);
                a_state->public_cloud = NULL;
            }

            if (a_state->private_cloud) {
                ZPN_FREE(a_state->private_cloud);
                a_state->private_cloud = NULL;
            }

            if (a_state->region_id) {
                ZPN_FREE(a_state->region_id);
                a_state->region_id = NULL;
            }

            if (a_state->log.g_ast_country_code) {
                ZPN_FREE(a_state->log.g_ast_country_code);
                a_state->log.g_ast_country_code = NULL;
            }

            if (a_state->log.g_ast_city) {
                ZPN_FREE(a_state->log.g_ast_city);
                a_state->log.g_ast_city = NULL;
            }

            if (a_state->log.g_ast_subdivision_code) {
                ZPN_FREE(a_state->log.g_ast_subdivision_code);
                a_state->log.g_ast_subdivision_code = NULL;
            }

            zpn_zrdt_tlv_destroy(&(a_state->zrdt_tlv_state), MT_CLOSED_DTLS_CONN_GONE_AST_CLOSED);
            zpn_broker_a_state_active_q_del(a_state);
            zpn_broker_a_state_free(a_state);
        } else {
            /* No a_state?? */
            ZPN_LOG(AL_WARNING, "No a_state");
            zrdt_conn_set_dynamic_cookie(zrdt_conn, NULL);
            zrdt_conn_destroy(zrdt_conn);
        }
    }
}

int zpn_broker_assistant_listen_zrdt(char *sni_str, int wildcard_sni_prefix)
{
    if (broker.mux) {
        socklen_t len = sizeof(struct sockaddr_storage);
        struct sockaddr_storage zrdt_listen_address;
        int res;

        fohh_str_to_sockaddr_storage("0.0.0.0", &zrdt_listen_address, &len);
        fohh_sockaddr_set_port(&zrdt_listen_address, htons(ZPN_CONNECTOR_BROKER_DATA_PORT_UDP));

        res = zdtls_listen(broker.mux,
                           &zrdt_listen_address,
                           sni_str,
                           wildcard_sni_prefix,
                           ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                           ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                           ZPATH_LOCAL_PRIVATE_KEY_FILE,
                           zpn_broker_assistant_ctx_callback_zrdt,
                           NULL,
                           zpn_broker_assistant_zdtls_session_callback,
                           NULL,
                           0,      /* Indicating it is a server */
                           NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot listen for DTLS connection on broker assistant, sni = %s", sni_str);
            return ZPN_RESULT_ERR;
        }

        ZPN_DEBUG_ASSISTANT("Listening on DTLS connection on broker assistant side, sni = %s", sni_str);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int customer_log_collection_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    int res;
    res = zpath_customer_log_struct(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid),
                                    cust_log_collection_info->customer_log_type,
                                    cust_log_collection_info->log_type,
                                    NULL,
                                    NULL,
                                    NULL,
                                    NULL,
                                    cust_log_collection_info->log_description,
                                    data);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_inspection_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }
    struct argo_object* obj = (struct argo_object*) data;
    if (!obj) {
        return ZPN_RESULT_ERR;
    }
    int64_t siems[100];
    size_t siems_count = sizeof(siems) / sizeof(siems[0]);
    size_t i;
    int res;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

    res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_waf_http_exchanges_log, siems, &siems_count);
    if (res) {
        if (res == ZPN_RESULT_NOT_FOUND) {
            return ZPN_RESULT_NO_ERROR;
        }
        ZPN_LOG(AL_NOTICE, "Failed to get siem information for inspection log: %s", zpn_result_string(res));
        return res;
    }

    for (i = 0; i < siems_count; i++) {
        res = zpn_siem_log(siems[i], obj);
        if (res) {
            ZPN_LOG(AL_ERROR, "Customer %ld bad inspection log = %s to siem %ld",
                    (long) customer_gid, zpath_result_string(res), (long) siems[i]);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_app_inspection_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
	if (!cust_log_collection_info) {
		return ZPN_RESULT_ERR;
	}
	struct argo_object* obj = (struct argo_object*) data;
	if (!obj) {
		return ZPN_RESULT_ERR;
	}
	int64_t siems[100];
	size_t siems_count = sizeof(siems) / sizeof(siems[0]);
	int res;
	int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

	res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_app_inspection_log, siems, &siems_count);
	if (res) {
		if (res == ZPN_RESULT_NOT_FOUND) {
			return ZPN_RESULT_NO_ERROR;
		}
		ZPN_LOG(AL_NOTICE, "Failed to get siem information for app inspection log: %s", zpn_result_string(res));
		return res;
	}

	for (size_t i = 0; i < siems_count; i++) {
		res = zpn_siem_log(siems[i], obj);
		if (res) {
			ZPN_LOG(AL_ERROR, "Customer %ld bad inspection log = %s to siem %ld",
					(long) customer_gid, zpath_result_string(res), (long) siems[i]);
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

int zpn_krb_inspection_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
	if (!cust_log_collection_info) {
		return ZPN_RESULT_ERR;
	}
	struct argo_object* obj = (struct argo_object*) data;
	if (!obj) {
		return ZPN_RESULT_ERR;
	}
	int64_t siems[100];
	size_t siems_count = sizeof(siems) / sizeof(siems[0]);
	int res;
	int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

	res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_krb_inspection_log, siems, &siems_count);
	if (res) {
		if (res == ZPN_RESULT_NOT_FOUND) {
			return ZPN_RESULT_NO_ERROR;
		}
		ZPN_LOG(AL_NOTICE, "Failed to get siem information for krb inspection log: %s", zpn_result_string(res));
		return res;
	}

	for (size_t i = 0; i < siems_count; i++) {
		res = zpn_siem_log(siems[i], obj);
		if (res) {
			ZPN_LOG(AL_ERROR, "Customer %ld bad inspection log = %s to siem %ld",
					(long) customer_gid, zpath_result_string(res), (long) siems[i]);
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

int zpn_ldap_inspection_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
	if (!cust_log_collection_info) {
		return ZPN_RESULT_ERR;
	}
	struct argo_object* obj = (struct argo_object*) data;
	if (!obj) {
		return ZPN_RESULT_ERR;
	}
	int64_t siems[100];
	size_t siems_count = sizeof(siems) / sizeof(siems[0]);
	int res;
	int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

	res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_ldap_inspection_log, siems, &siems_count);
	if (res) {
		if (res == ZPN_RESULT_NOT_FOUND) {
			return ZPN_RESULT_NO_ERROR;
		}
		ZPN_LOG(AL_NOTICE, "Failed to get siem information for ldap inspection log: %s", zpn_result_string(res));
		return res;
	}

	for (size_t i = 0; i < siems_count; i++) {
		res = zpn_siem_log(siems[i], obj);
		if (res) {
			ZPN_LOG(AL_ERROR, "Customer %ld bad inspection log = %s to siem %ld",
					(long) customer_gid, zpath_result_string(res), (long) siems[i]);
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

int64_t zpn_broker_assistant_total_assistants_connections_to_redirect(const struct zpn_fohh_worker_assistant_stats* assistant_stats){
    return assistant_stats->num_assistant_ctrl
         + assistant_stats->num_assistant_cfg
         + assistant_stats->num_assistant_ovd
         + assistant_stats->num_assistant_stats
         + assistant_stats->num_assistant_logs
         + assistant_stats->num_assistant_data
         + assistant_stats->num_assistant_np_cfg;
}

int zpn_smb_inspection_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
	if (!cust_log_collection_info) {
		return ZPN_RESULT_ERR;
	}
	struct argo_object* obj = (struct argo_object*) data;
	if (!obj) {
		return ZPN_RESULT_ERR;
	}
	int64_t siems[100];
	size_t siems_count = sizeof(siems) / sizeof(siems[0]);
	int res;
	int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

	res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_smb_inspection_log, siems, &siems_count);
	if (res) {
		if (res == ZPN_RESULT_NOT_FOUND) {
			return ZPN_RESULT_NO_ERROR;
		}
		ZPN_LOG(AL_NOTICE, "Failed to get siem information for smb inspection log: %s", zpn_result_string(res));
		return res;
	}

	for (size_t i = 0; i < siems_count; i++) {
		res = zpn_siem_log(siems[i], obj);
		if (res) {
			ZPN_LOG(AL_ERROR, "Customer %ld bad inspection log = %s to siem %ld",
					(long) customer_gid, zpath_result_string(res), (long) siems[i]);
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

int zpn_inspection_api_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
	if (!cust_log_collection_info) {
		return ZPN_RESULT_ERR;
	}
	struct argo_object* obj = (struct argo_object*) data;
	if (!obj) {
		return ZPN_RESULT_ERR;
	}
	int64_t siems[100];
	size_t siems_count = sizeof(siems) / sizeof(siems[0]);
	int res;
	int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid);

	res = zpn_siem_get_by_type(customer_gid, zpn_siem_type_zpn_waf_http_api_exchanges_log, siems, &siems_count);
	if (res) {
		if (res == ZPN_RESULT_NOT_FOUND) {
			return ZPN_RESULT_NO_ERROR;
		}
		ZPN_LOG(AL_NOTICE, "Failed to get siem information for api inspection log: %s", zpn_result_string(res));
		return res;
	}

	for (size_t i = 0; i < siems_count; i++) {
		res = zpn_siem_log(siems[i], obj);
		if (res) {
			ZPN_LOG(AL_ERROR, "Customer %ld bad api inspection log = %s to siem %ld",
					(long) customer_gid, zpath_result_string(res), (long) siems[i]);
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_assistant_update_connection_snis_internal(struct fohh_generic_server *sni_server,
                                                          char *type, int wildcard_prefix,
                                                          char *old_alt_cloud, char *new_alt_cloud)
{
    char old_sni_str[ZPN_MAX_SNI_NAME_LEN + 1], new_sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    snprintf(old_sni_str, sizeof(old_sni_str), "%s.%s", type,
                    old_alt_cloud ? old_alt_cloud : zpn_broker_get_default_cloud_name());
    snprintf(new_sni_str, sizeof(new_sni_str), "%s.%s", type,
                    new_alt_cloud ? new_alt_cloud : zpn_broker_get_default_cloud_name());

    res = fohh_generic_server_re_register(sni_server, old_sni_str, new_sni_str, wildcard_prefix);
    if (res) {
        ZPN_LOG(AL_ERROR, "alt_cloud: Could not re-register assistant conn type: %s  generic server for %s to %s",
                            type, old_sni_str, new_sni_str);
        return;
    }
    zpn_broker_assistant_update_sni(old_sni_str, new_sni_str, wildcard_prefix);

    ZPN_LOG(AL_NOTICE, "alt_cloud: Updated assistant type: %s sni server from: %s  to: %s", type, old_sni_str, new_sni_str);
}

/*
 * zpn_broker_assistant_update_connection_snis
 *  Update SNIs for all connector connetion when alt-cloud changes
 */
void zpn_broker_assistant_update_connection_snis(char *old_alt_cloud, char *new_alt_cloud)
{
    struct fohh_generic_server *sni_server = NULL;
    char typestr[64];

    /* Lookup sni server for broker */
    sni_server = zpath_debug_lookup_fohh_generic_server("broker");
    if (!sni_server) {
        ZPN_LOG(AL_ERROR, "Unable to lookup sni_server for broker; returning");
        return;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        zpn_broker_assistant_update_connection_snis_internal(sni_server, "actl", 1, old_alt_cloud, new_alt_cloud);

        //adata
        zpn_broker_assistant_update_connection_snis_internal(sni_server, "adata", 1, old_alt_cloud, new_alt_cloud);
        snprintf(typestr, sizeof(typestr), "%ld.adata", (long)ZPN_BROKER_GET_GID());
        zpn_broker_assistant_update_connection_snis_internal(sni_server, typestr, 1, old_alt_cloud, new_alt_cloud);

        zpn_broker_assistant_update_connection_snis_internal(sni_server, "acfg", 1, old_alt_cloud, new_alt_cloud);
        zpn_broker_assistant_update_connection_snis_internal(sni_server, "aovd", 1, old_alt_cloud, new_alt_cloud);
        zpn_broker_assistant_update_connection_snis_internal(sni_server, "alog", 1, old_alt_cloud, new_alt_cloud);
        zpn_broker_assistant_update_connection_snis_internal(sni_server, "astats", 1, old_alt_cloud, new_alt_cloud);

    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        snprintf(typestr, sizeof(typestr), "%ld.a2pb_ctl", (long)ZPN_BROKER_GET_GID());
        zpn_broker_assistant_update_connection_snis_internal(sni_server, typestr, 1, old_alt_cloud, new_alt_cloud);

        //adata
        snprintf(typestr, sizeof(typestr), "%ld.adata", (long)ZPN_BROKER_GET_GID());
        zpn_broker_assistant_update_connection_snis_internal(sni_server, typestr, 1, old_alt_cloud, new_alt_cloud);
    }
}

struct assistant_log_info {
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;

};

static void* zpn_broker_assistant_app_info_callback(struct fohh_connection *f_conn)
{
    int64_t asst_id;
    struct assistant_log_info *asst;
    int res;
    struct zpn_assistant *assistant = NULL;

    asst_id = fohh_peer_get_id(f_conn);
    if (!asst_id) return NULL;

    asst = ZPN_CALLOC(sizeof(*asst));

    /* Generate an ID for this client */
    res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(asst);
        return NULL;
    }
    base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    asst->f_conn = f_conn;
    asst->f_conn_incarnation = fohh_connection_incarnation(f_conn);

    res = zpn_assistant_get_by_id(asst_id,
                                    &assistant,
                                    NULL,
                                    NULL,
                                    0);
    if (res) {
        ZPN_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) asst_id,
                                zpn_result_string(res));
    }

    if(res != ZPN_RESULT_NO_ERROR) {
        ZPN_FREE(asst);
        return NULL;
    } else {
        asst->customer_gid = assistant->customer_gid;
        asst->scope_gid    = assistant->scope_gid;
    }

    return asst;
}

static void zpn_broker_assistant_log_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    if (state == fohh_connection_connected) {
        zpn_fohh_worker_assistant_connect_log(f_conn->fohh_thread_id);
    } else {
        zpn_fohh_worker_assistant_disconnect_log(f_conn->fohh_thread_id);
    }
}

static void zpn_broker_assistant_log_conn_redirect(struct fohh_connection *f_conn, void *cookie)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_assistants, NULL)) {
        return;
    }

    struct assistant_log_info *asst = (struct assistant_log_info *)cookie;
    if (!asst) return;
    ZPN_LOG(AL_DEBUG, "%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), asst->tunnel_id);

    if (fohh_connection_incarnation(f_conn) != asst->f_conn_incarnation) {
        return;
    }

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    zpn_broker_assistant_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);

    zpn_broker_balance_conn_redirect(f_conn,
                                     asst->customer_gid,
                                     asst->scope_gid,
                                     &peer_ip, &peer_site,
                                     asst->tunnel_id,
                                     redirect_assistants,
                                     redirect_assistants_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_assistant,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].assistant_stats.num_asst_logs_alt_cloud_redirects), 1);
    }
}

static int64_t get_data_broker_gid_from_sni(const char *sni)
{
    char tmp[300] = {0};
    char *str;
    char *token;
    int i = 0;

    snprintf(tmp, sizeof(tmp), "%s", sni);
    str = tmp;

    /* SNI format: <asst GID>.<data broker GID>.adata.<cloud name> */
    while ((token = strtok_r(str, ".", &str))) {
        if (i == 1) {
            if (isdigit(token[0])) {
                return strtoll(token, NULL, 10);
            } else {
                return 0;
            }
        }
        i++;
    }
    return 0;
}

static void zpn_broker_assistant_adata_accept_cb(struct fohh_thread *f_thread,
                                                    int sock, struct sockaddr *sa,
                                                    int sa_len,
                                                    const char *sni,
                                                    const char *sni_suffix,
                                                    void *cookie,
                                                    void *bev,
                                                    struct pktinfo_s *pktinfo)
{
    struct fohh_connection *listen_f_conn = cookie;

    /* Drop old connector connection with SNI: adata.<cloud name> */
    if (strncmp(sni, "adata.", strlen("adata.")) == 0) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[listen_f_conn->fohh_thread_id].assistant_stats.num_asst_proxy_old_sni_error), 1);
        ZPN_LOG(AL_ERROR, "TWO HOP zpn_broker_assistant_adata_accept_cb: Old SNI error. Received sni: %s", sni);
        return;
    }

    //parse SNI and get the broker_id.
    int64_t broker_gid = get_data_broker_gid_from_sni(sni);
    if (broker_gid == 0) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[listen_f_conn->fohh_thread_id].assistant_stats.num_asst_proxy_sni_gid_error), 1);
        ZPN_LOG(AL_ERROR, "TWO HOP zpn_broker_assistant_adata_accept_cb: gid 0. Received sni: %s", sni);
        return;
    }

    if (broker_gid == ZPATH_INSTANCE_GID) {
        ZPN_LOG(AL_ERROR, "TWO_HOP zpn_broker_assistant_adata_accept_cb: ERROR: it is our gid, server registed with sni: broker_gid.adata.cloud_name should get this connetion with SNI: %s", sni);
        __sync_fetch_and_add_8(&(zpn_fohh_workers[listen_f_conn->fohh_thread_id].assistant_stats.num_asst_proxy_sni_own_gid_error), 1);
        return;
    } else {
        ZPN_LOG(AL_DEBUG, "TWO_HOP zpn_broker_assistant_adata_accept_cb: broker gid is not ours. SNI: %s", sni);
        char domain_name[256] = {0};

        struct zpath_instance *broker_instance = zpath_instance_get_no_locking(broker_gid);
        if (broker_instance == NULL) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[listen_f_conn->fohh_thread_id].assistant_stats.num_asst_proxy_sni_broker_instance_error), 1);
            ZPN_LOG(AL_ERROR, "TWO_HOP zpn_broker_assistant_adata_accept_cb: Cannot get the data broker instance from gid, for sni: %s", sni);
            return;
        }

        ZPN_LOG(AL_DEBUG, "TWO_HOP zpn_broker_assistant_adata_accept_cb: calling zpath_instance_to_domain_name for SNI: %s", sni);
        zpath_instance_to_domain_name(broker_instance, domain_name, sizeof(domain_name));

        ZPN_LOG(AL_DEBUG, "TWO_HOP zpn_broker_assistant_adata_accept_cb: call broker_proxy_accept for SNI: %s", sni);
        start_broker_proxy(f_thread, sock, sa, sa_len, sni, sni_suffix, cookie, bev, pktinfo, domain_name);
    }
}

/*
 * Listen for
 *  a. config channel (public broker only)
 *  b. control channel (public broker only)
 *  c. data channel
 *  d. log collection (public broker only)
 *  e. stats collection (public broker only)
 */
int zpn_broker_assistant_listen(struct fohh_generic_server* sni_server,
                                enum Zpn_Instance_Type      broker_personality)
{
    EVP_PKEY *pkey = NULL;

    struct fohh_connection *f_conn;
    static int initialized = 0;
    int i;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    ZPN_LOG(AL_NOTICE, "Initializing listening for connectors");

    res = zpn_broker_init_connected_assistants();
    if (res) {
        return res;
    }

    /* Only init the config connection state for public broker */
    if ((ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) &&
        (!initialized)) {

        /* to be able to control the logging of the connected assistant from broker */
        res = assistant_log_init();
        if (res) {
            return res;
        }

        for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
            ZPN_LOG(AL_NOTICE, "Initializing Wally Server for Assistants, shard %d", i);
            wally_servers[i] = wally_fohh_server_create(zpath_shard_wally[i],
                                                        argo_serialize_binary,
                                                        fohh_connection_style_argo,
                                                        1,
                                                        NULL,
                                                        0,
                                                        NULL,
                                                        NULL,
                                                        NULL,
                                                        1);
            if (!wally_servers[i]) {
                ZPN_LOG(AL_ERROR, "Could not initialize wally server for shard %d", i);
                return ZPN_RESULT_ERR;
            }
        }

        ZPN_LOG(AL_NOTICE, "Initializing Global Wally Server for Assistants");
        wally_server_global = wally_fohh_server_create(zpath_global_wally,
                                                       argo_serialize_binary,
                                                       fohh_connection_style_argo,
                                                       1,
                                                       NULL,
                                                       0,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       1);
        if (!wally_server_global) {
            ZPN_LOG(AL_ERROR, "Could not initialize global wally server");
            return ZPN_RESULT_ERR;
        }

        for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
            ZPN_LOG(AL_NOTICE, "Initializing Wally Server for Assistants,np shard %d", i);
            np_wally_servers[i] = wally_fohh_server_create(zpath_np_shard_wally[i],
                                                           argo_serialize_binary,
                                                           fohh_connection_style_argo,
                                                           1,
                                                           NULL,
                                                           0,
                                                           NULL,
                                                           NULL,
                                                           NULL,
                                                           1);
            if (!np_wally_servers[i]) {
                ZPN_LOG(AL_ERROR, "Could not initialize np wally server for shard %d", i);
                return ZPN_RESULT_ERR;
            }
        }

        initialized = 1;
    }

    if (ZPN_INSTANCE_TYPE_PRIVATE_BROKER == broker_personality) {
        pkey = g_broker_common_cfg->private_broker.pkey_mem;
    }

    if (!sni_server) return ZPN_RESULT_ERR;

    /* create cloud_name after checking alt-cloud name */
    char cloud_name[ZPN_MAX_CLOUD_NAME_LEN + 1];
    zpn_broker_get_cloud_name(cloud_name, sizeof(cloud_name));

    /*
     * We need to restrict the control connections to this private broker from only those
     * app connectors, which have the same scope_gid. The best place to make this check
     * would be in the fohh verify callback.
     */

    /* Control connection: Both private and public broker do it. */
    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_broker_assistant_control_conn_callback,
                                   NULL,
                                   zpn_broker_assistant_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                   zpn_broker_assistant_verify_control_conn_callback_fohh, // verify callback
                                   (ZPN_BROKER_IS_PUBLIC() ? zpn_broker_client_post_verify_region_check_cb : NULL ),
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    fohh_set_info_callback(f_conn, zpn_broker_assistant_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) {
        snprintf(sni_str, sizeof(sni_str), "actl.%s", cloud_name);
    } else if (ZPN_INSTANCE_TYPE_PRIVATE_BROKER == broker_personality) {
        snprintf(sni_str, sizeof(sni_str), "%ld.a2pb_ctl.%s", (long)ZPN_BROKER_GET_GID(), cloud_name);
        /*
         * NOT YET SUPPORTED: connector's co2br pointing to pbroker. For that support, pbroker should also setup proxy
         * for actl.
         */
    } else {
        ZPN_BROKER_ASSERT_HARD(0, "Broker assistant listen: Invalid broker personality!ß");
    }
    ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_ACTL);
    if (res) {
        return res;
    }

    zpn_broker_assistant_add_sni(sni_str, 1);

    /* Data connection: Both private and public broker do it. */
    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo_tlv, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_broker_assistant_data_conn_callback,
                                   zpn_fohh_tlv_data_callback,
                                   zpn_broker_assistant_data_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                   zpn_broker_assistant_verify_callback_fohh, // verify callback
                                   (ZPN_BROKER_IS_PUBLIC() ? zpn_broker_client_post_verify_region_check_cb : NULL), // post verify callback
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // Timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }
    fohh_set_version_callback(f_conn, data_conn_version_callback);

    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) {
        /*
         * For backward compatibility(think older connectors).
         */
        snprintf(sni_str, sizeof(sni_str), "adata.%s", cloud_name);
        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);

        res = fohh_generic_server_register_accept(sni_server,
                                            NULL,
                                            zpn_broker_assistant_adata_accept_cb,
                                            f_conn,
                                            NULL,
                                            sni_str,
                                            1,
                                            FOHH_WORKER_ZPN_ADATA);

        if (res) {
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);

        /*
         * Listen on zrdt data connection from older connectors
         */
        res = zpn_broker_assistant_listen_zrdt(sni_str, 1);
        if (res) {
            return res;
        }
    }
    snprintf(sni_str, sizeof(sni_str), "%ld.adata.%s", (long)ZPN_BROKER_GET_GID(), cloud_name);
    ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_ADATA);
    if (res) {
        return res;
    }
    zpn_broker_assistant_add_sni(sni_str, 1);

    /*
     * Listen on zrdt data connection from newer connectors
     */
    res = zpn_broker_assistant_listen_zrdt(sni_str, 1);
    if (res) {
        return res;
    }

    /* Only do config connection for public broker */
    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) {
        /* Config connection sni server */
        f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_assistant_config_conn_callback,
                                    NULL,
                                    zpn_broker_assistant_config_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require_assistant_cert,
                                    1, // int use_ssl);
                                    zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                    zpn_broker_assistant_verify_callback_fohh, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                    1, // Allow binary argo.
                                    ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        fohh_set_info_callback(f_conn, zpn_broker_assistant_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

        snprintf(sni_str, sizeof(sni_str), "acfg.%s", cloud_name);
        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_ACFG);
        if (res) {
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);

       f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_assistant_config_override_conn_callback,
                                    NULL,
                                    zpn_broker_assistant_config_override_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require_assistant_cert,
                                    1, // int use_ssl);
                                    zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                    zpn_broker_assistant_verify_callback_fohh, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                    1, // Allow binary argo.
                                    ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        fohh_set_info_callback(f_conn, zpn_broker_assistant_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

        snprintf(sni_str, sizeof(sni_str), "aovd.%s", cloud_name);
        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_AOVD);
        if (res) {
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);

        /* NP Config connection sni server */
        f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_assistant_np_config_conn_callback,
                                    NULL,
                                    zpn_broker_assistant_config_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require_assistant_cert,
                                    1, // int use_ssl);
                                    zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                    zpn_broker_assistant_verify_callback_fohh, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                    1, // Allow binary argo.
                                    ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        fohh_set_info_callback(f_conn, zpn_broker_assistant_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

        snprintf(sni_str, sizeof(sni_str), "anpcfg.%s", cloud_name);
        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_ACFG);
        if (res) {
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);


        /* amc starts */
        /* amc connection to public broker */
        f_conn = fohh_server_create_ex(0, //int quiet,
                                    argo_serialize_binary, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_assistant_mission_critical_conn_callback,
                                    NULL,
                                    zpn_broker_assistant_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    pkey,
                                    1, // int require_assistant_cert,
                                    1, // int use_ssl);
                                    zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                    zpn_broker_assistant_verify_control_conn_callback_fohh, // verify callback
                                    (ZPN_BROKER_IS_PUBLIC() ? zpn_broker_client_post_verify_region_check_cb : NULL ),
                                    1, // Allow binary argo.
                                    ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        fohh_set_info_callback(f_conn, zpn_broker_assistant_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

        snprintf(sni_str, sizeof(sni_str), "amc.%s", cloud_name);

        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
        res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_MC);
        if (res) {
            return res;
        }

        zpn_broker_assistant_add_sni(sni_str, 1);

    /* amc ends */

    } else {
        /*
         * NOT YET SUPPORTED: connector's co2br pointing to pbroker. For that support, pbroker should setup proxy
         * for actl.
         */
    }

    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) {
        struct zhash_table* event_log_collection_map = zhash_table_alloc(&zpn_allocator);
        if (!event_log_collection_map) {
            ZPN_LOG(AL_ERROR, "Out of memory");
            return ZPN_RESULT_NO_MEMORY;
        }

        struct customer_log_collection_info *event_log;
        ZPN_BROKER_ASSERT_SOFT((NULL != zpn_event_collection),
                               "event collection is not yet initialized");
        event_log = create_customer_log_collection(NULL,0, NULL, zpn_event_collection, 0, NULL, NULL, NULL, 0);
        if (!event_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        char* zpn_event_collection_name = argo_log_get_name(zpn_event_collection);
        int is_data_external;
        if (zhash_table_store(event_log_collection_map, zpn_event_collection_name, strlen(zpn_event_collection_name),
                              is_data_external = 1, event_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event log receiver for assistant");
        }

        if (zpn_ast_waf_http_exchanges_log_description == NULL) {
            zpn_ast_waf_http_exchanges_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_LOG_HELPER);
            if (!zpn_ast_waf_http_exchanges_log_description) {
                ZPN_LOG(AL_ERROR, "Failed to set up WAF http log description");
                return ZPN_RESULT_NO_MEMORY;
            }
        }

        struct customer_log_collection_info *waf_log;
        waf_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_http_inspection,
                                                 zpn_ast_waf_http_exchanges_log_description,
                                                 zpn_ast_waf_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_inspection_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!waf_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up WAF http log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add waf log collection to log collection hash-map */
        char* zpn_waf_log_name = argo_log_get_name(zpn_ast_waf_collection);
        if (zhash_table_store(event_log_collection_map, zpn_waf_log_name, strlen(zpn_waf_log_name),
                              is_data_external = 1, waf_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event WAF http log receiver for WAF assistant");
        }

        /* API Protection Logs */
        if (zpn_ast_waf_http_exchanges_api_log_description == NULL) {
            zpn_ast_waf_http_exchanges_api_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_API_LOG_HELPER);
            if (!zpn_ast_waf_http_exchanges_api_log_description) {
                ZPN_LOG(AL_ERROR, "Failed to set up WAF http api log description");
                return ZPN_RESULT_NO_MEMORY;
            }
        }

        struct customer_log_collection_info *waf_api_log;
        waf_api_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_http_api_inspection,
                                                 zpn_ast_waf_http_exchanges_api_log_description,
                                                 zpn_ast_waf_api_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_inspection_api_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!waf_api_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up WAF http API log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add API waf log collection to log collection hash-map */
        char* zpn_waf_api_log_name = argo_log_get_name(zpn_ast_waf_api_collection);
        if (zhash_table_store(event_log_collection_map, zpn_waf_api_log_name, strlen(zpn_waf_api_log_name),
                              /*is_data_external*/ 1, waf_api_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event WAF http API log receiver for WAF assistant");
        }

        /* create app inspection log collection */
        if (zpn_ast_app_inspection_log_description == NULL) {
            zpn_ast_app_inspection_log_description = argo_register_global_structure(ZPN_APP_INSPECTION_LOG_HELPER);
            if (!zpn_ast_app_inspection_log_description) {
                ZPN_LOG(AL_ERROR, "Failed to set up application inspection log description");
                return ZPN_RESULT_NO_MEMORY;
            }
        }

        struct customer_log_collection_info *app_inspection_log;
        app_inspection_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_app_inspection,
                                                 zpn_ast_app_inspection_log_description,
                                                 zpn_ast_app_inspection_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_app_inspection_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!app_inspection_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up app inspection log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add app inspection log collection to log collection hash-map */
        char* zpn_app_inspection_log_name = argo_log_get_name(zpn_ast_app_inspection_collection);
        size_t zpn_app_inspection_log_name_len = (zpn_app_inspection_log_name && zpn_app_inspection_log_name[0])?strlen(zpn_app_inspection_log_name):0;
        if (zhash_table_store(event_log_collection_map, zpn_app_inspection_log_name, zpn_app_inspection_log_name_len,
                    /* is_data_external */ 1, app_inspection_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event app inspection log receiver for assistant");
        }

        /* create krb inspection log collection */
        zpn_ast_krb_inspection_log_description = argo_register_global_structure(ZPN_KRB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_krb_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "Failed to set up krb inspection log description");
            return ZPN_RESULT_NO_MEMORY;
        }
        struct customer_log_collection_info *krb_inspection_log;
        krb_inspection_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_krb_inspection,
                                                 zpn_ast_krb_inspection_log_description,
                                                 zpn_ast_krb_inspection_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_krb_inspection_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!krb_inspection_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up krb inspection log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add krb inspection log collection to log collection hash-map */
        char* zpn_krb_inspection_log_name = argo_log_get_name(zpn_ast_krb_inspection_collection);
        size_t zpn_krb_inspection_log_name_len = (zpn_krb_inspection_log_name && zpn_krb_inspection_log_name[0])?strlen(zpn_krb_inspection_log_name):0;
        if (zhash_table_store(event_log_collection_map, zpn_krb_inspection_log_name, zpn_krb_inspection_log_name_len,
                    /* is_data_external */ 1, krb_inspection_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event krb inspection log receiver for assistant");
        }

        /* create ldap inspection log collection */
        zpn_ast_ldap_inspection_log_description = argo_register_global_structure(ZPN_LDAP_INSPECTION_LOG_HELPER);
        if (!zpn_ast_ldap_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "Failed to set up ldap inspection log description");
            return ZPN_RESULT_NO_MEMORY;
        }
        struct customer_log_collection_info *ldap_inspection_log;
        ldap_inspection_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_ldap_inspection,
                                                 zpn_ast_ldap_inspection_log_description,
                                                 zpn_ast_ldap_inspection_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_ldap_inspection_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!ldap_inspection_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up ldap inspection log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add ldap inspection log collection to log collection hash-map */
        char* zpn_ldap_inspection_log_name = argo_log_get_name(zpn_ast_ldap_inspection_collection);
        size_t zpn_ldap_inspection_log_name_len = (zpn_ldap_inspection_log_name && zpn_ldap_inspection_log_name[0])?strlen(zpn_ldap_inspection_log_name):0;
        if (zhash_table_store(event_log_collection_map, zpn_ldap_inspection_log_name, zpn_ldap_inspection_log_name_len,
                    /* is_data_external */ 1, ldap_inspection_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event ldap inspection log receiver for assistant");
        }

        /* create smb inspection log collection */
        zpn_ast_smb_inspection_log_description = argo_register_global_structure(ZPN_SMB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_smb_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "Failed to set up smb inspection log description");
            return ZPN_RESULT_NO_MEMORY;
        }
        struct customer_log_collection_info *smb_inspection_log;
        smb_inspection_log = create_customer_log_collection(NULL,
                                                 zpath_customer_log_type_zpn_smb_inspection,
                                                 zpn_ast_smb_inspection_log_description,
                                                 zpn_ast_smb_inspection_collection,
                                                 1, // transmit to log infra
                                                 customer_log_collection_callback,
                                                 zpn_smb_inspection_log_siem_callback,
                                                 NULL,
                                                 0);
        if (!smb_inspection_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up smb inspection log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add smb inspection log collection to log collection hash-map */
        char* zpn_smb_inspection_log_name = argo_log_get_name(zpn_ast_smb_inspection_collection);
        size_t zpn_smb_inspection_log_name_len = (zpn_smb_inspection_log_name && zpn_smb_inspection_log_name[0])?strlen(zpn_smb_inspection_log_name):0;
        if (zhash_table_store(event_log_collection_map, zpn_smb_inspection_log_name, zpn_smb_inspection_log_name_len,
                    /* is_data_external */ 1, smb_inspection_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event smb inspection log receiver for assistant");
        }

        /* create ptag log collection */
        if (zpn_ast_ptag_log_description == NULL) {
            zpn_ast_ptag_log_description = argo_register_global_structure(ZPN_PTAG_LOG_HELPER);
            if (!zpn_ast_ptag_log_description) {
                ZPN_LOG(AL_ERROR, "Failed to set up PTag log description");
                return ZPN_RESULT_NO_MEMORY;
            }
        }

        struct customer_log_collection_info *ptag_log;
        ptag_log = create_customer_log_collection(NULL,
                zpath_customer_log_type_zpn_ptag,
                zpn_ast_ptag_log_description,
                zpn_ast_ptag_collection,
                1, // transmit to log infra
                customer_log_collection_callback,
                NULL,
                NULL,
                0);
        if (!ptag_log) {
            ZPN_LOG(AL_ERROR, "Out of memory, when setting up ptag log receiver for assistant");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* add ptag log collection to log collection hash-map */
        char* zpn_ptag_log_name = argo_log_get_name(zpn_ast_ptag_collection);
        size_t zpn_ptag_log_name_len = (zpn_ptag_log_name && zpn_ptag_log_name[0])?strlen(zpn_ptag_log_name):0;
        if (zhash_table_store(event_log_collection_map, zpn_ptag_log_name, zpn_ptag_log_name_len,
                    /* is_data_external */ 1, ptag_log)) {
            ZPN_LOG(AL_ERROR, "Failed to setup event PTag log receiver for assistant");
        }

        snprintf(sni_str, sizeof(sni_str), "alog.%s", cloud_name);

        int wildcard_prefix;

        res = fohh_log_receive(sni_server, sni_str, wildcard_prefix = 1, zpn_broker_assistant_ctx_callback_fohh,
                               zpn_broker_assistant_verify_callback_fohh,
                               fohh_log_conn_info_callback,
                               zpn_broker_verify_alt_cloud_info_callback,
                               zpn_broker_client_post_verify_region_check_cb,
                               zpn_broker_assistant_app_info_callback,
                               zpn_broker_assistant_log_conn_redirect,
                               zpn_broker_assistant_log_stats_cb,
                               zpn_broker_assistant_log_connection_monitor_timer_cb,
                               zpn_broker_assistant_log_connection_periodic_logging_timer_cb,
                               event_log_collection_map,
                               NULL,
                               0);

        if (res) {
            ZPN_LOG(AL_ERROR, "Could not set up event log receiver for assistant: %s", zpn_result_string(res));
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);
        ZPN_LOG(AL_NOTICE, "Done setting up event log receiver for assistant");
    }

    /* Stats log connection: Only for public broker. */
    if (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == broker_personality) {
        f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_binary, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_assistant_stats_conn_callback,
                                    NULL,
                                    zpn_broker_assistant_stats_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require_assistant_cert,
                                    1, // int use_ssl);
                                    zpn_broker_assistant_ctx_callback_fohh, // ssl ctx callback
                                    zpn_broker_assistant_verify_callback_fohh, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                    1, // Allow binary argo.
                                    ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        fohh_set_info_callback(f_conn, zpn_broker_assistant_stats_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

        snprintf(sni_str, sizeof(sni_str), "astats.%s", cloud_name);

        ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_ASTATS);
        if (res) {
            return res;
        }
        zpn_broker_assistant_add_sni(sni_str, 1);
    }
    zpn_broker_quickack_enable(f_conn, 0);

    return ZPN_RESULT_NO_ERROR;
}


int
zpn_broker_assistant_log_upload_toggle(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    int64_t assistant_id = 0;
    const char *w;
    struct connected_assistant *asst;

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            assistant_id = strtoll(w, NULL, 0);
            break;
        }
    }

    //ZPN_LOG(AL_DEBUG, "zpn_broker_assistant_log(), assistant name = %s, id = %ld", query_values[0], (long)assistant_id);

    pthread_mutex_lock(&(asst_lock));

    asst = zpn_broker_find_connected_active_assistant(assistant_id);
    if (asst) {
        if (asst->log_upload) {
            zpath_debug_cb_printf_response(request_state, "Assistant %s log upload was enabled, disabling it\n", query_values[0]);
            asst->log_upload = 0;
        } else {
            zpath_debug_cb_printf_response(request_state, "Assistant %s log upload was disabled, enabling it\n", query_values[0]);
            asst->log_upload = 1;
        }

        zpn_send_zpn_assistant_log_control(asst->f_conn,
                                           asst->f_conn_incarnation,
                                           assistant_id,
                                           0,
                                           asst->log_upload,
                                           0);
    } else {
        zpath_debug_cb_printf_response(request_state, "Assistant %s doesn't seem to be connected.\n", query_values[0]);
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_assistant_stats_upload_toggle(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    int64_t assistant_id = 0;
    const char *w;
    struct connected_assistant *asst;

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            assistant_id = strtoll(w, NULL, 0);
            break;
        }
    }

    //ZPN_LOG(AL_DEBUG, "zpn_broker_assistant_stats(), assistant name = %s, id = %ld", query_values[0], (long)assistant_id);

    pthread_mutex_lock(&(asst_lock));

    asst = zpn_broker_find_connected_active_assistant(assistant_id);
    if (asst) {
        if (asst->stats_upload) {
            zpath_debug_cb_printf_response(request_state, "Assistant %s stats upload was enabled, disabling it\n", query_values[0]);
            asst->stats_upload = 0;
        } else {
            zpath_debug_cb_printf_response(request_state, "Assistant %s stats upload was disabled, enabling it\n", query_values[0]);
            asst->stats_upload = 1;
        }

        zpn_send_zpn_assistant_stats_control(asst->f_conn,
                                           asst->f_conn_incarnation,
                                           assistant_id,
                                           asst->stats_upload);
    } else {
        zpath_debug_cb_printf_response(request_state, "Assistant %s doesn't seem to be connected.\n", query_values[0]);
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Restart a connected assistant from broker.
 * (eg)
 * curl '127.0.0.1:8000/broker/assistant/restart?cn=asst-217307152769876152.bulkramesh.zscalerbeta.net'
 */
int zpn_broker_assistant_restart(struct zpath_debug_state* request_state,
                                 const char**              query_values,
                                 int                       query_value_count,
                                 void*                     cookie)
{
    int64_t assistant_id = 0;
    const char *w;
    struct connected_assistant *asst;

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            assistant_id = strtoll(w, NULL, 0);
            break;
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = zpn_broker_find_connected_active_assistant(assistant_id);
    if (asst) {
        zpn_send_zpn_assistant_restart(asst->f_conn,
                                       asst->f_conn_incarnation,
                                       assistant_id);
    } else {
        zpath_debug_cb_printf_response(request_state, "Assistant %s doesn't seem to be connected.\n", query_values[0]);
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_assistant_debug_flag(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int64_t assistant_id = 0;
    const char *w;
    struct connected_assistant *asst;

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            assistant_id = strtoll(w, NULL, 0);
            break;
        }
    }

    pthread_mutex_lock(&(asst_lock));

    asst = zpn_broker_find_connected_active_assistant(assistant_id);
    if (asst) {
        uint64_t count;

        if (!query_values[1]) {
            zpath_debug_cb_printf_response(request_state, "DEBUG, %s = %"PRIx64"\n", query_values[0], asst->debug_flag);
            for (count = 0; assistant_log_debug_names[count]; count++) {
                zpath_debug_cb_printf_response(request_state,
                                               "%s : %s\n",
                                               (asst->debug_flag & (1u << count)) ? "ON" : "OFF",
                                               assistant_log_debug_names[count]);
            }
        } else {
            ZPATH_LOG(AL_DEBUG, "Setting remote debugging for assistant name = %s, id = %ld flag = %"PRIx64,
                      query_values[0], (long)assistant_id, (uint64_t)query_values[1]);

            for (count = 0; assistant_log_debug_names[count]; count++) {
                if (!strncmp(query_values[1], assistant_log_debug_names[count], strlen(assistant_log_debug_names[count]))) {
                    zpath_debug_cb_printf_response(request_state,
                                                   "Debug flag %s was %s, setting it to %s\n",
                                                   query_values[1],
                                                   (asst->debug_flag & (1u << count)) ? "ON" : "OFF",
                                                   (asst->debug_flag & (1u << count)) ? "OFF" : "ON");

                    if (asst->debug_flag & (1u << count)) {
                        asst->debug_flag &= ~(1u << count);
                    } else {
                        asst->debug_flag |= (1u << count);
                    }

                    zpn_send_zpn_assistant_log_control(asst->f_conn,
                                                       asst->f_conn_incarnation,
                                                       assistant_id,
                                                       1,
                                                       0,
                                                       count);
                    break;
                }
            }

            if (!assistant_log_debug_names[count]) {
                /* Flag name is wrong */
                zpath_debug_cb_printf_response(request_state, "Cannot find flag %s\n", query_values[1]);
            }
        }
    } else {
        zpath_debug_cb_printf_response(request_state, "Assistant %s doesn't seem to be connected.\n", query_values[0]);
    }

    pthread_mutex_unlock(&(asst_lock));

    return ZPATH_RESULT_NO_ERROR;
}

void zpn_broker_a_state_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(free_q.list));
}

static struct zpn_broker_assistant_fohh_state *zpn_broker_a_state_alloc(void)
{
    struct zpn_broker_assistant_fohh_state *a_state = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&free_q.lock, __FILE__, __LINE__);

    if ((a_state = TAILQ_FIRST(&(free_q.list)))) {
        TAILQ_REMOVE(&(free_q.list), a_state, entry);
        free_q.stats.free_queue_count--;
        incarnation = a_state->incarnation;
        incarnation++;
        zpn_broker_assistant_free_capability(&a_state->capability);
        memset(a_state, 0, sizeof(struct zpn_broker_assistant_fohh_state));
        a_state->incarnation = incarnation;
    } else {
        a_state = ZPN_CALLOC(sizeof(struct zpn_broker_assistant_fohh_state));
        if (a_state) {
            free_q.stats.allocations++;
            memset(a_state, 0, sizeof(struct zpn_broker_assistant_fohh_state));
            a_state->incarnation = 1;
        }
    }

    zpn_broker_assistant_init_capability(&a_state->capability);
    ZPATH_MUTEX_UNLOCK(&free_q.lock, __FILE__, __LINE__);

    return a_state;
}


int
zpn_broker_assistant_get_astate_incarnation(struct zpn_broker_assistant_fohh_state *a_state)
{
    return a_state->incarnation;
}


static void zpn_broker_a_state_free(struct zpn_broker_assistant_fohh_state *a_state)
{
    ZPATH_MUTEX_LOCK(&free_q.lock, __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(free_q.list), a_state, entry);
    free_q.stats.free_queue_count++;
    ZPATH_MUTEX_UNLOCK(&free_q.lock, __FILE__, __LINE__);
}

void zpn_broker_a_state_active_q_init()
{
    memset(&active_q, 0, sizeof(active_q));
    active_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(active_q.list));
    active_q.tbl =  zhash_table_alloc(&zpn_allocator);
}

static void zpn_broker_a_state_active_q_add(struct zpn_broker_assistant_fohh_state *a_state)
{
    ZPATH_MUTEX_LOCK(&active_q.lock, __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(active_q.list), a_state, entry);
    zhash_table_store(active_q.tbl, a_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, 0, a_state);
    ZPATH_MUTEX_UNLOCK(&active_q.lock, __FILE__, __LINE__);
}

static void zpn_broker_a_state_active_q_del(struct zpn_broker_assistant_fohh_state *a_state)
{
    ZPATH_MUTEX_LOCK(&active_q.lock, __FILE__, __LINE__);
    TAILQ_REMOVE(&(active_q.list), a_state, entry);
    zhash_table_remove(active_q.tbl, a_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, a_state);
    ZPATH_MUTEX_UNLOCK(&active_q.lock, __FILE__, __LINE__);
}

static
int zpn_broker_assistant_data_dump_all(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct zpn_broker_assistant_fohh_state *a_state;

    ZPATH_MUTEX_LOCK(&active_q.lock, __FILE__, __LINE__);

    TAILQ_FOREACH(a_state, &(active_q.list), entry) {
        ZDP("* tunnel_id = %s, conn_type = %s, gid = %"PRId64" capability ASC[%d] APU[%d] version = %s cname = %s\n",
            a_state->tunnel_id, zpn_tlv_type_str(a_state->tlv_type), a_state->assitant_gid,
            a_state->capability.capability_sticky_cache_enabled,
            a_state->capability.capability_pathing_from_ubrk_enabled, a_state->version, a_state->cname);
    }

    ZPATH_MUTEX_UNLOCK(&active_q.lock, __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 *  FIXME, ZRDT, I am duplicating the verify code from fohh here. Will try to optimized it later.
 */
static int zpn_broker_assistant_verify_callback_callback_zrdt(void *response_callback_cookie,
                                                           struct wally_registrant *registrant,
                                                           struct wally_table *table,
                                                           int64_t request_id,
                                                           int row_count)
{
    struct zrdt_conn *z_conn = response_callback_cookie;

    if (zrdt_conn_incarnation(z_conn) != request_id) {
        ZPN_LOG(AL_INFO, "zpn_broker_assistant_verify_callback_callback_zrdt() come back too late, old zrdt conn is gone");
        return ZPN_RESULT_NO_ERROR;
    }

    zrdt_conn_verify(z_conn);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_assistant_verify_callback_zrdt(struct zrdt_conn *z_conn)
{
    struct zpn_tlv tlv;
    int res;

    zpn_tlv_init(&tlv, zpn_zrdt_tlv, z_conn, 0);
    res = zpn_broker_assistant_verify_callback_common(&tlv, 0);

    if (res == ZPN_RESULT_NO_ERROR) {
        struct zdtls_session *zd_sess = zrdt_conn_get_datagram_tx_cookie(z_conn);
        struct zpn_broker_assistant_fohh_state *a_state = zrdt_conn_get_dynamic_cookie(z_conn);

        a_state->assitant_gid = zpn_tlv_peer_get_id(&tlv);

        if (a_state->assitant_gid) {
            struct zpn_assistantgroup_assistant_relation *ag_relation = NULL;
            size_t count = 1;

            zpn_assistantgroup_assistant_relation_get_by_assistant(a_state->assitant_gid,
                                                                   &ag_relation,
                                                                   &count,
                                                                   NULL,
                                                                   NULL,
                                                                   0);
            if (ag_relation) {
                a_state->assistant_group_gid = ag_relation->assistant_group_id;
            }
        }

        zrdt_conn_set_status(z_conn, zrdt_conn_connected);
        res = zpn_broker_assistant_auth_log_zrdt(z_conn,
                                                 &(a_state->log),
                                                 a_state->assitant_gid,
                                                 ZPN_ASSISTANT_BROKER_DATA,
                                                 zrdt_conn_connected);

        res = zdtls_session_set_rx_callback(zd_sess, zpn_zrdt_zdtls_data_callback, z_conn, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not set rx_callback from dtls");
        }

        zpn_fohh_worker_assistant_connect_data_zrdt(zrdt_conn_get_thread_id(z_conn));
    }

    return res;
}

/* FIXME ZRDT. I am duplicate code here, should be consolidate with fohh version */

static int zpn_broker_assistant_ctx_callback_zrdt(struct zdtls_session *zd_sess,
                                                  char *sni,
                                                  const char *sni_suffix,
                                                  SSL_CTX **ssl_ctx)
{
    return zpn_broker_assistant_ctx_callback_common(zd_sess, sni, sni_suffix, ssl_ctx, zpn_zrdt_tlv, zdtls_description(zd_sess));
}

/*
 * Get a_state of a assistant which is learnt via data channel.
 */
struct zpn_broker_assistant_fohh_state*
zpn_broker_assistant_get_assistant_data_astate(const char* tunnel_id)
{
    return zhash_table_lookup(active_q.tbl, tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, NULL);
}

static void zpn_broker_assistant_add_sni(char* sni_str, int wildcard) {
    ZPN_BROKER_ASSERT_HARD(zpn_broker_assistant_snis_count < MAX_ZPN_BROKER_ASSISTANT_SNIS,
                            "Broker assistant: add snis count exceeded!");
    zpn_broker_assistant_snis[zpn_broker_assistant_snis_count] = ZPN_STRDUP(sni_str, strlen(sni_str));
    zpn_broker_assistant_sni_flags[zpn_broker_assistant_snis_count] = wildcard;
    zpn_broker_assistant_snis_count++;
}

static void zpn_broker_assistant_update_sni(char* old_sni_str, char *new_sni_str, int wildcard) {
    ZPN_ASSERT(zpn_broker_assistant_snis_count < MAX_ZPN_BROKER_ASSISTANT_SNIS);

    for (int count = 0; count < zpn_broker_assistant_snis_count; count++) {
        if (0 == strcmp(zpn_broker_assistant_snis[count], old_sni_str)) {
            ZPN_FREE(zpn_broker_assistant_snis[count]);
            zpn_broker_assistant_snis[count] = ZPN_STRDUP(new_sni_str, strlen(new_sni_str));
            zpn_broker_assistant_sni_flags[count] = wildcard;
            return;
        }
    }
    ZPN_LOG(AL_ERROR, "Could not update broker_assistant sni: %s to %s", old_sni_str, new_sni_str);
}

void zpn_broker_assistant_get_snis(char*** snis_out, int** flags_out, int* count_out)
{
    if (snis_out) {
        *snis_out = zpn_broker_assistant_snis;
    }
    if (flags_out) {
        *flags_out = zpn_broker_assistant_sni_flags;
    }
    if (count_out) {
        *count_out = zpn_broker_assistant_snis_count;
    }
}

/* Initiate redirecting all assistants, triggered when broker is about to be shut down.
 * Actual redirects will be carried out by *_monitor_cb which is triggered periodically.
 */
void zpn_broker_assistant_redirect_assistants(int do_it, const char* reason)
{
    redirect_assistants = do_it;
    redirect_assistants_reason = reason;
}

int zpn_broker_assistant_get_redirect_assistants_flag()
{
    return redirect_assistants;
}

const char *zpn_broker_assistant_get_redirect_assistants_reason()
{
    return redirect_assistants_reason;
}

/* Initiate disconnecting all broker assistants, triggered when broker is about to be shut down.
 * Actual client disconnects will be carried out by various xyz_monitor_cb functions.
 */
void zpn_broker_assistant_disconnect_assistants(int do_it, const char* reason)
{
    disconnect_assistants = do_it;
    disconnect_assistants_reason = reason;
}

int zpn_broker_assistant_get_disconnect_assistants_flag()
{
    return disconnect_assistants;
}

const char *zpn_broker_assistant_get_disconnect_assistants_reason()
{
    return disconnect_assistants_reason;
}

void zpn_broker_assistant_get_connection_counts(int* control, int* data)
{
    if (control) {
        struct connected_assistant *asst, *tmp;
        *control = 0;

        pthread_mutex_lock(&(assistants_control.lock));
        LIST_FOREACH_SAFE(asst, &(assistants_control.assistant_list), list_entry, tmp) {
            (*control)++;
        }
        pthread_mutex_unlock(&(assistants_control.lock));
    }
    if (data) {
        struct zpn_broker_assistant_fohh_state *a_state;
        *data = 0;

        ZPATH_MUTEX_LOCK(&active_q.lock, __FILE__, __LINE__);
        TAILQ_FOREACH(a_state, &(active_q.list), entry) {
            (*data)++;
        }
        ZPATH_MUTEX_UNLOCK(&active_q.lock, __FILE__, __LINE__);
    }
}

/*
 * Check the connection to assistant is valid or not
 */
int zpn_broker_assistant_validate_ast_conn(struct fohh_connection *f_conn)
{
    struct connected_assistant *asst;

    pthread_mutex_lock(&(asst_lock));
    asst = fohh_connection_get_dynamic_cookie(f_conn);
    pthread_mutex_unlock(&(asst_lock));

    if (NULL == asst) {
        ZPN_LOG(AL_DEBUG, "Broker to assistant connection is invalid");
        return 0;
    } else {
        ZPN_LOG(AL_DEBUG, "Broker to assistant connection is valid");
        return 1;
    }
}

/* zpn_broker_assistant_validate_version
 * Verify if version of the assistant is allowed to connect with broker
 * based on the data in zpn_version_control table
 */
static int zpn_broker_assistant_validate_version(const int64_t assistant_gid,
                                                    wally_response_callback_f callback_f,
                                                    void *callback_cookie,
                                                    int64_t callback_id) {
    struct zpn_assistant_version *version;
    int result;
    /* Perform Version Control check only from Public Broker */
    if (!ZPN_BROKER_IS_PUBLIC()) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (zpn_broker_feature_is_version_control_enabled() == 0) {
        ZPN_DEBUG_ASSISTANT("ZPN_VERSION_CONTROL Feature is disabled ");
        return ZPN_RESULT_NO_ERROR;
    }
    result = zpn_assistant_version_get_by_id(assistant_gid, &version, callback_f, callback_cookie, callback_id);
    if (ZPN_RESULT_NO_ERROR == result) {
        ZPN_DEBUG_ASSISTANT("Received assistant %"PRId64" with version %s ",assistant_gid, version->current_version);
        /* If the client version is lesser than BASELINE VERSION
         * Then continue with Version check here.
         * If the client version is same are greater than BASELINE VERSION,
         * Then version check is performed in zpn_version rpc, that gracefully handles invalid version
         */
        if(zpn_is_version_less_than_baseline_version(version->current_version, zpn_version_control_client_type_assistant)) {
            // Check the version against the broker version and return error
            enum zpn_version_control_client_type client_type=zpn_version_control_client_type_assistant;
            enum zpn_version_control_server_type server_type=zpn_version_control_server_type_broker;
            result = zpn_check_client_version_allowed(client_type,  server_type, version->current_version);
            if (ZPN_RESULT_NO_ERROR != result) {
                ZPN_LOG(AL_ERROR, "Received assistant %"PRId64" with version %s is REJECTED - %s",assistant_gid, version->current_version, zpn_result_string(result));
            }
        }
    } else {
        /* The zpn_assistant_version table is populated based on authlog by AUM.
         * Allow the connection temporarily, so that authlog could be generated and consumed by AUM
         */
        if (WALLY_RESULT_NOT_FOUND == result) {
            ZPN_LOG(AL_WARNING, "This is a new assistant, connection is allowed temporarily");
            return ZPN_RESULT_NO_ERROR;
        }
        if (WALLY_RESULT_ASYNCHRONOUS == result) {
            ZPN_DEBUG_ASSISTANT("Asynchronous assistant version fetch for gid = %"PRId64"", assistant_gid);
        } else {
            ZPN_LOG(AL_ERROR, "FAILED - Fetching assistant version from zpn_assistant_version table - %s",zpn_result_string(result));
        }
    }
    return result;
}

static int zpn_assistant_version_cb(void *argo_cookie_ptr __attribute__((unused)), void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zpn_version *version = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    //send back zpn_version_ack to pbroker based on version received
    struct zpn_tlv tlv;
    tlv.type = zpn_fohh_tlv;
    tlv.conn.f_conn = f_conn;
    tlv.conn_incarnation = fohh_connection_incarnation(f_conn);
    tlv.tlv_incarnation = 0;
    if (zpn_debug_get(ZPN_DEBUG_VER_CONTROL_IDX)) {
        char buf[ARGO_BUF_DEFAULT_SIZE];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("%s: %s: Rx zpn_version: %s", zpn_tlv_description(&tlv), zpn_tlv_peer_cn(&tlv), buf);
        }
    }
    return zpn_process_client_zpn_version_msg(version, &tlv,zpn_client_type_assistant);
}
