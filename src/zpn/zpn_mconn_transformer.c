/*
 * zpn_transformer.h. Copyright (C) 2017 Zscaler Inc. All rights reserved.
 */

#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn_transformer.h"



/*
 *  Transmit calls handler to process the data queued up at mconn. The handler returns any data to be
 *  handed to the opposite mconn_transformer
 *
 *  Since we are calling zpn_client_process_rx_data() directly in here, there is no separate receive
 *  function for mconn_transformer
 */
static void mconn_transformer_transmit(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct zpn_mconn_transformer *mconn_transformer = cookie;
    struct zpn_mconn *mconn = &(mconn_transformer->mconn);
    int res = ZPN_RESULT_NO_ERROR;

    if (mconn->global_owner) {
        if (mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return;
    }

    if (!mconn_transformer->tx_data) {
        goto exit;
    }

    if (0 == (mconn->global_owner_calls->validate_incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length,
                                                               int_cookie)) {
        /* mtunnel changed under us? bail */
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
        goto exit;
    }

    if (zpn_mconn_transmit_buffer_exists(mconn)) {

        /* Process the incoming data */
        res = mconn_transformer->handler(zpn_mconn_get_transmit_buffer(mconn), zpn_mconn_get_transmit_buffer_len(mconn), mconn_transformer->tx_data);

        /* Forward any processed data */
        if (mconn_transformer->consumer) {
            if (mconn_transformer->consumer->rx_data) {
                evbuffer_remove_buffer(mconn_transformer->tx_data, mconn_transformer->consumer->rx_data, evbuffer_get_length(mconn_transformer->tx_data));
                res = zpn_client_process_rx_data(mconn_transformer->consumer,
                                                 mconn_transformer->consumer->rx_data,
                                                 evbuffer_get_length(mconn_transformer->consumer->rx_data),
                                                 NULL, NULL);
                if (res) {
                    ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
                }
            }
        }
    }

exit:
    if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return;
}

int zpn_mconn_transformer_init(struct zpn_mconn_transformer *mconn_transformer,
                               void *mconn_self,
                               struct zpn_connector_transformer *cnt,
                               zpn_transformer_handler_f *handler)
{
    mconn_transformer->handler = handler;
    mconn_transformer->connector = cnt;
    return zpn_mconn_init(&(mconn_transformer->mconn), mconn_self);
}

static int zpn_mconn_transformer_bind_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t *owner_incarnation)
{
    struct zpn_mconn_transformer *mconn_transformer = mconn_base;

    mconn_transformer->mconn.rx_data = evbuffer_new();
    if (!mconn_transformer->mconn.rx_data) {
        return ZPN_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(mconn_transformer->mconn.rx_data);
    mconn_transformer->tx_data = evbuffer_new();
    if (!mconn_transformer->tx_data) {
        evbuffer_free(mconn_transformer->mconn.rx_data);
        return ZPN_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(mconn_transformer->tx_data);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_transformer_unbind_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           int drop_buffered_data,
                                           int dont_propagate,
                                           const char *err)
{
    struct zpn_mconn_transformer *mconn_transformer = mconn_base;

    if (!mconn_transformer->mconn.rx_data) {
        evbuffer_free(mconn_transformer->mconn.rx_data);
        mconn_transformer->mconn.rx_data = NULL;
    }

    if (mconn_transformer->tx_data) {
        evbuffer_free(mconn_transformer->tx_data);
        mconn_transformer->tx_data = NULL;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_transformer_lock_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length)
{
}


static void zpn_mconn_transformer_unlock_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length)
{
}

/*
 * Locking: it is assumed mtunnel is locked at this point
 */
static int zpn_mconn_transformer_transmit_cb(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length,
                                             int64_t owner_incarnation,
                                             int fohh_thread_id,
                                             struct evbuffer *buf,
                                             size_t buf_len)
{
    struct zpn_mconn_transformer *mconn_transformer = mconn_base;
    int res = ZPN_RESULT_NO_ERROR;

    if (mconn_transformer->consumer && mconn_transformer->handler) {
        struct zpn_mconn *mconn = &(mconn_transformer->mconn);
        int64_t incarnation;

        incarnation = (mconn->global_owner_calls->incarnation)(mconn,
                                                               mconn->self,
                                                               mconn->global_owner,
                                                               mconn->global_owner_key,
                                                               mconn->global_owner_key_length);
        if (fohh_thread_call(mconn_transformer->mconn.fohh_thread_id,
                             mconn_transformer_transmit,
                             mconn_transformer,
                             incarnation) != FOHH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for bev_write_cb!");
        }
    }

    return res;
}

static int zpn_mconn_transformer_pause_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_transformer_resume_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_transformer_forward_tunnel_end_cb(void *mconn_base,
                                                void *mconn_self,
                                                void *owner,
                                                void *owner_key,
                                                size_t owner_key_length,
                                                int64_t owner_incarnation,
                                                const char *err,
                                                int32_t drop_data)
{
    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_transformer_window_update_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length,
                                            int64_t owner_incarnation,
                                            int fohh_thread_id,
                                            int tx_len)
{
    return;
}

void zpn_mconn_transformer_stats_update_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id,
                                          enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_transformer_disable_read_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_transformer_enable_read_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Callbacks exposed to mconn
 */
const struct zpn_mconn_local_owner_calls mconn_transformer_calls = {
    zpn_mconn_transformer_bind_cb,
    zpn_mconn_transformer_unbind_cb,
    zpn_mconn_transformer_lock_cb,
    zpn_mconn_transformer_unlock_cb,
    zpn_mconn_transformer_transmit_cb,
    zpn_mconn_transformer_pause_cb,
    zpn_mconn_transformer_resume_cb,
    zpn_mconn_transformer_forward_tunnel_end_cb,
    zpn_mconn_transformer_window_update_cb,
    zpn_mconn_transformer_stats_update_cb,
    zpn_mconn_transformer_disable_read_cb,
    zpn_mconn_transformer_enable_read_cb
};


/********************************************************************************************
 *     transformer connector code below
 ********************************************************************************************/
int zpn_connector_transformer_init(struct zpn_connector_transformer *cnt,
                                   void *mconn_self,
                                   zpn_transformer_handler_f *handler_cs,
                                   zpn_transformer_handler_f *handler_sc,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   struct zpn_mconn_global_owner_calls *global_call_set_c,
                                   struct zpn_mconn_global_owner_calls *global_call_set_s)
{
    int res;

    zpn_connector_init(&cnt->conn, zct_transformer);
    zpn_mconn_transformer_init(&(cnt->mconn_c), mconn_self, cnt, handler_cs);
    zpn_mconn_transformer_init(&(cnt->mconn_s), mconn_self, cnt, handler_sc);
    cnt->mconn_c.consumer = &(cnt->mconn_s.mconn);
    cnt->mconn_c.producer = &(cnt->mconn_s.mconn);
    cnt->mconn_s.consumer = &(cnt->mconn_c.mconn);
    cnt->mconn_s.producer = &(cnt->mconn_c.mconn);

    res = zpn_mconn_add_global_owner(&(cnt->mconn_c.mconn),
                                     0,
                                     mconn_self,
                                     global_owner_key,
                                     global_owner_key_length,
                                     global_call_set_c);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add global owner");
    }

    res = zpn_mconn_add_global_owner(&(cnt->mconn_s.mconn),
                                     0,
                                     mconn_self,
                                     global_owner_key,
                                     global_owner_key_length,
                                     global_call_set_c);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add global owner");
    }

    res = zpn_mconn_add_local_owner(&(cnt->mconn_c.mconn),
                                    0,
                                    &(cnt->mconn_c.mconn),
                                    NULL,
                                    0,
                                    &mconn_transformer_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot attach local owner on transformer client side");
    }

    res = zpn_mconn_add_local_owner(&(cnt->mconn_s.mconn),
                                    0,
                                    &(cnt->mconn_s.mconn),
                                    NULL,
                                    0,
                                    &mconn_transformer_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot attach local owner on transformer server side");
    }

    return ZPN_RESULT_NO_ERROR;
}

struct zpn_connector_transformer *zpn_connector_transformer_create(void *mconn_self,
                                                                   zpn_transformer_handler_f *handler_cs,
                                                                   zpn_transformer_handler_f *handler_sc,
                                                                   void *global_owner_key,
                                                                   size_t global_owner_key_length,
                                                                   struct zpn_mconn_global_owner_calls *global_call_set_c,
                                                                   struct zpn_mconn_global_owner_calls *global_call_set_s)

{
    struct zpn_connector_transformer *cnt = NULL;

    cnt = ZPN_CALLOC(sizeof(struct zpn_connector_transformer));
    if (cnt) {
        zpn_connector_transformer_init(cnt,
                                       mconn_self,
                                       handler_cs,
                                       handler_sc,
                                       global_owner_key,
                                       global_owner_key_length,
                                       global_call_set_c,
                                       global_call_set_s);
    }

    return cnt;
}


void zpn_connector_transformer_destroy(struct zpn_connector_transformer *cnt)
{
    if (cnt) {
        ZPN_FREE(cnt);
    }
}

int zpn_mconn_tranformer_null_handler(struct evbuffer *buf, size_t buf_len, struct evbuffer *output_buf)
{
    if (output_buf) {
        ZPN_DEBUG_MCONN("zpn_mconn_tranformer_null_handler(), dequeue %d bytes", (int)buf_len);
        evbuffer_remove_buffer(buf, output_buf, buf_len);
    }

    return ZPN_RESULT_NO_ERROR;
}
