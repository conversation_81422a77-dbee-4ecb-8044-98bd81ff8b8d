/*
 * zpn_policy_engine.h. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 */

#ifndef __ZPN_POLICY_ENGINE_H__
#define __ZPN_POLICY_ENGINE_H__

#include "wally/wally.h"
#include "zhash/zhash_table.h"
#include "zthread/zthread.h"

typedef int32_t (hold_param_callback_f)(void *param_cookie);

extern struct zpath_allocator zpe_allocator;

#define ZPE_POLICY_TYPE_XX(XX)                                              \
    XX(zpe_policy_type_deprecated, "deprecated")                            \
    XX(zpe_policy_type_access, "access_policy")                             \
    XX(zpe_policy_type_reauth, "reauth_policy")                             \
    XX(zpe_policy_type_siem, "siem_policy")                                 \
    XX(zpe_policy_type_bypass, "bypass_policy")                             \
    XX(zpe_policy_type_isolate, "isolation_policy")                         \
    XX(zpe_policy_type_inspection, "inspection_policy")                     \
    XX(zpe_policy_type_priv_capabilities, "privileged_capabilities_policy") \
    XX(zpe_policy_type_cred_map, "credential_mapping_policy")               \
    XX(zpe_policy_type_csp_policy, "clientless_session_protection_policy")  \
    XX(zpe_policy_type_broker_redirect, "broker_redirection_policy")        \
    XX(zpe_policy_type_priv_portal_policy, "privileged_portal_policy")      \
    XX(zpe_policy_type_global_vpn, "global_vpn_policy")
    // when adding an entry, update ZPE_POLICY_TYPE_LAST

/* The orders of the following policy types should match with API */
enum zpe_policy_type {
#define XX(a, b) a,
    ZPE_POLICY_TYPE_XX(XX)
#undef XX
};

enum zpe_access_action
    {
     zpe_access_action_none,                      // 0: Invalid

     /* Access policies: */
     zpe_access_action_allow,                     // 1: ALLOW : Access policies use this
     zpe_access_action_deny,                      // 2: DENY : Access policies use this

     /* Reauth policies: */
     zpe_access_action_re_auth,                   // 3: RE_AUTH : Reauth policies use this

     /* Siem policies: */
     zpe_access_action_log,                       // 4: LOG: SIEM policies use this

     /* Bypass policies: */
     zpe_access_action_bypass,                    // 5: BYPASS: Tell client to bypass.
     zpe_access_action_intercept,                 // 6: INTERCEPT: Tell client to intercept.
     zpe_access_action_nodownload,                // 7: NO_DOWNLOAD: Don't tell client about app.
     zpe_access_action_bypass_on_reauth,          // 8: BYPASS_RE_AUTH: Tell client to bypass if it gets a reauth message, until reauth.
     zpe_access_action_intercept_if_accessible,   // 9: INTERCEPT_ACCESSIBLE: Evaluate access policies. Intercept if accessible, nodownload if not.

     /* Browser isolation policies */
     zpe_access_action_isolate,                   // 10: ISOLATE: client is provisioned with CBI
     zpe_access_action_bypass_isolate,            // 11: BYPASS_ISOLATE: client is not provisoned with CBI

     /* Inspection policy actions */
     zpe_inspection_bypass,                       // 12: BYPASS_INSPECTION.
     zpe_inspection_waf,                          // 13: Inspect - Inspect all HTTP based traffic

     /* Approval for Just-in-Time(JIT) access */
     zpe_access_action_approval_required,         // 14: Approval based jit access

     /* Privileged capabilities - file transfer, scan */
     zpe_access_action_check_priv_capabilities,   // 15: Check action with a lookup in zpn_privileged_capabilities
     zpe_access_action_inject_credentials,        // 16: Inject Credential as per CRED MAP policy

     /* Browser Session Protection Policy */
     zpe_csp_policy_monitor,                      // 17: Monitor Traffic for Fingerprinting
     zpe_csp_policy_do_not_monitor,               // 18: Do Not Monitor Traffic for Fingerprinting

     /* Broker Redirect - Policy based broker redirect actions */
     zpe_access_action_redirect_always,          // 19: redirect only to target PSEs, Enforce policy decision, No fallback public brokers
     zpe_access_action_redirect_preferred,       // 20: prefer redirecting to target PSEs but provide public_brokers as fallback
     zpe_access_action_redirect_default,         // 21: Use default redirection logic
     /* Privileged Portal capabilities */
     zpe_access_action_check_priv_portal_capabilities,   // 22: Check action with a lookup in zpn_privileged_portal_rule
    };

enum zpe_access_approval_status
    {
      zpe_access_approval_none,
      zpe_access_approval_active,
      zpe_access_approval_future,
      zpe_access_approval_expired,
    };

#define ZPE_POLICY_TYPE_FIRST 1
#define ZPE_POLICY_TYPE_LAST  zpe_policy_type_global_vpn
#define ZPE_POLICY_TYPE_COUNT (ZPE_POLICY_TYPE_LAST + 1)

/* stats logging for policy evaluation */
struct zpe_policy_evaluate_stats {      /* _ARGO: object_definition */
    int64_t eval_count;                 /* _ARGO: integer */
    int64_t rules_evaluated_old;        /* _ARGO: integer */
    int64_t rules_evaluated_new;        /* _ARGO: integer */
    int64_t rules_evaluated;            /* _ARGO: integer */
    int64_t rule_evaluate_time_avg_us;  /* _ARGO: integer */
    uint64_t rule_evaluate_data_avg_bytes; /* _ARGO: integer */
};

/* Avg time for rule evaluation */
struct zpe_rule_evaluate_avg_time {
    int64_t rule_evaluate_time_sum;
    int64_t rule_evaluate_count;
};

/* Avg size of data for rule evaluation */
struct zpe_rule_evaluate_avg_data_size {
    uint64_t rule_evaluate_data_size_total;
    int64_t rule_evaluate_count;
};

/*The stats reflect how many times the configuration for below tables was changed*/
struct zpe_policy_config_change_stats {                 /* _ARGO: object_definition */
    int64_t zpn_application;                            /* _ARGO: integer */
    int64_t zpn_application_group;                      /* _ARGO: integer */
    int64_t zpn_application_domain;                     /* _ARGO: integer */
    int64_t zpn_application_group_application_mapping;  /* _ARGO: integer */
    int64_t zpn_policy_set;                             /* _ARGO: integer */
    int64_t zpn_rule;                                   /* _ARGO: integer */
    int64_t zpn_rule_condition_set;                     /* _ARGO: integer */
    int64_t zpn_rule_condition_operand;                 /* _ARGO: integer */

    /*policy rebuild stats*/
    int64_t num_policy_rebuild_req_received;            /* _ARGO: integer */
    int64_t num_policy_rebuild_req_skipped;             /* _ARGO: integer */
    int64_t num_policy_rebuild_max_backoff_triggered;   /* _ARGO: integer */

    /*updated application domain processing load stats*/
    int64_t num_domain_processed;                       /* _ARGO: integer */
    int64_t num_domain_processed_for_heartbeat;         /* _ARGO: integer */
    int64_t num_domain_in_process_queue;                /* _ARGO: integer */
    int64_t num_domain_process_reset_heartbeat;         /* _ARGO: integer */
    int64_t num_domain_process_empty_queue;             /* _ARGO: integer */
};

/* stats logging for old and new policy evaluation comparasion */
struct zpe_policy_eval_compare_stats {  /* _ARGO: object_definition */
    int64_t rules_evaluated_new;        /* _ARGO: integer */
    int64_t rules_evaluated_old;        /* _ARGO: integer */
    int64_t rules_evaluated;            /* _ARGO: integer */
    int64_t eval_count;                 /* _ARGO: integer */
    int64_t eval_count_abnormal;        /* _ARGO: integer */
    int64_t eval_count_fail_1;          /* _ARGO: integer */
    int64_t eval_count_fail_2;          /* _ARGO: integer */
    int64_t eval_enhance_us;            /* _ARGO: integer */
    int64_t eval_old_us;                /* _ARGO: integer */
    int64_t eval_count_fail_wo_npol;    /* _ARGO: integer */
};

/* stats logging for mtunnel policy check time */
struct zpe_policy_mtunnel_eval_stats {               /* _ARGO: object_definition */
    int64_t num_mtunnels;                            /* _ARGO: integer */
    int64_t max_mtunnel_policy_eval_time_us;         /* _ARGO: integer */
    int64_t avg_mtunnel_policy_eval_time_us;         /* _ARGO: integer */
    int64_t total_mtunnel_policy_eval_time_us;       /* _ARGO: quiet, integer */
};

struct zpe_policy_set;
struct zpe_policy_set_meta;
struct zpe_mini_policy_set;
struct zpe_policy_built;
struct zpe_policy_built_timestamp;

/*
 * Dump policy state. (generally for debugging)
 */
int zpe_dump_policy_state(int64_t scope_gid,
                          char *str,
                          size_t str_len);
/*
 * Ensure that policy for the specified scope is loaded. Once this
 * routine returns NO_ERROR (instead of asynchronous), forever after
 * all calls to zpe_get_current_policy will succeed without being
 * asynchronous.
 */
int zpe_load_policy(int64_t scope_gid,
                    wally_response_callback_f *callback,
                    void *void_cookie,
                    int64_t int_cookie,
                    hold_param_callback_f *hold_callback,
                    void *param_cookie);

/*
 * Get a policy that can be used for evaluation. The resulting policy
 * is immutable and need not be locked, but must not be 'held' for an
 * excessive amount of time as it will eventually be freed.
 *
 * Returns pretty standard wally results
 *
 * being_built, if specified, is set to '1' if a new policy is being
 * built (even if the old one is returned!), and 0 otherwise. This was
 * included so that we know when this policy engine is out of sync
 * with the old style policy engine.
 *
 * last_tickle, if specified, indicates when this scope saw its
 * last configuration change.
 *
 * scope_policy_version, if specified, returns the version of the
 * policy that has been built. (as policies change, this will return
 * arbitrarily higher numbers, loosely corresponding to the number of
 * changes)
 *
 * This call is blocking, and guarantee to get the current version of policy.
 */
int zpe_get_current_policy(int64_t scope_gid,
                           enum zpe_policy_type policy_type,
                           struct zpe_policy_built **policy_built,
                           wally_response_callback_f *callback,
                           void *void_cookie,
                           int64_t int_cookie,
                           hold_param_callback_f *hold_callback,
                           void *param_cookie,
                           int *being_built,
                           struct zpe_policy_built_timestamp *policy_ts);

/*
 * This function is called synchrously without rebuilding a policy
 * The policy is built asynchroursly when zpn_cscope_reay or when a policy change happens
 * If the policy is still being built, this function will just use previous policy
 * If no policy available, return "not found" error
 *
 * This call is non-blocking and do not guarantee the policy version is up to date
 */
int zpe_get_policy_without_rebuild(int64_t scope_gid,
                                   enum zpe_policy_type policy_type,
                                   struct zpe_policy_built **policy_built,
                                   int *being_built,
                                   struct zpe_policy_built_timestamp *policy_ts);

int zpe_get_scope_policy_without_rebuild(int64_t scope_gid,
                                         enum zpe_policy_type policy_type,
                                         struct zpe_policy_built **policy_built_default,
                                         struct zpe_policy_built **policy_built,
                                         struct zpe_policy_built_timestamp *policy_ts);

/*
 * If a new version of policy being built return 1, or has been built return 2
 * Otherwise return 0 or -1 (error)
 */
int is_new_policy_available(const struct zpe_policy_built *current_policy_built);

/*
 * Get a string representation of the policy type
 */
const char *zpe_policy_type_string(enum zpe_policy_type policy_type);

/*
 * Get a string representation of the policy action
 */
const char *zpe_access_action_string(enum zpe_access_action action);

/*
 * Get a string representation of the JIT approval status
 */
const char *zpe_access_approval_status_string(enum zpe_access_approval_status status);

/*
 * This routine should be called whenever a scope's policy has
 * changed in any way, as detected by wally. This will cause compiled
 * policy to refresh as necessary.
 *
 * zpe_scope_cancel_tickle is only used by unit tests to avoid
 * deferred policy updates interfering with unit tests.
 */
void zpe_scope_tickle(int64_t scope_gid);
void zpe_customer_tickle(int64_t customer_gid);
void zpe_scope_cancel_tickle(int64_t scope_gid);
void ut_dispatch_tickle(int64_t scope_gid);

/* This routine indicates if there is a deferred scope tickle in
 * progress. It is used almost exclusively for unit tests. */
int zpe_is_scope_tickled(int64_t scope_gid);
int zpe_is_scope_build_outstanding(int64_t scope_gid);

int zpe_pending_or_in_progress_policy_build(int64_t scope_gid);


/*
 * This routine will flush a scope's policies. It is used for
 * QA/testing only.
 */
void zpe_scope_flush(int64_t scope_gid);


/*
 * This routine will register the specified callback to be called
 * whenever policy changes on a scope.
 */
typedef void (zpe_policy_change_f)(int64_t scope_gid);
void zpe_register_for_change(zpe_policy_change_f *callback_f);


/*
 * Get the size of a policy set
 */
int zpe_policy_set_size(struct zpe_policy_set *set);


/*
 * Perform policy evaluation.
 *
 * Returns all relevant match information.
 *
 * If no match is found, this routine returns ZPATH_RESULT_NOT_FOUND.
 *
 * This routine is fully synchronous.
 *
 * Any other error returned indicates that an error occurred during
 * evaluation (exceedingly rare- generally indicates code is running
 * on a bad policy set)
 *
 * In:
 *
 * set: The policy set to evaluate
 *
 * general_context_hash: The set of strings representing the characteristics of
 *   the context for which the policy is being evaluated. (i.e. group
 *   attributes, etc)
 *
 * saml_state: The set of static SAML state strings that we can look in
 *   if enabled.  Can be disabled by sending NULL
 *
 * scim_state: The set of SCIM state strings that we can look in
 *   if enabled.  Can be disabled by sending NULL
 *
 * int64_hash: Hash table of int64's representing the characteristics
 *   of the context for which the policy is being evaluated. This is
 *   basically a hash table of GIDs that apply to the context.
 *
 * Out:
 *
 * rules_evaluated: The number of rules evaluated. Useful for guaging
 *   performance
 *
 * matched_rule: GID of the matched rule. This is set to 0 on not found.
 *
 * matched_custom_msg: Reference to the custom_msg for the matched
 *   rule. This data comes from within zpe_policy_set.
 *
 * matched_reauth_timeout: The reauth timeout specified by the rule.
 *
 * matched_reauth_idle_timeout: The reauth idle timeout specified by
 *   the rule.
 *
 * matched_action_id: The matched action_id specified by the rule.
 */
int zpe_evaluate(int skip_default_rule,  /* skip evaluate default rule for the policy, needed for DTA */
                 int debug_verbose,      /* used for debuggibg from curl command only */
                 int64_t *app_gids,        /* list application to access, if no app to accessset it to NULL */
                 int64_t app_gids_count,        /* # of matched apps, 0 incase of no app */
                 struct zpe_policy_built *policy_built, /* pre-built policy to evaluate */
                 struct zhash_table *general_state,  /* general string type attributes of the client */
                 struct zhash_table *saml_state,     /* saml attributes of the client */
                 struct zhash_table *scim_state,     /* scim attributes of the clint */
                 struct zhash_table *int64_hash,     /* general integer type attributes of the client */
                 const char * debug_str,             /* information for debugging */
                 int *rules_evaluated,               /* return: number of rules evaluated */
                 int64_t *matched_rule,              /* return: rule gid of matched rule */
                 enum zpe_access_action *matched_action, /* return: action of matched rule */
                 char **matched_custom_msg,              /* return: message of matched rule */
                 int64_t *matched_reauth_timeout,        /* return: reauth timeout for matched rule */
                 int64_t *matched_reauth_idle_timeout,   /* return: reauth idle timeout for matched rule */
                 int64_t *matched_action_id);            /* return: action id for matched rule */

int zpe_evaluate_scope(int64_t *app_gids,
                       int64_t app_gids_count,
                       struct zpe_policy_built *policy_built_default,
                       struct zpe_policy_built *policy_built,
                       struct zhash_table *general_state,
                       struct zhash_table *saml_state,
                       struct zhash_table *scim_state,
                       struct zhash_table *int64_hash,
                       const char * debug_str,
                       int *rules_evaluated,
                       int64_t *matched_rule,
                       enum zpe_access_action *matched_action,
                       char **matched_custom_msg,
                       int64_t *matched_reauth_timeout,
                       int64_t *matched_reauth_idle_timeout,
                       int64_t *matched_action_id,
                       int is_default_app);

int zpe_evaluate_scope_internal(int64_t *app_gids,
                                int64_t app_gids_count,
                                struct zpe_policy_built *policy_built_default,
                                struct zpe_policy_built *policy_built,
                                struct zhash_table *general_state,
                                struct zhash_table *saml_state,
                                struct zhash_table *scim_state,
                                struct zhash_table *int64_hash,
                                const char * debug_str,
                                int *rules_evaluated,
                                int64_t *matched_rule,
                                enum zpe_access_action *matched_action,
                                char **matched_custom_msg,
                                int64_t *matched_reauth_timeout,
                                int64_t *matched_reauth_idle_timeout,
                                int64_t *matched_action_id,
                                int is_default_app,
                                int debug_verbose);

/*
 * zpe_evaluate_all is similar to zpe_evaluate, except that it tests
 * ALL rules, without stopping on a match. It accumulates all the
 * matching action_ids and returns them to the caller.
 *
 * If no action_ids match, this routine returns
 * ZPATH_RESULT_NOT_FOUND. Otherwise returns similarly to
 * zpe_evaluate.
 */
int zpe_evaluate_all(struct zpe_policy_set *set,
                     struct zhash_table *general_state_hash,
                     struct zhash_table *saml_state_hash,
                     struct zhash_table *scim_state_hash,
                     struct zhash_table *int64_hash,
                     int64_t *matched_action_ids,
                     size_t *action_id_count);

/*
 * Initialize the zpe library
 */
int zpe_init(int policy_rebuild_backoff);

/*
 * Set the deferred delay on building a policy. Used almost
 * exclusively for unit testing.
 */
void zpe_set_deferred_tickle_us(int64_t defer_us);

void zpe_pretend_scope_tickle(int64_t scope_gid);
void zpe_pretend_scope_cancel_tickle(int64_t scope_gid);
void get_policy_rebuild_stats(int64_t *num_rebuild_req_received, int64_t *policy_rebuild_scheduled, int64_t *num_max_backoff_triggered);

void zpe_free_grp_to_apps(struct zhash_table *grp_to_apps);

int is_policy_build_failed(int64_t scope_gid, enum zpe_policy_type policy_type, int64_t *fail_count);
/*
 * This feature define how we evaluate policy
 * 0: old way, 1: new way, 2: both and compare results
 */
extern int64_t g_policy_eval_type;
extern const char *g_zpe_policy_type_stats_name[ZPE_POLICY_TYPE_COUNT];
extern struct zpe_policy_eval_compare_stats zpe_policy_eval_comp_stats[ZTHREAD_MAX_THREADS];
extern struct zpe_policy_evaluate_stats zpe_policy_eval_stats[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS];
extern struct zpe_rule_evaluate_avg_time zpe_rule_eval_avg_time[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS];
extern struct zpe_rule_evaluate_avg_data_size zpe_rule_evaluate_avg_data_size[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS];
extern struct zpe_policy_config_change_stats policy_config_change_stats;
extern struct zpe_policy_mtunnel_eval_stats policy_mtunnel_eval_stats[ZTHREAD_MAX_THREADS];

extern int64_t g_policy_rebuild_backoff_hard_disabled;
extern int64_t g_policy_rebuild_backoff;
extern int64_t g_policy_rebuild_backoff_interval_s;
extern int64_t g_policy_rebuild_backoff_periodic_check_interval_s;
#endif /* __ZPN_POLICY_ENGINE_H__ */
