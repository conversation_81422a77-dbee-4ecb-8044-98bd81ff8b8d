/*
 * zpn_mconn_icmp_tlv.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include <fcntl.h>

#include "argo/argo_hash.h"
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn_icmp_tlv.h"
#include "zpn/zpn_client.h"

static inline unsigned short csum(unsigned short *buf, int nwords)
{
    unsigned long sum;
    for (sum = 0; nwords > 0; nwords--)
        sum += *buf++;
    sum = (sum >> 16) + (sum & 0xffff);
    sum += (sum >> 16);
    return ~sum;
}

int zpn_icmp_tlv_init(struct zpn_icmp_tlv *icmp_tlv)
{
    icmp_tlv->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    icmp_tlv->ev = NULL;
    icmp_tlv->fohh_thread_id = 0;

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_icmp_tlv_event_callback(evutil_socket_t fd, short int what, void *pargs)
{
    struct zpn_icmp_tlv *icmp_tlv = pargs;
    char *data = NULL;
    int bytes = 0;
    struct evbuffer *evbuf = NULL;
    struct zpn_mconn_icmp_tlv *mconn_icmp_tlv;
    int res;

    data = ZPN_CALLOC(MAX_ICMP_PACKET);
    if (!data) return;

    bytes = read(fd, data, MAX_ICMP_PACKET);
    if (bytes < 0) {
        goto exit;
    }

    ZPN_DEBUG_MCONN("zpn_icmp_tlv_event_callback(), fd is %d, received %d bytes, buffer is %s\n",
                    (int)fd, bytes, data);

    mconn_icmp_tlv = icmp_tlv->mconn;
    if (mconn_icmp_tlv) {
        ZPN_DEBUG_MCONN("Found mconn_icmp_tlv for data, pass along the data");

        mconn_icmp_tlv->last_rx_epoch_s = epoch_s();
        mconn_icmp_tlv->data_arrived += bytes;

        evbuf = evbuffer_new();
        if (evbuf) {
            evbuffer_add(evbuf, data, (size_t)bytes);
            mconn_icmp_tlv->callbacks++;
            mconn_icmp_tlv->data_to_peer_attemp += bytes;

            res = zpn_client_process_rx_data(&(mconn_icmp_tlv->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            ZPN_DEBUG_MCONN("icmp tlv receive, Cannot alloc event buf for icmp");
        }

        goto exit;
    } else {
        ZPN_DEBUG_MCONN("Cannot find mconn_icmp_tlv for data, create new one");
        (*icmp_tlv->request_cb)(icmp_tlv->fohh_thread_id);
    }

exit:
    if (data) ZPN_FREE(data);
    if (evbuf) evbuffer_free(evbuf);

    return;
}

int zpn_icmp_tlv_listen(struct zpn_icmp_tlv *icmp_tlv,
                        struct argo_inet *inet,
                        int fohh_thread_id,
                        zpn_icmp_tlv_request_cb *request_cb)
{
    struct event_base *base = fohh_get_thread_event_base(fohh_thread_id);

    icmp_tlv->inet = *inet;
    icmp_tlv->fohh_thread_id = fohh_thread_id;
    icmp_tlv->request_cb = request_cb;

    icmp_tlv->ev = event_new(base, icmp_tlv->fd, EV_READ | EV_PERSIST, zpn_icmp_tlv_event_callback, icmp_tlv);
    event_add(icmp_tlv->ev, NULL);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_tlv_init(struct zpn_mconn_icmp_tlv *mconn_icmp_tlv, void *mconn_self, enum zpn_mconn_type type)
{
    mconn_icmp_tlv->data_arrived = 0;
    mconn_icmp_tlv->callbacks = 0;
    mconn_icmp_tlv->buffer_under = 0;
    mconn_icmp_tlv->buffer_over = 0;
    mconn_icmp_tlv->data_to_peer_attemp = 0;
    mconn_icmp_tlv->remote_paused = 0;
    mconn_icmp_tlv->last_rx_epoch_s = epoch_s();
    mconn_icmp_tlv->last_tx_epoch_s = epoch_s();
    return zpn_mconn_init(&(mconn_icmp_tlv->mconn), mconn_self, type);
}

int zpn_mconn_icmp_tlv_bind_cb(void *mconn_base,
                              void *mconn_self,
                              void *owner,
                              void *owner_key,
                              size_t owner_key_length,
                              int64_t *owner_incarnation)
{
    struct zpn_mconn_icmp_tlv *mconn_icmp_tlv = mconn_base;
    struct zpn_icmp_tlv *icmp_tlv = owner;

    icmp_tlv->mconn = mconn_icmp_tlv;

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_tlv_unbind_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int drop_buffered_data,
                                int dont_propagate,
                                const char *err)
{
    struct zpn_mconn_icmp_tlv *mconn_icmp_tlv = mconn_base;
    struct zpn_icmp_tlv *icmp_tlv = owner;
    int res;

    if (!dont_propagate) {
        res = zpn_mconn_forward_mtunnel_end(&(mconn_icmp_tlv->mconn), err, drop_buffered_data);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not send mtunnel_end. Is this really a problem?");
        }
    }

    icmp_tlv->mconn = NULL;

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_icmp_tlv_lock_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length)
{
    struct zpn_icmp_tlv *icmp_tlv = owner;

    pthread_mutex_lock(&(icmp_tlv->lock));
}

void zpn_mconn_icmp_tlv_unlock_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length)
{
    struct zpn_icmp_tlv *icmp_tlv = owner;

    pthread_mutex_unlock(&(icmp_tlv->lock));
}

int zpn_mconn_icmp_tlv_transmit_cb(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length,
                                  int64_t owner_incarnation,
                                  int fohh_thread_id,
                                  struct evbuffer *buf,
                                  size_t buf_len)
{
    struct zpn_mconn_icmp_tlv *mconn_icmp_tlv = mconn_base;
    struct zpn_icmp_tlv *icmp_tlv = owner;

    int icmp_buf_len = evbuffer_get_length(buf);
    uint8_t *icmp_linear_buf = evbuffer_pullup(buf, icmp_buf_len);
    if (!icmp_linear_buf) {
        return ZPN_RESULT_ERR;
    }

    struct ip *iph = (struct ip *)(icmp_linear_buf);
    int iph_len = iph->ip_hl << 2;

    uint16_t ip_tlen = ntohs(iph->ip_len);

    struct sockaddr sock_addr;
    socklen_t sock_len;
    argo_inet_to_sockaddr(&(icmp_tlv->inet), &(sock_addr), &sock_len, 0);

    iph->ip_dst = ((struct sockaddr_in *)(&sock_addr))->sin_addr;

    iph->ip_sum = 0;
    iph->ip_sum = csum((unsigned short *)iph, (iph_len / 2));

    int bytes = write(icmp_tlv->fd, icmp_linear_buf, ip_tlen);
    if (bytes < 0) {
        ZPN_DEBUG_MCONN("Failed to write for ICMP request from SIP = %08x to DIP = %08x, IP len = %d\n",
                         iph->ip_src.s_addr, iph->ip_dst.s_addr, ip_tlen);
        return ZPN_RESULT_ERR;
    }

    mconn_icmp_tlv->last_tx_epoch_s = epoch_s();
    ZPN_DEBUG_MCONN("Successfully sent out ICMP request from SIP = %08x to DIP = %08x, IP len = %d\n",
                     iph->ip_src.s_addr, iph->ip_dst.s_addr, ip_tlen);

    evbuffer_drain(buf, ip_tlen);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_tlv_pause_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_tlv_resume_cb(void *mconn_base,
                                void *mconn_self,
                                void *owner,
                                void *owner_key,
                                size_t owner_key_length,
                                int64_t owner_incarnation,
                                int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmp_tlv_forward_tunnel_end_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length,
                                            int64_t owner_incarnation,
                                            const char *err,
                                            int32_t drop_data)
{
    struct zpn_mconn_icmp_tlv *mconn_icmp_tlv = mconn_base;

    ZPN_DEBUG_MCONN("zpn_mconn_icmp_tlv_forward_tunnel_end_cb()");

    mconn_icmp_tlv->mconn.fin_rcvd = 1;
    mconn_icmp_tlv->mconn.fin_sent = 1;

    /* Reflect the tunnel end if not already */
    if (!mconn_icmp_tlv->mconn.fin_refl) {
        zpn_mconn_forward_mtunnel_end(mconn_icmp_tlv->mconn.peer, MT_CLOSED_TERMINATED, drop_data);
        mconn_icmp_tlv->mconn.fin_refl = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_icmp_tlv_window_update_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id,
                                        int tx_len,
                                        int batch_win_upd)
{
    return;
}

void zpn_mconn_icmp_tlv_stats_update_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id,
                                        enum zpn_mconn_stats stats_name)
{
    return;
}

int zpn_mconn_icmp_tlv_disable_read_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
int zpn_mconn_icmp_tlv_enable_read_cb(void *mconn_base,
                               void *mconn_self,
                               void *owner,
                               void *owner_key,
                               size_t owner_key_length,
                               int64_t owner_incarnation,
                               int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls zpn_mconn_icmp_tlv_calls = {
    zpn_mconn_icmp_tlv_bind_cb,
    zpn_mconn_icmp_tlv_unbind_cb,
    zpn_mconn_icmp_tlv_lock_cb,
    zpn_mconn_icmp_tlv_unlock_cb,
    zpn_mconn_icmp_tlv_transmit_cb,
    zpn_mconn_icmp_tlv_pause_cb,
    zpn_mconn_icmp_tlv_resume_cb,
    zpn_mconn_icmp_tlv_forward_tunnel_end_cb,
    zpn_mconn_icmp_tlv_window_update_cb,
    zpn_mconn_icmp_tlv_stats_update_cb,
    zpn_mconn_icmp_tlv_disable_read_cb,
    zpn_mconn_icmp_tlv_disable_read_cb
};


int zpn_mconn_icmp_tlv_done(struct zpn_mconn_icmp_tlv *mconn_icmp_tlv)
{
    uint32_t current_epoch_s = epoch_s();

    if (((current_epoch_s - mconn_icmp_tlv->last_rx_epoch_s) > ICMP_TIMEOUT_S) &&
        ((current_epoch_s - mconn_icmp_tlv->last_tx_epoch_s) > ICMP_TIMEOUT_S)) {

        if (mconn_icmp_tlv->mconn.peer) {
            zpn_mconn_forward_mtunnel_end(mconn_icmp_tlv->mconn.peer, MT_CLOSED_TERMINATED, 1);
        }

        mconn_icmp_tlv->mconn.fin_rcvd = 1;
        mconn_icmp_tlv->mconn.fin_sent = 1;

        return 1;
    }

    return 0;
}

void zpn_mconn_icmp_tlv_internal_display(struct zpn_mconn_icmp_tlv *mconn_icmp_tlv)
{
    ZPN_DEBUG_MCONN("data_arrived = %ld, data_to_peer_attemp = %ld",
                    (long)mconn_icmp_tlv->data_arrived, (long)mconn_icmp_tlv->data_to_peer_attemp);
    zpn_mconn_internal_display(&(mconn_icmp_tlv->mconn));
}

int zpn_mconn_icmp_tlv_clean(struct zpn_mconn_icmp_tlv *mconn_icmp_tlv)
{
    return zpn_mconn_clean(&(mconn_icmp_tlv->mconn));
}
