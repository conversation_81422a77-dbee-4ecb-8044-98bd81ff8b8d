/*
 * zpn_mconn_icmpv6.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_debug.h"

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include "fohh/fohh.h"
#include <fcntl.h>
#include <errno.h>
#include <sys/socket.h>
#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <netinet/ip6.h>
#include <netinet/icmp6.h>

#include "fohh/fohh_private.h"
#include "zpn/zpn_mconn_icmp.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_lib.h"
#include "zhealth/zhealth_probe_lib.h"
#include "zpn/zpn_mconn_icmp_util.h"
#include "zpath_misc/zpath_misc.h"

static struct argo_structure_description* zpn_mconn_icmpv6_stats_description;

static
struct zpn_mconn_icmpv6_stats {                                              /* _ARGO: object_definition */
    int64_t icmpv6_mconn_icmpinfo_alloc;                                     /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_free;                                      /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_alloc;                            /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_free;                             /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_payload_alloc;                             /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_payload_free;                              /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_payload_hdrincl_alloc;                     /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_payload_hdrincl_free;                      /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_cookie1_alloc;                    /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_cookie1_free;                     /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_cookie2_alloc;                    /* _ARGO: integer */
    int64_t icmpv6_mconn_icmpinfo_response_cookie2_free;                     /* _ARGO: integer */
    int64_t icmpv6_mconn_failcookie_alloc;                                   /* _ARGO: integer */
    int64_t icmpv6_mconn_failcookie_free;                                    /* _ARGO: integer */
    int64_t icmpv6_mconn_reqcookie_alloc;                                    /* _ARGO: integer */
    int64_t icmpv6_mconn_reqcookie_free;                                     /* _ARGO: integer */
    int64_t icmpv6_mconn_free_icmpinfo_invalid_icmpinfo;                     /* _ARGO: integer */
    int64_t icmpv6_mconn_icmp_failure_cb_invalid_icmpinfo;                   /* _ARGO: integer */
    int64_t icmpv6_mconn_icmp_response_cb_invalid_icmpinfo;                  /* _ARGO: integer */
    int64_t icmpv6_mconn_failure_cb_no_global_owner_to_lock;                 /* _ARGO: integer */
    int64_t icmpv6_mconn_failure_cb_mconn_already_gone;                      /* _ARGO: integer */
    int64_t icmpv6_mconn_failure_cb_no_global_owner_to_unlock;               /* _ARGO: integer */
    int64_t icmpv6_mconn_response_cb_no_global_owner_to_lock;                /* _ARGO: integer */
    int64_t icmpv6_mconn_response_cb_no_global_owner_to_unlock;              /* _ARGO: integer */
}stats;

#include "zpn/zpn_mconn_icmpv6_compiled_c.h"

/*
 * IMPORTANT
 * We do not free cookies here because cookies are self referential and not always consistantly owned by the icmp_info
 * instead it is up to the users of the icmpinfo here to properly free any cookies that they may have set in their
 * path
 */
static void zpn_mconn_icmpv6_info_free(struct zhealth_icmp_info *icmpinfo) {
    if (!icmpinfo) {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_free_icmpinfo_invalid_icmpinfo), 1);
        return;
    }

    if (icmpinfo->icmp_payload) {
        ZPN_FREE(icmpinfo->icmp_payload);
        icmpinfo->icmp_payload = NULL;
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmpinfo_payload_free), 1);
    }

    if (icmpinfo->response) {
        ZPN_FREE(icmpinfo->response);
        icmpinfo->response = NULL;
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmpinfo_response_free), 1);
    }

    if (icmpinfo->icmp_payload_hdrincl) {
        ZPN_FREE(icmpinfo->icmp_payload_hdrincl);
        icmpinfo->icmp_payload_hdrincl = NULL;
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmpinfo_payload_hdrincl_free), 1);
    }

    zhealth_probe_lib_probe_info_shutdown_probe_v6(&(icmpinfo->probe_info));
    zhealth_probe_lib_close_zhealth_owned_socket6_async(&(icmpinfo->probe_info));

    ZPN_FREE(icmpinfo);
    __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmpinfo_free), 1);
}


int zpn_mconn_icmpv6_init(struct zpn_mconn_icmp *mconn_icmp, void *mconn_self, enum zpn_mconn_type type)
{
    return zpn_mconn_icmp_common_init(mconn_icmp, mconn_self, type);
}

static int zpn_mconn_icmpv6_bind_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t *owner_incarnation)
{
    return zpn_mconn_icmp_common_bind(mconn_base, owner);

}

static int zpn_mconn_icmpv6_unbind_cb(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      int64_t owner_incarnation,
                                      int drop_buffered_data,
                                      int dont_propagate,
                                      const char *err)
{
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_icmpv6_lock_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length)
{
    return;
}


static void zpn_mconn_icmpv6_unlock_cb(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length)
{
    return;
}

int zpn_mconn_icmpv6_failure_cb(struct zhealth_icmp_info *icmp_info, int64_t pending_us)
{
    if (!icmp_info) {
        ZPN_LOG(AL_ERROR, "ICMP mconn failure cb - no icmp_info, returning");
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmp_failure_cb_invalid_icmpinfo), 1);
        return ZPN_RESULT_ERR;
    }

    struct zpn_mconn_icmpfail_cookie *icmpfail_cookie = icmp_info->failure_cookie;
    struct zhealth_icmp_info *icmpinfo = icmpfail_cookie->icmpinfo;
    struct zpn_mconn_icmp *mconn_icmp = icmpfail_cookie->mconn_icmp;
    struct zpn_mconn *mconn = &(mconn_icmp->mconn);

    /*
     * free the icmp info first and then access the mconn structure later with lock.
     * its okay we lose the mtunnel, but the icmp related data need to be freed anyways.
     */
    if (icmpfail_cookie->req_cookie) {
        ZPN_FREE(icmpfail_cookie->req_cookie);
        icmpfail_cookie->req_cookie = NULL;
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
    }

    ZPN_FREE(icmpfail_cookie);
    __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_free), 1);

    zpn_mconn_icmpv6_info_free(icmpinfo);

    if (!mconn) {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failure_cb_mconn_already_gone), 1);
        return ZPN_RESULT_NO_ERROR;
    }
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failure_cb_no_global_owner_to_lock), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    mconn->icmpv6_timeout_failure_drops++;

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failure_cb_no_global_owner_to_unlock), 1);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmpv6_response_cb(struct zhealth_icmp_info *icmpinfo, int icmp_response_type, int icmp_response_code)
{
    if (!icmpinfo) {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmp_response_cb_invalid_icmpinfo), 1);
        return ZPN_RESULT_ERR;
    }

    struct zpn_mconn_icmpreq_cookie *icmpreq_cookie = icmpinfo->response_cookie1;
    struct zpn_mconn_icmp *mconn_icmp = icmpinfo->response_cookie2;
    struct zpn_mconn *mconn = &(mconn_icmp->mconn);

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_response_cb_no_global_owner_to_lock), 1);
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_MCONN("Received ICMPv6 response on: %s", icmpinfo->rx.dest_ip);

    mconn_icmp->last_rx_epoch_s = epoch_s();
    mconn_icmp->rx_bytes += icmpinfo->response_len;
    mconn_icmp->mconn.icmpv6_rx_packets++;

    if (icmpinfo->response_len > (ZPN_MCONN_MTU - 100)) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        goto exit;
    }

    if (!mconn->peer) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        mconn_icmp->mconn.icmpv6_internal_err_drops++;
        goto exit;
    }

    // TODO:Check for time excedded and unreachable code, check for icmp file
    // If we receive errors for packets which were not ping packets then drop them
    if (((icmp_response_type == ICMP6_TIME_EXCEEDED && icmp_response_code == ICMP6_TIME_EXCEED_TRANSIT) ||
        (icmp_response_type == ICMP6_PACKET_TOO_BIG) ||
        (icmp_response_type == ICMP6_DST_UNREACH) ||
        (icmp_response_type == ICMP6_PARAM_PROB)) &&
        (mconn->peer->icmp_access_type != ZPN_MCONN_ICMP_ACCESS_TYPE_PING)) {
        mconn_icmp->dropped_bytes += icmpinfo->response_len;
        mconn_icmp->mconn.icmp_access_err_drops++;
        goto exit;
    }

    ZPN_DEBUG_MCONN("Received ICMPv6 payload of len: %zu", icmpinfo->response_len);

    struct evbuffer *evbuf = zpn_mconn_icmpv6_packetize(icmpinfo->response, icmpinfo->response_len,
                                                        icmp_response_type, icmp_response_code,
                                                        icmpreq_cookie->req_id,
                                                        icmpreq_cookie->req_seq,
                                                        icmpreq_cookie->ip6_src);
    if (!evbuf) {
        goto exit;
    }

    int res = zpn_client_process_rx_data(&(mconn_icmp->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
    if (res) {
        ZPN_DEBUG_MCONN("Process_rx data for ICMPv6 returned %s", zpn_result_string(res));
    }

    evbuffer_free(evbuf);

exit:

    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    } else {
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_response_cb_no_global_owner_to_unlock), 1);
    }

    if (icmpreq_cookie) {
        if (icmpreq_cookie->failure_cookie) {
            ZPN_FREE(icmpreq_cookie->failure_cookie);
            icmpreq_cookie->failure_cookie = NULL;
            __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_free), 1);
        }

        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
    }

    zpn_mconn_icmpv6_info_free(icmpinfo);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Receiving packet as
 * [IPv6 header][ICMPv6 header][ICMPv6 payload]
 * so open the packet accordingly and extract the information
 */
static int zpn_mconn_icmpv6_transmit_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id,
                                        struct evbuffer *buf,
                                        size_t buf_len)
{
    (void) mconn_self;
    (void) owner;
    (void) owner_key;
    (void) owner_key_length;
    (void) owner_incarnation;
    (void) fohh_thread_id;

    struct zpn_mconn_icmp *mconn_icmp = mconn_base;
    char src_ip[256];
    char dst_ip[256];

    memset(src_ip, 0, sizeof(src_ip));
    memset(dst_ip, 0, sizeof(dst_ip));

    mconn_icmp->last_tx_epoch_s = epoch_s();

    /* Packet contains
     * [IPv6 header][ICMPv6 header][ICMPv6 payload]
     */
    size_t packet_len = evbuffer_get_length(buf);
    uint8_t *packet_buf = evbuffer_pullup(buf, packet_len);
    if (!packet_buf) {
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to extract packet, returning");
        return ZPN_RESULT_ERR;
    }

    struct ip6_hdr *ip6h = (struct ip6_hdr *)(packet_buf);
    int iph_len = sizeof(struct ip6_hdr);

    inet_ntop(AF_INET6, &(ip6h->ip6_src), src_ip, sizeof(src_ip));
    inet_ntop(AF_INET6, &(ip6h->ip6_dst), dst_ip, sizeof(dst_ip));

    /* Big MTU or fragmentation is not supported
     * Only supported echo ping request and response
     */
    if (ip6h->ip6_nxt != IPPROTO_ICMPV6) {
        ZPN_DEBUG_MCONN("Supported format is [IPv6 header][ICMPv6 header][ICMPv6 payload], for src %s and dst %s, "
                        "Came with IPv6 next header %d", src_ip, dst_ip, ip6h->ip6_nxt);
        ZPN_LOG(AL_ERROR, "ICMPv6 Only ICMPv6 ping request and response is supported for src %s and dst %s, "
                "IP next header came as %d, aborting",
                src_ip, dst_ip, ip6h->ip6_nxt);
        return ZPN_RESULT_ERR;
    }

    struct icmp6_hdr *icmpv6_hdr = (struct icmp6_hdr *)(packet_buf + iph_len);

    struct zpn_mconn_icmpreq_cookie *icmpreq_cookie =
           ZPN_CALLOC(sizeof(struct zpn_mconn_icmpreq_cookie));

    if (!icmpreq_cookie) {
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to alloc icmpv6 request cookie for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }
    __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_alloc), 1);

    icmpreq_cookie->req_id = icmpv6_hdr->icmp6_id;
    icmpreq_cookie->req_seq = icmpv6_hdr->icmp6_seq;
    icmpreq_cookie->ip6_src = ip6h->ip6_src;

    struct zhealth_icmp_info *icmpinfo = (struct zhealth_icmp_info *)
                                         ZPN_CALLOC(sizeof(struct zhealth_icmp_info));

    if (!icmpinfo) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to allocate memory for icmpreq_cookie for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }

    icmpinfo->response_cookie1 = icmpreq_cookie;
    icmpinfo->response_cookie2 = mconn_base;
    icmpinfo->response_cb = zpn_mconn_icmpv6_response_cb;

    /* IPv4 TTL is same as IPv6 Hops
     * IPv4 TOS is same as IPv6 traffic class
     */
#define IPV6_FLOWLABEL_LEN	20
#define	IPV6_TRAFFIC_CLASS(ip6)	((ntohl((ip6)->ip6_flow) >> IPV6_FLOWLABEL_LEN) & 0xff)

    icmpinfo->probe_info.max_ttl = ip6h->ip6_hops;
    icmpinfo->ip_tos = IPV6_TRAFFIC_CLASS(ip6h);
    snprintf_nowarn(icmpinfo->probe_info.dest_ip, sizeof(icmpinfo->probe_info.dest_ip), "%s", mconn_icmp->dst_ipaddr);

    struct zpn_mconn_icmpfail_cookie *icmpfail_cookie =
           ZPN_CALLOC(sizeof(struct zpn_mconn_icmpfail_cookie));
    if (!icmpfail_cookie) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
        zpn_mconn_icmpv6_info_free(icmpinfo);
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to allocate memory for icmpfail_cookie for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }

    __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_alloc), 1);

    icmpfail_cookie->mconn_icmp = mconn_icmp;
    icmpfail_cookie->req_cookie = icmpreq_cookie;
    icmpfail_cookie->icmpinfo = icmpinfo;

    icmpreq_cookie->failure_cookie = icmpfail_cookie;
    icmpinfo->failure_cookie = icmpfail_cookie;
    icmpinfo->failure_cb = zpn_mconn_icmpv6_failure_cb;

    uint16_t iptlen = ntohs(ip6h->ip6_plen) + iph_len;
    icmpinfo->response_len = iptlen + 68; // 68 using the same as IPv4
    icmpinfo->response = ZPN_CALLOC(icmpinfo->response_len);
    if (!icmpinfo->response) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
        ZPN_FREE(icmpfail_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_free), 1);
        zpn_mconn_icmpv6_info_free(icmpinfo);
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to allocate memory for icmpinfo response for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }
    __sync_add_and_fetch_8(&(stats.icmpv6_mconn_icmpinfo_response_alloc), 1);

    int icmp_payload_len = (iptlen - iph_len - ICMP_HDR_LEN);
    uint8_t *icmp_payload = (uint8_t *)ZPN_MALLOC(icmp_payload_len);
    if (!icmp_payload) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
        ZPN_FREE(icmpfail_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_free), 1);
        zpn_mconn_icmpv6_info_free(icmpinfo);
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to allocate memory for icmppayload for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }

    memcpy(icmp_payload, (packet_buf + iph_len + ICMP_HDR_LEN), icmp_payload_len);
    icmpinfo->icmp_payload = icmp_payload;
    icmpinfo->icmp_payload_len = icmp_payload_len;

    if (zhealth_icmp_gen_seqid(icmpinfo) != ARGO_RESULT_NO_ERROR) {
        ZPN_FREE(icmpreq_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_reqcookie_free), 1);
        ZPN_FREE(icmpfail_cookie);
        __sync_add_and_fetch_8(&(stats.icmpv6_mconn_failcookie_free), 1);
        zpn_mconn_icmpv6_info_free(icmpinfo);
        ZPN_LOG(AL_ERROR, "ICMPv6 unable to generate seq id for icmpinfo response for src %s and dst %s", src_ip, dst_ip);
        return ZPN_RESULT_ERR;
    }

    zhealth_send_icmpv6_probe(icmpinfo); // To finish this

    evbuffer_drain(buf, iptlen);
    mconn_icmp->mconn.bytes_to_client += iptlen;
    mconn_icmp->mconn.icmpv6_tx_packets++;
    mconn_icmp->tx_bytes += iptlen;

    if (mconn_icmp->mconn.peer) {
        zpn_mconn_client_window_update(mconn_icmp->mconn.peer, 0, iptlen, 1);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_icmpv6_pause_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length,
                                     int64_t owner_incarnation,
                                     int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_icmpv6_resume_cb(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      int64_t owner_incarnation,
                                      int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmpv6_forward_tunnel_end_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length,
                                           int64_t owner_incarnation,
                                           const char *err,
                                           int32_t drop_data)
{
    return zpn_mconn_icmp_common_forward_tunnel_end(mconn_base, drop_data);

}

void zpn_mconn_icmpv6_window_update_cb(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length,
                                       int64_t owner_incarnation,
                                       int fohh_thread_id,
                                       int tx_len,
                                       int batch_win_upd)
{
    return;
}

void zpn_mconn_icmpv6_stats_update_cb(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      int64_t owner_incarnation,
                                      int fohh_thread_id,
                                      enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_icmpv6_disable_read_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length,
                                     int64_t owner_incarnation,
                                     int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
static int zpn_mconn_icmpv6_enable_read_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *owner,
                                     void *owner_key,
                                     size_t owner_key_length,
                                     int64_t owner_incarnation,
                                     int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

const struct zpn_mconn_local_owner_calls mconn_icmpv6_calls = {
    zpn_mconn_icmpv6_bind_cb,
    zpn_mconn_icmpv6_unbind_cb,
    zpn_mconn_icmpv6_lock_cb,
    zpn_mconn_icmpv6_unlock_cb,
    zpn_mconn_icmpv6_transmit_cb,
    zpn_mconn_icmpv6_pause_cb,
    zpn_mconn_icmpv6_resume_cb,
    zpn_mconn_icmpv6_forward_tunnel_end_cb,
    zpn_mconn_icmpv6_window_update_cb,
    zpn_mconn_icmpv6_stats_update_cb,
    zpn_mconn_icmpv6_disable_read_cb,
    zpn_mconn_icmpv6_enable_read_cb
};

int zpn_mconn_icmpv6_done(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_done(mconn_icmp);
}

int zpn_mconn_icmpv6_clean(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_clean(mconn_icmp);
}

void zpn_mconn_icmpv6_internal_display(struct zpn_mconn_icmp *mconn_icmp)
{
    return zpn_mconn_icmp_common_internal_display(mconn_icmp);
}

static int
zpn_mconn_icmpv6_dump_stats(struct zpath_debug_state*  request_state,
                            const char**               query_values,
                            int                        query_value_count,
                            void*                      cookie)
{
    char        jsonout[50000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(zpn_mconn_icmpv6_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_icmpv6_debug_init()
{
    int res;

    if (!(zpn_mconn_icmpv6_stats_description = argo_register_global_structure(ZPN_MCONN_ICMPV6_STATS_HELPER))) {
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of zpn_mconn_icmpv6",
                                  "/zpn/zpn_mconn_icmpv6/dump/stats",
                                  zpn_mconn_icmpv6_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}
