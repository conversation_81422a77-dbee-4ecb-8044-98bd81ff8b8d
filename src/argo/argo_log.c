/*
 * argo_log.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 *
 * README: Important note
 * ----------------------
 * ET-53139 is culmination of many changes in this file between drop23 and drop27
 *
 * The following will be the overall change log:
 * 1) Enforcement of al_global.max_mem
 * 2) Enforement of memory high tide (CLI -agro_mem_threshold)
 * 3) Strict requirement of enforcing ordering in lock:
 *         a) Acquire al_global lock stand-alone WITHOUT any collection lock; OR
 *         b) Always acquire collection_lock before acquiring al_global lock
 * 4) Disable creating logging objects created from within logging code
 *        (i.e., bytes evicted messsage which used to gets logged to event_log is disabled)
 * 5) Additional REST APIs for:
 *         a) Checking al_global state and overall view of argo logging infra (this file specifically)
 *         b) Resetting eviction counters (for ease of debugging)
 *         c) Purge ALL objects from  ALL collections (fail safe instead of having to restart broker..hopefully)
 *         d) Set internal argo logging params via CURL commands
 * 6) Earlier, eviction function required locks to be held before calling. It has now been updated
 *    to acquire necessary locks itself. This has be done to enable it to call from collection
 *    thread as well as the from REST API (for all collections purge).
 * 7) To enable the above, al_global locks will be acquired right before usage and will be
 *    relinquished ASAP when done. We should avoid taking locks and going on lengthy sojourns in the code.
 * 8) All locks have been made PTHREAD_MUTEX_NORMAL, so as to use them in conjunction with conditional vars in future
 *         a) This has been done by adding a new delay queue in argo_log_collection to break the need of recursive mutex
 * 9) Argo high tide is a clearly separable feature in this file. Disabling ARGO_HIGH_TIDE_ENABLED macro will disable functionality
 */

#define _GNU_SOURCE

#include <pthread.h>
#include <unistd.h>
#include <syslog.h>
#include <poll.h>
#include <fcntl.h>
#include <errno.h>

#include "zthread/zthread.h"
#include <assert.h>
#include <errno.h>
#include <limits.h>

#include "argo/argo_hash.h"
#include "argo/argo_log.h"
#include "argo/argo_log_compiled.h"
#include "argo/argo_private.h"
#include "zpath_misc/zpath_misc.h"

#include <sched.h>

#ifdef __APPLE__
#include <mach/mach_init.h>
#include <mach/task.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#elif __linux__
#include <sys/sysinfo.h>
#endif

#define OUTPUT_RED "\033[31;3m"
#define OUTPUT_GRN "\033[32;3m"
#define OUTPUT_YEL "\033[33;3m"
#define OUTPUT_BLU "\033[94;3m"
#define OUTPUT_PRP "\033[35;3m"
#define OUTPUT_LBL "\033[36;3m"
#define OUTPUT_GRY "\033[90;3m"
#define OUTPUT_WHT "\033[37;3m"
#define OUTPUT_RST "\033[0m"

#define KB (1024)
#define MB (1024 * KB)
#define GB (1024 * MB)
#define convert_MB(x) (x / (MB))

#ifndef ARGO_NO_HT
#define ARGO_HIGH_TIDE_ENABLED
#endif

#ifdef ARGO_HIGH_TIDE_ENABLED
/*Argo log mem related defaults*/
#define ARGO_MEM_THREAD_INTERVAL_MS 7000 //7s
#define ARGO_MEM_DIFF_THRESHOLD (10 * MB)
#define ARGO_MEM_HIGH_TIDE_INOPERATIVE 0
#define ARGO_MEM_HIGH_TIDE_TRIGGERED 1
#endif

LIST_HEAD(argo_log_registered_head, argo_log_registered_structure);
LIST_HEAD(argo_log_reader_head, argo_log_reader);
LIST_HEAD(argo_log_file_head, argo_log_file);
LIST_HEAD(argo_log_collection_head, argo_log_collection);

//#define TEMP_DEBUG
//#define TEMP_STATS

/*
 * Collection poll timeouts
 * Set to short on large apps, long on small apps.
 * Large apps (like broker) cannot block on pipe for too long, while
 * small apps (like connector) cannot afford extra cpu overhead
 * Ref: ET-30276, ET-31002
 */
#define ARGO_LOG_COLLECTIONS_POLL_TIMEOUT_SHORT_US  10 * 1000           /* 10ms */
#define ARGO_LOG_COLLECTIONS_POLL_TIMEOUT_LONG_US   1 * 1000 * 1000     /* 1s */

#define ARGO_LOG_SHORT_TIMEOUT_US                   1 * 1000 * 1000     /* 1s */
#define ARGO_LOG_MEDIUM_TIMEOUT_US                  60 * 1000 * 1000    /* 1m */

#define ARGO_NO_READER_COLL_LIMIT 5*MB

#define ARGO_LOG_UNPARTITIONED_NAME  "Unpartitioned"

enum argo_log_collection_object_priority {
    AL_PRIO_HIGH,
    AL_PRIO_MED,
    AL_PRIO_LOW,
    AL_PRIO_LAST
};

enum thread_origin_type {
    CLI_THREAD,
    LOGGING_THREAD,
    COLLECTION_THREAD
};

uint8_t al_prio_lookup_table[argo_log_priority_none + 1] =
    {
        [argo_log_priority_emergency] = AL_PRIO_HIGH,
        [argo_log_priority_alert] = AL_PRIO_HIGH,
        [argo_log_priority_critical] = AL_PRIO_HIGH,
        [argo_log_priority_error] = AL_PRIO_HIGH,
        [argo_log_priority_warning] = AL_PRIO_MED,
        [argo_log_priority_notice] = AL_PRIO_MED,
        [argo_log_priority_info] = AL_PRIO_LOW,
        [argo_log_priority_debug] = AL_PRIO_LOW,
        [argo_log_priority_none] = AL_PRIO_LOW,
    };

struct argo_log_collection_object {
    struct argo_object *log_obj;
    /*size of this obj + size of outer argo obj + size of inner argo obj*/
    uint32_t log_size;
    uint32_t ref_count;
    uint64_t index;
    TAILQ_ENTRY(argo_log_collection_object) transmit_q_entry;
    TAILQ_ENTRY(argo_log_collection_object) evict_q_entry;
};

struct argo_log_delayed_object {
    struct argo_object *delay_log_obj;
    TAILQ_ENTRY(argo_log_delayed_object) delay_q_entry;
};

TAILQ_HEAD(argo_log_collection_object_q_head, argo_log_collection_object);
TAILQ_HEAD(argo_log_delay_obj_q_head, argo_log_delayed_object);

#define MAX_PERIOD_US 1*1000*1000

struct argo_structure_description *global_argo_log_desc = NULL;
struct argo_structure_description *global_argo_log_text_desc = NULL;

/*
 * argo_log_priority_none is not used for logging. But just in case anybody uses it, lets give them a lowest limit of 1.
 */
static int argo_log_max_text_per_line_per_second[argo_log_priority_none + 1 ] =
    {200, // emergency
     200, // alert
     200, // critical
     200, // error
     200, // warning
     200, // notice
     200, // info
     50,  // debug
     1};

static int argo_log_global_use_printf = 0;
static int argo_log_global_use_printf_allowed_bitmap = 0xff;
static int argo_log_do_fflush = 1;

enum argo_log_read_state {
    argo_log_read_invalid = 0,
    argo_log_read_idle,
    argo_log_read_reading,
    argo_log_read_blocked
};

static const char *argo_log_read_state_strings[] = {
    [0] = "invalid",
    [1] = "idle",
    [2] = "reading",
    [3] = "blocked"
};

#define READER_SET_STATE(xxx_reader, xxx_state) do {                        \
        xxx_reader->current_state = xxx_state;                              \
        xxx_reader->stats.state = argo_log_read_state_strings[xxx_state];   \
    } while (0);


struct argo_log_global {
    int initialized;

    int64_t instance_gid;
    char instance_role[64];
    char instance_build[64];
    char instance_name[128]; //sufficient as this is just argo logging's view
    char partition_name[128]; //sufficient as this is just argo logging's view

    int64_t log_id;

    struct argo_structure_description *log_desc;
    struct argo_structure_description *log_text_desc;
    struct argo_structure_description *log_text_from_file_desc;
    struct argo_structure_description *reader_stats_desc;

    pthread_t memory_thread; //memory thread for all argo collections high tide check

    pthread_rwlock_t lock;

    struct argo_hash_table *collections;
    struct argo_log_collection_head collections_list;
    uint64_t collections_list_size;
    uint64_t collections_max_poll_timeout;
    /* max_mem - maximum memory that can be reserved for the log collections
     * curr_mem - current memory reserved for the log collections */
    uint64_t system_mem;
    uint64_t config_max_mem;
    uint64_t peak_mem;
    uint64_t max_mem;
    uint64_t curr_mem;
    uint64_t threshold_limit_file_exceed;
#ifdef ARGO_HIGH_TIDE_ENABLED
    /*Argo mem high tide related */
    uint64_t memory_thread_interval;
    uint64_t process_mem_threshold;
    uint64_t mem_difference_threshold;
    uint64_t curr_sys_free_mem;
    uint64_t total_proc_reads;
    uint64_t total_sysinfo_reads;
    uint64_t mem_proc;
    uint64_t mem_other;
    uint64_t proc_reads_fail;
    uint64_t scan_fail;
    uint64_t time_last_reading_s;
    uint8_t  process_mem_is_high_tide;
    uint8_t  argo_memory_hightide_disabled;

#endif
};

static struct argo_log_global al_global;

/* -With 16MB initial mem per collection, an app with 32 collections will hold
 * 512MB from al_global.max_mem
 * */
#define COLL_INIT_MEM                    (1 * MB)

/*Interval for triggering proactive eviction*/
#define COLL_CLEANUP_INTERVAL_US        (1000 * 1000)
#define COLL_SHRINK_OK_MIN_CYCLES        60
#define COLL_SINGLE_LOOP_DEL_MAX         5000

#ifdef TEMP_STATS
#define STATS_COUNT    4
struct index_n_call_stats {
    uint64_t prev_total_calls;
    uint64_t total_calls;
    uint64_t prev_total_walks;
    uint64_t total_walks;
};

struct collection_stats {
    int64_t last_log_us;
    struct index_n_call_stats    call_stats[STATS_COUNT];
    int cur_call_index;
};

#define COUNT_CALLS(c)   \
    { collection->stats.cur_call_index = c; collection->stats.call_stats[collection->stats.cur_call_index].total_calls ++; }

#define COUNT_WALKS(w)   \
    { collection->stats.call_stats[collection->stats.cur_call_index].total_walks += w; }

#else
#define COUNT_CALLS(n)  ((void)n)
#define COUNT_WALKS(w)  ((void)w)
#endif  /* End of TEMP_STATS */

/* Stores a collection's transmit Q stats and eviction stats */
struct collection_q_counters {
    /* Counts and bytes */
    uint64_t object_q_count[AL_PRIO_LAST];
    uint64_t object_q_bytes[AL_PRIO_LAST];
    uint64_t object_q_count_tot;
    uint64_t object_q_bytes_tot;
    uint64_t object_q_dropped_count_tot;
    uint64_t object_q_dropped_bytes_tot;
    uint64_t object_q_last_recorded_dropped_time_delta;
    uint64_t object_q_dropped_first_time_in_s;
    uint64_t object_q_dropped_last_time_in_s;
    uint64_t object_q_no_readers_count_tot;
    uint64_t object_q_no_readers_bytes_tot;
    uint64_t object_q_no_readers_drop_bytes_tot;
    uint64_t delay_q_bytes_tot;
};

struct eviction_counters {
    uint64_t evicted_cnt_tot; //obj count
    uint64_t evicted_bytes_tot; //byte count

    /* Let not directly manipulate evicted_bytes_tot since
     * it is used for argo_log_resume..lets maintain offset */
    uint64_t evicted_cnt_tot_counter_offset;

    /*Same as above...sorted on priority */
    uint64_t evicted_cnt[AL_PRIO_LAST]; //obj count
    uint64_t evicted_bytes[AL_PRIO_LAST]; //byte count

    uint64_t evicted_proactive_bytes_cnt_tot;
    uint64_t evicted_reactive_bytes_cnt_tot;

    uint64_t resize_up_cnt;
    uint64_t resize_down_cnt;
};

struct housekeeping_counters {
    uint64_t write_logfile_fail;
    uint32_t create_logfile_fail_malloc;
    uint32_t create_logfile_fail_fopen;
    uint32_t rstruct_fail;
    uint32_t readers_deleted;
};

struct collection_counters {
    struct collection_q_counters object_q;
    struct eviction_counters evict;
    struct housekeeping_counters misc;

};

struct argo_log_collection {
    struct argo_log_collection_object_q_head log_transmit_q;
    struct argo_log_collection_object_q_head log_evict_q[AL_PRIO_LAST];

    /* For parking argo objs temporarily while walking rstruct timeout queues */
    struct argo_log_delay_obj_q_head log_delay_q;

    uint64_t curr_mem;
    uint64_t max_mem;

    uint64_t local_write_index;
    uint64_t snapshot_write_index;

    uint32_t shrink_ok_cycles;

    uint32_t eviction_flags;

    uint64_t iterations; //total iterations of tight loop of collection_thread
    int thread_fired;

    struct collection_counters counters;

    char *name;
    pthread_mutex_t collection_lock;
    size_t log_2_max_log_messages;

    /* The collection is 'idle' if there is ANY reader that has
     * successfully read all the elements in the queue. */
    int idle;

    /* The registered structures: These all exist in a 10ms bucket
     * (for periods < 1s), 1s bucket (for periods < 1m) or 1m
     * bucket. This isn't expected to be super efficient, and is
     * actually designed so that common intervals arrive
     * concurrently. */
    struct argo_log_registered_head us_elements;
    struct argo_log_registered_head s_elements;
    struct argo_log_registered_head m_elements;

    struct argo_log_file_head files;
    int file_count;

    struct argo_log_reader_head readers;

    /* Pipe. Send on FD 1, receive on FD 0. Used to wake xmit thread
     * when passive readers need to be sent new logs. Wake is only
     * signaled if the collection is idle */
    int thread_wake_pipe[2];
    pthread_t thread;
    LIST_ENTRY(argo_log_collection) list;

#ifdef TEMP_STATS
    struct collection_stats stats;
#endif

    /* Creation API can request creation of a reader at the time of actually
     * triggering a thread for this collection. If so, the necessary info is
     * stored here, and will be used for creating the requested reader.
     * Not an error if NULL. If NULL, we dont create any such reader
     */
    struct argo_log_collection_create_reader *create_reader;
};

enum argo_log_collection_eviction_vals {
    AL_EVICTION_REACTIVE,
    AL_EVICTION_PROCTIVE,
    AL_EVICTION_IDLE
};

struct argo_log_registered_structure {
    /* constant config: */
    char *name;
    int64_t period_us;
    argo_log_pre_log_callback_f *pre_log_callback;
    void *pre_log_callback_cookie;
    struct argo_structure_description *description;
    void **structure_data;
    size_t structure_count;
    struct argo_log_collection *collection;
    LIST_ENTRY(argo_log_registered_structure) list;

    /* Dynamic config: */
    int64_t next_us_to_fire;
};

struct argo_log_reader {
    struct argo_log_collection *collection;
    struct argo_log_registered_structure *stats_reader; //pointer to stats_log if collecting stats
    /*pointer to last visited(read) entry in collection's log object tailq*/
    struct argo_log_collection_object *last_read_ptr;
    char name[256];
    enum argo_log_read_state current_state;
    pthread_mutex_t reader_lock;

    /* stats.read_index is the index of the log that is next to be read */
    struct argo_reader_stats stats;

    int deleted;

    int64_t max_index_sent_to_reader;

    argo_log_callback_f *callback;
    argo_log_status_f *status_callback;
    void *callback_cookie;

    LIST_ENTRY(argo_log_reader) list;
};

struct argo_log_file {
    char filename[192];
    char short_filename[64];
    FILE *fp;
    enum argo_serialize_mode mode;
    size_t max_length;
    int64_t current_written;
    int64_t total_written;
    struct argo_state *write_argo;
    /*Back pointer to collection if ever needed*/
    struct argo_log_collection *collection;
    LIST_ENTRY(argo_log_file) list;
};

/*
 *                              MEMORY HIGH TIDE:
 *                              -----------------
 * In production, brokers have seen memory explosion due to logging object overcrowding.
 * This has led to the need for argo to support protect brokers in such situation.
 *
 *
 * What is argo memory 'high tide'?
 * It literally means memory consumption by the process has reached a pre-decided threshold
 * value and its high time argo library does its part to ensure we do not run out of system mem.
 *
 *
 * How is it computed?
 * It is a percentage value of the total system RAM.
 * Use the value of 'argo_mem_threshold percent' of system memory
 * The percentage value is either obtained by:
 *          a) CLI: +ve integer passed via '-argo_mem_threshold' interpreted as percentage.
 *          b) If CLI does not pass the value, the default value is 80% currently.
 *
 *
 * What does it mean to say argo is in memory 'high tide'?
 * It means:
 *   1) argo logging infra will NOT allocate anymore additional memory for new logging objects.
 *   2) it will stop accepting new log elements AT SOURCE itself (i.e., while attempting logging of new obj)
 *
 * -> It basically means that in high tide, argo logging objects have now lost priority in the system.
 * -> Argo logging will then operate in 'status quo' mode. It will neither yield memory nor take memory.
 *       Defacto, 'status quo' => logging objects dropped at source
 * -> Thus memory high tide is argo doing its part to not tax the system memory to enable broker's survival.
 *
 *
 * Who computes it and when?
 * 1) Argo maintains all the state computation internally throughout.
 * 2) Argo mem high tide threshold is computed during argo logging infra initialization.
 * 3) argo_global_memory_thread will (depending on underlying OS type) obtain free system RAM info periodically
 * 4) The parameters that determine the 'periodic' nature of argo_global_memory_thread can be configured via CURL
 * 5) argo_global_memory_thread was designed to be as efficient as possible in deciding whether to query kernel for info
 *
 * Stats/REST_APIs:
 * ----------------
 * /argo/log/collection/summary                            ==> Prints summary stats
 * /argo/log/collection/summary_reset                      ==> Resets the eviction counters in all collections
 * /argo/log/collection/purge_all_collections              ==> Purges all objects from all collections (use with care)
 * /argo/log/collection/purge_collection                   ==> Purges individual collection by name
 * /argo/log/collection/set_argo_memory_threshold          ==> Sets the argo_mem_threshold value
 * /argo/log/collection/set_argo_memory_difference         ==> Sets the hightide computation memory diff parameter
 * /argo/log/collection/set_argo_memory_thread_interval    ==> Sets the hightide computation memory thread interval parameter
 */
static inline int argo_log_is_mem_hightide(void)
{
#ifdef ARGO_HIGH_TIDE_ENABLED
    return al_global.process_mem_is_high_tide;
#else
    return 0;
#endif

}

void get_argo_high_tide_state(uint8_t *argo_high_tide_state)
{
#ifdef ARGO_HIGH_TIDE_ENABLED
    *argo_high_tide_state = al_global.process_mem_is_high_tide;
#else
    *argo_high_tide_state = 0;
#endif
}

/* Custom looging assert for argo library */
static inline void argo_logging_abort( const char*    file,
                                       const char*    func,
                                       int            line,
                                       int assertion_condition)

{
    if (!assertion_condition) {
        fprintf(stdout, "Argo logging assertion failed in %s:%s:%d", file, func, line);
        fprintf(stderr, "Argo logging assertion failed in %s:%s:%d", file, func, line);
        fflush(stdout);
        fflush(stderr);
        abort();
    }
}

#define ARGO_LOGGING_ASSERT(x) argo_logging_abort(__FILE__, __FUNCTION__, __LINE__, x)
#define COMPARE_FIELD_VALUES(x,y,z) \
    if (x->z != y->z) return (x->z > y->z) ? -1 : 1

/* Pre-requisite: Al_global lock */
#ifdef ARGO_HIGH_TIDE_ENABLED
static inline void argo_log_set_is_hightide_value(uint8_t res)
{
    al_global.process_mem_is_high_tide = res;
}
#endif

static inline struct argo_log_collection_object *
collection_find_index_n(struct argo_log_collection *collection,
                        uint64_t n,
                        struct argo_log_collection_object *starting_at);

static void argo_log_collection_internal_q_copy(struct argo_log_collection *collection);
static int argo_log_cmptr(const void *, const void *);

/* Pre-requisite: al_global lock */
#ifdef ARGO_HIGH_TIDE_ENABLED
static inline void argo_log_global_reset_mem_to_conf_nolock(void)
{
    al_global.max_mem = al_global.config_max_mem;
    return;
}
#endif

/* Pre-requisite: Collection lock */
static inline void argo_log_collection_set_eviction_REACTIVE(
                                        struct argo_log_collection *coll)
{
    coll->eviction_flags |= AL_EVICTION_REACTIVE;
}

/* Pre-requisite: Collection lock */
static inline void argo_log_collection_reset_eviction_REACTIVE(
                                        struct argo_log_collection *coll)
{
    (coll)->eviction_flags &= ~AL_EVICTION_REACTIVE;
}

/* Pre-requisite: Collection lock */
static inline int argo_log_collection_isset_eviction_REACTIVE(
                                struct argo_log_collection *coll)
{
    return ((coll)->eviction_flags & AL_EVICTION_REACTIVE);
}

const char *argo_priorities[] = {
    [0] = "argo_log_priority_emergency",
    "argo_log_priority_alert",
    "argo_log_priority_critical",
    "argo_log_priority_error",
    "argo_log_priority_warning",
    "argo_log_priority_notice",
    "argo_log_priority_info",
    "argo_log_priority_debug"
};

const char *argo_priorities_short[] = {
    [0] = "emergency",
    "alert",
    "critical",
    "error",
    "warning",
    "notice",
    "info",
    "debug",
    "none"
};

const char *argo_priorities_short_color[] = {
    [0] = OUTPUT_PRP"EMG",
    OUTPUT_PRP"ALR",
    OUTPUT_RED"CRL",
    OUTPUT_RED"ERR",
    OUTPUT_RED"WNG",
    OUTPUT_YEL"NTC",
    OUTPUT_WHT"INF",
    OUTPUT_GRY"DBG"
};
const char *argo_priorities_short_no_color[] = {
    [0] = "EMG",
    "ALR",
    "CRL",
    "ERR",
    "WNG",
    "NTC",
    "INF",
    "DBG"
};

const char *argo_syslog_rfc_priorities[] = {
    [0] = "emergency",
    "alert",
    "critical",
    "error",
    "warning",
    "notice",
    "informational",
    "debug"
};


static int argo_log_capture_periodic(struct argo_log_registered_structure *structure);

const char *argo_log_priority_string(enum argo_log_priority priority)
{
    if (priority > 7) return "invalid large priority";
    //if (priority < 0) return "invalid negative priority";
    return argo_priorities[priority];
}

const char *argo_log_short_priority_string(enum argo_log_priority priority)
{
    if (priority > 7) return "invalid large priority";
    //if (priority < 0) return "invalid negative priority";
    return argo_syslog_rfc_priorities[priority];
}

enum argo_log_priority argo_log_rfc_priority_string_to_priority(const char *prio_str)
{
    int i;

    for (i = argo_log_priority_debug; i >= 0; i--) {
        if (!strncmp(prio_str, argo_syslog_rfc_priorities[i], strlen(argo_syslog_rfc_priorities[i]))) {
            return i;
        }
    }

    return argo_log_priority_none;
}

#if !defined(__APPLE__) && !defined(__linux__)
static uint64_t argo_log_get_num_pages(void)
{
    uint64_t ignore;
    uint64_t num_pages = 0;
    FILE* fptr = fopen("/proc/self/statm", "r");

    if (!fptr)
        return num_pages;

    if (fscanf(fptr, "%"PRIu64 "%"PRIu64, &ignore, &num_pages) != 2) {
         /*Sane default..assuming 64K page size ==> ~1GB*/
        num_pages = 16384;
    }

    fclose(fptr);

    return num_pages;
}
#endif

#ifdef ARGO_HIGH_TIDE_ENABLED
static uint64_t argo_get_system_free_mem_bytes(void)
{
#ifdef __APPLE__
    static uint64_t page_size = 0;
    if (!page_size) page_size = sysconf(_SC_PAGESIZE);
    uint64_t page_free_count = 0;
    size_t size = sizeof(uint64_t);
    if (sysctlbyname("vm.page_free_count", &page_free_count, &size, NULL, 0) < 0) {
        __sync_add_and_fetch_8(&al_global.scan_fail, 1);
        return 0;
    }
    __sync_add_and_fetch_8(&al_global.mem_other, 1);
    return page_free_count * page_size;
#elif defined(__linux__)
        struct sysinfo info;
        if (sysinfo(&info)) {
            __sync_add_and_fetch_8(&al_global.scan_fail, 1);
            return 0;
        }
        __sync_add_and_fetch_8(&al_global.mem_other, 1);
        return info.freeram * info.mem_unit;
#else
    static uint64_t page_size = 0;
    if (!page_size) page_size = sysconf(_SC_PAGE_SIZE);

    uint64_t num_pages = argo_log_get_num_pages();
    if (!num_pages) {
        __sync_add_and_fetch_8(&al_global.scan_fail, 1);
        return 0;
    }
    __sync_add_and_fetch_8(&al_global.mem_other, 1);
    return al_global.system_mem - (num_pages * page_size);
#endif
}
#endif

/*Get total RAM on the system in bytes*/
static inline uint64_t get_total_ram(void)
{
#ifdef __APPLE__

    //page_size = argo_log_get_num_pages();
    /*
     * latest M1 mac's RAM is total 34359738368 bytes
     *    => 2097152 pages with 16384 bytes page size
     *       [getpagesize() will return the page size]
     */
    int mib[2] = {CTL_HW, HW_MEMSIZE};
    uint64_t system_ram = 0;
    size_t len = sizeof(uint64_t);
    sysctl(mib, 2, &system_ram, &len, NULL, 0);
    return system_ram;

#else

    int64_t pages;
    int64_t page_size;

    if ((pages = sysconf(_SC_PHYS_PAGES)) < 0) {
        /*error*/
        return 0;
    }
    if ((page_size = sysconf(_SC_PAGE_SIZE)) < 0) {
        /*error*/
        return 0;
    }

    return pages * page_size;

#endif
}


/* Pre-requisite: al_global lock */
static void argo_log_global_init_max_mem(int is_thin_app, uint64_t max_logging_mem_mb)
{
    uint64_t total_ram = get_total_ram();

    if (max_logging_mem_mb) {
        al_global.max_mem = max_logging_mem_mb * MB;
    } else {
        if (!total_ram) {
            /*we were not able to determine total RAM in the system*/
            total_ram = 1ul * GB;
            al_global.max_mem = total_ram;
            goto done;
        }
        if (is_thin_app) { /*connector, sarge etc */
            al_global.max_mem = total_ram / 8;
            goto done;
        }

        al_global.max_mem = total_ram / 4;
    }

done:
    al_global.system_mem = total_ram;
    al_global.config_max_mem = al_global.max_mem;
    argo_log_global_set_mem_threshold(total_ram, 0); //Initialize to default here for everyone
    return;
}

/*
 * argo_log_update_instance_name
 *  Updates the instance name, in case we are having a new alt-cloud name and vice-versa.
 */
void
argo_log_update_instance_name(const char *instance_name)
{
    if (!instance_name)
        return;

    /* If name is same, do not change */
    if (!strcmp(al_global.instance_name, instance_name))
        return;

    if (al_global.instance_name[0] != '\0')
        memset(al_global.instance_name, 0, sizeof(al_global.instance_name));

    snprintf(al_global.instance_name, sizeof(al_global.instance_name), "%s", instance_name);

}

/*
 * argo_log_update_partition_name
 *  Updates the partition name for stats reporting
 */
void
argo_log_update_partition_name(const char *partition_name, int reset)
{
    if(reset){
        memset(al_global.partition_name, 0, sizeof(al_global.partition_name));
    } else {
        if (!partition_name)
            return;

        /* If name is same, do not change */
        if (!strcmp(al_global.partition_name, partition_name))
            return;

        if (al_global.partition_name[0] != '\0')
            memset(al_global.partition_name, 0, sizeof(al_global.partition_name));

        snprintf(al_global.partition_name, sizeof(al_global.partition_name), "%s", partition_name);
    }
}


#ifdef ARGO_HIGH_TIDE_ENABLED
static int argo_read_proc_filesystem(uint64_t *free_mem_available)
{
    if (!free_mem_available) {
        __sync_add_and_fetch_8(&al_global.scan_fail, 1);
        return ARGO_RESULT_INSUFFICIENT_DATA;
    }

    FILE *fp = fopen("/proc/meminfo", "r");

    if (fp) {

        __sync_add_and_fetch_8(&al_global.total_proc_reads, 1);

        /* Read input of /proc/meminfo and get value of MemAvailable */
        while (1) {

            char buffer[256] = {'\0'};

            if (fgets(buffer, sizeof(buffer), fp)) {

                char field_string[32] = {'\0'};
                uint64_t sys_free_mem_value = 0;

                /* Not all lines have kB in the end, but all the ones in the first 41 do,
                 * and we expect MemAvailable to be 3rd, so for now this should do */
                if (sscanf(buffer, "%s %"PRIu64" kB", field_string, &sys_free_mem_value) != 2) {
                    break;
                }

                if (strncmp("MemAvailable", field_string, 12) == 0) {
                    /* Bingo */
                    *free_mem_available = (sys_free_mem_value * KB); //KB to bytes
                    fclose(fp);
                    return ARGO_RESULT_NO_ERROR;
                }
            } else {
                /* Should not happen */
                break; /*EOF*/
            }
        } /* end-of-while */

        fclose(fp);
    }

    __sync_add_and_fetch_8(&al_global.scan_fail, 1);
    return ARGO_RESULT_ERR;
}
#endif

#ifdef ARGO_HIGH_TIDE_ENABLED
static void *argo_global_memory_thread(struct zthread_info *zthread, void *arg)
{
    /* most recent reading of al_global.curr_mem when high tide was last computed */
    uint64_t prev_mem = __sync_add_and_fetch_8(&al_global.curr_mem, 0);
    struct pollfd pfd;
    int mem_thread_wake_pipe[2];
    int old_state = ARGO_MEM_HIGH_TIDE_INOPERATIVE;
    int new_state = ARGO_MEM_HIGH_TIDE_INOPERATIVE;

    if (pipe(&mem_thread_wake_pipe[0])) {
        int pipe_error = errno; //for post-mortem analysis if needed - its never '0'
        ARGO_LOGGING_ASSERT(!pipe_error); //this is global memory calculator..so yes..its bye-bye
    }
    ARGO_LOGGING_ASSERT(fcntl(mem_thread_wake_pipe[0], F_SETFL, O_NONBLOCK) >= 0);
    ARGO_LOGGING_ASSERT(fcntl(mem_thread_wake_pipe[1], F_SETFL, O_NONBLOCK) >= 0);

    pfd.fd = mem_thread_wake_pipe[0];
    pfd.events = POLLIN;

    while (1) {

        zthread_heartbeat(zthread);

        pthread_rwlock_rdlock(&al_global.lock);
        int need_first_reading = al_global.curr_sys_free_mem ? 0 : 1;
        uint64_t interval  = al_global.memory_thread_interval;
        uint64_t curr_mem  = al_global.curr_mem;
        uint64_t max_mem   = al_global.max_mem;
        uint64_t c_max_mem = al_global.config_max_mem;
        uint64_t system_mem =  al_global.system_mem;
        uint64_t process_mem_threshold = al_global.process_mem_threshold;
        uint64_t mem_diff_threshold = al_global.mem_difference_threshold;
        uint64_t hightide_in_effect = al_global.process_mem_is_high_tide;
        uint8_t  argo_memory_hightide_disabled = al_global.argo_memory_hightide_disabled;
        pthread_rwlock_unlock(&al_global.lock);

        int read_proc = 0;
        int force_read_sysinfo = 1; //fail safe for reading sysinfo
        int64_t difference = curr_mem - prev_mem;

        if (old_state == ARGO_MEM_HIGH_TIDE_INOPERATIVE && difference > mem_diff_threshold) {
            /* Old state was high tide off, and curr mem has hiked by by more than mem diff threshold.
             * So, do a mem check to see if we need to enable high tide back on
             */
            read_proc = 1;
            prev_mem = curr_mem;
        } else {
            if (old_state == ARGO_MEM_HIGH_TIDE_TRIGGERED) {
                /* since high tide is in operation, take a reading to re-evaluate the situation */
                read_proc = 1;
                prev_mem = curr_mem;
            } else {
                if (need_first_reading) {
                    /* First time we will not have free_sys_mem reading..so need to update it */
                    read_proc = 1;
                    prev_mem = curr_mem;
                } else {
                    /* Since, within logging threshold, and not Hightide do nothing..do NOT update previous reading */
                    force_read_sysinfo = 0;
                }
            }
        }

#ifndef __linux__
        /* Do not attempt to read proc file-system for non-linux platforms
         * They will use only sysinfo call directly
         */
        read_proc = 0;
#endif

        int res = ARGO_RESULT_NO_ERROR;
        uint64_t u_sys_free = 0;
        int64_t sys_free_mem_value = 0;
        int verify_update_hightide_status = 1;


        /* Config override:
         * If argo memory hightide computation is disabled via config-override, do nothing
         */
        if (argo_memory_hightide_disabled) {
            read_proc = 0;
            force_read_sysinfo = 0;
            if (hightide_in_effect) {
                pthread_rwlock_wrlock(&al_global.lock);
                argo_log_set_is_hightide_value(ARGO_MEM_HIGH_TIDE_INOPERATIVE);
                pthread_rwlock_unlock(&al_global.lock);
            }
        }

        /* Do we need memory check? If so, get it.
         * If case of failure, or as per conditions, check if alternate method of obtaining memory info is needed
         * If so, get it that way.
         *
         * In the end, we should be walking away with some value of remaining free RAM estimate in the system
         */
        if (read_proc && (res = argo_read_proc_filesystem(&u_sys_free)) == ARGO_RESULT_NO_ERROR) {
            /* Success case */
            sys_free_mem_value = (int64_t)u_sys_free;
        } else {
            /* Failure case */
            if (force_read_sysinfo || res)
                sys_free_mem_value = (int64_t)argo_get_system_free_mem_bytes();
            else
                verify_update_hightide_status = 0;
        }

        /* One time reading during special case of system init */
        if (need_first_reading) {
            pthread_rwlock_wrlock(&al_global.lock);
            al_global.curr_sys_free_mem = sys_free_mem_value;
            al_global.time_last_reading_s = epoch_s();
            pthread_rwlock_unlock(&al_global.lock);
        }

        if (verify_update_hightide_status) {
            /* Actual argo memory high tide computation logic */
            if (sys_free_mem_value <= (system_mem - process_mem_threshold)) {
                new_state = ARGO_MEM_HIGH_TIDE_TRIGGERED;
            } else {
                new_state = ARGO_MEM_HIGH_TIDE_INOPERATIVE;
            }

            /* If old hightide state is not same as new state, lets update all */
            if (new_state != old_state) {
                pthread_rwlock_wrlock(&al_global.lock);
                argo_log_set_is_hightide_value(new_state);
                if (max_mem > c_max_mem && curr_mem < c_max_mem) {
                    /* If amount of mem that argo is CURRENTLY allowed to use (i.e., UPPER BOUND)
                     * is more than startup time (final CLI) configured argo memory, AND
                     * when CURRENT argo mem usage is LESSER than the startup time configured mem,
                     * THEN pull-down the amount of mem that argo is currently allowed to use to
                     * the level of startup time configured argo mem, i.e., fall back to earlier baseline
                     */
                    argo_log_global_reset_mem_to_conf_nolock();
                }
                al_global.curr_sys_free_mem = sys_free_mem_value;
                al_global.time_last_reading_s = epoch_s();
                pthread_rwlock_unlock(&al_global.lock);

                old_state = new_state;
            } else {
                /* Just update the curr_sys_free_mem for CURL display purposes */
                pthread_rwlock_wrlock(&al_global.lock);
                al_global.curr_sys_free_mem = sys_free_mem_value;
                al_global.time_last_reading_s = epoch_s();
                pthread_rwlock_unlock(&al_global.lock);
            }
        }

        int poll_res = poll(&pfd, 1, interval);

        if (poll_res > 0) {
            /* Should not happen..no one is sending us data yet */
            char dumb_buf[10000];
            read_nowarn(pfd.fd, dumb_buf, sizeof(dumb_buf));
        } else if (poll_res < 0) {
            int poll_errno = errno;
            /* Error. Bad bad bad...should not happen */
            if (argo_log_global_use_printf)
                fprintf(stderr, "%s:%s:%d: Error reading from pipe in argo_global_memory_thread: %s\n",
                        __FILE__, __FUNCTION__, __LINE__, strerror(poll_errno));
            else
                _ARGO_LOG(AL_ERROR, "Error reading from pipe in argo_global_memory_thread: %s",
                          strerror(poll_errno));
        } else {
            /* Timeout...only case currently! */
        }

    } /* end-of-outer-while */

    return NULL;
}
#endif


/*
 * It is ok for the system to log with instance gid of zero before the system figures out its own gid. So accept the
 * situation and be ok with gid argument, being zero.
 * Pre-requisite: al_global lock
 */
int
argo_log_init_with_maxmem(const char *instance_name, int64_t instance_gid,
                          const char *instance_role, const char *instance_build,
                          int is_thin_app, int max_logging_mb)
{
    /* One-time lock intitialization : */
    if (!al_global.initialized) {
        al_global.lock = (pthread_rwlock_t)PTHREAD_RWLOCK_INITIALIZER;
    }

    int error = ARGO_RESULT_NO_ERROR;

    /* Keeps coverity happy..not really needed because no competing access this instant */
    pthread_rwlock_wrlock(&(al_global.lock));

    if (instance_gid) {
        al_global.instance_gid = instance_gid;
    }

    if (instance_name) {

        if (al_global.instance_name[0] != '\0')
            memset(al_global.instance_name, 0, sizeof(al_global.instance_name));

        snprintf(al_global.instance_name, sizeof(al_global.instance_name), "%s", instance_name);

    } else {
        if (al_global.instance_name[0] == '\0') {
            error = ARGO_RESULT_BAD_ARGUMENT;
            goto done;
        }
    }

    if (instance_role) {

        if (al_global.instance_role[0] != '\0')
            memset(al_global.instance_role, 0, sizeof(al_global.instance_role));

        snprintf(al_global.instance_role, sizeof(al_global.instance_role), "%s", instance_role);

    } else {
        if (al_global.instance_role[0] == '\0') {
            error = ARGO_RESULT_BAD_ARGUMENT;
            goto done;
        }
    }

    if (instance_build) {

        if (al_global.instance_build[0] != '\0')
            memset(al_global.instance_build, 0, sizeof(al_global.instance_build));

        snprintf(al_global.instance_build, sizeof(al_global.instance_build), "%s", instance_build);

    } else {
        if (al_global.instance_build[0] == '\0') {
            error = ARGO_RESULT_BAD_ARGUMENT;
            goto done;
        }
    }

    if (al_global.initialized) goto done;

    /* One time initialization follows: */
    __sync_add_and_fetch_4(&(al_global.initialized), 1);

    LIST_INIT(&(al_global.collections_list));

    al_global.log_id = 1;

    al_global.log_desc = argo_register_global_structure(ARGO_LOG_HELPER);
    if (!al_global.log_desc) error = ARGO_RESULT_NO_MEMORY;

    al_global.log_text_desc = argo_register_global_structure(ARGO_LOG_TEXT_HELPER);
    if (!al_global.log_text_desc) error = ARGO_RESULT_NO_MEMORY;

    al_global.log_text_from_file_desc = argo_register_global_structure(ARGO_LOG_TEXT_FROM_FILE_HELPER);
    if (!al_global.log_text_from_file_desc) error = ARGO_RESULT_NO_MEMORY;

    al_global.reader_stats_desc = argo_register_global_structure(ARGO_READER_STATS_HELPER);
    if (!al_global.reader_stats_desc) error = ARGO_RESULT_NO_MEMORY;

    global_argo_log_desc = al_global.log_desc;
    global_argo_log_text_desc = al_global.log_text_desc;

    al_global.collections = argo_hash_alloc(8, 1);
    if (!al_global.collections) error = ARGO_RESULT_NO_MEMORY;

    al_global.system_mem = 0;
    al_global.curr_mem = 0;
    /*Argo library mem usage monitoring variables*/
    al_global.threshold_limit_file_exceed = 0;

#ifdef ARGO_HIGH_TIDE_ENABLED
    argo_log_set_is_hightide_value(ARGO_MEM_HIGH_TIDE_INOPERATIVE);
    al_global.process_mem_threshold = 0;
#endif

    argo_log_global_init_max_mem(is_thin_app, max_logging_mb);
    al_global.peak_mem = al_global.curr_mem;

    if (is_thin_app) {
        al_global.collections_max_poll_timeout = ARGO_LOG_COLLECTIONS_POLL_TIMEOUT_LONG_US;
    } else {
        al_global.collections_max_poll_timeout = ARGO_LOG_COLLECTIONS_POLL_TIMEOUT_SHORT_US;
    }

#ifdef ARGO_HIGH_TIDE_ENABLED
    al_global.memory_thread_interval = ARGO_MEM_THREAD_INTERVAL_MS;
    al_global.mem_difference_threshold = ARGO_MEM_DIFF_THRESHOLD;
    ARGO_LOGGING_ASSERT(!zthread_create(&al_global.memory_thread,
                           argo_global_memory_thread,
                           NULL,                                  /* Input args to func */
                           "argo_global_memory_thread",
                           60,                                    /* 60 second watchdog */
                           16*MB,                                        /* 16 MB stack */
                           60*1000*1000,    /* 60 second statistics collection interval */
                           NULL));
#endif

done:
    pthread_rwlock_unlock(&(al_global.lock));
    return error;
}

int
argo_log_init(const char *instance_name, int64_t instance_gid,
              const char *instance_role, const char *instance_build, int is_thin_app)
{
    return argo_log_init_with_maxmem(instance_name, instance_gid, instance_role, instance_build, is_thin_app, 0);
}

void argo_log_global_set_max_mem(uint64_t max_mem_mb)
{
    pthread_rwlock_wrlock(&(al_global.lock));
    al_global.max_mem = max_mem_mb * MB;
    al_global.config_max_mem = al_global.max_mem;
    pthread_rwlock_unlock(&(al_global.lock));
    return;
}

/* Pre-requisite: al_global lock */
static inline void argo_log_global_set_max_mem_nolock(uint64_t max_mem)
{
    al_global.max_mem = max_mem;
    return;
}

/* Test-ut only!!! Do not invoke from normal code */
void argo_log_global_set_mem_threshold_testing(int8_t on)
{
#ifdef ARGO_HIGH_TIDE_ENABLED
    if (on)
        al_global.process_mem_threshold = al_global.curr_mem - 1;
    else
        al_global.process_mem_threshold = al_global.curr_mem + 1;

    argo_log_set_is_hightide_value(on);
#endif
    return;
}

void argo_log_global_set_mem_threshold(uint64_t total_ram, int threshold_percent)
{
    /* ARGO_MEM_THRESHOLD_DEFAULT by default if not set from CLI */
    if (!threshold_percent) threshold_percent = ARGO_MEM_THRESHOLD_DEFAULT;

    /* Argo mem high tide threshold calculation:
     * Use the value of 'argo_mem_threshold percent' of system memory*/
    uint64_t system_ram = total_ram ? total_ram : get_total_ram();
    uint64_t   argo_ram = ((system_ram * threshold_percent)/100);

    /* No need to lock this because this is the only place we set this
     * and this is set during init so no one is competing for access right now..*/
#ifdef ARGO_HIGH_TIDE_ENABLED
    al_global.process_mem_threshold = argo_ram;
#else
    (void)argo_ram;
#endif

    return;
}

void argo_log_use_printf(int use_printf)
{
    argo_log_global_use_printf = use_printf;
}

void argo_log_set_printf_allowed_bitmap(int bitmap)
{
    argo_log_global_use_printf_allowed_bitmap = bitmap;
}

void argo_log_set_instance_gid(int64_t instance_gid)
{
    al_global.instance_gid = instance_gid;
}

/* Pre-requisite: Collection lock should be held */
static int64_t collection_process_timeouts(struct argo_log_collection *collection, struct argo_log_registered_head *head, int64_t current_us)
{
    int64_t min = current_us + MAX_PERIOD_US;
    int res;
    struct argo_log_registered_structure *walk, *tmp;
    int count = 0;
    int periodic = 0;
    int64_t ts_us = 0;
    int64_t now_us = 0;
    int64_t last_us = 0;
    int64_t start_us = current_us;

    LIST_FOREACH_SAFE(walk, head, list, tmp) {
        count++;
        if (count % 1000 == 0) {
            now_us = epoch_us();
            ts_us = now_us - start_us;
            /* heartbeat monitor: log every 2 seconds if ts_us more than 5 seconds */
            if (ts_us > 5000000l && now_us-last_us > 2000000l ) {
                if (walk->next_us_to_fire <= current_us) periodic++;
                _ARGO_LOG(AL_WARNING, "heartbeat monitor: count=%d, periodic=%d, ts_us=%ld, %s, %s",
                                      count, periodic, (long)ts_us,
                                      collection->name?collection->name:"", walk->name?walk->name:"");
                last_us = now_us;
            }
        }

        if (walk->next_us_to_fire <= current_us) {
            res = argo_log_capture_periodic(walk);
            if (res) {
                /*
                LIST_REMOVE(walk, list);
                ARGO_FREE(walk->name);
                ARGO_FREE(walk->structure_data);
                ARGO_FREE(walk);

                We already have a registration/deregistration mechanism for registered struct's.
                So, registering owner will have a reference and will try to deregister later on.
                So, treat this as a transient error, and skip logging stats for this iteration.
                */
                _ARGO_LOG(AL_WARNING, "Failed to capture periodic stats for rstruct [%s] of type [%s] in %s due to error [%s]",
                          walk->name, walk->description ? argo_description_get_type(walk->description) : "",
                          argo_log_get_name(walk->collection), argo_result_string(res));
                __sync_fetch_and_add_8(&(walk->collection->counters.misc.rstruct_fail), 1);

            }
            walk->next_us_to_fire = current_us - (current_us % walk->period_us) + walk->period_us;
            if (walk->next_us_to_fire < min) min = walk->next_us_to_fire;
        } else {
            if (walk->next_us_to_fire < min) min = walk->next_us_to_fire;
        }
    }
    if (min < current_us) min = current_us + 1;
    return min;
}

static inline int argo_log_global_can_hold_collection(uint64_t coll_size)
{
    int ret = 0;
    pthread_rwlock_rdlock(&(al_global.lock));
    if (!argo_log_is_mem_hightide() && (al_global.curr_mem + coll_size) <= al_global.max_mem)
        ret = 1;
    pthread_rwlock_unlock(&(al_global.lock));
    return ret;
}

static inline void argo_log_global_account_collection_size(uint64_t size)
{
    __sync_fetch_and_add_8(&(al_global.curr_mem), size);

    pthread_rwlock_rdlock(&(al_global.lock));
    const int update =  (al_global.peak_mem < al_global.curr_mem) ? 1 : 0;
    pthread_rwlock_unlock(&(al_global.lock));

    if (update) {
        pthread_rwlock_wrlock(&(al_global.lock));
        al_global.peak_mem = al_global.curr_mem;
        pthread_rwlock_unlock(&(al_global.lock));
    }
    return;
}

static inline void argo_log_global_discount_collection_size(uint64_t size)
{
    __sync_fetch_and_sub_8(&(al_global.curr_mem), size);
}

static int argo_log_calculate_object_size(struct argo_object *log_object)
{
#define INNER_OBJ ((struct argo_object *)(((struct argo_log *)(log_object->base_structure_void))->l_obj))
    return sizeof(struct argo_log_collection_object) +     /*size of struct argo_log_collection_object */
           log_object->total_size + /*size of outer argo object*/
           INNER_OBJ->total_size; /*size of inner argo object*/
}

static struct argo_log_collection_object *argo_log_collection_object_alloc(
                                        struct argo_log_collection *coll,
                                        struct argo_object *log_object)
{
    struct argo_log_collection_object *obj;

    obj = ARGO_CALLOC(sizeof(*obj));
    assert(obj != NULL);
    obj->log_obj = log_object;
#if 0
    if (strcmp(argo_object_get_type(log_object), "argo_log")) {
        fprintf(stderr, "Bad argo log object type = %s\n", argo_object_get_type(log_object));
        abort();
    }
#endif // 0
    obj->log_size = argo_log_calculate_object_size(log_object);
    obj->ref_count = 1;
    return obj;
#undef INNER_OBJ
}

/*Free collection queue object*/
static void argo_log_collection_object_release(struct argo_log_collection_object *obj)
{
    uint32_t count = __sync_sub_and_fetch_4(&(obj->ref_count), 1);
    if (count == 0) {
        argo_object_release(obj->log_obj);
        ARGO_FREE(obj);
    }
    return;
}

static void argo_log_collection_object_hold(struct argo_log_collection_object *obj)
{
    __sync_add_and_fetch_4(&(obj->ref_count), 1);
}

/* Pre-requisite: collection lock */
static inline void argo_log_collection_discount_log_size(
                                            struct argo_log_collection *coll,
                                            enum argo_log_collection_object_priority priority,
                                            enum thread_origin_type thread,
                                            uint64_t log_size)
{
    coll->curr_mem -= log_size;

    coll->counters.object_q.object_q_count[priority]--;
    coll->counters.object_q.object_q_count_tot--;

    coll->counters.object_q.object_q_bytes[priority] -= log_size;
    coll->counters.object_q.object_q_bytes_tot -= log_size;

    coll->counters.evict.evicted_cnt_tot++;
    coll->counters.evict.evicted_cnt[priority]++;
    if (thread == LOGGING_THREAD) {
    }

    if (coll->counters.object_q.object_q_no_readers_count_tot) {
        coll->counters.object_q.object_q_no_readers_count_tot--;
        coll->counters.object_q.object_q_no_readers_bytes_tot -= log_size;
    }

    coll->counters.evict.evicted_bytes_tot += log_size;
    coll->counters.evict.evicted_bytes[priority] += log_size;

    return;
}

/*
 * Must be called with collection lock held, but not required to hold
 * reader lock (but okay if held).
 *
 * Dequeue log object from collection's tailq. This will check
 * readers' read ptr cache to see if it must be invalidated.
 *
 * dequeue_safe is called knowing that the object to be dequeued
 * cannot be referenced by a reader.
 */
/* Pre-requisite: collection lock */
static void argo_log_collection_object_dequeue_safe(
                struct argo_log_collection *collection,
                struct argo_log_collection_object *obj,
                enum thread_origin_type thread)
{
    enum argo_log_collection_object_priority priority;

    priority = ((struct argo_log *)obj->log_obj->base_structure_void)->priority_int;
    priority = al_prio_lookup_table[priority];

    argo_log_collection_discount_log_size(collection, priority, thread, obj->log_size);

    TAILQ_REMOVE(&(collection->log_transmit_q), obj, transmit_q_entry);
    TAILQ_REMOVE(&(collection->log_evict_q[priority]), obj, evict_q_entry);
}

/* Pre-requisite: collection lock */
static void argo_log_collection_object_dequeue(
                struct argo_log_collection *collection,
                struct argo_log_collection_object *obj,
                enum thread_origin_type thread)
{
    struct argo_log_reader *reader;
    LIST_FOREACH(reader, &(collection->readers), list) {
        if (reader->last_read_ptr == obj) {
            reader->last_read_ptr = NULL;
        }
    }

    argo_log_collection_object_dequeue_safe(collection, obj, thread);
}

static inline void collection_shrink_max_mem(struct argo_log_collection *coll, uint64_t size)
{
    /* We want to retain a minimum of COLL_INIT_MEM worth of logs */
    pthread_mutex_lock(&(coll->collection_lock));
    if (coll->max_mem >= size && coll->max_mem > COLL_INIT_MEM) {
        argo_log_global_discount_collection_size(size);
        coll->max_mem -= size;
        coll->counters.evict.resize_down_cnt++;
    }
    pthread_mutex_unlock(&(coll->collection_lock));
}

/* Pre-requisite: collection lock */
static void argo_log_collection_proactive_shrink(struct argo_log_collection *coll)
{
    /*Make sure we have atleast COLL_INIT_MEM worth of space in the collection
     * before we shrink it*/
    if (coll->max_mem <= COLL_INIT_MEM || coll->curr_mem > (coll->max_mem - COLL_INIT_MEM)) {
        coll->shrink_ok_cycles = 0;
        return;
    }

    coll->shrink_ok_cycles++;

    /*wait for enough num of cycles before reducing the collection's max mem*/
    if (coll->shrink_ok_cycles < COLL_SHRINK_OK_MIN_CYCLES)
        return;

    collection_shrink_max_mem(coll, COLL_INIT_MEM);
}

/* Prerequisite: NO collection lock should be held
 *
 * type => reactive or proactive
 * eviction_param => depending on type, interpreted as size or index
 *
 *
 * This routine starts evicting based from the lowest priority queues
 * We try to delete COLL_SINGLE_LOOP_DEL_MAX number of objects before
 * checking to see if it is ideal to yield
 *
 * Yield maybe needed because collections can be huge, and evicting them
 * all in oneshot may cause mayhem elsewhere (ET-49219)
 *
 * Hence after every MAX_PERIOD_US, we proactively try to yield
 *
 * returns the total bytes evicted
 */
static uint64_t collection_eviction_queue_cleanup_reactive(
                                        struct argo_log_collection *collection,
                                        uint64_t cleanup_sz,
                                        enum thread_origin_type thread)
{
    struct argo_log_collection_object *iter;
    struct argo_log_collection_object *obj;
    uint64_t evicted_sz = 0;
    int i, count = 0;

    if (cleanup_sz == 0) return evicted_sz;

    for (i = AL_PRIO_LOW; i >= AL_PRIO_HIGH; i--) {
        pthread_mutex_lock(&(collection->collection_lock));
        iter = TAILQ_FIRST(&(collection->log_evict_q[i]));
        while (iter) {
            count++;
            obj = iter;
            iter = TAILQ_NEXT(iter, evict_q_entry);
            argo_log_collection_object_dequeue(collection, obj, thread);
            evicted_sz += obj->log_size;
            collection->counters.evict.evicted_reactive_bytes_cnt_tot += obj->log_size;
            argo_log_collection_object_release(obj);
            if (evicted_sz >= cleanup_sz) {
                pthread_mutex_unlock(&(collection->collection_lock));
                return evicted_sz;
            }
            /* Release and re-acquire the collection lock to prevent
             * other logging requestors from missing heartbeat
             * Note: only collection_threads are expected to reach this far */
            if (count > COLL_SINGLE_LOOP_DEL_MAX) {
                count = 0;    /* reset count */
                i++;         /* increment i to offset outer loop decrement */
                break;
            }
        }
        pthread_mutex_unlock(&(collection->collection_lock));
    }
    return evicted_sz;
}

static uint64_t collection_eviction_queue_cleanup_proactive(
                                    struct argo_log_collection *collection,
                                    uint64_t max_evict_index,
                                    enum thread_origin_type thread)
{
    uint64_t evicted_sz = 0;
    int count = 0, work_currently_pending = 1;
    struct argo_log_collection_object *obj;

    while (work_currently_pending) {

        work_currently_pending = 0; /* Set below if required */

        pthread_mutex_lock(&(collection->collection_lock));

        while (!TAILQ_EMPTY(&(collection->log_transmit_q))) {
            obj = TAILQ_FIRST(&(collection->log_transmit_q));

            /* Don't go past a max evict index */
            if (obj->index > max_evict_index) break;
            count++;

            argo_log_collection_object_dequeue(collection, obj, thread);
            evicted_sz += obj->log_size;
            collection->counters.evict.evicted_proactive_bytes_cnt_tot += obj->log_size;
            argo_log_collection_object_release(obj);

            /* Release and re-acquire the collection lock to prevent
             * other logging requestors from missing heartbeat
             * Note: only collection_threads are expected to reach this far */
            if (count > COLL_SINGLE_LOOP_DEL_MAX) {
                count = 0;    /* reset count */
                work_currently_pending = 1; /* set to 1 because we need to still carry on deleting */
                break;
            }
        } /*end-of-inner-while*/

        pthread_mutex_unlock(&(collection->collection_lock));
    } /*end-of-outer-while*/


    return evicted_sz;
}

/*
 * Proactive Eviction
 * Interval based - runs every COLL_CLEANUP_INTERVAL_US from collection_thread()
 *
 * Evict log object if it has been ack'd by all the readers.
 *
 * Return value is amount of memory in bytes reclaimed by evicting log objects.
 */
static uint64_t argo_log_collection_proactive_eviction(
        struct argo_log_collection *collection,
        uint64_t max_evict_index,
        enum thread_origin_type thread)
{
    uint64_t evicted_sz = 0;

#ifdef TEMP_DEBUG
    if (!strcmp(collection->name, "event_log")) {
        fprintf(stderr, "event_log_proactive_evict: max_ix = %ld\n", (long) max_evict_index);
    }
#endif

    evicted_sz = collection_eviction_queue_cleanup_proactive(collection, max_evict_index, thread);

    argo_log_collection_proactive_shrink(collection);

    return evicted_sz;
}


/*
 * Reactive Eviction
 * Trigger based - when argo log global mem limit is hit.
 *
 * Memory is reclaimed by evicting log objects from _this_ collection.
 * Reclaim size:
 *     a) Normal condiitons:
 *           MIN(25% of collection->curr_mem, COLL_INIT_MEM)
 *     b) Argo mem high tide condition:
 *          Force will be set in this case.
 *          MAX(25% of collection->curr_mem, custom_size)
 *          We use max because in hightide, we do not want to keep coming back for cleanup
 *          again and again. Logs are low priority now, so create decent space in evictions
 *
 * Log objects from high priority queue may also get evicted if reclaim size
 * is more than memory held by medium and low priority queues.
 *
 * There is a possibilty that all memory is held by high priority queue:
 * - chatty app: logging way too many high priority log objects.
 * - argo log global has not been initialized with enough memory.
 *
 * Return value is amount of memory in bytes reclaimed by evicting log objects
 */
static uint64_t argo_log_collection_reactive_eviction(
                                struct argo_log_collection *collection,
                                uint64_t is_hightide,
                                uint64_t custom_size,
                                enum thread_origin_type thread)
{
    uint64_t evicted_sz;
    uint64_t cleanup_sz;

    if (thread == LOGGING_THREAD) {

        cleanup_sz = custom_size;

    } else {

        cleanup_sz = collection->curr_mem >> 2;

        if (is_hightide) {
            if (cleanup_sz < custom_size)
                cleanup_sz = custom_size;
        } else {
            if (cleanup_sz > COLL_INIT_MEM)
                cleanup_sz = COLL_INIT_MEM;
        }
   }


    collection_shrink_max_mem(collection, cleanup_sz);
    evicted_sz = collection_eviction_queue_cleanup_reactive(collection, cleanup_sz, thread);

    return evicted_sz;
}

/*
 * Get index in the collection's queue upto which reader has
 * successfully ack'd the log objects
 *
 * It is important to note that this is a 'hint'- i.e. it is not
 * guaranteed as it is not retreived with locking enabled.
 *
 * i.e. it can be used to proactive evict, and status logs, but not
 * anything that needs to be precise.
 */
/* Pre-requisite: collection lock */
static inline uint64_t argo_log_reader_get_ack_index(struct argo_log_reader *reader)
{
    if (reader->status_callback) {
        return (reader->status_callback)(reader->callback_cookie);
    } else {
        /* Readers writing to disk file do NOT register status_callback.
         * It can be safely assumed that log object read by such a reader
         * is always successfully written to disk file. So ack index is always
         * one less than read index*/
        return reader->stats.read_index - 1;
    }
}

/*
 * Collection lock is held
 *
 * Find the smallest ack index. This says hint because the smallest
 * ack index may increment before the result is even used.
 */
/* Pre-requisite: collection lock */
static uint64_t argo_log_collection_find_smallest_ack_index_hint(struct argo_log_collection *collection)
{
    struct argo_log_reader *reader = NULL;
    uint64_t smallest_ack_index = UINT64_MAX;
    uint64_t ack_index;

    pthread_mutex_lock(&(collection->collection_lock));
    LIST_FOREACH(reader, &(collection->readers), list) {
        ack_index = argo_log_reader_get_ack_index(reader);
        if (ack_index < smallest_ack_index) {
            smallest_ack_index = ack_index;
        }
    }
    pthread_mutex_unlock(&(collection->collection_lock));

    /* Special case: if there is no reader, then we don't want to
     * throw away any logs. (a reader should come around soon...), so
     * return 0. */
    if (smallest_ack_index == UINT64_MAX) return 0;

    return smallest_ack_index;
}

/* Pre-requisite: collection lock */
static uint64_t collection_proactive_eviction(
                                    struct argo_log_collection *collection,
                                    int64_t current_us,
                                    int64_t *last_cleanup_us,
                                    enum thread_origin_type thread)
{
    uint64_t smallest_ack_index;
    uint64_t bytes_evicted = 0;

    if ((current_us - *last_cleanup_us) < COLL_CLEANUP_INTERVAL_US)
        return bytes_evicted;

    smallest_ack_index = argo_log_collection_find_smallest_ack_index_hint(collection);
    bytes_evicted = argo_log_collection_proactive_eviction(collection, smallest_ack_index, thread);
    *last_cleanup_us = current_us;

    return bytes_evicted;
}

/* Pre-requisite: No collection lock */
static inline int argo_log_collection_can_hold_object_no_lock(
                                            struct argo_log_collection *coll,
                                            uint64_t log_size)
{
    return ((coll->curr_mem + log_size) <= coll->max_mem) ? 1 : 0;
}

 /* If superclean_evict_all is true, then we dump all the objs in the collection */
static int64_t collection_evict_log_objects(
                                        struct argo_log_collection *collection,
                                        int64_t current_us,
                                        int64_t *last_cleanup_us,
                                        int superclean_evict_all)
{
    /*
     * Do NOT jump the gun in acquiring mutexes as that might lead to heartbeat exceeding on other threads..
     * So we'll defer lock taking till the last sec..
     *
     * Also:
     * Do NOT create logging object from within the logging lib (especially during eviction...there is a chance for deadlock)
     * when holding any locks...release all locks and only then call logging APIs
     */

    static int64_t minimum_size = sizeof(struct argo_log_collection_object);

    if (superclean_evict_all) {
        /* Dump everything.....requestor beware */
        /* Currently only invoked via CLI curl command */
        return argo_log_collection_reactive_eviction(collection, 1, collection->curr_mem, CLI_THREAD);
    }

    pthread_mutex_lock(&(collection->collection_lock));
    int can_coll_hold_obj = argo_log_collection_can_hold_object_no_lock(collection, minimum_size);
    int is_eviction_reactive = argo_log_collection_isset_eviction_REACTIVE(collection);
    pthread_mutex_unlock(&(collection->collection_lock));

#ifdef ARGO_HIGH_TIDE_ENABLED
    pthread_rwlock_rdlock(&(al_global.lock));
    int is_hightide = argo_log_is_mem_hightide();
    pthread_rwlock_unlock(&(al_global.lock));
#else
    int is_hightide = 0;
#endif

    /* Clean up space only if we cannot even park one log object into the collection */
    if (!can_coll_hold_obj) {
        if (is_eviction_reactive)
            return argo_log_collection_reactive_eviction(collection, is_hightide, COLL_INIT_MEM, COLLECTION_THREAD);
    } else {
        /*
        * Proactive will wait shrink cycles before shrinking. If we are mem high tide, we are not
        * interested in waiting too many cycles to shrink...so, lets not reset the flag in that case
        */
        if (!is_hightide) argo_log_collection_reset_eviction_REACTIVE(collection);
    }

    return collection_proactive_eviction(collection, current_us, last_cleanup_us, COLLECTION_THREAD);
}

static void *collection_thread(struct zthread_info *zthread, void *arg)
{
    struct argo_log_collection *collection = (struct argo_log_collection *) arg;

    struct argo_log_reader *walk, *tmp;
    struct argo_log_reader *reads[100]; /* should never have more than 100 */
    int reads_count;
    struct argo_log_reader_head deleted_readers;


    FILE **fp_list = NULL;
    int file_count = 0;

    struct pollfd pfd;

    int64_t current_us;
    int64_t next_us;
    int64_t tmp_us;
    int64_t last_flush = 0;

    int64_t last_cleanup_us = 0;

    int res;
    int i;

    /* Create reader for this collection if specified at the time of collection creation */
    if (collection->create_reader) {
        if (collection->create_reader->create_reader_cb) {
            collection->create_reader->create_reader_cb(collection->create_reader);
        }
        ARGO_FREE(collection->create_reader); //done creating reader..not required anymore
        collection->create_reader = NULL;
    }

    LIST_INIT(&deleted_readers);

    pfd.fd = collection->thread_wake_pipe[0];
    pfd.events = POLLIN;

    while (1) {
        zthread_heartbeat(zthread);
        current_us = epoch_us_accuracy_us();
        next_us = current_us + MAX_PERIOD_US;

        /* Grab lock for the duration of our processing periodic timeouts. */
        pthread_mutex_lock(&(collection->collection_lock));

        if (++collection->iterations == 0) ++collection->iterations; //its called paranoia..

        /* Process periodic collection timeouts.  XXX eventually only
         * check the ones needed */
        /* Long timeouts. */
        tmp_us = collection_process_timeouts(collection, &(collection->m_elements), current_us);
        if (tmp_us && (tmp_us < next_us)) next_us = tmp_us;
        /* Short timeouts */
        tmp_us = collection_process_timeouts(collection, &(collection->s_elements), current_us);
        if (tmp_us && (tmp_us < next_us)) next_us = tmp_us;
        /* Tiny timeouts */
        tmp_us = collection_process_timeouts(collection, &(collection->us_elements), current_us);
        if (tmp_us && (tmp_us < next_us)) next_us = tmp_us;

        /* Transfer all collected r_struct argo objects onto our object queue for processing */
        argo_log_collection_internal_q_copy(collection);

        // Take a copy of file list to do fflush without collection lock
        if (argo_log_do_fflush && collection->file_count && ((current_us - last_flush) > 100000)) {
            fp_list = (FILE**)ARGO_ALLOC(sizeof(FILE*) * collection->file_count);
            file_count = 0;
            struct argo_log_file *lf;
            LIST_FOREACH(lf, &(collection->files), list) {
                fp_list[file_count++] = lf->fp;
            }
            last_flush = current_us;
        }

        /* While we're sending logs to remote systems we still hold
         * the lock. Not exactly kind... too bad. (Most log consumers
         * are really, really fast, and all are non-blocking) At some
         * point I will need to add the ability to do simultaneous
         * read/write. */
        reads_count = 0;
        LIST_FOREACH_SAFE(walk, &(collection->readers), list, tmp) {
            if (walk->deleted) {
                LIST_REMOVE(walk, list);
                LIST_INSERT_HEAD(&deleted_readers, walk, list);
                // Do not free reader as it can be referenced in argo_reader_pre_callback (see ET-27471).
            } else {
                reads[reads_count] = walk;
                reads_count++;
            }
        }

        collection->idle = 0;

        /* It will be beneficial to let the function evicting the logs to acquire coll lock.
         * This will enable us to purge all log objects from a collection using REST APIs
         */

        pthread_mutex_unlock(&(collection->collection_lock));

        /* Delete any readers which were marked for delete - note: this is not expected to happen */
        while (!LIST_EMPTY(&deleted_readers)) {
            walk = LIST_FIRST(&deleted_readers);
            LIST_REMOVE(walk, list);
            argo_log_unread(walk);
            walk = NULL;
        }

        collection_evict_log_objects(collection, current_us, &last_cleanup_us, 0);

        /* As both writing to the log file and flushing the file
         * happens in the same collection thread context fflush can
         * be done without holding the collection_lock.
         */
        /* And let's flush our files if necessary... */
        if (fp_list != NULL) {
            for (i = 0; i < file_count; i++) {
                fflush(fp_list[i]);
            }
            ARGO_FREE(fp_list);
            fp_list = NULL;
            file_count = 0;
        }

        int keep_reading = 0;
        for (i = 0; i < reads_count; i++) {
            argo_log_resume(reads[i]);
            if (reads[i]->current_state == argo_log_read_reading) {
                keep_reading = 1;
            }
            //zthread_heartbeat(zthread);
        }

        /* It may have taken us a while to transmit all that
         * stuff. We'll re-grab TOD here so that we don't sleep too
         * long. */
        /* Fix up potential overflow conditions */
        current_us = epoch_us_accuracy_us();
        next_us -= current_us;
        if (keep_reading) {
            next_us = 1000;
        } else if (next_us <= 0) {
            next_us = 1000;
        } else if (next_us > al_global.collections_max_poll_timeout) {
            next_us = al_global.collections_max_poll_timeout;
        }

#ifdef TEMP_DEBUG
        if (!strcmp(collection->name, "event_log")) {
            fprintf(stderr, "event_log: polling waiting %ld ms\n", (long) (next_us / 1000) /* To get milliseconds */);
        }
#endif

#ifdef TEMP_STATS
            pthread_mutex_lock(&(collection->collection_lock));
        if (!collection->stats.last_log_us || (current_us - collection->stats.last_log_us > 60*1000*1000)) {
            _ARGO_LOG(AL_DEBUG, "collection call stats %s: (0: %ld, %ld, %ld, %ld), (1: %ld, %ld, %ld, %ld), (2: %ld, %ld, %ld, %ld), (3: %ld, %ld, %ld, %ld)",
                             collection->name,
                             (long)collection->stats.call_stats[0].total_calls, (long)(collection->stats.call_stats[0].total_calls - collection->stats.call_stats[0].prev_total_calls),
                             (long)collection->stats.call_stats[0].total_walks, (long)(collection->stats.call_stats[0].total_walks - collection->stats.call_stats[0].prev_total_walks),
                             (long)collection->stats.call_stats[1].total_calls, (long)(collection->stats.call_stats[1].total_calls - collection->stats.call_stats[1].prev_total_calls),
                             (long)collection->stats.call_stats[1].total_walks, (long)(collection->stats.call_stats[1].total_walks - collection->stats.call_stats[1].prev_total_walks),
                             (long)collection->stats.call_stats[2].total_calls, (long)(collection->stats.call_stats[2].total_calls - collection->stats.call_stats[2].prev_total_calls),
                             (long)collection->stats.call_stats[2].total_walks, (long)(collection->stats.call_stats[2].total_walks - collection->stats.call_stats[2].prev_total_walks),
                             (long)collection->stats.call_stats[3].total_calls, (long)(collection->stats.call_stats[3].total_calls - collection->stats.call_stats[3].prev_total_calls),
                             (long)collection->stats.call_stats[3].total_walks, (long)(collection->stats.call_stats[3].total_walks - collection->stats.call_stats[3].prev_total_walks));
            for (i = 0; i < 4; i++) {
                collection->stats.call_stats[i].prev_total_calls = collection->stats.call_stats[i].total_calls;
                collection->stats.call_stats[i].prev_total_walks = collection->stats.call_stats[i].total_walks;
            }
            collection->stats.last_log_us = current_us;
            pthread_mutex_unlock(&(collection->collection_lock));
        }
#endif

        res = poll(&pfd, 1, next_us / 1000);
        if (res > 0) {
            /* Received data- We probably need to wake up to send stuff. */
            char dumb_buf[10000];
            read_nowarn(pfd.fd, dumb_buf, sizeof(dumb_buf));
        } else if (res < 0) {
            int poll_errno = errno;
            /* Error. Bad bad bad. */
            if (argo_log_global_use_printf)
                fprintf(stderr, "%s:%s:%d: Error reading from pipe: %s\n",
                        __FILE__, __FUNCTION__, __LINE__, strerror(poll_errno));
            else
                _ARGO_LOG(AL_ERROR, "Error reading from pipe in collection_thread: %s",
                          strerror(poll_errno));
        } else {
            /* Timeout... */
        }
    }

    return NULL;
}

/*
 * Creates a log file
 *
 * Note that all the files are block buffered by default with buffer size of BUFSIZ (FWIW, in my centos it is 8k, OSX it
 * is 1k). We need a strong usecase to change the defaults (setvbuf or setbuf). Since it is block buffered, don't
 * attempt to do fflush on top of it.
 *
 */
struct argo_log_collection *argo_log_create(const char *log_name, struct argo_log_collection_create_reader *reader, int *created)
{
    struct argo_log_collection *collection = NULL;
    int res;
    int i;
    int pipe_created = 0;
    int hash_table_inserted = 0;
    int list_inserted = 0;
    size_t name_size = 0;

    if (!log_name || log_name[0] == '\0') goto fail_free;

    pthread_rwlock_rdlock(&(al_global.lock));

    /* Check if it was already here... */
    collection = (struct argo_log_collection *)argo_hash_lookup(al_global.collections, log_name, strlen(log_name), NULL);

    pthread_rwlock_unlock(&(al_global.lock));

    if (collection) {
        if (created) *created = 0;
        return collection;
    }

    if (!argo_log_global_can_hold_collection(COLL_INIT_MEM)) {

        /* Its generally a bad idea to refuse to create a collection because a log collection is a very basic infra service.
         * Some lib user code might treat it as an error condition, and/or insufficient CLI value might make it catastrophic later.
         *
         * So, let's create it and push the al_global.max_mem (yes, over the CLI specified limit if necessary).
         * To be precise, even if the CLI (or default) level argo maximum mem has been reached, we will still create a collection of
         * standard size, and we will reflect the same in the argo maintained max_mem state.
         *
         * This applies irrespective of whether mem threshold mark is currently breached (for above reason), or if al_global.max_mem
         * has been hit.
         *
         * We will proactively try to recover this mem during toggling of argo memory hightide by
         * trying to re-adjust it back to initialized config mem.
         */

        pthread_rwlock_wrlock(&(al_global.lock));
        al_global.threshold_limit_file_exceed++;
        /*
        fprintf(stderr, "ARGO memory warning: Process memory threshold breached, still creating logging collection %s",
                  log_name ? log_name : "unknown");
        */
        /* Lets do accounting correctly */
        argo_log_global_set_max_mem_nolock(al_global.max_mem + COLL_INIT_MEM);
        pthread_rwlock_unlock(&(al_global.lock));
    }

    collection = (struct argo_log_collection *) ARGO_ALLOC(sizeof(*collection));
    if (!collection) goto fail_free;

    if (created) *created = 1;
    memset(collection, 0, sizeof(*collection));

    name_size = strlen(log_name) + 1;
    collection->name = ARGO_CALLOC(name_size);
    if (!collection->name) goto fail_free;
    snprintf(collection->name, name_size, "%s", log_name); //Null termination guaranteed

    collection->max_mem = COLL_INIT_MEM;
    argo_log_global_account_collection_size(COLL_INIT_MEM);

    TAILQ_INIT(&(collection->log_transmit_q));
    for (i = 0; i < AL_PRIO_LAST; i++) {
        TAILQ_INIT(&(collection->log_evict_q[i]));
    }
    TAILQ_INIT(&(collection->log_delay_q));

    memset(&collection->counters, 0, sizeof(struct collection_counters));

    collection->collection_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    collection->eviction_flags |= AL_EVICTION_IDLE;

    LIST_INIT(&(collection->us_elements));
    LIST_INIT(&(collection->s_elements));
    LIST_INIT(&(collection->m_elements));

    if (pipe(&(collection->thread_wake_pipe[0]))) {
        goto fail_free;
    }
    pipe_created = 1;

    if (fcntl(collection->thread_wake_pipe[0], F_SETFL, O_NONBLOCK) < 0) {
        _ARGO_LOG(AL_ERROR, "Could not make pipe 0 nonblocking");
        goto fail_free;
    }
    if (fcntl(collection->thread_wake_pipe[1], F_SETFL, O_NONBLOCK) < 0) {
        _ARGO_LOG(AL_ERROR, "Could not make pipe 1 nonblocking");
        goto fail_free;
    }

    /* If reader is set, we'll use this at the time of creation of collection thread to create a reader.
     *
     * Note:
     * A collection thread gets created only when the collection becomes active (i.e., first argo object
     * is put into that collection) {see 'thread_fired' variable usage in collection definition}
     * By using this param, we get the functionality of creating a reader for this collection ONLY
     * when the collection becomes active. This is useful for collections created from et_service_endpoint
     * table since majority of those collection are unused
     */
    if (reader) {
        struct argo_log_collection_create_reader *create_reader = ARGO_CALLOC(sizeof(*create_reader));
        memcpy(create_reader, reader, sizeof(*create_reader));
        collection->create_reader = create_reader;
    }

    pthread_rwlock_wrlock(&(al_global.lock));
    res = argo_hash_store(al_global.collections, collection->name, strlen(collection->name), 1, collection);
    if (res) {
        pthread_rwlock_unlock(&(al_global.lock));
        goto fail_free;
    }
    hash_table_inserted = 1;

    LIST_INSERT_HEAD(&(al_global.collections_list), collection, list);
    list_inserted = 1;
    al_global.collections_list_size ++;


    pthread_rwlock_unlock(&(al_global.lock));
    return collection;

 fail_free:
    if (collection) {
        if (pipe_created) {
            close(collection->thread_wake_pipe[1]);
            close(collection->thread_wake_pipe[0]);
        }
        pthread_rwlock_wrlock(&(al_global.lock));
        if (hash_table_inserted) {
            argo_hash_remove(al_global.collections, collection->name, strlen(collection->name), collection);
        }
        if (list_inserted) {
            LIST_REMOVE(collection, list);
        }
        pthread_rwlock_unlock(&(al_global.lock));
        if (collection->name) ARGO_FREE(collection->name);
        ARGO_FREE(collection);
    }
    return NULL;
}

void argo_log_collection_set_max_mem(struct argo_log_collection *coll,
                                                        uint64_t max_mem_mb)
{
    pthread_mutex_lock(&(coll->collection_lock));
    coll->max_mem = max_mem_mb * MB;
    pthread_mutex_unlock(&(coll->collection_lock));
}

uint64_t argo_log_collection_get_max_mem(struct argo_log_collection *coll)
{
    uint64_t max_mem;
    pthread_mutex_lock(&(coll->collection_lock));
    max_mem = coll->max_mem;
    pthread_mutex_unlock(&(coll->collection_lock));
    return max_mem;
}

uint64_t argo_log_collection_get_evicted_cnt_tot(
                            struct argo_log_collection *coll)
{
    uint64_t cnt;
    pthread_mutex_lock(&(coll->collection_lock));
    cnt = coll->counters.evict.evicted_cnt_tot;
    pthread_mutex_unlock(&(coll->collection_lock));
    return cnt;
}

struct argo_log_collection *argo_log_get(const char *log_name)
{
    struct argo_log_collection *collection;

    if (!al_global.initialized) return NULL;

    pthread_rwlock_rdlock(&(al_global.lock));
    collection = (struct argo_log_collection *)argo_hash_lookup(al_global.collections, log_name, strlen(log_name), NULL);
    pthread_rwlock_unlock(&(al_global.lock));

    return collection;
}

char *argo_log_get_name(struct argo_log_collection *collection)
{
    if (!collection) return "NULL";
    return collection->name;
}

/* Get our instance name... Did we forget it? hehe.. */
char *argo_log_get_instance_name(void)
{
    return al_global.instance_name;
}

/*
 * Object creation API might fail, and we might lose a log-id sequence..hence set it only
 * after the heap objects are successfully created. This way, if heap object creation
 * fails for any reason, we will not lose this log-id sequence number forever..
 */
struct argo_object *
argo_log_create_log_object(int64_t epoch_in_us,
                           enum argo_log_priority priority,
                           int include_role,
                           const char *log_name,
                           const char *instance_name,
                           int64_t instance_gid,
                           struct argo_structure_description *description,
                           void *structure_data,
                           const char *role)
{
    struct argo_object *object;
    struct argo_log log;

    memset (&log, 0, sizeof(log));
    if (epoch_in_us) {
        log.l_us = epoch_in_us;
    } else {
        log.l_us = epoch_us_accuracy_us();
    }

    if (instance_name) {
        log.l_inst = instance_name;
    } else {
        log.l_inst = al_global.instance_name;
    }
    if (instance_gid) {
        log.l_inst_gid = instance_gid;
    } else {
        log.l_inst_gid = al_global.instance_gid;
    }
    if (al_global.partition_name[0] != '\0') {
        log.l_pt = al_global.partition_name;
    } else {
        log.l_pt = ARGO_LOG_UNPARTITIONED_NAME;
    }
    log.l_name = log_name;
    log.l_otyp = description->type;
    if (include_role) {
        log.l_role = al_global.instance_role;
    } else {
        log.l_role = role;
    }
    if (priority != argo_log_priority_none) {
        log.l_prio = argo_syslog_rfc_priorities[priority];
        log.priority_int = priority;
    } else {
        log.priority_int = argo_log_priority_info;
    }

    struct argo_object *obj = argo_object_create(description, structure_data);
    if (!obj) {
        return NULL;
    }
    log.l_obj = obj;

    object = argo_object_create(al_global.log_desc, &log);

    if (object) {
        /* Heap object created successfully...so now set the log-id */
        struct argo_log *final_log_obj = object->base_structure_void;
        final_log_obj->l_id = __sync_fetch_and_add_8(&(al_global.log_id), 1);
    }

    /* Creating the object ref-counts obj, so we can release it immediately */
    argo_object_release(obj);

    return object;
}


/* Only used by zpn_event module to cretae log object. */
/*
 * Object creation API might fail, and we might lose a log-id sequence..hence set it only
 * after the heap objects are successfully created. This way, if heap object creation
 * fails for any reason, we will not lose this log-id sequence number forever..
 */
struct argo_object *
argo_log_create_event_log_object(const char *log_name,
                                 const char *priority,
                                 const char *disposition,
                                 const char *system_name,
                                 const char *category_name,
                                 const char *instance_name,
                                 int64_t instance_gid,
                                 struct argo_structure_description *description,
                                 void *structure_data)
{
    struct argo_object *object;
    struct argo_log log;

    memset (&log, 0, sizeof(log));
    log.l_us = epoch_us_accuracy_us();
    log.l_inst = al_global.instance_name;
    log.l_inst_gid = al_global.instance_gid;
    log.l_name = log_name;
    log.l_otyp = "zpn_event_log";
    log.l_prio = priority;
    log.l_disp = disposition;
    log.l_sys = system_name;
    log.l_ctg = category_name;

    if (instance_name) {
        log.l_inst = instance_name;
    }

    if (instance_gid) {
        log.l_inst_gid = instance_gid;
    }

    struct argo_object *obj = argo_object_create(description, structure_data);
    if (!obj) {
        return NULL;
    }
    log.l_obj = obj;

    object = argo_object_create(al_global.log_desc, &log);

    if (object) {
        /* Heap object created successfully...so now set the log-id */
        struct argo_log *final_log_obj = object->base_structure_void;
        final_log_obj->l_id = __sync_fetch_and_add_8(&(al_global.log_id), 1);
    }

    /* Creating the object ref-counts obj, so we can release it immediately */
    argo_object_release(obj);

    return object;
}


static inline void argo_log_collection_account_log_size(
                                            struct argo_log_collection *coll,
                                            enum argo_log_collection_object_priority priority,
                                            uint64_t log_size)
{
    coll->curr_mem += log_size;

    coll->counters.object_q.object_q_count[priority]++;
    coll->counters.object_q.object_q_count_tot++;

    coll->counters.object_q.object_q_bytes[priority] += log_size;
    coll->counters.object_q.object_q_bytes_tot += log_size;

    return;
}

/* Pre-requisite: collection lock */
static void argo_log_collection_object_enqueue(
                struct argo_log_collection *collection,
                struct argo_log_collection_object *obj)
{
    enum argo_log_collection_object_priority priority;

    priority =
        ((struct argo_log *)obj->log_obj->base_structure_void)->priority_int;
    priority = al_prio_lookup_table[priority];

    argo_log_collection_account_log_size(collection, priority, obj->log_size);

    TAILQ_INSERT_TAIL(&(collection->log_transmit_q), obj, transmit_q_entry);
    TAILQ_INSERT_TAIL(&(collection->log_evict_q[priority]), obj, evict_q_entry);

    /* The very first log (at index 0) is always missed, so start it with index 1. */
    if (collection->local_write_index == 0) collection->local_write_index++;

    obj->index = collection->local_write_index;

    collection->local_write_index++;
}

/* Pre-requisite: Needs caller to take collection lock
 *
 * This was written to enable least amount af work under lock protection
 * in logging thread while trying to log an object.
 *
 * We try to see if coll itself can hold object. If yes, all good.
 * If not, we try to increase global mem and do collection accounting
 *
 * Return success of whether ultimately this collection can be logged to at this instant
 */
static inline int argo_log_collection_try_accomodating_log_object(struct argo_log_collection *coll, uint64_t size)
{
    int success = argo_log_collection_can_hold_object_no_lock(coll, size);

    /* If collection itself cannot hold us..check with al_global if coll can expand */
    if (!success) {
        if (argo_log_global_can_hold_collection(COLL_INIT_MEM)) {
            /*additive increase*/
            argo_log_global_account_collection_size(COLL_INIT_MEM);

            /* Protected by collection lock in parent */
            coll->max_mem += COLL_INIT_MEM;
            coll->counters.evict.resize_up_cnt++;

            success = 1;
        } else {
            /* Log failure stats under lock protection */
            uint64_t time_now = epoch_s();

            /* First drop? */
            if (coll->counters.object_q.object_q_dropped_count_tot++ == 0) {
                coll->counters.object_q.object_q_dropped_first_time_in_s = time_now;
            } else {
                coll->counters.object_q.object_q_last_recorded_dropped_time_delta =
                    (time_now - coll->counters.object_q.object_q_dropped_last_time_in_s);
            }

            coll->counters.object_q.object_q_dropped_last_time_in_s = time_now;

            coll->counters.object_q.object_q_dropped_bytes_tot += size;
        }
    }

    /* Active readers check!
     * If there are no active readers in the system, we might want to check against a
     * non-zero ARGO_NO_READER_COLL_LIMIT, to prevent collections with no active readers
     * from infinitely accumulating memory
     */
    if (success && LIST_EMPTY(&coll->readers)) {
        if (coll->counters.object_q.object_q_no_readers_bytes_tot >= ARGO_NO_READER_COLL_LIMIT) {
            coll->counters.object_q.object_q_no_readers_drop_bytes_tot += size;
            success = 0;
        } else {
            coll->counters.object_q.object_q_no_readers_bytes_tot += size;
            coll->counters.object_q.object_q_no_readers_count_tot++;
        }
    }

    return success;
}

static int argo_log_check_mem_limits(struct argo_log_collection *collection,
                                        uint64_t log_obj_size)
{
    /*
     * Lets first check if collection can already accomodate the expansion. If yes, all good.
     * If no:
     *     Check for high tide or al_global.max_mem. If all good, expand coll memory (allocate)
     *     If no:
     *          If not high tide => clear MAX(25% of coll mem || COLL_INIT_MEM)
     *          If high tide,    => clear MAX(25% of coll mem || log_obj_size)
     */
    const int can_log_object = argo_log_collection_try_accomodating_log_object(collection, log_obj_size);

    if (!can_log_object) {
            /* Operating at either high tide or global max mem limit, so evict from within the collection
             * Note: we will not do an aggressive delete since we are here as part of a logging thread
             *
             * Note: Uncomment the below when needed
             *
            pthread_rwlock_rdlock(&(al_global.lock));
            const int is_hightide = argo_log_is_mem_hightide();
            pthread_rwlock_unlock(&(al_global.lock));

            (void)argo_log_collection_reactive_eviction(collection, is_hightide, log_obj_size, LOGGING_THREAD);
            */

            /* al_global max mem limit hit. Trigger reactive eviction */
            argo_log_collection_set_eviction_REACTIVE(collection);
    }

    return can_log_object;
}

/* Pre-requisite: Collection lock should be held */
static int argo_log_structure_delayed(struct argo_log_collection *collection,
                                 enum argo_log_priority priority,
                                 int include_role,
                                 const char *name,
                                 struct argo_structure_description *description,
                                 void *structure_data)
{
    struct argo_object *obj = argo_log_create_log_object(0,
                                                         priority,
                                                         include_role,
                                                         name,
                                                         NULL,
                                                         0,
                                                         description,
                                                         structure_data,
                                                         NULL);
    if (!obj) return ARGO_RESULT_NO_MEMORY;
    /*
     * For now, put it on log_delay_q..later in collection_thread, we'll copy over
     * this entire queue in one shot over to the collection queue under lock protection
     *
     * This has been done this way because of necessity in BREAKING the stranglehold of a
     * recursive collection_lock mutex. The recursive mutex was needed to protect rstructs
     * from getting deregistered when argo_log_capture_periodic was called from collection_thread
     * earlier. This was further because of argo_log_capture_periodic trying to log to the main
     * collection queue directly in this child function, and since that argo_log_log_object_immediate
     * API needs a collection_lock, the collection_lock was likely made recursive instead!
     *
     * This change is to switch back from recursive mutex to normal mutex (to enable cond-var if needed)
     * for a better simpler overall flow which will enable multiple threads to operate if needed.
     */
    struct argo_log_delayed_object *envelope = ARGO_CALLOC(sizeof(struct argo_log_delayed_object));
    if (envelope) {
        envelope->delay_log_obj = obj;
    } else {
        argo_object_release(obj);
        return ARGO_RESULT_NO_MEMORY;
    }
    TAILQ_INSERT_TAIL(&(collection->log_delay_q), envelope, delay_q_entry);
    collection->counters.object_q.delay_q_bytes_tot += argo_log_calculate_object_size(obj) - sizeof(struct argo_log_collection_object);

    /* Statistics collection does not use argo_log_log_object_immediate_api */
    if (!collection->thread_fired) {                                          /* One time only */
        ARGO_LOGGING_ASSERT(!zthread_create(&(collection->thread),
                           collection_thread,
                           collection,
                           collection->name,
                           180,                                 /* 180 second watchdog */
                           16*MB,                                /* 16 MB stack */
                           60*1000*1000,   /* 60 second statistics collection interval */
                           NULL));
        collection->thread_fired = 1;
    }

    return ARGO_RESULT_NO_ERROR;
}

/*
 * CONSUMES ONE REFERENCE COUNT (it is transferred to the queue)
 */
int argo_log_log_object_immediate(struct argo_log_collection *collection,
                                  struct argo_object *log_object)
{
    int do_wake = 0;
    struct argo_log_collection_object *obj;

    if (!collection || !log_object) return ARGO_RESULT_NO_ERROR;

    pthread_mutex_lock(&(collection->collection_lock));

    /* Check first, create later...life will be simpler */
    const int can_log_object = argo_log_check_mem_limits(collection,
                                            argo_log_calculate_object_size(log_object));

    if (can_log_object) {

        if (collection->iterations) {

            argo_object_hold(log_object);

            obj = argo_log_collection_object_alloc(collection, log_object);

            /* Enqueue log obj even if mem limits are hit. It should be OK to cross
             * the limit by a small amount. Eviction will take care of bringing down
             * the memory */
            argo_log_collection_object_enqueue(collection, obj);

            /* Wake up if we have idle readers. */
            do_wake = collection->idle;

            pthread_mutex_unlock(&(collection->collection_lock));

            /* Wake our dequeuing thread if there is a reader waiting for log
             * message. */
            if (do_wake) {
                write_nowarn(collection->thread_wake_pipe[1], "x", 1);
            }
        } else {
            /* For the very first object, the collection_thread will not exist yet...
             * So, put it on the delayed queue and spawn the collection_thread..
             * While the collection_thread is yet to trigger, keep logging to delayed_queue,
             * once collection_thread fires, it will pick up the wole delayed_queue in the exact order
             */
            struct argo_log_delayed_object *envelope = ARGO_CALLOC(sizeof(struct argo_log_delayed_object));
            if (envelope) {
                argo_object_hold(log_object);
                envelope->delay_log_obj = log_object;
                TAILQ_INSERT_TAIL(&(collection->log_delay_q), envelope, delay_q_entry);
                collection->counters.object_q.delay_q_bytes_tot += argo_log_calculate_object_size(log_object) - sizeof(struct argo_log_collection_object);
            }

            /*
             * Heartbeat timeout for collection thread = 120 seconds
             * collection_thread() calls argo_log_syslog_minimal_callback() which uses
             * syslog() blocking call. When msg are being pushed to syslogd at a rate
             * faster than it can consume, we may see syslog() call getting blocked for
             * longer durations. So making the timeout as 120s.
             * Important Note: Argo code MUST NOT use any free-slows after this change.
             * */

            if (!collection->thread_fired) {                                  /* One time only */
                ARGO_LOGGING_ASSERT(!zthread_create(&(collection->thread),
                                   collection_thread,
                                   collection,
                                   collection->name,
                                   180,                                 /* 180 second watchdog */
                                   16*MB,                                /* 16 MB stack */
                                   60*1000*1000,   /* 60 second statistics collection interval */
                                   NULL));
                collection->thread_fired = 1;
            }

            pthread_mutex_unlock(&(collection->collection_lock));
        }
    } else {
        pthread_mutex_unlock(&(collection->collection_lock));
    }

    return ARGO_RESULT_NO_ERROR;
}

/* Pre-requisite: Collection lock should be held */
static void argo_log_collection_internal_q_copy(struct argo_log_collection *collection)
{
    if (!collection) return;

    while (!TAILQ_EMPTY(&(collection->log_delay_q))) {

        struct argo_log_delayed_object *delay_obj = TAILQ_FIRST(&(collection->log_delay_q));

        struct argo_object *log_object = delay_obj->delay_log_obj;

        TAILQ_REMOVE(&(collection->log_delay_q), delay_obj, delay_q_entry);
        collection->counters.object_q.delay_q_bytes_tot -= argo_log_calculate_object_size(log_object) - sizeof(struct argo_log_collection_object);
        ARGO_FREE(delay_obj);

        /* Check first, create later...life will be simpler */
        int can_log_object = argo_log_check_mem_limits(collection,
                                                argo_log_calculate_object_size(log_object));

        if (can_log_object) {

            argo_object_hold(log_object);

            struct argo_log_collection_object *obj = argo_log_collection_object_alloc(collection, log_object);

            /* Enqueue log obj even if mem limits are hit. It should be OK to cross
             * the limit by a small amount. Eviction will take care of bringing down
             * the memory */
            argo_log_collection_object_enqueue(collection, obj);
        }

        /* We are done...release from our side */
        argo_object_release(log_object);
    }

}

int argo_log_structure_immediate(struct argo_log_collection *collection,
                                 enum argo_log_priority priority,
                                 int include_role,
                                 const char *name,
                                 struct argo_structure_description *description,
                                 void *structure_data)
{
    struct argo_object *obj;
    int res;

    obj = argo_log_create_log_object(0,
                                     priority,
                                     include_role,
                                     name,
                                     NULL,
                                     0,
                                     description,
                                     structure_data,
                                     NULL);
    if (!obj) return ARGO_RESULT_NO_MEMORY;

    res = argo_log_log_object_immediate(collection, obj);

    argo_object_release(obj);
    return res;
}

/* Pre-requisite: Collection lock should be held */
static int argo_log_capture_periodic(struct argo_log_registered_structure *structure)
{
    int res;
    /*struct argo_log_reader *reader = structure->pre_log_callback_cookie;*/
    if (structure->structure_count == 1) {
        if (structure->pre_log_callback) {
            (structure->pre_log_callback)(structure->pre_log_callback_cookie, 0,
                                          structure->structure_data[0]);
        }
        res = argo_log_structure_delayed(structure->collection,
                                           argo_log_priority_none,
                                           1,
                                           structure->name,
                                           structure->description,
                                           structure->structure_data[0]);
    } else {
        size_t i;
        size_t field;
        struct argo_object *obj;

        if (structure->pre_log_callback) {
            for (i = 0; i < structure->structure_count; i++) {
                (structure->pre_log_callback)(structure->pre_log_callback_cookie, i,
                                              structure->structure_data[i]);
            }
        }

        obj = argo_object_create(structure->description,
                                 structure->structure_data[0]);
        if (!obj) return ARGO_RESULT_NO_MEMORY;
        /* This function should only log locally known fields!
         * In case we received external log with dynamic fields, we should ignore them. Reasons of dynamic fields being
         * added here is when broker received stats/log with shared structure (e.g. zpath_debug_memory_allocator_stats)
         * which has more fields than local
         * Ref: ET-58181
         */
        for (field = 0; field < structure->description->static_description_count; field++) {
            struct argo_field_description *fd = &(structure->description->description[field]->public_description);
            int64_t obja_value;

            if ((fd->argo_field_type == argo_field_data_type_integer) &&
                (!fd->is_array) &&
                (!fd->is_reference)) {
                obja_value = argo_read_int_offset(structure->structure_data[0],
                                                  fd->offset,
                                                  fd->size);
                //fprintf(stderr, "Field %20s: obja (0) = %ld\n", fd->field_name, (long)obja_value);
                for (i = 1; i < structure->structure_count; i++) {
                    int64_t objb_value;
                    objb_value = argo_read_int_offset(structure->structure_data[i],
                                                      fd->offset,
                                                      fd->size);
                    //fprintf(stderr, "Field %20s: objb (%d) = %ld\n", fd->field_name, i, (long)objb_value);
                    obja_value += objb_value;
                }
                //fprintf(stderr, "Field %20s: obja (0) = %ld\n", fd->field_name, (long)obja_value);
                argo_write_int_offset(obj->base_structure_void,
                                      fd->offset,
                                      obja_value,
                                      fd->size);
            }
        }
        res = argo_log_structure_delayed(structure->collection,
                                           argo_log_priority_none,
                                           1,
                                           structure->name,
                                           structure->description,
                                           obj->base_structure_void);
        argo_object_release(obj);
    }

    /* This will be freed, do not use it anymore, ET-27398 */
    /*if (reader && reader->deleted) structure->pre_log_callback_cookie = NULL;*/

    return res;
}

struct argo_log_registered_structure *
argo_log_register_structure(struct argo_log_collection *collection,
                            const char *name,
                            enum argo_log_priority priority,
                            int64_t period_us,
                            struct argo_structure_description *description,
                            void *structure_data,
                            int log_immediate,
                            argo_log_pre_log_callback_f *pre_log_callback,
                            void *pre_log_callback_cookie)
{
    return argo_log_register_structures(collection,
                                        name,
                                        priority,
                                        period_us,
                                        description,
                                        &structure_data,
                                        1,
                                        log_immediate,
                                        pre_log_callback,
                                        pre_log_callback_cookie);
}

struct argo_log_registered_structure *
argo_log_register_structures(struct argo_log_collection *collection,
                             const char *name,
                             enum argo_log_priority priority,
                             int64_t period_us,
                             struct argo_structure_description *description,
                             void **structure_data,
                             size_t structure_count,
                             int log_immediate,
                             argo_log_pre_log_callback_f *pre_log_callback,
                             void *pre_log_callback_cookie)
{
    struct argo_log_registered_structure *r_struct;

    if (!collection) return ARGO_RESULT_NO_ERROR;

    r_struct = (struct argo_log_registered_structure *)ARGO_ALLOC(sizeof(*r_struct));
    if (!r_struct) return NULL;

    memset(r_struct, 0, sizeof(*r_struct));

    if (period_us < 1) goto fail_free;

    r_struct->name = ARGO_CALLOC(strlen(name) + 1);
    if (!r_struct->name) goto fail_free;
    strcpy(r_struct->name, name);

    r_struct->period_us = period_us;
    r_struct->description = description;
    r_struct->structure_data = ARGO_ALLOC(sizeof(void *) * structure_count);
    if (!r_struct->structure_data) goto fail_free;
    memcpy(r_struct->structure_data, structure_data, sizeof(*structure_data) * structure_count);
    r_struct->structure_count = structure_count;
    r_struct->collection = collection;
    r_struct->pre_log_callback = pre_log_callback;
    r_struct->pre_log_callback_cookie = pre_log_callback_cookie;

    pthread_mutex_lock(&(collection->collection_lock));
    if (period_us < ARGO_LOG_SHORT_TIMEOUT_US) {
        LIST_INSERT_HEAD(&(collection->us_elements), r_struct, list);
    } else if (period_us < ARGO_LOG_MEDIUM_TIMEOUT_US) {
        LIST_INSERT_HEAD(&(collection->s_elements), r_struct, list);
    } else {
        LIST_INSERT_HEAD(&(collection->m_elements), r_struct, list);
    }

    if (log_immediate) {
        int res;
        int64_t current_us = epoch_us_accuracy_us();
        r_struct->next_us_to_fire = current_us - (current_us % r_struct->period_us) + r_struct->period_us;
        res = argo_log_capture_periodic(r_struct);
        if (res) {
            LIST_REMOVE(r_struct, list);
            pthread_mutex_unlock(&(collection->collection_lock));
            goto fail_free;
        }
    }
    pthread_mutex_unlock(&(collection->collection_lock));

    return r_struct;

 fail_free:
    if (r_struct) {
        if (r_struct->name) ARGO_FREE(r_struct->name);
        if (r_struct->structure_data) ARGO_FREE(r_struct->structure_data);
        ARGO_FREE(r_struct);
    }
    return NULL;
}


int argo_log_deregister_structure(struct argo_log_registered_structure *structure,
                                  int log_last_time)
{

    if (!structure) return ARGO_RESULT_NO_ERROR;

    struct argo_log_collection *collection = structure->collection;

    pthread_mutex_lock(&(collection->collection_lock));

    if (log_last_time) {
        argo_log_capture_periodic(structure);
    }

    LIST_REMOVE(structure, list);
    ARGO_FREE(structure->name);
    ARGO_FREE(structure->structure_data);
    ARGO_FREE(structure);

    pthread_mutex_unlock(&(collection->collection_lock));

    return ARGO_RESULT_NO_ERROR;
}


int argo_log_text(struct argo_log_collection *collection,
                  enum argo_log_priority priority,
                  const char *module,
                  const char *file,
                  const char *function,
                  int line,
                  const char *format,
                  ...)
{
    const char *current_thread_name = zthread_name();
    char *alloc_string = NULL;
    size_t len;
    int ret = ARGO_RESULT_NO_ERROR;

    if (argo_log_global_use_printf) {
        va_list list;
        char date_str[ARGO_LOG_GEN_TIME_STR_LEN];
        char tmp_str[4096];

        if ((1 << priority) & argo_log_global_use_printf_allowed_bitmap) {

            va_start(list, format);
            len = vsnprintf(tmp_str, sizeof(tmp_str), format, list);

            if (argo_log_global_use_printf == 2)
                fprintf(stderr, "[%s:%s %s %s:%d]%s %s\n", argo_priorities_short_color[priority], current_thread_name,
                        function, file, line, OUTPUT_RST, tmp_str);
            else if (argo_log_global_use_printf == 3) { // compact with collors
                argo_log_gen_time_only(epoch_us(), date_str, sizeof(date_str));
                fprintf(stderr, "%s %-3s [%9s] %s %s[%s %s:%d]%s\n", date_str, argo_priorities_short_color[priority],
                        module, tmp_str, OUTPUT_GRY, current_thread_name, file, line, OUTPUT_RST);
            } else if (argo_log_global_use_printf == 4) { // compact, no colors
                argo_log_gen_time_only(epoch_us(), date_str, sizeof(date_str));
                fprintf(stderr, "%s %-3s [%9s] %s [%s %s:%d]\n", date_str, argo_priorities_short_no_color[priority],
                        module, tmp_str, current_thread_name, file, line);
            } else if (argo_log_global_use_printf == 5) {
                argo_log_gen_time(epoch_us(), date_str, sizeof(date_str), 0, 0);
                fprintf(stderr, "%s:%8s:%9s:%20s:%32s:%45s:%5d:%s\n", date_str, argo_priorities_short[priority], module,
                        current_thread_name, file, function, line, tmp_str);
            } else {  // 1 or anything else
                argo_log_gen_time(epoch_us(), date_str, sizeof(date_str), 0, 0);
                fprintf(stderr, "%s:%8s:%20s:%32s:%45s:%5d:%s\n", date_str, argo_priorities_short[priority],
                        current_thread_name, file, function, line, tmp_str);
            }
            va_end(list);
        }
        return ARGO_RESULT_NO_ERROR;

    } else {
        struct argo_log_text log;
        va_list list;
        char tmp_str[1024];

        static struct {
            int64_t last_s_clear;
            int64_t current_count;
        } lines[4096];

        int64_t now_s = epoch_s();
        int ix = line & 4095;

        if (!collection) return ARGO_RESULT_NO_ERROR;


        /* The following isn't thread safe, and I don't care. */
        if (now_s != lines[ix].last_s_clear) {
            lines[ix].last_s_clear = now_s;
            lines[ix].current_count = 0;
        }

        log.thread = current_thread_name;
        log.file = file;
        log.function = function;
        log.line = line;

        assert((priority <= argo_log_priority_none) && (priority >= argo_log_priority_emergency));
        if (lines[ix].current_count < argo_log_max_text_per_line_per_second[priority]) {
            va_start(list, format);
            len = vsnprintf(tmp_str, sizeof(tmp_str), format, list);
            if (len >= sizeof(tmp_str)) {
                alloc_string = ARGO_ALLOC(len + 1);
                va_start(list, format);
                len = vsnprintf(alloc_string, len + 1, format, list);
            }
            va_end(list);
        } else if (lines[ix].current_count == argo_log_max_text_per_line_per_second[priority]) {
            snprintf(tmp_str, sizeof(tmp_str), "Log Rate Exceeded %d logs per second for this line",
                     argo_log_max_text_per_line_per_second[priority]);
        } else {
            return ARGO_RESULT_NO_ERROR;
        }

        log.message = alloc_string ? alloc_string : &(tmp_str[0]);
        lines[ix].current_count++;

        ret = argo_log_structure_immediate(collection,
                                           priority,
                                           0,
                                           module,
                                           al_global.log_text_desc,
                                           &log);
        if (alloc_string) ARGO_FREE(alloc_string);
        return ret;
    }
}

/*
 * Read contents from a file and log it.
 * The file could be pseudo filesystem too (like /proc) where stat or fseek magic doesn't work to get the file size.
 * So let us always use a scrap(right now in stack) area to get the file and then transfer it another pointer in heap
 * for log collection to hold.
 *
 * The file contents can be binary too.
 *
 * Caveat:
 * Only the initial 64KB of the file contents will be dumped. Rest just ignored.
 */
int argo_log_text_from_file(struct argo_log_collection *collection, enum argo_log_priority priority, int insert_role,
                            const char *module, const char *file_to_read, const char *file, const char *function,
                            int line)
{
    const char*     current_thread_name = zthread_name();
    FILE*           fptr;
    char            buffer[65536];
    size_t          total_bytes_read;
    size_t          current_bytes_read;
    int             file_not_read_fully;
    int             res;

    fptr = fopen(file_to_read, "rb");
    if (!fptr) {
        res = ARGO_RESULT_ERR;
        goto done;
    }

    total_bytes_read = 0;
    while(!ferror(fptr)) {
        current_bytes_read = fread(buffer + total_bytes_read, 1, (65536 - total_bytes_read - 1), fptr);
        /* not using feof() as that will work only for text file. instead assume EOF, when fread() returns nothing */
        if (0 == current_bytes_read) {
            break;
        }
        total_bytes_read += current_bytes_read;
    }
    file_not_read_fully = 1;
    if (feof(fptr)) {
        file_not_read_fully = 0;
    }

    if (0 == total_bytes_read) {
        res = ARGO_RESULT_ERR;
        goto done;
    }
    buffer[total_bytes_read] = '\0';

    if (argo_log_global_use_printf) {
        char date_str[256];
        argo_log_gen_time(epoch_us(), date_str, sizeof(date_str), 0, 0);

        fprintf(stderr, "%s:%8s:%20s:%30s:%45s:%5d:%s:%zd:%d:%s\n", date_str, argo_priorities_short[priority],
                current_thread_name, file, function, line, file_to_read, total_bytes_read, file_not_read_fully, buffer);
        res = ARGO_RESULT_NO_ERROR;
    } else {
        struct argo_log_text_from_file log;
        if (!collection) {
            if (fptr) fclose(fptr);
            return ARGO_RESULT_NO_ERROR;
        }
        /* It is ok to pass local variable as logging is in-line */
        log.file_path = file_to_read;
        log.file_contents = buffer;
        log.file_contents_size = (int)total_bytes_read;
        log.file_contents_not_fully_read = file_not_read_fully;

        res = argo_log_structure_immediate(collection, priority, insert_role, module,
                                            al_global.log_text_from_file_desc, &log);
    }
done:
    if (fptr) fclose(fptr);
    return res;
}

int argo_log_set_max_text_per_line_per_s(int max)
{
    int iter;
    for (iter = 0; iter <= argo_log_priority_none; iter++) {
        argo_log_max_text_per_line_per_second[iter] = max;
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_log_reset_max_text_per_line_per_s(int max, int priority)
{
    argo_log_max_text_per_line_per_second[priority] = max;
    return ARGO_RESULT_NO_ERROR;
}

int argo_log_get_max_text_per_line_per_s(int priority)
{
    return (argo_log_max_text_per_line_per_second[priority]);
}

/* Prerequisite: No collection_lock */
static void argo_log_obtain_readers_summary_for_printing_nolock(struct argo_log_collection *coll,
                                                    uint64_t *local_readers, uint64_t *local_min_index,
                                                    uint64_t *remote_readers, uint64_t *remote_min_index)
{
    if (!coll) return;

    struct argo_log_reader *reader = NULL;
    uint64_t local = 0, local_index = ULONG_MAX;
    uint64_t remote = 0, remote_index = ULONG_MAX;

    LIST_FOREACH(reader, &coll->readers, list) {
        uint64_t index = argo_log_reader_get_ack_index(reader);
        if (reader->status_callback) {
            remote++;
            remote_index = (index < remote_index) ? index : remote_index;
        } else {
            local++;
            local_index = (index < local_index) ? index : local_index;
        }
    }

    if (local_readers) *local_readers = local;
    if (remote_readers) *remote_readers = remote;
    if (local_min_index) *local_min_index = (local_index == ULONG_MAX) ? 0 : local_index;
    if (remote_min_index) *remote_min_index = (remote_index == ULONG_MAX) ? 0 : remote_index;
}

/*
 * Set the reader to read from a particular index in the queue. The
 * index is the NEXT log that should be read. Everything before index
 * has already been read.
 */
void argo_log_read_set_index(struct argo_log_reader *reader,
                             uint64_t index)
{
    struct argo_log_collection *collection = reader->collection;
#if 0
    int i;
#endif // 0

    pthread_mutex_lock(&(reader->reader_lock));
    pthread_mutex_lock(&(collection->collection_lock));

#ifdef TEMP_DEBUG
    struct argo_log_collection_object *iter;
    iter = TAILQ_FIRST(&(collection->log_transmit_q));
    fprintf(stderr, "SET INDEX: to %ld, first element contains %ld, collection %s. read_index was %ld\n",
            (long) index,
            (long) (iter ? iter->index : -1),
            collection->name,
            (long) reader->stats.read_index);
#endif

#if 0
    /*use index - 1 since log id starts from 1*/
    index--;
    if (index < collection->evict.evicted_cnt_tot) {
        index = collection->evict.evicted_cnt_tot;
    }
    i = index - collection->evict.evicted_cnt_tot;
    reader->last_read_ptr = collection_find_index_n(collection, i, reader->last_read_ptr);
#endif // 0
    //reader->last_read_ptr = collection_find_index_n(collection, index, reader->last_read_ptr);
    COUNT_CALLS(0);
    reader->last_read_ptr = collection_find_index_n(collection, index, NULL);

    if (reader->last_read_ptr) {
        reader->stats.read_index = reader->last_read_ptr->index;
    } else {
        reader->stats.read_index = index;
    }

    reader->stats.seek_cnt++;
    pthread_mutex_unlock(&(collection->collection_lock));
    pthread_mutex_unlock(&(reader->reader_lock));
}

/*
 * This is used just to fill in stats.
 */
static int argo_reader_pre_callback(void *callback_cookie, int counter,
                                    void *structure_data)
{
    struct argo_log_reader *reader = callback_cookie;
    if (reader) {
        /* Do not lock here because it will yield lock inversion in case of statistics_log */

        reader->stats.head_index = reader->collection->local_write_index;
        reader->stats.ack_index = argo_log_reader_get_ack_index(reader);
        /* There is a little massaging here before any logs have arrived */
        if (reader->stats.ack_index == UINT64_MAX) {
            reader->stats.ack_index = 0;
        }
        if ((reader->stats.read_index == 0) && (reader->stats.ack_index == 0)) {
            reader->stats.in_flight = 0;
        } else {
            reader->stats.in_flight = (reader->stats.read_index - reader->stats.ack_index) - 1;
        }
        if (reader->stats.head_index) {
            reader->stats.backlog = reader->stats.head_index - reader->stats.ack_index - 1;
        } else {
            reader->stats.backlog = 0;
        }
        reader->stats.cur_memory = reader->collection->curr_mem;
        reader->stats.max_memory = reader->collection->max_mem;

    }
    return ARGO_RESULT_NO_ERROR;
}

/*
 * Collection lock must be held
 */
static inline struct argo_log_collection_object *
collection_find_index_n(struct argo_log_collection *collection,
                        uint64_t n,
                        struct argo_log_collection_object *starting_at)
{
    struct argo_log_collection_object *iter;
    struct argo_log_collection_object *prev;
    int w = 0;
    if (starting_at) {
        iter = starting_at;
    } else {
        iter = TAILQ_FIRST(&(collection->log_transmit_q));
    }
    if (!iter) return NULL;
    if (iter->index == n) {
        COUNT_WALKS(1);
        return iter;
    } else if (iter->index > n) {
        /* iter is deeper in the queue than the requested index, so
         * search earlier. We never return an index smaller than what
         * was requested. */
        do {
            prev = TAILQ_PREV(iter, argo_log_collection_object_q_head, transmit_q_entry);
            if (!prev) { COUNT_WALKS(1); return iter; }
            if (prev->index < n) { COUNT_WALKS(1); return iter; }
            iter = prev;
            w ++;
        } while (iter->index > n);
        COUNT_WALKS(w);
        return iter;
    } else { // (iter->index < n)
        /* iter is shallower in the queue than the requested index, so
         * search deeper. We never return an index smaller than what
         * was requested. It is common for the reader to have read all
         * the way to the front of the queue, in which case the
         * requested index will not be found, and we will have
         * traversed the whole list for nothing- so we check for that
         * case first */
        struct argo_log_collection_object *last = TAILQ_LAST(&(collection->log_transmit_q), argo_log_collection_object_q_head);
        if (last) {
            if (last->index < n) {
                COUNT_WALKS(1);
                return NULL;
            } else if ((last->index - n) < (n - iter->index)) {
                /* n is closer to the end of the list than to iter, we search backwards */
                iter = last;
                do {
                    prev = TAILQ_PREV(iter, argo_log_collection_object_q_head, transmit_q_entry);
                    if (!prev) { COUNT_WALKS(1); return iter; }
                    if (prev->index < n) { COUNT_WALKS(1); return iter; }
                    iter = prev;
                    w ++;
                } while (iter->index > n);
                COUNT_WALKS(w);
                return iter;
            }
        }
        while (iter && (iter->index < n)) {
            iter = TAILQ_NEXT(iter, transmit_q_entry);
            w ++;
        }
        COUNT_WALKS(w);
        return iter; /* Can be NULL */
    }
    /* Never reached */
}

struct argo_log_reader *argo_log_read(struct argo_log_collection *collection,
                                      const char *name,
                                      uint64_t last_log_id_seen,
                                      int continuous,
                                      argo_log_callback_f callback,
                                      argo_log_status_f status_callback,
                                      void *callback_cookie,
                                      struct argo_log_collection *stats_collection,
                                      int64_t stats_interval_us)
{
    struct argo_log_reader *reader;
    uint64_t id = 0ul;
    int i;

    if (!collection) return NULL;

    reader = (struct argo_log_reader *) ARGO_ALLOC(sizeof(*reader));
    assert(reader != NULL); //for debug only..remove before final image
    //if (!reader) return NULL;

    memset(reader, 0, sizeof(*reader));

    snprintf(reader->name, sizeof(reader->name), "%s", name);

    reader->collection = collection;
    READER_SET_STATE(reader, argo_log_read_idle);
    reader->callback = callback;
    reader->status_callback = status_callback;
    reader->callback_cookie = callback_cookie;
    reader->reader_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    /*
     * We are currently in init_thread and going to access reader object down below. So take a lock on the reader to
     * make sure the reader thread (say event_log thread or stats_log thread or ..) don't get scheduled and get
     * surprised or surprise the init_thread.
     */
    pthread_mutex_lock(&(reader->reader_lock));

    /* Discover where in our queue this reader wants to look... We
     * just do a linear search here beacuse it's easy. We don't expect
     * a ton of readers coming along. */
    pthread_mutex_lock(&(collection->collection_lock));

    if (collection->local_write_index && last_log_id_seen) {
        id = collection->counters.evict.evicted_cnt_tot;
        /* Find out if last_log_id_seen is between id and local_write_index.*/
        if ((last_log_id_seen >= id) &&
                (last_log_id_seen < collection->local_write_index)) {
            reader->stats.read_index = (last_log_id_seen + 1);
        } else {
            reader->stats.read_index = id;
        }
        i = id - collection->counters.evict.evicted_cnt_tot;
        COUNT_CALLS(1);
        reader->last_read_ptr = collection_find_index_n(collection, i, reader->last_read_ptr);
    }
    /* Must exist on a list... We'll pretend we're blocked, and then
     * call unblock. That will dequeue everything correctly and keep
     * state consistent */
    LIST_INSERT_HEAD(&(collection->readers), reader, list);

    pthread_mutex_unlock(&(collection->collection_lock));

    if (stats_collection) {
        int log_immediate;
        char str[1000];
        snprintf(str, sizeof(str), "collection_reader_%s", name);
        reader->stats_reader = argo_log_register_structure(stats_collection,
                                                           str,
                                                           AL_INFO,
                                                           stats_interval_us,
                                                           al_global.reader_stats_desc,
                                                           &(reader->stats),
                                                           log_immediate = 1,
                                                           argo_reader_pre_callback,
                                                           reader);
        if (!reader->stats_reader) {
            _ARGO_LOG(AL_ERROR, "Could not register stats collection for reader for %s", str);
        }
    }

    pthread_mutex_unlock(&(reader->reader_lock));
    return reader;
}

void argo_log_collection_mem_view(char *str, size_t str_len)
{
    struct argo_log_collection *coll = NULL;
    struct argo_log_collection **collection_array = NULL;
    int collections_list_size;
    int i = 0, j = 0;
    char *s = str;
    char *e = str + str_len;

    pthread_rwlock_rdlock(&(al_global.lock));

    collections_list_size = al_global.collections_list_size; // copied, to avoid growing after unlock
    collection_array = ARGO_CALLOC(sizeof(struct argo_log_collection *) * collections_list_size);

    if (!collection_array) {
        pthread_rwlock_unlock(&(al_global.lock));
        return;
    }

    LIST_FOREACH(coll, &(al_global.collections_list), list) {
        collection_array[i] = coll;
        i++;
    }

    s += sxprintf(s, e, "al_global: curr_mem = %"PRIu64", max_mem = %"PRIu64" configured_mem = %"PRIu64"\n",
                  al_global.curr_mem, al_global.max_mem, al_global.config_max_mem);

    pthread_rwlock_unlock(&(al_global.lock));

    qsort(collection_array, collections_list_size, sizeof(struct argo_log_collection *), argo_log_cmptr);

    for (j = i - 1; j >= 0; j--) {

        coll = collection_array[j];

        pthread_mutex_lock(&(coll->collection_lock));
        char * name = coll->name;
        uint64_t curr_mem = coll->curr_mem;
        uint64_t max_mem = coll->max_mem;
        uint64_t wrt_idx = coll->local_write_index;
        uint64_t cnt_total = coll->counters.evict.evicted_cnt_tot;
        uint64_t cnt_total_offset = coll->counters.evict.evicted_cnt_tot_counter_offset;
        uint64_t cnt_LOW = coll->counters.evict.evicted_cnt[AL_PRIO_LOW];
        uint64_t cnt_MED = coll->counters.evict.evicted_cnt[AL_PRIO_MED];
        uint64_t cnt_HIGH = coll->counters.evict.evicted_cnt[AL_PRIO_HIGH];
        uint64_t up = coll->counters.evict.resize_up_cnt;
        uint64_t down = coll->counters.evict.resize_down_cnt;
        uint64_t cycles = coll->shrink_ok_cycles;
        pthread_mutex_unlock(&(coll->collection_lock));

        s += sxprintf(s, e,
                      "collection: name = %s, curr_mem = %"PRIu64", max_mem = %"PRIu64", "
                      " local_write_index = %"PRIu64", evicted_cnt_tot = %"PRIu64", "
                      "evicted_cnt_high = %"PRIu64", evicted_cnt_med = %"PRIu64", "
                      "evicted_cnt_low = %"PRIu64", resize_up_cnt = %"PRIu64", "
                      "resize_down_cnt = %"PRIu64", shrink_ok_cycles = %"PRIu64"\n",
                      name, curr_mem, max_mem, wrt_idx, (cnt_total - cnt_total_offset),
                      cnt_HIGH, cnt_MED, cnt_LOW, up, down, cycles);
    }
    ARGO_FREE(collection_array);
}

void argo_log_collection_iterate(argo_log_collection_iterate_f *callback, void *cookie)
{
    struct argo_log_collection *collection;
    struct argo_log_collection **collection_array;
    int collections_list_size;
    int i = 0;

    pthread_rwlock_rdlock(&(al_global.lock));
    collections_list_size = al_global.collections_list_size; // copied, to avoid growing after unlock
    collection_array = ARGO_CALLOC(sizeof(struct argo_log_collection *) * collections_list_size);
    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        collection_array[i] = collection;
        i++;
    }
    pthread_rwlock_unlock(&(al_global.lock));

    for (i = 0; i < collections_list_size; i++) {
        collection = collection_array[i];
        (callback)(collection, cookie);
    }

    ARGO_FREE(collection_array);
}

/*
 * Dump information about current log collection.
 */
void argo_log_status(struct argo_log_collection *collection, char *str, size_t str_len)
{
    struct argo_log_reader *walk;
    char *s = str;
    char *e = str + str_len;

    struct argo_log_reader *readers[100];
    size_t reader_count = 0;

    size_t i;

    *s = 0;

    pthread_mutex_lock(&(collection->collection_lock));
    LIST_FOREACH(walk, &(collection->readers), list) {
        readers[reader_count] = walk;
        reader_count++;
    }
    pthread_mutex_unlock(&(collection->collection_lock));

    for (i = 0; i < reader_count; i++) {
        pthread_mutex_lock(&(readers[i]->reader_lock));
        argo_reader_pre_callback(readers[i], 0, NULL);
        s += sxprintf(s, e, "Collection %7s, max_mem= %"PRIu64", cur_mem= %"PRIu64", head_index= %"PRIu64", read_index= %"PRIu64", ack_index= %"PRIu64", backlog= %"PRIu64", in_flight= %"PRIu64", total_skips= %"PRIu64", seek_cnt= %"PRIu64", retransmits= %"PRIu64", blocks= %"PRIu64", errors= %"PRIu64", name = %s\n",
                      readers[i]->stats.state,
                      readers[i]->stats.max_memory,
                      readers[i]->stats.cur_memory,
                      readers[i]->stats.head_index,
                      readers[i]->stats.read_index,
                      readers[i]->stats.ack_index,
                      readers[i]->stats.backlog,
                      readers[i]->stats.in_flight,
                      readers[i]->stats.total_skips,
                      readers[i]->stats.seek_cnt,
                      readers[i]->stats.retransmits,
                      readers[i]->stats.blocks,
                      readers[i]->stats.errors,
                      readers[i]->name);
        pthread_mutex_unlock(&(readers[i]->reader_lock));
    }
}



int argo_log_resume(struct argo_log_reader *reader)
{

    /* New log_resume- don't hold lock so long! */

    struct argo_log_collection *collection = reader->collection;
    int res = ARGO_RESULT_NO_ERROR;
    int64_t us;

    pthread_mutex_lock(&(reader->reader_lock));

    if (reader->deleted) {
        /* Dont bother if this reader has been deleted...not expected unless something an error was hit on this reader */
        pthread_mutex_unlock(&(reader->reader_lock));
        return ARGO_RESULT_BAD_STATE;
    }

#ifdef TEMP_DEBUG
    if (!strcmp(collection->name, "event_log")) {
        fprintf(stderr, "event_log_resume: Enter: %s: %ld, state = %s\n", reader->name, (long) epoch_us(), argo_log_read_state_strings[reader->current_state]);
    }
#endif

    us = epoch_us();

    READER_SET_STATE(reader, argo_log_read_reading);

    while (1) {
        /* Grab up to 10 logs to process, then release lock... */
#define MAX_SIMUL_OBJ 10
        struct argo_log_collection_object *obj[MAX_SIMUL_OBJ];
        struct argo_log_collection_object *read_ptr;
        int obj_count;
        int i;

        /* Lock the collection, and grab some objects (There might be none) */
        pthread_mutex_lock(&(collection->collection_lock));

#ifdef TEMP_DEBUG
        if (!strcmp(collection->name, "event_log")) {
            fprintf(stderr, "event_log_resume: start: reader->stats.read_index = %ld, reader->last_read_ptr index = %ld\n",
                    (long)reader->stats.read_index,
                    (long)(reader->last_read_ptr ? reader->last_read_ptr->index : -1));
        }
#endif

        /* Check for empty queue. */
        if (TAILQ_EMPTY(&(collection->log_transmit_q))) {
            /* No objects */
            res = ARGO_RESULT_NO_ERROR;
            READER_SET_STATE(reader, argo_log_read_idle);
            reader->last_read_ptr = NULL;
            /* We set 'idle' whenever any reader has nothing to read. */
            collection->idle = 1;
            pthread_mutex_unlock(&(collection->collection_lock));
#ifdef TEMP_DEBUG
            if (!strcmp(collection->name, "event_log")) {
                fprintf(stderr, "event_log_resume: nothing in queue\n");
            }
#endif
            break;
        }
        obj_count = 0;

        /* Set read ptr in case it was NULL. */
        read_ptr = reader->last_read_ptr;
        if (read_ptr == NULL) {
            /* No objects read yet, or eviction invalidated read_ptr. */
            COUNT_CALLS(2);
            read_ptr = collection_find_index_n(collection, reader->stats.read_index, NULL);
            if (!read_ptr) {
                /* No objects. */
                res = ARGO_RESULT_NO_ERROR;
                READER_SET_STATE(reader, argo_log_read_idle);
                reader->last_read_ptr = NULL;
                /* We set 'idle' whenever any reader has nothing to read. */
                collection->idle = 1;
                pthread_mutex_unlock(&(collection->collection_lock));
#ifdef TEMP_DEBUG
                if (!strcmp(collection->name, "event_log")) {
                    fprintf(stderr, "event_log_resume: nothing found for read_index = %ld\n",
                            (long)(reader->stats.read_index));
                }
#endif
                break;
            }
#ifdef TEMP_DEBUG
            if (!strcmp(collection->name, "event_log")) {
                fprintf(stderr, "event_log_resume: found: read_ptr->index = %ld\n",
                        (long)(read_ptr->index));
            }
#endif

        }

        /* Grab up to MAX_SIMUL_OBJ objects */
        for (obj_count = 0; read_ptr && (obj_count < MAX_SIMUL_OBJ); obj_count++, read_ptr = TAILQ_NEXT(read_ptr, transmit_q_entry)) {
            obj[obj_count] = read_ptr;
            argo_log_collection_object_hold(obj[obj_count]);
            /* Reassigning last_read_ptr here makes sure it is always
             * a valid ptr, rather than ending up null in the terminal
             * case. This makes the code after re-locking more
             * efficient, as it doesn't have to search for the next
             * object in case a new element is appended to the queue
             * once we drop lock (common) */
            reader->last_read_ptr = read_ptr;
        }

        if (obj_count == 0) {
            /* No objects. */
            /* Should never occur, since we guaranteed read_ptr had at least one value a bit above */
            abort();
        }
        pthread_mutex_unlock(&(collection->collection_lock));

        /* NOTE: after this point, 'read_ptr' can refer to unlinked
         * objects, since we are now out of locked region- so we don't
         * ever use it */

        for (i = 0; i < obj_count; i++) {
            res = (reader->callback)(obj[i]->log_obj, reader->callback_cookie, obj[i]->index);
            if (res == ARGO_RESULT_NO_ERROR) {
                // Track retransmit/skips stats and continue callbacks
                int64_t delta = obj[i]->index - reader->max_index_sent_to_reader;
                if (delta > 0) {
                    reader->max_index_sent_to_reader = obj[i]->index;
                    reader->stats.total_skips += (delta - 1);
                } else {
                    if (obj[i]->index != 0) {
                        reader->stats.retransmits++;
                    }
                }
            } else {
#ifdef TEMP_DEBUG
                _ARGO_LOG(AL_NOTICE, "Received %s on obj %d, index %ld",
                          argo_result_string(res),
                          i,
                          (long)obj[i]->index);

#endif
                if (res == ARGO_RESULT_WOULD_BLOCK)
                    reader->stats.blocks++;
                else
                    reader->stats.errors++;

                break;
            }
        }

        /*
         * Reset the reader's read ptr (while locked). This
         * accommodates cases where the queue changed behind our
         * back. Those cases include last_read_ptr being nulled, and
         * includes all variations of objects in our array being
         * removed. (we ref counted them so we will still have the
         * index- though not the list linkages. So we can't
         * necessarily walk... We let collection_find_index_n do
         * that. We rely on eviction to clear last_read_ptr if
         * necessary.
         */
        pthread_mutex_lock(&(collection->collection_lock));

        /* First, calculate what our last_read_ptr ought to be. */
        if (i == obj_count) {
            /* We read every entry, so seek to the next. We didn't fetch next, so estimate it.*/
            reader->stats.read_index = obj[obj_count - 1]->index + 1;
        } else {
            /* We stopped an a specific entry on this list. */
            reader->stats.read_index = obj[i]->index;
        }
        /* Then seek to it. */
        COUNT_CALLS(3);
        reader->last_read_ptr = collection_find_index_n(collection,
                                                        reader->stats.read_index,
                                                        reader->last_read_ptr);

#ifdef TEMP_DEBUG
        if (!strcmp(collection->name, "event_log")) {
            fprintf(stderr, "event_log_resume: later: obj_count = %ld, i = %ld, read_index now %ld, last_read_ptr->index now %ld\n",
                    (long) obj_count,
                    (long) i,
                    (long) reader->stats.read_index,
                    (long) (reader->last_read_ptr ? reader->last_read_ptr->index : -1));
        }
#endif

        pthread_mutex_unlock(&(collection->collection_lock));

        /* Release all the objects we grabbed. */
        for (i = 0; i < obj_count; i++) {
            argo_log_collection_object_release(obj[i]);
        }

        if (res == ARGO_RESULT_WOULD_BLOCK) break;
        if (res != ARGO_RESULT_NO_ERROR) {
            break;
        }
        /* Short circuit after 100ms, so we don't die for heartbeat. */
        if ((epoch_us() - us) > 100000) {
            READER_SET_STATE(reader, argo_log_read_reading);
            break;
        }
    }

    if (res == ARGO_RESULT_WOULD_BLOCK) {
        READER_SET_STATE(reader, argo_log_read_blocked);
    } else if (res == ARGO_RESULT_NO_ERROR) {
    } else {
        /* Should delete it... */
        reader->deleted = 1;
    }

#ifdef TEMP_DEBUG
    if (!strcmp(collection->name, "event_log")) {
        fprintf(stderr, "event_log_resume: Leave: %ld, state = %s, res = %s, stats.read_index = %ld\n",
                (long) epoch_us(), argo_log_read_state_strings[reader->current_state], argo_result_string(res), (long)reader->stats.read_index);
    }
#endif

    pthread_mutex_unlock(&(reader->reader_lock));


    return res;
}



/*
 * This a removal of a read registration.
 *
 * Pre-requisite: NO collection_lock, NO reader_lock
 * Deletes and frees the passed in reader
 */
int argo_log_unread(struct argo_log_reader *reader)
{
    if (reader)  {
        /* The pre-callback function also acquires the reader lock, so first, log
         * for final time before grabbing the lock here, otherwise it will deadlock
         */
        if (reader->stats_reader) {
            argo_log_deregister_structure(reader->stats_reader, 1);
        }

        struct argo_log_collection *collection = reader->collection;
        const char *coll_name = argo_log_get_name(collection);

        __sync_fetch_and_add_8(&collection->counters.misc.readers_deleted, 1);

        _ARGO_LOG(AL_WARNING, "Deletion of argo log reader from collection [%s]: [%s]", coll_name, reader->name);

        //Do not free because reference could be held by fohh_log_connection_unblock and log_tx_conn_unblock_cb :(
        //ARGO_FREE(reader);
        return ARGO_RESULT_NO_ERROR;
    }
    return ARGO_RESULT_ERR;
}

static int argo_log_cmptr(const void *coll_a, const void *coll_b)
{
    const struct argo_log_collection *a = *((struct argo_log_collection **)coll_a);
    const struct argo_log_collection *b = *((struct argo_log_collection **)coll_b);

    if (a == b) return 0;
    if (a && !b) return 1;
    if (!a && b) return -1;

    /* Order of comparing for displaying active argo collections:
     * 1) First show those with most bytes currently on transmit queues;
     * 2) Else, show those with more memory currently in use;
     * 3) Else, show those with fewer objs on the queue (since memory usage are same);
     * 4) Else, show those with more memory allocated;
     * 5) Else, show those with more objects that got deleted (i.e., more active usage);
     * 6) Else, show those with an active thread underpinning them
     */

    uint64_t a_val = a->counters.object_q.object_q_bytes_tot + a->counters.object_q.delay_q_bytes_tot;
    uint64_t b_val = b->counters.object_q.object_q_bytes_tot + b->counters.object_q.delay_q_bytes_tot;
    if (a_val != b_val) return (a_val > b_val) ? 1 : -1;

    COMPARE_FIELD_VALUES(b, a, curr_mem);
    /* Reverse...coll with lowest Q count has more importance due to same bytes */
    COMPARE_FIELD_VALUES(a, b, counters.object_q.object_q_count_tot);
    COMPARE_FIELD_VALUES(b, a, max_mem);
    COMPARE_FIELD_VALUES(b, a, counters.evict.evicted_bytes_tot);
    COMPARE_FIELD_VALUES(b, a, thread_fired);

    uint64_t a_size = (a->name) ? strlen(a->name) :0;
    uint64_t b_size = (b->name) ? strlen(b->name) :0;

    if (a_size || b_size) {
        if (a_size && b_size)
            return strcmp(a->name, b->name);
        else
            return a_size ? 1 : -1;
    }

    return 0; /* Equivalent */
}

void argo_log_show_network_remote_readers_active(struct argo_log_collection ** collections,
                                                 size_t size, char *str, size_t str_len)
{
    if (!collections || !str || str_len == 0) return;

    int i = size;
    int found = 0;

    char *s = str;
    char *e = s + str_len;

    qsort(collections, size, sizeof(struct argo_log_collection *), argo_log_cmptr);

    while (i--) {
        pthread_mutex_lock(&(collections[i]->collection_lock));
        struct argo_log_file *file = LIST_FIRST(&(collections[i]->files));
        uint64_t file_count   = collections[i]->file_count;
        uint64_t max_mem      = collections[i]->max_mem;
        uint64_t delay_q      = collections[i]->counters.object_q.delay_q_bytes_tot;
        uint64_t obj_bytes    = collections[i]->counters.object_q.object_q_bytes_tot + delay_q;
        uint64_t wrt_idx      = collections[i]->local_write_index;
        uint64_t evict_bytes  = collections[i]->counters.evict.evicted_bytes_tot;
        uint64_t proactive    = collections[i]->counters.evict.evicted_proactive_bytes_cnt_tot;
        uint64_t no_readers   = collections[i]->counters.object_q.object_q_no_readers_count_tot;
        uint64_t readerless_b = collections[i]->counters.object_q.object_q_no_readers_drop_bytes_tot;
        uint64_t rstruct_fail = collections[i]->counters.misc.rstruct_fail;
        const char *name      = collections[i]->name;
        uint64_t num_local_readers, num_remote_readers, min_local_ack_index, min_remote_ack_index;
        argo_log_obtain_readers_summary_for_printing_nolock(collections[i],
                                                            &num_local_readers, &min_local_ack_index,
                                                            &num_remote_readers, &min_remote_ack_index);
        pthread_mutex_unlock(&(collections[i]->collection_lock));

        char error_buffer[80] = {'\0'};
        char *start = error_buffer;
        char *end   = start + sizeof(error_buffer);
        if (no_readers && readerless_b)
            start += sxprintf(start, end, " [discarded bytes: %"PRIu64"]", readerless_b);
        if (rstruct_fail)
            start += sxprintf(start, end, " [rstruct_fail: %"PRIu64"]", rstruct_fail);

        if (wrt_idx || obj_bytes) {
            //no op
            if (!found) {
                s += sxprintf(s, e,
                  "\nActual active collections:\n"
                  "-----------------------------\n\n"
                  "   Maximum memory    | Queued bytes (write_index | Readers/Ack_index) |"
                  "    Evicted bytes    | Proactive bytes     | Name\n"
                  "-------------------------------------------------------------"
                  "-------------------------------------------------------------\n");
            }
            found++;
            char local_index_buf[45];
            char remote_index_buf[45];
            char wrt_index[22];
            char index_buf[128];
            snprintf(local_index_buf, sizeof(local_index_buf), "L:(%"PRIu64"/%"PRIu64")", num_local_readers, min_local_ack_index);
            snprintf(remote_index_buf, sizeof(remote_index_buf), "R:(%"PRIu64"/%"PRIu64")", num_remote_readers, min_remote_ack_index);
            snprintf(wrt_index, sizeof(wrt_index), "%"PRIu64, wrt_idx);
            snprintf(index_buf, sizeof(index_buf), "%"PRIu64" (%s|%s|%s)", obj_bytes, wrt_index, num_local_readers ? local_index_buf : "",
                                                                          num_remote_readers ? remote_index_buf : "");
            s += sxprintf(s, e,
                   " %-19"PRIu64" | %s%-45s | %-19"PRIu64" | %-19"PRIu64" | %s%s%s%s\n",
                       max_mem,
                       no_readers ? "#" : " ",
                       index_buf, evict_bytes, proactive,
                       file && file->fp ? "^" : "", file_count ? "*" : "",
                       name, error_buffer);
        } else {
            //No-op
        }
    }
    return;
}

/*
 * Call without any lock!
 * Will acquire both al_global and each connection lock (one at a time)
 * Goal is to give a one place snapshot while debugging this logging infra code.
 */
void argo_log_dump_logging_lib_params(char *str, size_t str_len)
{
    struct argo_log_global al_local = {0};

    uint64_t coll_memory_total = 0;
    uint64_t coll_memory_curr = 0;
    uint64_t coll_e_c_tot = 0;
    uint64_t coll_e_c_off = 0;
    uint64_t coll_e_c_low = 0;
    uint64_t coll_e_c_med = 0;
    uint64_t coll_e_c_high = 0;
    uint64_t coll_e_b_tot = 0;
    uint64_t coll_e_b_low = 0;
    uint64_t coll_e_b_med = 0;
    uint64_t coll_e_b_high = 0;
    uint64_t coll_res_up = 0;
    uint64_t coll_res_down = 0;
    uint64_t coll_q_c_tot = 0;
    uint64_t coll_q_c_low = 0;
    uint64_t coll_q_c_med = 0;
    uint64_t coll_q_c_high = 0;
    uint64_t coll_q_b_tot = 0;
    uint64_t coll_q_b_low = 0;
    uint64_t coll_q_b_med = 0;
    uint64_t coll_q_b_high = 0;
    uint64_t threaded = 0;
    uint64_t no_file_persistence = 0;
    uint64_t empty_collections = 0;
    uint64_t idle_collections = 0;
    uint64_t coll_m_write = 0;
    uint64_t coll_m_malloc = 0;
    uint64_t coll_m_fopen = 0;

    char *s = str;
    char *e = s + str_len;

    size_t size = 0;
    int i = 0;
    struct argo_log_collection *collection = NULL;
    struct argo_log_collection **collections = NULL;

    /* Grab all the info with lock held */
    pthread_rwlock_rdlock(&(al_global.lock));

    /* Make a local copy of al_global */
    memcpy(&al_local, &al_global, sizeof(struct argo_log_global));

    size = al_global.collections_list_size;

    collections = ARGO_ALLOC(size * sizeof(struct argo_log_collection *));

    if (!collections) {
        pthread_rwlock_unlock(&(al_global.lock));
        return;
    }

    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        collections[i++] = collection;
    }

    pthread_rwlock_unlock(&(al_global.lock));

    while(i--) {
        pthread_mutex_lock(&(collections[i]->collection_lock));
        coll_memory_total +=     collections[i]->max_mem;
        coll_memory_curr  +=     collections[i]->curr_mem;
        coll_e_c_tot      +=     collections[i]->counters.evict.evicted_cnt_tot;
        coll_e_c_off      +=     collections[i]->counters.evict.evicted_cnt_tot_counter_offset;
        coll_e_b_tot      +=     collections[i]->counters.evict.evicted_bytes_tot;
        coll_q_c_tot      +=     collections[i]->counters.object_q.object_q_count_tot;
        coll_q_c_low      +=     collections[i]->counters.object_q.object_q_count[AL_PRIO_LOW];
        coll_q_c_med      +=     collections[i]->counters.object_q.object_q_count[AL_PRIO_MED];
        coll_q_c_high     +=     collections[i]->counters.object_q.object_q_count[AL_PRIO_HIGH];
        coll_q_b_tot      +=     collections[i]->counters.object_q.object_q_bytes_tot;
        coll_q_b_low      +=     collections[i]->counters.object_q.object_q_bytes[AL_PRIO_LOW];
        coll_q_b_med      +=     collections[i]->counters.object_q.object_q_bytes[AL_PRIO_MED];
        coll_q_b_high     +=     collections[i]->counters.object_q.object_q_bytes[AL_PRIO_HIGH];
        coll_e_c_low      +=     collections[i]->counters.evict.evicted_cnt[AL_PRIO_LOW];
        coll_e_c_med      +=     collections[i]->counters.evict.evicted_cnt[AL_PRIO_MED];
        coll_e_c_high     +=     collections[i]->counters.evict.evicted_cnt[AL_PRIO_HIGH];
        coll_e_b_low      +=     collections[i]->counters.evict.evicted_bytes[AL_PRIO_LOW];
        coll_e_b_med      +=     collections[i]->counters.evict.evicted_bytes[AL_PRIO_MED];
        coll_e_b_high     +=     collections[i]->counters.evict.evicted_bytes[AL_PRIO_HIGH];
        coll_res_up       +=     collections[i]->counters.evict.resize_up_cnt;
        coll_res_down     +=     collections[i]->counters.evict.resize_down_cnt;
        coll_m_write      +=     collections[i]->counters.misc.write_logfile_fail;
        coll_m_malloc     +=     collections[i]->counters.misc.create_logfile_fail_malloc;
        coll_m_fopen      +=     collections[i]->counters.misc.create_logfile_fail_fopen;
        threaded          +=     collections[i]->iterations ? 1 : 0;
        uint64_t file_cnt  =     collections[i]->file_count;
        uint64_t obj_cnt   =     collections[i]->counters.object_q.object_q_count_tot;
        pthread_mutex_unlock(&(collections[i]->collection_lock));

        if (obj_cnt) {
            if (!file_cnt) no_file_persistence++;
        } else {
            empty_collections++;
            if (!file_cnt) idle_collections++;
        }
    }

#ifdef ARGO_HIGH_TIDE_ENABLED
    uint64_t sys_mem_free = al_local.curr_sys_free_mem;
#endif

    s += sxprintf(s, e,
                  "\nInstance details:%s\n" "-----------------\n"
                  " Name  = %s\n" " GID   = %"PRId64"\n" " Role  = %s\n" " Build = %s\n"
                  "\nMemory details:\n" "---------------\n"
                  " System RAM*       : %-20"PRIu64 " (%"PRIu64 " MB)\n"
#ifdef ARGO_HIGH_TIDE_ENABLED
                  " System RAM free*  : %-20"PRIu64 " (%"PRIu64 " MB)\n"
#endif
                  " Configured mem    : %-20"PRIu64 " (%"PRIu64 " MB)\n"
                  " Max mem allowed   : %-20"PRIu64 " (%"PRIu64 " MB)\n"
                  " Current mem used  : %-20"PRIu64 " (%"PRIu64 " MB)\n"
                  " Peak utilization  : %-20"PRIu64 " (%"PRIu64 " MB)\n"
#ifdef ARGO_HIGH_TIDE_ENABLED
                  " High tide mark    : %-20"PRIu64 " (%"PRIu64 " MB)\t%s\n"
                  " Memory check intv : %"PRIu64" seconds\n"
                  " Memory diff thres : %-20"PRIu64 " (%"PRIu64 " MB)\n"
                  " Memory check count: Proc: %"PRIu64", Other: %"PRIu64
                  " (fail: %"PRIu64 "(p),%"PRIu64 "(s), other: %"PRIu64 ") Time elapsed (s): %"PRIu64"\n"
#endif
                  "\nCollection details:\n" "-------------------\n"
                  " Collections count : %-12"PRIu64 " (Threaded: %"PRIu64" [NoDiskFile: %"PRIu64"], Empty: %"PRIu64" [NoDiskFile: %"PRIu64"])\n"
                  " Log create exceed : %-12"PRIu64"\n"
                  " Disk logfile NA   : %-20"PRIu64 " " "%"PRIu64"/""%"PRIu64 " (malloc/fopen)\n"
                  " Poll timeout      : %-12"PRId64"\n"
                  " Usage (actual)    : %-20"PRIu64  " (%"PRIu64 " MB)\n"
                  " Usage (allocated) : %-20"PRIu64  " (%"PRIu64 " MB)\n"
                  " Total queued objs : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "             low   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "             med   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "            high   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  " Eviction counts   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "             low   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "             med   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  "            high   : %-20"PRIu64" (%"PRIu64" bytes (%"PRIu64 " MB))\n"
                  " Resize       UP   : %-20"PRIu64"\n"
                  "            DOWN   : %-20"PRIu64"\n\n",
                  (al_local.initialized) ? "" : " (Un-initialized)",
                  al_local.instance_name, al_local.instance_gid,
                  al_local.instance_role, al_local.instance_build,
                  al_local.system_mem, convert_MB(al_local.system_mem),
#ifdef ARGO_HIGH_TIDE_ENABLED
                  sys_mem_free, convert_MB(sys_mem_free),
#endif
                  al_local.config_max_mem, convert_MB(al_local.config_max_mem),
                  al_local.max_mem, convert_MB(al_local.max_mem),
                  al_local.curr_mem, convert_MB(al_local.curr_mem),
                  al_local.peak_mem, convert_MB(al_local.peak_mem),
#ifdef ARGO_HIGH_TIDE_ENABLED
                  al_local.process_mem_threshold, convert_MB(al_local.process_mem_threshold),
                  al_local.argo_memory_hightide_disabled ? "(High tide disabled)" :
                  al_local.process_mem_is_high_tide ? "(High tide in effect)" : "",
                  al_local.memory_thread_interval / 1000,
                  al_local.mem_difference_threshold, convert_MB(al_local.mem_difference_threshold),
                  al_local.total_proc_reads, al_local.mem_other, al_local.proc_reads_fail, al_local.scan_fail,
                  al_local.total_sysinfo_reads, epoch_s() - al_local.time_last_reading_s,
#endif
                  al_local.collections_list_size,
                  threaded, no_file_persistence, empty_collections, idle_collections,
                  al_local.threshold_limit_file_exceed,
                  coll_m_write, coll_m_malloc, coll_m_fopen,
                  al_local.collections_max_poll_timeout,
                  coll_memory_curr, convert_MB(coll_memory_curr),
                  coll_memory_total, convert_MB(coll_memory_total),
                  coll_q_c_tot,
                  coll_q_b_tot, convert_MB(coll_q_b_tot),
                  coll_q_c_low,
                  coll_q_b_low, convert_MB(coll_q_b_low),
                  coll_q_c_med,
                  coll_q_b_med, convert_MB(coll_q_b_med),
                  coll_q_c_high,
                  coll_q_b_high, convert_MB(coll_q_b_high),
                  (coll_e_c_tot - coll_e_c_off),
                  coll_e_b_tot, convert_MB(coll_e_b_tot),
                  coll_e_c_low,
                  coll_e_b_low, convert_MB(coll_e_b_low),
                  coll_e_c_med,
                  coll_e_b_med, convert_MB(coll_e_b_med),
                  coll_e_c_high,
                  coll_e_b_high, convert_MB(coll_e_b_high),
                  coll_res_up,
                  coll_res_down
                );

    argo_log_show_network_remote_readers_active(collections, size, s, str_len - (s - str));
    ARGO_FREE(collections);

    return;
}

int64_t argo_log_purge_collections(int all, const char *name, uint64_t unused)
{
    struct argo_log_collection *collection = NULL;
    struct argo_log_collection **collections = NULL;
    int64_t bytes_evicted = 0;
    uint32_t size = 0;
    int i = 0;
    (void)unused;

    /* Again, already holding al_global while trying to lock a collection is a BIG blunder.
     * al_global should universally (in this file here) be acquired either:
     *  a) Independently without holding any collection lock; OR
     *  b) ALWAYS after a collection_lock but NEVER before a collection lock.
     * Since we need to get list of collections, make a copy of the state and then iterate
     * through that local copy AFTER releasing al_global lock
     */

    if (!all && !name) return bytes_evicted;

    pthread_rwlock_rdlock(&(al_global.lock));

    if (all) {
        size = al_global.collections_list_size;

        collections = ARGO_ALLOC(size * sizeof(struct argo_log_collection *));
        if (!collections) {
            pthread_rwlock_unlock(&(al_global.lock));
            return bytes_evicted;
        }

        LIST_FOREACH(collection, &(al_global.collections_list), list) {
            collections[i++] = collection;
        }
    } else {
        collection = argo_hash_lookup(al_global.collections, name, strlen(name), NULL);
        if (!collection) {
            pthread_rwlock_unlock(&(al_global.lock));
            return bytes_evicted;
        }
    }

    pthread_rwlock_unlock(&(al_global.lock));

    /* Now lets iterate local copy and evict from each..the callee function will
     * take care of acquiring both locks in required order
     */
    if (all) {
        while(i--) {
            bytes_evicted += collection_evict_log_objects(collections[i], 0, NULL, 1);
        }
    } else {
        /* No memory malloced if specific collection...just return */
        return collection_evict_log_objects(collection, 0, NULL, 1);
    }

    ARGO_FREE(collections);

    return bytes_evicted;
}

void argo_log_logging_lib_params_reset(void)
{
    struct argo_log_collection *collection = NULL;
    struct argo_log_collection **collections = NULL;
    uint32_t size = 0;
    int i = 0;

    pthread_rwlock_rdlock(&(al_global.lock));

    size = al_global.collections_list_size;

    collections = ARGO_ALLOC(size * sizeof(struct argo_log_collection *));
    if (!collections) {
        pthread_rwlock_unlock(&(al_global.lock));
        return;
    }

    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        collections[i++] = collection;
    }

    pthread_rwlock_unlock(&(al_global.lock));


    while (i--) {
        pthread_mutex_lock(&(collections[i]->collection_lock));
        uint64_t eviction_count_total = collections[i]->counters.evict.evicted_cnt_tot;
        /* Wipe slate clean */
        memset(&(collections[i]->counters.evict), 0, sizeof(struct eviction_counters));
        /* reset offset also to same value as total to show accurate eviction count in output*/
        collections[i]->counters.evict.evicted_cnt_tot = eviction_count_total;
        collections[i]->counters.evict.evicted_cnt_tot_counter_offset = eviction_count_total;
        pthread_mutex_unlock(&(collections[i]->collection_lock));
    }

    ARGO_FREE(collections);
    return;
}

void argo_log_global_set_mem_threshold_rest_endpoint(uint64_t size_in_MB)
{
    if (!size_in_MB) return;

#ifdef ARGO_HIGH_TIDE_ENABLED
    pthread_rwlock_wrlock(&(al_global.lock));
    al_global.process_mem_threshold = size_in_MB * MB;
    pthread_rwlock_unlock(&(al_global.lock));
#endif

    return;
}

void argo_log_global_set_mem_difference_threshold_rest_endpoint(uint64_t size_in_MB)
{
    if (!size_in_MB) return;

#ifdef ARGO_HIGH_TIDE_ENABLED
    pthread_rwlock_wrlock(&(al_global.lock));
    al_global.mem_difference_threshold = size_in_MB * MB;
    pthread_rwlock_unlock(&(al_global.lock));
#endif

    return;
}

void argo_log_global_set_memory_thread_interval_rest_endpoint(uint64_t interval_s)
{
    if (!interval_s) return;

#ifdef ARGO_HIGH_TIDE_ENABLED
    pthread_rwlock_wrlock(&(al_global.lock));
    al_global.memory_thread_interval = interval_s * 1000; //milliseconds
    pthread_rwlock_unlock(&(al_global.lock));
#endif

    return;
}

void argo_disable_memory_hightide(uint64_t disable)
{
#ifdef ARGO_HIGH_TIDE_ENABLED
    pthread_rwlock_wrlock(&(al_global.lock));
    if (disable)
        al_global.argo_memory_hightide_disabled = 1;
    else
        al_global.argo_memory_hightide_disabled = 0;
    pthread_rwlock_unlock(&(al_global.lock));

    _ARGO_LOG(AL_WARNING, "Argo memory hightide enforcement: %s", disable ? "DISABLED" : "ENABLED");
#endif
}

int argo_collections_snapshot(void)
{
    struct argo_log_collection *collection;
    pthread_rwlock_rdlock(&(al_global.lock));
    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        /* Don't really need to lock here. */
        collection->snapshot_write_index = collection->local_write_index;
    }
    pthread_rwlock_unlock(&(al_global.lock));
    return ARGO_RESULT_NO_ERROR;
}

int64_t argo_collections_verify_snapshot(void)
{
    struct argo_log_collection *collection = NULL;
    struct argo_log_collection **collections = NULL;
    uint32_t size = 0;
    int i = 0, j = 0;
    int64_t total = 0;

#define MAX_READERS 100

    pthread_rwlock_rdlock(&(al_global.lock));

    size = al_global.collections_list_size;
    collections = ARGO_ALLOC(size * sizeof(struct argo_log_collection *));

    if (!collections) {
        pthread_rwlock_unlock(&(al_global.lock));
        return total;
    }

    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        collections[i++] = collection;
    }

    pthread_rwlock_unlock(&(al_global.lock));


    while(i--) {
        struct argo_log_reader *readers[MAX_READERS] = {NULL};
        struct argo_log_reader *reader = NULL;
        int count = 0;
        uint64_t snapshot_index;

        /* The lock order is, first reader lock and only then collection lock..
         * Inverting this order could result in potential deadlock
         */
        pthread_mutex_lock(&(collections[i]->collection_lock));
        LIST_FOREACH(reader, &(collections[i]->readers), list) {
            if (count < MAX_READERS) readers[count++] = reader;
        }
        reader = NULL;
        snapshot_index = collections[i]->snapshot_write_index;
        pthread_mutex_unlock(&(collections[i]->collection_lock));

        for (j = 0; j < count; j++) {
            argo_reader_pre_callback(readers[j], 0, NULL);
            int64_t diff = snapshot_index - readers[j]->stats.ack_index;
            if (diff > 1) {
                fprintf(stderr, "Waiting for %ld objects to drain from collection %s, reader %s\n",
                        (long) diff,
                        collections[i]->name,
                        readers[j]->name);
                total += diff;
            } else {
                /*
                 * ZZZZ: dont flood the logs please....hard to pick up relevant stuff
                fprintf(stderr, "Collection %s, reader %s up to date\n",
                        collections[i]->name,
                        readers[j]->name);
                */
            }
        }
    }

    ARGO_FREE(collections);
    return total;
}

int argo_log_syslog_clean_callback(struct argo_object *log_object,
                                   void *callback_cookie,
                                   int64_t argo_log_sequence)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        struct argo_log *log = (struct argo_log *)log_object->base_structure_data;
        if (log->l_obj && (argo_global.all_descriptions[log->l_obj->base_structure_index] == global_argo_log_text_desc)) {
            /* Log in a cleaner form for syslog. */
            struct argo_log_text *text = log->l_obj->base_structure_void;
            syslog(log->priority_int, "%-30s:%5d:%s", text->file, text->line, text->message);
        } else {
            char *dump = argo_object_dump_allocated(log_object, 0);
            if (dump) {
                syslog(log->priority_int, "%s", dump);
                ARGO_FREE(dump);
            }
        }
    }

    return ARGO_RESULT_NO_ERROR;
}


int argo_log_syslog_minimal_callback(struct argo_object *log_object,
                                     void *callback_cookie,
                                     int64_t argo_log_sequence)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        struct argo_log *log = (struct argo_log *)log_object->base_structure_data;
        if (log->l_obj && (argo_global.all_descriptions[log->l_obj->base_structure_index] == global_argo_log_text_desc)) {
            /* Log in a cleaner form for syslog. */
            struct argo_log_text *text = log->l_obj->base_structure_void;
            syslog(log->priority_int, "%s", text->message);
        } else {
            char *dump = argo_object_dump_allocated(log_object, 0);
            if (dump) {
                syslog(log->priority_int, "%s", dump);
                ARGO_FREE(dump);
            }
        }
    }

    return ARGO_RESULT_NO_ERROR;
}


int argo_log_syslog_callback(struct argo_object *log_object,
                             void *callback_cookie,
                             int64_t argo_log_sequence)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        char *dump = argo_object_dump_allocated(log_object, 0);
        if (dump) {
            syslog(log->priority_int, "%s", dump);
            ARGO_FREE(dump);
        }
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_log_stderr_clean_callback(struct argo_object *log_object,
                                   void *callback_cookie,
                                   int64_t argo_log_sequnce)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        struct argo_log *log = (struct argo_log *)log_object->base_structure_data;

        if (log->l_obj && (argo_global.all_descriptions[log->l_obj->base_structure_index] == global_argo_log_text_desc)) {
            /* Log in a cleaner form for syslog. */
            struct argo_log_text *text = log->l_obj->base_structure_void;
            fprintf(stderr, "%-20s:%10s:%5d:%s\n", text->file, log->l_prio, text->line, text->message);
        } else {
            char *dump = argo_object_dump_allocated(log_object, 0);
            if (dump) {
                syslog(log->priority_int, "%s", dump);
                ARGO_FREE(dump);
            }
        }
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_log_stderr_minimal_callback(struct argo_object *log_object,
                                     void *callback_cookie,
                                     int64_t argo_log_sequnce)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        struct argo_log *log = (struct argo_log *)log_object->base_structure_data;

        if (log->l_obj && (argo_global.all_descriptions[log->l_obj->base_structure_index] == global_argo_log_text_desc)) {
            /* Log in a cleaner form for syslog. */
            struct argo_log_text *text = log->l_obj->base_structure_void;
            fprintf(stderr, "%10s:%s\n", log->l_prio, text->message);
        } else {
            char *dump = argo_object_dump_allocated(log_object, 0);
            if (dump) {
                fprintf(stderr, "%s", dump);
                ARGO_FREE(dump);
            }
        }
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_log_stderr_callback(struct argo_object *log_object,
                             void *callback_cookie,
                             int64_t argo_log_sequence)
{
    int mask;
    struct argo_log *log = (struct argo_log *) log_object->base_structure_data;

    if (callback_cookie) {
        mask = *((int *)callback_cookie);
    } else {
        /* No DEBUG, No Info for syslog.*/
        mask = 0x3f;
    }

    if ((1 << log->priority_int) & mask) {
        /* This priority log message is desired for syslog. */
        char *dump = argo_object_dump_allocated(log_object, 0);
        if (dump) {
            fprintf(stderr, "%s", dump);
            ARGO_FREE(dump);
        }
    }

    return ARGO_RESULT_NO_ERROR;
}


static void argo_gen_filename(const char *filename, char *buf, size_t buf_len)
{
    struct timeval tv;
    struct tm tm;

    gettimeofday(&tv, NULL);
    gmtime_r(&(tv.tv_sec), &tm);
    snprintf(buf, buf_len, "%s.UTC.%04d-%02d-%02d.%02d:%02d:%02d.%06d",
             filename,
             tm.tm_year + 1900,
             tm.tm_mon + 1,
             tm.tm_mday,
             tm.tm_hour,
             tm.tm_min,
             tm.tm_sec,
             (int)tv.tv_usec);
}


/*
 * Argo callback for writing serialized data to file.
 *
 * Do not attempt to return WOULD_BLOCK here to prevent deletion of such reader from collection.
 * Resulting log file contains partial data unusable with agrep after disk space is cleaned and logging continues.
 */
static int argo_log_file_argo_write(void *cookie, const uint8_t *data, size_t length)
{
    size_t written;
    struct argo_log_file *logfile = (struct argo_log_file *)cookie;

    /* Weird, I know..but if disk space is exhausted, then we will not have a file handle
     * Just crashing is un-acceptable...we should soldier on as long as we are not OOMing
     * So, only write if a file pointer exists..not having one is not an error..
     */
    if (logfile->fp) {
        written = fwrite(data, length, 1, logfile->fp);
        if (written != 1) {
            return ARGO_RESULT_ERR;
        }
    } else {
        __sync_fetch_and_add_8(&(logfile->collection->counters.misc.write_logfile_fail), 1);
    }

    logfile->current_written += length;
    logfile->total_written += length;

    if (logfile->current_written >= logfile->max_length) {
        char tmp_name[1000];
        if (logfile->fp) {
            fclose(logfile->fp);
            logfile->fp = NULL;
        }
        argo_gen_filename(logfile->filename, tmp_name, sizeof(tmp_name));
        logfile->fp = fopen(tmp_name, "w");
        if (!logfile->fp) {
            //return ARGO_RESULT_ERR;
            /* Disk space exhaustion is not a system fail condition..soldier on */
            __sync_fetch_and_add_4(&(logfile->collection->counters.misc.create_logfile_fail_fopen), 1);
        } else {
            logfile->current_written = 0;
            /* Try to unlink the base name, and symlink to the latest file. */
            /* don't care if these fail. */
            unlink(logfile->filename);
            symlink_nowarn(tmp_name, logfile->filename);

            if (strlen(logfile->short_filename)) {
                unlink(logfile->short_filename);
                symlink_nowarn(tmp_name, logfile->short_filename);
            }
        }
    }

    return ARGO_RESULT_NO_ERROR;
}

void argo_log_dump_registered_structs(char *str, size_t str_len)
{
    char *s = str;
    char *e = s + str_len;

    size_t size = 0;
    int i = 0;
    int num = 0;
    int total_count = 0;
    struct argo_log_collection *collection = NULL;
    struct argo_log_collection **collections = NULL;

    /* Grab the size */
    pthread_rwlock_rdlock(&(al_global.lock));
    size = al_global.collections_list_size;

    collections = ARGO_ALLOC(size * sizeof(struct argo_log_collection *));

    if (!collections) {
        pthread_rwlock_unlock(&(al_global.lock));
        return;
    }

    LIST_FOREACH(collection, &(al_global.collections_list), list) {
        collections[i++] = collection;
    }
    pthread_rwlock_unlock(&(al_global.lock));


    while(i--) {
        if (collections[i]->thread_fired) {
            struct argo_log_registered_structure *r_struct = NULL;

            /* Skip if empty */
            pthread_mutex_lock(&(collections[i]->collection_lock));
            if (LIST_EMPTY(&collections[i]->us_elements) && LIST_EMPTY(&collections[i]->s_elements) && LIST_EMPTY(&collections[i]->m_elements)) {
                    pthread_mutex_unlock(&(collections[i]->collection_lock));
                    continue;
            } else {
                s += sxprintf(s, e, "Found registered structures for collection: %s\n", argo_log_get_name(collections[i]));
            }

            num = 0;
            LIST_FOREACH(r_struct, &collections[i]->us_elements, list) {
                s += sxprintf(s, e, "[us element %d]: %s => %"PRId64"s\n", ++num, r_struct->name, r_struct->period_us/(1000*1000));
                total_count++;
            }
            num = 0;
            LIST_FOREACH(r_struct, &collections[i]->s_elements, list) {
                s += sxprintf(s, e, "[s element %d]: %s => %"PRId64"s\n", ++num, r_struct->name, r_struct->period_us/(1000*1000));
                total_count++;
            }
            num = 0;
            LIST_FOREACH(r_struct, &collections[i]->m_elements, list) {
                s += sxprintf(s, e, "[m element %d]: %s => %"PRId64"s\n", ++num, r_struct->name, r_struct->period_us/(1000*1000));
                total_count++;
            }
            pthread_mutex_unlock(&(collections[i]->collection_lock));
        }
    }

    if (total_count) {
        s += sxprintf(s, e, "Found total of %d registered stats structs across %zu collections\n", total_count, size);
    }

    ARGO_FREE(collections);
    return;
}

/*
 * This routine creates a logging file suitable for use with a file
 * logging callback (argo_log_file_callback)
 *
 * This routine will create a file named filename.date, where date is
 * the ISO order: year, then month, then day, then hour, then minute,
 * then second, then microsecond. For example, a logfile might be
 * named: filename.2013-04-11-19:53:21.432569UTC
 *
 * The time appended to the file is ALWAYS UTC, NEVER local time.
 */
struct argo_log_file *argo_log_file_create(struct argo_log_collection *collection,
                                           const char *filename,
                                           char *short_filename,
                                           size_t max_length,
                                           enum argo_serialize_mode mode)
{
    struct argo_log_file *logfile;
    char tmp_name[1000];

    logfile = (struct argo_log_file *) ARGO_ALLOC(sizeof(*logfile));
    if (!logfile) {
        __sync_fetch_and_add_4(&(collection->counters.misc.create_logfile_fail_malloc), 1);
        return NULL;
    }
    memset(logfile, 0, sizeof(*logfile));

    snprintf(logfile->filename, sizeof(logfile->filename), "%s", filename);
    snprintf(logfile->short_filename, sizeof(logfile->short_filename), "%s", short_filename ? short_filename : "");

    argo_gen_filename(filename, tmp_name, sizeof(tmp_name));

    logfile->fp = fopen(tmp_name, "w");
    if (!logfile->fp) {
        /* As weird as this sounds, we do not want to error out if fopen fails
         * It could only mean disk space exhaustion and not OOM
         * So, we persevere on....we will mark this object as read for the file
         * handle based reader
         */
        //goto fail_free;
        __sync_fetch_and_add_4(&(collection->counters.misc.create_logfile_fail_fopen), 1);
    } else {
        /* don't care if these fail. */
        unlink(filename);
        symlink_nowarn(tmp_name, filename);

        if (short_filename) {
            unlink(logfile->short_filename);
            symlink_nowarn(tmp_name, logfile->short_filename);
        }
    }

    logfile->collection = collection;
    logfile->mode = mode;
    logfile->max_length = max_length;
    logfile->current_written = 0;
    logfile->total_written = 0;

    logfile->write_argo = argo_alloc(argo_log_file_argo_write,
                                     NULL,
                                     NULL,
                                     logfile,
                                     (GB),
                                     mode,
                                     500,
                                     1,
                                     1);
    if (!logfile->write_argo) goto fail_free;

    pthread_mutex_lock(&(collection->collection_lock));
    LIST_INSERT_HEAD(&(collection->files), logfile, list);
    collection->file_count++;
    pthread_mutex_unlock(&(collection->collection_lock));

    return logfile;

 fail_free:
    if (logfile) {
        if (logfile->fp) fclose(logfile->fp);
        ARGO_FREE(logfile);
    }
    return NULL;
}


/*
 * time_out must have room for at least 30 chars.
 */
int argo_log_gen_time(int64_t epoch_us, char *time_out, size_t time_out_size, int sumo_form, int utc)
{
    struct tm ltm;
    int tz_hrs;
    int tz_min;
    time_t t;

    if (epoch_us < 4000000000) {
        /* Assume we got seconds... */
        epoch_us *= 1000000;
    }

    t = epoch_us / 1000000;

    if (utc) {
        gmtime_r(&t, &ltm);
    } else {
        localtime_r(&t, &ltm);
    }
    if (ltm.tm_gmtoff < 0) {
        tz_min = (0 - ltm.tm_gmtoff) / 60;
    } else {
        tz_min = (ltm.tm_gmtoff) / 60;
    }
    tz_hrs = tz_min / 60;
    tz_min = tz_min % 60;
    if (sumo_form) {
        snprintf(time_out, time_out_size, "%04d-%02d-%02d %02d:%02d:%02d,%03d %c%02d%02d",
                 ltm.tm_year + 1900,
                 ltm.tm_mon + 1,
                 ltm.tm_mday,
                 ltm.tm_hour,
                 ltm.tm_min,
                 ltm.tm_sec,
                 (int)((epoch_us % 1000000) / 1000),
                 ltm.tm_gmtoff < 0 ? '-' : '+',
                 tz_hrs,
                 tz_min);
    } else {
        if (utc) {
            snprintf(time_out, time_out_size, "%04d-%02d-%02dT%02d:%02d:%02d.%03dZ",
                     ltm.tm_year + 1900,
                     ltm.tm_mon + 1,
                     ltm.tm_mday,
                     ltm.tm_hour,
                     ltm.tm_min,
                     ltm.tm_sec,
                     (int)((epoch_us % 1000000) / 1000));
        } else {
            snprintf(time_out, time_out_size, "%04d-%02d-%02dT%02d:%02d:%02d.%06d%c%02d%02d",
                     ltm.tm_year + 1900,
                     ltm.tm_mon + 1,
                     ltm.tm_mday,
                     ltm.tm_hour,
                     ltm.tm_min,
                     ltm.tm_sec,
                     (int)((epoch_us % 1000000) / 1),
                     ltm.tm_gmtoff < 0 ? '-' : '+',
                     tz_hrs,
                     tz_min);
        }
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_log_gen_time_only(int64_t epoch_us, char *time_out, size_t time_out_size) {
    struct tm ltm;
    time_t t;

    if (epoch_us < 4000000000) {
        /* Assume we got seconds... */
        epoch_us *= 1000000;
    }

    t = epoch_us / 1000000;

    localtime_r(&t, &ltm);

    snprintf(time_out, time_out_size, "T%02d:%02d:%02d.%03d", ltm.tm_hour, ltm.tm_min, ltm.tm_sec,
             (int)((epoch_us % 1000000) / 10000));

    return ARGO_RESULT_NO_ERROR;
}
/*
 * When logging to a file, we do a little hack, here- we add a date
 * field if it wasn't already filled in. (But only for text file logs)
 */
int argo_log_file_callback(struct argo_object *log_object,
                           void *callback_cookie,
                           int64_t argo_log_sequence)
{
    int res;
    struct argo_log_file *af = (struct argo_log_file *) callback_cookie;
    struct argo_log *log = (struct argo_log *)log_object->base_structure_data;
    char date[ARGO_LOG_GEN_TIME_STR_LEN];
    int did_date = 0;

    if ((!log->l_date) && (af->mode != argo_serialize_binary)) {
        argo_log_gen_time(log->l_us, date, sizeof(date), 1, 0);
        log->l_date = date;
        did_date = 1;
    }

    res = argo_serialize_object(af->write_argo,
                                log_object,
                                argo_serialize_dont_care);
    if (res) {
        //fprintf(stderr, "argo_serialize_object returned %s (%d)\n", argo_result_string(res), res);
    }
    if (did_date) log->l_date = NULL;

    return res;
}


size_t argo_log_get_freespace(struct argo_log_collection *collection)
{
    uint64_t free_size;

    pthread_mutex_lock(&(collection->collection_lock));
    pthread_rwlock_rdlock(&(al_global.lock));
    free_size = collection->max_mem -  collection->curr_mem + al_global.max_mem - al_global.curr_mem;
    pthread_rwlock_unlock(&(al_global.lock));
    pthread_mutex_unlock(&(collection->collection_lock));

    return free_size;
}

int argo_log_collection_has_reader(struct argo_log_collection *collection, char *reader_name)
{
    struct argo_log_reader *walk, *tmp;

    if (!collection || !reader_name) {
        return 0;
    }

    LIST_FOREACH_SAFE(walk, &(collection->readers), list, tmp) {
        if (!walk->deleted && !strncmp(walk->name, reader_name, strlen(walk->name))) {
                return 1;
        }
    }

    return 0;
}

int argo_log_set_interval_by_log_name(struct argo_log_collection *collection,
                                      const char                 *name,
                                      int                        name_len,
                                      int64_t                    interval_us)
{
    int found = 0;

    struct argo_log_registered_structure *r_struct;

    if (!collection) return ARGO_RESULT_NO_ERROR;

    pthread_mutex_lock(&(collection->collection_lock));

    LIST_FOREACH(r_struct, &(collection->us_elements), list) {
        if (0 == strncmp(name, r_struct->name, name_len)) {
            found = 1;
            LIST_REMOVE(r_struct, list);
            break;
        }
    }
    if (found) goto done;
    LIST_FOREACH(r_struct, &(collection->s_elements), list) {
        if (0 == strncmp(name, r_struct->name, name_len)) {
            found = 1;
            LIST_REMOVE(r_struct, list);
            break;
        }
    }
    if (found) goto done;
    LIST_FOREACH(r_struct, &(collection->m_elements), list) {
        if (0 == strncmp(name, r_struct->name, name_len)) {
            found = 1;
            LIST_REMOVE(r_struct, list);
            break;
        }
    }
    if (found) goto done;

    pthread_mutex_unlock(&(collection->collection_lock));
    return ARGO_RESULT_NOT_FOUND;

done:
    r_struct->period_us = interval_us;
    int64_t current_us = epoch_us_accuracy_us();
    r_struct->next_us_to_fire = current_us - (current_us % r_struct->period_us) + r_struct->period_us;
    if (interval_us < ARGO_LOG_SHORT_TIMEOUT_US) {
        LIST_INSERT_HEAD(&(collection->us_elements), r_struct, list);
    } else if (interval_us < ARGO_LOG_MEDIUM_TIMEOUT_US) {
        LIST_INSERT_HEAD(&(collection->s_elements), r_struct, list);
    } else {
        LIST_INSERT_HEAD(&(collection->m_elements), r_struct, list);
    }
    pthread_mutex_unlock(&(collection->collection_lock));
    return ARGO_RESULT_NO_ERROR;
}
