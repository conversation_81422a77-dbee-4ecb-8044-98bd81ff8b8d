/*
 * argo.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * See argo.h, argo_private.h.
 */

#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <pthread.h>
#include <ctype.h>
#include "base64/base64.h"
#include "argo/argo.h"
#include "argo/argo_private.h"
#include "argo/argo_private_compiled.h"
#include "argo/argo_buf.h"
#include "argo/argo_encode.h"
#include "argo/argo_structure.h"
#include "argo/argo_dictionary.h"
#include "argo/argo_list.h"
#include "argo/argo_log.h"
#include "zpath_misc/zpath_misc.h"
#include "zpath_misc/zpath_version.h"
#include "zhash/zhash_table.h"


#include <stdio.h>

// argo counts 2 per object, so 100 new fields max
#define LIMIT_ARGO_DYNAMIC_FIELDS_MAX_SIZE ( 800 )


/*
 * Global argo state. Yay.
 */

struct zpath_allocator argo_allocator = ZPATH_ALLOCATOR_INIT("argo");
struct argo_global_state argo_global;
struct argo_log_collection *argo_log_internal = NULL;
static int global_argo_initialized = 0;
int64_t global_argo_adj_time_us = 0;
static int argo_structure_registration_barrier_set = 0;
static argo_structure_registration_barrier_f* argo_structure_registration_barrier_cb = NULL;

uint64_t argo_debug =
    (!ARGO_DEBUG_AGREP_BIT) |
    (!ARGO_DEBUG_SERIALIZE_BIT) |
    (!ARGO_DEBUG_DESERIALIZE_BIT) |
    (!ARGO_DEBUG_TOKEN_BIT) |
    0;

uint64_t argo_debug_catch_defaults =
    (ARGO_DEBUG_AGREP_BIT) |
    (ARGO_DEBUG_SERIALIZE_BIT) |
    (ARGO_DEBUG_DESERIALIZE_BIT) |
    (ARGO_DEBUG_TOKEN_BIT) |
    0;

const char *argo_debug_names[] = ARGO_DEBUG_NAMES;

int64_t *argo_watch = NULL;
int64_t argo_value = 0;

static const char *argo_results[] = {
    [ARGO_RESULT_NO_ERROR] = "ARGO_RESULT_NO_ERROR",
    [ARGO_RESULT_ERR] = "ARGO_RESULT_ERR",
    [ARGO_RESULT_NOT_FOUND] = "ARGO_RESULT_NOT_FOUND",
    [ARGO_RESULT_NO_MEMORY] = "ARGO_RESULT_NO_MEMORY",
    [ARGO_RESULT_CANT_WRITE] = "ARGO_RESULT_CANT_WRITE",
    [ARGO_RESULT_ERR_TOO_LARGE] = "ARGO_RESULT_ERR_TOO_LARGE",
    [ARGO_RESULT_BAD_ARGUMENT] = "ARGO_RESULT_BAD_ARGUMENT",
    [ARGO_RESULT_INSUFFICIENT_DATA] = "ARGO_RESULT_INSUFFICIENT_DATA",
    [ARGO_RESULT_NOT_IMPLEMENTED] = "ARGO_RESULT_NOT_IMPLEMENTED",
    [ARGO_RESULT_BAD_DATA] = "ARGO_RESULT_BAD_DATA",
    [ARGO_RESULT_REPARSE] = "ARGO_RESULT_REPARSE",
    [ARGO_RESULT_WOULD_BLOCK] = "ARGO_RESULT_WOULD_BLOCK",
    [ARGO_RESULT_BAD_STATE] = "ARGO_RESULT_BAD_STATE",
    [ARGO_RESULT_INCOMPLETE] = "ARGO_RESULT_INCOMPLETE",
    [ARGO_RESULT_ASYNCHRONOUS] = "ARGO_RESULT_ASYNCHRONOUS",
    [ARGO_RESULT_EXCESS_DYN_FIELDS] = "ARGO_RESULT_EXCESS_DYN_FIELDS",
};

#if 0
static const char *argo_types_str[] = {
    [argo_field_data_type_invalid] = "argo_field_data_type_invalid",
    [argo_field_data_type_integer] = "argo_field_data_type_integer",
    [argo_field_data_type_string] = "argo_field_data_type_string",
    [argo_field_data_type_binary] = "argo_field_data_type_binary",
    [argo_field_data_type_object] = "argo_field_data_type_object"
};
#endif /* 0 */

int argo_deserialize_object_binary(struct argo_state *argo,
                                   uint8_t **heap,
                                   uint8_t *heap_end,
                                   struct argo_structure_description *description,
                                   uint8_t *structure_start);
static int argo_deserialize_json_internal(struct argo_buf **parse_buf, struct argo_object **new_object, char *object_type);
static struct argo_object *argo_object_create_internal(struct argo_structure_description *description,
                                                       void *structure,
                                                       int excess_fields,
                                                       int needs_locking);



int argo_serialize_structure_json_inner(struct argo_structure_description *description,
                                        const char *struct_name,
                                        void *struct_data,
                                        struct argo_buf **buf,
                                        int need_leading_comma,
                                        int depth,
                                        size_t max_fields,
                                        enum argo_serialize_mode mode,
                                        int trim_trailing_newline,
                                        int encode_type,
                                        int limit_string_length);

int argo_serialize_structure_json_dynamic_memory(struct argo_structure_description *description,
                                                 const char *struct_name,
                                                 void *struct_data,
                                                 struct argo_buf **buf,
                                                 int need_leading_comma,
                                                 int depth,
                                                 size_t max_fields,
                                                 enum argo_serialize_mode mode,
                                                 int trim_trailing_newline,
                                                 int encode_type,
                                                 int limit_string_length);

const char *argo_result_string(int argo_result)
{
    if ((argo_result < 0) || (argo_result >= sizeof(argo_results) / sizeof(const char*))) {
        return "Bad Argo Result";
    }
    if (!argo_results[argo_result]) return "INVALID_RESULT";
    return argo_results[argo_result];
}

static int argo_json_string_generate_internal(char **buf, int len, const char *str, int have_quotes)
{
    char *walk = *buf;
    char *end = *buf + len;

    if (walk + 2 >= end) {
        //fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    if (have_quotes) {
        *walk = '"';
        walk++;
    }
    if (str) {
        while ((walk < end) && (*str)) {
            switch(*str) {
            case '"':
            case '\\':
            case '\b':
            case '\f':
            case '\n':
            case '\r':
            case '\t':
                if (have_quotes) {
                    if (walk + 3 >= end) goto too_large;
                } else {
                    if (walk + 2 >= end) goto too_large;
                }
                *walk = '\\';
                walk++;
                switch(*str) {
                case '"':
                    *walk = '"';
                    break;
                case '\\':
                    *walk = '\\';
                    break;
                case '/':
                    *walk = '/';
                    break;
                case '\b':
                    *walk = 'b';
                    break;
                case '\f':
                    *walk = 'f';
                    break;
                case '\n':
                    *walk = 'n';
                    break;
                case '\r':
                    *walk = 'r';
                    break;
                case '\t':
                    *walk = 't';
                    break;
                }
                walk++;
                break;
            default:
                {
                    unsigned char out = *str;
                    if (out < 32) {
                        if (have_quotes) {
                            if ((walk + 7) >= end) goto too_large;
                        } else {
                            if ((walk + 6) >= end) goto too_large;
                        }
                        walk += sxprintf(walk, end, "\\u%04x", out);
                    } else {
                        *walk = *str;
                        walk++;
                    }
                }
                break;
            }
            str++;
        }
    }
    if ((walk == end) || (have_quotes && ((walk + 1) == end))) {
        //fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
    too_large:
        if (have_quotes) {
            while (walk + 1 >= end) walk--;
            *walk = '"';
            walk++;
        } else {
            while (walk >= end) walk--;
        }
        *walk = 0;
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    if (have_quotes) {
        *walk = '"';
        walk++;
    }

    *walk = 0;

    *buf += walk - *buf;

    return ARGO_RESULT_NO_ERROR;
}

/*
 * NOTE: This routine does NOT null-terminate!!!
 */
int argo_json_string_generate_argo_buf(struct argo_buf **buf, const char *str, int have_quotes)
{
    if (have_quotes) {
        argo_buf_append_byte(buf, (uint8_t)'"');
    }
    if (str) {
        while (*str) {
            switch(*str) {
            case '"':
            case '\\':
            case '\b':
            case '\f':
            case '\n':
            case '\r':
            case '\t':
                argo_buf_append_byte(buf, (uint8_t)'\\');
                switch(*str) {
                case '"':
                    argo_buf_append_byte(buf, (uint8_t)'"');
                    break;
                case '\\':
                    argo_buf_append_byte(buf, (uint8_t)'\\');
                    break;
                case '/':
                    argo_buf_append_byte(buf, (uint8_t)'/');
                    break;
                case '\b':
                    argo_buf_append_byte(buf, (uint8_t)'b');
                    break;
                case '\f':
                    argo_buf_append_byte(buf, (uint8_t)'f');
                    break;
                case '\n':
                    argo_buf_append_byte(buf, (uint8_t)'n');
                    break;
                case '\r':
                    argo_buf_append_byte(buf, (uint8_t)'r');
                    break;
                case '\t':
                    argo_buf_append_byte(buf, (uint8_t)'t');
                    break;
                }
                break;
            default:
                {
                    unsigned char out = *str;
                    if (out < 32) {
                        char tmp_str[10];
                        snprintf(tmp_str, sizeof(tmp_str), "\\u%04x", out);
                        argo_buf_append(buf, tmp_str, strlen(tmp_str));
                    } else {
                        argo_buf_append_byte(buf, (uint8_t) *str);
                    }
                }
                break;
            }
            str++;
        }
    }
    if (have_quotes) {
        argo_buf_append_byte(buf, (uint8_t)'"');
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_json_string_generate(char **buf, int len, const char *str)
{
    return argo_json_string_generate_internal(buf, len, str, 1);
}
int argo_json_string_generate_no_quotes(char **buf, int len, const char *str)
{
    return argo_json_string_generate_internal(buf, len, str, 0);
}

int argo_postgres_string_generate(char **buf, int len, const char *str)
{
    char *walk = *buf;
    char *end = *buf + len;

    if (walk + 2 >= end) {
        // fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    *walk = '"';
    walk++;
    if (str) {
        while ((walk < end) && (*str)) {
            switch(*str) {
            case '"':
            case '\\':
                //case '/':
                //case '\b':
                //case '\f':
                //case '\n':
                //case '\r':
                //case '\t':
                *walk = '\\';
                walk++;
                if (walk == end) {
                    // fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
                    return ARGO_RESULT_ERR_TOO_LARGE;
                }
                *walk = *str;
                walk++;
                break;
            default:
                *walk = *str;
                walk++;
                break;
            }
            str++;
        }
    }
    if (walk == end) {
        // fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    *walk = '"';
    walk++;

    if (walk == end) {
        // fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    *walk = 0;

    *buf += walk - *buf;

    return ARGO_RESULT_NO_ERROR;
}

int argo_json_binary_generate_argo_buf(struct argo_buf **buf, const void *raw_bin, size_t bin_len)
{
#define STACK_BINARY_STRING_SIZE 200
    const uint8_t *bin;
    char *alloc_buf = NULL;
    char static_buf[STACK_BINARY_STRING_SIZE + 3];
    char *tmp_buf;
    INLINE_VOID_INFERENCE(bin, raw_bin);

    size_t out_size = base64_encoded_size(bin_len);
    if (out_size < STACK_BINARY_STRING_SIZE) {
        tmp_buf = static_buf;
    } else {
        alloc_buf = tmp_buf = ARGO_ALLOC(out_size + 3);
    }

    tmp_buf[0] = '"';
    if (out_size) base64_encode_binary(tmp_buf + 1, bin, bin_len);
    tmp_buf[out_size + 1] = '"';
    tmp_buf[out_size + 2] = 0;

    argo_buf_append(buf, tmp_buf, out_size + 2);

    if (alloc_buf) ARGO_FREE(alloc_buf);
    return ARGO_RESULT_NO_ERROR;
}

/*
 * Keep heap aligned on 8-byte boundary.
 */
static void heap_increment_and_align(uint8_t **heap, size_t size)
{
    uintptr_t new_heap = (uintptr_t) *heap;
    new_heap += size;
    if (new_heap & 0x7) {
        new_heap |= 0x7;
        new_heap++;
    }
    (*heap) = (uint8_t *)new_heap;
}

/*
 * argo callback that occurs when we read a block description. We
 * define ourselves? Haha, not as impressive as it sounds.
 */
static int argo_block_description_callback_func(void *argo_cookie_ptr,
                                                void *argo_struct_cookie_ptr,
                                                struct argo_object *object) {
    struct argo_state *argo = (struct argo_state *) argo_struct_cookie_ptr;
    struct argo_description *description = (struct argo_description *) object->base_structure_data;

    (void) argo_cookie_ptr;

    argo->block_description = *description;
    argo->block_byte_count_next_block_description =
        argo->block_byte_count + argo->block_description.block_size;

    //_ARGO_LOG(AL_NOTICE, "Block Description: Received argo major version %d", (int)description->major_version);

    /* We do not allow- at all- for our argo versions to mismatch. */
    if (description->major_version == 3) {
        //fprintf(stderr, "%p: description callback with version 3", argo);
        argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;
    } else if (description->major_version == 2) {
        //fprintf(stderr, "%p: description callback with version 2", argo);
        argo->argo_encoding_version = ARGO_ENCODING_VERSION_2;
    } else {
        /* Default to version 3 */
        //fprintf(stderr, "%p: description callback with no version? assuming 3", argo);
        argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;
        if (argo->strict_version_check) {
            //fprintf(stderr, "Error: Incompatible argo version, ours = %d, theirs = %d\n", ARGO_VERSION_MAJOR, (int)description->major_version);
            _ARGO_LOG(AL_ERROR, "Incompatible argo version, ours = %d, theirs = %d", (int)ARGO_VERSION_MAJOR, (int)description->major_version);
            return ARGO_RESULT_ERR;
        } else {
            _ARGO_LOG(AL_NOTICE, "Incompatible argo version, ours = %d, theirs = %d", (int)ARGO_VERSION_MAJOR, (int)description->major_version);
        }
    }

    //_ARGO_LOG(AL_DEBUG, "Received connection, remote argo major/minor = %d/%d", (int) description->major_version, (int) description->minor_version);

    if (argo->version_callback) {
        (argo->version_callback)(argo->callback_cookie, description->major_version, description->minor_version);
    }

    //fprintf(stderr, "Read block description\n");
    return ARGO_RESULT_NO_ERROR;
}

/*
 * See header.
 */
int argo_library_init(size_t max_descriptions)
{
    if (!global_argo_initialized) {
        argo_global.all_descriptions_hash = zhash_table_alloc(&argo_allocator);
        if (!argo_global.all_descriptions_hash) return ARGO_RESULT_NO_MEMORY;

        argo_global.all_descriptions = (struct argo_structure_description **)
            ARGO_ALLOC(sizeof (struct argo_structure_description *) * max_descriptions);
        if (!argo_global.all_descriptions) return ARGO_RESULT_NO_MEMORY;

        argo_global.all_description_count = 0;
        argo_global.all_description_max = max_descriptions;

        argo_global.rwlock = ZPATH_RWLOCK_INIT;
        argo_global.argo_states_lock = ZPATH_MUTEX_INIT;
        global_argo_initialized = 1;
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_library_reinit(size_t max_descriptions)
{
    // clean up memory, if the argo already initialized
    if (global_argo_initialized) {
        zhash_table_free(argo_global.all_descriptions_hash);  // Free hash table

        // Free the array of descriptions and its elements
        for (size_t i = 0; i < argo_global.all_description_count; i++) {
            struct argo_structure_description *d = argo_global.all_descriptions[i];
            // Free each description
            if (d != NULL) {
                // Free each private_field_description if allocated
                if (d->description) {
                    for (size_t j = 0; j < d->description_count; j++) {
                        if (d->description[j]) {
                            // Free the dynamic field name
                            if (j >= d->static_description_count) {
                                ARGO_FREE((char *)d->description[j]->public_description.field_name);
                            }
                            // Then free the argo_private_field_description
                            ARGO_FREE(d->description[j]);
                        }
                    }
                    ARGO_FREE(d->description);
                }
                ARGO_FREE(d->sub_object_indexes);
                ARGO_FREE(d->type);

                if (d->described_fields) {
                    zhash_table_free(d->described_fields);
                }

                ARGO_FREE(d);
            }
        }
        ARGO_FREE(argo_global.all_descriptions);  // Free the main array

        ZPATH_RWLOCK_DESTROY(&argo_global.rwlock);
        ZPATH_MUTEX_DESTROY(&argo_global.argo_states_lock, __FILE__, __LINE__);

        // Reset the global initialized flag
        global_argo_initialized = 0;
    }

    memset(&argo_global, 0, sizeof(argo_global));
    return argo_library_init(max_descriptions);
}

size_t argo_library_get_max_descriptions(void)
{
    return argo_global.all_description_max;
}

int argo_library_log(const char *log_name)
{
    argo_log_internal = argo_log_get(log_name);
    return ARGO_RESULT_NO_ERROR;
}

/*
 * Add a new field to an existing structure description.
 *
 * This routine must grab a write lock in order to keep other pieces
 * of the system from horking.
 *
 * The only style of data that argo normally supports that cannot be
 * added dynamically is an array of strings.
 *
 * Two fields are actually added- a count field and the specifically
 * requested field itself.
 */
int argo_global_structure_add_field(struct argo_structure_description *description,
                                    char *name,
                                    size_t name_length,
                                    enum argo_field_data_type data_type,
                                    int is_array,
                                    int is_double,
                                    int is_inet)
{
    int ix;
    int len;
    int res;
    char *sss;

    /* Adding a field requires a global write lock */
    ZPATH_RWLOCK_WRLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    /* It's worth verifying this field doesn't already exist- it's
     * pretty easy to get race conditions when promoting a read lock
     * to a write lock. */
    if (zhash_table_lookup(description->described_fields, name, name_length, NULL)) {
        /* It's already there. Let it be. */
        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        return ARGO_RESULT_NO_ERROR;
    }

    ix = description->description_count;
    /* For now, increase structure size by one at a time. This might
     * need to be more efficient in the future. (i.e. a 20K field
     * sructure would probably take a while) */
    struct argo_private_field_description **new_desc = ARGO_ALLOC(sizeof(struct argo_private_field_description *) * (ix + 2));
    struct argo_private_field_description **old_desc = description->description;
    if (old_desc) {
        memcpy(new_desc, old_desc, sizeof(struct argo_private_field_description *) * (ix));
    }
    memset(&new_desc[ix], 0, sizeof(struct argo_private_field_description *) * 2);
    description->description = new_desc;
    ARGO_FREE_SLOW(old_desc);  // Make sure other threads have valid fd pointers for some time.

    /* [ix]     = Actual field.
     * [ix + 1] = Dynamic count field. */

    description->description[ix] = (struct argo_private_field_description *)
        ARGO_ALLOC(sizeof(struct argo_private_field_description));
    memset(description->description[ix], 0, sizeof(*(description->description[ix])));

    description->description[ix + 1] = (struct argo_private_field_description *)
        ARGO_ALLOC(sizeof(struct argo_private_field_description));
    memset(description->description[ix + 1], 0, sizeof(*(description->description[ix])));

    /*
     * Fill in argo_field_description with "best guess"...
     */
    description->description[ix]->public_description.is_array = is_array;
    description->description[ix]->public_description.is_double = is_double;
    description->description[ix]->public_description.is_inet = is_inet;
    description->description[ix]->public_description.argo_field_type = data_type;
    description->description[ix]->public_description.argo_new_field_type = data_type;
    description->description[ix + 1]->public_description.argo_field_type = argo_field_data_type_integer;
    description->description[ix + 1]->public_description.argo_new_field_type = argo_field_data_type_integer;
    description->description[ix + 1]->public_description.references_other = 1;
    description->description[ix + 1]->public_description.is_runtime_defined = 1;
    description->description[ix]->public_description.write_value = 1;
    description->description[ix]->public_description.have_dynamic_count_index = 1;
    description->description[ix]->public_description.is_runtime_defined = 1;
    description->description[ix]->public_description.dynamic_count_index = ix + 1;
    switch(data_type) {
    case argo_field_data_type_integer:
        description->description[ix]->public_description.argo_caching_hint = argo_field_caching_tiny;
        description->description[ix]->public_description.argo_encoding_hint = argo_field_encoding_differential;
        description->description[ix]->public_description.size = sizeof(int64_t);
        break;
    case argo_field_data_type_string:
    case argo_field_data_type_binary:
    case argo_field_data_type_double:
    case argo_field_data_type_inet:
        description->description[ix]->public_description.argo_caching_hint = argo_field_caching_tiny;
        description->description[ix]->public_description.argo_encoding_hint = argo_field_encoding_cached;
        if (is_double) {
            description->description[ix]->public_description.size = sizeof(double);
            description->description[ix]->public_description.argo_new_field_type = argo_field_data_type_double;
        } else if (is_inet) {
            description->description[ix]->public_description.size = sizeof(struct argo_inet);
            description->description[ix]->public_description.argo_new_field_type = argo_field_data_type_inet;
        } else {
            description->description[ix]->public_description.size = sizeof(char);
        }
        break;
    case argo_field_data_type_argo_object:
        description->description[ix]->public_description.argo_caching_hint = argo_field_caching_tiny;
        description->description[ix]->public_description.argo_encoding_hint = argo_field_encoding_cached;
        description->description[ix]->public_description.size = 0;
        break;
    case argo_field_data_type_object:
    default:
        ARGO_FREE(description->description[ix]);
        description->description[ix] = NULL;
        fprintf(stdout, "Err: Bad argument, file %s, line %d, struct = %s, field = %s\n",
                __FILE__, __LINE__, description->type, name);
        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    description->description[ix + 1]->public_description.size = sizeof(int64_t);

    /* Dynamic fields occur immediately after static fields. */
    description->description[ix]->public_description.offset =
        description->structure_size +
        (sizeof(struct argo_excess_field) * ((ix - description->static_description_count) / 2));
    description->description[ix + 1]->public_description.offset =
        description->description[ix]->public_description.offset +
        offsetof(struct argo_excess_field, count);
    description->description[ix]->public_description.is_reference = 1;
    if (is_array) {
        description->description[ix]->public_description.reference_count = 2;
    } else {
        description->description[ix]->public_description.reference_count = 1;
    }
    description->description[ix]->public_description.count = 1;
    description->description[ix + 1]->public_description.count = 1;

    INLINE_VOID_INFERENCE(sss, ARGO_ALLOC(name_length + 1));
    strcpy(sss, name);
    description->description[ix]->public_description.field_name = sss;
    description->description[ix]->public_description.field_name_length = name_length;

    len = name_length + 1 + strlen("_dynamic_count");
    INLINE_VOID_INFERENCE(sss, ARGO_ALLOC(len));
    snprintf(sss, len, "%s_dynamic_count", name);
    description->description[ix + 1]->public_description.field_name = sss;
    description->description[ix + 1]->public_description.field_name_length = len - 1;

    description->description[ix]->public_description.description = NULL;

    description->description[ix]->index = ix;
    description->description[ix + 1]->index = ix + 1;
    if ((res = zhash_table_store(description->described_fields,
                               description->description[ix]->public_description.field_name,
                               strlen(description->description[ix]->public_description.field_name),
                               0,
                               description->description[ix]))) {
        fprintf(stdout, "free field: %s\n", description->description[ix]->public_description.field_name);
        ARGO_FREE((char *)description->description[ix]->public_description.field_name);
        ARGO_FREE(description->description[ix]);
        description->description[ix] = NULL;
        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        return res;
    }

    /* Update count... */
    __sync_synchronize();  // Make sure description pointer is updated first.
    description->description_count += 2;
    description->field_count++;

    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    return ARGO_RESULT_NO_ERROR;
}

int argo_bulk_read(void *data_reference, uint8_t **out_data, size_t *out_data_len)
{
    struct argo_buf **buf = (struct argo_buf **)data_reference;

    return argo_buf_bulk_get(buf, out_data, out_data_len);
}

size_t argo_bulk_size(void *data_reference)
{
    struct argo_buf **buf = (struct argo_buf **)data_reference;

    return argo_buf_size(*buf);
}

/*
 * Allocate state for processing argo streams.
 */
struct argo_state *argo_alloc(argo_write_callback_f *write_callback,
                              argo_bulk_write_callback_f *bulk_write_callback,
                              argo_structure_callback_f *default_read_callback,
                              void *cookie_ptr,
                              uint64_t block_size,
                              enum argo_serialize_mode mode,
                              size_t max_types,
                              int strict_version_check,
                              int accept_binary)
{
    struct argo_state *argo;
    int res;

    argo = (struct argo_state *) ARGO_ALLOC(sizeof(*argo));
    if (argo) {
        memset(argo, 0, sizeof(*argo));

        if (mode == argo_serialize_dont_care) {
            argo->mode = argo_serialize_json_pretty;
        } else {
            argo->mode = mode;
        }

        argo->strict_version_check = strict_version_check;

        argo->block_description.major_version = ARGO_VERSION_MAJOR;
        argo->block_description.minor_version = ARGO_VERSION_MINOR;
        argo->block_description.block_size = block_size;

        argo->write_callback = write_callback;
        argo->bulk_callback = bulk_write_callback;
        argo->default_read_callback = default_read_callback;
        argo->callback_cookie = cookie_ptr;
        argo->tokenizing = 1;

        argo->argo_incarnation = 1;
        if (accept_binary) argo->accept_binary = 1;

        argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;

        argo->read_state = read_state_reading_block_description;
        argo->read_buf = argo_buf_create(0, NULL, NULL, ARGO_BUF_DEFAULT_SIZE);
        if (!argo->read_buf) {
            ARGO_FREE(argo);
            return NULL;
        }

        if (!(argo->registered_structures = zhash_table_alloc(&argo_allocator))) {
            argo_buf_free_all(argo->read_buf);
            ARGO_FREE(argo);
            return NULL;
        }

        argo->argo2_token_pool = argo2_token_pool_alloc(ARGO2_MAX_TOKENS);
        if (!argo->argo2_token_pool) {
            argo_buf_free_all(argo->read_buf);
            zhash_table_free(argo->registered_structures);
            ARGO_FREE(argo);
            return NULL;
        }

        argo->structures = (struct argo_structure_state **)
            ARGO_ALLOC(sizeof(struct argo_structure_state *) * argo_global.all_description_max);
        if (!argo->structures) {
            argo2_token_pool_free(argo->argo2_token_pool);
            argo_buf_free_all(argo->read_buf);
            zhash_table_free(argo->registered_structures);
            ARGO_FREE(argo);
            return NULL;
        }
        memset(argo->structures, 0, sizeof(struct argo_structure_state *) * argo_global.all_description_max);

        argo->argo_description_description =
            argo_register_global_structure(ARGO_DESCRIPTION_HELPER);
        if (!(argo->argo_description_description)) {
            argo2_token_pool_free(argo->argo2_token_pool);
            ARGO_FREE(argo->structures);
            argo_buf_free_all(argo->read_buf);
            zhash_table_free(argo->registered_structures);
            ARGO_FREE(argo);
            return NULL;
        }

        res = argo_register_structure(argo, argo->argo_description_description, argo_block_description_callback_func, argo);
        if (res) {
            argo2_token_pool_free(argo->argo2_token_pool);
            ARGO_FREE(argo->structures);
            argo_buf_free_all(argo->read_buf);
            zhash_table_free(argo->registered_structures);
            ARGO_FREE(argo);
            return NULL;
        }

        /* Grab global write lock to link ourselves into the global list of argos. */
        ZPATH_MUTEX_LOCK(&argo_global.argo_states_lock, __FILE__, __LINE__);
        LIST_INSERT_HEAD(&(argo_global.argos), argo, argos);
        ZPATH_MUTEX_UNLOCK(&argo_global.argo_states_lock, __FILE__, __LINE__);
    }
    return argo;
}

void argo_set_encoding(struct argo_state *argo, int version)
{
    argo->argo_encoding_version = version;
    argo->block_description.major_version = version;
}

int argo_get_encoding(struct argo_state *argo)
{
    return argo->argo_encoding_version;
}

void argo_set_version_callback(struct argo_state *argo, argo_encoding_version_callback_f *callback)
{
    argo->version_callback = callback;
}

static void argo_structure_state_free(struct argo_structure_state *s_state) {
    if (!s_state)
        return;

    ARGO_FREE(s_state->decode_index_to_description_index);

    if (s_state->field_values) {
        for (int i = 0; i < s_state->field_values_count; i++) {
            if (s_state->field_values[i].state == AF_MEMORY_STATE_ALLOCATED) {
                ARGO_FREE(s_state->field_values[i].raw_value);
            }
        }
        ARGO_FREE(s_state->field_values);
    }

    ARGO_FREE(s_state);
}

/*
 * Free argo processing state.
 */
void argo_free(struct argo_state *argo)
{
    size_t i;

    /* Grab global write lock to unlink ourselves from the global list
     * of argos before any of our state disappears. */
    ZPATH_MUTEX_LOCK(&argo_global.argo_states_lock, __FILE__, __LINE__);
    LIST_REMOVE(argo, argos);
    ZPATH_MUTEX_UNLOCK(&argo_global.argo_states_lock, __FILE__, __LINE__);

    zhash_table_free(argo->registered_structures);

    /*
     * Free all the compiled structures.
     */
    for (i = 0; i < argo_global.all_description_max; i++) {
        argo_structure_state_free(argo->structures[i]);
    }

    ARGO_FREE(argo->structures);

    argo2_token_pool_free(argo->argo2_token_pool);
    argo_buf_free_all(argo->read_buf);

    ARGO_FREE(argo);
}

/*
 * See header
 */
void argo_no_version_xmit(struct argo_state *argo)
{
    argo->skip_version_xmit = 1;
}

/*
 * See header for usage.
 *
 * Relatively simple allocation function.
 */
static struct argo_structure_description *
argo_register_global_structure_internal(const char *structure_type,
                                        int is_dictionary,
                                        size_t structure_size,
                                        const struct argo_field_description *description,
                                        size_t description_count)
{
    struct argo_structure_description *d;
    enum argo_structure_encoding_style encoding_style = argo_encoding_repeating;

    INLINE_VOID_INFERENCE(d, zhash_table_lookup(argo_global.all_descriptions_hash,
                                                structure_type,
                                                strlen(structure_type),
                                                NULL));
    if (d) return d;

    ZPATH_RWLOCK_WRLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    INLINE_VOID_INFERENCE(d, zhash_table_lookup(argo_global.all_descriptions_hash,
                                                structure_type,
                                                strlen(structure_type),
                                                NULL));
    if (!d) {
        if (argo_structure_registration_barrier_set) {
            _ARGO_LOG(AL_ERROR, "%s:%d: registration of argo structure [%s] occurred after barrier was set.",
                        __FILE__, __LINE__, structure_type);
            if (argo_structure_registration_barrier_cb) {
                (argo_structure_registration_barrier_cb)(structure_type,
                                    is_dictionary,
                                    structure_size,
                                    description,
                                    description_count);
            }
        }
        if (argo_global.all_description_count < argo_global.all_description_max) {
            d = (struct argo_structure_description *)ARGO_ALLOC(sizeof(*d));
            if (d) {
                size_t i;

                memset(d, 0, sizeof(*d));

                d->type_len = strlen(structure_type);
                INLINE_VOID_INFERENCE(d->type, ARGO_ALLOC(d->type_len + 1));
                if (!d->type) {
                    ARGO_FREE(d);
                    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                    return NULL;
                }
                strcpy(d->type, structure_type);

                d->global_index = argo_global.all_description_count;

                if (is_dictionary) {
                    d->is_dictionary = 1;
                } else {
                    d->structure_size = structure_size;
                    d->static_description = description;
                    d->static_description_count = description_count;
                    for (i = 0; i < description_count; i++) {
                        if (!description[i].references_other) {
                            d->static_field_count++;
                        }
                    }

                    d->field_count = d->static_field_count;
                    d->description_count = d->static_description_count;

                    if (d->description_count) {
                        d->description = (struct argo_private_field_description **)
                            ARGO_ALLOC(sizeof(struct argo_private_field_description *) * d->description_count);

                        if (!d->description) {
                            ARGO_FREE(d->type);
                            ARGO_FREE(d);
                            ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                            return NULL;
                        }
                        memset(d->description, 0, sizeof(struct argo_private_field_description *) * d->description_count);
                    }

                    d->described_fields = zhash_table_alloc(&(argo_allocator));
                    if (!d->described_fields) {
                        if (d->description) ARGO_FREE(d->description);
                        ARGO_FREE(d->type);
                        ARGO_FREE(d);
                        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                        return NULL;
                    }

                    d->reqid_index = -1;
                    d->deleted_index = -1;
                    d->sequence_index = -1;
                    d->key_index = -1;

                    for (i = 0; i < d->static_description_count; i++) {
                        if (description[i].is_reqid) d->reqid_index = i;
                        if (description[i].is_deleted) d->deleted_index = i;
                        if (description[i].is_sequence) d->sequence_index = i;
                        if (description[i].is_key) d->key_index = i;
                        if ((description[i].argo_field_type == argo_field_data_type_object) ||
                            (description[i].argo_field_type == argo_field_data_type_argo_object)) {
                            d->sub_object_count++;
                        }
                        d->description[i] = (struct argo_private_field_description *)
                            ARGO_ALLOC(sizeof(struct argo_private_field_description));
                        if (!(d->description[i])) {
                            while (i) {
                                i--;
                                ARGO_FREE(d->description[i]);
                            }
                            zhash_table_free(d->described_fields);
                            ARGO_FREE(d->description);
                            ARGO_FREE(d->type);
                            ARGO_FREE(d);
                            ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                            return NULL;
                        }
                        memset(d->description[i], 0, sizeof(*(d->description[i])));
                        memcpy(&(d->description[i]->public_description), &(description[i]), sizeof(description[i]));
                        d->description[i]->index = i;
                        if (zhash_table_store(d->described_fields,
                                            d->description[i]->public_description.field_name,
                                            strlen(d->description[i]->public_description.field_name),
                                            0,
                                            d->description[i])) {
                            while (i) {
                                i--;
                                ARGO_FREE(d->description[i]);
                            }
                            zhash_table_free(d->described_fields);
                            ARGO_FREE(d->description);
                            ARGO_FREE(d->type);
                            ARGO_FREE(d);
                            ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                            return NULL;
                        }
                        /* If we have a synonym for this field, make
                         * lookups for this name refer to the same
                         * field */
                        if (d->description[i]->public_description.synonym_count) {
                            for (size_t j = 0; j < d->description[i]->public_description.synonym_count && j < ARGO_MAX_SYNONYMS; j++) {
                                if (zhash_table_store(d->described_fields,
                                                      d->description[i]->public_description.synonym[j],
                                                      strlen(d->description[i]->public_description.synonym[j]),
                                                      0,
                                                      d->description[i])) {
                                    while (i) {
                                        i--;
                                        ARGO_FREE(d->description[i]);
                                    }
                                    zhash_table_free(d->described_fields);
                                    ARGO_FREE(d->description);
                                    ARGO_FREE(d->type);
                                    ARGO_FREE(d);
                                    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                                    return NULL;
                                }
                            }
                        }
                    }

                    if (d->sub_object_count) {
                        d->sub_object_indexes = (int *) ARGO_ALLOC(sizeof(int) * d->sub_object_count);
                        if (!d->sub_object_indexes) {
                            /* Ish. */
                            for (i = 0; i < d->static_description_count; i++) {
                                ARGO_FREE(d->description[i]);
                            }
                            zhash_table_free(d->described_fields);
                            if (d->description) ARGO_FREE(d->description);
                            ARGO_FREE(d->type);
                            ARGO_FREE(d);
                            ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                            return NULL;
                        }
                        d->sub_object_count = 0;
                        for (i = 0; i < d->static_description_count; i++) {
                            if ((description[i].argo_field_type == argo_field_data_type_object) ||
                                (description[i].argo_field_type == argo_field_data_type_argo_object)) {
                                d->sub_object_indexes[d->sub_object_count] = i;
                                d->sub_object_count++;
                            }
                        }
                    }
                }

                d->encoding_style = encoding_style;

                if (zhash_table_store(argo_global.all_descriptions_hash, d->type, d->type_len, 0, d)) {
                    if (d->sub_object_indexes) ARGO_FREE(d->sub_object_indexes);
                    /* Ish. */
                    for (i = 0; i < d->static_description_count; i++) {
                        ARGO_FREE(d->description[i]);
                    }
                    zhash_table_free(d->described_fields);
                    if (d->description) ARGO_FREE(d->description);
                    ARGO_FREE(d->type);
                    ARGO_FREE(d);
                    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                    return NULL;
                }

                argo_global.all_descriptions[d->global_index] = d;
                argo_global.all_description_count++;

            }
        } else {
            _ARGO_LOG(AL_ERROR, "Registration of argo structure description for [%s] failed: max descriptions %zu already reached",
                      structure_type, argo_global.all_description_max);
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    return d;
}

#ifndef MOCK_ARGO_REGISTER_GLOBAL_STRUCTURE
struct argo_structure_description *
argo_register_global_structure(const char *structure_type,
                               size_t structure_size,
                               const struct argo_field_description *description,
                               size_t description_count)
{
    return argo_register_global_structure_internal(structure_type,
                                                   0,
                                                   structure_size,
                                                   description,
                                                   description_count);
}
#endif //MOCK_ARGO_REGISTER_GLOBAL_STRUCTURE

int
argo_rename_global_structure(struct argo_structure_description *d,
                             const char *new_name)
{
    size_t len;
    char *alloc_new_name;

    ZPATH_RWLOCK_WRLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    len = strlen(new_name);
    INLINE_VOID_INFERENCE(alloc_new_name, ARGO_ALLOC(len + 1));
    if (!alloc_new_name) {
        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        return ARGO_RESULT_NO_MEMORY;
    }

    strcpy(alloc_new_name, new_name);

    ARGO_FREE(d->type);
    d->type_len = len;
    d->type = alloc_new_name;

    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    return ARGO_RESULT_NO_ERROR;
}

struct argo_structure_description *
argo_register_global_dictionary(const char *structure_type)
{
    return argo_register_global_structure_internal(structure_type,
                                                   1,
                                                   0,
                                                   NULL,
                                                   0);
}


/*
 * See header
 *
 * This is largely a simple allocation function.
 */
int argo_register_structure_nolock(struct argo_state *argo,
                                   struct argo_structure_description *description,
                                   argo_structure_callback_f *callback_f,
                                   void *callback_cookie)
{
    struct argo_structure_state *s;
    int description_count = 0;

    /* Add description to argo->registered_structures...*/
    s = argo->structures[description->global_index];
    if (!s) {
        s = (struct argo_structure_state*)ARGO_ALLOC(sizeof (*s));
        if (!s) goto fail_and_free;

        memset(s, 0, sizeof(*s));

        s->master = description;
        description_count = s->master->description_count;
        s->callback_f = callback_f;
        s->callback_cookie = callback_cookie;

        s->decode_index_to_description_index = (int *) ARGO_ALLOC(sizeof(int) * description_count);
        if (!s->decode_index_to_description_index) goto fail_and_free;
        memset(s->decode_index_to_description_index, 0, sizeof(int) * description_count);

        if (zhash_table_store(argo->registered_structures, s->master->type, s->master->type_len, 0, s)) {
            goto fail_and_free;
        }
        argo->structures[description->global_index] = s;
    }
    /* Release global write lock. */
    return ARGO_RESULT_NO_ERROR;

 fail_and_free:
    if (s) {
        if (s->decode_index_to_description_index) ARGO_FREE(s->decode_index_to_description_index);
        ARGO_FREE(s);
    }
    /* Release global write lock. */
    return ARGO_RESULT_NO_MEMORY;
}

/* unit test used, not tested in prod */
int argo_update_registration(struct argo_state *argo,
                             struct argo_structure_description *description,
                             argo_structure_callback_f *callback_f,
                             void *callback_cookie) {
    struct argo_structure_state *s;

    /* Add description to argo->registered_structures...*/
    s = argo->structures[description->global_index];
    if (!s) {
        // first time
        return argo_register_structure_nolock(argo, description, callback_f, callback_cookie);
    } else {
        // change the callback function
        // how do we lock, so both callback_f and callback_cookie are atomic?
        s->callback_f = callback_f;
        s->callback_cookie = callback_cookie;
    }
    /* Release global write lock. */
    return ARGO_RESULT_NO_ERROR;
}

int argo_register_structure(struct argo_state *argo,
                            struct argo_structure_description *description,
                            argo_structure_callback_f *callback_f,
                            void *callback_cookie)
{
    int res;
    /* Grab global write lock to register. */
    //ZPATH_RWLOCK_WRLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    res = argo_register_structure_nolock(argo, description, callback_f, callback_cookie);
    //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    return res;
}

struct argo_structure_description * argo_get_structure_description(const char *structure_type)
{
    struct argo_structure_description *ret;

    /* Grab global write lock during lookup... */
    ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    INLINE_VOID_INFERENCE(ret, zhash_table_lookup(argo_global.all_descriptions_hash, structure_type, strlen(structure_type), NULL));

    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    return ret;
}

struct argo_structure_description * argo_get_object_description(struct argo_object *object)
{
    /* No lock really needed, as we never free these arrays even when reallocated. */
    return argo_global.all_descriptions[object->base_structure_index];
}

char *argo_description_get_type(struct argo_structure_description *desc)
{
    return desc->type;
}


/*
 * Writes the given data using the the argo write callback
 * function. This routine does correct counter maintenance.
 */
int argo_write(struct argo_state *argo, uint8_t *data, size_t size)
{
    int res;

    res = (*argo->write_callback)(argo->callback_cookie,
                                  data,
                                  size);
    if (res) return res;
    argo->block_byte_count += size;

    return ARGO_RESULT_NO_ERROR;
}

/*
 * Writes out padding bytes at least somewhat efficiently.
 */
int argo_pad(struct argo_state *argo, size_t size)
{
    static uint8_t padding[65536];
    static int initialized = 0;
    int res;

    if (argo->write_callback) {
        if (!initialized) {
            memset(padding, '\n', sizeof(padding));
            initialized = 1;
        }
        while (size > 65536) {
            res = argo_write(argo, padding, 65536);
            if (res) return res;
            size -= 65536;
        }
        if (size) {
            res = argo_write(argo, padding, size);
            if (res) return res;
        }
    }
    return ARGO_RESULT_NO_ERROR;
}


/*
 * Write a block header. Yes, this should be static. No, you don't
 * want to call it directly, unless you're sure you will not overwrite
 * a block boundary.
 */
static int argo_write_block_header(struct argo_state *argo)
{
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf;
    int ret;

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    ret = argo_serialize_structure_json(argo->argo_description_description,
                                        "argo_description",
                                        &(argo->block_description),
                                        &(out_buf),
                                        0,
                                        0,
                                        argo->argo_description_description->static_description_count,
                                        argo_serialize_json_pretty,
                                        0,
                                        1,
                                        0);
    if (ret) return ret;

    ret = argo_buf_write(argo, out_buf);

    return ret;
}



/*
 * Routine to reset compression state. Should probably only be called
 * internally...
 */
int argo_state_reset_compression(struct argo_state *argo)
{
    uint64_t block_remaining = argo->block_byte_count_next_block_description - argo->block_byte_count;
    int res = ARGO_RESULT_NO_ERROR;

    if (!(argo->skip_version_xmit)) {
        /* If we are within ARGO_MAX_BLOCK_HEADER_SIZE of the end of a
         * block, we will pad out to end of block. */
        if (block_remaining < ARGO_MAX_BLOCK_HEADER_SIZE) {
            res = argo_pad(argo, block_remaining);
            if (res) return res;
        }

        res = argo_write_block_header(argo);
    }
    argo->block_byte_count_next_block_description += argo->block_description.block_size;

    argo->argo_incarnation++;

    argo2_token_pool_reset(argo->argo2_token_pool);

    return res;
}



/***************************************************
 *
 * Serialization routines follow.
 *
 */


/*
 * Internal version of argo_serialize. Just takes an argument for how
 * many fields to encode.
 */
int argo_serialize_internal(struct argo_state *argo,
                            struct argo_structure_description *description,
                            void *data,
                            enum argo_serialize_mode mode,
                            size_t max_fields)
{
    /* One on the stack. Rest allocated as needed. */
    struct argo_buf output_buffer[2];
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf = &(output_buffer[0]);
    uint64_t *stack = (uint64_t *)&(output_buffer[1]);

    uint8_t encoded_len[10];
    uint8_t *encoded_walk;

    int res;
    unsigned int size;
    uint64_t block_remaining;

    /* Simple stack checking... */
    *stack = 0xfeedfacebeef00dll;
    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }

    /* Grab global read lock while serializing. */
    //ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    if (mode == argo_serialize_dont_care) mode = argo->mode;

    /* If we are at the beginning of a block, we need to reset
     * compression state and output a block header. Resetting
     * compression state automatically writes a block header. */
    if (argo->block_description.block_size) {
        if (argo->block_byte_count == argo->block_byte_count_next_block_description) {
            if ((res = argo_state_reset_compression(argo))) {
                /* Note: Far side can happily tell us we haven't sent it enough data. That's pretty normal */
                if (res != ARGO_RESULT_INSUFFICIENT_DATA) {
                    //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
                    return res;
                }
            }
        }
    }

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &(output_buffer[0]), buffer_data, sizeof(buffer_data));

    /* Serialize structure: */
    if (mode == argo_serialize_binary) {
        if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_ANY) {
            //fprintf(stderr, "%p: Assuming tx version is 2 as we received binary data without json version header\n", argo);
            argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;
        }
        if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_2) {
            //fprintf(stderr, "%p: Tx version 2\n", argo);
            abort();
        } else if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_3) {
            //fprintf(stderr, "%p: Tx version 3\n", argo);
            res = argo2_binary_serialize_structure(argo,
                                                   description,
                                                   data,
                                                   &out_buf,
                                                   max_fields - description->static_description_count);
        } else {
            fprintf(stdout, "Bad serialization version\n");
            abort();
        }
    } else {
        res = argo_serialize_structure_json(description, description->type, data, &out_buf, 1, 0, max_fields, mode, 0, 1, 0);
        if (res == ARGO_RESULT_ERR_TOO_LARGE) {
            /* Lets try this again with a 2MB buffer. */
            argo_buf_free_all(output_buffer[0].next);
            out_buf = argo_buf_create(8, &(output_buffer[0]), buffer_data, sizeof(buffer_data));
            res = argo_serialize_structure_json_dynamic_memory(description, description->type, data, &out_buf, 1, 0, max_fields, mode, 0, 1, 0);
        }
    }
    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
    if (res) {
        argo_buf_free_all(output_buffer[0].next);
        //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
        return res;
    }

    /* Get the size we will write, not including the size itself */
    size = argo_buf_size(&(output_buffer[0]));
    if (mode == argo_serialize_binary) {
        /* For binary, we have a length prepended. */
        encoded_walk = encoded_len;
        *encoded_walk = ARGO_RECORD_CODE_BINARY;
        argo_encode_uint64(&encoded_walk, ARGO_RECORD_CODE_BINARY_VBIT, size);
        size += encoded_walk - encoded_len;
    }

    /* If we have a non-zero block size, see if we need to pad out and
     * write a new block description. */
    if (argo->block_description.block_size) {
        /* Get the remaining space available on this block. */
        block_remaining = argo->block_byte_count_next_block_description - argo->block_byte_count;

        /* If there is not enough room, we need to pad out to block_size,
         * and reserialize. */
        if (block_remaining < size) {
            /* Pad to block size: */
            argo_pad(argo, block_remaining);

            /* Reset compression state. Writes a new block header
             * automatically. */
            if ((res = argo_state_reset_compression(argo))) {
                /* Note: Far side can happily tell us we haven't sent it enough data. That's pretty normal */
                if (res != ARGO_RESULT_INSUFFICIENT_DATA) {
                    //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
                    return res;
                }
            }

            /* Compression state is reset, so reinitialize our buffer,
             * too. */
            argo_buf_free_all(output_buffer[0].next);
            out_buf = argo_buf_create(8, &(output_buffer[0]), buffer_data, sizeof(buffer_data));

            /* Serialize structure again. Size will change. */
            if (mode == argo_serialize_binary) {
                if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_2) {
                    abort();
                } else if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_3) {
                    res = argo2_binary_serialize_structure(argo,
                                                           description,
                                                           data,
                                                           &out_buf,
                                                           max_fields - description->static_description_count);
                } else {
                    fprintf(stdout, "Bad serialization version\n");
                    abort();
                }
            } else {
                res = argo_serialize_structure_json(description, description->type, data, &out_buf, 1, 0, max_fields, mode, 0, 1, 0);
                if (res == ARGO_RESULT_ERR_TOO_LARGE) {
                    /* Lets try this again with a 2MB buffer. */
                    argo_buf_free_all(output_buffer[0].next);
                    out_buf = argo_buf_create(8, &(output_buffer[0]), buffer_data, sizeof(buffer_data));
                    res = argo_serialize_structure_json_dynamic_memory(description, description->type, data, &out_buf, 1, 0, max_fields, mode, 0, 1, 0);
                }
            }
            if (res) {
                if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
                argo_buf_free_all(output_buffer[0].next);
                //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
                return res;
            }

            /* Get the size we will write. */
            size = argo_buf_size(&(output_buffer[0]));

            if (mode == argo_serialize_binary) {
                /* Binary has length prepended */
                encoded_walk = encoded_len;
                *encoded_walk = ARGO_RECORD_CODE_BINARY;
                argo_encode_uint64(&encoded_walk, ARGO_RECORD_CODE_BINARY_VBIT, size);
                size += encoded_walk - encoded_len;
            }

            block_remaining = argo->block_byte_count_next_block_description - argo->block_byte_count;
        }

        if (block_remaining < size) {
            /* The data being written is larger than our block
             * size. That's not so good. */
            //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
            fprintf(stderr, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
            if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
            return ARGO_RESULT_ERR_TOO_LARGE;
        }
    }

    /* Finally, write our length and serialized data. */
    if (mode == argo_serialize_binary) {
        res = argo_buf_prepend(&(output_buffer[0]), encoded_len, encoded_walk - encoded_len);
        if (res) {
            //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
            if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
            return res;
        }
    }


    /* We don't hold lock while actually sending serialized data. */
    //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
    res = argo_buf_write(argo, &(output_buffer[0]));

    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
    /* Free up buffer memory. */
    argo_buf_free_all(output_buffer[0].next);
    if (*stack != 0xfeedfacebeef00dll) { fprintf(stdout, "Stack check failed... Halting..."); abort(); }
    return res;
}



/*
 * See header
 *
 * Note: If struct_name is NULL, then this routine assumes we do not
 * need to write out our field name
 */
int argo_serialize_structure_json(struct argo_structure_description *description,
                                  const char *struct_name,
                                  void *struct_data,
                                  struct argo_buf **buf,
                                  int need_leading_comma,
                                  int depth,
                                  size_t max_fields,
                                  enum argo_serialize_mode mode,
                                  int trim_trailing_newline,
                                  int encode_type,
                                  int limit_string_length)
{
    return argo_serialize_structure_json_inner(description,
                                               struct_name,
                                               struct_data,
                                               buf,
                                               need_leading_comma,
                                               depth,
                                               max_fields,
                                               mode,
                                               trim_trailing_newline,
                                               encode_type,
                                               limit_string_length);

}

int argo_serialize_structure_json_dynamic_memory(struct argo_structure_description *description,
                                                 const char *struct_name,
                                                 void *struct_data,
                                                 struct argo_buf **buf,
                                                 int need_leading_comma,
                                                 int depth,
                                                 size_t max_fields,
                                                 enum argo_serialize_mode mode,
                                                 int trim_trailing_newline,
                                                 int encode_type,
                                                 int limit_string_length)
{
    int ret =  argo_serialize_structure_json_inner(description,
                                                   struct_name,
                                                   struct_data,
                                                   buf,
                                                   need_leading_comma,
                                                   depth,
                                                   max_fields,
                                                   mode,
                                                   trim_trailing_newline,
                                                   encode_type,
                                                   limit_string_length);

    return ret;
}


static void argo_serialize_field_prefix(struct argo_buf **buf, enum argo_serialize_mode mode, int depth, char *field_name, int *wrote_first, int *have_output, int do_array)
{
    if (*have_output) return;
    *have_output = 1;
    if (mode == argo_serialize_json_pretty) {
        if (*wrote_first) {
            argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, "");
            argo_buf_append_str(buf, field_name);
            argo_buf_append_char(buf, ':');
            // len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:", (depth + 1) * 2, "", buf1);
        } else {
            argo_buf_append_printf(buf, "\n%*s", (depth + 1) * 2, "");
            argo_buf_append_str(buf, field_name);
            argo_buf_append_char(buf, ':');
            //len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:", (depth + 1) * 2, "", buf1);
            *wrote_first = 1;
        }
    } else {
        if (*wrote_first) {
            argo_buf_append_char(buf, ',');
            argo_buf_append_str(buf, field_name);
            argo_buf_append_char(buf, ':');
            //len = sxprintf(buf3, buf3 + bufsize, ",%s:", buf1);
        } else {
            argo_buf_append_str(buf, field_name);
            argo_buf_append_char(buf, ':');
            //len = sxprintf(buf3, buf3 + bufsize, "%s:", buf1);
            *wrote_first = 1;
        }
    }
    if (do_array) {
        argo_buf_append_char(buf, '[');
    }
    return;
}

int argo_serialize_structure_json_inner(struct argo_structure_description *description,
                                        const char *struct_name,
                                        void *struct_data,
                                        struct argo_buf **buf,
                                        int need_leading_comma,
                                        int depth,
                                        size_t max_fields,
                                        enum argo_serialize_mode mode,
                                        int trim_trailing_newline,
                                        int encode_type,
                                        int limit_string_length)

{
    //char *bp;
    int ret;
    int wrote_first = 0;
    size_t i;
    int array_count, array_index;

    /*
     * Generate header:  "structure name": { \n
     */

    if (struct_name) {
        /* if ((ret = argo_json_string_generate(&bp, bufsize, struct_name))) { */
        /*     return ret; */
        /* } */
        if (mode == argo_serialize_json_pretty) {
            if (need_leading_comma) {
                if (encode_type) {
                    argo_buf_append_str(buf, ",\n{");
                    argo_json_string_generate_argo_buf(buf, struct_name, 1);
                    argo_buf_append_str(buf, ":{");
                    //len = sxprintf(buf2, buf2 + bufsize, ",\n{%s:{", buf1);
                } else {
                    argo_buf_append_str(buf, ",\n{");
                    //len = sxprintf(buf2, buf2 + bufsize, ",\n{");
                }
            } else {
                if (encode_type) {
                    argo_buf_append_char(buf, '{');
                    argo_json_string_generate_argo_buf(buf, struct_name, 1);
                    argo_buf_append_str(buf, ":{");
                    //len = sxprintf(buf2, buf2 + bufsize, "{%s:{", buf1);
                } else {
                    argo_buf_append_char(buf, '{');
                    //len = sxprintf(buf2, buf2 + bufsize, "{");
                }
            }
        } else {
            if (need_leading_comma) {
                if (encode_type) {
                    argo_buf_append_str(buf, ",{");
                    argo_json_string_generate_argo_buf(buf, struct_name, 1);
                    argo_buf_append_str(buf, ":{");
                    //len = sxprintf(buf2, buf2 + bufsize, ",{%s:{", buf1);
                } else {
                    argo_buf_append_str(buf, ",{");
                    //len = sxprintf(buf2, buf2 + bufsize, ",{");
                }
            } else {
                if (encode_type) {
                    argo_buf_append_char(buf, '{');
                    argo_json_string_generate_argo_buf(buf, struct_name, 1);
                    argo_buf_append_str(buf, ":{");
                    //len = sxprintf(buf2, buf2 + bufsize, "{%s:{", buf1);
                } else {
                    argo_buf_append_char(buf, '{');
                    //len = sxprintf(buf2, buf2 + bufsize, "{");
                }
            }
        }
    } else {
        if (need_leading_comma) {
            if (mode == argo_serialize_json_pretty) {
                ret = argo_buf_append_printf(buf, ",\n%*s{", depth * 2, "");
                //len = sxprintf(buf2, buf2 + bufsize, ",\n%*s{", depth * 2, "");
                //ret = argo_buf_append(buf, buf2, len);
            } else {
                ret = argo_buf_append_str(buf, ",{");
                //ret = argo_buf_append(buf, ",{", 2);
            }
        } else {
            ret = argo_buf_append_char(buf, '{');
            //ret = argo_buf_append(buf, "{", 1);
        }
    }

    /*
     * Walk through the structure description, writing fields.
     */
    for (i = 0; i < max_fields; i++) {
        struct argo_field_description *fd = &(description->description[i]->public_description);
        struct argo_structure_description *sub_desc = NULL;
        int res;
        /* char *s = buf2; */
        /* char *e = s + bufsize; */
        void *data_location;
        size_t data_size;
        int have_output = 0;
        char tmp_field_name[ARGO_MAX_NAME_LENGTH];
        char *tmp_field_name_ptr = tmp_field_name;

        //argo_buf_append_printf(buf, "\nevaluate field %d:\n", i);

        if (!fd->write_value) continue;

        /*
         * Generate stringified name- If it's an object, then include it's type...
         */
        if (fd->argo_field_type == argo_field_data_type_argo_object) {
            struct argo_object *obj;

            res = argo_structure_read_field_by_description(description, fd, struct_data, 0, &data_location, &data_size, &sub_desc);
            if (res) return res;

            obj = (struct argo_object *)data_location;
            if (obj) {
                sub_desc = argo_global.all_descriptions[obj->base_structure_index];

                argo_json_string_generate(&tmp_field_name_ptr, sizeof(tmp_field_name), fd->field_name);

                /* snprintf(tmp_field_name, sizeof(tmp_field_name), "%s", fd->field_name); */
                /* bp = buf1; */
                /* if ((ret = argo_json_string_generate(&bp, bufsize, buf))) { */
                /*     return ret; */
                /* } */
            } else {
                argo_json_string_generate(&tmp_field_name_ptr, sizeof(tmp_field_name), fd->field_name);
                /* bp = buf1; */
                /* if ((ret = argo_json_string_generate(&bp, bufsize, fd->field_name))) { */
                /*     return ret; */
                /* } */
            }
        } else {
            argo_json_string_generate(&tmp_field_name_ptr, sizeof(tmp_field_name), fd->field_name);
            /* bp = buf1; */
            /* if ((ret = argo_json_string_generate(&bp, bufsize, fd->field_name))) { */
            /*     return ret; */
            /* } */
        }

        /* array_count = 0; */
        /* if ((fd->argo_field_type != argo_field_data_type_dictionary && */
        /*      fd->argo_field_type != argo_field_data_type_list)) { */
        /*     array_count = argo_structure_field_get_array_count_by_index(description, i, struct_data); */
        /*     //argo_buf_append_printf(buf, "<testing field %d, see array_count=%d>", i, array_count); */
        /*     if (array_count) { */
        /*         if (mode == argo_serialize_json_pretty) { */
        /*             if (wrote_first) { */
        /*                 argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, ""); */
        /*                 argo_buf_append_str(buf, tmp_field_name); */
        /*                 argo_buf_append_char(buf, ':'); */
        /*                 // len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:", (depth + 1) * 2, "", buf1); */
        /*             } else { */
        /*                 argo_buf_append_printf(buf, "\n%*s", (depth + 1) * 2, ""); */
        /*                 argo_buf_append_str(buf, tmp_field_name); */
        /*                 argo_buf_append_char(buf, ':'); */
        /*                 //len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:", (depth + 1) * 2, "", buf1); */
        /*                 wrote_first = 1; */
        /*             } */
        /*         } else { */
        /*             if (wrote_first) { */
        /*                 argo_buf_append_char(buf, ','); */
        /*                 argo_buf_append_str(buf, tmp_field_name); */
        /*                 argo_buf_append_char(buf, ':'); */
        /*                 //len = sxprintf(buf3, buf3 + bufsize, ",%s:", buf1); */
        /*             } else { */
        /*                 argo_buf_append_str(buf, tmp_field_name); */
        /*                 argo_buf_append_char(buf, ':'); */
        /*                 //len = sxprintf(buf3, buf3 + bufsize, "%s:", buf1); */
        /*                 wrote_first = 1; */
        /*             } */
        /*         } */
        /*     } */
        /* } */

        if (fd->argo_field_type == argo_field_data_type_dictionary) {
            struct argo_dictionary *dict;
            res = argo_structure_read_field_by_description(description, fd, struct_data, 0, &data_location, &data_size, &sub_desc);
            if (res) return res;

            dict = (struct argo_dictionary *)data_location;
            if (!dict) continue;

            if (!argo_dictionary_key_count(dict)) continue;

            argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, 0);
            /* if (mode == argo_serialize_json_pretty) { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         // len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*     } else { */
            /*         argo_buf_append_printf(buf, "\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*         wrote_first = 1; */
            /*     } */
            /* } else { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_char(buf, ','); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, ",%s:", buf1); */
            /*     } else { */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "%s:", buf1); */
            /*         wrote_first = 1; */
            /*     } */
            /* } */
            wrote_first = 1;
            res = argo_dictionary_serialize_json(dict, buf, depth + 1, mode);
            if (res) return res;
        } else if (fd->argo_field_type == argo_field_data_type_list) {
            struct argo_list *list;
            res = argo_structure_read_field_by_description(description, fd, struct_data, 0, &data_location, &data_size, &sub_desc);
            if (res) return res;

            list = (struct argo_list *)data_location;
            if (!list) continue;

            struct argo_object *obj = argo_list_get_head(list);
            if (!obj) continue;

            argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, 0);
            /* if (mode == argo_serialize_json_pretty) { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*     } else { */
            /*         argo_buf_append_printf(buf, "\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*         wrote_first = 1; */
            /*     } */
            /* } else { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_char(buf, ','); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, ",%s:", buf1); */
            /*     } else { */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "%s:", buf1); */
            /*         wrote_first = 1; */
            /*     } */
            /* } */
            wrote_first = 1;
            res = argo_list_serialize_json(list, buf, depth + 1, mode);
            if (res) return res;

        } else if (fd->argo_field_type == argo_field_data_type_argo_object) {
            struct argo_object *obj;
            int wrote_one_from_array = 0;

            array_count = argo_structure_field_get_array_count_by_index(description, i, struct_data);

            if (!array_count) continue;

            argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, 0);
            /* if (mode == argo_serialize_json_pretty) { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*     } else { */
            /*         argo_buf_append_printf(buf, "\n%*s", (depth + 1) * 2, ""); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:", (depth + 1) * 2, "", buf1); */
            /*     } */
            /* } else { */
            /*     if (wrote_first) { */
            /*         argo_buf_append_char(buf, ','); */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, ",%s:", buf1); */
            /*     } else { */
            /*         argo_buf_append_str(buf, tmp_field_name); */
            /*         argo_buf_append_char(buf, ':'); */
            /*         //len = sxprintf(buf3, buf3 + bufsize, "%s:", buf1); */
            /*     } */
            /* } */

            if (array_count > 1) depth++;
            for (array_index = 0; array_index < array_count; array_index++) {
                res = argo_structure_read_field_by_description(description, fd, struct_data, array_index, &data_location, &data_size, &sub_desc);
                if (res) return res;


                if (array_index == 0) {
                    /* if ((ret = argo_buf_append(buf, buf3, len))) { */
                    /*     return ret; */
                    /* } */
                    if (fd->is_array) {
                        if (mode == argo_serialize_json_pretty) {
                            ret = argo_buf_append_printf(buf, "[\n%*s", (depth + 1) * 2, "");
                            /* len = sxprintf(buf3, buf3 + bufsize, "[\n%*s", (depth + 1) * 2, ""); */
                            /* ret = argo_buf_append(buf, buf3, len); */
                        } else {
                            ret = argo_buf_append_char(buf, '[');
                            // ret = argo_buf_append(buf, "[", 1);
                        }
                        if (ret) return ret;
                    }
                    have_output = 1;
                }

                obj = (struct argo_object *)data_location;
                if (!obj) {
                    if (fd->is_array) {
                        if (mode == argo_serialize_json_pretty) {
                            if (wrote_one_from_array) {
                                argo_buf_append_printf(buf, ",\n%*snull", (depth + 1) * 2, "");
                                //len = sxprintf(buf3, buf3 + bufsize, ",\n%*snull", (depth + 1) * 2, "");
                            } else {
                                argo_buf_append_str(buf, "null");
                                //len = sxprintf(buf3, buf3 + bufsize, "null");
                            }
                        } else {
                            if (wrote_one_from_array) {
                                argo_buf_append_str(buf, ",null");
                                //len = sxprintf(buf3, buf3 + bufsize, ",null");
                            } else {
                                argo_buf_append_str(buf, "null");
                                //len = sxprintf(buf3, buf3 + bufsize, "null");
                            }
                        }
                        /* ret = argo_buf_append(buf, buf3, len); */
                        /* if (ret) return ret; */
                        have_output = 1;
                        wrote_one_from_array = 1;
                    }
                    continue;
                }

                have_output = 1;

                if (wrote_one_from_array) {
                    if (mode == argo_serialize_json_pretty) {
                        ret = argo_buf_append_printf(buf, ",\n%*s", (depth + 1) * 2, "");
                        /* len = sxprintf(buf3, buf3 + bufsize, ",\n%*s", (depth + 1) * 2, ""); */
                        /* ret = argo_buf_append(buf, buf3, len); */
                    } else {
                        ret = argo_buf_append_char(buf, ',');
                        //ret = argo_buf_append(buf, ",", 1);
                    }
                } else {
                    if (mode == argo_serialize_json_pretty) {
                        //ret = argo_buf_append_printf(buf, "%*s", (depth + 1) * 2, "");
                        /* len = sxprintf(buf3, buf3 + bufsize, "%*s", (depth + 1) * 2, ""); */
                        /* ret = argo_buf_append(buf, buf3, len); */
                    }
                }
                wrote_one_from_array = 1;

                sub_desc = argo_global.all_descriptions[obj->base_structure_index];

                res = argo_serialize_structure_json_dynamic_memory(sub_desc,
                                                                   sub_desc->type, //NULL,
                                                                   obj->base_structure_void,
                                                                   buf,
                                                                   0,
                                                                   depth + 1,
                                                                   sub_desc->static_description_count + obj->excess_description_count,
                                                                   mode,
                                                                   1,
                                                                   (fd->json_old || !encode_type) ? 0 : 1,
                                                                   0);
                if (res) return res;
            }

            if (fd->is_array && have_output) {
                if (mode == argo_serialize_json_pretty) {
                    ret = argo_buf_append_printf(buf,  "\n%*s]", depth*2, "");
                    /* char tmp_str[100]; */
                    /* len = sxprintf(tmp_str, tmp_str + sizeof(tmp_str), "\n%*s]", depth*2, ""); */
                    /* ret = argo_buf_append(buf, tmp_str, len); */
                } else {
                    ret = argo_buf_append_char(buf, ']');
                    //ret = argo_buf_append(buf, "]", 1);
                }
                if (ret) return ret;
            }

            if (array_count > 1) depth--;

            wrote_first = 1;

        } else {

            /* Get number of elements we will be writing out... */
            array_count = argo_structure_field_get_array_count_by_index(description, i, struct_data);

            if (fd->is_array && !array_count) continue;

            /* if (fd->is_array && array_count) { */
            /*     argo_buf_append_char(buf, '['); */
            /*     //s += sxprintf(s, e, "["); */
            /* } */
            /*
             * Generate stringified value.
             */
            for (array_index = 0; array_index < array_count; array_index++) {
                int64_t int_val;

                res = argo2_structure_read_field_by_description(description, fd, struct_data, array_index, &data_location, &data_size);
                if (res) return res;

                if (array_index) {
                    argo_buf_append_str(buf, ", ");
                    //s += sxprintf(s, e, ", ");
                }

                switch (fd->argo_field_type) {
                case argo_field_data_type_string:
                    if (!data_location) {
                        if (array_count > 1) {
                            if (array_index == 0) {
                                argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                            }
                            argo_buf_append_str(buf, "\"\"");;
                            //s += sxprintf(s, e, "\"\"");
                            have_output = 1;
                        } else {
                            continue;
                        }
                    } else {
                        /* Prevent serialization of empty or NULL
                         * strings if "nozero" characteristic is
                         * set */
                        if (fd->is_nozero &&
                            (!fd->is_array)) {
                            if (!data_location) continue;
                            if (strlen(data_location) == 0) continue;
                        }

                        argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                        if (limit_string_length) {
                            char tmp_buf[limit_string_length + 1];
                            char *tmp_buf_ptr = tmp_buf;
                            argo_json_string_generate(&tmp_buf_ptr, sizeof(tmp_buf), (char *) data_location);
                            ret = argo_buf_append_str(buf, tmp_buf);
                            /* if ((e - s) > limit_string_length) { */
                            /*     ret = argo_json_string_generate(&s, limit_string_length, (char *) data_location); */
                            /*     ret = ARGO_RESULT_NO_ERROR; */
                            /* } else { */
                            /*     ret = argo_json_string_generate(&s, e - s, (char *) data_location); */
                            /* } */
                        } else {
                            ret = argo_json_string_generate_argo_buf(buf, (char *) data_location, 1);
                            //ret = argo_json_string_generate(&s, e - s, (char *) data_location);
                            if (ret) return ret;
                        }
                        have_output = 1;
                    }
                    break;
                case argo_field_data_type_integer:
                    if (!data_location) {
                        if (array_count > 1) {
                            if (fd->stringify) {
                                argo_buf_append_str(buf, "\"0\"");
                                //s += sxprintf(s, e, "\"0\"");
                            } else {
                                argo_buf_append_char(buf, '0');
                                //s += sxprintf(s, e, "0");
                            }
                            have_output = 1;
                        } else {
                            continue;
                        }
                    } else {
                        int_val = argo_read_int(data_location, data_size);
                        if (fd->is_nozero &&
                            ((!fd->is_array) &&
                             !int_val)) {
                            continue;
                        }
                        argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                        /* Do time adjustment. Make sure this looks
                         * like an epoch in seconds, milliseconds, or
                         * microseconds. No deltas allowed! */
                        if (int_val && global_argo_adj_time_us && fd->adj_time && (int_val > 1000l*1000l*1000l)) {
                            if (int_val < 1000l * 1000l * 1000l *10l) {
                                /* Seconds */
                                int_val += (global_argo_adj_time_us / 1000000l);
                            } else if (int_val < 1000l * 1000l * 1000l * 1000l *10l) {
                                /* Milliseconds */
                                int_val += (global_argo_adj_time_us / 1000l);
                            } else {
                                /* Microseconds */
                                int_val += global_argo_adj_time_us;
                            }
                        }
                        if (data_location) {
                            if (fd->stringify) {
                                argo_buf_append_printf(buf, "\"%ld\"", (long)int_val);
                                //s += sxprintf(s, e, "\"%ld\"", (long)int_val);
                            } else {
                                argo_buf_append_printf(buf, "%ld", (long)int_val);
                                //s += sxprintf(s, e, "%ld", (long)int_val);
                            }
                        } else {
                            if (fd->stringify) {
                                argo_buf_append_str(buf, "\"0\"");
                                //s += sxprintf(s, e, "\"0\"");
                            } else {
                                argo_buf_append_char(buf, '0');
                                //s += sxprintf(s, e, "0");
                            }
                        }
                        have_output = 1;
                    }
                    break;
                case argo_field_data_type_binary:
                case argo_field_data_type_double:
                case argo_field_data_type_inet:
                    if (fd->is_inet) {
                        //have_output = 1;
                        if (data_size && (data_size != sizeof(struct argo_inet))) {
                            fprintf(stdout, "Err: Bad inet size, file %s, line %d\n", __FILE__, __LINE__);
                            return ARGO_RESULT_BAD_DATA;
                        }
                        if (fd->is_nozero &&
                            (!fd->is_array) &&
                            ((!data_location) ||
                             (((struct argo_inet *)data_location)->length == 0))) {
                            have_output = 0;
                        } else {
                            argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                            if (!data_location) {
                                argo_buf_append_str(buf, "\"\"");
                                //s += sxprintf(s, e, "\"\"");
                            } else {
                                char tmp_str[100];
                                char *tmp_str_ptr = tmp_str;
                                argo_json_inet_generate(&tmp_str_ptr, sizeof(tmp_str), (struct argo_inet *)data_location, 1);
                                ret = argo_buf_append_str(buf, tmp_str);
                                //ret = argo_json_inet_generate(&s, e - s, (struct argo_inet *)data_location, 1);
                                if (ret) return ret;
                            }
                        }
                    } else if (fd->is_double) {
                        //have_output = 1;
                        if (data_location && (data_size != sizeof(double))) {
                            fprintf(stdout, "Err: Bad double size, file %s, line %d, data size = %ld\n", __FILE__, __LINE__, (long) data_size);
                            return ARGO_RESULT_BAD_DATA;
                        }
                        if (fd->is_nozero &&
                            (!fd->is_array) &&
                            ((!data_location) ||
                             (*((double *)data_location) == 0))) {
                            have_output = 0;
                            fprintf(stderr, "Have output = 0\n");
                        } else {
                            argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                            if (!data_location) {
                                /* We write 0 for non-existent doubles */
                                argo_buf_append_char(buf, '0');
                                //s += (sxprintf(s, e, "0"));
                            } else {
                                argo_buf_append_printf(buf, "%.9g", *((double *)data_location));
                                //s += (sxprintf(s, e, "%.9g", *((double *)data_location)));
                            }
                        }
                    } else {
                        argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                        ret = argo_json_binary_generate_argo_buf(buf, data_location, data_size);
                        //ret = argo_json_binary_generate_argo_buf(&s, e - s, data_location, data_size);
                        if (ret) return ret;
                    }
                    break;
                case argo_field_data_type_object:
                    if (!data_location) continue;
                    argo_serialize_field_prefix(buf, mode, depth, tmp_field_name, &wrote_first, &have_output, fd->is_array && array_count);
                    have_output = 1;
                    {
                        if (fd->is_array) {
                            _ARGO_LOG(AL_ERROR, "%s:%d: Not implemented", __FILE__, __LINE__);
                            return ARGO_RESULT_NOT_IMPLEMENTED;
                        }

                        if (!sub_desc) {
                            fprintf(stdout, "%s:%s:%d: Err: Do not have substructure desc for field <%s>\n", __FILE__, __FUNCTION__, __LINE__, fd->field_name);
                            return ARGO_RESULT_BAD_DATA;
                        }

                        /* XXXXXXXXX THIS IS WRONG- WE SHOULD ONLY
                         * OUTPUT STATIC DESCRIPTION COUNT HERE, AS WE
                         * DO NOT KNOW THE SOURCE OF THIS
                         * STRUCTURE. BUT WHEN READING SOME OLD-FORMAT
                         * FILES, THIS IS REQUIRED. ISH ISH ISH Most
                         * modern code uses argo_objects rather than
                         * substructures, and doesn't have this
                         * problem. */
                        res = argo_serialize_structure_json_dynamic_memory(sub_desc,
                                                            fd->field_name,
                                                            data_location,
                                                            buf,
                                                            wrote_first ? 1 : 0,
                                                            depth + 1,
                                                            // RIGHT: sub_desc->static_description_count,
                                                            sub_desc->description_count,
                                                            mode,
                                                            0,
                                                            encode_type ? 1 : 0,
                                                            0);

                        if (res) {
                            fprintf(stdout, "%s:%s:%d: Could not serialize substructure: %s\n", __FILE__, __FUNCTION__, __LINE__, argo_result_string(res));
                            return res;
                        }

                    }
                    break;
                default:
                    _ARGO_LOG(AL_ERROR, "%s:%d: Not implemented", __FILE__, __LINE__);
                    return ARGO_RESULT_NOT_IMPLEMENTED;
                }
            }

            if (fd->argo_field_type != argo_field_data_type_object) {
                if (fd->is_array && array_count && have_output) {
                    argo_buf_append_char(buf, ']');
                    //s += sxprintf(s, e, "]");
                }

                /* /\* buf1 now has name, buf2 now has value *\/ */
                /* if (have_output) { */
                /*     if (mode == argo_serialize_json_pretty) { */
                /*         if (wrote_first) { */
                /*             len = sxprintf(buf3, buf3 + bufsize, ",\n%*s%s:%s", (depth + 1) * 2, "", buf1, buf2); */
                /*         } else { */
                /*             len = sxprintf(buf3, buf3 + bufsize, "\n%*s%s:%s", (depth + 1) * 2, "", buf1, buf2); */
                /*             wrote_first = 1; */
                /*         } */
                /*     } else { */
                /*         if (wrote_first) { */
                /*             len = sxprintf(buf3, buf3 + bufsize, ",%s:%s", buf1, buf2); */
                /*         } else { */
                /*             len = sxprintf(buf3, buf3 + bufsize, "%s:%s", buf1, buf2); */
                /*             wrote_first = 1; */
                /*         } */
                /*     } */
                /*     if ((ret = argo_buf_append(buf, buf3, len))) { */
                /*         return ret; */
                /*     } */
                /* } */
            }
        }
    }

    /*
     * Generate trailer: Newline for last element, plus our trailing
     * '}'
     */
    if (mode == argo_serialize_json_pretty) {
        if (encode_type) {
            argo_buf_append_printf(buf, "\n%*s}}", depth * 2, "");
            //len = sxprintf(buf2, buf2 + bufsize, "\n%*s}}", depth * 2, "");
        } else {
            argo_buf_append_printf(buf, "\n%*s}", depth * 2, "");
            //len = sxprintf(buf2, buf2 + bufsize, "\n%*s}", depth * 2, "");
        }
    } else {
        if (encode_type) {
            if (trim_trailing_newline) {
                argo_buf_append_str(buf, "}}");
                //len = sxprintf(buf2, buf2 + bufsize, "}}");
            } else {
                argo_buf_append_str(buf, "}}\n");
                //len = sxprintf(buf2, buf2 + bufsize, "}}\n");
            }
        } else {
            argo_buf_append_char(buf, '}');
            //len = sxprintf(buf2, buf2 + bufsize, "}");
        }
    }
    /* if ((ret = argo_buf_append(buf, buf2, len))) { */
    /*     return ret; */
    /* } */

    return ARGO_RESULT_NO_ERROR;
}


void argo_dump_description(struct argo_state *argo,
                           struct argo_structure_description *description)
{
    fprintf(stderr, "Implement me\n");
}

void argo_dump_all_descriptions(struct argo_state *argo)
{
    size_t i;
    for (i = 0; i < argo_global.all_description_count; i++) {
        if (argo->structures[i]) {
            argo_dump_description(argo, argo_global.all_descriptions[i]);
        }
    }
}


struct argo_object *argo_heap_instantiate_object(struct argo_structure_description *description,
                                                 uint8_t **heap,
                                                 uint8_t *heap_end)
{
    struct argo_object *object;

    object = (struct argo_object *) *heap;
    heap_increment_and_align(heap, sizeof(struct argo_object));
    if (*heap > heap_end) {
        return NULL;
    }

    object->base_structure_data = *heap;
    object->excess_description_count = description->description_count - description->static_description_count;
    object->base_structure_index = description->global_index;

    /* Move heap past the end of the structure- that's where we'll put
     * dynamic stuff. */
    heap_increment_and_align(heap, description->structure_size);

    /* First dynamic stuff is excess fields. */
    object->excess_fields = (struct argo_excess_field *)*heap;
    heap_increment_and_align(heap, sizeof(struct argo_excess_field) * object->excess_description_count);
    if ((*heap) > heap_end) {
        return NULL;
    }

    /* Clear structure memory. */
    memset(object->base_structure_data, 0, description->structure_size);

    /* Clear excess field memory. */
    memset(object->excess_fields, 0, sizeof(struct argo_excess_field) * object->excess_description_count);

    return object;
}

int argo_deserialize_object_json(struct argo_buf **parse_buf,
                                 uint8_t **heap,
                                 uint8_t *heap_end,
                                 struct argo_structure_description *description,
                                 uint8_t *structure_start)
{
    struct argo_private_field_description *field_description;
    struct argo_field_description *fd;
    enum argo_field_data_type data_type;
    int array_max_index;
    int array_index;
    size_t array_count = 0;
 //   size_t current_field;
    size_t length;
    size_t str_length;
    uint64_t value;
    int need_terminator;
    int res;

    res = argo_buf_require_char(parse_buf, '{');
    if (res) {
        return res;
    }

   // current_field = 0;
    while (1) {
        char field_name[ARGO_MAX_NAME_LENGTH];
        int is_array_initiator = 0;
        int is_array_terminator = 0;
        int is_null_field = 0;

        /* Check for terminal... */
        res = argo_buf_require_char(parse_buf, '}');
        if (!res) break;
        if (res != ARGO_RESULT_NOT_FOUND) {
            return res;
        }

        /* Read a name... */
        length = sizeof(field_name);
        res = argo_buf_read_json_string(parse_buf, field_name, &length);
        if (res) {
            //argo_buf_dump_near(argo->parse_buf, 0, 64);
            return res;
        }
        /* Before we do a field lookup, validate for the corner case where the reported length of the
         * string actually is not matching the 'C' string length..if this is the case, we got some
         * nasty NULL byte(s) in the field name, and only bad things will follow..so abort parsing
         */
        str_length = strnlen(field_name, length + 1);
        if (length != str_length) {
            res = ARGO_RESULT_BAD_DATA;
            _ARGO_LOG(AL_ERROR, "Bad input detected: In RPC [%s] for field [%.32s], parse_len %zu, str_len %zu..aborting with error %s",
                                  description->type, field_name, length, str_length, argo_result_string(res));
            return res;
        }

        res = argo_buf_require_char(parse_buf, ':');
        if (res) {
            return res;
        }

        /* Check type.. Must be string or int, for the time being. */
        res = argo_buf_peek_json_type(parse_buf, &data_type, &is_array_initiator, &is_array_terminator, &is_null_field);
        if (res) {
            return res;
        }

        /* Look up field name in descriptions */
        field_description = (struct argo_private_field_description *)
            zhash_table_lookup(description->described_fields,
                             (const void *)field_name,
                             length,
                             NULL);
        if (!field_description) {
            /* It is okay to have fields we are not
             * expecting. This is a good thing to implement in the
             * future. Shouldnt be hard. Famous last words. */
            //fprintf(stderr, "Ooooh, new field (%s) file %s, line %d\n", field_name, __FILE__, __LINE__);

            /* Update argo to support this field */
            //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);


            int excess_description_count = description->description_count - description->static_description_count;

            if (excess_description_count >= LIMIT_ARGO_DYNAMIC_FIELDS_MAX_SIZE) {
                _ARGO_LOG(AL_ERROR, "RPC '%s' has too many dynamic fields, over %d [%s:%d]", description->type ?: "",
                          excess_description_count / 2,  __FILE__, __LINE__);
                return ARGO_RESULT_EXCESS_DYN_FIELDS; // it will disconnect the connection with FOHH_CLOSE_REASON_TLV_DESERIALIZE
            }

            /* Add the structure... */
            field_name[length] = 0;
            res = argo_global_structure_add_field(description, field_name, length, data_type, is_array_initiator, 0, 0);
            /* Re-lock, so we don't mess up locking state. */
            // ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
            if (res) {
                return res;
            } else {
                return ARGO_RESULT_REPARSE;
            }
        }

        fd = &(field_description->public_description);

        if (is_array_initiator) {
            res = argo_buf_require_char(parse_buf, '[');
            if (res) {
                return res;
            }

            res = argo_buf_peek_json_array_count(*parse_buf, &array_count);
            if (res) {
                return res;
            }

            need_terminator = 1;
            if (fd->count > 1) {
                if (array_count > fd->count) {
                    return ARGO_RESULT_ERR_TOO_LARGE;
                }
                array_max_index = array_count;
            } else {
                array_max_index = array_count;
            }
        } else {
            array_max_index = 1;
            need_terminator = 0;
        }

        for (array_index = 0; array_index < array_max_index; array_index++) {
            res = argo_buf_peek_json_type(parse_buf, &data_type, &is_array_initiator, &is_array_terminator, &is_null_field);
            if (res) {
                return res;
            }

            if (is_array_initiator) {
                /* multidimensional arrays not implemented. */
                _ARGO_LOG(AL_ERROR, "%s:%d: Not implemented", __FILE__, __LINE__);
                return ARGO_RESULT_NOT_IMPLEMENTED;
            }

            if (is_array_terminator) {
                /* Nothing (else) in the array */
                break;
            }

            /* For mismatched types... Reading inet_addr from a JSON string is allowed. */
            /* Also, reading an integer into a string is allowed. */
            if (data_type != fd->argo_field_type) {
                if ((data_type != argo_field_data_type_string) && (fd->argo_field_type != argo_field_data_type_binary)) {
                    if ((data_type != argo_field_data_type_integer) && (fd->argo_field_type != argo_field_data_type_string)) {
                        _ARGO_LOG(AL_ERROR, "Type mismatch, fd->field_name = %s, sname = %s, data_type = %d, file %s, line %d",
                                  fd->field_name,
                                  description->type,
                                  data_type,
                                  __FILE__, __LINE__);
                        return ARGO_RESULT_BAD_DATA;
                    }
                }
            }

            switch(fd->argo_field_type) {
            case argo_field_data_type_integer:
                /* Check type.. int only allowed. If it says it is an
                 * array, that probably means we are at the end of our
                 * array. Yeah, sort of overloaded. Sorry. */
                if ((data_type == argo_field_data_type_string) && (!is_null_field)) {
                    char int_str[100];
                    char *end;
                    res = argo_buf_peek_json_string_length(*parse_buf, &length);
                    if (res) {
                        return res;
                    }
                    if (length >= (sizeof(int_str) - 2)) {
                        return ARGO_RESULT_BAD_DATA;
                    }
                    length++;
                    res = argo_buf_read_json_string(parse_buf, int_str, &length);
                    if (res) return res;
                    value = strtoll(int_str, &end, 10);
                } else {
                    res = argo_buf_read_json_integer(parse_buf, &value);
                    if (res) {
                        if (res != ARGO_RESULT_INSUFFICIENT_DATA) {
                            argo_buf_dump_near(*parse_buf, 0, 64);
                            _ARGO_LOG(AL_ERROR, "Bad data read, array = %d, type = %s, field = %s, res=%s",
                                      (int)array_index,
                                      description->type,
                                      field_name, argo_result_string(res));
                        }
                        return res;
                    }
                }

                res = argo2_structure_write_field_by_description(fd,
                                                                 structure_start,
                                                                 array_index,
                                                                 array_max_index,
                                                                 &value,
                                                                 heap,
                                                                 heap_end);
                if (res) return res;

                break;

            case argo_field_data_type_string:

                /* Special: Read integer as string- we support this for some amount of backwards compatibility */
                {
                    char little[1000];
                    char *where = little;
                    size_t new_length;
                    if (data_type == argo_field_data_type_integer) {
                        res = argo_buf_read_json_integer(parse_buf, &value);
                        if (res) {
                            return res;
                        }
                        length = sxprintf(little, little + sizeof(little), "%ld", (long) value);
                    } else {
                        {
                            int cc;
                            char peekbuf[4];
                            /* Check for null json value */
                            cc = argo_buf_peek(*parse_buf);

                            if (cc < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
                            if (cc == 'n') {
                                /* assume null! */
                                res = argo_buf_peek_binary(*parse_buf, 0, peekbuf, 4);
                                if (res) return res;
                                if ((peekbuf[0] == 'n') &&
                                    (peekbuf[1] == 'u') &&
                                    (peekbuf[2] == 'l') &&
                                    (peekbuf[3] == 'l')) {
                                    /* No value for this field. NULL is default. */
                                    argo_buf_read_binary(parse_buf, peekbuf, 4);
                                    continue;
                                }
                            }
                        }
                        res = argo_buf_peek_json_string_length(*parse_buf, &length);
                        if (res) {
                            return res;
                        }

                        if (length < (sizeof(little) - 1)) {
                        } else {
                            INLINE_VOID_INFERENCE(where, ARGO_ALLOC(length + 1));
                            if (!where) return ARGO_RESULT_NO_MEMORY;
                        }

                        new_length = length + 1;
                        res = argo_buf_read_json_string(parse_buf, where, &new_length);
                        if (res) {
                            if (where != little) {
                                ARGO_FREE(where);
                            }
                            return res;
                        }
                        str_length = strnlen(where, new_length + 1);
                        if (new_length != str_length) {
                            res = ARGO_RESULT_BAD_DATA;
                            _ARGO_LOG(AL_ERROR, "Bad input detected: In RPC [%s] for field [%.32s], parse_len %zu, str_len %zu..aborting with error %s",
                                                  description->type, where ? where : "", new_length, str_length, argo_result_string(res));
                            return res;
                        }

                        length = new_length;
                    }

                    res = argo2_structure_write_field_by_description(fd,
                                                                     structure_start,
                                                                     array_index,
                                                                     array_max_index,
                                                                     length ? where : NULL,
                                                                     heap,
                                                                     heap_end);
                    if (where != little) {
                        ARGO_FREE(where);
                    }
                    if (res) return res;
                }
                break;

            case argo_field_data_type_binary:
                {
                    if (fd->is_inet) {
                        char inet_str[100];
                        struct argo_inet inet;
                        /* Special code to read inets. We use heap as scratch space */
                        length = sizeof(inet_str);
                        res = argo_buf_read_json_string(parse_buf, (char *)inet_str, &length);
                        if (res) return res;

                        /* Okay, we have a string. Now we need to convert it to binary representation. */
                        res = argo_string_to_inet(inet_str, &inet);
                        if (res) return res;
                        res = argo2_structure_write_field_by_description(fd,
                                                                         structure_start,
                                                                         array_index,
                                                                         array_max_index,
                                                                         &inet,
                                                                         heap,
                                                                         heap_end);
                        if (res) return res;
                    } else if (fd->is_double) {
                        double db;
                        res = argo_buf_read_json_double(parse_buf, &db);
                        if (res) return res;
                        res = argo2_structure_write_field_by_description(fd,
                                                                         structure_start,
                                                                         array_index,
                                                                         array_max_index,
                                                                         &db,
                                                                         heap,
                                                                         heap_end);
                    } else {
                        size_t b64_len;
                        size_t bin_len;
                        char *b64 = NULL;
                        char *bin = NULL;
                        res = argo_buf_peek_json_binary_length(*parse_buf, &b64_len, &bin_len);
                        if (b64_len) {
                            if (res) return res;
                            INLINE_VOID_INFERENCE(b64, ARGO_ALLOC(b64_len + bin_len));
                            if (!b64) return ARGO_RESULT_NO_MEMORY;
                            bin = b64 + b64_len;
                            //fprintf(stderr, "read binary json, expect len = %d\n", (int) length);
                            res = argo_buf_read_json_binary(parse_buf,
                                                            (uint8_t *)bin, &bin_len,
                                                            b64, b64_len);
                            if (res) {
                                ARGO_FREE(b64);
                                return res;
                            }
                            res = argo2_structure_write_field_by_description(fd,
                                                                             structure_start,
                                                                             array_index,
                                                                             array_max_index,
                                                                             bin,
                                                                             heap,
                                                                             heap_end);
                            ARGO_FREE(b64);
                            if (res) return res;
                        } else {
                            res = argo2_structure_write_field_by_description(fd,
                                                                             structure_start,
                                                                             array_index,
                                                                             array_max_index,
                                                                             NULL,
                                                                             heap,
                                                                             heap_end);
                            if (res) return res;
                        }
                        //fprintf(stderr, "read binary json, got len = %d\n", (int) length);
                    }
                }
                break;
            case argo_field_data_type_object:
                fprintf(stderr, "Sub-object\n");
#if 0
                /* Verify structure type. */
                if (fd->have_sub_type_index) {
                    size_t structure_size;
                    const char *sub_type;
                    struct argo_structure_description *sub_desc;

                    /* Note: JSON doesn't care about substructure name. */
                    sub_type = argo_structure_get_substructure_type(description,
                                                                    fd,
                                                                    structure_start);

                    if (!sub_type) {
                        fprintf(stderr, "%s:%s:%d: Get structure name failed\n",
                                __FILE__, __FUNCTION__, __LINE__);
                        return ARGO_RESULT_BAD_DATA;
                    }

                    /* Look up structure in global structure state... */
                    sub_desc = zhash_table_lookup(argo_global.all_descriptions_hash,
                                                sub_type,
                                                strlen(sub_type),
                                                NULL);

                    if (!sub_desc) {
                        ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                        res = argo_register_unknown_structure(argo, sub_type, strlen(sub_type));
                        ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                        return res;
                    }

                    /* Setup location on the heap... */
                    structure_size = ARGO_ALIGN(sub_desc->structure_size);
                    structure_size += ARGO_ALIGN(sizeof(struct argo_excess_field) *
                                                 (sub_desc->description_count -
                                                  sub_desc->static_description_count));

                    res = argo_structure_setup_write_field(description,
                                                           fd,
                                                           structure_start,
                                                           array_index,
                                                           &target_addr,
                                                           &target_addr_end,
                                                           structure_size,
                                                           heap,
                                                           heap_end);
                    if (res) {
                        fprintf(stderr, "%s:%s:%d: Substructure write setup failed\n",
                                __FILE__, __FUNCTION__, __LINE__);
                        return res;
                    }

                    memset(target_addr, 0, structure_size);

                    /* We should be prepared to fill in the substructure... */
                    res = argo_deserialize_object_json(argo,
                                                       heap,
                                                       heap_end,
                                                       sub_desc,
                                                       target_addr);
                    if (res) {
                        if ((res != ARGO_RESULT_REPARSE) && (res != ARGO_RESULT_INSUFFICIENT_DATA)) {
                            fprintf(stderr, "%s:%s:%d: Substructure deserialize failed for type %s\n",
                                    __FILE__, __FUNCTION__, __LINE__, sub_desc->type);
                        }
                        return res;
                    }
                } else {
                    fprintf(stderr, "%s:%s:%d: Substructure must have a type\n",
                            __FILE__, __FUNCTION__, __LINE__);
                    return ARGO_RESULT_BAD_DATA;
                }
#endif // 1
                break;
            case argo_field_data_type_argo_object:
                //_ARGO_LOG(AL_ERROR, "%s:%d: json decode of argo_object not implemented. (array_index = %d, array_count = %d)", __FILE__, __LINE__, (int)array_index, (int)array_count);
                {
                    struct argo_object *child_obj;
                    char *structure_type = NULL;

                    /* If this is 'old json' style, we read the object
                     * type from the existing structure */
                    if (fd->json_old && fd->have_sub_type_index) {
                        res = argo_structure_read_string_by_column_index(description, structure_start, fd->sub_type_index, &structure_type);
                        if (res) {
                            _ARGO_DEBUG_DES("Could not get structure type: %s", argo_result_string(res));
                            return res;
                        }
                        if (!structure_type) {
                            _ARGO_DEBUG_DES("Structure type came back NULL");
                            return res;
                        }
                    }



                    res = argo_deserialize_json_internal(parse_buf, &child_obj, structure_type);
                    if (res) {
                        return res;
                    }
                    res = argo2_structure_write_field_by_description(fd,
                                                                     structure_start,
                                                                     array_index,
                                                                     array_max_index,
                                                                     child_obj,
                                                                     heap,
                                                                     heap_end);
                    if (child_obj) {
                        argo_object_release(child_obj);
                    }
                    if (res) return res;
                }
                break;
            default:
                _ARGO_LOG(AL_ERROR, "%s:%d: Not implemented", __FILE__, __LINE__);
                return ARGO_RESULT_NOT_IMPLEMENTED;
            }
        }
        /* Align heap correctly. Might waste a teeny bit of space. Oh well. */
        heap_increment_and_align(heap, 0);

        if (fd->have_dynamic_count_index) {
            /* Write array_count */
            argo_structure_write_int_by_column_index(description, structure_start, fd->dynamic_count_index, array_index);
            //_ARGO_DEBUG_DES("Writing dynamic count for field %s, at index %d as %d", fd->field_name, (int)fd->dynamic_count_index, (int) struct_max_array);
        }

        if (need_terminator) {
            res = argo_buf_require_char(parse_buf, ']');
            if (res) {
                if (res == ARGO_RESULT_NOT_FOUND) {
                    fprintf(stdout, "%s:%s:%d: Array size exceeded, field %s?\n",
                            __FILE__, __FUNCTION__, __LINE__, fd->field_name);
                }
                return res;
            }
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_register_unknown_structure(struct argo_state *argo,
                                    const char *structure_name,
                                    size_t structure_name_length)
{
    struct argo_structure_description *new_desc;
    int res;

    new_desc = argo_get_structure_description(structure_name);
    if (!new_desc) {
        /*
         * We need to create a description of this structure. The
         * description is used to keep parsing state up to date.
         */
        new_desc = argo_register_global_structure(structure_name, 0, NULL, 0);
        if (!new_desc) {
            _ARGO_LOG(AL_ERROR, "Could not dynamically create structure description for structure named %s", structure_name);
            return ARGO_RESULT_ERR;
        }
        //_ARGO_LOG(AL_NOTICE, "Created dynamic structure description for structure named %s", structure_name);
    }

    /* Attach new_desc to this argo */
    if (argo) {
        res = argo_register_structure(argo, new_desc, NULL, NULL);
        if (res == ARGO_RESULT_NO_ERROR) {
            //_ARGO_LOG(AL_NOTICE, "Associated global structure %s with our global state", structure_name);
            res = ARGO_RESULT_REPARSE;
        }
        return res;
    } else {
        return ARGO_RESULT_REPARSE;
    }
}


/*
 * Deserialize a JSON encoded structure. (This is the root
 * call. argo_deserialize_object_json is for recursing)
 *
 * If structure_type is NULL, then format is:
 * { "STRUCTURE_TYPE" : { ...STRUCTURE DATA... }}
 *
 * If structure_type in non-NULL, then format is:
 *
 * { ... STRUCTURE DATA ... }
 *
 * if structure_type is non-NULL, then there is no "type"
 */
int
argo_deserialize_root_json(struct argo_buf **parse_buf,
                           struct argo_state *cb_argo,
                           uint8_t **heap,
                           uint8_t *heap_end,
                           argo_structure_callback_f **do_callback,
                           void **do_callback_cookie,
                           char *structure_type)
{
    char structure_name[ARGO_MAX_NAME_LENGTH];
    struct argo_structure_state *s_state = NULL;
    struct argo_structure_description *description;
    struct argo_object *object;
    size_t length;
    size_t str_length;
    int res;

    if (!structure_type) {
        /*
         * We must have a leading '{'
         */
        res = argo_buf_require_char(parse_buf, '{');
        if (res) {
            _ARGO_DEBUG_DES("Missing leading \"{\", res = %s.", argo_result_string(res));
            return res;
        }

        /*
         * Read name of structure.
         */
        length = sizeof(structure_name);
        res = argo_buf_read_json_string(parse_buf, structure_name, &length);
        if (res) {
            _ARGO_DEBUG_DES("Cannot read name of structure, res = %s.", argo_result_string(res));
            return res;
        }
        str_length = strnlen(structure_name, length + 1);
        if (length != str_length) {
            res = ARGO_RESULT_BAD_DATA;
            _ARGO_LOG(AL_ERROR, "Bad input detected: In RPC [%s] for field [%.32s], parse_len %zu, str_len %zu..aborting with error %s",
                                  "UNKNOWN", structure_name, length, str_length, argo_result_string(res));
            return res;
        }

        res = argo_buf_require_char(parse_buf, ':');
        if (res) {
            _ARGO_DEBUG_DES("Missing \":\", res = %s.", argo_result_string(res));
            return res;
        }
    } else {
        length = sxprintf(structure_name, structure_name + sizeof(structure_name), "%s", structure_type);
    }

    /*
     * Check if we have a compiled description of this structure. If
     * we do, then that description dictates where we put elements
     * from within the structure.
     *
     * Description of NULL is allowed- means we are not really storing
     * this structure anywhere. However, we interpret the whole thing
     * in order to keep parsing state up to date.
     */
    INLINE_VOID_INFERENCE(description, zhash_table_lookup(argo_global.all_descriptions_hash,
                                                          (const uint8_t *) structure_name,
                                                          length,
                                                          NULL));
    if (!description) {
        /*
         * We drop our lock for the duration of this maintenance...
         */
        //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        res = argo_register_unknown_structure(NULL, structure_name, length);
        //ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
        _ARGO_DEBUG_DES("No description, structure name = %s, res = %s.", structure_name, argo_result_string(res));
        return res;
    }

    if (cb_argo) {
        /* We only fetch this to find registered callbacks for
         * returning. Almost seems a waste. */
        s_state = (struct argo_structure_state *)
            zhash_table_lookup(cb_argo->registered_structures,
                               (const uint8_t *)structure_name,
                               length, NULL);
    }

    /* Set up our object. */
    object = argo_heap_instantiate_object(description, heap, heap_end);
    if (!object) {
        _ARGO_LOG(AL_ERROR, "Not enough space for heap object, structure name = %s, heap_size = %ld, res = %s.",
                  structure_name, heap_end - *heap,
                  argo_result_string(ARGO_RESULT_ERR_TOO_LARGE));
        return ARGO_RESULT_ERR_TOO_LARGE;
    }

    res = argo_deserialize_object_json(parse_buf,
                                       heap,
                                       heap_end,
                                       description,
                                       object->base_structure_data);
    if (res) {
        _ARGO_DEBUG_DES("argo_deserialize_object_json() failed, res = %s.", argo_result_string(res));
        return res;
    }

    if (cb_argo) {
        if ((!s_state) || (!s_state->callback_f)) {
            *do_callback = cb_argo->default_read_callback;
            *do_callback_cookie = cb_argo->callback_cookie;
        } else {
            *do_callback = s_state->callback_f;
            *do_callback_cookie = s_state->callback_cookie;
        }
    }

    if (!structure_type) {
        /*
         * We must have a trailing '}'
         */
        res = argo_buf_require_char(parse_buf, '}');
        if (res) {
            _ARGO_DEBUG_DES("Missing trailing \"}\", res = %s.", argo_result_string(res));
            return res;
        }
    }

    return res;
}


struct argo_object *argo_deserialize_json(const void *data, size_t data_len)
{
    uint8_t structure_buffer[ARGO_BUF_DEFAULT_SIZE];
    uint8_t small_buffer[2048];
    uint8_t *heap_walk;
    uint8_t *heap_end;
    uint8_t *heap_base = structure_buffer;
    uint8_t *allocated_heap = NULL;
    size_t heap_base_size = sizeof(structure_buffer);
    struct argo_buf bufspace;
    struct argo_buf parsespace;
    struct argo_buf *heap_buf;
    struct argo_buf *parse_buf;
    struct argo_buf *release_heap_buf = NULL;
    struct argo_buf *release_parse_buf = NULL;
    int res;
    struct argo_object *obj;
    struct argo_object *new_obj;
    int peek;
    argo_structure_callback_f *do_callback_f;
    void *do_callback_cookie;

    //fprintf(stderr, "\n**************************************************\nargo_deserialize_json\n");

    heap_buf = argo_buf_create(0, &bufspace, structure_buffer, sizeof(structure_buffer));
    if (!heap_buf) {
        return NULL;
    }
    release_heap_buf = heap_buf;

    parse_buf = argo_buf_create(0, &parsespace, small_buffer, sizeof(small_buffer));
    if (!parse_buf) {
        argo_buf_free_all(release_heap_buf);
        return NULL;
    }
    release_parse_buf = parse_buf;

    if ((res = argo_buf_append(&parse_buf, data, data_len))) {
        /* Error */
        fprintf(stderr, "Append error\n");
        argo_buf_free_all(release_heap_buf);
        argo_buf_free_all(release_parse_buf);
        return NULL;
    }
    parse_buf = release_parse_buf;

    while (1) {
        peek = argo_buf_peek(parse_buf);
        if (peek < 0) {
            argo_buf_free_all(release_heap_buf);
            argo_buf_free_all(parse_buf);
            return NULL;
        }
        if (peek == '{') break;
        argo_buf_remove_from_front(&parse_buf, 1);
    }

    do {
        heap_walk = heap_base;
        heap_end = heap_walk + heap_base_size;
        obj = (struct argo_object *) heap_walk;
        obj->base_structure_index = -1;
        parse_buf = release_parse_buf;
        argo_buf_reset(parse_buf);
        res = argo_deserialize_root_json(&parse_buf, NULL, &heap_walk, heap_end, &do_callback_f, &do_callback_cookie, NULL);
        if (res) {
            /* Release sub-objects we may have created... */
            argo_object_force_release(obj);
        }
        if (res == ARGO_RESULT_ERR_TOO_LARGE) {
            /* Retry with a (larger) allocated buffer */
            if (allocated_heap) {
                ARGO_FREE(allocated_heap);
                allocated_heap = NULL;
            }
            heap_base_size *= ARGO_BUF_GROWTH_MULTIPLE;
            //fprintf(stderr, "\n**************************************************\nGrow heap_base to %ld bytes\n", (long) heap_base_size);
            if (heap_base_size <= ARGO_BUF_MAX_SIZE) {
                allocated_heap = ARGO_ALLOC(heap_base_size);
                heap_base = allocated_heap;
                res = ARGO_RESULT_REPARSE;
            }
        }
    } while (res == ARGO_RESULT_REPARSE);

    if (res) {
        fprintf(stderr, "Could not parse: %s\n", argo_result_string(res));
    }

    if (res) {
        new_obj = NULL;
    } else {
        obj->total_size = heap_walk - heap_base;
        new_obj = argo_object_copy(obj);
        argo_object_force_release(obj);
    }

    argo_buf_free_all(release_heap_buf);
    argo_buf_free_all(release_parse_buf);
    if (allocated_heap) ARGO_FREE(allocated_heap);

    return new_obj;
}

static int argo_deserialize_json_internal(struct argo_buf **parse_buf, struct argo_object **new_object, char *object_type)
{
    uint8_t structure_buffer[ARGO_BUF_DEFAULT_SIZE];
    uint8_t *heap_walk;
    uint8_t *heap_end;
    uint8_t *heap_base = structure_buffer;
    size_t heap_base_size = sizeof(structure_buffer);
    struct argo_buf bufspace;
    struct argo_buf *heap_buf;
    struct argo_buf *release_heap_buf = NULL;
    struct argo_buf *save_parse_buf;
    uint8_t *current = NULL;
    int res;
    struct argo_object *obj = NULL;
    int peek;
    argo_structure_callback_f *do_callback_f;
    void *do_callback_cookie;

    //fprintf(stderr, "\n**************************************************\nStart heap_base as %ld bytes\n", (long) heap_base_size);
    heap_buf = argo_buf_create(0, &bufspace, structure_buffer, sizeof(structure_buffer));
    if (!heap_buf) {
        res = ARGO_RESULT_NO_MEMORY;
        return res;
    }
    release_heap_buf = heap_buf;

    while (1) {
        peek = argo_buf_peek(*parse_buf);
        if (peek < 0) {
            argo_buf_free_all(release_heap_buf);
            return ARGO_RESULT_BAD_DATA;
        }
        if (peek == '{') break;
        argo_buf_remove_from_front(parse_buf, 1);
    }

    save_parse_buf = *parse_buf;
    current = save_parse_buf->current;
    do {
        heap_walk = heap_base;
        heap_end = heap_walk + heap_base_size;
        obj = (struct argo_object *) heap_walk;
        obj->base_structure_index = -1;
        res = argo_deserialize_root_json(parse_buf, NULL, &heap_walk, heap_end, &do_callback_f, &do_callback_cookie, object_type);
        if (res == ARGO_RESULT_ERR_TOO_LARGE) {
            argo_buf_free_all(release_heap_buf);
            release_heap_buf = NULL;
            heap_base_size *= ARGO_BUF_GROWTH_MULTIPLE;
            //fprintf(stderr, "\n**************************************************\nGrow heap_base to %ld bytes\n", (long) heap_base_size);
            if (heap_base_size <= ARGO_BUF_MAX_SIZE) {
                heap_buf = argo_buf_create(0, &bufspace, NULL, heap_base_size);
                release_heap_buf = heap_buf;
                heap_base = heap_buf->start;
                *parse_buf = save_parse_buf;
                argo_buf_reset(*parse_buf);
                save_parse_buf->current = current;
            } else {
                break;
            }
        }
    } while (res == ARGO_RESULT_ERR_TOO_LARGE);

    if (res) {
        argo_buf_free_all(release_heap_buf);
        return res;
    }

    obj->total_size = heap_walk - heap_base;

    (*new_object) = argo_object_copy(obj);

    /* Release sub-objects we may have created... */
    argo_object_force_release(obj);

    argo_buf_free_all(release_heap_buf);

    return res;
}

/*
 * Often called quite recursively.
 *
 * Deserialize:
 *
 * 1. Append data to buffer.
 * 2. Continue processing.
 *
 * There are only really two deserialization states:
 *
 * 1. Waiting on/processing description.
 * 2. Waiting on/processing element length.
 * 3. Waitong on/processing element.
 *
 */
int argo_deserialize(struct argo_state *argo,
                     const void *data,
                     size_t data_len,
                     size_t *objects_found)
{
    /* The following buffer is HUGE. See 'XXX' NOTE below */
    uint8_t structure_buffer[ARGO_BUF_DEFAULT_SIZE];
    uint8_t *heap_walk;
    uint8_t *heap_end;
    uint8_t *heap_base = structure_buffer;
    size_t heap_base_size = sizeof(structure_buffer);
    uint64_t count;
    uint64_t value;
    argo_structure_callback_f *do_callback_f;
    void *do_callback_cookie;
    int res;
    int peek;

    /* This size constrains how big of a static structure we can deserialize with argo2 */
    uint8_t bufferspace[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf bufspace;
    struct argo_buf *heap_buf;
    struct argo_buf *release_heap_buf = NULL;

    size_t object_count;
    struct argo_object *obj;
    struct argo_object *deserialized_object;

    object_count = 0;

#if 0
    {
        static int64_t byte_count = 0;
        static int64_t last_print_byte_count = 0;
        static int64_t last_time;
        static int64_t current_time;


        byte_count += data_len;
        if ((byte_count - last_print_byte_count) > 1024*1024*16) {
            double seconds;
            double diff_count = byte_count - last_print_byte_count;
            current_time = epoch_us_accuracy_us();
            seconds = ((double)current_time - (double)last_time) / 1000000.0;
            fprintf(stderr, "%s:%s:%d: Received %ld bytes in %8.6f seconds, or %8.2f bytes/s\n",
                    __FILE__, __FUNCTION__, __LINE__,
                    (long) diff_count,
                    seconds,
                    (double) diff_count / seconds);
            last_print_byte_count = byte_count;
            last_time = current_time;
        }
    }
#endif

    argo->parse_buf = argo->read_buf;
    if ((res = argo_buf_append(&(argo->parse_buf), data, data_len))) {
        /* Error */
        fprintf(stderr, "Append error\n");
        goto done;
    }

 retry:
    do {

        int is_json = 0;
        deserialized_object = NULL;
        /* Reset our buffer read ptr. */
        argo_buf_reset(argo->read_buf);
        argo->parse_buf = argo->read_buf;

        /*
         * Argo is always trying to read a structure. We throw away
         * NOP's until we get to one of two "keys" that indicate a
         * structure is beginning: Either a '"' or an encoded length,
         * which will always have the most significant bit set.
         *
         * Note that the JSON parser leaves trailing '}'
         */
        while (1) {
            peek = argo_buf_peek(argo->read_buf);
            if (peek < 0) {
                res = ARGO_RESULT_INSUFFICIENT_DATA;
                goto done;
            }
            if ((peek == '{') || (argo->accept_binary && ARGO_RECORD_CODE_IS_BINARY(peek))) break;
            argo_buf_remove_from_front(&(argo->read_buf), 1);
            argo->block_byte_count++;
        }

        /*
         * Grab global read lock while deserializing.
         *
         * NOTE*** Deserialization routines will release the read
         * lock (and acquire and release a write lock) if they
         * need to update structure descriptions.  That is why
         * this read lock is WITHIN the do {} loop!
         */
        /* Reset our buffer read ptr. */
        if (release_heap_buf) {
            argo_buf_free_all(release_heap_buf);
            release_heap_buf = NULL;
        }
        heap_buf = argo_buf_create(0, &bufspace, bufferspace, sizeof(bufferspace));
        if (!heap_buf) {
            res = ARGO_RESULT_NO_MEMORY;
            goto done;
        }
        release_heap_buf = heap_buf;

        argo_buf_reset(argo->read_buf);
        argo->parse_buf = argo->read_buf;
        //ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

        do_callback_f = NULL;

        heap_walk = heap_base;
        heap_end = heap_walk + heap_base_size;
        obj = (struct argo_object *) heap_walk;

        /* We use an update of base_structure_index as an indication
         * that a valid object has been written. (It might not be
         * complete, but it should be zeroed) i.e. we should be able
         * to clean up any sub-objects that may have been
         * created/allocated before parsing failed/stopped. */
        obj->base_structure_index = -1;

#if 0
        fprintf(stderr, "Deserializing. Current buf = \n");
        argo_buf_dump(argo->read_buf, 0, 10000);
#endif // 0


        if (peek == '{') {
            //fprintf(stderr, "Decoding as JSON\n");
            if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_ANY) {
                //fprintf(stderr, "%p: Current version is ANY, received JSON frame", argo);
            }

            res = argo_deserialize_root_json(&(argo->parse_buf), argo, &heap_walk, heap_end, &do_callback_f, &do_callback_cookie, NULL);
            is_json = 1;
        } else {
            /*
             * Read structure length.
             */
            res = argo_buf_read_encoded_uint64(&(argo->parse_buf), ARGO_RECORD_CODE_BINARY_VBIT, &count, &value);
            if (res) {
                //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                goto done;
            }

            /* Length of structure is value + count. */
            if ((argo->block_byte_count + count + value) >
                argo->block_byte_count_next_block_description) {
                /* Uh oh- we have a structure that spans the time
                 * when we are supposed to have a new block
                 * description. */
                //return ARGO_RESULT_ERR_TOO_LARGE;
            }

            /* Verify that we have enough data buffered to parse
             * this structure. */
            if ((count + value) > argo_buf_size(argo->read_buf)) {
                /* Still don't have enough data to process this
                 * structure. */
                //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                res = ARGO_RESULT_INSUFFICIENT_DATA;
                goto done;
            }

            if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_ANY) {
                //fprintf(stderr, "%p: Assuming version is 2 as we received binary data without json version header", argo);
                argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;
            }
            if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_2) {
                //fprintf(stderr, "%p: Decoding as version 2\n", argo);
                abort();
            } else if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_3) {
                //fprintf(stderr, "%p: Decoding as version 3\n", argo);
                res = argo2_binary_deserialize_argo_object(argo,
                                                           &(argo->parse_buf),
                                                           &(deserialized_object),
                                                           heap_buf);
                if (res) {
                    //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
                    goto done;
                }
                struct argo_structure_state *s_state = argo->structures[deserialized_object->base_structure_index];
                if (s_state) {
                    if ((!s_state->callback_f) && (argo->default_read_callback)) {
                        do_callback_f = argo->default_read_callback;
                        do_callback_cookie = argo->callback_cookie;
                    } else {
                        do_callback_f = s_state->callback_f;
                        do_callback_cookie = s_state->callback_cookie;
                    }
                } else {
                    do_callback_f = NULL;
                }
            } else {
                res = ARGO_RESULT_BAD_DATA;
            }

            /* CHEAP. For the time being this is fine. */
            /* XXX Do better memory management than using stack here? */
        }
        //ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

        if (res == ARGO_RESULT_NO_ERROR) {
            object_count++;
            if (objects_found) *objects_found = object_count;
#if 0
            {
                static int64_t stupo_count = 0;
                stupo_count++;
                if ((stupo_count % 1000) == 0) {
                    fprintf(stderr, "%s:%s:%d Processed %ld callbacks\n", __FILE__, __FUNCTION__, __LINE__, (long) stupo_count);
                }
            }
#endif // 0
            obj->total_size = heap_walk - heap_base;
            if (deserialized_object) obj = deserialized_object;
            if (do_callback_f) {
                if (!argo->received_any_object) {
                    if (do_callback_f != argo_block_description_callback_func) {
                        /* First object is supposed to be an
                         * argo_description with version
                         * information. If it is not, we simulate
                         * receiving such */
                        if (argo->argo_encoding_version == ARGO_ENCODING_VERSION_ANY) {
                            //fprintf(stderr, "%p: Received an object without receiving version, assuming version 2\n", argo);
                            argo->argo_encoding_version = ARGO_ENCODING_VERSION_3;
                        }
                        if (argo->strict_version_check) {
                            //fprintf(stderr, "Error: Incompatible argo version, ours = %d, theirs = %d\n", ARGO_VERSION_MAJOR, (int)description->major_version);
                            _ARGO_LOG(AL_ERROR, "Incompatible argo version, ours = %d, theirs = %d", (int)ARGO_VERSION_MAJOR, (int)2);
                            res = ARGO_RESULT_ERR;
                            goto done;
                        }
                        if (argo->version_callback) {
                            (argo->version_callback)(argo->callback_cookie, argo->argo_encoding_version, 0);
                        }
                    } else {
                        //_ARGO_LOG(AL_NOTICE, "Received version object as first thing received");
                    }
                    argo->received_any_object = 1;
                }
                res = (*(do_callback_f))(argo->callback_cookie,
                                         do_callback_cookie,
                                         obj);
                if (res) {
                    //fprintf(stderr, "Callback function returned %s\n", argo_result_string(res));
                    /* XXX LOG */
                }
            } else {
                _ARGO_LOG(AL_NOTICE, "No callback function: %s", argo_object_get_type(obj));
            }
            /* We need to release any objects we might have allocated-
             * REGARDLESS of the success of parsing! (i.e. we need to
             * release state from partially successful parsing) */
            if (deserialized_object) {
                //argo_object_release(deserialized_object);
                if (deserialized_object->base_structure_index >= 0) {
                    argo_object_force_release(deserialized_object);
                }
            } else {
                if (obj->base_structure_index >= 0) {
                    argo_object_force_release(obj);
                }
            }
            argo->block_byte_count += argo_buf_collapse_front(&(argo->read_buf));

        } else if (res == ARGO_RESULT_ERR_TOO_LARGE) {
            if (!is_json) {
                _ARGO_LOG(AL_CRITICAL, "Unrecoverable error- object exceeded %ld bytes", (long)sizeof(structure_buffer));
                /*
                 * XXX
                 * NOTE: Re-decoding a differentially encoded stream
                 * doesn't work. Differential integers will be
                 * re-differentialed, and differential token id's will
                 * have the same happen to them
                 *
                 * Once the differential encoding issue is resolved, then
                 * the following heap allocation code can be engaged
                 * again.
                 *
                 * Note: This code is set up so the stack based buffer is
                 * 16K, which grows to 1MB, then 16 MB, then fails- so if
                 * differential encoding gets fixed, the size of the
                 * buffer on the stack should be dropped back down to
                 * something reasonable.
                 */
                /* Clean up a bit, because we're outta here */
                if (obj->base_structure_index >= 0) {
                    argo_object_force_release(obj);
                }
                goto done;
            }
            /* We need more heap- we'll actually allocate some for it. */
            if (heap_base != structure_buffer) {
                ARGO_FREE(heap_base);
                heap_base = NULL;
            }
            heap_base_size *= ARGO_BUF_GROWTH_MULTIPLE;
            if (heap_base_size > ARGO_BUF_MAX_SIZE) {
                res = ARGO_RESULT_NO_MEMORY;
                goto done;
            }
            //fprintf(stderr, "\n**************************************************\nGrow heap_base to %ld bytes\n", (long) heap_base_size);
            heap_base = ARGO_ALLOC(heap_base_size);
            if (!heap_base) {
                res = ARGO_RESULT_NO_MEMORY;
                goto done;
            }
            goto retry;
        } else if (res != ARGO_RESULT_REPARSE) {
            if (obj->base_structure_index >= 0) {
                argo_object_force_release(obj);
            }
            goto done;
        }

    } while (((res == ARGO_RESULT_NO_ERROR) || (res == ARGO_RESULT_REPARSE)) && argo_buf_size(argo->read_buf));

 done:
    if (release_heap_buf) {
        argo_buf_free_all(release_heap_buf);
        release_heap_buf = NULL;
    }
    if (heap_base && heap_base != structure_buffer ) {
        ARGO_FREE(heap_base);
        heap_base = NULL;
    }

#if 0
    if ((res != ARGO_RESULT_NO_ERROR) &&
        (res != ARGO_RESULT_REPARSE) &&
        (res != ARGO_RESULT_INSUFFICIENT_DATA)) {
        argo_buf_dump(argo->read_buf, 0, 2000);
        argo_buf_dump(argo->parse_buf, 0, 2000);
    }
#endif // 0

    return res;
}


/*
 * Serialize a structure based on its description. This will result in
 * callbacks to the write callback.
 *
 * argo - The argo context to use for serialization.
 *
 * description - The compiled description that describes the data
 *      passed in.
 *
 * structure_data - The structure to serialized. Described by
 *      'description'
 */
int argo_serialize_structure(struct argo_state *argo,
                             struct argo_structure_description *description,
                             void *structure_data,
                             enum argo_serialize_mode mode)
{
    return argo_serialize_internal(argo,
                                   description,
                                   structure_data,
                                   mode,
                                   description->static_description_count);
}

/*
 * Serialize an object. This will result in callbacks to the write
 * callback. This is very similar to argo_serialize, except that it
 * operates on an argo_object rather than a void * to a structure.
 *
 * argo - The argo context to use for serialization.
 *
 * object - The object to serialize.
 *
 * data - The data to serialized. Described by 'description'
 */
int argo_serialize_object(struct argo_state *argo,
                          struct argo_object *object,
                          enum argo_serialize_mode mode)
{
    struct argo_structure_description *desc = NULL;
    struct argo_structure_state *ss = argo->structures[object->base_structure_index];
    if (!ss) {
        desc = argo_global.all_descriptions[object->base_structure_index];
        //fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        if (!desc) return ARGO_RESULT_BAD_ARGUMENT;
    } else {
        desc = ss->master;
    }
    return argo_serialize_internal(argo,
                                   desc,
                                   object->base_structure_data,
                                   mode,
                                   desc->static_description_count + object->excess_description_count);
}


int argo_send_version(struct argo_state *argo)
{
    struct argo_description description;
    memset(&description, 0, sizeof(description));
    description.major_version = ARGO_VERSION_MAJOR;
    description.minor_version = ARGO_VERSION_MINOR;
    description.block_size = 0;
    argo_serialize_structure(argo, argo->argo_description_description, &description, argo_serialize_json_pretty);
    return ARGO_RESULT_NO_ERROR;
}



/*
 * Up reference count on object.
 */
void argo_object_hold(struct argo_object *object)
{
    //int prior_value;
    //prior_value =
    __sync_fetch_and_add_4(&(object->reference_count), 1);
    //fprintf(stderr, "******************** obj  %d -> %d\n", prior_value, prior_value + 1);
};

void argo_structure_hold(void *structure)
{
    uint8_t *s = (uint8_t *)structure;
    s -= sizeof(struct argo_object);
    argo_object_hold((struct argo_object *)s);
}

/*
 * Release all allocated elements of an object, but ignore the object
 * itself. This is only used by argo_object_release and the
 * deserialization routines, which place their root object on the
 * stack, thus it is not to be released...
 */
void argo_object_force_release(struct argo_object *object)
{
    struct argo_structure_description *d;
    int array_count;
    int res;
    size_t i;

    if (!object || object->base_structure_index == -1) {
        return;
    }

    d = argo_global.all_descriptions[object->base_structure_index];
    for (i = 0; i < d->static_description_count + object->excess_description_count; i++) {
        struct argo_field_description *fd = &(d->description[i]->public_description);

        if (fd->argo_field_type == argo_field_data_type_dictionary) {
            int array_index;
            array_count = argo_structure_field_get_array_count_by_index(d, i, object->base_structure_void);
            for (array_index = 0; array_index < array_count; array_index++) {
                void *where;
                res = argo2_structure_where(fd,
                                            (uint8_t *) object->base_structure_void,
                                            array_index,
                                            &where);
                if (res) {
                    _ARGO_LOG(AL_ERROR, "Bad reference");
                } else if (where) {
                    struct argo_dictionary *dict;
                    INLINE_VOID_INFERENCE(dict, where);
                    if (dict) {
                        argo_dictionary_release(dict);
                    }
                }
            }
        } else if (fd->argo_field_type == argo_field_data_type_list) {
            int array_index;
            array_count = argo_structure_field_get_array_count_by_index(d, i, object->base_structure_void);
            for (array_index = 0; array_index < array_count; array_index++) {
                void *where;
                res = argo2_structure_where(fd,
                                            (uint8_t*) object->base_structure_void,
                                            array_index,
                                            &where);

                if (res) {
                    _ARGO_LOG(AL_ERROR, "Bad reference");
                } else if (where) {
                    struct argo_list *list;
                    INLINE_VOID_INFERENCE(list, where);
                    if (list) {
                        argo_list_release(list);
                    }
                }
            }
        } else if (fd->argo_field_type == argo_field_data_type_argo_object) {
            int array_index;
            array_count = argo_structure_field_get_array_count_by_index(d, i, object->base_structure_void);
            for (array_index = 0; array_index < array_count; array_index++) {
                void *where;
                res = argo2_structure_where(fd,
                                            (uint8_t *) object->base_structure_void,
                                            array_index,
                                            &where);

                if (res) {
                    _ARGO_LOG(AL_ERROR, "Bad reference");
                } else if (where) {
                    struct argo_object *obj;
                    INLINE_VOID_INFERENCE(obj, where);
                    if (obj) {
                        argo_object_release(obj);
                    }
                }
            }
        }
    }
}

/*
 * Drop reference count on object.
 */
void argo_object_release(struct argo_object *object)
{
    int new_value;

    new_value = __sync_sub_and_fetch_4(&(object->reference_count), 1);
    if (new_value == 0) {
        /* As horrible as it sounds, FOHH overloads
         * base_structure_index to do TLV queueing of argo objects, so
         * we only want to release our children if we're a 'real' argo
         * object */
        if (object->base_structure_index >= 0) {
            struct argo_structure_description *desc = argo_global.all_descriptions[object->base_structure_index];
            __sync_add_and_fetch_8(&(desc->frees), 1);
            __sync_add_and_fetch_8(&(desc->bytes_freed), object->total_size);
            argo_object_force_release(object);
        }
        ARGO_FREE(object);
    }
}

int argo_object_release_expected(struct argo_object *object, int32_t expected_end_reference)
{
    int new_value;

    new_value = __sync_sub_and_fetch_4(&(object->reference_count), 1);
    if (new_value == 0) {
        /* As horrible as it sounds, FOHH overloads
         * base_structure_index to do TLV queueing of argo objects, so
         * we only want to release our children if we're a 'real' argo
         * object */
        if (object->base_structure_index >= 0) {
            struct argo_structure_description *desc = argo_global.all_descriptions[object->base_structure_index];
            __sync_add_and_fetch_8(&(desc->frees), 1);
            __sync_add_and_fetch_8(&(desc->bytes_freed), object->total_size);
            argo_object_force_release(object);
        }
        ARGO_FREE(object);
    }
    if (new_value == expected_end_reference) {
        return ARGO_RESULT_NO_ERROR;
    } else {
        return ARGO_RESULT_ERR;
    }
}

void argo_structure_release(void *structure)
{
    uint8_t *s = (uint8_t *)structure;
    s -= sizeof(struct argo_object);
    argo_object_release((struct argo_object *)s);
}

int argo_structure_copy(struct argo_structure_description *d,
                        void *raw_source_structure_data,
                        uint8_t *dest_structure_data,
                        size_t excess_description_count,
                        char **heap,
                        char *heap_end)
{
    size_t i;
    size_t array_count;
    size_t array_index;
    int res;
    void *source_data;
    size_t source_len;
    struct argo_field_description *fd = NULL;
    struct argo_structure_description *source_desc = NULL;

    uint8_t *source_structure_data;
    INLINE_VOID_INFERENCE(source_structure_data, raw_source_structure_data);

    //char *heap_start = *heap;

    size_t structure_len;

    structure_len = ARGO_ALIGN(d->structure_size);
    structure_len += ARGO_ALIGN(sizeof(struct argo_excess_field) * (excess_description_count / 2));

    //fprintf(stderr, "Structure len = %ld, sizeof(excess_field) == %ld, excess_description_count = %ld\n", structure_len, sizeof(struct argo_excess_field), (long)excess_description_count);
    memcpy(dest_structure_data, source_structure_data, structure_len);

    /* For all members of the structure itself, check for
     * referenced fields, and fix them */
    for (i = 0; i < d->static_description_count + excess_description_count; i++) {
        fd = &(d->description[i]->public_description);

        if (fd->is_reference) {

            /* Clear all references that were copied, unless they are a dictionary or argo_object */
            if (fd->argo_field_type == argo_field_data_type_dictionary) {
                for (array_index = 0; array_index < fd->count; array_index++) {
                    struct argo_dictionary *dict = *((struct argo_dictionary **)(dest_structure_data + fd->offset + ((sizeof (char *)) * array_index)));
                    if (dict) {
                        argo_dictionary_hold(dict);
                    }
                }
                continue;
            } else if (fd->argo_field_type == argo_field_data_type_list) {
                for (array_index = 0; array_index < fd->count; array_index++) {
                    struct argo_list *list = *((struct argo_list **)(dest_structure_data + fd->offset + ((sizeof (char *)) * array_index)));
                    if (list) {
                        argo_list_hold(list);
                    }
                }
                continue;
            } else {
                for (array_index = 0; array_index < fd->count; array_index++) {
                    *((char **)(dest_structure_data + fd->offset + ((sizeof (char *)) * array_index))) = NULL;
                }
            }

            array_count = argo_structure_field_get_array_count_by_index(d, i, dest_structure_data);

            for (array_index = 0; array_index < array_count; array_index++) {
                res = argo2_structure_read_field_by_description(d,
                                                                fd,
                                                                source_structure_data,
                                                                array_index,
                                                                &source_data,
                                                                &source_len);
                if (res) {
                    fprintf(stdout, "%s:%s:%d: Failed to setup read (%s) for field %s\n", __FILE__, __FUNCTION__, __LINE__, argo_result_string(res),fd->field_name);
                    return ARGO_RESULT_ERR;
                }

                if (fd->argo_field_type == argo_field_data_type_object) {
                    uint8_t *write_location;
                    uint8_t *write_location_end;

                    /* source_len is zero if the structure is NULL */
                    if (source_len) {

                        if (!source_desc) {
                            fprintf(stdout, "%s:%s:%d: Failed to read substructure type (%s)\n", __FILE__, __FUNCTION__, __LINE__, fd->field_name);
                            return ARGO_RESULT_ERR;
                        }
                        res = argo_structure_setup_write_field(d,
                                                               fd,
                                                               dest_structure_data,
                                                               array_index,
                                                               &write_location,
                                                               &write_location_end,
                                                               source_len,
                                                               (uint8_t **)heap,
                                                               (uint8_t *)heap_end);
                        if (res) {
                            fprintf(stdout, "%s:%s:%d: Failed to set up write (%s) for field %s\n", __FILE__, __FUNCTION__, __LINE__, argo_result_string(res),fd->field_name);
                            return ARGO_RESULT_ERR;
                        }
                        //fprintf(stderr, "substructure size appears to be %ld\n", write_location_end - write_location);

                        res = argo_structure_copy(source_desc,
                                                  source_data,
                                                  write_location,
                                                  source_desc->description_count - source_desc->static_description_count,
                                                  heap,
                                                  heap_end);
                        if (res) {
                            fprintf(stdout, "%s:%s:%d: Failed to copy structure (%s(%s)) (%s)\n", __FILE__, __FUNCTION__, __LINE__, fd->field_name, source_desc->type, argo_result_string(res));
                            return ARGO_RESULT_ERR;
                        }
                    }

                } else {
                    /* Include NULL terminator on strings... */
                    if (fd->argo_field_type == argo_field_data_type_string) source_len++;
                    res = argo2_structure_write_field_by_description(fd,
                                                                     dest_structure_data,
                                                                     array_index,
                                                                     array_count,
                                                                     source_data,
                                                                     (uint8_t **)heap,
                                                                     (uint8_t *)heap_end);
                    if (res) {
                        fprintf(stdout, "%s:%s:%d: Failed to write (%s) for field %s\n", __FILE__, __FUNCTION__, __LINE__, argo_result_string(res),fd->field_name);
                        return ARGO_RESULT_ERR;
                    }
#if 0
                    {
                        char *walk = heap_end - 1;
                        while((walk > heap_start) && (!(*walk))) walk--;
                        fprintf(stderr, "Wrote field %s, heap offset = %ld, last nonzero offset = %ld\n", fd->field_name, (*heap) - heap_start, walk - heap_start);
                    }
#endif // 0
                }
            }
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_structure_compare(struct argo_structure_description *d,
                           void *a,
                           void *b)
{
    uint8_t *a_structure_data;
    INLINE_VOID_INFERENCE(a_structure_data, a);
    uint8_t *b_structure_data;
    INLINE_VOID_INFERENCE(b_structure_data, b);
    size_t i;
    int64_t array_count;
    int64_t tmp;
    int64_t array_index;
    const uint8_t *ref_a, *ref_b;
    int res;
    struct argo_field_description *fd = NULL;

    for (i = 0; i < d->static_description_count; i++) {
        fd = &(d->description[i]->public_description);

        /* Get element count */
        if (fd->have_dynamic_count_index) {
            res = argo_structure_read_int_by_column_index(d, a_structure_data, fd->dynamic_count_index, &array_count);
            if (res) return res;
            res = argo_structure_read_int_by_column_index(d, b_structure_data, fd->dynamic_count_index, &tmp);
            if (res) return res;
            if (tmp != array_count) {
                /* Mismatch */
                return ARGO_RESULT_ERR;
            }
        } else {
            array_count = fd->count;
        }

        if (!fd->is_reference) {
            if (memcmp(a_structure_data + fd->offset, b_structure_data + fd->offset, fd->size * fd->count)) {
                /* Mismatch */
                return ARGO_RESULT_ERR;
            }
        } else {
            /* Ignore all 'child' objects. Can recurse them in the future if necessary- particularly argo_objects */
            if (fd->argo_field_type == argo_field_data_type_dictionary) {
                continue;
            } else if (fd->argo_field_type == argo_field_data_type_list) {
                continue;
            }

            for (array_index = 0; array_index < array_count; array_index++) {
                /* The size of the element is in fd->size, unless it
                 * is a string, in which case the reference we have
                 * are to null terminated strings */
                res = argo2_structure_where(fd, a_structure_data, array_index, (void **)&ref_a);
                if (res) return res;
                res = argo2_structure_where(fd, b_structure_data, array_index, (void **)&ref_b);
                if (res) return res;
                if ((!ref_a) && (!ref_b)) {
                    continue;
                }
                if ((!ref_a) || (!ref_b)) {
                    return ARGO_RESULT_ERR;
                }

                if (fd->argo_field_type == argo_field_data_type_argo_object) {
                    struct argo_structure_description *d1, *d2;
                    d1 = argo_get_object_description((struct argo_object *)ref_a);
                    d2 = argo_get_object_description((struct argo_object *)ref_b);
                    if (d1 != d2) return ARGO_RESULT_ERR;
                    //fprintf(stderr, "Comparing SUB-STRUCTURES. WHEEEEEE\n");
                    if (argo_structure_compare(d1, ((struct argo_object *)ref_a)->base_structure_void, ((struct argo_object *)ref_b)->base_structure_void)) {
                        return ARGO_RESULT_ERR;
                    }
                } else if (fd->argo_field_type == argo_field_data_type_string) {
                    if (strcmp((const char *)ref_a, (const char *)ref_b)) {
                        return ARGO_RESULT_ERR;
                    }
                } else {
                    if (memcmp(ref_a, ref_b, fd->size)) {
                        return ARGO_RESULT_ERR;
                    }
                }
            }
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

static struct argo_object *argo_object_copy_internal(struct argo_object *source_object, int needs_locking)
{
    char *heap;
    char *heap_end;
    struct argo_object *dest_object = NULL;
    struct argo_structure_description *d;
    int res;
    struct argo_structure_description *desc = argo_global.all_descriptions[source_object->base_structure_index];

    //fprintf(stderr, "%s:%s:%d: Copying source object of size = %d\n", __FILE__, __FUNCTION__, __LINE__, (int) source_object->total_size);

    if (source_object->total_size < (int)sizeof(struct argo_object)) {
        source_object->total_size = sizeof(struct argo_object) + argo_structure_size(desc, source_object->base_structure_void, source_object->excess_description_count);
    }
    INLINE_VOID_INFERENCE(heap, ARGO_ALLOC(source_object->total_size));
    if (heap) {
        memset(heap, 0, source_object->total_size);

        /* Set up argo object itself, and its references. */
        heap_end = heap + source_object->total_size;

        dest_object = (struct argo_object *) heap;
        dest_object->base_structure_index = source_object->base_structure_index;
        dest_object->excess_description_count = source_object->excess_description_count;
        dest_object->reference_count = 1;
        dest_object->total_size = source_object->total_size;
        heap += sizeof(struct argo_object);
        INLINE_VOID_INFERENCE(dest_object->base_structure_data, (void *)heap);

        //if (needs_locking) argo_read_lock();
        d = argo_global.all_descriptions[source_object->base_structure_index];
        heap += d->structure_size;
        INLINE_VOID_INFERENCE(dest_object->excess_fields, (void *)heap);
        heap += sizeof(struct argo_excess_field) * (dest_object->excess_description_count / 2);
        if (heap > heap_end) {
            //if (needs_locking) argo_unlock();
            ARGO_FREE(dest_object);
            fprintf(stderr, "fail 1\n");
            return NULL;
        }

        //if (needs_locking) argo_unlock();

        res = argo_structure_copy(d, source_object->base_structure_data, dest_object->base_structure_data, dest_object->excess_description_count, &heap, heap_end);
        if (res) {
            fprintf(stdout, "%s:%s:%d: Failed to setup read (%s)\n", __FILE__, __FUNCTION__, __LINE__, argo_result_string(res));
            goto failure;
        }

        __sync_add_and_fetch_8(&(desc->allocations), 1);
        __sync_add_and_fetch_8(&(desc->bytes_allocated), dest_object->total_size);
    }
    return dest_object;

 failure:
    //if (needs_locking) argo_unlock();
    {
        char foo[4000];
        if (!argo_object_dump(source_object, foo, sizeof(foo), NULL, 1)) {
            fprintf(stdout, "fail 2: Field = %s, Object to copy = %s\n", "NULL", foo);
        }
    }
    fprintf(stdout, "fail 2: heap = %p, heap_end = %p, desc = %s\n",
            heap,
            heap_end,
            "NULL");

    ARGO_FREE(dest_object);
    return NULL;
}

struct argo_object *argo_object_copy(struct argo_object *source_object)
{
    return argo_object_copy_internal(source_object, 1);
}


size_t argo_structure_size(struct argo_structure_description *description, void *raw_structure, int excess_description_count)
{
    size_t i;
    const char *sub_type;
    struct argo_structure_description *sub_desc;
    int count;
    size_t size = 0;
    size_t q_size;
    int array_ix;
    uint8_t *structure;
    INLINE_VOID_INFERENCE(structure, raw_structure);

    size += description->structure_size;

    size += (excess_description_count / 2) * sizeof (struct argo_excess_field);

    //fprintf(stderr, "Structure size starts at: %ld\n", size);
    /* Find all the reference elements of the structure, and add their lengths. */
    for (i = 0; i < description->static_description_count + excess_description_count; i++) {
        struct argo_field_description *fd;
        fd = &(description->description[i]->public_description);
        if (fd->is_reference) {
            void *where;
            count = argo_structure_field_get_array_count_by_description(description, fd, structure);
            if (fd->reference_count == 2) {
                /* We have a foo **, so there is an extra (void *) * count */
                size += count * sizeof (void *);
            }

            for (array_ix = 0; array_ix < count; array_ix++) {
                int res;
                res = argo2_structure_where(fd, structure, array_ix, &where);
                if (res) {
                    fprintf(stdout, "%s:%d: Can't figure out where, desc = %s, field = %s\n", __FILE__, __LINE__, description->type, fd->field_name);
                    return 0;
                }
                if (where) {
                    switch (fd->argo_field_type) {
                    case argo_field_data_type_string:
                        q_size = ARGO_ALIGN(strlen((char *) where) + 1);
                        size += q_size;
                        break;
                    case argo_field_data_type_integer:
                    case argo_field_data_type_double:
                    case argo_field_data_type_inet:
                        size += fd->size;
                        break;
                    case argo_field_data_type_binary:
                        size += fd->size;
                        break;
                    case argo_field_data_type_object:
                        sub_type = argo_structure_get_substructure_type(description, fd, structure);
                        if (sub_type) {
                            /* Look up structure in global structure state... */
                            INLINE_VOID_INFERENCE(sub_desc, zhash_table_lookup(argo_global.all_descriptions_hash,
                                                                               sub_type,
                                                                               strlen(sub_type),
                                                                               NULL));
                            if (sub_desc) {
                                q_size = ARGO_ALIGN(argo_structure_size(sub_desc, *((uint8_t **)(structure + fd->offset)), 0));
                                size += q_size;
                            }
                        }
                        break;
                    case argo_field_data_type_list:
                    case argo_field_data_type_dictionary:
                    case argo_field_data_type_argo_object:
                        /* argo_objects, dictionaries, and lists are copied by
                         * reference, so they don't get counted in the size */
                        break;
                    default:
                        /* XXX LOG. */
                        fprintf(stderr, "%s:%s:%d: Not implemented, type %d\n", __FILE__, __FUNCTION__, __LINE__, fd->argo_field_type);
                        break;
                    }
                }
            }
        }
    }
    //fprintf(stderr, "Structure size ends at: %ld\n", size);
    return size;
}

/*
 * We create an object by first making a fake object structure and
 * filling in most fields, including object size. (must be determined
 * by examining the structure). Then we copy the fake object
 * structure.
 */
static struct argo_object *argo_object_create_internal(struct argo_structure_description *description,
                                                       void *structure,
                                                       int excess_fields,
                                                       int needs_locking)
{
    struct argo_object fake_object;

    fake_object.base_structure_index = description->global_index;
    //fake_object.excess_description_count = description->description_count - description->static_description_count;
    fake_object.excess_description_count = excess_fields;
    fake_object.reference_count = 1;
    fake_object.base_structure_data = (uint8_t *) structure;
    fake_object.excess_fields = (struct argo_excess_field *)(fake_object.base_structure_data + description->structure_size);

    /* Add up the size of the structure we are creating a copy of. */
    fake_object.total_size = sizeof(fake_object) + (fake_object.excess_description_count * sizeof(struct argo_excess_field));

    fake_object.total_size += argo_structure_size(description, structure, fake_object.excess_description_count);

    /* Now that we have a fake object, we just copy it. */
    return argo_object_copy_internal(&fake_object, needs_locking);
}

struct argo_object *argo_object_create(struct argo_structure_description *description,
                                       void *structure)
{
    return argo_object_create_internal(description, structure, 0, 1);
}

struct argo_object *argo_object_create_from_object(struct argo_object *object)
{
    return argo_object_create_internal(argo_global.all_descriptions[object->base_structure_index], object->base_structure_void, 0, 1);
}

int argo_object_read_int_by_column_name(struct argo_object *o, char *column_name, int64_t *value)
{
    int column_index;
    struct argo_private_field_description *f;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    INLINE_VOID_INFERENCE(f, zhash_table_lookup(d->described_fields, column_name, strlen(column_name), NULL));
    if (!f) {
        return ARGO_RESULT_NOT_FOUND;
    }
    column_index = f->index;
    return argo_object_read_int_by_column_index(o, column_index, value);
}

int argo_structure_read_int_by_column_index(struct argo_structure_description *d, const void *structure_data, int column_index, int64_t *value)
{
    struct argo_private_field_description *f;
    void *ref;

    f = d->description[column_index];
    if (f->public_description.is_reference) {
        ref = *((void **)((char *)structure_data + f->public_description.offset));
    } else {
        ref = (void *)((char *)structure_data + f->public_description.offset);
    }
    switch(f->public_description.size) {
    case 1:
        *value = *((int8_t *)ref);
        break;
    case 2:
        *value = *((int16_t *)ref);
        break;
    case 4:
        *value = *((int32_t *)ref);
        break;
    case 8:
        *value = *((int64_t *)ref);
        break;
    default:
        fprintf(stderr, "Err: Bad data, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_read_int_by_column_index(struct argo_object *o, int column_index, int64_t *value)
{
    struct argo_structure_description *d;
    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) {
        fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    return argo_structure_read_int_by_column_index(d, o->base_structure_void, column_index, value);

}

int argo_object_read_string_by_column_name(struct argo_object *o, char *column_name, char **value)
{
    int column_index;
    struct argo_private_field_description *f;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    INLINE_VOID_INFERENCE(f, zhash_table_lookup(d->described_fields, column_name, strlen(column_name), NULL));
    if (!f) {
        return ARGO_RESULT_NOT_FOUND;
    }
    column_index = f->index;
    return argo_object_read_string_by_column_index(o, column_index, value);
}

int argo_object_read_string_by_column_index(struct argo_object *o, int column_index, char **value)
{
    struct argo_private_field_description *f;
    struct argo_structure_description *d;
    void *ref;
    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) {
        fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    f = d->description[column_index];
    if (f->public_description.is_reference) {
        ref = *((void **)((char *)o->base_structure_data + f->public_description.offset));
    } else {
        ref = (void *)((char *)o->base_structure_data + f->public_description.offset);
    }
    *value = (char *) ref;
    return ARGO_RESULT_NO_ERROR;
}

int argo_structure_read_string_by_column_index(struct argo_structure_description *description, void *structure_data, int column_index, char **value)
{
    struct argo_private_field_description *f;
    void *ref;
    if (column_index >= description->description_count) {
        fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    f = description->description[column_index];
    if (f->public_description.is_reference) {
        ref = *((void **)((char *)structure_data + f->public_description.offset));
    } else {
        ref = (void *)((char *)structure_data + f->public_description.offset);
    }
    *value = (char *) ref;
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_read_binary_by_column_name(struct argo_object *o, char *column_name, uint8_t **value, size_t *length)
{
    int column_index;
    struct argo_private_field_description *f;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    INLINE_VOID_INFERENCE(f, zhash_table_lookup(d->described_fields, column_name, strlen(column_name), NULL));
    if (!f) {
        return ARGO_RESULT_NOT_FOUND;
    }
    column_index = f->index;
    return argo_object_read_binary_by_column_index(o, column_index, value, length);
}

int argo_object_read_binary_by_column_index(struct argo_object *o, int column_index, uint8_t **value, size_t *length)
{
    struct argo_private_field_description *f;
    struct argo_structure_description *d;
    void *ref;
    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) {
        fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    f = d->description[column_index];
    if (f->public_description.is_reference) {
        ref = *((void **)((char *)o->base_structure_data + f->public_description.offset));
    } else {
        ref = (void *)((char *)o->base_structure_data + f->public_description.offset);
    }
    *value = (uint8_t *) ref;
    *length = f->public_description.size;
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_get_column_index(struct argo_object *o, char *column_name, int *column_index)
{
    struct argo_private_field_description *f;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    INLINE_VOID_INFERENCE(f, zhash_table_lookup(d->described_fields, column_name, strlen(column_name), NULL));
    if (!f) {
        return ARGO_RESULT_NOT_FOUND;
    }
    *column_index = f->index;
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_get_column_type_by_index(struct argo_object *o, int column_index, enum argo_field_data_type *data_type)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) return ARGO_RESULT_BAD_ARGUMENT;
    *data_type = d->description[column_index]->public_description.argo_new_field_type;
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_get_column_is_array_by_index(struct argo_object *o, int column_index, int *is_array)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) return ARGO_RESULT_BAD_ARGUMENT;
    *is_array = d->description[column_index]->public_description.is_array;
    return ARGO_RESULT_NO_ERROR;
}

int argo_object_get_column_count_by_index(struct argo_object *o, int column_index, int *count)
{
    struct argo_structure_description *d;
    struct argo_field_description *fd;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) return ARGO_RESULT_BAD_ARGUMENT;
    fd = &d->description[column_index]->public_description;

    *count = argo_structure_field_get_array_count_by_description(d, fd, o->base_structure_void);

    return ARGO_RESULT_NO_ERROR;
}

enum argo_field_data_type argo_object_get_column_type_by_name(struct argo_object *o, const char *column_name)
{
    struct argo_private_field_description *f;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    INLINE_VOID_INFERENCE(f, zhash_table_lookup(d->described_fields, column_name, strlen(column_name), NULL));
    if (!f) {
        return argo_field_data_type_invalid;
    }
    return f->public_description.argo_new_field_type;
}


char *argo_object_get_type(struct argo_object *o)
{
    char *res = NULL;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d) {
        res = d->type;
    }
    return res;
}

int64_t argo_object_read_request_id(struct argo_object *o)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->reqid_index >= 0) {
        int64_t result;
        if (argo_object_read_int_by_column_index(o, d->reqid_index, &result)) {
            return 0;
        } else {
            return result;
        }
    } else {
        return 0;
    }
}

void argo_object_set_request_id(struct argo_object *o, int64_t value)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->reqid_index >= 0) {
        argo_object_write_int_by_column_index(o, d->reqid_index, value);
    }
}

int argo_object_is_deleted(struct argo_object *o)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->deleted_index >= 0) {
        int64_t result;
        if (argo_object_read_int_by_column_index(o, d->deleted_index, &result)) {
            return 0;
        } else {
            if (result) return 1;
            return 0;
        }
    } else {
        return 0;
    }
}

int64_t argo_object_get_key_int(struct argo_object *o)
{
    int64_t res;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->key_index >= 0) {
        if (argo_object_read_int_by_column_index(o, d->key_index, &res)) {
            return 0;
        } else {
            return res;
        }
    }
    return 0;
}

int64_t argo_object_get_sequence(struct argo_object *o)
{
    int64_t res;
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->sequence_index >= 0) {
        if (argo_object_read_int_by_column_index(o, d->sequence_index, &res)) {
            return 0;
        } else {
            return res;
        }
    }
    return 0;
}


int argo_object_set_sequence(struct argo_object *o, int64_t sequence)
{
    struct argo_structure_description *d;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (d->sequence_index >= 0) {
        return argo_object_write_int_by_column_index(o, d->sequence_index, sequence);
    } else {
        return ARGO_RESULT_BAD_ARGUMENT;
    }
}

int argo_object_write_int_by_column_index(struct argo_object *o, int column_index, int64_t value)
{
    struct argo_structure_description *description;
    struct argo_field_description *fd;
    uint8_t *address;
    argo_read_lock();
    description = argo_global.all_descriptions[o->base_structure_index];
    if ((column_index < 0) ||
        (column_index >= description->description_count)) {
        argo_unlock();
        fprintf(stderr, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    fd = &(description->description[column_index]->public_description);
    argo_unlock();
    address = o->base_structure_data + fd->offset;
    if (fd->is_reference) {
        address = *((uint8_t **)(address));
    }
    argo_write_int(address, value, fd->size);
    return ARGO_RESULT_NO_ERROR;
}

void argo_structure_write_int_by_column_index(struct argo_structure_description *description,
                                              void *structure_data,
                                              int column_index,
                                              int64_t value)
{
    struct argo_field_description *fd;
    uint8_t *address;
    fd = &(description->description[column_index]->public_description);
    address = ((uint8_t *)structure_data) + fd->offset;
    if (fd->is_reference) {
        address = *((uint8_t **)(address));
    }
    argo_write_int(address, value, fd->size);
    return;
}

int argo_object_allocate_and_dump(struct argo_object *o, char **destination, size_t * written_size, int pretty)
{
    int res;
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf;
    struct argo_structure_description *description;
    size_t written;

    if (!o || !destination) return ARGO_RESULT_BAD_ARGUMENT;

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    argo_read_lock();
    description = argo_global.all_descriptions[o->base_structure_index];
    res = argo_serialize_structure_json(description,
                                        description->type,
                                        o->base_structure_data,
                                        &out_buf,
                                        0,
                                        0,
                                        description->static_description_count + o->excess_description_count,
                                        pretty ? argo_serialize_json_pretty : argo_serialize_json_no_newline,
                                        1,
                                        1,
                                        0);
    if (!res) {
        size_t output_size = argo_buf_size(&output_buffer);
        /* **NEVER** ARGO_ALLOC in this place because we need caller un-constrained while freeing */
        char *output = calloc(output_size + 1, sizeof(char));
        if (output) {
            res = argo_buf_copy_out(&output_buffer, output, output_size, &written);
            if (res) {
                free(output); //because we calloced above
            } else {
                output[written] = 0;
                if (written_size) *written_size = written;
                *destination = output; //Caller to free this memory
            }
        }
    }
    argo_buf_free_all(output_buffer.next);
    argo_unlock();
    return res;
}

void argo_object_deallocate_dump(char *mem)
{
        /* **NEVER** ARGO_FREE in this place because we called plain malloc while allocating */
    if (mem) free(mem);
}

int argo_object_dump(struct argo_object *o, char *destination, size_t destination_size, size_t *written_size, int pretty)
{
    int res;
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf;
    struct argo_structure_description *description;
    size_t written;

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    argo_read_lock();
    description = argo_global.all_descriptions[o->base_structure_index];
    res = argo_serialize_structure_json(description,
                                        description->type,
                                        o->base_structure_data,
                                        &out_buf,
                                        0,
                                        0,
                                        description->static_description_count + o->excess_description_count,
                                        pretty ? argo_serialize_json_pretty : argo_serialize_json_no_newline,
                                        1,
                                        1,
                                        20000);
    if (!res) {
        res = argo_buf_copy_out(&output_buffer, destination, destination_size - 1, &written);
        if (res) fprintf(stderr, "%s:%s:%d: Object of type [%s] could not be JSON serialized in supplied buffer of size %zu\n",
                                __FILE__, __FUNCTION__, __LINE__, description->type, destination_size);
        destination[written] = 0;
        if (written_size) *written_size = written;
    }
    argo_buf_free_all(output_buffer.next);
    argo_unlock();
    return res;
}

char *argo_object_dump_allocated(struct argo_object *o, int pretty)
{
    int res;
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf;
    struct argo_structure_description *description;
    char *out_buffer = NULL;

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    description = argo_global.all_descriptions[o->base_structure_index];
    res = argo_serialize_structure_json(description,
                                        description->type,
                                        o->base_structure_data,
                                        &out_buf,
                                        0,
                                        0,
                                        description->static_description_count + o->excess_description_count,
                                        pretty ? argo_serialize_json_pretty : argo_serialize_json_no_newline,
                                        1,
                                        1,
                                        20000);
    if (!res) {
        size_t len = argo_buf_size(&output_buffer);
        out_buffer = ARGO_ALLOC(len + 1);
        res = argo_buf_copy_out(&output_buffer, out_buffer, len, NULL);
        if (res) fprintf(stderr, "%s:%s:%d: Object of type [%s] could not be JSON serialized in supplied buffer of size %zu\n",
                                __FILE__, __FUNCTION__, __LINE__, description->type, len);
        out_buffer[len] = 0;
    }
    argo_buf_free_all(output_buffer.next);
    return out_buffer;
}


char *argo_object_dump_inline(struct argo_object *o, char *destination, size_t destination_length, int pretty)
{
    int res;
    res = argo_object_dump(o, destination, destination_length, NULL, pretty);
    if (res) return "object_dump failed";
    return destination;
}

int argo_structure_dump(struct argo_structure_description *description,
                        void *structure_data,
                        char *destination,
                        size_t destination_size,
                        size_t *written_size,
                        int pretty)
{
    int res;
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    struct argo_buf *out_buf;
    size_t written;

    /* Quick buffer initialization. */
    out_buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    argo_read_lock();
    res = argo_serialize_structure_json(description,
                                        description->type,
                                        structure_data,
                                        &out_buf,
                                        0,
                                        0,
                                        description->static_description_count,
                                        pretty ? argo_serialize_json_pretty : argo_serialize_json_no_newline,
                                        1,
                                        1,
                                        4000);
    if (!res) {
        res = argo_buf_copy_out(&output_buffer, destination, destination_size - 1, &written);
        if (res) fprintf(stderr, "%s:%s:%d: Object of type [%s] could not be JSON serialized in supplied buffer of size %zu\n",
                                __FILE__, __FUNCTION__, __LINE__, description->type, destination_size);
        destination[written] = 0;
        if (written_size) *written_size = written;
    }
    argo_buf_free_all(output_buffer.next);
    argo_unlock();
    return res;
}

char *argo_structure_dump_allocated_no_header(struct argo_structure_description *description,
                                              void *structure_data,
                                              int pretty)
{
    int res;
    /* temp output buffer used for argo buf allocation */
    struct argo_buf output_buffer;
    uint8_t buffer_data[ARGO_BUF_DEFAULT_SIZE];
    /* buffer being returned, it should have memory allocated and data copied in it */
    char *ret_buffer = NULL;

    /* Quick buffer initialization. */
    struct argo_buf *buf;
    buf = argo_buf_create(8, &output_buffer, buffer_data, sizeof(buffer_data));

    argo_read_lock();
    res = argo_serialize_structure_json(description,
                                        description->type,
                                        structure_data,
                                        &buf,
                                        0,
                                        0,
                                        description->static_description_count,
                                        pretty ? argo_serialize_json_pretty : argo_serialize_json_no_newline,
                                        1,
                                        0,/*do not encode type*/
                                        4000);
    if (!res) {
        size_t len = argo_buf_size(&output_buffer);
        ret_buffer = ARGO_ALLOC(len + 1);
        res = argo_buf_copy_out(&output_buffer, ret_buffer, len, NULL);
        if (res) _ARGO_LOG(AL_ERROR, "%s:%s:%d:%s: Object of type [%s] could not be JSON serialized in supplied buffer of size %zu\n",
                                __FILE__, __FUNCTION__, __LINE__, argo_result_string(res), description->type, len);
        ret_buffer[len] = 0;
    }
    argo_buf_free_all(output_buffer.next);
    argo_unlock();
    return ret_buffer;
}

static void argo_add_64(void *dst, void *src)
{
    *(uint64_t *)dst += *(uint64_t *)src;
}

static void argo_add_32(void *dst, void *src)
{
    *(uint32_t *)dst += *(uint32_t *)src;
}

static void argo_add_16(void *dst, void *src)
{
    *(uint16_t *)dst += *(uint16_t *)src;
}

static void argo_add_8(void *dst, void *src)
{
    *(uint8_t *)dst += *(uint8_t *)src;
}

static inline void argo_structure_array_add_field(
        void (*argo_add_fnptr)(void *, void *),
        void *dst,
        void *src_start_addr,
        size_t src_struct_size,
        size_t field_offset,
        uint32_t src_cnt)
{
    uint8_t *src;
    size_t i;

    for (i = 0; i < src_cnt; i++) {
        src = (uint8_t *)src_start_addr + (i * src_struct_size);
        src += field_offset;
        argo_add_fnptr(dst, src);
    }
    return;
}

int argo_structure_array_add(struct argo_structure_description *description,
                             void *objs,
                             uint32_t obj_cnt,
                             void *result_obj,
                             size_t result_size)
{
    struct argo_field_description *field_desc;
    void *dst;
    size_t struct_size;
    size_t i;

    struct_size = description->structure_size;

    if (result_size < struct_size) {
        _ARGO_LOG(AL_ERROR, "result_obj size is lesser than struct_size");
        return ARGO_RESULT_BAD_ARGUMENT;
    }

    memset(result_obj, 0, result_size);

    for (i  = 0; i < description->field_count; i++) {

        field_desc = &(description->description[i]->public_description);

        if (field_desc->argo_field_type != argo_field_data_type_integer) {
            _ARGO_LOG(AL_WARNING, "struct contains non-intger field !");
            continue;
        }

        dst = (uint8_t *)result_obj + field_desc->offset;
        switch (field_desc->size) {
        case 8:
            argo_structure_array_add_field(argo_add_64, dst, objs,
                                           struct_size, field_desc->offset, obj_cnt);
            break;
        case 4:
            argo_structure_array_add_field(argo_add_32, dst, objs,
                                           struct_size, field_desc->offset, obj_cnt);
            break;
        case 2:
            argo_structure_array_add_field(argo_add_16, dst, objs,
                                           struct_size, field_desc->offset, obj_cnt);
            break;
        case 1:
            argo_structure_array_add_field(argo_add_8, dst, objs,
                                           struct_size, field_desc->offset, obj_cnt);
            break;
        default:
            _ARGO_LOG(AL_WARNING, "Unexpected size!");
            break;
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

void argo_hexdump(const uint8_t *data, size_t len, size_t newline_pad)
{
    argo_hexdump_file(stderr, data, len, newline_pad);
}

void argo_hexdump_file(FILE *fp, const uint8_t *data, size_t len, size_t newline_pad)
{
    const uint8_t *s = data;
    const uint8_t *e = data + len;
    int width;
    int i;

    fprintf(fp, "hexdump\n");


    if (s == e) fprintf(fp, "\n");

    while (s < e) {
        width = 16;
        if ((s + width) > e) width = e-s;

        for (i = 0; i < width; i++) {
            fprintf(fp, "%02x ", s[i]);
            if (i == 7) fprintf(fp, " ");
        }
        for (;i < 16; i++) {
            fprintf(fp, "   ");
            if (i == 7) fprintf(fp, " ");
        }

        for (i = 0; i < width; i++) {
            fprintf(fp, "%c", isprint(s[i]) ? s[i]:'.');
        }
        fprintf(fp, "\n");
        s += width;
        if (s < e) {
            fprintf(fp, "%*.s", (int)newline_pad, " ");
        }
    }
}

void argo_hexdump_buf_with_offset(const uint8_t *data, size_t len, int64_t offset, size_t newline_pad, char *dest_buf, size_t dest_buf_len)
{
    const uint8_t *s = data;
    const uint8_t *e = data + len;
    char *os = dest_buf;
    char *oe = os + dest_buf_len;
    int width;
    int i = 0;

    if (s == e) os += sxprintf(os, oe, "\n");

    while (s < e) {
        width = 16;
        if ((s + width) > e) width = e-s;

        os += sxprintf(os, oe, "%08lx: ", offset + s + i - data);

        for (i = 0; i < width; i++) {
            os += sxprintf(os, oe, "%02x ", s[i]);
            if (i == 7) os += sxprintf(os, oe, " ");
        }
        for (;i < 16; i++) {
            os += sxprintf(os, oe, "   ");
            if (i == 7) os += sxprintf(os, oe, " ");
        }

        for (i = 0; i < width; i++) {
            os += sxprintf(os, oe, "%c", isprint(s[i]) ? s[i]:'.');
        }
        os += sxprintf(os, oe, "\n");
        s += width;
        if (s < e) {
            os += sxprintf(os, oe, "%*.s", (int)newline_pad, " ");
        }
    }
}

void argo_hexdump_buf(const uint8_t *data, size_t len, size_t newline_pad, char *dest_buf, size_t dest_buf_len)
{
    argo_hexdump_buf_with_offset(data, len, 0, newline_pad, dest_buf, dest_buf_len);
}

int argo_string_to_inet(const char *in_str, struct argo_inet *inet)
{
    char tmp[ARGO_INET_ADDRSTRLEN + 1];
    char *walk;
    char *end;
    const char *str = in_str;

    const char *slash = NULL;
    unsigned int netmask = 0;
    int saw_dot = 0;
    int saw_colon = 0;

    /* If no address at all is passed in, just zero out inet. */
    if (!(*str)) {
        memset(inet, 0, sizeof(*inet));
        return ARGO_RESULT_NO_ERROR;
    }

    /* Scan string to find whether it is likely IPv4 or IPv6, and to
     * find the netmask (if it exists) at the end */

    walk = tmp;
    end = walk + sizeof(tmp) - 1;

    /* Make sure tmp is always null terminated */
    tmp[ARGO_INET_ADDRSTRLEN] = '\0';

    while (walk < end) {
        if (slash) {
            if ((*str) && (!isdigit(*str))) {
                //fprintf(stderr, "%s:%d: Could not parse IP address <%s>(%s)\n", __FILE__, __LINE__, in_str, str);
                return ARGO_RESULT_BAD_DATA;
            }
        }
        if (*str == '/') {
            *walk = 0;
            slash = str + 1;
        } else {
            *walk = *str;
        }
        if (!*str) break;
        if (*str == '.') saw_dot = 1;
        if (*str == ':') saw_colon = 1;

        walk++;
        str++;
    }

    if (slash) {
        if (!(*slash)) {
            //fprintf(stderr, "%s:%d: Could not parse empty netmask <%s>\n", __FILE__, __LINE__, in_str);
            return ARGO_RESULT_BAD_DATA;
        }
        netmask = atoi(slash);
    }

    /* IPv4 + IPv6 in same address == bad */
    if (saw_dot || saw_colon) {
        if (saw_colon) {
            /* IPv6 (Note: Supports IPv4 in IPv6) */
            if (!inet_pton(AF_INET6, tmp, &(inet->address[0]))) {
                //fprintf(stderr, "%s:%d: Could not parse IP address <%s>\n", __FILE__, __LINE__, tmp);
                return ARGO_RESULT_BAD_DATA;
            }
            if (!slash) netmask = 128;
            if (netmask > 128) {
                //fprintf(stderr, "%s:%d: Bad netmask > 128 on IP address <%s>\n", __FILE__, __LINE__, in_str);
                return ARGO_RESULT_BAD_DATA;
            }
            inet->length = 16;
        } else {
            /* IPv4 */
            if (!inet_pton(AF_INET, tmp, &(inet->address[0]))) {
                //fprintf(stderr, "%s:%d: Could not parse IP address <%s>\n", __FILE__, __LINE__, tmp);
                return ARGO_RESULT_BAD_DATA;
            }
            if (!slash) netmask = 32;
            if (netmask > 32) {
                //fprintf(stderr, "%s:%d: Bad netmask > 32 on IP address <%s>\n", __FILE__, __LINE__, in_str);
                return ARGO_RESULT_BAD_DATA;
            }
            inet->length = 4;
            /* Clear out the tail end of IPv4 address so that it's
             * null. Just keeps everyone a little happier. */
            memset(&(inet->address[4]), 0, 12);
        }
        inet->netmask = netmask;
    } else {
        /* Unrecognized. */
        //fprintf(stderr, "%s:%d: Could not parse IP address <%s>\n", __FILE__, __LINE__, tmp);
        return ARGO_RESULT_BAD_DATA;
    }

    return ARGO_RESULT_NO_ERROR;
}


int argo_binary_to_inet(struct argo_inet *dst_inet, uint8_t *addr, size_t addr_len) {
    if (addr_len == 4) {
        /* IPv4 */
	    memset(dst_inet, 0, sizeof(*dst_inet));
        dst_inet->length = 4;
        dst_inet->netmask = 32;
        memcpy(dst_inet->address, addr, 4);
    } else if (addr_len == 16) {
        /* IPv6 */
        dst_inet->length = 16;
	    dst_inet->netmask = 128;
        memcpy(dst_inet->address, addr, 16);
    } else {
	    return ARGO_RESULT_BAD_DATA;
    }

    return ARGO_RESULT_NO_ERROR;
}


int argo_json_inet_generate(char **out, size_t out_len, const struct argo_inet *inet, int include_quotes)
{
    char tmp[INET6_ADDRSTRLEN + 7]; /* + 4 to include up to '/128' and leading/trailing '"', and one more in case NULL isn't counted */

    char *s = tmp;
    char *e = s + sizeof(tmp);

    if (!inet) {
        sxprintf(s, e, "(null)");
        return ARGO_RESULT_NO_ERROR;
    }

    if (include_quotes) s += sxprintf(s, e, "\"");

    if (inet->length == 4) {
        if (!inet_ntop(AF_INET, &(inet->address[0]), s, e - s)) {
            fprintf(stdout, "%s:%d: Could not write IP address\n", __FILE__, __LINE__);
            return ARGO_RESULT_BAD_DATA;
        }
        /* Yay for running over data, AGAIN */
        s = tmp + strlen(tmp);
        if (inet->netmask < 32) {
            if (include_quotes) {
                s += sxprintf(s, e, "/%d\"", inet->netmask);
            } else {
                s += sxprintf(s, e, "/%d", inet->netmask);
            }
        } else if (inet->netmask == 32) {
            if (include_quotes) s += sxprintf(s, e, "\"");
        } else {
            fprintf(stdout, "Err: Bad data, file %s, line %d\n", __FILE__, __LINE__);
            return ARGO_RESULT_BAD_DATA;
        }
    } else if (inet->length == 16) {
        if (!inet_ntop(AF_INET6, &(inet->address[0]), s, e - s)) {
            fprintf(stdout, "%s:%d: Could not write IP address\n", __FILE__, __LINE__);
            return ARGO_RESULT_BAD_DATA;
        }
        /* Yay for running over data, AGAIN */
        s = tmp + strlen(tmp);
        if (inet->netmask < 128) {
            if (include_quotes) {
                s += sxprintf(s, e, "/%d\"", inet->netmask);
            } else {
                s += sxprintf(s, e, "/%d", inet->netmask);
            }
        } else if (inet->netmask == 128) {
            if (include_quotes) s += sxprintf(s, e, "\"");
        } else {
            fprintf(stdout, "Err: Bad data, file %s, line %d\n", __FILE__, __LINE__);
            return ARGO_RESULT_BAD_DATA;
        }
    } else if (inet->length == 0) {
        if (include_quotes) s += sxprintf(s, e, "\"");
    } else {
        fprintf(stdout, "Err: Bad data, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_DATA;
    }

    /* Ensure null termination. */
    *s = 0;

    if ((1 + s - tmp) > out_len) {
        fprintf(stdout, "%s:%s:%d: ERR_TOO_LARGE\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }

    strncpy(*out, tmp, out_len);
    *out += (s - tmp);

    return ARGO_RESULT_NO_ERROR;
}

char *argo_inet_generate(char *dest_str, const struct argo_inet *inet)
{
    char *s = dest_str;
    argo_json_inet_generate(&s, ARGO_INET_ADDRSTRLEN, inet, 0);
    return dest_str;
}

const char *argo_version(void)
{
    return ZPATH_VERSION;
}

void argo_inet_to_sockaddr(struct argo_inet *inet, struct sockaddr *sock, socklen_t *len, uint16_t port_ne)
{
    if (inet->length == 16) {
        struct sockaddr_in6 *sin6 = (struct sockaddr_in6 *) sock;
        sin6->sin6_family = AF_INET6;
        sin6->sin6_port = port_ne;
#ifndef __linux__
        sin6->sin6_len = sizeof(*sin6);
#endif
        if (len) *len = sizeof(*sin6);
        memcpy(&(sin6->sin6_addr),
               &(inet->address[0]),
               16);
    } else if (inet->length == 4) {
        struct sockaddr_in *sin = (struct sockaddr_in *) sock;
        sin->sin_family = AF_INET;
        sin->sin_port = port_ne;
#ifndef __linux__
        sin->sin_len = sizeof (*sin);
#endif
        if (len) *len = sizeof(*sin);
        memcpy(&(sin->sin_addr),
               &(inet->address[0]),
               4);
    }
}

int argo_inet_is_cidr(const struct argo_inet *inet)
{
    if (inet->length == 4) {
        if (inet->netmask < 32) return 1;
    } else if (inet->length == 16) {
        if (inet->netmask < 128) return 1;
    }
    return 0;
}

/*
 * I am treating loopback as private, don't feel like creating another function for it
 */
int argo_inet_is_private(struct argo_inet *inet)
{
    if (inet->length == 4) {
        if ((inet->address[0] == 10) ||
            ((inet->address[0] == 192) && (inet->address[1] == 168)) ||
            ((inet->address[0] == 172) && (inet->address[1] >= 16) && (inet->address[1] <= 31)) ||
            ((inet->address[0] == 127) && (inet->address[1] == 0) && (inet->address[2] == 0) && (inet->address[3] == 1))) {
            return 1;
        }
    }

    return 0;
}

int argo_inet_is_ipv4_any(struct argo_inet *inet)
{
    if ((inet->length == 4) &&
        (inet->netmask == 32) &&
        (inet->address[0] == 0) &&
        (inet->address[1] == 0) &&
        (inet->address[2] == 0) &&
        (inet->address[3] == 0)) {
        return 1;
    }
    return 0;
}

int argo_inet_is_ipv6_any(struct argo_inet *inet)
{
    if ((inet->length == 16) &&
        (inet->netmask == 128) &&
        (inet->address[0] == 0) &&
        (inet->address[1] == 0) &&
        (inet->address[2] == 0) &&
        (inet->address[3] == 0) &&
        (inet->address[4] == 0) &&
        (inet->address[5] == 0) &&
        (inet->address[6] == 0) &&
        (inet->address[7] == 0) &&
        (inet->address[8] == 0) &&
        (inet->address[9] == 0) &&
        (inet->address[10] == 0) &&
        (inet->address[11] == 0) &&
        (inet->address[12] == 0) &&
        (inet->address[13] == 0) &&
        (inet->address[14] == 0) &&
        (inet->address[15] == 0)) {
        return 1;
    }
    return 0;
}

/*
 * return 1 when child is contained within or equal to parent, 0 otherwise
 */
int argo_inet_is_contained(struct argo_inet *parent, struct argo_inet *child)
{
    int whole_bytes;
    int remaining_bits;

    if (parent->length != child->length) {
        /*
         * obvious case of failure - different IP versions
         */
        return 0;
    }

    if (parent->netmask > child->netmask) {
        /*
         * obvious case of failure - where child is too big to be contained in parent
         */
        return 0;
    }

    whole_bytes = parent->netmask >> 3;
    remaining_bits = parent->netmask & 0x7;

    /*
     * for eg, if the prefix len is 83, first memcmp the first 10 octets.
     */
    if (whole_bytes) {
        if (memcmp(parent->address, child->address, whole_bytes)) {
            return 0;
        }
    }

    /*
     * for eg, if the prefix len is 83, check the last 3 bits.
     */
    if (remaining_bits) {
        uint8_t     mask = (uint8_t)((0xff) << (8 - remaining_bits));
        if ((parent->address[whole_bytes] ^ child->address[whole_bytes]) & mask) {
            return 0;
        }
    }

    return 1;
}


/*
 * return 1 when inet1 is same as inet2. 0 otherwise
 */
int argo_inet_is_same(const struct argo_inet *inet1, const struct argo_inet *inet2)
{
    int whole_bytes;
    int remaining_bits;

    if (!inet1 || !inet2) return 0;

    if (inet1->length != inet2->length) {
        /*
         * obvious case of failure - different IP versions
         */
        return 0;
    }

    if (inet1->netmask != inet2->netmask) {
        /*
         * obvious case of failure - mask of both inets differ.
         */
        return 0;
    }

    whole_bytes = inet1->netmask >> 3;
    remaining_bits = inet1->netmask & 0x7;

    /*
     * for eg, if the prefix len is 83, first memcmp the first 10 octets.
     */
    if (whole_bytes) {
        if (memcmp(inet1->address, inet2->address, whole_bytes)) {
            return 0;
        }
    }

    /*
     * for eg, if the prefix len is 83, check the last 3 bits.
     */
    if (remaining_bits) {
        uint8_t     mask = (uint8_t)((0xff) << (8 - remaining_bits));
        if ((inet1->address[whole_bytes] ^ inet2->address[whole_bytes]) & mask) {
            return 0;
        }
    }

    return 1;
}

static int argo_inet_is_lt_internal(const struct argo_inet *lhs, const struct argo_inet *rhs)
{
    const int lhs_whole_bytes = lhs->netmask >> 3;
    const int rhs_whole_bytes = rhs->netmask >> 3;
    const int lhs_remaining_bits = lhs->netmask & 0x7;
    const int rhs_remaining_bits = rhs->netmask & 0x7;
    uint8_t lhs_byte, rhs_byte;

    for (int i = 0; i < lhs->length; ++i) {
        if (i < lhs_whole_bytes) {
            lhs_byte = lhs->address[i];
        } else if (i == lhs_whole_bytes && lhs_remaining_bits) {
            lhs_byte = lhs->address[i] & (uint8_t)((0xff) << (8 - lhs_remaining_bits));
        } else {
            lhs_byte = 0;
        }

        if (i < rhs_whole_bytes) {
            rhs_byte = rhs->address[i];
        } else if (i == rhs_whole_bytes && rhs_remaining_bits) {
            rhs_byte = rhs->address[i] & (uint8_t)((0xff) << (8 - rhs_remaining_bits));
        } else {
            rhs_byte = 0;
        }

        if (lhs_byte == rhs_byte) {
            continue;
        }

        return lhs_byte < rhs_byte;
    }

    return 0;
}

/* Return 1 when lhs < rhs. 0 otherwise */
int argo_inet_is_lt(const struct argo_inet *lhs, const struct argo_inet *rhs)
{
    if (!lhs || !rhs ||                 /* null pointer sanity */
        lhs->length != rhs->length) {   /* obvious case of failure - different IP versions */
        return 0;
    }

    return argo_inet_is_lt_internal(lhs, rhs);
}

/* Return 1 when lhs <= rhs. 0 otherwise */
int argo_inet_is_le(const struct argo_inet *lhs, const struct argo_inet *rhs)
{
    if (!lhs || !rhs ||                 /* null pointer sanity */
        lhs->length != rhs->length) {   /* obvious case of failure - different IP versions */
        return 0;
    }

    return !argo_inet_is_lt_internal(rhs, lhs);
}

/* Return 1 when lhs > rhs. 0 otherwise */
int argo_inet_is_gt(const struct argo_inet *lhs, const struct argo_inet *rhs)
{
    if (!lhs || !rhs ||                 /* null pointer sanity */
        lhs->length != rhs->length) {   /* obvious case of failure - different IP versions */
        return 0;
    }

    return argo_inet_is_lt_internal(rhs, lhs);
}

/* Return 1 when lhs >= rhs. 0 otherwise */
int argo_inet_is_ge(const struct argo_inet *lhs, const struct argo_inet *rhs)
{
    if (!lhs || !rhs ||                 /* null pointer sanity */
        lhs->length != rhs->length) {   /* obvious case of failure - different IP versions */
        return 0;
    }

    return !argo_inet_is_lt_internal(lhs, rhs);
}

/* Return 1 if all the bytes in inet->address are 0xff. 0 otherwise */
int argo_inet_is_max(const struct argo_inet *inet)
{
    if (!inet) {
        return 0;
    }

    uint8_t address[16];
    memset(address, 0xff, sizeof(address));
    return memcmp(address, inet->address, inet->length) == 0;
}

/* Return 1 if all the bytes in inet->address are 0x00. 0 otherwise */
int argo_inet_is_min(const struct argo_inet *inet)
{
    if (!inet) {
        return 0;
    }

    uint8_t address[16];
    memset(address, 0, sizeof(address));
    return memcmp(address, inet->address, inet->length) == 0;
}

/* Increment inet->address */
int argo_inet_inc(struct argo_inet *inet)
{
    if (!inet || argo_inet_is_max(inet)) {
        return ARGO_RESULT_BAD_ARGUMENT;
    }

    for (int i = inet->length - 1; i >= 0; i--) {
        if (inet->address[i] != 0xff) {
            inet->address[i]++;
            break;
        }
        inet->address[i] = 0;
    }

    return ARGO_RESULT_NO_ERROR;
}

/* Decrement inet->address */
int argo_inet_dec(struct argo_inet *inet)
{
    if (!inet || argo_inet_is_min(inet)) {
        return ARGO_RESULT_BAD_ARGUMENT;
    }

    for (int i = inet->length - 1; i >= 0; i--) {
        if (inet->address[i] != 0) {
            inet->address[i]--;
            break;
        }
        inet->address[i] = 0xff;
    }

    return ARGO_RESULT_NO_ERROR;
}

void argo_sockaddr_to_inet(struct sockaddr *sock, struct argo_inet *inet, uint16_t *port_ne)
{
    if (sock->sa_family == AF_INET6) {
        struct sockaddr_in6 *sin6 = (struct sockaddr_in6 *) sock;
        memcpy(&(inet->address[0]),
               &(sin6->sin6_addr),
               16);
        inet->length = 16;
        inet->netmask = 128;
        if (port_ne) (*port_ne) = sin6->sin6_port;
    } else if (sock->sa_family == AF_INET) {
        struct sockaddr_in *sin = (struct sockaddr_in *) sock;
        memcpy(&(inet->address[0]),
               &(sin->sin_addr),
               4);
        memset(&(inet->address[4]),
               0,
               12);
        inet->length = 4;
        inet->netmask = 32;
        if (port_ne) (*port_ne) = sin->sin_port;
    }
}

int argo_structures_defined(void)
{
    return argo_global.all_description_count;
}

void argo_structure_info(int argo_structure_index,
                         char **name,
                         int64_t *bytes_allocated,
                         int64_t *allocations,
                         int64_t *bytes_freed,
                         int64_t *frees)
{
    struct argo_structure_description *desc = argo_global.all_descriptions[argo_structure_index];
    *name = desc->type;
    *bytes_allocated = desc->bytes_allocated;
    *allocations = desc->allocations;
    *bytes_freed = desc->bytes_freed;
    *frees = desc->frees;
}


char *argo_field_data_type_to_str(enum argo_field_data_type t)
{
    switch (t) {
    case argo_field_data_type_invalid:
        return "argo_field_data_type_invalid";
    case argo_field_data_type_object:
        return "argo_field_data_type_object";
    case argo_field_data_type_null:
        return "argo_field_data_type_null";
    case argo_field_data_type_boolean:
        return "argo_field_data_type_boolean";
    case argo_field_data_type_integer:
        return "argo_field_data_type_integer";
    case argo_field_data_type_epoch:
        return "argo_field_data_type_epoch";
    case argo_field_data_type_float:
        return "argo_field_data_type_float";
    case argo_field_data_type_double:
        return "argo_field_data_type_double";
    case argo_field_data_type_string:
        return "argo_field_data_type_string";
    case argo_field_data_type_inet:
        return "argo_field_data_type_inet";
    case argo_field_data_type_binary:
        return "argo_field_data_type_binary";
    case argo_field_data_type_dictionary:
        return "argo_field_data_type_dictionary";
    case argo_field_data_type_argo_object:
        return "argo_field_data_type_argo_object";
    case argo_field_data_type_list:
        return "argo_field_data_type_list";
    default:
        return "invalid";
    }
}

int argo_transmits_binary(struct argo_state *argo)
{
    if (!argo) return 0;
    if (argo->mode == argo_serialize_binary) return 1;
    return 0;
}

int argo_object_read_array_binary_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, uint8_t **value, size_t *length)
{
    struct argo_structure_description *d;
    struct argo_field_description *fd;
    void *where;
    int res;

    d = argo_global.all_descriptions[o->base_structure_index];
    if (column_index >= d->description_count) {
        fprintf(stdout, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }

    fd = &(d->description[column_index]->public_description);
    res = argo2_structure_where(fd, o->base_structure_data, array_index, &where);
    if (res) {
        fprintf(stdout, "Err: Bad argument, file %s, line %d\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_ARGUMENT;
    }
    INLINE_VOID_INFERENCE(*value, where);
    *length = fd->size;

    return ARGO_RESULT_NO_ERROR;
}

int argo_object_read_array_int_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, int64_t *value)
{
    uint8_t *data;
    size_t size;
    int res;

    res = argo_object_read_array_binary_by_column_index(o, column_index, array_index, &data, &size);
    if (res) {
        return res;
    }
    if (!data) {
        *value = 0;
    } else {
        *value = argo_read_int(data, size);
    }

    return ARGO_RESULT_NO_ERROR;
}

int argo_object_read_array_string_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, char **value)
{
    uint8_t *data;
    size_t size;
    int res;

    res = argo_object_read_array_binary_by_column_index(o, column_index, array_index, &data, &size);
    if (res) {
        return res;
    }
    *value = (char *)data;

    return ARGO_RESULT_NO_ERROR;
}

void argo_set_adj_time(int64_t time_us)
{
    global_argo_adj_time_us = time_us;
}

void argo_structure_dump_dynamic_fields(char *buf, size_t len) {
    char *s = buf;
    char *e = s + len;
    if (len) buf[0] = '\0';

    size_t total_count = 0;

    ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__);
    size_t i;
    for (i = 0; i < argo_global.all_description_count; ++i) {
        const struct argo_structure_description *d = argo_global.all_descriptions[i];
        if (d->static_field_count == d->field_count)
            continue;

        s += sxprintf(s, e, "%s\n", d->type);
        size_t j;
        size_t count = 0;
        for (j = d->static_description_count; j < d->description_count; j += 2) {
            count++;
            total_count++;
            const struct argo_field_description *fd = &d->description[j]->public_description;
            s += sxprintf(s, e, " %3zu. %s [%s, ref_count=%zu, is_array=%u]\n", count, fd->field_name,
                          argo_field_data_type_to_str(fd->argo_field_type), fd->reference_count, fd->is_array);
        }
        s += sxprintf(s, e, "\n");
    }
    ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__);

    s += sxprintf(s, e, "Total count = %zu\n", total_count);
}

void argo_structure_set_registration_barrier(int set, argo_structure_registration_barrier_f* cb) {
    argo_structure_registration_barrier_set = set;
    argo_structure_registration_barrier_cb = cb;
}

int log_dump_structure(struct argo_log_collection *collection,
                       struct argo_structure_description *description,
                       void *data,
                       const char *label,
                       const char *conn_desc,
                       const char *module_name,
                       const char *func,
                       const char *file,
                       int line) {
    struct argo_object *object = argo_object_create(description, data);

    char dump[4000] = {0};
    int res = argo_object_dump(object, dump, sizeof(dump), NULL, 0);
    if (res == ARGO_RESULT_NO_ERROR || res == ARGO_RESULT_ERR_TOO_LARGE) {
        res = argo_log_text(collection, argo_log_priority_debug, module_name ?: "", file ?: "", func ?: "", line, "%s %s %s",
                            label ?: "", dump, conn_desc ?: "");
    }

    argo_object_release(object);

    return res;
}
int log_dump_argo(struct argo_log_collection *collection,
                  struct argo_object *object,
                  const char *label,
                  const char *label2,
                  const char *conn_desc,
                  const char *module_name,
                  const char *func,
                  const char *file,
                  int line) {
    char buf[4000];
    int res = 0;
    res = argo_object_dump(object, buf, sizeof(buf), NULL, Z_FALSE);
    if (res == ARGO_RESULT_NO_ERROR) {
        res = argo_log_text(collection, argo_log_priority_debug, module_name, file, func, line, "%s %s %s (%s)", label ?: "",
                            buf, label2 ?: "", conn_desc ?: "");
    }
    return res;
}

/* callback for argo*/
static int argo_read_from_json_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    (void)argo_structure_cookie_ptr;
    struct st_argo_holder *holder = argo_structure_cookie_ptr;
    if (holder) {
        holder->object = argo_object_copy(object);
    }
    return ARGO_RESULT_NO_ERROR;
}

/* read single argo RPC from json file */
int argo_read_from_json(const char *file_path,
                        struct argo_structure_description *description,
                        argo_structure_callback_f *callback,
                        struct st_argo_holder *holder,
                        size_t max_size,
                        struct argo_log_collection *collection) {
#define DEFAULT_BUFFER_SIZE (64 * 1024)
    int res = ARGO_RESULT_NO_ERROR;
    size_t len = 0;
    char *file_data = NULL;

    if (0 == max_size) {
        max_size = DEFAULT_BUFFER_SIZE;
    }

    file_data = ARGO_CALLOC(max_size);

    if (!file_data)
        return ARGO_RESULT_NO_MEMORY;

    FILE *fp = fopen(file_path, "r");
    if (!fp) {
        res = ARGO_RESULT_NOT_FOUND;
        goto END;
    }

    len = fread(file_data, 1, max_size, fp);
    fclose(fp);

    if (len >= max_size || len <= 0) {
        res = ARGO_RESULT_ERR_TOO_LARGE;
        goto END;
    }

    file_data[len] = 0;

    // strip trailing whitespace after last }} or you get ZPATH_RESULT_INSUFFICIENT_DATA parser error
    char *e = file_data + len - 1;
    while (e > file_data && isspace((unsigned char)*e)) {
        e--;
    }
    e[1] = '\0';

    struct argo_state *reader = argo_alloc(NULL, NULL, NULL, NULL, (1024 * 1024), argo_serialize_dont_care, 10, 0, 0);
    if (!reader) {
        res = ARGO_RESULT_NO_MEMORY;
        goto END;
    }
    argo_register_structure(reader, description, callback ?: argo_read_from_json_cb, holder);

    len = strlen(file_data);
    size_t count = 0;
    res = argo_deserialize(reader, file_data, len, &count);

END:

    if (res && collection) {
        argo_log_text(collection, argo_log_priority_error, "argo", __FUNCTION__, __FILE__, __LINE__,
                      "argo_read_from_json error=%s len=%zu '%s'", argo_result_string(res), len, file_data);
    }

    ARGO_FREE(file_data);
    return res;
}
