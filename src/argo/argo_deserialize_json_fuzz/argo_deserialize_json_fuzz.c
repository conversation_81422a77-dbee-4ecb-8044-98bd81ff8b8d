/*
 * argo_deserialize_json_fuzz.c. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved.
 *
 * Fuzzing for argo json deserialize module.
 */

#include <stdlib.h>
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <arpa/inet.h>
#include <ctype.h>
#include "argo/argo.h"
#include "argo/argo_private.h"

#include "argo/argo_test2.h"
#include "argo/argo_test2_compiled.h"

void argo_deserialize_json_fuzzing(const char *idata, size_t idata_size) {
    // Initialize fuzzing environment
    if (argo_library_reinit(1024)) {
        fprintf(stderr, "Error: Argo library init failed\n");
        exit(-1);
    }
    struct argo_structure_description *desc = argo_register_global_structure(TEST_STRUCT_HELPER);
    if (!desc) {
        fprintf(stderr, "Error: Could not register structure\n");
        exit(-1);
    }
    struct argo_structure_description *sub_desc = argo_register_global_structure(SUB_TEST_STRUCT_HELPER);
    if (!sub_desc) {
        fprintf(stderr, "Error: Could not register structure\n");
        exit(-1);
    }
    struct argo_structure_description *super_desc = argo_register_global_structure(SUPER_STRUCT_HELPER);
    if (!super_desc) {
        fprintf(stderr, "Error: Could not register structure\n");
        exit(-1);
    }

    // Perfom JSON deserialization fuzzing
    struct argo_object *obj = argo_deserialize_json(idata, idata_size);
    if (obj) {
        argo_object_release(obj);
    }
}

int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    argo_deserialize_json_fuzzing((const char *)data, size);
    return 0;
}
