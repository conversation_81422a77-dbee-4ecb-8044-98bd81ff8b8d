add_executable(argo_parse argo_parse.c argo_hash.c)
target_link_libraries(argo_parse PRIVATE zpath_misc cityhash-c)

argo_parse_files(
    INPUT_FILES
        agrep_types.h
        argo.h
        argo2_test.c
        argo2_test2_gen.c
        argo_dictionary_test.h
        argo_dns.h
        argo_log.h
        argo_loggen.h
        argo_private.h
        argo_test.h
        argo_test2.h
        argo_test3.h
        argo_test4.c
        argo_test6.c
        coll_basic_2_argo_ut.c
        coll_basic_argo_ut.c
        coll_eviction_proactive_argo_ut.c
        coll_eviction_reactive_argo_ut.c
        et-36902.c
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_library(
    argo
    STATIC
    argo.c
    argo2.c
    argo_buf.c
    argo_encode.c
    argo_dictionary.c
    argo_fieldcheck.c
    argo_hash.c
    argo_list.c
    argo_log.c
    argo_structure.c
    argo_token.c
    ${generated_headers}
)
target_link_libraries(argo PUBLIC zpath_misc base64 zthread zhash)

add_executable(
    agrep
    agrep.c
    argo.c
    argo2.c
    argo_buf.c
    argo_log.c
)
target_compile_definitions(agrep PRIVATE -DARGO_NO_HT -DARGO2_MAX_OBJECT_FIELDS=1024 -DARGO_BUF_DEFAULT_SIZE=32768)
target_link_libraries(agrep PRIVATE argo m)

add_simple_apps(
    SOURCES
        argo2_test.c
        argo2_test2_gen.c
        argo_dictionary_test.c
        argo_loggen.c
        argo_rewrite.c
        argo_test.c
        argo_test2.c
        argo_test3.c
        argo_test4.c
        argo_test5.c
        argo_test6.c
        argo_test_inet_compare.c
        argo_test_lookup.c
        coll_basic_2_argo_ut.c
        coll_basic_argo_ut.c
        coll_eviction_proactive_argo_ut.c
        coll_eviction_reactive_argo_ut.c
        et-27981.c
        et-36902.c
    DEPS argo m
)

if(BUILD_FUZZING)
    add_executable(argo_deserialize_json_fuzz argo_deserialize_json_fuzz/argo_deserialize_json_fuzz.c)
    target_link_libraries(argo_deserialize_json_fuzz PRIVATE argo)
    target_link_options(argo_deserialize_json_fuzz PRIVATE -fsanitize=fuzzer)
endif()

add_simple_tests(
    argo2_test
    argo_test2
    argo_test3
    argo_test4
    argo_test6
    argo_test_inet_compare
    argo_test_lookup
    coll_basic_2_argo_ut
    coll_basic_argo_ut
    et-27981
    et-36902
)

add_rpm(NAME libargo SPEC rpm/argo.spec MAKEFILE Makefile.rpm.argo FILES agrep license.txt)
