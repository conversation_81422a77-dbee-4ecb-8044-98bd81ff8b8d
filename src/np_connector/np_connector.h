/*
 * np_connector.h. Copyright (C) 2024-2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_CONNECTOR_H_
#define _NP_CONNECTOR_H_

#include "argo/argo.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "wally/wally.h"
#include "zcrypt/zcrypt.h"
#include "np_connector/np_connector_stats.h"

/*
 * np connector to send new np connector state via assistant state report
 *
 */
typedef int (np_connector_state_cb_f());

/* Init NP Connector Functionality,
 *  - np config connection, register np_tenant_gateways, np_client_subnets, and np_connectors callbacks.
 *  - np thread
 *
 * customer_gid     : customer gid
 * entity_gid       : parent module gid (app connector gid)
 * np_wally         : wally handler
 */
int np_connector_init(int64_t customer_gid,
                      int64_t entity_gid,
                      struct wally *np_wally,
                      np_connector_state_cb_f *state_update_cb);

/*
 * NP Connector needs to talk to Enrollment Service to push wireguard publi key
 * It needs the credential from APPC to talk enrollment service.
 * APPC will pass those info in following init function call.
 */
void np_connector_enroll_state_init(struct zcrypt_rsa_key *rsa_key,
                                    char *api_hostname,
                                    enum zpn_enrollment_type enroll_type,
                                    enum zpn_enrollment_style enroll_style,
                                    char *cloud,
                                    char *provisioning_key,
                                    char *hw_fingerprint,
                                    struct zcrypt_key* cfg_hw_key);

/*
 * pre initializaiton of np_connector, ie logging lib, allocator allocation.
 * this has to be done at assistant_init time before zpath_debug_mem_stats_init() due to number of allocator has to be set at this time.
 *
 * event_log : log collection that np connector module can write to
 */
int np_connector_pre_init(struct argo_log_collection *event_log);

/*
 * Get existing wireguard online/offline state of NP Connector
 */
int np_connector_get_state();

/*
 * Get BGP state structure, returns NULL if state is uninitialized
 */
struct np_bgp_state* np_connector_get_bgp_state();

/*
 * Get existing wireguard enable/disable state of NP Connector
 */
int np_connector_get_enablement();

/*
 * NP Connector operates in 2 state, enable and disable.
 * This is the public API exposed to AppC to turn on and off NP connector
 *
 * details see assistant_state_set_np_connector_enablement()
 */
int np_connector_enable();
int np_connector_disable();

/*
 * Return if NP Connector library has been previously initailized.
 * this is to avoid duplicate runtime intiialization if a NP Capabiliy is turned on/off multiple times
 */
int np_connector_is_initialized();

/*
 * Get NP Connector GID
 */
int64_t np_connector_get_connector_gid();

/*
 * Get NP Connector wally handle
 */
struct wally *np_connector_get_np_wally();

/*
 * Get NP Connector Group GID
 */
int64_t np_connector_get_connector_group_gid();

/*
 * Get NP Connector Customer GID
 */
int64_t np_connector_get_customer_gid();

/*
 * this api log a message to event.log collection about current np connector state/stats
 * when called
 * */
void np_connector_monitor_log();

/*
 * NP Connector module is wireguard manager such that it should take care of
 * bringing up and terminating wireguard driver if required.
 *
 * In case of Connector process crashes/exits for any reason,
 * this api should be called before the process exit/crash.
 * this api will terminate wireguard driver if it has previously brought up the driver.
 */
void np_connector_terminate_wireguard_before_exit();

const char* np_connector_get_frr_version();

#endif /* _NP_CONNECTOR_H_ */
