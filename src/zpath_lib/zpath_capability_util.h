/*
 * zpath_capability_util.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPATH_CAPABILITY_UTIL_H_
#define _ZPATH_CAPABILITY_UTIL_H_

/*Enum representing the three supported capability selection modes. */
typedef enum {
    CAP_MODE_MINIMAL,
    CAP_MODE_FULL,
    CAP_MODE_CUSTOM
} zpath_capability_mode_t;

/*
 * Returns the current capability mode based on the environment variable
 * ZPA_CAPABILITY_MODE. If not set, defaults to FULL. If invalid defaults to minimal.
 */
zpath_capability_mode_t zpath_get_capability_mode();

/*
 * Parses the ZPA_CUSTOM_CAPABILITIES environment variable, validates and returns
 * a filtered list of allowed custom capabilities. If the variable is empty or invalid,
 * defaults to the minimal capability set.
 *
 * Parameters:
 * - count: Pointer to store the number of returned capabilities.
 *
 * Returns:
 * - An array of validated capability strings (e.g., "cap_net_raw").
 */
const char **zpath_get_custom_capabilities(int *count);

/*
 * Determines the effective capability list based on the selected mode (env driven).
 *
 * Parameters:
 * - count: Pointer to store the number of capabilities returned.
 *
 * Returns:
 * - Array of capability strings according to the resolved mode.
 */
const char * const *zpath_get_final_capabilities(int *count);

/*
 * Builds a comma-separated string of capabilities from the input list, appending "+pe"
 * at the end. If in container mode, skips restricted caps (like cap_sys_boot).
 *
 * Parameters:
 * - cap_list: List of capabilities.
 * - count: Number of capabilities in the list.
 * - cap_string: Output buffer to hold the formatted string.
 * - cap_string_size: Size of the output buffer.
 * - is_container_env: Flag to indicate container environment.
 */
void zpath_build_cap_string(const char * const *cap_list, int count, char *cap_string, size_t cap_string_size, int is_container_env);

const char *zpath_mode_to_string(zpath_capability_mode_t mode);
void zpath_log_final_caps(zpath_capability_mode_t mode, const char * const *cap_list, int cap_count);

#endif /* _ZPATH_CAPABILITY_UTIL_H_ */
