/*
 * zpath_debug.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved
 */


/*
 * zpath_debug is set up to allow querying/setting information in a
 * running system. It uses HTTP (local requests only) with a very
 * simple format.
 *
 * "commands" are simply the path part of a URI.
 * The "query" part of the URI represent arguments.
 *
 * Results are generally returned as one or more JSON objects.
 *
 * Commands that should "set" things all use "put" rather than "get".
 *
 * All commands are created via path+query strings ONLY.
 *
 *
 * When registering a command, the set of allowed (and required) query
 * strings are registered, allowing much argument checking to be done
 * by the debug interface itself.
 */

#ifndef _ZPATH_DEBUG_H_
#define _ZPATH_DEBUG_H_

#include "argo/argo.h"
#include "argo/argo_log.h"
#include "fohh/fohh.h"
#include <stdint.h>
#include <stdlib.h>
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_app.h"
#include <pwd.h>
#include <grp.h>
#include <unistd.h>
#include <sys/types.h>

/* Please tune/refine this timing as and when we get more clarity on the kind of issues that we get */
/* 30 mins */
#define  MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC                 ((int64_t)(30ll * 60ll * 1000ll * 1000ll))
/* 12 hrs  */
#define  ASSISTANT_MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC       ((int64_t)(12ll * 60ll * 60ll * 1000ll * 1000ll))
/* 60 mins */
#define  MEMORY_STATS_TX_MEM_USAGE_ALLOCATOR_TIMEPERIOD_USEC               ((int64_t)(60ll * 60ll * 1000ll * 1000ll))
/* 5 mins  */
#define  PBROKER_MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC         ((int64_t)(15ll * 60ll * 1000ll * 1000ll))

#define SYSTEM_MEM_API_WARN_TIME_SEC    7
#define US_PER_MSEC                     1000l

/* Surely we won't have more than 4000 commands, right? */
#define ZPATH_DEBUG_MAX_COMMANDS 4000
#define ZPATH_DEBUG_MAX_COMMANDS_WARNING ((ZPATH_DEBUG_MAX_COMMANDS) - 100)

/* define all the role names here */
//These are the ones initiated by app_init
#define ZPATH_ROLE_BLOCKD               "blockd"
#define ZPATH_ROLE_EXPORTER             "exporter"
#define ZPATH_ROLE_GRIDLOCK             "gridlock"
#define ZPATH_ROLE_LOOKUP               "lookup"
#define ZPATH_ROLE_REBOUND              "rebound"
#define ZPATH_ROLE_SHIFTY               "shifty"
#define ZPATH_ROLE_SLOGGER              "slogger"
#define ZPATH_ROLE_UATU                 "uatu"
#define ZPATH_ROLE_WALLYD               "wallyd"
#define ZPATH_ROLE_ZMESHD               "zmeshd"
#define ZPATH_ROLE_ZPN_BROKERD          "zpn_brokerd"
#define ZPATH_ROLE_ZPN_DISPATCHER       "zpn_dispatcher"
#define ZPATH_ROLE_ZPN_IPARSD           "zpn_iparsd"
#define ZPATH_ROLE_ZPN_L4PROXYD         "zpn_l4proxyd"
#define ZPATH_ROLE_ZPN_NPGATEWAYD       "zpn_npgatewayd"

//These are the ones initiated by simple_app_init
#define ZPATH_ROLE_NATURAL                  "natural"
#define ZPATH_ROLE_ZPA_CONNECTOR_CHILD      "zpa-connector-child"
#define ZPATH_ROLE_ZPA_SERVICE_EDGE_CHILD   "zpa-service-edge-child"
#define ZPATH_ROLE_ZPA_PCC_CHILD            "zpa-pcc-child"


/*
 * ZPATH_DEBUG_BYTES is the number of bytes that we limit on
 * displaying debug information for keys, etc.
 *
 * This value must be small enough in production that we are missing
 * the MAJORITY of the key. If set larger it can show complete keys
 * for stronger debugging/vastly poorer security.
 */
#define ZPATH_DEBUG_BYTES 20

#ifndef ZDP
#define ZDP(format...) zpath_debug_cb_printf_response(request_state, ##format)
#endif

extern struct fohh_http_server *zpath_debug_http_server;
struct fohh_http_server *zpath_debug_get_http_server(void);

extern uint64_t zpath_debug;
extern char *debug_port_bind_ip;  // Override this before
                                  // zpath_debug_init if you want to
                                  // bind to a different IP than
                                  // 127.0.0.1


#define ZPATH_DEBUG_INSTANCE_BIT               (uint64_t)0x0000000001
#define ZPATH_DEBUG_POLICY_BIT                 (uint64_t)0x0000000002
#define ZPATH_DEBUG_RULE_BIT                   (uint64_t)0x0000000002  // Yes, repeated on purpose.
#define ZPATH_DEBUG_LIMIT_BIT                  (uint64_t)0x0000000004
#define ZPATH_DEBUG_DEBUG_BIT                  (uint64_t)0x0000000008
#define ZPATH_DEBUG_MESSAGE_BIT                (uint64_t)0x0000000010
#define ZPATH_DEBUG_QUERY_BIT                  (uint64_t)0x0000000020
#define ZPATH_DEBUG_ZURLDB_BIT                 (uint64_t)0x0000000040
#define ZPATH_DEBUG_LOCAL_BIT                  (uint64_t)0x0000000080
#define ZPATH_DEBUG_IP_ENTITY_BIT              (uint64_t)0x0000000100
#define ZPATH_DEBUG_ENTITY_BIT                 (uint64_t)0x0000000200
#define ZPATH_DEBUG_CUSTOMER_LOG_CONFIG_BIT    (uint64_t)0x0000000400
#define ZPATH_DEBUG_CONSTELLATION_BIT          (uint64_t)0x0000000800
#define ZPATH_DEBUG_DOMAIN_LOOKUP_BIT          (uint64_t)0x0000001000
#define ZPATH_DEBUG_DOMAINLIST_BIT             (uint64_t)0x0000002000
#define ZPATH_DEBUG_CUSTOMER_NOTIFICATION_BIT  (uint64_t)0x0000004000
#define ZPATH_DEBUG_CATEGORY_BIT               (uint64_t)0x0000008000
#define ZPATH_DEBUG_CLOUD_BIT                  (uint64_t)0x0000010000
#define ZPATH_DEBUG_IP_LOCATION_BIT            (uint64_t)0x0000020000
#define ZPATH_DEBUG_AGG_BIT                    (uint64_t)0x0000040000
#define ZPATH_DEBUG_TABLE_BIT                  (uint64_t)0x0000080000
#define ZPATH_DEBUG_TAG_ENTITY_BIT             (uint64_t)0x0000100000
#define ZPATH_DEBUG_CUSTOMER_LOGO_BIT          (uint64_t)0x0000200000
#define ZPATH_DEBUG_LOCATION_BIT               (uint64_t)0x0000400000
#define ZPATH_DEBUG_TLS_BIT                    (uint64_t)0x0000800000
#define ZPATH_DEBUG_CIDR_LOOKUP_BIT            (uint64_t)0x0001000000
#define ZPATH_DEBUG_CUSTOMER_BIT               (uint64_t)0x0002000000
#define ZPATH_DEBUG_LOG_CONFIG_BIT             (uint64_t)0x0004000000
#define ZPATH_DEBUG_LOG_STORE_BIT              (uint64_t)0x0008000000
#define ZPATH_DEBUG_SERVICE_BIT                (uint64_t)0x0010000000
#define ZPATH_DEBUG_GEOIP_OVERRIDE_BIT         (uint64_t)0x0020000000
#define ZPATH_DEBUG_SCIM_BIT                   (uint64_t)0x0040000000
#define ZPATH_DEBUG_CONFIG_OVERRIDE_BIT        (uint64_t)0x0080000000
#define ZPATH_DEBUG_PARTITIONING_BIT           (uint64_t)0x0100000000
#define ZPATH_DEBUG_CURL_CMD_BIT               (uint64_t)0x0200000000
#define ZPATH_DEBUG_LOG_ENDPOINT_BIT           (uint64_t)0x0400000000
#define ZPATH_DEBUG_CLOUD_CONFIG_BIT           (uint64_t)0x0800000000
#define ZPATH_DEBUG_ZIA_BIT                    (uint64_t)0x1000000000
#define ZPATH_DEBUG_CMD_LISTENER_BIT           (uint64_t)0x2000000000

/* The following is used to set up more debugging... Add to this list
 * as you add debugging bits. Make sure they stay in sync. */
#define ZPATH_DEBUG_NAMES {                     \
    "instance",                                 \
        "rule",                                 \
        "limit",                                \
        "debug",                                \
        "message",                              \
        "query",                                \
        "zurldb",                               \
        "local",                                \
        "ip_entity",                            \
        "entity",                               \
        "customer_log_config",                  \
        "constellation",                        \
        "domain_lookup",                        \
        "domainlist",                           \
        "customer_notification",                \
        "category",                             \
        "cloud",                                \
        "ip_location",                          \
        "agg",                                  \
        "table",                                \
        "tag_entity",                           \
        "customer_logo",                        \
        "location",                             \
        "tls",                                  \
        "cidr_lookup",                          \
        "customer",                             \
        "log_config",                           \
        "log_store",                            \
        "service",                              \
        "geoip_override",                       \
        "scim",                                 \
        "config_override",                      \
        "logical_partitioning",                 \
        "curl_cmd",                             \
        "log_endpoint",                         \
        "cloud_config",                         \
        "zia",                                  \
        "command_listener",                     \
        NULL                                    \
        }


#define ZPATH_LOG(priority, format...) ARGO_LOG(zpath_event_collection, priority, "zpath_lib", ##format)
#define ZPATH_DEBUG_LOG(condition, format...) ARGO_DEBUG_LOG(condition, zpath_event_collection, argo_log_priority_debug, "zpath_lib", ##format)
#define ZPATH_GENERIC_LOG(priority, module, format...) ARGO_LOG(zpath_event_collection, priority, module, ##format)
#define ZPATH_LOG_TEXT(priority, file, func, line, format...)     \
              argo_log_text(zpath_event_collection, priority, "zpath_lib", file, func, line, ##format)

#define ZPATH_DEBUG_INSTANCE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_INSTANCE_BIT, ##format)
#define ZPATH_DEBUG_POLICY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_POLICY_BIT, ##format)
#define ZPATH_DEBUG_RULE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_RULE_BIT, ##format)
#define ZPATH_DEBUG_LIMIT(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LIMIT_BIT, ##format)
#define ZPATH_DEBUG_DEBUG(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_DEBUG_BIT, ##format)
#define ZPATH_DEBUG_MESSAGE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_MESSAGE_BIT, ##format)
#define ZPATH_DEBUG_QUERY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_QUERY_BIT, ##format)
#define ZPATH_DEBUG_ZURLDB(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_ZURLDB_BIT, ##format)
#define ZPATH_DEBUG_LOCAL(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LOCAL_BIT, ##format)
#define ZPATH_DEBUG_IP_ENTITY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_IP_ENTITY_BIT, ##format)
#define ZPATH_DEBUG_ENTITY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_ENTITY_BIT, ##format)
#define ZPATH_DEBUG_CUSTOMER_LOG_CONFIG(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CUSTOMER_LOG_CONFIG_BIT, ##format)
#define ZPATH_DEBUG_CONSTELLATION(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CONSTELLATION_BIT, ##format)
#define ZPATH_DEBUG_DOMAIN_LOOKUP(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_DOMAIN_LOOKUP_BIT, ##format)
#define ZPATH_DEBUG_DOMAINLIST(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_DOMAINLIST_BIT, ##format)
#define ZPATH_DEBUG_CUSTOMER_NOTIFICATION(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CUSTOMER_NOTIFICATION_BIT, ##format)
#define ZPATH_DEBUG_CATEGORY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CATEGORY_BIT, ##format)
#define ZPATH_DEBUG_CLOUD(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CLOUD_BIT, ##format)
#define ZPATH_DEBUG_CONFIG_OVERRIDE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CONFIG_OVERRIDE_BIT, ##format)
#define ZPATH_DEBUG_IP_LOCATION(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_IP_LOCATION_BIT, ##format)
#define ZPATH_DEBUG_AGG(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_AGG_BIT, ##format)
#define ZPATH_DEBUG_TABLE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_TABLE_BIT, ##format)
#define ZPATH_DEBUG_TAG_ENTITY(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_TAG_ENTITY_BIT, ##format)
#define ZPATH_DEBUG_CUSTOMER_LOGO(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CUSTOMER_LOGO_BIT, ##format)
#define ZPATH_DEBUG_LOCATION(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LOCATION_BIT, ##format)
#define ZPATH_DEBUG_TLS(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_TLS_BIT, ##format)
#define ZPATH_DEBUG_CIDR_LOOKUP(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CIDR_LOOKUP_BIT, ##format)
#define ZPATH_DEBUG_CUSTOMER(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CUSTOMER_BIT, ##format)
#define ZPATH_DEBUG_LOG_CONFIG(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LOG_CONFIG_BIT, ##format)
#define ZPATH_DEBUG_LOG_STORE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LOG_STORE_BIT, ##format)
#define ZPATH_DEBUG_SERVICE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_SERVICE_BIT, ##format)
#define ZPATH_DEBUG_GEOIP_OVERRIDE(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_GEOIP_OVERRIDE_BIT, ##format)
#define ZPATH_DEBUG_SCIM(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_SCIM_BIT, ##format)
#define ZPATH_DEBUG_PARTITIONING(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_PARTITIONING_BIT, ##format)
#define ZPATH_DEBUG_CURL_CMD(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CURL_CMD_BIT, ##format)
#define ZPATH_DEBUG_LOG_ENDPOINT(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_LOG_ENDPOINT_BIT, ##format)
#define ZPATH_DEBUG_CLOUD_CONFIG(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CLOUD_CONFIG_BIT, ##format)
#define ZPATH_DEBUG_ZIA(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_ZIA_BIT, ##format)
#define ZPATH_DEBUG_CMD_LISTENER(format...) ZPATH_DEBUG_LOG(zpath_debug & ZPATH_DEBUG_CMD_LISTENER_BIT, ##format)

#define ZDP_ZPATH_LOG(priority, format...)  \
({                                          \
    if(request_state == NULL)               \
        ZPATH_LOG(priority, ##format);      \
    else                                    \
        zpath_debug_cb_printf_response(request_state, ##format); \
})

struct zpath_debug_state;

#define COMMAND_LISTENER_SOCKET_PATH "command-listener"

/*
 * command types.. Read, Write and Action
 *  Read    - Commands that only serve to read data and display them
 *  Write   - Commands that can change the state of the system (eg. debugs)
 *  Action  - Commands that could lead to certain actions like shutdown, drain, maintenance etc.
 *  PS: set min as read; and max as admin.. Update them, if they ever change.
 */
enum zpath_debug_cmd_access_level {
    zpath_debug_cmd_min = 0,
    zpath_debug_cmd_read = zpath_debug_cmd_min,
    zpath_debug_cmd_write = 1,
    zpath_debug_cmd_admin = 2,
    zpath_debug_cmd_max = zpath_debug_cmd_admin     //Update min and max if things change.
};

struct zpath_debug_command_user_info {
    /* UID of the client -> we can derive username from it as needed */
    uid_t peer_uid;
};

/* Struct to hold info of a group name and the associated permissions */
struct zpath_debug_rbac_groups_info {
    char group_name[256];
    enum zpath_debug_cmd_access_level access_level;
};

/* Use the roles to set interval for memory allocator stats collection */
enum zpath_service_type {
    zpath_service_none = 0,
    zpath_service_broker,
    zpath_service_wally,
    zpath_service_exporter,
    zpath_service_assistant,
    zpath_service_l4proxy,
    zpath_service_private_broker
};

/* Log types for argo memory allocators*/
enum zpath_argo_allocator_type {
    ZPATH_ARGO_ALLOCATOR_ZPN_APPLICATION,
    ZPATH_ARGO_ALLOCATOR_ZPN_APPLICATION_DOMAIN,
    ZPATH_ARGO_ALLOCATOR_ZPN_APP_GROUP_RELATION,
    ZPATH_ARGO_ALLOCATOR_ZPN_TRANS_LOG,
    ZPATH_ARGO_ALLOCATOR_ZPN_CLIENT_APP,
    ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT /* MUST BE LAST. Update
                                  * argo_allocator_type_names when
                                  * making changes to this enum
                                  */
};

/*
 * This will help to export memory usage into statistics collection object
 */
struct zpath_debug_memory_allocator_stats {                 /* _ARGO: object_definition */
    char        *name;                                      /* _ARGO: string */
    int64_t     allocations;                                /* _ARGO: integer */
    int64_t     allocation_bytes;                           /* _ARGO: integer */
    int64_t     frees;                                      /* _ARGO: integer */
    int64_t     free_bytes;                                 /* _ARGO: integer */
    int64_t     drain_queue;                                /* _ARGO: integer */
    int64_t     drain_queue_bytes;                          /* _ARGO: integer */
};

/*
 * This will help to export argo memory usage into statistics collection object
 */
struct argo_allocator_mem_stats {                           /* _ARGO: object_definition */
    int64_t     allocations;                                /* _ARGO: integer */
    int64_t     allocation_bytes;                           /* _ARGO: integer */
    int64_t     frees;                                      /* _ARGO: integer */
    int64_t     free_bytes;                                 /* _ARGO: integer */
};

/*
 * This will help to export mallinfo statistics to collection object
 * This is GNU allocator's memory statistics
 */
struct zpath_debug_mallinfo {                                  /* _ARGO: object_definition */
    size_t arena;                                              /* _ARGO: integer */
    size_t ordblks;                                            /* _ARGO: integer */
    size_t smblks;                                             /* _ARGO: integer */
    size_t hblks;                                              /* _ARGO: integer */
    size_t hblkhd;                                             /* _ARGO: integer */
    size_t usmblks;                                            /* _ARGO: integer */
    size_t fsmblks;                                            /* _ARGO: integer */
    size_t uordblks;                                           /* _ARGO: integer */
    size_t fordblks;                                           /* _ARGO: integer */
    size_t keepcost;                                           /* _ARGO: integer */
};

extern struct argo_structure_description *argo_allocator_mem_stats_description;
extern struct argo_structure_description *zpath_debug_memory_allocator_stats_description;
extern struct argo_structure_description *zpath_debug_mallinfo_description;

/*
 * Initialize the zpath debugging system. Tells this system what
 * event/stats logs to use, starts up the debug thread, things like
 * that.
 *
 * debug_pre_init should be called first, and initializes structures
 * for holding state.
 *
 * debug_init initializes the event listener, which is often set up a
 * little later during initialization.
 *
 * Both must be called.
 */
int zpath_debug_pre_init(int init_http_server);
int zpath_debug_init(const char *event_log_name,
                     const char *stats_log_name,
                     int32_t debug_port_he);

/*
 * Get the version of zpath_lib. (Both work, and return the same thing)
 */
const char *zpath_debug_version(void);
const char *zpath_lib_version(void);

/*
 * It is the callback's responsibility to either return success (0) or
 * failure (non-zero).
 *
 * A callback may use the ZDP macro, or call
 * zpath_debug_cb_printf_response, to add data to the response
 */
typedef int (zpath_debug_callback_f)(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie);

/*
 * Add a command to the debug interface.
 *
 * When registering commands, do not include a trailing '/'. (But do
 * include leading '/')
 *
 * A nice path might be somthing like "/url/zurldb" or maybe "/fohh"
 *
 * Note that you can differentiate get/put here, but that's only
 * really for convenience. The effect of commands is at the user's
 * discretion.
 *
 * The built in parser will reject requests that have an unrecognized
 * query string, but will accept commands that are missing query
 * strings. (The built in parser assumes all are optional). For those
 * query values, it will leave the ptr NULL for that callback
 * occasion.
 *
 * IMPORTANT: The variable arguments are pointers to strings,
 * terminated by a NULL. YOU MUST HAVE AT LEAST ONE NULL as a vararg
 * arg! There MUST be an even number of non-null vararg args!!!
 *
 * The strings come in pairs. The first string of a pair is the name
 * of a query string. The second string of a pair is a description of
 * the query string. When the name of a query string is NULL, this
 * routine will stop parsing arguments.
 *
 * For example, you might call
 *
 * zpath_debug_add_admin_command(desc, path, cb, cookie,
 *                         "name", "Name of the thing we are looking for",
 *                         "age", "Age of the thing we are looking for",
 *                         "style", "One of italic, bold, or blinking",
 *                         NULL);
 */
int zpath_debug_add_read_command(const char *command_description,
                            char *path,
                            zpath_debug_callback_f *cb,
                            void *cb_cookie,
                            ...); /* Query strings. ORDER IS
                                   * IMPORTANT, and matches the
                                   * query value order in the
                                   * callback! */
int zpath_debug_add_write_command(const char *command_description,
                            char *path,
                            zpath_debug_callback_f *cb,
                            void *cb_cookie,
                            ...); /* Query strings. ORDER IS
                                   * IMPORTANT, and matches the
                                   * query value order in the
                                   * callback! */
int zpath_debug_add_admin_command(const char *command_description,
                            char *path,
                            zpath_debug_callback_f *cb,
                            void *cb_cookie,
                            ...); /* Query strings. ORDER IS
                                   * IMPORTANT, and matches the
                                   * query value order in the
                                   * callback! */

/* Same as zpath_debug_add_admin_command, and the command is considered safe.
 * Safe commands are allowed to be executed through airwave. */
int zpath_debug_add_safe_read_command(const char *command_description,
                            char *path,
                            zpath_debug_callback_f *cb,
                            void *cb_cookie,
                            ...);

/* Unix domain socket based command handler */
int zpath_debug_command_listener_init(const char *role_name);
char *zpath_debug_get_username_from_uid(uid_t client_uid);
const char *zpath_debug_get_cmd_access_level_str(enum zpath_debug_cmd_access_level level);
enum zpath_debug_cmd_access_level zpath_debug_get_highest_access_level(uid_t client_uid);
int zpath_debug_is_rbac_supported(const char *role_name);
void zpath_debug_update_rbac_enablement(const char *role_name);

/* Diagnostic report triggered via /diagnostics command.
 * - Automatically detects and reports top offenders or anomalies
 * - Shows a "at-a-glance" view of the system before drilling into specific areas
 * - (To a lesser degree,) a place to learn other, more detailed debugging commands
 *
 * Examples:
 * - thread(s) of highest CPUs or CPU crossing certain threshold
 * - unusual amount of traffic for certain protocols
 * - service endpoints not connected for certain amount of time
 *
 * If your code is capable of recognizing issues with potential system impact, it'll
 * be good to implement a callback and make the issues visible through /diagnostics endpoint.
 *
 * Notes on callback implementation:
 * - What constitutes a "problem" is up to the implementation. Keep in mind though,
 *   the primary goal of this tool is to diagnose system issues.
 * - Implementation can report issues on-demand when callback is invoked, or track
 *   issues on-the fly and report through when command is invoked.
 * - All registered callbacks are invoked in sequence. Please keep the time and verbosity
 *   to a minimum. For example, report "top 3" and add link to "detailed debug command".
 * - You don't have to generate any output if there are no problems/anomalies.
 */
typedef int (zpath_debug_diag_callback_f)(struct zpath_debug_state *request_state,
                                          void *cookie,
                                          int verbose);
int zpath_debug_add_diagnostic_cb(const char *name,
                            const char *detail_path, /* link to a command that may have more info (can be null) */
                            zpath_debug_diag_callback_f *cb,
                            void *cb_cookie);

/*
 * Add simple debugging string.
 */
/* int zpath_debug_cb_printf_response(struct zpath_debug_state *request_state, */
/*                                    const char *format, */
/*                                    ...); */
void zpath_debug_cb_printf_response(struct zpath_debug_state *request_state,
                                    const char *format,
                                    ...)
    __attribute__((format(printf, 2, 3)));

/*
 * Send HTTP error code. (And nothing else)
 */
int zpath_debug_cb_http_error(struct zpath_debug_state *request_state, int error_code, char *error_string);

/*
 * Add debug flag setting utility.
 *
 * bit_names and bit_descriptions are null terminated arrays of
 * strings describing the bits.
 *
 * flag_address is the address of the field containing the flags.
 *
 * catch_default is the default configuration for catch debugging. It
 * is often passed in as zero...
 *
 * Creates paths:
 *
 * /debug/$flag_name      - To show a flag in normal debug mode.
 * /catch/$flag_name      - To show a flag in catch debug mode.
 * /debug/$flag_name/$bit - To toggle a specific debug bit.
 * /catch/$flag_name/$bit - To toggle a specific catch bit.
 *
 */
int zpath_debug_add_flag(uint64_t *flag_address,
                         uint64_t catch_default,
                         const char *flag_name,
                         const char **bit_names);
// used by zpn debug flags
int zpath_debug_add_flag_ext(void *flag_address,
                             const void *catch_default,
                             uint16_t count,
                             const char *flag_name,
                             const char **bit_names);
/*
 * The catch/uncatch commands are used to perform simple debug
 * enable/disable based on an arriving IP address.
 *
 * If the arriving IP address matches a configured catch IP address,
 * then debug flags are set to the catch configuration.
 *
 * When uncatch is called, the debug flags are reset to normal debug
 * configuration.
 *
 * The IP address is configured internally via the HTTP interface to
 * zpath_debug.
 *
 * The IP address passed in is the address from the wire, so to speak.
 *
 * These two routines operate a bit like a stack- they must be called
 * an identical number of times- for each call to catch_ip, there
 * should be a corresponding call to uncatch
 */
int zpath_debug_catch_ip(struct argo_inet *inet);
int zpath_debug_uncatch_ip(struct argo_inet *inet);

/*
 * Add collection's mem view to debug interface
 * */
int zpath_debug_add_collection_mem_view(void);

/*
 * Add an argo collection to our debug interface.
 */
int zpath_debug_add_collection(struct argo_log_collection *collection);


/*
 * Add an fohh log collection to our debug interface.
 */
int zpath_debug_add_fohh_log_collection(struct argo_log_collection *collection);

/*
 * Add a zpath_allocator to our debug interface.
 */
int zpath_debug_add_allocator(struct zpath_allocator *allocator, const char *name);

/*
 * Application can decide freeze on further spawning allocators for reasons known to them.
 * Once this is set, applications should expect to receive error on trying to add an allocator.
 */
void zpath_debug_disallow_further_allocators();

/*
 * Memory allocator stats, disallows further allocators before capturing stats
 */
int zpath_debug_mem_stats_init(enum zpath_service_type service_type);

/*
 * Argo memory allocator stats init and callback
 */
int argo_mem_stats_init(enum zpath_service_type service_type);
int argo_load_stats_cb(void *cookie, int counter, void *structure_data);

int zpath_debug_add_fohh_generic_server(struct fohh_generic_server *server, const char *name);
struct fohh_generic_server *zpath_debug_lookup_fohh_generic_server(const char *name);


/*
 * Let the memory stats be filled into the structured_data
 */
int zpath_debug_memory_allocator_stats_fill_all(void *cookie, int counter, void *structure_data);
int zpath_debug_memory_allocator_stats_fill_allocator(void *cookie, int counter, void *structure_data);
#ifdef __linux__
int zpath_debug_mallinfo_fill(void *cookie, int counter, void *structure_data);
int zpath_sysinfo_fill(void *cookie, int counter, void *structure_data);
#endif
size_t zpath_debug_current_allocators_count();

/*
 * Log the allocator info
 */
void zpath_allocator_log();
/*
 * Log the allocator info - highest consumer only.
 */
void zpath_allocator_log_highest_consumer_only();

/*
 * Get debug event base
 */
struct event_base *zpath_debug_get_base();

int zpath_debug_update_natural_logs_repeat_count(struct zpath_debug_state*  request_state,
                                                 const char **              query_values,
                                                 int                        query_value_count,
                                                 void*                       cookie);

void zpath_debug_add_fohh_tracker_commands();
int zpath_is_dev_environment();
int64_t zpath_allocator_by_name_used_bytes_get(const char *name);
char *zpath_specific_allocator_status(const char *name);
#endif /* _ZPATH_DEBUG_H_ */
