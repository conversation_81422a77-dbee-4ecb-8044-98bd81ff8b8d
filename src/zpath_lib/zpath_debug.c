/*
 * zpath_debug.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */

#define _GNU_SOURCE

#include <sched.h>
#include <string.h>
#include <sys/queue.h>
#include <stdarg.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "argo/argo.h"
#include "argo/argo_private.h"
#include "argo/argo_log.h"
#include "argo/argo_hash.h"
#include <event2/event.h>
#include <event2/http.h>
#include <event2/event_struct.h>
#include <event2/buffer.h>
#include <event2/listener.h>
#include <event2/bufferevent.h>
#include "zpath_misc/zpath_misc.h"
#include "zthread/zthread.h"
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_http.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh_tracker.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "zudp_conn/zudp_conn.h"
#include "pattern_match/pattern_match.h"

#include "zpath_lib/zpath_debug.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_local.h"
#include "zhash/zhash_table_locked.h"
#include "zpath_lib/zpath_debug_compiled.h"
#include "zpath_lib/zpa_cloud_config.h"

#include "zpn/zpn_lib.h"

#include <sys/socket.h>
#include <sys/un.h>

#define ZDP(format...) zpath_debug_cb_printf_response(request_state, ##format)
#define ZPATH_DEBUG_ARR_SIZE(arr)   (sizeof(arr))/sizeof((arr)[0])

#define OUT_BUF_LEN 2*1000*1000

#define MAX_DEBUG_SUB_CATEGORIES 128 // at least ZPN_DEBUG_NUM_IDX

char *debug_port_bind_ip = "127.0.0.1";

uint64_t zpath_debug =
    (!ZPATH_DEBUG_INSTANCE_BIT) |
    (!ZPATH_DEBUG_RULE_BIT) |
    (!ZPATH_DEBUG_LIMIT_BIT) |
    (!ZPATH_DEBUG_DEBUG_BIT) |
    (!ZPATH_DEBUG_MESSAGE_BIT) |
    (!ZPATH_DEBUG_QUERY_BIT) |
    (!ZPATH_DEBUG_ZURLDB_BIT) |
    (!ZPATH_DEBUG_LOCAL_BIT) |
    (!ZPATH_DEBUG_IP_ENTITY_BIT) |
    (!ZPATH_DEBUG_ENTITY_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_LOG_CONFIG_BIT) |
    (!ZPATH_DEBUG_CONSTELLATION_BIT) |
    (!ZPATH_DEBUG_DOMAIN_LOOKUP_BIT) |
    (!ZPATH_DEBUG_DOMAINLIST_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_NOTIFICATION_BIT) |
    (!ZPATH_DEBUG_CATEGORY_BIT) |
    (!ZPATH_DEBUG_CLOUD_BIT) |
    (!ZPATH_DEBUG_IP_LOCATION_BIT) |
    (!ZPATH_DEBUG_AGG_BIT) |
    (!ZPATH_DEBUG_TABLE_BIT) |
    (!ZPATH_DEBUG_TAG_ENTITY_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_LOGO_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_BIT) |
    (!ZPATH_DEBUG_LOG_CONFIG_BIT) |
    (!ZPATH_DEBUG_LOG_STORE_BIT) |
    (!ZPATH_DEBUG_SERVICE_BIT) |
    (!ZPATH_DEBUG_GEOIP_OVERRIDE_BIT) |
    (ZPATH_DEBUG_CURL_CMD_BIT) |
    (!ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
    (!ZPATH_DEBUG_LOG_ENDPOINT_BIT) |
    (!ZPATH_DEBUG_CMD_LISTENER_BIT) |
    0;

uint64_t zpath_debug_catch_defaults =
    (!ZPATH_DEBUG_INSTANCE_BIT) |
    (!ZPATH_DEBUG_RULE_BIT) |
    (!ZPATH_DEBUG_LIMIT_BIT) |
    (!ZPATH_DEBUG_DEBUG_BIT) |
    (!ZPATH_DEBUG_MESSAGE_BIT) |
    (!ZPATH_DEBUG_QUERY_BIT) |
    (!ZPATH_DEBUG_ZURLDB_BIT) |
    (!ZPATH_DEBUG_LOCAL_BIT) |
    (!ZPATH_DEBUG_IP_ENTITY_BIT) |
    (!ZPATH_DEBUG_ENTITY_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_LOG_CONFIG_BIT) |
    (!ZPATH_DEBUG_CONSTELLATION_BIT) |
    (!ZPATH_DEBUG_DOMAIN_LOOKUP_BIT) |
    (!ZPATH_DEBUG_DOMAINLIST_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_NOTIFICATION_BIT) |
    (!ZPATH_DEBUG_CATEGORY_BIT) |
    (!ZPATH_DEBUG_CLOUD_BIT) |
    (!ZPATH_DEBUG_IP_LOCATION_BIT) |
    (!ZPATH_DEBUG_AGG_BIT) |
    (!ZPATH_DEBUG_TABLE_BIT) |
    (!ZPATH_DEBUG_TAG_ENTITY_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_LOGO_BIT) |
    (!ZPATH_DEBUG_CUSTOMER_BIT) |
    (!ZPATH_DEBUG_LOG_CONFIG_BIT) |
    (!ZPATH_DEBUG_LOG_STORE_BIT) |
    (!ZPATH_DEBUG_SERVICE_BIT) |
    (!ZPATH_DEBUG_GEOIP_OVERRIDE_BIT) |
    (!ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
    (!ZPATH_DEBUG_LOG_ENDPOINT_BIT) |
    0;

/* Please be careful while mapping names as it is case sensitive. The names should
   should be what we see in the 'itascurl memory/argo' output.
*/
const char *argo_allocator_type_names[ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT] = {
    [ZPATH_ARGO_ALLOCATOR_ZPN_APPLICATION] = "zpn_application",
    [ZPATH_ARGO_ALLOCATOR_ZPN_APPLICATION_DOMAIN] = "zpn_application_domain",
    [ZPATH_ARGO_ALLOCATOR_ZPN_APP_GROUP_RELATION] = "zpn_app_group_relation",
    [ZPATH_ARGO_ALLOCATOR_ZPN_TRANS_LOG] = "zpn_trans_log",
    [ZPATH_ARGO_ALLOCATOR_ZPN_CLIENT_APP] = "zpn_client_app",
};

const char *zpath_debug_names[] = ZPATH_DEBUG_NAMES;

struct argo_log_collection *zpath_debug_event = NULL;
struct argo_log_collection *zpath_debug_stats = NULL;

//externalizing http_server
struct fohh_http_server *zpath_debug_http_server = NULL;

/* access levels */
const char* zpath_debug_cmd_access_level_strings[] = {
    [zpath_debug_cmd_read] = "read",
    [zpath_debug_cmd_write] = "write",
    [zpath_debug_cmd_admin] = "admin"
};

//NOTE: components with no override support have been excluded for now.
const char* zpath_debug_rbac_supported_roles[] = {
        ZPATH_ROLE_ZPN_BROKERD,
        ZPATH_ROLE_WALLYD,
        //ZPATH_ROLE_NATURAL,
        ZPATH_ROLE_SLOGGER,
        ZPATH_ROLE_EXPORTER,
        //ZPATH_ROLE_REBOUND,
        ZPATH_ROLE_ZPN_IPARSD,
        //ZPATH_ROLE_LOOKUP,
        ZPATH_ROLE_UATU,
        //ZPATH_ROLE_BLOCKD,
        ZPATH_ROLE_ZPN_L4PROXYD,
        ZPATH_ROLE_ZPN_DISPATCHER
};

/* This is the default group for components, if not specifically overriden */
static struct zpath_debug_rbac_groups_info zpath_debug_default_rbac_groups[] = {
    {"datapath-ro", zpath_debug_cmd_read},
    {"datapath-rw", zpath_debug_cmd_write},
    {"datapath-admin", zpath_debug_cmd_admin},
    {"root", zpath_debug_cmd_admin}     //adding root access here.
};

static struct zpath_debug_rbac_groups_info *zpath_debug_rbac_groups = &(zpath_debug_default_rbac_groups[0]);
static size_t zpath_debug_rbac_groups_count = sizeof(zpath_debug_default_rbac_groups)/sizeof(zpath_debug_default_rbac_groups[0]);

#define ZPATH_DEBUG_MAX_ALLOCATORS 100
#define ZPATH_DEBUG_MAX_GENERIC_SERVERS 100
static struct zpath_allocator *allocator[ZPATH_DEBUG_MAX_ALLOCATORS];
static const char *allocator_name[ZPATH_DEBUG_MAX_ALLOCATORS];
static int allocator_count = 0;
static int zpath_debug_allocator_disallow_further_allocations = 0;
static pthread_mutex_t allocator_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

static int sni_server_count = 0;

#define ZPATH_DEBUG_MAX_QUERY_ARGS_COUNT 20
/*
 * Recording the peak of allocations is two dimensional,
 * 1. each allocator can hit its peak independent of the system peak. This peak is recorded in zpath_allocator itself.
 * 2. the system peak. This is recorded below here.
 *
 * We are ok with not being accurate with respect to peak calculation (vs being accurate and doing thread locking)
 * and so we decided to check for the peak every 1sec.
 */
static struct zpath_allocator_peak_snapshot {
    struct {
        struct zpath_allocator_stats stats;
        struct zpath_allocator_stats peak_stats;
        int64_t allocator_peak_stats_hit_timestamp_s;
    }allocator[ZPATH_DEBUG_MAX_ALLOCATORS];
    int64_t peak_total_outstanding_bytes;
    int64_t peak_time_epoch_s;
} zpath_allocator_peak_snapshot;

#define ZPATH_ZTHREAD_HEARTBEAT_MIN_OVERRIDE_TIMEOUT  20
#define ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_TIMEOUT  3600
#define ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_DURATION  10800
static struct event *zpath_zthread_hb_override_timer = NULL;
static struct timeval zpath_zthread_hb_override_target_time;
static int zpath_zthread_hb_override_duration = 0;


extern struct zpath_allocator avl_allocator;
extern struct zpath_allocator phash_allocator;
extern struct zpath_allocator cshash_allocator;

/* The set of paths registered with the debug interface */
#define DEBUG_COMMAND_FLAG_SAFE 0x1
struct debug_command {
    int flags;
    char *path;
    char *description;
    char **query_strings;
    char **query_descriptions;
    int query_count;
    zpath_debug_callback_f *cb;
    void *cb_cookie;
    enum zpath_debug_cmd_access_level access_level;
};

static pthread_mutex_t lock;

struct debug_command_set {
    struct debug_command *all_commands[ZPATH_DEBUG_MAX_COMMANDS];
    int all_commands_count;
};

struct debug_flag_bit_desc {
    struct debug_flag *flag;
    int bit_position;
    const char *bit_name;
};

struct debug_flag {
    struct debug_flag_bit_desc bits[MAX_DEBUG_SUB_CATEGORIES];
    int bit_count;
    uint64_t *flag;
    void *flag_2; // array
    const char *flag_name;
    uint64_t flag_normal;
    uint64_t flag_catch;
    uint8_t flag_normal_2[MAX_DEBUG_SUB_CATEGORIES];
    uint8_t flag_catch_2[MAX_DEBUG_SUB_CATEGORIES];
};

static struct debug_flag debug_flags[100];
static int debug_flag_count = 0;
static int catch_stack = 0;
static int catch_max = 100;
static int catch_count = 0;
static int catch_enabled = 0;

static struct argo_inet zpath_debug_ip_to_catch;

static struct debug_command_set all_commands;

/* The set of callbacks triggered via "/diagnostics" debug command */
#define ZPATH_DEBUG_MAX_DIAGNOSTIC_COMMANDS 256
struct diagnostic_command {
    char *name;
    char *detail_path;
    zpath_debug_diag_callback_f *cb;
    void *cb_cookie;
};
struct diagnostic_command_set {
    zpath_mutex_t lock;
    struct diagnostic_command *commands[ZPATH_DEBUG_MAX_DIAGNOSTIC_COMMANDS];
    int count;
};
static struct diagnostic_command_set diagnostics;

static struct event_base *base;

static struct zthread_info *zthread;

struct argo_structure_description *zpath_debug_mallinfo_description = NULL;
struct argo_structure_description *zpath_debug_memory_allocator_stats_description = NULL;
struct argo_structure_description *argo_allocator_mem_stats_description = NULL;

int zpath_debug_catch_ip_ui(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie);
int zpath_debug_dump_catch_all(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie);
int zpath_debug_dump_debug_all(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie);
int zpath_zthread_set_heartbeat_override(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object);

void zpath_zthread_hb_override_timer_cb(int sock, short flags, void *cookie);

int zpath_zthread_hb_get_remaining_override_duration();

int zpath_debug_normal(void);

const char *zpath_lib_version(void)
{
    return zpath_debug_version();
}

const char *zpath_debug_version(void)
{
    return ZPATH_VERSION;
}

struct event_base *zpath_debug_get_base()
{
    return base;
}

int zpath_debug_comparator(const void *a, const void *b)
{
    const struct debug_command **aa = (const struct debug_command **)a;
    const struct debug_command **bb = (const struct debug_command **)b;

    return strcmp((*aa)->path, (*bb)->path);
}

int zpath_debug_fohh_client_connections(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    enum fohh_conn_describe_type desc_type;
    int fohh_thread_id = -1;
    const int out_buf_len = OUT_BUF_LEN;
    char *out_buf = ZLIB_MALLOC(out_buf_len);

    if (query_values[2]) fohh_thread_id = strtol(query_values[2], NULL, 0);

    desc_type = fohh_connections_describe_get_type(query_values[1]);

    if (query_values[3]) {
        desc_type = fohh_conn_describe_type_stats;
    }
    int64_t limit = 0;
    if (query_values[4]) {
        limit = strtoll(query_values[4], NULL, 0);
    }
    fohh_connections_describe(out_buf, out_buf_len, fohh_connection_type_client, query_values[0], desc_type, fohh_thread_id, limit);
    zpath_debug_cb_printf_response(request_state, "%s", out_buf);
    ZLIB_FREE(out_buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_client_diagnostic_cb(struct zpath_debug_state *request_state,
                                        void *cookie,
                                        int verbose)
{
    const int out_buf_len = OUT_BUF_LEN;
    char *out_buf = ZLIB_MALLOC(out_buf_len);

    fohh_connections_describe(out_buf, out_buf_len, fohh_connection_type_client, NULL, fohh_conn_describe_type_err, -1, 0);
    zpath_debug_cb_printf_response(request_state, "%s", out_buf);

    ZLIB_FREE(out_buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_server_diagnostic_cb(struct zpath_debug_state *request_state,
                                        void *cookie,
                                        int verbose)
{
    const int out_buf_len = OUT_BUF_LEN;
    char *out_buf = ZLIB_MALLOC(out_buf_len);

    fohh_connections_describe(out_buf, out_buf_len, fohh_connection_type_server, NULL, fohh_conn_describe_type_err, -1, 0);
    zpath_debug_cb_printf_response(request_state, "%s", out_buf);

    ZLIB_FREE(out_buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_server_connections(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    enum fohh_conn_describe_type desc_type;
    int fohh_thread_id = -1;
    //const int out_buf_len = 2*1000*1000;
    const int out_buf_len = 60*1000*1000; // temporary; should be converted to streaming
    char *out_buf = ZLIB_MALLOC(out_buf_len);

    if (query_values[2]) fohh_thread_id = strtol(query_values[2], NULL, 0);

    desc_type = fohh_connections_describe_get_type(query_values[1]);

    if (query_values[3]) {
        desc_type = fohh_conn_describe_type_stats;
    }
    int64_t limit = 0;
    if (query_values[4]) {
        limit = strtoll(query_values[4], NULL, 0);
    }
    fohh_connections_describe(out_buf, out_buf_len, fohh_connection_type_server, query_values[0], desc_type, fohh_thread_id, limit);
    zpath_debug_cb_printf_response(request_state, "%s", out_buf);
    ZLIB_FREE(out_buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_reset(struct zpath_debug_state *request_state,
                           const char **query_values,
                           int query_value_count,
                           void *cookie,
                           enum fohh_connection_type type)
{
    int result;
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    } else {
        const char *domain = query_values[0];
        const char *port = query_values[1];  /* CAN BE NULL */
        int limit;
        if (query_values[2]) {
            limit = strtoul(query_values[2], NULL, 0);
        } else {
            limit = 1;
        }
        result = fohh_connections_reset(domain, port, type, limit, (fohh_external_log_f *)zpath_debug_cb_printf_response, request_state);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Request(s) dispatched\n");
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "%s connection with domain %s not found.\n",
                                           (type == fohh_connection_type_server) ? "Server" : "Client",
                                           query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "%s connection with domain %s is not connected\n",
                                           (type == fohh_connection_type_server) ? "Server" : "Client",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not dispatch request\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_client_reset(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    return zpath_debug_fohh_reset(request_state,
                                  query_values,
                                  query_value_count,
                                  cookie,
                                  fohh_connection_type_client);
}

int zpath_debug_fohh_server_reset(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    return zpath_debug_fohh_reset(request_state,
                                  query_values,
                                  query_value_count,
                                  cookie,
                                  fohh_connection_type_server);
}

static int zpath_debug_fohh_thread_state_stats(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!fohh_get_state_stats_logging()) {
        zpath_debug_cb_printf_response(request_state, "fohh stats logging by state is not enabled (try /fohh/thread/stats_by_states/toggle).\n");
    } else {
        const int out_buf_len = 2*1000*1000;
        char *out_buf = ZLIB_MALLOC(out_buf_len);
        fohh_connection_state_stats_dump(out_buf, out_buf_len);
        zpath_debug_cb_printf_response(request_state, "%s", out_buf);
        ZLIB_FREE(out_buf);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_fohh_thread_state_stats_consolidated(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!fohh_get_state_stats_logging()) {
        zpath_debug_cb_printf_response(request_state, "fohh stats logging by state is not enabled (try /fohh/thread/stats_by_states/toggle).\n");
    } else {
        const int out_buf_len = 2*1000*1000;
        char *out_buf = ZLIB_MALLOC(out_buf_len);
        fohh_connection_state_stats_consolidated_dump(out_buf, out_buf_len);
        zpath_debug_cb_printf_response(request_state, "%s", out_buf);
        ZLIB_FREE(out_buf);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_fohh_connection_aggregated_stats(struct zpath_debug_state *request_state,
                                                        const char **query_values,
                                                        int query_value_count,
                                                        void *cookie)
{
    if (!fohh_connection_aggregated_stats_enabled()) {
        zpath_debug_cb_printf_response(request_state, "fohh aggregated stats logging is not enabled\n");
    } else {
        fohh_connection_aggregated_stats_dump(request_state);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_fohh_thread_error_stats(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    fohh_connection_error_stats_dump(request_state);
    return ZPATH_RESULT_NO_ERROR;
}


static int zpath_debug_fohh_thread_alloc_stats(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    fohh_thread_alloc_stats_dump(request_state);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_fohh_thread_state_stats_toggle(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int curr_state = fohh_get_state_stats_logging();
    fohh_set_state_stats_logging(!curr_state);
    zpath_debug_cb_printf_response(request_state,
                                    "fohh stats logging by state: was %d, now %d\n",
                                    curr_state, !curr_state);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_autotune(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    int result;
    if (!query_values[0]) {
        ZDP("Must have at least a 'domain' argument\n");
        return ZPATH_RESULT_ERR;
    } else {
        uint16_t port_he = 0;
        if (query_values[1]) {
            port_he = (uint16_t)atoi(query_values[1]);
        }

        result = fohh_connection_tune_tx_sockbuf_by_name(query_values[0], port_he);
        if (result == ZPATH_RESULT_NO_ERROR) {
            ZDP("Done\n");
        } else {
            ZDP("Cannot tune tx socket buffer\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_autotune_params(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    int port = 0;
    int enabled;
    double factor;
    double reduction;
    int samples;
    int minbuf;

    struct fohh_connection *f_conn = NULL;

    if (!query_values[0] ||
        !query_values[2] ||
        !query_values[3] ||
        !query_values[4] ||
        !query_values[5] ||
        !query_values[6]) {
        ZDP("Must have domain, enabled, factor, reduction, samples, minbuf specified\n");
        return ZPATH_RESULT_ERR;
    }

    if (query_values[1]) {
        port = (uint16_t)atoi(query_values[1]);
    }

    f_conn = fohh_find_connection(query_values[0], port);
    if (!f_conn) {
        ZDP("Could not find connection");
        return ZPATH_RESULT_ERR;
    }

    enabled = atoi(query_values[2]);

    factor = strtod(query_values[3], NULL);
    reduction = strtod(query_values[4], NULL);
    samples = atoi(query_values[5]);
    minbuf = atoi(query_values[6]);

    if (factor <= 0 || factor >100) {
        ZDP("Factor out of range\n");
        return ZPATH_RESULT_ERR;
    }
    if (reduction <= 0 || reduction >100) {
        ZDP("Reduction factor out of range\n");
        return ZPATH_RESULT_ERR;
    }
    if (samples <= 0 || samples > 1000) {
        ZDP("Samples out of range\n");
        return ZPATH_RESULT_ERR;
    }
    if (minbuf <= 0 || minbuf > 100000000) {
        ZDP("minbuf out of range\n");
        return ZPATH_RESULT_ERR;
    }

    fohh_set_autotune(f_conn, enabled, factor, reduction, samples, minbuf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_debug_toggle(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    int result = ZPATH_RESULT_NO_ERROR;
    uint16_t port = 0;

    if (query_values[1]) {
        port = (uint16_t)strtoul(query_values[1], NULL, 0);
    }

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    } else {
        result = fohh_connections_toggle_debug(query_values[0], port);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Toggled debug flag for %s:%s\n", query_values[0], query_values[1] ? query_values[1] : "*");
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s:%s not found.\n", query_values[0], query_values[1] ? query_values[1] : "*");
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not dispatch request\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_delete_filter_history(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }
    fohh_tx_filter_history_remove(query_values[0]);
    ZDP("Done\n");
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_add_filter_history(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }
    fohh_tx_filter_history_add(query_values[0]);
    ZDP("Done\n");
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_show_filter_history(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    char **filters;
    int filter_count;
    int i;

    fohh_tx_filter_history_get(&filters, &filter_count);
    for (i = 0; i < filter_count; i++) {
        ZDP("Matches: %s\n", filters[i]);
    }
    ZDP("Done\n");
    fohh_tx_filter_history_free(filters, filter_count);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_fohh_dump_history(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    struct fohh_connection *f_conn = fohh_find_connection_2(query_values[0], query_values[1]);
    if (!f_conn) {
        ZDP("Could not find any matching connections");
    } else {
        struct argo_object **object_data;
        int64_t *object_us;
        size_t max_depth = fohh_get_max_tx_history_depth(f_conn);
        size_t object_count;
        size_t index;

        ZDP("{\"connection\":{\"description\":\"%s\",\"objects\":[\n",
            fohh_description(f_conn));

        if (max_depth) {
            object_data = ZLIB_MALLOC(sizeof(*object_data) * max_depth);
            object_us = ZLIB_MALLOC(sizeof(object_us) * max_depth);
            object_count = fohh_get_tx_history(f_conn, object_data, object_us, max_depth);

            for (index = 0; index < object_count; index++) {
                char buf[16*1024];
                argo_object_dump(object_data[index], buf, sizeof(buf), NULL, 0);
                ZDP("{\"time\":%ld,\"object\":%s}", (long) object_us[index], buf);
                if (index < (object_count - 1)) {
                    ZDP(",\n");
                } else {
                    ZDP("\n");
                }
                argo_object_release(object_data[index]);
            }
        }
        ZDP("]}}\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_http_default_cb(struct fohh_http_server_connection *connection,
                                      int64_t connection_incarnation,
                                      struct fohh_http_request *request,
                                      const char *matched_host,
                                      const char *matched_path,
                                      void *void_cookie,
                                      int64_t int_cookie)
{
    int i;
    struct debug_command_set *set = void_cookie;
    struct fohh_http_response response;
    int res;

    response.http_version_major = request->http_version_major;
    response.http_version_minor = request->http_version_minor;
    response.status = 200;
    response.content_type = NULL;
    response.extra_headers = NULL;
    response.body = evbuffer_new();
    if (!response.body) {
        ZPATH_LOG(AL_WARNING, "Debug CB %s:%s could not allocate evbuffer", matched_host, matched_path);
        return FOHH_RESULT_ERR;
    }

    ZPATH_DEBUG_DEBUG("Did not find command matching Host = \"%s\", Path = \"%s\", checking commands...",
                      request->norm_host ? request->norm_host : "",
                      request->req_path ? request->req_path : "");

    evbuffer_add_printf(response.body,
                        "Could not find command matching Host = %s, Path = %s, checking commands\n",
                        matched_host,
                        matched_path);
    for (i = 0; i < set->all_commands_count; i++) {
        /* if rbac is enabled, send only the list of read commands */
        if (zpath_rbac_enabled &&
            (set->all_commands[i]->access_level > zpath_debug_cmd_read)) {
            continue;
        }
        if (strncmp(request->req_path ? request->req_path : "",
                    set->all_commands[i]->path, request->req_path ? strlen(request->req_path) : strlen("")) == 0) {
            evbuffer_add_printf(response.body,
                                "%s\n",
                                set->all_commands[i]->path);
        }
    }

    res = fohh_http_send_sync_response(connection, &response);
    return res;
}

static int zpath_debug_http_cb(struct fohh_http_server_connection *connection,
                              int64_t connection_incarnation,
                              struct fohh_http_request *request,
                              const char *matched_host,
                              const char *matched_path,
                              void *void_cookie,
                              int64_t int_cookie)
{
    struct fohh_http_response response;
    struct debug_command *cmd = void_cookie;
    struct fohh_http_query_string *qs;
    const char *query_values[ZPATH_DEBUG_MAX_QUERY_ARGS_COUNT];
    int res = 0;
    int err = 0;
    int i;

    memset(query_values, 0, sizeof(query_values));

    ZPATH_DEBUG_DEBUG("Matched debug command path \"%s\" to path \"%s\", desc = %s",
                      request->req_path ? request->req_path : "",
                      matched_path,
                      cmd->description);

    response.http_version_major = request->http_version_major;
    response.http_version_minor = request->http_version_minor;
    response.status = 200;
    response.content_type = NULL;
    response.extra_headers = NULL;
    response.body = evbuffer_new();
    if (!response.body) {
        ZPATH_LOG(AL_WARNING, "Debug CB %s:%s could not allocate evbuffer", matched_host, matched_path);
        return FOHH_RESULT_ERR;
    }

    /*
     * Perform access check here.
     *  If RBAC is disabled(via overrride); proceed with the command.
     *  If RBAC is enabled, allow only read commands to be executed via this path.
     */
    if (zpath_rbac_enabled) {
        if (cmd->access_level > zpath_debug_cmd_read) {
            evbuffer_add_printf(response.body, "Only read commands can be executed using curl; Please use icurl instead!\n");
            ZPATH_DEBUG_CURL_CMD("command-listener: Attempt to execute [%s] curl command: %s with RBAC enabled",
                            zpath_debug_get_cmd_access_level_str(cmd->access_level), request->req_url);
            evbuffer_set_dont_dump(response.body);
            res = fohh_http_send_sync_response(connection, &response);
            return res;
        }
    }

    for (qs = request->queries; qs; qs = qs->next) {
        if (strcmp("safe_mode", qs->key) == 0) {
            if (!(cmd->flags & DEBUG_COMMAND_FLAG_SAFE)) {
                ZPATH_LOG(AL_NOTICE, "Unsafe debug command for path <%s> not allowed.", cmd->path);
                err = 2;
                break;
            }
            continue; /* skipped */
        }
        for (i = 0; i < cmd->query_count; i++) {
            if (strcmp(qs->key, cmd->query_strings[i]) == 0) break;
        }
        if (i == cmd->query_count) {
            ZPATH_LOG(AL_NOTICE, "Invalid query <%s> encountered for path <%s>", qs->key, cmd->path);
            err = 1;
            break;
        }
        query_values[i] = qs->value;
    }

    if (!err) {
        /* Found that the pthread_mutex_unlock() call below this comment should be
         * pthread_mutex_lock() while doing RCA for ET-45166.
         *
         * It seemed like that the original intention was to prevent commands from
         * executing concurrently. Because pthread_mutex_unlock() has been called
         * instead of pthread_mutex_lock(), the intended protection hasn't worked
         * at all, however, no issues has been actually raised in the production.
         * If we replaced the pthread_mutex_unlock() with pthread_mutex_lock(),
         * it could rather introduce many false positive alerts in the production
         * because there are a bunch of scripts hitting command endpoints in order
         * to monitor a running Itasca application.
         *
         * ET-45166 could be the only case, so far, to be protected by the lock
         * because /san/check_leaks command might take more than 30 seconds and
         * eventually an FOHH thread processing the command would be killed by
         * heartbeat timeout. So we won't fix such a heartbeat timeout issue by
         * changing below code but merely comment out both pthread_mutex_unlock()
         * calls because they are no longer needed.
         */
        // pthread_mutex_unlock(&lock);
        res = (cmd->cb)((struct zpath_debug_state *) &response,
                        &(query_values[0]),
                        cmd->query_count,
                        cmd->cb_cookie);
        // pthread_mutex_unlock(&lock);
    }
    if (res || err) {
        /* Invalid query... */
        /* Clear out any request that might have been accumulated
         * by the callback */
        evbuffer_drain(response.body, evbuffer_get_length(response.body));
        if (err == 2) {
            evbuffer_add_printf(response.body, "Only commands marked as safe can be executed. err = %d\n", err);
        } else if (!cmd->query_count) {
            evbuffer_add_printf(response.body, "No query string allowed for this query. res = %s, err = %d\n", zpath_result_string(res), err);
        } else {
            evbuffer_add_printf(response.body, "Encountered query string error. Accepted queries:\n");
            for (i = 0; i < cmd->query_count; i++) {
                evbuffer_add_printf(response.body,
                                    "  %20s - %s\n",
                                    cmd->query_strings[i],
                                    cmd->query_descriptions[i]);
            }
        }
        ZPATH_DEBUG_CURL_CMD("CMD:Failure itascurl %s",request->req_url);
    } else {
        ZPATH_DEBUG_CURL_CMD("CMD:Success itascurl %s",request->req_url);
    }
    res = fohh_http_send_sync_response(connection, &response);

    return res;
}

/*
 * default callback on identifying path for command via domain socket
 */
static int zpath_debug_command_listener_default_cb(struct fohh_domain_sock_cmd_listener_info *command_info,
                                                   struct fohh_domain_sock_cmd_request *request,
                                                   const char *matched_host,
                                                   const char *matched_path,
                                                   void *void_cookie,
                                                   int64_t int_cookie)
{
    int i;
    struct debug_command_set *set = void_cookie;
    int res = ZPATH_RESULT_NO_ERROR;

    /* we are only concerned on updating the body in the response..Other fields are unused */
    command_info->response.body = evbuffer_new();
    if (!command_info->response.body) {
        ZPATH_LOG(AL_WARNING, "command-listener: default CB:  Host:%s Path:%s could not allocate evbuffer", matched_host, matched_path);
        return ZPATH_RESULT_ERR;
    }

    ZPATH_DEBUG_CMD_LISTENER("Did not find command matching Path = \"%s\", checking commands...",
                      request->req_path ? request->req_path : "");
    evbuffer_add_printf(command_info->response.body,
                        "Could not find command matching Host = %s, Path = %s, checking commands\n",
                        matched_host,
                        matched_path);

    enum zpath_debug_cmd_access_level user_access_level = zpath_debug_get_highest_access_level(command_info->user_info.peer_uid);

    for (i = 0; i < set->all_commands_count; i++) {
        /* if rbac is enabled, send only the list of commands the user has access to */
        if (zpath_rbac_enabled &&
            (set->all_commands[i]->access_level > user_access_level)) {
            continue;
        }
        if (strncmp(request->req_path ? request->req_path : "",
                    set->all_commands[i]->path, request->req_path ? strlen(request->req_path) : strlen("")) == 0) {
            evbuffer_add_printf(command_info->response.body,
                                "%s\n",
                                set->all_commands[i]->path);
        }
    }
    return res;
}

/*
 * Callback called on identifying path for command via domain socket
 *  response will be saved in the command info; which will be sent by the caller.
 */
static int zpath_debug_command_listener_cb(struct fohh_domain_sock_cmd_listener_info *command_info,
                                           struct fohh_domain_sock_cmd_request *request,
                                           const char *matched_host,
                                           const char *matched_path,
                                           void *void_cookie,
                                           int64_t int_cookie)
{
    struct debug_command *cmd = void_cookie;
    struct fohh_domain_sock_cmd_query_string *qs;
    const char *query_values[ZPATH_DEBUG_MAX_QUERY_ARGS_COUNT];
    int res = 0;
    int err = 0;
    int i;
    memset(query_values, 0, sizeof(query_values));

    ZPATH_DEBUG_CMD_LISTENER("command-listener: Matched debug command path \"%s\" to path \"%s\", desc = %s access_level: %s",
                      request->req_path ? request->req_path : "",
                      matched_path,
                      cmd->description,
                      zpath_debug_get_cmd_access_level_str(cmd->access_level));

    command_info->response.body = evbuffer_new();
    if (!command_info->response.body) {
        ZPATH_LOG(AL_WARNING, "command-listener: Callback: Host:%s Path:%s could not allocate evbuffer", matched_host, matched_path);
        return ZPATH_RESULT_ERR;
    }
    /* There are cases we may not have data to send, in such cases, we still need to send some response to client, hence the carriage return */
    evbuffer_add_printf(command_info->response.body,"\r");

    /*
     * Perform access check here.
     * If the user is not privileged enough to run the command, return a message in the body.
     * We do the access check only if the feature is enabled
     */
    if (zpath_rbac_enabled) {
        enum zpath_debug_cmd_access_level user_access_level = zpath_debug_get_highest_access_level(command_info->user_info.peer_uid);
        if (user_access_level < cmd->access_level) {
            evbuffer_add_printf(command_info->response.body, "User: %s has insufficient permissions(%s) to execute command with access level(%s)\n",
                    zpath_debug_get_username_from_uid(command_info->user_info.peer_uid),
                    zpath_debug_get_cmd_access_level_str(user_access_level),
                    zpath_debug_get_cmd_access_level_str(cmd->access_level));
            ZPATH_DEBUG_CURL_CMD("CMD:User: %s has insufficient permissions(%s) to execute [%s] command: %s",
                    zpath_debug_get_username_from_uid(command_info->user_info.peer_uid),
                    zpath_debug_get_cmd_access_level_str(user_access_level),
                    zpath_debug_get_cmd_access_level_str(cmd->access_level),
                    request->req_cmd);
            evbuffer_set_dont_dump(command_info->response.body);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* update the query strings in the command */
    for (qs = request->queries; qs; qs = qs->next) {
        if (strcmp("safe_mode", qs->key) == 0) {
            if (!(cmd->flags & DEBUG_COMMAND_FLAG_SAFE)) {
                ZPATH_DEBUG_CURL_CMD("command-listener: Unsafe debug command for path <%s> not allowed.", cmd->path);
                err = 2;
                break;
            }
            continue; /* skipped */
        }
        for (i = 0; i < cmd->query_count; i++) {
            if (strcmp(qs->key, cmd->query_strings[i]) == 0) break;
        }
        if (i == cmd->query_count) {
            ZPATH_DEBUG_CURL_CMD("command-listener: Invalid query <%s> encountered for path <%s>", qs->key, cmd->path);
            err = 1;
            break;
        }
        query_values[i] = qs->value;
    }

    /* now execute the callback func. for the command */
    if (!err) {
        res = (cmd->cb)((struct zpath_debug_state *) &(command_info->response),
                        &(query_values[0]),
                        cmd->query_count,
                        cmd->cb_cookie);
    }

    /* Fail case or invalid query */
    if (res || err) {
        /* Clear out any request */
        evbuffer_drain(command_info->response.body, evbuffer_get_length(command_info->response.body));
        if (err == 2) {
            evbuffer_add_printf(command_info->response.body, "Only commands marked as safe can be executed. err = %d\n", err);
        } else if (!cmd->query_count) {
            evbuffer_add_printf(command_info->response.body, "No query string allowed for this query. res = %s, err = %d\n", zpath_result_string(res), err);
        } else {
            evbuffer_add_printf(command_info->response.body, "Encountered query string error. Accepted queries:\n");
            for (i = 0; i < cmd->query_count; i++) {
                evbuffer_add_printf(command_info->response.body,
                                    "  %20s - %s\n",
                                    cmd->query_strings[i],
                                    cmd->query_descriptions[i]);
            }
        }
        ZPATH_DEBUG_CURL_CMD("CMD:Failure ICURL %s", request->req_cmd);
    } else {
        ZPATH_DEBUG_CURL_CMD("CMD:Success ICURL %s", request->req_cmd);
    }

    evbuffer_set_dont_dump(command_info->response.body);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_set_fohh_snd_buf(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    int sock_sndbuf;
    int result;
    if (!query_values[0] || (query_value_count < 3) || !query_values[1] || !query_values[2]) {
        return ZPATH_RESULT_ERR;
    } else {
        sock_sndbuf = strtol(query_values[2], NULL, 0);
        result = fohh_set_socket_snd_buf(query_values[0], query_values[1], fohh_connection_type_server, sock_sndbuf);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Socket sndbuf is set to %d\n", sock_sndbuf);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n",
                                           query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set socket sndbuf\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_auto_tune_disable(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    int auto_tune_disable;
    int result;

    if (!query_values[0] || (query_value_count < 2) || !query_values[1]) {
        return ZPATH_RESULT_ERR;
    } else {
        result = fohh_toggle_auto_tune_disable(query_values[0], query_values[1], fohh_connection_type_server, &auto_tune_disable);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Auto tune is is set to %d\n", auto_tune_disable);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n", query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set socket sndbuf\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_tune_delay_optimized(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    int delay_optimized;
    int result;

    if (!query_values[0] || (query_value_count < 2) || !query_values[1]) {
        return ZPATH_RESULT_ERR;
    } else {
        result = fohh_toggle_delay_optimized(query_values[0], query_values[1], fohh_connection_type_server, &delay_optimized);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Delay optimized is set to %d\n", delay_optimized);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n", query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set socket sndbuf\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_tune_chunk_size(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int chunk_size;
    int result;
    if (!query_values[0] || (query_value_count < 3)) {
        return ZPATH_RESULT_ERR;
    } else {
        chunk_size = strtol(query_values[2], NULL, 0);
        result = fohh_set_chunk_size(query_values[0], query_values[1], fohh_connection_type_server, chunk_size);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Chunk size is set to %d\n", chunk_size);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n",
                                           query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set chunk size\n");
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_tune_allowed_chunk(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int allowed_chunk;
    int result;
    if (!query_values[0] || (query_value_count < 3) || !query_values[1] || !query_values[2]) {
        return ZPATH_RESULT_ERR;
    } else {
        allowed_chunk = strtol(query_values[2], NULL, 0);
        result = fohh_set_allowed_chunk(query_values[0], query_values[1], fohh_connection_type_server, allowed_chunk);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Allowed chunk is set to %d\n", allowed_chunk);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n",
                                           query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set allowed chunk\n");
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_tune_show_config(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int delay_optimized;
    int sock_sndbuf;
    int allowed_chunk;
    int chunk_size;
    int auto_tune_disable;
    int result;

    if (!query_values[0] || (query_value_count < 2) || !query_values[1]) {
        return ZPATH_RESULT_ERR;
    } else {
        result = fohh_show_tune_values(query_values[0], query_values[1], fohh_connection_type_server,
                                       &sock_sndbuf, &delay_optimized, &chunk_size, &allowed_chunk, &auto_tune_disable);
        if (result == ZPATH_RESULT_NO_ERROR) {
            zpath_debug_cb_printf_response(request_state, "Tune Configuration:\n");
            zpath_debug_cb_printf_response(request_state, " - sock_sndbuf = %d\n", sock_sndbuf);
            zpath_debug_cb_printf_response(request_state, " - delay_optimized = %d\n", delay_optimized);
            zpath_debug_cb_printf_response(request_state, " - chunk_size = %d\n", chunk_size);
            zpath_debug_cb_printf_response(request_state, " - allowed_chunk = %d\n", allowed_chunk);
            zpath_debug_cb_printf_response(request_state, " - auto_tune_disable = %d\n", auto_tune_disable);
        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s not found.\n", query_values[0]);
        } else if (result == ZPATH_RESULT_BAD_STATE) {
            zpath_debug_cb_printf_response(request_state, "Connection with domain %s is not connected\n",
                                           query_values[0]);
        } else {
            zpath_debug_cb_printf_response(request_state, "Could not set socket sndbuf\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_show_safe_commands(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int i;
    struct debug_command_set *set = &all_commands;
    for (i = 0; i < set->all_commands_count; i++) {
        if (set->all_commands[i]->flags & DEBUG_COMMAND_FLAG_SAFE) {
            zpath_debug_cb_printf_response(request_state, "%s\n", set->all_commands[i]->path);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_show_all_commands(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int i;
    enum zpath_debug_cmd_access_level access_level = zpath_debug_cmd_min;
    struct debug_command_set *set = &all_commands;

    /* If no option is provided, show all commands */
    if (!query_values[0]) {
        for (i = 0; i < set->all_commands_count; i++) {
            zpath_debug_cb_printf_response(request_state, "[%*s]:  %s\n",
                                5, zpath_debug_get_cmd_access_level_str(set->all_commands[i]->access_level),
                                set->all_commands[i]->path);
        }
    } else {
        /* value has to be one of read, write or admin; else fail the command */
        enum zpath_debug_cmd_access_level level;
        int is_valid_input = 0;
        for (level = zpath_debug_cmd_min; level <= zpath_debug_cmd_max; level++) {
            if (0 == strcmp(query_values[0], zpath_debug_get_cmd_access_level_str(level))) {
                is_valid_input = 1;
                access_level = level;
                break;
            }
        }
        //did not find a valid input; bail out.
        if (!is_valid_input) {
            ZDP("Invalid command; Provide one of [read, write, admin] as option. \n");
            return ZPN_RESULT_NO_ERROR;
        }
        //now dump all commands matching the level.
        for (i = 0; i < set->all_commands_count; i++) {
            if (set->all_commands[i]->access_level == access_level) {
                zpath_debug_cb_printf_response(request_state, "[%*s]:  %s\n",
                                    5, zpath_debug_get_cmd_access_level_str(set->all_commands[i]->access_level),
                                    set->all_commands[i]->path);
            }
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* debug command to check role_based access control status */
static int zpath_debug_show_rbac_status(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    ZDP("Role-based access control is %s\n", zpath_rbac_supported ? "supported" : "not-supported");
    ZDP("Role-based access control is %s\n", zpath_rbac_enabled ? "enabled" : "disabled");
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_add_command_internal(int flags,
                            enum zpath_debug_cmd_access_level access_level,
                            const char *description,
                            char *path,
                            zpath_debug_callback_f *cb,
                            void *cb_cookie,
                            va_list vl,
                            int va_count)
{
    struct debug_command *cmd = NULL;
    int i;
    int res;
    int count;
    struct debug_command_set *set = &all_commands;

    if (cb == NULL) {
        ZPATH_LOG(AL_CRITICAL, "Attempt to register command with no callback %s:%s", path, description);
        return ZPATH_RESULT_ERR;
    }
    if ((va_count & 1) == 1) {
        /* Must have odd number of varargs. */
        ZPATH_LOG(AL_ERROR, "Error, debug path <%s> must have an even number of string vararg arguments (%d)",
                  path, va_count);
        goto failure;
    }
    count = va_count / 2;

    pthread_mutex_lock(&lock);

    /*
     * If no http server is created, it means it is just not initialized. This will happen when the app don't want a
     * http server to debug
     */
    if (NULL == zpath_debug_http_server) {
        pthread_mutex_unlock(&lock);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = fohh_http_server_is_registered(zpath_debug_http_server, "*", path , NULL, NULL);
    if (res == FOHH_RESULT_NO_ERROR) {
        pthread_mutex_unlock(&lock);
        ZPATH_LOG(AL_ERROR, "Attempt to register duplicate command %s:%s", path, description);
        return ZPATH_RESULT_ERR;
    }


    if (set->all_commands_count == ZPATH_DEBUG_MAX_COMMANDS) {
        ZPATH_LOG(AL_ERROR, "Reached maximum number of debug commands (%d) for command '%s'", ZPATH_DEBUG_MAX_COMMANDS,
                  path ? path : "");
        pthread_mutex_unlock(&lock);
    	return ZPATH_RESULT_ERR;
    }

    // check if we are close to limit and print in logs
    if (set->all_commands_count >= ZPATH_DEBUG_MAX_COMMANDS_WARNING) {
        ZPATH_LOG(AL_NOTICE, "Reached number of debug commands %d close to limit %d for command '%s'",
                  set->all_commands_count, ZPATH_DEBUG_MAX_COMMANDS, path ? path : "");
    }
    cmd = (struct debug_command *) ZLIB_MALLOC(sizeof(*cmd));
    if (!cmd) {
		ZPATH_LOG(AL_ERROR, "Failure in malloc");
		pthread_mutex_unlock(&lock);
    	return ZPATH_RESULT_ERR;
	}
    cmd->flags = flags;
	cmd->query_strings = NULL;
	cmd->query_descriptions = NULL;

    cmd->path = ZLIB_STRDUP(path, strlen(path));
    if (!cmd->path) {
		ZPATH_LOG(AL_ERROR, "Failure in dup");
		ZLIB_FREE(cmd);
        pthread_mutex_unlock(&lock);
        return ZPATH_RESULT_ERR;
	}

    cmd->description = ZLIB_STRDUP(description, strlen(description));
    if (!cmd->description) {
		ZPATH_LOG(AL_ERROR, "Failure in dup");
		ZLIB_FREE(cmd->path);
        ZLIB_FREE(cmd);
        pthread_mutex_unlock(&lock);
        return ZPATH_RESULT_ERR;
	}

    cmd->query_count = 0;
    if (count) {
        cmd->query_strings = (char **) ZLIB_MALLOC(sizeof (char *) * count);
        if (!cmd->query_strings) goto failure;
        memset(cmd->query_strings, 0, sizeof(char *) * count);

        cmd->query_descriptions = (char **) ZLIB_MALLOC(sizeof (char *) * count);
        if (!cmd->query_descriptions) goto failure;
        memset(cmd->query_descriptions, 0, sizeof(char *) * count);

        for (i = 0; i < count; i++) {
            const char *string = va_arg(vl, const char *);
            const char *desc = va_arg(vl, const char *);

            cmd->query_strings[i] = ZLIB_STRDUP(string, strlen(string));
            if (!cmd->query_strings[i]) goto failure;

            cmd->query_descriptions[i] = ZLIB_STRDUP(desc, strlen(desc));
            if (!cmd->query_descriptions[i]) goto failure;
        }
        cmd->query_count = count;
    }
    if (cmd->query_count > ZPATH_DEBUG_MAX_QUERY_ARGS_COUNT) {
        ZPATH_LOG(AL_ERROR, "We support only debug args upto %d", ZPATH_DEBUG_MAX_QUERY_ARGS_COUNT);
        goto failure;

    }

    cmd->cb = cb;
    cmd->cb_cookie = cb_cookie;
    cmd->access_level = access_level;

    res = fohh_http_server_register(zpath_debug_http_server, "*", path, zpath_debug_http_cb, cmd, 0);
    if (res) goto failure;

    /* register callback for the path to be called on command invocation */
    res = fohh_http_server_update_path_command_req_callback(zpath_debug_http_server, "*", path, zpath_debug_command_listener_cb);
    if (res) goto failure;

    set->all_commands[set->all_commands_count] = cmd;
    set->all_commands_count++;

    qsort(&(set->all_commands[0]),
          set->all_commands_count,
          sizeof(set->all_commands[0]),
          zpath_debug_comparator);

    pthread_mutex_unlock(&lock);
    return ZPATH_RESULT_NO_ERROR;

 failure:
    ZPATH_LOG(AL_ERROR, "Failure. (Likely malloc or dup)");
    if (cmd) {
        if (cmd->path) ZLIB_FREE(cmd->path);
        if (cmd->description) ZLIB_FREE(cmd->description);
        if (cmd->query_strings) {
            for (i = 0; i < count; i++) {
                if (cmd->query_strings[i]) ZLIB_FREE(cmd->query_strings[i]);
            }
            ZLIB_FREE(cmd->query_strings);
        }
        if (cmd->query_descriptions) {
            for (i = 0; i < count; i++) {
                if (cmd->query_descriptions[i]) ZLIB_FREE(cmd->query_descriptions[i]);
            }
            ZLIB_FREE(cmd->query_descriptions);
        }
        ZLIB_FREE(cmd);
    }
    pthread_mutex_unlock(&lock);
    return ZPATH_RESULT_ERR;
}

int zpath_debug_add_safe_read_command(const char *description, char *path, zpath_debug_callback_f *cb, void *cb_cookie, ...)
{
    int res;
    int va_count = 0;
    va_list vl;
    va_start(vl, cb_cookie);
    while (va_arg(vl, const char *)) {
        va_count++;
    }
    va_start(vl, cb_cookie);
    res = zpath_debug_add_command_internal(DEBUG_COMMAND_FLAG_SAFE, zpath_debug_cmd_read, description, path, cb, cb_cookie, vl, va_count);
    va_end(vl);
    return res;
}

int zpath_debug_add_read_command(const char *description, char *path, zpath_debug_callback_f *cb, void *cb_cookie, ...)
{
    int res;
    int va_count = 0;
    va_list vl;
    va_start(vl, cb_cookie);
    while (va_arg(vl, const char *)) {
        va_count++;
    }
    va_start(vl, cb_cookie);
    res = zpath_debug_add_command_internal(0, zpath_debug_cmd_read, description, path, cb, cb_cookie, vl, va_count);
    va_end(vl);
    return res;
}

int zpath_debug_add_write_command(const char *description, char *path, zpath_debug_callback_f *cb, void *cb_cookie, ...)
{
    int res;
    int va_count = 0;
    va_list vl;
    va_start(vl, cb_cookie);
    while (va_arg(vl, const char *)) {
        va_count++;
    }
    va_start(vl, cb_cookie);
    res = zpath_debug_add_command_internal(0, zpath_debug_cmd_write, description, path, cb, cb_cookie, vl, va_count);
    va_end(vl);
    return res;
}

int zpath_debug_add_admin_command(const char *description, char *path, zpath_debug_callback_f *cb, void *cb_cookie, ...)
{
    int res;
    int va_count = 0;
    va_list vl;
    va_start(vl, cb_cookie);
    while (va_arg(vl, const char *)) {
        va_count++;
    }
    va_start(vl, cb_cookie);
    res = zpath_debug_add_command_internal(0, zpath_debug_cmd_admin, description, path, cb, cb_cookie, vl, va_count);
    va_end(vl);
    return res;
}

int zpath_debug_add_diagnostic_cb(const char *name,
                            const char *detail_path,
                            zpath_debug_diag_callback_f *cb,
                            void *cb_cookie)
{
    struct diagnostic_command *cmd;
    const char* s;
    int res = ZPATH_RESULT_NO_ERROR;
    if (cb == NULL) {
        ZPATH_LOG(AL_CRITICAL, "Attempt to register diagnostic command with no callback %s", name ? name : "?");
        return ZPATH_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&diagnostics.lock, __FILE__, __LINE__);
    {
        if (diagnostics.count == ZPATH_DEBUG_MAX_DIAGNOSTIC_COMMANDS) {
            ZPATH_LOG(AL_ERROR, "Reached maximum number of diagnostics commands (%d)", ZPATH_DEBUG_MAX_DIAGNOSTIC_COMMANDS);
            res = ZPATH_RESULT_ERR;
        } else {
            cmd = (struct diagnostic_command *) ZLIB_MALLOC(sizeof(*cmd));
            s = name ? name : "unknown";
            cmd->name = ZLIB_STRDUP(s, strlen(s));
            s = detail_path ? detail_path : "n/a";
            cmd->detail_path = ZLIB_STRDUP(s, strlen(s));
            cmd->cb = cb;
            cmd->cb_cookie = cb_cookie;
            diagnostics.commands[diagnostics.count++] = cmd;
        }
    }
    ZPATH_MUTEX_UNLOCK(&diagnostics.lock, __FILE__, __LINE__);
    return res;
}

static int zpath_debug_run_diagnostics(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int res, i;
    for (i = 0; i < diagnostics.count; i++) {
        ZDP("****** [%d/%d] %s (%s) ******\n",
                i+1, diagnostics.count, diagnostics.commands[i]->name, diagnostics.commands[i]->detail_path);
        res = diagnostics.commands[i]->cb(request_state, diagnostics.commands[i]->cb_cookie, query_values[0] ? 1 : 0);
        if (res) {
            ZDP("Error: command %s returned %s.\n", diagnostics.commands[i]->name, zpath_result_string(res));
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}


void zpath_debug_cb_printf_response(struct zpath_debug_state *request_state,
                                    const char *format,
                                    ...)
{
    struct fohh_http_response *resp = (struct fohh_http_response *) request_state;
    va_list va;
    int res;

    if (!resp->body) {
        resp->body = evbuffer_new();
        if (!resp->body) {
            ZPATH_LOG(AL_ERROR, "Allocation failure");
            return;
        }
    }

    va_start(va, format);
    res = evbuffer_add_vprintf(resp->body, format, va);
    if (res < 0) {
        ZPATH_LOG(AL_ERROR, "Printf failure: format str = %s", format);
    } else {
    }
    va_end(va);

    return;
}

int zpath_debug_cb_http_error(struct zpath_debug_state *request_state, int error_code, char *error_string)
{
    struct evhttp_request *req = (struct evhttp_request *) request_state;
    evhttp_send_reply(req, error_code, error_string, NULL);

    return ZPATH_RESULT_NO_ERROR;
}

static void zpath_debug_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct argo_log_registered_structure *s;
    struct zthread_info *threads;
    static int registered_thread_count = 0;
    int thread_count;

    struct zthread_group_info *groups;
    static int registered_group_count = 0;
    int group_count;

    size_t i;
    int64_t last_hb_us = epoch_us();
    zthread_heartbeat(zthread);
    int64_t     current_total_outstanding_bytes;
    int64_t     current_allocator_outstanding_bytes;


    /* Clean up all the delayed free queues. */
    current_total_outstanding_bytes = 0;
    current_allocator_outstanding_bytes = 0;
    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        int64_t now_us;
	    /*
	     *Record this instance as the peak utilization, if applicable.
	     */
	    current_allocator_outstanding_bytes = allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes;
	    current_total_outstanding_bytes += current_allocator_outstanding_bytes;
	    if ((allocator[i]->peak_stats.allocation_bytes - allocator[i]->peak_stats.free_bytes) <
			    current_allocator_outstanding_bytes) {
		    memcpy(&allocator[i]->peak_stats, &allocator[i]->stats, sizeof(allocator[i]->peak_stats));
		    allocator[i]->peak_stats_hit_epoch_s = epoch_s();
	    }
        zpath_free_drain_timed(allocator[i], 30, __LINE__, __FILE__, US_PER_SEC); /* 1s max per allocator */
#ifdef ZPATH_MALLOC_OVERWRITE_FULL
        {
            int count;
            count  = zpath_malloc_check(allocator[i]);
            ZPATH_LOG(AL_DEBUG, "%20s: Tested allocations: %d", allocator_name[i], count);
        }
#endif
        /* heartbeat if free_drain took long... */
        now_us = epoch_us();
        if (now_us >= last_hb_us + US_PER_SEC) {
            zthread_heartbeat(zthread);
            last_hb_us = now_us;
        }
    }
    pthread_mutex_unlock(&allocator_lock);

    /*
     * We have found a new peak in terms of memory utilization. Let us record this state.
     */
    if (current_total_outstanding_bytes > zpath_allocator_peak_snapshot.peak_total_outstanding_bytes) {
        zpath_allocator_peak_snapshot.peak_total_outstanding_bytes = current_total_outstanding_bytes;
        zpath_allocator_peak_snapshot.peak_time_epoch_s = epoch_s();
        pthread_mutex_lock(&allocator_lock);
        for (i = 0; i < allocator_count; i++) {
            memcpy(&zpath_allocator_peak_snapshot.allocator[i].stats, &allocator[i]->stats,
                   sizeof(zpath_allocator_peak_snapshot.allocator[i].stats));
            memcpy(&zpath_allocator_peak_snapshot.allocator[i].peak_stats, &allocator[i]->peak_stats,
                   sizeof(zpath_allocator_peak_snapshot.allocator[i].peak_stats));
            zpath_allocator_peak_snapshot.allocator[i].allocator_peak_stats_hit_timestamp_s =
                    allocator[i]->peak_stats_hit_epoch_s;
        }
        pthread_mutex_unlock(&allocator_lock);
    }

    /* Get state for all threads, and start up a stats counter for
     * those that have not yet had it done for them */
    threads = zthread_state_get(&thread_count);
    for (;registered_thread_count < thread_count; registered_thread_count++) {
        char str[1000];

        if (threads[registered_thread_count].user_int) {
            snprintf(str, sizeof(str), "rusage_thread_%s", threads[registered_thread_count].stack.thread_name);
            s = argo_log_register_structure(argo_log_get("statistics_log"),
                                            str,
                                            AL_INFO,
                                            threads[registered_thread_count].user_int,
                                            zthread_rusage_desc,
                                            &(threads[registered_thread_count].rusage),
                                            1,
                                            NULL,
                                            NULL);
            if (!s) {
                ZPATH_LOG(AL_ERROR, "Could not set up statistics logging for %s", str);
            }
        }
    }

    /* stats counter for thread groups */
    groups = zthread_group_state_get(&group_count);
    for (;registered_group_count < group_count; registered_group_count++) {
        char str[1000];
        snprintf(str, sizeof(str), "rusage_group_%s", groups[registered_group_count].name);
        s = argo_log_register_structure(argo_log_get("statistics_log"),
                                        str,
                                        AL_INFO,
                                        60*1000*1000,
                                        zthread_rusage_desc,
                                        &(groups[registered_group_count].rusage),
                                        1,
                                        NULL,
                                        NULL);
        if (!s) {
            ZPATH_LOG(AL_ERROR, "Could not set up statistics logging for %s", str);
        }
    }
}

void *zpath_debug_thread(struct zthread_info *zthread_arg, void *cookie)
{
    int *init_complete = (int *) cookie;
    struct event *timer_event;
    struct timeval tv;

    /* Need an event_base for our timer... */
    base = event_base_new();
    if (!base) {
        ZPATH_LOG(AL_ERROR, "Could not create debug thread event_base");
        *init_complete = ZPATH_RESULT_ERR;
        return NULL;
    }

    /* We need a timer event, for watchdogs. */
    timer_event = event_new(base, -1, EV_PERSIST, zpath_debug_timer_callback, NULL);
    if (!timer_event) {
        ZPATH_LOG(AL_ERROR, "Could not create timer event for debug thread");
        return NULL;
    }

    /* Trigger timer every second. */
    tv.tv_sec = 1;
    tv.tv_usec = 0;
    if (event_add(timer_event, &tv)) {
        ZPATH_LOG(AL_ERROR, "Could not add timer event for debug thread");
        return NULL;
    }

    zthread = zthread_arg;

    *init_complete = ZPATH_RESULT_NO_ERROR;

    zevent_base_dispatch(base);

    ZPATH_LOG(AL_ERROR, "Event_base completed");

    return NULL;
}


/*
 * Initialize the non-event pieces of the debugging state, so commands
 * can be registered, etc.
 */
int zpath_debug_pre_init(int init_http_server)
{
    static int initialized = 0;

    if (initialized) goto done;

    initialized = 1;

    diagnostics.lock = ZPATH_MUTEX_INIT;
    diagnostics.count = 0;

    /* Iniitalize state for tracking debug commands. */
    lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    zpath_misc_register_allocator_method(&zpath_debug_add_allocator);

    if (0 == init_http_server) {
        goto done;
    }

    zpath_debug_http_server = fohh_http_server_create("Debug HTTP Server");
    if (!zpath_debug_http_server) {
        return ZPATH_RESULT_NO_MEMORY;
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}

struct fohh_http_server *zpath_debug_get_http_server(void)
{
    return zpath_debug_http_server;
}


static int zpath_set_max_debug_messages(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int i;
    if (!query_values[0]) {
        ZDP("Missing argument: count\n");
    } else {
        i = strtol(query_values[0], NULL, 0);
        if (argo_log_set_max_text_per_line_per_s(i)) {
            ZDP("Failed\n");
        } else {
            ZDP("Max debug messages per line set to %d\n", i);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

#define ZPATH_DEBUG_REF_THREAD_GROUP_NAME "dbg-ref-threads"
#define ZPATH_DEBUG_REF_THREAD_GROUP_SIZE 3
int ref_thread_cpu_target_pct = 50;
int64_t ref_thread_run_till_us = 0;

void zpath_debug_ref_function(struct zthread_info *zthread)
{
    while(1) {
        int64_t busy_us = (ref_thread_cpu_target_pct * 10000);
        int64_t idle_us = 1000000 - busy_us;
        int64_t now = epoch_us();

        zthread_heartbeat(NULL);

        /* not our turn to run */
        if (now > ref_thread_run_till_us) {
            sleep(1);
            continue;
        }

        /* try to consume ref_thread_cpu_target_pct percent of a CPU by:
         *  - dead, tight loop for ref_thread_cpu_target_pct percent of a second
         *  - then idle for the rest of the second */
        do { } while (busy_us > 0 && (epoch_us() - now) < busy_us);

        if (idle_us > 0) usleep(idle_us);
    }
}

static void *zpath_debug_ref_thread(struct zthread_info *zthread, void *cookie)
{
    struct zthread_group_info* group = (struct zthread_group_info*)cookie;

    /* this is only to ensure thread name shows up in cores */
    char msg[256];
    snprintf(msg, sizeof(msg), "starting thread %s", zthread->stack.thread_name);
    ZPATH_LOG(AL_INFO, "%s", msg);

    zthread_group_add_thread(group, zthread->stack.thread_num);

    zpath_debug_ref_function(zthread);

    return NULL;
}

static int zpath_debug_run_ref_thread_group(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    /* just to make sure these debug commands don't get called simutaneously
     * and we end up creating extra threads than intended. */
    static pthread_mutex_t ref_thread_group_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

    int i;
    int res = ZPATH_RESULT_NO_ERROR;
    int target_pct = 0;
    int num_sec = 0;
    char name[100];
    pthread_t thread;

    if (!query_values[0] || !query_values[1]) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    num_sec = atoi(query_values[0]);
    if (num_sec <= 0 || num_sec > 24*3600) { /* do not allow more than a day */
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    target_pct = atoi(query_values[1]);
    if (target_pct <= 0 || target_pct > 100) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    struct zthread_group_info* group = zthread_group_get_or_create(ZPATH_DEBUG_REF_THREAD_GROUP_NAME);
    if (!group) {
        ZDP("Failed - can't create group.\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    pthread_mutex_lock(&ref_thread_group_lock);

    ref_thread_cpu_target_pct = target_pct;
    ref_thread_run_till_us = epoch_us() + num_sec * 1000000;

    for (i = group->thread_count; i < ZPATH_DEBUG_REF_THREAD_GROUP_SIZE; i++) {
        snprintf(name, sizeof(name), "dbg-ref-thread-%d", i+1);
        res = zthread_create(&thread,
                            zpath_debug_ref_thread,
                            group,         /* cookie */
                            name,
                            20,            /* heartbeat */
                            1*1024*1024,   /* small stack size compare to regular threads */
                            60*1000*1000,  /* 60s stats, yes we do need them... */
                            NULL);
        if (res) break;
    }
    pthread_mutex_unlock(&ref_thread_group_lock);

    if (res) {
        ZDP("Failed - can't create thread: %s\n", zpath_result_string(res));
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Caveat:
 * 1. delta can be wrong as last_heartbeat_epoch variable is not thread protected. So if you see a high value for
 * delta, think integer underflow!
 */
static int zpath_thread_status(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    struct zthread_info *threads;
    int thread_count;
    int64_t temp_max_timeout;
    int i;

    threads = zthread_state_get(&thread_count);
    if (threads) {
        if (zpath_zthread_hb_override_timer) {
            ZDP("Heartbeat Override Remaining Duration: %ds \n", zpath_zthread_hb_get_remaining_override_duration());
        }

        for (i = 0; i < thread_count; i++) {
            /*
             * don't try to optimize epoch_s call below, done intentionally to minimize the delta calculation issues
             * as there is no thread locking around that variable updates.
             */
            temp_max_timeout=zthread_get_heartbeat_override_for_thread(i);
            zpath_debug_cb_printf_response(request_state,
                                           "Thread %3d, %90s, delta: %3d, max: %3"PRId64", override_max: %3"PRId64", stack_kb: %5d, nice: %3d, ev_init: %d, heartbeats=%9ld\n",
                                           i,
                                           threads[i].stack.thread_name,
                                           (int)(monotime_s() - threads[i].last_heartbeat_monotime_s),
                                           threads[i].maximum_heartbeat_delta_s,
                                           temp_max_timeout,
                                           (int)threads[i].stack_size / 1024,
                                           threads[i].nice,
                                           zevent_thread_initialized(i),
                                           (long)threads[i].heartbeats);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_thread_usage(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    struct zthread_info *threads;
    int thread_count;
    int i;
    int show_history = 0;
    char tmp_buf[10000];

    if (query_values[0]) show_history = 1;

    threads = zthread_state_get(&thread_count);
    if (threads) {
        for (i = 0; i < thread_count; i++) {
            zthread_usage_dump(&threads[i].rusage, &threads[i].cpu, tmp_buf, sizeof(tmp_buf));
            zpath_debug_cb_printf_response(request_state, "Thread %3d, %90s, %s\n", i, threads[i].stack.thread_name, tmp_buf);

            if (show_history) {
                zthread_usage_history_dump(&(threads[i].short_interval_usage_history), tmp_buf, sizeof(tmp_buf));
                zpath_debug_cb_printf_response(request_state, "short-interval stats history:\n%s\n", tmp_buf);
                zthread_usage_history_dump(&(threads[i].long_interval_usage_history), tmp_buf, sizeof(tmp_buf));
                zpath_debug_cb_printf_response(request_state, "long-interval stats history:\n%s\n", tmp_buf);
            }
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_thread_group_usage(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    struct zthread_group_info *groups;
    int group_count;
    int i;
    char tmp_buf[10000];

    groups = zthread_group_state_get(&group_count);
    for (i = 0; i < group_count; i++) {
        zthread_usage_dump(&groups[i].rusage, &groups[i].cpu, tmp_buf, sizeof(tmp_buf));
        zpath_debug_cb_printf_response(request_state, "Thread-Group %2d, %90s, threads: %2d, %s\n",
                                        i,
                                        groups[i].name,
                                        groups[i].thread_count,
                                        tmp_buf);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * called from anywhere in the code.
 */
void
zpath_allocator_log()
{
    size_t i;

    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        ZPATH_LOG(AL_INFO, "%s: Alloc: %ld %ld, Free: %ld %ld, Drain: %ld %ld, Out: %ld %ld %0.3fmb\n",
            allocator_name[i],
            (long) allocator[i]->stats.allocations,
            (long) allocator[i]->stats.allocation_bytes,
            (long) allocator[i]->stats.frees,
            (long) allocator[i]->stats.free_bytes,
            (long) allocator[i]->stats.drain_queue,
            (long) allocator[i]->stats.drain_queue_bytes,
            (long) (allocator[i]->stats.allocations - allocator[i]->stats.frees),
            (long) (allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes),
            (double) (((double)(allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes)) / (1024.0*1024.0)));

        // the removed code here would never be executed as show_lines is always 0
    }
    pthread_mutex_unlock(&allocator_lock);
}


/*
 * called from anywhere in the code.
 */
void
zpath_allocator_log_highest_consumer_only()
{
    int     highest_mem_consumer_idx;
    int64_t highest_mem_consumer_bytes;
    size_t     allocator_iter;

    highest_mem_consumer_idx = 0;
    highest_mem_consumer_bytes = 0;
    pthread_mutex_lock(&allocator_lock);
    for (allocator_iter = 0; allocator_iter < allocator_count; allocator_iter++) {
        int64_t current_allocator_outstanding_mem = allocator[allocator_iter]->stats.allocation_bytes - allocator[allocator_iter]->stats.free_bytes;
        if (current_allocator_outstanding_mem  > highest_mem_consumer_bytes) {
            highest_mem_consumer_bytes = current_allocator_outstanding_mem;
            highest_mem_consumer_idx = allocator_iter;
        }
    }

    ZPATH_LOG(AL_INFO, "Highest memory consuming allocator - %s: Alloc: %"PRId64" %"PRId64", Free: %"PRId64" %"PRId64", Drain: %"PRId64" %"PRId64", Out: %"PRId64" %"PRId64" %0.3fmb",
              allocator_name[highest_mem_consumer_idx],
              allocator[highest_mem_consumer_idx]->stats.allocations,
              allocator[highest_mem_consumer_idx]->stats.allocation_bytes,
              allocator[highest_mem_consumer_idx]->stats.frees,
              allocator[highest_mem_consumer_idx]->stats.free_bytes,
              allocator[highest_mem_consumer_idx]->stats.drain_queue,
              allocator[highest_mem_consumer_idx]->stats.drain_queue_bytes,
              (allocator[highest_mem_consumer_idx]->stats.allocations - allocator[highest_mem_consumer_idx]->stats.frees),
              (allocator[highest_mem_consumer_idx]->stats.allocation_bytes - allocator[highest_mem_consumer_idx]->stats.free_bytes),
              (double) (((double)(allocator[highest_mem_consumer_idx]->stats.allocation_bytes - allocator[highest_mem_consumer_idx]->stats.free_bytes)) / (1024.0 * 1024.0)));

    pthread_mutex_unlock(&allocator_lock);
    // the removed code here would never be executed as show_lines is always 0

}

//Caller must free the return value
char *zpath_specific_allocator_status(const char *name)
{
    int i = 0;
    char *alloc_stats = NULL;

    if(!name)
        return NULL;

    alloc_stats = ZLIB_CALLOC(ZTHREAD_BUFFER_SIZE);

    if(!alloc_stats)
        return NULL;

    pthread_mutex_lock(&allocator_lock);
    for (i = 0;i < allocator_count; i++){
         if (strcmp(name, allocator_name[i])) continue;

        snprintf(alloc_stats, ZTHREAD_BUFFER_SIZE, "Alloc: %"PRId64" %"PRId64", Free: %"PRId64" %"PRId64", Drain: %"PRId64" %"PRId64", Out: %"PRId64" %"PRId64" %9.3fmb  Overhead: %9.3fmb (%zu)",
            allocator[i]->stats.allocations,
            allocator[i]->stats.allocation_bytes,
            allocator[i]->stats.frees,
            allocator[i]->stats.free_bytes,
            allocator[i]->stats.drain_queue,
            allocator[i]->stats.drain_queue_bytes,
            (allocator[i]->stats.allocations - allocator[i]->stats.frees),
            (allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes),
            (double) (((double)(allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes)) / (1024.0*1024.0)),
            (long) ((allocator[i]->stats.allocations - allocator[i]->stats.frees)*allocator[i]->overhead_per_allocation) / (1024.0*1024.0),
            allocator[i]->overhead_per_allocation);
        break;
    }
    pthread_mutex_unlock(&allocator_lock);
    return alloc_stats;
}

/*
 * called from debug curl command
 */
int zpath_allocator_status(struct zpath_debug_state *request_state,
                           const char **query_values,
                           int query_value_count,
                           void *cookie)
{
    size_t i;
    int show_peak = 0;
    int show_lines = 0;
    uint64_t allocations = 0;
    uint64_t allocation_bytes = 0;
    uint64_t frees = 0;
    uint64_t free_bytes = 0;
    uint64_t drain_queue = 0;
    uint64_t drain_queue_bytes = 0;
    uint64_t overhead_bytes = 0;

#ifdef ZPATH_MALLOC_DEBUG
    int64_t min = 0;
#endif // ZPATH_MALLOC_DEBUG
    const char *limit_name = NULL;

    if (query_values[0]) show_peak = 1;
    if (query_values[1]) show_lines = 1;
#ifdef ZPATH_MALLOC_DEBUG
    if (query_values[2]) min = strtoul(query_values[2], NULL, 0);
#endif // ZPATH_MALLOC_DEBUG
    if (query_values[3]) limit_name = query_values[3];

    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        if (limit_name) {
            if (strcmp(limit_name, allocator_name[i])) continue;
        }
        ZDP("%20s: Alloc: %14ld %14ld, Free: %14ld %14ld, Drain: %14ld %14ld, Out: %14ld %14ld %9.3fmb  Overhead: %9.3fmb (%zu)  AutoReg: %d\n",
            allocator_name[i],
            (long) allocator[i]->stats.allocations,
            (long) allocator[i]->stats.allocation_bytes,
            (long) allocator[i]->stats.frees,
            (long) allocator[i]->stats.free_bytes,
            (long) allocator[i]->stats.drain_queue,
            (long) allocator[i]->stats.drain_queue_bytes,
            (long) (allocator[i]->stats.allocations - allocator[i]->stats.frees),
            (long) (allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes),
            (double) (((double)(allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes)) / (1024.0*1024.0)),
            (long) ((allocator[i]->stats.allocations - allocator[i]->stats.frees)*allocator[i]->overhead_per_allocation) / (1024.0*1024.0),
            allocator[i]->overhead_per_allocation,
            (__sync_fetch_and_or(&allocator[i]->allocator_flag, 0) & ZPATH_ALLOCATOR_FLAG_DBG_AUTO_REGISTER) ? 1 : 0);

        // sum up all
        allocations += allocator[i]->stats.allocations;
        allocation_bytes += allocator[i]->stats.allocation_bytes;
        frees += allocator[i]->stats.frees;
        free_bytes += allocator[i]->stats.free_bytes;
        drain_queue += allocator[i]->stats.drain_queue;
        drain_queue_bytes += allocator[i]->stats.drain_queue_bytes;
        overhead_bytes += (long) (allocator[i]->stats.allocations - allocator[i]->stats.frees)*allocator[i]->overhead_per_allocation;

        if (show_lines && allocator[i]->overhead_per_allocation == sizeof(struct zpath_allocation_header)) {
#ifdef ZPATH_MALLOC_DEBUG
            int j;
            for (j = 0; j < ZPATH_MALLOC_LINES_DEBUG; j++) {
                if ((allocator[i]->stats.line_stats[j].allocations < min) && (allocator[i]->stats.line_stats[j].frees < min)) continue;
                if (allocator[i]->stats.line_stats[j].line) {
                    ZDP("                      Alloc: %14ld %14ld, Free: %14ld %14ld, Drain: %14ld %14ld, Out: %14ld %14ld %9.3fmb, Line %5ld, File %s\n",
                        (long) allocator[i]->stats.line_stats[j].allocations,
                        (long) allocator[i]->stats.line_stats[j].allocation_bytes,
                        (long) allocator[i]->stats.line_stats[j].frees,
                        (long) allocator[i]->stats.line_stats[j].free_bytes,
                        (long) allocator[i]->stats.line_stats[j].drain_queue,
                        (long) allocator[i]->stats.line_stats[j].drain_queue_bytes,
                        (long) (allocator[i]->stats.line_stats[j].allocations - allocator[i]->stats.line_stats[j].frees),
                        (long) (allocator[i]->stats.line_stats[j].allocation_bytes - allocator[i]->stats.line_stats[j].free_bytes),
                        (double) (((double)(allocator[i]->stats.line_stats[j].allocation_bytes - allocator[i]->stats.line_stats[j].free_bytes)) / (1024.0*1024.0)),
                        (long) allocator[i]->stats.line_stats[j].line,
                        allocator[i]->stats.line_stats[j].file);
                }
            }
#ifndef __FreeBSD__
            int count = 0;
            int64_t sum_alloc_count = 0;
            int64_t sum_alloc_bytes = 0;
            int64_t sum_free_count = 0;
            int64_t sum_free_bytes = 0;
            for (j = 0; j < ZPATH_MALLOC_CALLS_DEBUG; j++) {
                if (allocator[i]->stats.call_stats[j].hash) {
                    int k;
                    count++;
                    if ((allocator[i]->stats.call_stats[j].allocations < min) && (allocator[i]->stats.call_stats[j].frees < min)) continue;
                    char **symbols = backtrace_symbols((void *const *)&(allocator[i]->stats.call_stats[j].stack[0]), allocator[i]->stats.call_stats[j].stack_depth);
                    ZDP("                      Alloc: %14ld %14ld, Free: %14ld %14ld, Drain: %14ld %14ld, Line %5ld, File %s",
                        (long) allocator[i]->stats.call_stats[j].allocations,
                        (long) allocator[i]->stats.call_stats[j].allocation_bytes,
                        (long) allocator[i]->stats.call_stats[j].frees,
                        (long) allocator[i]->stats.call_stats[j].free_bytes,
                        (long) allocator[i]->stats.call_stats[j].drain_queue,
                        (long) allocator[i]->stats.call_stats[j].drain_queue_bytes,
                        (long) allocator[i]->stats.call_stats[j].line,
                        allocator[i]->stats.call_stats[j].file);
                    for (k = 0; k < allocator[i]->stats.call_stats[j].stack_depth; k++) {
                        ZDP("\n                %s", symbols[k]);
                    }
                    ZDP("\n");
                    sum_alloc_count += allocator[i]->stats.call_stats[j].allocations;
                    sum_alloc_bytes += allocator[i]->stats.call_stats[j].allocation_bytes;
                    sum_free_count += allocator[i]->stats.call_stats[j].frees;
                    sum_free_bytes += allocator[i]->stats.call_stats[j].free_bytes;
                }
            }
            ZDP("          Totals      Alloc: %14ld %14ld, Free: %14ld %14ld\n",
                (long)sum_alloc_count,
                (long)sum_alloc_bytes,
                (long)sum_free_count,
                (long)sum_free_bytes);
            ZDP("%d out of %d entries full\n", count, ZPATH_MALLOC_CALLS_DEBUG);
#endif // __FreeBSD__
#endif // ZPATH_MALLOC_DEBUG
        }
    }
    pthread_mutex_unlock(&allocator_lock);

    ZDP("Total:                Alloc: %14ld %14ld, Free: %14ld %14ld, Drain: %14ld %14ld, Out: %14ld %14ld %9.3fmb  Overhead: %9.3fmb\n",
            (long) allocations,
            (long) allocation_bytes,
            (long) frees,
            (long) free_bytes,
            (long) drain_queue,
            (long) drain_queue_bytes,
            (long) (allocations - frees),
            (long) (allocation_bytes - free_bytes),
            (double) (((double)(allocation_bytes - free_bytes)) / (1024.0*1024.0)),
            overhead_bytes / (1024.0*1024.0));

    allocations = 0;
    allocation_bytes = 0;
    frees = 0;
    free_bytes = 0;
    drain_queue = 0;
    drain_queue_bytes = 0;

    if (show_peak) {
        ZDP("Peak for each allocator - delayed by upto a sec\n");
        pthread_mutex_lock(&allocator_lock);
        for (i = 0; i < allocator_count; i++) {
            if (!allocator[i]) {
                continue;
            }
            ZDP("%20s: Alloc: %10ld %12ld, Free: %10ld %12ld, Drain: %10ld %12ld, Out: %10ld %12ld %9.3fmb epoch_s: "
                "%12ld\n",
                allocator_name[i],
                (long) allocator[i]->peak_stats.allocations,
                (long) allocator[i]->peak_stats.allocation_bytes,
                (long) allocator[i]->peak_stats.frees,
                (long) allocator[i]->peak_stats.free_bytes,
                (long) allocator[i]->peak_stats.drain_queue,
                (long) allocator[i]->peak_stats.drain_queue_bytes,
                (long) (allocator[i]->peak_stats.allocations - allocator[i]->peak_stats.frees),
                (long) (allocator[i]->peak_stats.allocation_bytes - allocator[i]->peak_stats.free_bytes),
                (double) (((double)(allocator[i]->peak_stats.allocation_bytes - allocator[i]->peak_stats.free_bytes)) / (1024.0*1024.0)),
                (long) (allocator[i]->peak_stats_hit_epoch_s));

            // sum up all
            allocations += allocator[i]->stats.allocations;
            allocation_bytes += allocator[i]->stats.allocation_bytes;
            frees += allocator[i]->stats.frees;
            free_bytes += allocator[i]->stats.free_bytes;
            drain_queue += allocator[i]->stats.drain_queue;
            drain_queue_bytes += allocator[i]->stats.drain_queue_bytes;
        }
        pthread_mutex_unlock(&allocator_lock);

        ZDP("Total:                Alloc: %14ld %14ld, Free: %14ld %14ld, Drain: %14ld %14ld, Out: %14ld %14ld "
            "%9.3fmb\n",
            (long)allocations,
            (long)allocation_bytes,
            (long)frees,
            (long)free_bytes,
            (long)drain_queue,
            (long)drain_queue_bytes,
            (long)(allocations - frees),
            (long)(allocation_bytes - free_bytes),
            (double)(((double)(allocation_bytes - free_bytes)) / (1024.0 * 1024.0)));
    }

    return ZPATH_RESULT_NO_ERROR;
}

#ifdef ZPATH_MALLOC_DEBUG
static int zpath_allocator_stack_trace(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    int i;

    if (!query_values[0]) {
        ZDP("Require allocator to be specified\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        if (strcmp(query_values[0], allocator_name[i]) == 0) {
            ZDP("%s stack tracing was %d, now %d\n", allocator_name[i], allocator[i]->do_stack_debug, allocator[i]->do_stack_debug ^ 1);
            allocator[i]->do_stack_debug ^= 1;
            pthread_mutex_unlock(&allocator_lock);
            return ZPATH_RESULT_NO_ERROR;
        }
    }
    pthread_mutex_unlock(&allocator_lock);
    ZDP("No allocator %s found\n", query_values[1]);
    return ZPATH_RESULT_NO_ERROR;
}
#endif

int zpath_allocator_peak_status(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    size_t         i;
    int64_t     event_delta_s;
    char        time_str[ARGO_LOG_GEN_TIME_STR_LEN] = {0};
    uint64_t allocations = 0;
    uint64_t allocation_bytes = 0;
    uint64_t frees = 0;
    uint64_t free_bytes = 0;
    uint64_t drain_queue = 0;
    uint64_t drain_queue_bytes = 0;

    if (0 == zpath_allocator_peak_snapshot.peak_time_epoch_s) {
        ZDP("No peak values recorded\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    event_delta_s = epoch_s() - zpath_allocator_peak_snapshot.peak_time_epoch_s;
    argo_log_gen_time(zpath_allocator_peak_snapshot.peak_time_epoch_s, time_str, sizeof(time_str), 0, 1);
    ZDP("At the peak time(%s) which is (%"PRId64 " days, %"PRId64" hours %"PRId64" minutes %"PRId64 " seconds) ago "
        "we had  %"PRId64" bytes outstanding at that time\n",
        time_str, event_delta_s/86400, (event_delta_s % 86400)/3600, ((event_delta_s%86400) %3600)/60,
        ((event_delta_s% 86400) % 3600) % 60, zpath_allocator_peak_snapshot.peak_total_outstanding_bytes);

    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        ZDP("%20s: Alloc: %10ld %12ld, Free: %10ld %12ld, Drain: %10ld %12ld, Out: %10ld %12ld %9.3fmb\n",
            allocator_name[i],
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.allocations,
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.allocation_bytes,
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.frees,
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.free_bytes,
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.drain_queue,
            (long) zpath_allocator_peak_snapshot.allocator[i].stats.drain_queue_bytes,
            (long) (zpath_allocator_peak_snapshot.allocator[i].stats.allocations -
                                                              zpath_allocator_peak_snapshot.allocator[i].stats.frees),
            (long) (zpath_allocator_peak_snapshot.allocator[i].stats.allocation_bytes -
                                                         zpath_allocator_peak_snapshot.allocator[i].stats.free_bytes),
            (double) (((double)(zpath_allocator_peak_snapshot.allocator[i].stats.allocation_bytes -
                                     zpath_allocator_peak_snapshot.allocator[i].stats.free_bytes)) / (1024.0*1024.0)));
        // sum up all
        allocations += allocator[i]->stats.allocations;
        allocation_bytes += allocator[i]->stats.allocation_bytes;
        frees += allocator[i]->stats.frees;
        free_bytes += allocator[i]->stats.free_bytes;
        drain_queue += allocator[i]->stats.drain_queue;
        drain_queue_bytes += allocator[i]->stats.drain_queue_bytes;
    }
    pthread_mutex_unlock(&allocator_lock);
    ZDP("Total:                Alloc: %10ld %12ld, Free: %10ld %12ld, Drain: %10ld %12ld, Out: %10ld %12ld %9.3fmb\n",
            (long)allocations,
            (long)allocation_bytes,
            (long)frees,
            (long)free_bytes,
            (long)drain_queue,
            (long)drain_queue_bytes,
            (long)(allocations - frees),
            (long)(allocation_bytes - free_bytes),
            (double)(((double)(allocation_bytes - free_bytes)) / (1024.0 * 1024.0)));

    ZDP("Peak for each allocator, at the time when the system hit the peak - delayed by upto a sec\n");
    pthread_mutex_lock(&allocator_lock);
    for (i = 0; i < allocator_count; i++) {
        ZDP("%20s: Alloc: %10ld %12ld, Free: %10ld %12ld, Drain: %10ld %12ld, Out: %10ld %12ld %9.3fmb epoch_s: %12ld\n",
            allocator_name[i],
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.allocations,
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.allocation_bytes,
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.frees,
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.free_bytes,
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.drain_queue,
            (long) zpath_allocator_peak_snapshot.allocator[i].peak_stats.drain_queue_bytes,
            (long) (zpath_allocator_peak_snapshot.allocator[i].peak_stats.allocations -
                                                           zpath_allocator_peak_snapshot.allocator[i].peak_stats.frees),
            (long) (zpath_allocator_peak_snapshot.allocator[i].peak_stats.allocation_bytes -
                                                      zpath_allocator_peak_snapshot.allocator[i].peak_stats.free_bytes),
            (double) (((double)(zpath_allocator_peak_snapshot.allocator[i].peak_stats.allocation_bytes -
                                  zpath_allocator_peak_snapshot.allocator[i].peak_stats.free_bytes)) / (1024.0*1024.0)),
            (long) (zpath_allocator_peak_snapshot.allocator[i].allocator_peak_stats_hit_timestamp_s));
    }
    pthread_mutex_unlock(&allocator_lock);
    return ZPATH_RESULT_NO_ERROR;
}

int64_t zpath_allocator_by_name_used_bytes_get(const char *name)
{
    size_t i;

    for (i = 0; i < allocator_count; i++) {

        if (strcmp(name, allocator_name[i])) continue;

        return (allocator[i]->stats.allocation_bytes - allocator[i]->stats.free_bytes);
    }
    return 0;
}

int zpath_argo_status(struct zpath_debug_state *request_state,
                      const char **query_values,
                      int query_value_count,
                      void *cookie)
{
    int count = argo_structures_defined();
    int i;

    for (i = 0; i < count; i++) {
        char *name;
        int64_t allocations, bytes_allocated;
        int64_t frees, bytes_freed;

        argo_structure_info(i,
                            &name,
                            &bytes_allocated,
                            &allocations,
                            &bytes_freed,
                            &frees);

        ZDP("%45s: Alloc: %10ld %12ld, Free: %10ld %12ld, Out: %10ld %12ld %9.3fmb\n",
            name,
            (long) allocations,
            (long) bytes_allocated,
            (long) frees,
            (long) bytes_freed,
            (long) (allocations - frees),
            (long) (bytes_allocated - bytes_freed),
            (double) (((double)(bytes_allocated - bytes_freed)) / (1024.0*1024.0)));
    }

    return ZPATH_RESULT_NO_ERROR;
}

#ifdef __linux__

#if defined(__GLIBC__) && __GLIBC__ == 2 && __GLIBC_MINOR__ <= 32
#define MALLINFO mallinfo
#define MALLINFO_FMT "d"
#else
#define MALLINFO mallinfo2
#define MALLINFO_FMT "zu"
#endif

int zpath_debug_mallinfo(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count,
                         void *cookie)
{
    int64_t start_us = epoch_us();
    struct MALLINFO mi = MALLINFO();
    int64_t end_us = epoch_us();
    if ((end_us - start_us) > (SYSTEM_MEM_API_WARN_TIME_SEC * US_PER_SEC))
      ZPATH_LOG(AL_WARNING, "Mallinfo is running long: %"PRId64" ms", (end_us - start_us)/US_PER_MSEC);

    ZDP("Total non-mmapped bytes (arena):       %" MALLINFO_FMT "\n", mi.arena);
    ZDP("# of free chunks (ordblks):            %" MALLINFO_FMT "\n", mi.ordblks);
    ZDP("# of free fastbin blocks (smblks):     %" MALLINFO_FMT "\n", mi.smblks);
    ZDP("# of mapped regions (hblks):           %" MALLINFO_FMT "\n", mi.hblks);
    ZDP("Bytes in mapped regions (hblkhd):      %" MALLINFO_FMT "\n", mi.hblkhd);
    ZDP("Max. total allocated space (usmblks):  %" MALLINFO_FMT "\n", mi.usmblks);
    ZDP("Free bytes held in fastbins (fsmblks): %" MALLINFO_FMT "\n", mi.fsmblks);
    ZDP("Total allocated space (uordblks):      %" MALLINFO_FMT "\n", mi.uordblks);
    ZDP("Total free space (fordblks):           %" MALLINFO_FMT "\n", mi.fordblks);
    ZDP("Topmost releasable block (keepcost):   %" MALLINFO_FMT "\n", mi.keepcost);

    return ZPATH_RESULT_NO_ERROR;
}

// called outside of debug thread
int zpath_debug_mallinfo_fill(void *cookie, int bad_counter, void *structure_data)
{
    struct zpath_debug_mallinfo*    data;

    (void) cookie;
    data = (struct zpath_debug_mallinfo *)structure_data;

    struct MALLINFO mi = MALLINFO();

    data->arena = mi.arena;
    data->ordblks = mi.ordblks;
    data->smblks = mi.smblks;
    data->hblks = mi.hblks;
    data->hblkhd = mi.hblkhd;
    data->usmblks = mi.usmblks;
    data->fsmblks = mi.fsmblks;
    data->uordblks = mi.uordblks;
    data->fordblks = mi.fordblks;
    data->keepcost = mi.keepcost;
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_malloc_info(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    FILE *stream = NULL;
    char *buffer = NULL;
    size_t len = 0;
    int rval = 0;

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    stream = open_memstream (&buffer, &len);
    if (stream == NULL) {
        ZDP("failed to open memstream: %s\n", strerror(errno));
        return ZPATH_RESULT_NO_ERROR;
    }
    int64_t start_us = epoch_us();
    rval = malloc_info(0, stream);
    int64_t end_us = epoch_us();
    if ((end_us - start_us) > (SYSTEM_MEM_API_WARN_TIME_SEC * US_PER_SEC))
      ZPATH_LOG(AL_WARNING, "Malloc_info is running long: %"PRId64" ms", (end_us - start_us)/US_PER_MSEC);
    if (rval != 0) {
        ZDP("failed to read malloc info: %s\n", strerror(errno));
        fclose(stream);
        free(buffer);
        return ZPATH_RESULT_NO_ERROR;
    }

    fflush(stream);
    fclose(stream);

    ZDP("%s", buffer);
    free(buffer);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_malloc_trim(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int i;
    if (!query_values[0]) {
        ZDP("Missing argument: pad\n");
    } else {
        i = strtol(query_values[0], NULL, 0);
        int64_t start_us = epoch_us();
        int rc = malloc_trim(i);
        int64_t end_us = epoch_us();
        if ((end_us - start_us) > (SYSTEM_MEM_API_WARN_TIME_SEC * US_PER_SEC))
          ZPATH_LOG(AL_WARNING, "Malloc_trim is running long: %"PRId64" ms", (end_us - start_us)/US_PER_MSEC);
        if (!rc) {
            ZDP("Release Failed\n");
        } else {
            ZDP("Release successful\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

#endif

static int zpath_debug_zhash_table_size_log2(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int i;
    ZDP("Current zhash_max_table_size_log2_default: %d\n", zhash_max_table_size_log2_default);

    if (query_values[0]) {
        i = strtol(query_values[0], NULL, 0);
        zhash_max_table_size_log2_default = i;
        ZDP("New zhash_max_table_size_log2_default: %d\n", zhash_max_table_size_log2_default);
    }
    return ZPATH_RESULT_NO_ERROR;
}


void dump_help(struct zpath_debug_state *request_state,
               const char *path)
{
    struct debug_command *cmd = NULL;
    struct debug_command_set *cmdset = NULL;
    void *cookie;
    int res;

    res = fohh_http_server_is_registered(zpath_debug_http_server, "*", path, &cookie, NULL);
    if (res == FOHH_RESULT_NO_ERROR) {
        int i, j;
        if (path[0] == 0 || strcmp(path, "*") == 0) {
            cmdset = cookie;
            ZDP("Dump all the commands:\n\n");
            for (i = 0; i < cmdset->all_commands_count; i++) {
                cmd = cmdset->all_commands[i];
                ZDP("%s: %s\n", cmd->path, cmd->description);
                if (cmd->query_count) {
                    ZDP("  Supported query arguments:\n");
                    for (j = 0; j < cmd->query_count; j++) {
                        ZDP("  %15s - %s\n", cmd->query_strings[j], cmd->query_descriptions[j]);
                    }
                }
            }
        } else {
            cmd = cookie;
            ZDP("%s: %s\n", path, cmd->description);
            if (cmd->query_count) {
                ZDP("Supported query arguments:\n");
                for (i = 0; i < cmd->query_count; i++) {
                    ZDP("%15s - %s\n", cmd->query_strings[i], cmd->query_descriptions[i]);
                }
            }
        }
    } else {
        ZDP("Could not find help for %s. A request with no path will show all valid paths.\n", path);
    }
}

int zpath_debug_help(struct zpath_debug_state *request_state,
                     const char **query_values,
                     int query_value_count,
                     void *cookie)
{
    if (query_values[0] == NULL) {
        dump_help(request_state, "/help");
    } else {
        dump_help(request_state, query_values[0]);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_fohh_worker_status(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    char buf[10000];

    fohh_worker_pool_dump(buf, sizeof(buf));

    ZDP("Getting worker_status...\n%s\nDone!\n", buf);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_zudp_conn_table_print_stats(struct zpath_debug_state *request_state,
                                            const char **query_values __attribute__((unused)),
                                            int query_value_count __attribute__((unused)),
                                            void *cookie __attribute__((unused)))
{
    char buf[1024];

    zudp_conn_table_print_stats(buf, sizeof(buf));
    zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_zudp_conn_table_print_conn(struct zpath_debug_state *request_state,
                                           const char **query_values __attribute__((unused)),
                                           int query_value_count __attribute__((unused)),
                                           void *cookie __attribute__((unused)))
{
    char buf[16384];

    while(!zudp_conn_table_print_conns(buf, sizeof(buf))) {
        zpath_debug_cb_printf_response(request_state, "%s", buf);
    }
    zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_zudp_conn_change_stale_timeout(struct zpath_debug_state *request_state,
                                           const char **query_values __attribute__((unused)),
                                           int query_value_count __attribute__((unused)),
                                           void *cookie __attribute__((unused)))
{
    if(!query_values[0]) {
        ZDP("Please specify timeout value");
        return ZPATH_RESULT_ERR;
    }
    int timeout = atoi(query_values[0]);
    if (timeout < 0 || timeout > 50) {
        ZDP("Please specify a valid timeout value, setting it to default value of 15\n");
        timeout = ZUDP_CONN_STALE_TIMEOUT_S;
        zudp_conn_set_stale_timer(timeout);
        return ZPATH_RESULT_NO_ERROR;
    }
    zudp_conn_set_stale_timer(timeout);
    ZDP("The timer is set to timeout %d\n", timeout);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_fohh_error_debug_stats(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    fohh_error_debug_stats(request_state);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_buffer_pool_stats_get(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    const int out_buf_len = 2000;
    char *out_buf = ZLIB_MALLOC(out_buf_len);
    zpath_buffer_pool_stats_dump(out_buf, out_buf_len);
    zpath_debug_cb_printf_response(request_state, "%s", out_buf);
    ZLIB_FREE(out_buf);
    return ZPATH_RESULT_NO_ERROR;
}

#define FOHH_TRACKER_DEBUG_BUF_SIZE (8096)
static int debug_print_fohh_tracker_stats(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie)
{
    char buf[FOHH_TRACKER_DEBUG_BUF_SIZE];
    buf[0] = '\0';
    fohh_tracker_print_stats_all(buf, FOHH_TRACKER_DEBUG_BUF_SIZE);
    ZDP("%s", buf);
    return ZPATH_RESULT_NO_ERROR;
}
static int debug_print_fohh_tracker_counters(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie)
{
    char buf[FOHH_TRACKER_DEBUG_BUF_SIZE];
    buf[0] = '\0';
    fohh_tracker_print_counters(buf, FOHH_TRACKER_DEBUG_BUF_SIZE);
    ZDP("%s", buf);
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_fohh_tracker_reset(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie)
{
    fohh_tracker_reset();
    ZDP("OK!\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_fohh_tracker_log(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie)
{
    if (query_values[0]) {
        g_fohh_tracker_log_start_end = 1;
        ZDP("Logging is on.\n");
    } else if (query_values[1]) {
        g_fohh_tracker_log_start_end = 0;
        ZDP("Logging is off.\n");
    } else {
        ZDP("Logging is currently %s\n", g_fohh_tracker_log_start_end ? "on" : "off");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int argo_load_stats_cb(void *cookie, int counter, void *structure_data)
{
    struct argo_allocator_mem_stats *data = (struct argo_allocator_mem_stats *)structure_data;
    int idx = *(int *)cookie;
    char *log_name = (char *)argo_allocator_type_names[idx];

    int count = argo_structures_defined();
    for(int i = 0; i < count; i++) {
        char *name;
        int64_t allocations, bytes_allocated;
        int64_t frees, bytes_freed;

        argo_structure_info(i,
                            &name,
                            &bytes_allocated,
                            &allocations,
                            &bytes_freed,
                            &frees);

        /* fetch the results only for the desired allocator */
        if(strcmp(log_name, name) == 0) {
            data->allocations = allocations;
            data->allocation_bytes = bytes_allocated;
            data->frees = frees;
            data->free_bytes = bytes_freed;

            break;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Initialize the zpath debugging system. Tells this system what
 * event/stats logs to use, starts up the debug thread, things like
 * that.
 */
int zpath_debug_init(const char *event_log_name,
                     const char *stats_log_name,
                     int32_t debug_port_he)
{
    static int initialized = 0;
    pthread_t thread;
    int res;

    static int init_complete = ZPATH_RESULT_WOULD_BLOCK;

    zpath_debug_event = argo_log_get(event_log_name);
    //zpath_debug_stats = argo_log_get(stats_log_name);

    if (initialized) return ZPATH_RESULT_NO_ERROR;

    if (!(zpath_debug_memory_allocator_stats_description = argo_register_global_structure(ZPATH_DEBUG_MEMORY_ALLOCATOR_STATS_HELPER))){
        return ZPATH_RESULT_ERR;
    }

#ifdef __linux__
    if (!(zpath_debug_mallinfo_description = argo_register_global_structure(ZPATH_DEBUG_MALLINFO_HELPER))){
        return ZPATH_RESULT_ERR;
    }
#endif

    res = zpath_debug_add_flag(&zpath_debug,
                               zpath_debug_catch_defaults,
                               "zpath_lib",
                               zpath_debug_names);
    if (res) {
        return res;
    }

    if (debug_port_he > 0) {
        res = fohh_http_server_register(zpath_debug_http_server, "*", "*", zpath_debug_http_default_cb, &all_commands, 0);
        if (res) {
            return res;
        }
        /* register default callback for command invocation via domain socket */
        res = fohh_http_server_update_path_command_req_callback(zpath_debug_http_server, "*", "*", zpath_debug_command_listener_default_cb);
        if (res) {
            return res;
        }
    }

    res = zpath_debug_add_read_command("Run diagnostics",
                                  "/diagnostics",
                                  zpath_debug_run_diagnostics,
                                  NULL,
                                  "verbose", "when present, callbacks print more details (if implemented that way)",
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Get fohh worker pool status",
                                  "/fohh/worker_pool",
                                  zpath_fohh_worker_status,
                                  NULL,
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Check all threads' status.",
                                  "/thread/status",
                                  zpath_thread_status,
                                  NULL,
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_admin_command("Check all threads' status.",
                                  "/thread/heartbeat/override",
                                  zpath_zthread_set_heartbeat_override,
                                  NULL,
                                  "timeout",  "Heartbeat Timeout override value in seconds (mandatory, min: 20s, max: 3600s)",
                                  "duration", "Heartbeat Timeout override validity duration (default: 2*timeout, max: 10800s)",
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Check all threads' status.",
                                  "/thread/usage",
                                  zpath_thread_usage,
                                  NULL,
                                  "history", "Show rusage history",
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Check all threads' status.",
                                  "/thread/group/usage",
                                  zpath_thread_group_usage,
                                  NULL,
                                  NULL);
    if (res) return res;

    /* run a set of threads (3) within a thread group
     * - for testing only.
     * - to calibrate our thread group rusage
     */
    res = zpath_debug_add_write_command("Run reference thread group (testing only).",
                                  "/thread/run_ref_grp",
                                  zpath_debug_run_ref_thread_group,
                                  NULL,
                                  "sec", "required, how many seconds to run",
                                  "pct", "required, target cpu usage percentage",
                                  NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Check all memory allocators' status.",
                                  "/memory/status",
                                  zpath_allocator_status,
                                  NULL,
                                  "peak", "Show peak memory utilization as well",
                                  "lines", "Show memory utilization per allocation point- only available with debugging",
                                  "min", "Show only those memory allocations/frees exceeding min in count",
                                  "allocator", "Show the specified allocator stats only ",
                                  NULL);
    if (res) return res;

#ifdef ZPATH_MALLOC_DEBUG
    res = zpath_debug_add_read_command("Check all memory allocators' status.",
                                  "/memory/stack_trace",
                                  zpath_allocator_stack_trace,
                                  NULL,
                                  "allocator", "Show the specified allocator stats only ",
                                  NULL);
    if  (res) return res;
#endif // ZPATH_MALLOC_DEBUG

    res = zpath_debug_add_safe_read_command("Check all memory allocators' status.",
                                  "/memory/peak_status",
                                  zpath_allocator_peak_status,
                                  NULL,
                                  NULL);
    if  (res) return res;

    res = zpath_debug_add_safe_read_command("Check all memory allocators' status.",
                                  "/memory/argo",
                                  zpath_argo_status,
                                  NULL,
                                  NULL);
    if  (res) return res;

#ifdef __linux__
    res = zpath_debug_add_write_command("Dump glibc's memory allocator status.",
                                  "/memory/mallinfo",
                                  zpath_debug_mallinfo,
                                  NULL,
                                  NULL);
    if  (res) return res;

    res = zpath_debug_add_write_command("Dump glibc malloc memory state.",
                                  "/memory/malloc_info",
                                  zpath_debug_malloc_info,
                                  NULL,
                                  NULL);
    if  (res) return res;

    res = zpath_debug_add_write_command("Trim glibc memory allocation.",
                                  "/memory/malloc_trim",
                                  zpath_debug_malloc_trim,
                                  NULL,
                                  "pad", "Amount of free space to leave untrimmed at top of heap",
                                  NULL);
    if  (res) return res;
#endif

    res = zpath_debug_add_write_command("Set zhash_table max bucket size (2^value).",
                                  "/memory/zhash_table_size",
                                  zpath_debug_zhash_table_size_log2,
                                  NULL,
                                  "value", "zhash_table max table size will not exceed 2^value",
                                  NULL);
    if  (res) return res;

    res = zpath_debug_add_write_command("Set max event messages per line per second.",
                                  "/app/max_events",
                                  zpath_set_max_debug_messages,
                                  NULL,
                                  "count", "The maximum number of event messages per line per second",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Get all client FOHH connections status.",
                                  "/fohh/clients",
                                  zpath_debug_fohh_client_connections,
                                  NULL,
                                  "filter", "filter by description substring, case insensitive",
                                  "small", "show small (terse) output, single line per connection, use option '?small=debug' for debug string",
                                  "fohh_thread", "choose the FOHH thread for which to dump connections",
                                  "stats", "show stats for each connection only, in terse form",
                                  "limit", "stats mode only- show only those connections with at least <limit> bytes buffered",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_diagnostic_cb("Client FOHH connections",
                                  "/fohh/clients",
                                  zpath_debug_fohh_client_diagnostic_cb,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_diagnostic_cb("Server FOHH connections",
                                  "/fohh/servers",
                                  zpath_debug_fohh_server_diagnostic_cb,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Get all server FOHH connections status.",
                                  "/fohh/servers",
                                  zpath_debug_fohh_server_connections,
                                  NULL,
                                  "filter", "filter by description substring, case insensitive",
                                  "small", "show small (terse) output, single line per connection, use option '?small=debug' for debug string",
                                  "fohh_thread", "choose the FOHH thread for which to dump connections",
                                  "stats", "show stats for each connection only, in terse form",
                                  "limit", "stats mode only- show only those connections with at least <limit> bytes buffered",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Reset the specified client connection.",
                                  "/fohh/client/reset",
                                  zpath_debug_fohh_client_reset,
                                  NULL,
                                  "domain", "The target domain of the client connection to reset",
                                  "port", "The target port of the server connection to reset",
                                  "limit", "Set the limit for how many connections to reset. Defaults 1. 0 means all.",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Reset the specified server connection.",
                                  "/fohh/server/reset",
                                  zpath_debug_fohh_server_reset,
                                  NULL,
                                  "domain", "The target domain of the server connection to reset",
                                  "port", "The target port of the server connection to reset",
                                  "limit", "Set the limit for how many connections to reset. Defaults 1. 0 means all.",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump fohh connection aggregated stats.",
                                       "/fohh/client/aggregated_stats",
                                       zpath_debug_fohh_connection_aggregated_stats,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump fohh_connection_state_stats per FOHH thread.",
                                       "/fohh/thread/stats_by_state",
                                       zpath_debug_fohh_thread_state_stats,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump consolidated fohh_connection_state_stats.",
                                       "/fohh/thread/stats_by_state_consolidated",
                                       zpath_debug_fohh_thread_state_stats_consolidated,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump fohh_connection_error_stats per FOHH thread.",
                                       "/fohh/thread/error_stats",
                                       zpath_debug_fohh_thread_error_stats,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Dump freeq stats per FOHH thread.",
                                       "/fohh/thread/alloc_stats",
                                       zpath_debug_fohh_thread_alloc_stats,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Dump fohh_connection_state_stats per FOHH thread.",
                                       "/fohh/thread/stats_by_state/toggle",
                                       zpath_debug_fohh_thread_state_stats_toggle,
                                       NULL,
                                       NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Reset the specified server connection.",
                                  "/fohh/autotune/params",
                                  zpath_debug_fohh_autotune_params,
                                  NULL,
                                  /* 0 */"domain", "Domain of the connection",
                                  /* 1 */"port", "Port of the connection. Optional",
                                  /* 2 */"enabled", "Whether to enable or disable",
                                  /* 3 */"factor", "The tuning factor to user. Floating point",
                                  /* 4 */"reduction", "The factor by which buffer size will decrease",
                                  /* 5 */"samples", "The number of samples over which to average",
                                  /* 6 */"minbuf", "The minimum size of socket buffer allowed",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Reset the specified server connection.",
                                  "/fohh/server/autotune",
                                  zpath_debug_fohh_autotune,
                                  NULL,
                                  "domain", "The target domain of the server connection to reset",
                                  "port", "The target port of the server connection to reset",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Turn on/off fohh debug for connections belonging to certain common name.",
                                  "/fohh/debug",
                                  zpath_debug_fohh_debug_toggle,
                                  NULL,
                                  "domain", "The domain of the fohh connection to debug",
                                  "port", "The port of the fohh connection to debug",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump object history for the first found connections.",
                                  "/fohh/history/dump",
                                  zpath_debug_fohh_dump_history,
                                  NULL,
                                  "filter", "Apply to fohh connections matching the string",
                                  "filter2", "If set, must also match this string",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Dump object history for the first found connections.",
                                  "/fohh/history/filter/add",
                                  zpath_debug_fohh_add_filter_history,
                                  NULL,
                                  "rpc", "Add specified rpc to the filter",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Dump object history for the first found connections.",
                                  "/fohh/history/filter/remove",
                                  zpath_debug_fohh_delete_filter_history,
                                  NULL,
                                  "rpc", "Add specified rpc to the filter",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump filters for history capture. Only objects matching filter are captured.",
                                  "/fohh/history/filter/dump",
                                  zpath_debug_fohh_show_filter_history,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Get help for a command.",
                                  "/help",
                                  zpath_debug_help,
                                  NULL,
                                  "path", "The path for which to get help.",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Set/View catch IP",
                                  "/catchip",
                                  zpath_debug_catch_ip_ui,
                                  NULL,
                                  "ip", "The IP address to catch",
                                  "max", "The maximum times to trigger",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("View all debug flags",
                                  "/debug",
                                  zpath_debug_dump_debug_all,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("View all catch flags",
                                  "/catch",
                                  zpath_debug_dump_catch_all,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Show current tune configuration.",
                                  "/tune/show",
                                  zpath_debug_tune_show_config,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("Set socket sndbuf of specified connection with current value.",
                                  "/tune/fohh/setsndbuf",
                                  zpath_debug_set_fohh_snd_buf,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  "value", "The value of socket sndbuf",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Toggle delay optimized or throughput optimized.",
                                  "/tune/fohh/delay_optimized",
                                  zpath_debug_tune_delay_optimized,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Toggle auto tune disable for fohh.",
                                  "/tune/fohh/autotune_disable",
                                  zpath_debug_auto_tune_disable,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Tune chunk size for mtunnel.",
                                  "/tune/chunksize",
                                  zpath_debug_tune_chunk_size,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  "value", "The target chunk size for mtunnel",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("Tune allowed chunk for mtunnel.",
                                  "/tune/allowedchunk",
                                  zpath_debug_tune_allowed_chunk,
                                  NULL,
                                  "domain", "The target domain of the server connection to set",
                                  "port", "The target port of the server connection to set",
                                  "value", "The target allowed chunks",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Show all safe commands.",
                                  "/safe_commands",
                                  zpath_debug_show_safe_commands,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Show all safe commands.",
                                  "/all_commands",
                                  zpath_debug_show_all_commands,
                                  NULL,
                                  "type", "Optional. Allowed values: read, write, admin",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_safe_read_command("Show all safe commands.",
                                  "/role_based_access_control_status",
                                  zpath_debug_show_rbac_status,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("udp conn table print connections",
                                  "/zcdns/data/udp_conn_table/print_conn",
                                  zpath_debug_zudp_conn_table_print_conn,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("udp conn set stale connection timeout",
                                  "/zcdns/data/udp_conn_table/stale_timeout",
                                  zpath_debug_zudp_conn_change_stale_timeout,
                                  NULL,
                                  "value", "Stale timeout value",
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("udp conn table print stats",
                                  "/zcdns/data/udp_conn_table/print_stats",
                                  zpath_debug_zudp_conn_table_print_stats,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("print fohh error stats",
                                  "/fohh/error/debug_stats",
                                  zpath_fohh_error_debug_stats,
                                  NULL,
                                  NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_read_command("Dump zpath buffer pool memory stats",
                                  "/memory/packet_buffer_pools",
                                  zpath_buffer_pool_stats_get,
                                  NULL,
                                  NULL);
    if (res) {
      return res;
    }

    zpath_debug_wally_init();
    zpath_system_debug_init();

    initialized = 1;

    if (zthread_create(&thread,
                       zpath_debug_thread,
                       &init_complete,
                       "debug",
                       120,          /* 120s watchdog timeout for debug thread */
                       16*1024*1024, /* 16 MB stack */
                       60*1000*1000, /* 60s stats interval */
                       NULL)) {
        return ZPATH_RESULT_ERR;
    }

    /* Only start debugging thread if our configured port is > 0 */
    if (debug_port_he > 0) {
        struct argo_inet inet;
        argo_string_to_inet(debug_port_bind_ip, &inet);
        res = fohh_http_server_listen(zpath_debug_http_server,
                                      &inet,
                                      debug_port_he,
                                      NULL,
                                      NULL,
                                      0,
                                      1);
        if (res) {
            return res;
        }

    }

    while (init_complete == ZPATH_RESULT_WOULD_BLOCK) sched_yield();
    if (init_complete) {
        return init_complete;
    }

    zpath_debug_add_allocator(&libevent_allocator, "libevent");
    zpath_debug_add_allocator(&openssl_allocator, "openssl");
    zpath_debug_add_allocator(&argo_allocator, "argo");
    zpath_debug_add_allocator(&avl_allocator, "avl");
    zpath_debug_add_allocator(&diamond_allocator, "diamond");
    zpath_debug_add_allocator(&pattern_match_allocator, "pattern_match");
    zpath_debug_add_allocator(&fohh_allocator, "fohh");
    zpath_debug_add_allocator(&phash_allocator, "phash");
    zpath_debug_add_allocator(&wally_allocator, "wally");
    zpath_debug_add_allocator(&zpath_lib_allocator, "zpath_lib");
    zpath_debug_add_allocator(&zthread_allocator, "zthread");
    zpath_debug_add_allocator(&zevent_allocator, "zevent");
    zpath_debug_add_allocator(&zhash_table_allocator, "zhash_table");
    zpath_debug_add_allocator(&zhash_table_locked_allocator, "zhash_table_locked");
    zpath_debug_add_allocator(&cshash_allocator, "cshash");
    zpath_debug_add_allocator(&zudp_allocator, "zudp");

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_flag_put_normal(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct debug_flag_bit_desc *bit_desc = cookie;
    int64_t bit_val;

    /* Get original value. */
    bit_val = (bit_desc->flag->flag_normal >> bit_desc->bit_position) & 1;

    /* Toggle bit */
    bit_desc->flag->flag_normal ^= (1ll << bit_desc->bit_position);

    *(bit_desc->flag->flag) = bit_desc->flag->flag_normal;

    zpath_debug_cb_printf_response(request_state,
                                   "Update Debug: %s/%s, was %"PRIx64", now %"PRIx64"\n",
                                   bit_desc->flag->flag_name,
                                   bit_desc->bit_name,
                                   bit_val,
                                   bit_val ^ 1);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_flag_put_normal_ext(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct debug_flag_bit_desc *bit_desc = cookie;
    int64_t bit_val;

    /* Get original value. */
    bit_val = bit_desc->flag->flag_normal_2[bit_desc->bit_position];

    /* Toggle bit */
    bit_desc->flag->flag_normal_2[bit_desc->bit_position] ^= 1;
    if (bit_desc->flag->flag_normal_2[bit_desc->bit_position])
      zpn_debug_set_with_ctx(bit_desc->flag->flag_2, bit_desc->bit_position);
    else
      zpn_debug_reset_with_ctx(bit_desc->flag->flag_2, bit_desc->bit_position);
    zpath_debug_cb_printf_response(request_state,
                                   "Update Debug: %s/%s, was %"PRIx64", now %"PRIx64"\n",
                                   bit_desc->flag->flag_name,
                                   bit_desc->bit_name,
                                   bit_val,
                                   bit_val ^ 1);

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_flag_put_catch(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct debug_flag_bit_desc *bit_desc = cookie;
    int64_t bit_val;

    /* Get original value. */
    bit_val = (bit_desc->flag->flag_catch >> bit_desc->bit_position) & 1;

    /* Toggle bit */
    bit_desc->flag->flag_catch ^= (1ll << bit_desc->bit_position);

    zpath_debug_cb_printf_response(request_state,
                                   "Update Catch: %s/%s, was %"PRIx64", now %"PRIx64"\n",
                                   bit_desc->flag->flag_name,
                                   bit_desc->bit_name,
                                   bit_val,
                                   bit_val ^ 1);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_flag_put_catch_ext(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie)
{
    struct debug_flag_bit_desc *bit_desc = cookie;
    int64_t bit_val;

    /* Get original value. */
    bit_val = bit_desc->flag->flag_catch_2[bit_desc->bit_position];

    /* Toggle bit */
    bit_desc->flag->flag_catch_2[bit_desc->bit_position] ^= 1;

    zpath_debug_cb_printf_response(request_state,
                                   "Update Catch: %s/%s, was %"PRIx64", now %"PRIx64"\n",
                                   bit_desc->flag->flag_name,
                                   bit_desc->bit_name,
                                   bit_val,
                                   bit_val ^ 1);

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_flag_dump_normal(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    struct debug_flag *flag = cookie;
    int i;

    zpath_debug_cb_printf_response(request_state,
                                   "DEBUG %s = %"PRIx64"\n",
                                   flag->flag_name,
                                   *flag->flag);

    if (query_values[0]) {
        flag->flag_normal = strtoull(query_values[0], NULL, 0);
        (*((uint64_t *)flag->flag)) = strtoull(query_values[0], NULL, 0);
        zpath_debug_cb_printf_response(request_state,
                                       "DEBUG %s = %"PRIx64" NOW\n",
                                       flag->flag_name,
                                       *flag->flag);

    }
    for (i = 0; i < flag->bit_count; i++) {
        if (flag->flag_normal & (1ll << flag->bits[i].bit_position)) {
            zpath_debug_cb_printf_response(request_state,
                                           "ON  : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        } else {
            zpath_debug_cb_printf_response(request_state,
                                           "OFF : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* The API gets the bits which are set and reset to enable/disable module logs.
 * query_values input string from the user through cli
 * high bits values for higher bits from 64 to 128 bits
 * low bits values for lower bits from 0 to 63 bits
 * returns void
 */
static void zpath_debug_get_flag_bits_ext(const char *query_values, uint64_t *high, uint64_t *low)
{
#define ZPATH_DEBUG_FLAG_BIT_MASK_LEN 16 /* Length of hex string which can hold 64 bits */
    /* Buffer to hold the high and low parts as strings */
    char high_str[ZPATH_DEBUG_FLAG_BIT_MASK_LEN + 3] = {0}; /* 16 chars + "0x" prefix + null terminator */
    char low_str[ZPATH_DEBUG_FLAG_BIT_MASK_LEN + 3] = {0};  /* 16 chars + "0x" prefix + null terminator */
    size_t len = (query_values != NULL) ? strnlen(query_values, (ZPATH_DEBUG_FLAG_BIT_MASK_LEN << 1) + 2) : 0;

    if (len == 0)
        return;

    if (len >= 2 && (memcmp(query_values, "0x", 2) == 0 || memcmp(query_values, "0X", 2) == 0)) {
        // Handle hexadecimal input
        if (len > ZPATH_DEBUG_FLAG_BIT_MASK_LEN + 2 && len <= (ZPATH_DEBUG_FLAG_BIT_MASK_LEN << 1) + 2) {
            /* Split the string into high and low parts if string is more than 16 characters */
            size_t high_len = len - ZPATH_DEBUG_FLAG_BIT_MASK_LEN;

            // Copy the high part including "0x" prefix
            memcpy(high_str, query_values, high_len);
            high_str[high_len] = '\0';

            // Copy the low part including "0x" prefix
            memcpy(low_str, "0x", 2); // Add "0x" prefix
            memcpy(low_str + 2, query_values + high_len, ZPATH_DEBUG_FLAG_BIT_MASK_LEN);
            low_str[ZPATH_DEBUG_FLAG_BIT_MASK_LEN + 2] = '\0';

            *high = strtoull(high_str, NULL, 0);
            *low = strtoull(low_str, NULL, 0);
        } else if (len <= ZPATH_DEBUG_FLAG_BIT_MASK_LEN + 2) {
            /* Only low part if the string is 16 characters or less */
            memcpy(low_str, query_values, len);
            low_str[len] = '\0';
            *high = 0;
            *low = strtoull(low_str, NULL, 0);
        } else {
            /* invalid string length */
            *high = 0;
            *low = 0;
        }
    } else {
        // Handle decimal & octal input
        if (len > ZPATH_DEBUG_FLAG_BIT_MASK_LEN && len <= (ZPATH_DEBUG_FLAG_BIT_MASK_LEN << 1)) {
            /* Split the string into high and low parts if string is more than 16 characters */
            size_t high_len = len - ZPATH_DEBUG_FLAG_BIT_MASK_LEN;
            memcpy(high_str, query_values, high_len);
            high_str[high_len] = '\0';
            memcpy(low_str, query_values + high_len, ZPATH_DEBUG_FLAG_BIT_MASK_LEN);
            low_str[ZPATH_DEBUG_FLAG_BIT_MASK_LEN] = '\0';

            *high = strtoull(high_str, NULL, 0);
            *low = strtoull(low_str, NULL, 0);
        } else if (len <= ZPATH_DEBUG_FLAG_BIT_MASK_LEN) {
            /* Only low part if the string is 16 characters or less */
            memcpy(low_str, query_values, len);
            low_str[len] = '\0';
            *high = 0;
            *low = strtoull(low_str, NULL, 0);
        } else {
            /* invalid string length */
            *high = 0;
            *low = 0;
        }
    }
}

int zpath_debug_flag_dump_normal_ext(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
#define ZPATH_DEBUG_MAX_BITS_IN_BYTES 64
    struct debug_flag *flag = cookie;
    uint64_t high_bits = 0;
    uint64_t low_bits = 0;
    int i;

    zpath_debug_cb_printf_response(request_state,
                                   "DEBUG %s:\n",
                                   flag->flag_name);

    if (query_values && query_values[0]) {
        zpath_debug_get_flag_bits_ext(query_values[0], &high_bits, &low_bits);
        for (i = 0; i < ZPATH_DEBUG_MAX_BITS_IN_BYTES; i++) {
            if (low_bits & ((uint64_t)1 << i)) {
                flag->flag_normal_2[flag->bits[i].bit_position] = 1;
            } else {
                flag->flag_normal_2[flag->bits[i].bit_position] = 0;
            }
            if (high_bits & ((uint64_t)1 << i)) {
                flag->flag_normal_2[ZPATH_DEBUG_MAX_BITS_IN_BYTES + flag->bits[i].bit_position] = 1;
            } else {
                flag->flag_normal_2[ZPATH_DEBUG_MAX_BITS_IN_BYTES + flag->bits[i].bit_position] = 0;
            }
        }
    }

    for (i = 0; i < flag->bit_count; i++) {
        if (flag->flag_normal_2[flag->bits[i].bit_position]) {
            zpath_debug_cb_printf_response(request_state,
                                           "ON  : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        } else {
            zpath_debug_cb_printf_response(request_state,
                                           "OFF : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_flag_dump_catch(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct debug_flag *flag = cookie;
    int i;

    zpath_debug_cb_printf_response(request_state,
                                   "CATCH %s = %08x\n",
                                   flag->flag_name,
                                   *((uint32_t *)flag->flag));
    for (i = 0; i < flag->bit_count; i++) {
        if (flag->flag_catch & (1ll << flag->bits[i].bit_position)) {
            zpath_debug_cb_printf_response(request_state,
                                           "ON  : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        } else {
            zpath_debug_cb_printf_response(request_state,
                                           "OFF : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_flag_dump_catch_ext(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct debug_flag *flag = cookie;
    int i;

    zpath_debug_cb_printf_response(request_state,
                                   "CATCH %s:\n",
                                   flag->flag_name);

    for (i = 0; i < flag->bit_count; i++) {
        if (flag->flag_catch_2[flag->bits[i].bit_position]) {
            zpath_debug_cb_printf_response(request_state,
                                           "ON  : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        } else {
            zpath_debug_cb_printf_response(request_state,
                                           "OFF : %s : %s\n",
                                           flag->flag_name,
                                           flag->bits[i].bit_name);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_dump_catch_all(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    struct debug_flag *flag = cookie;
    int i;
    int fid;
    int any = 0;

    for (fid = 0; fid < debug_flag_count; fid++) {
        flag = &(debug_flags[fid]);
        zpath_debug_cb_printf_response(request_state,
                                       "CATCH %s:\n",
                                       flag->flag_name);
        for (i = 0; i < flag->bit_count; i++) {
            if (flag->flag_catch & (1ll << flag->bits[i].bit_position)) {
                zpath_debug_cb_printf_response(request_state,
                                               "ON  : %s : %s\n",
                                               flag->flag_name,
                                               flag->bits[i].bit_name);
                any = 1;
            } else {
                /* Only show debugging/etc that is on. */
                zpath_debug_cb_printf_response(request_state,
                                               "OFF : %s : %s\n",
                                               flag->flag_name,
                                               flag->bits[i].bit_name);
            }
        }
    }
    if (!any) {
        zpath_debug_cb_printf_response(request_state, "CATCH: None configured\n");
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_dump_debug_all(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    struct debug_flag *flag = cookie;
    int i;
    int fid;
    int any = 0;

    for (fid = 0; fid < debug_flag_count; fid++) {
        flag = &(debug_flags[fid]);
        zpath_debug_cb_printf_response(request_state,
                                       "DEBUG %s:\n",
                                       flag->flag_name);
        for (i = 0; i < flag->bit_count; i++) {
            if (flag->flag_normal & (1ll << flag->bits[i].bit_position)) {
                zpath_debug_cb_printf_response(request_state,
                                               "ON  : %s : %s\n",
                                               flag->flag_name,
                                               flag->bits[i].bit_name);
                any = 1;
            }
        }
    }
    if (!any) {
        zpath_debug_cb_printf_response(request_state, "DEBUG: None configured\n");
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_add_flag(uint64_t *flag_address,
                         uint64_t catch_default,
                         const char *flag_name,
                         const char **bit_names)
{
    int count;
    char str[100];
    char str2[100];

    struct debug_flag *flag;

    flag = &(debug_flags[debug_flag_count]);
    debug_flag_count++;

    for (count = 0; bit_names[count]; count++) {
        flag->bits[count].flag = flag;
        flag->bits[count].bit_position = count;
        flag->bits[count].bit_name = bit_names[count];
        snprintf(str, sizeof(str), "/debug/%s/%s", flag_name, bit_names[count]);
        zpath_debug_add_write_command("Toggle debug bit configuration",
                                str,
                                zpath_debug_flag_put_normal,
                                &(flag->bits[count]),
                                NULL);
        snprintf(str, sizeof(str), "/catch/%s/%s", flag_name, bit_names[count]);
        zpath_debug_add_write_command("Toggle catch bit configuration",
                                str,
                                zpath_debug_flag_put_catch,
                                &(flag->bits[count]),
                                NULL);
    }

    flag->bit_count = count;
    flag->flag_name = flag_name;
    flag->flag = flag_address;
    flag->flag_normal = *flag_address;
    flag->flag_catch = catch_default;

    snprintf(str, sizeof(str), "/debug/%s", flag_name);
    snprintf(str2, sizeof(str2), "Dump bit configuration for %s", flag_name);
    zpath_debug_add_write_command(str2,
                            str,
                            zpath_debug_flag_dump_normal,
                            flag,
                            "value", "Set debug flag to value",
                            NULL);


    snprintf(str, sizeof(str), "/catch/%s", flag_name);
    snprintf(str2, sizeof(str2), "Dump bit configuration for %s", flag_name);
    zpath_debug_add_write_command(str2,
                            str,
                            zpath_debug_flag_dump_catch,
                            flag,
                            NULL);



    return ZPATH_RESULT_NO_ERROR;
}

// used by zpn debug flags
int zpath_debug_add_flag_ext(void *flag_address,
                             const void *catch_default,
                             uint16_t size,
                             const char *flag_name,
                             const char **bit_names)
{
    int count;
    char str[100];
    char str2[100];

    struct debug_flag *flag;

    flag = &(debug_flags[debug_flag_count]);
    debug_flag_count++;

    for (count = 0; bit_names[count]; count++) {
        flag->bits[count].flag = flag;
        flag->bits[count].bit_position = count;
        flag->bits[count].bit_name = bit_names[count];
        snprintf(str, sizeof(str), "/debug/%s/%s", flag_name, bit_names[count]);
        zpath_debug_add_write_command("Toggle debug bit configuration",
                                str,
                                zpath_debug_flag_put_normal_ext,
                                &(flag->bits[count]),
                                NULL);
        snprintf(str, sizeof(str), "/catch/%s/%s", flag_name, bit_names[count]);
        zpath_debug_add_write_command("Toggle catch bit configuration",
                                str,
                                zpath_debug_flag_put_catch_ext,
                                &(flag->bits[count]),
                                NULL);
    }

    flag->bit_count = count;
    flag->flag_name = flag_name;
    flag->flag_2 = flag_address;
    for (int i = 0; i < size; i++)
      flag->flag_normal_2[i] = (uint16_t)zpn_debug_get_with_ctx(flag_address, i);
    for (int i = 0; i < size; i++)
      flag->flag_catch_2[i] = (uint16_t)zpn_debug_catch_defaults_get_with_ctx(catch_default, i);

    snprintf(str, sizeof(str), "/debug/%s", flag_name);
    snprintf(str2, sizeof(str2), "Dump bit configuration for %s", flag_name);
    zpath_debug_add_write_command(str2,
                            str,
                            zpath_debug_flag_dump_normal_ext,
                            flag,
                            "value", "Set debug flag to value",
                            NULL);


    snprintf(str, sizeof(str), "/catch/%s", flag_name);
    snprintf(str2, sizeof(str2), "Dump bit configuration for %s", flag_name);
    zpath_debug_add_write_command(str2,
                            str,
                            zpath_debug_flag_dump_catch_ext,
                            flag,
                            NULL);



    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_mem_view(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    char buf[16384];

    argo_log_collection_mem_view(buf, sizeof(buf));
    zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_summary(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    char buffer[16384] = {'\0'};
    argo_log_dump_logging_lib_params(buffer, sizeof(buffer));
    zpath_debug_cb_printf_response(request_state, "%s\n", buffer);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_dump_registered_structs(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    char buffer[1024*1024] = {'\0'};
    argo_log_dump_registered_structs(buffer, sizeof(buffer));
    zpath_debug_cb_printf_response(request_state, "%s\n", buffer);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_purge_all(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    (void)argo_log_purge_collections(1, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_purge_specific(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    if (query_values[0]) {
        const char *name = query_values[0];
        (void)argo_log_purge_collections(0, name, 0);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_summary_reset(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    argo_log_logging_lib_params_reset();
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_set_mem_threshold(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    if (query_values[0]) {
        int64_t mem_threshold_size_MB = strtol(query_values[0], NULL, 0);
        if (mem_threshold_size_MB > 0)
            argo_log_global_set_mem_threshold_rest_endpoint(mem_threshold_size_MB);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_set_mem_difference(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    if (query_values[0]) {
        int64_t mem_threshold_size_MB = strtol(query_values[0], NULL, 0);
        if (mem_threshold_size_MB > 0)
            argo_log_global_set_mem_difference_threshold_rest_endpoint(mem_threshold_size_MB);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_set_mem_thread_interval(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    if (query_values[0]) {
        int64_t seconds = strtol(query_values[0], NULL, 0);
        if (seconds > 0)
            argo_log_global_set_memory_thread_interval_rest_endpoint(seconds);
    }
    return ZPATH_RESULT_NO_ERROR;
}

void zpath_debug_collection_dump_all_cb(struct argo_log_collection *collection, void *cookie)
{
    struct zpath_debug_state *request_state = cookie;
    char buf[8192];
    argo_log_status(collection, buf, sizeof(buf));
    ZDP("%s", buf);
}

int zpath_debug_collection_dump_all(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    argo_log_collection_iterate(zpath_debug_collection_dump_all_cb, request_state);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_add_collection_mem_view(void)
{
    char str[256];
    int res;

    snprintf(str, sizeof(str), "/argo/log/collection/mem_view");
    res = zpath_debug_add_read_command("argo log collection mem view",
                                  str,
                                  zpath_debug_collection_mem_view,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/all");
    res = zpath_debug_add_read_command("argo log collection dump all",
                                  str,
                                  zpath_debug_collection_dump_all,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/dump_registered_stats_list");
    res = zpath_debug_add_read_command("argo log registered stats structs dump all",
                                  str,
                                  zpath_debug_dump_registered_structs,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/summary");
    res = zpath_debug_add_safe_read_command("system argo collection summaries",
                                  str,
                                  zpath_debug_collection_summary,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/summary_reset");
    res = zpath_debug_add_write_command("reset eviction counters in summary",
                                  str,
                                  zpath_debug_collection_summary_reset,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/purge_all_collections");
    res = zpath_debug_add_admin_command("Purges all logging objects from all collections",
                                  str,
                                  zpath_debug_collection_purge_all,
                                  NULL,
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/purge_collection");
    res = zpath_debug_add_admin_command("Purges all logging objects from a specific collection",
                                  str,
                                  zpath_debug_collection_purge_specific,
                                  NULL,
                                  "name", "Name of the collection to purge",
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/set_argo_memory_threshold");
    res = zpath_debug_add_write_command("Purges all logging objects from all collections",
                                  str,
                                  zpath_debug_collection_set_mem_threshold,
                                  NULL,
                                  "MB", "Absolute value to be set in MB",
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/set_argo_memory_difference");
    res = zpath_debug_add_write_command("Sets argo logging high tide check mem difference",
                                  str,
                                  zpath_debug_collection_set_mem_difference,
                                  NULL,
                                  "MB", "Absolute value to be set in MB",
                                  NULL);
    if (res) return res;

    snprintf(str, sizeof(str), "/argo/log/collection/set_argo_memory_thread_interval");
    res = zpath_debug_add_write_command("Sets argo logging high tide check interval",
                                  str,
                                  zpath_debug_collection_set_mem_thread_interval,
                                  NULL,
                                  "seconds", "Absolute value to be set in seconds",
                                  NULL);
    if (res) return res;

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_collection_dump(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    char buf[8192];
    struct argo_log_collection *collection = cookie;
    argo_log_status(collection, buf, sizeof(buf));
    zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_add_collection(struct argo_log_collection *collection)
{
    char str[256];

    snprintf(str, sizeof(str), "/argo/log/%s", argo_log_get_name(collection));
    return zpath_debug_add_read_command("Dump log buffer state",
                                   str,
                                   zpath_debug_collection_dump,
                                   collection,
                                   NULL);
}

int zpath_debug_fohh_collection_dump(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    char buf[8192];
    struct argo_log_collection *collection = cookie;
    fohh_log_status(collection, buf, sizeof(buf));
    zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_add_fohh_log_collection(struct argo_log_collection *collection)
{
    char str[256];
    snprintf(str, sizeof(str), "/fohh/log/%s", argo_log_get_name(collection));
    return zpath_debug_add_read_command("Dump log transmit state",
                                   str,
                                   zpath_debug_fohh_collection_dump,
                                   collection,
                                   NULL);

}

int zpath_debug_catch_ip_ui(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    char out_str[ARGO_INET_ADDRSTRLEN];

    ZDP("CATCH: %d triggers have occurred since catch change.\n",
        catch_count);

    if (query_values[1]) {
        catch_max = strtoul(query_values[1], NULL, 0);
        ZDP("SET: Maximum catch triggers to %d, cleared trigger count\n", catch_max);
        catch_count = 0;
    } else {
        ZDP("CATCH: Maximum triggers currently set to %d\n", catch_max);
    }


    if (query_values[0] == NULL) {
        if (!catch_enabled) {
            ZDP("CATCH: No IP address configured\n");
        } else {
            ZDP("CATCH: IP address: %s\n", argo_inet_generate(out_str, &zpath_debug_ip_to_catch));
        }
    } else {
        /* Scan a new IP... Note that when clearing the IP we create a
         * small race condition on our catch 'stack'. We deal with
         * that by resetting the catch stack, and making sure uncatch
         * can deal with underrun. Note that this STILL leaves a
         * little race... Which should be fixable by recalling this
         * routine. */
        int res = argo_string_to_inet(query_values[0], &zpath_debug_ip_to_catch);
        if ((strlen(query_values[0]) == 0) || (res != ARGO_RESULT_NO_ERROR)) {
            ZDP("CATCH: IP address could not be interpreted: <%s>, now disabled, trigger count cleared\n", query_values[0]);
            memset(&zpath_debug_ip_to_catch, 0, sizeof(zpath_debug_ip_to_catch));
            catch_count = 0;
            catch_enabled = 0;
        } else {
            ZDP("CATCH: IP address set to %s, cleared trigger count.\n",
                argo_inet_generate(out_str, &zpath_debug_ip_to_catch));
            catch_count = 0;
            catch_enabled = 1;
        }
        zpath_debug_normal();
        catch_stack = 0;
    }
    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_catch(void)
{
    int i;
    for (i = 0; i < debug_flag_count; i++) {
        *(debug_flags[i].flag) = debug_flags[i].flag_catch;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_normal(void)
{
    int i;
    for (i = 0; i < debug_flag_count; i++) {
        *(debug_flags[i].flag) = debug_flags[i].flag_normal;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_catch_ip(struct argo_inet *inet)
{
    int count;

    if (catch_enabled) {
        if (memcmp(inet, &zpath_debug_ip_to_catch, sizeof(struct argo_inet)) == 0) {
            count = __sync_fetch_and_add_4(&catch_stack, 1);
            if (count == 0) {
                count = __sync_add_and_fetch_4(&catch_count, 1);
                if (count < catch_max) {
                    zpath_debug_catch();
                }
            }
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_uncatch_ip(struct argo_inet *inet)
{
    int count;
    if (catch_enabled) {
        if (memcmp(inet, &zpath_debug_ip_to_catch, sizeof(struct argo_inet)) == 0) {
            count = __sync_sub_and_fetch_4(&catch_stack, 1);
            if (count == 0) {
                zpath_debug_normal();
            } else if (count < 0) {
                zpath_debug_normal();
                catch_stack = 0;
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_add_allocator(struct zpath_allocator *allocator_arg, const char *name)
{
    if (strnlen(name, ZPATH_ALLOCATOR_NAME_MAX_LEN + 1) > ZPATH_ALLOCATOR_NAME_MAX_LEN) {
        ZPATH_LOG(AL_ERROR, "Allocator name %s is larger than max length %d", name, ZPATH_ALLOCATOR_NAME_MAX_LEN);
        return ZPATH_RESULT_ERR;
    }

    pthread_mutex_lock(&allocator_lock);

    if (allocator_count >= ZPATH_DEBUG_MAX_ALLOCATORS) {
        pthread_mutex_unlock(&allocator_lock);
        ZPATH_LOG(AL_ERROR, "Adding more allocators than max supported for debug, allocator name %s", name);
        return ZPATH_RESULT_ERR;
    }

    if (zpath_debug_allocator_disallow_further_allocations) {
        pthread_mutex_unlock(&allocator_lock);
        ZPATH_LOG(AL_NOTICE,
                  "New allocator '%s' is created past memory stats creation, it wont be in the stats. "
                  "Current allocators(%d)",
                  name, allocator_count);
        return ZPATH_RESULT_NO_ERROR;
    }

    for (int i = 0; i < allocator_count; i++) {
        if (allocator[i] == allocator_arg) {
            pthread_mutex_unlock(&allocator_lock);
            __sync_fetch_and_or(&allocator_arg->allocator_flag, ZPATH_ALLOCATOR_FLAG_DBG_RE_REGISTER);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    __sync_fetch_and_or(&allocator_arg->allocator_flag, ZPATH_ALLOCATOR_FLAG_DBG_REGISTERED);
    allocator[allocator_count] = allocator_arg;
    allocator_name[allocator_count] = name;
    __sync_add_and_fetch_4(&allocator_count, 1);

    pthread_mutex_unlock(&allocator_lock);

    return ZPATH_RESULT_NO_ERROR;
}

void zpath_debug_disallow_further_allocators()
{
    pthread_mutex_lock(&allocator_lock);
    zpath_debug_allocator_disallow_further_allocations = 1;
    pthread_mutex_unlock(&allocator_lock);
}

void zpath_debug_memory_allocator_stats_fill_thread_internal(int counter, struct zpath_debug_memory_allocator_stats *data)
{
    pthread_mutex_lock(&allocator_lock);
    data->allocations = allocator[counter]->stats.allocations;
    data->allocation_bytes = allocator[counter]->stats.allocation_bytes;
    data->frees = allocator[counter]->stats.frees;
    data->free_bytes = allocator[counter]->stats.free_bytes;
    data->drain_queue = allocator[counter]->stats.drain_queue;
    data->drain_queue_bytes = allocator[counter]->stats.drain_queue_bytes;
    pthread_mutex_unlock(&allocator_lock);
}

/*
 * Name is hardcoded as "overall usage". This is because the stats collection
 * will add all the thread stats, but name the combination as the last thread's name.
 * Its a hack, but for now..
 */
int zpath_debug_memory_allocator_stats_fill_all(void *cookie, int counter, void *structure_data)
{
    struct zpath_debug_memory_allocator_stats *data;
    (void) cookie;

    data = (struct zpath_debug_memory_allocator_stats *)structure_data;
    data->name = (char *)"overall usage";
    zpath_debug_memory_allocator_stats_fill_thread_internal(counter, data);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Fill the stats per allocator.
 */
int zpath_debug_memory_allocator_stats_fill_allocator(void *cookie, int bad_counter, void *structure_data)
{
    int counter;
    struct zpath_debug_memory_allocator_stats *data;

    counter = *(int *)cookie;

    data = (struct zpath_debug_memory_allocator_stats *)structure_data;
    data->name = (char *)allocator_name[counter];
    zpath_debug_memory_allocator_stats_fill_thread_internal(counter, data);

    return ZPATH_RESULT_NO_ERROR;
}

size_t zpath_debug_current_allocators_count()
{
    return __sync_fetch_and_or(&allocator_count, 0);
}

static struct debug_sni_servers {
    struct fohh_generic_server *server;
    const char *name;
} sni_servers[ZPATH_DEBUG_MAX_GENERIC_SERVERS];


int zpath_debug_fohh_list_snis(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    struct debug_sni_servers *server = cookie;
    size_t count;
    size_t allocated;
    size_t ix;
    int res;
    char **dest;

    res = fohh_generic_server_get_snis(server->server, NULL, 0, &count, 0);
    if (res) {
        ZDP("Could not get sni count\n");
        return res;
    }

    allocated = count;
    dest = ZLIB_MALLOC(sizeof(*dest) * allocated);
    for (ix = 0; ix < allocated; ix++) {
        dest[ix] = ZLIB_MALLOC(256 + 16); /* 16 for additional states */
    }

    res = fohh_generic_server_get_snis(server->server, dest, 256 + 16, &count, 1);
    if (res) {
        ZDP("Could not get snis\n");
    } else {
        ZDP("SNIs for %s\n", server->name);
        for (ix = 0; ix < count; ix++) {
            ZDP("  %s\n", dest[ix]);
        }
    }

    for (ix = 0; ix < allocated; ix++) {
        ZLIB_FREE(dest[ix]);
    }
    ZLIB_FREE(dest);

    return res;
}

int zpath_debug_add_fohh_generic_server(struct fohh_generic_server *server, const char *name)
{
    int res;

    char str[100];
    snprintf(str, sizeof(str), "/fohh/generic_server/%s", name);
    if (sni_server_count >= ZPATH_DEBUG_MAX_GENERIC_SERVERS) {
        ZPATH_LOG(AL_ERROR, "Too many");
        return ZPATH_RESULT_ERR;
    }
    sni_servers[sni_server_count].server = server;
    sni_servers[sni_server_count].name = ZLIB_STRDUP(name, strlen(name));

    res = zpath_debug_add_read_command("Show SNIs for generic server",
                                  str,
                                  zpath_debug_fohh_list_snis,
                                  &(sni_servers[sni_server_count]),
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not add command: %s", zpath_result_string(res));
    } else {
        sni_server_count++;
    }
    return res;
}

struct fohh_generic_server *zpath_debug_lookup_fohh_generic_server(const char *name)
{
    for (int i = 0; i < sni_server_count; i++) {
        if (!strcmp(sni_servers[i].name, name)) {
            return sni_servers[i].server;
        }
    }
    return NULL;
}

int zpath_debug_mem_stats_init(enum zpath_service_type service_type)
{
    int res = 0;
    int64_t time_period = MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC;
    size_t count;
    void** zpath_mem_stats;
    void** zpath_mem_stats_thread;
    int** zpath_mem_stats_thread_cookie;

    /*
     * At this point of time, most of the process initialization is done. We don't expect any further memory
     * allocators to be initialized. It would have been nicer for us to dynamically discover any memory allocators and
     * keep track of it from application, but don't see any need for it at the moment. BTW, we keep track of the
     * allocators to pull stats from it.
     */
    zpath_debug_disallow_further_allocators();

    if(service_type == zpath_service_assistant) {
        time_period = ASSISTANT_MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC;
    }

    if (service_type == zpath_service_private_broker) {
        time_period = PBROKER_MEMORY_STATS_TX_MEM_USAGE_OVERALL_TIMEPERIOD_USEC;
    }

    size_t num_allocators = zpath_debug_current_allocators_count();
    zpath_mem_stats = ZLIB_MALLOC(sizeof(void *) * num_allocators);
    zpath_mem_stats_thread = ZLIB_MALLOC(sizeof(void *) * num_allocators);
    zpath_mem_stats_thread_cookie = ZLIB_MALLOC(sizeof(void *) * num_allocators);
    for (count=0; count < num_allocators; count++) {
        zpath_mem_stats[count] = ZLIB_MALLOC(sizeof(struct zpath_debug_memory_allocator_stats));
        zpath_mem_stats_thread[count] = ZLIB_MALLOC(sizeof(struct zpath_debug_memory_allocator_stats));
        zpath_mem_stats_thread_cookie[count] = ZLIB_MALLOC(sizeof(int));
        *zpath_mem_stats_thread_cookie[count] = count;
    }

    if (!argo_log_register_structures(argo_log_get("statistics_log"),
                                      "zpath_mem_usage_overall",
                                      AL_INFO,
                                      time_period,
                                      zpath_debug_memory_allocator_stats_description,
                                      &(zpath_mem_stats[0]),
                                      num_allocators,
                                      1,
                                      zpath_debug_memory_allocator_stats_fill_all,
                                      NULL)) {
        ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    for (count=0; count < num_allocators; count++) {
        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         allocator_name[count],
                                         AL_INFO,
                                         MEMORY_STATS_TX_MEM_USAGE_ALLOCATOR_TIMEPERIOD_USEC,
                                         zpath_debug_memory_allocator_stats_description,
                                         zpath_mem_stats_thread[count],
                                         1,
                                         zpath_debug_memory_allocator_stats_fill_allocator,
                                         zpath_mem_stats_thread_cookie[count])) {
            ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
            res = ZPATH_RESULT_ERR;
            goto done;
        }
    }

done:
    ZLIB_FREE(zpath_mem_stats);
    ZLIB_FREE(zpath_mem_stats_thread);
    ZLIB_FREE(zpath_mem_stats_thread_cookie);
    return res;
}

int argo_mem_stats_init(enum zpath_service_type service_type)
{
    if (!(argo_allocator_mem_stats_description = argo_register_global_structure(ARGO_ALLOCATOR_MEM_STATS_HELPER))){
        return ZPATH_RESULT_ERR;
    }

    int result = 0;

    void **zpath_argo_mem_stats = ZLIB_MALLOC(sizeof(void *) * ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT);
    int **zpath_argo_mem_stats_cookie = ZLIB_MALLOC(sizeof(void *) * ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT);

    /* Adding memory stats for argo. Check enum zpath_argo_allocator_type */
    int index;
    for (index = 0; index < ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT; index++) {
        zpath_argo_mem_stats[index] = ZLIB_MALLOC(sizeof(struct argo_allocator_mem_stats));
        zpath_argo_mem_stats_cookie[index] = ZLIB_MALLOC(sizeof(int));
        *zpath_argo_mem_stats_cookie[index] = index;
    }

    for(index = 0; index < ZPATH_ARGO_ALLOCATOR_TOTAL_COUNT; index++) {
        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                    argo_allocator_type_names[index],
                                    AL_INFO,
                                    MEMORY_STATS_TX_MEM_USAGE_ALLOCATOR_TIMEPERIOD_USEC,
                                    argo_allocator_mem_stats_description,
                                    zpath_argo_mem_stats[index],
                                    1,
                                    argo_load_stats_cb,
                                    zpath_argo_mem_stats_cookie[index])) {
            ZPATH_LOG(AL_ERROR, "Could not register zpath_argo stats");
            result = ZPATH_RESULT_ERR;
            break;
        }
    }

    ZLIB_FREE(zpath_argo_mem_stats);
    ZLIB_FREE(zpath_argo_mem_stats_cookie);
    return result;
}

void zpath_debug_add_fohh_tracker_commands() {
    (void)zpath_debug_add_safe_read_command("Print fohh_tracker stats.",
                                  "/fohh/tracker/stats",
                                  debug_print_fohh_tracker_stats,
                                  NULL,
                                  NULL);
    (void)zpath_debug_add_safe_read_command("Print fohh_tracker stats.",
                                  "/fohh/tracker/counters",
                                  debug_print_fohh_tracker_counters,
                                  NULL,
                                  NULL);
    (void)zpath_debug_add_write_command("Print fohh_tracker stats config.",
                                  "/fohh/tracker/reset",
                                  debug_fohh_tracker_reset,
                                  NULL,
                                  NULL);
    (void)zpath_debug_add_write_command("Toggle logging of fohh tracker start/end events.",
                                  "/fohh/tracker/log",
                                  debug_fohh_tracker_log,
                                  NULL,
                                  "on", "turn logging on",
                                  "off", "turn logging off",
                                  NULL);
}

/*
 * 1 if we are running currently in DEV environment. 0 otherwise.
 */
int
zpath_is_dev_environment()
{
    static int is_dev_env = 0;
    static int result_already_cooked = 0;

    if (result_already_cooked) {
        return is_dev_env;
    }

    if (ZPATH_LOCAL_CLOUD_NAME == NULL) {
        // Default to Prod when cloud name is NULL
        ZPATH_LOG(AL_NOTICE, "Cloud name is NULL, defaulting to Prod environment");
        is_dev_env = 0;
    } else {
        int is_zpa_cloud_dev = zpath_is_cloud_dev_env(ZPATH_LOCAL_CLOUD_NAME);
        if (is_zpa_cloud_dev == 1) {
            is_dev_env = 1;
        }
    }

    fohh_set_dev_environment(is_dev_env);

    result_already_cooked = 1;
    return is_dev_env;
}

void zpath_zthread_hb_override_timer_cb(int sock, short flags, void *cookie) {
    ZPATH_LOG(AL_NOTICE, "Expired: Heartbeat Override for duration: %ds ", zpath_zthread_hb_override_duration);
    zthread_set_heartbeat_override(0);
    zpath_zthread_hb_override_duration=0;
    if (zpath_zthread_hb_override_timer)
    {
        event_free(zpath_zthread_hb_override_timer);
        zpath_zthread_hb_override_timer = NULL;
    }
}

int zpath_zthread_set_heartbeat_override(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object)
{
    struct timeval tv;
    int override_timeout=0;
    int override_duration=0;
    struct timeval time_now;

    override_timeout  = query_values[0] ? atoi(query_values[0]) : 0;
    if (override_timeout == 0){
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if ((override_timeout < ZPATH_ZTHREAD_HEARTBEAT_MIN_OVERRIDE_TIMEOUT )  ||
        (override_timeout > ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_TIMEOUT)) {
        ZDP("ERROR: Heartbeat Override NOT INITIATED \n");
        ZDP("Override timeout not in valid range between %ds - %ds \n", ZPATH_ZTHREAD_HEARTBEAT_MIN_OVERRIDE_TIMEOUT, ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_TIMEOUT);
        return ZPATH_RESULT_NO_ERROR;
    }


    override_duration = query_values[1] ? atoi(query_values[1]) : 2*override_timeout;
    if ((override_duration <= 0) ||
        (override_duration > ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_DURATION)) {
        ZDP("ERROR: Heartbeat Override NOT INITIATED \n");
        ZDP("Override Duration is not in valid range between 1s - %ds \n", ZPATH_ZTHREAD_HEARTBEAT_MAX_OVERRIDE_DURATION);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (zpath_zthread_hb_override_timer) {
        ZDP("ERROR: Heartbeat Override NOT INITIATED\n");
        ZDP("Active Heartbeat override in-progress with values: \n");
        zthread_get_heartbeat_override(&override_timeout);
        ZDP("Override Timeout: %ds \n", override_timeout);
        ZDP("Override Duration: %ds \n", zpath_zthread_hb_override_duration);
        ZDP("Remaining Time: %d seconds \n", zpath_zthread_hb_get_remaining_override_duration());
        return ZPATH_RESULT_NO_ERROR;
    }

    tv.tv_sec = override_duration;
    tv.tv_usec = 0;

    zpath_zthread_hb_override_timer = event_new(zpath_debug_get_base(),
                         -1,
                         0,
                         zpath_zthread_hb_override_timer_cb,
                         NULL);

    if (!zpath_zthread_hb_override_timer) {
        ZPATH_LOG(AL_ERROR, "Could not configure heartbeat override");
        return ZPATH_RESULT_ERR;
    }

    if (event_add(zpath_zthread_hb_override_timer, &tv)) {
        ZPATH_LOG(AL_ERROR, "Could not start heartbeat override timer");
        event_free(zpath_zthread_hb_override_timer);
        zpath_zthread_hb_override_timer = NULL;
        return ZPATH_RESULT_ERR;
    }

    evutil_gettimeofday(&time_now, NULL);
    evutil_timeradd(&time_now, &tv, &zpath_zthread_hb_override_target_time);
    zthread_set_heartbeat_override(override_timeout);
    zpath_zthread_hb_override_duration=override_duration;
    ZPATH_LOG(AL_NOTICE, "New Heartbeat Override initiated with timeout %ds and duration: %ds ",override_timeout, zpath_zthread_hb_override_duration);
    ZDP("New Heartbeat Override initiated with values: \n");
    ZDP("Override Timeout: %ds \n", override_timeout);
    ZDP("Override Duration: %ds \n", zpath_zthread_hb_override_duration);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_zthread_hb_get_remaining_override_duration() {
    struct timeval time_now;
    struct timeval time_diff;
    evutil_gettimeofday(&time_now, NULL);
    evutil_timersub(&zpath_zthread_hb_override_target_time, &time_now, &time_diff);
    return (int)time_diff.tv_sec;
}

/** Unix domain socket based command handler **/
/*
 * Parse the query string and gather the arguments.
 *  This function is similar to query_parse; except that this works on a different arg.
 */
static int zpath_debug_command_listener_query_parse(struct fohh_domain_sock_cmd_listener_info *command_info)
{
    enum states {
        init,
        key_found,
        key_done,
        value_found,
        value_done,
        done
    };

    struct fohh_domain_sock_cmd_query_string **q;
    struct fohh_domain_sock_cmd_request *request = &(command_info->request);

    const char *w = request->req_query;

    const char *key_start = w;
    const char *key_end = NULL;
    const char *value_start = NULL;
    const char *value_end = NULL;

    enum states s = init;
    char ch;

    if (!w) return ZPATH_RESULT_NO_ERROR;

    q = &(request->queries);

    while (s != done) {
        ch = *w;
        switch (s) {
        case init:
        case value_done:
            if (ch == 0) {
                s = done;
                break;
            }
            if (isspace(ch) || (ch == '&') || (ch == '=')) {
                break;
            }
            key_start = w;
            s = key_found;
            break;

        case key_found:
            key_end = w;
            if (isspace(ch) || (ch == 0)) {
                value_start = w;
                value_end = w;
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            if (ch == '=') {
                s = key_done;
                break;
            } else if (ch == '&') {
                s = key_done;
                ZPATH_FALLTHROUGH;
            } else {
                break;
            }

        case key_done:
            /* Skip '=', whitespace */
            if (isspace(ch) || (ch == 0) || (ch == '&')) {
                /* Erk. */
                value_start = w;
                value_end = w;
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            if (ch == '=') break;
            value_start = w;
            s = value_found;
            break;

        case value_found:
            value_end = w;
            if ((ch == '&') || isspace(ch) || (ch == 0)) {
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            break;
        default:
            break;
        }

        w++;

        if ((s == value_done) || (s == done)) {
            struct fohh_domain_sock_cmd_query_string *qs;

            // coverity[REVERSE_INULL]
            if (key_start && key_end && value_start && value_end) {
                qs = zmicro_heap_alloc_aligned(&(command_info->heap), sizeof(*qs));
                if (!qs) {
                    return ZPATH_RESULT_NO_MEMORY;
                }
                qs->key = zmicro_heap_str(&(command_info->heap), key_start, key_end - key_start);
                if (!qs->key) return ZPATH_RESULT_NO_MEMORY;
                qs->value = zmicro_heap_str(&(command_info->heap), value_start, value_end - value_start);
                if (!qs->value) return ZPATH_RESULT_NO_MEMORY;

                ZPATH_DEBUG_CMD_LISTENER("Command %s: Query Key = \"%s\", Value = \"%s\"",
                                       command_info->request.req_cmd,
                                       qs->key,
                                       qs->value);

                /* Link it in: */
                qs->next = (*q);
                (*q) = qs;
            }
            key_end = NULL;
            value_start = NULL;
            value_end = NULL;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* a description for the command type. */
const char *
zpath_debug_get_cmd_access_level_str(enum zpath_debug_cmd_access_level level)
{
    if (level < zpath_debug_cmd_min || level > zpath_debug_cmd_max)
        return "<Invalid>";
    return zpath_debug_cmd_access_level_strings[level];
}

/* Parse the command to separate out path and query */
static int
zpath_debug_handle_command_listener_input(const char *data, size_t size, struct fohh_domain_sock_cmd_listener_info *command_info)
{
    int res = ZPATH_RESULT_NO_ERROR;

    ZPATH_DEBUG_CMD_LISTENER("command-listener: Handler - data: %s  len: %lu", data, size);

    command_info->request.req_cmd = zmicro_heap_str(&(command_info->heap), data, size);

    /* Find the '?' in the data, indicating the start of the query string */
    char *query_start = strchr(data, '?');
    if (query_start) {
        /* Split the data into path and query */
        size_t path_length = query_start - data;
        command_info->request.req_path = zmicro_heap_str(&(command_info->heap), data, path_length);

        char *query = query_start + 1;  // The query starts after the '?'

        size_t query_length = size - path_length - 1;
        command_info->request.req_query = zmicro_heap_str(&(command_info->heap), query, query_length);

        /* parse the query */
        zpath_debug_command_listener_query_parse(command_info);

    } else {
        /* If there's no query string, treat the entire data as path */
        command_info->request.req_path = zmicro_heap_str(&(command_info->heap), data, size);
    }

    ZPATH_DEBUG_CMD_LISTENER("command-listener: request cmd: %s", command_info->request.req_cmd);
    ZPATH_DEBUG_CMD_LISTENER("command-listener: request path: %s", command_info->request.req_path);
    ZPATH_DEBUG_CMD_LISTENER("command-listener: request query: %s", command_info->request.req_query);
    ZPATH_DEBUG_CMD_LISTENER("command-listener: user_info: user: %s uid: %d",
                zpath_debug_get_username_from_uid(command_info->user_info.peer_uid),
                command_info->user_info.peer_uid);

    struct fohh_domain_sock_cmd_query_string *temp;
    for (temp = command_info->request.queries ; temp; temp = temp->next) {
        ZPATH_DEBUG_CMD_LISTENER("command-listener: Query Key: %s\t Value: %s", temp->key, temp->value);
    }

    /* Now we have parsed the command, find the path and options */
    struct fohh_http_server_registered_path *registered_path = NULL;
    registered_path = path_find_command_request(zpath_debug_get_http_server(), command_info->request.req_path);

    if (!registered_path) {
        /* send return as unable to find path */
        command_info->response.body = evbuffer_new();
        if (!command_info->response.body) {
            ZPATH_LOG(AL_ERROR, "command-listener: Command: %s could not allocate evbuffer for response", command_info->request.req_cmd);
            return ZPATH_RESULT_NO_MEMORY;
        }
        evbuffer_set_dont_dump(command_info->response.body);
        evbuffer_add_printf(command_info->response.body, "404 Not Found\n\r");
    } else {
        if (registered_path->command_req_cb) {
            res = (registered_path->command_req_cb)(command_info,
                                     &(command_info->request),
                                     registered_path->host->host,
                                     registered_path->path,
                                     registered_path->void_cookie,
                                     registered_path->int_cookie);
        } else {
            ZPATH_LOG(AL_ERROR, "command-listener: callback not present for command: %s", command_info->request.req_cmd);
            res = ZPATH_RESULT_ERR;
        }
    }
    return res;
}

/* Callback to handle read events (when we receive data from the client) */
void zpath_debug_command_listener_read_cb(struct bufferevent *bev, void *ctx)
{
    struct evbuffer *in_buf;
    size_t in_buf_len, out_buf_len = 0;
    struct zpath_debug_command_user_info *user_info = ctx;

    in_buf = bufferevent_get_input(bev);
    in_buf_len = evbuffer_get_length(in_buf);
    char data_buf[in_buf_len+1];
    size_t rx_len;

    if (in_buf_len > 0) {
        memset(data_buf, 0, sizeof(data_buf));
        rx_len = evbuffer_copyout(in_buf, data_buf, sizeof(data_buf));
        if (rx_len <= 0) {
            ZPATH_LOG(AL_CRITICAL, "command-listener: evbuffer_copyout failed for buf len: %ld", in_buf_len);
            return;
        }
        ZPATH_DEBUG_CMD_LISTENER("command-listener: Received command from client: %s", data_buf);

        /* Keep the request on the stack and process it. */
        struct fohh_domain_sock_cmd_listener_info command_info;

        /* initializations */
        memset(&command_info, 0, sizeof(command_info));
        zmicro_heap_init(&(command_info.heap), command_info.header_value_buf, sizeof(command_info.header_value_buf));
        memset(&(command_info.request), 0, sizeof(command_info.request));
        memset(&(command_info.response), 0, sizeof(command_info.response));

        /* copy the user info details */
        command_info.user_info.peer_uid = user_info->peer_uid;

        int res = zpath_debug_handle_command_listener_input(data_buf, rx_len, &command_info);
        if ((ZPATH_RESULT_NO_ERROR == res)) {
            if (command_info.response.body != NULL) {
                out_buf_len = evbuffer_get_length(command_info.response.body);
            }
            if (bufferevent_write_buffer(bev, command_info.response.body)) {
                ZPATH_LOG(AL_ERROR, "command-listener: Failed to write response to bev for command: %s", data_buf);
            } else {
                ZPATH_DEBUG_CMD_LISTENER("command-listener: Successfully wrote response of size: %ld", out_buf_len);
            }
            evbuffer_free(command_info.response.body);
        } else {
            ZPATH_LOG(AL_CRITICAL, "command-listener: failed to get response for command: %s; error: %s(%d)",
                            data_buf, zpath_result_string(res), res);
        }

        /* Cleanups */
        evbuffer_drain(in_buf, in_buf_len);

    } else {
        ZPATH_LOG(AL_ERROR, "command-listener: read_cb invoked, but no data received");
    }
}

static void zpath_debug_command_listener_write_cb(struct bufferevent *bev, void *cookie)
{
    /* do nothing */
}

/* Callback to handle events on the buffer (e.g., connection closed, error) */
void zpath_debug_command_listener_event_cb(struct bufferevent *bev, short events, void *ctx)
{
    //TODO: add debugs to another filter
    if (events & BEV_EVENT_ERROR) {
        ZPATH_LOG(AL_ERROR, "command-listener: Error with bufferevent");
    } else if (events & BEV_EVENT_EOF) {
        ZPATH_DEBUG_CMD_LISTENER("command-listener: Client disconnected");
    }

    if (events & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
        struct zpath_debug_command_user_info *info = ctx;
        if (info) {
            ZLIB_FREE(info);
        }
        if (NULL == bev) {
            return;
        }

        bufferevent_disable(bev, EV_READ|EV_WRITE);
        bufferevent_setcb(bev, NULL, NULL, NULL, NULL);
        bufferevent_free(bev);
    }
}

/* Checks if RBAC is supported for the role */
int zpath_debug_is_rbac_supported(const char *role_name)
{
    if (!role_name)
        return 0;

    //Iterate over the allowed roles list to find a match
    size_t len = sizeof(zpath_debug_rbac_supported_roles)/sizeof(zpath_debug_rbac_supported_roles[0]);
    for (size_t i = 0; i < len; i++) {
        if (0 == strcmp(zpath_debug_rbac_supported_roles[i], role_name)) {
            return 1;
        }
    }
    //No match found, so rbac not supported
    return 0;
}

/*
 * Update default rbac mode based on component.
 * If component supports RBAC, we enable it here; and let the override take care of updation.
 * else, we keep it disabled.
 */
void zpath_debug_update_rbac_enablement(const char *role_name)
{
    zpath_rbac_supported = zpath_debug_is_rbac_supported(role_name);
    ZPATH_DEBUG_CURL_CMD("command-listener: Role-based access control init for role: %s -> %s: %s",
                role_name, zpath_rbac_supported ? "supported" : "not-supported",
                zpath_rbac_enabled ? "enabled" : "disabled");
}

void zpath_debug_dump_access_groups_info(const char *role_name)
{
    if (!role_name) {
        return;
    }

    for (size_t j = 0; j < zpath_debug_rbac_groups_count; j++) {
        ZPATH_DEBUG_CURL_CMD("command-listener: Added group: %s with access-level: %s for role: %s",
                zpath_debug_rbac_groups[j].group_name,
                zpath_debug_get_cmd_access_level_str(zpath_debug_rbac_groups[j].access_level),
                role_name);
    }
}

/*
 * For a given usergroup, check if it is part of the global rbac group configured.
 *  if so, return the corresponding access level.
 *  If not found, return the least privilege (ie. read access)
 */
enum zpath_debug_cmd_access_level
zpath_debug_get_access_level_for_group(const char *group_name)
{
    if (!group_name) {
        return zpath_debug_cmd_read;
    }
    for (size_t i = 0; i < zpath_debug_rbac_groups_count; i++) {
        if (0 == strcmp(group_name, zpath_debug_rbac_groups[i].group_name)) {
            return zpath_debug_rbac_groups[i].access_level;
        }
    }
    return zpath_debug_cmd_read;
}

/*
 * For the userid, check the highest access level possible.
 *  To achieve this:
 *      - first identify the groups the user is part of.
 *      - We have specific user groups with privileges associated with it.
 *      - If any user group matches one of those, we assume that privilege.
 *      - Go over all the groups and identify which group gives highest privilege.
 * NOTE: doing this only on linux; for the rest, we give all access.
 */
enum zpath_debug_cmd_access_level
zpath_debug_get_highest_access_level(uid_t client_uid)
{
#if defined(__linux__)
    struct passwd *user_info;
    struct group *grp_info;
    int group_count = 0;
    enum zpath_debug_cmd_access_level tmp_access_level = zpath_debug_cmd_read;
    enum zpath_debug_cmd_access_level access_level = zpath_debug_cmd_read; //default is lowest privilege.

    /* get username from UID */
    char *username = zpath_debug_get_username_from_uid(client_uid);
    if (!username) {
        ZPATH_LOG(AL_ERROR, "command-listener: failed to get username from uid: %d", client_uid);
        return access_level;
    }

    /*  Get user information based on username */
    user_info = getpwnam(username);
    if (user_info == NULL) {
        ZPATH_LOG(AL_ERROR, "command-listener: User %s not found", username);
        return access_level;
    }

    /* get the group names and compare to see what permissions we got */
    if (getgrouplist(username, user_info->pw_gid, NULL, &group_count) == -1) {
        gid_t groups[group_count];
        //Now gather the group info.
        if (getgrouplist(username, user_info->pw_gid, groups, &group_count) == -1) {
            ZPATH_LOG(AL_ERROR, "command-listener: Error getting group list for user %s", username);
            return access_level;
        }

        for (int i = 0; i < group_count; i++) {
            grp_info = getgrgid(groups[i]);
            if (!grp_info) {
                ZPATH_LOG(AL_ERROR, "command-listener: failed to get group name for GID: %d", groups[i]);
                continue;
            }

            tmp_access_level = zpath_debug_get_access_level_for_group(grp_info->gr_name);
            if (tmp_access_level > access_level) {
                ZPATH_DEBUG_CMD_LISTENER("command-listener: access-level for user: %s (group: %s) elevated to %s",
                                username, grp_info->gr_name, zpath_debug_get_cmd_access_level_str(tmp_access_level));
                access_level = tmp_access_level;
            }
        }
    }
    return access_level;
#else
    return zpath_debug_cmd_admin;
#endif
}

/* get username from UID. NOTE: we are doing this for Linux alone. */
char *
zpath_debug_get_username_from_uid(uid_t client_uid)
{
#if defined(__linux__)
    struct passwd *pw;
    pw = getpwuid(client_uid);
    if (pw == NULL) {
        return NULL;
    }
    return pw->pw_name;
#else
    return "N/A";
#endif
}

int
zpath_debug_get_user_access_info(evutil_socket_t fd, struct zpath_debug_command_user_info *info)
{
#if defined(__linux__)
    struct ucred peer_cred;
    socklen_t peer_cred_len = sizeof(peer_cred);

    if (getsockopt(fd, SOL_SOCKET, SO_PEERCRED, &peer_cred, &peer_cred_len) == -1) {
        ZPATH_LOG(AL_ERROR, "command-listener: failed to get peercred socket options!!");
        return ZPATH_RESULT_NOT_FOUND;
    }
    info->peer_uid = peer_cred.uid;
#else
    /* we give all permissions in non linux env. as its not prod. */
    info->peer_uid = 0;
#endif

    return ZPATH_RESULT_NO_ERROR;
}

/* Callback to accept new client connections */
void zpath_debug_command_listener_accept_conn_cb(struct evconnlistener *listener,
                                               evutil_socket_t fd,
                                               struct sockaddr *address,
                                               int socklen,
                                               void *ctx)
{
    struct event_base *base = ctx;

    /* collect the credentials and access level for the user */
    struct zpath_debug_command_user_info *info;
    info = ZLIB_CALLOC(sizeof(struct zpath_debug_command_user_info));
    if (!info) {
        ZPATH_LOG(AL_ERROR, "command-listener: failed to allocate memory for user info");
        close(fd);
        return;
    }

    int res = ZPATH_RESULT_NO_ERROR;
    res = zpath_debug_get_user_access_info(fd, info);
    if (res) {
        ZPATH_LOG(AL_ERROR, "command-listener: Failed to get user credentials and access levels, return");
        close(fd);
        ZLIB_FREE(info);
        return;
    }

    /* Create a new bufferevent for the incoming connection */
    struct bufferevent *bev = bufferevent_socket_new(base, fd, BEV_OPT_CLOSE_ON_FREE);
    if (!bev) {
        ZPATH_LOG(AL_ERROR, "command-listener: Error creating new bufferevent socket");
        close(fd);
        ZLIB_FREE(info);
        return;
    }

    /* Set read and event callbacks */
    bufferevent_setcb(bev,
                      zpath_debug_command_listener_read_cb,
                      zpath_debug_command_listener_write_cb,
                      zpath_debug_command_listener_event_cb, info);
    bufferevent_enable(bev, EV_READ | EV_WRITE);
}

/* Keep the command handler thread alive with heartbeats */
static void zpath_debug_command_listener_heartbeat_timer(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zthread_info *zthread_arg = cookie;
    zthread_heartbeat(zthread_arg);
    return;
}

/*
 * Creates a unix domain socket based communication channel, and is listening for clients
 * clients send the command, and is processed by the server.
 */
static void *zpath_debug_command_listener_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct timeval tv;
    struct event *ev;

    /* create new event base */
    struct event_base *command_listener_event_base;
    command_listener_event_base = event_base_new();
    if (!command_listener_event_base) {
        ZPATH_LOG(AL_ERROR, "command-listener: Could not init command_listener_event_base");
        goto fail;
    }

    ev = event_new(command_listener_event_base,
                    -1,
                    EV_PERSIST,
                    zpath_debug_command_listener_heartbeat_timer,
                    zthread_arg);
    if (!ev) {
        ZPATH_LOG(AL_ERROR, "command-listener: Could not init HB timer event in command_listener evbase");
        goto fail;
    }

    /* punch heartbeat every second */
    tv.tv_usec = 0;
    tv.tv_sec = 1;
    if (event_add(ev, &tv)) {
        ZPATH_LOG(AL_ERROR, "command-listener: Could not add HB timer event in command_listener evbase");
        goto fail;
    }

    /* Remove the old socket if it exists */
    unlink(COMMAND_LISTENER_SOCKET_PATH);

    /* create a Unix domain socket address */
    struct sockaddr_un sa;
    memset(&sa, 0, sizeof(sa));
    sa.sun_family = AF_UNIX;
    // Skip the null byte at the start
    snprintf(&sa.sun_path[1], (sizeof(sa.sun_path) - 2), "%s", COMMAND_LISTENER_SOCKET_PATH);

    size_t len = offsetof(struct sockaddr_un, sun_path) + strlen(COMMAND_LISTENER_SOCKET_PATH) + 1;
    struct evconnlistener *listener;
    listener = evconnlistener_new_bind(command_listener_event_base,
                                       zpath_debug_command_listener_accept_conn_cb,
                                       command_listener_event_base,
                                       LEV_OPT_REUSEABLE | LEV_OPT_CLOSE_ON_FREE,
                                       128,
                                       (struct sockaddr*)&sa,
                                       len);

    if (!listener) {
        ZPATH_LOG(AL_ERROR, "command-listener: Error creating listener: %s", strerror(errno));
        return NULL;
    }

    ZPATH_DEBUG_CURL_CMD("command-listener: Server started on Unix domain socket %s len: %lu", COMMAND_LISTENER_SOCKET_PATH, len);

    zevent_base_dispatch(command_listener_event_base);

    ZPATH_LOG(AL_CRITICAL, "command-listener: event_base -  Not reachable");
    /* clean up */
    evconnlistener_free(listener);
    unlink(COMMAND_LISTENER_SOCKET_PATH);

 fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}

/*
 * zpath_debug_command_listener_init
 *  creates a domain-socker listener for serving commands
 *  NOTE: the server will be running in all modules
 */
int zpath_debug_command_listener_init(const char *role_name)
{
    /* we have the same groups for all roles; dump them for debuggability */
    zpath_debug_dump_access_groups_info(role_name);

    pthread_t thread;
    int res;

    res = zthread_create(&thread,
                         zpath_debug_command_listener_thread,
                         NULL,
                         "command_listener_thread",
                         60,            /* 60s watchdog timeout */
                         16*1024*1024,  /* 16 MB stack */
                         60*1000*1000, /* 60s stats interval */
                         NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "command-listener: Could not create command_listener_thread");
        return res;
    }
    return ZPATH_RESULT_NO_ERROR;
}
