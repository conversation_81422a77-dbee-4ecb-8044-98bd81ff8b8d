/*
 * zpath_config_override_keys.h. Copyright (C) 2021-2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPATH_CONFIG_OVERRIDE_KEYS_H_
#define _ZPATH_CONFIG_OVERRIDE_KEYS_H_

/*
 * This file contains all the config keys used in config overirde calls in itasca
 * for which target_type is other than feature_flag in db.
 * If you're using config override in your code, please define the config key here
 * and provide appropriate details.
 */

#include "zthread/zthread.h"
#include "zpath_lib/zpath_config_feature_groups.h"

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Describe the number of dns task that can be executed concurrently in amdin_probe.
 */
#define ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING            "config.feature.admin_probe.task_module.dns.rate_limiting.concurrent_executing"
#define ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT    10
#define LOW_ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING        1
#define HIGH_ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING       20

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Describe the number of ICMP task that
 * can be executed concurrently in amdin_probe
 */
#define ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING           "config.feature.admin_probe.task_module.icmp.rate_limiting.concurrent_executing"
#define ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT   20
#define LOW_ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING       1
#define HIGH_ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING      30

#define ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING            "config.feature.admin_probe.task_module.tcp.rate_limiting.concurrent_executing"
#define ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT     20
#define LOW_ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING         1
#define HIGH_ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING        30

/*describe the number of tcpdump task that can be executed concurrently in admin_probe*/
#define ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING            "config.feature.admin_probe.task_module.tcpdump.rate_limiting.concurrent_executing"
#define ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT	  1
#define LOW_ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING	      0
#define HIGH_ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING	      1

/*describe the number of frr cmds task that can be executed concurrently in admin_probe*/
#define ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING             "config.feature.admin_probe.task_module.bgp_frr.rate_limiting.concurrent_executing"
#define ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT	    10
#define LOW_ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING	        1
#define HIGH_ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING	    20


/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 */
#define ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US                                 "config.feature.admin_probe.uploader.http_timeout_us"
#define ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_DEFAULT_US                         (60*(1000*1000))
#define LOW_ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US                             (60*(1000*1000))
#define HIGH_ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US                            (5*60*(1000*1000))

#define ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US                                     "config.feature.admin_probe.uploader.total_timeout_us"
#define ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_DEFAULT_US                             (5 * 60) * (1000 * 1000)
#define LOW_ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US                                 (5 * 60) * (1000 * 1000)
#define HIGH_ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US                                (10 * 60) * (1000 * 1000)

#define ADMIN_PROBE_UPLOADER_RETRY_COUNT                                          "config.feature.admin_probe.uploader.retry_count"
#define ADMIN_PROBE_UPLOADER_RETRY_COUNT_DEFAULT          	                      1
#define LOW_ADMIN_PROBE_UPLOADER_RETRY_COUNT          	                          0
#define HIGH_ADMIN_PROBE_UPLOADER_RETRY_COUNT          	                          2

/* This is the max file size for tcpdump upload */
#define ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES                                 "config.feature.admin_probe.tcpdump.max_file_size_bytes"
#define ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_DEFAULT_BYTES	                        ((int64_t)1 * 1024 * 1024 * 1024) // 1GB
#define LOW_ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES	                            ((int64_t)1 * 1024)               // 1KB min
#define HIGH_ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES	                        ((int64_t)1 * 1024 * 1024 * 1024) // 1GB max

#define ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES                                "config.feature.admin_probe.tcpdump.max_chunk_size_bytes"
#define ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_DEFAULT_BYTES	                    ((int64_t)100 * 1024 * 1024)        //100 MB for S3 uploads
#define LOW_ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES	                        ((int64_t)1 * 1024 * 1024)          //1 MB for S3 uploads
#define HIGH_ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES	                        ((int64_t)1 * 1024 * 1024 * 1024)   //1 GB for S3 uploads

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * By default, for all new task command other than restart, we validate the creation time of the task.
 * if its with 5min (default), we take the request, otherwise we ignore it.
 *
 * making this configuration just in case as we never know what will happen in production.
 */
#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK             "config.feature.admin_probe.task_module.validation.general.creation_time_s"
#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_DEFAULT_S   5*60
#define LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_S       60
#define HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_S      7*60

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * By default, for new probe command, we validate the creation time of the task.
 * For tcpdump we need it longer and will be having 15 minutes as 1 session can be atleast 5 mins long.
 */

#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK             "config.feature.admin_probe.task_module.validation.tcpdump.creation_time_s"
#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_DEFAULT_S   (15 * 60)
#define LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_S       (6 * 60)
#define HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_S      (20 * 60)


/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * By default, for new restart command, we validate the creation time of the task.
 * if its with 5 seconds (default), we take the request, otherwise we ignore it.
 *
 * making this configuration just in case as we never know what will happen in production.
 * even though 5 seconds works well with dev testing
 */
#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART             "config.feature.admin_probe.task_module.validation.restart.creation_time_s"
#define ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_DEFAULT_S   5
#define LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_S       1
#define HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_S      10

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Number of ICMP probes we want to collect for one probe command request
 */
#define ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES                             "config.feature.admin_probe.task_module.icmp.number_of_probe_per_command"
#define ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES_DEFAULT                     3
#define LOW_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES                         1
#define HIGH_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES                        10

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Expiry of ICMP commands once a ICMP probe request is in processing state
 */
#define ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING                          "config.feature.admin_probe.task_module.icmp.expiry_in_processing_s"
#define ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_DEFAULT_S                5*60
#define LOW_ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_S                    1*60
#define HIGH_ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_S                   8*60

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Number of TCP probes we want to collect for one probe command request
 */
#define ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES                             "config.feature.admin_probe.task_module.tcp.number_of_probe_per_command"
#define ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES_DEFAULT                     3
#define LOW_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES                         1
#define HIGH_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES                        10

/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Expiry of TCP commands once a TCP probe request is in processing state
 */
#define ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING                          "config.feature.admin_probe.task_module.tcp.expiry_in_processing_s"
#define ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_DEFAULT_S                5*60
#define LOW_ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_S                    1*60
#define HIGH_ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_S                   8*60



/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * The status for each task in admin_probe could be PENDING/PROCESSING/CANCEL_REQUEST/TIMEOUT/COMPLETED/CANCELLED/FAILED
 * among all the status mentioned above, we consider TIMEOUT/COMPLETED/CANCELLED/FAILED as termination status.
 *
 * when itasca side accept the task and finish the task, it will move the status of the task to termination status.
 * and this termination status is important such that connector will resend this termination status if no ack is coming back from api side.
 *
 * but we do not want to resend such report too aggressively which we will flood the api services.
 *
 * logic:
 *
 * if we resend first termination report in 60 second (1min) and have default factor == 5
 * when we complete a task and send the complete status report.
 * we will set the next_resend_time_interval = admin_probe_rate_limiting_get_termination_status_first_resend_time_general_s(), which return 60 (1min) by default
 *
 * after 1min, if there is no ack coming back from kafka, we send the termination status report again and update next_resend_time_interval to be 60 * factor (5)
 * which is 300s (5min after)
 *
 * then after 5 mins, if we still not get an ack from kafka, we will send the status report again,
 * and update next next_resend_time_interval = 300 * 5 (factor) = 1500 seconds (25 mins)
 *
 * and then the next resend time supose to be after 25 mins (but the expiry time is 10mins, thus we do not really care about the ack after 10mins and we destroy node anyway.)
 *
 */
#define ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S              "config.feature.admin_probe.task_module.status_report.resend.general.first_resend_s"
#define ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_DEFAULT_S      300
#define LOW_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S          60
#define HIGH_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S         600

#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL            "config.feature.admin_probe.task_module.status_report.resend.general.factor"
#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL_DEFAULT     2
#define LOW_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL               1
#define HIGH_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL              5

#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S           "config.feature.admin_probe.task_module.status_report.resend.general.expiry"
#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_DEFAULT_S   10*60
#define LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S       1*60
#define HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S      10*60

#define ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S              "config.feature.admin_probe.task_module.status_report.resend.restart.first_resend_s"
#define ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_DEFAULT_S      30
#define LOW_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S          5
#define HIGH_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S         30

#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART             "config.feature.admin_probe.task_module.status_report.resend.restart.factor"
#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART_DEFAULT     1
#define LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART         1
#define HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART        2

#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S           "config.feature.admin_probe.task_module.status_report.resend.restart.expiry"
#define ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_DEFAULT_S   5*60
#define LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S       1*60
#define HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S      5*60


/*
 * Feature: Admin Probe Rate Limiting, Affecting Component: Connector
 * Describe the number of task commands that admin_probe can take concurrently(tasks in memory)
 *
 * For example, if there are 10 tasks sitting on inactive queue, 10 tasks sitting on active queue, 10 tasks on reapign queue.
 * we say admin probe is taking 30 tasks concurrently.
 * if the tasks allowed is 30, when there is a new task come in, admin probe will cancel the task as it reaches the limit.
 *
 * This is to protect admin_probe library to be overloaded with too much probe requests.
 */
#define ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE                      "config.feature.admin_probe.task_module.all.max_tasks_allowed_in_queue"
#define ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE_DEFAULT              150
#define LOW_ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE                  1
#define HIGH_ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE                 300

/*
 * Feature: Exporter Conn, Affected Components: Exporter
 */
#define EXPORTER_CONFIG_OVERRIDE_IDEL_TIMEOUT_S                                      "config.feature.exporter.idle_timeout_s"
#define EXPORTER_DEFAULT_CONN_TIMEOUT_S                                              300
#define EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MAX                                  6*60*60    /* safeguard */
#define EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MIN                                  60         /* safeguard */

#define EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED                              "config.feature.exporter.mtunnel.reuse.disabled"
#define DEFAULT_CONFIG_VAL_EXPORTER_MTUNNEL_REUSE_DISABLED                           0
#define EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED_MIN                          0
#define EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED_MAX                          1

/*
 * Feature: Exporter Cors, Affected Components: Exporter
 */
#define EXPORTER_CORS_FEATURE                                                        "config.feature.cors_enabled"
#define DEFAULT_CORS_CAPABILITY_FOR_CUSTOMER                                         0   /* CORS is disabled by default for all customers */
#define DEFAULT_CORS_FOR_DOMAIN                                                      1   /* CORS is enabled by default for all client less applications */

#define EXPORTER_XFF_HEADER_FEATURE                                                  "config.feature.xff_header"
#define DEFAULT_EXPORTER_XFF_HEADER_FEATURE                                          0   /* XFF header is disabled by default for all customers */

#define EXPORTER_FORWARDED_FOR_HEADER_FEATURE                                        "config.feature.forwarded_for"
#define DEFAULT_EXPORTER_FORWARDED_FOR_HEADER_FEATURE                                0   /* Forwarded header is disabled by default for all customers */

#define EXPORTER_CLEAR_COOKIES_FEATURE                                               "config.feature.clear_cookies"
#define DEFAULT_EXPORTER_CLEAR_COOKIES_FEATURE                                       0   /* Clear cookies is disabled by default for all customers */
#define EXPORTER_CLEAR_COOKIES_FEATURE_MIN                                           0
#define EXPORTER_CLEAR_COOKIES_FEATURE_MAX                                           1

#define EXPORTER_PEERIP_INCLUDED                                                     "config.feature.peerip_included"
#define DEFAULT_PEERIP_INCLUDED_FOR_CUSTOMER                                         1   /* Peer IP is included by default for all customers for token generation */
#define EXPORTER_PEERIP_INCLUDED_MIN                                                 0
#define EXPORTER_PEERIP_INCLUDED_MAX                                                 1

#define EXPORTER_SAMESITE_NONE                                                       "config.feature.samesite_none"
#define DEFAULT_SAMESITE_NONE_FOR_CUSTOMER                                           0   /* Application domain cookie will be Lax for all customers */
#define EXPORTER_SAMESITE_NONE_MIN                                                   0
#define EXPORTER_SAMESITE_NONE_MAX                                                   1

#define CONFIG_BROKER_CATEGORY                          "Broker config overrides"
/*
 * Feature: Dtls, Affected Components: Broker, PB, Connector
 */
#define DTLS_FEATURE                                                                 "config.feature.dtls"
#define DEFAULT_BROKER_DTLS                                                          0
#define DEFAULT_ASST_DTLS                                                            0
#define DEFAULT_PBROKER_DTLS                                                         0

#define DTLS_FEATURE_MIN                                                             0
#define DTLS_FEATURE_MAX                                                             1

#define DTLS_FEATURE_CLIENT                                                          "config.feature.dtls-client"
#define DEFAULT_ASST_DTLS_CLIENT                                                     0
#define DEFAULT_PBROKER_DTLS_CLIENT                                                  0
#define DEFAULT_BROKER_DTLS_CLIENT                                                   0

#define DTLS_FEATURE_CLIENT_MIN                                                      0
#define DTLS_FEATURE_CLIENT_MAX                                                      1

#define DTLS_FEATURE_MTU                   "config.feature.dtls-mtu"
#define DTLS_CLIENT_MTU                    "config.feature.dtls-client-mtu"

/*
 * Feature: Quickack, Affected Components: Broker, Connector
 * This config enables the socket setting for quickack in accept/listen and connect of the socket
 * to force the delayed acknowledgements to stop.
 */
#define QUICKACK_CONFIG_CLIENT                                                       "config.feature.quickack-client"
#define DEFAULT_BROKER_QUICKACK                                                      0

#define QUICKACK_CONFIG_ASSISTANT                                                    "config.feature.quickack-assistant"
#define DEFAULT_ASST_QUICKACK                                                        0

/*
 * Feature: Quickack, Affected Components: Broker, Connector
 * This config enables the socket setting for quickack at read callback to force the delayed acknowledgements to stop.
 * Adding at read callback was needed as the kernel can override any TCP_QUICKACK setting in certain conditions.
 * This will add an overhead at every read callback and has to be enabled if really needed for the customer.
 */
#define QUICKACK_READ_CONFIG_CLIENT                                                  "config.feature.quickack-read-client"
#define DEFAULT_BROKER_QUICKACK_READ                                                 0

#define QUICKACK_READ_CONFIG_ASSISTANT                                               "config.feature.quickack-read-assistant"
#define DEFAULT_ASST_QUICKACK_READ                                                   0

/*
 * Feature: Multi DC Redirect, Affected Components: Broker, Connector
 * This config enables MULTIDC  support for connector
 */

#define MULTIDC_REDIRECT_VAL_FOR_ASSISTANT                                           "config.feature.balance.mdc_redir.assistant"
#define MULTIDC_REDIRECT_MAX_VAL_FOR_ASSISTANT                                       1
#define MULTIDC_REDIRECT_MIN_VAL_FOR_ASSISTANT                                       0
#define DEFAULT_MULTIDC_REDIRECT_VAL_FOR_ASSISTANT                                   0
/*
 * Feature: Multi DC Redirect, Affected Components: Broker, PSE
 * This config enables MULTIDC  support for PSE
 */
#define MULTIDC_REDIRECT_VAL_FOR_PSE                                                 "config.feature.balance.mdc_redir.pse"
#define MULTIDC_REDIRECT_MAX_VAL_FOR_PSE                                             1
#define MULTIDC_REDIRECT_MIN_VAL_FOR_PSE                                             0
#define DEFAULT_MULTIDC_REDIRECT_VAL_FOR_PSE                                         0

/*
 * Feature: Multi DC Redirect, Affected Components: Broker, SiteC
 * This config enables MULTIDC  support for SiteC
 */
#define MULTIDC_REDIRECT_VAL_FOR_SITEC                                                 "config.feature.balance.mdc_redir.sitec"
#define MULTIDC_REDIRECT_MAX_VAL_FOR_SITEC                                             1
#define MULTIDC_REDIRECT_MIN_VAL_FOR_SITEC                                             0
#define DEFAULT_MULTIDC_REDIRECT_VAL_FOR_SITEC                                         0

/*
 * This config enables the socket setting for quickack at accept and read callback
 * to force the TCP delayed acknowledgements to stop.
 * This config is only between server and connector.
 * This will add an overhead at every read callback and has to be enabled if really needed for the customer.
 */
#define ASSISTANT_QUICKACK_APP_CONFIG                                                "config.feature.quickack-app"
#define DEFAULT_ASST_QUICKACK_APP                                                    0

#define SARGE_MINIMUM_VERSION                                                        "config.feature.sarge_minimum_version"
#define DEFAULT_SARGE_MINIMUM_VERSION                                                NULL

/*
 * This config gives the maximum time the connection between assistant and server
 * can be stuck before closing the mtunnel
 */
#define ASSISTANT_MAX_PAUSE_TIME_CONFIG                                                "config.feature.assistant-max-pause-time"
#define DEFAULT_ASST_MAX_PAUSE_TIME                                                    (2*60) //2 mins
#define HIGH_ASST_MAX_PAUSE_TIME                                                       (5*60) //5 mins
#define LOW_ASST_MAX_PAUSE_TIME                                                        (1*60) //1 mins

/*
 * Feature: Connector stats upload to zi, Affected Component: Broker
 * This config enables broker to upload connector stats to zi endpoint
 */
#define ASST_ZISTATS_UPLOAD_CONFIG                                                   "config.feature.asst_zistats_upload_disable"
#define DEFAULT_ASST_ZISTATS_UPLOAD_DISABLED                                         0

/*
 * Feature: Private broker stats upload to zi, Affected Component: Broker
 * This config enables broker to upload private broker stats to zi endpoint
 */
#define PBROKER_ZISTATS_UPLOAD_CONFIG                                                "config.feature.pbroker_zistats_upload_disable"
#define DEFAULT_PBROKER_ZISTATS_UPLOAD_DISABLED                                      0

/*
 * Feature: Site controller stats upload to zi, Affected Component: Broker
 * This config enables broker to upload site contoller stats to zi endpoint
 */
#define SITEC_ZISTATS_UPLOAD_CONFIG                                                  "config.feature.sitec_zistats_upload_disable"
#define DEFAULT_SITEC_ZISTATS_UPLOAD_DISABLED                                        0

/*
 * Feature: Broker Client, Affected Components: Broker, PB
 */
#define ZPN_BROKER_DNS_CLIENT_CHECK_MAX_TTL_FEATURE                                  "config.feature.broker_dns_client_check_max_ttl"
#define ZPN_BROKER_DNS_CLIENT_CHECK_DEFAULT_MAX_TTL_S                                30
#define ZPN_BROKER_DNS_CLIENT_CHECK_TTL_MIN_S                                        0
#define ZPN_BROKER_DNS_CLIENT_CHECK_TTL_MAX_S                                        86400 // 1 day

#define BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC                                         "config.vdi_client_status_timeout_sec"
#define BROKER_VDI_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC                                  43200   // default connection expiry, 12hrs, let Ops configure the 15 mins timeout through config
#define BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC_MIN                                      300   // min 5 mins
#define BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC_MAX                                      86400  // max 24 hrs


#define BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC                                         "config.sipa_client_status_timeout_sec"
#define BROKER_SIPA_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC                                  900   // default connection expiry is set the global value i.e. 15 mins
#define BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC_MIN                                      300   // min 5 mins
#define BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC_MAX                                      86400  // max 24 hrs

#define BROKER_CLIENT_STATUS_TIMEOUT_SEC                                             "config.client_status_timeout_sec"
#define BROKER_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC                                     43200  // default connection expiry, 12 hrs. It was determined from Production experience. The inital 300s was way to short in some cases.
#define BROKER_CLIENT_STATUS_TIMEOUT_SEC_MIN                                         300    // min 5 mins
#define BROKER_CLIENT_STATUS_TIMEOUT_SEC_MAX                                         86400  // max 24 hrs

#define BROKER_CLIENT_ENABLE_CLEANUP_STALE                                           "config.client_enable_cleanup_stale"
#define DEFAULT_BROKER_CLIENT_ENABLE_CLEANUP_STALE                                   0  // turn on until feature is mature
#define BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MIN                                   0
#define BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MAX                                   1

#define CONFIG_APP_SCALING_CATEGORY                                                  "App Scaling flag"
#define APP_SCALING_FEATURE                                                          "config.feature.app_scaling"
#define DEFAULT_APP_SCALING_FEATURE                                                  0
#define APP_SCALING_FEATURE_VAL_MIN                                                  0
#define APP_SCALING_FEATURE_VAL_MAX                                                  1

#define ANDROID_APP_SCALING_FEATURE                                                 "config.feature.app_scaling.android"
#define DEFAULT_ANDROID_APP_SCALING_FEATURE                                         1

#ifdef SIPA_APP_SCALE_CODE_ENABLED
#define SIPA_APP_SCALING_FEATURE                                                    "config.feature.app_scaling.sipa"
#define DEFAULT_SIPA_APP_SCALING_FEATURE                                            0
#endif

#define BROKER_CC_APP_SCALING_FEATURE                                               "config.feature.app_scaling.cc.broker"
#define DEFAULT_BROKER_CC_APP_SCALING_FEATURE                                       0
#define PSE_CC_APP_SCALING_FEATURE                                                  "config.feature.app_scaling.cc.pse"
#define DEFAULT_PSE_CC_APP_SCALING_FEATURE                                          0

#define BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE                                  "config.feature.app_scaling.cc.broker.hard_disabled"
#define DEFAULT_BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE                          0
#define PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE                                     "config.feature.app_scaling.cc.pse.hard_disabled"
#define DEFAULT_PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE                             0

#define BROKER_VDI_APP_SCALING_FEATURE                                               "config.feature.app_scaling.vdi.broker"
#define DEFAULT_BROKER_VDI_APP_SCALING_FEATURE                                       0
#define PSE_VDI_APP_SCALING_FEATURE                                                  "config.feature.app_scaling.vdi.pse"
#define DEFAULT_PSE_VDI_APP_SCALING_FEATURE                                          0

#define BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE                                 "config.feature.app_scaling.vdi.broker.hard_disabled"
#define DEFAULT_BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE                          0
#define PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE                                    "config.feature.app_scaling.vdi.pse.hard_disabled"
#define DEFAULT_PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE                             0

/*
 * Feature: App scaling bypass improvement
 * Option to enable/disable App scaling bypass improvement feature
 */
#define APP_SCALING_BYPASS_IMPROVEMENT_FEATURE                                       "config.feature.app_scaling_bypass_improvement"
#define DEFAULT_APP_SCALING_BYPASS_IMPROVEMENT_FEATURE                               0
#define APP_SCALING_BYPASS_IMPROVEMENT_FEATURE_VAL_MIN                               0
#define APP_SCALING_BYPASS_IMPROVEMENT_FEATURE_VAL_MAX                               1

/* Global cloudwise App scaling bypass improvement feature hard disabled flag: 1 = hard disabled, 0 = not hard disabled */
#define APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED                                 "config.feature.app_scaling_bypass_improvement.hard_disabled"
#define APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED_MIN                             0
#define APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED_MAX                             1
#define DEFAULT_APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED                         0

/* App Pagination keys */
#define AGGREGATE_DOMAIN_UPDATE_PAGINATION_HARD_DISABLED                             "config.feature.app_pagination.aggregate_domains.hard_disabled"
#define AGGREGATE_DOMAIN_UPDATE_PAGINATION_BROKER_ENABLED                            "config.feature.broker.app_pagination.aggregate_domains.enabled"
#define AGGREGATE_DOMAIN_UPDATE_PAGINATION_PSE_ENABLED                               "config.feature.pse.app_pagination.aggregate_domains.enabled"

/*
 * Feature : User risk feature
 * For broker: There are two separate feature flags
 *      At customer level: config.feature.broker.user_risk.enabled (Always monitored)
 *      At global level: config.feature.global.broker.user_risk.enabled (Read only at broker startup time)
 * Order of priority: Customer, global, default
 * If customer level flag is not set, then global level flag value is considered for each customer.
 * To distinguish if customer level flag is not set, default value of -1 is used
 *
 * For PSE: There is one feature flag
 * config.feature.pse.user_risk.enabled: Always monitored and used for customer and global level
 *
 */
#define CONFIG_USER_RISK_CATEGORY                                                    "User risk"
#define PSE_USER_RISK_FEATURE_VAL_MIN                                                0
#define PSE_USER_RISK_FEATURE_VAL_MAX                                                1
#define PSE_USER_RISK_FEATURE_ENABLED                                                "config.feature.pse.user_risk.enabled"
#define DEFAULT_PSE_USER_RISK_FEATURE                                                0

#define BROKER_CUSTOMER_USER_RISK_FEATURE_ENABLED                                    "config.feature.broker.user_risk.enabled"
#define DEFAULT_BROKER_CUSTOMER_USER_RISK_FEATURE                                    -1
#define BROKER_CUST_USER_RISK_FEATURE_VAL_MIN                                        -1
#define BROKER_CUST_USER_RISK_FEATURE_VAL_MAX                                        1

#define BROKER_GLOBAL_USER_RISK_FEATURE_ENABLED                                      "config.feature.global.broker.user_risk.enabled"
#define DEFAULT_BROKER_GLOBAL_USER_RISK_FEATURE                                      0
#define BROKER_GLOBAL_USER_RISK_FEATURE_VAL_MIN                                      0
#define BROKER_GLOBAL_USER_RISK_FEATURE_VAL_MAX                                      1

#define USER_RISK_REGISTRATION_ENABLED                                               "config.feature.user_risk_registration.enabled"
#define DEFAULT_USER_RISK_REGISTRATION_ENABLED                                       1
#define USER_RISK_REGISTRATION_ENABLED_VAL_MIN                                       0
#define USER_RISK_REGISTRATION_ENABLED_VAL_MAX                                       1

#define USER_RISK_FEATURE_HARD_DISABLED                                              "config.feature.user_risk.hard_disabled"
#define DEFAULT_USER_RISK_FEATURE_HARD_DISABLED                                      0
#define USER_RISK_FEATURE_HARD_DISABLED_VAL_MIN                                      0
#define USER_RISK_FEATURE_HARD_DISABLED_VAL_MAX                                      1

/*
 * Feature : to restrict the application download
 * for embedded devices like iOS and android
 */
#define CONFIG_REST_APP_DOWNLOAD_CATEGORY                                            "Restrict App download for embedded devices"
#define RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE                               "config.feature.restrict_app_download_embedded_devices"
#define DEFAULT_RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE                       0
#define RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE_VAL_MIN                       0
#define RESTRICT_APP_DOWNLOAD_EMBEDDED_DEVICES_FEATURE_VAL_MAX                       1

#define ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_IOS                                       "config.feature.max_app_download_ios"
#define ZPN_BROKER_CONFIG_MAX_APP_DOWNLOAD_ANDROID                                   "config.feature.max_app_download_android"
#define DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_DEVICE                                     5000
#define REST_APP_DOWNLOAD_EMBEDDED_VAL_MIN                                           1
#define REST_APP_DOWNLOAD_EMBEDDED_VAL_MAX                                           5000

#define APP_DOWNLOAD_KEEP_ALIVE_FEATURE             "config.feature.app_download_keep_alive"
#define DEFAULT_APP_DOWNLOAD_KEEP_ALIVE_FEATURE         0
#define DEFAULT_APP_DOWNLOAD_KEEP_ALIVE_FEATURE_MIN     0
#define DEFAULT_APP_DOWNLOAD_KEEP_ALIVE_FEATURE_MAX     1

/*
 * Feature: Broker Client Rate Limiting, Affected Components: Broker, PB
 * RATE_LIMIT_MAX_MT_REQ_RATE: max num of mtunnel request per second
 * RATE_LIMIT_CC_MAX_MT_REQ_RATE: max num of mtunnel request per second from
 *      loop sensitive clients (currently only edge connector)
 * RATE_LIMIT_MAX_NEW_APP_RATE: max num of mtunnel requests for new domains per second
 * Bucket sizes are used to ensure we can handle spikes
 * refer to zpn_rate_limit.h for more details
 */
#define CONFIG_RATE_LIMIT_CATEGORY                  "ZPA Broker Mtunnel Rate Limiting"
#define RATE_LIMIT_STATUS_FEATURE                   "config.feature.rate_limiting"
#define CC_RATE_LIMIT_STATUS_FEATURE                "config.feature.rate_limiting_cc"
#define DEFAULT_RATE_LIMIT_STATUS	                1
#define RATE_LIMIT_STATUS_MIN	                    0
#define RATE_LIMIT_STATUS_MAX	                    1

#define RATE_LIMIT_MAX_MT_REQ_RATE                  "config.feature.rate_limiting_app_rate"
#define DEFAULT_MAX_MT_REQ_RATE	                    100
#define MAX_MT_REQ_RATE_MIN_VAL                     1
#define MAX_MT_REQ_RATE_MAX_VAL	                    1024

#define RATE_LIMIT_MT_REQ_BUCKET_SIZE               "config.feature.rate_limiting_app_bucket_size"
#define DEFAULT_MT_REQ_BUCKET_SIZE	                1000
#define RATE_LIMIT_MT_REQ_BUCKET_SIZE_MIN_VAL       10
#define RATE_LIMIT_MT_REQ_BUCKET_SIZE_MAX_VAL       1024*1024

#define RATE_LIMIT_CC_MAX_MT_REQ_RATE               "config.feature.rate_limiting_cc_app_rate"
#define DEFAULT_CC_MAX_MT_REQ_RATE	                200
#define CC_MAX_MT_REQ_RATE_MIN_VAL                  1
#define CC_MAX_MT_REQ_RATE_MAX_VAL	                1024

#define RATE_LIMIT_CC_MT_REQ_BUCKET_SIZE            "config.feature.rate_limiting_cc_app_bucket_size"
#define DEFAULT_CC_MT_REQ_BUCKET_SIZE	            2000
#define CC_MT_REQ_BUCKET_SIZE_MIN_VAL               10
#define CC_MT_REQ_BUCKET_SIZE_MAX_VAL               1024*1024

#define RATE_LIMIT_MAX_NEW_APP_RATE                 "config.feature.rate_limiting_num_app_rate"
#define DEFAULT_MAX_NEW_APP_RATE	                100
#define MAX_NEW_APP_RATE_MIN_VAL                    1
#define MAX_NEW_APP_RATE_MAX_VAL                    1024

#define RATE_LIMIT_NEW_APP_BUCKET_SIZE              "config.feature.rate_limiting_num_app_bucket_size"
#define DEFAULT_NEW_APP_BUCKET_SIZE	                1000
#define NEW_APP_BUCKET_SIZE_MIN_VAL                 10
#define NEW_APP_BUCKET_SIZE_MAX_VAL                 1024*1024

/*
 * Feature: WAF, Affected Components: Broker, Connector
 */
#define WAF_FEATURE                                                                  "config.feature.waf"
#define WAF_ENABLED                                                                  0 /* Default WAF behavior is off */

#define WAF_FEATURE_GLOBAL_DISABLE                                                   "config.feature.waf_global_disable"
#define WAF_GLOBAL_DISABLE                                                           0

#define WAF_THREATLABZ_FEATURE                                                      "config.feature.waf.threatlabz"
#define WAF_THREATLABZ_ENABLED                                                       0 /* Default WAF Threatlabz behavior is off */

#define WAF_THREATLABZ_FEATURE_GLOBAL_DISABLE                                        "config.feature.waf.threatlabz_global_disable"
#define WAF_THREATLABZ_GLOBAL_DISABLE                                                0

#define WAF_EXCEPTIONS_FEATURE                                                      "config.feature.waf.exceptions"
#define WAF_EXCEPTIONS_ENABLED                                                       0 /* Default WAF Exceptions behavior is off */

#define WAF_EXCEPTIONS_FEATURE_GLOBAL_DISABLE                                        "config.feature.waf.exceptions_global_disable"
#define WAF_EXCEPTIONS_GLOBAL_DISABLE                                                0

/* JSON Parsing */
#define WAF_JSONPARSING_FEATURE                                                      "config.feature.waf.jsonparsing"
#define WAF_JSONPARSING_ENABLED                                                       0 /* Default WAF modsec json parsing is off */

#define WAF_JSONPARSING_FEATURE_GLOBAL_DISABLE                                        "config.feature.waf.jsonparsing_global_disable"
#define WAF_JSONPARSING_GLOBAL_DISABLE                                                0

/* XML Parsing */
#define WAF_XMLPARSING_FEATURE                                                      "config.feature.waf.xmlparsing"
#define WAF_XMLPARSING_ENABLED                                                       0 /* Default WAF modsec XML parsing is off */

#define WAF_XMLPARSING_FEATURE_GLOBAL_DISABLE                                        "config.feature.waf.xmlparsing_global_disable"
#define WAF_XMLPARSING_GLOBAL_DISABLE                                                0

#define WAF_ACCESS_MODE_FEATURE                                                      "config.feature.waf.strict_mode_access"
#define WAF_ACCESS_STRICT_MODE                                                       0 /* Default allow access if inspection not ready */

#define WAF_MAX_REQUEST_LIMIT_FEATURE                                                "config.feature.waf.max_request_limit"
#define WAF_MAX_REQUEST_LIMIT                                                        10*1024*1024
#define WAF_MIN_REQUEST_LIMIT                                                        1024
#define WAF_DFLT_REQUEST_LIMIT                                                       1.5*1024*1024

#define WAF_MAX_RESPONSE_LIMIT_FEATURE                                               "config.feature.waf.max_response_limit"
#define WAF_MAX_RESPONSE_LIMIT                                                       10*1024*1024
#define WAF_MIN_RESPONSE_LIMIT                                                       1024
#define WAF_DFLT_RESPONSE_LIMIT                                                      1.5*1024*1024

#define WAF_MAX_HEADER_SIZE_LIMIT_FEATURE                                            "config.feature.waf.max_http_size_limit"
#define WAF_MAX_HEADER_SIZE_LIMIT                                                    128*1024
#define WAF_MIN_HEADER_SIZE_LIMIT                                                    32*1024
#define WAF_DFLT_HEADER_SIZE_LIMIT                                                   32*1024

#define WAF_INSPECTION_TIMEOUT_FEATURE                                               "config.feature.waf.inspection_timeout"
#define WAF_MIN_INSPECTION_TIMEOUT                                                   20 /* 20 seconds */
#define WAF_MAX_INSPECTION_TIMEOUT                                                   360 /* 1 hr */
#define WAF_DFLT_INSPECTION_TIMEOUT                                                  30 /* default : 30 seconds */

#define WAF_MAX_REQ_PAYLOAD_LIMIT_FEATURE                                           "config.feature.waf.max_req_payload_limit"
#define WAF_MAX_RSP_PAYLOAD_LIMIT_FEATURE                                           "config.feature.waf.max_rsp_payload_limit"
#define WAF_MAX_PAYLOAD_LIMIT                                                        10*1024*1024
#define WAF_MIN_PAYLOAD_LIMIT                                                        0 /* disables payload inspection */
#define WAF_DFLT_PAYLOAD_LIMIT                                                       32*1024

#define WAF_PROF_BUILD_DELAY_INTERVAL                                               "config.feature.waf.profile_build_delay_interval"
#define WAF_PROF_BUILD_DELAY_INTERVAL_DEF_VAL                                        15  /* Default profile build delay interval (in seconds) */
#define WAF_PROF_BUILD_DELAY_INTERVAL_MIN_VAL                                        5   /* Minimum allowed profile build delay interval (in seconds) */
#define WAF_PROF_BUILD_DELAY_INTERVAL_MAX_VAL                                        600 /* Maximum allowed profile build delay interval (in seconds) */

#define ASST_WAF_DEBUG_FILTER_CONFIG                                                "config.feature.waf.debug_filter"
#define ASST_WAF_DEBUG_FILTER_DEF                                                   NULL

/*  Feature: Auto App Protection and Auto Inspection Certificate Generation */
#define AUTO_APP_PROTECTION_FEATURE                                                 "config.feature.auto_app_protect"
#define AUTO_APP_PROTECTION_ENABLED                                                  0 /* Default Auto APP protection is off */

#define AUTO_APP_PROTECTION_FEATURE_GLOBAL_DISABLE                                   "config.feature.auto_app_protect.global_disable"
#define AUTO_APP_PROTECTION_GLOBAL_DISABLE                                           0

#define AUTO_APP_CERT_GEN_FEATURE_GLOBAL_DISABLE                                     "config.feature.auto_cert_gen.global_disable"
#define AUTO_APP_CERT_GEN_GLOBAL_DISABLE                                             0

/* Feature: ADP  */
#define AD_PROTECTION_FEATURE                                                        "config.feature.adp"
#define AD_PROTECTION_ENABLED                                                         0 /* Default AD protection is off */

#define AD_PROTECTION_FEATURE_GLOBAL_DISABLE                                         "config.feature.adp.global_disable"
#define AD_PROTECTION_GLOBAL_DISABLE                                                 0

#define AD_PROTOCOL_OVERRIDE_CONFIG                                                  "config.feature.adp.protocol_override"
#define AD_PROTOCOL_OVERRIDE_DEFAULT                                                 "\"{\"ad_proto_bits\":{\"dcerpc_bit\":0,\"ldap_bit\":0,\"smb_bit\":0,\"krb_bit\":0}}\"\n"
#define AD_PROTOCOL_OVERRIDE_BITMASK                                                 0x0000

/* DP Expectations wrt Pipeline
 * WAF_FEATURE or AUTO_APP_PROTECTION_FEATURE (http/s) - http/s only inspection
 * API_PROTECTION_FEATURE - api detection + http with/without api inspection based on API controls applied + waf log integrated with api inspection
 * API_DISCOVERY_FEATURE - flag set for api discovery in waf log
 */

/* API inspection enable/disable in WAF */
#define API_PROTECTION_FEATURE                                                        "config.feature.waf.api_protection"
#define API_PROTECTION_ENABLED                                                         0 /* Default API inspection is off */

/* API Discovery enable/disable in WAF */
#define API_DISCOVERY_FEATURE                                                        "config.feature.waf.api_discovery"
#define API_DISCOVERY_ENABLED                                                         1
#define API_DISCOVERY_DISABLED                                                        0 /* Default API Discovery is off */

#define API_PROTECTION_FEATURE_GLOBAL_DISABLE                                         "config.feature.waf.api_protection.global_disable"
#define API_PROTECTION_GLOBAL_DISABLE                                                 0 /* ENABLED by default */

#define API_DISCOVERY_FEATURE_GLOBAL_DISABLE                                          "config.feature.waf.api_discovery.global_disable"
#define API_DISCOVERY_GLOBAL_DISABLE                                                  0 /* ENABLED by default */
/*
 * Feature: Protocol Discovery & Tagging, Affected Components: Connector
 */
#define WAF_PROTOCOL_TAGGING_FEATURE                                                 "config.feature.waf.ptag"
#define WAF_PROTOCOL_TAGGING_ENABLED                                                 0 /* Default behavious is off and controlled by UI */

#define WAF_PROTOCOL_TAGGING_FEATURE_GLOBAL_DISABLE                                  "config.feature.waf.ptag_global_disable"
#define WAF_PROTOCOL_TAGGING_GLOBAL_DISABLE                                          0

/*
 *  Feature: ET-52700 IDPS and HTTP Inspection Pipeline Integration and PDP Enhancement
 *  Components: Connector
 */
#define PDP_PROFILE_PURGE_INTERVAL                                                  "config.feature.pdp.profile_purge_interval"
#define PDP_PROFILE_PURGE_INTERVAL_DEFAULT                                          24*60*60 /* 24 hours */
#define PDP_PROFILE_PURGE_INTERVAL_MIN                                              60*60 /* 1 hour */
#define PDP_PROFILE_PURGE_INTERVAL_MAX                                              14*24*60*60 /* 14 days */

#define PDP_PROFILE_AGING_TIMEOUT                                                   "config.feature.pdp.profile_aging_timeout"
#define PDP_PROFILE_AGING_TIMEOUT_DEFAULT                                           24*60*60 /* 24 hours */
#define PDP_PROFILE_AGING_TIMEOUT_MIN                                               60*60 /* 1 hour */
#define PDP_PROFILE_AGING_TIMEOUT_MAX                                               14*24*60*60 /* 14 days */

#define PDP_PROFILE_AGING                                                           "config.feature.pdp.profile_aging"
#define PDP_PROFILE_AGING_ENABLED                                                   1 /* Default behavious is enabled and controlled by UI */

/*
 * Affected Components: Connector
 * Feature flag: "config.feature.connector.waf.set_reason_payload_status"
 * Enable connector support for sending reason payload for traffic denied during inspection.
 */

#define WAF_ENABLE_REASON_PAYLOAD_STATUS                                            "config.feature.connector.waf.set_reason_payload_status"
#define WAF_DEFAULT_REASON_PAYLOAD_STATUS                                           0


#define WAF_CFG_OVD_FEATURE_ENABLE_CATEGORY                                    "WAF feature enable/disable"
#define WAF_CFG_OVD_SET_VALUE_CATEGORY                                         "WAF set values for parameters"



/*
 * Feature: C2C, Affected Components: Broker
 */
#define C2C_FEATURE                                                                  "config.feature.c2c"
#define C2C_DEFAULT_BROKER_FEATURE                                                   1

#define C2C_FEATURE_CUSTOMER                                                         "config.feature.c2c.customer"
#define C2C_DEFAULT_CUSTOMER_FEATURE                                                 1

#define C2C_MULTIPLE_REGISTRATION                                                    "config.feature.c2c.multi_regs_protection"
#define C2C_DEFAULT_MULTIPLE_REGISTRATION                                            1
/*
 * timer and expiration values in seconds for c2c registration re-sending
 * defaults are specified here
 * config overide can change them
 */
#define C2C_REG_RESEND_SEC                                                           "config.c2c.reg.renew_sec"
#define C2C_REG_RESEND_DEFAULT_TIMER_SEC                                             300  // default 5 min

#define C2C_REG_EXPIRY_SEC                                                           "config.c2c.reg.expiry_sec"
#define C2C_REG_DEFAULT_EXPIRY_TTL_SEC                                               3600   // default registration expiry, 1 hr

/*
 * Feature: S2C, Affected Components: Broker
 */
#define C2C_IP_HARD_DISABLED                                                         "config.feature.c2c_ip.hard_disabled"
#define C2C_IP_HARD_DISABLED_DEFAULT                                                 1  // default enable

#define C2C_IP_FEATURE_CUSTOMER                                                      "config.feature.c2c_ip.customer"
#define C2C_IP_DEFAULT_CUSTOMER_FEATURE                                              -1
#define C2C_IP_CUSTOMER_FEATURE_VAL_MIN                                              -1
#define C2C_IP_CUSTOMER_FEATURE_VAL_MAX                                              1
#define C2C_IP_CUSTOMER_FEATURE_DISABLED                                             0
#define C2C_IP_CUSTOMER_FEATURE_ENABLED                                              1
#define C2C_IP_CUSTOMER_FEATURE_NOT_IN_DB                                            -1

#define C2C_IP_RETRY_MINUTES                                                         "config.feature.c2c_ip.retry_minutes"
#define C2C_IP_RETRY_MINUTES_DEFAULT                                                 5  // default 5 min
#define C2C_IP_RETRY_MINUTES_MIN                                                     1
#define C2C_IP_RETRY_MINUTES_MAX                                                     60
#define C2C_IP_RETRY_MINUTES_DEFAULT                                                 5  // default 5 min

#define C2C_BYPASS_LOCAL_DISPATCH_PROMOTE                                            "config.feature.c2c.pse.local_dispatch_bypass.promote"
#define C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_MIN                                        0
#define C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_MAX                                        1
#define C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_DEFAULT                                    0

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2                                             "config.feature.c2c.local_dispatch_bypass.phase2.enable"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_MIN                                         0
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_MAX                                         1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_DEFAULT                                     0

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY                   "config.feature.c2c.local_dispatch_bypass.phase2.pse.timeout_route_locally.enable"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_MIN               0
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_MAX               1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_DEFAULT           1

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS                             "config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_chains"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_MIN                         1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_MAX                         50
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_DEFAULT                     10

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS                           "config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_buckets"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_MIN                       2
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_MAX                       4096
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_DEFAULT                   2048

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S                                "config.feature.c2c.local_dispatch_bypass.phase2.pse.expiry_s"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_MIN                            60
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_MAX                            3600
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_DEFAULT                        300

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S                           "config.feature.c2c.local_dispatch_bypass.phase2.pse.check_timeout_s"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_MIN                        1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_MAX                        60
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_DEFAULT                    8

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS                        "config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_buckets"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_MIN                    2
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_MAX                    16384
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_DEFAULT                8192

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS                         "config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_chains"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_MIN                     1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_MAX                     50
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_DEFAULT                 13

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S                             "config.feature.c2c.local_dispatch_bypass.phase2.broker.expiry_s"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_MIN                         60
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_MAX                         3600
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_DEFAULT                     300

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S                        "config.feature.c2c.local_dispatch_bypass.phase2.broker.check_timeout_s"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_MIN                    1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_MAX                    60
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_DEFAULT                3

#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED                               "config.feature.c2c.local_dispatch_bypass.phase2.hard_disabled"
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_MIN                           0
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_MAX                           1
#define C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_DEFAULT                       0

#define BROKER_IPARS_PENDING_QUEUE_SIZE                                              "config.broker.ipars_pending_queue_size"
#define BROKER_IPARS_DEFAULT_PENDING_QUEUE_SIZE                                      10000  // default 10000
#define BROKER_IPARS_PENDING_QUEUE_SIZE_MIN                                          1000
#define BROKER_IPARS_PENDING_QUEUE_SIZE_MAX                                          100000

/*
 * Feature: S2C, Affected Components: IPARS
 */
#define IPARS_RESERVATION_EXPIRY_HOURS                                               "config.ipars.reservation_expiry_hours"
#define IPARS_DEFAULT_RESERVATION_EXPIRY_HOURS                                       8760 // default 365 days
#define IPARS_RESERVATION_EXPIRY_HOURS_MIN                                           1
#define IPARS_RESERVATION_EXPIRY_HOURS_MAX                                           17520

#define IPARS_SOFT_RESERVATION_DAYS                                                  "config.ipars.soft_reservation_days"
#define IPARS_DEFAULT_SOFT_RESERVATION_DAYS                                          7  // default 1 week
#define IPARS_SOFT_RESERVATION_DAYS_MIN                                              1
#define IPARS_SOFT_RESERVATION_DAYS_MAX                                              365

#define IPARS_EXPIRY_MONITOR_INTERVAL_SEC                                            "config.ipars.expiry_monitor_interval_sec"
#define IPARS_DEFAULT_EXPIRY_MONITOR_INTERVAL_SEC                                    60 // default 1 min
#define IPARS_EXPIRY_MONITOR_INTERVAL_SEC_MIN                                        10
#define IPARS_EXPIRY_MONITOR_INTERVAL_SEC_MAX                                        600

#define IPARS_EXPIRY_GRACEFUL_HOURS                                                  "config.ipars.expiry_graceful_hours"
#define IPARS_DEFAULT_EXPIRY_GRACEFUL_HOURS                                          3 // default 3 hours
#define IPARS_EXPIRY_GRACEFUL_HOURS_MIN                                              1
#define IPARS_EXPIRY_GRACEFUL_HOURS_MAX                                              24

#define IPARS_REFRESH_RATE_LIMIT_RPS                                                 "config.ipars.refresh_rate_limit_rps"
#define IPARS_DEFAULT_REFRESH_RATE_LIMIT_RPS                                         10 // default 10 rps
#define IPARS_REFRESH_RATE_LIMIT_RPS_MIN                                             1
#define IPARS_REFRESH_RATE_LIMIT_RPS_MAX                                             60

#define IPARS_PG_TIMEOUT_S                                                           "config.ipars.pg_timeout_s"
#define IPARS_DEFAULT_PG_TIMEOUT_S                                                   60 // default 60 sec
#define IPARS_PG_TIMEOUT_S_MIN                                                       1
#define IPARS_PG_TIMEOUT_S_MAX                                                       120

#define IPARS_PG_CONNECTION_TIMEOUT_S                                                "config.ipars.pg_connection_timeout"
#define IPARS_DEFAULT_PG_CONNECTION_TIMEOUT_S                                        15 // default 15 sec
#define IPARS_PG_CONNECTION_TIMEOUT_S_MIN                                            1
#define IPARS_PG_CONNECTION_TIMEOUT_S_MAX                                            20

#define IPARS_PG_CONN_POOL_MIN_CONNS                                                 "config.ipars.pg_conn_pool.min_conns"
#define IPARS_PG_CONN_POOL_MIN_CONNS_DEFAULT                                         10 // default 10 connections
#define IPARS_PG_CONN_POOL_MIN_CONNS_MIN                                             1
#define IPARS_PG_CONN_POOL_MIN_CONNS_MAX                                             50

#define IPARS_PG_CONN_POOL_MAX_CONNS                                                 "config.ipars.pg_conn_pool.max_conns"
#define IPARS_PG_CONN_POOL_MAX_CONNS_DEFAULT                                         200 // default 200 connections
#define IPARS_PG_CONN_POOL_MAX_CONNS_MIN                                             50
#define IPARS_PG_CONN_POOL_MAX_CONNS_MAX                                             1000

#define IPARS_PG_CONN_POOL_AUTO_DOWNSCALE_RATE                                       "config.ipars.pg_conn_pool.auto_downscale_rate"
#define IPARS_PG_CONN_POOL_AUTO_DOWNSCALE_RATE_DEFAULT                               50
#define IPARS_PG_CONN_POOL_AUTO_DOWNSCALE_RATE_MIN                                   10
#define IPARS_PG_CONN_POOL_AUTO_DOWNSCALE_RATE_MAX                                   100

#define CONFIG_OVR_S2C_CATEGORY                                                      "Server to Client (S2C)"

/*
 * Feature: NP, Affected Components: IPARS
 */
#define NETWORK_PRESENCE_MAX_CANDIDATE_GATEWAY                                            "config.ipars.network_presence.max_candidate_gateways"
#define NETWORK_PRESENCE_MAX_CANDIDATE_GATEWAY_DEFAULT                                    1
#define NETWORK_PRESENCE_MAX_CANDIDATE_GATEWAY_VAL_MIN                                    1
#define NETWORK_PRESENCE_MAX_CANDIDATE_GATEWAY_VAL_MAX                                    5


#define NETWORK_PRESENCE_SHORT_TERM_EXPIRY_SEC                                            "config.ipars.network_presence.short_term_expiry_sec"
#define NETWORK_PRESENCE_SHORT_TERM_EXPIRY_SEC_DEFAULT                                    60
#define NETWORK_PRESENCE_SHORT_TERM_EXPIRY_SEC_VAL_MIN                                    10
#define NETWORK_PRESENCE_SHORT_TERM_EXPIRY_SEC_VAL_MAX                                    300


#define NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS                                           "config.ipars.network_presence.long_term_expiry_hours"
#define NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_DEFAULT                                   24
#define NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_VAL_MIN                                   1
#define NETWORK_PRESENCE_LONG_TERM_EXPIRY__HOURS_VAL_MAX                                  17520

#define NETWORK_PRESENCE_LONG_TERM_GRACEFUL_HOURS                                         "config.ipars.network_presence.long_term_graceful_hours"
#define NETWORK_PRESENCE_LONG_TERM_GRACEFUL_HOURS_DEFAULT                                 1
#define NETWORK_PRESENCE_LONG_TERM_GRACEFUL_HOURS_VAL_MIN                                 1
#define NETWORK_PRESENCE_LONG_TERM_GRACEFUL_HOURS_VAL_MAX                                 24


/*
 * Feature: tcp_keepalive flag will be turned on by OPS along with UI/API changes in drop15.
 * This flag will be turned off by OPS when UI/API changes are reverted from drop15
 */
#define CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT                                    "config.feature.connector.app_segment.tcpkeepalive"
#define DEFAULT_CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT                            0  /* tcp_keepalive for backward compatability */

/*
 * Feature: Keepalive, Affected Components: Connector
 */
#define CONNECTOR_KEEPALIVE_FEATURE                                                  "config.tcp.app.keepalive.enabled"
#define DEFAULT_CONNECTOR_KEEPALIVE_ENABLED                                          0  /* SO_KEEPALIVE is disabled by default for all customers */

/*
 * Feature: Admin Probe, Affected Components: Connector
 */
#define CONNECTOR_ADMIN_PROBE_FEATURE_ALL                                            "config.feature.connector.admin_probe.all"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_ALL_ENABLED                                    0  /* Admin probe is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_PROCESS                                "config.feature.connector.admin_probe.restart_process"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_PROCESS_ENABLED                        0  /* Admin probe on resatrt process is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_SYSTEM                                 "config.feature.connector.admin_probe.restart_system"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_SYSTEM_ENABLED                         0  /* Admin probe on resatrt system is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_DNS                                            "config.feature.connector.admin_probe.dns"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_DNS_ENABLED                                    0  /* Admin probe on DNS is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_ICMP                                           "config.feature.connector.admin_probe.icmp"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_ICMP_ENABLED                                   0  /* Admin probe on ICMP is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_TCP                                            "config.feature.connector.admin_probe.tcp"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_TCP_ENABLED                                    0  /* Admin probe on TCP is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_TCPDUMP                                        "config.feature.connector.admin_probe.tcpdump"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_TCPDUMP_ENABLED                                0  /* Admin probe on TCPDUMP is disabled by default */

#define CONNECTOR_ADMIN_PROBE_FEATURE_FRR_CMDS                                       "config.feature.connector.admin_probe.frr"
#define DEFAULT_CONNECTOR_ADMIN_PROBE_FRR_CMDS_ENABLED                               0  /* Admin probe on frr is disabled by default */

#define GATEWAY_ADMIN_PROBE_FEATURE_FRR_CMDS                                         "config.feature.gateway.admin_probe.frr"
#define DEFAULT_GATEWAY_ADMIN_PROBE_FRR_CMDS_ENABLED                                 0  /* Admin probe on frr is disabled by default */

/* connector zpn event cpu starvation config */
#define CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL                                    "config.feature.connector.zpn_event.cpu_starvation"
#define CONFIG_ZPN_EVENT_CPU_STARVATION_DEFAULT_INTERVAL                            900
#define LOW_CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL                                0
#define HIGH_CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL                               86400

/*
 * Feature: ZDX, Affected Components: Broker, PB, Connector
 */

/* Global cloudwise enable disable flag: 0 = disabled, 1 = enabled */
#define ZPN_ZDX_CONFIG_FEATURE_ENABLE                                                "config.feature.zdx.enabled"
#define ZPN_ZDX_CONFIG_FEATURE_ENABLE_DEFAULT                                        1      // Enabled by default

/* Global cloudwise zdx hard disable flag: 1 = hard disabled, 0 = not hard disabled */
#define ZPN_ZDX_CONFIG_FEATURE_HARD_DISABLED                                         "config.feature.zdx.hard_disabled"
#define ZPN_ZDX_CONFIG_FEATURE_HARD_DISABLED_DEFAULT                                 0      // i.e. Not hard disabled by default

/* Per system max cache entries */
#define ZPN_ZDX_CONFIG_BROKER_CACHE_COUNT                                            "config.feature.zdx.broker.cache.count"
#define ZPN_ZDX_CONFIG_BROKER_CACHE_COUNT_DEFAULT                                    (100*1000)
#define ZPN_ZDX_CONFIG_BROKER_CACHE_COUNT_MIN                                        (4*1000)
#define ZPN_ZDX_CONFIG_BROKER_CACHE_COUNT_MAX                                        (200*1000)

#define ZPN_ZDX_CONFIG_PBROKER_CACHE_COUNT                                           "config.feature.zdx.pbroker.cache.count"
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_COUNT_DEFAULT                                   (25*1000)
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_COUNT_MIN                                       (1000)
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_COUNT_MAX                                       (30*1000)

#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_COUNT                                         "config.feature.zdx.connector.cache.count"
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_COUNT_DEFAULT                                 (15*1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_COUNT_MIN                                     (1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_COUNT_MAX                                     (20*1000)

/* Limit what percent of total cache entries per customer */
#define ZDX_ZDX_CONFIG_BROKER_CACHE_SIZE_PER_CUST                                    "config.feature.zdx.broker.cache.customer.count"
#define ZDX_ZDX_CONFIG_BROKER_CACHE_SIZE_PER_CUST_DEFAULT                            (25*1000)
#define ZDX_ZDX_CONFIG_BROKER_CACHE_SIZE_PER_CUST_MIN                                (1000)
#define ZDX_ZDX_CONFIG_BROKER_CACHE_SIZE_PER_CUST_MAX                                (50*1000)

#define ZPN_ZDX_CONFIG_PBROKER_CACHE_SIZE_PER_CUST                                   "config.feature.zdx.pbroker.cache.customer.count"
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_SIZE_PER_CUST_DEFAULT                           (25*1000)
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_SIZE_PER_CUST_MIN                               (1000)
#define ZPN_ZDX_CONFIG_PBROKER_CACHE_SIZE_PER_CUST_MAX                               (30*1000)

#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_SIZE_PER_CUST                                 "config.feature.zdx.connector.cache.customer.count"
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_SIZE_PER_CUST_DEFAULT                         (15*1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_SIZE_PER_CUST_MIN                             (1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_CACHE_SIZE_PER_CUST_MAX                             (20*1000)

/* Cache config params */
#define ZPN_ZDX_CONFIG_CACHE_TTL_US                                                  "config.feature.zdx.cache.ttl_us"
#define ZPN_ZDX_CONFIG_CACHE_TTL_US_DEFAULT                                          (30*1000*1000)      // 30 seconds
#define ZPN_ZDX_CONFIG_CACHE_TTL_US_MIN                                              (15*1000*1000)      // 15 seconds
#define ZPN_ZDX_CONFIG_CACHE_TTL_US_MAX                                              (300*1000*1000)     // 300 seconds

#define ZPN_ZDX_CONFIG_CACHE_REFRESH_US                                              "config.feature.zdx.cache.refresh_us"
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_US_DEFAULT                                      (5*60*1000*1000)    // 300 seconds
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_US_MIN                                          (5*30*1000*1000)    // 150 seconds
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_US_MAX                                          (5*60*1000*1000)    // 300 seconds

#define ZPN_ZDX_CONFIG_CACHE_REFRESH_WARN_US                                         "config.feature.zdx.cache.refresh_warn_us"
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_WARN_US_DEFAULT                                 (15*1000*1000)      // 15 seconds
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_WARN_US_MIN                                     (5*1000*1000)       // 5 seconds
#define ZPN_ZDX_CONFIG_CACHE_REFRESH_WARN_US_MAX                                     (300*1000*1000)     // 300 seconds

#define ZPN_ZDX_CONFIG_CACHE_DELETE_ON_EXPIRY_US                                     "config.feature.zdx.cache.delete_on_expiry_us"
#define ZPN_ZDX_CONFIG_CACHE_DELETE_ON_EXPIRY_US_DEFAULT                             (60*1000*1000)      // 60 seconds
#define ZPN_ZDX_CONFIG_CACHE_DELETE_ON_EXPIRY_US_MIN                                 (30*1000*1000)      // 30 seconds
#define ZPN_ZDX_CONFIG_CACHE_DELETE_ON_EXPIRY_US_MAX                                 (120*1000*1000)     // 120 seconds

#define ZPN_ZDX_CONFIG_CACHE_EXPIRY_TIME_LIMIT_S                                     "config.feature.zdx.cache.expiry_time_limit_s"
#define ZPN_ZDX_CONFIG_CACHE_EXPIRY_TIME_LIMIT_S_DEFAULT                             (5*60)              // Cache expiry limit 300 seconds
#define ZPN_ZDX_CONFIG_CACHE_EXPIRY_TIME_LIMIT_S_MIN                                 (1*60)              // Cache expiry limit 60 seconds
#define ZPN_ZDX_CONFIG_CACHE_EXPIRY_TIME_LIMIT_S_MAX                                 (5*60)              // Cache expiry limit 300 seconds

#define ZPN_ZDX_CONFIG_CACHE_TIMER_PERIOD_S                                          "config.feature.zdx.cache.timer_period_s"
#define ZPN_ZDX_CONFIG_CACHE_TIMER_PERIOD_S_DEFAULT                                  (2)                // Cache timer period 2 seconds
#define ZPN_ZDX_CONFIG_CACHE_TIMER_PERIOD_S_MIN                                      (2)                // Cache timer period 2 seconds
#define ZPN_ZDX_CONFIG_CACHE_TIMER_PERIOD_S_MAX                                      (10)               // Cache timer period 10 seconds

#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED                   "config.feature.zdx.webprobe.cache.intercept.hard_disabled"
#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED_DEFAULT            0                   // http webprobe cache hard intercept is disabled by default

#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_ENABLED                          "config.feature.zdx.webprobe.cache.intercept.enabled"
#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT                  0                   // http webprobe cache intercept is disabled by default

#define ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED                  "config.feature.zdx.webprobe.cache.https.intercept.hard_disabled"
#define ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED_DEFAULT          0                   // https webprobe cache hard intercept is disabled by default

#define ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED                 "config.feature.zdx.broker.webprobe.cache.https.intercept.enabled"
#define ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT         0                   // broker https webprobe cache intercept is disabled by default

#define ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED                "config.feature.zdx.pbroker.webprobe.cache.https.intercept.enabled"
#define ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT        0                   //private broker https webprobe cache intercept is disabled by default

#define ZPN_ZDX_CONFIG_CONNECTOR_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED              "config.feature.zdx.connector.webprobe.cache.https.intercept.enabled"
#define ZPN_ZDX_CONFIG_CONNECTOR_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT       0                   //connector https webprobe cache intercept is disabled by default

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_COUNT                                         "config.feature.zdx.webprobe.cache.count"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_COUNT_DEFAULT                                 (100)           // webprobe cache maximum number of cache entries

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_ENTRY_REQUEST_COUNT                           "config.feature.zdx.webprobe.cache.entry_request_wait.count"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_ENTRY_REQUEST_COUNT_DEFAULT                   (14000)              // webprobe cache maximum number of waiting requests per entry

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_ENTRIES_REQUEST_COUNT                         "config.feature.zdx.webprobe.cache.entries_request_wait.count"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_ENTRIES_REQUEST_COUNT_DEFAULT                 (15000)        // webprobe cache maximum number of waiting requests for all entries

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_CONTENT_LENGTH_BYTES                          "config.feature.zdx.webprobe.cache.content_length.bytes"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_CONTENT_LENGTH_BYTES_DEFAULT                  (1024*1024)         // maximum number of stored content length in bytes

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_TTL_US                                        "config.feature.zdx.webprobe.cache.ttl_us"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_TTL_US_DEFAULT                                (1*60*1000*1000)      // webprobe cache entry time to live (or expiration) timeout - 1 min

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_WAIT_QUEUE_REQUEST_SEND_DELAY_S               "config.feature.zdx.webprobe.cache.timer_request_send_delay_s"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_WAIT_QUEUE_REQUEST_SEND_DELAY_S_DEFAULT       (10)                // webprobe requests re-send delay interval until receiving response - 10 seconds

#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_RESPONSES_PER_PASS_COUNT                      "config.feature.zdx.webprobe.cache.responses_per_pass.count"
#define ZPN_ZDX_CONFIG_WEBPROBE_CACHE_RESPONSES_PER_PASS_COUNT_DEFAULT              200                 // maximum number of sending responses per pass of 1 second

#define ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_IPV6_HARD_DISABLED             "config.feature.zdx_ipv6.webprobe.cache.https.intercept.hard_disabled"
#define ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_IPV6_HARD_DISABLED_DEFAULT     0                   // ipv6 https webprobe cache hard intercept is disabled by default

#define ZPN_ZDX_CONFIG_CONNECTOR_HTTPS_WEBPROBE_CACHE_INTERCEPT_IPV6_ENABLED         "config.feature.zdx_ipv6.connector.webprobe.cache.https.intercept.enabled"
#define ZPN_ZDX_CONFIG_CONNECTOR_HTTPS_WEBPROBE_CACHE_INTERCEPT_IPV6_ENABLED_DEFAULT 0                   // ipv6 connector https webprobe cache intercept is disabled by default

#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_IPV6_HARD_DISABLED              "config.feature.zdx_ipv6.webprobe.cache.intercept.hard_disabled"
#define ZPN_ZDX_CONFIG_HTTP_WEBPROBE_CACHE_INTERCEPT_IPV6_HARD_DISABLED_DEFAULT      0                   // ipv6 http webprobe cache hard intercept is disabled by default

#define ZPN_ZDX_CONFIG_CONNECTOR_HTTP_WEBPROBE_CACHE_INTERCEPT_IPV6_ENABLED          "config.feature.zdx_ipv6.connector.webprobe.cache.intercept.enabled"
#define ZPN_ZDX_CONFIG_CONNECTOR_HTTP_WEBPROBE_CACHE_INTERCEPT_ENABLED_IPV6_DEFAULT  0                   // ipv6 http webprobe cache intercept is disabled by default


/* zdx parallel mtr feature enable disable flag: 0 = disabled, 1 = enabled */
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_PBROKER                                   "config.feature.zdx.pbroker.parallel.probe.enabled"
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_CONNECTOR                                 "config.feature.zdx.connector.parallel.probe.enabled"
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_CONNECTOR_DEFAULT                         1                 // parallel mtr probing for connector is enabled by default
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_PBROKER_DEFAULT                           0                 // parallel mtr probing for pse is disabled by default

/* zdx parallel mtr ipv6 feature enable disable flag: 0 = disabled, 1 = enabled */
#define ZPN_ZDX_CONFIG_PARALLEL_IPV6_MTR_PROBE_PBROKER                              "config.feature.zdx_ipv6.pse.parallel.probe.enabled"
#define ZPN_ZDX_CONFIG_PARALLEL_IPV6_MTR_PROBE_CONNECTOR                            "config.feature.zdx_ipv6.connector.parallel.probe.enabled"
#define ZPN_ZDX_CONFIG_PARALLEL_IPV6_MTR_PROBE_PBROKER_DEFAULT                       0                 // parallel mtr ipv6 probing for pbroker is disabled by default
#define ZPN_ZDX_CONFIG_PARALLEL_IPV6_MTR_PROBE_CONNECTOR_DEFAULT                     0                 // parallel mtr ipv6 probing for connector is disabled by default

/* zdx parallel mtr feature hard disable flag: 1 = hard disabled, 0 = hard disabled is off*/
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_HARD_DISABLED                             "config.feature.zdx.parallel.probe.hard_disabled"
#define ZPN_ZDX_CONFIG_PARALLEL_MTR_PROBE_HARD_DISABLED_DEFAULT                     0                // hard disabled if off by default

/* zdx ipv6 mtr feature enable disable flag: 0 = disabled, 1 = enabled */
#define ZPN_ZDX_CONFIG_IPV6_MTR_PROBE_PBROKER                                       "config.feature.zdx_ipv6.pse.enabled"
#define ZPN_ZDX_CONFIG_IPV6_MTR_PROBE_CONNECTOR                                     "config.feature.zdx_ipv6.connector.enabled"
#define ZPN_ZDX_CONFIG_IPV6_MTR_PROBE_DEFAULT                                       0                 // ipv6 mtr is disabled by default

/* zdx ipv6 mtr feature hard disable flag: 1 = hard disabled, 0 = hard disabled is off*/
#define ZPN_ZDX_CONFIG_IPV6_MTR_PROBE_HARD_DISABLED                                 "config.feature.zdx_ipv6.hard_disabled"
#define ZPN_ZDX_CONFIG_IPV6_MTR_PROBE_HARD_DISABLED_DEFAULT                         0                // hard disabled if off by default

/* Customer Traceroute on Public Brokers */
/* Do not enable this on public brokers without
 * talking to ZDX team default is disabled, and it should remain off
 * until significant reason to turn it on is found
 * and broker changes to scale are finished
 */
#define ZPN_ZDX_CONFIG_CUSTOMER_PROBE_ON_PUBLIC_BROKER                              "config.feature.zdx.broker.customer.do_traceroute"
#define ZPN_ZDX_CONFIG_CUSTOMER_PROBE_ON_PUBLIC_BROKER_DEFAULT                      0                   // Disabled
#define ZPN_ZDX_CONFIG_CUSTOMER_PROBE_ON_PUBLIC_BROKER_MIN                          0
#define ZPN_ZDX_CONFIG_CUSTOMER_PROBE_ON_PUBLIC_BROKER_MAX                          1

/* Per system max probe entries */
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT                                            "config.feature.zdx.broker.probe.count"
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_DEFAULT                                    (100*1000)
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_MIN                                        (4*1000)
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_MAX                                        (200*1000)

#define ZDX_ZDX_CONFIG_PBROKER_PROBE_COUNT                                           "config.feature.zdx.pbroker.probe.count"
#define ZDX_ZDX_CONFIG_PBROKER_PROBE_COUNT_DEFAULT                                   (25*1000)
#define ZDX_ZDX_CONFIG_PBROKER_PROBE_COUNT_MIN                                       (1000)
#define ZDX_ZDX_CONFIG_PBROKER_PROBE_COUNT_MAX                                       (30*1000)

#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT                                         "config.feature.zdx.connector.probe.count"
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_DEFAULT                                 (15*1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_MIN                                     (1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_MAX                                     (20*1000)

/* Limit what percent of total probe entries per customer */
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_PER_CUST                                   "config.feature.zdx.broker.probe.customer.count"
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_PER_CUST_DEFAULT                           (25*1000)
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_PER_CUST_MIN                               (1000)
#define ZPN_ZDX_CONFIG_BROKER_PROBE_COUNT_PER_CUST_MAX                               (50*1000)

#define ZPN_ZDX_CONFIG_PBROKER_PROBE_COUNT_PER_CUST                                  "config.feature.zdx.pbroker.probe.customer.count"
#define ZPN_ZDX_CONFIG_PBROKER_PROBE_COUNT_PER_CUST_DEFAULT                          (25*1000)
#define ZPN_ZDX_CONFIG_PBROKER_PROBE_COUNT_PER_CUST_MIN                              (1000)
#define ZPN_ZDX_CONFIG_PBROKER_PROBE_COUNT_PER_CUST_MAX                              (30*1000)

#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_PER_CUST                                "config.feature.zdx.connector.probe.customer.count"
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_PER_CUST_DEFAULT                        (15*1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_PER_CUST_MIN                            (1000)
#define ZPN_ZDX_CONFIG_CONNECTOR_PROBE_COUNT_PER_CUST_MAX                            (20*1000)

/* Per system max probe process count */
#define ZPN_ZDX_CONFIG_BROKER_MAX_PROBE_PROCESS_COUNT                                "config.feature.zdx.broker.max.probe.process.count"
#define ZPN_ZDX_CONFIG_BROKER_MAX_PROBE_PROCESS_COUNT_DEFAULT                        1000
#define ZPN_ZDX_CONFIG_BROKER_MAX_PROBE_PROCESS_COUNT_MIN                            100
#define ZPN_ZDX_CONFIG_BROKER_MAX_PROBE_PROCESS_COUNT_MAX                            2000

#define ZDX_ZDX_CONFIG_PBROKER_MAX_PROBE_PROCESS_COUNT                               "config.feature.zdx.pbroker.max.probe.process.count"
#define ZDX_ZDX_CONFIG_PBROKER_MAX_PROBE_PROCESS_COUNT_DEFAULT                       1000
#define ZDX_ZDX_CONFIG_PBROKER_MAX_PROBE_PROCESS_COUNT_MIN                           100
#define ZDX_ZDX_CONFIG_PBROKER_MAX_PROBE_PROCESS_COUNT_MAX                           2000

#define ZPN_ZDX_CONFIG_CONNECTOR_MAX_PROBE_PROCESS_COUNT                             "config.feature.zdx.connector.max.probe.process.count"
#define ZPN_ZDX_CONFIG_CONNECTOR_MAX_PROBE_PROCESS_COUNT_DEFAULT                     1000
#define ZPN_ZDX_CONFIG_CONNECTOR_MAX_PROBE_PROCESS_COUNT_MIN                         100
#define ZPN_ZDX_CONFIG_CONNECTOR_MAX_PROBE_PROCESS_COUNT_MAX                         2000

#define ZPN_ZDX_CONFIG_KEY_CONNECTOR                                                 0
#define ZPN_ZDX_CONFIG_KEY_PBROKER                                                   0
#define ZPN_ZDX_CONFIG_KEY_BROKER                                                    0

/* Predictive Probes Feature - Hard Disable Flag */
#define ZPN_ZDX_CONFIG_PREDICTIVE_PROBE_HARD_DISABLE                                 "config.feature.zdx.predictive.probe.hard_disabled"
#define ZPN_ZDX_CONFIG_PREDICTIVE_PROBE_HARD_DISABLE_DEFAULT                         0      // Disabled

/* Predictive Probes Feature - Global flag */
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE                                      "config.feature.zdx.pbroker.predictive.probe.enabled"
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_DEFAULT                              0      // Disabled

#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE                                    "config.feature.zdx.connector.predictive.probe.enabled"
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_DEFAULT                            1      // Enabled

/* Predictive Probes IPv6 Feature - Global flag */
#define ZPN_ZDX_CONFIG_IPV6_PBROKER_PREDICTIVE_PROBE                                      "config.feature.zdx_ipv6.pse.predictive.probe.enabled"
#define ZPN_ZDX_CONFIG_IPV6_PBROKER_PREDICTIVE_PROBE_DEFAULT                              0      // Disabled

#define ZPN_ZDX_CONFIG_IPV6_CONNECTOR_PREDICTIVE_PROBE                                    "config.feature.zdx_ipv6.connector.predictive.probe.enabled"
#define ZPN_ZDX_CONFIG_IPV6_CONNECTOR_PREDICTIVE_PROBE_DEFAULT                            0      // Disabled

/* Predictive Probes - Memory Threshold */
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_MEM_THRESH                           "config.feature.zdx.pbroker.predictive.probe.mem_threshold"
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_MEM_THRESH_DEFAULT                   80
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_MEM_THRESH_MIN                       10
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_MEM_THRESH_MAX                       80

#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_MEM_THRESH                         "config.feature.zdx.connector.predictive.probe.mem_threshold"
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_MEM_THRESH_DEFAULT                 80
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_MEM_THRESH_MIN                     10
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_MEM_THRESH_MAX                     80

/* Predictive Probes - Refresh Stop Time */
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_STOP_REFRESH                         "config.feature.zdx.pbroker.predictive.probe.stop_refresh"
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_STOP_REFRESH_DEFAULT                 (60*60)    // 1 Hour
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_STOP_REFRESH_MIN                     (30*60)    // 0.5 Hour
#define ZPN_ZDX_CONFIG_PBROKER_PREDICTIVE_PROBE_STOP_REFRESH_MAX                     (24*60*60) // 24 Hours

#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_STOP_REFRESH                       "config.feature.zdx.connector.predictive.probe.stop_refresh"
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_STOP_REFRESH_DEFAULT               (60*60)    // 1 Hour
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_STOP_REFRESH_MIN                   (30*60)    // 0.5 Hour
#define ZPN_ZDX_CONFIG_CONNECTOR_PREDICTIVE_PROBE_STOP_REFRESH_MAX                   (24*60*60) // 24 Hour

/* Global cloudwise enable disable flag: 0 = disabled, 1 = enabled */
#define ZPN_ZDX_RATE_LIMIT_CONFIG_ENABLE                                             "config.feature.zdx.rate_limit.web_probe.enabled"
#define ZPN_ZDX_RATE_LIMIT_CONFIG_ENABLE_DEFAULT                                     1      // Enabled

/* Per system max cache entries */
#define ZPN_ZDX_RATE_LIMIT_CONFIG_BROKER_WEB_PROBE_RATE                              "config.feature.zdx.rate_limit.broker.web_probe_rate"
#define ZPN_ZDX_RATE_LIMIT_CONFIG_BROKER_WEB_PROBE_RATE_DEFAULT                      (50)

/* Per system max cache entries */
#define ZPN_ZDX_RATE_LIMIT_CONFIG_PBROKER_WEB_PROBE_RATE                             "config.feature.zdx.rate_limit.pbroker.web_probe_rate"
#define ZPN_ZDX_RATE_LIMIT_CONFIG_PBROKER_WEB_PROBE_RATE_DEFAULT                     (35)

#define ZPN_ZDX_RATE_LIMIT_CONFIG_KEY_CONNECTOR                                      0
#define ZPN_ZDX_RATE_LIMIT_CONFIG_KEY_PBROKER                                        0
#define ZPN_ZDX_RATE_LIMIT_CONFIG_KEY_BROKER                                         0

/* zpath_config_override_get_config_str call is made to get the cert. Default value passed is NULL. */
#define TPG_PUBLIC_CERT                                                              "1_config.certs.tpg_public_cert"

/*
 * Feature: FOHH log max in flight, Affected Components: Almost all (part of zpath_app_init)
 * Default value passed is 2000.
 */
#define CONFIG_FOHH_LOG_MAX_IN_FLIGHT                                                "config.fohh.log.max_in_flight"

/*
 * timeout option to delete fohh connections that doesn't progress to connected state
 */
#define FOHH_CONNECTION_SETUP_TIMEOUT                                               "config.fohh_conn_setup_timeout"
#define FOHH_CONNECTION_SETUP_TIMEOUT_SEC                                           10
#define FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MIN                                       5
#define FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MAX                                       60

/*
 * timeout option to delete client connections that doesn't authentication within the configured time
 */
#define BROKER_CLIENT_AUTH_TIMEOUT                                                  "config.broker_client_auth_timeout"
#define BROKER_CLIENT_AUTH_TIMEOUT_SEC                                              20
#define BROKER_CLIENT_AUTH_TIMEOUT_MIN_SEC                                          20
#define BROKER_CLIENT_AUTH_TIMEOUT_MAX_SEC                                          120

/*
 * sitec timeout option to delete client connections that doesn't authentication within the configured time
 */
#define SITEC_CLIENT_AUTH_TIMEOUT                                                  "config.sitec_client_auth_timeout"
#define SITEC_CLIENT_AUTH_TIMEOUT_SEC                                              20
#define SITEC_CLIENT_AUTH_TIMEOUT_MIN_SEC                                          20
#define SITEC_CLIENT_AUTH_TIMEOUT_MAX_SEC                                          120

/*
 * Feature: Broker auth log redirection status
 */
#define BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS                                           "config.feature.broker.authlog_redirection_status.enabled"
#define DEFAULT_BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS                                   0  /* disabled by default */
#define BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_MIN                                       0
#define BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_MAX                                       1

/*
 * feature flag for delegated admin
 */
#define DELEGATED_ADMIN_FEATURE                                                     "config.feature.delegated.admin"
#define DELEGATED_ADMIN_FEATURE_DISABLED                                            0
#define DELEGATED_ADMIN_FEATURE_ENABLED                                             1
#define DELEGATED_ADMIN_FEATURE_DEFAULT                                             DELEGATED_ADMIN_FEATURE_DISABLED

/*
 * timeout option to delete client ip anchor connections if idle within the configured time
 */
#define BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT                                     "config.broker_client_ip_anchor_rx_data_timeout"
#define BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC                                 12 * 60 * 60
#define BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MIN                             300 * 2
#define BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MAX                             2 * 12 * 60 * 60
/*
 * Feature: Policy Evaluate Type, Affected Components: Broker, PB
 * This feature define how we evaluate policy
 * 0: old way, 1: new way, 2: both and compare results
 */
#define POLICY_EVAL_TYPE_FEATURE                                                     "config.feature.policy_eval_type"
#define DEFAULT_POLICY_EVAL_TYPE                                                     0
#define DEFAULT_POLICY_EVAL_TYPE_MIN                                                 0
#define DEFAULT_POLICY_EVAL_TYPE_MAX                                                 2


#define POLICY_RE_EVAL_ON_POSTURE_CHANGE                                             "config.feature.policy_re_eval_on_posture_change"
#define DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE                                     0
#define DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE_MIN                                 0
#define DEFAULT_POLICY_RE_EVAL_ON_POSTURE_CHANGE_MAX                                 1

#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED                "config.feature.policy_re_eval_on_posture_change.hard_disabled"
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_DEFAULT        0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_VAL_MIN        0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_VAL_MAX        1

#define POLICY_SVCP_RE_EVAL_FREQUENCY_SEC                                            "config.feature.policy_svcp_re_eval_freq_sec"
#define DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC                                    900 //15 min
#define DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC_MIN                                900 //15 min
#define DEFAULT_POLICY_SVCP_RE_EVAL_FREQUENCY_SEC_MAX                                (86400*365) //365 days - seconds in 1 day*365

/*
* Option to enable/disable/hard disable svcp feature
*/

#define POLICY_SVCP_ENABLE                                                           "config.feature.server_validated_cert_posture"
#define DEFAULT_POLICY_SVCP_ENABLE_MIN                                               0
#define DEFAULT_POLICY_SVCP_ENABLE_MAX                                               1
#define DEFAULT_POLICY_SVCP_ENABLE                                                   0


#define POLICY_SVCP_HARD_DISABLE                                                     "config.feature.server_validated_cert_posture.hard_disabled"
#define DEFAULT_POLICY_SVCP_HARD_DISABLE_MIN                                         0
#define DEFAULT_POLICY_SVCP_HARD_DISABLE_MAX                                         1
#define DEFAULT_POLICY_SVCP_HARD_DISABLE                                             0


#define POLICY_REBUILD_BACKOFF_FEATURE                                               "config.feature.policy_rebuild_backoff"
#define POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT                                       0
#define POLICY_REBUILD_BACKOFF_FEATURE_MIN                                           0
#define POLICY_REBUILD_BACKOFF_FEATURE_MAX                                           1

#define POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED                                 "config.feature.policy_rebuild_backoff.hard_disabled"
#define POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_DEFAULT                         0
#define POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_MIN                             0
#define POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_MAX                             1

#define POLICY_REBUILD_BACKOFF_INTERVAL_SEC                                          "config.feature.policy_rebuild_backoff_interval_s"
#define POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT                                  5  //5 sec
#define POLICY_REBUILD_BACKOFF_INTERVAL_SEC_MIN                                      1  //1 sec
#define POLICY_REBUILD_BACKOFF_INTERVAL_SEC_MAX                                      10 //10 sec

#define POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC                           "config.feature.policy_rebuild_backoff_periodic_check_interval_s"
#define POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT                   60   //1 min
#define POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_MIN                       30   //30 sec
#define POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_MAX                       1800 //30 min


/*
 * Feature: Pattern match application feature flag
 * This feature defines if we allow pattern match style of applications or not.
 * 0: disable partially. Diamond add/remove isn't restricted for the domains matching pattern, but search is restricted.
 * 1: enable
 * 2: disable fully. Diamond add/remove/serach all operations restricted for the domains matching pattern.
 */
#define APPLICATION_PATTERN_MATCH_FEATURE                                           "config.feature.application_pattern_match"
#define APPLICATION_PATTERN_MATCH_FEATURE_DEFAULT                                   0
#define APPLICATION_PATTERN_MATCH_FEATURE_MIN                                       0
#define APPLICATION_PATTERN_MATCH_FEATURE_MAX                                       2
#define APPLICATION_PATTERN_MATCH_FEATURE_DISABLE                                   0
#define APPLICATION_PATTERN_MATCH_FEATURE_ENABLE                                    1
#define APPLICATION_PATTERN_MATCH_FEATURE_DISABLE_FULLY                             2

/* Global cloudwise Pattern match application hard disable flag: 1 = hard disabled, 0 = not hard disabled */
#define APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED                             "config.feature.application_pattern_match.hard_disabled"
#define APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED_DEFAULT                     0 /* Not hard disabled by default */
#define APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED_MIN                         0
#define APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED_MAX                         1

/*
 * Feature: Multi match application feature flag
 * This feature defines if we allow multi match style of applications instead of first match.
 * 0: disable, 1: enable
 */
#define APPLICATION_MULTI_MATCH_FEATURE                                              "config.feature.application_multi_match"
#define APPLICATION_MULTI_MATCH_FEATURE_DEFAULT                                      0
#define APPLICATION_MULTI_MATCH_FEATURE_MIN                                          0
#define APPLICATION_MULTI_MATCH_FEATURE_MAX                                          1

/* Global cloudwise Multi match application hard disabled flag: 1 = hard disabled, 0 = not hard disabled */
#define APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED                                "config.feature.application_multi_match.hard_disabled"
#define APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_DEFAULT                        0 /* Not hard disabled by default */
#define APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_MIN                            0
#define APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_MAX                            1

/*
 * Feature: FQDN to Server IP Policy Support
 * Option to enable/disable FQDN_TO_SERVER_IP feature
 */
#define POLICY_FQDN_TO_SERV_IP_ENABLE                                                 "config.feature.policy_fqdn_to_srv_ip"
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE_MIN                                     0
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE_MAX                                     1
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_ENABLE                                         0

/* Global cloudwise FQDN to Server IP Policy Support hard disabled flag: 1 = hard disabled, 0 = not hard disabled */
#define POLICY_FQDN_TO_SERV_IP_HARD_DISABLE                                           "config.feature.policy_fqdn_to_srv_ip.hard_disabled"
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_HARD_DISABLE_MIN                               0
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_HARD_DISABLE_MAX                               1
#define DEFAULT_POLICY_FQDN_TO_SERV_IP_HARD_DISABLE                                   0

/*
 * Feature: Application Match SIPA Only Apps For SIPA Client
 * Option to enable/disable Application Match SIPA Only Apps For SIPA Client feature
 */
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_ENABLE                       "config.feature.application_match_sipa_only_apps_for_sipa_client"
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_MIN                          0
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_MAX                          1
#define DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT                      0

/* Global cloudwise Application Match SIPA Only Apps For SIPA Client hard disabled flag: 1 = hard disabled, 0 = not hard disabled */
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED                "config.feature.application_match_sipa_only_apps_for_sipa_client.hard_disabled"
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED_MIN            0
#define APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED_MAX            1
#define DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED        0


/*
 * option to disable flow control for mtr probes. applicable only for fohh currently.
 */
#define BROKER_DISABLE_FC_MTR                                                       "config.feature.broker_disable_fc_mtr"
#define DEFAULT_BROKER_DISABLE_FC_MTR                                               1
#define BROKER_DISABLE_FC_MTR_MIN                                                   0
#define BROKER_DISABLE_FC_MTR_MAX                                                   1

/*
 * option to enable Libevent out buffer write low watermark
 */
#define LIBEVENT_LOW_WRITE_WATERMARK_BROKER                                                "config.feature.broker.libevent_low_write_watermark_enable"
#define LIBEVENT_LOW_WRITE_WATERMARK_PSE                                                   "config.feature.pse.libevent_low_write_watermark_enable"
#define LIBEVENT_LOW_WRITE_WATERMARK_CONNECTOR                                             "config.feature.connector.libevent_low_write_watermark_enable"
#define LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT                                               0
#define LIBEVENT_LOW_WRITE_WATERMARK_MIN                                                   0
#define LIBEVENT_LOW_WRITE_WATERMARK_MAX                                                   1

/*
 * flow control enhancements feature for fohh currently.
 */
#define FOHH_FLOW_CONTROL_ENHANCEMENTS                                                    "config.feature.fohh_flow_control_enhancements"
#define DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS                                             0
#define FOHH_FLOW_CONTROL_ENHANCEMENTS_MIN                                                 0
#define FOHH_FLOW_CONTROL_ENHANCEMENTS_MAX                                                 1

/*
 * Option to enable hostname validation on broker.
 * By default hostname validation is disabled.
 */
#define ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION                                       "config.feature.broker.enable_hostname_validation"
#define ZPN_DEFAULT_BROKER_ENABLE_HOSTNAME_VALIDATION                                0
#define ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION_MIN                                    0
#define ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION_MAX                                    1

/*
 * Option to enable hostname validation on private broker.
 * By default hostname validation is disabled.
 */
#define ZPN_PSE_ENABLE_HOSTNAME_VALIDATION                                          "config.feature.pse.enable_hostname_validation"
#define ZPN_DEFAULT_PSE_ENABLE_HOSTNAME_VALIDATION                                   0
#define ZPN_PSE_ENABLE_HOSTNAME_VALIDATION_MIN                                       0
#define ZPN_PSE_ENABLE_HOSTNAME_VALIDATION_MAX                                       1

/*
 * Feature: Broker load change for wally comms issue, Affected Components: Broker
 */

#define CONFIG_BROKER_BALANCE_CATEGORY                                               "Broker Balance flag"

/* To read the value of possible_broker_age_s using config override. */
#define CONFIG_POSSIBLE_BROKER_AGE_S                                                 "config.balance_control.possible_broker.age_s"
/* Default age (last load report time) of broker to get into the list of possible brokers - 2 days. */
#define POSSIBLE_BROKER_AGE_DEFAULT_S                                                172800
#define POSSIBLE_BROKER_AGE_MAX_S                                                    604800 // 7 days
#define POSSIBLE_BROKER_AGE_MIN_S                                                    180 // 3 min

/* To read the value of load_stale_age_s using config override. */
#define CONFIG_LOAD_STALE_AGE_S                                                      "config.balance_control.load_stale.age_s"
/* If load report time is old (by default 15 min) for any viable broker, reduce its load by 50%. */
#define LOAD_STALE_AGE_DEFAULT_S                                                     900
#define LOAD_STALE_AGE_MAX_S                                                         86400 // 1 day
#define LOAD_STALE_AGE_MIN_S                                                         120 // 2 min

/* If PSE is further to user than public brokers, but within the grace distance,
 * it's NOT taken out of the redirect list. This only affects public brokers.
 * Scope: Can be set on (in order of priority) a customer, a public broker (for canary), or globally.
 * Valid: 0 -  CONFIG_FEATURE_BALANCE_PSE_GRACE_DIST_MILES_MAX */
#define CONFIG_FEATURE_BALANCE_PSE_GRACE_DIST_MILES                                  "config.feature.balance.pse_grace_dist_miles"
#define CONFIG_FEATURE_BALANCE_PSE_GRACE_DIST_MILES_DEFAULT                          0
#define CONFIG_FEATURE_BALANCE_PSE_GRACE_DIST_MILES_MIN                              0
#define CONFIG_FEATURE_BALANCE_PSE_GRACE_DIST_MILES_MAX                              3000

/* The percentage of clients that should be redirected to public brokers from multiple DCs,
 * i.e. 2 public brokers from home/local DC and 1 from a secondary DC.
 * The remainder (100 - <value>) will use the existing single dc redirect logic.
 *      100: multi-dc redirect is always enabled
 *      0: multi-dc redirect is disabled
 *      < 0: treated as 0
 *      > 100: treated as 100
 * default: 100 (i.e. multi-dc redirect ENABLED) */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_PCT                                        "config.feature.balance.mdc_redir_public.pct"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_PCT_DEFAULT                                100
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_PCT_MIN                                    0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_PCT_MAX                                    100

/* multi-dc-redirect: if primary dc load is higer than this limit,
 * choose at least 2 secondary DCs and all 2 brokers are selected from secondary dcs.
 * range: [0-100] */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_PDC_LOAD                               "config.feature.balance.mdc_redir_public.max_pdc_load"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_PDC_LOAD_DEFAULT                       95
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_PDC_LOAD_MIN                           0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_PDC_LOAD_MAX                           100

/* multi-dc-redirect: if a candidate dc load is higher than this limit,
 * it's not taken as a secondary dc
 * range: [0-100] */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_LOAD                               "config.feature.balance.mdc_redir_public.max_sdc_load"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_LOAD_DEFAULT                       85
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_LOAD_MIN                           0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_LOAD_MAX                           100

/* Set a different load criteria (from the regular max_pdc_load and max_sdc_load)
 * when filtering geofenced DCs. This can be used to force traffic staying in
 * geofenced countries even when brokers in those DCs are highly loaded.
 * Default is 100, i.e. do not filter out geofenced DCs due to load. */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_PDC_LOAD                            "config.feature.balance.mdc_redir_public.max_gf_pdc_load"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_PDC_LOAD_DEFAULT                    100
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_PDC_LOAD_MIN                        0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_PDC_LOAD_MAX                        100

#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_SDC_LOAD                            "config.feature.balance.mdc_redir_public.max_gf_sdc_load"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_SDC_LOAD_DEFAULT                    100
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_SDC_LOAD_MIN                        0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_GF_SDC_LOAD_MAX                        100

/* multi-dc-redirect: max number of secondary DCs
 * range: [2 - 20] */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT                              "config.feature.balance.mdc_redir_public.max_sdc_count"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT_DEFAULT                      6
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT_MIN                          2
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT_MAX                          20

/* multi-dc-redirect: max distance (in miles) of a secondary DC from client
 * range: [100 - unlimited] */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES                         "config.feature.balance.mdc_redir_public.max_sdc_dist_miles"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES_DEFAULT                 3500 // rough size of a continent
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES_MIN                     100
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES_MAX                     30000

/* multi-dc-redirect: collect enough secondary dcs such that collectively they reach
 * a percentage of the primary dc's capacity.
 * range: [0-100] */
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_SDC_CAPCITY_TARGET_PCT                     "config.feature.balance.mdc_redir_public.sdc_capacity_target_pct"
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_SDC_CAPCITY_TARGET_PCT_DEFAULT             25
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_SDC_CAPCITY_TARGET_PCT_MIN                 0
#define CONFIG_FEATURE_BALANCE_PUBLIC_MDC_SDC_CAPCITY_TARGET_PCT_MAX                 100

/* If set to 1, Geofencing is enforced: never try to redirect users
 * across boundary of geofenced country. Let redirect fail instead.
 * If set to 0, Geofencing is a best effort: if geofencing fails to
 * yield any broker, retry with geofencing disabled. */
#define CONFIG_FEATURE_BALANCE_GEOFENCING_ENFORCED                                    "config.feature.balance.geofencing.enforced"
#define CONFIG_FEATURE_BALANCE_GEOFENCING_ENFORCED_DEFAULT                            0
#define CONFIG_FEATURE_BALANCE_GEOFENCING_ENFORCED_MIN                                0
#define CONFIG_FEATURE_BALANCE_GEOFENCING_ENFORCED_MAX                                1

/*
 * Feature: CPU report algo, Affected Components: Broker
 * Config override config.feature.broker.cpu_report_algo specifies how this public broker reports its cpu usage.
 * Possible values:
 *    BROKER_CPU_REPORT_ALGO_SYSTEM                 = 1 : report system cpu usage only.
 *    BROKER_CPU_REPORT_ALGO_SYSTEM_OR_THREAD_S     = 2 : default, report the higher of system cpu and average cpu of any thread over 60 seconds.
 *    BROKER_CPU_REPORT_ALGO_SYSTEM_OR_THREAD_L     = 3 : report the higher of system cpu and average cpu of any thread over 10 minutes.
 *    BROKER_CPU_REPORT_ALGO_SYSTEM_OR_GROUP_S      = 4 : report The higher of system cpu and average cpu of any thread group over 60 seconds.
 *    BROKER_CPU_REPORT_ALGO_SYSTEM_OR_GROUP_L      = 5 : report The higher of system cpu and average cpu of any thread group over 10 minutes.
 * Important: Do not change the numeric values. Only add to them.
 */
#define CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO                                        "config.feature.broker.cpu_report_algo"
#define BROKER_CPU_REPORT_ALGO_DEFAULT                                               2
#define CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO_MIN                                    1
#define CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO_MAX                                    5
/*
 * Feature: Session termination on reauth, Affected Components: Broker, PB
 */
#define CONFIG_OVERRIDE_SESS_TERM_ON_REAUTH                                          "config.override.session.termination.on.reauth"
#define CONFIG_OVERRIDE_SESS_TERM_ON_REAUTH_DEFAULT                                  0
#define CONFIG_OVERRIDE_SESS_TERM_ON_REAUTH_MIN                                      0
#define CONFIG_OVERRIDE_SESS_TERM_ON_REAUTH_MAX                                      1

/*
 * Feature: Private broker comprehensive stats TX, Affected Components: PB, Broker
 */
#define PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE                                  "config.feature.pbroker.stats.comprehensive"
#define PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT                          1
#define PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MIN                              0
#define PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MAX                              1

#define PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD                           "config.feature.pse.enable.wally_table_stats_upload"
#define PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_DEFAULT                   1
#define PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_MIN                       0
#define PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_MAX                       1

/*
 * Feature: Site controller comprehensive stats TX, Affected Components: Sitec, Broker
 */
#define SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE                                    "config.feature.sitec.stats.comprehensive"
#define SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT                            0
#define SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MIN                                0
#define SITEC_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MAX                                1

/*
 * Feature: Site controller split brain config sync
 */
#define SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE   "config.feature.sitec.split_brain.sync.enable"
#define SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_DEFAULT                        0
#define SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_MIN                            0
#define SITEC_CONFIG_OVERRIDE_SPLIT_BRAIN_SYNC_ENABLE_MAX                            1

/*
 * Minimum Physical memory required for a PSE to start
 * Atleast 2gB of physical memory is needed, since /proc/meminfo output will show some less phycal memory than what is actually present
 * we are setting value to 1.5gB and any box having MORE (NOT EQUAL) than this will be supported
 */

#define CONFIG_FEATURE_PSE_MIN_MEMORY_KB                                             "config.feature.pbroker.memory.min_kb"
#define CONFIG_FEATURE_PSE_MIN_MEMORY_KB_DEFAULT                           (1536 * 1024)
#define CONFIG_FEATURE_PSE_MIN_MEMORY_KB_MIN                                     (256 * 1024)              // 256 MB in units of KB
#define CONFIG_FEATURE_PSE_MIN_MEMORY_KB_MAX                                    (32 * 1024 * 1024)  // 32 GB in units of KB

/*
 * Decide on which priority queue to use for app threads 1=libevent 0=zevent
 */
#define CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY                               "config.feature.pse.libevent.priority"
#define CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_DEFAULT                       0
#define CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_MIN                           -1
#define CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_MAX                           1

/*
 * Decide on wether to use custom app threads or default
 */
#define CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD                               "config.feature.pse.custom.appthread.count"
#define CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_DEFAULT                       0
#define CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_MIN                          -1
#define CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_MAX                           1


/*
 * Feature: Exporter support for broker redirect, Affected Components: Exporter
 * Feature flag: "config.feature.exporter.redirect_mode"
 * Values:
 *   EXPORTER_REDIRECT_MODE_DISABLED =  0 :    redirects not supported.
 *   EXPORTER_REDIRECT_MODE_FORCE    =  1 :    only respond to forced redirects (e.g. broker shutting down).
 *   EXPORTER_REDIRECT_MODE_FULL     =  2 :    support initial redirect and any subsequent redirects, regardless of redirect's attribute.
 */
#define EXPORTER_REDIRECT_FEATURE_OVERRIDE                                           "config.feature.exporter.redirect_mode"
#define EXPORTER_REDIRECT_MODE_DEFAULT                                               1
#define EXPORTER_REDIRECT_FEATURE_OVERRIDE_MIN                                       0
#define EXPORTER_REDIRECT_FEATURE_OVERRIDE_MAX                                       2

/* Feature: Managed BA access. */
#define EXPORTER_MGED_BA_FEATURE                                                   "config.feature.exporter.browseraccess.managed"
#define EXPORTER_MGED_BA_FEATURE_ENABLE                                            1
#define EXPORTER_MGED_BA_FEATURE_DISABLE                                           0
#define EXPORTER_MGED_BA_FEATURE_DEFAULT                                           EXPORTER_MGED_BA_FEATURE_DISABLE

#define EXPORTER_MGED_BA_FEATURE_GLOBAL_DISABLE                                    "config.feature.exporter.browseraccess.managed_global_disable"
#define EXPORTER_MGED_BA_FEATURE_GLOBAL_DISABLE_DEFAULT                            0

/* unified portal */
#define EXPORTER_UNIFIED_PORTAL_FEATURE                                            "config.feature.exporter.browseraccess.unifiedportal"
#define EXPORTER_UNIFIED_PORTAL_FEATURE_ENABLE                                     1
#define EXPORTER_UNIFIED_PORTAL_FEATURE_DISABLE                                    0
#define EXPORTER_UNIFIED_PORTAL_FEATURE_DEFAULT                                    EXPORTER_UNIFIED_PORTAL_FEATURE_DISABLE

/* Feature: TLS fingerprinting, Affected Components: Exporter
 * TLS fingerprinting feature is disabled in itasca unless there is a config override
 * UI can enable/disable separately
 */

/* CSP feature config. By default, itasca has feature disabled */
#define EXPORTER_CSP_TIMEOUT_FEATURE                            "config.csp.timeout"
#define EXPORTER_CSP_TIMEOUT_FEATURE_DEFAULT                    1

/* Sync with UI at customer level */
#define EXPORTER_CSP_FEATURE                                    "feature.csp"
#define EXPORTER_CSP_FEATURE_ENABLED                            "enabled"
#define EXPORTER_CSP_FEATURE_DISABLED                           "disabled"
#define EXPORTER_CSP_FEATURE_DEFAULT                            EXPORTER_CSP_FEATURE_DISABLED

/* Window update send config. By default, disable for exporter */
#define EXPORTER_DISABLE_WINDOW_UPDATE_INNER_TUNNEL			"config.feature.exporter.disable_window_update"
#define DEFAULT_EXPORTER_DISABLE_WINDOW_UPDATE_INNER_TUNNEL 0

/* Global disable */
#define EXPORTER_CSP_FEATURE_GLOBAL_DISABLE                     "config.feature.csp.global_disable"
#define EXPORTER_CSP_FEATURE_GLOBAL_DISABLE_DEFAULT             0

/* JA3 flags for CSP */
#define EXPORTER_CSP_FEATURE_JA3                                "config.feature.exporter.csp.ja3"
#define EXPORTER_CSP_FEATURE_JA3_DEFAULT                        1

#define EXPORTER_SSL_ENABLE_PFS_CONFIG                                       "config.ssl.exporter.enable_pfs"
#define EXPORTER_DEFAULT_SSL_ENABLE_PFS_CONFIG                               0

/* Cipher key selection for BA */
#define EXPORTER_SSL_ENABLE_CIPHER_KEY_256                      "config.feature.exporter.cipher256"
#define EXPORTER_DEFAULT_SSL_ENABLE_CIPHER_KEY_256              0
#define EXPORTER_SSL_ENABLE_CIPHER_KEY_256_MIN                  0
#define EXPORTER_SSL_ENABLE_CIPHER_KEY_256_MAX                  1

/* Managed chrome hard disabled */
#define EXPORTER_MANAGED_CHROME_HARD_DISABLED                   "config.feature.exporter.managed-chrome.hard_disabled"
#define EXPORTER_MANAGED_CHROME_HARD_DISABLED_DEFAULT           0 //default disable
#define EXPORTER_MANAGED_CHROME_HARD_DISABLED_MIN               0
#define EXPORTER_MANAGED_CHROME_HARD_DISABLED_MAX               1

/* Managed chrome feature */
#define EXPORTER_MANAGED_CHROME_FEATURE                         "config.feature.exporter.managed-chrome"
#define EXPORTER_MANAGED_CHROME_FEATURE_DEFAULT                 0 //default disable
#define EXPORTER_MANAGED_CHROME_FEATURE_MIN                     0
#define EXPORTER_MANAGED_CHROME_FEATURE_MAX                     1

/* Managed Chrome Expiry time */
#define EXPORTER_MANAGED_CHROME_EXPIRY_IN_S                     "config.feature.exporter.managed-chrome.expiry_time_s"
#define EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_DEFAULT             15*60
#define EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_MIN                 5*60
#define EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_MAX                 30*60

/* Managed chrome-2 feature */
#define EXPORTER_MANAGED_CHROME_2_FEATURE                       "config.feature.exporter.managed-chrome-2"
#define EXPORTER_MANAGED_CHROME_2_FEATURE_DEFAULT               0
#define EXPORTER_MANAGED_CHROME_2_FEATURE_MIN                   0
#define EXPORTER_MANAGED_CHROME_2_FEATURE_MAX                   1

/* Feature: PRA, Affected Components: Exporter, Broker
 * PRA feature is not disabled in itasca unless there is a config override
 * UI can enable/disable separately
 */
#define PRA_FEATURE_GLOBAL_DISABLE                                                   "config.feature.pra_global_disable"
#define DEFAULT_PRA_FEATURE_GLOBAL_DISABLE                                           0

/* PRA feature config disablement per customer. By default, itasca has PRA feature disabled */
#define PRA_FEATURE                                                                  "feature.privilegedRemoteAccess"
#define DEFAULT_PRA_FEATURE                                                          "disabled"

/* Session proctoring global-email-service config. By default, itasca has global-email-service disabled */
#define PRA_SESSION_PROCTORING_GOLBAL_EMAIL_SVC                                     "config.feature.sessionProctoring.use_global_email_svc"
#define DEFAULT_PRA_SESSION_PROCTORING_GOLBAL_EMAIL_SVC                              0

/* Session proctoring email notification feature config enablement per customer. By default, itasca has session proctoring email notification feature disabled */
#define PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE                            "config.feature.sessionProctoring.emailNotification"
#define DEFAULT_PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE                    "disabled"

/* Feature: Privileged policy for credentials flag Affected Components: Exporter */
#define CREDENTIALS_FEATURE_GLOBAL_DISABLE                                           "config.feature.credentials_global_disable"
#define DEFAULT_CREDENTIALS_FEATURE_GLOBAL_DISABLE                                   0


/* Feature: Privileged policy for credentials flag Affected Components: Exporter */
#define PRIVILEGED_CREDENTIALS_FEATURE                                               "feature.privileged.credentials"
#define DEFAULT_PRIVILEGED_CREDENTIALS_FEATURE                                      "disabled"

/* Feature: Privileged policy for credential pool flag Affected Components: Exporter */
#define CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE                                           "config.feature.credentialpool_global_disable"
#define DEFAULT_CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE                                   0


/* Feature: Privileged policy for credentials flag Affected Components: Exporter */
#define PRIVILEGED_CREDENTIAL_POOL_FEATURE                                               "feature.privileged.credentialpool"
#define DEFAULT_PRIVILEGED_CREDENTIAL_POOL_FEATURE                                      "disabled"

/*
 * Feature: URL cookie encryption
 * Components: Exporter
 * By default, itasca has url cookie encryption enabled with the following config set to 1
 */
#define EXPORTER_URL_COOKIE_ENCRYPTION                                               "config.feature.exporter.url_cookie_encryption"
#define DEFAULT_URL_COOKIE_ENCRYPTION                                                1
#define EXPORTER_URL_COOKIE_ENCRYPTION_MIN                                           0
#define EXPORTER_URL_COOKIE_ENCRYPTION_MAX                                           1

#define EXPORTER_LOGIN_HINT_FEATURE                                                  "config.feature.exporter.login_hint"
#define DEFAULT_EXPORTER_LOGIN_HINT_FEATURE                                          "disabled"

/*
 * Feature:    JIT Approval Based Access Policy
 * Components: Broker, Exporter
 * Flag:       feature.approvalBasedAccess
 * Default:    feature.approvalBasedAccess = "disabled"
 * JIT can be disabled either with config.feature.pra_global_disable, config.feature.pra or
 * feature.approvalBasedAccess or feature.approvalBasedAccess.globalDisable
 * TBD later PRA can be removed as a knob to control JIT
 */
#define JIT_APPROVAL_BASED_ACCESS                                                   "feature.approvalBasedAccess"
#define DEFAULT_JIT_APPROVAL_BASED_ACCESS                                           "disabled"

#define APP_PROTECTION_CSP_PROFILE                                                   "config.feature.csp.default_profile"
#define APP_PROTECTION_CSP_PROFILE_DEFAULT_BITMASK                                   0x1F3FFFFF
#define APP_PROTECTION_CSP_PROFILE_DEFAULT_CRITERIA                                  "{\"name\":\"Zs Recommended profile\",\"description\":\"Zs Recommended profile\",\"criteria\":{\"fingerPrintCriteria\":{\"browser\":{\"ja3\":false,\"canvas\":true,\"browser_name\":true,\"browser_version\":true,\"browser_eng\":true,\"browser_eng_ver\":true,\"is_local_storage\":true,\"is_sess_storage\":true,\"is_cookie\":true,\"fp_usr_agent_str\":true,\"mime\":true,\"plugin\":true,\"flash_ver\":true,\"silverlight_ver\":true},\"system\":{\"cpu_arch\":true,\"os_name\":true,\"os_version\":true,\"curr_screen_resolution\":true,\"avail_screen_resolution\":true,\"tz\":true,\"usr_lang\":true,\"sys_lang\":true,\"monitor_mobile\":true,\"mobile_dev_type\":true,\"font\":true,\"java_ver\":true},\"location\":{\"lat\": false,\"lon\": false},\"collect_location\": false, \"fingerprint_timeout\": 31}}}"

/* Global config override to disable jit
 * config.feature.approvalBasedAccess.globalDisable
 * Default: config.feature.approvalBasedAccess.globalDisable = 0 (Jit is not globally disabled)
 * If this flag is 1, regardless of feature.approvalBasedAccess, JIT is disabled everywhere.
 */
#define JIT_FEATURE_GLOBAL_DISABLE                                                   "config.feature.approvalBasedAccess.globalDisable"
#define DEFAULT_JIT_FEATURE_GLOBAL_DISABLE                                           0

/*
 * Feature:    PRA VNC access
 * Components: Exporter
 * Flag:       feature.privileged.vnc
 * Default:    feature.privileged.vnc = "disabled"
 * PRA can be disabled either with config.feature.pra_global_disable, config.feature.pra or
 * feature.privileged.vnc or config.feature.vnc.globalDisable
 * TBD later PRA can be removed as a knob to control JIT
 */
#define PRA_VNC_ACCESS                                                              "feature.privileged.vnc"
#define DEFAULT_PRA_VNC_ACCESS                                                      "disabled"

/* Global config override to disable PRA VNC
 * config.feature.vnc.globalDisable
 * Default: config.feature.vnc.globalDisable = 1 (PRA VNC is globally disabled)
 * If this flag is 1, regardless of feature.privileged.vnc, PRA VNC is disabled everywhere.
 */
#define PRA_VNC_GLOBAL_DISABLE                                                       "config.feature.vnc.globalDisable"
#define DEFAULT_PRA_VNC_GLOBAL_DISABLE                                               0

/*
 * Feature:    Markdown content
 * Components: Exporter
 * Flag:       feature.privileged.markdowncontent"
 * Default:    feature.privileged.markdowncontent" = "disabled"
 */
#define PORTAL_MARKDOWN_CONTENT                                                              "feature.privileged.markdowncontent"
#define DEFAULT_PORTAL_MARKDOWN_CONTENT_ENABLED                                              "enabled"
#define DEFAULT_PORTAL_MARKDOWN_CONTENT_DISABLED                                             "disabled"
#define DEFAULT_PORTAL_MARKDOWN_CONTENT                                                      DEFAULT_PORTAL_MARKDOWN_CONTENT_DISABLED

/*
 * Feature:    PRA REALVNC access
 * Components: Exporter
 * Flag:       feature.privileged.realvnc
 * Default:    feature.privileged.realvnc = "disabled"
 * PRA can be disabled either with config.feature.pra_global_disable, config.feature.pra or
 * feature.privileged.realvnc or config.feature.realvnc.globalDisable
 */
#define PRA_REALVNC_ACCESS                                                              "feature.privileged.realvnc"
#define DEFAULT_PRA_REALVNC_ACCESS                                                      "disabled"

/* Global config override to disable PRA REALVNC
 * config.feature.realvnc.globalDisable
 * Default: config.feature.realvnc.globalDisable = 1 (PRA REALVNC is globally disabled)
 * If this flag is 1, regardless of feature.privileged.realvnc, PRA REALVNC is disabled everywhere.
 */
#define PRA_REALVNC_GLOBAL_DISABLE                                                       "config.feature.realvnc.globalDisable"
#define DEFAULT_PRA_REALVNC_GLOBAL_DISABLE                                               0

/*
 * Feature:    File Transfer
 * Components: Exporter
 * Flag:       feature.privileged.filetransfer
 * Default:    feature.privileged.filetransfer = "disabled"
 * File Transfer is disabled either with feature.privileged.filetransfer or config.feature.privileged.filetransfer.globalDisable
 */
#define FILE_TRANSFER_FEATURE_GLOBAL_DISABLE                             "config.feature.privileged.filetransfer.globalDisable"
#define DEFAULT_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE                     0
#define MAX_ZIA_SCAN_FILE_SIZE                                           20
#define MAX_TRANSFER_FILE_SIZE                                           50
#define MAX_TRANSFER_FILE_SIZE_RANGE_HI                                  100
#define FILE_TRANSFER                                                    "feature.privileged.filetransfer"
#define DEFAULT_FILE_TRANSFER                                            "disabled"
#define FILE_SCAN_MAX_SIZE                                               "config.feature.privileged.filetransfer.scanMaxSize"
#define FILE_TRANSFER_MAX_SIZE                                           "config.feature.privileged.filetransfer.transferMaxSize"
#define FILE_TRANSFER_FILE_SCAN_EXCEPTION_LIST                           "config.feature.privileged.filetransfer.scanException"

/*
 * Feature:    Faster File Transfer
 * Components: Exporter
 * Flag:       feature.privileged.fasterFileTransfer
 * Default:    feature.privileged.fasterFileTransfer = "disabled"
 * Faster File Transfer is disabled with feature.fasterFileTransfer
 */
#define FASTER_FILE_TRANSFER                                             "feature.privileged.fasterFileTransfer"
#define DEFAULT_FASTER_FILE_TRANSFER                                     "disabled"

/* Feature: PRA, Affected Components: Exporter
 * GUACD Service feature is not disabled in itasca unless there is a config override
 * UI can enable/disable separately
 */
#define PRA_GUACD_SERVICE_GLOBAL_DISABLE                             "config.feature.guacdservice.globalDisable"
#define DEFAULT_PRA_GUACD_SERVICE_GLOBAL_DISABLE                     0

#define PRA_GUACD_SERVICE_ZPA_CPU_LOAD_LIMIT                         "config.feature.guacdservice.cpuLoadLimit"
#define PRA_GUACD_SERVICE_ZPA_MEM_LOAD_LIMIT                         "config.feature.guacdservice.memLoadLimit"
#define PRA_GUACD_SERVICE_ZPA_MAX_SESSIONS_LIMIT                     "config.feature.guacdservice.maxSessionsLimit"
#define PRA_HEALTH_CHECK_ENABLE                                      "config.feature.privileged.healthCheck"

#define DEFAULT_CPU_LOAD_LIMIT                                       30  /* system loadavg */
#define DEFAULT_MEM_LOAD_LIMIT                                       85  /* percent usage */
#define DEFAULT_MAX_SESSIONS_LIMIT                                   -1  /* computed based on cpu cores and mem */
#define PRA_MAX_SESSIONS_LIMIT_HI                                    20000

#define DEFAULT_PRA_HEALTH_CHECK_ENABLE                              0 /* PRA health checks disabled. Normal LB health check 200 responses */
#define PRA_HEALTH_CHECK_ENABLE_HI                                   1 /* LB health check responses based on PRA limits. 503 responses for overload */
#define PRA_SESSION_PROCTORING_ZPA_MAX_JOIN_USERS_LIMIT              "config.feature.sessionproctoring.maxJoinUsersLimit"
#define DEFAULT_PRA_SESSION_PARTICIPANT_JOIN_COUNT                   10  // MAX_COUNT is 10

#define PRA_CREDENTIAL_POOL_API_RETRY_LIMIT                          "config.feature.credentialpool.apiRetryLimit"
#define DEFAULT_PRA_CREDENTIAL_POOL_API_RETRY_LIMIT                  10
#define PRA_CREDENTIAL_POOL_API_RETRY_LOW                            0
#define PRA_CREDENTIAL_POOL_API_RETRY_HI                             100

/*
 * Feature:    Arbitrary domains
 * Components: Exporter
 * Flag:       feature.arbitraryAuthDomains
 * Default:    feature.arbitraryAuthDomains = "disabled"
 * Arbitrary domains is disabled either with feature.arbitraryAuthDomains or feature.arbitraryAuthDomains.suppressed
 */
#define ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBALLY_SUPPRESSED "feature.arbitraryAuthDomains.suppressed"
#define DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBAL_DISABLE "disabled"
#define ARBITRARY_AUTH_DOMAIN_FEATURE "feature.arbitraryAuthDomains"
#define DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE  "disabled"

/*
 * Feature:    Enables/disables sending PRA log data from exporter to broker
 * Components: Exporter
 * Flag:       config.feature.diags_log_exporter_rpc
 * Default:    config.feature.diags_log_exporter_rpc_global_disable = "0"
 * sending PRA log data from exporter to broker is disabled either with config.feature.diags_log_exporter_rpc or config.feature.diags_log_exporter_rpc_global_disable
 */
#define DIAGS_LOG_RPC_GLOBAL_DISABLE "config.feature.diags_log_exporter_rpc_global_disable"
#define DEFAULT_DIAGS_LOG_RPC_GLOBAL_DISABLE 0
#define DIAGS_LOG_RPC "config.feature.diags_log_exporter_rpc"
#define DEFAULT_DIAGS_LOG_RPC  1


/*
 * Feature:    Clipboard
 * Components: Exporter
 * Flag:       feature.privileged.clipboard
 * Default:    feature.privileged.clipboard = "disabled"
 * Clipboard is disabled either with feature.privileged.clipboard or config.feature.privileged.clipboard.globalDisable
 */
#define CLIPBOARD_FEATURE_GLOBAL_DISABLE                                 "config.feature.privileged.clipboard.globalDisable"
#define DEFAULT_CLIPBOARD_FEATURE_GLOBAL_DISABLE                         0
#define CLIPBOARD_FEATURE                                                "feature.privileged.clipboard"
#define DEFAULT_CLIPBOARD                                                "disabled"
#define CLIPBOARD_MAX_SIZE                                               "config.feature.privileged.clipboard.maxSize"
#define CLIPBOARD_SIZE_VALUE                                             8192
#define DEFAULT_CLIPBOARD_SIZE                                           4096

/*
 * Feature:    UX improvements - display quality and resolution
 * Components: Exporter
 * Flag:       feature.privileged.displayquality
 * Default:    feature.privileged.displayquality = "disabled"
 * UX improvements is disabled either with feature.privileged.displayquality or
 * feature.privileged.displayquality.globalDisable
 */
#define UX_DISPLAY_QUALITY                                               "feature.privileged.displayquality"
#define DEFAULT_UX_DISPLAY_QUALITY                                       "disabled"
#define UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE                        "config.feature.privileged.displayquality.globalDisable"
#define DEFAULT_UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE                0

/*
 * Feature:    User-initiated approvals for PRA
 * Components: Exporter
 * Flag:       feature.privileged.endUserApprovals
 * Default:    feature.privileged.endUserApprovals = "disabled"
 * user-initiated approvals is disabled either with feature.privileged.endUserApprovals or
 * feature.privileged.endUserApprovals.globalDisable
 */
#define ENDUSER_APPROVALS                                                "feature.privileged.endUserApprovals"
#define DEFAULT_ENDUSER_APPROVALS                                        "disabled"
#define ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE                         "config.feature.privileged.endUserApprovals.globalDisable"
#define DEFAULT_ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE                 0

/*
 * Feature:    Session Recording
 * Components: Exporter
 * Flag:       feature.privileged.sessionrecording
 * Default:    feature.privileged.sessionrecording = "disabled"
 * Session Recording is disabled either with feature.privileged.sessionrecording or
 * feature.privileged.sessionrecording.globalDisable
 */
#define SESSION_RECORDING                                                "feature.privileged.sessionrecording"
#define DEFAULT_SESSION_RECORDING                                        "disabled"
#define SESSION_RECORDING_FEATURE_GLOBAL_DISABLE                         "config.feature.privileged.sessionrecording.globalDisable"
#define DEFAULT_SESSION_RECORDING_FEATURE_GLOBAL_DISABLE                 0

/*
 * Feature:    Session Sharing
 * Components: Exporter
 * Flag:       feature.privileged.sessionmonitoring
 * Default:    feature.privileged.sessionmonitoring = "disabled"
 * Session Sharing is disabled either with feature.privileged.sessionmonitoring or
 * config.feature.sessionmonitoring.globalDisable
 */

#define SESSION_MONITORING                                                "feature.privileged.sessionmonitoring"
#define DEFAULT_SESSION_MONITORING                                        "disabled"
#define SESSION_MONITORING_FEATURE_GLOBAL_DISABLE                         "config.feature.sessionmonitoring.globalDisable"
#define DEFAULT_SESSION_MONITORING_FEATURE_GLOBAL_DISABLE                 0

/*
 * Feature:    Privileged Desktops
 * Components: Exporter
 * Flag:       feature.privileged.desktops
 * Default:    feature.privileged.desktops = "disabled"
 * Privileged Desktops is disabled either with feature.privileged.desktops or
 * config.feature.privilegeddesktops.globalDisable
 */
 #define PRIVILEGED_DESKTOPS                                               "feature.privileged.desktops"
 #define DEFAULT_PRIVILEGED_DESKTOPS                                       "disabled"
 #define PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE                        "config.feature.privilegeddesktops.globalDisable"
 #define DEFAULT_PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE                0

/*
 * Global config override to disable DTA-PRA
 * config.feature.pra.dta.globalDisable
 * Default: config.feature.pra.dta.globalDisable = 0 (DTA-PRA is not globally disabled)
 * If this flag is 1, DTA-PRA is disabled everywhere.
 */
#define DELEGATED_ADMIN_PRA_GLOBAL_DISABLE                                  "config.feature.pra.dta.globalDisable"
#define DEFAULT_DELEGATED_ADMIN_PRA_GLOBAL_DISABLE                          0

/*
 * Limit the number of concurrent large PRA portal access - scope API and console API calls
 * If this flag is 0, all PRA portals that have more than 100 micro-tenants or more than 1000 consoles
 * access for large portals will be disabled.
 */
#define PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS                                  "config.feature.pra.ratelimit.largePortalAccess"
#define DEFAULT_PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS                          4

/*
 * Feature:    PRA Advanced File Transfer
 * Components: Exporter
 * Flag:       feature.privileged.myfiles
 * Default:    feature.privileged.myfiles = "disabled"
 * PRA advanced file transfer is disabled either with feature.privileged.myfiles or
 * config.feature.myfiles.globalDisable
 */

#define PRA_ADVANCED_FILE_TRANSFER                                        "feature.privileged.myfiles"
#define DEFAULT_ADVANCED_FILE_TRANSFER                                    "disabled"
#define ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE                     "config.feature.myfiles.globalDisable"
#define DEFAULT_ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE             0

#define PRA_PORTAL_FILE_TRANSFER                                          "feature.privileged.portalFileTransfer"
#define DEFAULT_PRA_PORTAL_FILE_TRANSFER                                  0

/*
 * Feature: Reduce client wait time during core, Affected Components: Exporter
 * Parameters for config-override selection for extra actions on crash for exporter
 */
#define ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY                             "config.feature.exporter.on_crash_do"
#define ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_DEFAULT                         zthread_on_crash_do_close_all_sockets
#define ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY_MIN                           0
#define ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY_MAX                           2

/*
 * Feature: Reduce client wait time during core, Affected Components: Broker
 * Parameters for config-override selection for extra actions on crash for broker
 */
#define ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY                               "config.feature.broker.on_crash_do"
#define ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_DEFAULT                           zthread_on_crash_do_close_all_sockets
#define ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY_MIN                           0
#define ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY_MAX                           2

/*
 * Feature: Slogger support for broker redirect, Affected Components: Slogger
 * Feature flag: "config.feature.slogger.redirect_mode"
 * Values:
 *   SLOGGER_REDIRECT_MODE_DISABLED:    0: redirects not supported.
 *   SLOGGER_REDIRECT_MODE_FORCE:       1: only respond to forced redirects (e.g. broker shutting down).
 *   SLOGGER_REDIRECT_MODE_FULL:        2: support initial redirect and any subsequent redirects, regardless of redirect's attribute.
 */
#define SLOGGER_REDIRECT_FEATURE_OVERRIDE                                            "config.feature.slogger.redirect_mode"
#define SLOGGER_REDIRECT_MODE_DEFAULT                                                1
#define SLOGGER_REDIRECT_MODE_MIN                                                    0
#define SLOGGER_REDIRECT_MODE_MAX                                                    2

/*
 * Feature : Slogger ack delay timeout in micro seconds, Affected Components: Slogger
 */
#define SLOGGER_ACK_DELAY_TIMEOUT_US_CONFIG_OVERRIDE                                 "config.feature.slogger.ack_delay_timeout_us"
#define SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT                                         (5*1000*1000)   // 5 seconds
#define SLOGGER_ACK_DELAY_TIMEOUT_US_MIN                                             (1*1000*1000)   // 1 seconds
#define SLOGGER_ACK_DELAY_TIMEOUT_US_MAX                                             (300*1000*1000) // 300 seconds

/*
 * Feature : Pbroker admin probe, Affected Components: PB
 */
#define PBROKER_ADMIN_PROBE_FEATURE_ALL                                              "config.feature.pbroker.admin_probe.all"
#define DEFAULT_PBROKER_ADMIN_PROBE_ALL_ENABLED                                      1  /* Admin probe is enabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_ALL_MIN                                                0
#define DEFAULT_PBROKER_ADMIN_PROBE_ALL_MAX                                               1

#define PBROKER_ADMIN_PROBE_FEATURE_RESTART_PROCESS                                  "config.feature.pbroker.admin_probe.restart_process"
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_ENABLED                          0  /* Admin probe on resatrt process is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_MIN                                    0
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_MAX                                   1

#define PBROKER_ADMIN_PROBE_FEATURE_RESTART_SYSTEM                                   "config.feature.pbroker.admin_probe.restart_system"
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_ENABLED                           0  /* Admin probe on resatrt system is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_MIN                                    0
#define DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_MAX                                   1

#define PBROKER_ADMIN_PROBE_FEATURE_DNS                                              "config.feature.pbroker.admin_probe.dns"
#define DEFAULT_PBROKER_ADMIN_PROBE_DNS_ENABLED                                      0  /* Admin probe on resatrt system is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_DNS_MIN                                               0
#define DEFAULT_PBROKER_ADMIN_PROBE_DNS_MAX                                              1

#define PBROKER_ADMIN_PROBE_FEATURE_ICMP                                             "config.feature.pbroker.admin_probe.icmp"
#define DEFAULT_PBROKER_ADMIN_PROBE_ICMP_ENABLED                                     0  /* Admin probe feature for icmp command is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_ICMP_MIN                                              0
#define DEFAULT_PBROKER_ADMIN_PROBE_ICMP_MAX                                             1

#define PBROKER_ADMIN_PROBE_FEATURE_TCP                                              "config.feature.pbroker.admin_probe.tcp"
#define DEFAULT_PBROKER_ADMIN_PROBE_TCP_ENABLED                                      0  /* Admin probe feature for tcp command is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_TCP_MIN                                               0
#define DEFAULT_PBROKER_ADMIN_PROBE_TCP_MAX                                              1

#define PBROKER_ADMIN_PROBE_FEATURE_TCPDUMP                                          "config.feature.pbroker.admin_probe.tcpdump"
#define DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_ENABLED                                  0  /* Admin probe feature for tcpdump command is disabled by default */
#define DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_MIN                                           0
#define DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_MAX                                          1

/*
 * Feature: Connector support for avg_rtt in the health message to dispatcher. Affected Components: Connector
 * Feature flag: "config.feature.connector.samples_for_avg_rtt"
 */

#define CONNECTOR_SAMPLES_FOR_AVG_RTT                                                "config.feature.connector.samples_for_avg_rtt"
#define CONNECTOR_SAMPLES_FOR_AVG_RTT_DEFAULT                                        10
#define LOW_CONNECTOR_SAMPLES_FOR_AVG_RTT                                            1
#define HIGH_CONNECTOR_SAMPLES_FOR_AVG_RTT                                           30


/*
 * Feature : zhealth_probe_lib configure socket engine
 */
#define ZHEALTH_PROBE_LIB_CONFIG_FEATURE_BROKER_SOCKET_ENGINE                        "config.feature.broker.zhealth_probe_lib.socket_engine"
#define ZHEALTH_PROBE_LIB_CONFIG_FEATURE_PBROKER_SOCKET_ENGINE                       "config.feature.pbroker.zhealth_probe_lib.socket_engine"
#define ZHEALTH_PROBE_LIB_CONFIG_FEATURE_CONNECTOR_SOCKET_ENGINE                     "config.feature.connector.zhealth_probe_lib.socket_engine"
#define ZHEALTH_PROBE_LIB_CONFIG_FEATURE_SOCKET_ENGINE_DEFAULT                       1

/* Feature : Dns check route optimization
 * This config enables broker to optimize the zpn_dns_assistant check route.
 * The zpn_dns_assistant_check is sent to the dispatcher that requested it instead of the entire pool.
 */
#define BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE                          "config.feature.broker.dns_assistant_check_unicast"
#define DEFAULT_BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE                   1
#define BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE_MIN                       0
#define BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE_MAX                       1

/* Feature : TXT query support through ZPA
 * This config enables ZPA to support clients to resolve TXT type DNS query
 */
#define DNS_TXT_QUERY_SUPPORT_FEATURE              "config.feature.dns_txt_query_support"
#define DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE      1
#define DNS_TXT_QUERY_SUPPORT_FEATURE_MIN          0
#define DNS_TXT_QUERY_SUPPORT_FEATURE_MAX          1

/* Option to enable pb client connection monitor for troubleshooting and configure its interval.
 */
#define ZPN_PB_CLIENT_CONNECTION_MONITOR                                  "config.feature.pb.client_conn_monitor.enable"
#define DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR                          0
#define ZPN_PB_CLIENT_CONNECTION_MONITOR_MIN                                    0
#define ZPN_PB_CLIENT_CONNECTION_MONITOR_MAX                                   1

#define ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL                         "config.feature.pb.client_conn_monitor.interval_sec"
#define DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL                          240
#define ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL_MIN                                    60
#define ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL_MAX                                  (60*60)

/* Option to enable malloc trim on pse and configure its interval.
 */
#define ZPN_PSE_MALLOC_TRIM_ENABLE                                      "config.pse.malloc_trim.enable"
#define DEFAULT_ZPN_PSE_MALLOC_TRIM_ENABLE                              0
#define ZPN_PSE_MALLOC_TRIM_ENABLE_MIN                                  0
#define ZPN_PSE_MALLOC_TRIM_ENABLE_MAX                                  1

#define ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS                              "config.pse.malloc_trim.schedule.days"
#define DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_DAYS                      "0"

#define ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME                              "config.pse.malloc_trim.schedule.time"
#define DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_TIME                      "03:00:00"

#define ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME                       "config.pse.malloc_trim.second.schedule.time"
#define DEFAULT_ZPN_PSE_MALLOC_TRIM_SECOND_SCHEDULE_TIME               "03:00:00"

#define ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC                          "config.pse.malloc_trim.schedule.rand_sec"
#define DEFAULT_ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC                  900
#define ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC_MIN                      0
#define ZPN_PSE_MALLOC_TRIM_SCHEDULE_RAND_SEC_MAX                      900
/*
Feature : Step up auth
*/
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_ENABLED                                     "config.feature.stepup_auth.enabled"
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_DEFAULT                                     0
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_MIN                                         0
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_MAX                                         1


#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED                               "config.feature.stepup_auth.hard_disabled"
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_DEFAULT                       0 //not hard disabled by default
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_MIN                           0
#define ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_MAX                           1

/*
 * Feature: Broker auth state log
 */
#define BROKER_FEATURE_AUTH_STATE_LOG                                                "config.feature.client.authstate.log"
#define DEFAULT_BROKER_FEATURE_AUTH_STATE_LOG                                        0
#define BROKER_FEATURE_AUTH_STATE_LOG_MIN                                            0
#define BROKER_FEATURE_AUTH_STATE_LOG_MAX                                            1

/*
 * Feature: Broker strict DNS checking
 */
#define BROKER_FEATURE_DNS_STRICT_CHECK                                               "config.feature.broker.dns_strict_check.enabled"
#define DEFAULT_BROKER_FEATURE_DNS_STRICT_CHECK                                       1  /* strict dns check enabled by default on restart */
#define BROKER_FEATURE_DNS_STRICT_CHECK_MIN                                           0
#define BROKER_FEATURE_DNS_STRICT_CHECK_MAX                                           1

/*
 * Feature: Broker to slogger queue shrinkage timer
 */
#define BROKER_FEATURE_SIEM_QUEUE_SHRINKAGE                                           "config.feature.broker.siem_queue_shrink_time.interval_sec"
#define BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_DEFAULT                        (900)
#define BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MIN                            (0)
#define BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MAX                            (7*24*60*60)

/*
 * Feature: Geoip: mmdb file downloads
 * If it is disabled, the mmdb file download on pb should stop
 */
#define ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE                                         "config.feature.pb.client.geoip.disable"
#define DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE                                 0
#define DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_MIN                                         0
#define DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_MAX                                        1

#define ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE                                         "config.feature.sitec.client.geoip.disable"
#define DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_DISABLE                                 0
#define DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_MIN                                     0
#define DEFAULT_ZPN_SITEC_GEOIP_MMDB_DOWNLOAD_MAX                                     1

/*
 * ZIA inspection feature, disable.
 */
#define OV_ZIA_INSPECTION_CATEGORY  "ZIA Inspection"
// global hard disable: broker, PSE, global
#define ZIA_INSPECTION_DISABLE                          "config.feature.zia_inspection.hard_disable"
#define ZIA_INSPECTION_DISABLE_DEFAULT                   0
#define ZIA_INSPECTION_DISABLE_MIN                       0
#define ZIA_INSPECTION_DISABLE_MAX                       1

#define ZIA_INSPECTION_IDLE_TIMEOUT_S                   "config.feature.zia_inspection.idle_timeout_s"
#define ZIA_INSPECTION_IDLE_TIMEOUT_S_DEFAULT           3600


/* ZIA Inspection 3.0 */
// enable individual customers
#define ZINS_FEATURE                                     "config.feature.zia_inspection.customer"
#define ZINS_DEFAULT_FEATURE                             1

/*
 * Feature: ET-38414 to not delay rcode!=0 dns response in dns check flow
 *
 * CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE is the flag to enable ET-38414 behaviour
 *
 * Before ET-38414:
 * -----------------
 * connector delay sending a error dns response by 8 second(default) when the dns response is:
 * nxdomain/NotImp/YXDomain/YXRRSet/NXRRSet/NXRRSet/NotAuth/NotZone/DSOTYPENI/BADVERS/BADSIG/BADKEY/BADTIME/BADMODE/BADNAME/BADALG/BADTRUNC/BADCOOKIE
 * upon receiving a error dns resposne, broker is adding another delay (50ms) to forward the response back to zcc.
 *
 * reason on the delay broker is doing there is there might be scenario where there are 2 connectors in the same connector group,
 * one connector can resolve the domain while the other connector can not.
 * if the error dns response come back faster than the good dns response, broker wants to delay the error one for a bit inorder to forward back the good dns response first.
 *
 * after ET-38414:
 * -----------------
 * there is no more delay on connector side when the dns response is:
 * nxdomain/NotImp/YXDomain/YXRRSet/NXRRSet/NXRRSet/NotAuth/NotZone/DSOTYPENI/BADVERS/BADSIG/BADKEY/BADTIME/BADMODE/BADNAME/BADALG/BADTRUNC/BADCOOKIE
 *
 * but if we are not delaying connector side, its better we extend the delay on the broker side holding the dns error response long
 */
#define BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US                                      "config.feature.broker.dns_check.error.delay_us"
#define BROKER_FEATURE_ZVPN_DNS_CHECK_ERROR_DELAY_US                                 "config.feature.broker.zvpn.dns_check.error.delay_us"
#define DEFAULT_BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US                               (50 * 1000)
#define BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US_MIN_US                                (25 * 1000)
#define BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US_MAX_US                                (150 * 1000)

#define CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE                                       "config.feature.connector.zcdns.disable_err_rcode_retry"
#define DEFAULT_CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE                               0

#define FEATURE_BRANCH_CONNECTOR_ENABLED                                              "config.feature.branch_connector.enabled"
#define DEFAULT_FEATURE_BRANCH_CONNECTOR_ENABLED                                      1

#define FEATURE_BRANCH_CONNECTOR_HARD_DISABLED                                        "config.feature.branch_connector.hard_disabled"
#define DEFAULT_FEATURE_BRANCH_CONNECTOR_HARD_DISABLED                                0

#define FEATURE_VDI_ENABLED                                                           "config.feature.vdi.enabled"
#define DEFAULT_FEATURE_VDI_ENABLED                                                   0

#define FEATURE_VDI_HARD_DISABLED                                                     "config.feature.vdi.hard_disabled"
#define DEFAULT_FEATURE_VDI_HARD_DISABLED                                             0

#define BROKER_FEATURE_MALLOC_FASTBIN                                                "config.feature.broker.malloc_fastbin"
#define PBROKER_FEATURE_MALLOC_FASTBIN                                               "config.feature.pse.malloc_fastbin"
#define BROKER_FEATURE_MALLOC_FASTBIN_ENABLED                                         128
#define BROKER_FEATURE_MALLOC_FASTBIN_DISABLED                                        0
#define BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT                                         (BROKER_FEATURE_MALLOC_FASTBIN_ENABLED)
/*
 * 15 minutes to wait for a dispatcher to be fully 'alive'
 *
 * 1. Give 300 seconds for all other brokers to connect to the dispatcher. Even then, some
 * brokers may not be connected to the broker, but those are, hmm lets not bother about those.
 * Why 300s? refer BACKOFF_MAX_S as that is some number we just choose to wait for.
 * 2. 300 seconds is the maximum time interval of a health message from connector(refer zhealth.c:launch_target().
 * 3. We want to give some delta time period for the dispatcher to be 'alive'. Without this the dispatcher can get
 * into a condition where it _just_ missed a health message on app exactly at the timer of reboot and after 300
 * seconds it still haven't received the next update as below,
 *
 *  t : dispatcher is rebooted.
 *  t + 300 : health message on app#1 arrived in broker, we didn't send to dispatcher.
 *  t + 300 : This broker connected
 *  t + 600 : next health message of app#1 is in-flight.
 *  t + 600 : dispatcher is made alive, but it still don't have the health of app#1.
 *
 * To avoid the problem in t+600, we should give more delta time for the broker to make dispatcher alive. Lets throw
 * in another 300s.
 * Not to be used in production
 */
#define ZPN_DISPATCHER_HOLD_TIME                                                    "config.feature.broker.dispatcher_hold_time"
#define DEFAULT_ZPN_DISPATCHER_HOLD_TIME                                            (300)+(300)+(300)
#define ZPN_DISPATCHER_HOLD_TIME_MIN_SECS                                           (1 * 60)
#define ZPN_DISPATCHER_HOLD_TIME_MAX_SECS                                           (20 * 60)

#define DSP_DISPATCH_RTT_THRESHOLD_US                                               "config.dispatcher.dispatch_rtt_threshold_us"
#define DEFAULT_DSP_DISPATCH_RTT_THRESHOLD_US                                        2000
#define MIN_DSP_DISPATCH_RTT_THRESHOLD_US                                            1
#define MAX_DSP_DISPATCH_RTT_THRESHOLD_US                                            100000

#define DSP_DISPATCH_RTT_DYNAMIC_THRESHOLD_PCT                                      "config.dispatcher.dispatch_rtt_dynamic_threshold_pct"
#define DEFAULT_DSP_DISPATCH_RTT_DYNAMIC_THRESHOLD_PCT                               20
#define MIN_DSP_DISPATCH_RTT_DYNAMIC_THRESHOLD_PCT                                   1
#define MAX_DSP_DISPATCH_RTT_DYNAMIC_THRESHOLD_PCT                                   100

#define DSP_DISPATCH_DIST_THRESHOLD_MI                                              "config.dispatcher.dispatch_dist_threshold_mi"
#define DEFAULT_DSP_DISPATCH_DIST_THRESHOLD_MI                                      100
#define MIN_DSP_DISPATCH_DIST_THRESHOLD_MI                                          1
#define MAX_DSP_DISPATCH_DIST_THRESHOLD_MI                                          500

#define DSP_DISCOVERY_AST_TARGET_LIMIT                                              "config.dispatcher.discovery_ast_target_limit"
#define DEFAULT_DSP_DISCOVERY_AST_TARGET_LIMIT                                      6000
#define MIN_DSP_DISCOVERY_AST_TARGET_LIMIT                                          1
#define MAX_DSP_DISCOVERY_AST_TARGET_LIMIT                                          6000

#define DSP_DISCOVERY_TIMEOUT_S                                                     "config.dispatcher.discovery_timeout_s"
#define DEFAULT_DSP_DISCOVERY_TIMEOUT_S                                             7
#define MIN_DSP_DISCOVERY_TIMEOUT_S                                                 1
#define MAX_DSP_DISCOVERY_TIMEOUT_S                                                 300

#define DSP_DISCOVERY_RENEWAL_S                                                     "config.dispatcher.discovery_renewal_s"
#define DEFAULT_DSP_DISCOVERY_RENEWAL_S                                             900
#define MIN_DSP_DISCOVERY_RENEWAL_S                                                 1
#define MAX_DSP_DISCOVERY_RENEWAL_S                                                 3600

#define DSP_DISCOVERY_EXPIRATION_S                                                  "config.dispatcher.discovery_expiration_s"
#define DEFAULT_DSP_DISCOVERY_EXPIRATION_S                                          (DEFAULT_DSP_DISCOVERY_RENEWAL_S * 2)
#define MIN_DSP_DISCOVERY_EXPIRATION_S                                              1
#define MAX_DSP_DISCOVERY_EXPIRATION_S                                              3600

#define DSP_DISCOVERY_TIMEOUT_CACHE_S                                               "config.dispatcher.discovery_timeout_cache_s"
#define DEFAULT_DSP_DISCOVERY_TIMEOUT_CACHE_S                                       120
#define MIN_DSP_DISCOVERY_TIMEOUT_CACHE_S                                           1
#define MAX_DSP_DISCOVERY_TIMEOUT_CACHE_S                                           300

#define DSP_DNS_POSITIVE_CACHE_S                                                    "config.dispatcher.dns_positive_cache_s"
#define DEFAULT_DSP_DNS_POSITIVE_CACHE_S                                            1800
#define MIN_DSP_DNS_POSITIVE_CACHE_S                                                1
#define MAX_DSP_DNS_POSITIVE_CACHE_S                                                3600

#define DSP_DNS_NEGATIVE_CACHE_S                                                    "config.dispatcher.dns_negative_cache_s"
#define DEFAULT_DSP_DNS_NEGATIVE_CACHE_S                                            120
#define MIN_DSP_DNS_NEGATIVE_CACHE_S                                                1
#define MAX_DSP_DNS_NEGATIVE_CACHE_S                                                300

#define DSP_DNS_TIMEOUT_S                                                           "config.dispatcher.dns_timeout_s"
#define DEFAULT_DSP_DNS_TIMEOUT_S                                                   10
#define MIN_DSP_DNS_TIMEOUT_S                                                       1
#define MAX_DSP_DNS_TIMEOUT_S                                                       60

#define DSP_DNS_MAX_CHECKS_PER_AST_GROUP                                            "config.dispatcher.dns_max_checks_per_ast_group"
#define DEFAULT_DSP_DNS_MAX_CHECKS_PER_AST_GROUP                                    1
#define MIN_DSP_DNS_MAX_CHECKS_PER_AST_GROUP                                        DEFAULT_DSP_DNS_MAX_CHECKS_PER_AST_GROUP
#define MAX_DSP_DNS_MAX_CHECKS_PER_AST_GROUP                                        100

#define DSP_SESSION_CACHE_S                                                         "config.dispatcher.session_cache_s"
#define DEFAULT_DSP_SESSION_CACHE_S                                                 1800
#define MIN_DSP_SESSION_CACHE_S                                                     1
#define MAX_DSP_SESSION_CACHE_S                                                     3600

#define DSP_AST_PREF_CACHE_S                                                        "config.dispatcher.ast_pref_cache_s"
#define DEFAULT_DSP_AST_PREF_CACHE_S                                                1800
#define MIN_DSP_AST_PREF_CACHE_S                                                    1
#define MAX_DSP_AST_PREF_CACHE_S                                                    3600

#define DSP_MAX_REG_LIFETIME_S                                                      "config.dispatcher.max_reg_lifetime_s"
#define DEFAULT_DSP_MAX_REG_LIFETIME_S                                              3600
#define MIN_DSP_MAX_REG_LIFETIME_S                                                  1
#define MAX_DSP_MAX_REG_LIFETIME_S                                                  3600

#define DSP_MAX_C2C_REG_LIFETIME_S                                                  "config.dispatcher.max_c2c_reg_lifetime_s"
#define DEFAULT_DSP_MAX_C2C_REG_LIFETIME_S                                          3600
#define MIN_DSP_MAX_C2C_REG_LIFETIME_S                                              1
#define MAX_DSP_MAX_C2C_REG_LIFETIME_S                                              3600

#define DSP_MAX_HEALTH_LIFETIME_S                                                   "config.dispatcher.max_health_lifetime_s"
#define DEFAULT_DSP_MAX_HEALTH_LIFETIME_S                                           3600
#define MIN_DSP_MAX_HEALTH_LIFETIME_S                                               1
#define MAX_DSP_MAX_HEALTH_LIFETIME_S                                               3600

#define DSP_EXPIRATION_WINDOW_S                                                     "config.dispatcher.expiration_window_s"
#define DEFAULT_DSP_EXPIRATION_WINDOW_S                                             3600
#define MIN_DSP_EXPIRATION_WINDOW_S                                                 1
#define MAX_DSP_EXPIRATION_WINDOW_S                                                 3600

#define CONFIG_OVR_C_DISPATCHER_CATEGORY                                            "C Dispatcher"


/*
 * Feature: Customer-DR, Affected Components: PB, Connector
 */

/* To enable/disable DR DNS check during cloud mode. flag: 1 = feature enabled, 0 = feature disabled */
#define ZPN_DR_CONFIG_DNS_CHECK                                         "config.feature.dr.dns_check.enabled"
#define ZPN_DR_CONFIG_DNS_CHECK_DEFAULT                                 0      // i.e. disabled by default
#define ZPN_DR_CONFIG_DNS_CHECK_MIN                                           0
#define ZPN_DR_CONFIG_DNS_CHECK_MAX                                          1

#define ZPN_DR_CONFIG_DNS_CHECK_INTERVAL_S                              "config.feature.dr.dns_check.interval_s"
#define ZPN_DR_CONFIG_DNS_CHECK_INTERVAL_S_DEFAULT                      30      // i.e. 30 secs by default
#define ZPN_DR_CONFIG_DNS_CHECK_INTERVAL_S_MIN                               10
#define ZPN_DR_CONFIG_DNS_CHECK_INTERVAL_S_MAX                              120

#define ZPN_DR_CONFIG_DNS_TXT_DONT_CACHE                                "config.feature.dr.dns_txt.skip_cache"
#define ZPN_DR_CONFIG_DNS_TXT_DONT_CACHE_DEFAULT                        0      // i.e. disabled by default
#define ZPN_DR_CONFIG_DNS_TXT_DONT_CACHE_MIN                                 0
#define ZPN_DR_CONFIG_DNS_TXT_DONT_CACHE_MAX                                1

#define ZPN_DR_CONFIG_DNS_MAX_TTL_CAP_S                                 "config.feature.dr.dns_txt.max_ttl_cap"
#define ZPN_DR_CONFIG_DNS_MAX_TTL_CAP_S_DEFAULT                         200      // i.e. 200 secs by default
#define ZPN_DR_CONFIG_DNS_MAX_TTL_CAP_S_MIN                                  30
#define ZPN_DR_CONFIG_DNS_MAX_TTL_CAP_S_MAX                                 600

/* Max config snapshots to store on disk */
#define ZPN_DR_MAX_CONFIG_SNAPSHOT_DAYS                                      "config.feature.dr.config_snapshot_days"
#define ZPN_DR_MAX_CONFIG_SNAPSHOT_DAYS_DEFAULT                              15
#define ZPN_DR_MAX_CONFIG_SNAPSHOT_DAYS_MIN                                       1
#define ZPN_DR_MAX_CONFIG_SNAPSHOT_DAYS_MAX                                      30

/* Config snapshots time, example: 00:00AM at midnight (00-23) hour format */
#define ZPN_DR_CONFIG_SNAPSHOT_TIME                                     "config.feature.dr.config_snapshot_time"
#define ZPN_DR_CONFIG_SNAPSHOT_TIME_DEFAULT                             "00:00"

/* Max binary snapshots to store on disk duration */
#define ZPN_DR_BINARY_SNAPSHOT_PURGE_AFTER_SECONDS                      "config.feature.dr.binary_snapshot_purge_after_seconds"
#define ZPN_DR_BINARY_SNAPSHOT_PURGE_AFTER_SECONDS_DEFAULT              (30*24*60*60)       // 30 days = 30 x 24 x 60 x 60 seconds
#define ZPN_DR_BINARY_SNAPSHOT_PURGE_AFTER_SECONDS_MIN              (60)       // 60 seconds
#define ZPN_DR_BINARY_SNAPSHOT_PURGE_AFTER_SECONDS_MAX              (2*30*24*60*60)       // 60 days = 2 x 30 x 24 x 60 x 60 seconds

/* Max binary snapshots to store on disk duration */
#define ZPN_DR_BINARY_SNAPSHOT_MAX_COUNT                                "config.feature.dr.binary_snapshot_max_count"
#define ZPN_DR_BINARY_SNAPSHOT_MAX_COUNT_DEFAULT                        (5)
#define ZPN_DR_BINARY_SNAPSHOT_MAX_COUNT_MIN                                 (1)
#define ZPN_DR_BINARY_SNAPSHOT_MAX_COUNT_MAX                                (10)

/* PSE Resiliency feature flag*/
#define ZPN_PSE_RESILIENCY_ENABLED                                      "config.pse.resiliency.enabled"
#define ZPN_PSE_RESILIENCY_ENABLED_DEFAULT                              (1)
#define ZPN_PSE_RESILIENCY_ENABLED_MIN                                       (0)
#define ZPN_PSE_RESILIENCY_ENABLED_MAX                                      (1)

/* PSE Resiliency cloud down interval*/
#define ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S                         "config.pse.resiliency.max_control_down_interval_s"
#define ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_DEFAULT                (60)
#define ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_MIN                          (30)              // 30 seconds
#define ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_MAX                         (60*60)         // 3600 seconds (1 hour)

/* PSE disable PB client creation */
#define ZPN_PB_CLIENT_NO_CREATE                                           "config.pse.debug.no_pb_client_create"
#define ZPN_PB_CLIENT_NO_CREATE_DEFAULT                                  (0)
#define ZPN_PB_CLIENT_NO_CREATE_MIN                                            (0)
#define ZPN_PB_CLIENT_NO_CREATE_MAX                                           (1)
/*
 * Frequency for automatic config dump from memory into the disk
 * 1) 15 mints to wait for the next config dump
 * 2) 5 mints to wait for no change in the configuration
 */
#define ZPN_AUTO_DR_CONFIG_DUMP_INTERVAL_SEC                                           "config.feature.dr.auto_config_dump_interval_sec"
#define ZPN_AUTO_DR_CONFIG_DUMP_INTERVAL_SEC_DEFAULT                                   (15 * 60)
#define ZPN_AUTO_DR_CONFIG_DUMP_INTERVAL_SEC_MIN                                             (1 * 60)
#define ZPN_AUTO_DR_CONFIG_DUMP_INTERVAL_SEC_MAX                                            (60 * 60)

#define ZPN_AUTO_DR_CONFIG_WAIT_FOR_NO_CHANGE                             "config.feature.dr.waitfor_config_no_change_interval_sec"
#define ZPN_AUTO_DR_CONFIG_WAIT_FOR_NO_CHANGE_DEFAULT                     (5 * 60)
#define ZPN_AUTO_DR_CONFIG_WAIT_FOR_NO_CHANGE_MIN                              (1 * 60)
#define ZPN_AUTO_DR_CONFIG_WAIT_FOR_NO_CHANGE_MAX                             (60 * 60)


/*
 * Config flag to hard disable redirect policy
 *
 */
#define BROKER_FEATURE_POLICY_REDIRECT_HARD_DISABLED                      "config.feature.balance.policy_redirect.hard_disabled"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT_HARD_DISABLED              0 //not hard disabled by default
#define BROKER_FEATURE_POLICY_REDIRECT_HARD_DISABLED_MIN                  0
#define BROKER_FEATURE_POLICY_REDIRECT_HARD_DISABLED_MAX                  1

/*
 * Config flag to enable/disable redirect policy
 *
 */
#define BROKER_FEATURE_POLICY_REDIRECT                                    "config.feature.balance.policy_redirect.enable"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT                            0 //disabled by default
#define BROKER_FEATURE_POLICY_REDIRECT_MIN                                0
#define BROKER_FEATURE_POLICY_REDIRECT_MAX                                1

/*
 * Config flag to enable/disable PSE IPv6 redirect support
 *
 */
#define BROKER_FEATURE_PSE_IPV6_REDIRECT                                    "config.feature.balance.pse_ipv6.enable"
#define DEFAULT_BROKER_FEATURE_PSE_IPV6_REDIRECT                            0 //disabled by default
#define BROKER_FEATURE_PSE_IPV6_REDIRECT_MIN                                0
#define BROKER_FEATURE_PSE_IPV6_REDIRECT_MAX                                1

/*
 * Config flag for string value which would be prefixed to a broker FQDN when dualstacked
 */
#define BROKER_FEATURE_IPV6_FQDN_PREFIX                                     "config.feature.balance.dual_stack.fqdn_prefix"
#define DEFAULT_BROKER_FEATURE_IPV6_FQDN_PREFIX                             ""

/*
 * Config flag to enable/disable IPv6 exclusive redirect support
 *
 */
#define BROKER_FEATURE_IPV6_EXCLUSIVE_REDIRECT                            "config.feature.balance.dual_stack.exclusive"
#define DEFAULT_BROKER_FEATURE_IPV6_EXCLUSIVE_REDIRECT                    0 //disabled by default
#define BROKER_FEATURE_IPV6_EXCLUSIVE_REDIRECT_MIN                        0
#define BROKER_FEATURE_IPV6_EXCLUSIVE_REDIRECT_MAX                        1

/*
 * Config flag to hard disable IPv6 aware redirection feature
 *
 */
#define BROKER_FEATURE_IPV6_REDIRECT_HARD_DISABLE                         "config.feature.balance.dual_stack.hard_disable"
#define DEFAULT_BROKER_FEATURE_IPV6_REDIRECT_HARD_DISABLE                 0 //disabled by default
#define BROKER_FEATURE_IPV6_REDIRECT_HARD_DISABLE_MIN                     0
#define BROKER_FEATURE_IPV6_REDIRECT_HARD_DISABLE_MAX                     1

/*
 * Config flag to enable/disable IPv6 aware redirection feature
 *
 */
#define BROKER_FEATURE_IPV6_REDIRECT                                      "config.feature.balance.dual_stack.enabled"
#define DEFAULT_BROKER_FEATURE_IPV6_REDIRECT                              0 //disabled by default
#define BROKER_FEATURE_IPV6_REDIRECT_MIN                                  0
#define BROKER_FEATURE_IPV6_REDIRECT_MAX                                  1

/*
 * Config flag to enable/disable grace distance feature for the PSE selection
 *
 */
#define BROKER_FEATURE_PSE_GRACE_DISTANCE                                   "config.feature.balance.grace_distance.enable"
#define DEFAULT_BROKER_FEATURE_PSE_GRACE_DISTANCE                           1 // i.e. enabled by default
#define BROKER_FEATURE_PSE_GRACE_DISTANCE_MIN                                0
#define BROKER_FEATURE_PSE_GRACE_DISTANCE_MAX                                1

/*
 * Config to enable/disable saml/scim from redirect policy evaluation
 */
#define BROKER_FEATURE_POLICY_REDIRECT_SAML                                 "config.feature.balance.policy_redirect.saml.enable"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT_SAML                         0
#define BROKER_FEATURE_POLICY_REDIRECT_SAML_MIN                             0
#define BROKER_FEATURE_POLICY_REDIRECT_SAML_MAX                             1

#define BROKER_FEATURE_POLICY_REDIRECT_SCIM                                 "config.feature.balance.policy_redirect.scim.enable"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT_SCIM                         0
#define BROKER_FEATURE_POLICY_REDIRECT_SCIM_MIN                             0
#define BROKER_FEATURE_POLICY_REDIRECT_SCIM_MAX                             1

/*
 * Config override to disable all the other criteria that are under general_context_hash (other than saml/scim/int)
 */

#define BROKER_FEATURE_POLICY_REDIRECT_GEN                                   "config.feature.balance.policy_redirect.general_criteria.enable"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT_GEN                           1
#define BROKER_FEATURE_POLICY_REDIRECT_GEN_MIN                               0
#define BROKER_FEATURE_POLICY_REDIRECT_GEN_MAX                               1

/*
 * Config to relax the REDIRECT_ALWAYS option to provide a fallback public broker.
 */
#define BROKER_FEATURE_POLICY_REDIRECT_FALLBACK_PUBLICBROKER                "config.feature.balance.policy_redirect.fallback_public_broker.enable"
#define DEFAULT_BROKER_FEATURE_POLICY_REDIRECT_FALLBACK_PUBLICBROKER        1
#define BROKER_FEATURE_POLICY_REDIRECT_FALLBACK_PUBLICBROKER_MIN            0
#define BROKER_FEATURE_POLICY_REDIRECT_FALLBACK_PUBLICBROKER_MAX            1

/*
 * config.feature.broker.mem_load_pct_lo_wm
 * config.feature.broker.mem_load_pct_hi_wm
 * Default (if not configured) low/high watermarks are 50 and 90, respectively.
 * The goal is to account for broker's idle memory usage, and avoid memory load
 * dominating balance algorithm (ET-36834). When computing effective memory load:
 *  -- If memory load is lower than low-watermark, treat memory load as 0.
 *  -- If memory load is higher than high-water, treat memory load as 100.
 *  -- If memory load is in-between, extrapolate between 0-100.
 */
#define CONFIG_FEATURE_BROKER_MEM_LOAD_PCT_LO_WM      "config.feature.broker.mem_load_pct_lo_wm"
#define BROKER_MEM_LOAD_PCT_LO_WM_DFLT                40
#define BROKER_MEM_LOAD_PCT_LO_WM_MIN                 0
#define BROKER_MEM_LOAD_PCT_LO_WM_MAX                 80

#define CONFIG_FEATURE_BROKER_MEM_LOAD_PCT_HI_WM      "config.feature.broker.mem_load_pct_hi_wm"
#define BROKER_MEM_LOAD_PCT_HI_WM_DFLT                100
#define BROKER_MEM_LOAD_PCT_HI_WM_MIN                 1
#define BROKER_MEM_LOAD_PCT_HI_WM_MAX                 100

/*
 * config.feature.broker.proc_fd_util_load_pct_hi_wm
 * Default (if not configured) high watermark is 95 (lower limit is 0).
 * When computing effective proc_fd_util load:
 *  -- If proc_fd_util load is higher than high-watermark, treat proc_fd_util load as 100.
 *  -- If proc_fd_util load is lesser, calculate as a percentage of high-watermark.
 */
#define CONFIG_FEATURE_BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM      "config.feature.broker.proc_fd_util_load_pct_hi_wm"
#define BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_DFLT                95
#define BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_MIN                 1
#define BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_MAX                 100

/*
 * Instance selection configurations
 */
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES "config.feature.broker.balance.instance_selection.hard_disable_retries"
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES_DEFAULT 0
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES_MIN 0
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES_MAX 1

#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES "config.feature.broker.balance.instance_selection.retries_enabled"
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES_DEFAULT 0
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES_MIN 0
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES_MAX 1

#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT "config.feature.broker.balance.instance_selection.use_retries"
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT_DEFAULT 3
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT_MIN 1
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT_MAX 50

#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD "config.feature.broker.balance.instance_selection.retry_min_bucket_threshold"
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD_DEFAULT 1
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD_MIN 0
#define CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD_MAX 30

/*
 * Use brokers calculated client load during load calculations
 */

// Kill switch to disable client load across the cloud regardless of any other configurations
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED         "config.feature.broker.balance.client_load.hard_disabled"
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED_DEFAULT 0
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED_MIN     0
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED_MAX     1

// Targeted Min/Max rates for client_load calculations for SEs and PSEs (0%-100%)
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_ENABLED_RATE_MAX           100
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_ENABLED_RATE_MIN           0

#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_SE_ENABLED_RATE               "config.feature.broker.balance.client_load.se_enabled_rate"
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_SE_ENABLED_RATE_DEFAULT       0

// Targeted Enable/Disable for client_load calculations for PSEs
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_PSE_ENABLED_RATE               "config.feature.broker.balance.client_load.pse_enabled_rate"
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_PSE_ENABLED_RATE_DEFAULT       0

// The min load data points we need before we will try to estimate predicted client load capacity
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD        "config.feature.broker.balance.client_load.min_load_percent_threshold"
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD_DEFAULT 75
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD_MIN     1
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD_MAX     100

// The min number of current clients before we will try to estimate predicted client load capacity
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD         "config.feature.broker.balance.client_load.min_client_count_threshold"
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD_DEFAULT (10*1000)
//TODO(blewis) move this back up to 10k once QA testing is complete
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD_MIN     (1000)
#define CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD_MAX     (20*1000)

/* Config override for dropping connection on version incompatibility*/
#define VERSION_COMPATIBILITY_CATEGORY                                              "version compatibility"
#define WALLY_FEATURE_DROP_INCOMPATIBLE_VERSION                                     "config.feature.drop_on_version_incompatibility"
#define DEFAULT_DROP_INCOMPATIBLE_VERSION                                           0
#define DEFAULT_DROP_INCOMPATIBLE_VERSION                                           0
#define DROP_INCOMPATIBLE_VERSION_MIN                                               0
#define DROP_INCOMPATIBLE_VERSION_MAX                                               1

/* Config override for prioritising table interest registrations when leafs connect to origins*/
#define WALLY_FEATURE_LEAF_RECONNECT_TABLE_REG_PRIORITY                            "config.feature.leaf_wally_reconn_table_reg_priority"
#define LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_DEFAULT                              0
#define LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_MIN                                  0
#define LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_MAX                                  1

// Wally dont dump
#define WALLY_FEATURE_DONT_DUMP                                                    "config.feature.wally.dont_dump"

/* Config override for getting logical partition profile version */
#define ZPN_CONFIG_LP_PROFILE_VERSION                           "config.feature.lp.profile.version"
#define DEFAULT_ZPN_CONFIG_LP_PROFILE_VERSION                   -1
#define ZPN_CONFIG_LP_PROFILE_VERSION_MIN                       -1
#define ZPN_CONFIG_LP_PROFILE_VERSION_MAX                       2147483647

/* Config override for logical partition feature */
#define ZPN_LP_FEATURE_CONFIG                       "config.lp.feature"
#define ZPN_LP_FEATURE_CONFIG_DEFAULT                0
#define ZPN_LP_FEATURE_CONFIG_MIN                    0
#define ZPN_LP_FEATURE_CONFIG_MAX                    1

#define CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST        "config.feature.lp.mdc_sdc_mixup_inst"
#define CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_DEFAULT        0
#define CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_MIN            0
#define CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_MAX            1

/*
 * Feature: Broker latency probe, Affected Components: Broker and Clients ( ZCC as of now )
 */
#define BROKER_LATENCY_PROBE                                             "config.feature.broker_latency_probe"
#define DEFAULT_BROKER_LATENCY_PROBE_FEATURE                             0
#define BROKER_LATENCY_PROBE_FEATURE_MIN                                 0
#define BROKER_LATENCY_PROBE_FEATURE_MAX                                 1

/*
 * Instructs brokers to stop syncing config from certain db (CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY)
 */
/* scope: instance (broker_gid) or global (1)
 * value: boolean (0/1)
 * default: 0 (not paused)
 * Note that DB name is parameterized */
#define CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY               "config.feature.broker.pause.wally.%s"
#define CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DISABLED      0
#define CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_ENABLED       1
#define CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DEFAULT       CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DISABLED

/* use for config override registration */
#define CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_GLOBAL         "config.feature.broker.pause.wally.global_wally"

/*
 * Feature: Partner Login, Affected Components: Broker and PB
 */
#define PARTNER_LOGIN_FEATURE                                                       "config.feature.partner_login"
#define DEFAULT_PARTNER_LOGIN_FEATURE                                               1
#define MAX_PARTNER_LOGIN_FEATURE                                                   1
#define MIN_PARTNER_LOGIN_FEATURE                                                   0

/*
 * Feature: Send connector/PSE disconnect event to kafka over zpn_event topic.
 * Affected Components: Broker
 */
#define CONFIG_SEND_AST_DISCONNECT_EVENT                                            "config.feature.zpn_event.send_ast_disconnect_event"
#define CONFIG_SEND_AST_DISCONNECT_EVENT_DEFAULT                                    0
#define CONFIG_SEND_AST_DISCONNECT_EVENT_MAX                                        1
#define CONFIG_SEND_AST_DISCONNECT_EVENT_MIN                                        0

#define CONFIG_SEND_PB_DISCONNECT_EVENT                                             "config.feature.zpn_event.send_pb_disconnect_event"
#define CONFIG_SEND_PB_DISCONNECT_EVENT_DEFAULT                                      0
#define CONFIG_SEND_PB_DISCONNECT_EVENT_MAX                                          1
#define CONFIG_SEND_PB_DISCONNECT_EVENT_MIN                                          0

/*
 * Clientless Protection Profile config override to define bit mapping of
 * attributes present in browser protection profile. This flag is used by itasca and API.
 */
#define CONFIG_FEATURE_CSP_BITMASK                                          "config.feature.csp.bitmask"
#define CSP_BITMASK_DEFAULT                                                 "\"{\"exporter_csp_bits\":{\"browser_ja3_bit\":1,\"device_fingerprint_bit\":2,\"canvas_info_bit\":4,\"browser_name_bit\":8,\"browser_ver_bit\":16,\"browser_eng_bit\":32,\"browser_eng_ver_bit\":64,\"browser_lcl_strg_bit\":128,\"browser_sess_strg_bit\":256,\"browser_cookie_cfg_bit\":512,\"cpu_arch_bit\":1024,\"os_type_bit\":2048,\"os_ver_bit\":4096,\"curr_screen_res_bit\":8192,\"avail_screen_res_bit\":16384,\"user_agent_bit\":32768,\"tz_bit\":65536,\"user_lang_bit\":131072,\"system_lang_bit\":262144,\"mobile_dev_bit\":524288,\"mobile_dev_type_bit\":1048576,\"mime_info_bit\":2097152,\"latitude_bit\":4194304,\"longitude_bit\":8388608,\"font_bit\":16777216,\"plugin_bit\":33554432,\"java_ver_bit\":67108864,\"flash_ver_bit\":134217728,\"silverlight_ver_bit\":268435456}}\"\n"

/*
 * To read the threshold value for detecting location change, if location difference is greater than
 * this threshold. Location change would be marked as true. Unit for this attribute is in meters.
 */
#define CONFIG_FEATURE_CSP_LOCATION_CHANGE_THRESHOLD                       "config.feature.csp.location_change_threshold"
/* Default location change threshold value in meters. */
#define LOCATION_CHANGE_THRESHOLD_DEFAULT                                  50

#define CONFIG_FEATURE_CSP_JS_ENCRYPTION_STATE                                      "config.feature.csp.js_encryption"
#define DEFAULT_CONFIG_FEATURE_CSP_JS_ENCRYPTION_STATE                              1

#define FOHH_ENABLE_FC_RESET_RECOVER                                                "config.feature.fohh.enable.flow_control.recover"
#define DEFAULT_FOHH_ENABLE_FC_RESET_RECOVER                                        0

#define FOHH_FC_RESET_RECOVER_INTERVAL_S                                            "config.feature.fohh.flow_control.recover.interval_s"
#define DEFAULT_FOHH_FC_RESET_RECOVER_INTERVAL_S                                    3*60
#define LOW_FOHH_FC_RESET_RECOVER_INTERVAL_S                                        1*60
#define HIGH_FOHH_FC_RESET_RECOVER_INTERVAL_S                                       10*60

#define FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED                          "config.feature.fohh.flow_control.recover.threshold"
#define DEFAULT_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED                  9900  /*meaning 99.00 percent*/
#define LOW_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED                      9500  /*meaning 95.00 percent*/
#define HIGH_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED                     10000 /*meaning 100.00 percent*/


/*
 * Feature: ET-47164 argo logging memory usage improvements
 */
#define ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT                                     "config.feature.argo_mem_threshold_percentage_assistant"
#define DEFAULT_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT                             95
#define LOW_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT                                 50
#define HIGH_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT                                100

#define ARGO_MEM_THRESHOLD_PERCENTAGE_PSE                                           "config.feature.argo_mem_threshold_percentage_pse"
#define ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_DEFAULT                                   95
#define ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_MIN                                             0
#define ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_MAX                                            100

/*
 * Feature: ET-52728 broker to send zpn_ast_auth_report every 1 min or 4 min
 */
#define BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN                                     "config.feature.broker.auth_report_1min"
#define BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT                             1
#define BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_LOW                                 0
#define BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_HIGH                                1


/*
 * Feature: ET-51866 SIPA load balancing optimization
 * Affected components: Broker
 */
#define BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN                                "config.feature.broker_client_path_cache_max_size_per_domain"
#define DEFAULT_BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN                        10
#define BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MIN                            1
#define BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MAX                            1000

#define ZPN_BROKER_APP_BUFFER_TUNE_FEATURE                                         "config.feature.broker.app_buffer_tune"
#define ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE                            0
#define LOW_ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_VALUE                                0
#define HIGH_ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_VALUE                               1

#define ZPN_PSE_APP_BUFFER_TUNE_FEATURE                                            "config.feature.pse.app_buffer_tune"
#define ZPN_PSE_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE                               0
#define LOW_ZPN_PSE_APP_BUFFER_TUNE_FEATURE_VALUE                                   0
#define HIGH_ZPN_PSE_APP_BUFFER_TUNE_FEATURE_VALUE                                  1

#define ZPN_BROKER_APP_BUFFER_CONFIG                                               "config.feature.broker.app_buffer.config"
#define ZPN_BROKER_APP_BUFFER_CONFIG_DEFAULT_VALUE                                  0
#define LOW_ZPN_BROKER_APP_BUFFER_CONFIG_VALUE                                      0
#define HIGH_ZPN_BROKER_APP_BUFFER_CONFIG_VALUE                                     (4*0x10*0x100000)

#define ZPN_BROKER_APP_BUFFER_MCONN_CONFIG                                         "config.feature.broker.app_buffer_mconn.config"
#define ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE                            0
#define LOW_ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_VALUE                                0
#define HIGH_ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_VALUE                               (16*0x100000)

#define ZPN_PSE_APP_BUFFER_CONFIG                                                  "config.feature.pse.app_buffer.config"
#define ZPN_PSE_APP_BUFFER_CONFIG_DEFAULT_VALUE                                     0
#define LOW_ZPN_PSE_APP_BUFFER_CONFIG_VALUE                                         0
#define HIGH_ZPN_PSE_APP_BUFFER_CONFIG_VALUE                                        (4*0x10*0x100000)
/* alt-cloud support */
#define CONFIG_FEATURE_ALT_CLOUD_DISABLED                                           0
#define CONFIG_FEATURE_ALT_CLOUD_ENABLED                                            1

#define CONFIG_FEATURE_ALT_CLOUD                                                    "config.feature.alt_cloud.enabled"
#define CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE                                      CONFIG_FEATURE_ALT_CLOUD_ENABLED

#define ZPN_PSE_APP_BUFFER_MCONN_CONFIG                                            "config.feature.pse.app_buffer_mconn.config"
#define ZPN_PSE_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE                               0
#define LOW_ZPN_PSE_APP_BUFFER_MCONN_CONFIG_VALUE                                   0
#define HIGH_ZPN_PSE_APP_BUFFER_MCONN_CONFIG_VALUE                                  (16*0x100000)

#define ZPN_BROKER_APP_BUFFER_WATERMARK                                            "config.feature.broker.app_buffer_watermark.config"
#define ZPN_BROKER_APP_BUFFER_WATERMARK_DEFAULT_VALUE                               0
#define LOW_ZPN_BROKER_APP_BUFFER_WATERMARK_VALUE                                   0
#define HIGH_ZPN_BROKER_APP_BUFFER_WATERMARK_VALUE                                  (2*0x10*0x100000)



#define ZPN_PSE_APP_BUFFER_WATERMARK                                               "config.feature.pse.app_buffer_watermark.config"
#define ZPN_PSE_APP_BUFFER_WATERMARK_DEFAULT_VALUE                                  0
#define LOW_ZPN_PSE_APP_BUFFER_WATERMARK_VALUE                                      0
#define HIGH_ZPN_PSE_APP_BUFFER_WATERMARK_VALUE                                     (2*0x10*0x100000)

#define ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK                                      "config.feature.broker.app_buffer_mconn_watermark.config"
#define ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE                         0
#define LOW_ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_VALUE                             0
#define HIGH_ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_VALUE                            (8*0x100000)

#define ZPN_PSE_APP_BUFFER_MCONN_WATERMARK                                         "config.feature.pse.app_buffer_mconn_watermark.config"
#define ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE                            0
#define LOW_ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_VALUE                                0
#define HIGH_ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_VALUE                               (8*0x100000)

// for ET-81763
#define ZPN_BROKER_ZIA_CLOUD_PREFIX                                                  "config.feature.broker.zia_cloud_prefix"
#define ZPN_BROKER_ZIA_CLOUD_PREFIX_DEFAULT                                          NULL

/* FOHH Status Interval related feature flags */
#define CONFIG_FOHH_STATUS_INTVL_CTL 	            "config.feature.broker.status.interval.ctl"
#define CONFIG_FOHH_STATUS_INTVL_STATS 	            "config.feature.broker.status.interval.stats"
#define CONFIG_FOHH_STATUS_INTVL_OVD 	            "config.feature.broker.status.interval.ovd"
#define CONFIG_FOHH_STATUS_INTVL_CFG 	            "config.feature.broker.status.interval.cfg"
#define CONFIG_FOHH_STATUS_INTVL_LOG 	            "config.feature.broker.status.interval.log"
#define CONFIG_FOHH_STATUS_INTVL_WAF 	            "config.feature.broker.status.interval.waf"
#define CONFIG_FOHH_STATUS_INTVL_DATA 	            "config.feature.broker.status.interval.data"
#define CONFIG_FOHH_STATUS_INTVL_USERDB             "config.feature.broker.status.interval.userdb"
#define CONFIG_FOHH_STATUS_INTVL_RCFG               "config.feature.broker.status.interval.rcfg"
#define CONFIG_FOHH_STATUS_INTVL_APP_INSPECTION   	"config.feature.broker.status.interval.app.inspection"

/* Default value in seconds for FOHH Status Interval related feature flags */
#define STATUS_INTVL_DEFAULT    1
#define LOW_STATUS_INTVL        1
#define HIGH_STATUS_INTVL       60

/* Default max logging size for connector is 64 MB */
#define DEFAULT_MAX_LOGGING_MB 64

#define MAX_FOHH_POOL 5
#define SIZE_FOHH_ARG_STRING 64

#define CONFIG_CONNECTOR_QUIET          "config.feature.connector.quiet"
#define CONFIG_CONNECTOR_QUIET_DEFAULT  0
#define CONNECTOR_QUIET_ENABLE          1
#define CONNECTOR_QUIET_DISABLE         0

#define ASSISTANT_APP_BUFFER_TUNE_FEATURE                                           "config.feature.assistant.app_buffer_tune"
#define ASSISTANT_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE                              0

#define ASSISTANT_APP_BUFFER_CONFIG                                                 "config.feature.assistant.app_buffer.config"
#define ASSISTANT_APP_BUFFER_CONFIG_DEFAULT_VALUE                                   0
#define LOW_ASSISTANT_APP_BUFFER_CONFIG_VALUE                                       0
#define HIGH_ASSISTANT_APP_BUFFER_CONFIG_VALUE                                      (4*0x10*0x100000)  //64 MB

#define ASSISTANT_APP_BUFFER_MCONN_CONFIG                                           "config.feature.assistant.app_buffer_mconn.config"
#define ASSISTANT_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE                             0
#define LOW_ASSISTANT_APP_BUFFER_MCONN_CONFIG_VALUE                                 0
#define HIGH_ASSISTANT_APP_BUFFER_MCONN_CONFIG_VALUE                                (16*0x100000)   //16 MB

#define ASSISTANT_APP_BUFFER_WATERMARK                                              "config.feature.assistant.app_buffer_watermark.config"
#define ASSISTANT_APP_BUFFER_WATERMARK_DEFAULT_VALUE                                0
#define LOW_ASSISTANT_APP_BUFFER_WATERMARK_VALUE                                    0
#define HIGH_ASSISTANT_APP_BUFFER_WATERMARK_VALUE                                   (2*0x10*0x100000) //32 MB

#define ASSISTANT_APP_BUFFER_MCONN_WATERMARK                                        "config.feature.assistant.app_buffer_mconn_watermark.config"
#define ASSISTANT_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE                          0
#define LOW_ASSISTANT_APP_BUFFER_MCONN_WATERMARK_VALUE                              0
#define HIGH_ASSISTANT_APP_BUFFER_MCONN_WATERMARK_VALUE                             (8*0x100000)     //8 MB

/*
 * Feature: Re-dispatch zpn_broker_request to a dispatcher in different DC if first
 * returned NO_CONNECTOR_AVAILABLE or INVALID_DOMAIN error.
 *
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC                         "config.feature.broker.redispatch_brk_req_to_diff_dc"
#define DEFAULT_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC                 1
#define MAX_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC                     1
#define MIN_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC                     0

/* Feature: Latency based broker (LBB)
 *          Configure the broker zapp idle tunnel burst packet count
 */
#define CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT                  "config.feature.broker.zapp.idle.tunnel.burst.counter"
#define DEFAULT_CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT           20
#define CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT_MIN               20
#define CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT_MAX               30



/* not going to use this limit as for now, thus making default value rediculously large.
 * have the logic there just in case we really need it
 * ref : ET-36929
 */
#define ASSISTANT_MTUNNEL_CONCURRENT_EXECUTING    "config.feature.connector.mtunnel.max_concurrent_mtunnels"
#define ASSISTANT_MAX_CONCURRENT_MTUNNEL_DEFAULT 1000000
#define ASSISTANT_MAX_CONCURRENT_MTUNNEL_LOW     50000
#define ASSISTANT_MAX_CONCURRENT_MTUNNEL_HIGH    5000000

#define BROKER_MAX_MTUNNEL_PER_USER                 "config.feature.broker.mtunnel.max_mtunnels_per_user"
#define BROKER_MAX_MTUNNEL_PER_USER_LOW             10000
#define BROKER_MAX_MTUNNEL_PER_USER_HIGH            2147483647
#define BROKER_MAX_MTUNNEL_PER_USER_DEFAULT         BROKER_MAX_MTUNNEL_PER_USER_HIGH

/* Feature: enable/disable PSE to connect with static/registration wally */
#define ZPN_PSE_REGISTRATION_WALLY_ENABLED             "config.feature.pse.registration_wally.enabled"
#define ZPN_PSE_REGISTRATION_WALLY_ENABLED_DEFAULT     0
#define ZPN_PSE_REGISTRATION_WALLY_ENABLED_MIN         0
#define ZPN_PSE_REGISTRATION_WALLY_ENABLED_MAX         1

#define ASSISTANT_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE                           0
/* Feature: enable/disable sitec to connect with static/registration wally */
#define ZPN_SITEC_REGISTRATION_WALLY_ENABLED             "config.feature.sitec.registration_wally.enabled"
#define ZPN_SITEC_REGISTRATION_WALLY_ENABLED_DEFAULT     0
#define ZPN_SITEC_REGISTRATION_WALLY_ENABLED_MIN         0
#define ZPN_SITEC_REGISTRATION_WALLY_ENABLED_MAX         1

#define CONFIG_FEATURE_PBROKER_FPROXY                                               "config.feature.pse.forward_proxy"
#define CONFIG_FEATURE_PBROKER_FPROXY_ENABLED                                       1
#define CONFIG_FEATURE_PBROKER_FPROXY_DISABLED                                      0
#define CONFIG_FEATURE_PBROKER_FPROXY_DEFAULT_STATUS                                CONFIG_FEATURE_PBROKER_FPROXY_DISABLED

#define CONFIG_FEATURE_SITEC_FPROXY                                                 "config.feature.pcc.forward_proxy"
#define CONFIG_FEATURE_SITEC_FPROXY_ENABLED                                         1
#define CONFIG_FEATURE_SITEC_FPROXY_DISABLED                                        0
#define CONFIG_FEATURE_SITEC_FPROXY_DEFAULT_STATUS                                  CONFIG_FEATURE_SITEC_FPROXY_DISABLED

/*
 * Feature: Broker negative path cache
 * Affected components: Broker
 */
#define CONFIG_FEATURE_NEGATIVE_PATH_CACHE                                          "config.feature.broker.enable.negative_path_cache"
#define DEFAULT_CONFIG_FEATURE_NEGATIVE_PATH_CACHE                                  1
#define MIN_CONFIG_FEATURE_NEGATIVE_PATH_CACHE                                      0
#define MAX_CONFIG_FEATURE_NEGATIVE_PATH_CACHE                                      1

#define CONFIG_FEATURE_NEGATIVE_PATH_CACHE_TTL_S                                    "config.feature.broker.negative_path_cache_ttl_s"
#define CONFIG_FEATURE_NEGATIVE_PATH_CACHE_TTL_S_DEFAULT                            30
#define CONFIG_FEATURE_NEGATIVE_PATH_CACHE_TTL_S_MIN                                1
#define CONFIG_FEATURE_NEGATIVE_PATH_CACHE_TTL_S_MAX                                120 // 2 min

/*
 * Feature: Positive path cache on broker
 * Affected components: Broker
 */
#define CONFIG_FEATURE_PATH_CACHE_TTL_US                                            "config.feature.broker.path_cache_ttl_us"
#define DEFAULT_CONFIG_FEATURE_PATH_CACHE_TTL_US                                    (int64_t)(2ll * 60ll * 1000ll * 1000ll) // 2 min
#define MIN_CONFIG_FEATURE_PATH_CACHE_TTL_US                                        0
#define MAX_CONFIG_FEATURE_PATH_CACHE_TTL_US                                        (int64_t)(60ll * 60ll * 1000ll * 1000ll) // 60 min

#define CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP                                       "config.feature.broker.path_cache_key_ast_grp"
#define DEFAULT_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP                               0
#define MIN_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP                                   0
#define MAX_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP                                   1


/*
 * Feature: Send pending mtunnel_end request to a assistant if that was received from client
 * before establishing tunnel.
 * Reference: ET-65936
 * As a permanent solution, we would prefer to remove this config override
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END                              "config.feature.broker.brk_send_pending_mtunnel_end"
#define DEFAULT_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END                      1   // This is same as the global value of the config
#define MAX_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END                          1
#define MIN_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END                          0
#define CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END                                 "config.feature.pse.brk_send_pending_mtunnel_end"
#define DEFAULT_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END                         0
#define MAX_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END                             1
#define MIN_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END                             0

/*Feature: Rate limitting for natural at Broker's end
Adding config override for customer Id
*/
// Keys for  global enable/disable our throttling function.
#define NATURAL_TRANS_LOG_RATE_LIMITTING                                            "config.feature.natural.rate.limiting"
#define NATURAL_TRANS_LOG_RATE_LIMITTING_ENABLED                                    1
#define NATURAL_TRANS_LOG_RATE_LIMITTING_DISABLED                                   0
#define NATURAL_TRANS_LOG_RATE_LIMITTING_DEFAULT                                    0

#define NATURAL_TRANS_LOG_RATE_LIMIT_ERRORS                                         "config.feature.natural.rate.limit.error"
#define NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_ERRORS                                 ""

#define NATURAL_TRANS_LOG_RATE_LIMIT_TIME                                           "config.feature.natural.rate.limit.time"
#define MIN_NATURAL_TRANS_LOG_RATE_LIMIT_TIME                                       60
#define MAX_NATURAL_TRANS_LOG_RATE_LIMIT_TIME                                       1800
#define NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_TIME                                   300

/*
 * knob to turn off ET-46929
 * Static Server configuraion will take precedance in dns check request starting ET-46929
 */
#define CONNECTOR_DNS_CHECK_USE_STATIC_SERVER                                       "config.feature.assistant.dns_check.use_static_config"
#define CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_DEFAULT                               1
#define CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_MIN                                   0
#define CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_MAX                                   1

/*
 * Feature: Enable precise calculation of lat, lon based on double.
 * Reference: ET-69063
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT                          "config.feature.broker.client_double_precision_geo_location_percent"
#define DEFAULT_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT                  100 // This is set to the global value of the config override
#define MAX_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT                      100
#define MIN_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT                      0

/*
 * Feature: Re-dispatch zpn_broker_request to a dispatcher in different DC if first
 * dispatcher didn't respond within 2 sec.
 *
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC                        "config.feature.broker.timeout_redispatch_brk_req_to_diff_dc"
#define CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_DEFAULT                1
#define CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_MAX                    1
#define CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_MIN                    0

#define CONFIG_FEATURE_BRK_REQ_TIMEOUT_S                                            "config.feature.broker.brk_req_timeout_s"
#define CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_DEFAULT                                    2 // 2 sec
#define CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_MAX                                        30 // 30 sec
#define CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_MIN                                        0

#define CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US                                "config.feature.broker.brk_req_timeout_max_jitter_us"
#define DEFAULT_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US                        100000 // 100 milli seconds
#define MAX_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US                            1000000 // 1 sec
#define MIN_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US                            0 // No jitter

#define ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                                "config.feature.broker.fohh_mconn_track_perf_stats.level"
#define ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE                  0
#define LOW_ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                            0
#define HIGH_ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                           3

#define ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                                   "config.feature.pse.fohh_mconn_track_perf_stats.level"
#define ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE                     0
#define LOW_ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                               0
#define HIGH_ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                              3

#define ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                             "config.feature.assistant.fohh_mconn_track_perf_stats.level"
#define ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE               0
#define LOW_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                             0
#define HIGH_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL                            3

#define CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES                            "config.feature.broker.mconn_batch_window_updates"
#define CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_DEFAULT                    0
#define CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_MAX                        1
#define CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_MIN                        0

#define CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES                               "config.feature.pse.mconn_batch_window_updates"
#define CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_DEFAULT                       0
#define CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_MAX                           1
#define CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_MIN                           0

#define CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES                         "config.feature.connector.mconn_batch_window_updates"
#define CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_DEFAULT                 0
#define CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_MAX                     1
#define CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_MIN                     0

#define CONFIG_FEATURE_BROKER_SYN_APP_RTT                                           "config.feature.broker.syn_app_rtt"
// client facing on broker
#define CONFIG_FEATURE_BROKER_CLIENT_SYN_APP_RTT                                    "config.feature.broker.client_syn_app_rtt"
#define CONFIG_FEATURE_BROKER_SYN_APP_RTT_DEFAULT                                   0
#define CONFIG_FEATURE_BROKER_SYN_APP_RTT_MAX                                       1
#define CONFIG_FEATURE_BROKER_SYN_APP_RTT_MIN                                       0

#define CONFIG_FEATURE_PSE_SYN_APP_RTT                                              "config.feature.pse.syn_app_rtt"
// client facing on broker
#define CONFIG_FEATURE_PSE_CLIENT_SYN_APP_RTT                                       "config.feature.pse.client_syn_app_rtt"
#define CONFIG_FEATURE_PSE_SYN_APP_RTT_DEFAULT                                      0
#define CONFIG_FEATURE_PSE_SYN_APP_RTT_MAX                                          1
#define CONFIG_FEATURE_PSE_SYN_APP_RTT_MIN                                          0

#define CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT                                        "config.feature.connector.syn_app_rtt"
#define CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_DEFAULT                                0
#define CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_MAX                                    1
#define CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_MIN                                    0

#define CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE                          "config.feature.broker.fohh_pipeline_latency_trace"
// client facing on broker
#define CONFIG_FEATURE_BROKER_CLIENT_FOHH_PIPELINE_LATENCY_TRACE                   "config.feature.broker.client_fohh_pipeline_latency_trace"
#define CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT                  0
#define CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MAX                      1
#define CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MIN                      0

#define CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE                             "config.feature.pse.fohh_pipeline_latency_trace"
// client facing on broker
#define CONFIG_FEATURE_PSE_CLIENT_FOHH_PIPELINE_LATENCY_TRACE                      "config.feature.pse.client_fohh_pipeline_latency_trace"
#define CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT                     0
#define CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MAX                         1
#define CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MIN                         0

#define CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE                       "config.feature.connector.fohh_pipeline_latency_trace"
#define CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT               0
#define CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_MAX                   1
#define CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_MIN                   0

/* Feature: Adaptive Load monitor - To monitor system resources more frequently and update other * brokers about the system load information
 * Reference: ET-69668
 * Affected Components: Public Broker
 */
#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR                              "config.feature.broker.adaptive_load_monitor"
#define DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR                      1  // This is set to the global value of the config override
#define MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR                          1
#define MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR                          0

#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL                     "config.feature.broker.adaptive_load_update_interval"
#define DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL             5
#define MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL                 30
#define MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL                 2

#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD                  "config.feature.broker.adaptive_load_cpu_spike_threshold"
#define DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD          20     // This is set to global value of the config override
#define MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD              100
#define MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD              2

#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD               "config.feature.broker.adaptive_load_memory_spike_threshold"
#define DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD       5
#define MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD           100
#define MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD           2

#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD              "config.feature.broker.adaptive_load_fd_spike_threshold"
#define DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD      5
#define MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD          100
#define MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD          2

/*
 * Feature: slow drain during maintenance - to ensure that clients are redirected gracefully over the drain time interval during maintenence.
 * Reference: ET-69667
 * Affected Components: Public Broker
 */
#define CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN                            "config.feature.broker.slow_maintenance_drain"
#define DEFAULT_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN                    1   // This is set to the global value of the config override
#define MAX_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN                        1
#define MIN_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN                        0

/*
 * slow maintenance drain threshold - Initiate slow drain only when the drainable connnections exceeds this threshold
 *  If lesser; still do a slow drain albeit across a proportionally smaller drain_timeout_s
 */
#define CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD                  "config.feature.broker.slow_maintenance_drain_threshold"
#define DEFAULT_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD           5000
#define MAX_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD               1000000    /* pretty large number.. 1M connections */
#define MIN_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD               0          /* 0 means, always do a slow drain */

#define CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT                                 "config.feature.balance.client_state_cap_support"
#define CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_DEFAULT                         0
#define CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_MAX                             1
#define CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_MIN                             0

/*
 * Feature: Workload tag group
 * Reference: ET-71158
 * Affected Components: Broker, PSE
 */
#define CONFIG_WORKLOAD_TAG_GRP_FEATURE                                              "config.feature.workload_tag_group"
#define CONFIG_FEATURE_WORKLOAD_TAG_GRP_MAX                                          1
#define CONFIG_FEATURE_WORKLOAD_TAG_GRP_MIN                                          0
#define CONFIG_FEATURE_WORKLOAD_TAG_GRP_DEFAULT                                      0

#define CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED                                "config.feature.workload_tag_group.hard_disabled"
#define CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT                        0

/*
 * Feature ZPA authenticated SNI tunnel
 * Ticket: ET-65255
 */

#define CONFIG_FEATURE_BROKER_AUTH_SNI                                              "config.feature.broker.authenticated_sni"
#define CONFIG_FEATURE_BROKER_AUTH_SNI_ENABLED                                      1
#define CONFIG_FEATURE_BROKER_AUTH_SNI_DISABLED                                     0
#define CONFIG_FEATURE_BROKER_AUTH_SNI_DEFAULT_STATUS                               CONFIG_FEATURE_BROKER_AUTH_SNI_DISABLED

#define CONFIG_FEATURE_ASSISTANT_FPROXY                                             "config.feature.assistant.forward_proxy"
#define CONFIG_FEATURE_ASSISTANT_FPROXY_ENABLED                                     1
#define CONFIG_FEATURE_ASSISTANT_FPROXY_DISABLED                                    0
#define CONFIG_FEATURE_ASSISTANT_FPROXY_DEFAULT_STATUS                              CONFIG_FEATURE_ASSISTANT_FPROXY_DISABLED

/*
 * Feature - Enqueue Dequeue Fix
 * Ticket ET-75183
 */
#define CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX                                          "config.feature.enqueue.dequeue.fix"
#define CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_DEFAULT                                  1
#define CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_MIN                                      0
#define CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_MAX                                      1

/*
 * Feature: enable version validation for client - to validate that version of the clients connecting to broker are in
 * acceptable range. Affected Components: Public Broker
 */
#define CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL                                   "config.feature.broker.zpn_version_control"
#define DEFAULT_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL                           0
#define MAX_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL                               1
#define MIN_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL                               0

#define CONFIG_FEATURE_PBROKER_ZPN_VERSION_CONTROL                                   "config.feature.pbroker.zpn_version_control"
#define DEFAULT_CONFIG_FEATURE_PBROKER_ZPN_VERSION_CONTROL                           0
#define MAX_CONFIG_FEATURE_PBROKER_ZPN_VERSION_CONTROL                               1
#define MIN_CONFIG_FEATURE_PBROKER_ZPN_VERSION_CONTROL                               0

#define CONFIG_FEATURE_ASSISTANT_ZPN_VERSION_CONTROL                                 "config.feature.assistant.zpn_version_control"
#define DEFAULT_CONFIG_FEATURE_ASSISTANT_ZPN_VERSION_CONTROL                         0
#define MAX_CONFIG_FEATURE_ASSISTANT_ZPN_VERSION_CONTROL                             1
#define MIN_CONFIG_FEATURE_ASSISTANT_ZPN_VERSION_CONTROL                             0


/*
 * Feature: App Connector and Data Broker connectivity resilience (Two Hop)
 * Affected components: App Connector, Broker
 */
#define CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE                                           "config.feature.broker.asst_databroker_resilience"
#define DEFAULT_BROKER_ASST_DATABROKER_RESILIENCE                                                  0
#define MIN_CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE                                       0
#define MAX_CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE                                       1

#define CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE                                            "config.feature.connector.asst_databroker_resilience"
#define DEFAULT_ASST_DATABROKER_RESILIENCE                                                    0
#define MIN_CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE                                         0
#define MAX_CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE                                         1


/*
 * Feature: Extranet
 * Reference: ET-77206
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_EXTRANET_BROKER_ENABLED                                      "config.feature.extranet.broker.enabled"
#define CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_DEFAULT                              0
#define CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_MIN                                  0
#define CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_MAX                                  1

#define CONFIG_FEATURE_EXTRANET_PSE_ENABLED                                         "config.feature.extranet.pse.enabled"
#define CONFIG_FEATURE_EXTRANET_PSE_ENABLED_DEFAULT                                 0
#define CONFIG_FEATURE_EXTRANET_PSE_ENABLED_MIN                                     0
#define CONFIG_FEATURE_EXTRANET_PSE_ENABLED_MAX                                     1

/*
* Kill Switch to disable extranet feature
*/
#define CONFIG_FEATURE_EXTRANET_HARD_DISABLED                                       "config.feature.extranet.hard_disabled"
#define CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT                               0
#define CONFIG_FEATURE_EXTRANET_HD_MIN                                              0
#define CONFIG_FEATURE_EXTRANET_HD_MAX                                              1

/*
* controls zia_health topic sent to natural/kafka. it doesnt control local log file
*/
#define CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED                                  "config.feature.extranet.zia_health.enabled"
#define CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_DEFAULT                          0
#define CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_MIN                              0
#define CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_MAX                              1

/*
* controls zia_health topic sent to natural/kafka. it doesnt control local log file
*/
#define CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S                                "config.feature.extranet.dns.neg_cache_timeout"
#define CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_DEFAULT                        (2*60)
#define CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MIN                            0
#define CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MAX                            14400

/*
 * Feature: broker to zpm connection configuration flag
 * deafult:0 (disabled), max:1 (enabled), min: 0 (disabled)
 */
#define CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG                           "config.feature.broker.zpm.connection.config"
#define DEFAULT_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG                    0
#define MAX_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG                        1
#define MIN_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG                        0

#define CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC                             "config.feature.broker.baseline_min_version_appc"
#define DEFAULT_CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC                     "NONE"

#define CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE                              "config.feature.broker.baseline_min_version_pse"
#define DEFAULT_CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE                      "NONE"

#define CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE                      "config.feature.exporter.propagate_fin_from_remote"
#define DEFAULT_CONFIG_FEATURE_PROPAGATE_FIN_FROM_REMOTE                        0
#define MAX_CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE                   1
#define MIN_CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE                   0

#define CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT                           "config.feature.exporter.fin_timeout"
#define DEFAULT_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT                   2
#define MAX_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT                       10
#define MIN_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT                       1

/* QBR insights feature flags */
#define CONFIG_FEATURE_CONN_QBR_INSIGHTS_FEATURE                                "config.feature.connector.qbr.enable"
#define CONFIG_FEATURE_PSE_QBR_INSIGHTS_FEATURE                                 "config.feature.pse.qbr.enable"
#define CONFIG_FEATURE_BROKER_QBR_INSIGHTS_FEATURE                              "config.feature.broker.qbr.enable"
#define CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT                             0
#define CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MIN                             0
#define CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MAX                             1


/*
 * Network Presence feature flags
 */
#define NETWORK_PRESENCE_HARD_DISABLED                                              "config.feature.network_presence.hard_disabled"
#define NETWORK_PRESENCE_HARD_DISABLED_DEFAULT                                      0
#define NETWORK_PRESENCE_HARD_DISABLED_VAL_MIN                                      0
#define NETWORK_PRESENCE_HARD_DISABLED_VAL_MAX                                      1

#define NETWORK_PRESENCE_FEATURE                                                    "config.feature.network_presence"
#define NETWORK_PRESENCE_FEATURE_DEFAULT                                            0
#define NETWORK_PRESENCE_FEATURE_VAL_MIN                                            0
#define NETWORK_PRESENCE_FEATURE_VAL_MAX                                            1

/*
 * Network Presence Access Policy feature flags
 */
#define NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED                                "config.feature.np.access_policy.hard_disabled"
#define NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_DEFAULT                        0
#define NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_VAL_MIN                        0
#define NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_VAL_MAX                        1

#define NETWORK_PRESENCE_ACCESS_POLICY_FEATURE                                      "config.feature.np.access_policy"
#define NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_DEFAULT                              0
#define NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_VAL_MIN                              0
#define NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_VAL_MAX                              1


/*
 * Network Presence NP Connector Redundancy feature flags
 */
#define NETWORK_PRESENCE_REDUNDANCY_FEATURE                                         "config.feature.network_presence.redundancy"
#define NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT                                 0
#define NETWORK_PRESENCE_REDUNDANCY_FEATURE_VAL_MIN                                 0
#define NETWORK_PRESENCE_REDUNDANCY_FEATURE_VAL_MAX                                 1


/*
 * Network Presence configurations
 */
#define NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS                          "config.feature.network_presence.keep_alive_timeout_seconds"
#define NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_DEFAULT                  120
#define NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_MIN                      60
#define NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_MAX                      600

/*
 * Network Presence FRR BGP STATS configurations
 */
#define NETWORK_PRESENCE_BGP_STATS_ENABLE                                           "config.feature.network_presence.bgp_stats.enable"
#define NETWORK_PRESENCE_BGP_STATS_ENABLE_DEFAULT                                   0

/* ZPN System Stats configurations*/
#define ZPN_SYSTEM_NET_STATS_ENABLED                                                "config.feature.zpn_sys.netstats.enabled"
#define ZPN_SYSTEM_NET_STATS_ENABLED_DEFAULT                                        0

// Broker dont dump; needs to enabled via command line option; config override is for disabling
#define BROKER_FEATURE_DONT_DUMP                                                    "config.feature.broker.dont_dump"
#define FEATURE_DONT_DUMP_ENABLED                                                   1
#define FEATURE_DONT_DUMP_DISABLED                                                  0
#define FEATURE_DONT_DUMP_DEFAULT                                                   (FEATURE_DONT_DUMP_ENABLED)

/*
 * Feature : Adaptive Access Engine Policy (AAE Policy)
 * Reference: ET-79354
 * Order of priority: Customer, global, default
 * If customer level flag is not set, then global level flag value is considered for each customer.
 * Affected Components: Broker, PSE
 *
 */
#define CONFIG_FEATURE_AAE_PROFILE_FEATURE                                          "config.feature.aae.profile"
#define CONFIG_FEATURE_AAE_PROFILE_FEATURE_DEFAULT                                  0
#define CONFIG_FEATURE_AAE_PROFILE_FEATURE_VAL_MIN                                  0
#define CONFIG_FEATURE_AAE_PROFILE_FEATURE_VAL_MAX                                  1

#define CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED                                    "config.feature.aae.profile.hard_disabled"
#define CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_DEFAULT                            0
#define CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_VAL_MIN                            0
#define CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_VAL_MAX                            1

/* ET-83880: MTunnel Reevaluation on SCIM Updates */
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE                                "config.feature.policy_re_eval_on_scim_update"
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT                        0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_VAL_MIN                        0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_VAL_MAX                        1

#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED                  "config.feature.policy_re_eval_on_scim_update.hard_disabled"
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_DEFAULT          0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_VAL_MIN          0
#define CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_VAL_MAX          1

// Region Restriction
#define ZPN_REGION_RESTRICTION_BROKER                                               "config.feature.region_restriction.broker.enable"
#define ZPN_REGION_RESTRICTION_SNI_BROKER                                           "config.feature.broker.region_restriction.sni.enable"
#define ZPN_REGION_RESTRICTION_EXPORTER                                             "config.feature.region_restriction.exporter.enable"
#define ZPN_REGION_RESTRICTION_HARD_DISABLE                                         "config.feature.region_restriction.hard_disable"
#define ZPN_REGION_RESTRICTION_ENFORCE_SME                                          "config.feature.broker.region_restriction.enforce_sipa"

// FOHH Server Cipher Suite selection configuration
#define CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE                                           "config.feature.fohh.server_cipher_suite"
#define CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_DEFAULT                                    0
#define CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_MIN                                        0
#define CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_MAX                                        (FOHH_SERVER_CIPHER_SUITE_LIST_MAX-1)

// FOHH Client Cipher Suite selection configuration
#define CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE                                            "config.feature.fohh.client_cipher_suite"
#define CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_DEFAULT                                    0
#define CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_MIN                                        0
#define CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_MAX                                        (FOHH_CLIENT_CIPHER_SUITE_LIST_MAX-1)

//Role-based access control(RBAC) overrides
#define CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL                                    "config.feature.role_based_access_control"
#define CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_DEFAULT                            0   //disabled by default
#define CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_MIN                                0   //disabled
#define CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_MAX                                1   //enabled

/*
 * This config gives the total timeout of a double hop connection at the assistant, whcih is equal to:
 * timeout of data broker connection (t1) + timeout of control broker connection (t2).
 * t1: is the time, for which the app connector tries to connect to data broker.
 * t2: is the time, for which the app connector tries to connector to control broker for double hop proxy after it
 * tried the data broker connection (t1). We want to keep ASSISTANT_DOUBLE_HOP_TIMEOUT to 30 sec to match broker side
 * connection timeout which is ZPN_MCONN_TIMEOUT_BRK ie., broker will timeout/terminates a mtunnel after mtunnel is
 * stuck in state < za_complete state for 30 seconds (ZPN_MCONN_TIMEOUT_BRK). So the default timeout of t1 is 20 seconds and
 * t2 is 10 seconds, to make the total time out = 30 sec (ZPN_MCONN_TIMEOUT_BRK)
 */
#define ASSISTANT_DOUBLE_HOP_TIMEOUT                                                "config.feature.assistant_double_hop_timeout_s"
#define DEFAULT_ASST_DOUBLE_HOP_TIMEOUT                                                    (30)   //30 seconds : ZPN_MCONN_TIMEOUT_AST + 10 Sec
#define HIGH_ASST_DOUBLE_HOP_TIMEOUT                                                       (35)   //35 seconds
#define LOW_ASST_DOUBLE_HOP_TIMEOUT                                                        (20)   //20 seconds

/* disable app connector sending route registration to public broker */
#define ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER                                 "config.feature.assistant.disable.public_broker.tx_route_reg"
#define ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER_DEFAULT_VALUE                   0
/*
 * This config gives the maximum time the assistant waits for the databroker connection to
 * come up before switching to double hop
 */
#define ASSISTANT_DOUBLE_HOP_SWITCH_TIMEOUT                                                "config.feature.assistant_double_hop_switch_timeout_s"
#define DEFAULT_ASST_DOUBLE_HOP_SWITCH_TIMEOUT                                                    (20)   //20 seconds : ZPN_MCONN_TIMEOUT_AST
#define HIGH_ASST_DOUBLE_HOP_SWITCH_TIMEOUT                                                       (20)   //20 seconds
#define LOW_ASST_DOUBLE_HOP_SWITCH_TIMEOUT                                                        (5)    //5 seconds

#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                      "config.feature.broker.fohh_connection_disable_read_config_flag"
#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE                        0
#define LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                  0
#define HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                 1

#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                             "config.feature.broker.fohh_connection_disable_read_client_tx_buff_high"
#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT                      (256*1024)
#define LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                          (64*1024)
#define HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                         (2*1024*1024)

#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                              "config.feature.broker.fohh_connection_disable_read_client_tx_buff_low"
#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT                      (128*1024)
#define LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                          (64*1024)
#define HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                         (2*1024*1024)

#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US           "config.feature.broker.fohh_connection_disable_read_client_tx_buff_high_allow_time_max_us"
#define ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT   (300*1000)
#define LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US       100
#define HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US      (5*60*1000*1000)

#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                        "config.feature.pse.fohh_connection_disable_read_config_flag"
#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE                          0
#define LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                    0
#define HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG                                   1

#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                                "config.feature.pse.fohh_connection_disable_read_client_tx_buff_high"
#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT                        (256*1024)
#define LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                            (64*1024)
#define HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH                           (2*1024*1024)

#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                                 "config.feature.pse.fohh_connection_disable_read_client_tx_buff_low"
#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT                         (128*1024)
#define LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                             (64*1024)
#define HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW                            (2*1024*1024)

#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US              "config.feature.pse.fohh_connection_disable_read_client_tx_buff_high_allow_time_max_us"
#define ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT      (300*1000)
#define LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US          100
#define HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US         (5*60*1000*1000)


#define ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S                                         "config.feature.assistant.idle_data_conn_timeout_s"
#define DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S                                 HOUR_TO_US(24) / US_PER_SEC
#define MIN_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S                                     MINUTE_TO_US(10) / US_PER_SEC
#define MAX_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S                                     DAY_TO_US(365) / US_PER_SEC

#define ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE                                   "config.feature.assistant.idle_data_conn_timeout.disable"
#define DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE                           0
#define MIN_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE                               0
#define MAX_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE                               1

#define ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S                            "config.feature.assistant.udp_server_inactivity_timeout_s"
#define ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S                       "config.feature.assistant.udp_server_inactivity_fast_timeout_s"
#define DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S                    60
#define DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S               2
#define MIN_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S                        1
#define MAX_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S                        DAY_TO_US(365) / US_PER_SEC

/*
 * ET-81946 Alt authsp support for IPv6
 * If enabled, broker/PSE/exporter will allow any audience predix matches with any authsp domain configured in zpath_cloud table
 */
#define CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP                                          "config.feature.pse.ipv6_alt_authsp"
#define CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_ENABLED                                  1
#define CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DISABLED                                 0
#define CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DEFAULT                                  CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DISABLED

/*
 * Enabling this config allows for udp health check to return status_timeout_failure
 * in case the UDP server does not respond to UDP health probes sent from Appc instead of
 * returning status_success.
 */
#define ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE                                        "config.feature.assistant.udp_health_timeout_failure"
#define DEFAULT_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE                                0
#define MIN_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE                                    0
#define MAX_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE                                    1

#define ASSISTANT_SARGE_UPGRADE_ENABLE                                              "config.feature.connector.sarge_upgrade_enable"
#define DEFAULT_ASSISTANT_SARGE_UPGRADE_ENABLE                                       0
#define ASSISTANT_SARGE_UPGRADE_ENABLE_MIN                                           0
#define ASSISTANT_SARGE_UPGRADE_ENABLE_MAX                                           1

#define PBROKER_SARGE_UPGRADE_ENABLE                                                "config.feature.pbroker.sarge_upgrade_enable"
#define DEFAULT_PBROKER_SARGE_UPGRADE_ENABLE                                         0
#define PBROKER_SARGE_UPGRADE_ENABLE_MIN                                             0
#define PBROKER_SARGE_UPGRADE_ENABLE_MAX                                             1

#define SITEC_SARGE_UPGRADE_ENABLE                                                  "config.feature.sitec.sarge_upgrade_enable"
#define DEFAULT_SITEC_SARGE_UPGRADE_ENABLE                                           0
#define SITEC_SARGE_UPGRADE_ENABLE_MIN                                               0
#define SITEC_SARGE_UPGRADE_ENABLE_MAX                                               1

#define AUTOMATIC_OS_UPGRADE_ENABLE                                                 "config.feature.auto_os_upgrade_enable"
#define DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE                                          0
#define AUTOMATIC_OS_UPGRADE_ENABLE_MIN                                              0
#define AUTOMATIC_OS_UPGRADE_ENABLE_MAX                                              1

#define AUTOMATIC_FULL_OS_UPGRADE_ENABLE                                             "config.feature.auto_full_os_upgrade_enable"
#define DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE                                      0
#define AUTOMATIC_FULL_OS_UPGRADE_MIN                                                 0
#define AUTOMATIC_FULL_OS_UPGRADE_MAX                                                 1

#define SARGE_BACKUP_VERSION_ENABLE                                                 "config.feature.sarge_backup_enable"
#define DEFAULT_SARGE_BACKUP_VERSION_ENABLE                                          0
#define SARGE_BACKUP_VERSION_ENABLE_MIN                                              0
#define SARGE_BACKUP_VERSION_ENABLE_MAX                                              1

#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED                       "config.feature.assistant.allocator_libevent_out_queue.enabled"
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_DEFAULT               0
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_MIN                   0
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_MAX                   1

#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES                 "config.feature.assistant.allocator_libevent_out_queue.min_len_bytes"
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_DEFAULT         (128 * 1024)
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MIN             (65 * 1024)
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MAX             (255 * 1024)

#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES                 "config.feature.assistant.allocator_libevent_out_queue.max_len_bytes"
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT         (1 * 1024 * 1024)
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MIN             (256 * 1024)
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MAX             (2 * 1024 * 1024)

#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT           "config.feature.assistant.allocator_libevent_out_queue.sys_mem_max_percent"
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_DEFAULT   40
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_MIN       5
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_MAX       70

#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT         "config.feature.assistant.allocator_libevent_out_queue.start_thresh_percent"
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_DEFAULT 50
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_MIN     10
#define CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_MAX     90

/*
 * Feature: Broker - dispatcher circuit breaker
 * Affected Components: Broker
 */
#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE                                   "config.feature.broker.brk_dsp_ckt_breaker.enable"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE                           0
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE                               0
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE                               1

#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE                             "config.feature.broker.brk_dsp_ckt_breaker.hard_disable"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE                     0
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE                         0
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE                         1

#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT                "config.feature.broker.brk_dsp_ckt_breaker.timeout_threshold_percent"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT        10
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT            1
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT            100

#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT          "config.feature.broker.brk_dsp_ckt_breaker.china_timeout_threshold_percent"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT  40
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT      1
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT      100

#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ                    "config.feature.broker.brk_dsp_ckt_breaker.num_dsp_split_dns_req"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ            2
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ                1
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ                19 // (MAX_DISPATCHERS_PER_POOL - 1)

#define CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE               "config.feature.broker.brk_dsp_ckt_breaker.evaluation_interval_minute"
#define DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE       5
#define MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE           5
#define MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE           (24 * 60) // 1 day

#define ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S                                        "config.feature.broker.mtunnel_fin_expire_s"
#define ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT                                (5*60)
#define LOW_ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S                                    30
#define HIGH_ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S                                   (7*60)

#define ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S                                            "config.feature.pse.mtunnel_fin_expire_s"
#define ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT                                    (5*60)
#define LOW_ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S                                        30
#define HIGH_ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S                                       (7*60)

#define ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S                                      "config.feature.assistant.mtunnel_fin_expire_s"
#define ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT                              (5*60)
#define LOW_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S                                      30
#define HIGH_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S                                     (7*60)

/*
 * option to enable app thread heartbeat override
 */
#define CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_BROKER                         "config.feature.broker.app_thread_heartbeat_override"
#define CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_PSE                            "config.feature.pse.app_thread_heartbeat_override"
#define APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT                                       20
#define APP_THREAD_HEARTBEAT_OVERRIDE_MIN                                           20
#define APP_THREAD_HEARTBEAT_OVERRIDE_MAX                                           300

#define ASSISTANT_OAUTH_ENROLL_DISABLE                                              "config.feature.assistant.oauth_enrollment_disable"
#define DEFAULT_ASSISTANT_OAUTH_ENROLL_DISABLE                                      0
#define ASSISTANT_OAUTH_ENROLL_DISABLE_MIN                                          0
#define ASSISTANT_OAUTH_ENROLL_DISABLE_MAX                                          1

#define PSE_OAUTH_ENROLL_DISABLE                                                    "config.feature.pse.oauth_enrollment_disable"
#define DEFAULT_PSE_OAUTH_ENROLL_DISABLE                                            0
#define PSE_OAUTH_ENROLL_DISABLE_MIN                                                0
#define PSE_OAUTH_ENROLL_DISABLE_MAX                                                1

#define SITEC_OAUTH_ENROLL_DISABLE                                                  "config.feature.sitec.oauth_enrollment_disable"
#define DEFAULT_SITEC_OAUTH_ENROLL_DISABLE                                          0
#define SITEC_OAUTH_ENROLL_DISABLE_MIN                                              0
#define SITEC_OAUTH_ENROLL_DISABLE_MAX                                              1

#define CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE                                     "config.feature.network_presence.multi_lan_subnet.enable"
#define DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE                             0
#define LOW_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE                                 0
#define HIGH_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE                                1


#endif /* _ZPATH_CONFIG_OVERRIDE_KEYS_H_ */
