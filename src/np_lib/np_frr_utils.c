/*
 * np_frr_utils.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#define _GNU_SOURCE

#include "np_lib/np_private.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_customer.h"
#include "np_lib/np.h"
#include "np_lib/np_frr_utils.h"
#include "parson/parson.h"
#include "np_lib/np_rpc.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "admin_probe/admin_probe_public.h"

#ifdef __linux__
#include <linux/if.h>
#endif
#include <sys/socket.h>
#include <sys/ioctl.h>

static struct np_stats_frr_monitor g_np_stats_frr_monitor = {0};
struct zpn_np_nw_comp_stats g_np_nw_frr_stats = {0};
struct zpn_np_route_comp_stats g_np_route_frr_stats = {0};

enum zpn_np_instance_type_e g_np_instance_type = zpn_np_instance_type_gateway;

struct zpn_np_cumulative_bgp_peer_stats g_bgp_peer_stats = {0};
struct zpn_np_cumulative_bgp_route_stats g_bgp_route_stats = {0};
struct zpn_np_frr_svc_stats g_frr_svc_stats = {0};

struct zpn_np_cumulative_bgp_peer_stats g_last_reported_bgp_peer_stats = {0};
struct zpn_np_cumulative_bgp_route_stats g_last_reported_bgp_route_stats = {0};
struct zpn_np_frr_svc_stats g_last_reported_frr_svc_stats = {0};

static get_bgp_peer_config_info_cb_f *g_get_bgp_peer_config_info_cb;

void np_frr_release_buf(void *buf) {
    if (buf) {
        NP_BGP_FREE(buf);
        buf = NULL;
    }
}

void np_frr_set_instance_type(enum zpn_np_instance_type_e inst_type) {
    g_np_instance_type = inst_type;
}

/* helper function to copy existing file1 to a new file: file2, overwrite if file2 exists */
int np_frr_util_file_copy(const char *src_file, const char *dest_file)
{
    if (src_file == NULL || dest_file == NULL) {
        NP_LOG(AL_ERROR, "Invalid src file :%s or dest_file:%s", src_file, dest_file);
        return NP_RESULT_BAD_ARGUMENT;
    }

    FILE *src = fopen(src_file, "rb");
    if (!src) {
        NP_LOG(AL_ERROR, "Cannot open source file %s: %s", src_file, strerror(errno));
        return NP_RESULT_ERR;
    }

    // Open destination file in binary write mode
    FILE *dest = fopen(dest_file, "wb");
    if (!dest) {
        NP_LOG(AL_ERROR, "Cannot open dest file %s: %s", dest_file, strerror(errno));
        fclose(src);
        return NP_RESULT_ERR;
    }

    char buffer[4096];
    size_t bytes_read;
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), src)) > 0) {
        fwrite(buffer, 1, bytes_read, dest);
    }

    fclose(src);
    fclose(dest);

    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_validate_interface(const char *interface) {
    int res = NP_RESULT_ERR;
#ifdef __linux__
    int sock;
    struct ifreq ifr;

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        perror("socket");
        return res;
    }
    strncpy(ifr.ifr_name, interface, INTERFACE_MAX_LEN);
    if (ioctl(sock, SIOCGIFFLAGS, &ifr) < 0) {
        perror("ioctl");
        close(sock);
        return res;
    }

    if (ifr.ifr_flags & IFF_UP) {
        res = NP_RESULT_NO_ERROR;
    }
    close(sock);
#else
    NP_LOG(AL_ERROR, "Validation unsupported for non-linux systems, execute validations only on linux systems");
#endif
    return res;
}

static int execute_command(char *arg_cmd, char **out)
{
    char cmd[256] = {0};
    int res = NP_RESULT_NO_ERROR;

    snprintf(cmd, sizeof(cmd), "%s", arg_cmd);

    res = np_frr_util_execute_command(cmd, out);

    return res;
}

static int np_frr_bgp_validate_vrf(char *vrf_name) {
    int res = NP_RESULT_ERR;
#ifdef __linux__
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s \"%s %s\"", VTYSH, SHOW, VRF);
    res = execute_command(cmd, &out_buf);
    if(res || out_buf == NULL) {
        NP_LOG(AL_ERROR, "Error validating VRF Instance");
    } else {
        if (strstr(out_buf, vrf_name) != NULL) {
            NP_FRR_DEBUG("VRF Instance %s is valid", vrf_name);
            res = NP_RESULT_NO_ERROR;
        }
    }
    np_frr_release_buf(out_buf);
#else
    NP_LOG(AL_ERROR, "Validation unsupported for non-linux systems, execute validations only on linux systems");
#endif
    return res;
}

/* Parsing output of systemctl status frr to get frr service status
frr.service - FRRouting
     Loaded: loaded (/usr/lib/systemd/system/frr.service; disabled; preset: disabled)
     Active: active (running) since Tue 2025-05-13 08:15:39 UTC; 2 days ago
       Docs: https://frrouting.readthedocs.io/en/latest/setup.html
   Main PID: 113160 (watchfrr)
     Status: "FRR Operational"
      Tasks: 14 (limit: 198108)
     Memory: 31.9M
        CPU: 44.073s
     CGroup: /system.slice/frr.service
             ├─113160 /usr/lib/frr/watchfrr -d -F traditional zebra mgmtd bgpd staticd
             ├─113171 /usr/lib/frr/zebra -d -F traditional -A 127.0.0.1 -s ********
             ├─113176 /usr/lib/frr/mgmtd -d -F traditional -A 127.0.0.1
             ├─113178 /usr/lib/frr/bgpd -d -F traditional -A 127.0.0.1
             └─113184 /usr/lib/frr/staticd -d -F traditional -A 127.0.0.1
*/
char* np_frr_parse_status() {
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *status = NULL;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, STATUS, FRR);
    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        NP_LOG(AL_ERROR, "Error getting FRR Status for cmd: %s", cmd);
    } else {
        status = np_frr_parse_status_from_output(out_buf, cmd);
    }

    np_frr_release_buf(out_buf);

    return status;
}

char *np_frr_parse_status_from_output(char *out_buf, char *cmd)
{
    if (out_buf == NULL || cmd == NULL) {
        NP_LOG(AL_ERROR, "Could not parse FRR Status");
        return NULL;
    }
    char *status = NULL;
    char *final_status = NULL;
    char *status_line = strstr(out_buf, "Active:");
    if (status_line != NULL) {
        status = strstr(status_line, ": ");
        if (status != NULL) {
            status++;
            while (*status == ' ') {
                status++;
            }

            char *tmp = status;
            while(*tmp != ' ') {
                tmp++;
            }
            *tmp = '\0';
        }
    }
    if (status) {
        final_status = NP_BGP_STRDUP(status, strlen(status));
    } else {
        NP_FRR_DEBUG("cmd: %s did not gave output in expected format\n Output - %s", cmd, out_buf);
    }
    return final_status;
}

/* Parsing the output of /usr/bin/sudo vtysh -c "show version" to get FRR Version
FRRouting 10.2.2 (sonam.khurana-de85c.awslab.dev.zpath.net) on Linux(5.14.0-503.31.1.el9_5.x86_64).
Copyright 1996-2005 Kunihiro Ishiguro, et al.
configured with:
    '--build=x86_64-redhat-linux-gnu' '--host=x86_64-redhat-linux-gnu' '--program-prefix=' '--disable-dependency-tracking' \
    '--prefix=/usr' '--exec-prefix=/usr' '--bindir=/usr/bin' '--datadir=/usr/share' '--includedir=/usr/include' \
    '--libdir=/usr/lib64' '--libexecdir=/usr/libexec' '--sharedstatedir=/var/lib' '--mandir=/usr/share/man' \
    '--infodir=/usr/share/info' '--sbindir=/usr/lib/frr' '--sysconfdir=/etc' '--localstatedir=/var' '--disable-static' \
    '--disable-werror' '--enable-multipath=256' '--enable-vtysh' '--enable-ospfclient' '--enable-ospfapi' '--enable-rtadv' \
    '--enable-ldpd' '--enable-pimd' '--enable-pim6d' '--enable-pbrd' '--enable-nhrpd' '--enable-eigrpd' '--enable-babeld' \
    '--enable-vrrpd' '--enable-user=frr' '--enable-group=frr' '--enable-vty-group=frrvty' '--enable-fpm' '--enable-watchfrr' \
    '--disable-bgp-vnc' '--enable-isisd' '--enable-rpki' '--enable-bfdd' '--enable-pathd' '--disable-grpc' '--enable-snmp' \
    'build_alias=x86_64-redhat-linux-gnu' 'host_alias=x86_64-redhat-linux-gnu' \
    'PKG_CONFIG_PATH=:/usr/lib64/pkgconfig:/usr/share/pkgconfig' 'CC=gcc' 'CXX=g++' 'LT_SYS_LIBRARY_PATH=/usr/lib64:' \
*/
char* np_frr_parse_version() {
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *version = NULL;
    char *final_version = NULL;

    snprintf(cmd, sizeof(cmd), "%s \"%s %s\"", VTYSH, SHOW, VERSION);
    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        NP_LOG(AL_ERROR, "Error getting FRR Version from cmd: %s - %s", cmd, zpath_result_string(res));
    } else {
        char *version_line = strstr(out_buf, "FRRouting");
        if (version_line != NULL) {
            version = version_line + strlen("FRRouting");
            while (version != NULL && *version == ' ') {
                version++;
            }
            char *tmp = version;

            while(*tmp != ' ') {
                tmp++;
            }
            *tmp = '\0';
        }
        if (version) {
            final_version = NP_BGP_STRDUP(version, strlen(version));
        } else {
            NP_FRR_DEBUG("cmd: %s did not gave output in expected format\n Output - %s", cmd, out_buf);
        }
    }

    np_frr_release_buf(out_buf);

    return final_version;
}

/* Parsing output of systemctl status frr to get frr service uptime
frr.service - FRRouting
     Loaded: loaded (/usr/lib/systemd/system/frr.service; disabled; preset: disabled)
     Active: active (running) since Tue 2025-05-13 08:15:39 UTC; 2 days ago
       Docs: https://frrouting.readthedocs.io/en/latest/setup.html
   Main PID: 113160 (watchfrr)
     Status: "FRR Operational"
      Tasks: 14 (limit: 198108)
     Memory: 31.9M
        CPU: 44.073s
     CGroup: /system.slice/frr.service
             ├─113160 /usr/lib/frr/watchfrr -d -F traditional zebra mgmtd bgpd staticd
             ├─113171 /usr/lib/frr/zebra -d -F traditional -A 127.0.0.1 -s ********
             ├─113176 /usr/lib/frr/mgmtd -d -F traditional -A 127.0.0.1
             ├─113178 /usr/lib/frr/bgpd -d -F traditional -A 127.0.0.1
             └─113184 /usr/lib/frr/staticd -d -F traditional -A 127.0.0.1
*/
static int64_t np_frr_parse_uptime_epoch() {
    char cmd[256] = {0};
    char *out_buf = NULL;
    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, STATUS, FRR);
    struct tm time = {0};
    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        NP_LOG(AL_ERROR, "Error getting Uptime - %s", zpath_result_string(res));
    } else {
        char *timestamp = NULL;
        char *since = strstr(out_buf, "since");
        if (since != NULL) {
            timestamp = since + strlen("since");
            while (*timestamp == ' ') {
                timestamp++;
            }
            while(*timestamp != ' ') {
                timestamp++;
            }
        }

        if (timestamp) {
            strptime(timestamp, "%Y-%m-%d %H:%M:%S", &time);
        } else {
            NP_FRR_DEBUG("cmd: %s did not gave output in expected format\n Output - %s", cmd, out_buf);
        }
    }

    int64_t epoch_s = (int64_t) timegm(&time);
    np_frr_release_buf(out_buf);
    return epoch_s;
}

static int np_frr_bgp_dump_interface(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *interface = NULL;
    char *is_out_json = NULL;

    if (query_values && query_values[0]) {
        interface = (char *)query_values[0];
        if (np_frr_bgp_validate_interface(interface) != NP_RESULT_NO_ERROR) {
            ZDP("Interface not configured");
            return NP_RESULT_NO_ERROR;
        }
    } else {
        interface = "";
    }

    if (query_values && query_values[1] && (strcmp(query_values[1], "json") == 0)) {
        is_out_json = (char *)query_values[1];
    } else {
        is_out_json = "";
    }

    snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s\"", VTYSH, SHOW, INTERFACE, interface, is_out_json);
    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_vrf_dump_peer_output(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *peer = NULL;
    char *vrf_name = NULL;
    char *is_out_json = NULL;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, peer, is_out_json);
    } else if (g_np_instance_type == zpn_np_instance_type_gateway) {
        if (query_values && query_values[0]) {
            vrf_name = (char *)query_values[0];
            if (np_frr_bgp_validate_vrf(vrf_name) != NP_RESULT_NO_ERROR) {
                ZDP("VRF %s is invalid", vrf_name);
                return NP_RESULT_ERR;
            }
        } else {
            vrf_name = ALL;
        }
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        } else {
            peer = "";
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, vrf_name, peer, is_out_json);
    } else {
        NP_LOG(AL_ERROR, "invalid system type");
        return NP_RESULT_ERR;
    }

    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_vrf_dump_peer_neighbors(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *peer = NULL;
    char *vrf_name = NULL;
    char *is_out_json = NULL;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        } else {
            peer = "";
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, NEIGHBORS, peer, is_out_json);
    } else if (g_np_instance_type == zpn_np_instance_type_gateway) {
        if (query_values && query_values[0]) {
            vrf_name = (char *)query_values[0];
            if (np_frr_bgp_validate_vrf(vrf_name) != NP_RESULT_NO_ERROR) {
                ZDP("VRF %s is invalid", vrf_name);
                return NP_RESULT_ERR;
            }
        } else {
            vrf_name = ALL;
        }
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        } else {
            peer = "";
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, vrf_name, NEIGHBORS, peer, is_out_json);
    } else {
        NP_LOG(AL_ERROR, "invalid system type");
        return NP_RESULT_ERR;
    }

    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_vrf_dump_routes(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *network = NULL;
    char *is_out_json = NULL;
    char *vrf_name = NULL;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        if (query_values && query_values[1]) {
            network = (char *)query_values[1];
        } else {
            network = "";
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s\"", VTYSH, SHOW, IP, ROUTE, network, is_out_json);
    } else if (g_np_instance_type == zpn_np_instance_type_gateway) {
        if (query_values && query_values[0]) {
            vrf_name = (char *)query_values[0];
            if (np_frr_bgp_validate_vrf(vrf_name) != NP_RESULT_NO_ERROR) {
                ZDP("VRF %s is invalid", vrf_name);
                return NP_RESULT_ERR;
            }
        } else {
            vrf_name = ALL;
        }
        if (query_values && query_values[1]) {
            network = (char *)query_values[1];
        } else {
            network = "";
        }
        if (query_values && query_values[2] && (strcmp(query_values[2], "json") == 0)) {
            is_out_json = (char *)query_values[2];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, ROUTE, VRF, vrf_name, network, is_out_json);
    } else {
        NP_LOG(AL_ERROR, "invalid system type");
        return NP_RESULT_ERR;
    }

    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_vrf_dump_peer_summary(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *is_out_json = NULL;
    char *vrf_name = NULL;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        ZDP("This command is currently not supported on NP Connector");
        return NP_RESULT_NO_ERROR;
    } else if (g_np_instance_type == zpn_np_instance_type_gateway) {
        if (query_values && query_values[0]) {
            vrf_name = (char *)query_values[0];
            if (np_frr_bgp_validate_vrf(vrf_name) != NP_RESULT_NO_ERROR) {
                ZDP("VRF %s is invalid", vrf_name);
                return NP_RESULT_ERR;
            }
        } else {
            vrf_name = ALL;
        }
        if (query_values && query_values[1] && (strcmp(query_values[1], "json") == 0)) {
            is_out_json = (char *)query_values[1];
        } else {
            is_out_json = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, vrf_name, SUMMARY, is_out_json);
    } else {
        NP_LOG(AL_ERROR, "invalid system type");
        return NP_RESULT_ERR;
    }

    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_vrf_clear_peer_routes(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    char *peer = NULL;
    char *vrf_name = NULL;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        } else {
            peer = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s\"", VTYSH, CLEAR, IP, BGP, peer);
    } else if (g_np_instance_type == zpn_np_instance_type_gateway) {
        if (query_values && query_values[0]) {
            vrf_name = (char *)query_values[0];
            if (np_frr_bgp_validate_vrf(vrf_name) != NP_RESULT_NO_ERROR) {
                ZDP("VRF %s is invalid", vrf_name);
                return NP_RESULT_ERR;
            }
        } else {
            vrf_name = ALL;
        }
        if (query_values && query_values[1]) {
            peer = (char *)query_values[1];
        } else {
            peer = "";
        }
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s\"", VTYSH, CLEAR, IP, BGP, VRF, vrf_name, peer);
    } else {
        NP_LOG(AL_ERROR, "invalid system type");
        return NP_RESULT_ERR;
    }

    if(!strncmp(peer, "", strlen(peer))) {
        ZDP("Peer is required to run this command");
        return NP_RESULT_NO_ERROR;
    }

    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_dump_running_config(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s \"%s %s\"", VTYSH, SHOW, RUNNING_CONFIG);
    int res = execute_command(cmd, &out_buf);
    if (res || out_buf == NULL) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("%s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_dump_failed_config(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    // TBD: this is not supported currently
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_start_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, START, FRR);

    if (execute_command(cmd, &out_buf)) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("Execute command \"%s\" success\n", cmd);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_stop_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, STOP, FRR);

    if (execute_command(cmd, &out_buf)) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("Execute command \"%s\" success\n", cmd);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_restart_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, RESTART, FRR);

    if (execute_command(cmd, &out_buf)) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("Execute command \"%s\" success\n", cmd);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_show_status_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char *status = NULL;

    status = np_frr_parse_status();
    if (status) {
        ZDP("FRR status is %s\n", status);
        NP_BGP_FREE(status);
    } else {
        ZDP("Failed to get FRR Service status\n");
    }

    return NP_RESULT_NO_ERROR;
}

static int np_frr_bgp_reload_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, RELOAD, FRR);

    if (execute_command(cmd, &out_buf)) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("Execute command \"%s\" success\n", cmd);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static int np_stats_cumulative_bgp_peers_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count, void *cookie)
{

    char jsonout[4096] = {0};
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_np_cumulative_bgp_peer_stats_description,
                                                        &g_last_reported_bgp_peer_stats, jsonout,
                                                        sizeof(jsonout), NULL, 1)){
        ZDP("NP %s cumulative bgp peer stats: %s\n",
                 ((g_np_instance_type == zpn_np_instance_type_connector) ? "Connector" : "Gateway"), jsonout);
    }

    return NP_RESULT_NO_ERROR;
}

static int np_stats_cumulative_bgp_routes_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count, void *cookie)
{

    char jsonout[4096] = {0};
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_np_cumulative_bgp_route_stats_description,
                                                        &g_last_reported_bgp_route_stats, jsonout,
                                                        sizeof(jsonout), NULL, 1)){
        ZDP("NP %s cumulative bgp route stats: %s\n",
                 ((g_np_instance_type == zpn_np_instance_type_connector) ? "Connector" : "Gateway"), jsonout);
    }

    return NP_RESULT_NO_ERROR;
}

static int np_stats_comprehensive_bgp_stats_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count, void *cookie)
{

    char jsonout[8192] = {0};
    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    if (g_np_nw_frr_stats.update_id == 0) {
        ZDP("NP comprehensive bgp stats isn't available. Try again later...\n");
    } else if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_np_nw_comp_stats_description,
                                                        &g_np_nw_frr_stats, jsonout,
                                                        sizeof(jsonout), NULL, 1)) {
        ZDP("NP %s comprehensive bgp stats: %s\n",
                 ((g_np_instance_type == zpn_np_instance_type_connector) ? "Connector" : "Gateway"), jsonout);
    }
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    return NP_RESULT_NO_ERROR;
}

static int np_stats_comprehensive_bgp_route_stats_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count, void *cookie)
{

    char jsonout[8192] = {0};
    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    if (g_np_route_frr_stats.bgp_routes_count == 0 || g_np_route_frr_stats.ip_routes_count == 0) {
        ZDP("NP comprehensive bgp route stats isn't available. Try again later...\n");
    } else if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_np_route_comp_stats_description,
                                                        &g_np_route_frr_stats, jsonout,
                                                        sizeof(jsonout), NULL, 1)) {
        ZDP("NP %s comprehensive bgp route stats: %s\n",
                 ((g_np_instance_type == zpn_np_instance_type_connector) ? "Connector" : "Gateway"), jsonout);
    }
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    return NP_RESULT_NO_ERROR;
}

static int64_t is_valid_number(const char *number) {
    if(number == NULL || *number == '\0') {
        return -1;
    }

    char *endptr;
    int64_t val = strtol(number, &endptr, 10);
    if (*endptr != '\0') {
        return -1;
    }

    return val;
}

static int np_frr_get_logs_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    char cmd[256] = {0};
    char *out_buf = NULL;
    int64_t since = 0;
    int64_t until = 0;

    snprintf(cmd, sizeof(cmd) - strlen(cmd), " %s -u %s -n", JOURNALCTL, FRR);

    if(query_values && query_values[0]) {
        int64_t num_lines = is_valid_number(query_values[0]);
        if (num_lines == -1 || num_lines > MAX_LOG_LINES) {
            ZDP("Provide valid format for -n flag, integer less than %d", MAX_LOG_LINES);
            return NP_RESULT_NO_ERROR;
        }
        snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), " %s", query_values[0]);
    } else {
        snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), " %d", DEFAULT_LOG_LINES);
    }
    if (query_values && query_values[1]) {
        since = is_valid_number(query_values[1]);
        if (since == -1) {
            ZDP("Provide valid format for --since flag");
            return NP_RESULT_NO_ERROR;
        }
        snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), " --since \"%s minutes ago\"", query_values[1]);
    }
    if (query_values && query_values[2]) {
        until = is_valid_number(query_values[2]);
        if (until == -1) {
            ZDP("Provide valid argument for --until flag");
            return NP_RESULT_NO_ERROR;
        }
        snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), " --until \"%s minutes ago\"", query_values[2]);
    }
    if (query_values && query_values[1] && query_values[2] && since < until) {
        ZDP("Error: --until ('%s') must be after --since ('%s').\n", query_values[1], query_values[2]);
        return NP_RESULT_NO_ERROR;
    }

    snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), " --no-pager");

    if (execute_command(cmd, &out_buf)) {
        ZDP("Execute command \"%s\" failed\n", cmd);
    } else {
        ZDP("Execute command \"%s\" success\n", cmd);
        ZDP("Output - \n %s", out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_RESULT_NO_ERROR;
}

static uint8_t np_stats_get_sys_type() {
    uint8_t systype = 0;
    if (g_np_instance_type == zpn_np_instance_type_gateway) {
        systype = NP_STATS_SYSTYPE_INT_GATEWAY;
    } else {
        systype = NP_STATS_SYSTYPE_INT_CONNECTOR;
    }
    return systype;
}

static int np_cumulative_bgp_peer_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_np_cumulative_bgp_peer_stats *out_data = (struct zpn_np_cumulative_bgp_peer_stats *)structure_data;

    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    memcpy(out_data, &g_bgp_peer_stats, sizeof(g_bgp_peer_stats));
    out_data->sys_type = np_stats_get_sys_type();
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    return NP_RESULT_NO_ERROR;
}

static int np_cumulative_bgp_route_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_np_cumulative_bgp_route_stats *out_data = (struct zpn_np_cumulative_bgp_route_stats *)structure_data;

    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    memcpy(out_data, &g_bgp_route_stats, sizeof(g_bgp_route_stats));
    out_data->sys_type = np_stats_get_sys_type();
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    return NP_RESULT_NO_ERROR;
}

static int np_frr_svc_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_np_frr_svc_route_stats *out_data = (struct zpn_np_frr_svc_route_stats *)structure_data;

    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    memcpy(out_data, &g_frr_svc_stats, sizeof(g_frr_svc_stats));
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    return NP_RESULT_NO_ERROR;
}

static void np_frr_stats_config_override_monitor() {
    g_np_stats_frr_monitor.np_frr_stats_monitor_enabled = zpath_config_override_get_config_int(NETWORK_PRESENCE_BGP_STATS_ENABLE,
                                                          &g_np_stats_frr_monitor.np_frr_stats_monitor_enabled,
                                                          NETWORK_PRESENCE_BGP_STATS_ENABLE_DEFAULT,
                                                          g_np_stats_frr_monitor.inst_gid,
                                                          g_np_stats_frr_monitor.customer_gid,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);

    zpath_config_override_monitor_int(NETWORK_PRESENCE_BGP_STATS_ENABLE,
                                      &g_np_stats_frr_monitor.np_frr_stats_monitor_enabled,
                                      NULL,
                                      NETWORK_PRESENCE_BGP_STATS_ENABLE_DEFAULT,
                                      g_np_stats_frr_monitor.inst_gid,
                                      g_np_stats_frr_monitor.customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

int np_frr_start(char *out_buf, size_t out_buf_len)
{
    char cmd[256] = {0};
    char *buf = NULL;
    int res = NP_RESULT_NO_ERROR;

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, START, FRR);

    res = execute_command(cmd, &buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute command: %s: %s", cmd, zpath_result_string(res));
    }
    if (out_buf) snprintf(out_buf, out_buf_len, "%s", buf);

    np_frr_release_buf(buf);
    return NP_RESULT_NO_ERROR;
}

/* move config to NP_BGP_FRR_CONFIG_FILENAME and reload it */
static int np_frr_reload(const char *config_file_loc, char *out_buf, size_t out_buf_len)
{
    char cmd[256] = {0};
    char *buf = NULL;
    int res = NP_RESULT_NO_ERROR;

    res = np_frr_util_file_copy(config_file_loc, NP_BGP_FRR_CONFIG_FILENAME);
    if (res) {
        NP_LOG(AL_ERROR, ": Could not copy %s to %s: %s", config_file_loc, NP_BGP_FRR_CONFIG_FILENAME, strerror(errno));
        return res;
    }

    snprintf(cmd, sizeof(cmd), "%s --%s --%s %s", FRR_RELOAD, STDOUT, RELOAD, NP_BGP_FRR_CONFIG_FILENAME);

    res = execute_command(cmd, &buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute command: %s: %s", cmd, zpath_result_string(res));
    }
    if (out_buf) snprintf(out_buf, out_buf_len, "%s", buf);

    np_frr_release_buf(buf);
    return res;
}

/* return config status of test reload */
int np_frr_test_config_status(const char *config_file_loc, int *is_config_valid)
{
    char cmd[256] = {0};
    char *buf = NULL;
    int res = NP_RESULT_NO_ERROR;

    if (is_config_valid == NULL) {
        NP_LOG(AL_ERROR, "Invalid argument");
        return NP_RESULT_BAD_ARGUMENT;
    }

    snprintf(cmd, sizeof(cmd), "%s --%s --%s %s", FRR_RELOAD, STDOUT, TEST, config_file_loc);

    res = execute_command(cmd, &buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute command: %s: %s", cmd, zpath_result_string(res));
    }
    if (!buf) return NP_RESULT_ERR;
    if (res) {
        np_frr_release_buf(buf);
        return res;
    }

    if (strstr(buf, "ERROR:") != NULL) {
        *is_config_valid = 0;
    } else {
        // No Error log found in config test validation output
        *is_config_valid = 1;
    }

    np_frr_release_buf(buf);
    return res;
}

/* test reload config, record error if seen */
static int np_frr_test_reload(const char    *config_file_loc,
                              const char    *error_config_file_loc,
                              const char    *error_log_file_loc,
                              char          *out_buf,
                              size_t        out_buf_len)
{
    char cmd[256] = {0};
    char *buf = NULL;
    int res = NP_RESULT_NO_ERROR;

    snprintf(cmd, sizeof(cmd), "%s --%s --%s %s", FRR_RELOAD, STDOUT, TEST, config_file_loc);

    res = execute_command(cmd, &buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute command: %s: %s", cmd, zpath_result_string(res));
    }
    if (!buf) return NP_RESULT_ERR;
    if (res) {
        np_frr_release_buf(buf);
        return res;
    }

    if (out_buf) snprintf(out_buf, out_buf_len, "%s", buf);

    if (strstr(buf, "ERROR:") != NULL) {
        /* test reload returned error */
        int rc = NP_RESULT_NO_ERROR;
        FILE *fp;
        fp = fopen(error_log_file_loc, "w");
        if (!fp) {
            NP_LOG(AL_ERROR, "Failed to open file %s to write test reload output: %s", error_log_file_loc, strerror(errno));
            rc = NP_RESULT_ERR;
        } else {
            if (fwrite(buf, strlen(buf), 1, fp) != 1) {
                NP_LOG(AL_ERROR, "Failed to write test reload output to file %s: %s", error_log_file_loc, strerror(errno));
                rc = NP_RESULT_ERR;
            }
            fclose(fp);
        }
        if (rc) res = rc;

        rc = np_frr_util_file_copy(config_file_loc, error_config_file_loc);
        if (rc) {
            NP_LOG(AL_ERROR, ": Could not copy %s to %s: %s", config_file_loc, error_config_file_loc, zpath_result_string(rc));
        }
        if (rc) res = rc;
    }

    np_frr_release_buf(buf);
    return res;
}

/* move frr_conf_filename to NP_BGP_FRR_CONFIG_FILENAME and reload it */
static int np_frr_force_reload(const char *config_file_loc, char *out_buf, size_t out_buf_len)
{
    char cmd[256] = {0};
    char *buf = NULL;
    int res = NP_RESULT_NO_ERROR;

    res = np_frr_util_file_copy(config_file_loc, NP_BGP_FRR_CONFIG_FILENAME);
    if (res) {
        NP_LOG(AL_ERROR, ": Could not copy %s to %s: %s", config_file_loc, NP_BGP_FRR_CONFIG_FILENAME, strerror(errno));
        return res;
    }

    snprintf(cmd, sizeof(cmd), "%s %s %s", SYSTEMCTL, RELOAD, FRR);

    res = execute_command(cmd, &buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute command: %s: %s", cmd, zpath_result_string(res));
    }
    if (out_buf) snprintf(out_buf, out_buf_len, "%s", buf);

    np_frr_release_buf(buf);
    return res;
}

/* copy NP_BGP_FRR_CONFIG_FILENAME to NP_BGP_FRR_CONFIG_FILENAME_PREV */
static int np_frr_save_prev_conf()
{
    return np_frr_util_file_copy(NP_BGP_FRR_CONFIG_FILENAME, NP_BGP_FRR_CONFIG_FILENAME_PREV);
}

/*
 * Test reload the config file, if test succeeded, reload it, otherwise do sysctl frr reload only if force_reload is set
 */
int np_frr_reload_conf(int use_override, int force_reload, char *out_buf, size_t out_buf_len)
{
    int res = NP_RESULT_NO_ERROR;

    res = np_frr_test_reload(use_override ? NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE : NP_BGP_FRR_CONFIG_FILENAME_GENERATED,
                             use_override ? NP_BGP_FRR_ERROR_CONFIG_FILENAME_OVERRIDE : NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED,
                             use_override ? NP_BGP_FRR_ERROR_LOG_FILENAME_OVERRIDE : NP_BGP_FRR_ERROR_LOG_FILENAME_GENERATED,
                             out_buf, out_buf_len);
    if (res) {
        NP_LOG(AL_ERROR, "Test reload frr config file %s failed: %s, output: %s",
                            use_override ? NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE : NP_BGP_FRR_CONFIG_FILENAME_GENERATED,
                            zpath_result_string(res), out_buf);
        if (!force_reload) {
            return res;
        }
    }

    /* save previous frr config */
    res = np_frr_save_prev_conf();
    if (res) {
        NP_LOG(AL_ERROR, "Failed to save frr config to previous: %s", zpath_result_string(res));
    }

    if (force_reload) {
        res = np_frr_force_reload(use_override ? NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE : NP_BGP_FRR_CONFIG_FILENAME_GENERATED,
                                    out_buf, out_buf_len);
    } else {
        res = np_frr_reload(use_override ? NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE : NP_BGP_FRR_CONFIG_FILENAME_GENERATED,
                                out_buf, out_buf_len);
    }
    return res;
}

static int np_frr_utils_stats_register() {

    /* Initialize fd and socket statistics. */
    struct argo_log_registered_structure *s = NULL;

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                   "zpn_np_cumulative_bgp_peer_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpn_np_cumulative_bgp_peer_stats_description,
                                    &g_last_reported_bgp_peer_stats,
                                    1,
                                    np_cumulative_bgp_peer_stats_fill,
                                    NULL);

    if (!s) {
        NP_LOG(AL_CRITICAL, "Could not register zpn_np_cumulative_bgp_peer_stats.. statistics_log not initialized?");
        return NP_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                   "zpn_np_cumulative_bgp_route_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpn_np_cumulative_bgp_route_stats_description,
                                    &g_last_reported_bgp_route_stats,
                                    1,
                                    np_cumulative_bgp_route_stats_fill,
                                    NULL);

    if (!s) {
        NP_LOG(AL_CRITICAL, "Could not register zpn_np_cumulative_bgp_route_stats. statistics_log not initialized?");
        return NP_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                   "zpn_np_frr_svc_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(1), /* 1 mins interval */
                                    zpn_np_frr_svc_stats_description,
                                    &g_last_reported_frr_svc_stats,
                                    1,
                                    np_frr_svc_stats_fill,
                                    NULL);

    if (!s) {
        NP_LOG(AL_CRITICAL, "Could not register zpn_np_frr_svc_stats. statistics_log not initialized?");
        return NP_RESULT_ERR;
    }

    return NP_RESULT_NO_ERROR;
}

int np_frr_utils_init(enum zpn_np_instance_type_e inst_type, int64_t inst_gid,
                 int64_t customer_id, get_bgp_peer_config_info_cb_f *get_bgp_peer_config_info_cb) {

    int res = 0;

    if (g_np_stats_frr_monitor.initialized) {
        return NP_RESULT_NO_ERROR;
    }

    np_frr_set_instance_type(inst_type);

    g_np_stats_frr_monitor.np_frr_stats_lock = ZPATH_MUTEX_INIT;

    g_np_stats_frr_monitor.nw_stats_update_id = epoch_us();
    g_np_stats_frr_monitor.route_stats_update_id = epoch_s();

    g_np_stats_frr_monitor.customer_gid = customer_id;
    g_np_stats_frr_monitor.inst_gid = inst_gid;

    if (get_bgp_peer_config_info_cb) {
        g_get_bgp_peer_config_info_cb = get_bgp_peer_config_info_cb;
    }

    np_frr_stats_config_override_monitor();

    np_stats_frr_monitor_init();

    res = zpath_debug_add_read_command("NP stats cumulative bgp peers",
                                  "/np/frr/stats/cumulative_bgp_peers",
                                  np_stats_cumulative_bgp_peers_dump,
                                  NULL,
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init /np/frr/stats/cumulative_bgp_peers debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP stats cumulative bgp routes",
                                       "/np/frr/stats/cumulative_bgp_routes",
                                       np_stats_cumulative_bgp_routes_dump,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init /np/frr/stats/cumulative_bgp_routes debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP stats bgp frr comprehensive stats",
                                  "/np/frr/stats/np_nw_comprehensive",
                                  np_stats_comprehensive_bgp_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init /np/frr/stats/np_nw_comprehensive debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP stats bgp frr route comprehensive stats",
                                  "/np/frr/stats/np_route_comprehensive",
                                  np_stats_comprehensive_bgp_route_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init /np/frr/stats/np_route_comprehensive debug command");
        return NP_RESULT_ERR;
    }

    // NPGW FRR CLI
    res = zpath_debug_add_read_command("Dump the interfaces seen by FRR for interface",
                                       "/np/frr/ip_interfaces",
                                       np_frr_bgp_dump_interface,
                                       NULL,
                                       "interface", "interface: default value - all",
                                       "format", "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not register /np/frr/ip_interfaces debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump BGP VRF output for peer",
                                       "/np/frr/ip_bgp",
                                       np_frr_bgp_vrf_dump_peer_output,
                                       NULL,
                                       "vrf_name", "vrf_name: default value - all",
                                       "peer", "peer: default value - all",
                                       "format", "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not register /np/frr/ip_bgp debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump BGP VRF neighbors for peer",
                                       "/np/frr/ip_bgp_neighbors",
                                       np_frr_bgp_vrf_dump_peer_neighbors,
                                       NULL,
                                       "vrf_name", "vrf_name: default value - all",
                                       "peer", "peer: default value - all",
                                       "format", "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not register /np/frr/ip_bgp_neighbors debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump VRF routes seen by FRR",
                                       "/np/frr/ip_route",
                                       np_frr_bgp_vrf_dump_routes,
                                       NULL,
                                       "vrf_name", "vrf_name: default value - all",
                                       "network", "network: default value - all",
                                       "format", "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not register /np/frr/ip_route debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump BGP VRF summary for peer",
                                       "/np/frr/ip_bgp_summary",
                                       np_frr_bgp_vrf_dump_peer_summary,
                                       NULL,
                                       "vrf_name", "vrf_name: default value - all",
                                       "format", "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not register /np/frr/ip_bgp_summary debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("Clear BGP VRF routes for peer",
                                       "/np/frr/clear_bgp",
                                       np_frr_bgp_vrf_clear_peer_routes,
                                       NULL,
                                       "vrf_name", "vrf_name: default value - all",
                                       "peer", "peer: default value - all",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/clear_bgp debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump BGP running config from FRR",
                                       "/np/frr/running_config",
                                       np_frr_bgp_dump_running_config,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/running_config debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Show last generated BGP config that failed to apply to BGP service",
                                       "/np/frr/failed_config",
                                       np_frr_bgp_dump_failed_config,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/failed_config debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Start the FRR service",
                                       "/np/frr/bgp_start",
                                       np_frr_bgp_start_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/bgp_start debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Stop the FRR service",
                                       "/np/frr/bgp_stop",
                                       np_frr_bgp_stop_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/bgp_stop debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Restart the FRR service",
                                       "/np/frr/bgp_restart",
                                       np_frr_bgp_restart_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/bgp_rsetart debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Check the status of FRR service",
                                       "/np/frr/bgp_status",
                                       np_frr_bgp_show_status_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/bgp_status debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Reload of FRR service",
                                       "/np/frr/bgp_reload",
                                       np_frr_bgp_reload_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/bgp_reload debug command");
        return NP_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Get FRR Service Logs",
                                       "/np/frr/get_logs",
                                       np_frr_get_logs_callback,
                                       NULL,
                                       "number", "-n: default value - 1000",
                                       "since", "--since: optional",
                                       "until", "--until: optional",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Could not init np/frr/get_logs debug command");
        return NP_RESULT_ERR;
    }

    g_np_stats_frr_monitor.initialized = 1;
    return NP_RESULT_NO_ERROR;
}

int np_frr_util_execute_command(const char *command, char **out) {
    FILE *fp = NULL;                                // File pointer for popen
    char buffer[4096] = {0};                               // Temporary buffer for reading chunks
    size_t output_size = INITIAL_BUFFER_SIZE;
    size_t used_size = 0;

    if (command == NULL) {
        NP_LOG(AL_ERROR, "Invalid command argument");
        return NP_RESULT_ERR;
    }
    NP_LOG(AL_INFO, "Executing command : %s", command);

    // Open a pipe to execute the command
    fp = popen(command, "r");
    if (fp == NULL) {
        NP_LOG(AL_ERROR, "popen failed for command:%s error:%s", command, strerror(errno));
        return NP_RESULT_ERR;
    }

    // Allocate initial output buffer
    *out = (char *)NP_BGP_CALLOC(output_size);
    if (*out == NULL) {
        NP_LOG(AL_ERROR, "malloc failed for command:%s", command);
        pclose(fp);
        return NP_RESULT_ERR;
    }

    // Read popen output in chunks and dynamically grow the output buffer if needed
    while (fgets(buffer, sizeof(buffer) - 1, fp) != NULL) {
        size_t chunk_length = strnlen(buffer, sizeof(buffer));

        // Check if there's enough space in the allocated output buffer
        if (used_size + chunk_length >= output_size) {
            // Double the output buffer size
            output_size *= 2;
            *out = (char *) NP_REALLOC(*out, output_size);
            if (*out == NULL) {
                NP_LOG(AL_ERROR, "realloc failed for command:%s, bytes_used:%zu bytes_required:%zu",
                                                                     command, used_size, chunk_length);
                pclose(fp);
                return NP_RESULT_ERR;
            }
        }

        // Append the chunk to the buffer
        strncpy(*out + used_size, buffer, output_size - used_size);
        used_size += chunk_length;
    }

    // Close the pipe
    if (pclose(fp) == -1) {
        return NP_RESULT_ERR;
    }

    if (used_size) {
        // Null-terminate the final buffer
        *(*(out) + used_size) = '\0';
        NP_FRR_DEBUG("Command:%s output_len:%zu", command, used_size);
        NP_FRR_DEBUG("Output:%s", *out);
    } else {
        NP_FRR_DEBUG("No output for Command:%s", command);
        NP_BGP_FREE(*out);
        *out = NULL;
    }
    return NP_RESULT_NO_ERROR;
}

/**
 * Write a large buffer to a file in chunks.
 *
 * @param buffer: Pointer to the data to be written.
 * @param buffer_size: Size of the buffer in bytes.
 * @param file_path: File path where the data will be written.
 *
 * @return: NP_RESULT_NO_ERROR on success, or a error code on failure.
 */
int np_frr_util_write_buffer_to_file(const char *buffer, size_t buffer_size, const char *file_path) {
    if (buffer == NULL || file_path == NULL || buffer_size == 0) {
        // Invalid input parameters
        NP_LOG(AL_ERROR, "Invalid argument");
        return NP_RESULT_BAD_ARGUMENT;
    }

    // Open the file for writing
    FILE *file = fopen(file_path, "w");
    if (file == NULL) {
        // Failed to open the file
        NP_LOG(AL_ERROR, "Failed to open file:%s for writting, error:%s", file_path, strerror(errno));
        return NP_RESULT_BAD_ARGUMENT;
    }

    // Define the chunk size
    const size_t chunk_size = INITIAL_BUFFER_SIZE; // 64 KB chunks
    size_t bytes_written = 0;
    size_t bytes_to_write = 0;

    // Write the buffer in chunks
    while (bytes_written < buffer_size) {
        // Calculate the size of the chunk to write
        bytes_to_write = (buffer_size - bytes_written) < chunk_size
                             ? (buffer_size - bytes_written)
                             : chunk_size;

        // Write the current chunk to the file
        size_t written = fwrite(buffer + bytes_written, 1, bytes_to_write, file);
        if (written != bytes_to_write) {
            // Error writing to the file
            NP_LOG(AL_ERROR, "Failed to write a chunk to the file, bytes to_write:%zu written:%zu error:%s",
                                                         bytes_to_write, written, strerror(errno));
            fclose(file);
            return NP_RESULT_CANT_WRITE;
        }

        // Update the total bytes written
        bytes_written += written;
    }

    // Close the file
    if (fclose(file) != 0) {
        // Error closing the file
        NP_LOG(AL_ERROR, "Failed to close the file:%s error:%s", file_path, strerror(errno));
        return NP_RESULT_ERR;
    }

    // Successful operation
    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_bgp_get_logs_cmd(int number, int since_mins,
                             int until_mins, char *cmd, int size_cmd) {
    if (cmd == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    snprintf(cmd, size_cmd, "%s -u %s -n %d", JOURNALCTL, FRR, number);
    if (since_mins) {
        snprintf(cmd + strlen(cmd), size_cmd - strlen(cmd), " --since \"%d minutes ago\"", since_mins);
    }

    if (until_mins) {
        snprintf(cmd + strlen(cmd), size_cmd - strlen(cmd), " --until \"%d minutes ago\"", until_mins);
    }

    snprintf(cmd + strlen(cmd), size_cmd - strlen(cmd), " --no-pager");

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_bgp_config_validate_cmd(int config_type, char *cmd, int size_cmd) {

    char *config_file_loc = NULL;

    if (config_type == zpn_frr_config_type_generated) {
        config_file_loc = NP_BGP_FRR_CONFIG_FILENAME_GENERATED;
    } else {
        config_file_loc = NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE;
    }

    snprintf(cmd, size_cmd, "%s --%s --%s %s", FRR_RELOAD, STDOUT, TEST, config_file_loc);

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_bgp_running_config_cmd(char *cmd, int size_cmd) {
    if (cmd == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    snprintf(cmd, size_cmd, "%s \"%s %s\"",
                        VTYSH, SHOW, RUNNING_CONFIG);

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_clear_ip_bgp_cmd(char *target, char *cmd, int size_cmd) {

    int is_target_all = 0;
    if (target == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    if (strncmp(target, ALL, strlen(ALL)) == 0) {
        is_target_all = 1;
    }

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s\"",
                            VTYSH, CLEAR, IP, BGP, is_target_all ? "*" : target);
    } else {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s\"",
                        VTYSH, CLEAR, IP, BGP, VRF, NP_VRF_NAME, is_target_all ? "*" : target);
    }

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_stop_bgp_cmd(char *cmd, int size_cmd) {

    snprintf(cmd, size_cmd, "%s %s %s", SYSTEMCTL, STOP, FRR);
    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_start_bgp_cmd(char *cmd, int size_cmd) {

    snprintf(cmd, size_cmd, "%s %s %s", SYSTEMCTL, START, FRR);
    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_restart_bgp_cmd(char *cmd, int size_cmd) {

    snprintf(cmd, size_cmd, "%s %s %s", SYSTEMCTL, RESTART, FRR);
    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_status_bgp_cmd(char *cmd, int size_cmd) {

    snprintf(cmd, size_cmd, "%s %s %s", SYSTEMCTL, STATUS, FRR);
    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_show_ip_bgp_summary_cmd(char *cmd, int size_cmd) {

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s\"",
                            VTYSH, SHOW, IP, BGP, ALL, SUMMARY, JSON);
    } else {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s %s\"",
                    VTYSH, SHOW, IP, BGP, VRF, ALL, SUMMARY, JSON);
    }

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_show_ip_bgp_neighbors_cmd(char *target, char *cmd, int size_cmd) {
    int is_target_all = 0;

    if (target == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    if (strncmp(target, ALL, strlen(ALL)) == 0) {
        is_target_all = 1;
    }

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s\"",
                            VTYSH, SHOW, IP, BGP, NEIGHBORS, is_target_all ? "" : target , JSON);
    } else {
        if (is_target_all) {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, BGP, VRF, ALL, NEIGHBORS, JSON);
        } else {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, BGP, VRF, NP_VRF_NAME, NEIGHBORS, target, JSON);
        }
    }

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_show_ip_bgp_cmd(char *target, char *cmd, int size_cmd) {
    int is_target_all = 0;

    if (target == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    if (strncmp(target, ALL, strlen(ALL)) == 0) {
        is_target_all = 1;
    }

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s\"",
                            VTYSH, SHOW, IP, BGP, target, JSON);
    } else {
        if (is_target_all) {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, BGP, VRF, ALL, JSON);
        } else {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, BGP, VRF, NP_VRF_NAME, target, JSON);
        }
    }

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_show_ip_route_cmd(char *target, char *cmd, int size_cmd) {
    int is_target_all = 0;

    if (target == NULL) {
        return NP_RESULT_BAD_DATA;
    }

    if (strncmp(target, ALL, strlen(ALL)) == 0) {
        is_target_all = 1;
    }

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s\"",
                            VTYSH, SHOW, IP, ROUTE, is_target_all? "" : target, JSON);
    } else {
        if (is_target_all) {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, ROUTE, VRF, ALL, JSON);
        } else {
            snprintf(cmd, size_cmd, "%s \"%s %s %s %s %s %s %s\"",
                        VTYSH, SHOW, IP, ROUTE, VRF, NP_VRF_NAME, target, JSON);
        }
    }

    return NP_RESULT_NO_ERROR;
}

int zpn_np_prepare_show_ip_interface_cmd(char *interface, char *cmd, int size_cmd) {
    // Validate Interface
    if (interface) {
        snprintf(cmd, size_cmd, "%s \"%s %s %s %s\"", VTYSH, SHOW, INTERFACE, interface, JSON);
    } else {
        snprintf(cmd, size_cmd, "%s \"%s %s %s\"", VTYSH, SHOW, INTERFACE, JSON);
    }
    return NP_RESULT_NO_ERROR;
}

/**
 * @brief Parses a JSON string and retrieves the root JSON object.
 *
 * This function takes a JSON string as input, parses it, and retrieves the root JSON object.
 * If the JSON string is invalid, or if the root object cannot be extracted, the function returns NULL.
 * For a "gateway" instance type, a specific VRF JSON object may override the root object.
 *
 * @param json_str Pointer to the JSON string to be parsed.
 * @param out_root_value Output pointer to return the JSON root value to the caller.
 * @return Pointer to the root JSON object if successful, or NULL if an error occurs.
 */
static JSON_Object *np_stats_get_root_object_json(const char *json_str, JSON_Value **out_root_value) {
    JSON_Value  *json           = NULL;         // Holds the parsed JSON value.
    JSON_Object *root_object    = NULL; // Points to the root object in the JSON structure.
    JSON_Object *vrf_object     = NULL;

    // Validate the input JSON string.
    if (json_str == NULL || out_root_value == NULL) {
        return NULL; // Return NULL if the input JSON string is invalid or NULL.
    }

    // Parse the JSON string into a JSON value structure.
    json = json_parse_string(json_str);
    if (!json) {
        NP_FRR_DEBUG("Unable to parse %s", json_str);
        return NULL; // Return NULL if parsing the JSON string fails.
    }

    // Extract the root object from the parsed JSON value.
    root_object = json_value_get_object(json);
    if (!root_object) {
        json_value_free(json); // Free the allocated memory for the JSON value before returning.
        return NULL;
    }

    // For gateway-specific functionality, handle VRF object extraction.
    if (g_np_instance_type == zpn_np_instance_type_gateway) {
        // Attempt to retrieve the VRF object within the root object. Only for Gateway
        vrf_object = json_object_get_object(root_object, NP_VRF_NAME);
        if (vrf_object) {
            root_object = vrf_object;
        }
    }

    *out_root_value = json;

    // Return the pointer to the root JSON object.
    return root_object;
}

static int64_t uptime_str_to_seconds(const char *uptime) {
    int64_t total_seconds = 0;

    if (strchr(uptime, ':')) {
        // Format: "HH:MM:SS" or "HH:MM"
        int64_t hours = 0, minutes = 0, seconds = 0;
        char *time_str = NP_BGP_STRDUP(uptime, strlen(uptime));  // Duplicate string to safely tokenize
        char *saveptr;

        char *token = strtok_r(time_str, ":", &saveptr);
        if (token != NULL) {
            hours = atoi(token); // Extract hours
            token = strtok_r(NULL, ":", &saveptr);

            if (token != NULL) { // Extract minutes
                minutes = atoi(token);
                token = strtok_r(NULL, ":", &saveptr);

                if (token != NULL) { // Extract seconds if HH:MM:SS format
                    seconds = atoi(token);
                }
            }
        }
        NP_BGP_FREE(time_str); // Free duplicated string
        total_seconds = hours * SECONDS_IN_HOUR + minutes * SECONDS_IN_MINUTE + seconds;
    } else {
        // Format: "XwXdXhXmXs"
        const char *ptr = uptime;
        int64_t value = 0;
        while (*ptr) {
            if (isdigit(*ptr)) {
                value = value * 10 + (*ptr - '0'); // Build numeric value
            } else {
                // Process the suffix (e.g., 'w', 'd', 'h', 'm', 's')
                switch (*ptr) {
                    case 'w': total_seconds += value * SECONDS_IN_WEEK; break;
                    case 'd': total_seconds += value * SECONDS_IN_DAY; break;
                    case 'h': total_seconds += value * SECONDS_IN_HOUR; break;
                    case 'm': total_seconds += value * SECONDS_IN_MINUTE; break;
                    case 's': total_seconds += value; break;
                    default:
                        printf("Invalid character '%c' in uptime string.\n", *ptr);
                        return -1; // Error case for invalid input
                }
                value = 0; // Reset value after processing each suffix
            }
            ptr++;
        }
    }

    return total_seconds;
}

static char *np_json_field_get_string(JSON_Object* object, const char *field_name) {
    char *value = NULL;
    char *out_str = NULL;
    if (object == NULL || field_name == NULL) {
        return NULL;
    }

    // TODO:Siva Add validation
    value = (char *)json_object_get_string(object, field_name);
    if (value) {
        out_str = NP_BGP_STRDUP(value, strlen(value));
    }
    return out_str;
}

static char *np_json_field_dotget_string(JSON_Object* object, const char *field_name) {
    char *value = NULL;
    char *out_str = NULL;
    if (object == NULL || field_name == NULL) {
        return NULL;
    }

    value = (char *)json_object_dotget_string(object, field_name);
    if (value) {
        out_str = NP_BGP_STRDUP(value, strlen(value));
    }
    return out_str;
}

static uint8_t np_json_field_get_boolean(JSON_Object* object, const char *field_name) {
    int ret_value = 0;
    if (object == NULL || field_name == NULL) {
        return 0;
    }

    ret_value = json_object_get_boolean(object, field_name);
    if (ret_value == -1) {
        // Field doesn't exist
        ret_value = 0;
    }
    return (uint8_t)ret_value;
}

static int np_parse_bgp_neighbors_bfd_stats(char *stats_str, const char *peerKey, struct zpn_np_bgp_peer_stats *peer) {
    JSON_Value *root_value = NULL;
    int res = 0;
    JSON_Object *peerObject = NULL;
    char key_bfd_multiplier[512]        = {0};
    char key_bfd_rx_min_interval[512]   = {0};
    char key_bfd_tx_min_interval[512]   = {0};
    char key_bfd_status[512]            = {0};

    JSON_Object *rootObject = np_stats_get_root_object_json(stats_str, &root_value);
    // Validate root object
    if (!rootObject) {
        return NP_RESULT_BAD_DATA;
    }

    if (g_np_instance_type == zpn_np_instance_type_gateway) {
        peerObject = json_object_get_object(rootObject, peerKey);
    } else {
        peerObject = rootObject;
    }

    if (peerObject) {
        snprintf(key_bfd_multiplier, sizeof(key_bfd_multiplier), "%s.%s", "peerBfdInfo", "detectMultiplier");
        snprintf(key_bfd_rx_min_interval, sizeof(key_bfd_rx_min_interval), "%s.%s", "peerBfdInfo", "rxMinInterval");
        snprintf(key_bfd_tx_min_interval, sizeof(key_bfd_tx_min_interval), "%s.%s", "peerBfdInfo", "txMinInterval");
        snprintf(key_bfd_status, sizeof(key_bfd_status), "%s.%s", "peerBfdInfo", "status");

        peer->bfd_multiplier = (int16_t)json_object_dotget_number(peerObject, key_bfd_multiplier);
        peer->bfd_rx_min_interval = (int32_t)json_object_dotget_number(peerObject, key_bfd_rx_min_interval);
        peer->bfd_tx_min_interval = (int32_t)json_object_dotget_number(peerObject, key_bfd_tx_min_interval);
        peer->bfd_status = np_json_field_dotget_string(peerObject, key_bfd_status);
        res = NP_RESULT_NO_ERROR;
    } else {
        NP_LOG(AL_ERROR, "Error parsing the bfd stats for peer:%s in:%s", peerKey, stats_str);
        res = NP_RESULT_BAD_DATA;
    }

    json_value_free(root_value); // Free the JSON resources
    return res;
}

static int np_stats_frr_get_peer_router_info_from_config(const char *neighbor_ip, int64_t neighbor_asn,
                                                             struct zpn_np_bgp_peer_stats *peer) {
    int64_t peer_gid = 0;
    int res = 0;
    int peer_type = 0;

    if (g_get_bgp_peer_config_info_cb) {
        res = g_get_bgp_peer_config_info_cb(neighbor_ip, neighbor_asn, &peer_gid, &peer_type);
        if (res == NP_RESULT_NO_ERROR) {
            NP_FRR_DEBUG("BGP config found for neighbor:%s gid:%"PRId64" type:%d", neighbor_ip, peer_gid, peer_type);
        }
    }

    peer->peer_gid = peer_gid;
    peer->peer_type = peer_type;

    return NP_RESULT_NO_ERROR;
}

static int np_stats_frr_get_bgp_neighbors_bfd_stats(const char *peerKey, struct zpn_np_bgp_peer_stats *peer) {
    char cmd[256] = {0};
    char *out_buf = NULL;
    int res = 0;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        // show ip bgp neighbors <peer_ip> json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, NEIGHBORS, peerKey, JSON);
    } else {
        // show ip bgp vrf all neighbors <peer_ip> json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, ALL, NEIGHBORS, peerKey, JSON);
    }
    res = np_frr_util_execute_command(cmd, &out_buf);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to execute stats command:%s error:%s", cmd, zpath_result_string(res));
        np_frr_release_buf(out_buf);
        return res;
    }

    res = np_parse_bgp_neighbors_bfd_stats(out_buf, peerKey, peer);
    if (res) {
        NP_LOG(AL_ERROR, "Error:%s parsing the output:%s of stats command:%s", zpath_result_string(res), out_buf, cmd);
    }

    np_frr_release_buf(out_buf);
    return res;
}

static void np_bgp_peer_free(void *peer_stats, bool is_summary) {
    if (peer_stats == NULL) {
        return;
    }

    if (is_summary) {
        struct zpn_np_bgp_peer_stats *peer = (struct zpn_np_bgp_peer_stats *)peer_stats;
        NP_BGP_FREE(peer->router_id);
        NP_BGP_FREE(peer->state);
        NP_BGP_FREE(peer->peer_state);
    } else {
        struct zpn_np_neighbor *neighbor = (struct zpn_np_neighbor *)peer_stats;
        NP_BGP_FREE(neighbor->remote_router_id);
        NP_BGP_FREE(neighbor->bgp_state);
        NP_BGP_FREE(neighbor->bfd_status);
    }

    NP_BGP_FREE(peer_stats); // Free the main peer object
}

static void *np_parse_bgp_peer_json(JSON_Object* peerObject, const char *peerKey, bool is_summary) {
    int res = 0;
    if (peerObject == NULL || peerKey == NULL) {
        return NULL;
    }

     // Allocate memory for the appropriate stats structure
    void *peer_stats = is_summary
                          ? NP_BGP_CALLOC(sizeof(struct zpn_np_bgp_peer_stats))
                          : NP_BGP_CALLOC(sizeof(struct zpn_np_neighbor));
    if (!peer_stats) {
        return NULL;
    }

    if (is_summary && peerKey) {
        ((struct zpn_np_bgp_peer_stats *)peer_stats)->router_id = NP_BGP_STRDUP(peerKey, strlen(peerKey));
    }

    if (is_summary) {
        struct zpn_np_bgp_peer_stats *peer = (struct zpn_np_bgp_peer_stats *)peer_stats;
        int64_t peerUptimeMsec = 0;

        peer->remote_as = (int64_t)json_object_get_number(peerObject, "remoteAs");
        peerUptimeMsec = (int64_t)json_object_get_number(peerObject, "peerUptimeMsec");
        peer->uptime_s = peerUptimeMsec ? peerUptimeMsec/1000 : 0 ;
        peer->prefixes_sent = (int64_t)json_object_get_number(peerObject, "pfxSnt");
        peer->prefixes_received = (int64_t)json_object_get_number(peerObject, "pfxRcd");
        peer->state = np_json_field_get_string(peerObject, "state");
        peer->peer_state = np_json_field_get_string(peerObject, "peerState");

        // Fill stats relevant to bfd using bgp neighbors
        res = np_stats_frr_get_bgp_neighbors_bfd_stats(peerKey, peer);
        if (res) {
            NP_LOG(AL_ERROR, "Error getting bfd stats through BGP neighbors for:%s", peerKey);
        }

        // Fill the peer id and peer type info by fetching config based on router id
        res = np_stats_frr_get_peer_router_info_from_config(peerKey, peer->remote_as, peer);
        if (res) {
            NP_LOG(AL_ERROR, "Error getting peer info from config for:%s", peerKey);
        }

        // Update the cumulative summary stats
        g_bgp_peer_stats.total_prefixes_sent +=  peer->prefixes_sent;
        g_bgp_peer_stats.total_prefixes_received += peer->prefixes_received;
        if (peer->uptime_s && peer->uptime_s > g_bgp_peer_stats.max_peer_uptime_s) {
            g_bgp_peer_stats.max_peer_uptime_s = peer->uptime_s;
        }
        if (peer->uptime_s && (peer->uptime_s < g_bgp_peer_stats.min_peer_uptime_s || g_bgp_peer_stats.min_peer_uptime_s == 0)) {
            g_bgp_peer_stats.min_peer_uptime_s = peer->uptime_s;
        }
    } else {
        struct zpn_np_neighbor *neighbor = (struct zpn_np_neighbor *)peer_stats;

        neighbor->remote_router_id = np_json_field_get_string(peerObject, "remoteRouterId");
        neighbor->remote_as = (int64_t)json_object_get_number(peerObject, "remoteAs");
        neighbor->bgp_state = np_json_field_get_string(peerObject, "bgpState");

        JSON_Object* bfdObject = json_object_get_object(peerObject, "peerBfdInfo");
        if (bfdObject) {
            neighbor->bfd_multiplier = (int16_t)json_object_get_number(bfdObject, "detectMultiplier");
            neighbor->bfd_rx_min_interval = (int32_t)json_object_get_number(bfdObject, "rxMinInterval");
            neighbor->bfd_tx_min_interval = (int32_t)json_object_get_number(bfdObject, "txMinInterval");
            neighbor->bfd_status = np_json_field_get_string(bfdObject, "status");
        }
    }

    return peer_stats;
}

static void NP_BGP_FREE_next_hop(void *next_hop, bool is_bgp) {
    if (!next_hop) return;

    if (is_bgp) {
        struct zpn_np_bgp_route_next_hop *bgp_next_hop = next_hop;
        NP_BGP_FREE(bgp_next_hop->hostname);
        NP_BGP_FREE(bgp_next_hop->afi);
    } else {
        struct zpn_np_ip_route_next_hop *ip_next_hop = next_hop;
        NP_BGP_FREE(ip_next_hop->interface_name);
        NP_BGP_FREE(ip_next_hop->afi);
    }
    NP_BGP_FREE(next_hop);
}

static void np_route_free(void *route, bool is_bgp) {
    if (route == NULL) {
        return;
    }

    if (is_bgp) {
        // Free fields for BGP route
        struct zpn_np_bgp_route *bgp_route = (struct zpn_np_bgp_route *)route;
        NP_BGP_FREE(bgp_route->path);
        NP_BGP_FREE(bgp_route->selectionReason);
        NP_BGP_FREE(bgp_route->path_from);
        NP_BGP_FREE(bgp_route->origin);
    } else {
        // Free fields for IP route
        struct zpn_np_ip_route *ip_route = (struct zpn_np_ip_route *)route;
        NP_BGP_FREE(ip_route->protocol);
    }

    NP_BGP_FREE(route); // Free the main route struct
}

static void *np_parse_next_hop_json(JSON_Object *nexthopObject, bool is_bgp) {
    if (!nexthopObject) return NULL;

    void *next_hop = is_bgp
                        ? NP_BGP_CALLOC(sizeof(struct zpn_np_bgp_route_next_hop))
                        : NP_BGP_CALLOC(sizeof(struct zpn_np_ip_route_next_hop));
    if (!next_hop) return NULL;

    // Parse common route fields
    const char *ip = json_object_get_string(nexthopObject, "ip");
    if (ip) {
        argo_string_to_inet(ip,
                            is_bgp ? &((struct zpn_np_bgp_route_next_hop *)next_hop)->ip
                                   : &((struct zpn_np_ip_route_next_hop *)next_hop)->ip);
    }

    if (is_bgp) {
        struct zpn_np_bgp_route_next_hop *bgp_next_hop = next_hop;
        bgp_next_hop->used = np_json_field_get_boolean(nexthopObject, "used");
        bgp_next_hop->hostname = np_json_field_get_string(nexthopObject, "hostname");
        bgp_next_hop->afi = np_json_field_get_string(nexthopObject, "afi");
    } else {
        struct zpn_np_ip_route_next_hop *ip_next_hop = next_hop;
        ip_next_hop->active = np_json_field_get_boolean(nexthopObject, "active");
        ip_next_hop->interface_name = np_json_field_get_string(nexthopObject, "interfaceName");
        ip_next_hop->afi = np_json_field_get_string(nexthopObject, "afi");
        ip_next_hop->weight = (uint16_t)json_object_get_number(nexthopObject, "weight");
        ip_next_hop->fib = np_json_field_get_boolean(nexthopObject, "fib");
        ip_next_hop->directly_connected = np_json_field_get_boolean(nexthopObject, "directlyConnected");
        ip_next_hop->recursive = np_json_field_get_boolean(nexthopObject, "recursive");
        ip_next_hop->on_link = np_json_field_get_boolean(nexthopObject, "onLink");
    }

    return next_hop;
}

/**
 * @brief Generic parser for a single route object (IP/BGP).
 *
 * Parses a JSON route object into structured data, including next-hop information.
 *
 * @param routeObject JSON object representing a single route.
 * @param is_bgp Flag to distinguish BGP routes (true) from IP routes (false).
 * @return Pointer to a parsed structured route object (either BGP or IP), or NULL on error.
 */
static void *np_parse_route_json(JSON_Object *routeObject, bool is_bgp) {
    if (!routeObject) return NULL;

    // Allocate either an IP route or a BGP route structure
    void *route = is_bgp
                    ? NP_BGP_CALLOC(sizeof(struct zpn_np_bgp_route))
                    : NP_BGP_CALLOC(sizeof(struct zpn_np_ip_route));
    if (!route) return NULL;

    // Parse common route fields
    const char *prefix = json_object_get_string(routeObject, "prefix");
    if (prefix) {
        argo_string_to_inet(prefix,
                            is_bgp ? &((struct zpn_np_bgp_route *)route)->net_prefix
                                   : &((struct zpn_np_ip_route *)route)->net_prefix);
    }

    if (is_bgp) {
        // Parse BGP-specific fields
        struct zpn_np_bgp_route *bgp_route = (struct zpn_np_bgp_route *)route;
        bgp_route->prefix_len = (int16_t)json_object_get_number(routeObject, "prefixLen");
        const char *peer_id = json_object_get_string(routeObject, "peerId");
        if (peer_id) {
            argo_string_to_inet(peer_id, &bgp_route->peer_id);
        }
        bgp_route->path = np_json_field_get_string(routeObject, "path");
        bgp_route->bestpath = np_json_field_get_boolean(routeObject, "bestpath");
        bgp_route->multi_path = np_json_field_get_boolean(routeObject, "multipath");
        bgp_route->selectionReason = np_json_field_get_string(routeObject, "selectionReason");
        bgp_route->path_from = np_json_field_get_string(routeObject, "pathFrom");
        bgp_route->origin = np_json_field_get_string(routeObject, "origin");
        bgp_route->valid = np_json_field_get_boolean(routeObject, "valid");
        bgp_route->weight = (int16_t)json_object_get_number(routeObject, "weight");

        // Update cumulative stats for BGP routes
        g_bgp_route_stats.total_best_path_count += bgp_route->bestpath;
        g_bgp_route_stats.total_multi_path_count += bgp_route->multi_path;
        g_bgp_route_stats.total_valid_count += bgp_route->valid;
        if (bgp_route->origin && strcmp(bgp_route->origin, "IGP") == 0) {
            g_bgp_route_stats.total_igp_origin_count += 1;
        }
        // TODO : Siva
        //g_bgp_route_stats.total_external_origin_count
    } else {
        // Parse IP-specific fields
        struct zpn_np_ip_route *ip_route = (struct zpn_np_ip_route *)route;
        ip_route->prefix_len = (int16_t)json_object_get_number(routeObject, "prefixLen");
        ip_route->protocol = np_json_field_get_string(routeObject, "protocol");
        ip_route->selected_route = np_json_field_get_boolean(routeObject, "selected");
        ip_route->dest_selected = np_json_field_get_boolean(routeObject, "destSelected");
        ip_route->installed_route = np_json_field_get_boolean(routeObject, "installed");

        // Convert uptime string to seconds and store
        const char *uptime = json_object_get_string(routeObject, "uptime");
        if (uptime) {
            ip_route->uptime_s = uptime_str_to_seconds(uptime);
        }
    }

    // Extract and parse array of next-hop objects
    JSON_Array *next_hops_array = json_object_get_array(routeObject, "nexthops");
    size_t next_hops_count = json_array_get_count(next_hops_array);

    if (is_bgp) {
        struct zpn_np_bgp_route *bgp_route = (struct zpn_np_bgp_route *)route;

        for (size_t i = 0; i < next_hops_count && i < ZPN_NP_MAX_FRR_NEXT_HOPS; i++) {
            JSON_Object *nexthopObject = json_array_get_object(next_hops_array, i);
            if (nexthopObject) {
                struct zpn_np_bgp_route_next_hop *nexthop = np_parse_next_hop_json(nexthopObject, true);
                if (nexthop) {
                    bgp_route->next_hops[i] = argo_object_create(zpn_np_bgp_route_next_hop_description, nexthop);
                    bgp_route->next_hops_count++;
                    NP_BGP_FREE_next_hop(nexthop, true);  // Free intermediate structure after encapsulation
                }
            }
        }
        g_bgp_route_stats.total_next_hop_count += next_hops_count;
    } else {
        struct zpn_np_ip_route *ip_route = (struct zpn_np_ip_route *)route;

        for (size_t i = 0; i < next_hops_count && i < ZPN_NP_MAX_FRR_NEXT_HOPS; i++) {
            JSON_Object *nexthopObject = json_array_get_object(next_hops_array, i);
            if (nexthopObject) {
                struct zpn_np_ip_route_next_hop *nexthop = np_parse_next_hop_json(nexthopObject, false);
                if (nexthop) {
                    ip_route->next_hops[i] = argo_object_create(zpn_np_ip_route_next_hop_description, nexthop);
                    ip_route->next_hops_count++;
                    NP_BGP_FREE_next_hop(nexthop, false);  // Free intermediate structure after encapsulation
                }
            }
        }
    }

    return route;
}

/**
 * @brief Parses route stats from a JSON string for both IP and BGP routes.
 *
 * Depending on `is_bgp`, this function parses either IP routes or BGP routes from the JSON string.
 * It extracts child objects, converts them into structured data (`zpn_np_ip_route_stats` or `zpn_np_bgp_route_stats`).
 *
 * @param route_str JSON string representing route stats.
 * @param is_bgp Flag to indicate whether to parse BGP routes (true) or IP routes (false).
 * @param out_stats output stats structure where the bgp or ip route stats will be updated.
 * @return Result no error incase of success otherwise appropriate error code.
 */
int np_parse_ip_bgp_routes_stats(const char *route_str, bool is_bgp, void *out_stats) {
    JSON_Value *root_value = NULL;
    struct zpn_np_route_comp_stats *route_stats = (struct zpn_np_route_comp_stats *)out_stats;
    int route_index = 0;
    int max_routes_exceeded = 0;
    JSON_Object *rootObject = np_stats_get_root_object_json(route_str, &root_value);

    // Validate root object
    if (!rootObject || !route_stats) {
        return NP_RESULT_BAD_DATA;
    }

    if (is_bgp) {
        // Parse BGP-route specific fields
        char *router_id = np_json_field_get_string(rootObject, "routerId");
        if (router_id) {
            strncpy(g_np_stats_frr_monitor.self_router_id, router_id, sizeof(g_np_stats_frr_monitor.self_router_id) - 1);
            NP_BGP_FREE(router_id);
        }

        g_np_stats_frr_monitor.self_local_as = (int64_t)json_object_get_number(rootObject, "localAS");

        // Update self router_id and localAS to cumulative stats
        argo_string_to_inet(g_np_stats_frr_monitor.self_router_id, &g_bgp_peer_stats.router_id);
        argo_string_to_inet(g_np_stats_frr_monitor.self_router_id, &g_bgp_route_stats.router_id);
        g_bgp_peer_stats.local_as = g_np_stats_frr_monitor.self_local_as;
        g_bgp_route_stats.local_as = g_np_stats_frr_monitor.self_local_as;

        JSON_Object *routesObject = json_object_get_object(rootObject, "routes");

        // Parse individual BGP routes
        for (int i = 0; i < json_object_get_count(routesObject); i++) {
            const char *key = json_object_get_name(routesObject, i);
            JSON_Array *routeArray = json_object_get_array(routesObject, key);

            if (routeArray) {
                for (int j = 0; j < json_array_get_count(routeArray); j++) {
                    JSON_Object *routeObject = json_array_get_object(routeArray, j);

                    if (routeObject) {
                        struct zpn_np_bgp_route *bgp_route = np_parse_route_json(routeObject, true);
                        if (bgp_route) {
                            if (route_index < ZPN_NP_MAX_FRR_ROUTES) {
                                route_stats->bgp_routes[route_index] =
                                    argo_object_create(zpn_np_bgp_route_description, bgp_route);
                                route_index++;
                            } else {
                                NP_LOG(AL_ERROR, "Route index %d value exceed the max frr routes %d", route_index, ZPN_NP_MAX_FRR_ROUTES);
                                max_routes_exceeded++;
                            }
                            np_route_free(bgp_route, true); // Free intermediate after encapsulation
                        }
                    }
                }
            }
        }

    } else {
        // Parse IP-route specific fields

        // Parse individual IP routes
        for (int i = 0; i < json_object_get_count(rootObject); i++) {
            const char *key = json_object_get_name(rootObject, i);
            JSON_Value *routeValue = json_object_get_value(rootObject, key);

            if (routeValue) {
                JSON_Array *routeArray = json_value_get_array(routeValue);
                if (routeArray) {
                    for (int j = 0; j < json_array_get_count(routeArray); j++) {
                        JSON_Object *routeObject = json_array_get_object(routeArray, j);

                        if (routeObject) {
                            struct zpn_np_ip_route *ip_route = np_parse_route_json(routeObject, false);
                            if (ip_route) {
                                if (route_index < ZPN_NP_MAX_FRR_ROUTES) {
                                    route_stats->ip_routes[route_index] =
                                        argo_object_create(zpn_np_ip_route_description, ip_route);
                                    route_index++;
                                } else {
                                    NP_LOG(AL_ERROR, "Route index %d value exceed the max frr routes %d", route_index, ZPN_NP_MAX_FRR_ROUTES);
                                    max_routes_exceeded++;
                                }
                                np_route_free(ip_route, false); // Free intermediate after encapsulation
                            }
                        }
                    }
                }
            }
        }
    }

    // Fill routes count
    if (is_bgp) {
        route_stats->bgp_routes_count = route_index;
        route_stats->max_bgp_routes_exceeded = max_routes_exceeded;
    } else {
        route_stats->ip_routes_count = route_index;
        route_stats->max_ip_routes_exceeded = max_routes_exceeded;
    }

    json_value_free(root_value); // Free the JSON resources
    return NP_RESULT_NO_ERROR;
}


void np_ip_bgp_routes_stats_release(void *stats, bool is_bgp) {
    struct zpn_np_route_comp_stats *route_stats = (struct zpn_np_route_comp_stats *)stats;
    if (route_stats == NULL) {
        return;
    }

    // Determine routes and count
    int routes_count = is_bgp
                           ? route_stats->bgp_routes_count
                           : route_stats->ip_routes_count;

    // Iterate through routes and free each one
    for (int i = 0; i < routes_count; i++) {
        struct argo_object *route_obj = NULL;
        route_obj = is_bgp
                        ? route_stats->bgp_routes[i]
                        : route_stats->ip_routes[i];
        if (route_obj) {
            void *route = route_obj->base_structure_void;
            if (route) {
                int nexthops_count = is_bgp
                                        ? ((struct zpn_np_bgp_route *)route)->next_hops_count
                                        : ((struct zpn_np_ip_route *)route)->next_hops_count;
                for (int j = 0; j < nexthops_count; j++) {
                    struct argo_object *next_hop_obj = NULL;
                    next_hop_obj =  is_bgp
                                        ? ((struct zpn_np_bgp_route *)route)->next_hops[j]
                                        : ((struct zpn_np_ip_route *)route)->next_hops[j];
                    if (next_hop_obj) {
                        argo_object_release(next_hop_obj);
                    }
                }
            }
            argo_object_release(route_obj);
        }
        if (is_bgp) {
            route_stats->bgp_routes[i] = NULL;
        } else {
            route_stats->ip_routes[i] = NULL;
        }
    }

    if (is_bgp) {
        route_stats->bgp_routes_count = 0;
    } else {
        route_stats->ip_routes_count = 0;
    }
}

/**
 * @brief Parses BGP stats from a JSON string for both summary and neighbor stats.
 *
 * Depending on `is_summary`, this function parses either BGP summary stats or BGP neighbor stats
 * from the JSON string. It extracts child objects, converts them into structured data.
 *
 * @param stats_str JSON string representing BGP stats.
 * @param is_summary Flag to indicate whether to parse summary stats (true) or neighbor stats (false).
 * @param out_stats output stats structure where the bgp summary stats will be updated.
 * @return Result no error incase of success otherwise appropriate error code.
 */
int np_parse_bgp_stats(const char *stats_str, bool is_summary, void *out_stats) {
    JSON_Value *root_value = NULL;
    struct zpn_np_nw_comp_stats *bgp_nw_stats = (struct zpn_np_nw_comp_stats *)out_stats;
    struct zpn_np_neighbor_stats *neighbor_stats = NULL;
    JSON_Object *childObject = NULL;

    JSON_Object *rootObject = np_stats_get_root_object_json(stats_str, &root_value);
    // Validate root object
    if (!rootObject || !bgp_nw_stats) {
        return NP_RESULT_BAD_ARGUMENT;
    }

    if (is_summary) {
        // Parse BGP summary stats

        // NOTE: Currently we support only ipv4Unicast, Revise this in future if we wanna support multicast/ipv6
        JSON_Object *ipv4UnicastObject = json_object_get_object(rootObject, "ipv4Unicast");
        if (!ipv4UnicastObject) {
            json_value_free(root_value);
            return NP_RESULT_BAD_DATA;
        }

        // Extract BGP peers
        JSON_Object *peersObject = json_object_get_object(ipv4UnicastObject, "peers");

        childObject = peersObject;

        // Update cumulative BGP summary stats
        g_bgp_peer_stats.total_peers = (int32_t)json_object_get_number(ipv4UnicastObject, "totalPeers");
        g_bgp_peer_stats.total_peers_not_established = (int32_t)json_object_get_number(ipv4UnicastObject, "failedPeers");
        g_bgp_peer_stats.total_peers_established = g_bgp_peer_stats.total_peers - g_bgp_peer_stats.total_peers_not_established;
    } else {
        // Parse BGP neighbor stats. Currently not used.
        neighbor_stats = NP_BGP_CALLOC(sizeof(struct zpn_np_neighbor_stats));

        if (neighbor_stats) {
            childObject = rootObject;
        }
    }

    int peer_index = 0;
    int peers_exceeded = 0;
    for (int i = 0; i < json_object_get_count(childObject); i++) {
        const char *peerKey = json_object_get_name(childObject, i);

        if (!is_summary && g_np_instance_type == zpn_np_instance_type_gateway && strstr(peerKey, "vrf")) {
            continue;
        }

        JSON_Value *peerValue = json_object_get_value(childObject, peerKey);
        if (peerValue) {
            JSON_Object *peerObject = json_value_get_object(peerValue);
            void *peer = np_parse_bgp_peer_json(peerObject, peerKey, is_summary);
            if (peer) {
                if (peer_index < ZPN_NP_MAX_FRR_PEERS) {
                    if (is_summary) {
                        bgp_nw_stats->peers[peer_index] = argo_object_create(zpn_np_bgp_peer_stats_description, peer);
                    } else {
                        neighbor_stats->neighbors[peer_index] =
                            argo_object_create(zpn_np_neighbor_description, peer);
                    }
                    peer_index++;
                } else {
                    peers_exceeded++;
                }
                np_bgp_peer_free(peer, is_summary); // Free intermediate after encapsulation
            }
        }
    }

    // Fill the peers count
    if (is_summary) {
        bgp_nw_stats->peers_count = peer_index;
        bgp_nw_stats->max_peers_exceeded = peers_exceeded;
    } else {
        neighbor_stats->neighbors_count = peer_index;
        neighbor_stats->max_neighbors_exceeded = peers_exceeded;
    }

    // Free stats after conversion into encapsulated object
    if (!is_summary) {
        NP_BGP_FREE(neighbor_stats);
    }
    json_value_free(root_value); // Free the JSON resources
    return NP_RESULT_NO_ERROR;
}

void np_bgp_summary_stats_release(void *nw_stats) {
    struct zpn_np_nw_comp_stats *bgp_nw_stats = (struct zpn_np_nw_comp_stats *)nw_stats;
    if (nw_stats == NULL) {
        return;
    }

    // Determine peers and count
    int peers_count = bgp_nw_stats->peers_count;

    // Iterate through peers and free each one
    for (int i = 0; i < peers_count; i++) {
        struct argo_object *peer_obj = NULL;
        peer_obj = bgp_nw_stats->peers[i];
        if (peer_obj) {
            argo_object_release(peer_obj);
        }
        bgp_nw_stats->peers[i] = NULL;
    }
    bgp_nw_stats->peers_count = 0;
}

void NP_BGP_FREE_splitted_stats_messages(void **messages, int num_messages, int type_flag) {
    if (messages == NULL) {
        return;
    }

    for (int i = 0; i < num_messages; i++) {
        void *message_ptr = NULL;

        if (type_flag == BGP_NETWORK_STATS) {
            struct zpn_np_nw_comp_stats *message = (struct zpn_np_nw_comp_stats *)messages[i];
            message_ptr = message;
        } else {
            struct zpn_np_route_comp_stats *message = (struct zpn_np_route_comp_stats *)messages[i];
            message_ptr = message;
        }
        NP_BGP_FREE(message_ptr);
    }

    NP_BGP_FREE(messages);
}

static void np_stats_fill_self_info(int type_flag, void *input, void *out_stats) {
    if (input == NULL || out_stats == NULL) {
        return;
    }

    if (type_flag == BGP_NETWORK_STATS) {
        struct zpn_np_nw_comp_stats *in_nw_stats = (struct zpn_np_nw_comp_stats *)input;
        struct zpn_np_nw_comp_stats *out_nw_stats = (struct zpn_np_nw_comp_stats *)out_stats;

        out_nw_stats->local_as = in_nw_stats->local_as;
        out_nw_stats->router_id = in_nw_stats->router_id;
        out_nw_stats->sys_gid = in_nw_stats->sys_gid;
        out_nw_stats->g_cst = in_nw_stats->g_cst;
        out_nw_stats->sys_type = in_nw_stats->sys_type;
        out_nw_stats->update_id = in_nw_stats->update_id;
        out_nw_stats->bgp_svc_status = in_nw_stats->bgp_svc_status;
        out_nw_stats->max_peers_exceeded = in_nw_stats->max_peers_exceeded;
    } else {
        struct zpn_np_route_comp_stats *in_route_stats = (struct zpn_np_route_comp_stats *)input;
        struct zpn_np_route_comp_stats *out_route_stats = (struct zpn_np_route_comp_stats *)out_stats;

        out_route_stats->local_as = in_route_stats->local_as;
        out_route_stats->router_id = in_route_stats->router_id;
        out_route_stats->sys_gid = in_route_stats->sys_gid;
        out_route_stats->g_cst = in_route_stats->g_cst;
        out_route_stats->sys_type = in_route_stats->sys_type;
        out_route_stats->update_id = in_route_stats->update_id;
        out_route_stats->max_bgp_routes_exceeded = in_route_stats->max_bgp_routes_exceeded;
        out_route_stats->max_ip_routes_exceeded = in_route_stats->max_ip_routes_exceeded;
    }
    return;
}


/**
 * @brief Splits the input stats data into smaller chunks of messages.
 * This function works as a unified logic to process both network stats
 * and route stats based on the user-specified type flag.
 *
 * @param type_flag         An integer flag to differentiate between BGP_NETWORK_STATS and BGP_ROUTE_STATS.
 * @param input             Pointer to the input structure (network stats or route stats).
 * @param num_messages      Pointer to an integer to store the total number of messages created.
 *
 * @return An array of void pointers pointing to the split messages, or NULL on failure.
 */
void **np_split_stats_object_to_messages(int type_flag, void *input, int *num_messages) {

    int32_t peer_index = 0, bgp_route_index = 0, ip_route_index = 0;
    int32_t peers_count = 0, bgp_routes_count = 0, ip_routes_count = 0;

    if (input == NULL) {
        return NULL;
    }

    if (type_flag == BGP_NETWORK_STATS) {
        struct zpn_np_nw_comp_stats *input_nw_stats = (struct zpn_np_nw_comp_stats *)input;
        peers_count = input_nw_stats->peers_count;
    } else {
        struct zpn_np_route_comp_stats *input_route_stats = (struct zpn_np_route_comp_stats *)input;
        ip_routes_count = input_route_stats->ip_routes_count;
        bgp_routes_count = input_route_stats->bgp_routes_count;
    }

    // Calculate the maximum number of messages(rpc) required
    int total_messages = 0;
    if (type_flag == BGP_NETWORK_STATS) {
        total_messages = (peers_count + MAX_PEERS_PER_MESSAGE - 1) / MAX_PEERS_PER_MESSAGE;
    } else {
        total_messages = (bgp_routes_count + MAX_BGP_ROUTES_PER_MESSAGE - 1) / MAX_BGP_ROUTES_PER_MESSAGE;

        total_messages = (ip_routes_count + MAX_IP_ROUTES_PER_MESSAGE - 1) / MAX_IP_ROUTES_PER_MESSAGE > total_messages
                        ? (ip_routes_count + MAX_IP_ROUTES_PER_MESSAGE - 1) / MAX_IP_ROUTES_PER_MESSAGE
                        : total_messages;
    }

    // Allocate memory for the message objects
    void **messages = (void **)NP_BGP_CALLOC(total_messages * sizeof(void *));
    if (!messages) {
        return NULL;
    }

    *num_messages = 0; // Initialize the counter for messages
    // Splitting logic
    while (peer_index < peers_count ||
           bgp_route_index < bgp_routes_count ||
           ip_route_index < ip_routes_count) {

        void *message_ptr = NULL;

        if (type_flag == BGP_NETWORK_STATS) {
            // Create a new nw message object
            struct zpn_np_nw_comp_stats *message = NP_BGP_CALLOC(sizeof(struct zpn_np_nw_comp_stats));
            struct zpn_np_nw_comp_stats *in_nw_stats = (struct zpn_np_nw_comp_stats *)input;

            np_stats_fill_self_info(type_flag, input, message);

            // Split peers
            int peers_to_copy = peer_index + MAX_PEERS_PER_MESSAGE <= peers_count
                                    ? MAX_PEERS_PER_MESSAGE
                                    : peers_count - peer_index;
            if (peers_to_copy && message) {
                for (int i = 0; i < peers_to_copy; i++) {
                    message->peers[i] = in_nw_stats->peers[peer_index + i];
                }
                message->peers_count = peers_to_copy;
            }
            peer_index += peers_to_copy;
            message_ptr = message;

        } else {
            // Create a new route message object
            struct zpn_np_route_comp_stats *message = NP_BGP_CALLOC(sizeof(struct zpn_np_route_comp_stats));
            struct zpn_np_route_comp_stats *in_routes = (struct zpn_np_route_comp_stats *)input;

            np_stats_fill_self_info(type_flag, input, message);

            // Split BGP routes
            int bgp_routes_to_copy = bgp_route_index + MAX_BGP_ROUTES_PER_MESSAGE <= bgp_routes_count
                                        ? MAX_BGP_ROUTES_PER_MESSAGE
                                        : bgp_routes_count - bgp_route_index;
            if (bgp_routes_to_copy && message) {
                for (int i = 0; i < bgp_routes_to_copy; i++) {
                    message->bgp_routes[i] = in_routes->bgp_routes[bgp_route_index + i];
                }
                message->bgp_routes_count = bgp_routes_to_copy;
            }
            bgp_route_index += bgp_routes_to_copy;

            // Split IP routes
            int ip_routes_to_copy = ip_route_index + MAX_IP_ROUTES_PER_MESSAGE <= ip_routes_count
                                        ? MAX_IP_ROUTES_PER_MESSAGE
                                        : ip_routes_count - ip_route_index;
            if (ip_routes_to_copy) {
                for (int i = 0; i < ip_routes_to_copy; i++) {
                    message->ip_routes[i] = in_routes->ip_routes[ip_route_index + i];
                }
                message->ip_routes_count = ip_routes_to_copy;
            }
            ip_route_index += ip_routes_to_copy;
            message_ptr = message;
        }

        // Add the message to the array
        messages[(*num_messages)++] = message_ptr;
    }
    return messages;
}

static void np_stats_frr_monitor_heartbeat(void)
{
    if (g_np_stats_frr_monitor.monitor_hb_thread) {
        zthread_heartbeat(g_np_stats_frr_monitor.monitor_hb_thread);
    }
}

static int np_stats_frr_dump_ip_routes(char **ip_routes_buf) {
    char cmd[256] = {0};
    char *out_buf = NULL;
    int res = 0;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        // show ip route json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s\"", VTYSH, SHOW, IP, ROUTE, JSON);
    } else {
        // show ip route vrf all json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s\"", VTYSH, SHOW, IP, ROUTE, VRF, NP_VRF_NAME, JSON);
    }
    res = np_frr_util_execute_command(cmd, &out_buf);
    if (out_buf == NULL) {
        NP_LOG(AL_ERROR, "Failed to execute stats command:%s", cmd);
        return NP_RESULT_ERR;
    }

    *ip_routes_buf = out_buf;
    return res;
}

static int np_stats_frr_dump_bgp_routes(char **bgp_routes_buf) {
    char cmd[256] = {0};
    char *out_buf = NULL;
    int res = 0;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        // show ip bgp json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s\"", VTYSH, SHOW, IP, BGP, JSON);
    } else {
        // show ip bgp vrf all json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, ALL, JSON);
    }
    res = np_frr_util_execute_command(cmd, &out_buf);
    if (out_buf == NULL) {
        NP_LOG(AL_ERROR, "Failed to execute stats command:%s", cmd);
        return NP_RESULT_ERR;
    }

    *bgp_routes_buf = out_buf;
    return res;
}

int np_stats_frr_dump_bgp_summary(char **bgp_summary_buf) {
    char cmd[256] = {0};
    char *out_buf = NULL;
    int res = 0;

    if (g_np_instance_type == zpn_np_instance_type_connector) {
        // show ip bgp json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, SUMMARY, JSON);
    } else {
        // show ip bgp vrf all json
        snprintf(cmd, sizeof(cmd), "%s \"%s %s %s %s %s %s %s\"", VTYSH, SHOW, IP, BGP, VRF, ALL, SUMMARY, JSON);
    }
    res = np_frr_util_execute_command(cmd, &out_buf);
    if (out_buf == NULL) {
        NP_LOG(AL_ERROR, "Failed to execute stats command:%s", cmd);
        return NP_RESULT_ERR;
    }

    *bgp_summary_buf = out_buf;
    return res;
}

static int64_t np_stats_nw_frr_get_update_id() {
    g_np_stats_frr_monitor.nw_stats_update_id++;
    return g_np_stats_frr_monitor.nw_stats_update_id;
}

static int64_t np_stats_route_frr_get_update_id() {
    g_np_stats_frr_monitor.route_stats_update_id++;
    return g_np_stats_frr_monitor.route_stats_update_id;
}

static void np_stats_fill_global_fields(void *np_frr_stats, int type_flag, char **log_name) {
    if (np_frr_stats == NULL || log_name == NULL) {
        return;
    }

    if (type_flag == BGP_NETWORK_STATS) {
        struct zpn_np_nw_comp_stats *np_nw_frr_stats = (struct zpn_np_nw_comp_stats *)np_frr_stats;
        // Update global fields to BGP summary stats struct
        np_nw_frr_stats->update_id = np_stats_nw_frr_get_update_id();
        argo_string_to_inet(g_np_stats_frr_monitor.self_router_id, &np_nw_frr_stats->router_id);
        np_nw_frr_stats->local_as = g_np_stats_frr_monitor.self_local_as;
        np_nw_frr_stats->sys_gid = g_np_stats_frr_monitor.inst_gid;
        np_nw_frr_stats->g_cst = g_np_stats_frr_monitor.customer_gid;
        np_nw_frr_stats->sys_type = (uint8_t)g_np_instance_type;
        np_nw_frr_stats->bgp_svc_status = NP_BGP_STRDUP(g_frr_svc_stats.svc_status, strlen(g_frr_svc_stats.svc_status));
        *log_name = NP_STATS_BGP_SUMMARY_NAME;
    } else {
        struct zpn_np_route_comp_stats *np_route_frr_stats = (struct zpn_np_route_comp_stats *)np_frr_stats;
        // Update global fields to BGP route stats struct
        np_route_frr_stats->update_id = np_stats_route_frr_get_update_id();
        argo_string_to_inet(g_np_stats_frr_monitor.self_router_id, &np_route_frr_stats->router_id);
        np_route_frr_stats->local_as = g_np_stats_frr_monitor.self_local_as;
        np_route_frr_stats->sys_gid = g_np_stats_frr_monitor.inst_gid;
        np_route_frr_stats->g_cst = g_np_stats_frr_monitor.customer_gid;
        np_route_frr_stats->sys_type = (uint8_t)g_np_instance_type;
        *log_name = NP_STATS_BGP_ROUTES_NAME;
    }
    return;
}

static int np_stats_frr_send(void *np_frr_stats, int type_flag) {
    int res = 0;
    void **messages = NULL;
    int messages_count = 0;
    char *log_name = NULL;

    if (np_frr_stats == NULL) {
        return NP_RESULT_ERR;
    }

    np_stats_fill_global_fields(np_frr_stats, type_flag, &log_name);

    messages = np_split_stats_object_to_messages(type_flag, np_frr_stats, &messages_count);
    if (messages == NULL) {
        return NP_RESULT_ERR;
    }

    for (int i = 0; i < messages_count; i++) {
        void *stats_message = messages[i];
        NP_FRR_DEBUG("Sending NP FRR stats log:%s\n", log_name);
        if(type_flag == BGP_NETWORK_STATS) {
            struct zpn_np_nw_comp_stats *message = (struct zpn_np_nw_comp_stats *)stats_message;
            message->total_updates = messages_count;
            message->update_index = i + 1;
        } else if (type_flag == BGP_ROUTE_STATS) {
            struct zpn_np_route_comp_stats *message = (struct zpn_np_route_comp_stats *)stats_message;
            message->total_updates = messages_count;
            message->update_index = i + 1;
        } else {
            NP_FRR_DEBUG("Unknown NP FRR Stats type %d", type_flag);
        }
        if (zpath_stats_collection) {
            res = argo_log_structure_immediate(zpath_stats_collection,
                                        argo_log_priority_info,
                                        0,
                                        log_name,
                                        (type_flag == BGP_NETWORK_STATS
                                             ? zpn_np_nw_comp_stats_description : zpn_np_route_comp_stats_description),
                                        stats_message);
            if (res) {
                NP_LOG(AL_ERROR, "Could not log %s structure immediate: %s",
                                     log_name, argo_result_string(res));
            }
        }

        // Send log to KAFKA
        if (g_np_instance_type == zpn_np_instance_type_gateway && type_flag == BGP_NETWORK_STATS) {

            res = zpath_service_endpoint_log_struct(g_np_stats_frr_monitor.customer_gid,
                                NP_STATS_BGP_SUMMARY_NAME,
                                zpath_customer_log_type_zpn_np_nw_comp_stats,
                                NULL,
                                NULL,
                                NULL,
                                NULL,
                                zpn_np_nw_comp_stats_description,
                                stats_message);
            if (res) {
                NP_LOG(AL_ERROR, "Error sending zpn_np_nw_comp_stats to kafka.. result:%s",
                                                                                 zpath_result_string(res));
            }
        }
        np_stats_frr_monitor_heartbeat();
    }

    // Free the messages
    NP_BGP_FREE_splitted_stats_messages(messages, messages_count, type_flag);
    return res;
}

static void np_stats_reset_last_updated_stats() {

    np_ip_bgp_routes_stats_release(&g_np_route_frr_stats, true);
    np_ip_bgp_routes_stats_release(&g_np_route_frr_stats, false);
    np_bgp_summary_stats_release(&g_np_nw_frr_stats);

    memset(&g_bgp_peer_stats, 0, sizeof(g_bgp_peer_stats));
    memset(&g_bgp_route_stats, 0, sizeof(g_bgp_route_stats));

    if (g_np_nw_frr_stats.bgp_svc_status) {
        NP_BGP_FREE(g_np_nw_frr_stats.bgp_svc_status);
    }
    memset(&g_np_nw_frr_stats, 0, sizeof(g_np_nw_frr_stats));
    memset(&g_np_route_frr_stats, 0, sizeof(g_np_route_frr_stats));

    memset(&g_frr_svc_stats, 0, sizeof(g_frr_svc_stats));
    return;
}

static void np_svc_frr_stats_update() {
    int64_t now = epoch_s();
    char *status  = np_frr_parse_status();
    char *version = np_frr_parse_version();

    if (status) {
        strncpy(g_frr_svc_stats.svc_status, status, sizeof(g_frr_svc_stats.svc_status) - 1);
        NP_BGP_FREE(status);
    }
    if (version) {
        strncpy(g_frr_svc_stats.detected_version, version, sizeof(g_frr_svc_stats.detected_version) - 1);
        NP_BGP_FREE(version);
    }
    if (!strcmp(g_frr_svc_stats.svc_status, "active")) {
        g_frr_svc_stats.svc_uptime_s = now - np_frr_parse_uptime_epoch();
    }

    return;
}

static int np_stats_frr_parse_and_send_stats_locked(char *bgp_routes_buf,
                                                    char *ip_routes_buf,
                                                    char *bgp_summary_buf) {
    int res = 0;

    np_stats_frr_monitor_heartbeat();

    np_stats_reset_last_updated_stats();

    np_svc_frr_stats_update();

    if (bgp_routes_buf) {
        res = np_parse_ip_bgp_routes_stats(bgp_routes_buf, true, &g_np_route_frr_stats);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to parse the output:%s of bgp route stats", bgp_routes_buf);
        }
    }

    if (ip_routes_buf) {
        res = np_parse_ip_bgp_routes_stats(ip_routes_buf, false, &g_np_route_frr_stats);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to parse the output:%s of ip routes stats", ip_routes_buf);
        }
    }

    if (bgp_summary_buf) {
        res = np_parse_bgp_stats(bgp_summary_buf, true, &g_np_nw_frr_stats);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to parse the output:%s of bgp summary stats res:%s", bgp_summary_buf, zpath_result_string(res));
        }
    }

    np_stats_frr_monitor_heartbeat();

    // Report BGP summary stats every monitor interval
    res = np_stats_frr_send(&g_np_nw_frr_stats, BGP_NETWORK_STATS);
    if (res) {
        NP_LOG(AL_ERROR, "Error sending bgp frr nw stats : %s", zpath_result_string(res));
    }

    // Report BGP route and IP route stats every 15 mins
    if (g_np_stats_frr_monitor.monitor_count == 1 ||
         (g_np_stats_frr_monitor.monitor_count % (NP_STATS_FRR_ROUTE_STATS_INTERVAL_S / NP_STATS_FRR_MONITOR_S) == 0)) {

        res = np_stats_frr_send(&g_np_route_frr_stats, BGP_ROUTE_STATS);
        if (res) {
            NP_LOG(AL_ERROR, "Error sending bgp frr route stats : %s", zpath_result_string(res));
        }
    }

    return res;
}

static int np_stats_frr_dump_all() {
    int res = 0;
    char *bgp_routes_buf = NULL;
    char *ip_routes_buf = NULL;
    char *bgp_summary_buf = NULL;

    res = np_stats_frr_dump_bgp_routes(&bgp_routes_buf);
    if (res) {
        NP_LOG(AL_ERROR, "Error dumping BGP routes stats");
    }

    res = np_stats_frr_dump_ip_routes(&ip_routes_buf);
    if (res) {
        NP_LOG(AL_ERROR, "Error dumping IP routes stats");
    }

    res = np_stats_frr_dump_bgp_summary(&bgp_summary_buf);
    if (res) {
        NP_LOG(AL_ERROR, "Error dumping BGP summary stats");
    }

    ZPATH_MUTEX_LOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);
    res = np_stats_frr_parse_and_send_stats_locked(bgp_routes_buf, ip_routes_buf, bgp_summary_buf);
    if (res) {
        NP_LOG(AL_ERROR, "Error parsing/sending bgp frr stats : %s", zpath_result_string(res));
    }
    ZPATH_MUTEX_UNLOCK(&(g_np_stats_frr_monitor.np_frr_stats_lock), __FILE__, __LINE__);

    np_frr_release_buf(bgp_routes_buf);
    np_frr_release_buf(ip_routes_buf);
    np_frr_release_buf(bgp_summary_buf);

    return res;
}

static void np_stats_frr_monitor_timer_cb(__attribute__((unused)) evutil_socket_t sock,
                                           __attribute__((unused)) short flags,
                                           __attribute__((unused)) void *cookie)
{
    /* initial heartbeat */
    np_stats_frr_monitor_heartbeat();

    if (g_np_stats_frr_monitor.np_frr_stats_monitor_enabled) {
        if (!g_np_stats_frr_monitor.np_frr_stats_registered) {
            int res = np_frr_utils_stats_register();
            if (res) {
                NP_LOG(AL_ERROR, "Unable to register argo stats for np_frr_utils");
                return;
            } else {
                g_np_stats_frr_monitor.np_frr_stats_registered = 1;
            }

        }

        if (g_np_stats_frr_monitor.np_frr_stats_registered) {
            g_np_stats_frr_monitor.monitor_count++;
            np_stats_frr_dump_all();
        }
    }
}

static int np_stats_frr_monitor_timer_init(struct event_base *base)
{
    // init main timer cb
    g_np_stats_frr_monitor.timer = event_new(base, -1, EV_PERSIST, np_stats_frr_monitor_timer_cb, NULL);
    if (!g_np_stats_frr_monitor.timer) {
        NP_LOG(AL_ERROR, "ERROR np frr stats monitor timer init event_new");
        return NP_RESULT_ERR;
    }
    struct timeval tv = { .tv_sec = NP_STATS_FRR_MONITOR_S, .tv_usec = 0 };
    if (event_add(g_np_stats_frr_monitor.timer, &tv)) {
        NP_LOG(AL_ERROR, "ERROR NP frr stats monitor timer init event_add");
        return NP_RESULT_ERR;
    }
    return NP_RESULT_NO_ERROR;
}

static void* np_stats_frr_monitor_thread(struct zthread_info *zthread_arg, void *config_cookie)
{
    struct event_base *base = NULL;
    int res;

    /* save heartbeat tickle ctx */
    g_np_stats_frr_monitor.monitor_hb_thread = zthread_arg;

    base = event_base_new();
    if (!base) {
        NP_LOG(AL_ERROR, "ERROR NP frr stats monitor thread base_new");
        goto fail;
    }
    g_np_stats_frr_monitor.monitor_thread_base = base;

    res = np_stats_frr_monitor_timer_init(base);
    if (res) {
        NP_LOG(AL_ERROR, "ERROR NP frr stats monitor timer init");
        goto fail;
    }

    /* Run cache timer */
    zevent_base_dispatch(base);

    NP_FRR_DEBUG("NP frr stats monitor thread base dispatch");

fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
    return NULL;
}

int np_stats_frr_monitor_init()
{
    int res = NP_RESULT_NO_ERROR;

    /*
     * This will create a monitor thread, this in turn will create timer event base and timer event
     */
    res = zthread_create(&g_np_stats_frr_monitor.monitor_thread_id,   // thread
                         np_stats_frr_monitor_thread,      // thread func
                         NULL,                              // cookie
                         "np_stats_frr_monitor_thread",    // thread name
                         NP_STATS_FRR_MONITOR_HEARTBEAT_TIMEOUT_S,
                         NP_STATS_FRR_MONITOR_THREAD_STACK_SIZE,
                         MINUTE_TO_US(5),
                         NULL);                             // user void

    if (res) {
        NP_LOG(AL_ERROR, "ERROR NP frr stats monitor init thread_create ret %s",
                  zpath_result_string(res));
    }

    return res;
}
