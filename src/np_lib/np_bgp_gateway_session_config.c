/*
 * np_bgp_gateway_session_config.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np_private.h"
#include "np_lib/np_bgp_gateway_session_config.h"
#include "np_lib/np_bgp_gateway_session_config_compiled.h"
#include "np_lib/np.h"

#define NP_BGP_MAX_SESSIONS_PER_CUSTOMER 1000 // this can be huge, 1k is only for debug dump

struct argo_structure_description *np_bgp_gateway_session_config_description = NULL;

struct wally_index_column **np_bgp_gateway_session_config_bgp_config_gid_column = NULL;
struct wally_index_column **np_bgp_gateway_session_config_customer_gid_column = NULL;

static void
np_bgp_gateway_session_config_row_fixup(struct argo_object *row)
{
    // scope gid needs to be fixed when we support it in future
    return;
}

static int
np_bgp_gateway_session_config_row_callback(void *cookie,
                                           struct wally_registrant *registrant,
                                           struct wally_table *table,
                                           struct argo_object *previous_row,
                                           struct argo_object *row,
                                           int64_t request_id)
{
    if (IS_NP_DEBUG_BGP_SESSION()) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_DEBUG_BGP_SESSION("np_bgp_gateway_session_config row callback: %s", dump);
        }
    }
    return NP_RESULT_NO_ERROR;
}

/* Gets in memory results, no wally registration */
static int
np_bgp_gateway_session_config_dump_by_customer(struct zpath_debug_state* request_state,
                                               const char** query_values,
                                               int query_value_count,
                                               void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct np_bgp_gateway_session_config *session_config[NP_BGP_MAX_SESSIONS_PER_CUSTOMER];
    char jsonout[10000];
    size_t session_config_count;
    size_t i;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return NP_RESULT_NO_ERROR;
    }

    session_config_count = NP_BGP_MAX_SESSIONS_PER_CUSTOMER;
    res = np_bgp_gateway_session_config_get_by_customer_gid_immediate(customer_gid, session_config, &session_config_count);
    if (res != NP_RESULT_NO_ERROR) {
        ZDP("couldn't query np_bgp_gateway_session_config table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return NP_RESULT_NO_ERROR;
    }

    for (i = 0; i < session_config_count; i++) {
        if (argo_structure_dump(np_bgp_gateway_session_config_description, session_config[i], jsonout, sizeof(jsonout), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZDP("%s\n", jsonout);
        }
    }
    if (session_config_count) {
        ZDP("%zu matching BGP session config in np_bgp_gateway_session_config table\n", session_config_count);
    }

    return NP_RESULT_NO_ERROR;
}

static int
np_bgp_gateway_session_config_table_response_callback(void *cookie,
                                                      struct wally_registrant *registrant,
                                                      struct wally_table *table,
                                                      int64_t sequence,
                                                      int row_count)
{
    struct wally_interlock *lock = (struct wally_interlock *)cookie;
    if (lock) {
        wally_interlock_release(lock);
    }
    NP_LOG(AL_INFO, "Loaded %d rows from np_bgp_gateway_session_config_table", row_count);

    return WALLY_RESULT_NO_ERROR;
}

static inline int wally_table_register_by_bgp_config_gid(int64_t bgp_config_gid, struct wally_interlock *lock)
{
    int shard_index = ZPATH_SHARD_FROM_GID(bgp_config_gid);
    int res = wally_table_register_for_row(NULL, np_bgp_gateway_session_config_bgp_config_gid_column[shard_index],
                &bgp_config_gid, sizeof(bgp_config_gid), 0, 0, REQUEST_AT_LEAST_ONE, 0, 0,
                np_bgp_gateway_session_config_table_response_callback, lock);
    if (res == WALLY_RESULT_NOT_READY) {
        wally_table_deregister_for_row(NULL, np_bgp_gateway_session_config_bgp_config_gid_column[shard_index], NULL, 0);
    }
    return res;
}

int
np_bgp_gateway_session_config_register_by_bgp_config_gid(int64_t bgp_config_gid)
{
    struct wally_interlock lock;
    int not_ready_count = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    wally_interlock_lock_1(&lock);
    while ((res = wally_table_register_by_bgp_config_gid(bgp_config_gid, &lock)) == WALLY_RESULT_NOT_READY) {
        /* Heartbeat so we don't crash when wally connection is down */
        zthread_heartbeat(NULL);
        if ((not_ready_count++ % 10) == 0) {
            NP_LOG(AL_NOTICE, "Wally is not ready for registering np_bgp_gateway_session_config table");
        }
        sleep(1);
    }
    if (res != WALLY_RESULT_NO_ERROR && res != WALLY_RESULT_ASYNCHRONOUS) {
        NP_LOG(AL_ERROR, "Failed to register wally table for row: %s",  zpath_result_string(res));
        return res;
    }

    NP_LOG(AL_NOTICE, "Registering np_bgp_gateway_session_config table with bgp_config_gid %"PRId64"", bgp_config_gid);
    /* Deadlock until our callback releases this lock */
    wally_interlock_lock_2(&lock);
    NP_LOG(AL_NOTICE, "Registering np_bgp_gateway_session_config table with bgp_config_gid %"PRId64"... Complete", bgp_config_gid);

    return NP_RESULT_NO_ERROR;
}

int
np_bgp_gateway_session_config_table_init(struct wally *single_tenant_wally,
                                         int64_t tenant_id,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         int single_tenant_fully_loaded)
{
    static int initialized = 0;
    int res;

    if (initialized) return NP_RESULT_NO_ERROR;

    np_bgp_gateway_session_config_description = argo_register_global_structure(NP_BGP_GATEWAY_SESSION_CONFIG_HELPER);
    if (!np_bgp_gateway_session_config_description) {
        return NP_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(tenant_id);

        np_bgp_gateway_session_config_bgp_config_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_bgp_gateway_session_config_bgp_config_gid_column));
        np_bgp_gateway_session_config_customer_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_bgp_gateway_session_config_customer_gid_column));

        if (single_tenant_fully_loaded) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        np_bgp_gateway_session_config_description,
                                                        (all_rows_callback ? all_rows_callback : &np_bgp_gateway_session_config_row_callback),
                                                        all_rows_callback_cookie,
                                                        np_bgp_gateway_session_config_row_fixup,
                                                        0); // Do not register with zpath_table
            if (res) {
                NP_LOG(AL_ERROR, "Could not get np_bgp_gateway_session_config fully loaded");
                return NP_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       np_bgp_gateway_session_config_description,
                                       (all_rows_callback ? all_rows_callback : &np_bgp_gateway_session_config_row_callback),
                                       all_rows_callback_cookie,
                                       1,
                                       0,
                                       np_bgp_gateway_session_config_row_fixup);
            if (!table) {
                NP_LOG(AL_ERROR, "Could not get np_bgp_gateway_session_config table");
                return NP_RESULT_ERR;
            }
        }

        np_bgp_gateway_session_config_bgp_config_gid_column[shard_index] = wally_table_get_index(table, "bgp_config_gid");
        if (!np_bgp_gateway_session_config_bgp_config_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get bgp_config_gid column from np_bgp_gateway_session_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_gateway_session_config_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_bgp_gateway_session_config_customer_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get customer gid column from np_bgp_gateway_session_config table");
            return NP_RESULT_ERR;
        }
    } else {
        res = zpath_app_add_np_sharded_table(np_bgp_gateway_session_config_description,
                                             (all_rows_callback ? all_rows_callback : &np_bgp_gateway_session_config_row_callback),
                                             all_rows_callback_cookie,
                                             0,
                                             np_bgp_gateway_session_config_row_fixup);
        if (res) {
            return res;
        }

        np_bgp_gateway_session_config_bgp_config_gid_column = zpath_app_get_np_sharded_index("np_bgp_gateway_session_config", "bgp_config_gid");
        if (!np_bgp_gateway_session_config_bgp_config_gid_column) {
            NP_LOG(AL_ERROR, "Could not get gateway gid column from np_bgp_gateway_session_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_gateway_session_config_customer_gid_column = zpath_app_get_np_sharded_index("np_bgp_gateway_session_config", "customer_gid");
        if (!np_bgp_gateway_session_config_customer_gid_column) {
            NP_LOG(AL_ERROR, "Could not get customer gid column from np_bgp_gateway_session_config table");
            return NP_RESULT_ERR;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all gateway session configs per customer.",
                                            "/np/bgp/gateway_session_config/customer_dump",
                                            np_bgp_gateway_session_config_dump_by_customer,
                                            NULL,
                                            "customer", "Required. Customer GID",
                                            NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/gateway_session_config/customer_dump");
        return res;
    }

    initialized = 1;

    return NP_RESULT_NO_ERROR;
}

int
np_bgp_gateway_session_config_get_by_customer_gid_immediate(int64_t customer_gid,
                                                            struct np_bgp_gateway_session_config **session_config,
                                                            size_t *session_config_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(np_bgp_gateway_session_config_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)session_config,
                                    session_config_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_bgp_gateway_session_config_get_by_bgp_config_gid_immediate(int64_t bgp_config_gid,
                                                              struct np_bgp_gateway_session_config **session_config,
                                                              size_t *session_config_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(bgp_config_gid);

    res = wally_table_get_rows_fast(np_bgp_gateway_session_config_bgp_config_gid_column[shard_index],
                                    &bgp_config_gid,
                                    sizeof(bgp_config_gid),
                                    (void **)session_config,
                                    session_config_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}
