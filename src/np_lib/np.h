/*
 * np.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
 */

#ifndef __NP_H__
#define __NP_H__

#include <inttypes.h>

#include "argo/argo.h"
#include "argo/argo_log.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef void (np_feature_instance_monitor_flag_cb)(int enabled);
typedef void (np_feature_customer_monitor_flag_cb)(int64_t customer_gid, int enabled);

extern struct zpath_allocator np_allocator;
extern struct zpath_allocator np_bgp_allocator;

#define NP_MALLOC(x)       zpath_malloc(&np_allocator, x, __LINE__, __FILE__)
#define NP_FREE(x)         zpath_free(x, __LINE__, __FILE__)
#define NP_FREE_SLOW(x)    zpath_free_slow(x, __LINE__, __FILE__)
#define NP_CALLOC(x)       zpath_calloc(&np_allocator, x, __LINE__, __FILE__)
#define NP_STRDUP(x, y)    zpath_strdup(&np_allocator, x, y, __LINE__, __FILE__)
#define NP_REALLOC(x, y)   zpath_realloc(&np_allocator, x, y, __LINE__, __FILE__)

#define NP_BGP_MALLOC(x)       zpath_malloc(&np_bgp_allocator, x, __LINE__, __FILE__)
#define NP_BGP_FREE(x)         zpath_free(x, __LINE__, __FILE__)
#define NP_BGP_FREE_SLOW(x)    zpath_free_slow(x, __LINE__, __FILE__)
#define NP_BGP_CALLOC(x)       zpath_calloc(&np_bgp_allocator, x, __LINE__, __FILE__)
#define NP_BGP_STRDUP(x, y)    zpath_strdup(&np_bgp_allocator, x, y, __LINE__, __FILE__)
#define NP_BGP_REALLOC(x, y)   zpath_realloc(&np_bgp_allocator, x, y, __LINE__, __FILE__)


extern struct argo_log_collection *np_event_log;

#define NP_LOG(priority, format...)         ARGO_LOG(np_event_log, priority, "np", ##format)
#define NP_LOG_NOT_IMPLEMENTED()            NP_LOG(AL_ERROR, "Not Implemented")
#define NP_DEBUG_LOG(condition, format...)  ARGO_DEBUG_LOG(condition, np_event_log, argo_log_priority_debug, "np", ##format)

/*
 * NP_BGP_FRR_CONFIG_FILENAME_GENERATED -> latest config 'generated'
 * NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED -> copy of error config (if any) when test reloading NP_BGP_FRR_CONFIG_FILENAME_GENERATED
 * NP_BGP_FRR_ERROR_LOG_FILENAME_GENERATED -> copy of error log (if any) when test reloading NP_BGP_FRR_CONFIG_FILENAME_GENERATED

 * NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE -> latest config from 'override_config'
 * NP_BGP_FRR_ERROR_CONFIG_FILENAME_OVERRIDE -> copy of error config (if any) when test reloading NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE
 * NP_BGP_FRR_ERROR_LOG_FILENAME_OVERRIDE -> copy of error log (if any) when test reloading NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE

 * NP_BGP_FRR_CONFIG_FILENAME:
 *   will be overwritten if NP_BGP_FRR_CONFIG_FILENAME_GENERATED/NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE is validated
 * NP_BGP_FRR_CONFIG_FILENAME_PREV:
 *   will be replaced as NP_BGP_FRR_CONFIG_FILENAME before it is overwritten
 */
#define NP_BGP_FRR_CONFIG_FILENAME_GENERATED        "/opt/zscaler/etc/frr/frr_generated.conf"
#define NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED  "/opt/zscaler/etc/frr/frr_generated.error"
#define NP_BGP_FRR_ERROR_LOG_FILENAME_GENERATED     "/opt/zscaler/etc/frr/frr_generated.log"

#define NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE         "/opt/zscaler/etc/frr/frr_override.conf"
#define NP_BGP_FRR_ERROR_CONFIG_FILENAME_OVERRIDE   "/opt/zscaler/etc/frr/frr_override.error"
#define NP_BGP_FRR_ERROR_LOG_FILENAME_OVERRIDE      "/opt/zscaler/etc/frr/frr_override.log"

#define NP_BGP_FRR_CONFIG_FILENAME                  "/opt/zscaler/etc/frr/frr.conf"
#define NP_BGP_FRR_CONFIG_FILENAME_PREV             "/opt/zscaler/etc/frr/frr.conf.prev"


#define NP_BGP_MAX_PEERS        1024 // ensure it is >= with NPWG_MAX_PEERS
#define NP_BGP_MAX_SUBNETS      2000 // ensure it is >= NPWG_MAX_ALLOWED_IPS_PER_PEER

/*
 * AFI SAFI translation:
 * AFI=1, SAFI=1: IPv4 unicast.
 * AFI=1, SAFI=2: IPv4 multicast.
 * AFI=2, SAFI=1: IPv6 unicast.
 * AFI=2, SAFI=2: IPv6 multicast.
 */
#define NP_BGP_MAX_AFI_SAFI                 10
#define NP_BGP_AFI_SAFI_IPV4_UNICAST        "1,1"
#define NP_BGP_AFI_SAFI_IPV4_MULTICAST      "1,2"
#define NP_BGP_AFI_SAFI_IPV6_UNICAST        "2,1"
#define NP_BGP_AFI_SAFI_IPV6_MULTICAST      "2,2"

enum np_bgp_afi_safi_code {
    np_bgp_afi_safi_code_ipv4_unicast = 0,
    np_bgp_afi_safi_code_ipv6_unicast = 1,
    np_bgp_afi_safi_code_ipv4_multicast = 2,
    np_bgp_afi_safi_code_ipv6_multicast = 3,
};

#define NP_VRF_NAME             "vpn_vrf1" // ensure it is in sync with NPWG_VRF_NAME
#define NP_WG_INTERFACE_NAME    "npwg0" // ensure it is in sync with NPWG_INTERFACE_NAME

/*
 * Return a boolean whether NP hard_disabled is enabled.
 */
int np_is_hard_disabled();
int np_policy_is_hard_disabled();

void np_set_all_disabled_config_monitor(np_feature_instance_monitor_flag_cb *callback);
void np_set_feature_config_monitor(np_feature_customer_monitor_flag_cb *callback);
void np_set_redundancy_feature_config_monitor(np_feature_customer_monitor_flag_cb *callback);

/*
 * Return a boolean whether NP feature is enabled for the customer.
 * This function also checks the hard_disabled flag so you don't need
 * to call np_is_hard_disabled() separately.
 */
int np_is_feature_enabled(int64_t customer_gid);

int np_policy_feature_is_enabled(int64_t customer_gid, int *is_policy_feature_toggle);

int np_is_redundancy_feature_enabled(int64_t customer_gid);

/* for non-app connector coponents, pass instance_gid as 0 if the component(e.g. np_gateway) don't need NP feature flags */
int np_init(int64_t instance_gid);

/* for app_connector
 * connector_gid : app connector gid
 * connector_grp_gid : app connector group gid
 *
 * the feature flag init function has to be called in component intialization time,
 * as there is a allocator allocated inside this function, all allocators has to be allocated at init time.
 *
 * np connector init only happened after intiialization when config tables are loaded and read.
 * then appc will retrieve np connector gid .. etc (at this point np connector is already enabled)
 *
 * thus for feature enablement of NP connector functionality, we will use the component main gid, appc gid.
 *
 * appc gid can be looked up by np_connector gid from np_connector table.
 *
 */
int np_init_appc(int64_t connector_gid, int64_t connector_grp_gid);
int np_init_pre_appc();
int np_is_initialized();
int is_np_customer_connected(int64_t customer_gid);

#ifdef __cplusplus
} // extern "C"
#endif

#endif /* __NP_H__ */
