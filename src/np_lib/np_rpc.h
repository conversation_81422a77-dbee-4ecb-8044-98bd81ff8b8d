/*
 * np_rpc.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * This file defines RPC messages for the Network Presence feature
 * exchanged between ZCC (NP Client) and broker.
 */

#ifndef __NP_RPC_H__
#define __NP_RPC_H__

#include "argo/argo.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_BUF_LEN 256

extern struct argo_structure_description *zpn_np_bgp_peer_stats_description;
extern struct argo_structure_description *zpn_np_bgp_summary_stats_description;
extern struct argo_structure_description *zpn_np_ip_route_next_hop_description;
extern struct argo_structure_description *zpn_np_ip_route_description;
extern struct argo_structure_description *zpn_np_ip_route_stats_description;
extern struct argo_structure_description *zpn_np_bgp_route_next_hop_description;
extern struct argo_structure_description *zpn_np_bgp_route_description;
extern struct argo_structure_description *zpn_np_bgp_route_stats_description;
extern struct argo_structure_description *zpn_np_neighbor_description;
extern struct argo_structure_description *zpn_np_nw_comp_stats_description;
extern struct argo_structure_description *zpn_np_route_comp_stats_description;

extern struct argo_structure_description *zpn_np_cumulative_bgp_peer_stats_description;
extern struct argo_structure_description *zpn_np_cumulative_bgp_route_stats_description;
extern struct argo_structure_description *zpn_np_frr_svc_stats_description;


/*
 * zpn_np_config_request is initiated by NP Client in order to fetch NP Gateway configuration
 * to establish a new tunnel.
 */
struct zpn_np_config_request              /* _ARGO: object_definition */
{
    /* An array of NP Gateway GIDs which the NP Client wants to exclude from zpn_np_config. */
    int64_t *exclude_gateway_gids;        /* _ARGO: integer */

    /* The number of GIDs in exclude_gateway_gids. */
    int32_t exclude_gateway_gids_count;   /* _ARGO: integer, quiet, count:exclude_gateway_gids */
};


/*
 * zpn_np_config is sent by broker as a reply to zpn_np_config_request.
 * zpn_np_gateway_config represents configuration for a single gateway.
 */
struct zpn_np_gateway_config      /* _ARGO: object_definition */
{
    /* An NP Gateway GID. */
    int64_t gid;                  /* _ARGO: integer */

    /* A base64 encoded public key of the NP Gateway. */
    const char *public_key;       /* _ARGO: string */

    /* An epoch time in seconds at when the public_key will be expired. */
    int64_t key_expiry_s;         /* _ARGO: integer */

    /* An IP address which NP Client can connect the NP Gateway. */
    struct argo_inet listen_ip;   /* _ARGO: inet */

    /* A port number which the NP Gateway listens on. */
    int32_t listen_port;          /* _ARGO: integer */

    /* MTU for the NP Gateway. */
    int32_t mtu;                  /* _ARGO: integer */

    /* An NP IP address which NP Client can use for a virtual network interface
     * to connect the NP Gateway. */
    struct argo_inet client_ip;   /* _ARGO: inet */
};


/*
 * Status code for NP config request.
 */
enum zpn_np_config_status_code
{
    /* At least one gateway config is returned. */
    zpn_np_config_success,

    /* Failure because of an incorrect request. */
    zpn_np_config_error_bad_request,

    /* Failure because NP is forbidden by policy. */
    zpn_np_config_error_forbidden,

    /* Failure because no active gateway is found. */
    zpn_np_config_error_no_gateway_available,

    /* Failure because all the IP addresses are run out. */
    zpn_np_config_error_no_ip_available,

    /* Failure because of an internal service error. */
    zpn_np_config_error_service_not_available,

    /* Failure because there's another new connection with the same CN. */
    zpn_np_config_error_stale_connection,

    /* Failure because the feature is disabled for the customer. */
    zpn_np_config_error_service_disabled,

    /* Failure because the feature is not supported. (For PSE use case) */
    zpn_np_config_error_not_supported,

    /* Failure because the NP app registration failed */
    zpn_np_config_error_app_registration_fail,
};

const char *zpn_np_config_status_code_to_string(enum zpn_np_config_status_code status_code);



#define NP_CONFIG_MAX_GATEWAYS 5

struct zpn_np_config                                        /* _ARGO: object_definition */
{
    /* An array of zpn_np_gateway_config objects. */
    struct argo_object *gateways[NP_CONFIG_MAX_GATEWAYS];   /* _ARGO: argo_object */

    /* The number of objects in gateways. */
    int32_t gateways_count;                                 /* _ARGO: integer, quiet, count:gateways */

    /* Indicates whether success or failure for zpn_np_config_request with
     * the codes defined as zpn_ipars_reservation_status_code above.
     */
     int32_t status_code;                                   /* _ARGO: integer */

    /* An error message for a case that no gateways is returned.
     * It'll be NULL if gateways has at least one object. */
    const char *error;                                      /* _ARGO: string */
};


/*
 * zpn_np_gateway_select is sent by NP Client to inform NP Gateway selection with broker.
 */
struct zpn_np_gateway_select      /* _ARGO: object_definition */
{
    /* A GID of the NP Gateway which the NP Client successfully established an NP tunnel. */
    int64_t gid;                  /* _ARGO: integer */

    /* An NP IP address which NP Client actually binds to a virtual network interface.
     * Only this NP IP address will be promoted to long-term reservation. */
    struct argo_inet client_ip;   /* _ARGO: inet */
};


/*
 * zpn_np_gateway_select_ack is sent by broker as a reply to zpn_np_gateway_select.
 */
struct zpn_np_gateway_select_ack    /* _ARGO: object_definition */
{
    /* A message for failure reason.
     * It'll be NULL if zpn_np_gateway_select is successfully processed. */
    const char *error;              /* _ARGO: string */

    /* An interval in seconds between each zpn_np_broker_keep_alive that
     * the NP Client is supposed to send. It'll be valid only if error is NULL.
     */
    int32_t keep_alive_interval_s;  /* _ARGO: integer */
};


/*
 * zpn_np_broker_keep_alive is sent by NP Client to notify broker the aliveness of NP tunnel.
 */
struct zpn_np_broker_keep_alive   /* _ARGO: object_definition */
{
    /* A GID of the NP Gateway which the NP Client is connected to. */
    int64_t gateway_gid;          /* _ARGO: integer */

    /* An NP IP address which NP Client actually binds to a virtual network interface. */
    struct argo_inet client_ip;   /* _ARGO: inet */
};


/*
 * zpn_np_config_update is sent by broker to notify that NP Client needs to reflect
 * the latest configuration according to a given action.
 */
enum zpn_np_config_update_action
{
    zpn_np_config_update_action_reset = 1
};

struct zpn_np_config_update   /* _ARGO: object_definition */
{
    /* An action what NP Client needs to take defined as zpn_np_config_update_action. */
    int32_t action;           /* _ARGO: integer */
};

/*
 * Broker NP app download: LAN subnets, REMOTE subnets
 */
struct zpn_np_client_app                    /* _ARGO: object_definition */
{
    const struct argo_inet *app_subnet;     /* _ARGO: inet */

    /* app has been deleted or not */
    int deleted;                            /* _ARGO: integer */

    /* "LAN" for LAN subnets, "CLIENT" for client subnets */
    const char *app_type;                   /* _ARGO: string */
};

/* Sent when initial np app download is complete */
struct zpn_np_client_app_complete           /* _ARGO: object_definition */
{
    const char *error;                      /* _ARGO: string */
};

/*
 * Broker NP domain app download: FQDN, DNS name servers
 */
struct zpn_np_app_domain {                  /* _ARGO: object_definition */
    /* FQDN registered */
    const char *domain;                     /* _ARGO: string */

    /* list of name servers associated to the FQDN */
    const struct argo_inet **nameservers;   /* _ARGO: inet */
    int nameservers_count;                  /* _ARGO: integer, quiet, count:nameservers */

    /* app has been deleted or not */
    int deleted;                            /* _ARGO: integer */
};

/* Sent when initial np domain app download is complete */
struct zpn_np_app_domain_complete {         /* _ARGO: object_definition */
    const char *error;                      /* _ARGO: string */
};

extern struct argo_structure_description *zpn_np_config_request_description;
extern struct argo_structure_description *zpn_np_gateway_config_description;
extern struct argo_structure_description *zpn_np_config_description;
extern struct argo_structure_description *zpn_np_gateway_select_description;
extern struct argo_structure_description *zpn_np_gateway_select_ack_description;
extern struct argo_structure_description *zpn_np_broker_keep_alive_description;
extern struct argo_structure_description *zpn_np_config_update_description;
extern struct argo_structure_description *zpn_np_client_app_description;
extern struct argo_structure_description *zpn_np_client_app_complete_description;
extern struct argo_structure_description *zpn_np_app_domain_description;
extern struct argo_structure_description *zpn_np_app_domain_complete_description;


/*
 * np_rpc_init
 *
 * Initialize ARGO descriptions for NP RPC messages declared above.
 *
 * RETURN VALUE:
 *   0 for successful initialization.
 *   -1 if fail to register NP RPC messages.
 */
int np_rpc_broker_init();

int np_rpc_stats_init();

#define ZPN_NP_MAX_FRR_NEIGHBORS 1000
#define ZPN_NP_MAX_FRR_NEXT_HOPS 10
#define ZPN_NP_MAX_FRR_ROUTES 1000

// Show ip bgp [vrf] all json
struct zpn_np_bgp_route_next_hop {                                                  /* _ARGO: object_definition */
    uint8_t used;                                                                   /* _ARGO: integer */
    struct argo_inet ip;                                                            /* _ARGO: inet */
    char *afi;                                                                      /* _ARGO: string */
    char *hostname;                                                                 /* _ARGO: string */
};
// Show ip bgp [vrf] all json
 struct zpn_np_bgp_route {                                                                              /* _ARGO: object_definition */
    struct argo_inet net_prefix;                                                                        /* _ARGO: inet */
    int16_t prefix_len;                                                                                 /* _ARGO: integer */
    struct argo_inet peer_id;                                                                           /* _ARGO: inet */
    char *path;                                                                                         /* _ARGO: string */
    uint8_t bestpath;                                                                                   /* _ARGO: integer */
    uint8_t multi_path;                                                                                 /* _ARGO: integer */
    char *selectionReason;                                                                              /* _ARGO: string */
    char *path_from;                                                                                    /* _ARGO: string */
    char *origin;                                                                                       /* _ARGO: string */
    uint8_t valid;                                                                                      /* _ARGO: integer */
    int64_t weight;                                                                                     /* _ARGO: integer */

    /* Next hops are argo_objects of type zpn_np_bgp_route_next_hop */
    struct argo_object *next_hops[ZPN_NP_MAX_FRR_NEXT_HOPS];                                            /* _ARGO: argo_object */
    int32_t next_hops_count;                                                                            /* _ARGO: integer, quiet, count:next_hops */
};

// Show ip bgp [vrf] all json
struct zpn_np_bgp_route_stats {                                                                          /* _ARGO: object_definition */
    int32_t max_routes_exceeded;                                                                        /* _ARGO: integer */

    /* Routes are argo_objects of type zpn_np_bgp_route */
    struct argo_object *bgp_routes[ZPN_NP_MAX_FRR_ROUTES];                                                  /* _ARGO: argo_object */
    int32_t routes_count;                                                                               /* _ARGO: integer, quiet, count:bgp_routes */
};

// Show ip route [vrf] all json
struct zpn_np_ip_route_next_hop {                                                   /* _ARGO: object_definition */
    uint8_t active;                                                                 /* _ARGO: integer */
    struct argo_inet ip;                                                            /* _ARGO: inet */
    uint8_t fib;                                                                    /* _ARGO: integer */
    uint8_t directly_connected;                                                     /* _ARGO: integer */
    char *interface_name;                                                           /* _ARGO: string */
    char *afi;                                                                      /* _ARGO: string */
    uint16_t weight;                                                                /* _ARGO: integer */
    uint8_t recursive;                                                              /* _ARGO: integer */
    uint8_t on_link;                                                                /* _ARGO: integer */
};

// Show ip route [vrf] all json
struct zpn_np_ip_route {                                                                                /* _ARGO: object_definition */
    struct argo_inet net_prefix;                                                                        /* _ARGO: inet */
    int16_t prefix_len;                                                                                 /* _ARGO: integer */
    char *protocol;                                                                                     /* _ARGO: string */
    uint8_t selected_route;                                                                             /* _ARGO: integer */
    uint8_t dest_selected;                                                                              /* _ARGO: integer */
    uint8_t installed_route;                                                                            /* _ARGO: integer */
    int64_t uptime_s;                                                                                   /* _ARGO: integer */

    /* Next hops are argo_objects of type zpn_np_ip_route_next_hop */
    struct argo_object *next_hops[ZPN_NP_MAX_FRR_NEXT_HOPS];                                            /* _ARGO: argo_object */
    int32_t next_hops_count;                                                                            /* _ARGO: integer, quiet, count:next_hops */
};

// Show ip route [vrf] all json
struct zpn_np_ip_route_stats {                                                                          /* _ARGO: object_definition */
    int32_t max_routes_exceeded;                                                                        /* _ARGO: integer */

    /* Routes are argo_objects of type zpn_np_ip_route */
    struct argo_object *ip_routes[ZPN_NP_MAX_FRR_ROUTES];                                                  /* _ARGO: argo_object */
    int32_t routes_count;                                                                               /* _ARGO: integer, quiet, count:ip_routes */
};

 // Show ip bgp [vrf] all neighbors json
struct zpn_np_neighbor {                                          /* _ARGO: object_definition */
    int64_t remote_as;                                            /* _ARGO: integer */
    char *remote_router_id;                                       /* _ARGO: string */
    char *bgp_state;                                              /* _ARGO: string */
    int16_t bfd_multiplier;                                       /* _ARGO: integer */
    int32_t bfd_rx_min_interval;                                  /* _ARGO: integer */
    int32_t bfd_tx_min_interval;                                  /* _ARGO: integer */
    char *bfd_status;                                             /* _ARGO: string */
};

 // Show ip bgp [vrf] all neighbors json
struct zpn_np_neighbor_stats {

    /* Peers are argo_objects of type zpn_np_neighbor */
    struct argo_object *neighbors[ZPN_NP_MAX_FRR_NEIGHBORS];
    int32_t neighbors_count;
    int32_t max_neighbors_exceeded;
};


// Show ip bgp [vrf] all summary json
struct zpn_np_bgp_peer_stats {                                                /* _ARGO: object_definition */
    char *router_id;                                                    /* _ARGO: string */
    int64_t peer_gid;                                                   /* _ARGO: integer */
    uint8_t peer_type;                                                  /* _ARGO: integer */ /* Either "vse == 1" or "npc == 2" or "externel_router == 3" */
    int64_t remote_as;                                                  /* _ARGO: integer */
    int64_t uptime_s;                                                   /* _ARGO: integer */
    int64_t prefixes_sent;                                              /* _ARGO: integer */
    int64_t prefixes_received;                                          /* _ARGO: integer */
    char *state;                                                        /* _ARGO: string */
    char *peer_state;                                                   /* _ARGO: string */
    int16_t bfd_multiplier;                                             /* _ARGO: integer */
    int32_t bfd_rx_min_interval;                                        /* _ARGO: integer */
    int32_t bfd_tx_min_interval;                                        /* _ARGO: integer */
    char *bfd_status;                                                   /* _ARGO: string */
};

#define ZPN_NP_MAX_FRR_PEERS 1000
// Show ip bgp [vrf] all summary json
struct zpn_np_bgp_summary_stats {                                                  /* _ARGO: object_definition */
    int32_t max_peers_exceeded;                                                    /* _ARGO: integer */

    /* Peers are argo_objects of type zpn_np_bgp_peer_stats */
    struct argo_object *peers[ZPN_NP_MAX_FRR_PEERS];                               /* _ARGO: argo_object */
    int32_t peers_count;                                                           /* _ARGO: integer, quiet, count:peers */
};

// Consolidated network stats
struct zpn_np_nw_comp_stats {                                   /* _ARGO: object_definition */
    int64_t update_id;                                          /* _ARGO: integer */
    int64_t sys_gid;                                            /* _ARGO: integer */
    int64_t g_cst;                                              /* _ARGO: integer */
    uint8_t sys_type;                                           /* _ARGO: integer */ /* Either "vse == 1" or "npc == 2"*/
    char *bgp_svc_status;                                       /* _ARGO: string */
    struct argo_inet router_id;                                 /* _ARGO: inet */
    int64_t local_as;                                           /* _ARGO: integer */
    int64_t total_updates;                                      /* _ARGO: integer */
    int64_t update_index;                                       /* _ARGO: integer */

    /* Show ip bgp [vrf] all summary json */
    /* Peers are argo_objects of type zpn_np_bgp_peer_stats */
    struct argo_object *peers[ZPN_NP_MAX_FRR_PEERS];            /* _ARGO: argo_object */
    int32_t peers_count;                                        /* _ARGO: integer, quiet, count:peers */
    int32_t max_peers_exceeded;                                 /* _ARGO: integer */
};

// Consolidated network route stats
struct zpn_np_route_comp_stats {                                /* _ARGO: object_definition */
    int64_t update_id;                                          /* _ARGO: integer */
    int64_t sys_gid;                                            /* _ARGO: integer */
    int64_t g_cst;                                              /* _ARGO: integer */
    uint8_t sys_type;                                           /* _ARGO: integer */ /* Either "vse == 1" or "npc == 2"*/
    struct argo_inet router_id;                                 /* _ARGO: inet */
    int64_t local_as;                                           /* _ARGO: integer */
    int64_t total_updates;                                      /* _ARGO: integer */
    int64_t update_index;                                       /* _ARGO: integer */

    /* Show ip route [vrf] all json */
    /* Routes are argo_objects of type zpn_np_ip_route */
    struct argo_object *ip_routes[ZPN_NP_MAX_FRR_ROUTES];       /* _ARGO: argo_object */
    int32_t ip_routes_count;                                    /* _ARGO: integer, quiet, count:ip_routes */
    int32_t max_ip_routes_exceeded;                             /* _ARGO: integer */

    /* Show ip bgp [vrf] all json */
    /* Routes are argo_objects of type zpn_np_bgp_route */
    struct argo_object *bgp_routes[ZPN_NP_MAX_FRR_ROUTES];      /* _ARGO: argo_object */
    int32_t bgp_routes_count;                                   /* _ARGO: integer, quiet, count:bgp_routes */
    int32_t max_bgp_routes_exceeded;                            /* _ARGO: integer */

};

 // show ip bgp vrf all summary json ( cumulative )
struct zpn_np_cumulative_bgp_peer_stats {                               /* _ARGO: object_definition */
    uint8_t sys_type;                                                   /* _ARGO: integer */ /* Either "vse == 1" or "npc == 2" */
    struct argo_inet router_id;                                         /* _ARGO: inet */
    int64_t local_as;                                                   /* _ARGO: integer */

    int64_t min_peer_uptime_s;                                          /* _ARGO: integer */
    int64_t max_peer_uptime_s;                                          /* _ARGO: integer */

    int64_t total_prefixes_sent;                                        /* _ARGO: integer */
    int64_t total_prefixes_received;                                    /* _ARGO: integer */

    int32_t total_peers_established;                                    /* _ARGO: integer */
    int32_t total_peers_not_established;                                /* _ARGO: integer */
    int32_t total_peers;                                                /* _ARGO: integer */
};

 // show ip bgp vrf all json
 struct zpn_np_cumulative_bgp_route_stats {                                                             /* _ARGO: object_definition */
    uint8_t sys_type;                                                                                   /* _ARGO: integer */ /* Either "vse == 1" or "npc == 2" */
    struct argo_inet router_id;                                                                         /* _ARGO: inet */
    int64_t local_as;                                                                                   /* _ARGO: integer */

    int32_t total_path_count;                                                                           /* _ARGO: integer */
    int32_t total_best_path_count;                                                                      /* _ARGO: integer */
    int32_t total_multi_path_count;                                                                     /* _ARGO: integer */
    int32_t total_igp_origin_count;                                                                     /* _ARGO: integer */
    int32_t total_external_origin_count;                                                                /* _ARGO: integer */
    int32_t total_valid_count;                                                                          /* _ARGO: integer */
    int32_t total_next_hop_count;                                                                       /* _ARGO: integer */
};

struct zpn_np_frr_svc_stats {                               /* _ARGO: object_definition */
    char svc_status[MAX_BUF_LEN];                           /* _ARGO: string */
    int32_t cfg_mode;                                       /* _ARGO: integer */
    char cfg_status[MAX_BUF_LEN];                           /* _ARGO: string */
    char detected_version[MAX_BUF_LEN];                     /* _ARGO: string */
    int16_t cfg_reload_option;                              /* _ARGO: integer */
    int16_t cfg_use_pre_generated;                          /* _ARGO: integer */
    int64_t svc_uptime_s;                                   /* _ARGO: integer */
};

#ifdef __cplusplus
} // extern "C"
#endif

#endif /* __ZPN_NP_RPC_H__ */
