/*
 * np_bgp_config_frr.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved.
 */
#include <cstdio>
#include <cstdlib>
#include <string>
#include <ctime>
#include <iostream>
#include <fstream>
#include <inja/inja.hpp>

#include "zpath_lib/zpath_debug.h"
#include "np_lib/frr.gateway.h"
#include "np_lib/frr.connector.h"
#include "np_lib/np.h"
#include "np_lib/np_bgp_config_frr.h"

using namespace std;
using namespace inja;

#define OVERRIDE_FRR_TEMPLATE_FILE_LOCATION "/opt/zscaler/etc/frr/frr_template.txt"

struct bfd_config bfd_profile[bfd_profile_total_count] =
{
//    name      bfd_detect_multiplier       bfd_rx_interval          bfd_tx_interval
    {"FAST",            3,                         300,                    300},
    {"MEDIUM",          3,                         500,                    500},
    {"SLOW",            3,                         1000,                   1000},
};

string bfd_profiles_to_str()
{
    stringstream ss;
    for (int i = 0; i < bfd_profile_total_count; i++) {
        ss << " profile " << bfd_profile[i].name << endl
           << " detect-multiplier " << bfd_profile[i].bfd_detect_multiplier << endl
           << " receive-interval " << bfd_profile[i].bfd_rx_interval << endl
           << " transmit-interval " << bfd_profile[i].bfd_tx_interval;
        if (i < bfd_profile_total_count - 1) {
            ss << endl;
        }
    }
    return ss.str();
};

static void define_inja_callbacks(inja::Environment& env)
{
    /*
     * Customized callback for printing datetime string
     */
    env.add_callback("datetime", 0, [](Arguments args) {
        (void)args;
        time_t current_time = time(0);
        string time_str = ctime(&current_time);
        return time_str;
    });

    /*
     * Customized callback for adding /netmask to IP.
     *  arg0: inet string. If string already has netmask field, do nothing.
     */
    env.add_callback("netmask", 1, [](Arguments& args) {
        string str = args.at(0)->get<string>();
        if (str.find("/") == string::npos) {
            // string doesn't have /netmask, add /32
            str.append("/32");
        }
        return str;
    });

    /*
     * Customized callback for writing all bfd profiles.
     */
    env.add_callback("bfd_write_profiles", 0, [](Arguments& args) {
        (void)args;
        return bfd_profiles_to_str();
    });

    /*
     * Customized callback for getting bfd profile name.
     *  arg0: bfd_detect_multiplier
     *  arg1: bfd_rx_interval
     *  arg2: bfd_tx_interval
     * Returns bfd profile's name
     */
    env.add_callback("bfd_get_profile", 3, [](Arguments& args) {
        int bfd_detect_multiplier = args.at(0)->get<int>();
        int bfd_rx_interval = args.at(1)->get<int>();
        int bfd_tx_interval = args.at(2)->get<int>();
        for (int i = 0; i < bfd_profile_total_count; i++) {
            if ((bfd_profile[i].bfd_detect_multiplier == bfd_detect_multiplier) &&
                (bfd_profile[i].bfd_rx_interval == bfd_rx_interval) &&
                (bfd_profile[i].bfd_tx_interval == bfd_tx_interval)) {
                // found
                string name = bfd_profile[i].name;
                return "profile " + name;
            }
        }
        // not found
        return string();
    });
}

/* Function to check if a file exists */
bool fileExists(const std::string& filePath) {
    std::ifstream file(filePath.c_str());
    return file.good();
}

int np_bgp_config_frr_generate_from_str(const char *config_json_str,
                                        const char *output_file_path,
                                        enum np_bgp_config_frr_template_type template_type)
{
    json data;
    try {
        data = json::parse(config_json_str);
    } catch (const json::parse_error& e) {
        NP_LOG(AL_ERROR, "Parse error when parsing json string");
        return NP_BGP_CONFIG_PARSER_ERROR;
    } catch (const exception& e) {
        NP_LOG(AL_ERROR, "Unknown error when parsing FRR template: %s", e.what());
        return NP_BGP_CONFIG_UNKNOWN_ERROR;
    }

    Environment env;
    Template temp;
    env.set_trim_blocks(true);

    define_inja_callbacks(env);

    try {
        if (fileExists(OVERRIDE_FRR_TEMPLATE_FILE_LOCATION)) {
            /* This could help us in the future? */
            NP_LOG(AL_NOTICE, "Override template file detected: %s, using override template", OVERRIDE_FRR_TEMPLATE_FILE_LOCATION);
            temp = env.parse_template(OVERRIDE_FRR_TEMPLATE_FILE_LOCATION);
        } else {
            if (template_type == NP_BGP_CONFIG_TEMPLATE_GATEWAY) {
                string gateway_str((char*)frr_gateway, frr_gateway_len);
                temp = env.parse(gateway_str);
            } else if (template_type == NP_BGP_CONFIG_TEMPLATE_CONNECTOR) {
                string connector_str((char*)frr_connector, frr_connector_len);
                temp = env.parse(connector_str);
            } else {
                return NP_BGP_CONFIG_DATA_ERROR;
            }
        }
    } catch (const ParserError& e) {
        NP_LOG(AL_ERROR, "Parser error when parsing FRR template: %s", e.message.c_str());
        return NP_BGP_CONFIG_PARSER_ERROR;
    } catch (const exception& e) {
        NP_LOG(AL_ERROR, "Unknown error when parsing FRR template: %s", e.what());
        return NP_BGP_CONFIG_UNKNOWN_ERROR;
    }

    try {
        env.write(temp, data, output_file_path);
    } catch (const RenderError& e) {
        // possibly one or more variable is NULL
        NP_LOG(AL_ERROR, "Render error when writing FRR config: %s", e.message.c_str());
        return NP_BGP_CONFIG_RENDER_ERROR;
    } catch (const DataError& e) {
        NP_LOG(AL_ERROR, "Data error when writing FRR config: %s", e.message.c_str());
        return NP_BGP_CONFIG_DATA_ERROR;
    } catch (const exception& e) {
        NP_LOG(AL_ERROR, "Unknown error when writing FRR config: %s", e.what());
        return NP_BGP_CONFIG_UNKNOWN_ERROR;
    }

    return NP_BGP_CONFIG_NO_ERROR;
}

int np_bgp_config_frr_generate_from_file(const char *template_file_path,
                                         const char *data_file_path,
                                         const char *output_file_path)
{
    Environment env;
    env.set_trim_blocks(true);

    /* add customized callbacks */
    define_inja_callbacks(env);

    try {
        env.write_with_json_file(template_file_path, data_file_path, output_file_path);
    } catch (const ParserError& e) {
        //cout << e.what() << endl;
        return NP_BGP_CONFIG_PARSER_ERROR;
    } catch (const RenderError& e) {
        //cout << e.what() << endl;
        return NP_BGP_CONFIG_RENDER_ERROR;
    } catch (const FileError& e) {
        //cout << e.what() << endl;
        return NP_BGP_CONFIG_FILE_ERROR;
    } catch (const DataError& e) {
        //cout << e.what() << endl;
        return NP_BGP_CONFIG_DATA_ERROR;
    }

    return NP_BGP_CONFIG_NO_ERROR;
}
