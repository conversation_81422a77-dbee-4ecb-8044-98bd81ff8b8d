/*
 * np_lan_subnets.c. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np_private.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_lan_subnets_compiled.h"
#include "np_lib/np.h"

#define NP_LAN_SUBNETS_MAX_SUBNETS_PER_CUSTOMER 1000

struct argo_structure_description *np_lan_subnets_description = NULL;

struct wally_index_column **np_lan_subnets_index_column = NULL;
struct wally_index_column **np_lan_subnets_customer_gid_column = NULL;
struct wally_index_column **np_lan_subnets_connector_group_gid_column = NULL;

static zpath_mutex_t hash_lock;
static struct zhash_table *customer_table = NULL;

struct customer_load_state {
    int done;
    zpath_mutex_t lock;
    struct wally_callback_queue *queue;
};

static int
np_lan_subnets_row_callback(void *cookie,
                            struct wally_registrant *registrant,
                            struct wally_table *table,
                            struct argo_object *previous_row,
                            struct argo_object *row,
                            int64_t request_id)
{
    if (IS_NP_DEBUG_LAN_SUBNETS()) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_DEBUG_LAN_SUBNETS("Row callback: %s", dump);
        }
    }
    return NP_RESULT_NO_ERROR;
}

static void
np_lan_subnets_row_fixup(struct argo_object *row)
{
    // scope gid needs to be fixed when we support it in future

    return;
}

/* This only gives result in memory, does not do registration */
static int
np_lan_subnets_dump(struct zpath_debug_state* request_state,
                    const char** query_values,
                    int query_value_count,
                    void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct np_lan_subnets *subnets[NP_LAN_SUBNETS_MAX_SUBNETS_PER_CUSTOMER];
    char jsonout[10000];
    size_t subnets_count;
    size_t i;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return NP_RESULT_NO_ERROR;
    }

    subnets_count = NP_LAN_SUBNETS_MAX_SUBNETS_PER_CUSTOMER;
    res = np_lan_subnets_get_by_customer_gid_immediate(customer_gid, subnets, &subnets_count);
    if (res != NP_RESULT_NO_ERROR) {
        ZDP("couldn't query np_lan_subnets table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return NP_RESULT_NO_ERROR;
    }

    for (i = 0; i < subnets_count; i++) {
        if (argo_structure_dump(np_lan_subnets_description, subnets[i], jsonout, sizeof(jsonout), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZDP("%s\n", jsonout);
        }
    }
    if (subnets_count) {
        ZDP("%zu matching subnets in np_lan_subnets table\n", subnets_count);
    }

    return NP_RESULT_NO_ERROR;
}

static void lan_subnet_complete_callback_deferred(void *cookie1, void *cookie2)
{
    struct customer_load_state *load_state = cookie1;
    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (!load_state->done) {
        wally_callback_queue_callback(load_state->queue);
        load_state->done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
}


static int lan_subnet_complete_callback(void *response_callback_cookie,
                                        struct wally_registrant *registrant,
                                        struct wally_table *table,
                                        int64_t request_id,
                                        int row_count)
{
    zevent_defer(lan_subnet_complete_callback_deferred, response_callback_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}


int np_lan_subnets_load(int64_t customer_gid,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id)
{
    struct customer_load_state *load_state;
    int res;

    if (!customer_gid) {
        NP_LOG(AL_ERROR, "No customer gid when loading np lan subnets!");
        return NP_RESULT_ERR;
    }

    load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
    if (!load_state) {
        ZPATH_MUTEX_LOCK(&hash_lock, __FILE__, __LINE__);
        load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!load_state) {
            load_state = NP_CALLOC(sizeof(*load_state));
            load_state->lock = ZPATH_MUTEX_INIT;
            zhash_table_store(customer_table, &customer_gid, sizeof(customer_gid), 0, load_state);
        }
        ZPATH_MUTEX_UNLOCK(&hash_lock, __FILE__, __LINE__);
    }

    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (load_state->done) {
        /* Might have been set while we wait for lock... */
        ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!load_state->queue) {
        /* Need to register */
        /* NOTE: register_for_row can call our callback SYNCHRONOUSLY
         * if wally already has state for this connection. We are not
         * handling that in this case because it should be impossible
         * for something else to have read this state before this
         * routine, and this routine only reads it once. If this use
         * model were to change, this code would have to be
         * improved. */

        int shard_id = ZPATH_GID_GET_SHARD(customer_gid);
        load_state->queue = wally_callback_queue_create();
        res = wally_table_register_for_row(NULL,
                                           np_lan_subnets_customer_gid_column[shard_id],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0, // int64_t request_id,
                                           0, // int64_t request_sequence,
                                           0, // int request_atleast_one,
                                           0, // int just_callback,
                                           0, // int unique_registration,
                                           lan_subnet_complete_callback,
                                           load_state);
        /* Result is ALWAYS asynchronous, even if the callback will
         * come synchronously... Should fix that some day. */
        if (res != NP_RESULT_ASYNCHRONOUS) {
            NP_LOG(AL_ERROR, "np_lan_subnets load load got error");
        }
    }
    wally_callback_queue_add(load_state->queue, callback_f, callback_cookie, callback_id);

    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);

    return NP_RESULT_ASYNCHRONOUS;
}

int
np_lan_subnets_table_init(struct wally *single_tenant_wally,
                          int64_t tenant_id,
                          wally_row_callback_f *all_rows_callback,
                          void *all_rows_callback_cookie,
                          int single_tenant_fully_loaded)
{
    int res;

    customer_table = zhash_table_alloc(&np_allocator);
    hash_lock = ZPATH_MUTEX_INIT;

    np_lan_subnets_description = argo_register_global_structure(NP_LAN_SUBNETS_HELPER);
    if (!np_lan_subnets_description) {
        return NP_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(tenant_id);
        np_lan_subnets_index_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_lan_subnets_index_column));
        np_lan_subnets_customer_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_lan_subnets_customer_gid_column));
        np_lan_subnets_connector_group_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_lan_subnets_connector_group_gid_column));

        if (single_tenant_fully_loaded) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        np_lan_subnets_description,
                                                        (all_rows_callback ? all_rows_callback : &np_lan_subnets_row_callback),
                                                        all_rows_callback_cookie,
                                                        np_lan_subnets_row_fixup,
                                                        0); // Do not register with zpath_table
            if (res) {
                NP_LOG(AL_ERROR, "Could not get np_lan_subnets fully loaded");
                return NP_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       np_lan_subnets_description,
                                       (all_rows_callback ? all_rows_callback : &np_lan_subnets_row_callback),
                                       all_rows_callback_cookie,
                                       1,
                                       0,
                                       np_lan_subnets_row_fixup);
            if (!table) {
                NP_LOG(AL_ERROR, "Could not get np_lan_subnets table");
                return NP_RESULT_ERR;
            }
        }

        np_lan_subnets_index_column[shard_index] = wally_table_get_index(table, "gid");
        if (!np_lan_subnets_index_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get index column");
            return NP_RESULT_ERR;
        }

        np_lan_subnets_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_lan_subnets_customer_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get customer gid column");
            return NP_RESULT_ERR;
        }

        np_lan_subnets_connector_group_gid_column[shard_index] = wally_table_get_index(table, "connector_group_gid");
        if (!np_lan_subnets_connector_group_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get connector group gid column");
            return NP_RESULT_ERR;
        }

    } else {
        res = zpath_app_add_np_sharded_table(np_lan_subnets_description,
                                             (all_rows_callback ? all_rows_callback : &np_lan_subnets_row_callback),
                                             all_rows_callback_cookie,
                                             0,
                                             np_lan_subnets_row_fixup);
        if (res) {
            return res;
        }

        np_lan_subnets_index_column = zpath_app_get_np_sharded_index("np_lan_subnets", "gid");
        if (!np_lan_subnets_index_column) {
            NP_LOG(AL_ERROR, "Could not get index column");
            return NP_RESULT_ERR;
        }

        np_lan_subnets_customer_gid_column = zpath_app_get_np_sharded_index("np_lan_subnets", "customer_gid");
        if (!np_lan_subnets_customer_gid_column) {
            NP_LOG(AL_ERROR, "Could not get customer gid column");
            return NP_RESULT_ERR;
        }

        np_lan_subnets_connector_group_gid_column = zpath_app_get_np_sharded_index("np_lan_subnets", "connector_group_gid");
        if (!np_lan_subnets_connector_group_gid_column) {
            NP_LOG(AL_ERROR, "Could not get connector group gid column");
            return NP_RESULT_ERR;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all lan subnets registered per customer.",
                                       "/np/lan_subnets/dump",
                                       np_lan_subnets_dump,
                                       NULL,
                                       "customer", "Required. Customer GID",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/lan_subnets/dump");
        return res;
    }

    return NP_RESULT_NO_ERROR;
}

int np_lan_subnets_get_by_id_immediate(int64_t subnet_id,
                                       struct np_lan_subnets **subnet)
{
    size_t row_count = 1;
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(subnet_id);

    res = wally_table_get_rows_fast(np_lan_subnets_index_column[shard_index],
                                    &subnet_id,
                                    sizeof(subnet_id),
                                    (void **) subnet,
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_lan_subnets_get_by_customer_gid_immediate(int64_t customer_gid,
                                             struct np_lan_subnets **subnets,
                                             size_t *subnets_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(np_lan_subnets_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)subnets,
                                    subnets_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_lan_subnets_get_by_connector_group_gid_immediate(int64_t connector_group_gid,
                                                    struct np_lan_subnets **subnets,
                                                    size_t *subnets_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(connector_group_gid);

    res = wally_table_get_rows_fast(np_lan_subnets_connector_group_gid_column[shard_index],
                                    &connector_group_gid,
                                    sizeof(connector_group_gid),
                                    (void **)subnets,
                                    subnets_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}


int np_lan_subnet_find_lan_subnets_delta_hash_table(const struct argo_inet **new_subnet, int new_subnet_count,
                                                    const struct argo_inet **old_subnet, int old_subnet_count,
                                                    struct argo_inet **ips_to_add, int ips_to_add_len, int *ips_to_add_count,
                                                    struct argo_inet **ips_to_delete, int ips_to_delete_len, int *ips_to_delete_count,
                                                    int64_t old_subnet_gid, int64_t new_subnet_gid)
{
    int ips_to_add_index = 0;
    int ips_to_delete_index = 0;

    struct zhash_table *old_subnet_table = zhash_table_alloc(&np_allocator);
    if (!old_subnet_table) {
        return NP_RESULT_NO_MEMORY;
    }
    struct zhash_table *new_subnet_table = zhash_table_alloc(&np_allocator);
    if (!new_subnet_table) {
        zhash_table_free(old_subnet_table);
        return NP_RESULT_NO_MEMORY;
    }

    char key_buffer[INET6_ADDRSTRLEN + 4] = {0};
    int res = NP_RESULT_NO_ERROR;

    for (int i = 0; i < old_subnet_count; i++) {
        argo_inet_generate(key_buffer, old_subnet[i]);
        res = zhash_table_store(old_subnet_table, key_buffer, strlen(key_buffer), 0, (struct argo_inet*)old_subnet[i]);
        if (res) {
            NP_LOG(AL_ERROR,
                "Failed to store old subnet in hash table. Subnet: %s, connector group gid: %" PRId64 ", error code: %d.",
                key_buffer, old_subnet_gid, res);
            goto done;
        }
    }

    for (int i = 0; i < new_subnet_count; i++) {
        argo_inet_generate(key_buffer, new_subnet[i]);

        if (!zhash_table_lookup(old_subnet_table, key_buffer, strlen(key_buffer), NULL)) {
            if (ips_to_add_index < ips_to_add_len) {
                ips_to_add[ips_to_add_index++] = (struct argo_inet *)new_subnet[i];
                NP_DEBUG_LAN_SUBNETS("LAN Subnet: %"PRId64", Marking for addition: %s", new_subnet_gid, key_buffer);
            } else {
                NP_LOG(AL_ERROR, "old subnet %" PRId64 ", new subnet %" PRId64 " exceed delta limit %d ",
                       old_subnet_gid, new_subnet_gid, ips_to_add_len);
            }
        }
    }

    for (int i = 0; i < new_subnet_count; i++) {
        argo_inet_generate(key_buffer, new_subnet[i]);

        res = zhash_table_store(new_subnet_table, key_buffer, strlen(key_buffer), 0, (struct argo_inet*)new_subnet[i]);
        if (res) {
            NP_LOG(AL_ERROR,
                "Failed to store new subnet in hash table. Subnet: %s, connector group gid: %" PRId64 ", error code: %d.",
                key_buffer, new_subnet_gid, res);
            goto done;
        }
    }

    for (int i = 0; i < old_subnet_count; i++) {
        argo_inet_generate(key_buffer, old_subnet[i]);

        if (!zhash_table_lookup(new_subnet_table, key_buffer, strlen(key_buffer), NULL)) {
            if (ips_to_delete_index < ips_to_delete_len) {
                ips_to_delete[ips_to_delete_index++] = (struct argo_inet *)old_subnet[i];
                NP_DEBUG_LAN_SUBNETS("LAN Subnet: %"PRId64", Marking for deletion: %s", old_subnet_gid, key_buffer);
            } else {
                NP_LOG(AL_ERROR, "old subnet %" PRId64 ", new subnet %" PRId64 " exceed delta limit %d ",
                       old_subnet_gid, new_subnet_gid, ips_to_delete_len);
            }
        }
    }

    NP_DEBUG_LAN_SUBNETS("LAN Subnets %"PRId64" Delta calculation complete. Subnets to add: %d, Subnets to delete: %d.", new_subnet_gid, ips_to_add_index, ips_to_delete_index);

    if (ips_to_add_count) {
        *ips_to_add_count = ips_to_add_index;
    }
    if (ips_to_delete_count) {
        *ips_to_delete_count = ips_to_delete_index;
    }

done:

    zhash_table_free(old_subnet_table);
    zhash_table_free(new_subnet_table);

    return res;
}



void np_lan_subnet_find_lan_subnets_delta(const struct argo_inet **new_subnet, int new_subnet_count,
                                          const struct argo_inet **old_subnet, int old_subnet_count,
                                          struct argo_inet **ips_to_add, int ips_to_add_len, int *ips_to_add_count,
                                          struct argo_inet **ips_to_delete, int ips_to_delete_len, int *ips_to_delete_count,
                                          int64_t old_subnet_gid, int64_t new_subnet_gid)
{
    int ips_to_add_index = 0;
    int ips_to_delete_index = 0;
    char inet_str[INET6_ADDRSTRLEN + 4];

    for (int i = 0; i < old_subnet_count; i++) {
        int found = 0;

        for (int j = 0; j < new_subnet_count; j++) {
            if (argo_inet_is_same(old_subnet[i], new_subnet[j])) {
                found = 1;
                break;
            }
        }

        if (!found) {
            if (ips_to_delete_index < ips_to_delete_len) {
                ips_to_delete[ips_to_delete_index++] = (struct argo_inet *)old_subnet[i];
                NP_DEBUG_LAN_SUBNETS("LAN Subnet: %"PRId64", Marking for deletion: %s", old_subnet_gid, argo_inet_generate(inet_str, old_subnet[i]));
            } else {
                NP_LOG(AL_ERROR, "LAN subnet %" PRId64 " exceed delta limit %d ", old_subnet_gid, ips_to_delete_len);
            }
        }
    }

    for (int i = 0; i < new_subnet_count; i++) {
        int found = 0;

        for (int j = 0; j < old_subnet_count; j++) {
            if (argo_inet_is_same(new_subnet[i], old_subnet[j])) {
                found = 1;
                break;
            }
        }

        if (!found) {
            if (ips_to_add_index < ips_to_add_len) {
                ips_to_add[ips_to_add_index++] = (struct argo_inet *)new_subnet[i];
                NP_LOG(AL_DEBUG, "LAN Subnet: %"PRId64", Marking for addition: %s", new_subnet_gid, argo_inet_generate(inet_str, new_subnet[i]));
            } else {
                NP_LOG(AL_ERROR, "LAN subnet %" PRId64 " exceed delta limit %d ", new_subnet_gid, ips_to_add_len);
            }
        }
    }

    NP_DEBUG_LAN_SUBNETS("LAN Subnets %"PRId64" Delta calculation complete. Subnets to add: %d, Subnets to delete: %d.", new_subnet_gid, ips_to_add_index, ips_to_delete_index);


    if (ips_to_add_count) *ips_to_add_count = ips_to_add_index;
    if (ips_to_delete_count) *ips_to_delete_count = ips_to_delete_index;

    return;
}
