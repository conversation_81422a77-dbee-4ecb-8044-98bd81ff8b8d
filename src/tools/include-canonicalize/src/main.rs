use anyhow::Result;
use clap::Parser;
use log::error;
use regex::{Captures, Regex};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::LazyLock;

#[derive(Parser, Debug)]
#[command(about, long_about = None)]
struct Args {
    /// Path to root folder
    #[arg(short, required = true)]
    root: Vec<PathBuf>,
    /// Path to src
    src: PathBuf,
}

fn visit_fs<F>(path: &Path, exts: &[&str], mut callback: F)
where
    F: FnMut(&Path, &str) -> Result<()>,
{
    let mut queue = Vec::from([path.to_owned()]);
    while let Some(path) = queue.pop() {
        if path.is_file() {
            if let Some(fname) = path.file_name().map(|f| f.to_string_lossy()) {
                if exts.iter().any(|ext| fname.ends_with(ext)) {
                    if let Err(err) = callback(&path, &fname) {
                        error!("visit_fs callback fail: {err:?}");
                    }
                }
            } else {
                error!("can't get file_name component out of path {path:?}");
            }
        } else {
            match path.read_dir() {
                Ok(entries) => {
                    for entry in entries {
                        match entry {
                            Ok(entry) => queue.push(entry.path()),
                            Err(err) => {
                                error!("can't get DirEntry for path ${path:?} due to {err:?}");
                            }
                        }
                    }
                }
                Err(err) => error!("can't read dir {path:?} due to {err:?}"),
            }
        }
    }
}

fn build_header_map(roots: &[PathBuf]) -> HashMap<String, String> {
    let mut map = HashMap::new();
    for root in roots {
        visit_fs(root, &[".h", ".hpp", ".c"], |path, fname| {
            let replacement = path.strip_prefix(root)?.to_string_lossy().to_string();
            map.entry(fname.to_string())
                .and_modify(|old| error!("dup header fname, old={old}, new={replacement}"))
                .or_insert(replacement);
            Ok(())
        });
    }
    map
}

fn apply_fixes(path: &Path, header_map: &HashMap<String, String>) {
    visit_fs(path, &[".h", ".c", ".cpp"], |path, _| {
        fix_file(path, header_map)?;
        Ok(())
    });
}

fn fix_file(path: &Path, header_map: &HashMap<String, String>) -> Result<()> {
    static RE: LazyLock<Regex> =
        LazyLock::new(|| Regex::new(r#"^\s*#\s*include\s*["<](.+)[">]"#).unwrap());
    static SYS_PREFIXES: [&str; 3] = ["sys/", "ldns/", "event2/"];

    let mut out = String::new();

    let content = fs::read_to_string(path)?;
    for line in content.lines() {
        let repl_line = &RE.replace(line, |caps: &Captures| {
            let header = &caps[1];
            let fname = header.split('/').next_back().unwrap();
            let is_system = SYS_PREFIXES.iter().any(|prefix| header.starts_with(prefix));
            let replacement = header_map.get(fname);
            if !is_system && replacement.is_some() {
                format!("#include \"{}\"", replacement.unwrap())
            } else {
                format!("#include <{header}>")
            }
        });

        out.push_str(repl_line);
        out.push('\n');
    }
    fs::write(path, out)?;
    Ok(())
}

fn main() {
    env_logger::init();

    let args = Args::parse();
    let header_map = build_header_map(&args.root);
    apply_fixes(&args.src, &header_map);
}
