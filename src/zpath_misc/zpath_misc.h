/*
 * Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Miscellaneous functionality that is kind of handy to have around.
 *
 * This is designed to be a pretty lightweight library. For the moment
 * it's just a header file, hahaha!
 */
#ifndef __ZPATH_MISC_H__
#define __ZPATH_MISC_H__

#include <stdint.h>
#include <sys/time.h>
#include <stdarg.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/queue.h>
#include <pthread.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <inttypes.h>
#include <execinfo.h>
#include "cityhash-c/city-c.h"
#include <assert.h>

#define US_PER_SEC          1000000l
#define MS_PER_SEC          1000l
#define SECOND_TO_US(x)     (US_PER_SEC * x)
#define MINUTE_TO_US(x)     (SECOND_TO_US(60 * x))
#define MINUTE_TO_MSEC(x)   ((60 * x) * MS_PER_SEC)
#define HOUR_TO_US(x)       (MINUTE_TO_US(60 * x))
#define DAY_TO_US(x)        (HOUR_TO_US(24 * x))

#define likely(x)       __builtin_expect(!!(x), 1)
#define unlikely(x)     __builtin_expect(!!(x), 0)

// Sanitizer related macros.
// Check whether code is being compiled with address sanitizer.
#if __clang__
#if __has_feature(address_sanitizer)
#define ADDRESS_SANITIZER
#endif                               // __has_feature(address_sanitizer)
#elif defined(__SANITIZE_ADDRESS__)  // __clang__
#define ADDRESS_SANITIZER
#endif  // __clang__

// Mem poisoning helpers to catch invalid access.
// No need to unpoison heap mem before calling free(). A must for stack mem.
#ifdef ADDRESS_SANITIZER
#include <sanitizer/asan_interface.h>
#define ASAN_POISON_MEMORY_REGION(addr, size) __asan_poison_memory_region((addr), (size))
#define ASAN_UNPOISON_MEMORY_REGION(addr, size) __asan_unpoison_memory_region((addr), (size))
#else  // ADDRESS_SANITIZER
#define ASAN_POISON_MEMORY_REGION(addr, size) ((void)(addr), (void)(size))
#define ASAN_UNPOISON_MEMORY_REGION(addr, size) ((void)(addr), (void)(size))
#endif  // ADDRESS_SANITIZER

// Do not optimize entire function.
#if defined(__clang__)
#define Z_DO_NOT_OPTIMIZE __attribute__((optnone))
#elif defined(__GNUC__)
#define Z_DO_NOT_OPTIMIZE __attribute__((optimize("O0")))
#else
#define Z_DO_NOT_OPTIMIZE
#endif

// LSAN produces false negatives for 0 byte allocs, need to alloc something as workaround.
// ASAN shadow mem is 8 byte aligned, make it 8 byte for poisoning.
#define LSAN_ZERO_BYTE_ALLOC_SIZE 8

// Explicitly mark variables as unused.
#define UNUSED(...) ((void)sizeof((_Bool[]){__VA_ARGS__}))
#define UNUSED_PARAM(x)   (void)(x)
#define ZPATH_MISC_DEAD_BEEF_32 (0xdeadbeef)

/***************************************************
 *
 * Given a 64 bit integer, get the log2 bucket appropriate for a histogram.
 * i.e f(0) = 0   (no bits set)
 *     f(1) = 1   (first bit is max set- the 1's position)
 *     f(2) = 2   (second bit is max set- the 2's position)
 *     f(3) = 2   (second bit is max set)
 *     f(4) = 3   (third bit is max set)
 *     etc.
 * If no bits are set, returns -1.
 */
static __inline__ int histogram_log2_bucket(uint64_t value, int max_return_value)
{
    if (!value) return 0;
    /* __builtin_clzl returns the number of leading zeros in the
     * argument. The maximum value returned is 63. It is undefined
     * when given an input of 0. */
    int bucket = 64 - __builtin_clzl(value);
    if (bucket > max_return_value) return max_return_value;
    return bucket;
}

/***************************************************
 * LIST_FOREACH_SAFE in case our OS doesn't have it.
 */

#ifndef LIST_FOREACH_SAFE
#define	LIST_FOREACH_SAFE(var, head, field, tvar)           \
	for ((var) = LIST_FIRST((head));                        \
         (var) && ((tvar) = LIST_NEXT((var), field), 1);    \
         (var) = (tvar))
#endif


/*
 * Sometimes we are inlining functions that are doing memory allocations
 * and/or handling void*
 * c has implicit type conversions between void* and concrete types
 * however c++ does not allow implicit conversions
 * This allows us to compile with c++ for unit testing while
 * maintaining normal C behavior at zero cost
 * This is should be generic enough to work with any expressions
 * Modules that are brought in for unit testing should use this as well
 * TODO(blewis) If we want to streamline create and initialize into a single call then we should add that here
 */
#ifdef __cplusplus
    #ifndef INLINE_VOID_INFERENCE
        #define INLINE_VOID_INFERENCE(var, value) \
            var = reinterpret_cast<decltype(var)>(value)
    #endif
#else
    #ifndef  INLINE_VOID_INFERENCE
        #define INLINE_VOID_INFERENCE(var, value) \
            var = value
    #endif
#endif


/***************************************************
 * Pull out pair of arguments, if first one is matched
 *
 * returns 0 on success, non-zero on error.
 */
static __inline__ int zpath_pull_arg(int *argc, char *argv[], char *target, char **value)
{
    int i;
    int j;
    for (i = 1; i < (*argc); i++) {
        if (strcmp(argv[i], target) == 0) {
            if ((i + 1) < (*argc)) {
                *value = argv[i + 1];
                for (j = i; j < (*argc); j++) {
                    argv[j] = argv[j + 2];
                }
                *argc -= 2;
                return 0;
            }
        }
    }
    return -1;
}


/***************************************************
 * Bit manipulation functions
 *
 * Bit 0 set == 0x01
 */

#define SET_BIT(addr, bit) (*(((uint8_t *) (addr)) + ((bit) >> 3)) |= (1 << ((bit) & 0x7)))
#define CLEAR_BIT(addr, bit) (*(((uint8_t *) (addr)) + ((bit) >> 3)) &= (~(1 << ((bit) & 0x7))))
#define GET_BIT(addr, bit) ((*(((uint8_t *) (addr)) + ((bit) >> 3)) >> ((bit) & 0x7)) & 1)

/**************************************************
 * Bit manipulation functions, where each byte is swapped- usually for
 * querying network stuff.
 *
 * i.e. bit 0 set == 0x80.
 */
#define NET_SET_BIT(addr, bit) (*(((uint8_t *) (addr)) + ((bit) >> 3)) |= (1 << (7 - ((bit) & 0x7))))
#define NET_CLEAR_BIT(addr, bit) (*(((uint8_t *) (addr)) + ((bit) >> 3)) &= (~(1 << (7 - ((bit) & 0x7)))))
#define NET_GET_BIT(addr, bit) ((*(((uint8_t *) (addr)) + ((bit) >> 3)) >> (7 - ((bit) & 0x7))) & 1)

/***************************************************
 * Bit-wise flag definitions
 *
 * BIT_FLAG_U32(0)  == 0x00000001UL
 * BIT_FLAG_U32(1)  == 0x00000002UL
 * BIT_FLAG_U32(31) == 0x80000000UL
 */
#define BIT_FLAG_U32(n)   (uint32_t)(1UL << n)
#define BIT_FLAG_U64(n)   (uint64_t)(1ULL << n)

/***************************************************
 * Simple string downcase
 */
static __inline__ void zpath_downcase(char *str)
{
    char *c;
    if (str) {
        for (c = str; (*c); c++) {
            (*c) = tolower(*(c));
        }
    }
}

/***************************************************
 * Simple string upcase
 */
static __inline__ void zpath_upcase(char *str)
{
    char *c;
    if (str) {
        for (c = str; (*c); c++) {
            (*c) = toupper(*(c));
        }
    }
}



/***************************************************
 * Time functions
 */

/*
 * Return current epoch, in seconds.
 *
 * (This could be replaced by returning a variable that is updated
 * every second)
 */
static __inline__ int64_t epoch_s(void)
{
    int64_t ret;
    struct timeval tv;
    gettimeofday(&tv, NULL);
    ret = (int64_t)tv.tv_sec;
    return ret;
}

/*
 * Linux only: Return current time elapsed since boot time, in seconds.
 * This should ensure a monotonic clock as CLOCK_MONOTONIC_RAW is not disciplined by NTP
 *
 * Other platforms: Return current epoch in seconds.
 */
static __inline__ int64_t monotime_s(void)
{
    int64_t ret;
#ifdef __linux__
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC_RAW, &ts) == -1) {
        /* most likely on very old kernel and CLOCK_MONOTONIC_RAW is not supported */
        ret = epoch_s();
    } else {
        ret = (int64_t)ts.tv_sec;
    }
#else
    ret = epoch_s();
#endif
    return ret;
}

/*
 * Return current epoch, in microseconds, but with second accuracy.
 *
 * (This could be replaced by returning a variable that is updated
 * every second)
 */
static __inline__ int64_t epoch_us_accuracy_s(void)
{
    int64_t ret;
    struct timeval tv;
    gettimeofday(&tv, NULL);
    ret = (int64_t)tv.tv_sec * 1000000;
    return ret;
}

/*
 * Return current epoch, in microseconds, with microsecond accuracy.
 */
static __inline__ int64_t epoch_us_accuracy_us(void)
{
    int64_t ret;
    struct timeval tv;
    gettimeofday(&tv, NULL);
    ret = (int64_t)tv.tv_sec * 1000000l + tv.tv_usec;
    return ret;
}
static __inline__ int64_t epoch_us(void)
{
    int64_t ret;
    struct timeval tv;
    gettimeofday(&tv, NULL);
    ret = (int64_t)tv.tv_sec * 1000000l + tv.tv_usec;
    return ret;
}

/*
 * Linux only: Return current time elapsed since boot time, in microseconds.
 * This should ensure a monotonic clock as CLOCK_MONOTONIC_RAW is not disciplined by NTP
 *
 * Other platforms: Return current epoch in microseconds.
 */
static __inline__ int64_t monotime_us(void)
{
    int64_t ret;
#ifdef __linux__
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC_RAW, &ts) == -1) {
        /* most likely on very old kernel and CLOCK_MONOTONIC_RAW is not supported */
        ret = epoch_us();
    } else {
        ret = (int64_t)ts.tv_sec * 1000000l + ts.tv_nsec / 1000l;
    }
#else
    ret = epoch_us();
#endif
    return ret;
}

/*
 * el7 and other systems treat clock_gettime differently
 * On el7 we want to avoid using clock_gettime in some scenarios
 * see 'semi_monotime_us_compatible' for all the details
 *
 * Here we are just detecting systems where clock_gettime is safe to call
 * clang: on mac/freebsd we are fine to load up clock_gettime as we don't have a performance issue
 * gcc versions >= 12 imply that we are on RHEL9 or later - meaning that clock_gettime is optimized and correct
 *
 * So we will figure this out, set the flags for the function to use later
 */
#ifdef __clang__
#define use_clock_gettime
#endif
#if __GNUC__ >= 12
#define use_clock_gettime
#endif

/*
 * semi_monotime_us_compatible
 * This function exists to solve a very specific problem:
 * In el7 clock_gettime is a system call - incurring the costly overhead
 * In practice this increases the runtime by more than 10x as compared to gettimeofday
 * However, there are still many use cases where we would like to get a monotonic time
 * but cannot afford the cost of clock_gettime on el7 systems.
 *
 * On RHEL9 this issue is no longer true as clock_gettime has been promoted from a system call
 *
 * As such it is useful for us to detect our target system and use clock_gettime when we can
 * otherwise fall back on epoch_us
 *
 * Note: This actually means we aren't a true mono clock! on el7 this number *can* and
 * *will* be set back by NTP changes due to the underlying fallback being gettimeofday.
 * Note that our previous implementation could have resulted in this however likely never happened
 *
 * This ia drop in for code where it is *strictly* required to have a different implementation on el7
 * and the lack of monotime on el7 is not a problem for the implementation.
 *
 * User beware you almost certainly want to use monotime_us
 *
 * For further reading see vDSO in linux
 */
static __inline__ int64_t semi_monotime_us_compatible(void)
{
#ifdef use_fast_timer
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC_RAW, &ts);
    return (int64_t)ts.tv_sec * 1000000l + ts.tv_nsec / 1000l;
#else
    return epoch_us();
#endif
}

#undef use_clock_gettime


/***************************************************
 * Allocation functions
 */

/* Simple debugging- logs line number of allocations/frees */
//#define ZPATH_MALLOC_DEBUG

//#define ZPATH_MALLOC_DEBUG_PER_LINE_STATS

/* Uncomment this, AND per_line_stats above in order to see call
 * stats. Call stats are tracked based on a 10-deep stack trace where
 * malloc is called. */
//#define ZPATH_MALLOC_DEBUG_PER_CALL_STATS

/* NOTE: This debugging never frees memory! User RAREly */
//#define ZPATH_MALLOC_DF_DEBUG

/* Simple debugging- logs a magic number at beginning of allocation in
 * rudimentary attempt to catch double frees, etc */
//#define ZPATH_MALLOC_MAGIC

/* Overwrite protection check - checks guard magic cookie at end of
 * allocation on free. */
//#define ZPATH_MALLOC_OVERWRITE

/* In addition to overwrite allocation, keeps all allocated memory
 * tracked so that an overwrite check can be performed by calling
 * zpath_malloc_check. This puts significant load on the allocators,
 * etc, because they are doing tons of locking and extra management of
 * allocations*/
//#define ZPATH_MALLOC_OVERWRITE_FULL

/* How many 8 byte words of overwrite protection/buffer we create */
//#define ZPATH_MALLOC_OVERWRITE_WORDS 128

/* The data we use for overwrite protection */
#define ZPATH_MALLOC_OVERWRITE_MAGIC ((void *)0xa5a5a5a5a5a5a5a5)
#define ZPATH_MALLOC_LINES_DEBUG 2048
#define ZPATH_MALLOC_CALLS_DEPTH 10
#define ZPATH_MALLOC_CALLS_DEBUG (1024*16)

#ifdef ZPATH_MALLOC_DEBUG
struct zpath_allocator_line_stats {
    int64_t allocations;
    int64_t allocation_bytes;
    int64_t frees;
    int64_t free_bytes;
    int64_t drain_queue;
    int64_t drain_queue_bytes;
    int64_t line;
    const char *file;
};

struct zpath_allocator_call_stats {
    int64_t allocations;
    int64_t allocation_bytes;
    int64_t frees;
    int64_t free_bytes;
    int64_t drain_queue;
    int64_t drain_queue_bytes;
    int64_t hash;
    int64_t line;
    const char *file;
    int64_t stack_depth;
    int64_t stack[ZPATH_MALLOC_CALLS_DEPTH];
};

#endif // ZPATH_MALLOC_DEBUG

struct zpath_allocator_stats {
    int64_t allocations;
    int64_t allocation_bytes;
    int64_t frees;
    int64_t free_bytes;
    int64_t drain_queue;
    int64_t drain_queue_bytes;
#ifdef ZPATH_MALLOC_DEBUG
    struct zpath_allocator_line_stats line_stats[ZPATH_MALLOC_LINES_DEBUG];
    struct zpath_allocator_call_stats call_stats[ZPATH_MALLOC_CALLS_DEBUG];
#endif // ZPATH_MALLOC_DEBUG
};

/* Max length for allocator provided during dbg registration */
#define ZPATH_ALLOCATOR_NAME_MAX_LEN 64
/* Below flags are used for allocator_flag in struct zpath_allocator */
/* set: dbg registration required; reset: dbg registration can be skipped; init: set*/
#define ZPATH_ALLOCATOR_FLAG_DBG_REQUIRED (1 << 0)
/* set: allocator is registered with dbg; reset: allocator is not registered yet; init: reset */
#define ZPATH_ALLOCATOR_FLAG_DBG_REGISTERED (1 << 1)
/* set: allocator is auto registered; reset: allocator is manually registered or not registered yet
 * (depends on ZPATH_ALLOCATOR_FLAG_DBG_REGISTERED); init: reset */
#define ZPATH_ALLOCATOR_FLAG_DBG_AUTO_REGISTER (1 << 2)
/* set: allocator registration already done, trying again; reset: allocator is registered once or
 * not registered (depends on ZPATH_ALLOCATOR_FLAG_DBG_REGISTERED); init: reset */
#define ZPATH_ALLOCATOR_FLAG_DBG_RE_REGISTER (1 << 3)

#ifdef ZPATH_MALLOC_DEBUG
#define ZPATH_ALLOCATOR_INIT_LINE_LOCK (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER,
#else
#define ZPATH_ALLOCATOR_INIT_LINE_LOCK
#endif

#ifdef ZPATH_MALLOC_OVERWRITE_FULL
#define ZPATH_ALLOCATOR_INIT_ALLOCATED NULL,
#else
#define ZPATH_ALLOCATOR_INIT_ALLOCATED
#endif

#define ZPATH_ALLOCATOR_INIT(alloc_name) { \
            (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER, \
            ZPATH_ALLOCATOR_INIT_LINE_LOCK \
            sizeof(struct zpath_allocation_header), \
            0, \
            ZPATH_ALLOCATOR_FLAG_DBG_REQUIRED, \
            ZPATH_MISC_DEAD_BEEF_32, \
            alloc_name, \
            {0}, \
            {0}, \
            0, \
            NULL, \
            NULL, \
            ZPATH_ALLOCATOR_INIT_ALLOCATED \
            NULL, \
            NULL \
        }

#define ZPATH_ALLOCATOR_COMPACT_INIT { 0 }

/*
 * peak_stats is a copy of stats object, at the time when the peak of this particular allocation is hit.
 * peak_stats_hit_timestamp_s is the epoch time in sec, of when the peak has hit for this particular allocator.
 */
struct zpath_allocator {
    pthread_mutex_t lock;
#ifdef ZPATH_MALLOC_DEBUG
    pthread_mutex_t line_lock;
#endif // ZPATH_MALLOC_DEBUG
    // the overhead in bytes per allocations
    size_t overhead_per_allocation;
    int do_stack_debug;
    uint32_t allocator_flag;
    uint32_t init_magic;
    const char *name;
    struct zpath_allocator_stats stats;
    struct zpath_allocator_stats peak_stats;
    int64_t peak_stats_hit_epoch_s;
    /* Head of the queue used by slow free threads */
    struct zpath_allocation_header *first;
    /* Tail of the queue used by slow free threads */
    struct zpath_allocation_header *last;
#ifdef ZPATH_MALLOC_OVERWRITE_FULL
    struct zpath_allocation_header *allocated;
#endif
    /* Head of the queue used by debug thread */
    struct zpath_allocation_header *debug_thr_first;
    /* Tail of the queue used by debug thread */
    struct zpath_allocation_header *debug_thr_last;
};

struct zpath_allocation_header {
#ifdef ZPATH_MALLOC_MAGIC
    void *magic;
#endif
    uint32_t magic_pat;
    uint32_t alloc_pages; // should be no padding b4 'next'
    struct zpath_allocation_header *next;
    int32_t free_epoch_s;
    int32_t alloc_size;
    struct zpath_allocator *allocator;
#ifdef ZPATH_MALLOC_DEBUG
    int32_t alloc_line;
    int32_t free_line;
    const char *alloc_file;
    const char *free_file;
#endif
#ifdef ZPATH_MALLOC_DF_DEBUG
    int freed;
#endif
#ifdef ZPATH_MALLOC_OVERWRITE
    struct zpath_allocation_header **o_prev;
    struct zpath_allocation_header *o_next;
#endif
};

/*
* compact zpath_allocator support
*/
struct zpath_allocation_header_compact {
    uint64_t alloc_size;
};

#ifdef ZPATH_MALLOC_OVERWRITE_FULL
#define LINK(hdr)                                   \
    pthread_mutex_lock(&(hdr->allocator->lock));    \
    hdr->o_prev = &(hdr->allocator->allocated);     \
    hdr->o_next = hdr->allocator->allocated;        \
    if (hdr->allocator->allocated) {                \
        hdr->o_next->o_prev = &(hdr->o_next);       \
    }                                               \
    hdr->allocator->allocated = hdr;                \
    pthread_mutex_unlock(&(hdr->allocator->lock));

#define UNLINK(hdr)                                 \
    pthread_mutex_lock(&(hdr->allocator->lock));    \
    *(hdr->o_prev) = hdr->o_next;                   \
    if (hdr->o_next) {                              \
        hdr->o_next->o_prev = hdr->o_prev;          \
    }                                               \
    pthread_mutex_unlock(&(hdr->allocator->lock));

#else

#define LINK(hdr)
#define UNLINK(hdr)

#endif

#ifdef ZPATH_MALLOC_DEBUG


#ifdef ZPATH_MALLOC_DEBUG_PER_LINE_STATS
#ifdef __FreeBSD__
#define backtrace(x, y) 0
#endif // __FreeBSD__
#define ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(_allocator, _field_bytes, _field_count, _line, _file, _bytes, _count)                                                                 \
    /*{ */                      \
    /*int _walk;  */                  \
    /*pthread_mutex_lock(&(_allocator->line_lock)); */              \
    /*for (_walk = _line % ZPATH_MALLOC_LINES_DEBUG; _walk != (_line % ZPATH_MALLOC_LINES_DEBUG) - 1; _walk = (_walk + 1) % ZPATH_MALLOC_LINES_DEBUG) { */          \
    /*if (_allocator->stats.line_stats[_walk].line) { */      \
    /*if ((_allocator->stats.line_stats[_walk].line == _line) &&  */  \
    /*(strcmp(_allocator->stats.line_stats[_walk].file, _file) == 0)) { */ \
    /*__sync_fetch_and_add_8(&(_allocator->stats.line_stats[_walk]._field_bytes), _bytes);  */ \
    /*__sync_fetch_and_add_8(&(_allocator->stats.line_stats[_walk]._field_count), _count);  */ \
    /*break;  */ \
    /*} */ \
    /*} else {  */ \
    /*_allocator->stats.line_stats[_walk].file = _file; */ \
    /*_allocator->stats.line_stats[_walk].line = _line; */ \
    /*__sync_fetch_and_add_8(&(_allocator->stats.line_stats[_walk]._field_bytes), _bytes);  */ \
    /*__sync_fetch_and_add_8(&(_allocator->stats.line_stats[_walk]._field_count), _count);  */ \
    /*break;  */ \
    /*} */ \
    /*} */ \
    /*pthread_mutex_unlock(&(_allocator->line_lock)); */ \
    /*} */ \
    if (_allocator->do_stack_debug) {                                                                                                                                               \
        int _walk;                                                                                                                                                                  \
        int64_t _stack[ZPATH_MALLOC_CALLS_DEPTH];                                                                                                                                   \
        int64_t _stack_depth;                                                                                                                                                       \
        _stack_depth = backtrace((void **)&(_stack[0]), ZPATH_MALLOC_CALLS_DEPTH);                                                                                                  \
        int64_t _hash = CityHash64((const char *)_stack, _stack_depth * sizeof(_stack[0]));                                                                                         \
        _walk = _hash;                                                                                                                                                              \
        /*pthread_mutex_lock(&(_allocator->line_lock)); */                      \
        for (_walk = _line % ZPATH_MALLOC_CALLS_DEBUG; _walk != (_line % ZPATH_MALLOC_CALLS_DEBUG) - 1; _walk = (_walk + 1) % ZPATH_MALLOC_CALLS_DEBUG) {                           \
            if (_allocator->stats.call_stats[_walk].hash) {                                                                                                                         \
                if (_allocator->stats.call_stats[_walk].hash == _hash) {                                                                                                            \
                    __sync_fetch_and_add_8(&(_allocator->stats.call_stats[_walk]._field_bytes), _bytes);                                                                            \
                    __sync_fetch_and_add_8(&(_allocator->stats.call_stats[_walk]._field_count), _count);                                                                            \
                    break;                                                                                                                                                          \
                }                                                                                                                                                                   \
            } else {                                                                                                                                                                \
                memcpy(&(_allocator->stats.call_stats[_walk].stack[0]), &(_stack[0]), sizeof(_stack));                                                                              \
                _allocator->stats.call_stats[_walk].stack_depth = _stack_depth;                                                                                                     \
                _allocator->stats.call_stats[_walk].hash = _hash;                                                                                                                   \
                _allocator->stats.call_stats[_walk].line = _line;                                                                                                                   \
                _allocator->stats.call_stats[_walk].file = _file;                                                                                                                   \
                __sync_fetch_and_add_8(&(_allocator->stats.call_stats[_walk]._field_bytes), _bytes);                                                                                \
                __sync_fetch_and_add_8(&(_allocator->stats.call_stats[_walk]._field_count), _count);                                                                                \
                break;                                                                                                                                                              \
            }                                                                                                                                                                       \
        }                                                                                                                                                                           \
        /*pthread_mutex_unlock(&(_allocator->line_lock)); */                      \
    }
#else
#define ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(_allocator, _field_bytes, _field_count, _line, _file, _bytes, _count)
#endif /* ZPATH_MALLOC_DEBUG_PER_LINE_STATS */

#endif // ZPATH_MALLOC_DEBUG

typedef int (*zpath_misc_register_allocator_cb) (struct zpath_allocator *, const char *);
extern zpath_misc_register_allocator_cb zpath_misc_debug_add_allocator_callback;
void zpath_misc_register_allocator_method(zpath_misc_register_allocator_cb cb);

static __inline__ void *zpath_malloc(struct zpath_allocator *allocator, size_t bytes, int line, const char *file)
{
    struct zpath_allocation_header *header;
    size_t alloc_bytes = sizeof(struct zpath_allocation_header) + bytes;

    /* We assert if the allocator is not initialized, we are doing auto registration with debug,
     * but atleast the allocator should be initialized.
     */
    assert(allocator->init_magic == ZPATH_MISC_DEAD_BEEF_32);
    /* if allocator is not registered with debug, implicitly register it because otherwise it won't go
     * through the drain queue if the allocator is not registered with debug. As we won't loop through
     * the drain queue, we will have memory leak.
     */
    if (unlikely(!(__sync_fetch_and_or(&allocator->allocator_flag, 0) & ZPATH_ALLOCATOR_FLAG_DBG_REGISTERED)) &&
        likely(__sync_fetch_and_or(&allocator->allocator_flag, 0) & ZPATH_ALLOCATOR_FLAG_DBG_REQUIRED) &&
        likely(zpath_misc_debug_add_allocator_callback != NULL)) {
            if (!zpath_misc_debug_add_allocator_callback(allocator, allocator->name))
                allocator->allocator_flag |= ZPATH_ALLOCATOR_FLAG_DBG_AUTO_REGISTER;
    }

#ifdef ADDRESS_SANITIZER
    if (bytes == 0)
        alloc_bytes += LSAN_ZERO_BYTE_ALLOC_SIZE;
#endif

#ifdef ZPATH_MALLOC_OVERWRITE
    alloc_bytes += sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS;
#endif
    INLINE_VOID_INFERENCE(header, malloc(alloc_bytes));
    if (!header) {
        abort();
    }
    header->magic_pat = ZPATH_MISC_DEAD_BEEF_32;
    header->alloc_size = bytes;
    header->alloc_pages = 0; // only used by aligned malloc
    header->allocator = allocator;
#ifdef ZPATH_MALLOC_OVERWRITE
    void **where = (void **)(((char *)(&header[0])) + alloc_bytes - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
    for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
        where[i] = ZPATH_MALLOC_OVERWRITE_MAGIC;
    }
    LINK(header);
#endif
#ifdef ZPATH_MALLOC_DEBUG
    header->alloc_line = line;
    header->free_line = 0;
    header->alloc_file = file;
    header->free_file = NULL;
#else
    (void)line;
    (void) file;
#endif // ZPATH_MALLOC_DEBUG
#ifdef ZPATH_MALLOC_DF_DEBUG
    header->freed = 0;
#endif
#ifdef ZPATH_MALLOC_MAGIC
    header->magic = header;
#endif
    __sync_add_and_fetch_8(&(allocator->stats.allocations), 1);
    __sync_add_and_fetch_8(&(allocator->stats.allocation_bytes), bytes);
#ifdef ZPATH_MALLOC_DEBUG
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(allocator, allocation_bytes, allocations, line, file, bytes, 1);
#endif

    ASAN_POISON_MEMORY_REGION(header, sizeof(*header));
#ifdef ADDRESS_SANITIZER
    if (bytes == 0)
        ASAN_POISON_MEMORY_REGION(header + 1, LSAN_ZERO_BYTE_ALLOC_SIZE);
#endif
    return &(header[1]);
}

static __inline__ void *zpath_realloc(struct zpath_allocator *allocator, void *orig, size_t new_bytes, int line, const char *file)
{
    struct zpath_allocation_header *header;
    INLINE_VOID_INFERENCE(header, orig);
    size_t orig_bytes;

    if (!orig) return zpath_malloc(allocator, new_bytes, line, file);

    header--;
    ASAN_UNPOISON_MEMORY_REGION(header, sizeof(*header));
#ifdef ZPATH_MALLOC_OVERWRITE
    UNLINK(header);
    new_bytes += sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS;
    void **where = (void **)(((char *)(&header[1])) + header->alloc_size - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
    for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
        if (where[i] != ZPATH_MALLOC_OVERWRITE_MAGIC) {
            abort();
        }
        where[i] = NULL;
    }
#endif
#ifdef ZPATH_MALLOC_MAGIC
    if (header->magic != header) {
        abort();
    }
    header->magic = NULL;
#endif

    orig_bytes = header->alloc_size;
#ifdef ADDRESS_SANITIZER
    if (orig_bytes == 0)
        ASAN_UNPOISON_MEMORY_REGION(header + 1, LSAN_ZERO_BYTE_ALLOC_SIZE);
#endif
#ifdef ZPATH_MALLOC_DEBUG
#ifdef ZPATH_MALLOC_DEBUG_PER_LINE_STATS
    int orig_line = header->alloc_line;
    const char *orig_file = header->alloc_file;
#endif
#endif // ZPATH_MALLOC_DEBUG;

    size_t alloc_bytes = sizeof(struct zpath_allocation_header) + new_bytes;
#ifdef ADDRESS_SANITIZER
    if (new_bytes == 0)
        alloc_bytes += LSAN_ZERO_BYTE_ALLOC_SIZE;
#endif
    INLINE_VOID_INFERENCE(header, realloc(header, alloc_bytes));
    if (!header) {
        abort();
    }
    header->magic_pat = ZPATH_MISC_DEAD_BEEF_32;
    header->alloc_size = new_bytes;
    header->alloc_pages = 0;
#ifdef __ZPATH_MALLOC_DEBUG
    header->alloc_line = line;
    header->alloc_file = file;
#endif // ZPATH_MALLOC_DEBUG;

#ifdef ZPATH_MALLOC_OVERWRITE
    where = (void **)(((char *)(&header[1])) + new_bytes - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
    for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
        where[i] = ZPATH_MALLOC_OVERWRITE_MAGIC;
    }
    LINK(header);
#endif
#ifdef ZPATH_MALLOC_MAGIC
    header->magic = header;
#endif
    __sync_add_and_fetch_8(&(header->allocator->stats.allocations), 1);
    __sync_add_and_fetch_8(&(header->allocator->stats.allocation_bytes), new_bytes);
    __sync_add_and_fetch_8(&(header->allocator->stats.frees), 1);
    __sync_add_and_fetch_8(&(header->allocator->stats.free_bytes), orig_bytes);
#ifdef ZPATH_MALLOC_DEBUG
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(header->allocator, allocation_bytes, allocations, line, file, new_bytes, 1);
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(header->allocator, free_bytes, frees, orig_line, orig_file, orig_bytes, 1);
#endif

    ASAN_POISON_MEMORY_REGION(header, sizeof(*header));
#ifdef ADDRESS_SANITIZER
    if (new_bytes == 0)
        ASAN_POISON_MEMORY_REGION(header + 1, LSAN_ZERO_BYTE_ALLOC_SIZE);
#endif
    return &(header[1]);
}

static __inline__ void zpath_free(void *data, int line, const char *file)
{
    struct zpath_allocation_header *header;
    if (!data) return;
    INLINE_VOID_INFERENCE(header, data);
    header--;
    ASAN_UNPOISON_MEMORY_REGION(header, sizeof(*header));
    assert(header->magic_pat == ZPATH_MISC_DEAD_BEEF_32);
    __sync_add_and_fetch_8(&(header->allocator->stats.frees), 1);
    __sync_add_and_fetch_8(&(header->allocator->stats.free_bytes), header->alloc_size);
#ifdef ZPATH_MALLOC_DEBUG
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(header->allocator, free_bytes, frees, header->alloc_line, header->alloc_file, header->alloc_size, 1);
#endif // ZPATH_MALLOC_DEBUG
#ifdef ZPATH_MALLOC_OVERWRITE
    UNLINK(header);
    void **where = (void **)(((char *)(&header[1])) + header->alloc_size - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
    for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
        if (where[i] != ZPATH_MALLOC_OVERWRITE_MAGIC) {
            abort();
        }
        where[i] = NULL;
    }
#endif // ZPATH_MALLOC_OVERWRITE
#ifdef ZPATH_MALLOC_MAGIC
    if (header->magic != header) {
        abort();
    }
    header->magic = NULL;
#endif // ZPATH_MALLOC_MAGIC
#ifdef ZPATH_MALLOC_DF_DEBUG
    if (header->freed) {
        /* WHOOPS */
        abort();
    } else {
        header->freed = 1;
    }
#ifdef ZPATH_MALLOC_DEBUG
    header->free_line = line;
    header->free_file = file;
#else
    (void)file;
    (void)line;
#endif // ZPATH_MALLOC_DEBUG
#else  /* !ZPATH_MALLOC_DF_DEBUG follows */
    (void)file;
    (void)line;
    free(header);
#endif // ZPATH_MALLOC_DF_DEBUG
}

/*
 * Free the specified object slowly- only after at least some # of
 * seconds have passed.
 */
static __inline__ void zpath_free_slow(void *data, int line, const char *file)
{
    if (!data) return;
    struct zpath_allocation_header *header;
    INLINE_VOID_INFERENCE(header, data);
    struct zpath_allocator *allocator;
    header--;
    ASAN_UNPOISON_MEMORY_REGION(header, sizeof(*header));
    allocator = header->allocator;

#ifdef ZPATH_MALLOC_OVERWRITE
    void **where = (void **)(((char *)(&header[1])) + header->alloc_size - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
    for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
        if (where[i] != ZPATH_MALLOC_OVERWRITE_MAGIC) {
            abort();
        }
    }
#endif
#ifdef ZPATH_MALLOC_MAGIC
    if (header->magic != header) {
        abort();
    }
#endif
#ifdef ZPATH_MALLOC_DF_DEBUG
    if (header->freed) {
        /* WHOOPS */
        abort();
    } else {
        header->freed = 1;
    }
    __sync_add_and_fetch_8(&(allocator->stats.drain_queue), 1);
    __sync_add_and_fetch_8(&(allocator->stats.drain_queue_bytes), header->alloc_size);
#ifdef ZPATH_MALLOC_DEBUG
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(allocator, drain_queue_bytes, drain_queue, header->alloc_line, header->alloc_file, header->alloc_size, 1);
#endif
#ifdef ZPATH_MALLOC_DEBUG
    header->free_line = line;
    header->free_file = file;
#else
    (void)file;
    (void)line;
#endif // ZPATH_MALLOC_DEBUG

#else /* !ZPATH_MALLOC_DF_DEBUG follows */

#ifdef ZPATH_MALLOC_DEBUG
    header->free_line = line;
    header->free_file = file;
#else
    (void)file;
    (void)line;
#endif // ZPATH_MALLOC_DEBUG
    __sync_add_and_fetch_8(&(allocator->stats.drain_queue), 1);
    __sync_add_and_fetch_8(&(allocator->stats.drain_queue_bytes), header->alloc_size);
#ifdef ZPATH_MALLOC_DEBUG
    ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(allocator, drain_queue_bytes, drain_queue, header->alloc_line, header->alloc_file, header->alloc_size, 1);
#endif
    header->next = NULL;
    header->free_epoch_s = epoch_s();
    pthread_mutex_lock(&(allocator->lock));
    if (allocator->last) {
        allocator->last->next = header;
        allocator->last = header;
    } else {
        allocator->first = header;
        allocator->last = header;
    }
    pthread_mutex_unlock(&(allocator->lock));
#endif
}

static __inline__ int zpath_malloc_check(struct zpath_allocator *allocator)
{
#ifdef ZPATH_MALLOC_OVERWRITE_FULL
    int count = 0;
    struct zpath_allocation_header *hdr;

    pthread_mutex_lock(&(allocator->lock));
    for (hdr = allocator->allocated; hdr; hdr = hdr->o_next) {
        void **where = (void **)(((char *)(&hdr[1])) + hdr->alloc_size - (sizeof(void *) * ZPATH_MALLOC_OVERWRITE_WORDS));
        for (int i = 0; i < ZPATH_MALLOC_OVERWRITE_WORDS; i++) {
            if (where[i] != ZPATH_MALLOC_OVERWRITE_MAGIC) {
                abort();
            }
        }
        count++;
    }
    pthread_mutex_unlock(&(allocator->lock));
    return count;
#else
    (void)allocator;
    return 0;
#endif // ZPATH_MALLOC_OVERWRITE_FULL
}

/*
 * The drain function must be called periodically to drain the free queue.
 * WARNING: This function must be called from single thread only as we are
 * iterating on debug thread queues without locks.
 */
static __inline__ void zpath_free_drain_timed(struct zpath_allocator *allocator, int min_age_s, int line, const char *file, int64_t timeout_us)
{
    int64_t start_us = epoch_us();
    struct zpath_allocation_header *header;
    void *freeme = NULL;

    int iterations = 0;

    /* Move the items to be freed to debug thread queue so that
     * iterations can happen without lock
     */
    pthread_mutex_lock(&(allocator->lock));
    if (allocator->first != NULL) {
        if (allocator->debug_thr_last) {
            allocator->debug_thr_last->next = allocator->first;
            allocator->debug_thr_last = allocator->last;
        } else {
            allocator->debug_thr_first = allocator->first;
            allocator->debug_thr_last = allocator->last;
        }
        allocator->first = allocator->last = NULL;
    }
    pthread_mutex_unlock(&(allocator->lock));

    /*
     *If taking allocator lock itself consumed more time,
     * Just bail out. Free will happen in the next iteration
     */
    if (epoch_us() >= (start_us + timeout_us)) {
        return;
    }

    header = allocator->debug_thr_first;
    while (header != NULL && (!min_age_s || (header->free_epoch_s + min_age_s) < (start_us/US_PER_SEC))) {
        __sync_add_and_fetch_8(&(allocator->stats.drain_queue), -1);
        __sync_add_and_fetch_8(&(allocator->stats.drain_queue_bytes), 0LL-header->alloc_size);
#ifdef ZPATH_MALLOC_DEBUG
        ZPATH_MALLOC_DEBUG_INCREMENT_PER_LINE(header->allocator, drain_queue_bytes, drain_queue, header->alloc_line, header->alloc_file, 0LL - header->alloc_size, -1);
#endif
        if (allocator->debug_thr_last == header) {
            allocator->debug_thr_last = NULL;
        }
        allocator->debug_thr_first = header->next;
        freeme = &(header[1]);
        if (freeme) zpath_free(freeme, line, file);
        if (timeout_us > 0) {
            /* check every 100 iterations to avoid querying clock every single time */
            iterations++;
            if ((iterations % 100 == 0) && (epoch_us() >= (start_us + timeout_us))) {
                /* we ran for too long, stop and let the next timer pick up the slacks */
                break;
            }
        }
        header = allocator->debug_thr_first;
    }
}

static __inline__ void zpath_free_drain(struct zpath_allocator *allocator, int min_age_s, int line, const char *file)
{
    zpath_free_drain_timed(allocator, min_age_s, line, file, 0L);
}

static __inline__ void *zpath_calloc(struct zpath_allocator *allocator, size_t bytes, int line, const char *file)
{
    void *result = zpath_malloc(allocator, bytes, line, file);
    memset(result, 0, bytes);
    return result;
}

static __inline__ char *zpath_strdup(struct zpath_allocator *allocator, const char *string, size_t string_length,
        int line, const char* file)
{
    char *result;
    INLINE_VOID_INFERENCE(result, zpath_malloc(allocator, string_length + 1, line, file));
    memcpy(result, string, string_length);
    result[string_length] = 0;
    return result;
}

static __inline__ int64_t zpath_allocator_bytes_outstanding(struct zpath_allocator *allocator)
{
    int64_t res;
    res = __sync_add_and_fetch_8(&(allocator->stats.allocation_bytes), 0) -
        (__sync_add_and_fetch_8(&(allocator->stats.free_bytes), 0) +
        __sync_add_and_fetch_8(&(allocator->stats.drain_queue_bytes), 0));
    return res;
}

/***************************************************
 * String printing functions
 */


/*
 * sort of like snprintf, but returns the number of characters
 * printed, NOT including the NULL termination. (But makes sure
 * termination happens, if possible)
 *
 * rather than length, the second argument of this routine is the
 * character after the end of the buffer. (str_end - str == buffer
 * length). This is done so that multiple follow-on calls to sxprintf
 * do not have to update a length reference.  This routine attempts to
 * always leave the buffer null-terminated.  i.e. common use:
 *
 * char buf[30];
 * char *s = buf;
 * char *e = s + sizeof(buf);
 *
 * s += sxprintf(s, e, "First print");
 * s += sxprintf(s, e, "Second print");
 * s += sxprintf(s, e, "etc");
 */
static inline int sxprintf(char *str, char *str_end, const char *format, ...)
    __attribute__((format(printf, 3, 4)));
static inline int sxprintf(char *str, char *str_end, const char *format, ...) {
	int result;
	int len = str_end - str;
	va_list list;

	va_start(list, format);
	result = vsnprintf(str, len, format, list);
    va_end(list);
	if (result >= len)
		return (len > 0) ? len - 1 : 0;
	return result;
}

/*
 * Exactly like sxprintf, above, but takes a length for second argument.
 *
 * This is useful instead of snprinf because it returns the length of
 * the resulting string, not the length of the string that could have
 * been printed if there were enough space...
 */
static inline int szprintf(char *str, int len, const char *format, ...)
    __attribute__((format(printf, 3, 4)));
static inline int szprintf(char *str, int len, const char *format, ...) {
	int result;
	va_list list;

	va_start(list, format);
	result = vsnprintf(str, len, format, list);
    va_end(list);
	if (result >= len)
		return (len > 0) ? len - 1 : 0;
	return result;
}

/**************************************************
 * Interlocking- i.e. wait for some other thread to do something.
 *
 * Common use:
 *
 ***********
 * Thread 1:
 *
 * struct zpath_interlock foo;
 *
 * zpath_interlock_init(&foo)
 * some_async_request_to_another_thread(cookie = &foo)
 * zpath_interlock_wait(&foo)
 * At this point, we have guaranteed that the other thread has
 * completed its task.
 *
 ***********
 * Thread 2:
 *
 * do_async_request_from_above.
 * zpath_interlock_signal(foo)
 */
struct zpath_interlock {
    pthread_mutex_t lock;
    pthread_cond_t cond;
};

/* Init an interlock... required before any other use */
static inline void zpath_interlock_init(struct zpath_interlock *i)
{
    i->cond = (pthread_cond_t)PTHREAD_COND_INITIALIZER;
    i->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    pthread_mutex_lock(&(i->lock));
}

/* Wait for the signal. Signal can happen before or after this call-
 * it doesn't matter. If the signal happens 'later', this call will
 * block until the signal happens. */
static inline void zpath_interlock_wait(struct zpath_interlock *i)
{
    pthread_cond_wait(&(i->cond), &(i->lock));
}

/* Signal that an interlock con proceed... */
static inline void zpath_interlock_signal(struct zpath_interlock *i)
{
    pthread_mutex_lock(&(i->lock));
    pthread_cond_signal(&(i->cond));
    pthread_mutex_unlock(&(i->lock));
}

/* unlocks the mutex taken during init */
static inline void zpath_interlock_unlock(struct zpath_interlock *i)
{
    pthread_mutex_unlock(&(i->lock));
}


/***************************************************
 * Locking + lock debugging.
 */

//#define ZPATH_LOCK_DEBUG
//#define ZPATH_DISABLE_RW_LOCKS

#ifdef ZPATH_LOCK_DEBUG
struct lock_state {
    int16_t action;
    int16_t count;
    pthread_t tid;
    int line;
    const char *file;
};
#endif

struct zpath_rwlock {
#ifdef ZPATH_DISABLE_RW_LOCKS
    pthread_mutex_t lock;
#else
    pthread_rwlock_t lock;
#endif
#ifdef ZPATH_LOCK_DEBUG
    int32_t total;
    uint8_t index;
    int16_t count;
    struct lock_state state[256];
#endif
    int64_t lock_acquired_us;
};

struct zpath_mutex {
    pthread_mutex_t lock;
    int64_t lock_acquired_us;
#ifdef ZPATH_LOCK_DEBUG
    int32_t total;
    uint8_t index;
    int16_t count;
    struct lock_state state[256];
#endif
};

typedef struct zpath_rwlock zpath_rwlock_t;
typedef struct zpath_mutex zpath_mutex_t;

struct zpath_buffer_pool_stats {
  uint64_t buffer_size_low_range;
  uint64_t total_buffers;
  uint64_t allocations;
  uint64_t frees;
  uint64_t min_buffers;
};

// keep in sync with struct zpath_allocation_header
// ensure zpath_buffer_entry is smaller than struct zpath_allocation_header
struct zpath_buffer_entry {
  uint32_t magic_pat;
  uint32_t alloc_pages; // should be no padding b4 'next'
  TAILQ_ENTRY(zpath_buffer_entry) entry;  // 16 bytes
  uint32_t buffer_pool_allocated:1; // only relevant when alloc_pages is used
};

union zpath_buffer_entry_union {
  struct zpath_buffer_entry      zpath_buffer_entry_smaller;
  struct zpath_allocation_header zpath_buffer_entry_larger;
};

TAILQ_HEAD(zpath_tailq, zpath_buffer_entry);
struct zpath_buffer_free_queue {
    zpath_mutex_t                   lock; // protects free_queue access
    struct zpath_tailq              list;
    struct zpath_buffer_pool_stats  buffer_free_queue_stats;
};

#ifdef ZPATH_LOCK_DEBUG

#define LOCK_STAMP(lock, d, f, l)                               \
    do {                                                        \
        uint8_t ix;                                             \
        int16_t count;                                          \
        ix = __sync_add_and_fetch_1(&((lock)->index), 1);       \
        count = __sync_add_and_fetch_2(&((lock)->count), d);    \
        __sync_add_and_fetch_4(&((lock)->total), 1);            \
        (lock)->state[ix].action = d;                           \
        (lock)->state[ix].count = count;                        \
        (lock)->state[ix].tid = pthread_self();                 \
        (lock)->state[ix].line = l;                             \
        (lock)->state[ix].file = f;                             \
    } while (0);

#ifdef ZPATH_DISABLE_RW_LOCKS
#define ZPATH_RWLOCK_INIT (zpath_rwlock_t){         \
        (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER, \
            0,                                      \
            0,                                      \
            0}
#else
#define ZPATH_RWLOCK_INIT (zpath_rwlock_t){             \
        (pthread_rwlock_t)PTHREAD_RWLOCK_INITIALIZER,   \
            0,                                          \
            0,                                          \
            0}
#endif

#define ZPATH_MUTEX_INIT (zpath_mutex_t){               \
        (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER,     \
            0,                                          \
            0,                                          \
            0}

#define ZPATH_MUTEX_LOCK(l, file, line)         \
    do {                                        \
        pthread_mutex_lock(&((l)->lock));       \
        LOCK_STAMP((l), (256), (file), (line)); \
    } while (0)

#define ZPATH_MUTEX_UNLOCK(l, file, line)       \
    do {                                        \
        if ((l)->count & 0xff00) {              \
            LOCK_STAMP((l), -256, file, line);  \
        } else {                                \
            LOCK_STAMP((l), -1, file, line);    \
        }                                       \
        pthread_mutex_unlock(&((l)->lock));     \
    } while (0)

#ifdef ZPATH_DISABLE_RW_LOCKS

#define ZPATH_RWLOCK_RDLOCK(l, file, line)      \
    ZPATH_MUTEX_LOCK(l, file, line)
#define ZPATH_RWLOCK_WRLOCK(l, file, line)      \
    ZPATH_MUTEX_LOCK(l, file, line)
#define ZPATH_RWLOCK_UNLOCK(l, file, line)      \
    ZPATH_MUTEX_UNLOCK(l, file, line)

#else /* !ZPATH_DISABLE_RW_LOCKS follows */

#define ZPATH_RWLOCK_RDLOCK(l, file, line)      \
    do {                                        \
        pthread_rwlock_rdlock(&((l)->lock));    \
        LOCK_STAMP((l), 1, file, line);         \
    } while (0)
#define ZPATH_RWLOCK_WRLOCK(l, file, line)      \
    do {                                        \
        pthread_rwlock_wrlock(&((l)->lock));    \
        LOCK_STAMP((l), 256, file, line);       \
    } while (0)
#define ZPATH_RWLOCK_UNLOCK(l, file, line)      \
    do {                                        \
        if ((l)->count & 0xff00) {              \
            LOCK_STAMP((l), -256, file, line);  \
        } else {                                \
            LOCK_STAMP((l), -1, file, line);    \
        }                                       \
        pthread_rwlock_unlock(&((l)->lock));    \
    } while (0)

#endif /* !ZPATH_DISABLE_RW_LOCKS */

#else /* !ZPATH_LOCK_DEBUG follows */

#define ZPATH_RWLOCK_INIT (zpath_rwlock_t){(pthread_rwlock_t)PTHREAD_RWLOCK_INITIALIZER}
#define ZPATH_MUTEX_INIT (zpath_mutex_t){(pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER}

#define ZPATH_LOCK_LOG_LIMIT 2000000

#define ZPATH_MUTEX_LOCK(l, file, line) do {                                       \
        volatile int64_t lock_acquire_wait_us= semi_monotime_us_compatible();      \
        pthread_mutex_lock(&((l)->lock));                                          \
        (l)->lock_acquired_us = semi_monotime_us_compatible();                     \
        if (((l)->lock_acquired_us - lock_acquire_wait_us) > ZPATH_LOCK_LOG_LIMIT) \
            printf("%s:%d:%p waited for %" PRId64 " us to acquire a mutex\n",      \
            file, line, (l), (l)->lock_acquired_us - lock_acquire_wait_us);        \
    } while (0)
#define ZPATH_MUTEX_UNLOCK(l, file, line)                                      \
    do {                                                                       \
        volatile int64_t lock_released_us = semi_monotime_us_compatible();     \
        if ((lock_released_us - (l)->lock_acquired_us) > ZPATH_LOCK_LOG_LIMIT) \
            printf("%s:%d:%p held mutex for %" PRId64 " us\n",                 \
            file, line, (l), lock_released_us - (l)->lock_acquired_us);        \
        pthread_mutex_unlock(&((l)->lock));                                    \
    } while (0)

#ifdef ZPATH_DISABLE_RW_LOCKS

#define ZPATH_RWLOCK_RDLOCK(l, file, line)      \
    ZPATH_MUTEX_LOCK(l, file, line)
#define ZPATH_RWLOCK_WRLOCK(l, file, line)      \
    ZPATH_MUTEX_LOCK(l, file, line)
#define ZPATH_RWLOCK_UNLOCK(l, file, line)      \
    ZPATH_MUTEX_UNLOCK(l, file, line)

#else /* !ZPATH_DISABLE_RW_LOCKS follows */

#define ZPATH_RWLOCK_RDLOCK(l, file, line) do {                                      \
        volatile int64_t lock_acquire_wait_us = semi_monotime_us_compatible();       \
        pthread_rwlock_rdlock(&((l)->lock));                                         \
        int64_t local_rdlock_acquire_us = semi_monotime_us_compatible();             \
        if ((local_rdlock_acquire_us - lock_acquire_wait_us) > ZPATH_LOCK_LOG_LIMIT) \
            printf("%s:%d:%p waited for %" PRId64 " us to acquire a read lock\n",    \
            file, line, (l), local_rdlock_acquire_us - lock_acquire_wait_us);        \
    } while (0)
#define ZPATH_RWLOCK_WRLOCK(l, file, line) do {                                    \
        volatile int64_t lock_acquire_wait_us= semi_monotime_us_compatible();      \
        pthread_rwlock_wrlock(&((l)->lock));                                       \
        (l)->lock_acquired_us = semi_monotime_us_compatible();                     \
        if (((l)->lock_acquired_us - lock_acquire_wait_us) > ZPATH_LOCK_LOG_LIMIT) \
            printf("%s:%d:%p waited for %" PRId64 " us to acquire a write lock\n", \
            file, line, (l), (l)->lock_acquired_us - lock_acquire_wait_us);        \
    } while (0)
#define ZPATH_RWLOCK_UNLOCK(l, file, line)                                         \
    do {                                                                           \
        if ((l)->lock_acquired_us) {                                               \
            volatile int64_t lock_released_us = semi_monotime_us_compatible();     \
            if ((lock_released_us - (l)->lock_acquired_us) > ZPATH_LOCK_LOG_LIMIT) \
                printf("%s:%d:%p write lock held lock for %" PRId64 " us\n",       \
                file, line, (l), lock_released_us - (l)->lock_acquired_us);        \
            (l)->lock_acquired_us = 0;                                             \
        }                                                                          \
        pthread_rwlock_unlock(&((l)->lock));                                       \
    } while (0)

#endif /* !ZPATH_DISABLE_RW_LOCKS */

#endif /* !ZPATH_LOCK_DEBUG */

#define ZPATH_MUTEX_DESTROY(l, file, line) do { \
        pthread_mutex_destroy(&((l)->lock));    \
    } while(0)

// Define the macro for destroying the "ZPATH_RWLOCK"
#ifdef ZPATH_DISABLE_RW_LOCKS
#define ZPATH_RWLOCK_DESTROY(lock_ptr) \
    pthread_mutex_destroy(&(lock_ptr)->lock)
#else
#define ZPATH_RWLOCK_DESTROY(lock_ptr) \
    pthread_rwlock_destroy(&(lock_ptr)->lock)
#endif

/***************************************************************
 * To indicate an intentional fall-through between switch labels.
 * This tops compiler to issue warnings when compilation unit is
 * enabled with -Wimplicit-fallthrough flag.
 * Fall-through without this annotation will be caught.
 */
#if defined __has_attribute
    #if __has_attribute (__fallthrough__)
        #define ZPATH_FALLTHROUGH      __attribute__ ((__fallthrough__))
    #else
        #define ZPATH_FALLTHROUGH      do {} while (0)
    #endif
#else
    #define ZPATH_FALLTHROUGH          do {} while (0)
#endif

// Macro replacements to explicitly ignore compiler warnings due to not checking return result.
#ifdef __GNUC__
#define snprintf_nowarn(...) (snprintf(__VA_ARGS__) < 0 ? abort() : (void)0)
#else
#define snprintf_nowarn(...) snprintf(__VA_ARGS__)
#endif

#define fread_nowarn(...) (void)! fread(__VA_ARGS__)
#define fscanf_nowarn(...) (void)! fscanf(__VA_ARGS__)
#define read_nowarn(...) (void)! read(__VA_ARGS__)
#define write_nowarn(...) (void)! write(__VA_ARGS__)
#define symlink_nowarn(...) (void)! symlink(__VA_ARGS__)
#define system_nowarn(...) (void)! system(__VA_ARGS__)
#define getcwd_nowarn(...) (void)! getcwd(__VA_ARGS__)

void *zpath_malloc_aligned(size_t sz, size_t *allocated, size_t *total_mem_out);
void zpath_free_aligned(void *data, size_t *total_mem_out);
void zpath_dont_dump_enable_command_line();
void zpath_dont_dump_enable_config_override(uint64_t enabled);
uint64_t zpath_dont_dump_get();
void zpath_create_buffer_pools();
void zpath_buffer_pool_stats_dump(char *out_str, size_t out_str_len);

#endif /* __ZPATH_MISC_H__ */
