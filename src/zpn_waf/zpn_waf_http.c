/*
 * zpn_waf_http.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 */

#include "zpn_waf/zpn_waf_http.h"
#include "zpn_waf/zpn_waf_lib.h"
#include "zpn_inspection/zpn_transform.h"
#include "zpn_waf/zpn_waf_profile.h"
#include "zpn_waf/zpn_waf_http_parser.h"
#include "zpn_waf/zpn_waf_http_parser_state.h"
#include "zpn_waf/zpn_waf_http_exchange.h"
#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_predefined_rules.h"
#include "zpn_waf/zpn_waf_config_override.h"
#include "zpn_waf/zpn_waf_websocket.h"
#include "zpn_waf/zpn_inspection_profile.h"
#include "fohh/http_parser.h"

// These variables are defined here. But can change through
// config override. Take a look at zpn_waf_config_override.c
//

static int zpn_handle_pending_response(struct evbuffer** forward,
                                       struct evbuffer* input,
                                       struct zpn_http_modsec_parser* modsec_parser)
{
    struct zpn_http_exchange* exchange = zpn_get_current_exchange_response(modsec_parser->exq);
    if (!exchange) {
        WAF_DEBUG_HTTP("Error, could not find matching exchange for response");
        return INSP_TRANSFORM_ERROR;
    }
    if (!exchange || !exchange->synthetic_response) {
        // We expect to send the reply coming from the application.
        // We do not have a pending response to be sent back.
        //
        return INSP_TRANSFORM_REPLY;
    }
    WAF_DEBUG_HTTP("[%.*s] : reply synthetic, exchange = %p, %d",
            MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str, exchange, exchange->synthetic_response);
    // Taking the data from the input buffer.
    // This data is coming from the inspection pipeline.
    // It was created by us to respond to a previous exchange
    // that had some issue.
    // The response comes back like a message in the response pipeline,
    // almost as if it is a response from the application itself.
    //
    size_t response_len = evbuffer_get_length(input);
    exchange->waf_log->total_bytes_proc += response_len;
    exchange->waf_log->rsp_hdr_size += response_len;
    exchange->waf_log->rsp_bdy_size = 0;
    exchange->waf_log->rsp_rx_start_us = epoch_us();
    *forward = evbuffer_new();
    evbuffer_add_buffer(*forward, input);
    exchange->pending_response = NULL;
    modsec_parser->error_response_pending = 0;
    zpn_reset_http_response_parser((struct zpn_http_parser*)modsec_parser);

    struct zpn_http_exchange* active_exchanges[MAX_EXCHGS] = {0};
    int num_responses = zpn_get_all_active_exchanges(modsec_parser->exq, &(active_exchanges[0]), MAX_EXCHGS);
    int ii = 0;
    for (ii = 0; ii < num_responses; ii++) {
        zpn_log_http_exchange(active_exchanges[ii]);
    }
    zpn_exchange_complete(modsec_parser->exq);
    return INSP_TRANSFORM_FWD;
}

#define HTTP_RESPONSE_403_LEN 143
static char HTTP_RESPONSE_403[] = "HTTP/1.1 403 FORBIDDEN\r\n" \
                                  "Server: ZPA\r\n"              \
                                  "Content-Length: 58\r\n"        \
                                  "Content-Type: text/html;\r\n\r\n" \
                                  "<html><body><h3>Result = 403 FORBIDDEN</h3></body></html>\n";

uint64_t zpn_get_waf_intervention_errors(struct zpn_http_exchange* exchange, uint64_t result)
{
    if (!exchange) {
        return result;
    }
    if (exchange->intervention_status != zpn_no_intervention) {
        result |= ZPN_WAF_RESULT_ERROR;
        if (exchange->intervention_status == zpn_modsec_redirect) {
            result |= ZPN_WAF_RESULT_MODSEC_REDIRECT | ZPN_WAF_RESULT_MODSEC_ERROR;
        } else if (exchange->intervention_status == zpn_modsec_block) {
            result |= ZPN_WAF_RESULT_MODSEC_BLOCK | ZPN_WAF_RESULT_MODSEC_ERROR;
        } else if (exchange->intervention_status == zpn_websock_block) {
            result |= ZPN_WAF_RESULT_WEBSOCK_BLOCK | ZPN_WAF_RESULT_WEBSOCK_ERROR;
        }
    }
    return result;
}

uint64_t zpn_process_http_response(struct zpn_http_modsec_parser* modsec_parser,
                                   http_parser *parser,
                                   const http_parser_settings *settings,
                                   const char* buf, size_t bytes,
                                   size_t* bytes_processed_now)
{

    struct zpn_http_parser_state* resp_parser_state = modsec_parser->resp_parser_state;
    uint64_t result = 0;
    *bytes_processed_now = http_parser_execute(parser,
                                               settings,
                                               buf,
                                               bytes);

    if (*bytes_processed_now != bytes) {
        result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_BYTES_UNPROCESSED;
    }
    if (resp_parser_state->parser.http_errno) {
        result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_HTTP_ERROR;
    }

    struct zpn_http_exchange* exchange = zpn_get_current_exchange_response(modsec_parser->exq);
    if (resp_parser_state->parser.upgrade) {
        if (exchange->websocket_upgrade) {
            result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_WEBSOCK_UPGRADE | ZPN_WAF_RESULT_HTTP_UPGRADE;
        } else
            result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_HTTP_UPGRADE;
    }

    exchange->response_status = resp_parser_state->parser.status_code;
    result = zpn_get_waf_intervention_errors(exchange, result);

    return (result & ZPN_WAF_RESULT_ERROR) ? result : ZPN_WAF_RESULT_SUCCESS;
}

uint64_t zpn_process_http_request(struct zpn_http_modsec_parser* modsec_parser,
                                  http_parser *parser,
                                  const http_parser_settings *settings,
                                  const char* buf, size_t bytes,
                                  size_t* bytes_processed_now)
{
    struct zpn_http_parser_state* req_parser_state = modsec_parser->req_parser_state;
    uint64_t result = 0;
    *bytes_processed_now = http_parser_execute(parser,
                                               settings,
                                               buf,
                                               bytes);
    if (*bytes_processed_now != bytes) {
        result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_BYTES_UNPROCESSED;
    }
    if (req_parser_state->parser.http_errno) {
        result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_HTTP_ERROR;
    }

    struct zpn_http_exchange* exchange = zpn_get_current_exchange_request(modsec_parser->exq);
    if (req_parser_state->parser.upgrade) {
        if (exchange->websocket_upgrade)
            result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_WEBSOCK_UPGRADE | ZPN_WAF_RESULT_HTTP_UPGRADE;
        else
            result |= ZPN_WAF_RESULT_ERROR | ZPN_WAF_RESULT_HTTP_UPGRADE;
    }
    result = zpn_get_waf_intervention_errors(exchange, result);
    return (result & ZPN_WAF_RESULT_ERROR) ? result : ZPN_WAF_RESULT_SUCCESS;
}

static void zpn_get_reason_payload_for_parser_error(char* buf, size_t bytes, char* reason)
{
    size_t reason_bytes = reason ? strlen(reason) : 0;
    char* s = buf;
    char *e = buf + bytes;
    s += sxprintf(s, e, "HTTP/1.1 403 FORBIDDEN\r\nServer: ZPA\r\nContent-Length: %ld\r\nContent-Type: text;\r\n\r\n%s", reason_bytes, reason);
}

static int zpn_waf_process_response_error(uint64_t result,
                                          struct zpn_http_exchange* exchange,
                                          struct zpn_http_modsec_parser* modsec_parser,
                                          size_t bytes_received,
                                          size_t bytes_processed_now,
                                          struct evbuffer** forward)
{
    struct zpn_http_parser_state* resp_parser_state = modsec_parser->resp_parser_state;
    *forward = evbuffer_new();
    if (!(result & ZPN_WAF_RESULT_MODSEC_ERROR) && !(result & ZPN_WAF_RESULT_WEBSOCK_ERROR)) {
        // Not a modsec error. The parser has found an issue.
        //
        // This is a catch-all for any non modsec errors.
        // ZPN_WAF_RESULT_HTTP_ERROR
        // ZPN_WAF_RESULT_BYTES_UNPROCESSED
        //
        int index = (exchange->num_violations >= MAX_VIOLATIONS_TO_TRACK) ?
                    MAX_VIOLATIONS_TO_TRACK-1 :
                    exchange->num_violations;
        char* catchall_log = zpn_get_catchall_log(modsec_parser->profile->gid);
        char* catchall_log_cpy = "";
        if (catchall_log) {
            catchall_log_cpy = WAF_STRDUP(catchall_log, strlen(catchall_log));
        } else {
            catchall_log_cpy = (char *)WAF_CALLOC(1024);
            zpn_get_catchall_log_without_rule(catchall_log_cpy, strlen(catchall_log_cpy));
        }
        WAF_DEBUG_HTTP_PARSER("[%.*s] WAF Intervention required. Response status = 403, pause = 0, "
                              "disruptive = 1, profile = %ld, log : %s",
                              MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                              (long)(modsec_parser->profile->gid), catchall_log_cpy);
        zpn_waf_get_log_fields(catchall_log_cpy, &(exchange->log_fields[index]), waf_inspection_actions[zpn_modsec_block]);
        WAF_FREE(catchall_log_cpy);
        ++exchange->num_violations;
        zpn_update_waf_log_response(exchange, resp_parser_state);
        if (g_include_reason_payload || g_zpath_config_include_reason_payload) {
            char* reason = zpn_get_catchall_log(modsec_parser->profile->gid);
            char http_response[1024] = {0};
            zpn_get_reason_payload_for_parser_error(http_response, sizeof(http_response), reason);
            evbuffer_add(*forward,
                         http_response,
                         strlen(http_response));
        } else {
            evbuffer_add(*forward,
                         HTTP_RESPONSE_403,
                         HTTP_RESPONSE_403_LEN);
        }
    } else {
        if ((result & ZPN_WAF_RESULT_MODSEC_REDIRECT) && exchange->redirect_response) {
            evbuffer_add(*forward,
                         exchange->redirect_response,
                         strlen(exchange->redirect_response));
            WAF_FREE(exchange->redirect_response);
            exchange->redirect_response = NULL;
        } else if (((result & ZPN_WAF_RESULT_MODSEC_BLOCK) || (result & ZPN_WAF_RESULT_WEBSOCK_BLOCK)) && exchange->bad_response) {
            evbuffer_add(*forward,
                         exchange->bad_response,
                         strlen(exchange->bad_response));
            WAF_FREE(exchange->bad_response);
            exchange->bad_response = NULL;
        } else {
            // This should not happen..
            // its a modsecurity error without a redirect/block
            //
            evbuffer_add(*forward, HTTP_RESPONSE_403, HTTP_RESPONSE_403_LEN);
        }
    }

    modsec_parser->error_response_pending = 1;
    if (modsec_parser->populate_log_fields) {
        modsec_parser->populate_log_fields(modsec_parser->log_cookie, exchange->waf_log);
    }
    zpn_log_http_exchange(exchange);
    zpn_reset_http_response_parser((struct zpn_http_parser*)modsec_parser);
    return INSP_TRANSFORM_FWD;
}

uint8_t zpn_waf_get_http_parser_error(struct http_parser* parser)
{
    int error_no = HTTP_PARSER_ERRNO(parser);
    switch (error_no) {
        case HPE_INVALID_METHOD:
            return ZPN_WAF_HTTP_INVALID_METHOD;
        case HPE_INVALID_CONTENT_LENGTH:
            return ZPN_WAF_HTTP_INVALID_CONTENT_LEN;
        case HPE_UNEXPECTED_CONTENT_LENGTH:
            return ZPN_WAF_HTTP_INVALID_CHUNKING;
        case HPE_INVALID_VERSION:
            return ZPN_WAF_HTTP_INVALID_VERSION;
        case HPE_OK:
            return ZPN_WAF_HTTP_VALID;
        default:
            return ZPN_WAF_HTTP_INVALID_REQUEST;
    }

    return ZPN_WAF_HTTP_VALID;
}

static int zpn_waf_process_request_configurable_preprocessor(uint64_t* result,
                                                             struct zpn_http_modsec_parser* modsec_parser,
                                                             struct zpn_http_parser_state* req_parser_state)
{
    // If non-complaint http traffic and preprocessor rule is configurable
    uint8_t http_parser_error = zpn_waf_get_http_parser_error(&(req_parser_state->parser));
    WAF_DEBUG_HTTP("Http parser returned with HTTP error %s",zpn_waf_http_parser_status_str(http_parser_error));
    int control_id = zpn_waf_http_parser_status_control(http_parser_error);
    char * http_parser_error_log = (char *)WAF_CALLOC(1024);
    // Corresponding preprocessor rule is allow/unselected, block/redirect:1, allow/unselected:0
    int preprocessor_enabled = zpn_get_preprocessor_enable_status(modsec_parser->profile, control_id);
    struct zpn_waf_profile* profile = modsec_parser->profile;

    int64_t control_gid = 0;
    zpn_get_predefined_rule_control_gid(profile->gid, control_id, &control_gid, profile->incarnation);
    int preprocessor_allow = (preprocessor_enabled==0 && control_gid!=0);
    int preprocessor_unselected = (preprocessor_enabled==0 && control_gid==0);
    zpn_get_preprocessor_violation_log(http_parser_error_log, 1024, control_id, control_gid,
                                        zpn_waf_http_parser_status_str(http_parser_error), preprocessor_allow, preprocessor_unselected);
    /* Free if there was earlier allocation for this */
    if (modsec_parser->preprocessor_violation) {
        WAF_DEBUG_HTTP("Preprocessor violation %p reused, old_log: %s", (const char *)(modsec_parser->preprocessor_violation),
                modsec_parser->preprocessor_violation);
        WAF_FREE(modsec_parser->preprocessor_violation);
        modsec_parser->preprocessor_violation = NULL;
    }
    modsec_parser->preprocessor_violation = http_parser_error_log;

    if (preprocessor_allow || preprocessor_unselected) {
        struct zpn_http_exchange* exchange = zpn_get_current_exchange_request(modsec_parser->exq);
        WAF_DEBUG_HTTP("Preprocessor rule: Forwarding non-compliant http request, no inspection performed, log : %s", modsec_parser->preprocessor_violation);
        if (!exchange) {
            RulesSet* rs = modsec_parser->profile->rs;
            WAF_DEBUG_HTTP("Create exchange when forward non http request..");
            exchange = zpn_create_new_exchange(modsec_parser->exq,
                                               modsec_parser->profile->msc,
                                               rs,
                                               NULL,
                                               modsec_parser->mdbg_str);
            if (!exchange) {
                WAF_DEBUG_HTTP("Failed to start a exchange when forward non http request");
                return WAF_RESULT_ERR;
            }
        }

        int index = (exchange->num_violations >= MAX_VIOLATIONS_TO_TRACK) ?
                     MAX_VIOLATIONS_TO_TRACK-1 :
                     exchange->num_violations;

        if (preprocessor_allow) {
            zpn_waf_get_log_fields(http_parser_error_log, &(exchange->log_fields[index]), waf_inspection_actions[zpn_modsec_allow]);
            ++exchange->num_violations;
        }

        struct zpn_http_exchange* active_exchanges[MAX_EXCHGS] = {0};
        int num_responses = zpn_get_all_active_exchanges(modsec_parser->exq, active_exchanges, MAX_EXCHGS);
        int ii = 0;
        for (ii = 0; ii < num_responses; ii++) {
            if (!active_exchanges[ii]->waf_log->req_rx_start_us) {
                active_exchanges[ii]->waf_log->req_rx_start_us = epoch_us();
            }
            if (preprocessor_allow) {
                zpn_update_waf_log_request(exchange, modsec_parser->req_parser_state);
            }
        }
        // Rewrite the parser result and forward traffic
        (*result) = ZPN_WAF_RESULT_SUCCESS;
        modsec_parser->req_parser_state->parsing_complete = 1;
    }
    return WAF_RESULT_NO_ERROR;
}

static int zpn_waf_process_request_error(uint64_t result,
                                         struct zpn_http_modsec_parser* modsec_parser,
                                         size_t bytes_received,
                                         size_t bytes_processed_now,
                                         struct evbuffer** response)
{
    struct zpn_http_exchange* exchange = zpn_get_current_exchange_request(modsec_parser->exq);

    if (!exchange) {
        // This can happen if the request start callback did not get executed, if there are
        // issues with the first http request line.
        //
        RulesSet* rs = modsec_parser->profile->rs;
        WAF_DEBUG_HTTP("Create exchange when waf process request error..");
        exchange = zpn_create_new_exchange(modsec_parser->exq,
                                           modsec_parser->profile->msc,
                                           rs,
                                           NULL, // This transation has failed. No allow log required.
                                           modsec_parser->mdbg_str);
        if (!exchange) {
            WAF_DEBUG_HTTP("Failed to start a exchange after processing the request");
            return INSP_TRANSFORM_ERROR;
        }
        if (modsec_parser->populate_log_fields) {
            modsec_parser->populate_log_fields(modsec_parser->log_cookie, exchange->waf_log);
        }
    }

    modsec_parser->error_response_pending = 1;
    *response = evbuffer_new();
    struct zpn_http_exchange* active_exchanges[MAX_EXCHGS] = {0};
    int num_responses = zpn_get_all_active_exchanges(modsec_parser->exq, active_exchanges, MAX_EXCHGS);

    if ((result & ZPN_WAF_RESULT_MODSEC_REDIRECT) && exchange->redirect_response) {
        // strlen of the response buffer is important. so the \0 does not
        // go out with the rest of the response.
        //
        int ii = 0;
        for (ii = 0; ii < num_responses; ii++) {
            active_exchanges[ii]->synthetic_response = 1;
            evbuffer_add(*response,
                          exchange->redirect_response,
                          strlen(exchange->redirect_response));
            active_exchanges[ii]->response_status = 302;
            active_exchanges[ii]->waf_log->response_status = 302;
        }
        WAF_FREE(exchange->redirect_response);
        exchange->redirect_response = NULL;
    } else if (((result & ZPN_WAF_RESULT_MODSEC_BLOCK) || (result & ZPN_WAF_RESULT_WEBSOCK_BLOCK)) && exchange->bad_response) {
        // strlen of the response buffer is important. so the \0 does not
        // go out with the rest of the response.
        //
        int ii = 0;
        for (ii = 0; ii < num_responses; ii++) {
            active_exchanges[ii]->synthetic_response = 1;
            evbuffer_add(*response,
                         exchange->bad_response,
                         strlen(exchange->bad_response));
            active_exchanges[ii]->response_status = 403;
            active_exchanges[ii]->waf_log->response_status = 403;
        }
        WAF_FREE(exchange->bad_response);
        exchange->bad_response = NULL;
    } else {
        // Not a modsec error. The parser has found an issue.
        //
        // This is a catch-all for any non modsec errors.
        // ZPN_WAF_RESULT_HTTP_ERROR
        // ZPN_WAF_RESULT_BYTES_UNPROCESSED
        //
        int index = (exchange->num_violations >= MAX_VIOLATIONS_TO_TRACK) ?
                    MAX_VIOLATIONS_TO_TRACK-1 :
                    exchange->num_violations;

        // ET-75423: overwrite the catchall_log with specific reasons
        char* catchall_log = modsec_parser->preprocessor_violation ? modsec_parser->preprocessor_violation : zpn_get_catchall_log(modsec_parser->profile->gid);
        char* catchall_log_cpy = WAF_STRDUP(catchall_log, strlen(catchall_log));
        WAF_DEBUG_HTTP_PARSER("[%.*s] WAF Intervention required. Response status = 403, pause = 0, "
                              "disruptive = 1, profile = %ld, log : %s",
                              MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                              (long)(modsec_parser->profile->gid), catchall_log_cpy);
        zpn_waf_get_log_fields(catchall_log_cpy, &(exchange->log_fields[index]), waf_inspection_actions[zpn_modsec_block]);
        WAF_FREE(catchall_log_cpy);
        ++exchange->num_violations;
        int ii = 0;
        for (ii = 0; ii < num_responses; ii++) {
            active_exchanges[ii]->synthetic_response = 1;
            if (g_include_reason_payload || g_zpath_config_include_reason_payload) {
                char* reason = zpn_get_catchall_log(modsec_parser->profile->gid);
                char http_response[1024] = {0};
                zpn_get_reason_payload_for_parser_error(http_response, sizeof(http_response), reason);
                evbuffer_add(*response,
                         http_response,
                         strlen(http_response));
            } else {
                evbuffer_add(*response,
                             HTTP_RESPONSE_403,
                             HTTP_RESPONSE_403_LEN);
            }
            if (!active_exchanges[ii]->waf_log->req_rx_start_us) {
                active_exchanges[ii]->waf_log->req_rx_start_us = epoch_us();
            }
            active_exchanges[ii]->response_status = 403;
            active_exchanges[ii]->waf_log->response_status = active_exchanges[ii]->response_status;

             // There may have been successful requests prior to this that are active
             // not yet forwarded. We will send 403 back for them as well.
             //
            // If it is a http parsing error, we did not get to the end of a http
            // request. We can update the request method, error status, etc.
            //
            zpn_update_waf_log_request(exchange,
                                       modsec_parser->req_parser_state);
        }
    }

    zpn_reset_http_request_parser((struct zpn_http_parser*)modsec_parser);
    return INSP_TRANSFORM_REPLY;
}

static int zpn_waf_process_response_success(struct zpn_http_modsec_parser* modsec_parser,
                                            struct zpn_http_exchange* exchange,
                                            size_t bytes_received,
                                            struct evbuffer** forward)
{
    struct zpn_http_parser_state* resp_parser_state = modsec_parser->resp_parser_state;
    resp_parser_state->bytes_processed += bytes_received;

    if (!resp_parser_state->parsing_complete) {
        return INSP_TRANSFORM_IN_PROGRESS;
    }

    zpn_log_http_exchange(exchange);

	*forward = evbuffer_new();
	evbuffer_remove_buffer(resp_parser_state->msg_buf, *forward, resp_parser_state->bytes_processed);
	zpn_reset_http_response_parser((struct zpn_http_parser*)modsec_parser);
	zpn_exchange_complete(modsec_parser->exq);
	return INSP_TRANSFORM_FWD;
}

static int zpn_waf_process_request_success(struct zpn_http_modsec_parser* modsec_parser,
                                           size_t bytes_received,
                                           struct evbuffer** forward)
{
    struct zpn_http_exchange* exchange = zpn_get_current_exchange_request(modsec_parser->exq);
    if (!exchange) {
        WAF_DEBUG_HTTP("Processed http exchange, could not find the current exchange");
        return INSP_TRANSFORM_ERROR;
    }

    struct zpn_http_parser_state* req_parser_state = modsec_parser->req_parser_state;
    req_parser_state->bytes_processed += bytes_received;

    if (!req_parser_state->parsing_complete) {
        WAF_DEBUG_HTTP("WAF process request: in progress, so far processed = %ld",
                          req_parser_state->bytes_processed);
        return INSP_TRANSFORM_IN_PROGRESS;
    }

    *forward = evbuffer_new();
    evbuffer_remove_buffer(req_parser_state->msg_buf, *forward, req_parser_state->bytes_processed);
    zpn_reset_http_request_parser((struct zpn_http_parser*)modsec_parser);
    return INSP_TRANSFORM_FWD;
}

int zpn_buffer_waf_http_request(struct evbuffer* input,
                                struct evbuffer** forward,
                                struct evbuffer** response,
                                struct evbuffer** drop,
                                struct evbuffer** error,
                                void* cookie)
{
    size_t bytes_received = evbuffer_get_length(input);
    if (!bytes_received) {
        return INSP_TRANSFORM_IN_PROGRESS;
    }

    if (!cookie) {
        WAF_DEBUG_HTTP("Received request without cookie");
        return INSP_TRANSFORM_ERROR;
    }

    struct zpn_http_parser* http_parser = (struct zpn_http_parser*)cookie;
    if (http_parser->type != zpn_modsec) {
        WAF_DEBUG_HTTP("Received request for an unsupported parser type: %d", http_parser->type);
        return INSP_TRANSFORM_ERROR;
    }

    struct zpn_http_modsec_parser* modsec_parser = (struct zpn_http_modsec_parser*)cookie;
    if (!modsec_parser) {
        WAF_DEBUG_HTTP("Internal error, no modsec parser");
        return INSP_TRANSFORM_ERROR;
    }

    // Do not process any more requests on this mtunnel.
    // An earlier request on this mtunnel was flagged.
    //
    if (modsec_parser->error_response_pending) {
        WAF_DEBUG_HTTP("Dropping message, error response is pending");
        return INSP_TRANSFORM_DROP;
    }

    struct zpn_http_exchange* exchange = zpn_get_current_exchange_request(modsec_parser->exq);
    if (exchange && exchange->upgrade && exchange->request_headers_complete &&
        exchange->websocket_upgrade && (exchange->response_status == HTTP_WEBSOCKET_SWITCHING))
    {
		if (exchange->intervention_status == zpn_websock_block)
        {
            exchange->waf_log->total_bytes_proc += bytes_received;
            evbuffer_drain(input, bytes_received);
            WAF_DEBUG_WEBSOCK("[%.*s] Dropping websocket req frame %ld bytes, intervention status already blocked",
                               MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                               (long)bytes_received);
			return INSP_TRANSFORM_DROP;
        }
        else if (zpn_websock_block == zpn_waf_websocket_process_request(exchange->mdbg_str, &(exchange->websocket), input, bytes_received))
        {
            exchange->waf_log->total_bytes_proc += bytes_received;
            evbuffer_drain(input, bytes_received);
            WAF_DEBUG_WEBSOCK("[%.*s] Dropping websocket req frame %ld bytes, intervention status block",
                               MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                               (long)bytes_received);
            /* If websocket request identifies violation with action block,
             * response with a close frame to the client to initiate session close */
            zpn_waf_websocket_build_close_frame(exchange->mdbg_str, response);
            return INSP_TRANSFORM_REPLY;
        }
    }

    if (exchange && (exchange->upgrade || exchange->request_limit_exceeded)) {
        *forward = evbuffer_new();
        evbuffer_remove_buffer(input, *forward, bytes_received);
        exchange->waf_log->total_bytes_proc += bytes_received;
        return INSP_TRANSFORM_FWD;
    }

    struct zpn_http_parser_state* req_parser_state = modsec_parser->req_parser_state;
    size_t current_index = req_parser_state->bytes_processed;
    evbuffer_add_buffer(req_parser_state->msg_buf, input);
    if (current_index > g_max_request_limit) {
        /* g_max_request_limit should be configured less than g_max_req_payload_limit */
        WAF_DEBUG_HTTP("current index exceeds %ld bytes, pass forward", (long)g_max_request_limit);
        *forward = evbuffer_new();
        evbuffer_remove_buffer(req_parser_state->msg_buf, *forward, current_index+bytes_received);
        zpn_reset_http_request_parser((struct zpn_http_parser*)modsec_parser);
        // We are here because the current index is non 0.
        // The current index is the total bytes that have been processed at this point.
        // This means an exchange must exist. As they are created at the start of an http
        // transaction. We are checking for the exchange any way.
        //
        if (exchange) {
            exchange->request_limit_exceeded = 1;
            exchange->waf_log->total_bytes_proc += bytes_received;
        }
        return INSP_TRANSFORM_FWD;
    }

    /* set inspection in-progress active */
    __sync_fetch_and_or(modsec_parser->inspection_active, 1);

    unsigned char* front = evbuffer_pullup(req_parser_state->msg_buf, -1);
    req_parser_state->parser.data = modsec_parser;
    uint64_t result = 0;
    size_t bytes_processed_now = 0;
    result = zpn_process_http_request(modsec_parser,
                                      &(req_parser_state->parser),
                                      &(req_parser_state->http_callbacks),
                                      (const char*) (front+current_index),
                                      bytes_received,
                                      &bytes_processed_now);

    __sync_fetch_and_and(modsec_parser->inspection_active, 0);
    WAF_DEBUG_HTTP("HTTP Request process result : [0x%lx]", (long)result);
    if (((result & ZPN_WAF_RESULT_HTTP_UPGRADE) || (result & ZPN_WAF_RESULT_WEBSOCK_UPGRADE)) && !(result & ZPN_WAF_RESULT_HTTP_ERROR_ALL)) {
        // If we detected an upgrade, an exchange must be created.
        // Exchanges get created when a request start is detected.
        //
        exchange = zpn_get_current_exchange_request(modsec_parser->exq);
        if (exchange) {
            exchange->upgrade = 1;
            exchange->waf_log->total_bytes_proc += bytes_received;
            zpn_update_waf_log_request(exchange,
                                       modsec_parser->req_parser_state);
        }
        *forward = evbuffer_new();
        evbuffer_remove_buffer(req_parser_state->msg_buf, *forward, bytes_processed_now);
        zpn_reset_http_request_parser((struct zpn_http_parser*)modsec_parser);
        return INSP_TRANSFORM_FWD;
    }

    if ((result & ZPN_WAF_RESULT_HTTP_ERROR) && !(result & ZPN_WAF_RESULT_MODSEC_ERROR) && !(result & ZPN_WAF_RESULT_WEBSOCK_ERROR)) {
        // Preprocessor violation is identified with http parser error.
        // Websocket error and modsec header rule violation adds intervention error
        // to http parser return value and needs to be excluded.
        int process_res = zpn_waf_process_request_configurable_preprocessor(&result,
                                                                            modsec_parser,
                                                                            req_parser_state);
        if (process_res == WAF_RESULT_ERR)
            return INSP_TRANSFORM_ERROR;
    }

    int res = INSP_TRANSFORM_FWD;
    if (result & ZPN_WAF_RESULT_ERROR) {
        res = zpn_waf_process_request_error(result,
                                             modsec_parser,
                                             bytes_received,
                                             bytes_processed_now,
                                             response);
        return res;
    }

    res = zpn_waf_process_request_success(modsec_parser,
                                           bytes_received,
                                           forward);
    return res;
}

int zpn_buffer_waf_http_response(struct evbuffer* input,
                                 struct evbuffer** forward,
                                 struct evbuffer** response,
                                 struct evbuffer** drop,
                                 struct evbuffer** error,
                                 void* cookie)
{
    size_t bytes_received = evbuffer_get_length(input);
    if (!bytes_received) {
        return INSP_TRANSFORM_IN_PROGRESS;
    }

    if (!cookie) {
        WAF_DEBUG_HTTP("Received response without cookie");
        return INSP_TRANSFORM_ERROR;
    }

    struct zpn_http_parser* http_parser = (struct zpn_http_parser*)cookie;
    if (http_parser->type != zpn_modsec) {
        WAF_DEBUG_HTTP("Received response for an unsupported parser type: %d",
                       http_parser->type);
        return INSP_TRANSFORM_ERROR;
    }

    struct zpn_http_modsec_parser* modsec_parser = (struct zpn_http_modsec_parser*)cookie;
    int proc_state = zpn_handle_pending_response(forward, input, modsec_parser);
    if (proc_state == INSP_TRANSFORM_ERROR || proc_state == INSP_TRANSFORM_FWD) {
        return proc_state;
    }

    struct zpn_http_exchange* exchange = zpn_get_current_exchange_response(modsec_parser->exq);
    if (!exchange) {
        WAF_DEBUG_HTTP("Error, could not find matching exchange for response");
        return INSP_TRANSFORM_ERROR;
    }

    if (exchange && exchange->upgrade && exchange->response_headers_complete &&
        exchange->websocket_upgrade && (exchange->response_status == HTTP_WEBSOCKET_SWITCHING))
    {
		if (exchange->intervention_status == zpn_websock_block)
        {
            exchange->waf_log->total_bytes_proc += bytes_received;
            evbuffer_drain(input, bytes_received);
            WAF_DEBUG_WEBSOCK("[%.*s] Dropping websocket resp frame %ld bytes, intervention status already blocked",
                               MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                               bytes_received);
			return INSP_TRANSFORM_DROP;
        }

        if (zpn_websock_block == zpn_waf_websocket_process_response(exchange->mdbg_str, &(exchange->websocket), input, bytes_received))
        {
            exchange->waf_log->total_bytes_proc += bytes_received;
            evbuffer_drain(input, bytes_received);
            WAF_DEBUG_WEBSOCK("[%.*s] Dropping websocket resp frame %ld bytes, intervention status block",
                               MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str,
                               bytes_received);
            /* If websocket response identifies violation with action block,
             * forward a close frame to the client to initiate session close */
            zpn_waf_websocket_build_close_frame(exchange->mdbg_str, forward);
            return INSP_TRANSFORM_FWD;
        }
    }

    if (exchange && exchange->response_headers_complete && (exchange->upgrade || exchange->response_limit_exceeded)) {
        WAF_DEBUG_HTTP("[%.*s] upgrade: %d response limit reached forwarding: %d",
                MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str, exchange->upgrade, exchange->response_limit_exceeded);
        *forward = evbuffer_new();
        evbuffer_remove_buffer(input, *forward, bytes_received);
        exchange->waf_log->total_bytes_proc += bytes_received;
        return INSP_TRANSFORM_FWD;
    }

    struct zpn_http_parser_state* resp_parser_state = modsec_parser->resp_parser_state;
    size_t current_index = resp_parser_state->bytes_processed;
    evbuffer_add_buffer(resp_parser_state->msg_buf, input);
    if (current_index > g_max_response_limit) {
        WAF_DEBUG_HTTP("Current index exceeds %ld bytes, pass forward", (long)g_max_response_limit);
        *forward = evbuffer_new();
        evbuffer_remove_buffer(resp_parser_state->msg_buf, *forward, current_index+bytes_received);
        zpn_reset_http_response_parser((struct zpn_http_parser*)modsec_parser);
        // We are here because the current index is non 0.
        // The current index is the total bytes that have been processed at this point.
        // This means an exchange must exist. As they are created at the start of an http
        // transaction. We are checking for the exchange any way.
        //
        if (exchange) {
            exchange->response_limit_exceeded = 1;
            exchange->waf_log->total_bytes_proc += bytes_received;
        }
        return INSP_TRANSFORM_FWD;
    }

    /* set inspection in-progress to active */
    __sync_fetch_and_or(modsec_parser->inspection_active, 1);

    unsigned char* front = evbuffer_pullup(resp_parser_state->msg_buf, -1);
    resp_parser_state->parser.data = modsec_parser;

    size_t bytes_processed_now = 0;
    uint64_t result = zpn_process_http_response(modsec_parser,
            &(resp_parser_state->parser),
            &(resp_parser_state->http_callbacks),
            (const char*) (front+current_index),
            bytes_received,
            &bytes_processed_now);

    __sync_fetch_and_and(modsec_parser->inspection_active, 0);
    WAF_DEBUG_HTTP("[%.*s] HTTP Response process result : [0x%lx]",
                    MDBG_STR_LEN(exchange->mdbg_str), exchange->mdbg_str, (long)result);

    if (exchange->websocket_upgrade &&
       (!(result & ZPN_WAF_RESULT_WEBSOCK_UPGRADE) || (exchange->response_status != HTTP_WEBSOCKET_SWITCHING))) {
            exchange->websocket_upgrade = 0;
            __sync_fetch_and_add_8(&(g_waf_stats.num_websocket_upgrade_fail), 1);
    }

    int res = INSP_TRANSFORM_FWD;

    if (result & ZPN_WAF_RESULT_ERROR) {
        if ((result & ZPN_WAF_RESULT_HTTP_UPGRADE) &&
                !(result & ZPN_WAF_RESULT_HTTP_ERROR_ALL))
        {
            /* HTTP Upgrade without any HTTP/Modsec errors */
            *forward = evbuffer_new();
            exchange->waf_log->total_bytes_proc += bytes_received;
            evbuffer_remove_buffer(resp_parser_state->msg_buf, *forward, bytes_processed_now);
            zpn_reset_http_response_parser((struct zpn_http_parser*)modsec_parser);

            if (modsec_parser->populate_log_fields) {
                modsec_parser->populate_log_fields(modsec_parser->log_cookie, exchange->waf_log);
            }

            /* Initialize WebSocket Parser */
            if (result & ZPN_WAF_RESULT_WEBSOCK_UPGRADE) {
                zpn_waf_websocket_exchange_init(exchange, modsec_parser->profile);
                __sync_fetch_and_add_8(&(g_waf_stats.num_websocket_inspections), 1);
            }
            return INSP_TRANSFORM_FWD;
        }

        res = zpn_waf_process_response_error(result,
                exchange,
                modsec_parser,
                bytes_received,
                bytes_processed_now,
                forward);
        return res;
    }

    res = zpn_waf_process_response_success(modsec_parser,
            exchange,
            bytes_received,
            forward);
    return res;
}
