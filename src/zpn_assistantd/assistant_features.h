/*
 * assistant_features.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_FEATURES_H_
#define _ASSISTANT_FEATURES_H_

int assistant_features_init();

void assistant_features_stats_log();
void assistant_features_enable_ut_mode();
int assistant_features_is_enabled_ut_mode();
void
assistant_features_enable_health_txn_prefer_local_cache_over_dsp_cache();
void
assistant_features_disable_health_txn_prefer_local_cache_over_dsp_cache();
int
assistant_features_is_health_txn_prefer_local_cache_over_dsp_cache();

int assistant_get_max_pause_time(int64_t connector_gid);
void assistant_monitor_max_pause_time(int64_t connector_gid);

int assistant_features_is_dtls_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int assistant_to_broker_dtls_mtu(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);

int assistant_features_is_assistant_ncache_enabled(void);
void assistant_features_enable_assistant_ncache(void);

int assistant_features_is_quickack_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int assistant_features_is_quickack_read_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int assistant_features_is_quickack_app_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
char* assistant_features_get_sarge_minimum_version(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int assistant_features_is_app_buffer_tune_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int64_t assistant_config_get_fohh_window(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int64_t assistant_config_get_fohh_mconn_window(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int64_t assistant_config_get_fohh_watermark (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int64_t assistant_config_get_fohh_mconn_watermark (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);

int assistant_features_get_zhealth_probe_lib_socket_engine_cb(int sys_type, int64_t sys_gid, int64_t sys_grp_gid);

int assistant_features_is_alt_cloud_enabled(int64_t connector_gid);
void assistant_features_alt_cloud_cfg_monitor_init(int64_t connector_gid);
int assistant_features_is_qbr_insights_feature_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);

int assistant_features_fproxy_init();
void assistant_features_fproxy_unregister();
int assistant_features_is_fproxy_enabled(int64_t connector_gid);
void assistant_features_fproxy_cfg_monitor_init(int64_t connector_gid);
void assistant_features_fproxy_log_status();
int
assistant_fproxy_stats_fill(void*     cookie,
                            int       counter,
                            void*     structure_data);

int assistant_features_is_databroker_resilience_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int assistant_get_doublehop_timeout(int64_t connector_gid);
int assistant_features_allocator_libevent_out_queue_is_enabled(int64_t connector_gid);
int assistant_features_allocator_libevent_out_queue_get_params(int64_t connector_gid,
                                                               int64_t *out_queue_min_len_bytes,
                                                               int64_t *out_queue_max_len_bytes,
                                                               int64_t *sys_mem_max_percent,
                                                               int64_t *start_thresh_percent);


int assistant_get_doublehop_switch_timeout(int64_t connector_gid);
int64_t assistant_fohh_flow_control_enhancement_enabled(int64_t customer_gid);
int64_t assistant_features_mtunnel_fin_expire_us(int64_t connector_gid);

#endif // _ASSISTANT_FEATURES_H_
