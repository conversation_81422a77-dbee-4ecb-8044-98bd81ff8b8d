argo_parse_files(
    INPUT_FILES
        assistant_app.c
        assistant_app_mock.c
        assistant_broker.c
        assistant_broker_control.c
        assistant_broker_control_tx.c
        assistant_waf_ssl.c
        assistant_control.c
        assistant_control_tx.c
        assistant_cfg.c
        assistant_cfg_override.c
        assistant_data.c
        assistant_dns.c
        assistant_features.c
        assistant_init.c
        assistant_monitor.c
        assistant_dr_interface.c
        assistant_pbroker.c
        assistant_pbroker_control.c
        assistant_pbroker_control_tx.c
        assistant_rpc.c
        assistant_rpc_rx.c
        assistant_rpc_tx.c
        assistant_service.c
        assistant_log_tx.c
        assistant_state.c
        assistant_stats.c
        assistant_stats_tx.c
        assistant_sticky_cache.c
        assistant_ncache.c
        assistant_np_cfg.c
        assistant_additional_debug_logs.c
        zpn_assistant_mtunnel.c
        zpn_assistant_private.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

set(lib_sources
    assistant_app.c
    assistant_app_mock.c
    assistant_broker.c
    assistant_broker_control.c
    assistant_broker_control_tx.c
    assistant_stats.c
    assistant_waf_ssl.c
    assistant_cfg.c
    assistant_cfg_application.c
    assistant_cfg_application_group.c
    assistant_cfg_assistant.c
    assistant_cfg_assistant_group.c
    assistant_cfg_map_application_to_application_group.c
    assistant_cfg_map_assistant_group_to_server_group.c
    assistant_cfg_map_assistant_to_assistant_group.c
    assistant_cfg_map_server_group_to_app.c
    assistant_cfg_map_server_group_to_server.c
    assistant_cfg_override.c
    assistant_cfg_override_feature.c
    assistant_cfg_server.c
    assistant_cfg_server_group.c
    assistant_cfg_pbroker.c
    assistant_site.c
    assistant_cfg_pbroker_load.c
    assistant_cfg_version.c
    assistant_cfg_sub_module_upgrade.c
    assistant_admin_probe.c
    assistant_cfg_waf.c
    assistant_cfg_zpath_customer.c
    assistant_control.c
    assistant_control_tx.c
    assistant_data.c
    assistant_dns.c
    assistant_features.c
    assistant_init.c
    assistant_log_tx.c
    assistant_monitor.c
    assistant_dr_interface.c
    assistant_pbroker.c
    assistant_pbroker_control.c
    assistant_pbroker_control_tx.c
    assistant_rpc.c
    assistant_start_program.c
    assistant_rpc_rx.c
    assistant_rpc_tx.c
    assistant_state.c
    assistant_stats.c
    assistant_stats_tx.c
    assistant_sticky_cache.c
    assistant_ncache.c
    assistant_np_cfg.c
    assistant_np_connector.c
    assistant_service.c
    assistant_target.c
    assistant_zdx.c
    assistant_additional_debug_logs.c
    assistant_config_override_desc.c
    ast_waf_sess_mgr.c
    zpn_assistant_health.c
    zpn_assistant_mtunnel.c
    zpn_assistant_private.c
    zpn_connector_inspect_impl.c
    zpn_connector_inspect.c
    zpn_connector_tls_inspect.c
    zpn_connector_tun_inspect.c
    zpn_connector_tun_tls_inspect.c
    zpn_connector_idps_inspect.c
    zpn_connector_webprobe_http_inspect.c
    zpn_assistant_two_hop_data_health.c
)

add_library(assistant_lib STATIC ${lib_sources})
message(STATUS "SIGNED='$ENV{SIGNED}'")
target_compile_options(
    assistant_lib
    PUBLIC -DJENKINS_BUILD=$<BOOL:$ENV{JENKINS_BUILD}>
    PUBLIC -DPRODUCTION_BUILD=$<IF:$<BOOL:$ENV{SIGNED}>,"true","false">
)

target_link_libraries(
    assistant_lib
    PUBLIC
        zpn_zdx_combiner
        zpn_enrollment
        zpn_zdx_webprobe_cache
        np_connector
        $<$<PLATFORM_ID:Linux>:LibCAP>
        zpn_waf
        zpn_version_control
        zpath_lib
)
add_dependencies(assistant_lib gen-headers-zpn_assistantd)

if(PKG_PLATFORM STREQUAL "el9")
    add_executable(np-connector-child zpa_connector.c)
    target_link_libraries(np-connector-child PRIVATE assistant_lib zpn_pcap zpath_app_simple)
    target_compile_definitions(np-connector-child PRIVATE APP_ROLE="np-connector-child")
    add_rpm(
        NAME np-connector-child
        SPEC rpm/np-connector-child.spec
        MAKEFILE Makefile.rpm.np-connector-child
        FILES
            np-connector-child
            rpm/np-connector-child.service
            rpm/53-zscaler.preset
            rpm/asan.options
            rpm/np-connector-child.conf
            rpm/15-zscaler-user
            rpm/npwg0.conf
            rpm/frr.conf
            ${wg}
            ${wg-quick}
            license.txt
    )
    add_bin(TARGET np-connector-child DEFAULT_BUILD_ONLY)
endif()

add_executable(zpa-connector-child zpa_connector.c)

target_link_libraries(zpa-connector-child PRIVATE assistant_lib zpn_pcap zpath_app_simple)

target_compile_definitions(zpa-connector-child PRIVATE APP_ROLE="zpa-connector-child")

add_simple_apps(
    SOURCES
        assistant_sticky_cache_test.c
        assistant_sticky_cache_test_scale.c
        assistant_app_test.c
        assistant_ncache_test.c
        assistant_start_program_test.c
        assistant_data_test.c
    DEPS assistant_lib zpath_app_simple
)

add_simple_tests(assistant_sticky_cache_test assistant_ncache_test assistant_data_test)

#Testing version of library - could be done with looping but thats causing merge conflicts so just seperate
add_library(assistant_testing_lib STATIC ${lib_sources})
target_compile_options(
    assistant_testing_lib
    PUBLIC -DJENKINS_BUILD=$<BOOL:$ENV{JENKINS_BUILD}>
    PUBLIC -DPRODUCTION_BUILD=$<IF:$<BOOL:$ENV{SIGNED}>,"true","false">
)
target_link_libraries(
    assistant_testing_lib
    PUBLIC
        zpn
        zpn_zdx_combiner
        zpn_enrollment
        zpn_zdx_webprobe_cache
        np_connector
        $<$<PLATFORM_ID:Linux>:LibCAP>
        zpn_waf
        zpn_version_control
        zpath_lib
)

target_compile_definitions(assistant_testing_lib PRIVATE ASSISTANT_LIB_TESTING=1)
add_dependencies(assistant_testing_lib gen-headers-zpn_assistantd)
#end of testing lib settings

add_subdirectory(tests)

find_program(wg wg REQUIRED)
find_program(wg-quick wg-quick REQUIRED)

add_rpm(
    NAME zpa-connector-child
    SPEC rpm/zpa-connector-child.spec
    MAKEFILE Makefile.rpm.zpa-connector-child
    FILES
        zpa-connector-child
        rpm/zpa-connector-child.service
        rpm/52-zscaler.preset
        rpm/asan.options
        rpm/zpa-connector-child.conf
        license.txt
)

add_bin(TARGET zpa-connector-child DEFAULT_BUILD_ONLY)
