/*
 * assistant_config_override_desc.c. Copyright (C) 2023-2024 Zscaler Inc. All Rights Reserved.
 *
 * Assistant's config override description registration.
 */

#include "zpn_assistantd/assistant_config_override_desc.h"
#include "zrdt/zrdt.h"
#include "fohh/fohh_log.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_lib.h"


static struct zpath_config_override_desc assistant_config_override_descriptions[] = {
        {
                .key                = QUICKACK_CONFIG_ASSISTANT,
                .feature_group      = FEATURE_GROUP_QUICKACK,
                .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
                .details            = "0: Quickack on assistant is disabled\n"
                                      "1: Quickack on assistant is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. Quickack on assistant is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_ASST_QUICKACK
        },
        {
                .key                = QUICKACK_READ_CONFIG_ASSISTANT,
                .feature_group      = FEATURE_GROUP_QUICKACK,
                .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
                .details            = "0: Quickack read on assistant is disabled\n"
                                      "1: Quickack read on assistant is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. Quickack read on assistant is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_ASST_QUICKACK_READ
        },
        {
                .key                = LIBEVENT_LOW_WRITE_WATERMARK_CONNECTOR,
                .desc               = "Enable Low Write Watermark on App Connector",
                .details            = "0: LibEvent Low Write Watermark on App Connector is disabled\n"
                                      "1: LibEvent Low Write Watermark on App Connector is enabled\n"
                                      "Order of check: customer gid, global\n"
                                      "default: 0 (i.e. LibEvent low write watermark on App Connector is disabled by default)",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LIBEVENT_LOW_WRITE_WATERMARK_MIN,
                .int_range_hi       = LIBEVENT_LOW_WRITE_WATERMARK_MAX,
                .int_default        = LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
                .feature_group      = FEATURE_GROUP_LIBEVENT_LOW_WRITE_WATERMARK,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = ASSISTANT_QUICKACK_APP_CONFIG,
                .feature_group      = FEATURE_GROUP_QUICKACK,
                .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for TCP data connection between app connector and app server",
                .details            = "0: Quickack app is disabled\n"
                                      "1: Quickack app is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. Quickack on app is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_ASST_QUICKACK_APP
        },
        {
                .key                = ASSISTANT_MAX_PAUSE_TIME_CONFIG,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "max pause time for connector to server connections until timeout",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 120 secs (i.e. 120 secs is the default max pause time for connector to server connections)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASST_MAX_PAUSE_TIME,
                .int_range_hi       = HIGH_ASST_MAX_PAUSE_TIME,
                .int_default        = DEFAULT_ASST_MAX_PAUSE_TIME
        },
        {
                .key                = CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_KEEPALIVE,
                .desc               = "enable SO_KEEPALIVE socket option on tcp connection between app connector and app server per app segment gid",
                .details            = "0: TCP keepalive from app segment is disabled\n"
                                      "1: TCP keepalive from app segment is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. TCP keepalive from app segment is enabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT,
        },
        {
                .key                = CONNECTOR_KEEPALIVE_FEATURE,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_KEEPALIVE,
                .desc               = "enable SO_KEEPALIVE socket option on tcp connection between app connector and app server",
                .details            = "0: TCP keepalive for app is disabled\n"
                                      "1: TCP keepalive for app is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. TCP keepalive for app is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_KEEPALIVE_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_ALL,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE,
                .desc               = "enable or disable ALL admin probe actions",
                .details            = "0: admin probe feature is disabled\n"
                                      "1: admin probe feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_ALL_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_PROCESS,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE,
                .desc               = "enable or disable RESTART_PROCESS action from admin probe",
                .details            = "0: admin probe on restart process feature is disabled\n"
                                      "1: admin probe on restart process feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on restart process feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_PROCESS_ENABLED,
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_SYSTEM,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE,
                .desc               = "enable or disable RESTART_SYSTEM action from admin probe",
                .details            = "0: admin probe on restart system feature is disabled\n"
                                      "1: admin probe on restart system feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on restart system feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_SYSTEM_ENABLED,
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_DNS,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_DNS_ADMIN_PROBE,
                .desc               = "enable or disable DNS action from admin probe",
                .details            = "0: admin probe on dns feature is disabled\n"
                                      "1: admin probe on dns feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on dns feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_DNS_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_ICMP,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ICMP_ADMIN_PROBE,
                .desc               = "enable or disable ICMP PROBE action from admin probe",
                .details            = "0: admin probe on icmp feature is disabled\n"
                                      "1: admin probe on icmp feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on icmp feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_ICMP_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_TCP,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_TCP_ADMIN_PROBE,
                .desc               = "enable or disable TCP PROBE action from admin probe",
                .details            = "0: admin probe on tcp feature is disabled\n"
                                      "1: admin probe on tcp feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on tcp feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_TCP_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_TCPDUMP,
                .desc               = "enable or disable TCPDUMP action from admin probe",
                .details            = "0: admin probe on tcpdump feature is disabled\n"
                                      "1: admin probe on tcpdump feature is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. admin probe on tcpdump feature is disabled by default)",
                .val_type           = config_type_int,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_TCPDUMP_ADMIN_PROBE,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_TCPDUMP_ENABLED
        },
        {
                .key                = CONNECTOR_ADMIN_PROBE_FEATURE_FRR_CMDS,
                .desc               = "enable or disable FRR commands support through NP admin probe",
                .details            = "0: FRR commands execution through Admin Probe is disabled.\n"
                                      "1: FRR commands execution through Admin Probe is enabled\n"
                                      "Order of check: global\n"
                                      "default: 0 (i.e. FRR commands execution through Admin Probe is disabled by default)",
                .val_type           = config_type_int,
                .feature_group      = FEATURE_GROUP_NP_CONNECTOR_FRR_ADMIN_PROBE,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ADMIN_PROBE_FRR_CMDS_ENABLED
        },
        {
                .key                = CONNECTOR_SAMPLES_FOR_AVG_RTT,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "number of the samples app connector should be collected for average app server RTT calculation for health reporting",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 10 (i.e. 10 is the default sample size for the average RTT)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_CONNECTOR_SAMPLES_FOR_AVG_RTT,
                .int_range_hi       = HIGH_CONNECTOR_SAMPLES_FOR_AVG_RTT,
                .int_default        = CONNECTOR_SAMPLES_FOR_AVG_RTT_DEFAULT
        },
        {
                .key                = CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ZCDNS,
                .desc               = "disallow app connector dns retry mechanism upon a failure dns response",
                .details            = "0: retry in zcdns in case of an error feature is disabled\n"
                                      "1: retry in zcdns in case of an error feature is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. retry in zcdns in case of an error feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE
        },
        {
                .key                = CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "Set the inter event interval for CPU starvation event which is sent by AC when it doesn't receive enough CPU cycles to perform its operation.",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 900 (i.e. 900 is the default value for the CPU starvation interval)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL,
                .int_range_hi       = HIGH_CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL,
                .int_default        = CONFIG_ZPN_EVENT_CPU_STARVATION_DEFAULT_INTERVAL
        },
        {
                .key                = ASSISTANT_APP_BUFFER_TUNE_FEATURE,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER,
                .desc               = "enable or disable app buffer tune feature such that we can tune up our FOHH window update window size",
                .details            = "0: app buffer tune feature is disabled\n"
                                      "1: app buffer tune feature is enabled\n"
                                      "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. app buffer tune feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = ASSISTANT_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE
        },
        {
                .key                = ASSISTANT_APP_BUFFER_CONFIG,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER,
                .desc               = "tune FOHH window size to the desired value",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. 0 is the default value for the app buffer size)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASSISTANT_APP_BUFFER_CONFIG_VALUE,
                .int_range_hi       = HIGH_ASSISTANT_APP_BUFFER_CONFIG_VALUE,
                .int_default        = ASSISTANT_APP_BUFFER_CONFIG_DEFAULT_VALUE
        },
        {
                .key                = ASSISTANT_APP_BUFFER_MCONN_CONFIG,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER,
                .desc               = "tune FOHH mconn window size to the desired value",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. 0 is the default value for the mconn app buffer size)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASSISTANT_APP_BUFFER_MCONN_CONFIG_VALUE,
                .int_range_hi       = HIGH_ASSISTANT_APP_BUFFER_MCONN_CONFIG_VALUE,
                .int_default        = ASSISTANT_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE
        },
        {
                .key                = ASSISTANT_APP_BUFFER_WATERMARK,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER,
                .desc               = "tune FOHH window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. 0 is the default watermark value for the app buffer)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASSISTANT_APP_BUFFER_WATERMARK_VALUE,  //16MB
                .int_range_hi       = HIGH_ASSISTANT_APP_BUFFER_WATERMARK_VALUE, //~24MB
                .int_default        = ASSISTANT_APP_BUFFER_WATERMARK_DEFAULT_VALUE
        },
        {
                .key                = ASSISTANT_APP_BUFFER_MCONN_WATERMARK,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_APP_BUFFER,
                .desc               = "tune FOHH mconn window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (i.e. 0 is the default watermark value for the mconn app buffer)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASSISTANT_APP_BUFFER_MCONN_WATERMARK_VALUE,
                .int_range_hi       = HIGH_ASSISTANT_APP_BUFFER_MCONN_WATERMARK_VALUE,
                .int_default        = ASSISTANT_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE
        },
        {
                .key                = CONFIG_CONNECTOR_QUIET,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "feature is to disabling sending status requests on FOHH connections",
                .details            = "0: connector quiet feature is disabled\n"
                                      "1: connector quiet feature is enabled\n"
                                      "Order of check: customer gid\n"
                                      "default: 0 (i.e. connector quiet feature is disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = CONFIG_CONNECTOR_QUIET_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_CTL,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on ctl connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_STATS,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on stats connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_OVD,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on ovd connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_CFG,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on config connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_LOG,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on Log connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_WAF,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on WAF log connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_APP_INSPECTION,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on Application Inspection log connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = CONFIG_FOHH_STATUS_INTVL_DATA,
                .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
                .desc               = "status interval for sending fohh_status_request messages on data connection",
                .details            = "Order of check: customer gid\n"
                                      "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
                .int_range_lo       = LOW_STATUS_INTVL,
                .int_range_hi       = HIGH_STATUS_INTVL,
                .int_default        = STATUS_INTVL_DEFAULT
        },
        {
                .key                = ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                .desc               = "argo logging memory threshold from config override and update",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 95",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                .int_range_hi       = HIGH_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                .int_default        = DEFAULT_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                .feature_group      = FEATURE_GROUP_ARGO_LOGGING
        },
        {
                .key                = ASSISTANT_MTUNNEL_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "max concurrent mtunnel appc can take in memory",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 1000000 (i.e. not going to use this limit as for now, thus making default value rediculously large.)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = ASSISTANT_MAX_CONCURRENT_MTUNNEL_LOW,
                .int_range_hi       = ASSISTANT_MAX_CONCURRENT_MTUNNEL_HIGH,
                .int_default        = ASSISTANT_MAX_CONCURRENT_MTUNNEL_DEFAULT
        },
        {
                .key                = CONFIG_FEATURE_ALT_CLOUD,
                .feature_group      = FEATURE_GROUP_ALT_CLOUD,
                .desc               = "enable alt cloud feature",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 1 (enabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc | config_component_broker | config_component_pbroker | config_component_wally,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE
        },
        {
                .key                = FOHH_ENABLE_FC_RESET_RECOVER,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_FLOW_CONTROL,
                .desc               = "enable/disable bad state recover mitigation caused by FOHH flow control",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_FOHH_ENABLE_FC_RESET_RECOVER
        },
        {
                .key                = FOHH_FC_RESET_RECOVER_INTERVAL_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_FLOW_CONTROL,
                .desc               = "time interval window to monitor for to determine whether it is a FOHH flow control bad state",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 3*60 (monitor for 3 mins time interval)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_FOHH_FC_RESET_RECOVER_INTERVAL_S,
                .int_range_hi       = HIGH_FOHH_FC_RESET_RECOVER_INTERVAL_S,
                .int_default        = DEFAULT_FOHH_FC_RESET_RECOVER_INTERVAL_S
        },
        {
                .key                = FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_FLOW_CONTROL,
                .desc               = "threshold to determine whether we are experiencing a FOHH flow control bad state",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "its percentage times hundred, thus if we want 99percent, we put 9900\n"
                                      "default: 9900 (99 percent threshold)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED,
                .int_range_hi       = HIGH_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED,
                .int_default        = DEFAULT_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED
        },
        {
                .key                = DTLS_FEATURE,
                .feature_group      = FEATURE_GROUP_DTLS,
                .desc               = "enable/disable dtls connection for data path",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc | config_component_broker | config_component_pbroker,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = DEFAULT_ASST_DTLS
        },
        {
                .key                = DTLS_FEATURE_MTU,
                .feature_group      = FEATURE_GROUP_DTLS,
                .desc               = "customize interface MTU for dtls connection",
                .details            = "Order of check: component id, component group id, customer gid\n"
                                      "default: 1482 bytes (DEFAULT_STREAM_MTU)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc | config_component_broker | config_component_pbroker,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust,
                .int_range_lo       = LOW_DTLS_MTU,
                .int_range_hi       = HIGH_DTLS_MTU,
                .int_default        = DEFAULT_STREAM_MTU
        },
        {
                .key                = CONFIG_FOHH_LOG_MAX_IN_FLIGHT,
                .feature_group      = FEATURE_GROUP_FOHH_CONFIG,
                .desc               = "maximum allowed fohh logs in flight",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 2000",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = FOHH_LOG_MAX_LOGS_IN_FLIGHT_LOW,
                .int_range_hi       = FOHH_LOG_MAX_LOGS_IN_FLIGHT_HIGH,
                .int_default        = FOHH_LOG_MAX_LOGS_IN_FLIGHT_DEFAULT

        },
        {
                .key                = SARGE_MINIMUM_VERSION,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "minimum sarge version customer recommended to run",
                .details            = "configure a minimum version this customer zpa-connector should be on",
                .val_type           = config_type_str,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .str_default        = DEFAULT_SARGE_MINIMUM_VERSION
        },
        {
                .key                = ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER,
                .feature_group      = FEATURE_GROUP_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER,
                .desc               = "feature flag to disable app connector send app route registration message to public broker",
                .details            = "appc stop sending app route registration to public broker as the only consumer java dispatcher is no longer using the info\n"
                                      "default: 0 (behaviour disabled by default)\n"
                                      "min: 0\n"
                                      "max: 1",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER_DEFAULT_VALUE
        },
        /* WAF related overrides */
        {
                .key                = WAF_FEATURE,
                .desc               = "Enable or disable WAF feature.",
                .details            = "0: disable (WAF is disabled)\n"
                                      "1: enable (WAF is enabled)\n"
                                      "default: 0 (i.e. WAF is disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable WAF feature globally.",
                .details            = "0: enable (WAF is enabled)\n"
                                      "1: disable (WAF is disabled)\n"
                                      "default: 0 (i.e. WAF is enabled globally)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_THREATLABZ_FEATURE,
                .desc               = "Enable or disable Threatlabz feature.",
                .details            = "0: disable (Threatlabz is disabled)\n"
                                      "1: enable (Threatlabz is enabled)\n"
                                      "default: 0 (i.e. Threatlabz is disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_THREATLABZ_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_THREATLABZ_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable Threatlabz feature globally",
                .details            = "0: enable (Threatlabz is enabled)\n"
                                      "1: disable (Threatlabz is disabled)\n"
                                      "default: 0 (i.e. Threatlabz is enab:qled globally)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_THREATLABZ_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_EXCEPTIONS_FEATURE,
                .desc               = "Enable or disable WAF Exceptions feature.",
                .details            = "0: disable (WAF Exceptions are disabled)\n"
                                      "1: enable (WAF Exceptions are enabled)\n"
                                      "default: 0 (i.e. WAF Exceptions are disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_EXCEPTIONS_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_EXCEPTIONS_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable WAF Exceptions feature globally",
                .details            = "0: enable (WAF Exceptions are enabled)\n"
                                      "1: disable (WAF Exceptions are disabled)\n"
                                      "default: 0 (i.e. WAF Exceptions are enabled globally)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_EXCEPTIONS_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_JSONPARSING_FEATURE,
                .desc               = "Enable or disable WAF JSON parsing feature.",
                .details            = "0: disable (WAF JSON parsing disabled)\n"
                                      "1: enable (WAF JSON parsing is enabled)\n"
                                      "default: 0 (i.e. WAF JSON parsing is disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_JSONPARSING_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_JSONPARSING_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable WAF JSON parsing feature globally",
                .details            = "0: enable (WAF JSON parsing is enabled)\n"
                                      "1: disable (WAF JSON parsing is disabled)\n"
                                      "default: 0 (i.e. WAF JSON parsing is enabled globally)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_JSONPARSING_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_XMLPARSING_FEATURE,
                .desc               = "Enable or disable WAF XML parsing feature.",
                .details            = "0: disable (WAF XML parsing is disabled)\n"
                                      "1: enable (WAF XML parsing is enabled)\n"
                                      "default: 0 (i.e. WAF XML parsing is disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_XMLPARSING_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_XMLPARSING_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable WAF XML parsing feature globally",
                .details            = "0: enable (WAF XML parsing is enabled)\n"
                                      "1: disable (WAF XLM parsing is disabled)\n"
                                      "default: 0 (i.e. WAF XML parsing is enabled globally)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_XMLPARSING_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_ACCESS_MODE_FEATURE,
                .desc               = "Enable or disable strict access in WAF.",
                .details            = "0: disable (Not strict access.)\n"
                                      "1: enable (Block inspection on failures.)\n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_ACCESS_STRICT_MODE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = AD_PROTECTION_FEATURE,
                .desc               = "Enable or disable AD protection feature",
                .details            = "0: disable \n"
                                      "1: enable \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = AD_PROTECTION_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = AD_PROTECTION_FEATURE_GLOBAL_DISABLE,
                .desc               = "Enable or disable AD protection feature globally",
                .details            = "0: enable \n"
                                      "1: disable \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = AD_PROTECTION_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_ENABLE_REASON_PAYLOAD_STATUS,
                .desc               = "Enable this to set a payload inspection status in case of error",
                .details            = "0: Not to set payloadstatus \n"
                                      "1: To set payload status \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_DEFAULT_REASON_PAYLOAD_STATUS,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_MAX_REQUEST_LIMIT_FEATURE,
                .desc               = "Set the max number of http request under inspection",
                .details            = "Any request beyond this is forwarded without inspection.\n"
                                      "Default is 1024X1024",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_REQUEST_LIMIT,
                .int_range_hi       = WAF_MAX_REQUEST_LIMIT,
                .int_default        = WAF_DFLT_REQUEST_LIMIT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_MAX_RESPONSE_LIMIT_FEATURE,
                .desc               = "Set the max number of http responses under inspection",
                .details            = "Any response beyond this is forwarded without inspection.\n"
                                      "Default is 1024X1024",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_RESPONSE_LIMIT,
                .int_range_hi       = WAF_MAX_RESPONSE_LIMIT,
                .int_default        = WAF_DFLT_RESPONSE_LIMIT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = APPLICATION_MULTI_MATCH_FEATURE,
                .feature_group      = FEATURE_GROUP_APPLICATION_MULTI_MATCH,
                .desc               = "Multi match application feature flag",
                .details            = "This feature defines if we allow multi match style of applications instead of first match\n"
                                      "Order of check: customer id, global\n"
                                      "Default: 0, feature is disabled",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = APPLICATION_MULTI_MATCH_FEATURE_MIN,
                .int_range_hi       = APPLICATION_MULTI_MATCH_FEATURE_MAX,
                .int_default        = APPLICATION_MULTI_MATCH_FEATURE_DEFAULT
        },
        {
                .key                = APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED,
                .feature_group      = FEATURE_GROUP_APPLICATION_MULTI_MATCH,
                .desc               = "Multi match application feature hard disable flag",
                .details            = "feature flag to hard disable the multi match application globally\n"
                                      "Order of check: global\n"
                                      "Default: 0, feature is not hard disabled",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_MIN,
                .int_range_hi       = APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_MAX,
                .int_default        = APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_DEFAULT
        },
        {
                .key                = AD_PROTOCOL_OVERRIDE_CONFIG,
                .desc               = "Set AD inspection per protocol type",
                .details            = "Enable/Disable AD inspection per AD protocol type(json str)",
                .val_type           = config_type_str,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .str_default        = AD_PROTOCOL_OVERRIDE_DEFAULT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = AUTO_APP_PROTECTION_FEATURE,
                .desc               = "Enable/Disable Auto APP Protection",
                .details            = "0: Disable Auto APP Protection \n"
                                      "1: Enable Auto APP Protection \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = AUTO_APP_PROTECTION_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = AUTO_APP_PROTECTION_FEATURE_GLOBAL_DISABLE,
                .desc               = "Global level disable Auto APP Protection",
                .details            = "if Zscaler global customer and tenant have Auto App Protection enabled = Enable, else Disable \n"
                                      "1: Global Disable Auto APP Protection \n"
                                      "0: Global Enable Auto APP Protection \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = AUTO_APP_PROTECTION_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = AUTO_APP_CERT_GEN_FEATURE_GLOBAL_DISABLE,
                .desc               = "Global level disable Auto Certificate Generation",
                .details            = "if Zscaler global customer and tenant have Auto Certificate Generation enabled = Enable, else Disable \n"
                                      "1: Global Disable Auto Certificate Generation \n"
                                      "0: Global Enable Auto Certificate Generation \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = AUTO_APP_CERT_GEN_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_PROTOCOL_TAGGING_FEATURE,
                .desc               = "Enable/Disable WAF Protocol Tagging",
                .details            = "0: Disable WAF Protocol Tagging \n"
                                      "1: Enable WAF Protocol Tagging \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_PROTOCOL_TAGGING_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_PROTOCOL_TAGGING_FEATURE_GLOBAL_DISABLE,
                .desc               = "Global level disable WAF Protocol Tagging",
                .details            = "if Zscaler global customer and tenant have WAF Protocol Tagging enabled = Enable, else Disable \n"
                                      "1: Global Disable Auto WAF Protocol Tagging \n"
                                      "0: Global Enable Auto WAF Protocol Tagging \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_systemwide,
                .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = WAF_PROTOCOL_TAGGING_GLOBAL_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = PDP_PROFILE_PURGE_INTERVAL,
                .desc               = "Set frequency for executing pdp profile purging",
                .details            = "Set a non negative integer for pdp profile purging time cycle in second \n"
                                      "min is 1hour: 60*60 \n"
                                      "default is 24hour: 24*60*60 ",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = PDP_PROFILE_PURGE_INTERVAL_MIN,
                .int_range_hi       = PDP_PROFILE_PURGE_INTERVAL_MAX,
                .int_default        = PDP_PROFILE_PURGE_INTERVAL_DEFAULT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = PDP_PROFILE_AGING_TIMEOUT,
                .desc               = "Set aging timeout for pdp profile",
                .details            = "Set a non negative integer for pdp profile aging timeout in second \n"
                                      "profile marked as idle after not being accessed for aging timeout, \n"
                                      "and will be purged when executing pdp auto profile purging \n"
                                      "min is 1hour: 60*60 \n"
                                      "default is 24hour: 24*60*60",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = PDP_PROFILE_AGING_TIMEOUT_MIN,
                .int_range_hi       = PDP_PROFILE_AGING_TIMEOUT_MAX,
                .int_default        = PDP_PROFILE_AGING_TIMEOUT_DEFAULT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = PDP_PROFILE_AGING,
                .desc               = "Enable/Disable aging for pdp profile",
                .details            = "0: Disable pdp profile aging, inactive profile \n"
                                      "which remain unaccessed for some time will not be purged \n"
                                      "1: Enable pdp profile aging, inactive profile will be purged \n"
                                      "default: 1",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = PDP_PROFILE_AGING_ENABLED,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_INSPECTION_TIMEOUT_FEATURE,
                .desc               = "Set the WAF Inspection MTunnel timeout in seconds",
                .details            = "Set a value range 20s-360s(1 hr).Default is 30s.",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_INSPECTION_TIMEOUT,
                .int_range_hi       = WAF_MAX_INSPECTION_TIMEOUT,
                .int_default        = WAF_DFLT_INSPECTION_TIMEOUT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_MAX_REQ_PAYLOAD_LIMIT_FEATURE,
                .desc               = "Set the WAF Max Payload Limit for Inspection in Bytes",
                .details            = "Set a value range 0 - 10*1024*1024 Bytes",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_PAYLOAD_LIMIT,
                .int_range_hi       = WAF_MAX_PAYLOAD_LIMIT,
                .int_default        = WAF_DFLT_PAYLOAD_LIMIT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_MAX_RSP_PAYLOAD_LIMIT_FEATURE,
                .desc               = "Set the WAF Max Payload Limit for Inspection in Bytes",
                .details            = "Set a value range 0 - 10*1024*1024 Bytes",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_PAYLOAD_LIMIT,
                .int_range_hi       = WAF_MAX_PAYLOAD_LIMIT,
                .int_default        = WAF_DFLT_PAYLOAD_LIMIT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_MAX_HEADER_SIZE_LIMIT_FEATURE,
                .desc               = "Set the WAF Max Header Limit for Inspection in Bytes",
                .details            = "Set a value range 32*1024 - 128*1024 Bytes",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = WAF_MIN_HEADER_SIZE_LIMIT,
                .int_range_hi       = WAF_MAX_HEADER_SIZE_LIMIT,
                .int_default        = WAF_DFLT_HEADER_SIZE_LIMIT,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = WAF_PROF_BUILD_DELAY_INTERVAL,
                .desc               = "Set the profile build delay interval in seconds",
                .details            = "Set a value range 5 - 600 seconds",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst,
                .int_range_lo       = WAF_PROF_BUILD_DELAY_INTERVAL_MIN_VAL,
                .int_range_hi       = WAF_PROF_BUILD_DELAY_INTERVAL_MAX_VAL,
                .int_default        = WAF_PROF_BUILD_DELAY_INTERVAL_DEF_VAL,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = ASST_WAF_DEBUG_FILTER_CONFIG,
                .desc               = "Set debug filter for mtunnel domain, content-type and content-encoding",
                .details            = "Enable/Disable enhanced inspection using debug filters (json str)",
                .val_type           = config_type_str,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
                .str_default        = ASST_WAF_DEBUG_FILTER_DEF,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = API_PROTECTION_FEATURE,
                .desc               = "Enable or disable API inspection feature",
                .details            = "0: Disable API protection \n"
                                      "1: Enable API Protection \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = API_PROTECTION_ENABLED,
                .value_traits       = config_value_traits_feature_enablement,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = API_PROTECTION_FEATURE_GLOBAL_DISABLE,
                .desc               = "Global level disable WAF API Protection",
                .details            = "if Zscaler global customer and tenant have WAF API Protection enabled = Enable, else Disable \n"
                                      "1: Global Disable WAF API Protection \n"
                                      "0: Global Enable  WAF API Protection \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = API_PROTECTION_GLOBAL_DISABLE,
                .value_traits       = config_value_traits_systemwide,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = API_DISCOVERY_FEATURE,
                .desc               = "Enable or disable API Discovery feature",
                .details            = "0: Disable API Discovery \n"
                                      "1: Enable API Discovery \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = API_DISCOVERY_DISABLED,
                .int_range_hi       = API_DISCOVERY_ENABLED,
                .int_default        = API_DISCOVERY_DISABLED,
                .value_traits       = config_value_traits_feature_enablement,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = API_DISCOVERY_FEATURE_GLOBAL_DISABLE,
                .desc               = "Global level disable WAF API Discovery",
                .details            = "if Zscaler global customer and tenant have WAF API Discovery enabled = Enable, else Disable \n"
                                      "1: Global Disable WAF API Discovery \n"
                                      "0: Global Enable  WAF API Discovery \n"
                                      "default: 0 ",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = API_DISCOVERY_DISABLED,
                .int_range_hi       = API_DISCOVERY_ENABLED,
                .int_default        = API_DISCOVERY_GLOBAL_DISABLE,
                .value_traits       = config_value_traits_systemwide,
                .feature_group      = FEATURE_GROUP_APP_PROTECTION
        },
        {
                .key                = CONNECTOR_DNS_CHECK_USE_STATIC_SERVER,
                .desc               = "enable/disable dns check honouring static server config",
                .details            = "Order of check: component id, component group id, customer gid, global\n"
                                      "default: 0 (disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_MIN,
                .int_range_hi       = CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_MAX,
                .int_default        = CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_DEFAULT,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_DNS_STRICT_CHECK
        },
        {
                .key                = ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .desc               = "when enabled allows adding into transaction logs fohh mconn track performance statistics",
                .details            = "0: no extra statistics is added into transaction logs on public brokers\n"
                           "1: adds level 1 fohh mconn tx_peer_rx_data_hist track performance statistics into transaction\n"
                           "2: adds level 2 fohh mconn rx_diff_rx_data_hist track performance statistics into transaction\n"
                           "3: adds level 3 fohh mconn tx_peer_rx_data_max  track performance statistics into transaction\n"
                           "default: 0 - no extra statistics is added into transaction logs",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_range_hi       = HIGH_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_default        = ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                .value_traits       = config_value_traits_normal,
                .feature_group      = FEATURE_GROUP_MCONN_TRACK_PERF_STATS
        },
        {
                .key                = CONFIG_FEATURE_ASSISTANT_FPROXY,
                .feature_group      = FEATURE_GROUP_AUTH_SNI,
                .desc               = "Feature Knob for app connector forward proxy",
                .details            = "Enable/Disable localhost forward proxy on app connector, default port 9010\n"
                                      "default: 0 (disabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = CONFIG_FEATURE_ASSISTANT_FPROXY_DEFAULT_STATUS
        },
        {
                .key                = CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE,
                .feature_group      = FEATURE_GROUP_ASST_DATABROKER_RESILIENCE,
                .desc               = "App connector data path resiliency using two hop through control broker",
                .details            = "1: two hop feature is enabled at connector \n"
                                      "0: two hop feature is is disabled at connector \n"
                                      "default: 0 (i.e. two hop is disabled at connector)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
                .int_range_lo       = MIN_CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE,
                .int_range_hi       = MAX_CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE,
                .int_default        = DEFAULT_ASST_DATABROKER_RESILIENCE
        },
        {
                .key                = NETWORK_PRESENCE_FEATURE,
                .desc               = "enable np feature",
                .details            = "0: np is not enabled\n"
                                      "1: np is enabled\n"
                                      "Order of check: connector_gid, connector_group_gid, customer_gid, global\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp| config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_FEATURE_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = NETWORK_PRESENCE_HARD_DISABLED,
                .desc               = "when enabled disables np",
                .details            = "0: np hard disablement is off\n"
                                      "1: np hard disablement is on\n"
                                      "Order of check: global\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_HARD_DISABLED_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_systemwide,
        },
        {
                .key                = NETWORK_PRESENCE_BGP_STATS_ENABLE,
                .desc               = "enable np bgp stats monitoring",
                .details            = "0: np bgp stats monitoring is not enabled\n"
                                      "1: np bgp stats monitoring is enabled\n"
                                      "Order of check: connector_gid, customer_gid, global\n"
                                      "default: 1",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_BGP_STATS_ENABLE_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
                .key                = NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                .desc               = "enable np redundancy feature",
                .details            = "0: np redundancy is not enabled\n"
                                      "1: np redundancy is enabled\n"
                                      "Order of check: connector_gid, connector_group_gid, customer_gid, global\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp| config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = 0,
                .int_range_hi       = 1,
                .int_default        = NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
                .value_traits       = config_value_traits_feature_enablement,
        },
        {
            .key                = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "set app connector allocator libevent output queue enable",
            .details            = "Enable/Disable app connector output queue length adjustment based on memory used by libevent allocator\n"
                                  "default: 0 (disabled)",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_feature_enablement,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_MIN,
            .int_range_hi       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_MAX,
            .int_default        = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_DEFAULT
        },
        {
            .key                = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "set app connector allocator libevent output queue length min bytes",
            .details            = "set app connector allocator libevent output queue length min bytes\n"
                                  "set minimum output queue length to the app server\n"
                                  "default: 128KB ",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_normal,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MIN,
            .int_range_hi       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MAX,
            .int_default        = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_DEFAULT
        },
        {
            .key                = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "app connector allocator libevent output queue length max bytes",
            .details            = "set app connector allocator libevent output queue length max bytes\n"
                                  "set maximum output queue length to the app server\n"
                                  "default: 1MB ",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_normal,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MIN,
            .int_range_hi       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MAX,
            .int_default        = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT
        },
        {
            .key                = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "set app connector allocator libevent output queue length sys mem max percent",
            .details            = "set app connector allocator libevent output queue length sys mem max percent\n"
                                  "set parameter of max system memory percent, used by allocator libevent for adjusting output queue length \n"
                                  "default: 20 percent",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_normal,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_MIN,
            .int_range_hi       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_MAX,
            .int_default        = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_DEFAULT
        },
        {
            .key                = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "set app connector allocator libevent output queue length sys mem start threshold percent",
            .details            = "set app connector allocator libevent output queue length sys mem start threshold percent\n"
                                  "Start adjustment of output queue length to the app server, \n"
                                  "when memory used by allocator libevent reaches this percentage threshold value \n"
                                  "out of max value parametrized by allocator_libevent_out_queue.sys_mem_max_percent \n"
                                  "default: 50 percent",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_normal,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_MIN,
            .int_range_hi       = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_MAX,
            .int_default        = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_DEFAULT
        },
        {
                .key                = ASSISTANT_DOUBLE_HOP_TIMEOUT,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "max time for connector to control broker double hop connections until timeout for data flow",
                .details            = "default: 30 sec. After this timeout AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER\n"
                                      "error is thrown",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_inst_grp |config_target_gid_type_global,
                .int_range_lo       = LOW_ASST_DOUBLE_HOP_TIMEOUT,
                .int_range_hi       = HIGH_ASST_DOUBLE_HOP_TIMEOUT,
                .int_default        = DEFAULT_ASST_DOUBLE_HOP_TIMEOUT
        },
        {
                .key                = ASSISTANT_DOUBLE_HOP_SWITCH_TIMEOUT,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "max time for which connector waits for data broker connection to come up before switching to double hop",
                .details            = "default: 20 sec. After this timeout assistant switches to double hop",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_inst_grp |config_target_gid_type_global,
                .int_range_lo       = LOW_ASST_DOUBLE_HOP_SWITCH_TIMEOUT,
                .int_range_hi       = HIGH_ASST_DOUBLE_HOP_SWITCH_TIMEOUT,
                .int_default        = DEFAULT_ASST_DOUBLE_HOP_SWITCH_TIMEOUT
        },
        {
                .key                = CONFIG_FEATURE_CONN_QBR_INSIGHTS_FEATURE,
                .feature_group      = FEATURE_GROUP_QBR,
                .desc               = "When enabled, new fields for QBR report will be made available in the transaction logs",
                .details            = "0: feature is disabled\n"
                                      "1: feature is enabled\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MIN,
                .int_range_hi       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MAX,
                .int_default        = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT
        },
        {
                .key                = ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "Data Connection IDLE expiry time",
                .details            = "App Connector will termiantes IDLE Data Connection based on this time\n"
                                      "default: 24 hours",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = MIN_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S,
                .int_range_hi       = MAX_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S,
                .int_default        = DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S
        },
        {
                .key                = ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "Data Connection IDLE expiry time Disable Knob",
                .details            = "Disable the behaviour of App Connector to termiantes IDLE Data Connection upon idle expiry\n"
                                      "default: 0 (behaviour enabled by default, this knob is to disable the behaviour)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = MIN_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE,
                .int_range_hi       = MAX_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE,
                .int_default        = DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE
        },
        {
                .key                = FOHH_FLOW_CONTROL_ENHANCEMENTS,
                .feature_group      = FEATURE_GROUP_FOHH_FLOW_CONTROL_ENHANCEMENTS,
                .desc               = "Enable Flow control enhancements",
                .details            = "Enable  the flow control enhancements\n"
                                      "default: 0 (behaviour disabled by default)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MIN,
                .int_range_hi       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MAX,
                .int_default        = DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS
        },
        {
                .key                = ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE,
                .feature_group      = FEATURE_GROUP_ASST_UDP_HEALTH_TIMEOUT_FAILURE,
                .desc               = "Report health check failure in case of udp timeout",
                .details            = "1: feature is enabled at connector \n"
                                      "0: feature is disabled at connector \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
                .int_range_lo       = MIN_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE,
                .int_range_hi       = MAX_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE,
                .int_default        = DEFAULT_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE
        },
        {
                .key                = CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES,
                .feature_group      = FEATURE_GROUP_MCONN_WINDOW_UPDATES,
                .desc               = "When enabled, Mconn window updates are batched ",
                .details            = "0: feature is disabled\n"
                                      "1: feature is enabled\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_MIN,
                .int_range_hi       = CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_MAX,
                .int_default        = CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_DEFAULT
        },
        {
                .key                = CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT,
                .feature_group      = FEATURE_GROUP_APP_RTT,
                .desc               = "When enabled, APP RTT uses synchronous status messages ",
                .details            = "0: feature is disabled\n"
                                      "1: feature is enabled\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_MIN,
                .int_range_hi       = CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_MAX,
                .int_default        = CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_DEFAULT
        },
        {
                .key                = CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE,
                .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
                .desc               = "When enabled, Fohh pipeline latency tracing is active ",
                .details            = "0: feature is disabled\n"
                                      "1: feature is enabled\n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_MIN,
                .int_range_hi       = CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_MAX,
                .int_default        = CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT
        },
        {
            .key                = ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S,
            .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
            .desc               = "Configure fin expire timeout in seconds",
            .details            = "After receive fin, mtunnel will expire after specified time if it is still alive\n"
                                  "default: 5min",
            .val_type           = config_type_int,
            .value_traits       = config_value_traits_normal,
            .component_types    = config_component_appc,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
            .int_range_lo       = LOW_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S,
            .int_range_hi       = HIGH_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S,
            .int_default        = ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT
        },
        {
                .key                = ASSISTANT_SARGE_UPGRADE_ENABLE,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "Enable automatic upgrade of Assistant sarge",
                .details            = "1: feature is enabled\n"
                                      "0: feature is disabled \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = ASSISTANT_SARGE_UPGRADE_ENABLE_MIN,
                .int_range_hi       = ASSISTANT_SARGE_UPGRADE_ENABLE_MAX,
                .int_default        = DEFAULT_ASSISTANT_SARGE_UPGRADE_ENABLE
        },
        {
                .key                = AUTOMATIC_OS_UPGRADE_ENABLE,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "Enable automatic upgrade of OS",
                .details            = "1: feature is enabled\n"
                                      "0: feature is disabled \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = AUTOMATIC_OS_UPGRADE_ENABLE_MIN,
                .int_range_hi       = AUTOMATIC_OS_UPGRADE_ENABLE_MAX,
                .int_default        = DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE
        },
        {
                .key                = AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "Enable automatic upgrade of entire OS",
                .details            = "1: feature is enabled\n"
                                      "0: feature is disabled \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = AUTOMATIC_FULL_OS_UPGRADE_MIN,
                .int_range_hi       = AUTOMATIC_FULL_OS_UPGRADE_MAX,
                .int_default        = DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE
        },
        {
                .key                = SARGE_BACKUP_VERSION_ENABLE,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "Enable/disable backup sarge version feature for sarge",
                .details            = "1: feature is enabled\n"
                                      "0: feature is disabled \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = SARGE_BACKUP_VERSION_ENABLE_MIN,
                .int_range_hi       = SARGE_BACKUP_VERSION_ENABLE_MAX,
                .int_default        = DEFAULT_SARGE_BACKUP_VERSION_ENABLE
        },
        {
                .key                = ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "UDP server inactivity timeout",
                .details            = "App Connector will trigger termination of mtunnel chain \n"
                                      "default: 60 seconds",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = MIN_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                .int_range_hi       = MAX_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                .int_default        = DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S
        },
        {
                .key                = ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_CONFIG,
                .desc               = "UDP server inactivity fast timeout",
                .details            = "App Connector will trigger termination of mtunnel chain \n"
                                      "default: 2 seconds",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_normal,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = MIN_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                .int_range_hi       = MAX_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                .int_default        = DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S
        },
        {
                .key                = ASSISTANT_OAUTH_ENROLL_DISABLE,
                .feature_group      = FEATURE_GROUP_SARGE,
                .desc               = "Enable enrollment via OAuth service",
                .details            = "1: OAuth Enrollment disabled\n"
                                      "0: OAuth Enrollment enabled \n"
                                      "default: 0 (Oauth Enrollment Enabled)",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = ASSISTANT_OAUTH_ENROLL_DISABLE_MIN,
                .int_range_hi       = ASSISTANT_OAUTH_ENROLL_DISABLE_MAX,
                .int_default        = DEFAULT_ASSISTANT_OAUTH_ENROLL_DISABLE
        },
};

int assistant_config_ovd_desc_register_all() {

        int desc_count = sizeof(assistant_config_override_descriptions) / sizeof(struct zpath_config_override_desc);

        int res = ZPN_RESULT_NO_ERROR;
        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&assistant_config_override_descriptions[i]);
                if (res) {
                        break;
                }
        }

        return res;
}
