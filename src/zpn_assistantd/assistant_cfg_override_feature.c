/*
 * assistant_cfg_override_feature.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 *
 */

#include "zpath_lib/zpath_config_override.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "admin_probe/admin_probe_public.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn_assistantd/assistant_cfg_override_feature.h"
#include "zpn_waf/zpn_waf_config_override.h"
#include "zpn_pdp/zpn_pdp_lib.h"
#include "zhealth/zhealth.h"
#include "zvm/zvm.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "np_lib/np.h"
#include "zpath_lib/zpath_oauth_utils.h"

static struct {
    int is_keepalive_to_app_server_enabled;
}state;

struct zhealth* asst_health_zh;

static uint64_t adp_protocol_override = 0;

/*
 * keepalive feature will be enabled if ANY of the situations below is met:
 * 1. customer is enabling keepalive at customer level
 * 2. customer is enabling keepalive at connector group level
 * 3. customer is enabling keeplive at connector level.
 *
 * The keepalive config will be read every 1 second triggered from the health thread.
 *
 */
static void
assistant_cfg_override_feature_process_is_to_app_server_keepalive_enabled_update()
{
    int     i;
    int64_t config_value;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(CONNECTOR_KEEPALIVE_FEATURE,
                                                            &config_value,
                                                            DEFAULT_CONNECTOR_KEEPALIVE_ENABLED,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            state.is_keepalive_to_app_server_enabled = 1;
            return;
        }
    }

    state.is_keepalive_to_app_server_enabled = 0;
}

static void
assistant_cfg_override_feature_process_ad_protection_protocol_override_config()
{
    uint64_t protocol_override = 0;

    for (int i = 0; i < global_assistant.grp_gids_len; i++) {
        if (zpn_is_waf_enabled_on_assistant(global_assistant.gid, global_assistant.grp_gids[i])
            && zpn_ad_protection_feature_enabled()) {
            struct ad_proto_bits bitmask = {0};
            if (ZPATH_RESULT_NO_ERROR == zpn_app_inspection_override_bitmask(global_assistant.gid, global_assistant.grp_gids[i], &bitmask)) {
                if(bitmask.dcerpc_bit)
                    protocol_override |= (1 << PDP_PROTO_DCERPC);
                if(bitmask.ldap_bit)
                    protocol_override |= (1 << PDP_PROTO_LDAP);
                if(bitmask.smb_bit)
                    protocol_override |= (1 << PDP_PROTO_SMB);
                if(bitmask.krb_bit)
                    protocol_override |= (1 << PDP_PROTO_KRB);

                if (adp_protocol_override != protocol_override) {
                    ASSISTANT_DEBUG_CFG_OVERRIDE("Connector config override %s configuration updated old 0x%lx new 0x%lx",
                            AD_PROTOCOL_OVERRIDE_CONFIG, (long)adp_protocol_override, (long)protocol_override);
                    adp_protocol_override = protocol_override;
                }
            }
            return;
        }

    }
}

int
assistant_cfg_override_feature_get_tcp_keepalive_enable_from_app_segment()
{
    int64_t config_value;

    config_value = zpath_config_override_get_config_int(CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT,
                                                        &config_value,
                                                        DEFAULT_CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    if (config_value == DEFAULT_CONNECTOR_TCP_KEEP_ALIVE_FROM_APP_SEGMENT) {
        return 0;
    } else {
        return 1;
    }
}

int
assistant_cfg_override_feature_get_is_to_app_server_keepalive_enabled()
{
    return state.is_keepalive_to_app_server_enabled;
}

uint64_t
assistant_cfg_override_feature_get_ad_protection_protocol_override()
{
    return adp_protocol_override;
}

int
assistant_cfg_override_get_sample_size_for_avg_rtt()
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {

        config_value = zpath_config_override_get_config_int(CONNECTOR_SAMPLES_FOR_AVG_RTT,
                                                            &config_value,
                                                            CONNECTOR_SAMPLES_FOR_AVG_RTT_DEFAULT,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);

        if (config_value > 0 && config_value <= ASSISTANT_TARGET_MAX_AVG_RTT_SAMPLES &&
            config_value != CONNECTOR_SAMPLES_FOR_AVG_RTT_DEFAULT) {
            return config_value;
        }
    }
    return CONNECTOR_SAMPLES_FOR_AVG_RTT_DEFAULT;
}

static void assistant_cfg_override_feature_sarge_and_os_overrides_config()
{
    int i;
    int64_t config_value;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_SARGE_UPGRADE_ENABLE,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_SARGE_UPGRADE_ENABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value != global_assistant.sarge_upgrade_feature_flag) {
            global_assistant.sarge_upgrade_feature_flag = config_value;
            /* Write into sarge_os_upgrade_cfg.json file*/
            zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(global_assistant.sarge_upgrade_feature_flag);
            break;
        }
    }

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(AUTOMATIC_OS_UPGRADE_ENABLE,
                                                            &config_value,
                                                            DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);

        if (config_value != global_assistant.os_upgrade_feature_flag) {
            global_assistant.os_upgrade_feature_flag = config_value;
            /* Write into sarge_os_upgrade_cfg.json file*/
            zpath_upgrade_set_os_upgrade_feature_flag_cfg(global_assistant.os_upgrade_feature_flag);
            break;
        }
    }

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(SARGE_BACKUP_VERSION_ENABLE,
                                                            &config_value,
                                                            DEFAULT_SARGE_BACKUP_VERSION_ENABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value != global_assistant.sarge_backup_version_feature_flag) {
            global_assistant.sarge_backup_version_feature_flag = config_value;
            /* Write into sarge_os_upgrade_cfg.json file*/
            zpath_upgrade_set_sarge_backup_version_feature_flag(global_assistant.sarge_backup_version_feature_flag);
            break;
        }
    }

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                            &config_value,
                                                            DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value != global_assistant.full_os_upgrade_feature_flag) {
            global_assistant.full_os_upgrade_feature_flag = config_value;
            /* Write into sarge_os_upgrade_cfg.json file*/
            zpath_upgrade_set_full_os_upgrade_version_feature_flag(global_assistant.full_os_upgrade_feature_flag);
            break;
        }
    }
}

static void assistant_cfg_override_feature_process_udp_timeout_failure()
{
    int     i;
    static int64_t previous_config_value = ZHEALTH_UDP_TIMEOUT_FAIL_DISABLED;
    int64_t config_value;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_UDP_HEALTH_TIMEOUT_FAILURE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            ZPATH_GID_GET_CUSTOMER_GID(global_assistant.gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value != previous_config_value) {
            if (config_value){
                zhealth_set_udp_timeout_failure_flag(asst_health_zh, ZHEALTH_UDP_TIMEOUT_FAIL_ENABLED);
            } else {
                zhealth_set_udp_timeout_failure_flag(asst_health_zh, ZHEALTH_UDP_TIMEOUT_FAIL_DISABLED);
            }
            global_assistant.udp_health_timeout_failure_flag_enabled = config_value;
            previous_config_value = config_value;
            return;
        }
    }
}

static void assistant_cfg_override_feature_oauth_enable()
{
    int i;
    static int64_t previous_config_value = DEFAULT_ASSISTANT_OAUTH_ENROLL_DISABLE;
    int64_t config_value;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_OAUTH_ENROLL_DISABLE,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_OAUTH_ENROLL_DISABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            ZPATH_GID_GET_CUSTOMER_GID(global_assistant.gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value != previous_config_value) {
            if (config_value) {
                FILE *fp = fopen(OAUTH_ENROLL_DISABLE_FLAG, "w");
                if (fp == NULL) {
                    ASSISTANT_LOG(AL_ERROR, "[OAuth] Error creating OAuth flag file %s - %s",
                                             OAUTH_ENROLL_DISABLE_FLAG,
                                             strerror(errno));
                    return;
                }
                fclose(fp);
                ASSISTANT_LOG(AL_NOTICE, "[OAuth] OAuth Enrollment Disabled. OAuth is the Preferred way of enrollment. Alternatively, you need to copy Provision key to the machine and restart service.");
            } else {
                if (unlink(OAUTH_ENROLL_DISABLE_FLAG) != 0) {
                    if (errno != ENOENT) {
                        ASSISTANT_LOG(AL_ERROR, "[OAuth] Error deleting OAuth flag file: %s - %s",
                                                 OAUTH_ENROLL_DISABLE_FLAG,
                                                 strerror(errno));
                    }
                }
                ASSISTANT_LOG(AL_NOTICE, "[OAuth] Oauth Enrollment Enabled");
            }
            previous_config_value = config_value;
            return;
        }
    }
}

void
assistant_cfg_override_feature_process()
{
    assistant_cfg_override_feature_process_is_to_app_server_keepalive_enabled_update();
    assistant_cfg_override_feature_process_ad_protection_protocol_override_config();
    assistant_cfg_override_feature_process_udp_timeout_failure();
    assistant_cfg_override_feature_oauth_enable();
    if (zvm_vm_type_is_zscaler_rh_image()) {
        assistant_cfg_override_feature_sarge_and_os_overrides_config();
    }
}

int assistant_cfg_override_feature_is_connector_admin_probe_enabled(enum admin_probe_task_type type)
{
    int64_t config_value = 0;

    /*check overall feature flag first, if its enabled, return immediately*/
    config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_ALL,
                                                        &config_value,
                                                        DEFAULT_CONNECTOR_ADMIN_PROBE_ALL_ENABLED,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    if (config_value) {
        return 1;
    }

    switch (type) {
        case admin_probe_task_type_restart_process: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_PROCESS,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_PROCESS_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            break;
        }

        case admin_probe_task_type_restart_system: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_RESTART_SYSTEM,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_RESTART_SYSTEM_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        }

        case admin_probe_task_type_dns: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_DNS,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_DNS_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        }

        case admin_probe_task_type_icmp: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_ICMP,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_ICMP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        }

        case admin_probe_task_type_tcp: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_TCP,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_TCP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        }

        case admin_probe_task_type_tcpdump: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_TCPDUMP,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_TCPDUMP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        }

        case admin_probe_task_type_ip_route:
        case admin_probe_task_type_ip_interfaces:
        case admin_probe_task_type_ip_bgp:
        case admin_probe_task_type_ip_bgp_neighbors:
        case admin_probe_task_type_ip_bgp_summary:
        case admin_probe_task_type_ip_clear_bgp:
        case admin_probe_task_type_bgp_running_config:
        case admin_probe_task_type_bgp_failed_config:
        case admin_probe_task_type_bgp_generated_config:
        case admin_probe_task_type_bgp_config_validate:
        case admin_probe_task_type_bgp_config_status:
        case admin_probe_task_type_bgp_config_status_details:
        case admin_probe_task_type_bgp_get_logs:
        case admin_probe_task_type_stop_bgp:
        case admin_probe_task_type_start_bgp:
        case admin_probe_task_type_restart_bgp:
        case admin_probe_task_type_status_bgp:
        case admin_probe_task_type_reload_bgp: {
            config_value = zpath_config_override_get_config_int(CONNECTOR_ADMIN_PROBE_FEATURE_FRR_CMDS,
                                                                &config_value,
                                                                DEFAULT_CONNECTOR_ADMIN_PROBE_FRR_CMDS_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            if (config_value) {
                // Make sure np redundancy feature is enabled to allow commands related to FRR.
                if (np_is_redundancy_feature_enabled(global_assistant.customer_gid)) {
                    config_value = 1;
                } else {
                    config_value = 0;
                }
            }
            break;
        }

        default:
            break;

    }

    return config_value?1:0;
}


int
assistant_cfg_override_zcdns_rcode_retry_enables()
{
    int     i;
    int64_t config_value;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE,
                                                            &config_value,
                                                            DEFAULT_CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.connector.zcdns.disable_err_rcode_retry is detected to be enabled");
            return 1;
        }
    }

    return DEFAULT_CONNECTOR_ZCDNS_NO_RETRY_ON_ERROR_RCODE;
}


int
assistant_cfg_override_is_fc_reset_recover_enabled()
{
    int     i;
    int64_t config_value = DEFAULT_FOHH_ENABLE_FC_RESET_RECOVER;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(FOHH_ENABLE_FC_RESET_RECOVER,
                                                            &config_value,
                                                            DEFAULT_FOHH_ENABLE_FC_RESET_RECOVER,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.fohh.enable.flow_control.recover is detected to be enabled");
            return 1;
        }
    }

    return DEFAULT_FOHH_ENABLE_FC_RESET_RECOVER;
}


int64_t
assistant_cfg_override_get_fc_reset_recover_interval_s()
{
    int     i;
    int64_t config_value = DEFAULT_FOHH_FC_RESET_RECOVER_INTERVAL_S;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(FOHH_FC_RESET_RECOVER_INTERVAL_S,
                                                            &config_value,
                                                            DEFAULT_FOHH_FC_RESET_RECOVER_INTERVAL_S,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    }

    return config_value;
}

int64_t
assistant_cfg_override_get_fc_reset_recover_threshold_times_hundred()
{
    int     i;
    int64_t config_value = DEFAULT_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED,
                                                            &config_value,
                                                            DEFAULT_FOHH_FC_CONTINUOUS_THRESHOLD_PERCENT_TIMES_HUNDRED,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    }

    return config_value;
}

int
assistant_cfg_override_is_dns_check_use_static_server_config_enabled()
{
    int64_t config_value = CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_DEFAULT;

    config_value = zpath_config_override_get_config_int(CONNECTOR_DNS_CHECK_USE_STATIC_SERVER,
                                                        &config_value,
                                                        CONNECTOR_DNS_CHECK_USE_STATIC_SERVER_DEFAULT,
                                                        global_assistant.gid,
                                                        global_assistant.customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    if (config_value) {
        return 1;
    }

    return 0;
}

int64_t
assistant_cfg_override_disable_tx_app_route_reg()
{
    int     i;
    int64_t config_value = ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER_DEFAULT_VALUE;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER,
                                                            &config_value,
                                                            ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER_DEFAULT_VALUE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.assistant.disable.public_broker.tx_route_reg is detected to be enabled");
            return 1;
        }
    }

    return ASSISTANT_DISABLE_TX_ROUTE_REG_TO_PUB_BROKER_DEFAULT_VALUE;
}

int
assistant_cfg_override_is_idle_data_conn_timeout_config_disabled()
{
    int64_t config_value = DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE;

    for (int i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_DISABLE,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.assistant.idle_data_conn_timeout.disable is detected to be disabled");
            return 1;
        }
    }

    return 0;
}

int64_t
assistant_cfg_override_get_idle_data_connection_timeout_s()
{
    int     i;
    int64_t config_value = DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.assistant.idle_data_conn_timeout_s is detected as %"PRId64" ", config_value);
            return config_value;
        }
    }

    return DEFAULT_ASSISTANT_IDLE_DATA_CONN_TIMEOUT_S;
}

int64_t
assistant_cfg_override_get_udp_server_inactivity_timeout_s()
{
    int     i;
    int64_t config_value = DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.assistant.udp_server_inactivity_timeout_s is detected as %"PRId64" ", config_value);
            return config_value;
        }
    }

    return DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_TIMEOUT_S;
}

int64_t
assistant_cfg_override_get_udp_server_inactivity_fast_timeout_s()
{
    int     i;
    int64_t config_value = DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S;

    for (i = 0; i < global_assistant.grp_gids_len; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S,
                                                            &config_value,
                                                            DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S,
                                                            global_assistant.gid,
                                                            global_assistant.grp_gids[i],
                                                            global_assistant.customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) {
            ASSISTANT_DEBUG_CFG_OVERRIDE("config.feature.assistant.udp_server_inactivity_fast_timeout_s is detected as %"PRId64" ", config_value);
            return config_value;
        }
    }

    return DEFAULT_ASSISTANT_UDP_SERVER_INACTIVITY_FAST_TIMEOUT_S;
}
