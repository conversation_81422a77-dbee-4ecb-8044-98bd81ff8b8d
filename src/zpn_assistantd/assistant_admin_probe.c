/*
 * assistant_admin_probe.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#include "zpn_assistantd/assistant_admin_probe.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_cfg_override_feature.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_state.h"
#include "admin_probe/admin_probe.h"
#include "admin_probe/admin_probe_public.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/zpn_assistant_health.h"
#include "zthread/zthread.h"
#include "admin_probe/admin_probe_stats.h"
#include "np_lib/np_frr_utils.h"
#include "np_connector/np_connector_bgp.h"
#include "np_lib/np.h"
#include "np_lib/np_bgp.h"

#ifdef __linux__
#include <linux/reboot.h>
#include <sys/syscall.h>
#include <sys/reboot.h>
#include <unistd.h>
#else
#endif

#include "zpath_lib/zpath_debug.h"

/*once admin_probe receive a restart request, it restart after ADMIN_PROBE_RESTART_TIME_S*/
#define ADMIN_PROBE_RESTART_TIME_S 30

static int g_admin_probe_initialized = 0;


void assistant_admin_probe_restart_evaluable()
{
#ifdef __linux__
    static int restart_process_counter_s = ADMIN_PROBE_RESTART_TIME_S;
    static int restart_system_counter_s = ADMIN_PROBE_RESTART_TIME_S;

    if (assistant_state_is_admin_probe_pause_for_restart_process()) {
        if (0 == (restart_process_counter_s%10)) {
            ASSISTANT_LOG(AL_NOTICE, "Connector will restart the process in %d sec due to admin probe request", restart_process_counter_s);
        }
        restart_process_counter_s--;
        if (restart_process_counter_s <= 0) {

            if (global_assistant.broker_control) {
                zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                         fohh_connection_incarnation(global_assistant.broker_control),
                                                         global_assistant.gid,
                                                         ASST_RSTR_SESS_CMD);
            } else {
                ASSISTANT_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                          assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_SESS_CMD);
            }

            ASSISTANT_LOG(AL_NOTICE, "Connector restarting now for admin probe request");
            sleep(1);
            exit(0);
        }
    } else if (assistant_state_is_admin_probe_pause_for_restart_system()) {
        int res;
        /*not worry about sync(), if we do sync there will be some hang which is out of our control*/
        if (0 == (restart_system_counter_s%10)) {
            ASSISTANT_LOG(AL_NOTICE, "Connector will reboot the system in %d sec due to admin probe request", restart_system_counter_s);
        }
        restart_system_counter_s--;
        if (restart_system_counter_s <= 0) {

            if (global_assistant.broker_control) {
                zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                         fohh_connection_incarnation(global_assistant.broker_control),
                                                         global_assistant.gid,
                                                         ASST_RSTR_HOST_SESS_CMD);
            } else {
                ASSISTANT_LOG(AL_NOTICE, "Unable to send assistant(ID = %"PRId64", Name = %s) restart reason(%s) to broker because control connection is not established !",
                                        global_assistant.gid, assistant_state_get_configured_name(), ASST_RSTR_HOST_SESS_CMD);
            }

            ASSISTANT_LOG(AL_NOTICE, "System rebooting now for admin probe request");
            sleep(1);
            res = reboot(LINUX_REBOOT_CMD_RESTART);
            if (res != 0) {
                ASSISTANT_LOG(AL_NOTICE, "System reboot failed, will restart process now instead");
                sleep(1);
                exit(0);
            }
        }
    }
#else
    return;
#endif
}

int assistant_admin_probe_restart(enum restart_type type)
{
    if (assistant_state_is_paused()) {
        ASSISTANT_LOG(AL_NOTICE, "rx admin_probe request on restart, but connector is already in pause state and scheduled to restart soon");
        return ZPN_RESULT_NO_ERROR;
    }

#ifdef __linux__
    if (type == process_restart) {
        ASSISTANT_LOG(AL_NOTICE, "preparing for restarting process");
        assistant_state_set_admin_probe_pause_for_process_restart();
    } else if (type == system_restart) {

        if (!effective_asst_configured_capabilities[cap_sys_boot] || !permitted_asst_configured_capabilities[cap_sys_boot]) {
            ASSISTANT_LOG(AL_NOTICE, "This assistant has no capability to reboot the system, returning ");
            return ZPN_RESULT_ERR;
        } else {
            ASSISTANT_LOG(AL_NOTICE, "preparing for rebooting system");
            assistant_state_set_admin_probe_pause_for_system_restart();
        }
    } else {
        ASSISTANT_LOG(AL_NOTICE, "rx unknown restart type from admin_probe, returning");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
#else
    ASSISTANT_LOG(AL_ERROR, "Admin Probe restart not supportted on current platform, returning");
    return ZPN_RESULT_NOT_IMPLEMENTED;;
#endif


    return ZPN_RESULT_NO_ERROR;
}


int assistant_admin_probe_tx_task_update(void* status, int is_np_command_probe)
{
    int res;
    res = assistant_control_tx_command_probe_status(status, NULL, NULL, NULL, is_np_command_probe);
    if (res) {
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_async_http_upload_cb(struct fohh_http_client *client,
              enum fohh_http_client_request_status status,
              int http_status,
              struct evbuffer *result_body,
              void *void_cookie,
              int64_t int_cookie)
{
    ZPN_LOG(AL_ERROR, "async CB. request status = %s, Status = %d, Body length = %ld\n",
            fohh_http_client_request_status_str(status),
            http_status,
            result_body ? evbuffer_get_length(result_body) : 0);
    return 0;
}

int assistant_get_remote_host_for_s3_upload(char *remote_host, int size, int *no_proxy)
{
    if (remote_host) {
        snprintf(remote_host, size, "co2br.%s", zthread_get_cloud_name());
    }
    if (no_proxy) {
        *no_proxy = 0;
    }
    return 0;

}

static int zpn_np_prepare_frr_cmds(struct zpn_frr_cmds_execute_args *frr_arg, char *cmd,
                                         int size_cmd, int *result_to_file) {
    int res = 0;
    int config_mode = -1;

    // Default true
    *result_to_file = 1;

    switch (frr_arg->task_type) {
        case admin_probe_task_type_ip_route:
            res = zpn_np_prepare_show_ip_route_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_interfaces:
            res = zpn_np_prepare_show_ip_interface_cmd(frr_arg->params->interface, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp:
            res = zpn_np_prepare_show_ip_bgp_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp_neighbors:
            res = zpn_np_prepare_show_ip_bgp_neighbors_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp_summary:
            res = zpn_np_prepare_show_ip_bgp_summary_cmd(cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_clear_bgp:
            res = zpn_np_prepare_clear_ip_bgp_cmd(frr_arg->params->target, cmd, size_cmd);
            // file upload not required for ip clear bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_bgp_running_config:
            res = zpn_np_prepare_bgp_running_config_cmd(cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_config_validate:
            res = zpn_np_prepare_bgp_config_validate_cmd(frr_arg->params->config_type, cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_config_status_details:
            config_mode = np_connector_get_bgp_config_mode();
            if (config_mode == -1) {
                ZPN_LOG(AL_ERROR, "Invalid BGP config mode for np connector");
                return ZPN_RESULT_BAD_STATE;
            }
            res = zpn_np_prepare_bgp_config_validate_cmd(config_mode, cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_get_logs:
            res = zpn_np_prepare_bgp_get_logs_cmd(frr_arg->params->number, frr_arg->params->since_mins,
                                                     frr_arg->params->until_mins, cmd, size_cmd);
            break;
        case admin_probe_task_type_stop_bgp:
            res = zpn_np_prepare_stop_bgp_cmd(cmd, size_cmd);
            // file upload not required for stop bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_start_bgp:
            res = zpn_np_prepare_start_bgp_cmd(cmd, size_cmd);
            // file upload not required for start bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_restart_bgp:
            res = zpn_np_prepare_restart_bgp_cmd(cmd, size_cmd);
            // file upload not required for restart bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_status_bgp:
            res = zpn_np_prepare_status_bgp_cmd(cmd, size_cmd);
            break;
        case admin_probe_task_type_reload_bgp:
            break;
        default:
            res = ZPN_RESULT_NOT_FOUND;
            break;
    }
    return res;
}

int assistant_admin_probe_frr_cmds_execute(struct zpn_frr_cmds_execute_args *frr_arg, int *cmd_result) {
    int res = ZPN_RESULT_NO_ERROR;
    char cmd_to_execute[512] = {0};
    char *out_buf = NULL;
    char *filepath = NULL;
    int config_mode = -1;
    int is_config_valid = 0;

    if (frr_arg == NULL || cmd_result == NULL) {
        ZPN_LOG(AL_ERROR, "Invalid Input");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    filepath = frr_arg->file_path;

    if (frr_arg->task_type == admin_probe_task_type_bgp_failed_config) {
        res = np_frr_util_file_copy(NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED, filepath);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error copying generated frr config:%s to:%s for command for task_type:%d error:%s",
                                        NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED, filepath,
                                        frr_arg->task_type, zpath_result_string(res));
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_bgp_generated_config) {
        res = np_frr_util_file_copy(NP_BGP_FRR_CONFIG_FILENAME_GENERATED, filepath);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error copying generated frr config:%s to:%s, for command task_type:%d error:%s",
                                         NP_BGP_FRR_CONFIG_FILENAME_GENERATED, filepath,
                                        frr_arg->task_type, zpath_result_string(res));
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_reload_bgp) {
        res = np_bgp_connector_reload_frr_conf(frr_arg->params->is_force_reload);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error reloading frr config for command task_type:%d force_regen:%d error:%s",
                                        frr_arg->task_type, frr_arg->params->is_force_reload, zpath_result_string(res));
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_bgp_config_status) {

        config_mode = np_connector_get_bgp_config_mode();
        if (config_mode == -1) {
            ZPN_LOG(AL_ERROR, "Invalid BGP config mode");
            return ZPN_RESULT_BAD_STATE;
        }

        res = np_frr_test_config_status((config_mode == np_bgp_config_mode_generated)?
                                NP_BGP_FRR_CONFIG_FILENAME_GENERATED :
                                NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, &is_config_valid);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error validating frr config for mode:%d task_type:%d error:%s",
                                         config_mode, frr_arg->task_type, zpath_result_string(res));
            return res;
        }
        *cmd_result = is_config_valid ? 1 : 0;
    } else {
        int write_result_to_file = 0;

        res = zpn_np_prepare_frr_cmds(frr_arg, cmd_to_execute, sizeof(cmd_to_execute), &write_result_to_file);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to prepare command for task_type:%d error:%s", frr_arg->task_type, zpath_result_string(res));
            return res;
        }

        ZPN_LOG(AL_INFO, "Executing command:%s for task_type:%d", cmd_to_execute, frr_arg->task_type);

        res = np_frr_util_execute_command(cmd_to_execute, &out_buf);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to execute np probe command:%s error:%s", cmd_to_execute, zpath_result_string(res));
            np_frr_release_buf(out_buf);
            return res;
        }

        if (write_result_to_file) {
            if (out_buf) {
                res = np_frr_util_write_buffer_to_file(out_buf, strlen(out_buf), filepath);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Failed to write frr command:%s output to file:%s error:%s",
                                            cmd_to_execute, filepath, zpath_result_string(res));
                    np_frr_release_buf(out_buf);
                    return res;
                }
            } else {
                ZPN_LOG(AL_ERROR, "Command:%s executed, but no output.. Review the command/setup..", cmd_to_execute);
                return ZPN_RESULT_BAD_STATE;
            }
        }

        // command execution is success
        *cmd_result = 1;
        np_frr_release_buf(out_buf);
    }

    return res;
}


int assistant_admin_probe_init(struct wally *asst_wally, int64_t asst_gid, int64_t customer_gid,
                                int is_np_command_probe, struct argo_log_collection *event_log,
                                int is_zpath_config_override_inited)
{
    int res;

    int is_dev_env;

    if (g_admin_probe_initialized) {
        ZPN_LOG(AL_WARNING, "Admin probe already initialized");
        return ZPN_RESULT_NO_ERROR;
    }

    is_dev_env = assistant_state_is_dev_environment();

    ZPN_LOG(AL_INFO, "Initializing %s admin probe for Connector", is_np_command_probe? "NP": "ZPN");

    if (asst_wally == NULL) {
        ZPN_LOG(AL_ERROR, "Invalid assistant wally handle");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    res = admin_probe_init(asst_wally,
                           asst_gid,
                           assistant_state_get_configured_name(),
                           customer_gid,
                           admin_probe_app_type_connector,
                           assistant_admin_probe_tx_task_update,
                           assistant_cfg_override_feature_is_connector_admin_probe_enabled,
                           assistant_admin_probe_restart,
                           is_np_command_probe ? assistant_admin_probe_frr_cmds_execute : NULL,
                           assistant_state_get_current_time_cloud_s,
                           is_dev_env,
                           is_np_command_probe,
                           assistant_get_remote_host_for_s3_upload,
                           assistant_state_is_paused,
                           event_log,
                           is_zpath_config_override_inited);
    if (res) {
        ASSISTANT_LOG(AL_NOTICE, "assistant probe init failed");
        return ZPN_RESULT_ERR;
    }

    g_admin_probe_initialized = 1;

    return ZPN_RESULT_NO_ERROR;
}

int
assistant_admin_probe_stats_fill(void*     cookie,
                                 int       counter,
                                 void*     structure_data)
{
    struct admin_probe_stats*    out_data;

    (void)cookie;

    out_data = (struct admin_probe_stats*)structure_data;

    admin_probe_stats_fill(out_data);

    return ZPATH_RESULT_NO_ERROR;
}
