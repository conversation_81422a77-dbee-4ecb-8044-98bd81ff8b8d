

/*
 * zpn_assistant_mtunnel.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 *
 * FIXME: rename to assistant_data_mtunnel.c
 */
#include <sys/queue.h>
#include <openssl/rand.h>
#include <errno.h>
#include <unistd.h>
#include <netinet/tcp.h>
#include <event2/event.h>
#include <event2/listener.h>
#include <event2/bufferevent.h>
#include <event2/buffer.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include "zhealth/zhealth_probe_lib.h"
#include "zpath_lib/zpath_config_override.h"

#include "argo/argo_hash.h"
#include "argo/argo.h"
#include "zlibevent/zlibevent_bufferevent.h"
#include <event2/bufferevent_ssl.h>
#include <openssl/ssl.h>
#include <openssl/bio.h>
#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/err.h>
#include "fohh/fohh.h"

#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/zpn_assistant_health.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn_assistantd/assistant_app.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_rpc.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_features.h"
#include "fohh/fohh_private.h"
#include "zthread/zthread.h"
#include "zpn_assistantd/assistant_broker.h"
#include "zpn_assistantd/assistant_cfg_override_feature.h"
#include "zpn/zpn_debug.h"
#include "zpn/zpn_connector.h"
#include "zpn/zpn_connector_tun.h"
#include "zpn_assistantd/zpn_connector_inspect.h"
#include "zpn_assistantd/zpn_connector_webprobe_http_inspect.h"
#include "zpn/zpn_connector_ssl_helpers.h"
#include "zpn/zpn_mconn_icmp.h"
#include "zpn/zpn_mconn_icmpv6.h"
#include "zpn_zdx/zpn_zdx_lib.h"
#include "zpn_zdx/zpn_zdx_http.h"
#include "zpn/zpn_mconn_bufferevent.h"

#include "zhealth/zhealth_route_lookup.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_ncache.h"
#include "zpn_waf/zpn_waf_ssl.h"
#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_lib.h"
#include "zpn_waf/zpn_waf_debug.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn_waf/zpn_waf_profile_manager.h"
#include "zpn_waf/zpn_waf_config_override.h"
#include "zpn_assistantd/ast_waf_sess_mgr.h"
#include "zudp_conn/zudp_conn.h"
#include "zpn_waf/zpn_idps.h"
#include "zpn_assistantd/assistant_util.h"
#include <sys/ioctl.h>
#include "zpn_zdx/zpn_zdx_webprobe_cache_https_ptls.h"
#include "zpn/zpn_application_def.h"
#include "zpn_assistantd/assistant_dns.h"
#include "zpn/zpn_assistant_config_override.h"
#include "zpn/zpn_assistant_config_override_compiled.h"
#include "zpn_assistantd/zpn_assistant_two_hop_data_health.h"


#define _SSL_CTX_FROM_WAF_SESS_MNG_
#define FIN_FORWARD_DELAY_US        1000000

#define AST_DBG_MTUN_WAF ASSISTANT_DEBUG_MTUNNEL
/*we wait for 20s to free the mtunnel because we need some time for freeing ssl related stuff, we have sen crash before when not giving the extra time*/
#define FREE_MTUNNEL_TIMEOUT_US                 (20*1000000)
#define MAX_CONNECTOR_FREED_PER_ONE_ROUND  2500

#define IPV6_ADDR_LEN 16

#define MTUNNEL_DEC_ASYNC_COUNT(mtunnel) \
    if (mtunnel->async_count > 0) { \
        mtunnel->async_count--;    \
    } else { \
        mtunnel->async_count = 0; \
        ASSISTANT_LOG(AL_WARNING, "assitant_mtunnel: %s over sub CB triggering", \
                      mtunnel_id); \
    }


#define IS_TWO_HOP_MTUNNEL(mtunnel) (assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len) && \
                                    ((mtunnel)->g_brk != (mtunnel)->g_bfw))
typedef void (mtunnel_injection_f)(struct zpn_mconn_bufferevent *mconn_b, struct evbuffer* buf);

int assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn(const char* mtunnel_id, void *cookie);
int assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_and_webprobe_cache_http_inject_request_to_mconn(const char* mtunnel_id, void *cookie);
int assistant_mtunnel_inject_webprobe_cache_response_to_mconn(const char *key, const char* mtunnel_id, void *request_cookie, void *response_cookie);
int assistant_mtunnel_inject_webprobe_cache_https_response_to_mconn(const char *key, const char* mtunnel_id, void *request_cookie, void *response_cookie);
int assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn(const char* mtunnel_id, int cookie);
int assistant_mtunnel_zdx_webprobe_cache_https_stats_ssl_data_get(const char* mtunnel_id, int thread_id,
                                                                  void *response_cookie);
uint8_t zpn_assistant_mtunnel_get_debug_status(char *domain);
static int za_mtunnel_setup_broker_conn(struct zpn_assistant_mtunnel *mtunnel, int conn_type);

extern struct zpn_assistant_state global_assistant;

static const char* traffic_inspection_modes[] =
{
    [zpn_traffic_inspection_disabled]               = "traffic inspection disabled",
    [zpn_traffic_inspection_http]                   = "traffic inspection http only",
    [zpn_traffic_inspection_https]                  = "traffic inspection https",
    [zpn_traffic_inspection_http_double_encrypted]  = "traffic inspection http with double encryption",
    [zpn_traffic_inspection_https_double_encrypted] = "traffic inspection https with double encryption",
    [zpn_traffic_inspection_http_webprobe]          = "traffic inspection http webprobe",
    [zpn_traffic_inspection_ldap]                   = "traffic inspection ldap",
    [zpn_traffic_inspection_ldap_double_encrypted]  = "traffic inspection ldap with double encryption",
    [zpn_traffic_inspection_smb]                    = "traffic inspection smb",
    [zpn_traffic_inspection_smb_double_encrypted]   = "traffic inspection smb with double encryption",
    [zpn_traffic_inspection_krb]                    = "traffic inspection krb",
    [zpn_traffic_inspection_krb_double_encrypted]   = "traffic inspection krb with double encryption",
    [zpn_traffic_inspection_auto]                   = "traffic inspection auto",
    [zpn_traffic_inspection_auto_tls]               = "traffic inspection auto tls",
    [zpn_traffic_inspection_auto_double_encrypted]  = "traffic inspection auto with double encryption",
    [zpn_traffic_inspection_auto_tls_double_encrypted] = "traffic inspection auto tls with double encryption",
    [zpn_traffic_inspection_ptag]                   = "traffic inspection ptag",
    [zpn_traffic_inspection_ptag_double_encrypted]  = "traffic inspection ptag with double encryption"
};

/*
 * When we are done with a mtunnel, we do not free the mtunnel memory immediately.
 * Instead, we put the mtunnel onto the free_q queue, and the mtunnel memory will get freed up after 20 seconds.
 * the stats.frees indicated the total number of mtunnel memory we have freed.
 */
static
struct {
    pthread_mutex_t lock;
    struct zpn_assistant_mtunnel_head_tailq mt_list;
    struct {
        int64_t frees;
        int64_t free_queue_count;
    } stats;
} free_q;

/*
 * the state_lock is used when we want to write any data in the state.stats and state.audit
 * today we are using the state_lock for the mtunnel allocation as we are tracking mtunnel allocation stats.
 */
static struct {
    pthread_mutex_t state_lock;
    struct {
        int64_t total_allocations;
        int64_t peak_mtunnels;
        int64_t cur_mtunnels_in_mem;
    } stats;

    struct {
        int64_t peak_mtunnel_cloud_us;
    } audit;

    struct {
        int64_t max_time_elapsed_in_free_mtunnel_since_last_upload;
        int64_t max_time_elapsed_in_check_mtunnel_since_last_upload;
    } timer_cb_stats;
}state;

static struct assistant_data_mtunnel_stats {                    /* _ARGO: * object_definition */
    int64_t     tun_connect_double_encrypt_server_side;         /* _ARGO: integer */
    int64_t     tun_connect_inspect_server_side;                /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side_fail;    /* _ARGO: integer */
    int64_t     tun_connect_inspect_server_side_fail;           /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_client_side;         /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_client_side_fail;    /* _ARGO: integer */
    int64_t     tun_connect_inspect_client_side;                /* _ARGO: integer */
    int64_t     tun_connect_inspect_client_side_fail;           /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side1;        /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side_fail1;   /* _ARGO: integer */
    int64_t     server_side_bev_create;                         /* _ARGO: integer */
    int64_t     server_side_bev_create_fail;                    /* _ARGO: integer */

    int64_t     terminate_bev_free;                             /* _ARGO: integer */
    int64_t     process_event_bev_free_on_local_owner_fail;     /* _ARGO: integer */
    int64_t     process_event_bev_free_on_server_sock_close;    /* _ARGO: integer */
    int64_t     verify_and_connect_bev_free_on_connect_failure; /* _ARGO: integer */
    int64_t     zdx_injection_total_requested;                  /* _ARGO: integer */
    int64_t     zdx_injection_total_bytes_sent;                 /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_success;                 /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_fail_mtunnel_gone;       /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_fail_no_memory;          /* _ARGO: integer */

    int64_t     zdx_webprobe_http_requests;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_https_requests;                                 /* _ARGO: integer */
    int64_t     zdx_webprobe_https_requests_rejected_feature_disabled;       /* _ARGO: integer */

    //Webprobe inject to server stats
    int64_t     zdx_webprobe_client_injection_failed_to_queue_request;       /* _ARGO: integer */

    //Webprobe inject to zcc stats
    int64_t     zdx_webprobe_client_side_success;                            /* _ARGO: integer */
    int64_t     zdx_webprobe_client_side_fail;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone;    /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_failed_to_queue_response;      /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_already_injected_skipping;     /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_no_memory;       /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_bad_argument;    /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_serialize_err;        /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_success;              /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_total_requested;           /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error;     /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone;         /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error;   /* _ARGO: integer */

    // Webprobe https inject to zcc stats
    int64_t     zdx_webprobe_https_client_side_success;                                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_side_fail;                                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_total_requested;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_failed_to_queue_response;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_already_injected_skipping;            /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_no_memory;              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_serialize_err;               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_success;                     /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_total_bytes_sent;                     /* _ARGO: integer */

    //Webprobe http inject to server stats
    int64_t     zdx_webprobe_http_server_injection_to_mconn_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_already_connected;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_no_memory;                /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data;              /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_success;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_http_server_side_bev_create;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_create_fail;                             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connect_init_fail;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_socket_init_fail;                        /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connect_event_cb_fail;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connected;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_fail_mtunnel_gone;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_free_connect_error;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_free_on_local_owner_fail;                /* _ARGO: integer */

    //Webprobe https inject to server stats
    int64_t     zdx_webprobe_https_server_injection_to_mconn_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_already_connected;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_no_memory;                /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data;              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_success;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_https_server_side_bev_create;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_create_fail;                             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connect_init_fail;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connect_event_cb_fail;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connected;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_fail_mtunnel_gone;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_free_connect_error;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_free_on_local_owner_fail;                /* _ARGO: integer */

    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_count;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error;   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_ssl_messages_max_exceeded;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes;          /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_get_ex_data_null_count;                              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_ctx_null;                                            /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_object_null_count;                                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_name_set_fail_count;                              /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus;      /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val; /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s;             /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus;        /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus;      /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val; /* _ARGO: integer */

    int64_t     tcp_bev_connected;                              /* _ARGO: integer */
    int64_t     tcp_bev_connect_error;                          /* _ARGO: integer */
    int64_t     mtls_bev_connected;                             /* _ARGO: integer */
    int64_t     mtls_bev_connect_error;                         /* _ARGO: integer */
    int64_t     mtls_ssl_created;                               /* _ARGO: integer */
    int64_t     mtls_ssl_free;                                  /* _ARGO: integer */

    int64_t     num_mtunnel_in_buckets_queue;                             /* _ARGO: integer */
    int64_t     num_mtunnel_in_reap_queue;                                /* _ARGO: integer */
    int64_t     num_mtunnel_in_free_queue;                                /* _ARGO: integer */
    int64_t     num_mtunnel_in_reap_state_but_not_moving_to_reap_queue;   /* _ARGO: integer */
    int64_t     num_mtunnel_in_reaping_queue_but_not_clean;               /* _ARGO: integer */

    int64_t     num_of_mtunnel_rejected_due_to_no_system_capacity;        /* _ARGO: integer */
    int64_t     mtunnel_mconn_track_perf_stats_level;                     /* _ARGO: integer */
    int64_t     mtunnel_mconn_track_perf_stats_send;                      /* _ARGO: integer */
    int64_t     mtunnel_mconn_track_perf_stats_send_error;                /* _ARGO: integer */
    int64_t     num_mtunnel_in_double_hop;                                /* _ARGO: integer */
    int64_t     broker_setup_timeout_not_double_hop_mtunnel;              /* _ARGO: integer */
    int64_t     num_double_hop_webprobe_http_connector_destroy;           /* _ARGO: integer */
    int64_t     num_double_hop_webprobe_https_connector_destroy;          /* _ARGO: integer */
    int64_t     num_double_hop_tun_connector_destroy;                     /* _ARGO: integer */
    int64_t     num_double_hop_inspect_connector_destroy;                 /* _ARGO: integer */

} stats;

enum zpn_zdx_webprobe_cache_https_stats_ssl_data_state {
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_initial,
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_write_client_start,
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_write_client_continue,
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_read_server_start,
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_read_server_continue,
    zpn_zdx_webprobe_cache_https_stats_ssl_data_state_max
};
struct zpn_zdx_webprobe_cache_https_stats_ssl_data {
    enum zpn_zdx_webprobe_cache_https_stats_ssl_data_state stats_ssl_data_state;
    uint64_t    ssl_stime;
    uint64_t    ssl_handshake_start_ts;
    uint64_t    ssl_handshake_stop_ts;
    uint8_t     ssl_message_pair_index_curr;
    char        ssl_version[ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_VERSION_STR_LEN_MAX];
    uint32_t    ssl_req_nbytes[ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_HANDSHAKE_MESSAGE_PAIR_MAX];
    uint32_t    ssl_resp_nbytes[ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_HANDSHAKE_MESSAGE_PAIR_MAX];
    int         ssl_flag_write_p;
    uint8_t     ssl_handshake_done;
};

#include "zpn_assistantd/zpn_assistant_mtunnel_compiled_c.h"

struct argo_structure_description*  assistant_data_mtunnel_stats_description;

//exist through out the lifetime.
static SSL_CTX *ssl_ctx_connector = NULL;
static SSL_CTX *ssl_ctx_mtls = NULL;
extern int zpn_zdx_webprobe_cache_https_stats_ssl_data_ex_index;

/*
 * @startuml
 * za_request_received : setup connection to the server app
 * za_server_conn_open : setup connection to data_broker
 * za_broker_conn_open : tx zpn_mtunnel_bind msg to data_broker
 *
 * [*] --> za_request_received : rx zpn_broker_request from control_broker
 * za_request_received --> za_server_conn_open  : connection with server app is done
 * za_server_conn_open --> za_broker_conn_open : connection with data_broker is done
 * za_broker_conn_open --> za_complete : data_broker ACKed the connection
 *
 * @enduml
 */
enum zpn_assistant_mtunnel_state {
    za_request_received = 0,
    za_server_conn_open,
    za_broker_conn_two_hop,
    za_broker_conn_open,
    za_complete,
    za_reaping,
    za_free
};

enum za_mtunnel_two_hop_state {
    za_two_hop_started = 0,
    za_control_brk_proxy_conn_done
};

#define MAX_MTUNNEL_DEBUG_STR 512

/*
 * The mtunnels known by this assistant.
 * g_aps -> gid of application server
 * path_decision -> flag of path decision taken at differnt legs in the system.
 */
struct zpn_assistant_mtunnel {
    /* Everyone needs a bucket list */
    TAILQ_ENTRY(zpn_assistant_mtunnel) bucket_list;
    TAILQ_ENTRY(zpn_assistant_mtunnel) broker_list;

    uint64_t mtunnel_id_hash;
    enum zpn_assistant_mtunnel_state state;
    uint64_t start_us;
    uint64_t start_time_in_cloud_us;
    uint64_t server_connection_setup_start_us;
    uint64_t server_connection_setup_end_us;

    /* The following fields mirror the broker_request: */
    char *mtunnel_id;
    int64_t g_brk;
    int64_t g_bfw;
    int64_t g_cst;
    int64_t g_app;
    int64_t g_aps;
    int64_t g_ast;
    int64_t g_dsp;
    int64_t g_app_grp;
    int64_t g_ast_grp;
    int64_t g_srv_grp;
    struct argo_inet brk_req_server_inet;
    int64_t brk_req_bfw_us;
    int64_t brk_req_dsp_us;
    int64_t brk_req_ast_rx_us;
    int64_t bind_tx_cloud_us;
    int64_t bind_ack_ast_rx_us;
    char brk_name[256];
    char bfw_name[256];
    char domain[256];
    char app_domain[MAX_APPLICATION_DOMAIN_LEN];
    char user_id_str[FOHH_MAX_NAMELEN];
    char mdbg_str[MAX_MTUNNEL_DEBUG_STR];

    struct argo_inet client_pub_ip;
    uint16_t client_port_he;
    uint16_t ip_protocol;
    enum zpn_mtunnel_type mtunnel_type;

    /* End of fields mirroring broker_request */

    /*
     * brk_req_server_inet will be equal to server_inet if connector chooses the same IP that dispatcher wished for.
     * But that is not always the case.
     */
    struct argo_inet server_inet;
    uint16_t server_port_he;
    int64_t  server_rtt_us;
    struct argo_inet assistant_inet;
    uint16_t assistant_port_he;

    int64_t rx_broker_label;

    struct zpn_assistant_broker_data *broker;
    int32_t broker_tag;

    char *err;

    struct zpn_mconn_bufferevent mconn_bufferevent_server;
    struct zpn_mconn_udp_bufferevent mconn_udp_server;
    struct zpn_mconn_icmp mconn_icmp_server;
    struct zpn_mconn_fohh_tlv mconn_fohh_tlv_broker;
    struct zpn_mconn_zrdt_tlv mconn_zrdt_tlv_broker;

    enum zpn_tlv_type tlv_type;

    pthread_mutex_t lock;
    int termination_started;

    struct zpn_trans_log log;

    int double_encrypt;
    int zpn_probe_type;
    struct zpn_connector *connector;
    int64_t delayed_fin_forward_us;              /* Delay forwarding FIN from broker to server */

    /* Temporarily store bev before it is assigned to local owner */
    struct bufferevent *bev_tmp;

    int64_t incarnation;

    int32_t brkreq_seq_num;

    uint64_t path_decision;
    int lss_encrypted_sock;

    SSL *lss_encrypted_ssl;

    /* Error state from openssl. This is a bit big, but worth it. */
    struct fohh_ssl_status lss_encrypted_ssl_status;

    enum za_mtunnel_two_hop_state two_hop_state;
    struct {
        /* WAF Flags indicators */
        unsigned waf_requested:1;
        unsigned waf_req_compl:1;
        unsigned waf_req_error:1;
        unsigned waf_disabled:1;
        unsigned waf_secure:1;
        unsigned waf_session_ready:1;

        /* Connect status flags */
        unsigned connect_requested:1;
        unsigned connect_success:1;
        unsigned connect_timeout:1;
        unsigned connect_eof:1;
        unsigned connect_error:1;
        unsigned connect_ssl_error:1;
        /* It is muted health (aka no-health report based application) if the server ip is not valid. */
        unsigned is_muted_health_app:1;
        /* did user broker send the broker request directly, bypassing the dispatcher? */
        unsigned dsp_bypassed :1;
        unsigned broker_bind_sent:1;
        unsigned broker_end_done:1;
        unsigned in_broker_list:1;
        unsigned ready:1;
        unsigned is_terminating_due_to_data_conn_lost:1;
        unsigned is_tcp_keepalive_enabled:1;
        unsigned is_terminating_due_to_conn_stuck:1;

        /* zdx_webprobe_cache connection indication, which is end terminated at connector  */
        unsigned zdx_webprobe_cache_connect_end_terminated:1;
        unsigned zdx_webprobe_cache_connect_requested:1;
        unsigned zdx_webprobe_cache_connect_success:1;
        unsigned zdx_webprobe_cache_connect_error:1;
        unsigned zdx_webprobe_cache_connect_ssl_requested:1;
        unsigned zdx_webprobe_cache_connect_ssl_success:1;
        unsigned zdx_webprobe_cache_connect_ssl_error:1;

        /* zdx_mtr connection indication, which is end terminated at connector  */
        unsigned zdx_mtr_connect_end_terminated:1;

        unsigned adp_enabled:1;
        unsigned ptag_mode:1;
        unsigned auto_app_protect_enabled:1;
        unsigned debug_flag:1;

        /* Double hop proxy flags */
        unsigned two_hop:1;
        unsigned two_hop_from_cache:1;
        unsigned two_hop_proxy_conn_init_done:1;
        unsigned two_hop_proxy_conn_mcons_set:1;
        unsigned two_hop_data_broker_conn_process_done:1;
        unsigned two_hop_proxy_conn_process_done:1;
        unsigned try_if_other_ip_reachable:1;

        /* Connector flags */
        int64_t connector_created;
    } flag;
    int async_count;
    struct {
        unsigned zrdt:1;       /* brk tunnel - zrdt vs. fohh */
        unsigned de:1;         /* double enc */
        unsigned ssl_srv:1;    /* application client traffic HTTPS/SSL */
        unsigned icmp:1;       /* data handling icmp mtunnel */
        unsigned zdx:1;        /* zdx mtunnel data handling */
        unsigned waf:1;        /* http/https appl.security  */
        unsigned ssl_cln:1;    /* traffic to server is HTTPS/SSL */
        unsigned srv_tcp:1;    /* appl server transport tcp vs. udp */
        /* these flow attributes can replace mtunnel members and
        ** can be extexnted. It is based on limited knowledge
        ** of the possible mtunnel types and permutations.
        ** It can be good, helpfull and readable to see all the charactristis in one data member
        */
    }pipeline_atributes;
    int allow_all_xport;
    int64_t free_time_s;
    int64_t on_free_queue_us;
    int64_t gid_inspection_appl;
    int64_t gid_inspection_profile;
    int64_t gid_inspection_rule;
    int64_t gid_app_domain;
    enum zpn_waf_inspection_type e_inspection_type;
    enum zpn_traffic_inspection_mode e_inspection_mode;
    struct mtunnel_waf_sess_ctx* inspection_ctx;
    uint8_t insp_status;
    uint64_t ssl_err;
    void * request_cookie;
    struct zpn_zdx_webprobe_cache_https_stats_ssl_data webprobe_cache_stats_ssl_data;
    int64_t zpn_zdx_webprobe_start_ts;
    int64_t zpn_zdx_webprobe_mtunnel_setup_complete_ts;
    int appl_protocol_msk;
    int insp_protocol_msk;
    PDP_PROTO pdp_profile;
    uint64_t adp_override_msk;
    int64_t zpn_update_trans_log_ts;
};

static const char *mtunnel_states[] = {
    [za_request_received] = "za_request_received",
    [za_server_conn_open] = "za_server_conn_open",
    [za_broker_conn_two_hop] = "za_broker_conn_two_hop",
    [za_broker_conn_open] = "za_broker_conn_open",
    [za_complete] = "za_complete",
    [za_reaping] = "za_reaping",
    [za_free] = "za_free",
};

/* Translate state... */
static const char *za_mtunnel_state(enum zpn_assistant_mtunnel_state state);

static int zpn_assistant_mtunnel_log(struct zpn_assistant_mtunnel *mtunnel);
static struct zpn_assistant_mtunnel *zpn_assistant_mtunnel_alloc(void);
static void zpn_assistant_mtunnel_soft_free(struct zpn_assistant_mtunnel *mtunnel);
static void za_mtunnel_server_connect_flags_internal_display(struct zpn_assistant_mtunnel *mtunnel);
static void za_mtunnel_clear_connect_flags(struct zpn_assistant_mtunnel *mtunnel);
static struct zpn_assistant_mtunnel*
    za_mtunnel_lookup_and_lock_bucket(const char *mtunnel_id, size_t tun_id_len,
                                      uint64_t mtunnel_id_hash, int return_if_reaping);
static void za_mtunnel_locked_state_machine(struct zpn_assistant_mtunnel *mtunnel);
static int za_mtunnel_locked_mark_destroyed(struct zpn_assistant_mtunnel *mtunnel, const char *err);
static void za_mtunnel_bucket_lock(struct zpn_assistant_mtunnel *mtunnel);
static void za_mtunnel_bucket_unlock(struct zpn_assistant_mtunnel *mtunnel);

static int is_mtunnel_conn_type_connector_process_not_done(int conn_type, struct zpn_assistant_mtunnel *mtunnel)
{
    if (IS_TWO_HOP_MTUNNEL(mtunnel)) {
        if (((conn_type == ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN) && (mtunnel->flag.two_hop_data_broker_conn_process_done == 0)) ||
            ((conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) && (mtunnel->flag.two_hop_proxy_conn_process_done == 0))) {
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST is_mtunnel_conn_type_connector_process_not_done returning 1 for mtunnel: %s", mtunnel->mtunnel_id);
            return 1;
        } else {
            return 0;
        }
    }
    return 1;
}

static void set_mtunnel_conn_type_connector_process_done(int conn_type, struct zpn_assistant_mtunnel *mtunnel)
{
    if (IS_TWO_HOP_MTUNNEL(mtunnel)) {
        if (conn_type == ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN) {
            mtunnel->flag.two_hop_data_broker_conn_process_done = 1;
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST set two_hop_data_broker_conn_process_done = 1 for mtunnel: %s", mtunnel->mtunnel_id);
        } else {
            mtunnel->flag.two_hop_proxy_conn_process_done = 1;
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST set two_hop_proxy_conn_process_done = 1 for mtunnel: %s", mtunnel->mtunnel_id);
        }
    }
}

static inline struct zpn_assistant_mtunnel*
za_mtunnel_lookup_and_lock(const char *mtunnel_id, size_t tun_id_len, uint64_t mtunnel_id_hash)
{
    return za_mtunnel_lookup_and_lock_bucket(mtunnel_id, tun_id_len, mtunnel_id_hash, 0);
}

static
struct zpn_mconn *zpn_assistant_mtunnel_broker_mconn(struct zpn_assistant_mtunnel *mtunnel)
{
    if (mtunnel->tlv_type == zpn_fohh_tlv) {
        return &(mtunnel->mconn_fohh_tlv_broker.mconn);
    } else {
        return &(mtunnel->mconn_zrdt_tlv_broker.mconn);
    }
}

static int zpn_assistant_mtunnel_verify_ptag_mode_enabled(struct zpn_assistant_mtunnel *mtunnel)
{
    if (!zpn_is_waf_enabled_on_assistant(mtunnel->g_ast, mtunnel->g_ast_grp)) {
        return ZPN_RESULT_NO_ERROR;
    }
    pthread_mutex_lock(&(global_assistant.config_lock));

	if (zpn_is_waf_ptag_feature_enabled()) {
        mtunnel->flag.ptag_mode = 1;
	}

    pthread_mutex_unlock(&(global_assistant.config_lock));
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_assistant_mtunnel_verify_adp_enabled_application(struct zpn_assistant_mtunnel *mtunnel)
{
    int adp_enabled = 0;
	int protocol_bitmask = 0;
    int res;

    if (!zpn_ad_protection_feature_enabled()) {
        return ZPN_RESULT_NO_ERROR;
    }

    pthread_mutex_lock(&(global_assistant.config_lock));
    res = assistant_app_check_adp_enabled(mtunnel->g_app, mtunnel->g_app_grp, mtunnel->g_ast_grp, mtunnel->g_srv_grp,
                                         mtunnel->client_port_he, mtunnel->ip_protocol, mtunnel->mtunnel_type, mtunnel->mtunnel_id,
                                         mtunnel->domain, &adp_enabled, &protocol_bitmask);
    if (ZPN_RESULT_NO_ERROR != res) {
        pthread_mutex_unlock(&(global_assistant.config_lock));
        return res;
    }

    mtunnel->flag.adp_enabled = adp_enabled;
    mtunnel->appl_protocol_msk = protocol_bitmask;

    pthread_mutex_unlock(&(global_assistant.config_lock));
    return ZPN_RESULT_NO_ERROR;
}


static int zpn_assistant_mtunnel_verify_waf_feature_status_of_application(struct zpn_assistant_mtunnel *mtunnel, int64_t *status)
{
    int64_t feature_enabled = 0;
    int res;
    if (!status || !mtunnel) return ZPN_RESULT_ERR;

    *status = 0;

    pthread_mutex_lock(&(global_assistant.config_lock));

    res = assistant_app_waf_get_feature_enable_status_mask(mtunnel->g_app, mtunnel->g_app_grp, mtunnel->g_ast_grp,
                                     mtunnel->g_srv_grp, &feature_enabled, mtunnel->mtunnel_id, mtunnel->domain);
    if (ZPN_RESULT_NO_ERROR != res) {
        pthread_mutex_unlock(&(global_assistant.config_lock));
        return res;
    }
    pthread_mutex_unlock(&(global_assistant.config_lock));

    if ( (feature_enabled & zpn_auto_insp_app_segment) &&
         (zpn_auto_app_protection_feature_enabled()) ) {
         mtunnel->flag.auto_app_protect_enabled = 1;
    }

    /* ADP feature has its own logic- getting bitmask etc. We just return the status of the appsegment.
     * if it is an ADP appsegment or not.
     */

    *status = feature_enabled;
    return ZPN_RESULT_NO_ERROR;
}

static int global_bind_broker_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    *global_owner_incarnation = mtunnel->incarnation;

    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_broker_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    if (mtunnel) {
        if (err && !mtunnel->err) mtunnel->err = ASST_STRDUP(err, strlen(err));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int global_bind_server_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    *global_owner_incarnation = mtunnel->incarnation;

    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_server_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    if (mtunnel) {
        if (err && !mtunnel->err) mtunnel->err = ASST_STRDUP(err, strlen(err));
    }
    return ZPN_RESULT_NO_ERROR;
}

static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    za_mtunnel_lock(mtunnel);
}

static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    za_mtunnel_unlock(mtunnel);
}

static int global_ip_proto_cb(void *mconn_base,
                              void *mconn_self,
                              void *global_owner,
                              void *global_owner_key,
                              size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    return mtunnel->ip_protocol;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    return mtunnel->double_encrypt;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;
    struct zpn_mconn *outer_mconn = mconn_base;

    if (mtunnel->double_encrypt && mtunnel->connector && mtunnel->connector->type == zct_tun) {
        struct zpn_connector_tun *conn = (struct zpn_connector_tun *)mtunnel->connector;
        if (mconn_base == &conn->mconn_c.mconn) {
            if (mtunnel->ip_protocol == IPPROTO_UDP) {
                outer_mconn = &(mtunnel->mconn_udp_server.mconn);
            } else {
                outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
            }
        } else if (mconn_base == &conn->mconn_s.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    } else if (mtunnel->connector && mtunnel->connector->type == zct_webprobe_http){
        struct zpn_connector_webprobe_http_inspect *conn = (struct zpn_connector_webprobe_http_inspect *) mtunnel->connector;
        if (mconn_base == &conn->inspect_common->mconn_cpair.mconn) {
            outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        } else if (mconn_base == &conn->inspect_common->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    }  else if (mtunnel->connector && mtunnel->connector->type == zct_webprobe_https){
        struct zpn_connector_webprobe_https_inspect *conn = (struct zpn_connector_webprobe_https_inspect *) mtunnel->connector;
        if (mconn_base == &conn->inspect_common->mconn_cpair.mconn) {
            outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        } else if (mconn_base == &conn->inspect_common->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    }  else if (mtunnel->connector && mtunnel->connector->type == zct_tun_inspect) {
        struct zpn_connector_tun_inspect *conn = (struct zpn_connector_tun_inspect *)mtunnel->connector;
        if (mconn_base == &conn->mconn_cpair.mconn) {
            /* UDP is needed since we can reach here with ptag/auto pipeline with double encryption */
            if (mtunnel->ip_protocol == IPPROTO_UDP) {
                outer_mconn = &(mtunnel->mconn_udp_server.mconn);
            } else {
                outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
            }
        } else if (mconn_base == &conn->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    } else if (mtunnel->connector && mtunnel->connector->type == zct_inspect) {
        struct zpn_connector_inspect *conn = (struct zpn_connector_inspect *)mtunnel->connector;
        if (mconn_base == &conn->mconn_cpair.mconn) {
            /* UDP is needed since we can reach here with ptag/auto pipeline */
            if (mtunnel->ip_protocol == IPPROTO_UDP) {
                outer_mconn = &(mtunnel->mconn_udp_server.mconn);
            } else {
                outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
            }
        } else if (mconn_base == &conn->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    } else if (mtunnel->connector && mtunnel->connector->type == zct_tls_inspect) {
        struct zpn_connector_tls_inspect *conn = (struct zpn_connector_tls_inspect *)mtunnel->connector;
        if (mconn_base == &conn->mconn_cpair.mconn) {
            outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        } else if (mconn_base == &conn->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    } else if (mtunnel->connector && mtunnel->connector->type == zct_tun_tls_inspect) {
        struct zpn_connector_tun_tls_inspect *conn = (struct zpn_connector_tun_tls_inspect *)mtunnel->connector;
        if (mconn_base == &conn->mconn_cpair.mconn) {
            outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        } else if (mconn_base == &conn->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    } else if (mtunnel->connector && mtunnel->connector->type == zct_idps_inspect) {
        struct zpn_connector_idps_inspect *conn = (struct zpn_connector_idps_inspect *)mtunnel->connector;
        if (mconn_base == &conn->mconn_cpair.mconn) {
            if (mtunnel->ip_protocol == IPPROTO_UDP) {
                outer_mconn = &(mtunnel->mconn_udp_server.mconn);
            } else {
                outer_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
            }
        } else if (mconn_base == &conn->mconn_spair.mconn) {
            outer_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);
        }
    }
    return outer_mconn;
}

static void mtunnel_async_terminate_callback(struct fohh_thread *thread, void *term_cookie, int64_t int_cookie)
{
    struct terminate_cookie *cookie = term_cookie;
    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;

    if (!cookie) {
        ASSISTANT_LOG(AL_CRITICAL, "Async terminate with NULL cookie");
        return;
    }

    if (!cookie->mtunnel_id) {
        ASSISTANT_LOG(AL_CRITICAL, "Async terminate with NULL mtunnel_id");
        return;
    }

    ASSISTANT_DEBUG_MTUNNEL("global_mtunnel_terminate_callback(), mtunnel_id = %s", cookie->mtunnel_id);

    mtunnel_id_len = strlen(cookie->mtunnel_id);
    mtunnel_id_hash = CityHash64(cookie->mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock(cookie->mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (mtunnel) {
        /* We are the original requesting broker, send mtunnel_req_ack back to client */
        ASSISTANT_LOG(AL_NOTICE, "%s: Async terminate for mtunnel with reason:%s, state = %s",
                mtunnel->mtunnel_id, (cookie->error) ? cookie->error : "unknown", za_mtunnel_state(mtunnel->state));

        /* FIXME: Should we send out message to other modules connected to us? */

        za_mtunnel_lock(mtunnel);
        if (strcmp(cookie->error, AST_MT_CONN_TO_SERVER_STUCK) == 0) {
            mtunnel->flag.is_terminating_due_to_conn_stuck = 1;
        }
        mtunnel->flag.broker_end_done = 1;
        za_mtunnel_locked_mark_destroyed(mtunnel, cookie->error);
        za_mtunnel_locked_state_machine(mtunnel);
        za_mtunnel_unlock(mtunnel);
        za_mtunnel_bucket_unlock(mtunnel);
    } else {
        /* mtunnel probably has been deleted already. Just ignore the call */
    }

    ASST_FREE(cookie->mtunnel_id);
    if (cookie->error) ASST_FREE(cookie->error);
    ASST_FREE(cookie);
}

/*
 * Locking: it is assumed we have the lock to mtunnel at this point
 */
static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;
    struct terminate_cookie *cookie;

    if (!mtunnel) {
        ASSISTANT_LOG(AL_CRITICAL, "Terminating NULL mtunnel!");
        return;
    }

    if (mtunnel->termination_started) {
        ASSISTANT_DEBUG_MTUNNEL("Terminating already started.");
        return;
    }

    ZPN_ASSERT(mtunnel->mtunnel_id);

    cookie = ASST_CALLOC(sizeof(struct terminate_cookie));
    if (!cookie) {
        ASSISTANT_LOG(AL_CRITICAL, "No more memory for terminate cookie!");
        return;
    }

    cookie->mtunnel_id = ASST_STRDUP(mtunnel->mtunnel_id, strlen(mtunnel->mtunnel_id));
    if (!cookie->mtunnel_id) {
        ASSISTANT_LOG(AL_CRITICAL, "No more memory for mtunnel_id!");
        ASST_FREE(cookie);
        return;
    }

    if (error) {
        cookie->error = ASST_STRDUP(error, strlen(error));
        if (!cookie->error) {
            ASSISTANT_LOG(AL_CRITICAL, "No more memory for error!");
            ASST_FREE(cookie->mtunnel_id);
            ASST_FREE(cookie);
            return;
        }
    }

    if (mtunnel->ip_protocol == IPPROTO_UDP) {
        if (fohh_thread_call(mtunnel->mconn_udp_server.mconn.fohh_thread_id,
                             mtunnel_async_terminate_callback,
                             cookie,
                             0) == FOHH_RESULT_NO_ERROR) {
            mtunnel->termination_started = 1;
            return;
        }
    } else if (mtunnel->ip_protocol == IPPROTO_ICMP) {
        if (fohh_thread_call(mtunnel->mconn_icmp_server.mconn.fohh_thread_id,
                             mtunnel_async_terminate_callback,
                             cookie,
                             0) == FOHH_RESULT_NO_ERROR) {
            mtunnel->termination_started = 1;
            return;
        }
    } else if (mtunnel->ip_protocol == IPPROTO_ICMPV6) {
        if (fohh_thread_call(mtunnel->mconn_icmp_server.mconn.fohh_thread_id,
                             mtunnel_async_terminate_callback,
                             cookie,
                             0) == FOHH_RESULT_NO_ERROR) {
            mtunnel->termination_started = 1;
            return;
        }
    } else {
        if (fohh_thread_call(mtunnel->mconn_bufferevent_server.mconn.fohh_thread_id,
                             mtunnel_async_terminate_callback,
                             cookie,
                             0) == FOHH_RESULT_NO_ERROR) {
            mtunnel->termination_started = 1;
            return;
        }
    }

    /* Erk... All threads have probably gone? Nothing matters anymore ..., whatever */
    ASSISTANT_LOG(AL_CRITICAL, "Cannot do thread call");
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    return mtunnel->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    if (mtunnel->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;

    return mtunnel->mtunnel_id;
}

static int64_t global_mtunnel_get_customer_gid(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    struct zpn_assistant_mtunnel *mtunnel = mconn_self;
    if (!mtunnel) {
        return 0;
    }

    return mtunnel->g_cst;
}

const struct zpn_mconn_global_owner_calls za_broker_global_call_set = {
    global_bind_broker_cb,
    global_unbind_broker_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id,
    global_get_peer_no_op,
    global_mtunnel_get_customer_gid
};

const struct zpn_mconn_global_owner_calls za_server_global_call_set = {
    global_bind_server_cb,
    global_unbind_server_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id,
    global_get_peer_no_op,
    global_mtunnel_get_customer_gid
};

static struct zpn_zdx_cookie *get_zpn_zdx_cookie(void) {
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)ZPN_CALLOC(sizeof(struct zpn_zdx_cookie));
    memset(zdx_cookie, 0, sizeof(*zdx_cookie));
    return zdx_cookie;
}

static void reset_zpn_zdx_cookie(void *cookie) {
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)cookie;
    if (zdx_cookie) ZPN_FREE(zdx_cookie);
    zdx_cookie = NULL;
}

/* mt is locked when this function is called */
static int zpn_assistant_mtunnel_clean(struct zpn_assistant_mtunnel *mt)
{
    int64_t now_s = epoch_s();

    ASSISTANT_DEBUG_MTUNNEL("%s: zpn_assistant_mtunnel_clean check, state = %s, proto = %s, time since free_time_s = %ld",
                            mt->mtunnel_id, mtunnel_states[mt->state], (mt->ip_protocol == IPPROTO_TCP) ? "tcp" : "udp", (long)(now_s - mt->free_time_s));

    if (mt->broker) return 0;

    if (mt->ip_protocol == IPPROTO_UDP) {

        if (mt->free_time_s && (now_s - mt->free_time_s > UDP_TIMEOUT_S)) {
            ASSISTANT_DEBUG_MTUNNEL("UDP mtunnel is free since it has been on free list for %ld sec", (long)(now_s - mt->free_time_s));
            zpn_mconn_flush_data(&(mt->mconn_udp_server.mconn));
            return 1;
        }

        if (!zpn_mconn_udp_bufferevent_clean(&(mt->mconn_udp_server))) {
            return 0;
        }
    } else if (mt->ip_protocol == IPPROTO_ICMP) {
        if (!zpn_mconn_icmp_clean(&(mt->mconn_icmp_server))) {
            return 0;
        }
    } else if (mt->ip_protocol == IPPROTO_ICMPV6) {
        if (!zpn_mconn_icmpv6_clean(&(mt->mconn_icmp_server))) {
            return 0;
        }
    } else {
        if (!zpn_mconn_bufferevent_clean(&(mt->mconn_bufferevent_server))) {
            return 0;
        }
    }

    if (mt->tlv_type == zpn_fohh_tlv) {
        if (!zpn_mconn_fohh_tlv_clean(&(mt->mconn_fohh_tlv_broker))) {
            return 0;
        }
    } else {
        if (!zpn_mconn_zrdt_tlv_clean(&(mt->mconn_zrdt_tlv_broker))) {
            return 0;
        }
    }

    if (mt->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        struct zpn_mconn* server_tcp_mconn = &(mt->mconn_bufferevent_server.mconn);;
        reset_zpn_zdx_cookie(server_tcp_mconn->pipeline_cookie);
    }

    return 1;
}

static int zpn_assistant_mtunnel_stuck(struct zpn_assistant_mtunnel *mt)
{
    if (mt->ip_protocol == IPPROTO_TCP) {
        return zpn_mconn_bufferevent_stuck(&mt->mconn_bufferevent_server);
    }

    return 0;
}

static int zpn_assistant_mtunnel_done(struct zpn_assistant_mtunnel *mt)
{
    int64_t now_us = epoch_us();

    /* Check if we have delayed FIN */
    if (mt->delayed_fin_forward_us) {
        if (mt->delayed_fin_forward_us > now_us) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Checking mtunnel done. Not ready to send out delayed FIN yet", mt->mtunnel_id);
        } else if ((mt->tlv_type == zpn_fohh_tlv) &&
                   ((mt->mconn_fohh_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US) > mt->delayed_fin_forward_us) &&
                   ((mt->mconn_fohh_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US) > now_us)) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Checking mtunnel done. We got new data over fohh after last FIN from broker", mt->mtunnel_id);
            mt->delayed_fin_forward_us = mt->mconn_fohh_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US;
        } else if ((mt->tlv_type == zpn_zrdt_tlv) &&
                   ((mt->mconn_zrdt_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US) > mt->delayed_fin_forward_us) &&
                   ((mt->mconn_zrdt_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US) > now_us)) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Checking mtunnel done. We got new data over zrdt after last FIN from broker", mt->mtunnel_id);
            mt->delayed_fin_forward_us = mt->mconn_zrdt_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US;
        } else {
            ASSISTANT_DEBUG_MTUNNEL("%s: Checking mtunnel done. Sending out delayed FIN from broker", mt->mtunnel_id);
            if (mt->ip_protocol == IPPROTO_UDP) {
                zpn_mconn_forward_mtunnel_end(&mt->mconn_udp_server.mconn, MT_CLOSED_TERMINATED, 0);
            } else if (mt->ip_protocol == IPPROTO_ICMP) {
                zpn_mconn_forward_mtunnel_end(&mt->mconn_icmp_server.mconn, MT_CLOSED_TERMINATED, 0);
            } else if (mt->ip_protocol == IPPROTO_ICMPV6) {
                zpn_mconn_forward_mtunnel_end(&mt->mconn_icmp_server.mconn, MT_CLOSED_TERMINATED, 0);
            } else {
                zpn_mconn_forward_mtunnel_end(&mt->mconn_bufferevent_server.mconn, MT_CLOSED_TERMINATED, 0);
            }
            mt->delayed_fin_forward_us = 0;
        }
        return 0;
    }

    if (mt->connector) {
        if (zpn_is_connector_inspection_type(mt->connector)) {
            if (!zpn_connector_insp_done(mt->mdbg_str, mt->connector)) {
                return 0;
            }
        } else {
            if (!zpn_connector_tun_done(mt->connector)) {
               return 0;
            }
        }
    }

    if (mt->ip_protocol == IPPROTO_UDP) {
        if (zpn_mconn_udp_bufferevent_done(&mt->mconn_udp_server)) {
            return 1;
        }
    } else if (mt->ip_protocol == IPPROTO_ICMP) {
        if (zpn_mconn_icmp_done(&mt->mconn_icmp_server)) {
            return 1;
        }
    } else if (mt->ip_protocol == IPPROTO_ICMPV6) {
        if (zpn_mconn_icmpv6_done(&mt->mconn_icmp_server)) {
            return 1;
        }
    } else {
        if (zpn_mconn_bufferevent_done(&mt->mconn_bufferevent_server) &&
            zpn_mconn_done(zpn_assistant_mtunnel_broker_mconn(mt))) {
            return 1;
        } else if (zpn_mconn_fin_expired(&mt->mconn_bufferevent_server.mconn) ||
            zpn_mconn_fin_expired(zpn_assistant_mtunnel_broker_mconn(mt))) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Assistant mtunnel expired after receiving FIN", mt->mtunnel_id);
            return 1;
        }
    }

    return 0;
}

void zpn_assistant_mtunnel_internal_display(struct zpn_assistant_mtunnel *mt)
{
    if (assistant_debug_log & ASSISTANT_DEBUG_MTUNNEL_BIT) {
        ASSISTANT_LOG(AL_DEBUG, "mtunnel_id = %s", mt->mtunnel_id);
        ASSISTANT_LOG(AL_DEBUG, "mt state = %s, mt proto = %s",
                      mtunnel_states[mt->state],
                      ((mt->ip_protocol == IPPROTO_TCP) ? "tcp" : ((mt->ip_protocol == IPPROTO_UDP) ? "udp" : "icmp")));
        ASSISTANT_LOG(AL_DEBUG, "broker_mconn ----- %s", (mt->tlv_type == zpn_fohh_tlv) ? "FOHH" : "ZRDT");
        if (mt->tlv_type == zpn_fohh_tlv) {
            zpn_mconn_fohh_tlv_internal_display(&(mt->mconn_fohh_tlv_broker));
        } else {
            zpn_mconn_zrdt_tlv_internal_display(&(mt->mconn_zrdt_tlv_broker));
        }
        ASSISTANT_LOG(AL_DEBUG, "server_mconn -----");
        ASSISTANT_LOG(AL_DEBUG, "mtunnel_connect_flags -----");
        za_mtunnel_server_connect_flags_internal_display(mt);
        if (mt->ip_protocol == IPPROTO_UDP) {
            zpn_mconn_udp_bufferevent_internal_display(&(mt->mconn_udp_server));
        } else if (mt->ip_protocol == IPPROTO_ICMP) {
            zpn_mconn_icmp_internal_display(&(mt->mconn_icmp_server));
        } else if (mt->ip_protocol == IPPROTO_ICMPV6) {
            zpn_mconn_icmpv6_internal_display(&(mt->mconn_icmp_server));
        } else {
            zpn_mconn_bufferevent_internal_display(&(mt->mconn_bufferevent_server));
        }

        if (mt->connector) {
            if (zpn_is_connector_inspection_type(mt->connector)) {
                zpn_connector_insp_internal_display(mt->connector);
            } else {
                zpn_connector_tun_internal_display(mt->connector);
            }
        }
    }
}

void zpn_assistant_mtunnel_internal_stats(struct zpn_assistant_mtunnel *mt)
{
    struct zpn_mconn *broker_tlv_mconn = zpn_assistant_mtunnel_broker_mconn(mt);

    if (assistant_debug_log & ASSISTANT_DEBUG_MTUNNEL_BIT) {
        ASSISTANT_LOG(AL_DEBUG, "mtunnel_id = %s, tag = %d", mt->mtunnel_id, mt->broker_tag);
        if (mt->ip_protocol == IPPROTO_TCP) {
        ASSISTANT_LOG(AL_DEBUG, "    S to B: Rx = %ld, Tx = %ld",
                (long)mt->mconn_bufferevent_server.mconn.bytes_to_peer, (long)broker_tlv_mconn->bytes_to_client);
        ASSISTANT_LOG(AL_DEBUG, "    B to S: Rx = %ld, Tx = %ld",
                (long)broker_tlv_mconn->bytes_to_peer, (long)mt->mconn_bufferevent_server.mconn.bytes_to_client);
        } else if (mt->ip_protocol == IPPROTO_UDP) {
            ASSISTANT_LOG(AL_DEBUG, "    S to B: Rx = %ld, Tx = %ld",
                    (long)mt->mconn_udp_server.mconn.bytes_to_peer, (long)broker_tlv_mconn->bytes_to_client);
            ASSISTANT_LOG(AL_DEBUG, "    B to S: Rx = %ld, Tx = %ld",
                    (long)broker_tlv_mconn->bytes_to_peer, (long)mt->mconn_udp_server.mconn.bytes_to_client);
        }
    }
}

int64_t zpn_assistant_mtunnel_get_config_mconn_track_perf_stats()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                                                 &value,
                                                 ZPN_ASSISTANT_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                                                 global_assistant.gid,
                                                 global_assistant.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    return value;
}

void assistant_cfg_check_mconn_track_perf_stats()
{
    int64_t curr_value = 0;
    int64_t config_value = 0;

    config_value = zpn_assistant_mtunnel_get_config_mconn_track_perf_stats();

    curr_value = __atomic_load_n(&stats.mtunnel_mconn_track_perf_stats_level, __ATOMIC_RELAXED);

    if (curr_value != config_value) {
        // store new config value
        __atomic_store_n(&stats.mtunnel_mconn_track_perf_stats_level, config_value, __ATOMIC_RELAXED);
    }
}

int assistant_cfg_is_mconn_track_perf_stats()
{
    int64_t config_value = 0;
    config_value = __atomic_load_n(&stats.mtunnel_mconn_track_perf_stats_level, __ATOMIC_RELAXED);
    return config_value?1:0;
}

void zpn_assistant_mtunnel_log_mconn_stats(struct zpn_assistant_mtunnel *mt, int is_mt_reaped) {

    struct zpn_mconn *broker_mconn;
    struct zpn_mconn *server_mconn;
    int64_t diff_s;
    int64_t now_s;
    struct zpn_mtunnel_data_mconn_stats  mt_mconn_data_stats;

    if (!mt) {
        return;
    }

    // if not configured - return
    if(!assistant_cfg_is_mconn_track_perf_stats()) {
        return;
    }
    now_s = epoch_s();

    // try to log always at the end - reap time, otherwise wait at least minimum time for log
    if (!is_mt_reaped) {
        // first attempt - lets wait at least min
        if (!mt->zpn_update_trans_log_ts) {
            mt->zpn_update_trans_log_ts = now_s;
            return;
        }

        diff_s = now_s - mt->zpn_update_trans_log_ts;

#define ZPN_ASSISTANT_MTUNNEL_LOG_TRACK_PREF_STATS_UPDATE_IN_SEC 120

        if (diff_s < ZPN_ASSISTANT_MTUNNEL_LOG_TRACK_PREF_STATS_UPDATE_IN_SEC) {
            return;
        }
    }

    if (mt->tlv_type == zpn_fohh_tlv) {
        broker_mconn = &(mt->mconn_fohh_tlv_broker.mconn);
        ASSISTANT_LOG(AL_INFO,
                      "%s, mconn:%p, track perf broker: ingress rx_diff_rx_data_hist: %d,%d,%d,%d,%d, egress tx_peer_rx_data(max (us=%d, epoch=%"PRId64", unbl_cnt=%d), hist=%d,%d,%d,%d,%d), tx_data_unblock: max(us=%d, cnt=%d, tot us=%"PRId64"), bytes_to_client=%"PRId64" ",
                      mt->mtunnel_id, broker_mconn, broker_mconn->rx_diff_rx_data_hist[0], broker_mconn->rx_diff_rx_data_hist[1],
                      broker_mconn->rx_diff_rx_data_hist[2], broker_mconn->rx_diff_rx_data_hist[3], broker_mconn->rx_diff_rx_data_hist[4],
                      broker_mconn->tx_peer_rx_data_max_us, broker_mconn->tx_peer_rx_data_max_epoch_us,
                      broker_mconn->tx_peer_rx_data_max_unblock_cnt, broker_mconn->tx_peer_rx_data_hist[0],
                      broker_mconn->tx_peer_rx_data_hist[1], broker_mconn->tx_peer_rx_data_hist[2], broker_mconn->tx_peer_rx_data_hist[3],
                      broker_mconn->tx_peer_rx_data_hist[4], broker_mconn->tx_data_unblock_max_us, broker_mconn->tx_data_unblock_max_cnt,
                      broker_mconn->tx_data_unblock_tot_us, broker_mconn->bytes_to_client);

        // update assistant to broker stats
        mt_mconn_data_stats.ab_rx_diff_rx_data_hist[0] = broker_mconn->rx_diff_rx_data_hist[0];
        mt_mconn_data_stats.ab_rx_diff_rx_data_hist[1] = broker_mconn->rx_diff_rx_data_hist[1];
        mt_mconn_data_stats.ab_rx_diff_rx_data_hist[2] = broker_mconn->rx_diff_rx_data_hist[2];
        mt_mconn_data_stats.ab_rx_diff_rx_data_hist[3] = broker_mconn->rx_diff_rx_data_hist[3];
        mt_mconn_data_stats.ab_rx_diff_rx_data_hist[4] = broker_mconn->rx_diff_rx_data_hist[4];
        mt_mconn_data_stats.ab_tx_peer_rx_data_hist[0] = broker_mconn->tx_peer_rx_data_hist[0];
        mt_mconn_data_stats.ab_tx_peer_rx_data_hist[1] = broker_mconn->tx_peer_rx_data_hist[1];
        mt_mconn_data_stats.ab_tx_peer_rx_data_hist[2] = broker_mconn->tx_peer_rx_data_hist[2];
        mt_mconn_data_stats.ab_tx_peer_rx_data_hist[3] = broker_mconn->tx_peer_rx_data_hist[3];
        mt_mconn_data_stats.ab_tx_peer_rx_data_hist[4] = broker_mconn->tx_peer_rx_data_hist[4];
        mt_mconn_data_stats.ab_tx_peer_rx_data_max_us = broker_mconn->tx_peer_rx_data_max_us;
        mt_mconn_data_stats.ab_tx_peer_rx_data_max_epoch_us = broker_mconn->tx_peer_rx_data_max_epoch_us;

        mt_mconn_data_stats.ab_tx_peer_rx_data_max_cnt = broker_mconn->tx_peer_rx_data_max_cnt;
        mt_mconn_data_stats.ab_tx_peer_rx_data_max_unblock_cnt = broker_mconn->tx_peer_rx_data_max_unblock_cnt;
        mt_mconn_data_stats.ab_tx_data_unblock_max_us = broker_mconn->tx_data_unblock_max_us;
        mt_mconn_data_stats.ab_tx_data_unblock_max_cnt = broker_mconn->tx_data_unblock_max_cnt;
        mt_mconn_data_stats.ab_tx_data_unblock_tot_us = broker_mconn->tx_data_unblock_tot_us;

        // reset track performance counters at assistant
        __atomic_store_n(&broker_mconn->tx_peer_rx_data_max_us, 0, __ATOMIC_RELAXED);
        __atomic_store_n(&broker_mconn->tx_data_unblock_max_us, 0, __ATOMIC_RELAXED);
    }

    if (mt->ip_protocol == IPPROTO_UDP) {
        server_mconn = &(mt->mconn_udp_server.mconn);
    } else if (mt->ip_protocol == IPPROTO_ICMP) {
        server_mconn = &(mt->mconn_icmp_server.mconn);
    } else if (mt->ip_protocol == IPPROTO_ICMPV6) {
        server_mconn = &(mt->mconn_icmp_server.mconn);
    } else {
        server_mconn = &(mt->mconn_bufferevent_server.mconn);
    }

    ASSISTANT_LOG(AL_INFO,
                  "%s, mconn:%p, track perf server: ingress rx_diff_rx_data_hist: %d,%d,%d,%d,%d, egress tx_peer_rx_data(max (us=%d, epoch=%"PRId64", unbl_cnt=%d), hist=%d,%d,%d,%d,%d), tx_data_unblock: max(us=%d, cnt=%d, tot us=%"PRId64"), bytes_to_client=%"PRId64" ",
                  mt->mtunnel_id, server_mconn, server_mconn->rx_diff_rx_data_hist[0], server_mconn->rx_diff_rx_data_hist[1],
                  server_mconn->rx_diff_rx_data_hist[2], server_mconn->rx_diff_rx_data_hist[3], server_mconn->rx_diff_rx_data_hist[4],
                  server_mconn->tx_peer_rx_data_max_us, server_mconn->tx_peer_rx_data_max_epoch_us,
                  server_mconn->tx_peer_rx_data_max_unblock_cnt, server_mconn->tx_peer_rx_data_hist[0],
                  server_mconn->tx_peer_rx_data_hist[1], server_mconn->tx_peer_rx_data_hist[2], server_mconn->tx_peer_rx_data_hist[3],
                  server_mconn->tx_peer_rx_data_hist[4], server_mconn->tx_data_unblock_max_us, server_mconn->tx_data_unblock_max_cnt,
                  server_mconn->tx_data_unblock_tot_us, server_mconn->bytes_to_client);


    //update assistant to server stats
    mt_mconn_data_stats.as_rx_diff_rx_data_hist[0] = server_mconn->rx_diff_rx_data_hist[0];
    mt_mconn_data_stats.as_rx_diff_rx_data_hist[1] = server_mconn->rx_diff_rx_data_hist[1];
    mt_mconn_data_stats.as_rx_diff_rx_data_hist[2] = server_mconn->rx_diff_rx_data_hist[2];
    mt_mconn_data_stats.as_rx_diff_rx_data_hist[3] = server_mconn->rx_diff_rx_data_hist[3];
    mt_mconn_data_stats.as_rx_diff_rx_data_hist[4] = server_mconn->rx_diff_rx_data_hist[4];
    mt_mconn_data_stats.as_tx_peer_rx_data_hist[0] = server_mconn->tx_peer_rx_data_hist[0];
    mt_mconn_data_stats.as_tx_peer_rx_data_hist[1] = server_mconn->tx_peer_rx_data_hist[1];
    mt_mconn_data_stats.as_tx_peer_rx_data_hist[2] = server_mconn->tx_peer_rx_data_hist[2];
    mt_mconn_data_stats.as_tx_peer_rx_data_hist[3] = server_mconn->tx_peer_rx_data_hist[3];
    mt_mconn_data_stats.as_tx_peer_rx_data_hist[4] = server_mconn->tx_peer_rx_data_hist[4];
    mt_mconn_data_stats.as_tx_peer_rx_data_max_us = server_mconn->tx_peer_rx_data_max_us;
    mt_mconn_data_stats.as_tx_peer_rx_data_max_epoch_us = server_mconn->tx_peer_rx_data_max_epoch_us;


    // reset track performance counters at assistant
    __atomic_store_n(&server_mconn->tx_peer_rx_data_max_us, 0, __ATOMIC_RELAXED);
    __atomic_store_n(&server_mconn->tx_data_unblock_max_us, 0, __ATOMIC_RELAXED);

    // start next collection of stats
    mt->zpn_update_trans_log_ts = now_s;

    // send rpc with statts to the broker
    struct zpn_tlv *tlv;
    struct zpn_assistant_broker_data *broker;
    int res = ZPN_RESULT_ERR;

    // sanity check
    broker = mt->broker;
    if (!broker) {
        /* This is an inconsistent state... */
        __sync_add_and_fetch_8(&(stats.mtunnel_mconn_track_perf_stats_send_error), 1);
        ASSISTANT_LOG(AL_ERROR, "%s: track perf assistant_data_mconn_stats send error no broker res: %d curr error num: %"PRId64" ", mt->mtunnel_id, res, stats.mtunnel_mconn_track_perf_stats_send_error);
        return;
    }
    tlv = assistant_data_get_tlv(broker);
    if (zpn_tlv_sanity_check(tlv)) {
        /* Inconsistent state... */
        __sync_add_and_fetch_8(&(stats.mtunnel_mconn_track_perf_stats_send_error), 1);
        ASSISTANT_LOG(AL_ERROR, "%s: track perf assistant_data_mconn_stats send error zpn_tlv_sanity_check res: %d curr error num: %"PRId64" ", mt->mtunnel_id, res, stats.mtunnel_mconn_track_perf_stats_send_error);
        return;
    }

    mt_mconn_data_stats.tag_id = mt->broker_tag;

    res = assistant_data_mconn_stats(broker, &mt_mconn_data_stats);
    if(res) {
        __sync_add_and_fetch_8(&(stats.mtunnel_mconn_track_perf_stats_send_error), 1);
        ASSISTANT_LOG(AL_ERROR, "%s: track perf assistant_data_mconn_stats send error res: %d curr error num: %"PRId64" ", mt->mtunnel_id, res, stats.mtunnel_mconn_track_perf_stats_send_error);
    } else {
        __sync_add_and_fetch_8(&(stats.mtunnel_mconn_track_perf_stats_send), 1);
        ASSISTANT_LOG(AL_INFO, "%s: track perf assistant_data_mconn_stats send success, curr success num: %"PRId64" ", mt->mtunnel_id, stats.mtunnel_mconn_track_perf_stats_send);
    }

    return;
}

/* We only send broker request ack when there is error in establishing connection */
void zpn_assistant_broker_request_ack(struct zpn_assistant_mtunnel *mt, char *err)
{
    struct zpn_broker_request_ack ack;
    int                           res;

    memset(&ack, 0, sizeof(ack));
    ack.mtunnel_id = mt->log.mtunnel_id;
    ack.g_ast = global_assistant.gid;
    ack.g_ast_grp = mt->g_ast_grp;
    ack.g_app = mt->g_app;
    ack.g_brk = mt->g_brk;
    ack.g_dsp = mt->g_dsp;
    ack.g_srv_grp = mt->g_srv_grp;
    ack.insp_status = mt->insp_status;
    ack.ssl_err = mt->ssl_err;
    ack.error = err;
    ack.seq_num = mt->brkreq_seq_num;
    if (mt->domain[0]) {
        ack.domain = mt->domain;
    } else {
        ack.domain = NULL;
    }
    ack.g_bfw = mt->g_bfw;
    ack.ast_bind_tx_us = mt->bind_tx_cloud_us;
    ack.ast_tx_us = assistant_state_get_current_time_cloud_us();
    if (mt->server_connection_setup_end_us) {
        ack.server_us = mt->server_connection_setup_end_us - mt->server_connection_setup_start_us;
    } else {
        ack.server_us = 0;
    }
    ack.a_inet = mt->assistant_inet;
    ack.a_port = mt->assistant_port_he;
    ack.path_decision = mt->path_decision;
    ack.ip_protocol = mt->ip_protocol;
    if(mt->server_inet.length) {
        ack.s_inet = mt->server_inet;
        ack.s_port = mt->server_port_he;
    }
    ack.dsp_bypassed = mt->flag.dsp_bypassed;

    res = assistant_broker_tx_broker_request_ack(mt->rx_broker_label, &ack);
    if (res && mt->flag.dsp_bypassed) {
        assistant_broker_tx_broker_request_ack(global_assistant.broker_control_label, &ack);
    }
}

static void zpn_assistant_mtunnel_touch_stream(struct zpn_assistant_mtunnel *mt) {
    if (mt->tlv_type == zpn_zrdt_tlv) {
        zpn_mconn_zrdt_tlv_stream_touch(&(mt->mconn_zrdt_tlv_broker));
    }
}

/*
 * In ET-31498, we noticed there is one situation with ZCC that ZCC sent us mtunnel request and keep the mtunnel IDLE forever.
 * The situation we found here is that this happens when users triggers tons of DE app transaction request.
 *
 * Normally, Connector terminates the mtunnel when data exchanges are done between both end and 2 FIN received from both ends.
 * But in this case, since there is no data and no FIN exchanged in between, these IDLE mtunnel then just hang in connector, causing memory being leaked.
 *
 * thus we are adding this check to let connector terminate mtunnel itself for IDLE mtunnel that is hanging for over mtunnels
 *
 */
#define ZPN_ASSISTANT_MTUNNEL_INTERNAL_EXPIRE_US (24ll*60ll*60ll*1000*1000) /*24 hrs in usec*/

int zpn_assistant_mtunnel_expired(struct zpn_assistant_mtunnel *mtunnel)
{
    int64_t tx_to_server_bytes;
    int64_t tx_to_broker_bytes;
    int64_t rx_to_broker_bytes;
    size_t  tx_buffer_to_server_bytes;
    size_t  rx_buffer_from_server_bytes;

    if (((assistant_state_get_current_time_cloud_us() - mtunnel->start_time_in_cloud_us) > ZPN_ASSISTANT_MTUNNEL_INTERNAL_EXPIRE_US) &&
        (!mtunnel->flag.is_tcp_keepalive_enabled)) {

        tx_buffer_to_server_bytes   = 0;
        rx_buffer_from_server_bytes = 0;
        tx_to_server_bytes          = 0;
        tx_to_broker_bytes          = 0;
        rx_to_broker_bytes          = 0;

        if (IPPROTO_UDP == mtunnel->ip_protocol) {
            if (mtunnel->mconn_udp_server.tx_queue) {
                tx_buffer_to_server_bytes = evbuffer_get_length(mtunnel->mconn_udp_server.tx_queue);
                tx_to_server_bytes        = mtunnel->mconn_udp_server.mconn.bytes_to_client;
                tx_to_broker_bytes        = mtunnel->mconn_udp_server.mconn.bytes_to_peer;
                rx_to_broker_bytes        = mtunnel->mconn_udp_server.mconn.bytes_from_peer;

                if (!tx_buffer_to_server_bytes && !tx_to_server_bytes && !tx_to_broker_bytes && !rx_to_broker_bytes) {
                    ASSISTANT_LOG(AL_NOTICE, "Observe idle UDP mtunnel %s hanging for over 24 hours, terminating", mtunnel->mtunnel_id);
                    return 1;
                }
            }
        } else if (IPPROTO_ICMP == mtunnel->ip_protocol) {
                tx_to_server_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_to_client;
                tx_to_broker_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_to_peer;
                rx_to_broker_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_from_peer;

                if (!tx_to_server_bytes && !tx_to_broker_bytes && !rx_to_broker_bytes) {
                    ASSISTANT_LOG(AL_NOTICE, "Observe idle ICMP mtunnel %s hanging for over 24 hours, terminating", mtunnel->mtunnel_id);
                    return 1;
                }
        } else if (IPPROTO_ICMPV6 == mtunnel->ip_protocol) {
                tx_to_server_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_to_client;
                tx_to_broker_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_to_peer;
                rx_to_broker_bytes        = mtunnel->mconn_icmp_server.mconn.bytes_from_peer;

                if (!tx_to_server_bytes && !tx_to_broker_bytes && !rx_to_broker_bytes) {
                    ASSISTANT_LOG(AL_NOTICE, "Observe idle ICMPv6 mtunnel %s hanging for over 24 hours, terminating", mtunnel->mtunnel_id);
                    return 1;
                }
        } else {
            if (mtunnel->mconn_bufferevent_server.bev) {
                tx_buffer_to_server_bytes   = evbuffer_get_length(bufferevent_get_output(mtunnel->mconn_bufferevent_server.bev));
                rx_buffer_from_server_bytes = evbuffer_get_length(bufferevent_get_input(mtunnel->mconn_bufferevent_server.bev));
                tx_to_server_bytes          = mtunnel->mconn_bufferevent_server.mconn.bytes_to_client;
                tx_to_broker_bytes          = mtunnel->mconn_bufferevent_server.mconn.bytes_to_peer;
                rx_to_broker_bytes          = mtunnel->mconn_bufferevent_server.mconn.bytes_from_peer;

                if (!tx_buffer_to_server_bytes && !rx_buffer_from_server_bytes && !tx_to_server_bytes && !tx_to_broker_bytes && !rx_to_broker_bytes) {
                    ASSISTANT_LOG(AL_NOTICE, "Observe idle TCP mtunnel %s hanging for over 24 hours, terminating", mtunnel->mtunnel_id);
                    return 1;
                }
            }
        }
    }

    return 0;
}

void zpn_assistant_check_mtunnel()
{
    int i;
    int freed = 0;
    int64_t loop_start_us = epoch_us();
    int64_t loop_end_us;

    for (i = 0; i < ZPN_ASSISTANT_BUCKETS; i++)
    {
        struct zpn_assistant_bucket *bucket = &global_assistant.buckets[i];
        struct zpn_assistant_mtunnel *mt;
        struct zpn_assistant_mtunnel *tmp_mt;
        int64_t asst_conn_timeout_us = ZPN_MCONN_TIMEOUT_AST;

        if (assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)){
            asst_conn_timeout_us = SECOND_TO_US(assistant_get_doublehop_switch_timeout(global_assistant.gid));
        }

        pthread_mutex_lock(&(bucket->lock));

        for (mt = TAILQ_FIRST(&(bucket->bucket_mtunnel_list)); mt != NULL; mt = tmp_mt)
        {
            int64_t curr_us = epoch_us();

            tmp_mt = TAILQ_NEXT(mt, bucket_list);

            za_mtunnel_lock(mt);

            zpn_assistant_mtunnel_internal_display(mt);
            //zpn_assistant_mtunnel_internal_stats(mt);
            zpn_assistant_mtunnel_log_mconn_stats(mt, 0);
            zpn_assistant_mtunnel_touch_stream(mt);

            if ((mt->state < za_complete) && (curr_us - mt->start_us) > asst_conn_timeout_us) {
                char *err = NULL;
                int do_double_hop = 0;

                ASSISTANT_DEBUG_MTUNNEL("Assistant mtunnel %s expired", mt->mtunnel_id);

                switch (mt->state) {
                case za_request_received:
                    err = AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_SERVER;
                    break;
                case za_server_conn_open:
                    if(IS_TWO_HOP_MTUNNEL(mt)) {
                        /*
                         * (ET-86571) In case of multiple IPs returned when resolving the broker fqdn,
                         * may be one of them is reachable by now, check the connectivity before switching to double hop
                         */
                        if ((mt->broker == NULL) && (mt->flag.try_if_other_ip_reachable == 0)) { // zpn_assistant_mtunnel_data_disconnected should have removed the broker connection
                            mt->flag.try_if_other_ip_reachable = 1;
                            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: Check if data broker: %s is reachable by now before switching to double hop for mtunnel: %s", mt->brk_name, mt->mtunnel_id);
                            /* za_mtunnel_locked_state_machine tries to connect to the data broker.
                             * incase it is connected, we proceed futher with bind and data flow. Otherwise, after 3 sec
                             * this zpn_assistant_check_mtunnel is called and double hop is tried because
                             * try_if_other_ip_reachable is set to 1 */
                            za_mtunnel_locked_state_machine(mt);
                        } else if (mt->broker != NULL) {
                            struct zpn_tlv *tlv = assistant_data_get_tlv(mt->broker);
                            if (zpn_tlv_connected(tlv)) {
                                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: Found data broker: %s is reachable just before switching to double hop. Trying to connect to it for mtunnel: %s", mt->brk_name, mt->mtunnel_id);
                                za_mtunnel_locked_state_machine(mt);
                            } else {
                                do_double_hop = 1;
                            }
                        } else { // we come here when mt->flag.try_if_other_ip_reachable = 1, Now time for double hop connection.
                            do_double_hop = 1;
                        }

                        if(do_double_hop == 1) {
                            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: data broker: %s is NOT reachable! storing in cache and doing double hop for mtunnel: %s", mt->brk_name, mt->mtunnel_id);
                            /* Add the data broker domain name in cache */
                            int res = za_lookup_store_in_user_broker_cache(mt->brk_name);
                            if(res == ZPN_RESULT_NO_ERROR) {
                                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: cannot connect to data broker for %"PRId64"s, double hop enabled. switching to double hop for data broker: %s, mtunnel: %s", (asst_conn_timeout_us/US_PER_SEC), mt->brk_name, mt->mtunnel_id);
                                mt->flag.two_hop = 1; /* start double hop */
                                __sync_fetch_and_add_8(&(stats.num_mtunnel_in_double_hop), 1);
                            } else {
                                /* can not add to two hop cache, memory issue */
                                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: Can not store data broker: %s in double hop cache, setting error for mtunnel: %s", mt->brk_name, mt->mtunnel_id);
                                err = AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_BROKER;
                            }
                        }
                    } else {
                        if (mt->g_brk != mt->g_bfw) {
                            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: broker setup timeout. g_brk:%"PRId64", g_bfw:%"PRId64". Cannot do double hop for mtunnel: %s", mt->g_brk, mt->g_bfw, mt->mtunnel_id);
                            __sync_fetch_and_add_8(&(stats.broker_setup_timeout_not_double_hop_mtunnel), 1);
                        }
                        err = AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_BROKER;
                    }
                    break;
                case za_broker_conn_two_hop:
                    if((mt->flag.two_hop_from_cache) || ((curr_us - mt->start_us) > SECOND_TO_US(assistant_get_doublehop_timeout(global_assistant.gid)))) {
                        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: case za_broker_conn_two_hop, broker: %s from cache. timeup(%d s). err: AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER for mtunnel: %s", mt->brk_name, assistant_get_doublehop_timeout(global_assistant.gid), mt->mtunnel_id);
                        err = AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER;
                    }
                    break;
                case za_broker_conn_open:
                    if(assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)){
                        if((mt->flag.two_hop) &&
                            ((curr_us - mt->start_us) > SECOND_TO_US(assistant_get_doublehop_timeout(global_assistant.gid)))) {
                                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: case za_broker_conn_open, broker: %s from cache. timeup(%d s). err: AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER for mtunnel: %s", mt->brk_name, assistant_get_doublehop_timeout(global_assistant.gid), mt->mtunnel_id);
                                err = AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER;
                            }
                    } else {
                        err = AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND;
                    }
                    break;
                default:
                    err = AST_MT_SETUP_TIMEOUT;
                    break;
                }

                if((mt->state == za_server_conn_open) && (mt->flag.two_hop == 1)) {
                    if(IS_TWO_HOP_MTUNNEL(mt)) {
                        mt->state = za_broker_conn_two_hop;
                        za_mtunnel_locked_state_machine(mt);
                    }
                }
                if(err != NULL) {
                    zpn_assistant_broker_request_ack(mt, err);

                    mt->flag.broker_end_done = 1;
                    za_mtunnel_locked_mark_destroyed(mt, err);
                    za_mtunnel_locked_state_machine(mt);

                    ASSISTANT_DEBUG_MTUNNEL("%s: Mtunnel END. MTUNNEL GONE", mt->mtunnel_id);
                }
            } else if (zpn_assistant_mtunnel_done(mt) || zpn_assistant_mtunnel_expired(mt)) {
                ASSISTANT_DEBUG_MTUNNEL("%s: Mtunnel expired or terminated on both ends, should free it", mt->mtunnel_id);
                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST zpn_assistant_check_mtunnel: Mtunnel expired or terminated on both ends, should free it. calling za_mtunnel_locked_mark_destroyed for mtunnel: %s", mt->mtunnel_id);
                za_mtunnel_locked_mark_destroyed(mt, AST_MT_TERMINATED);
                mt->flag.broker_end_done = 1;
                za_mtunnel_locked_state_machine(mt);
            } else if (zpn_assistant_mtunnel_stuck(mt)) {
                ASSISTANT_LOG(AL_NOTICE, "%s: Mtunnel assistant conn to server stuck", mt->mtunnel_id);
                za_mtunnel_locked_mark_destroyed(mt, AST_MT_CONN_TO_SERVER_STUCK);
                mt->flag.is_terminating_due_to_conn_stuck = 1;
                mt->flag.broker_end_done = 1;
                za_mtunnel_locked_state_machine(mt);
            }

            za_mtunnel_unlock(mt);
        }

        for (mt = TAILQ_FIRST(&(bucket->bucket_reaped_list)); mt != NULL; mt = tmp_mt)
        {
            int free_mt = 0;

            za_mtunnel_lock(mt);
            zpn_assistant_mtunnel_touch_stream(mt);
            tmp_mt = TAILQ_NEXT(mt, bucket_list);
            ASSISTANT_DEBUG_MTUNNEL("Cleaning Mtunnel from reaping queue : %s", mt->mtunnel_id);
            if (zpn_assistant_mtunnel_clean(mt)) {
                TAILQ_REMOVE(&(bucket->bucket_reaped_list), mt, bucket_list);

                __sync_fetch_and_sub_8(&(stats.num_mtunnel_in_reap_queue), 1);

                /*
                 * Now we call zpn_connector_destroy right before mt is reused.
                 */
                //if (mt->double_encrypt) {
                //    if (mt->connector) zpn_connector_destroy(mt->connector);
                //}

                zpn_assistant_mtunnel_log(mt);
                zpn_assistant_mtunnel_log_mconn_stats(mt, 1);

                if (mt->lss_encrypted_ssl) {
                    SSL_free(mt->lss_encrypted_ssl);
                    zpn_mem_free_stats_update(assistant_mtunnel_ssl);
                    mt->lss_encrypted_ssl = NULL;
                }

                if (mt->lss_encrypted_sock > -1) {
                    close(mt->lss_encrypted_sock);
                    mt->lss_encrypted_sock = -1;
                }

                if (mt->err) {
                    ASST_FREE(mt->err);
                    mt->err = NULL;
                }

                free_mt = 1;
                freed++;
            } else {
                __sync_fetch_and_add_8(&(stats.num_mtunnel_in_reaping_queue_but_not_clean), 1);
            }
            za_mtunnel_unlock(mt);

            if (free_mt) {
                zpn_assistant_mtunnel_soft_free(mt);
            }
        }

        pthread_mutex_unlock(&(bucket->lock));
    }

    global_assistant.num_mtunnels_freed += freed;

    /* Since this routine could take a long time, we're exposing the elapsed time to cloud for debugging purposes */
    loop_end_us = epoch_us();
    if (state.timer_cb_stats.max_time_elapsed_in_check_mtunnel_since_last_upload < (loop_end_us - loop_start_us)) {
        state.timer_cb_stats.max_time_elapsed_in_check_mtunnel_since_last_upload = loop_end_us - loop_start_us;
    }

}

static const char *
za_mtunnel_state(enum zpn_assistant_mtunnel_state state)
{
    return mtunnel_states[state];
}

/*
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 */
int za_mtunnel_terminate(struct zpn_assistant_mtunnel *a_mt,
                         int terminate_fast,
                         char *err)
{
    int res;
    if (a_mt->bev_tmp) {
        /* Leave state alone, but clear out out server conn. */
        __sync_add_and_fetch_8(&(stats.terminate_bev_free), 1);
        zlibevent_bufferevent_free(a_mt->bev_tmp);
        a_mt->bev_tmp = NULL;
    }

    if (a_mt->ip_protocol == IPPROTO_UDP) {
        res = zpn_mconn_terminate(&(a_mt->mconn_udp_server.mconn), terminate_fast, 0, err, NULL);
    } else if (a_mt->ip_protocol == IPPROTO_ICMP || a_mt->ip_protocol == IPPROTO_ICMPV6) {
        res = zpn_mconn_terminate(&(a_mt->mconn_icmp_server.mconn), terminate_fast, 0, err, NULL);
    } else {
        res = zpn_mconn_terminate(&(a_mt->mconn_bufferevent_server.mconn), terminate_fast, 0, err, NULL);
    }

    if (res) {
        ASSISTANT_LOG(AL_ERROR, "za_mtunnel_terminate for mconn_bufferevent returned %s", zpn_result_string(res));
    }

    struct zpn_mconn *broker_tlv_mconn = zpn_assistant_mtunnel_broker_mconn(a_mt);

    res = zpn_mconn_terminate(broker_tlv_mconn, terminate_fast, 0, err, NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "za_mtunnel_terminate for mconn_fohh_tlv returned %s", zpn_result_string(res));
    }

    return res;
}

static void zpn_reset_http_parse_state(char* transform_name, void* processor_cookie)
{
    struct zpn_zdx_cookie *zdx_cookie = (struct zpn_zdx_cookie *)processor_cookie;
    if (!zdx_cookie) {
        return ;
    }
    zpn_free_http_parse_state(zdx_cookie->ps);
    zdx_cookie->ps = NULL;
}

static void setup_http_config_inspection_pipeline(struct zpn_mconn* mconn, void* cookie)
{
    zpn_mconn_setup_pipeline(mconn, "zdx mtr config request termination");

    struct zpn_zdx_cookie *zdx_cookie = cookie;
    zdx_cookie->ps = zpn_get_http_parse_state();
    zdx_cookie->ps->data = &(zdx_cookie->mtr);
    zdx_cookie->ps->probe_info_data = &(zdx_cookie->probe_info);
    zpn_mconn_add_transform_to_pipeline(mconn, zpn_terminate_zdx_http_request, zdx_cookie, "ZDX_CONFIG_PARSER", zpn_reset_http_parse_state);
}

static void setup_http_config_response_pipeline(struct zpn_mconn* mconn, void* cookie)
{
    zpn_mconn_setup_pipeline(mconn, "zdx mtr config response forwarding");
}

/*
 *
 * Allocate and lock an mtunnel.
 * Fails on duplicate. (REALLY, REALLY shouldn't happen)
 * Never asynchronous.
 *
 * Locking: it is assumed we have the lock to bucket when success
 */
static
struct zpn_assistant_mtunnel *za_mtunnel_allocate_and_bucket_lock(const char *mtunnel_id,
                                                                  uint64_t mtunnel_id_hash,
                                                                  char **err,
                                                                  uint16_t ip_protocol,
                                                                  uint32_t zpn_probe_type,
                                                                  uint64_t path_decision)
{
    struct zpn_assistant_mtunnel *mtunnel = NULL;
    struct zpn_assistant_bucket *bucket;
    size_t len;
    int res;
    int bucket_id;

    if (!mtunnel_id) {
        ASSISTANT_LOG(AL_CRITICAL, "Request for mtunnel lookup without mtunnel_id");
        *err = AST_MT_SETUP_ERR_NO_MTUNNEL_ID;
        return NULL;
    }

    len = strlen(mtunnel_id);
    bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel_id_hash);
    bucket = &(global_assistant.buckets[bucket_id]);

    pthread_mutex_lock(&(bucket->lock));

    mtunnel = argo_hash_lookup_with_hash(bucket->mtunnel_by_id,
                                         mtunnel_id,
                                         len,
                                         mtunnel_id_hash,
                                         NULL);
    if (mtunnel) {
        za_mtunnel_lock(mtunnel);
        if ((path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0 ||
            (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0) {
            /* User broker sent 2 zpn_broker_request and both of them landed here - ignore the later one. */
            za_mtunnel_unlock(mtunnel);
            pthread_mutex_unlock(&(bucket->lock));
            *err = AST_MT_SETUP_ERR_BRK_REQ_TIMEOUT_RETRY_DUP;
            return NULL;
        }
        za_mtunnel_unlock(mtunnel);
        pthread_mutex_unlock(&(bucket->lock));
        ASSISTANT_LOG(AL_WARNING, "%s: Duplicate mtunnel!?!?", mtunnel_id);
        *err = AST_MT_SETUP_ERR_DUP_MT_ID;
        return NULL;
    }

    mtunnel = zpn_assistant_mtunnel_alloc();
    if (NULL == mtunnel) {
        pthread_mutex_unlock(&(bucket->lock));
        ASSISTANT_LOG(AL_CRITICAL, "%s: Memory allocation failure - mtunnel!!", mtunnel_id);
        *err = AST_MT_MEMORY_ALLOCATION_FAILURE;
        return NULL;
    } else {
        mtunnel->lss_encrypted_sock = -1;
        mtunnel->mtunnel_id = ASST_STRDUP(mtunnel_id, len);
        if (!mtunnel->mtunnel_id) goto failmessage;
        mtunnel->mtunnel_id_hash = mtunnel_id_hash;

        mtunnel->log.mtunnel_id = mtunnel->mtunnel_id;
        mtunnel->start_us = epoch_us();
        mtunnel->start_time_in_cloud_us = assistant_state_get_current_time_cloud_us();

        res = argo_hash_store_with_hash(bucket->mtunnel_by_id,
                                        mtunnel->mtunnel_id,
                                        len,
                                        mtunnel->mtunnel_id_hash,
                                        1,
                                        mtunnel);
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ASSISTANT_LOG(AL_CRITICAL, "%s: Could not store mtunnel in bucket hash table", mtunnel_id);
            *err = AST_MT_MEMORY_ALLOCATION_FAILURE;
            goto fail;
        }
        TAILQ_INSERT_HEAD(&(bucket->bucket_mtunnel_list), mtunnel, bucket_list);
        __sync_add_and_fetch_8(&(stats.num_mtunnel_in_buckets_queue), 1);

        mtunnel->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
        mtunnel->termination_started = 0;

        /*
         * Init mconn's within mtunnel.
         */
        if (ip_protocol == IPPROTO_UDP) {
            ASSISTANT_DEBUG_MTUNNEL("Initializing mconn_udp");
            res = zpn_mconn_udp_bufferevent_init(&(mtunnel->mconn_udp_server), mtunnel, mconn_bufferevent_udp_s);
        } else if (ip_protocol == IPPROTO_ICMP) {
            res = zpn_mconn_icmp_init(&(mtunnel->mconn_icmp_server), mtunnel, mconn_icmp_s);
        } else if (ip_protocol == IPPROTO_ICMPV6) {
            res = zpn_mconn_icmpv6_init(&(mtunnel->mconn_icmp_server), mtunnel, mconn_icmp_s);
        } else {
            res = zpn_mconn_bufferevent_init(&(mtunnel->mconn_bufferevent_server), mtunnel, mconn_bufferevent_s);
            if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
                struct zpn_mconn* server_tcp_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
                server_tcp_mconn->setup_pipeline_cb = setup_http_config_inspection_pipeline;
                server_tcp_mconn->pipeline_cookie = get_zpn_zdx_cookie();
            }
        }
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ASSISTANT_LOG(AL_ERROR, "error initing bufferevent mconn");
            *err = AST_MT_SETUP_ERR_INIT_BE_MCONN;
            goto fail;
        }

        res = zpn_mconn_fohh_tlv_init(&(mtunnel->mconn_fohh_tlv_broker), mtunnel, mconn_fohh_tlv_c);
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ASSISTANT_LOG(AL_ERROR, "error initing fohh_tlv mconn");
            *err = AST_MT_SETUP_ERR_INIT_FOHH_MCONN;
            goto fail;
        }

        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* fohh_tlv_broker_mconn = &(mtunnel->mconn_fohh_tlv_broker.mconn);
            fohh_tlv_broker_mconn->setup_pipeline_cb = setup_http_config_response_pipeline;
        }

        res = zpn_mconn_zrdt_tlv_init(&(mtunnel->mconn_zrdt_tlv_broker), mtunnel, mconn_zrdt_tlv_c);
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ZPN_LOG(AL_ERROR, "error initing zrdt_tlv mconn");
            *err = AST_MT_SETUP_ERR_INIT_FOHH_MCONN;
            goto fail;
        }
        if (zpn_probe_type == zpn_probe_type_zdx_mtr) {
            struct zpn_mconn* zrdt_tlv_broker_mconn = &(mtunnel->mconn_zrdt_tlv_broker.mconn);
            zrdt_tlv_broker_mconn->setup_pipeline_cb = setup_http_config_response_pipeline;
        }

        if (ip_protocol == IPPROTO_UDP) {
            ASSISTANT_DEBUG_MTUNNEL("Add global owner for mconn_udp");
            res = zpn_mconn_add_global_owner(&(mtunnel->mconn_udp_server.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        } else if (ip_protocol == IPPROTO_ICMP || ip_protocol == IPPROTO_ICMPV6) {
            ASSISTANT_DEBUG_MTUNNEL("Add global owner for mconn_icmp");
            res = zpn_mconn_add_global_owner(&(mtunnel->mconn_icmp_server.mconn),
                                             0,
                                             mtunnel,
                                             NULL,
                                             0,
                                             &za_server_global_call_set);
        } else {
            res = zpn_mconn_add_global_owner(&(mtunnel->mconn_bufferevent_server.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        }
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ASSISTANT_LOG(AL_CRITICAL, "Could not bind client global owner.");
            *err = AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER;
            goto fail;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->mconn_fohh_tlv_broker.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ASSISTANT_LOG(AL_CRITICAL, "Could not bind client global owner.");
            *err = AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER;
            goto fail;
        }

        res = zpn_mconn_add_global_owner(&(mtunnel->mconn_zrdt_tlv_broker.mconn),
                                         0,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);
        if (res) {
            pthread_mutex_unlock(&(bucket->lock));
            ZPN_LOG(AL_CRITICAL, "Could not bind client global owner.");
            *err = AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER;
            goto fail;
        }

        ASSISTANT_DEBUG_MTUNNEL("%s: Allocated", mtunnel->mtunnel_id);
    } /*this code had silent failure to mtunnel alloc and crash - fixing it */

    return mtunnel;

failmessage:
    ASSISTANT_LOG(AL_CRITICAL, "Probable allocation failure creating mtunnel");
fail:
    if (mtunnel) {
        if (mtunnel->mtunnel_id) {
            ASST_FREE(mtunnel->mtunnel_id);
            mtunnel->mtunnel_id = NULL;
        }
        if (mtunnel->err) {
            ASST_FREE(mtunnel->err);
            mtunnel->err = NULL;
        }
        zpn_assistant_mtunnel_soft_free(mtunnel);
    }
    return NULL;
}

/*
 * Locking: it is assumed we have the lock to bucket when success
 */
static
struct zpn_assistant_mtunnel *za_mtunnel_lookup_and_lock_bucket(const char *mtunnel_id, size_t tun_id_len,
                                                                uint64_t mtunnel_id_hash, int return_if_reaping)
{
    struct zpn_assistant_mtunnel *mtunnel;
    struct zpn_assistant_bucket *bucket;

    int bucket_id;

    if (!mtunnel_id) {
        ASSISTANT_LOG(AL_CRITICAL, "Request for mtunnel lookup without mtunnel_id");
        return NULL;
    }

    bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel_id_hash);
    bucket = &(global_assistant.buckets[bucket_id]);

    pthread_mutex_lock(&(bucket->lock));

    mtunnel = argo_hash_lookup_with_hash(bucket->mtunnel_by_id,
                                         mtunnel_id,
                                         tun_id_len,
                                         mtunnel_id_hash,
                                         NULL);
    if (!mtunnel) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Lookup: not found", mtunnel_id);
        pthread_mutex_unlock(&(bucket->lock));
    } else {
        if (mtunnel->state >= za_reaping && 0 == return_if_reaping) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Lookup: found, but reaping.", mtunnel_id);
            pthread_mutex_unlock(&(bucket->lock));
            mtunnel = NULL;
        } else {
            ASSISTANT_DEBUG_MTUNNEL("%s: Lookup: found", mtunnel_id);
        }
    }
    return mtunnel;
}

static
void za_mtunnel_bucket_lock(struct zpn_assistant_mtunnel *mtunnel)
{
    struct zpn_assistant_bucket *bucket;
    int bucket_id;

    if (mtunnel) {
        bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
        bucket = &(global_assistant.buckets[bucket_id]);
        pthread_mutex_lock(&(bucket->lock));
    } else {
        ASSISTANT_LOG(AL_WARNING, "Unlocking NULL tunnel");
    }
}

static
void za_mtunnel_bucket_unlock(struct zpn_assistant_mtunnel *mtunnel)
{
    struct zpn_assistant_bucket *bucket;
    int bucket_id;

    if (mtunnel) {
        bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
        bucket = &(global_assistant.buckets[bucket_id]);
        pthread_mutex_unlock(&(bucket->lock));
    } else {
        ASSISTANT_LOG(AL_WARNING, "Unlocking NULL tunnel");
    }
}

void za_mtunnel_lock(struct zpn_assistant_mtunnel *mtunnel)
{
    if (mtunnel) {
        pthread_mutex_lock(&(mtunnel->lock));
    } else {
        ASSISTANT_LOG(AL_WARNING, "Locking NULL tunnel");
    }
}

void za_mtunnel_unlock(struct zpn_assistant_mtunnel *mtunnel)
{
    if (mtunnel) {
        pthread_mutex_unlock(&(mtunnel->lock));
    } else {
        ASSISTANT_LOG(AL_WARNING, "Unlocking NULL tunnel");
    }
}

/*
 *
 * Mark a locked mtunnel as destroyed.
 *
 * Will start the process of destroying the mtunnel, but much of the
 * operation will end up being asynchronous.
 *
 * Never asynchronous.
 *
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 */
static
int za_mtunnel_locked_mark_destroyed(struct zpn_assistant_mtunnel *mtunnel, const char *err)
{
    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "Destroying NULL tunnel");
        return ZPN_RESULT_BAD_ARGUMENT;
    } else {
        if (err && !mtunnel->err) mtunnel->err = ASST_STRDUP(err, strlen(err));
        if (mtunnel->state < za_reaping) {
            mtunnel->state = za_reaping;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Detach wants you to run state machine, eventually, on the mtunnel.
 *
 * Locking: ???
 */
int za_mtunnel_locked_detach_broker(struct zpn_assistant_mtunnel *mtunnel)
{
    struct zpn_assistant_broker_data *broker;
    int res;

    if (!mtunnel) {
        ASSISTANT_LOG(AL_CRITICAL, "Bad args");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (!mtunnel->broker) {
        ASSISTANT_LOG(AL_CRITICAL, "Mtunnel is already detached from client connection");
        return ZPN_RESULT_ERR;
    }

    if (!mtunnel->flag.in_broker_list) {
        ASSISTANT_LOG(AL_CRITICAL, "Mtunnel is not in broker list");
        return ZPN_RESULT_ERR;
    }

    broker = mtunnel->broker;

    pthread_mutex_lock(&(broker->lock));

    TAILQ_REMOVE(&(broker->mtunnel_list), mtunnel, broker_list);

    if (broker->mtunnels_by_tag && mtunnel->broker_tag) {
        res = argo_hash_remove(broker->mtunnels_by_tag,
                               &(mtunnel->broker_tag),
                               sizeof(mtunnel->broker_tag),
                               NULL);
        if (res) {
            ASSISTANT_LOG(AL_NOTICE, "%s: Could not remove tag %d from hash table", mtunnel->mtunnel_id, mtunnel->broker_tag);
        }
        mtunnel->flag.in_broker_list = 0;
    }

    pthread_mutex_unlock(&(broker->lock));

    /* We are done with the broker, discard anything not sent out yet. */
    zpn_mconn_discard_client_data(&(mtunnel->mconn_fohh_tlv_broker.mconn), NULL);

    if ((mtunnel->state < za_reaping) && (!IS_TWO_HOP_MTUNNEL(mtunnel))) {
        mtunnel->state = za_reaping;
        mtunnel->flag.broker_end_done = 1;
    }
    mtunnel->flag.in_broker_list = 0;
    mtunnel->broker = NULL;

    return ZPN_RESULT_NO_ERROR;
}

/* Connection event to connect flags */
static void za_mtunnel_event_to_connect_flags(struct zpn_assistant_mtunnel *mtunnel, short events, int bev_errno)
{
    if (events & BEV_EVENT_CONNECTED) {
        mtunnel->flag.connect_success = 1;
        //if (zpn_debug_get(ASSISTANT_DEBUG_MTUNNEL_BIT)) {
        //    ASSISTANT_LOG(AL_DEBUG, "%s: Connection established to Server", mtunnel->mtunnel_id);
        //}
    } else if (events & BEV_EVENT_EOF) {
        mtunnel->flag.connect_eof = 1;
        /* Skip logging on: EAGAIN,EINPROGRESS + EOF */
        if ((bev_errno > 0) && (bev_errno != EAGAIN) && (bev_errno != EINPROGRESS)) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Connection closed by Server, err: %s", mtunnel->mtunnel_id,
                              evutil_socket_error_to_string(bev_errno));
        }
    } else if (events & BEV_EVENT_TIMEOUT) {
        mtunnel->flag.connect_timeout = 1;
        if (bev_errno > 0) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Connection timeout to Server, err: %s", mtunnel->mtunnel_id,
                              evutil_socket_error_to_string(bev_errno));
        }
    } else if (events & BEV_EVENT_ERROR) {
        mtunnel->flag.connect_error = 1;

        /* Dont filter by errno > 0, if explicit error indication bit is set */
        ASSISTANT_DEBUG_MTUNNEL("%s: Connection error to Server, err: %s", mtunnel->mtunnel_id,
                          evutil_socket_error_to_string(bev_errno));
    }
}

/* Clear connect flags */
static void za_mtunnel_clear_connect_flags(struct zpn_assistant_mtunnel *mtunnel)
{
    mtunnel->flag.connect_requested = 0;
    mtunnel->flag.connect_success = 0;
    mtunnel->flag.connect_timeout = 0;
    mtunnel->flag.connect_eof = 0;
    mtunnel->flag.connect_error = 0;
}

/* Did connect to server fail ? */
static int za_mtunnel_is_server_connect_failed(struct zpn_assistant_mtunnel *mtunnel)
{
    if ((mtunnel->flag.connect_requested == 1) && (mtunnel->flag.connect_success == 0)) {
        if ((mtunnel->flag.connect_eof == 1) || (mtunnel->flag.connect_error == 1) || (mtunnel->flag.connect_timeout == 1)) {
            return 1;
        }
    }

    return 0;
}

/* Get connect failure error string */
static char* za_mtunnel_get_server_connect_error(struct zpn_assistant_mtunnel *mtunnel)
{
    /* Make sure connect was requested but not established */
    if ((mtunnel->flag.connect_requested == 1) && (mtunnel->flag.connect_success == 0)) {
        if (mtunnel->flag.connect_eof == 1) {
            return AST_MT_SETUP_ERR_OPEN_SERVER_CLOSE;
        } else if (mtunnel->flag.connect_timeout == 1) {
            return AST_MT_SETUP_ERR_OPEN_SERVER_TIMEOUT;
        } else if (1 == mtunnel->flag.connect_ssl_error) {
            __sync_fetch_and_add_8(&(global_assistant.num_mtunnel_inspt_appl_SSL_connect_failure), 1);
            return AST_MT_SETUP_ERR_SSL_ERROR;
        } else if (mtunnel->flag.connect_error == 1) {
            return AST_MT_SETUP_ERR_OPEN_SERVER_ERROR;
        }

        /* Fallback to generic error */
        return AST_MT_SETUP_ERR_OPEN_SERVER_CONN;
    }

    /* No error, this should not happen */
    return BRK_ERR_UNKNOWN;
}

static void za_mtunnel_send_server_connect_failed(struct zpn_assistant_mtunnel *mtunnel)
{
    char *err = za_mtunnel_get_server_connect_error(mtunnel);

    zpn_assistant_broker_request_ack(mtunnel, err);
    mtunnel->flag.broker_end_done = 1;
    za_mtunnel_locked_mark_destroyed(mtunnel, err);
}

/* Display connects flags for debugging */
static void za_mtunnel_server_connect_flags_internal_display(struct zpn_assistant_mtunnel *mtunnel)
{
    char *err = "no error, connect succeeded";

    if (za_mtunnel_is_server_connect_failed(mtunnel)) {
        err = za_mtunnel_get_server_connect_error(mtunnel);
    }

    ASSISTANT_DEBUG_MTUNNEL("%s: Connect flags: connect_requested: %d, connect_success: %d, connect_eof: %d, connect_timeout: %d, connect_error: %d, error: %s",
            mtunnel->mtunnel_id, mtunnel->flag.connect_requested, mtunnel->flag.connect_success,
            mtunnel->flag.connect_eof, mtunnel->flag.connect_timeout, mtunnel->flag.connect_error, err);
    if (mtunnel->flag.zdx_webprobe_cache_connect_end_terminated) {
        ASSISTANT_DEBUG_MTUNNEL(
                "%s: Connect zdx_webprobe_cache_connect flags: connect_end_terminated: %d, connect_requested: %d, connect_success: %d, connect_error: %d, connect_ssl_request: %d, connect_ssl_success: %d, connect_ssl_error: %d",
                mtunnel->mtunnel_id,
                mtunnel->flag.zdx_webprobe_cache_connect_end_terminated,
                mtunnel->flag.zdx_webprobe_cache_connect_requested,
                mtunnel->flag.zdx_webprobe_cache_connect_success,
                mtunnel->flag.zdx_webprobe_cache_connect_error,
                mtunnel->flag.zdx_webprobe_cache_connect_ssl_requested,
                mtunnel->flag.zdx_webprobe_cache_connect_ssl_success,
                mtunnel->flag.zdx_webprobe_cache_connect_ssl_error);
    }
    if (mtunnel->flag.zdx_mtr_connect_end_terminated) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Connect to server flag for zdx mtr: zdx_mtr_connect_end_terminated: %d",
                                mtunnel->mtunnel_id,
                                mtunnel->flag.zdx_mtr_connect_end_terminated);
    }
}

static int populate_log_fields(void* data, void* log_data)
{
    struct zpn_assistant_mtunnel *mtunnel = data;
    if (!mtunnel) {
        return ZPN_RESULT_ERR;
    }
    struct zpn_waf_http_exchanges_log* log = log_data;
    if (!log_data) {
        return ZPN_RESULT_ERR;
    }

    log->mtunnel_id = mtunnel->mtunnel_id;
    log->c_uid = mtunnel->user_id_str;
    log->domain = mtunnel->domain;
    log->c_port = mtunnel->server_port_he;
    log->g_ast = global_assistant.gid;
    log->g_cst = mtunnel->g_cst;
    log->g_app = mtunnel->g_app;
    log->g_app_grp = mtunnel->g_app_grp;
    log->g_insp_policy = mtunnel->gid_inspection_rule;
    log->c_pub_ip = mtunnel->client_pub_ip;
    log->ssl_inspection = 0;
    if (mtunnel->e_inspection_mode == zpn_traffic_inspection_https_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_https ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls)
    {
        log->ssl_inspection = 1;
    }
    log->double_encrypt = 0;
    if (mtunnel->e_inspection_mode == zpn_traffic_inspection_https_double_encrypted ||
       mtunnel->e_inspection_mode == zpn_traffic_inspection_http_double_encrypted ||
       mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_double_encrypted ||
       mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls_double_encrypted)
    {
        log->double_encrypt = 1;
    }
    return ZPN_RESULT_NO_ERROR;
}

static int populate_idps_log_fields(void* cookie, void* log_data)
{
    struct zpn_assistant_mtunnel *mtunnel = cookie;
    if (!mtunnel) {
        return ZPN_RESULT_ERR;
    }
    if (!log_data) {
        return ZPN_RESULT_ERR;
    }

    struct zpn_idps_status* status = log_data;
    status->mtunnel_id = mtunnel->mtunnel_id;
    status->c_uid = mtunnel->user_id_str;
    status->c_pub_ip = mtunnel->client_pub_ip;
    status->domain = mtunnel->domain;
    status->app_domain = mtunnel->app_domain;
    status->app_domain_gid = mtunnel->gid_app_domain;
    status->s_port = mtunnel->server_port_he;
    status->c_port = mtunnel->client_port_he;
    if (mtunnel->ip_protocol == IPPROTO_TCP)
        status->transport = transport_tcp;
    else if (mtunnel->ip_protocol == IPPROTO_UDP)
        status->transport = transport_udp;
    status->g_ast = global_assistant.gid;
    status->g_cst = mtunnel->g_cst;
    status->g_app = mtunnel->g_app;
    status->g_app_grp = mtunnel->g_app_grp;
    status->g_insp_policy = mtunnel->gid_inspection_rule;
    status->g_insp_profile = mtunnel->gid_inspection_profile;

    status->ssl_inspection = 0;
    if (mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls)
    {
        status->ssl_inspection = 1;
    }
    status->double_encrypt = 0;
    if (mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_ldap_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_smb_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_krb_double_encrypted ||
        mtunnel->e_inspection_mode == zpn_traffic_inspection_ptag_double_encrypted)
    {
        status->double_encrypt = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

static inline SSL_CTX* zpn_mtunnel_locked_get_waf_inspection_ssl_ctx(struct zpn_assistant_mtunnel *mtunnel)
{
    SSL_CTX* ctx = NULL;
    if (mtunnel && (mtunnel->inspection_ctx && mtunnel->inspection_ctx->ssl && 0 == mtunnel->flag.waf_req_error)) {
        ctx = SSL_get_SSL_CTX(mtunnel->inspection_ctx->ssl);
    }
    return ctx;
}
static inline SSL_CTX* zpn_mtunnel_locked_get_waf_client_ssl_ctx(struct zpn_assistant_mtunnel *mtunnel)
{
    SSL_CTX* ctx = NULL;
    if (mtunnel && (mtunnel->inspection_ctx && mtunnel->inspection_ctx->cln_ssl && 0 == mtunnel->flag.waf_req_error)) {
        ctx = SSL_get_SSL_CTX(mtunnel->inspection_ctx->cln_ssl);
    }
    return ctx;
}

/* mtunnel->e_inspection_mode - value is set upon WAF resource fetch completion.
** See mtunnel_locked_set_waf_inspection_mode function for setting this value.
** by default at mtunnel creation it is set to zpn_traffic_inspection_disabled.
*/
static inline enum zpn_traffic_inspection_mode
get_mtunnel_locked_inspection_mode(struct zpn_assistant_mtunnel* mtunnel)
{
    enum zpn_traffic_inspection_mode mode = zpn_traffic_inspection_disabled;
    if (mtunnel) mode = mtunnel->e_inspection_mode;
    return mode;
}

static struct zpn_waf_profile* zpn_mtunnel_get_inspection_profile(struct zpn_assistant_mtunnel *mtunnel)
{
    struct zpn_waf_profile* profile = NULL;
    if (mtunnel && (mtunnel->inspection_ctx && 0 == mtunnel->flag.waf_req_error)) {
        profile = mtunnel->inspection_ctx->zpn_waf_inspct_prfl;
        if (profile) {
            profile->populate_idps_log_fields = &populate_idps_log_fields;
            profile->populate_log_fields = &populate_log_fields;
            if ((mtunnel->e_inspection_mode == zpn_traffic_inspection_https_double_encrypted) ||
                (mtunnel->e_inspection_mode == zpn_traffic_inspection_https) ||
                (mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls) ||
                (mtunnel->e_inspection_mode == zpn_traffic_inspection_auto_tls_double_encrypted)) {
                profile->cert_id = mtunnel->inspection_ctx->cert_id;
            } else {
                profile->cert_id = 0;
            }
        }
    }
    return profile;
}

static int za_mtunnel_connect_mconns(struct zpn_assistant_mtunnel *mtunnel, struct bufferevent *bev, int set_mt_state)
{
    int res;
    int64_t now_s = epoch_s();
    //ZPN_LOG(AL_DEBUG, "mtunnel->gid_inspection_profile = %ld, mtunnel->e_inspection_type =%d with inspection mode = %s",
    //        (long)(mtunnel->gid_inspection_profile),  mtunnel->e_inspection_type,
    //        traffic_inspection_modes[mtunnel->e_inspection_mode]);
    struct zpn_waf_profile* profile = zpn_mtunnel_get_inspection_profile(mtunnel);
    enum zpn_traffic_inspection_mode mode = get_mtunnel_locked_inspection_mode(mtunnel);
    if (mode && !mtunnel->flag.ptag_mode && NULL == profile) { //mode disabled or PTag does not require WAF profile
        ZPN_LOG(AL_ERROR, "%s: Could not setup waf profile", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR;
    }

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        if (set_mt_state == 1) {
            mtunnel->state = za_server_conn_open;
        }
        ASSISTANT_DEBUG_MTUNNEL(
                "Since in MTRs we dont send anything to server. Hence, we can just avoid attaching to it by not adding "
                "local owner");
        /* return here ensures that we don't add local owner as by default below for server side. */
        return ZPN_RESULT_NO_ERROR;
    }

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_requests), 1);
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
        mtunnel->zpn_zdx_webprobe_start_ts = now_s;

        struct zpn_connector_webprobe_https_inspect *connector = zpn_connector_webprobe_https_inspect_create(
                mtunnel,
                mconn_bufferevent_thread_id,
                &(mtunnel->mconn_bufferevent_server),
                mtunnel->mtunnel_id,
                mtunnel->domain,
                mtunnel->server_port_he,
                mtunnel->g_ast,
                (const char *)assistant_state_get_cloud_name(),
                assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn,
                assistant_mtunnel_inject_webprobe_cache_https_response_to_mconn,
                assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn,
                zpn_zdx_webprobe_cache_https_pipeline_data_release,
                assistant_mtunnel_zdx_webprobe_cache_https_stats_ssl_data_get);
        if (!connector) {
            ZPN_LOG(AL_ERROR, "Could not allocate the webprobe_cache https connector");
            return ZPN_RESULT_ERR;
        }

        mtunnel->connector = (struct zpn_connector *)connector;
        ASSISTANT_DEBUG_MTUNNEL("Created connector webprobe_cache https inspect pipeline");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(
                &(connector->inspect_common->mconn_cpair.mconn), 1, mtunnel, NULL, 0, &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for wepbrobe_cache https client side connector, failed - %s",
                    zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        res = zpn_mconn_add_global_owner(
                &(connector->inspect_common->mconn_spair.mconn), 1, mtunnel, NULL, 0, &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for webprobe_cache https server side connector, failed - %s",
                    zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        res = zpn_connector_webprobe_http_inspect_connect_app_server(
                mconn_bufferevent_thread_id, connector->inspect_common, &(mtunnel->mconn_bufferevent_server.mconn));
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "webprobe_cache https server side connect returned: %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        struct zpn_mconn *server_tcp_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        // always hold server termination on a connector to allow for report forwarding
        server_tcp_mconn->hold_forward_mtunnel_end = 1;

        /* set state as connected to server to keep continue through mtunnel states for broker connection */
        if (set_mt_state == 1) {
            mtunnel->state = za_server_conn_open;
        }

        ASSISTANT_DEBUG_MTUNNEL(
                "webprobe_cache https server side - end terminate webprobe_cache pipeline by not adding local owner");

        /* return here ensures that we don't add local owner as by default below for server side. */
        /* Local owner may be added only on demand by selected webprobe_cache request */
        return ZPN_RESULT_NO_ERROR;

    } else if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe && zpn_zdx_http_webprobe_cache_config_is_intercept_enabled()) {

        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_requests), 1);
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
        mtunnel->zpn_zdx_webprobe_start_ts = now_s;

        struct zpn_connector_webprobe_http_inspect *connector = zpn_connector_webprobe_http_inspect_create(mtunnel,
                                                                                                           mconn_bufferevent_thread_id,
                                                                                                           &(mtunnel->mconn_bufferevent_server),
                                                                                                           mtunnel->mtunnel_id,
                                                                                                           mtunnel->domain,
                                                                                                           mtunnel->server_port_he,
                                                                                                           mtunnel->g_ast,
                                                                                                           (const char*)assistant_state_get_cloud_name(),
                                                                                                           assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_and_webprobe_cache_http_inject_request_to_mconn,
                                                                                                           assistant_mtunnel_inject_webprobe_cache_response_to_mconn,
                                                                                                           assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn,
                                                                                                           zpn_zdx_webprobe_cache_http_pipeline_data_release);

        if (!connector) {
            ZPN_LOG(AL_ERROR, "Could not allocate the webprobe_cache http connector");
            return ZPN_RESULT_ERR;
        }

        mtunnel->connector = (struct zpn_connector *) connector;
        ASSISTANT_DEBUG_MTUNNEL("Created connector webprobe_cache http inspect pipeline");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(connector->inspect_common->mconn_cpair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for wepbrobe_cache http client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        res = zpn_mconn_add_global_owner(&(connector->inspect_common->mconn_spair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for webprobe_cache http server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        res  = zpn_connector_webprobe_http_inspect_connect_app_server(mconn_bufferevent_thread_id,
                                                                     connector->inspect_common,
                                                                     &(mtunnel->mconn_bufferevent_server.mconn));
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "webprobe_cache http server side connect returned: %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }

        struct zpn_mconn *server_tcp_mconn = &(mtunnel->mconn_bufferevent_server.mconn);
        // always hold server termination on a connector to allow for report forwarding
        server_tcp_mconn->hold_forward_mtunnel_end = 1;

        /* set state as connected to server to keep continue through mtunnel states for broker connection */
        if (set_mt_state == 1) {
            mtunnel->state = za_server_conn_open;
        }

        ASSISTANT_DEBUG_MTUNNEL("webprobe_cache http server side - end terminate webprobe_cache pipeline by not adding local owner");

        /* return here ensures that we don't add local owner as by default below for server side. */
        /* Local owner may be added only on demand by selected webprobe_cache request */
        return ZPN_RESULT_NO_ERROR;

    } else if (zpn_traffic_inspection_http == mode || zpn_traffic_inspection_ptag == mode ||
               zpn_traffic_inspection_auto == mode || zpn_traffic_inspection_krb == mode ||
               zpn_traffic_inspection_ldap == mode || zpn_traffic_inspection_smb == mode) {
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
        mtunnel->connector = (struct zpn_connector *)zpn_connector_inspect_create(mtunnel, mconn_bufferevent_thread_id,
                                                                                  (void *)&(mtunnel->mconn_bufferevent_server),
                                                                                  profile, mode, &populate_idps_log_fields,
                                                                                  mtunnel->flag.debug_flag);
        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Created Connector Inspection");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_inspect *)mtunnel->connector)->mconn_spair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");
        res  = zpn_connector_inspect_connect(mconn_bufferevent_thread_id,
                                             (struct zpn_connector_inspect *)mtunnel->connector,
                                             &(mtunnel->mconn_bufferevent_server.mconn),
                                             1);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("Connected server side");
    } else if (zpn_traffic_inspection_http_double_encrypted == mode || zpn_traffic_inspection_ldap_double_encrypted == mode ||
               zpn_traffic_inspection_smb_double_encrypted == mode || zpn_traffic_inspection_krb_double_encrypted == mode ||
               zpn_traffic_inspection_ptag_double_encrypted == mode || zpn_traffic_inspection_auto_double_encrypted == mode) {
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
        mtunnel->connector = (struct zpn_connector *)zpn_connector_tun_inspect_create(mtunnel,
                                                                                      ssl_ctx_connector, NULL, 1, 0,
                                                                                      mconn_bufferevent_thread_id,
                                                                                      (void *)&(mtunnel->mconn_bufferevent_server),
                                                                                      profile,
                                                                                      mode,
                                                                                      &populate_idps_log_fields,
                                                                                      mtunnel->flag.debug_flag);
        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Created Double Encrypted Connector Inspection");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_inspect *)mtunnel->connector)->mconn_spair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");
        res  = zpn_connector_tun_inspect_connect(mconn_bufferevent_thread_id,
                                                 (struct zpn_connector_tun_inspect *)mtunnel->connector,
                                                 &(mtunnel->mconn_bufferevent_server.mconn),
                                                 1,
                                                 mtunnel->double_encrypt,
                                                 NULL, mode);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("Connected server side");
    } else if (zpn_traffic_inspection_https_double_encrypted == mode || zpn_traffic_inspection_auto_tls_double_encrypted == mode) {
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
#ifdef _SSL_CTX_FROM_WAF_SESS_MNG_
        SSL_CTX* ssl_ctx_app = zpn_mtunnel_locked_get_waf_inspection_ssl_ctx(mtunnel);
#else /* cert from disk */
        SSL_CTX* ssl_ctx_app = zpn_waf_get_ssl_ctx(mtunnel->domain);
#endif
        if (!ssl_ctx_app) {
            ZPN_LOG(AL_ERROR, "Failed to find inspection certificate for : %s", mtunnel->domain);
            return ZPN_RESULT_ERR;
        }
        mtunnel->connector = (struct zpn_connector *)zpn_connector_tun_tls_inspect_create(mtunnel,
                                                                                          ssl_ctx_app, NULL,
                                                                                          ssl_ctx_connector, NULL,
                                                                                          1, 0,
                                                                                          mconn_bufferevent_thread_id,
                                                                                          (void *)&(mtunnel->mconn_bufferevent_server),
                                                                                          profile,
                                                                                          mtunnel->gid_inspection_appl,
                                                                                          mode,
                                                                                          &populate_idps_log_fields,
                                                                                          mtunnel->flag.debug_flag);
        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Created TLS Double Encrypted Connector Inspection");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_tls_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_tls_inspect *)mtunnel->connector)->mconn_spair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");
        res  = zpn_connector_tun_tls_inspect_connect(mconn_bufferevent_thread_id,
                                                 (struct zpn_connector_tun_tls_inspect *)mtunnel->connector,
                                                 &(mtunnel->mconn_bufferevent_server.mconn),
                                                 1,
                                                 1,  // is_ssl
                                                 NULL, mode, mtunnel->flag.debug_flag);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("Connected server side");
    } else if (zpn_traffic_inspection_https == mode || zpn_traffic_inspection_auto_tls == mode) {
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
#ifdef _SSL_CTX_FROM_WAF_SESS_MNG_
        SSL_CTX* ssl_ctx_app = zpn_mtunnel_locked_get_waf_inspection_ssl_ctx(mtunnel);
#else /* cert from disk */
        SSL_CTX* ssl_ctx_app = zpn_waf_get_ssl_ctx(mtunnel->domain);
#endif
        if (!ssl_ctx_app) {
            ZPN_LOG(AL_ERROR, "Failed to find inspection certificate for : %s", mtunnel->domain);
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Found inspection certificate for : %s", mtunnel->domain);
        mtunnel->connector = (struct zpn_connector *)zpn_connector_tls_inspect_create(mtunnel,
                                                                                      ssl_ctx_app, NULL, 1, 0,
                                                                                      mconn_bufferevent_thread_id,
                                                                                      (void *)&(mtunnel->mconn_bufferevent_server),
                                                                                      profile,
                                                                                      mtunnel->gid_inspection_appl,
                                                                                      mode,
                                                                                      &populate_idps_log_fields);
        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocate connector");
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Created connector insp https");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tls_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tls_inspect *)mtunnel->connector)->mconn_spair.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");
        res = zpn_connector_tls_inspect_connect(mconn_bufferevent_thread_id,
                                                (struct zpn_connector_tls_inspect *)mtunnel->connector,
                                                &(mtunnel->mconn_bufferevent_server.mconn),
                                                1,
                                                1,  // is_ssl
                                                NULL, mode, mtunnel->flag.debug_flag);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("Connected server side");
    } else if (mtunnel->double_encrypt) {
        int mconn_bufferevent_thread_id;
        zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);

        mtunnel->connector = (struct zpn_connector *)zpn_connector_tun_create(mtunnel, ssl_ctx_connector, NULL, 1, 0);
        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }

        ASSISTANT_DEBUG_MTUNNEL("Created connector tun");
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mtunnel->connector)->mconn_c.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mtunnel->connector)->mconn_s.mconn),
                                         1,
                                         mtunnel,
                                         NULL,
                                         0,
                                         &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");

        res  = zpn_connector_tun_connect(mconn_bufferevent_thread_id,
                                          (struct zpn_connector_tun *)mtunnel->connector,
                                          &(mtunnel->mconn_bufferevent_server.mconn),
                                          1,
                                          mtunnel->double_encrypt,
                                          NULL);
        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("Connected server side");
    }
    /* we should reach here only if the WAF resources been fetched so no need to check - softassert can help */

    if (set_mt_state == 1) {
        mtunnel->state = za_server_conn_open;
    }

    if (bev != NULL) {
        /* Attach self to client... */
        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_bufferevent_server.mconn),
                                        0,
                                        bev,
                                        NULL,
                                        0,
                                        &mconn_bufferevent_calls);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Error adding local owner: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.process_event_bev_free_on_local_owner_fail), 1);
            zlibevent_bufferevent_free(bev);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

#ifdef IDPS_DEBUG
/* Print connection */
static void debug_connection(int fd) {
    struct sockaddr_in sa_in1;
    socklen_t sa_in1_len = sizeof(sa_in1);
    struct sockaddr_in sa_in2;
    socklen_t sa_in2_len = sizeof(sa_in2);
    char *ip1, *ip2;
    unsigned short port1, port2;

    if (getsockname(fd, (struct sockaddr *)&sa_in1, &sa_in1_len) < 0) {
        perror("getsockname");
        return;
    }
    ip1 = inet_ntoa(sa_in1.sin_addr);
    port1 = ntohs(sa_in1.sin_port);

    if (getpeername(fd, (struct sockaddr *)&sa_in2, &sa_in2_len) < 0) {
        perror("getpeername");
        return;
    }
    ip2 = inet_ntoa(sa_in2.sin_addr);
    port2 = ntohs(sa_in2.sin_port);

    ASSISTANT_DEBUG_MTUNNEL("Server Connected - Client Local IP: %s, Client Local Port: %u, Peer IP: %s, Peer Port: %u, fd: %d",
           ip1, port1, ip2, port2, fd);
    return;
}
#endif

static void za_mtunnel_process_event(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char *mtunnel_id = (char *)cookie;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_assistant_mtunnel *mtunnel = cookie;
    short events = int_cookie;
    struct bufferevent *bev;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int bev_errno_save = EVUTIL_SOCKET_ERROR(); /* Save bev errno */

    mtunnel_id_len = strlen(mtunnel_id);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%s: za_mtunnel_process_event, but no tunnel!?", mtunnel_id);
        ASST_FREE(mtunnel_id);
        return;
    }

    za_mtunnel_lock(mtunnel);

    bev = mtunnel->bev_tmp;

    if (!bev) {
        /* Should not happen, but in case the impossible happens, we don't crash and burn */
        ASSISTANT_LOG(AL_NOTICE, "za_mtunnel_process_event, but no bev?");
        goto exit;
    }

    if (mtunnel->flag.waf_secure && !(events & BEV_EVENT_CONNECTED)) {
        unsigned long oslerr = 0;
        unsigned long itr = 0;
        while ((oslerr = bufferevent_get_openssl_error(bev)) && itr++ < 12) {
            int oslerr_reason = ERR_GET_REASON(oslerr);
            if (oslerr_reason == SSL_R_UNKNOWN_PROTOCOL) {
                /* Purge PDP Profile if valid */
                /* eg. HTTPS or TLS PDP profile, but HTTP traffic comes in */
                zpn_waf_downgrade_pdp_profile_with_tolerance(mtunnel->domain, mtunnel->server_port_he, mtunnel->ip_protocol);
            }
            int need2log = (fp_n2log_ssl_err)(mtunnel->gid_inspection_appl, oslerr);
            if (need2log) {
                char buffer[128];
                ERR_error_string_n(oslerr, buffer, sizeof(buffer));
                ASSISTANT_LOG(AL_ERROR,
                              "Mtunnel-ID: %s (inspt-app %ld), Connect to server SSL error (%ld): %s (hit count %d)",
                              mtunnel->mtunnel_id, (long)mtunnel->gid_inspection_appl, (long)oslerr, buffer, need2log);
            }
            mtunnel->flag.connect_ssl_error = 1;
            mtunnel->insp_status = zpn_trans_waf_ssl_to_server_fail;
            if (1 == itr) {
                mtunnel->ssl_err = oslerr;
            }
        }
    }

    /* Update connect flags */
    za_mtunnel_event_to_connect_flags(mtunnel, events, bev_errno_save);

    if (events & BEV_EVENT_CONNECTED) {
        evutil_socket_t s;
        struct sockaddr_storage addr;
        socklen_t addrlen = sizeof(addr);

        ASSISTANT_DEBUG_MTUNNEL("%s: Server event received: CONNECTED", mtunnel->mtunnel_id);
        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connected), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connected), 1);
        }

        mtunnel->mconn_bufferevent_server.max_pause_time_interval = global_assistant.max_pause_time_interval;

        if (!mtunnel->server_connection_setup_end_us) {
            mtunnel->server_connection_setup_end_us = epoch_us();
        }
        mtunnel->state = za_server_conn_open;

        /* Extract assistant address of the connection */
        s = bufferevent_getfd(bev);
        if (getsockname(s, (struct sockaddr *)&addr, &addrlen) != -1) {
            uint16_t port_ne;

            argo_sockaddr_to_inet((struct sockaddr *)&addr, &(mtunnel->assistant_inet), &port_ne);
            mtunnel->assistant_port_he = ntohs(port_ne);

            ASSISTANT_DEBUG_MTUNNEL("Assistant Side: ip = %s, port = %d",
                                    argo_inet_generate(ipaddr, &(mtunnel->assistant_inet)),
                                    mtunnel->assistant_port_he);
        }

#ifdef IDPS_DEBUG
		debug_connection(s);
#endif
        if (za_mtunnel_connect_mconns(mtunnel, bev, 1) != ZPN_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_NOTICE, "za_mtunnel_process_event, failed to set up local mconns");
            //Todo tunnel state is not set to error... stuck conn - will fail on timeout?...
            goto exit;
        }
    } else {
        ASSISTANT_DEBUG_MTUNNEL("%s: Server event received: CLOSED", mtunnel->mtunnel_id);

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connect_error), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connect_error), 1);
        }

        /* Server connect failed, set state to reaping and send connect failure error code back to broker */
        if (za_mtunnel_is_server_connect_failed(mtunnel) && (mtunnel->flag.two_hop == 0)) {
            mtunnel->state = za_reaping;
        }

        /* Leave state alone, but clear out out server conn. */
        __sync_add_and_fetch_8(&(stats.process_event_bev_free_on_server_sock_close), 1);
        zlibevent_bufferevent_free(bev);
    }

    /* Don't need this anymore */
    mtunnel->bev_tmp = NULL;

    za_mtunnel_locked_state_machine(mtunnel);

exit:
    ASST_FREE(mtunnel_id);
    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);
}

static void za_mtunnel_event(struct bufferevent *bev, short events, void *ptr)
{
    struct zpn_assistant_mtunnel *mtunnel = ptr;
    int thread_id;
    char* mtunnel_id_copy;

    mtunnel_id_copy = (char *)ASST_MALLOC(strlen(mtunnel->mtunnel_id) + 1);
    snprintf(mtunnel_id_copy, strlen(mtunnel->mtunnel_id) + 1 , "%s", mtunnel->mtunnel_id);

    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &thread_id);

    if (fohh_thread_call(thread_id,
                         za_mtunnel_process_event,
                         mtunnel_id_copy,
                         events) != FOHH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for za_mtunnel_event!");
        ASST_FREE(mtunnel_id_copy);
    }
}

static void za_mtunnel_convert_ip_sockaddr_storage(char *str_ip, struct sockaddr_storage *addrp)
{
    struct sockaddr_in *paddr4 = (struct sockaddr_in *) addrp;
    struct sockaddr_in6 *paddr6 = (struct sockaddr_in6 *) addrp;

    if (inet_pton(AF_INET, str_ip, &paddr4->sin_addr) == 1) {
        addrp->ss_family = AF_INET;
    } else {
        if (inet_pton(AF_INET6, str_ip, &paddr6->sin6_addr) == 1) {
            addrp->ss_family = AF_INET6;
        } else {
            addrp->ss_family = AF_UNSPEC;
        }
    }
}

static int za_mtunnel_connect_udp_mconns(struct zpn_assistant_mtunnel*   mtunnel,
                                         int                             fohh_thread_id,
                                         int*                            is_process_fd_exhausted,
                                         int*                            is_system_fd_exhausted,
                                         int*                            is_ephemeral_port_exhausted)
{
    struct sockaddr_storage addr;
    socklen_t addr_len;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int res;

    ASSISTANT_DEBUG_MTUNNEL("%s: IDPS Connecting to UDP server, Server Side: ip = %s, port = %d",
            mtunnel->mtunnel_id,
            argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
            mtunnel->server_port_he);

    ASSISTANT_DEBUG_MTUNNEL("mtunnel->gid_inspection_profile = %ld, mtunnel->e_inspection_type =%d with inspection mode = %d [%s]",
            (long)(mtunnel->gid_inspection_profile),  mtunnel->e_inspection_type,
            mtunnel->e_inspection_mode, traffic_inspection_modes[mtunnel->e_inspection_mode]);

    /* Set thread id to the mconn_bufferevent */
    zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_udp_server.mconn), fohh_thread_id);
    memset(&addr, 0, sizeof(addr));
    argo_inet_to_sockaddr(&(mtunnel->server_inet), (struct sockaddr *)&addr, &addr_len, htons(mtunnel->server_port_he));

    ASSISTANT_DEBUG_MTUNNEL("%s: IDPS Connecting to UDP server, Server Side: ip = %s, port = %d",
            mtunnel->mtunnel_id,
            argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
            mtunnel->server_port_he);

    ASSISTANT_DEBUG_MTUNNEL("mtunnel->gid_inspection_profile = %ld, mtunnel->e_inspection_type =%d with inspection mode = %d [%s]",
            (long)(mtunnel->gid_inspection_profile),  mtunnel->e_inspection_type,
            mtunnel->e_inspection_mode, traffic_inspection_modes[mtunnel->e_inspection_mode]);

    if (mtunnel->flag.waf_secure) {
        ZPN_LOG(AL_ERROR, "%s: UDP Mtunnel for TLS session inspection invalid", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR; /* Invalid scenario */
    }

    struct zpn_waf_profile* profile = zpn_mtunnel_get_inspection_profile(mtunnel);
    enum zpn_traffic_inspection_mode mode = get_mtunnel_locked_inspection_mode(mtunnel);
    if (mode && !mtunnel->flag.ptag_mode && NULL == profile) { //mode disabled or protocol tagging does not require WAF profile
        ZPN_LOG(AL_ERROR, "%s: Could not setup waf profile", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR;
    }

#ifdef IDPS_DEBUG
    char server_inet_str[ARGO_INET_ADDRSTRLEN] = {0};
    char assist_inet_str[ARGO_INET_ADDRSTRLEN] = {0};

    ASSISTANT_DEBUG_MTUNNEL("UDP Pipeline : Assistant [%s : %d] server_port_he [%s : %d] client_port_he [%d] inspect_mode [%d]",
            argo_inet_generate(assist_inet_str, &mtunnel->assistant_inet), mtunnel->assistant_port_he,
            argo_inet_generate(server_inet_str, &mtunnel->server_inet), mtunnel->server_port_he,
            mtunnel->client_port_he, mode);
#endif

    int mconn_bufferevent_thread_id;
    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_udp_server.mconn), &mconn_bufferevent_thread_id);
    ASSISTANT_DEBUG_MTUNNEL("CREATE %s IDPS TUNNEL TYPE %d", (mtunnel->ip_protocol == IPPROTO_TCP) ? "TCP":"UDP", mode);

    if (zpn_traffic_inspection_ptag == mode || zpn_traffic_inspection_auto == mode ||
        zpn_traffic_inspection_ldap == mode || zpn_traffic_inspection_krb == mode ||
        zpn_traffic_inspection_smb == mode) {
        mtunnel->connector = (struct zpn_connector *)zpn_connector_inspect_create(mtunnel, mconn_bufferevent_thread_id,
                (void *)&(mtunnel->mconn_udp_server.mconn),
                profile, mode, &populate_idps_log_fields, mtunnel->flag.debug_flag);

        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }

        ASSISTANT_DEBUG_MTUNNEL("Created connector insp idps over udp - mode [%d]", mode);
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);


        ASSISTANT_DEBUG_MTUNNEL("IDPS: Created UDP connector");
        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                1,
                mtunnel,
                NULL,
                0,
                &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("IDPS: Added global owner for client side udp connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_inspect *)mtunnel->connector)->mconn_spair.mconn),
                1,
                mtunnel,
                NULL,
                0,
                &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("IDPS: Added global owner for server side udp connector");

        res  = zpn_connector_inspect_connect(mconn_bufferevent_thread_id,
                (struct zpn_connector_inspect *)mtunnel->connector,
                &(mtunnel->mconn_udp_server.mconn),
                1);

        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("IDPS: Connected UDP server side");

    } else if (mode == zpn_traffic_inspection_ptag_double_encrypted || mode == zpn_traffic_inspection_krb_double_encrypted ||
               mode == zpn_traffic_inspection_ldap_double_encrypted || mode == zpn_traffic_inspection_smb_double_encrypted ||
               mode == zpn_traffic_inspection_auto_double_encrypted) {
        mtunnel->connector = (struct zpn_connector *)zpn_connector_tun_inspect_create(mtunnel,
                ssl_ctx_connector, NULL, 1, 0,
                mconn_bufferevent_thread_id,
                (void *)&(mtunnel->mconn_udp_server.mconn),
                profile,
                mode,
                &populate_idps_log_fields,
                mtunnel->flag.debug_flag);

        if (!mtunnel->connector) {
            ZPN_LOG(AL_ERROR, "Cannot allocator connector");
            return ZPN_RESULT_ERR;
        }

        ASSISTANT_DEBUG_MTUNNEL("Created connector insp idps over udp - mode [%d]", mode);
        __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);


        ASSISTANT_DEBUG_MTUNNEL("IDPS: Created UDP connector");
        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_inspect *)mtunnel->connector)->mconn_cpair.mconn),
                1,
                mtunnel,
                NULL,
                0,
                &za_server_global_call_set);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("IDPS: Added global owner for client side udp connector");

        res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun_inspect *)mtunnel->connector)->mconn_spair.mconn),
                1,
                mtunnel,
                NULL,
                0,
                &za_broker_global_call_set);

        if (res) {
            ZPN_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
            return ZPN_RESULT_ERR;
        }
        ASSISTANT_DEBUG_MTUNNEL("IDPS: Added global owner for server side udp connector");

        res  = zpn_connector_tun_inspect_connect(mconn_bufferevent_thread_id,
                                                 (struct zpn_connector_tun_inspect *)mtunnel->connector,
                                                 &(mtunnel->mconn_udp_server.mconn),
                                                 1,
                                                 mtunnel->double_encrypt,
                                                 NULL, mode);

        if (res) {
            ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail), 1);
            return ZPN_RESULT_ERR;
        } else {
            __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side), 1);
        }

        ASSISTANT_DEBUG_MTUNNEL("IDPS: Connected UDP server side");

    } else {
        ZPN_LOG(AL_ERROR, "%s: Unsupported inspection mode [%d]", mtunnel->mtunnel_id, mode);
        return ZPN_RESULT_ERR;
    }

    /* we should reach here only if the WAF resources been fetched so no need to check - softassert can help */

    mtunnel->state = za_server_conn_open;

    /* Attach self to client... */
    res = zpn_mconn_add_local_owner(&(mtunnel->mconn_udp_server.mconn),
            0,
            &addr,
            NULL,
            0,
            &mconn_udp_bufferevent_calls);
    if (res) {
        if (EMFILE == mtunnel->mconn_udp_server.udp_socket_errno) {
            *is_process_fd_exhausted = 1;
        } else if (ENFILE == mtunnel->mconn_udp_server.udp_socket_errno) {
            *is_system_fd_exhausted = 1;
        } else if (EADDRNOTAVAIL == mtunnel->mconn_udp_server.udp_socket_errno) {
            *is_ephemeral_port_exhausted = 1;
        }
        ASSISTANT_LOG(AL_ERROR, "%s, Error adding local owner: %s(%s)", mtunnel->mtunnel_id, zpn_result_string(res),
                strerror(mtunnel->mconn_udp_server.udp_socket_errno));
        return ZPN_RESULT_ERR;
    }

    // Store assistant ip:port.
    uint16_t port_ne;
    argo_sockaddr_to_inet((struct sockaddr *)&(mtunnel->mconn_udp_server.udp_conn->conn_4tuple.src_addr), &(mtunnel->assistant_inet), &port_ne);
    mtunnel->assistant_port_he = ntohs(port_ne);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mtunnel_is_udp_supported_traffic_inspection(int mode) {
    if (mode == zpn_traffic_inspection_ldap ||
        mode == zpn_traffic_inspection_ldap_double_encrypted ||
        mode == zpn_traffic_inspection_smb ||
        mode == zpn_traffic_inspection_smb_double_encrypted ||
        mode == zpn_traffic_inspection_krb ||
        mode == zpn_traffic_inspection_krb_double_encrypted ||
        mode == zpn_traffic_inspection_ptag ||
        mode == zpn_traffic_inspection_ptag_double_encrypted ||
        mode == zpn_traffic_inspection_auto ||
        mode == zpn_traffic_inspection_auto_double_encrypted)
        return 1;
    else
        return 0;
}


static int za_mtunnel_verify_and_connect_server(struct zpn_assistant_mtunnel*   mtunnel,
                                                int*                            is_dns_failed,
                                                int*                            is_process_fd_exhausted,
                                                int*                            is_system_fd_exhausted,
                                                int*                            is_ephemeral_port_exhausted,
                                                int*                            is_mtunnel_terminating_at_assistant,
                                                int*                            is_zdx_http_ipv6_but_disabled,
                                                int*                            is_zdx_https_ipv6_but_disabled)
{
    struct sockaddr_storage addr;
    socklen_t addr_len;
    struct event_base *base;
    struct bufferevent *bev;
    int fohh_thread_id;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int res;
    int is_app_tcp_keepalive_enabled;

    pthread_mutex_lock(&(global_assistant.config_lock));
    res = assistant_app_check_before_connect(mtunnel->g_app, mtunnel->g_app_grp, mtunnel->g_ast_grp, mtunnel->g_srv_grp,
                                             mtunnel->double_encrypt, mtunnel->g_aps, mtunnel->client_port_he,
                                             mtunnel->ip_protocol, mtunnel->brk_req_server_inet,
                                             mtunnel->mtunnel_type, mtunnel->mtunnel_id,
                                             mtunnel->domain, &(mtunnel->server_inet),
                                             &(mtunnel->server_port_he), mtunnel->user_id_str,
                                             mtunnel->flag.is_muted_health_app, is_dns_failed, &(mtunnel->path_decision),
                                             &is_app_tcp_keepalive_enabled);
    if (ZPN_RESULT_NO_ERROR != res) {
        pthread_mutex_unlock(&(global_assistant.config_lock));
        return res;
    }
    ASSISTANT_ASSERT_SOFT(mtunnel->server_inet.length, "server inet is invalid");
    mtunnel->server_connection_setup_start_us = epoch_us();

    /* Get a thread/event_base to use... */
    fohh_thread_id = fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_ASERVER);
    base = fohh_get_thread_event_base(fohh_thread_id);

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Assistant setting up mtunnel for zdx probe", mtunnel->mtunnel_id);
    }
    if (mtunnel->server_inet.length == IPV6_ADDR_LEN) {
        if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe &&
            !zpn_zdx_http_webprobe_cache_config_is_ipv6_intercept_enabled()) {
            *is_zdx_http_ipv6_but_disabled = 1;
            ASSISTANT_LOG(AL_INFO, "%s: Server is ipv6 but webprobe ipv6 config is disabled, hence rejecting",
                          mtunnel->mtunnel_id);
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        } else if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https &&
                   !zpn_zdx_https_webprobe_cache_config_is_ipv6_intercept_enabled()) {
            *is_zdx_https_ipv6_but_disabled = 1;
            ASSISTANT_LOG(AL_INFO, "%s: Server is ipv6 but webprobe https ipv6 config is disabled, hence rejecting",
                          mtunnel->mtunnel_id);
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        }

    }

    if (mtunnel->ip_protocol == IPPROTO_UDP) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Connecting to UDP server, Server Side: ip = %s, port = %d",
                                mtunnel->mtunnel_id,
                                argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                                mtunnel->server_port_he);

		enum zpn_traffic_inspection_mode mode = get_mtunnel_locked_inspection_mode(mtunnel);
        if (zpn_mtunnel_is_udp_supported_traffic_inspection(mode)) {
            res =  za_mtunnel_connect_udp_mconns(mtunnel, fohh_thread_id, is_process_fd_exhausted,
                    is_system_fd_exhausted, is_ephemeral_port_exhausted);
            zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_udp_server.mconn), fohh_thread_id);
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return res;
        }

        /* Set thread id to the mconn_bufferevent */
        zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_udp_server.mconn), fohh_thread_id);

        memset(&addr, 0, sizeof(addr));
        argo_inet_to_sockaddr(&(mtunnel->server_inet), (struct sockaddr *)&addr, &addr_len, htons(mtunnel->server_port_he));

        if (mtunnel->double_encrypt) {
            struct zpn_connector_tun *tun_conn = zpn_connector_tun_create(mtunnel, ssl_ctx_connector, NULL, 1, 0);
            mtunnel->connector = (struct zpn_connector *)tun_conn;
            if (!mtunnel->connector) {
                ASSISTANT_LOG(AL_ERROR, "Cannot allocator connector");
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }

            ASSISTANT_DEBUG_MTUNNEL("Created connector udp tun");
            __sync_fetch_and_add_8(&(mtunnel->flag.connector_created), 1);

            res = zpn_mconn_add_global_owner(&(tun_conn->mconn_c.mconn),
                                             1,
                                             mtunnel,
                                             NULL,
                                             0,
                                             &za_server_global_call_set);

            if (res) {
                ASSISTANT_LOG(AL_CRITICAL, "Adding global owner for client side connector, failed - %s", zpn_result_string(res));
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }
            ASSISTANT_DEBUG_MTUNNEL("Added global owner for client side connector");

            res = zpn_mconn_add_global_owner(&(tun_conn->mconn_s.mconn),
                                             1,
                                             mtunnel,
                                             NULL,
                                             0,
                                             &za_broker_global_call_set);
            if (res) {
                ASSISTANT_LOG(AL_CRITICAL, "Adding global owner for server side connector, failed - %s", zpn_result_string(res));
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }
            ASSISTANT_DEBUG_MTUNNEL("Added global owner for server side connector");

            res  = zpn_connector_tun_connect(fohh_thread_id,
                                              tun_conn,
                                              &(mtunnel->mconn_udp_server.mconn),
                                              1,
                                              mtunnel->double_encrypt,
                                              NULL);
            if (res) {
                ASSISTANT_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
                __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side_fail1), 1);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            } else {
                __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_server_side1), 1);
            }

            ASSISTANT_DEBUG_MTUNNEL("Connected server side");
        }

        /* Attach self to client... */
        mtunnel->mconn_udp_server.mconn.udp_timeout_s = assistant_cfg_override_get_udp_server_inactivity_timeout_s();
        mtunnel->mconn_udp_server.mconn.udp_fast_timeout_s = assistant_cfg_override_get_udp_server_inactivity_fast_timeout_s();
        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_udp_server.mconn),
                                        0,
                                        &addr,
                                        NULL,
                                        0,
                                        &mconn_udp_bufferevent_calls);
        if (res) {
            if (EMFILE == mtunnel->mconn_udp_server.udp_socket_errno) {
                *is_process_fd_exhausted = 1;
            } else if (ENFILE == mtunnel->mconn_udp_server.udp_socket_errno) {
                *is_system_fd_exhausted = 1;
            } else if (EADDRNOTAVAIL == mtunnel->mconn_udp_server.udp_socket_errno) {
                *is_ephemeral_port_exhausted = 1;
            }
            ASSISTANT_LOG(AL_ERROR, "%s, Error adding local owner: %s(%s)", mtunnel->mtunnel_id, zpn_result_string(res),
                    strerror(mtunnel->mconn_udp_server.udp_socket_errno));
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        }

        // Store assistant ip:port.
        uint16_t port_ne;
        struct sockaddr_storage *src_addr = zudp_conn_get_srcaddr_from_connection(((mtunnel->mconn_udp_server).udp_conn));
        if (src_addr) {
            argo_sockaddr_to_inet((struct sockaddr *)src_addr, &(mtunnel->assistant_inet), &port_ne);
            mtunnel->assistant_port_he = ntohs(port_ne);
            src_addr = zudp_conn_get_srcaddr_ip_from_connection(((mtunnel->mconn_udp_server).udp_conn));
            if(src_addr) {
                uint16_t temp_port;
                argo_sockaddr_to_inet((struct sockaddr *)src_addr, &(mtunnel->assistant_inet), &temp_port);
            }
            char ip_str[ARGO_INET_ADDRSTRLEN];
            memset(ip_str, 0, sizeof(ip_str));
            ASSISTANT_DEBUG_MTUNNEL("Adding assistant address and port for udp %s address %s port %d", mtunnel->mtunnel_id,
                          argo_inet_generate(ip_str, &(mtunnel->assistant_inet)), mtunnel->assistant_port_he);
        }
        pthread_mutex_unlock(&(global_assistant.config_lock));
        return ZPN_RESULT_NO_ERROR;
    } else if (mtunnel->ip_protocol == IPPROTO_ICMP) {
        argo_inet_generate(ipaddr, &(mtunnel->server_inet));
        zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_icmp_server.mconn), fohh_thread_id);

        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_icmp_server.mconn),
                                        0,
                                        ipaddr,
                                        NULL,
                                        0,
                                        &mconn_icmp_calls);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "%s, Error adding local owner: %s", mtunnel->mtunnel_id, zpn_result_string(res));
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        }

        struct sockaddr_storage dst_sockaddr_ss;
        struct sockaddr_storage src_sockaddr_ss;
        struct sockaddr_storage gw_sockaddr_ss;

        struct sockaddr_in *paddr = (struct sockaddr_in *) &src_sockaddr_ss;

        char ms[INET6_ADDRSTRLEN];
        bzero(ms, sizeof(ms));

        za_mtunnel_convert_ip_sockaddr_storage(ipaddr, &dst_sockaddr_ss);

        if (zhealth_os_route_get(&dst_sockaddr_ss, &src_sockaddr_ss, &gw_sockaddr_ss)) {
            paddr = (struct sockaddr_in *) &src_sockaddr_ss;
            inet_ntop(AF_INET, &paddr->sin_addr.s_addr, ms, 24);
        }

        argo_string_to_inet(ms, &mtunnel->assistant_inet);

        ASSISTANT_DEBUG_MTUNNEL("%s: Connecting to ICMP DIP = %s using SIP = %s",
                                 mtunnel->mtunnel_id, ipaddr, ms);

        pthread_mutex_unlock(&(global_assistant.config_lock));
        return ZPN_RESULT_NO_ERROR;

    } else if (mtunnel->ip_protocol == IPPROTO_ICMPV6) {

        argo_inet_generate(ipaddr, &(mtunnel->server_inet));
        zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_icmp_server.mconn), fohh_thread_id);

        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_icmp_server.mconn),
                                        0,
                                        ipaddr,
                                        NULL,
                                        0,
                                        &mconn_icmpv6_calls);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "%s, Error adding local owner: %s", mtunnel->mtunnel_id,
                          zpn_result_string(res));
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        }

        struct sockaddr_storage dst_sockaddr_ss;
        struct sockaddr_storage src_sockaddr_ss;
        struct sockaddr_storage gw_sockaddr_ss;

        struct sockaddr_in6 *paddr = (struct sockaddr_in6 *) &src_sockaddr_ss;

        char ms[INET6_ADDRSTRLEN];
        memset(ms, 0, sizeof(ms));

        za_mtunnel_convert_ip_sockaddr_storage(ipaddr, &dst_sockaddr_ss);

        if (zhealth_os_route_get(&dst_sockaddr_ss, &src_sockaddr_ss, &gw_sockaddr_ss)) {
            paddr = (struct sockaddr_in6 *) &src_sockaddr_ss;
            inet_ntop(AF_INET6, &paddr->sin6_addr, ms, INET6_ADDRSTRLEN);
        }

        argo_string_to_inet(ms, &mtunnel->assistant_inet);

        ASSISTANT_DEBUG_MTUNNEL("%s: Connecting to ICMPv6 DIP = %s using SIP = %s",
                                 mtunnel->mtunnel_id, ipaddr, ms);

        pthread_mutex_unlock(&(global_assistant.config_lock));
        return ZPN_RESULT_NO_ERROR;

    } else { /* TCP server case */
        argo_inet_to_sockaddr(&(mtunnel->server_inet), (struct sockaddr *)&addr, &addr_len, htons(mtunnel->server_port_he));

        ASSISTANT_DEBUG_MTUNNEL("Server Side: ip = %s, port = %d",
                                argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                                mtunnel->server_port_he);

        if ((mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https) || (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) ||
            (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe && zpn_zdx_http_webprobe_cache_config_is_intercept_enabled())) {

            ASSISTANT_DEBUG_MTUNNEL("MTR/webprobe_cache, probe type %d end terminate mtunnel at connector for Server Side: ip = %s, port = %d",
                                    mtunnel->zpn_probe_type, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                                    mtunnel->server_port_he);

            mtunnel->bev_tmp = 0;
            bev = 0;

            /* indicate mtunnel connect request as usual */
            mtunnel->flag.connect_requested = 1;

            if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
                /* indicate tunnel request end termination for mtr mtunnel to server */
                mtunnel->flag.zdx_mtr_connect_end_terminated = 1;
            } else {
                /* indicate tunnel request end termination for webprobe_cache mtunnel */
                mtunnel->flag.zdx_webprobe_cache_connect_end_terminated = 1;
                /* Set thread id to the mconn_bufferevent */
                zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), fohh_thread_id);
            }
            *is_mtunnel_terminating_at_assistant = 1;
            pthread_mutex_unlock(&(global_assistant.config_lock));
            mtunnel->mconn_bufferevent_server.mconn.terminate_at_connector = 1;

            /* return success for expected next processing */
            return ZPN_RESULT_NO_ERROR;
        }

        if (mtunnel->mtunnel_type == zmt_use_tls || mtunnel->flag.waf_secure) {
            int tmp_sock;
            int tmp_val;
            struct argo_inet a_inet;
            SSL_CTX *mconn_ssl_ctx = (zmt_use_tls == mtunnel->mtunnel_type)
                                             ? ssl_ctx_mtls
                                             : zpn_mtunnel_locked_get_waf_client_ssl_ctx(mtunnel);
            if (mconn_ssl_ctx == NULL) {
                ZPN_LOG(AL_ERROR, "%s:Trying to start SSL communication but do not have a SSL CTX", mtunnel->mtunnel_id);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }

            if (mtunnel->lss_encrypted_ssl) {
                __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
                SSL_free(mtunnel->lss_encrypted_ssl);
                zpn_mem_free_stats_update(assistant_mtunnel_ssl);
                mtunnel->lss_encrypted_ssl = NULL;
            }

            mtunnel->lss_encrypted_ssl = SSL_new(mconn_ssl_ctx);
            if (!mtunnel->lss_encrypted_ssl) {
                ZPN_LOG(AL_ERROR, "Cannot create ssl object, error: %s", strerror(errno));
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }
            zpn_mem_alloc_stats_update(assistant_mtunnel_ssl);
            __sync_add_and_fetch_8(&(stats.mtls_ssl_created), 1);

            if (argo_string_to_inet(mtunnel->domain, &a_inet)) {

            #ifdef X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS
                {
                    X509_VERIFY_PARAM *param = NULL;
                    param = SSL_get0_param(mtunnel->lss_encrypted_ssl);

                    X509_VERIFY_PARAM_set_hostflags(param, X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS);
                    X509_VERIFY_PARAM_set1_host(param, mtunnel->domain, 0);
                }
            #endif
            }
            if (mtunnel->flag.waf_secure) {
                /* pls note if multiple domain level wild card support needed:
                ** this can be controlled here on seting param flags */
                X509_VERIFY_PARAM *param = NULL;
                param = SSL_get0_param(mtunnel->lss_encrypted_ssl);
                if (param) {
                    X509_VERIFY_PARAM_add1_host(param, mtunnel->domain, 0);
                } else {
                    ASSISTANT_DEBUG_MTUNNEL("Mtunnel %s - SSL WAF param is NULL - did not set X509 host %s ",
                    mtunnel->mtunnel_id, mtunnel->domain);
                }

                ASSISTANT_DEBUG_MTUNNEL("Mtunnel %s - Setting SNI %s on app protectionconnection",
                        mtunnel->mtunnel_id, mtunnel->domain);
                SSL_set_tlsext_host_name(mtunnel->lss_encrypted_ssl, mtunnel->domain);
            }

            SSL_set_ex_data(mtunnel->lss_encrypted_ssl, fohh_ssl_ex_index, &(mtunnel->lss_encrypted_ssl_status));

            if (addr.ss_family == AF_INET) {
                tmp_sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            } else {
                tmp_sock = socket(AF_INET6, SOCK_STREAM, IPPROTO_TCP);
            }

            if (tmp_sock < 0) {
                ZPN_LOG(AL_ERROR, "%s: Could not allocate bufferevent socket, error: %s", mtunnel->mtunnel_id, strerror(errno));
                if (mtunnel->lss_encrypted_ssl) {
                    __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
                    SSL_free(mtunnel->lss_encrypted_ssl);
                    zpn_mem_free_stats_update(assistant_mtunnel_ssl);
                }
                mtunnel->lss_encrypted_ssl = NULL;
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
            tmp_val = 1;
            setsockopt(tmp_sock, SOL_SOCKET, SO_NOSIGPIPE, (void *)&tmp_val, sizeof(tmp_val));
#endif
            tmp_val = 1;
            setsockopt(tmp_sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));

            if (is_app_tcp_keepalive_enabled || assistant_cfg_override_feature_get_is_to_app_server_keepalive_enabled()) {
                mtunnel->flag.is_tcp_keepalive_enabled = 1;
                int keepalive = 1;
                setsockopt(tmp_sock, SOL_SOCKET, SO_KEEPALIVE, (void *)&keepalive, sizeof(keepalive));
                ASSISTANT_DEBUG_MTUNNEL("%s: MTLS Turning on TCP SO_KEEPALIVE option for app %s", mtunnel->mtunnel_id, mtunnel->domain);
            }

            evutil_make_socket_nonblocking(tmp_sock);

            bev = bufferevent_openssl_socket_new(base, tmp_sock, mtunnel->lss_encrypted_ssl, BUFFEREVENT_SSL_CONNECTING, BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);

            if (!bev) {
                ZPN_LOG(AL_ERROR, "%s: Could not allocate bufferevent socket", mtunnel->mtunnel_id);
                __sync_add_and_fetch_8(&(stats.server_side_bev_create_fail), 1);
                if (mtunnel->lss_encrypted_ssl) {
                    __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
                    SSL_free(mtunnel->lss_encrypted_ssl);
                    zpn_mem_free_stats_update(assistant_mtunnel_ssl);
                }
                mtunnel->lss_encrypted_ssl = NULL;
                close(tmp_sock);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            } else {
                __sync_add_and_fetch_8(&(stats.server_side_bev_create), 1);
            }

            bufferevent_setcb(bev, NULL, NULL, za_mtunnel_event, mtunnel);
            bufferevent_enable(bev, EV_READ|EV_WRITE);

            mtunnel->bev_tmp = bev;
            mtunnel->flag.connect_requested = 1;

            if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {
                if (EMFILE == errno) {
                    *is_process_fd_exhausted = 1;
                } else if (ENFILE == errno) {
                    *is_system_fd_exhausted = 1;
                } else if (EADDRNOTAVAIL == errno) {
                    *is_ephemeral_port_exhausted = 1;
                }
                ZPN_LOG(AL_ERROR, "%s: Could not initiate connect to addr %s:%d : errno = %d, = %s",
                        mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                        mtunnel->server_port_he, errno, strerror(errno));
                mtunnel->bev_tmp = NULL;
                __sync_add_and_fetch_8(&(stats.verify_and_connect_bev_free_on_connect_failure), 1);
                zlibevent_bufferevent_free(bev);
                if (mtunnel->lss_encrypted_ssl) {
                    __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
                    SSL_free(mtunnel->lss_encrypted_ssl);
                    zpn_mem_free_stats_update(assistant_mtunnel_ssl);
                }
                mtunnel->lss_encrypted_ssl = NULL;
                close(tmp_sock);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }

            /* Set thread id to the mconn_bufferevent */
            zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), fohh_thread_id);
            mtunnel->lss_encrypted_sock = tmp_sock;

            pthread_mutex_unlock(&(global_assistant.config_lock));

        } else {
            int sock;
            int tmp_val;

            bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
            if (!bev) {
                ZPN_LOG(AL_ERROR, "%s: Could not allocate bufferevent socket", mtunnel->mtunnel_id);
                __sync_add_and_fetch_8(&(stats.server_side_bev_create_fail), 1);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            } else {
                __sync_add_and_fetch_8(&(stats.server_side_bev_create), 1);
            }

            bufferevent_setcb(bev, NULL, NULL, za_mtunnel_event, mtunnel);
            mtunnel->bev_tmp = bev;

            mtunnel->flag.connect_requested = 1;
            if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {
                if (EMFILE == errno) {
                    *is_process_fd_exhausted = 1;
                } else if (ENFILE == errno) {
                    *is_system_fd_exhausted = 1;
                } else if (EADDRNOTAVAIL == errno) {
                    *is_ephemeral_port_exhausted = 1;
                }
                ZPN_LOG(AL_ERROR, "%s: Could not initiate connect to addr %s:%d : errno = %d, = %s",
                        mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                        mtunnel->server_port_he, errno, strerror(errno));
                mtunnel->bev_tmp = NULL;
                __sync_add_and_fetch_8(&(stats.verify_and_connect_bev_free_on_connect_failure), 1);
                zlibevent_bufferevent_free(bev);
                pthread_mutex_unlock(&(global_assistant.config_lock));
                return ZPN_RESULT_ERR;
            }

            /* Set TCP_NODELAY */
            tmp_val = 1;
            sock = bufferevent_getfd(bev);
            setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));

#ifdef __linux__
            if (zpn_assistant_global_assistant_get_quickack_app() ||
                assistant_features_is_quickack_app_enabled(
                        global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
                tmp_val = 1;
                ZPN_DEBUG_MCONN("Set quickack app for server in accept\n");
                setsockopt(sock, IPPROTO_TCP, TCP_QUICKACK, (void *)&tmp_val, sizeof(tmp_val));
                zpn_mconn_bufferevent_set_quickack(&(mtunnel->mconn_bufferevent_server));
            }
#endif
            /*FIXME: should remove enabling so_keepalive at config override level by Nov/2021 Jira: ET-29253*/
            if (is_app_tcp_keepalive_enabled || assistant_cfg_override_feature_get_is_to_app_server_keepalive_enabled()) {
                mtunnel->flag.is_tcp_keepalive_enabled = 1;
                int keepalive = 1;
                setsockopt(sock, SOL_SOCKET, SO_KEEPALIVE, (void *)&keepalive, sizeof(keepalive));
                ASSISTANT_DEBUG_MTUNNEL("%s: Turning on TCP SO_KEEPALIVE option for app %s", mtunnel->mtunnel_id, mtunnel->domain);
            }

            /* Set thread id to the mconn_bufferevent */
            zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), fohh_thread_id);

            pthread_mutex_unlock(&(global_assistant.config_lock));
        }
        return ZPN_RESULT_ASYNCHRONOUS;
    }
}

static void zpn_zdx_fill_mtunnel_pipeline_cookie(struct zpn_tlv *tlv, struct zpn_assistant_mtunnel *mtunnel, int is_pbroker) {

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        struct zpn_mconn* server_tcp_mconn = &(mtunnel->mconn_bufferevent_server.mconn);

        //always hold server termination on a connector to allow for report forwarding
        server_tcp_mconn->hold_forward_mtunnel_end = 1;

        struct zpn_zdx_cookie *zdx_tcp_cookie = server_tcp_mconn->pipeline_cookie;
        if (zdx_tcp_cookie) {
            zpn_tlv_address_and_port(tlv,
                                     &(zdx_tcp_cookie->probe_info.upstream_ip), NULL,
                                     &(zdx_tcp_cookie->probe_info.self_src_ip) , NULL);

            zdx_tcp_cookie->probe_info.upstream_port = mtunnel->client_port_he;
            zdx_tcp_cookie->probe_info.self_src_port = ZHEALTH_TCP_SRC_PORT;

            if (is_pbroker) {
                zdx_tcp_cookie->probe_info.upstream_system_type = zpn_zdx_system_type_pbroker;
            } else {
                zdx_tcp_cookie->probe_info.upstream_system_type = zpn_zdx_system_type_broker;
            }
            zdx_tcp_cookie->probe_info.customer_gid = global_assistant.customer_gid;
            zdx_tcp_cookie->probe_info.self_system_gid =  global_assistant.gid;
            zdx_tcp_cookie->probe_info.self_system_type = zpn_zdx_system_type_connector;
            memcpy(&(zdx_tcp_cookie->probe_info.downstream_ip), &(mtunnel->server_inet), sizeof(struct argo_inet));
            zdx_tcp_cookie->probe_info.downstream_port = mtunnel->server_port_he;
            zdx_tcp_cookie->probe_info.downstream_system_type = zpn_zdx_system_type_app_server;
            snprintf(zdx_tcp_cookie->probe_info.cloud_name, ZPN_ZDX_CLOUD_NAME_MAX, "%s", assistant_state_get_cloud_name());
            snprintf(zdx_tcp_cookie->mtunnel_id, sizeof(zdx_tcp_cookie->mtunnel_id), "%s", mtunnel->mtunnel_id);
            zdx_tcp_cookie->probe_info.tag_id = mtunnel->broker_tag;
        }
    }
}

static int za_mtunnel_lookup_broker_conn(struct zpn_assistant_mtunnel *mtunnel, int conn_type)
{
    struct zpn_assistant_broker_data*   broker;
    int                                 is_pbroker = 0;
    char                                pbroker_remote_ip_addr[ASSISTANT_UTIL_DOMAIN_MAX_LEN] = {0};
    char                                alt_cloud[256] = {0};
    int                                 alt_domain_enabled = 0;
    int                                 is_proxied = 0;

    if (mtunnel->g_dsp == mtunnel->g_brk) {
        is_pbroker = 1;
        if (assistant_pbroker_control_get_connected_active_ip_and_alt_cloud(mtunnel->g_brk, pbroker_remote_ip_addr, sizeof(pbroker_remote_ip_addr), alt_cloud, sizeof(alt_cloud), &alt_domain_enabled)) {
            return ZPN_RESULT_ERR;
        }
    }

    global_assistant.data_dtls_enabled = assistant_features_is_dtls_enabled(global_assistant.gid,
                                                                                &global_assistant.grp_gids[0],
                                                                                global_assistant.grp_gids_len);

    is_proxied = fohh_is_proxied(is_pbroker ? pbroker_remote_ip_addr : mtunnel->brk_name);
    if (assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
        is_proxied = fohh_is_proxied(mtunnel->bfw_name);
    }

    broker = assistant_data_conn_lookup(mtunnel->g_brk,
                                        mtunnel->g_bfw,
                                        (global_assistant.data_dtls_enabled && !is_proxied),
                                        conn_type);
    if(broker != NULL) {
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_lookup_broker_conn: broker connection already present for conn_type: %d for mtunnel: %s", conn_type, mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_ERROR;
    }
    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_lookup_broker_conn: No broker connection for conn_type: %d for mtunnel: %s", conn_type, mtunnel->mtunnel_id);
    return ZPN_RESULT_ERR;
}

/*
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 *
 * to_control_brk = 0, for_proxy = 1 --> ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN  sni has b_brk, connection to g_bfw
 * to_control_brk = 0, for_proxy = 0 --> ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN           sni has g_brk, connection to g_brk
 *
 * mtunnel->broker: this is the broker data connection, reused for control broker data and control broker proxy data connection.
 *
 */
static int za_mtunnel_setup_broker_conn(struct zpn_assistant_mtunnel *mtunnel, int conn_type)
{
    struct zpn_assistant_broker_data* broker;
    int                               is_pbroker;
    char                              pbroker_remote_ip_addr[ASSISTANT_UTIL_DOMAIN_MAX_LEN] = {0};
    char                              alt_cloud[256] = {0};
    int                               alt_domain_enabled = 0;
    struct zpn_tlv                    *tlv;

    broker = mtunnel->broker;
    /*
     * only place where dispatcher GID == broker gid is pbroker. And in pbroker case the local dispatcher will
     * always say 'connect with me for the data'. So it is safe to assume it is pbroker if g_dsp==g_brk.
     */

    is_pbroker = 0;
    if (mtunnel->g_dsp == mtunnel->g_brk) {
        is_pbroker = 1;
    }

    if (!broker) {

        /*
         * only place where dispatcher GID == broker gid is pbroker. And in pbroker case the local dispatcher will
         * always say 'connect with me for the data'. So it is safe to assume it is pbroker if g_dsp==g_brk.
         *
         * In case of pbroker, dispatcher don't know which ip the connector can reach and connect to. So let this
         * component depend on the fact that this connector will have to maintain a control connection to the pbroker
         * and just use that same peer ip for the data connection too.
         */
        is_pbroker = 0;
        if (mtunnel->g_dsp == mtunnel->g_brk) {
            is_pbroker = 1;

            if (assistant_pbroker_control_get_connected_active_ip_and_alt_cloud(mtunnel->g_brk, pbroker_remote_ip_addr, sizeof(pbroker_remote_ip_addr), alt_cloud, sizeof(alt_cloud), &alt_domain_enabled)) {
                return ZPN_RESULT_ERR;
            }
        }

        global_assistant.data_dtls_enabled = assistant_features_is_dtls_enabled(global_assistant.gid,
                                                                                &global_assistant.grp_gids[0],
                                                                                global_assistant.grp_gids_len);

        int is_proxied = fohh_is_proxied(is_pbroker ? pbroker_remote_ip_addr : mtunnel->brk_name);
        if (assistant_features_is_databroker_resilience_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
            is_proxied = fohh_is_proxied(mtunnel->bfw_name);
        }
        ASSISTANT_DEBUG_MTUNNEL("Feature zrdt/dtls is %s, mtunnel->allow_all_xport = %d, is_proxied = %d",
                                global_assistant.data_dtls_enabled ? "enabled" : "disabled",
                                mtunnel->allow_all_xport, is_proxied);

        pthread_mutex_lock(&(global_assistant.config_lock));
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: broker is NULL, calling assistant_data_lookup_or_create for conn_type: %d for mtunnel: %s", conn_type, mtunnel->mtunnel_id);
        broker = assistant_data_lookup_or_create(mtunnel->g_brk,
                                                 is_pbroker ? pbroker_remote_ip_addr : mtunnel->brk_name,
                                                 is_pbroker,
                                                 (global_assistant.data_dtls_enabled && !is_proxied),
                                                 mtunnel->allow_all_xport,
                                                 alt_cloud,
                                                 alt_domain_enabled,
                                                 mtunnel->g_bfw,
                                                 mtunnel->bfw_name,
                                                 conn_type);
        if (!broker) {
            ASSISTANT_LOG(AL_ERROR, "%s: Could not create broker(%s)", mtunnel->mtunnel_id,
                    is_pbroker ? pbroker_remote_ip_addr : mtunnel->brk_name);
            pthread_mutex_unlock(&(global_assistant.config_lock));
            return ZPN_RESULT_ERR;
        }
        /* Link broker to mtunnel. */
        ASSISTANT_ASSERT_SOFT(!mtunnel->flag.in_broker_list, "%s:Already in broker list",
                              mtunnel->mtunnel_id);

        pthread_mutex_lock(&(broker->lock));
        TAILQ_INSERT_TAIL(&(broker->mtunnel_list), mtunnel, broker_list);
        broker->stats.num_mtunnel++;
        if (broker->is_pbroker) {
            global_assistant.num_mtunnels_pbroker++;
            if (mtunnel->flag.is_muted_health_app) {
                global_assistant.num_mtunnels_pbroker_muted_health_based++;
            } else {
                global_assistant.num_mtunnels_pbroker_health_based++;
            }
        } else {
            global_assistant.num_mtunnels_broker++;
            if (mtunnel->flag.is_muted_health_app) {
                global_assistant.num_mtunnels_broker_muted_health_based++;
            } else {
                global_assistant.num_mtunnels_broker_health_based++;
            }
        }
        pthread_mutex_unlock(&(broker->lock));

        mtunnel->flag.in_broker_list = 1;
        mtunnel->broker = broker;
        mtunnel->tlv_type = broker->tlv_type;

        pthread_mutex_unlock(&(global_assistant.config_lock));

        /* Now we know tlv type of the broker side connection, we can set up the peer relationship */
        {
            int res = ZPN_RESULT_NO_ERROR;
            if (mtunnel->connector && ((mtunnel->connector->type == zct_inspect) ||
                                       (mtunnel->connector->type == zct_tun_inspect) ||
                                       (mtunnel->connector->type == zct_tls_inspect) ||
                                       (mtunnel->connector->type == zct_tun_tls_inspect) ||
                                       (mtunnel->connector->type == zct_idps_inspect) ||
                                       (mtunnel->connector->type == zct_webprobe_http) ||
                                       (mtunnel->connector->type == zct_webprobe_https) ||
                                       (mtunnel->double_encrypt == 1))) {
                /* For double hop (control broker proxy connection), flush the current pipeline and create a new pipeline */
                if ((conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) && (mtunnel->flag.two_hop_proxy_conn_mcons_set == 0)) {
                    if ((mtunnel->connector->type == zct_webprobe_http) || (mtunnel->connector->type == zct_webprobe_https)) {
                        struct zpn_connector_webprobe_http_inspect *http_conn = NULL;
                        struct zpn_connector_webprobe_https_inspect *https_conn = NULL;
                        struct zpn_mconn *cmconn = NULL;
                        if (mtunnel->connector->type == zct_webprobe_http) {
                            http_conn = (struct zpn_connector_webprobe_http_inspect *) mtunnel->connector;
                            cmconn = &(http_conn->inspect_common->mconn_cpair.mconn);
                        } else {
                            https_conn = (struct zpn_connector_webprobe_https_inspect *) mtunnel->connector;
                            cmconn = &(https_conn->inspect_common->mconn_cpair.mconn);
                        }

                        if (cmconn->local_owner) {
                            res = zpn_mconn_remove_local_owner(cmconn, 0, 1, 0, NULL);
                            if (res) {
                                ZPN_LOG(AL_WARNING, "%s: webprobe - zpn_mconn_remove_local_owner failed for client mconn %s", mtunnel->mtunnel_id, zpn_result_string(res));
                            }
                        }
                        if (mtunnel->connector->type == zct_webprobe_http) {
                            zpn_connector_insp_destroy((struct zpn_connector *)http_conn);
                            __sync_fetch_and_add_8(&(stats.num_double_hop_webprobe_http_connector_destroy), 1);
                        } else {
                            zpn_connector_insp_destroy((struct zpn_connector *)https_conn);
                            __sync_fetch_and_add_8(&(stats.num_double_hop_webprobe_https_connector_destroy), 1);
                        }
                        __sync_fetch_and_sub_8(&mtunnel->flag.connector_created, 1);

                        if (za_mtunnel_connect_mconns(mtunnel, 0, 0) != ZPN_RESULT_NO_ERROR) {
                            ASSISTANT_LOG(AL_NOTICE, "za_mtunnel_setup_broker_conn: webprobe: can not connect mconns for mtunnel: %s", mtunnel->mtunnel_id);
                            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: webprobe: za_mtunnel_connect_mconns returned error for mtunnel: %s", mtunnel->mtunnel_id);
                        }
                    } else {
                        struct zpn_connector_inspect *conn = (struct zpn_connector_inspect *)mtunnel->connector;
                        struct zpn_mconn *cmconn = &(conn->mconn_cpair.mconn);
                        struct zpn_mconn *smconn = &(conn->mconn_spair.mconn);
                        if (cmconn->local_owner) {
                            res = zpn_mconn_remove_local_owner(cmconn, 0, 1, 0, NULL);
                            if (res) {
                                ZPN_LOG(AL_WARNING, "%s: zpn_mconn_remove_local_owner failed for client mconn %s", mtunnel->mtunnel_id, zpn_result_string(res));
                            }
                        }
                        if (smconn->local_owner) {
                            res = zpn_mconn_remove_local_owner(smconn, 0, 1, 0, NULL);
                            if (res) {
                                ZPN_LOG(AL_WARNING, "%s: zpn_mconn_remove_local_owner failed for server mconn %s", mtunnel->mtunnel_id, zpn_result_string(res));
                            }
                        }

                        if (mtunnel->double_encrypt && mtunnel->connector->type != zct_tun_inspect && mtunnel->connector->type != zct_tls_inspect && mtunnel->connector->type != zct_tun_tls_inspect) {
                            zpn_connector_tun_destroy((struct zpn_connector *)conn);
                            __sync_fetch_and_add_8(&(stats.num_double_hop_tun_connector_destroy), 1);
                        } else {
                            zpn_connector_insp_destroy((struct zpn_connector *)conn);
                            __sync_fetch_and_add_8(&(stats.num_double_hop_inspect_connector_destroy), 1);
                        }
                        __sync_fetch_and_sub_8(&mtunnel->flag.connector_created, 1);

                        if (za_mtunnel_connect_mconns(mtunnel, 0, 0) != ZPN_RESULT_NO_ERROR) {
                            ASSISTANT_LOG(AL_NOTICE, "za_mtunnel_setup_broker_conn: can not connect mconns for mtunnel: %s", mtunnel->mtunnel_id);
                            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: za_mtunnel_connect_mconns returned error for mtunnel: %s", mtunnel->mtunnel_id);
                        }
                    }
                    mtunnel->flag.two_hop_proxy_conn_mcons_set = 1;
                }
            }

            if (mtunnel->double_encrypt && mtunnel->connector && mtunnel->connector->type != zct_tun_inspect && mtunnel->connector->type != zct_tls_inspect && mtunnel->connector->type != zct_tun_tls_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_bufferevent_thread_id;
                    ASSISTANT_DEBUG_MTUNNEL("%s: Double encrypt, connect with tun on the client side for zrdt", mtunnel->mtunnel_id);
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_bufferevent_thread_id);
                    res  = zpn_connector_tun_connect(mconn_bufferevent_thread_id,
                                                (struct zpn_connector_tun *)mtunnel->connector,
                                                (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                0,
                                                0,
                                                NULL);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for double encrypt returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_double_encrypt_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_webprobe_http) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn),
                                                &mconn_buffereventpair_thread_id);


                    struct zpn_mconn *mconn = (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn)
                                                                                : &(mtunnel->mconn_fohh_tlv_broker.mconn);
                    res = zpn_connector_webprobe_http_inspect_connect_client(mconn_buffereventpair_thread_id,
                                                                            (struct zpn_connector_webprobe_http_inspect *) mtunnel->connector,
                                                                            mconn);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL,
                                    "%s: Client side connect for webprobe http returned: %s",
                                    mtunnel->mtunnel_id,
                                    zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_side_success), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_webprobe_https) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn),
                                                &mconn_buffereventpair_thread_id);


                    struct zpn_mconn *mconn = (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn)
                                                                                : &(mtunnel->mconn_fohh_tlv_broker.mconn);
                    res = zpn_connector_webprobe_https_inspect_connect_client(mconn_buffereventpair_thread_id,
                                                                            (struct zpn_connector_webprobe_https_inspect *) mtunnel->connector,
                                                                            mconn);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL,
                                    "%s: Client side connect for webprobe https returned: %s",
                                    mtunnel->mtunnel_id,
                                    zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_side_success), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_buffereventpair_thread_id);
                    res = zpn_connector_inspect_connect(mconn_buffereventpair_thread_id, (struct zpn_connector_inspect *)mtunnel->connector,
                                                        (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                        0);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for http inspect returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_tun_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_buffereventpair_thread_id);
                    res = zpn_connector_tun_inspect_connect(mconn_buffereventpair_thread_id,
                                                            (struct zpn_connector_tun_inspect *)mtunnel->connector,
                                                            (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                            0, 0, NULL, mtunnel->e_inspection_mode);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for tun inspect returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_tls_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_buffereventpair_thread_id);
                    res = zpn_connector_tls_inspect_connect(mconn_buffereventpair_thread_id,
                                                            (struct zpn_connector_tls_inspect *)mtunnel->connector,
                                                            (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                            0, 0, NULL, mtunnel->e_inspection_mode,
                                                            mtunnel->flag.debug_flag);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for tls inspect returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_tun_tls_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_buffereventpair_thread_id);
                    res = zpn_connector_tun_tls_inspect_connect(mconn_buffereventpair_thread_id,
                                                            (struct zpn_connector_tun_tls_inspect *)mtunnel->connector,
                                                            (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                            0, 0, NULL, mtunnel->e_inspection_mode, mtunnel->flag.debug_flag);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for tun tls inspect returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else if (mtunnel->connector && mtunnel->connector->type == zct_idps_inspect) {
                if (is_mtunnel_conn_type_connector_process_not_done(conn_type, mtunnel)) {
                    int mconn_buffereventpair_thread_id;
                    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &mconn_buffereventpair_thread_id);
                    res = zpn_connector_idps_inspect_connect(mconn_buffereventpair_thread_id, (struct zpn_connector_idps_inspect *)mtunnel->connector,
                                                        (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) : &(mtunnel->mconn_fohh_tlv_broker.mconn),
                                                        0);
                    if (res) {
                        ASSISTANT_LOG(AL_CRITICAL, "%s: Client side connect for IDPS returned: %s", mtunnel->mtunnel_id, zpn_result_string(res));
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side_fail), 1);
                    } else {
                        __sync_add_and_fetch_8(&(stats.tun_connect_inspect_client_side), 1);
                    }
                    set_mtunnel_conn_type_connector_process_done(conn_type, mtunnel);
                }
            } else {
                ASSISTANT_DEBUG_MTUNNEL("%s: Connect peer between server side and broker side for zrdt", mtunnel->mtunnel_id);
                res = zpn_mconn_connect_peer(((mtunnel->ip_protocol == IPPROTO_UDP) ? &(mtunnel->mconn_udp_server.mconn) :
                                             ((mtunnel->ip_protocol == IPPROTO_ICMP || mtunnel->ip_protocol == IPPROTO_ICMPV6) ? &(mtunnel->mconn_icmp_server.mconn) :
                                                                                       &(mtunnel->mconn_bufferevent_server.mconn))),
                                         (broker->tlv_type == zpn_zrdt_tlv) ? &(mtunnel->mconn_zrdt_tlv_broker.mconn) :
                                                                              &(mtunnel->mconn_fohh_tlv_broker.mconn));
                if (res) {
                    ASSISTANT_LOG(AL_CRITICAL, "%s: Cannot connect peer, res = %s", mtunnel->mtunnel_id, zpn_result_string(res));
                }
            }
            if (res != ZPN_RESULT_NO_ERROR) {
                return res;
            }
        }
    }

    if(conn_type == ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) {
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: this is control broker proxy conn, setting two_hop_proxy_conn_init_done to 1. for mtunnel: %s", mtunnel->mtunnel_id);
        mtunnel->flag.two_hop_proxy_conn_init_done = 1;
        mtunnel->two_hop_state = za_control_brk_proxy_conn_done;
    }

    tlv = assistant_data_get_tlv(broker);
    if (zpn_tlv_connected(tlv)) {
        ZPN_DEBUG_MTUNNEL("Data connection to broker is up");
        zpn_zdx_fill_mtunnel_pipeline_cookie(tlv, mtunnel, is_pbroker);
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: connection to broker is up. for mtunnel: %s", mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_ERROR;
    } else {
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_setup_broker_conn: connection to broker is not up yet, returning ASYNCHRONOUS for mtunnel: %s, mtunnel->broker: %p", mtunnel->mtunnel_id, mtunnel->broker);
        ZPN_DEBUG_MTUNNEL("Data connection to broker is not up yet");
        return ZPN_RESULT_ASYNCHRONOUS;
    }
}

/*
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 */
static int za_mtunnel_broker_bind(struct zpn_assistant_mtunnel *mtunnel)
{
    int res;
    struct zpn_assistant_broker_data *broker;
    struct zpn_tlv *tlv;
    int64_t delta_us;

    broker = mtunnel->broker;
    if (!broker) {
        /* This is an inconsistent state... */
        return ZPN_RESULT_ERR;
    }

    if (mtunnel->flag.broker_bind_sent) return ZPN_RESULT_ASYNCHRONOUS;

    tlv = assistant_data_get_tlv(broker);
    if (zpn_tlv_sanity_check(tlv)) {
        /* Inconsistent state... */
        return ZPN_RESULT_ERR;
    }

    mtunnel->bind_tx_cloud_us = assistant_state_get_current_time_cloud_us();
    delta_us = mtunnel->server_connection_setup_end_us - mtunnel->server_connection_setup_start_us;

    ASSISTANT_DEBUG_MTUNNEL("%s: For protocol = %d, SIP: %s", mtunnel->mtunnel_id, mtunnel->ip_protocol, mtunnel->mconn_icmp_server.src_ipaddr);
#if 0
    if (mtunnel->ip_protocol == IPPROTO_ICMP) {
        argo_string_to_inet(mtunnel->mconn_icmp_server.src_ipaddr, &mtunnel->assistant_inet);
    }
#endif
    res = assistant_data_rpc_tx_bind(broker, mtunnel->mtunnel_id, delta_us, global_assistant.control_brk_gid ? global_assistant.control_brk_gid : mtunnel->g_bfw, mtunnel->brk_req_bfw_us,
                                     mtunnel->g_aps, mtunnel->brk_req_server_inet, mtunnel->server_inet,
                                     mtunnel->server_port_he, mtunnel->assistant_inet, mtunnel->assistant_port_he,
                                     mtunnel->brk_req_dsp_us, mtunnel->bind_tx_cloud_us, mtunnel->brk_req_ast_rx_us,
                                     mtunnel->g_app, mtunnel->g_app_grp, mtunnel->g_ast_grp, mtunnel->g_srv_grp, mtunnel->g_dsp,
                                     mtunnel->path_decision, mtunnel->flag.dsp_bypassed, mtunnel->insp_status, mtunnel->ssl_err);
    if (res) {
        return res;
    }

    mtunnel->flag.broker_bind_sent = 1;

    return ZPN_RESULT_ASYNCHRONOUS;
}

int64_t zpn_assistant_mtunnel_get_mtunnel_limit_threashold()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ASSISTANT_MTUNNEL_CONCURRENT_EXECUTING,
                                                 &value,
                                                 ASSISTANT_MAX_CONCURRENT_MTUNNEL_DEFAULT,
                                                 global_assistant.gid,
                                                 global_assistant.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ASSISTANT_MAX_CONCURRENT_MTUNNEL_DEFAULT;
    }
}

/*
 * rejecting mtunnel request if total number of acitive mtunnel + reapped mtunnel reaches the limit
 * as active mtunnel and reapped mtunnel is holding system resoruces.
 */
static int zpn_assistant_mtunnel_check_mtunnel_limit()
{
    int64_t mtunnels_hold_system_resources;
    mtunnels_hold_system_resources = (stats.num_mtunnel_in_buckets_queue) + (stats.num_mtunnel_in_reap_queue);

    if (mtunnels_hold_system_resources >= zpn_assistant_mtunnel_get_mtunnel_limit_threashold()) {
        __sync_add_and_fetch_8(&(stats.num_of_mtunnel_rejected_due_to_no_system_capacity), 1);
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Run state machine on a locked mtunnel. This will make the mtunnel
 * 'make what progress it can'. If an error occurs, the mtunnel is
 * marked for destruction.
 *
 * This state machine includes checking for timeouts, etc.
 *
 * Never asynchronous, but some operations that it performs will occur
 * asynchronously over time.
 *
 * Send any messages that need to be sent...
 * Time out anything that needs to be timed out...
 * Locking: it is assumed bucket and mtunnel are both locked at this point.
 */
static
void za_mtunnel_locked_state_machine(struct zpn_assistant_mtunnel *mtunnel)
{

    int res;
    int is_dns_failed;
    int is_process_fd_exhausted;
    int is_system_fd_exhausted;
    int is_ephemeral_port_exhausted;
    int is_mtunnel_terminating_at_assistant;
    int is_zdx_http_ipv6_but_disabled;
    int is_zdx_https_ipv6_but_disabled;

    if (!mtunnel) {
        ASSISTANT_LOG(AL_CRITICAL, "Null mtunnel");
        return;
    }
    ASSISTANT_DEBUG_MTUNNEL("%s: State Machine Entry, state = %s", mtunnel->mtunnel_id, za_mtunnel_state(mtunnel->state));

    /* Generic timeout check */
    switch (mtunnel->state) {
    case za_request_received:

        res = zpn_assistant_mtunnel_check_mtunnel_limit();
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "%s : rejecting mtunnel request due to current active mtunnels reached limit", mtunnel->mtunnel_id);
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_SYSTEM_MTUNNEL_LIMIT_REACHED);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_SYSTEM_MTUNNEL_LIMIT_REACHED);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        }
        /* Verify we have all our state for this application/etc, and
         * open up the connection. */
        is_process_fd_exhausted = 0;
        is_system_fd_exhausted = 0;
        is_ephemeral_port_exhausted = 0;
        is_dns_failed = 0;
        is_mtunnel_terminating_at_assistant = 0;
        is_zdx_http_ipv6_but_disabled = 0;
        is_zdx_https_ipv6_but_disabled = 0;
        res = za_mtunnel_verify_and_connect_server(mtunnel, &is_dns_failed, &is_process_fd_exhausted,
                                                   &is_system_fd_exhausted, &is_ephemeral_port_exhausted,
                                                   &is_mtunnel_terminating_at_assistant, &is_zdx_http_ipv6_but_disabled,
                                                   &is_zdx_https_ipv6_but_disabled);
        if (is_process_fd_exhausted) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_NO_PROCESS_FD);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_NO_PROCESS_FD);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (is_system_fd_exhausted) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_NO_SYSTEM_FD);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_NO_SYSTEM_FD);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (is_ephemeral_port_exhausted) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_NO_EPHEMERAL_PORT);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_NO_EPHEMERAL_PORT);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (is_dns_failed) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_NO_DNS_TO_SERVER);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_NO_DNS_TO_SERVER);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (is_zdx_http_ipv6_but_disabled) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTP_DISABLED);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTP_DISABLED);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (is_zdx_https_ipv6_but_disabled) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTPS_DISABLED);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTPS_DISABLED);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Waiting for asynchronous response - server connection or dns resolution", mtunnel->mtunnel_id);
            break;
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        } else if (res) {
            ASSISTANT_LOG(AL_NOTICE, "%s: cannot open connection to server - %s", mtunnel->mtunnel_id, zpath_result_string(res));
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_OPEN_SERVER_CONN);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_OPEN_SERVER_CONN);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        }
        if (1 == mtunnel->flag.waf_requested && 0 == mtunnel->flag.waf_session_ready) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Waiting for asynchronous response - WAF resources", mtunnel->mtunnel_id);
            break;
        }

        if (is_mtunnel_terminating_at_assistant) {
            if ((mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https) ||  (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) ||
                (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe && zpn_zdx_http_webprobe_cache_config_is_intercept_enabled())) {

                ASSISTANT_DEBUG_MTUNNEL("%s: mtunnel for probe type %d webprobe_cache/mtr mtunnel end terminated at connector (not connected to server)",
                                        mtunnel->mtunnel_id, mtunnel->zpn_probe_type);

                /* Continue setting up webprobe_cache https pipeline */
                if (za_mtunnel_connect_mconns(mtunnel, 0, 1) != ZPN_RESULT_NO_ERROR) {

                    ASSISTANT_LOG(AL_NOTICE, "%s: mtr/webprobe_cache, probe type %d  cannot setup cache pipeline - %s",
                                  mtunnel->mtunnel_id, mtunnel->zpn_probe_type, zpath_result_string(res));
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_OPEN_SERVER_CONN);
                    mtunnel->flag.broker_end_done = 1;
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_OPEN_SERVER_CONN);
                    za_mtunnel_locked_state_machine(mtunnel);
                    break;
                }
                /* Success - continue to the next mtunnel states as za_server_conn_open, despite end terminated connection at connector (not connected to server) */
            }
        }

        /* server connection is already open. (Does this ever occur? Probably not) */
        if (!mtunnel->server_connection_setup_end_us) {
            mtunnel->server_connection_setup_end_us = epoch_us();
        }
        ASSISTANT_DEBUG_MTUNNEL("Transition for za_server_conn_open");
        mtunnel->state = za_server_conn_open;

        /* FALL THROUGH... */

    case za_server_conn_open:
        /* if double hop is enabled and we have data broker fqdn in the cache straight away try double hop */
        if(IS_TWO_HOP_MTUNNEL(mtunnel) && (za_lookup_user_broker_name_in_cache(mtunnel->brk_name) != NULL)) {
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, broker: %s present in cache for mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
            mtunnel->flag.two_hop_from_cache = 1;
            if(za_mtunnel_lookup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) == ZPN_RESULT_NO_ERROR) {
                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN is intact for broker: %s.  mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                /* we have proxy connection set up at the control broker, link this connection
                 * to mtunnel and move the mtunnel state to za_broker_conn_open */
                res = za_mtunnel_setup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN);
                if(res == ZPN_RESULT_NO_ERROR) {
                    mtunnel->flag.two_hop = 1;
                    mtunnel->state = za_broker_conn_open;
                    mtunnel->path_decision |= ZPN_TX_PATH_DECISION_AST_DATABROKER_RESILIENCE;
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, set the mtunnel broker to the proxy conn and state to za_broker_conn_open for: %s.", mtunnel->mtunnel_id);
                } else { /* this should not happen, but lets handle this */
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, lookup for proxy conn was pass, but cant setup broker conn for broker: %s.", mtunnel->brk_name);
                    /* go through two hop case */
                    mtunnel->state = za_broker_conn_two_hop;
                    mtunnel->two_hop_state = za_two_hop_started;
                }
            } else {
                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN is Not there for broker: %s.  mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                mtunnel->state = za_broker_conn_two_hop;
                mtunnel->two_hop_state = za_two_hop_started;
            }
            __sync_fetch_and_add_8(&(stats.num_mtunnel_in_double_hop), 1);
        } else {
            /* Verify if we have an open connection to the appropriate broker */
            res = za_mtunnel_setup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_DATA_BROKER_CONN);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                ASSISTANT_DEBUG_MTUNNEL("%s: Waiting for asynchronous response- broker connection", mtunnel->mtunnel_id);
                break;
            } else if (res) {
                /* Error in establishing broker connection... try double hop if it is enabled */
                if(IS_TWO_HOP_MTUNNEL(mtunnel)) {
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_server_conn_open, error in data conn to broker: %s. set state = za_broker_conn_two_hop for mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                    mtunnel->state = za_broker_conn_two_hop;
                    mtunnel->two_hop_state = za_two_hop_started;
                    __sync_fetch_and_add_8(&(stats.num_mtunnel_in_double_hop), 1);
                } else {
                    ASSISTANT_LOG(AL_NOTICE, "%s: Error opening broker connection: %s", mtunnel->mtunnel_id, zpath_result_string(res));
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    mtunnel->flag.broker_end_done = 1;
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    za_mtunnel_locked_state_machine(mtunnel);
                    break;
                }
            } else {
                /* Have a connection open... */
                mtunnel->state = za_broker_conn_open;
            }
        }

        /* FALL THROUGH... */

    case za_broker_conn_two_hop:
        /*
         * If the mtunnel state is za_broker_conn_open, just fall through. Otherwise if double hop is enabled,
         * setup control broker proxy connection with sni:
         * <assistant_gid>.<user broker_gid>.adata.<cloud_name>
         */
        if((mtunnel->state != za_broker_conn_open) && IS_TWO_HOP_MTUNNEL(mtunnel)) {
            mtunnel->path_decision |= ZPN_TX_PATH_DECISION_AST_DATABROKER_RESILIENCE;
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop for brk_name:%s, mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
            if(mtunnel->flag.two_hop_from_cache == 0) {
                if(za_store_user_broker_name_in_cache(mtunnel->brk_name) == ZPN_RESULT_ERR) {
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop, za_store_user_broker_name_in_cache returned error for brk_name:%s, mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                }
            }
            /* Delink from data broker connection */
            mtunnel->flag.two_hop = 1; /* Prevent mtunnel going to reaping state */
            if ((mtunnel->flag.in_broker_list) && (mtunnel->two_hop_state < za_control_brk_proxy_conn_done)) {
                za_mtunnel_locked_detach_broker(mtunnel);
            }

            /* look for proxy connection, if it is present,  link this connection
            * to mtunnel and move the mtunnel state to za_broker_conn_open */
            if(za_mtunnel_lookup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN) == ZPN_RESULT_NO_ERROR) {
                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN intact for brk_name:%s, mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                res = za_mtunnel_setup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN);
                if(res == ZPN_RESULT_NO_ERROR) {
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop, set mtunnel broker conn to proxy conn and state to za_broker_conn_open to reach brk_name:%s, mtunnel: %s, mtunnel->broker: %p", mtunnel->brk_name, mtunnel->mtunnel_id, mtunnel->broker);
                    mtunnel->flag.two_hop = 1;
                    mtunnel->state = za_broker_conn_open;
                } else if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop. proxy conn ASYNCHRONOUS. Waiting... for mtunnel: %s", mtunnel->mtunnel_id);
                    mtunnel->two_hop_state = za_two_hop_started;
                    break;
                } else { // Error case
                    ASSISTANT_LOG(AL_NOTICE, "%s: Error opening broker connection: %s", mtunnel->mtunnel_id, zpath_result_string(res));
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    mtunnel->flag.broker_end_done = 1;
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop. broker conn error. calling za_mtunnel_locked_mark_destroyed for mtunnel: %s", mtunnel->mtunnel_id);
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    za_mtunnel_locked_state_machine(mtunnel);
                    break;
                }
            }

            /* Setup broker proxy connection */
            if((mtunnel->state != za_broker_conn_open) && (mtunnel->two_hop_state < za_control_brk_proxy_conn_done)) {
                ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop, calling za_mtunnel_setup_broker_conn for ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN with brk_name:%s for mtunnel: %s", mtunnel->brk_name, mtunnel->mtunnel_id);
                res = za_mtunnel_setup_broker_conn(mtunnel, ZPN_ASSISTANT_MTUNNEL_CONTROL_BROKER_PROXY_CONN);
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop. proxy conn ASYNCHRONOUS. Waiting... for mtunnel: %s", mtunnel->mtunnel_id);
                    ASSISTANT_DEBUG_MTUNNEL("%s: Waiting for asynchronous response- control broker proxy connection", mtunnel->mtunnel_id);
                    break;
                } else if (res) {
                    ASSISTANT_LOG(AL_NOTICE, "%s: Error opening broker connection: %s", mtunnel->mtunnel_id, zpath_result_string(res));
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    mtunnel->flag.broker_end_done = 1;
                    ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop. broker conn error. calling za_mtunnel_locked_mark_destroyed for mtunnel: %s", mtunnel->mtunnel_id);
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_OPEN_BROKER_CONN);
                    za_mtunnel_locked_state_machine(mtunnel);
                    break;
                }
            }
            ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST za_mtunnel_locked_state_machine: case za_broker_conn_two_hop, set state: za_broker_conn_open for mtunnel: %s", mtunnel->mtunnel_id);
            mtunnel->state = za_broker_conn_open;
        }

        /* FALL THROUGH... */

    case za_broker_conn_open:
        res = za_mtunnel_broker_bind(mtunnel);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Waiting for asynchronous response - broker bind", mtunnel->mtunnel_id);
            break;
        } else if (res) {
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_BROKER_BIND_FAIL);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_BROKER_BIND_FAIL);
            za_mtunnel_locked_state_machine(mtunnel);
            break;
        }

        /* Have broker connection bound.. This is also a weird code path. */
        mtunnel->state = za_complete;

    case za_complete:
        /* Chill. */
        mtunnel->zpn_zdx_webprobe_mtunnel_setup_complete_ts = epoch_s();
        mtunnel->zpn_update_trans_log_ts = 0;
        break;

    case za_reaping:
        if (za_mtunnel_is_server_connect_failed(mtunnel)) {
            if (assistant_features_is_assistant_ncache_enabled()) {
                /* server connect failed - add server to ncache */
                char inet_str[ARGO_INET_ADDRSTRLEN];
                argo_inet_generate(inet_str, &mtunnel->brk_req_server_inet);
                assistant_ncache_insert(mtunnel->domain,
                                    mtunnel->client_port_he,
                                    mtunnel->ip_protocol,
                                    &mtunnel->brk_req_server_inet,
                                    NULL);
                ASSISTANT_LOG(AL_NOTICE, "mtunnel %s establishment: server connect failed for "
                    "domain=%s port=%d proto=%d inet=%s", mtunnel->mtunnel_id, mtunnel->domain,
                    mtunnel->client_port_he, mtunnel->ip_protocol, inet_str);
            }

            /* Send server connect failed error */
            za_mtunnel_send_server_connect_failed(mtunnel);
        }
        /* Resend end messages until they're successful. */
        if (mtunnel->flag.is_terminating_due_to_data_conn_lost || mtunnel->flag.is_terminating_due_to_conn_stuck) {
            ASSISTANT_DEBUG_MTUNNEL("Terminating Mtunnel %s with reason %s, will discard all the data sent through this mtunnel", mtunnel->mtunnel_id, (mtunnel->err) ? mtunnel->err : "data connection lost");
            za_mtunnel_terminate(mtunnel, 1, NULL);
        } else {
            za_mtunnel_terminate(mtunnel, 0, NULL);
        }

        mtunnel->flag.is_terminating_due_to_data_conn_lost = 0;
        if (mtunnel->flag.broker_end_done &&
            (!(mtunnel->flag.waf_requested) || 0 == mtunnel->async_count)
            ) { /*do not delete if pending async operations */
            ASSISTANT_ASSERT_SOFT(!(mtunnel->flag.waf_requested) ||
                    (mtunnel->flag.waf_requested && mtunnel->flag.waf_req_compl),
                    "Deleting mtunnel while state indicate WAF response pending");
            if (NULL != mtunnel->inspection_ctx) {
                if (mtunnel->inspection_ctx->ssl) {
                    SSL_free(mtunnel->inspection_ctx->ssl);
                }
                if (mtunnel->inspection_ctx->cln_ssl) {
                    SSL_free(mtunnel->inspection_ctx->cln_ssl);
                }
                ASST_FREE(mtunnel->inspection_ctx);
                mtunnel->inspection_ctx = NULL;
            }
            struct zpn_assistant_bucket *bucket;
            int bucket_id;

            ASSISTANT_DEBUG_MTUNNEL("%s: Reaping", mtunnel->mtunnel_id);

            /* Delink from client... */
            if (mtunnel->flag.in_broker_list) {
                za_mtunnel_locked_detach_broker(mtunnel);
            }

            ASSISTANT_DEBUG_MTUNNEL("Delinked from broker");

            /* Remove from bucket hash table... */
            bucket_id = MTUNNEL_HASH_TO_BUCKET(mtunnel->mtunnel_id_hash);
            bucket = &(global_assistant.buckets[bucket_id]);

            if (argo_hash_remove_with_hash(bucket->mtunnel_by_id,
                                           mtunnel->mtunnel_id,
                                           strlen(mtunnel->mtunnel_id),
                                           mtunnel->mtunnel_id_hash,
                                           NULL)) {
                ASSISTANT_LOG(AL_WARNING, "%s: Could not remove from bucket hash", mtunnel->mtunnel_id);
            }

            ASSISTANT_DEBUG_MTUNNEL("Removed from bucket list");

            /* Remove ourselves from bucket list, add to reaped list. */
            TAILQ_REMOVE(&(bucket->bucket_mtunnel_list), mtunnel, bucket_list);
            __sync_fetch_and_sub_8(&(stats.num_mtunnel_in_buckets_queue), 1);
            TAILQ_INSERT_TAIL(&(bucket->bucket_reaped_list), mtunnel, bucket_list);

            __sync_fetch_and_add_8(&stats.num_mtunnel_in_reap_queue, 1);

            mtunnel->state = za_free;
            mtunnel->free_time_s = epoch_s();
        } else {
            __sync_fetch_and_add_8(&stats.num_mtunnel_in_reap_state_but_not_moving_to_reap_queue, 1);
        }
        break;
    case za_free:
        /* Uh, scream. Loudly. */
        ASSISTANT_LOG(AL_CRITICAL, "Running state machine on free mtunnel");
        break;
    default:
        /* Uh, scream. Loudly. */
        ASSISTANT_LOG(AL_CRITICAL, "Unrecognized state machine state");
        break;
    }
}

/*
 * Locking: assume we have the lock of mtunnel
 */
static
int zpn_assistant_mtunnel_log(struct zpn_assistant_mtunnel *mtunnel)
{
    int res;

    mtunnel->log.c_rxbytes = mtunnel->mconn_fohh_tlv_broker.mconn.bytes_to_peer;
    mtunnel->log.c_txbytes = mtunnel->mconn_fohh_tlv_broker.mconn.bytes_to_client;
    if (mtunnel->ip_protocol == IPPROTO_UDP) {
        mtunnel->log.a_rxbytes = mtunnel->mconn_udp_server.mconn.bytes_to_peer;
        mtunnel->log.a_txbytes = mtunnel->mconn_udp_server.mconn.bytes_to_client;
    } else {
        mtunnel->log.a_rxbytes = mtunnel->mconn_bufferevent_server.mconn.bytes_to_peer;
        mtunnel->log.a_txbytes = mtunnel->mconn_bufferevent_server.mconn.bytes_to_client;
    }

    res = zpn_log_structure(zpn_trans_log_description, &(mtunnel->log));

    if (res) {
        ASSISTANT_LOG(AL_WARNING, "zpa connector mtunnel log fail");
    }

    return res;
}

int zpn_assistant_double_encrypt_ssl_ctx_init(const char *root_pem_filename,
                                              const char *certificate_pem_filename,
                                              EVP_PKEY *client_key)
{
    ssl_ctx_connector = zpn_connector_server_ssl_ctx_create(root_pem_filename, certificate_pem_filename, client_key);
    if (ssl_ctx_connector) {
        return ZPN_RESULT_NO_ERROR;
    } else {
        return ZPN_RESULT_ERR;
    }
}

int zpn_assistant_mtunnel_mtls_ssl_ctx_init(const char *root_pem_filename,
                                            const char *certificate_pem_filename,
                                            const char *private_key_pem_filename,
                                            EVP_PKEY *client_key)
{
    ssl_ctx_mtls = fohh_web_client_ssl_ctx_create_with_cert_with_cipher(root_pem_filename, certificate_pem_filename, private_key_pem_filename, client_key, DEFAULT_APP_CONNECTOR_CLIENT_CIPHERSUITE_INDEX);
    if (ssl_ctx_mtls) {
        return ZPN_RESULT_NO_ERROR;
    } else {
        return ZPN_RESULT_ERR;
    }
}

int zpn_assistant_ssl_tunnel_capable()
{
    return (ssl_ctx_connector != NULL);
}

void zpn_assistant_mtunnel_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    TAILQ_INIT(&(free_q.mt_list));
}

void zpn_assistant_mtunnel_state_init()
{
    memset(&state, 0, sizeof(state));
    state.state_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
}

static struct zpn_assistant_mtunnel *zpn_assistant_mtunnel_alloc(void)
{
    struct zpn_assistant_mtunnel *mtunnel = NULL;

    pthread_mutex_lock(&(state.state_lock));

    mtunnel = (struct zpn_assistant_mtunnel *)ASST_CALLOC(sizeof(struct zpn_assistant_mtunnel));
    if (mtunnel) {
        state.stats.total_allocations++;
        state.stats.cur_mtunnels_in_mem++;
        if (state.stats.cur_mtunnels_in_mem > state.stats.peak_mtunnels) {
            state.stats.peak_mtunnels = state.stats.cur_mtunnels_in_mem;
            state.audit.peak_mtunnel_cloud_us = assistant_state_get_current_time_cloud_us();
        }
        memset(mtunnel, 0, sizeof(struct zpn_assistant_mtunnel));
        mtunnel->incarnation = 1;
    }

    pthread_mutex_unlock(&(state.state_lock));

    return mtunnel;
}

static void zpn_assistant_mtunnel_soft_free(struct zpn_assistant_mtunnel *mtunnel)
{
    pthread_mutex_lock(&(free_q.lock));
    za_mtunnel_clear_connect_flags(mtunnel);
    TAILQ_INSERT_TAIL(&(free_q.mt_list), mtunnel, bucket_list);
    __sync_fetch_and_add_8(&(stats.num_mtunnel_in_free_queue), 1);
    mtunnel->on_free_queue_us = epoch_us();
    free_q.stats.free_queue_count++;
    if(mtunnel->flag.two_hop) {
        __sync_fetch_and_sub_8(&(stats.num_mtunnel_in_double_hop), 1);
    }
    if (mtunnel->flag.ready) {
        __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_active), 1);
        if (mtunnel->e_inspection_mode != zpn_traffic_inspection_disabled)
        {
            switch(mtunnel->e_inspection_mode) {
                case zpn_traffic_inspection_krb:
                case zpn_traffic_inspection_krb_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_krb_active), 1);
                    break;
                case zpn_traffic_inspection_smb:
                case zpn_traffic_inspection_smb_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_smb_active), 1);
                    break;
                case zpn_traffic_inspection_ldap:
                case zpn_traffic_inspection_ldap_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_ldap_active), 1);
                    break;
                case zpn_traffic_inspection_http:
                case zpn_traffic_inspection_http_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_http_active), 1);
                    break;
                case zpn_traffic_inspection_https:
                case zpn_traffic_inspection_https_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_https_active), 1);
                    break;
                case zpn_traffic_inspection_auto:
                case zpn_traffic_inspection_auto_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_auto_active), 1);
                    break;
                case zpn_traffic_inspection_auto_tls:
                case zpn_traffic_inspection_auto_tls_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_auto_tls_active), 1);
                    break;
                case zpn_traffic_inspection_ptag:
                case zpn_traffic_inspection_ptag_double_encrypted:
                    __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_inspect_ptag_active), 1);
                    break;
                default:
                    break;
            }
        }
        if (mtunnel->flag.is_muted_health_app) {
            __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_active_muted_health_based), 1);
        } else {
            __sync_fetch_and_sub_8(&(global_assistant.num_mtunnels_active_health_based), 1);
        }
    }
    pthread_mutex_unlock(&(free_q.lock));
}

int zpn_assistant_is_waf_http_inspection_mode(int mode)
{
    switch(mode) {
        case zpn_traffic_inspection_http:
        case zpn_traffic_inspection_https:
        case zpn_traffic_inspection_http_double_encrypted:
        case zpn_traffic_inspection_https_double_encrypted:
            return 1;

        default:
            break;
    }
    return 0;
}

void zpn_assistant_free_mtunnel_mem()
{
    struct zpn_assistant_mtunnel *mtunnel = NULL;
    struct zpn_assistant_mtunnel *tmp_mtunnel = NULL;
    int64_t now_us = epoch_us();
    int64_t loop_end_us;
    int freed_count = 0;
    int freed_connector = 0;
    int64_t mt_queue_us = 0;
    int mt_inspect = 0;
    int64_t mt_waf_timeout = zpn_waf_inspection_timeout() * 1000000;

    pthread_mutex_lock(&(free_q.lock));

    mtunnel = TAILQ_FIRST(&(free_q.mt_list));

    while(mtunnel != NULL) {
        tmp_mtunnel = TAILQ_NEXT(mtunnel, bucket_list);

        if (mtunnel->connector && freed_connector >= MAX_CONNECTOR_FREED_PER_ONE_ROUND) {
            mtunnel = tmp_mtunnel;
            continue;
        }

        mt_queue_us = now_us - mtunnel->on_free_queue_us;
        mt_inspect = zpn_is_connector_inspection_type(mtunnel->connector);

        if (mt_queue_us >= FREE_MTUNNEL_TIMEOUT_US) {
            if (mt_inspect && zpn_assistant_is_waf_http_inspection_mode(mtunnel->e_inspection_mode)) {
                if (mt_queue_us < mt_waf_timeout) {
                    /* Delay freeing the Inspection Mtunnel to configured timeout */
                    mtunnel = tmp_mtunnel;
                    continue;
                }
            }

            TAILQ_REMOVE(&(free_q.mt_list), mtunnel, bucket_list);
            __sync_fetch_and_sub_8(&(stats.num_mtunnel_in_free_queue), 1);
            if (mtunnel->connector) {
                if (zpn_is_connector_inspection_type(mtunnel->connector)) {
                    __sync_fetch_and_sub_8(&mtunnel->flag.connector_created, 1);
                    if (0 == mtunnel->flag.connector_created) {
                        ASSISTANT_DEBUG_MTUNNEL("[%s] Cleaning Inspection Mtunnel resources", mtunnel->mtunnel_id);
                        zpn_connector_insp_destroy(mtunnel->connector);
                    } else {
                        __sync_fetch_and_add_8(&(global_assistant.num_inspect_connectors_double_free), 1);
                        ASSISTANT_LOG(AL_ERROR, "Error connector already freed mtunnel: %p %s conn: %p connector_created: %"PRId64"",
                                mtunnel, mtunnel->mtunnel_id,
                                mtunnel->connector,
                                mtunnel->flag.connector_created);
                    }
                } else {
                    zpn_connector_tun_destroy(mtunnel->connector);
                }
                /* free slow since free for connector is a heavy operation */
                ZPN_FREE_SLOW(mtunnel->connector);
                freed_connector++;
            }
            if (mtunnel->mtunnel_id) {
                ASST_FREE(mtunnel->mtunnel_id);
                mtunnel->mtunnel_id = NULL;
            }
            ASST_FREE(mtunnel);
            mtunnel = NULL;
            free_q.stats.frees++;
            free_q.stats.free_queue_count--;
            freed_count ++;
            pthread_mutex_lock(&(state.state_lock));
            state.stats.cur_mtunnels_in_mem--;
            pthread_mutex_unlock(&(state.state_lock));
            if (freed_count > 5000) break;
        } else {
            break;
        }

        mtunnel = tmp_mtunnel;

    }

    /* Too chatty, disabling it!
     * ASSISTANT_DEBUG_MTUNNEL("zpn_assistant_free_mtunnel_mem: alloc = %ld, free = %ld, free_queue = %ld, this free = %ld",
     *                      (long)state.stats.total_allocations, (long)free_q.stats.frees,
     *                      (long)free_q.stats.free_queue_count, (long)freed_count);
     */

    pthread_mutex_unlock(&(free_q.lock));

    /* Since this routine could take a long time, we're exposing the elapsed time to cloud for debugging purposes */
    loop_end_us = epoch_us();
    if (state.timer_cb_stats.max_time_elapsed_in_free_mtunnel_since_last_upload < (loop_end_us - now_us)) {
        state.timer_cb_stats.max_time_elapsed_in_free_mtunnel_since_last_upload = loop_end_us - now_us;
    }

}

/*
 * Called when the data connection is connected.
 */
void
zpn_assistant_mtunnel_data_connected(struct zpn_assistant_broker_data *broker)
{
    struct zpn_assistant_mtunnel *mtunnel;

    /* Run state machine on all mtunnels that are 'chillin Be
     * careful of lock ordering. */
    pthread_mutex_lock(&(broker->lock));
    mtunnel = TAILQ_FIRST(&(broker->mtunnel_list));
    pthread_mutex_unlock(&(broker->lock));
    if (mtunnel) {
        do {
            za_mtunnel_bucket_lock(mtunnel);
            za_mtunnel_lock(mtunnel);
            za_mtunnel_locked_state_machine(mtunnel);
            za_mtunnel_unlock(mtunnel);
            za_mtunnel_bucket_unlock(mtunnel);
            pthread_mutex_lock(&(broker->lock));
            mtunnel = TAILQ_NEXT(mtunnel, broker_list);
            pthread_mutex_unlock(&(broker->lock));
        } while (mtunnel);
    }
}

/*
 * Called when the data connection is disconnected.
 */
void
zpn_assistant_mtunnel_data_disconnected(struct zpn_assistant_broker_data *broker)
{
    struct zpn_assistant_mtunnel *mtunnel;

    do {
        pthread_mutex_lock(&(broker->lock));
        mtunnel = TAILQ_FIRST(&(broker->mtunnel_list));
        pthread_mutex_unlock(&(broker->lock));
        if (mtunnel) {
            ASSISTANT_DEBUG_MTUNNEL("%s: removing mtunnel as data connection closed", mtunnel->mtunnel_id);
            za_mtunnel_bucket_lock(mtunnel);
            za_mtunnel_lock(mtunnel);
            if (za_mtunnel_locked_detach_broker(mtunnel) == ZPN_RESULT_NO_ERROR) {
                if(!IS_TWO_HOP_MTUNNEL(mtunnel)) {
                    mtunnel->flag.is_terminating_due_to_data_conn_lost = 1;
                    za_mtunnel_locked_state_machine(mtunnel);
                }
            }
            za_mtunnel_unlock(mtunnel);
            za_mtunnel_bucket_unlock(mtunnel);
        }
    } while (mtunnel);
}

/*
 * Window update came in from the data broker on data connection.
 */
void
zpn_assistant_mtunnel_window_update(struct zpn_assistant_broker_data* broker,
                                    int32_t                           tag_id,
                                    int64_t                           tx_limit,
                                    int64_t                           rx_data)
{
    struct zpn_assistant_mtunnel *mtunnel;

    pthread_mutex_lock(&(broker->lock));
    mtunnel = argo_hash_lookup(broker->mtunnels_by_tag, &tag_id, sizeof(tag_id), NULL);
    pthread_mutex_unlock(&(broker->lock));

    if (!mtunnel) {
        ASSISTANT_LOG(AL_INFO, "Window update received for mtunnel tag_id = %d that isn't found", tag_id);
        return;
    }

    za_mtunnel_lock(mtunnel);
    mtunnel->mconn_fohh_tlv_broker.remote_fc_status = flow_ctrl_enabled;
    mtunnel->mconn_fohh_tlv_broker.tx_limit = tx_limit;
    if (mtunnel->mconn_fohh_tlv_broker.remote_rx_data != rx_data) {
        mtunnel->mconn_fohh_tlv_broker.remote_rx_data = rx_data;
        mtunnel->mconn_fohh_tlv_broker.remote_rx_data_change_us = epoch_us();
    }
    zpn_client_drain_tx_data(&(mtunnel->mconn_fohh_tlv_broker.mconn));
    za_mtunnel_unlock(mtunnel);

}

/*
 * Today we are just dropping the mtunnel_bind_ack from data broker
 * if connector receives the ack late that connector already
 * destroyed the mtunnel (receive after 20s after bind sent)
 *
 * In the future, we might want to have connector to send another
 * broker_request_ack to data broker in this case for analysis purpose.
 */
int
zpn_assistant_mtunnel_bind_ack_cb(struct zpn_assistant_broker_data* broker,
                                  int32_t                           tag_id,
                                  const char*                       mtunnel_id,
                                  const char*                       error)
{
    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int res;

    ASSISTANT_DEBUG_MTUNNEL("%s: Got mtunnel_bin_ack, broker tlv_type = %s, tag_id = %d", mtunnel_id, zpn_tlv_type_str(broker->tlv_type), tag_id);

    mtunnel_id_len = strlen(mtunnel_id);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%s: Bind ack, tag = %d, but no tunnel!?", mtunnel_id, tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    za_mtunnel_lock(mtunnel);

    /* Bind ack has error, destroy the mtunnel */
    if (error) {
        mtunnel->flag.broker_end_done = 1;
        za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_BIND_ACK);
        za_mtunnel_locked_state_machine(mtunnel);

        ASSISTANT_DEBUG_MTUNNEL("%s: Mtunnel END. MTUNNEL GONE", mtunnel->mtunnel_id);

        za_mtunnel_unlock(mtunnel);
        za_mtunnel_bucket_unlock(mtunnel);

        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel->broker_tag = tag_id;
    mtunnel->bind_ack_ast_rx_us = assistant_state_get_current_time_cloud_us();

    if (broker->tlv_type == zpn_zrdt_tlv) {
        /* Save the max tag_id we have seen so far */
        if (tag_id > broker->broker_zrdt_tlv_state.max_stream_id) {
            broker->broker_zrdt_tlv_state.max_stream_id = tag_id;
        }

        if (!mtunnel->mconn_zrdt_tlv_broker.stream) {
            res = zrdt_stream_create(zpn_mconn_zrdt_tlv_get_conn(&(broker->broker_zrdt_tlv_state)),
                                     &(mtunnel->mconn_zrdt_tlv_broker.stream),
                                     mtunnel->broker_tag,
                                     (mtunnel->ip_protocol == IPPROTO_TCP) ? zrdt_stream_reliable_endpoint : zrdt_stream_unreliable_endpoint,
                                     zpn_zrdt_read_cb,
                                     zpn_zrdt_write_cb,
                                     zpn_zrdt_event_cb,
                                     &(broker->broker_zrdt_tlv_state));
            if (res) {
                ASSISTANT_LOG(AL_ERROR, "%s: failed to create stream for tag_id = %d.", mtunnel->mtunnel_id, mtunnel->broker_tag);
            } else {
                ASSISTANT_DEBUG_MTUNNEL("%s: create stream for tag_id = %d.", mtunnel->mtunnel_id, mtunnel->broker_tag);
                zrdt_set_stream_cookie(mtunnel->mconn_zrdt_tlv_broker.stream, mtunnel, mtunnel->incarnation);
            }
        }

        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_zrdt_tlv_broker.mconn),
                                        0,
                                        &(broker->broker_zrdt_tlv_state),
                                        &(mtunnel->broker_tag),
                                        sizeof(mtunnel->broker_tag),
                                        &zpn_mconn_zrdt_tlv_calls);
    } else {
        /* Save the max tag_id we have seen so far */
        if (tag_id > broker->broker_tlv_state.max_tag_id) {
            broker->broker_tlv_state.max_tag_id = tag_id;
        }

        zpn_mconn_set_fohh_thread_id(&(mtunnel->mconn_fohh_tlv_broker.mconn),
                                 fohh_connection_get_thread_id(zpn_mconn_fohh_tlv_get_conn(&(broker->broker_tlv_state))));

        res = zpn_mconn_add_local_owner(&(mtunnel->mconn_fohh_tlv_broker.mconn),
                                    0,
                                    &(broker->broker_tlv_state),
                                    &(mtunnel->broker_tag),
                                    sizeof(mtunnel->broker_tag),
                                    &zpn_mconn_fohh_tlv_calls);

        /* zdx probe stream are excluded from data link between broker and connector globally
         * remote_fc_status is initialize to flow_ctrl_none by default.
         * upon receiving mconn window update, corresponding mconn will set remote_fc_stats = flow_ctrl_enable
         * since for zdx probe type mtunnel, broker side is seting remote_fc_stats to flow_ctrl_disabled, appc side should keep in sync
         *
         * in above zpn_mconn_add_local_owner() call,
         * mconn_fohh_tlv->remote_fc_status = fohh_tlv->remote_fc_status;
         *
         * now we set mconn_fohh_tlv_broker.remote_fc_status mconn level.
         */
        if ((mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) ||
            (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe) ||
            (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https)) {
            mtunnel->mconn_fohh_tlv_broker.remote_fc_status = flow_ctrl_disabled;
        }
        /* configuration for fin expire time */
        int64_t fin_expire_us = assistant_features_mtunnel_fin_expire_us(global_assistant.gid);
        if (fin_expire_us) {
            mtunnel->mconn_fohh_tlv_broker.mconn.config_fin_expire_us = fin_expire_us;
            mtunnel->mconn_bufferevent_server.mconn.config_fin_expire_us = fin_expire_us;
        } else {
            mtunnel->mconn_fohh_tlv_broker.mconn.config_fin_expire_us = ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT*1000000;
            mtunnel->mconn_bufferevent_server.mconn.config_fin_expire_us = ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT*1000000;
        }
        /* check config if mconn_track_perf
         * if yes, set mconn flag to start mconn_track_perf stats
         * behavior - applicable for new mtunnels only - during mtunnel transition changes
         */
        if(assistant_cfg_is_mconn_track_perf_stats()){
            mtunnel->mconn_fohh_tlv_broker.mconn.is_mconn_track_perf_stats_enabled = 1;
            mtunnel->mconn_bufferevent_server.mconn.is_mconn_track_perf_stats_enabled = 1;
        } else {
            mtunnel->mconn_fohh_tlv_broker.mconn.is_mconn_track_perf_stats_enabled = 0;
            mtunnel->mconn_bufferevent_server.mconn.is_mconn_track_perf_stats_enabled = 0;
        }
        if (assistant_features_allocator_libevent_out_queue_is_enabled(global_assistant.gid)) {
            mtunnel->mconn_bufferevent_server.mconn.allocator_libevent_out_queue_is_enabled = 1;
        } else {
            mtunnel->mconn_bufferevent_server.mconn.allocator_libevent_out_queue_is_enabled = 0;
        }
    }
    if (res) {
        ASSISTANT_LOG(AL_CRITICAL, "Add local owner: %s", zpn_result_string(res));
        res = za_mtunnel_terminate(mtunnel, 0, AST_MT_SETUP_ERR_BIND_TO_AST_LOCAL_OWNER);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "za_mtunnel_terminate returned %s", zpn_result_string(res));
        }
        za_mtunnel_unlock(mtunnel);
        za_mtunnel_bucket_unlock(mtunnel);
        return res;
    }

    pthread_mutex_lock(&(broker->lock));
    res = argo_hash_store(broker->mtunnels_by_tag,
                          &(mtunnel->broker_tag),
                          sizeof(mtunnel->broker_tag),
                          1,
                          mtunnel);
    pthread_mutex_unlock(&(broker->lock));
    if (res) {
        ASSISTANT_LOG(AL_CRITICAL, "%s: hash store failed.", mtunnel->mtunnel_id);
        mtunnel->flag.broker_end_done = 1;
        za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_BRK_HASH_TBL_FULL);
        za_mtunnel_locked_state_machine(mtunnel);
        za_mtunnel_unlock(mtunnel);
        za_mtunnel_bucket_unlock(mtunnel);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel->state = za_complete;

    ASSISTANT_DEBUG_MTUNNEL("%s: Bind ack, tag = %d. MTUNNEL COMPLETE", mtunnel->mtunnel_id, tag_id);

    za_mtunnel_locked_state_machine(mtunnel);

    if (mtunnel->mconn_fohh_tlv_broker.mconn.fin_sent) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Got fin before bind_ack, forward the FIN", mtunnel->mtunnel_id);
        mtunnel->mconn_fohh_tlv_broker.mconn.fin_sent = 0;
        zpn_mconn_forward_mtunnel_end(&(mtunnel->mconn_fohh_tlv_broker.mconn), MT_CLOSED_TERMINATED, 0);
    }

    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    return ZPN_RESULT_NO_ERROR;
}

int
zpn_assistant_mtunnel_end(struct zpn_assistant_broker_data* broker,
                          const char*                       mtunnel_id,
                          int32_t                           tag_id,
                          int32_t                           drop_data,
                          const char*                       error)
{
    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int delay_forward_fin = 0;
    int64_t now_us = epoch_us();

    if (mtunnel_id) {
        mtunnel_id_len = strlen(mtunnel_id);
        mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

        mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
        if (!mtunnel) {
            ASSISTANT_LOG(AL_WARNING, "Mtunnel_id %s: Mtunnel end, but no tunnel!?", mtunnel_id);
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        pthread_mutex_lock(&(broker->lock));
        mtunnel = argo_hash_lookup(broker->mtunnels_by_tag,
                                   &tag_id,
                                   sizeof(tag_id),
                                   NULL);
        pthread_mutex_unlock(&(broker->lock));

        if (!mtunnel) {
            // This seems to be a moderately common occurrence...?
            //ASSISTANT_LOG(AL_WARNING, "Tag_id %d: Mtunnel end, but no tunnel!?", req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }

        za_mtunnel_bucket_lock(mtunnel);
    }

    za_mtunnel_lock(mtunnel);

    if (mtunnel->state >= za_reaping) {
        /* It is already being freed, so don't do anything here */
        goto exit;
    }

    ZPN_ASSERT(mtunnel->mtunnel_id);

    struct zpn_mconn *broker_tlv_mconn = zpn_assistant_mtunnel_broker_mconn(mtunnel);

    broker_tlv_mconn->fin_rcvd = 1;
    if (!broker_tlv_mconn->fin_rcvd_us) broker_tlv_mconn->fin_rcvd_us = epoch_us();
    if (drop_data) {
        broker_tlv_mconn->drop_tx = drop_data;
    }

    if (mtunnel->double_encrypt && !drop_data) {
        ASSISTANT_DEBUG_MTUNNEL("%s: Got FIN from broker, we are DE and drop_data is 0", mtunnel->mtunnel_id);

        if ((mtunnel->mconn_fohh_tlv_broker.mconn.rx_data_us +  FIN_FORWARD_DELAY_US) > now_us) {
            ASSISTANT_DEBUG_MTUNNEL("%s: Not ready to forward the FIN yet, rx_data_us = %ld, now_us = %ld",
                              mtunnel->mtunnel_id, (long)mtunnel->mconn_fohh_tlv_broker.mconn.rx_data_us, (long)now_us);
            mtunnel->delayed_fin_forward_us = mtunnel->mconn_fohh_tlv_broker.mconn.rx_data_us + FIN_FORWARD_DELAY_US;
            delay_forward_fin = 1;
        } else {
            ASSISTANT_DEBUG_MTUNNEL("%s: Ready to forward the FIN, rx_data_us = %ld, now_us = %ld",
                              mtunnel->mtunnel_id, (long)mtunnel->mconn_fohh_tlv_broker.mconn.rx_data_us, (long)now_us);
        }
    }

    if (!delay_forward_fin) {
        if (mtunnel->ip_protocol == IPPROTO_UDP) {
            zpn_mconn_forward_mtunnel_end(&mtunnel->mconn_udp_server.mconn, MT_CLOSED_TERMINATED, drop_data);
        } else if (mtunnel->ip_protocol == IPPROTO_ICMP || mtunnel->ip_protocol == IPPROTO_ICMPV6) {
            zpn_mconn_forward_mtunnel_end(&mtunnel->mconn_icmp_server.mconn, MT_CLOSED_TERMINATED, drop_data);
        } else {
            zpn_mconn_forward_mtunnel_end(&mtunnel->mconn_bufferevent_server.mconn, MT_CLOSED_TERMINATED, drop_data);
        }
    }

exit:
    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    return ZPN_RESULT_NO_ERROR;
}

void
zpn_assistant_mtunnel_tag_pause(struct zpn_assistant_broker_data* broker,
                                int32_t                           tag_id)
{
    struct zpn_assistant_mtunnel *mtunnel;
    pthread_mutex_lock(&(broker->lock));
    mtunnel = argo_hash_lookup(broker->mtunnels_by_tag, &tag_id, sizeof(tag_id), NULL);
    pthread_mutex_unlock(&(broker->lock));

    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%d: Mtunnel pause , but no tunnel!?", tag_id);
        return;
    }

    if (mtunnel->tlv_type == zpn_fohh_tlv) {
        za_mtunnel_lock(mtunnel);
        zpn_mconn_to_client_pause(&(mtunnel->mconn_fohh_tlv_broker.mconn));
        za_mtunnel_unlock(mtunnel);
    }
}

void
zpn_assistant_mtunnel_tag_resume(struct zpn_assistant_broker_data* broker,
                                 int32_t                           tag_id)
{
    struct zpn_assistant_mtunnel *mtunnel;
    pthread_mutex_lock(&(broker->lock));
    mtunnel = argo_hash_lookup(broker->mtunnels_by_tag, &tag_id, sizeof(tag_id), NULL);
    pthread_mutex_unlock(&(broker->lock));

    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%d: Mtunnel resume, but no tunnel!?", tag_id);
        return;
    }

    if (mtunnel->tlv_type == zpn_fohh_tlv) {
        za_mtunnel_lock(mtunnel);
        zpn_mconn_to_client_resume(&(mtunnel->mconn_fohh_tlv_broker.mconn));
        za_mtunnel_unlock(mtunnel);
    }
}


static void  mtunnel_locked_set_waf_inspection_mode(struct zpn_assistant_mtunnel* mtunnel)
{
    if (zpn_waf_inspt_none == mtunnel->e_inspection_type) {
        if (mtunnel->flag.ptag_mode) {
            if (mtunnel->double_encrypt) {
                mtunnel->e_inspection_mode = zpn_traffic_inspection_ptag_double_encrypted;
                mtunnel->insp_status = zpn_trans_waf_double_enc_ptag;
                __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_ptag), 1);
            } else {
                mtunnel->e_inspection_mode = zpn_traffic_inspection_ptag;
                mtunnel->insp_status = zpn_trans_waf_ptag;
                __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_ptag), 1);
            }
            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_ptag_active), 1);
        } else {
            mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
        }
        AST_DBG_MTUN_WAF("Mtunell %s for %s:%d, Seg AppId %ld insp. mode %d [%s]", mtunnel->mtunnel_id, mtunnel->domain,
                mtunnel->client_port_he, (long)mtunnel->g_app, mtunnel->e_inspection_mode,
                traffic_inspection_modes[mtunnel->e_inspection_mode]);
        return;
    }

    if (!mtunnel->inspection_ctx) {
        mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
        mtunnel->insp_status = zpn_trans_waf_feature_disabled;
        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
        AST_DBG_MTUN_WAF("Mtunell %s for %s:%d, Seg AppId %ld insp. mode %d [%s]", mtunnel->mtunnel_id, mtunnel->domain,
                mtunnel->client_port_he, (long)mtunnel->g_app, mtunnel->e_inspection_mode,
                traffic_inspection_modes[mtunnel->e_inspection_mode]);

        return;
    }

    switch (mtunnel->inspection_ctx->insp_appl_type) {
        case zpn_inspection_application_auto:
            {
                PDP_PROTO pclass = mtunnel->pdp_profile;
                if (pclass == PDP_PROTO_TLS) {
                    if (mtunnel->flag.waf_secure) {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto_tls_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_auto_tls;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_auto_tls), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto_tls;
                            mtunnel->insp_status = zpn_trans_waf_auto_tls;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto_tls), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto_tls_active), 1);
                    } else {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_auto;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_auto), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto;
                            mtunnel->insp_status = zpn_trans_waf_auto;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto_active), 1);
                    }
                } else if (pclass == PDP_PROTO_HTTPS) {
                    if (mtunnel->flag.waf_secure) {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_https_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_https;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_https), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_https;
                            mtunnel->insp_status = zpn_trans_waf_https;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_https), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_https_active), 1);
                    } else {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_auto;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_auto), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_auto;
                            mtunnel->insp_status = zpn_trans_waf_auto;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto_active), 1);
                    }
                } else if (pclass == PDP_PROTO_HTTP) {
                    if (mtunnel->double_encrypt) {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_http_double_encrypted;
                        mtunnel->insp_status = zpn_trans_waf_double_enc_http;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_http), 1);
                    } else {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_http;
                        mtunnel->insp_status = zpn_trans_waf_http;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_http), 1);
                    }
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_http_active), 1);
                } else if (pclass == PDP_PROTO_UNKNOWN) {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
                    mtunnel->insp_status = zpn_trans_waf_feature_disabled;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
                } else {
                    if (mtunnel->double_encrypt) {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_auto_double_encrypted;
                        mtunnel->insp_status = zpn_trans_waf_double_enc_auto;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_auto), 1);
                    } else {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_auto;
                        mtunnel->insp_status = zpn_trans_waf_auto;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto), 1);
                    }
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_auto_active), 1);
                }
            }
            break;

        case zpn_inspection_application_http:
        case zpn_inspection_application_https:
            if (mtunnel->flag.waf_secure) {
                if (mtunnel->double_encrypt) {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_https_double_encrypted;
                    mtunnel->insp_status = zpn_trans_waf_double_enc_https;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_https), 1);
                } else {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_https;
                    mtunnel->insp_status = zpn_trans_waf_https;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_https), 1);
                }
                __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_https_active), 1);
            } else {
                if (mtunnel->double_encrypt) {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_http_double_encrypted;
                    mtunnel->insp_status = zpn_trans_waf_double_enc_http;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_http), 1);
                } else {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_http;
                    mtunnel->insp_status = zpn_trans_waf_http;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_http), 1);
                }
                __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_http_active), 1);
            }
            break;

        case zpn_inspection_application_ad:
            {
                mtunnel->insp_protocol_msk = mtunnel->inspection_ctx->insp_protocol_bitmask;
                int appl_insp_type = mtunnel->appl_protocol_msk & mtunnel->insp_protocol_msk;
                if (mtunnel->flag.waf_secure) {
                    mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
                    mtunnel->insp_status = zpn_trans_waf_feature_disabled;
                    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
                    AST_DBG_MTUN_WAF("mtunnel_locked_set_waf_inspection_mode : insp_appl_type [%d] mode [%d] waf %s de %s",
                            mtunnel->inspection_ctx->insp_appl_type, mtunnel->e_inspection_mode,
                            mtunnel->flag.waf_secure?"enabled":"disabled",
                            mtunnel->double_encrypt?"enabled":"disabled");
                } else {
                    PDP_PROTO pclass = mtunnel->pdp_profile;
                    mtunnel->adp_override_msk = assistant_cfg_override_feature_get_ad_protection_protocol_override();
                    AST_DBG_MTUN_WAF("ADP Override bitmask:%lx", (long)mtunnel->adp_override_msk);
                    if (PDP_PROTO_UNKNOWN == pclass) {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
                        mtunnel->insp_status = zpn_trans_waf_feature_disabled;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);

                    } else if (appl_insp_type & zpn_application_protocol_krb && !(mtunnel->adp_override_msk & (1 << PDP_PROTO_KRB))) {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_krb_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_krb;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_krb), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_krb;
                            mtunnel->insp_status = zpn_trans_waf_krb;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_krb), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_krb_active), 1);
                    } else if (appl_insp_type & zpn_application_protocol_ldap && !(mtunnel->adp_override_msk & (1 << PDP_PROTO_LDAP))) {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_ldap_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_ldap;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_ldap), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_ldap;
                            mtunnel->insp_status = zpn_trans_waf_ldap;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_ldap), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_ldap_active), 1);
                    } else if (appl_insp_type & zpn_application_protocol_smb && !(mtunnel->adp_override_msk & (1 << PDP_PROTO_SMB))) {
                        if (mtunnel->double_encrypt) {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_smb_double_encrypted;
                            mtunnel->insp_status = zpn_trans_waf_double_enc_smb;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_DE_smb), 1);
                        } else {
                            mtunnel->e_inspection_mode = zpn_traffic_inspection_smb;
                            mtunnel->insp_status = zpn_trans_waf_smb;
                            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_smb), 1);
                        }
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_smb_active), 1);
                    } else {
                        mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
                        mtunnel->insp_status = zpn_trans_waf_feature_disabled;
                        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
                    }
                    AST_DBG_MTUN_WAF("Mtunnel %s for domain %s port %d set to inspection type %d mode %d",
                            mtunnel->mtunnel_id, mtunnel->domain, mtunnel->client_port_he,
                            mtunnel->inspection_ctx->insp_appl_type, mtunnel->e_inspection_mode);
                }
            }
            break;

        default:
            mtunnel->e_inspection_mode = zpn_traffic_inspection_disabled;
            mtunnel->insp_status = zpn_trans_waf_feature_disabled;
            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_disabled), 1);
            AST_DBG_MTUN_WAF("mtunnel_locked_set_waf_inspection_mode : insp_appl_type [%d] mode [%d]",
                    mtunnel->inspection_ctx->insp_appl_type, mtunnel->e_inspection_mode);
            break;

    }
    AST_DBG_MTUN_WAF("Mtunell %s for %s:%d, Seg AppId %ld insp. mode %d [%s]", mtunnel->mtunnel_id, mtunnel->domain,
            mtunnel->client_port_he, (long)mtunnel->g_app, mtunnel->e_inspection_mode,
            traffic_inspection_modes[mtunnel->e_inspection_mode]);

    return;
}

static char*
ast_mtunnel_locked_process_inspt_rsp(struct zpn_assistant_mtunnel* mtunnel, struct mtunnel_waf_sess_ctx* inspt_ctx, int *res)
{
    char* res_dscr =  "Un-assigned-description";
    if (NULL == mtunnel || NULL==inspt_ctx) {
        return "Invalid NULL input params";
    }
    char* mtunnel_id = mtunnel->mtunnel_id; /* for logging */

    mtunnel->flag.waf_req_compl = inspt_ctx->flag.inspection_resources_fetch_complete;

    /* check if there was an error in application/profile or any resource fetch */
    if (inspt_ctx->flag.inspection_failure) {
        /* We hit some error. Now update the reason for that in 'insp_status' */
        mtunnel->insp_status = zpn_trans_waf_resource_unavail;

        if (inspt_ctx->flag.inspection_ssl_ctx_fetch_failure) {
            if (inspt_ctx->flag.inspection_cert_gen_ssl_ctx_fetch_failure)
                mtunnel->insp_status = zpn_trans_waf_ssl_ctx_certgen_error;
            else
                mtunnel->insp_status = zpn_trans_waf_ssl_ctx_cryptosvc_error;
        }

        /* Update other mtunnel related flags */
        mtunnel->flag.waf_req_error = 1;
        mtunnel->e_inspection_type = zpn_waf_inspt_none;
        mtunnel->flag.waf_req_compl = 1;

        if (NULL != mtunnel->inspection_ctx) {
            if (mtunnel->inspection_ctx->ssl) {
                SSL_free(mtunnel->inspection_ctx->ssl);
            }
            if (mtunnel->inspection_ctx->cln_ssl) {
                SSL_free(mtunnel->inspection_ctx->cln_ssl);
            }
            ASST_FREE(mtunnel->inspection_ctx);
        }
        mtunnel->inspection_ctx = NULL;
        ASSISTANT_LOG(AL_WARNING, "%s : assistant_mtunnel WAF inspection resources failure", mtunnel_id);
        if (zpn_is_waf_access_mode_strict(mtunnel->g_ast, mtunnel->g_ast_grp)) {
            /* if access is strict - block / fail mtunnel due to resources unavailable */
            *res = ZPN_RESULT_NOT_READY;
            res_dscr = "Inspection application/resource fetch error";
        }
    } else if (inspt_ctx->flag.inspection_mode_disabled) { /* inspection application set to bypass */
        mtunnel->flag.waf_disabled = 1;
        mtunnel->flag.waf_req_compl = 1;
        mtunnel->e_inspection_type = zpn_waf_inspt_none;
        AST_DBG_MTUN_WAF("assitant_mtunnel: %s inspection application is set to bypass", mtunnel_id);
        if (NULL != mtunnel->inspection_ctx) {
            if (mtunnel->inspection_ctx->ssl) {
                SSL_free(mtunnel->inspection_ctx->ssl);
            }
            if (mtunnel->inspection_ctx->cln_ssl) {
                SSL_free(mtunnel->inspection_ctx->cln_ssl);
            }
            ASST_FREE(mtunnel->inspection_ctx);
        }
        mtunnel->inspection_ctx = NULL;
        res_dscr = "Inspection application disabled";
    } else {
        /* if reached here mtunnel inspection ctx is during processing */
        if (inspt_ctx->flag.inspection_resources_fetch_complete) {
            AST_DBG_MTUN_WAF("assitant-mtunnel: %s WAF Inspection for %s:%d is operational (app-seg %ld)", mtunnel_id,
                             mtunnel->domain, mtunnel->client_port_he, (long)mtunnel->g_app);
            mtunnel->flag.waf_session_ready =
                    1; /* this allows building the inspection pipeline and receive traffic into it */
            mtunnel->flag.waf_req_compl = 1;
            mtunnel->flag.waf_secure = inspt_ctx->flag.inspection_application_secure;
            res_dscr = "Inspection application ready";
        } else {
            AST_DBG_MTUN_WAF("mtunnel: %s WAF is still fetch reasource and cb pending %d", mtunnel_id,
                             mtunnel->async_count);
            res_dscr = "Insection application resource fetch pending";
        }
    }
    return res_dscr;
}

static int
ast_mtunnel_locked_handle_waf_response_internal(struct zpn_assistant_mtunnel *mtunnel,
                                                struct mtunnel_waf_sess_ctx  *inspt_ctx)
{
    int res = ZPN_RESULT_NO_ERROR;
    int result = ZPN_RESULT_NO_ERROR;
    char* mtunnel_id = mtunnel->mtunnel_id; /* for logging */
    if (NULL == inspt_ctx || /* Inspection required but error occured */
        NULL == mtunnel->inspection_ctx) {
        mtunnel->flag.waf_req_compl = 1;
        mtunnel->e_inspection_type = zpn_waf_inspt_none;
        if (NULL != mtunnel->inspection_ctx) {
            if (mtunnel->inspection_ctx->ssl) {
                SSL_free(mtunnel->inspection_ctx->ssl);
            }
            if (mtunnel->inspection_ctx->cln_ssl) {
                SSL_free(mtunnel->inspection_ctx->cln_ssl);
            }
            ASST_FREE(mtunnel->inspection_ctx);
        }
        mtunnel->inspection_ctx = NULL;
        mtunnel->flag.waf_req_error = 1;
        ASSISTANT_LOG(AL_WARNING, "%s : assistant_mtunnel WAF inspection resources failure", mtunnel_id);
        if (zpn_is_waf_access_mode_strict(mtunnel->g_ast, mtunnel->g_ast_grp)) {
            /* if access is strict - block / fail mtunnel due to resources unavailable */
            res = ZPN_RESULT_NOT_READY;
        } /* if relax mode ZPN_RESULT_NO_ERROR returned */
    } else {
        if (inspt_ctx != mtunnel->inspection_ctx) {
            if (mtunnel->inspection_ctx->ssl) {
                SSL_free(mtunnel->inspection_ctx->ssl);
                mtunnel->inspection_ctx->ssl = NULL;
            }
            if (mtunnel->inspection_ctx->cln_ssl) {
                SSL_free(mtunnel->inspection_ctx->cln_ssl);
                mtunnel->inspection_ctx->cln_ssl = NULL;
            }
            const char *mdbg_str = mtunnel->inspection_ctx->mdbg_str;
            memcpy(mtunnel->inspection_ctx, inspt_ctx, sizeof(struct mtunnel_waf_sess_ctx));
            mtunnel->inspection_ctx->mdbg_str = mdbg_str;
        }
        AST_DBG_MTUN_WAF("AST_MTUNNEL %s for %s:%d INS-APP-SEG %ld WAF_CB resource status [prfl, ssl]:=[%p, %p]",
                         mtunnel_id, mtunnel->domain, mtunnel->client_port_he, (long)mtunnel->g_app,
                         mtunnel->inspection_ctx->zpn_waf_inspct_prfl, mtunnel->inspection_ctx->ssl);
        char *rsp_dscrp = ast_mtunnel_locked_process_inspt_rsp(mtunnel, inspt_ctx, &result); /* set WAF flags/state */
        AST_DBG_MTUN_WAF("AST_MTUNNEL %s for %s:%d (app-seg %ld) got %s for processing WAF-SESS-MNG response.",
                         mtunnel_id, mtunnel->domain, mtunnel->client_port_he, (long)mtunnel->g_app, rsp_dscrp);
        if (ZPN_RESULT_NOT_READY == result) {
            return result;
        }
        if (!mtunnel->flag.waf_req_compl) {
            /* WAF inspection still pending resources fetch */
            ASSISTANT_LOG(AL_INFO, "AST_mtunnel: %s got %s - mtunnel establishment is pending resource fetch",
                          mtunnel_id, rsp_dscrp);
            mtunnel->async_count++;
            res = ZPN_RESULT_ASYNCHRONOUS;
        }
    }
    return res;
}

static void zpn_assistant_mtunnel_req_waf_cb(void *response_callback_cookie,
                                            struct mtunnel_waf_sess_ctx* inspt_ctx)
{
    struct zpn_assistant_mtunnel* mtunnel = NULL;
    const char* mtunnel_id = (const char *)response_callback_cookie;
    if (!mtunnel_id) {
        /* err = AST_MT_SETUP_ERR_NO_MTUNNEL_ID; */
        ASSISTANT_LOG(AL_CRITICAL, "Request for mtunnel lookup without mtunnel_id");
        return;
    }
    AST_DBG_MTUN_WAF("Tunnel-Id %s: invoked for WAF callback!", mtunnel_id);

    size_t mtunnel_id_len = strlen(mtunnel_id);
    uint64_t mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock_bucket(mtunnel_id, mtunnel_id_len, mtunnel_id_hash, 1);
    if (!mtunnel) {
        ASSISTANT_LOG(AL_ERROR, "%s: Tunnel not found on WAF inspection callback!?!?", mtunnel_id);
        return;
    }
    za_mtunnel_lock(mtunnel);
    if (mtunnel->state > za_reaping) {
        ASSISTANT_ASSERT_SOFT(0,
        "%s: Tunnel was freed while they were pending WAF inspection resource fetch!?!?",
                              mtunnel_id);
        goto unlock_and_return;
    }
    MTUNNEL_DEC_ASYNC_COUNT(mtunnel);

    int res = ast_mtunnel_locked_handle_waf_response_internal(mtunnel, inspt_ctx);
    if (res) {
        if (ZPN_RESULT_ASYNCHRONOUS == res) {
            goto unlock_and_return;
        }
        /* error case - terminate tunnel */
        mtunnel->flag.broker_end_done = 1;
        zpn_assistant_broker_request_ack(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
        za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
    }
    ASSISTANT_ASSERT_SOFT(mtunnel->flag.waf_req_compl, "WAF Req-incomplete but mtunnel proceeds!!!");
    mtunnel_locked_set_waf_inspection_mode(mtunnel);

    za_mtunnel_locked_state_machine(mtunnel);

unlock_and_return:
    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    return;
}

/*
 * Call when broker asked us to create a mtunnel and by now we know both ends of the mtunnel(server & data broker)
 */
char *
zpn_assistant_mtunnel_create(const char*                            mtunnel_id,
                             const char*                            connection_dbg_str,
                             int64_t                                broker_label,
                             uint16_t                               ip_protocol,
                             int64_t                                g_app,
                             int64_t                                g_aps,
                             int64_t                                g_ast,
                             int64_t                                g_brk,
                             int64_t                                g_bfw,
                             int64_t                                g_dsp,
                             int64_t                                g_app_domain,
                             int64_t                                g_app_grp,
                             int64_t                                g_ast_grp,
                             int64_t                                g_srv_grp,
                             int64_t                                bfw_us,
                             int64_t                                dsp_us,
                             struct argo_inet                       s_ip,
                             struct argo_inet*                      c_pub_ip,
                             int32_t                                c_port,
                             const char*                            app_type,
                             uint32_t                               double_encrypt,
                             char*                                  domain,
                             char*                                  brk_name,
                             char*                                  bfw_name,
                             int32_t                                seq_num,
                             int64_t                                ast_rx_us,
                             const char*                            user_id_str,
                             int                                    allow_all_xport,
                             int                                    dsp_bypassed,
                             uint64_t                               path_decision,
                             int                                    icmp_access_type,
                             uint32_t                               zpn_probe_type,
                             int64_t                                gid_ins_app,
                             int64_t                                gid_ins_profile,
                             int64_t                                gid_ins_rule,
                             int                                    e_ins_type,
                             char*                                  app_domain)
{
    struct zpn_assistant_mtunnel* mtunnel;
    uint64_t                      mtunnel_id_hash;
    size_t                        mtunnel_id_len;
    char*                         err;
    int                           res = ZPN_RESULT_NO_ERROR;
    int64_t                       status = 0;

    err = NULL;
    mtunnel_id_len = strlen(mtunnel_id);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_allocate_and_bucket_lock(mtunnel_id, mtunnel_id_hash, &err, ip_protocol, zpn_probe_type, path_decision);
    if (!mtunnel) {
        if (err && strcmp(err, AST_MT_SETUP_ERR_BRK_REQ_TIMEOUT_RETRY_DUP) == 0) {
            /* User broker sent 2 zpn_broker_request and both of them landed here - ignore the later one. */
            return NULL;
        }
        ASSISTANT_LOG(AL_WARNING, "%s: Could not lookup/create, from %s", mtunnel_id, connection_dbg_str);
        if (NULL == err) { /* protecting from silent failures. Broker will not be notified */
            err = AST_MT_MEMORY_ALLOCATION_FAILURE;
        }
        return err;
    }

    za_mtunnel_lock(mtunnel);

    mtunnel->flag.ready = 0;
    mtunnel->rx_broker_label = broker_label;

    mtunnel->g_app = g_app;
    mtunnel->g_aps = g_aps;
    mtunnel->g_ast = g_ast;
    mtunnel->g_brk = g_brk;
    mtunnel->g_bfw = g_bfw;
    mtunnel->g_dsp = g_dsp;
    mtunnel->g_app_grp = g_app_grp;
    mtunnel->g_cst = ZPATH_GID_GET_CUSTOMER_GID(g_app);
    mtunnel->g_ast_grp = g_ast_grp;
    mtunnel->g_srv_grp = g_srv_grp;
    mtunnel->brk_req_bfw_us = bfw_us;
    mtunnel->brk_req_dsp_us = dsp_us;
    mtunnel->brk_req_ast_rx_us = ast_rx_us;
    mtunnel->brk_req_server_inet = s_ip;
    mtunnel->client_pub_ip = *c_pub_ip;
    mtunnel->client_port_he = c_port;
    mtunnel->ip_protocol = ip_protocol;
    mtunnel->mtunnel_type = zpn_app_type_from_str(app_type);
    mtunnel->server_inet = s_ip;
    mtunnel->zpn_probe_type = zpn_probe_type;
    mtunnel->gid_inspection_appl = gid_ins_app;
    mtunnel->gid_inspection_profile = gid_ins_profile;
    mtunnel->gid_inspection_rule = gid_ins_rule;
    mtunnel->gid_app_domain = g_app_domain;
    mtunnel->insp_status = zpn_trans_waf_status_unassigned;
    mtunnel->brkreq_seq_num = seq_num;
    mtunnel->flag.dsp_bypassed = dsp_bypassed;
    mtunnel->path_decision |= path_decision;
    mtunnel->allow_all_xport = allow_all_xport;

    snprintf(mtunnel->domain, sizeof(mtunnel->domain), "%s", domain);
    snprintf(mtunnel->app_domain, sizeof(mtunnel->app_domain), "%s", app_domain);
    snprintf(mtunnel->brk_name, sizeof(mtunnel->brk_name), "%s", brk_name);
    snprintf(mtunnel->user_id_str, sizeof(mtunnel->user_id_str), "%s", user_id_str);
    snprintf(mtunnel->bfw_name, sizeof(mtunnel->bfw_name), "%s", bfw_name);

    snprintf(mtunnel->mdbg_str, MAX_MTUNNEL_DEBUG_STR - 1, "%s:%s:%d:%s", mtunnel_id, domain, c_port,
            (ip_protocol == IPPROTO_ICMP)?"icmp":(ip_protocol == IPPROTO_TCP ? "tcp": (ip_protocol == IPPROTO_UDP) ? "udp": "none"));

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_web_probe_https && !zpn_zdx_https_webprobe_cache_config_is_intercept_enabled()) {
        ASSISTANT_LOG(AL_DEBUG, "%s : ZDX HTTPs is disabled", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_requests_rejected_feature_disabled), 1);
        zpn_assistant_broker_request_ack(mtunnel, BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED);
        mtunnel->flag.broker_end_done = 1;
        za_mtunnel_locked_mark_destroyed(mtunnel, BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED);
        za_mtunnel_locked_state_machine(mtunnel);
        goto unlock_and_return;
    }

    if (mtunnel->ip_protocol != IPPROTO_ICMP &&
        mtunnel->ip_protocol != IPPROTO_ICMPV6) {

        /* Get the WAF feature list from zpn_application table */
        /* Set the corresponfing flags in mtunnel->flag. */
        res = zpn_assistant_mtunnel_verify_waf_feature_status_of_application(mtunnel, &status);
        if (res != ZPN_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_CRITICAL, "%s : WAF feature status fetch failed with error %d.", mtunnel_id, res);
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
            mtunnel->flag.broker_end_done = 1;
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
            za_mtunnel_locked_state_machine(mtunnel);
            goto unlock_and_return;
        }

        /* verify if ad enabled application */
        if (status & zpn_adp_insp_app_segment) {
            if (ZPN_RESULT_NO_ERROR != (res = zpn_assistant_mtunnel_verify_adp_enabled_application(mtunnel)) ) {
                ASSISTANT_LOG(AL_CRITICAL, "%s : assistant_mtunnel application verify error %d", mtunnel_id, res);
                if(res == ZPN_RESULT_BAD_STATE) {
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_AST_IN_PAUSE_STATE_FOR_UPGRADE);
                    mtunnel->flag.broker_end_done = 1;
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_AST_IN_PAUSE_STATE_FOR_UPGRADE);
                } else {
                    zpn_assistant_broker_request_ack(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
                    mtunnel->flag.broker_end_done = 1;
                    za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_SETUP_ERR_APP_NOT_FOUND);
                }
                za_mtunnel_locked_state_machine(mtunnel);
                goto unlock_and_return;
            }
        }
    }

    AST_DBG_MTUN_WAF("%s: Inspection type of %d requested with profile %ld, feature mask %ld",mtunnel->mdbg_str,
                     e_ins_type, (long)gid_ins_profile, (long)status);

    /* making sure if profile is zero the inspection type is none */
    if (e_ins_type == zpn_waf_inspt_all) {
        /* Verify if debugging enabled for the domain */
        mtunnel->flag.debug_flag = zpn_assistant_mtunnel_get_debug_status(domain);
        if (0 == mtunnel->gid_inspection_profile) {
            mtunnel->e_inspection_type = zpn_waf_inspt_none;
        } else {
            mtunnel->e_inspection_type = zpn_waf_inspt_all;
        }
    } else {
        mtunnel->e_inspection_type = zpn_waf_inspt_none;
    }

    if (zpn_waf_inspt_none != mtunnel->e_inspection_type &&
        !zpn_is_waf_enabled_on_assistant(mtunnel->g_ast, mtunnel->g_ast_grp)) {
        //ASSISTANT_LOG(AL_WARNING, "Broker request WAF inpsection (profile %ld), but WAF feature disabled",
        //              (long)gid_ins_profile);
        mtunnel->e_inspection_type = zpn_waf_inspt_none;
        mtunnel->insp_status = zpn_trans_waf_feature_disabled;
    }

    mtunnel->double_encrypt = double_encrypt;
    if ((double_encrypt) && !zpn_assistant_ssl_tunnel_capable()) {
        ASSISTANT_LOG(AL_WARNING, "Client request SSL inner tunnel, but we don't have context");
        mtunnel->double_encrypt = 0;
    }

    if ((mtunnel->ip_protocol == 17) && (!mtunnel->double_encrypt)) {
        mtunnel->mconn_fohh_tlv_broker.mconn.drop_udp_framed_data = 1;
    }

    if (mtunnel->ip_protocol == IPPROTO_ICMP || mtunnel->ip_protocol == IPPROTO_ICMPV6) {
        mtunnel->mconn_fohh_tlv_broker.mconn.icmp_access_type = icmp_access_type;
        mtunnel->mconn_zrdt_tlv_broker.mconn.icmp_access_type = icmp_access_type;
    }

    /* verify if app auto protect enabled */
    if (0 == mtunnel->gid_inspection_profile ||
        (zpn_waf_inspt_none == mtunnel->e_inspection_type && zpn_trans_waf_status_unassigned == mtunnel->insp_status)) {
        if (mtunnel->gid_inspection_rule == 0) {
            mtunnel->insp_status = zpn_trans_waf_no_policy_or_insp_disabled;
        } else {
            mtunnel->insp_status = zpn_trans_waf_disabled_insp_policy_for_app;
        }
    }

    if ((0 == mtunnel->brk_req_server_inet.length) && (((mtunnel->mtunnel_type == zmt_ip) || (mtunnel->mtunnel_type == zmt_name)))) {
        mtunnel->flag.is_muted_health_app = 1;
        global_assistant.num_mtunnels_muted_health_based++;
    } else {
        mtunnel->flag.is_muted_health_app = 0;
        global_assistant.num_mtunnels_health_based++;
    }

    if (mtunnel->ip_protocol == IPPROTO_UDP) {
        __sync_fetch_and_add_8(&global_assistant.num_udp_transactions, 1);
        if (mtunnel->double_encrypt) {
            __sync_fetch_and_add_8(&global_assistant.num_DE_transactions, 1);
            __sync_fetch_and_add_8(&global_assistant.num_udp_with_DE_transactions, 1);
        }
    } else if (mtunnel->ip_protocol == IPPROTO_ICMP || mtunnel->ip_protocol == IPPROTO_ICMPV6) {
        __sync_fetch_and_add_8(&global_assistant.num_icmp_transactions, 1);
    } else {
        __sync_fetch_and_add_8(&global_assistant.num_tcp_transactions, 1);

        if (mtunnel->double_encrypt) {
            __sync_fetch_and_add_8(&global_assistant.num_DE_transactions, 1);
            __sync_fetch_and_add_8(&global_assistant.num_tcp_with_DE_transactions, 1);
        }

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_fetch_and_add_8(&global_assistant.num_mtls_transactions, 1);
        }
    }

    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels), 1);
    __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_active), 1);

    if (global_assistant.num_mtunnels_active >= global_assistant.num_mtunnels_peak_active) {
        __sync_lock_test_and_set(&(global_assistant.num_mtunnels_peak_active), global_assistant.num_mtunnels_active);
        __sync_lock_test_and_set(&(global_assistant.num_mtunnels_peak_active_us), assistant_state_get_current_time_cloud_us());
    }
    if (mtunnel->flag.is_muted_health_app) {
        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_active_muted_health_based), 1);
    } else {
        __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_active_health_based), 1);
    }
    mtunnel->flag.ready = 1;

    if (zpn_waf_inspt_all == mtunnel->e_inspection_type && 0 == gid_ins_app) {
        /* broker did not set inspection application gid */
        AST_DBG_MTUN_WAF(
                "mtunnel %s: Received Inspection request with Ins-App-gid zero - query cache to verify correct state",
                mtunnel->mtunnel_id);
        /* fetch inspection application gid from wally on connector -  work-around sync only is supported */
        if (mtunnel->flag.adp_enabled)
            res = zpn_inspection_application_get_gid(g_app, /* app_seg_id */
                                                     domain, app_domain, 0 /* adp_enabled */, &gid_ins_app, NULL, NULL, 0);
        else
            res = zpn_inspection_application_get_gid(g_app, /* app_seg_id */
                                                     domain, app_domain, mtunnel->flag.auto_app_protect_enabled?0:c_port,
                                                     &gid_ins_app, NULL, NULL, 0);
        if (res || 0 == gid_ins_app) {
            mtunnel->e_inspection_type = zpn_waf_inspt_none;
            mtunnel->gid_inspection_appl = 0;
            mtunnel->insp_status = zpn_trans_waf_disabled_insp_policy_for_app;
            __sync_fetch_and_add_8(&(global_assistant.num_mtunnels_inspect_appl_not_found), 1);
            AST_DBG_MTUN_WAF(
                    "mtunnel %s : %s:%d (App-Seg %ld) didn't find inspection GID in cache (not marked for inspection).",
                    mtunnel_id, mtunnel->domain, mtunnel->client_port_he, (long)mtunnel->g_app);
        } else {
            mtunnel->gid_inspection_appl = gid_ins_app;
            AST_DBG_MTUN_WAF("mtunne %s: %s:%d inspt.appl GID %ld found in inspection application cache",
                             mtunnel_id, domain, c_port, (long)gid_ins_app);
        }
    }

    /* PTag application only for default Mtunnel types */
    if(mtunnel->zpn_probe_type == zpn_probe_type_default && mtunnel->e_inspection_type == zpn_waf_inspt_none) {
        /* Retrieve the WAF Protocol Tagging for those applications which does not have inspection enabled */
        zpn_assistant_mtunnel_verify_ptag_mode_enabled(mtunnel);
    }

    /* Retrieve the PDP profile status for Auto Detect and Auto Inspect Mode */
    if(mtunnel->flag.ptag_mode || zpn_waf_inspt_all == mtunnel->e_inspection_type) {
        uint16_t transport = transport_none;
        if (mtunnel->ip_protocol == IPPROTO_TCP)
            transport = transport_tcp;
        else if (mtunnel->ip_protocol == IPPROTO_UDP)
            transport = transport_udp;
        mtunnel->pdp_profile = zpn_pdp_retrieve_pdp_profile_protocol(domain, c_port, transport);
        AST_DBG_MTUN_WAF("Current PDP Profile [%s:%d:%s] Lookup Protocol : %s",
                domain, c_port, zpn_pdp_get_transport_str(transport), zpn_pdp_get_protocol_str(mtunnel->pdp_profile));
    }

    if (zpn_waf_inspt_all == mtunnel->e_inspection_type) {
        /* will be executed only if inspection feature is enabled. */
        mtunnel->inspection_ctx =  (struct mtunnel_waf_sess_ctx  *)ASST_MALLOC(sizeof(struct mtunnel_waf_sess_ctx ));
        if (NULL == mtunnel->inspection_ctx) {
            mtunnel->flag.broker_end_done = 1;
            ASSISTANT_LOG(AL_CRITICAL, "%s : assistant_mtunnel inspect ctx allocation failure", mtunnel_id);
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
            za_mtunnel_locked_state_machine(mtunnel);
            goto unlock_and_return;
        }
        struct mtunnel_waf_sess_ctx  *inspt_ctx = mtunnel->inspection_ctx;
        memset(inspt_ctx, 0, sizeof(struct mtunnel_waf_sess_ctx));
        inspt_ctx->mdbg_str = mtunnel->mdbg_str;
        mtunnel->flag.waf_requested = 1;
        mtunnel->insp_status = zpn_trans_waf_resource_arrival_timeout;
        inspt_ctx->flag.inspection_secure = zpn_pdp_is_secure_protocol(mtunnel->pdp_profile)? 1 : 0;
        if (mtunnel->flag.adp_enabled)
            mtunnel_inspection_req(&inspt_ctx, gid_ins_app, gid_ins_profile, domain, 0 /* adp_enabled */, zpn_assistant_mtunnel_req_waf_cb, mtunnel->mtunnel_id, mtunnel->flag.auto_app_protect_enabled);
        else
            mtunnel_inspection_req(&inspt_ctx, gid_ins_app, gid_ins_profile, domain, c_port, zpn_assistant_mtunnel_req_waf_cb, mtunnel->mtunnel_id, mtunnel->flag.auto_app_protect_enabled);
        int res = ast_mtunnel_locked_handle_waf_response_internal(mtunnel, inspt_ctx);
        if (res) {
            if (ZPN_RESULT_ASYNCHRONOUS == res) {
                goto unlock_and_return;
            }
            /* error case - terminate tunnel */
            mtunnel->flag.broker_end_done = 1;
            zpn_assistant_broker_request_ack(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
            za_mtunnel_locked_mark_destroyed(mtunnel, AST_MT_INSPECTION_APPLICATION_FAILURE);
        }
        ASSISTANT_ASSERT_SOFT(mtunnel->flag.waf_req_compl, "WAF Req-incomplete but mtunnel proceeds!!!");
        mtunnel_locked_set_waf_inspection_mode(mtunnel);
    } else if(mtunnel->flag.ptag_mode) {
        if (!mtunnel->pdp_profile && (mtunnel->pdp_profile != PDP_PROTO_UNKNOWN) && app_domain && g_app_domain) {
            /* Create PTag if the Domain is not an IPv4 or IPv6 Address */
            struct argo_inet inet;
            if(domain && (argo_string_to_inet(domain, &inet) != ARGO_RESULT_NO_ERROR)) {
                mtunnel_locked_set_waf_inspection_mode(mtunnel);
            }
        }
    }

    za_mtunnel_locked_state_machine(mtunnel);

unlock_and_return:
    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    return NULL;
}

long
get_peak_num_mtunnels() {
    return (long)(state.stats.peak_mtunnels);
}

long
get_total_mtunnel_alloc() {
    return (long)(state.stats.total_allocations);
}

long
get_mtunnel_in_reap_queue() {
    return (long)(stats.num_mtunnel_in_reap_queue);
}

long
get_total_mtunnel_free() {
    return (long)(free_q.stats.frees);
}

/*
 * Called once every minute to log the status
 */
void
assistant_data_mtunnel_log_status()
{
    struct zpn_zdx_mtr_stats mtr_stats;
    zpn_zdx_mtr_stats_get(&mtr_stats);

    ASSISTANT_LOG(AL_NOTICE, "Mtunnels(all|health-report-based|no-health-report-based), total %ld|%ld|%ld, to "
                  "broker %ld|%ld|%ld, to private broker %ld|%ld|%ld, unbound/errored %ld, "
                  "current active %ld|%ld|%ld, peak active %ld at cloud time %ld us, "
                  "peak in mem %ld, total mtunnel alloc %ld, total mtunnel free %ld "
                  "types(tcp|udp|icmp|mtls|de|tcp_de|udp_de) %ld|%ld|%ld|%ld|%ld|%ld|%ld , reaped %ld, "
                  "waf inspection status (http|httpDE|https|httpsDE|none) %ld|%ld|%ld|%ld|%ld, "
                  "adp inspection status (ldap|ldapDE|smb|smbDE|krb|krbDE) %ld|%ld|%ld|%ld|%ld|%ld, "
                  "auto inspection status (auto|auto_de|auto_tls|auto_tls_de|ptag|ptag_de) %ld|%ld|%ld|%ld|%ld|%ld, "
                  "active inspection status (http|https|ldap|smb|krb|auto|auto_tls|ptag) %ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld, "
                  "pipeline status %ld|%ld|%ld|%ld, "
                  "websocket stats (upgrade|inspection) %ld|%ld, "
                  "api traffic stats (detection|inspection) %ld|%ld, "
                  "mtr(create|delete) tcp %ld|%ld icmp %ld|%ld udp %ld|%ld ",
                  (long)(global_assistant.num_mtunnels), (long)(global_assistant.num_mtunnels_health_based),
                  (long)(global_assistant.num_mtunnels_muted_health_based),
                  (long)(global_assistant.num_mtunnels_broker),
                  (long)(global_assistant.num_mtunnels_broker_health_based),
                  (long)(global_assistant.num_mtunnels_broker_muted_health_based),
                  (long)(global_assistant.num_mtunnels_pbroker),
                  (long)(global_assistant.num_mtunnels_pbroker_health_based),
                  (long)(global_assistant.num_mtunnels_pbroker_muted_health_based),
                  (long)(global_assistant.num_mtunnels - global_assistant.num_mtunnels_broker - global_assistant.num_mtunnels_pbroker),
                  (long)(global_assistant.num_mtunnels_active),
                  (long)(global_assistant.num_mtunnels_active_health_based),
                  (long)(global_assistant.num_mtunnels_active_muted_health_based),
                  (long)(global_assistant.num_mtunnels_peak_active),
                  (long)(global_assistant.num_mtunnels_peak_active_us),
                  (long)(state.stats.peak_mtunnels),
                  (long)(state.stats.total_allocations),
                  (long)(free_q.stats.frees),
                  (long)(global_assistant.num_tcp_transactions),
                  (long)(global_assistant.num_udp_transactions),
                  (long)(global_assistant.num_icmp_transactions),
                  (long)(global_assistant.num_mtls_transactions),
                  (long)(global_assistant.num_DE_transactions),
                  (long)(global_assistant.num_tcp_with_DE_transactions),
                  (long)(global_assistant.num_udp_with_DE_transactions),
                  (long)(stats.num_mtunnel_in_reap_queue),
                  (long)(global_assistant.num_mtunnels_inspect_http),
                  (long)(global_assistant.num_mtunnels_inspect_DE_http),
                  (long)(global_assistant.num_mtunnels_inspect_https),
                  (long)(global_assistant.num_mtunnels_inspect_DE_https),
                  (long)(global_assistant.num_mtunnels_inspect_disabled),
                  (long)(global_assistant.num_mtunnels_inspect_ldap),
                  (long)(global_assistant.num_mtunnels_inspect_DE_ldap),
                  (long)(global_assistant.num_mtunnels_inspect_smb),
                  (long)(global_assistant.num_mtunnels_inspect_DE_smb),
                  (long)(global_assistant.num_mtunnels_inspect_krb),
                  (long)(global_assistant.num_mtunnels_inspect_DE_krb),
                  (long)(global_assistant.num_mtunnels_inspect_auto),
                  (long)(global_assistant.num_mtunnels_inspect_DE_auto),
                  (long)(global_assistant.num_mtunnels_inspect_auto_tls),
                  (long)(global_assistant.num_mtunnels_inspect_DE_auto_tls),
                  (long)(global_assistant.num_mtunnels_inspect_ptag),
                  (long)(global_assistant.num_mtunnels_inspect_DE_ptag),
                  (long)(global_assistant.num_mtunnels_inspect_http_active),
                  (long)(global_assistant.num_mtunnels_inspect_https_active),
                  (long)(global_assistant.num_mtunnels_inspect_ldap_active),
                  (long)(global_assistant.num_mtunnels_inspect_smb_active),
                  (long)(global_assistant.num_mtunnels_inspect_krb_active),
                  (long)(global_assistant.num_mtunnels_inspect_auto_active),
                  (long)(global_assistant.num_mtunnels_inspect_auto_tls_active),
                  (long)(global_assistant.num_mtunnels_inspect_ptag_active),
                  (long)(global_assistant.num_mtunnels_request_pipeline_created),
                  (long)(global_assistant.num_mtunnels_response_pipeline_created),
                  (long)(global_assistant.num_mtunnels_request_pipeline_destroyed),
                  (long)(global_assistant.num_mtunnels_response_pipeline_destroyed),
                  (long)(g_waf_stats.num_websocket_upgrades),
                  (long)(g_waf_stats.num_websocket_inspections),
                  (long)(g_waf_stats.num_api_detection),
                  (long)(g_waf_stats.num_api_inspection),
                  (long)(mtr_stats.tcp_reports_created),
                  (long)(mtr_stats.tcp_reports_cleaned),
                  (long)(mtr_stats.icmp_reports_created),
                  (long)(mtr_stats.icmp_reports_cleaned),
                  (long)(mtr_stats.udp_reports_created),
                  (long)(mtr_stats.udp_reports_cleaned));
}

/*
 * Stats upload callback to fill mtunnel related info.
 */
int
assistant_data_mtunnel_stats_fill(void*     cookie,
                                  int       counter,
                                  void*     structure_data)
{
    struct zpn_assistant_data_mtunnel_stats*    out_data;

    (void)cookie;

    out_data = (struct zpn_assistant_data_mtunnel_stats *)structure_data;

    out_data->tun_connect_double_encrypt_server_side = stats.tun_connect_double_encrypt_server_side;
    out_data->tun_connect_inspect_server_side = stats.tun_connect_inspect_server_side;
    out_data->tun_connect_double_encrypt_server_side_fail = stats.tun_connect_double_encrypt_server_side_fail;
    out_data->tun_connect_inspect_server_side_fail = stats.tun_connect_inspect_server_side_fail;
    out_data->tun_connect_double_encrypt_client_side = stats.tun_connect_double_encrypt_client_side;
    out_data->tun_connect_double_encrypt_client_side_fail = stats.tun_connect_double_encrypt_client_side_fail;
    out_data->tun_connect_inspect_client_side = stats.tun_connect_inspect_client_side;
    out_data->tun_connect_inspect_client_side_fail = stats.tun_connect_inspect_client_side_fail;
    out_data->tun_connect_double_encrypt_server_side1 = stats.tun_connect_double_encrypt_server_side1;
    out_data->tun_connect_double_encrypt_server_side_fail1 = stats.tun_connect_double_encrypt_server_side_fail1;
    out_data->server_side_bev_create = stats.server_side_bev_create;
    out_data->server_side_bev_create_fail = stats.server_side_bev_create_fail;

    out_data->terminate_bev_free = stats.terminate_bev_free;
    out_data->process_event_bev_free_on_local_owner_fail = stats.process_event_bev_free_on_local_owner_fail;
    out_data->process_event_bev_free_on_server_sock_close = stats.process_event_bev_free_on_server_sock_close;
    out_data->verify_and_connect_bev_free_on_connect_failure = stats.verify_and_connect_bev_free_on_connect_failure;
    out_data->zdx_injection_total_requested = stats.zdx_injection_total_requested;
    out_data->zdx_injection_total_bytes_sent = stats.zdx_injection_total_bytes_sent;
    out_data->zdx_injection_to_mconn_success = stats.zdx_injection_to_mconn_success;
    out_data->zdx_injection_to_mconn_fail_mtunnel_gone = stats.zdx_injection_to_mconn_fail_mtunnel_gone;
    out_data->zdx_injection_to_mconn_fail_no_memory = stats.zdx_injection_to_mconn_fail_no_memory;

    //Webprobe stats
    out_data->zdx_webprobe_http_requests = stats.zdx_webprobe_http_requests;
    out_data->zdx_webprobe_https_requests = stats.zdx_webprobe_https_requests;
    out_data->zdx_webprobe_https_requests_rejected_feature_disabled = stats.zdx_webprobe_https_requests_rejected_feature_disabled;

    //Webprobe inject to server stats
    out_data->zdx_webprobe_client_injection_failed_to_queue_request = stats.zdx_webprobe_client_injection_failed_to_queue_request;
    out_data->zdx_webprobe_http_server_injection_to_mconn_total_requested = stats.zdx_webprobe_http_server_injection_to_mconn_total_requested;
    out_data->zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument = stats.zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument;
    out_data->zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone = stats.zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone;
    out_data->zdx_webprobe_http_server_injection_to_mconn_already_connected = stats.zdx_webprobe_http_server_injection_to_mconn_already_connected;
    out_data->zdx_webprobe_http_server_injection_to_mconn_fail_no_memory = stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_memory;
    out_data->zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data = stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data;
    out_data->zdx_webprobe_http_server_injection_to_mconn_success = stats.zdx_webprobe_http_server_injection_to_mconn_success;
    out_data->zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent = stats.zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent;
    out_data->zdx_webprobe_http_server_side_bev_create = stats.zdx_webprobe_http_server_side_bev_create;
    out_data->zdx_webprobe_http_server_side_bev_create_fail = stats.zdx_webprobe_http_server_side_bev_create_fail;
    out_data->zdx_webprobe_http_server_side_bev_connect_init_fail = stats.zdx_webprobe_http_server_side_bev_connect_init_fail;
    out_data->zdx_webprobe_http_server_side_bev_socket_init_fail = stats.zdx_webprobe_http_server_side_bev_socket_init_fail;
    out_data->zdx_webprobe_http_server_side_bev_connect_event_cb_fail = stats.zdx_webprobe_http_server_side_bev_connect_event_cb_fail;
    out_data->zdx_webprobe_http_server_side_bev_connected = stats.zdx_webprobe_http_server_side_bev_connected;
    out_data->zdx_webprobe_http_server_side_bev_fail_mtunnel_gone = stats.zdx_webprobe_http_server_side_bev_fail_mtunnel_gone;
    out_data->zdx_webprobe_http_server_side_bev_free_connect_error = stats.zdx_webprobe_http_server_side_bev_free_connect_error;
    out_data->zdx_webprobe_http_server_side_bev_free_on_local_owner_fail = stats.zdx_webprobe_http_server_side_bev_free_on_local_owner_fail;
    out_data->zdx_webprobe_https_server_injection_to_mconn_total_requested = stats.zdx_webprobe_https_server_injection_to_mconn_total_requested;
    out_data->zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument = stats.zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument;
    out_data->zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone = stats.zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone;
    out_data->zdx_webprobe_https_server_injection_to_mconn_already_connected = stats.zdx_webprobe_https_server_injection_to_mconn_already_connected;
    out_data->zdx_webprobe_https_server_injection_to_mconn_fail_no_memory = stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_memory;
    out_data->zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data = stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data;
    out_data->zdx_webprobe_https_server_injection_to_mconn_success = stats.zdx_webprobe_https_server_injection_to_mconn_success;
    out_data->zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent = stats.zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent;
    out_data->zdx_webprobe_https_server_side_bev_create = stats.zdx_webprobe_https_server_side_bev_create;
    out_data->zdx_webprobe_https_server_side_bev_create_fail = stats.zdx_webprobe_https_server_side_bev_create_fail;
    out_data->zdx_webprobe_https_server_side_bev_connect_init_fail = stats.zdx_webprobe_https_server_side_bev_connect_init_fail;
    out_data->zdx_webprobe_https_server_side_bev_connect_event_cb_fail = stats.zdx_webprobe_https_server_side_bev_connect_event_cb_fail;
    out_data->zdx_webprobe_https_server_side_bev_connected = stats.zdx_webprobe_https_server_side_bev_connected;
    out_data->zdx_webprobe_https_server_side_bev_fail_mtunnel_gone = stats.zdx_webprobe_https_server_side_bev_fail_mtunnel_gone;
    out_data->zdx_webprobe_https_server_side_bev_free_connect_error = stats.zdx_webprobe_https_server_side_bev_free_connect_error;
    out_data->zdx_webprobe_https_server_side_bev_free_on_local_owner_fail = stats.zdx_webprobe_https_server_side_bev_free_on_local_owner_fail;
    out_data->zdx_webprobe_https_server_side_stats_ssl_handshake_count = stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count;
    out_data->zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error = stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error;
    out_data->zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone = stats.zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone;
    out_data->zdx_webprobe_https_server_side_ssl_messages_max_exceeded = stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded;
    out_data->zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes = stats.zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes;
    out_data->zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes = stats.zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes;
    out_data->zdx_webprobe_https_ssl_get_ex_data_null_count = stats.zdx_webprobe_https_ssl_get_ex_data_null_count;
    out_data->zdx_webprobe_https_ssl_ctx_null = stats.zdx_webprobe_https_ssl_ctx_null;
    out_data->zdx_webprobe_https_ssl_object_null_count = stats.zdx_webprobe_https_ssl_object_null_count;
    out_data->zdx_webprobe_https_server_name_set_fail_count = stats.zdx_webprobe_https_server_name_set_fail_count;

    //Webprobe inject to zcc stats

    out_data->zdx_webprobe_client_side_success = stats.zdx_webprobe_client_side_success;
    out_data->zdx_webprobe_client_side_fail = stats.zdx_webprobe_client_side_fail;
    out_data->zdx_webprobe_client_injection_total_requested = stats.zdx_webprobe_client_injection_total_requested;
    out_data->zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone = stats.zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone;
    out_data->zdx_webprobe_client_injection_failed_to_queue_response = stats.zdx_webprobe_client_injection_failed_to_queue_response;
    out_data->zdx_webprobe_client_injection_already_injected_skipping = stats.zdx_webprobe_client_injection_already_injected_skipping;
    out_data->zdx_webprobe_client_injection_to_mconn_fail_no_memory = stats.zdx_webprobe_client_injection_to_mconn_fail_no_memory;
    out_data->zdx_webprobe_client_injection_to_mconn_fail_bad_argument = stats.zdx_webprobe_client_injection_to_mconn_fail_bad_argument;
    out_data->zdx_webprobe_client_injection_to_mconn_serialize_err = stats.zdx_webprobe_client_injection_to_mconn_serialize_err;
    out_data->zdx_webprobe_client_injection_to_mconn_success = stats.zdx_webprobe_client_injection_to_mconn_success;
    out_data->zdx_webprobe_client_injection_total_bytes_sent = stats.zdx_webprobe_client_injection_total_bytes_sent;
    out_data->zdx_webprobe_client_injection_unblock_to_mconn_total_requested = stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested;
    out_data->zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error = stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error;
    out_data->zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone = stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone;
    out_data->zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error = stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error;
    out_data->zdx_webprobe_https_client_side_success = stats.zdx_webprobe_https_client_side_success;
    out_data->zdx_webprobe_https_client_side_fail = stats.zdx_webprobe_https_client_side_fail;
    out_data->zdx_webprobe_https_client_injection_total_requested = stats.zdx_webprobe_https_client_injection_total_requested;
    out_data->zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone = stats.zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone;
    out_data->zdx_webprobe_https_client_injection_failed_to_queue_response = stats.zdx_webprobe_https_client_injection_failed_to_queue_response;
    out_data->zdx_webprobe_https_client_injection_already_injected_skipping = stats.zdx_webprobe_https_client_injection_already_injected_skipping;
    out_data->zdx_webprobe_https_client_injection_to_mconn_fail_no_memory = stats.zdx_webprobe_https_client_injection_to_mconn_fail_no_memory;
    out_data->zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument = stats.zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument;
    out_data->zdx_webprobe_https_client_injection_to_mconn_serialize_err = stats.zdx_webprobe_https_client_injection_to_mconn_serialize_err;
    out_data->zdx_webprobe_https_client_injection_to_mconn_success = stats.zdx_webprobe_https_client_injection_to_mconn_success;
    out_data->zdx_webprobe_https_client_injection_total_bytes_sent = stats.zdx_webprobe_https_client_injection_total_bytes_sent;

    //Webprobe delay mtunnel stats

    out_data->zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s = stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s = stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s = stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus = stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val = stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s = stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s;
    out_data->zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus = stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus;
    out_data->zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s = stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s;
    out_data->zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s = stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s;
    out_data->zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s = stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s;
    out_data->zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus = stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus;
    out_data->zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val = stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val;

    out_data->tcp_bev_connected = stats.tcp_bev_connected;
    out_data->tcp_bev_connect_error = stats.tcp_bev_connect_error;
    out_data->mtls_bev_connected = stats.mtls_bev_connected;
    out_data->mtls_bev_connect_error = stats.mtls_bev_connect_error;
    out_data->mtls_ssl_created = stats.mtls_ssl_created;
    out_data->mtls_ssl_free = stats.mtls_ssl_free;

    out_data->num_mtunnel_in_buckets_queue = stats.num_mtunnel_in_buckets_queue;
    out_data->num_mtunnel_in_reap_queue = stats.num_mtunnel_in_reap_queue;
    out_data->num_mtunnel_in_free_queue = stats.num_mtunnel_in_free_queue;
    out_data->num_mtunnel_in_reap_state_but_not_moving_to_reap_queue = stats.num_mtunnel_in_reap_state_but_not_moving_to_reap_queue;
    out_data->num_mtunnel_in_reaping_queue_but_not_clean = stats.num_mtunnel_in_reaping_queue_but_not_clean;

    out_data->num_of_mtunnel_rejected_due_to_no_system_capacity = stats.num_of_mtunnel_rejected_due_to_no_system_capacity;
    out_data->num_mtunnel_in_double_hop = stats.num_mtunnel_in_double_hop;
    out_data->broker_setup_timeout_not_double_hop_mtunnel = stats.broker_setup_timeout_not_double_hop_mtunnel;
    out_data->appc_restart_invalid_version = global_assistant.version_data.restart_invalid_version;
    out_data->denied_version_del_fail = global_assistant.version_data.denied_version_del_fail;
    out_data->num_double_hop_webprobe_http_connector_destroy = stats.num_double_hop_webprobe_http_connector_destroy;
    out_data->num_double_hop_webprobe_https_connector_destroy = stats.num_double_hop_webprobe_https_connector_destroy;
    out_data->num_double_hop_tun_connector_destroy = stats.num_double_hop_tun_connector_destroy;
    out_data->num_double_hop_inspect_connector_destroy = stats.num_double_hop_inspect_connector_destroy;

    return ZPATH_RESULT_NO_ERROR;

}

int
assistant_data_mtunnel_global_stats_fill(void*     cookie,
                                  int       counter,
                                  void*     structure_data)
{
    struct zpn_assistant_data_mtunnel_global_stats*    out_data;

    (void)cookie;

    out_data = (struct zpn_assistant_data_mtunnel_global_stats *)structure_data;

    out_data->cloud_time_us = assistant_state_get_current_time_cloud_us();
    out_data->total_mtunnel_created = global_assistant.num_mtunnels;
    out_data->total_mtunnel_freed = global_assistant.num_mtunnels_freed;
    out_data->current_active_mtunnel_count = global_assistant.num_mtunnels_active;
    out_data->peak_active_mtunnel_count = global_assistant.num_mtunnels_peak_active;
    out_data->peak_active_mtunnel_cloud_time_us = global_assistant.num_mtunnels_peak_active_us;
    out_data->num_udp_transactions = global_assistant.num_udp_transactions;
    out_data->num_mtls_transactions = global_assistant.num_mtls_transactions;
    out_data->num_tcp_transactions = global_assistant.num_tcp_transactions;
    out_data->num_udp_with_DE_transactions = global_assistant.num_udp_with_DE_transactions;
    out_data->num_tcp_with_DE_transactions = global_assistant.num_tcp_with_DE_transactions;
    out_data->num_icmp_transactions = global_assistant.num_icmp_transactions;
    out_data->num_mtunnel_drops_max_sessions_reached_pra = global_assistant.num_mtunnel_drops_max_sessions_reached_pra;
    out_data->num_mtunnel_drops_cpu_limit_reached_pra = global_assistant.num_mtunnel_drops_cpu_limit_reached_pra;
    out_data->num_mtunnel_drops_mem_limit_reached_pra = global_assistant.num_mtunnel_drops_mem_limit_reached_pra;

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Fill assistant mtunnel stats into zpn_assistant_comprehensive_stats, which will be consumed by GUI/LSS
 */
int
assistant_mtunnel_comprehensive_stats_fill(void* structure_data)
{
    struct zpn_assistant_comprehensive_stats* out_data;

    out_data = (struct zpn_assistant_comprehensive_stats *)structure_data;

    out_data->total_mtunnel_created = global_assistant.num_mtunnels;
    out_data->total_mtunnel_freed = global_assistant.num_mtunnels_freed;
    out_data->current_active_mtunnel_count = global_assistant.num_mtunnels_active;

    out_data->num_pdp_profile_count = (int64_t)g_pdp_stats.profiles_count;
    out_data->num_mtunnels_inspect_http = global_assistant.num_mtunnels_inspect_http;
    out_data->num_mtunnels_inspect_DE_http = global_assistant.num_mtunnels_inspect_DE_http;
    out_data->num_mtunnels_inspect_https = global_assistant.num_mtunnels_inspect_https;
    out_data->num_mtunnels_inspect_DE_https = global_assistant.num_mtunnels_inspect_DE_https;

    out_data->num_mtunnels_inspect_ldap = global_assistant.num_mtunnels_inspect_ldap;
    out_data->num_mtunnels_inspect_DE_ldap = global_assistant.num_mtunnels_inspect_DE_ldap;
    out_data->num_mtunnels_inspect_smb = global_assistant.num_mtunnels_inspect_smb;
    out_data->num_mtunnels_inspect_DE_smb = global_assistant.num_mtunnels_inspect_DE_smb;
    out_data->num_mtunnels_inspect_krb = global_assistant.num_mtunnels_inspect_krb;
    out_data->num_mtunnels_inspect_DE_krb = global_assistant.num_mtunnels_inspect_DE_krb;

    out_data->num_mtunnels_autoinspect = global_assistant.num_mtunnels_inspect_auto;
    out_data->num_mtunnels_autoinspect_tls = global_assistant.num_mtunnels_inspect_auto_tls;
    out_data->num_mtunnels_autoinspect_DE = global_assistant.num_mtunnels_inspect_DE_auto;
    out_data->num_mtunnels_autoinspect_DE_tls = global_assistant.num_mtunnels_inspect_DE_auto_tls;

    out_data->num_mtunnels_autodetect = global_assistant.num_mtunnels_inspect_ptag;
    out_data->num_mtunnels_autodetect_DE = global_assistant.num_mtunnels_inspect_DE_ptag;

    out_data->num_mtunnels_inspect_disabled = global_assistant.num_mtunnels_inspect_disabled;

    out_data->num_mtunnels_inspect_http_active = global_assistant.num_mtunnels_inspect_http_active;
    out_data->num_mtunnels_inspect_https_active = global_assistant.num_mtunnels_inspect_https_active;
    out_data->num_mtunnels_inspect_ldap_active = global_assistant.num_mtunnels_inspect_ldap_active;
    out_data->num_mtunnels_inspect_smb_active = global_assistant.num_mtunnels_inspect_smb_active;
    out_data->num_mtunnels_inspect_krb_active = global_assistant.num_mtunnels_inspect_krb_active;
    out_data->num_mtunnels_inspect_autoinspect_active = global_assistant.num_mtunnels_inspect_auto_active + global_assistant.num_mtunnels_inspect_auto_tls_active;
    out_data->num_mtunnels_inspect_autodetect_active = global_assistant.num_mtunnels_inspect_ptag_active;

    out_data->num_websocket_upgrades = g_waf_stats.num_websocket_upgrades;
    out_data->num_websocket_inspections = g_waf_stats.num_websocket_inspections;
    out_data->num_mtunnels_total_waf_api_traffic = g_waf_stats.num_api_detection;
    out_data->num_mtunnels_inspect_waf_api_traffic = g_waf_stats.num_api_inspection;

    out_data->num_mtunnels_inspect_appl_not_found = global_assistant.num_mtunnels_inspect_appl_not_found;
    out_data->num_inspect_appl_cert_key_retrieval_requests = global_assistant.num_inspect_appl_cert_key_retrieval_requests;
    out_data->num_inspect_appl_cert_key_retrieval_failure = global_assistant.num_inspect_appl_cert_key_retrieval_failure;
    out_data->num_inspect_prfl_construct_failure = global_assistant.num_inspect_prfl_construct_failure;
    out_data->num_mtunnel_inspt_appl_SSL_connect_failure = global_assistant.num_mtunnel_inspt_appl_SSL_connect_failure;
    out_data->num_inspect_connectors_double_free = global_assistant.num_inspect_connectors_double_free;
    out_data->num_inspect_appl_cert_gen_requests = global_assistant.num_inspect_appl_cert_gen_requests;
    out_data->num_inspect_appl_cert_gen_failure = global_assistant.num_inspect_appl_cert_gen_failure;

    out_data->num_mtunnels_request_pipeline_created = global_assistant.num_mtunnels_request_pipeline_created;
    out_data->num_mtunnels_response_pipeline_created = global_assistant.num_mtunnels_response_pipeline_created;
    out_data->num_mtunnels_request_pipeline_destroyed = global_assistant.num_mtunnels_request_pipeline_destroyed;
    out_data->num_mtunnels_response_pipeline_destroyed = global_assistant.num_mtunnels_response_pipeline_destroyed;

    // obsolete after guacd subcomponent clean up from appc
    out_data->num_mtunnels_freed_pra = 0;
    out_data->num_mtunnels_created_pra = 0;

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Fill in some useful monitor stats
 */
int
assistant_mtunnel_monitor_stats_fill(void* structure_data)
{
    struct zpn_assistant_monitor_stats* out_data;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;

    out_data->max_time_elapsed_in_check_mtunnel = state.timer_cb_stats.max_time_elapsed_in_check_mtunnel_since_last_upload;
    out_data->max_time_elapsed_in_free_mtunnel = state.timer_cb_stats.max_time_elapsed_in_free_mtunnel_since_last_upload;

    state.timer_cb_stats.max_time_elapsed_in_check_mtunnel_since_last_upload = 0;
    state.timer_cb_stats.max_time_elapsed_in_free_mtunnel_since_last_upload = 0;

    out_data->num_mtunnel_in_buckets_queue = stats.num_mtunnel_in_buckets_queue;
    out_data->num_mtunnel_in_reap_queue = stats.num_mtunnel_in_reap_queue;
    out_data->num_mtunnel_in_free_queue = stats.num_mtunnel_in_free_queue;
    out_data->num_mtunnel_in_reap_state_but_not_moving_to_reap_queue = stats.num_mtunnel_in_reap_state_but_not_moving_to_reap_queue;
    out_data->num_mtunnel_in_reaping_queue_but_not_clean = stats.num_mtunnel_in_reaping_queue_but_not_clean;

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_assistant_mtunnel_ip_proto(struct zpn_assistant_mtunnel *mtunnel)
{
    return mtunnel->ip_protocol;
}

static int
assistant_data_mtunnel_dump_stats(struct zpath_debug_state*  request_state,
                                  const char **              query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_data_mtunnel_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* assistant data mtunnel http webprobe get stats string */
int assistant_data_mtunnel_webprobe_http_stats_get_string(char *stats_str, size_t stats_str_len)
{
    int res;
    char *s = stats_str;
    char *e = stats_str + stats_str_len;

    s += sxprintf(s, e, "    - Number of zdx webprobe http requests:                                                                    %"PRId64"\n", stats.zdx_webprobe_http_requests);
    s += sxprintf(s, e, "    - Number of zdx webprobe http requested injection to mconn:                                                %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_total_requested);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to mconn failed due to bad argument:                       %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to mconn failed due to mtunnel gone:                       %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request already connected to mconn:                                          %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_already_connected);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to mconn failed due to no memory:                          %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_memory);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to mconn failed due to no request data:                    %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to mconn success:                                          %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_success);
    s += sxprintf(s, e, "    - Total number of zdx webprobe http request bytes injected to mconn:                                       %"PRId64"\n", stats.zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev created:                                                     %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_create);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev creation failed:                                             %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_create_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev connect init failed:                                         %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_connect_init_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev socket init failed:                                          %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_socket_init_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev connect event callback failed:                               %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_connect_event_cb_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev connected:                                                   %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_connected);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev failed due to mtunnel gone:                                  %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev freed due to connect error:                                  %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_free_connect_error);
    s += sxprintf(s, e, "    - Number of zdx webprobe http server side bev freed on local owner fail:                                   %"PRId64"\n", stats.zdx_webprobe_http_server_side_bev_free_on_local_owner_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request client side sucess:                                                  %"PRId64"\n", stats.zdx_webprobe_client_side_success);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request client side failed:                                                  %"PRId64"\n", stats.zdx_webprobe_client_side_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe http requested client injection:                                                  %"PRId64"\n", stats.zdx_webprobe_client_injection_total_requested);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn failed due to mtunnel gone:                %"PRId64"\n", stats.zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn failed to queue response:                  %"PRId64"\n", stats.zdx_webprobe_client_injection_failed_to_queue_response);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn skipped already injected:                  %"PRId64"\n", stats.zdx_webprobe_client_injection_already_injected_skipping);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn failed due to no memory:                   %"PRId64"\n", stats.zdx_webprobe_client_injection_to_mconn_fail_no_memory);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn failed due to bad argument:                %"PRId64"\n", stats.zdx_webprobe_client_injection_to_mconn_fail_bad_argument);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn failed serialize error:                    %"PRId64"\n", stats.zdx_webprobe_client_injection_to_mconn_serialize_err);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request injection to client mconn success:                                   %"PRId64"\n", stats.zdx_webprobe_client_injection_to_mconn_success);
    s += sxprintf(s, e, "    - Total number of zdx webprobe http request bytes injected to client mconn:                                %"PRId64"\n", stats.zdx_webprobe_client_injection_total_bytes_sent);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request unblock to client mconn injection requested:                         %"PRId64"\n", stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request unblock to client mconn injection requested error:                   %"PRId64"\n", stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request unblock to client mconn injection failed due to mtunnel gone:        %"PRId64"\n", stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe http request unblock to client mconn injection failed to resume pause error:      %"PRId64"\n", stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error);
    res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;

    return res;
}

static int
assistant_data_mtunnel_dump_webprobe_http_stats(struct zpath_debug_state*  request_state,
                                  const char **              query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    int res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    char result_str[4096] = "";

    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    res = assistant_data_mtunnel_webprobe_http_stats_get_string(result_str, sizeof(result_str));
    if (res) {
        ZDX_WEBPROBE_CACHE_LOG(AL_ERROR, "ERROR_assistant_data_mtunnel_dump_webprobe_http stats_get_string: %s\n",
                               zpath_result_string(res));
        ZDP("assistant_data_mtunnel_dump_webprobe_http stats_get_string: %s\n", zpath_result_string(res));
        return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    } else {
        ZDP("Assistant data mtunnel Http Webprobe Statistics:\n%s\n", result_str);
    }

    return res;
}

/* assistant data mtunnel https webprobe get stats string */
int assistant_data_mtunnel_webprobe_https_stats_get_string(char *stats_str, size_t stats_str_len)
{
    int res;
    char *s = stats_str;
    char *e = stats_str + stats_str_len;

    s += sxprintf(s, e, "    - Number of zdx webprobe https requests:                                                               %"PRId64"\n", stats.zdx_webprobe_https_requests);
    s += sxprintf(s, e, "    - Number of zdx webprobe https requests rejected due to feature disabled:                              %"PRId64"\n", stats.zdx_webprobe_https_requests_rejected_feature_disabled);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request client side success:                                            %"PRId64"\n", stats.zdx_webprobe_https_client_side_success);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request client side failed:                                             %"PRId64"\n", stats.zdx_webprobe_https_client_side_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client requested:                                  %"PRId64"\n", stats.zdx_webprobe_https_client_injection_total_requested);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn failed due to mtunnel gone:           %"PRId64"\n", stats.zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn failed to queue response:             %"PRId64"\n", stats.zdx_webprobe_client_injection_failed_to_queue_response);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn skipped due to already injected:      %"PRId64"\n", stats.zdx_webprobe_https_client_injection_already_injected_skipping);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn failed due to no memory:              %"PRId64"\n", stats.zdx_webprobe_https_client_injection_to_mconn_fail_no_memory);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn failed due to bad argument:           %"PRId64"\n", stats.zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn serialize error:                      %"PRId64"\n", stats.zdx_webprobe_https_client_injection_to_mconn_serialize_err);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to client mconn success:                              %"PRId64"\n", stats.zdx_webprobe_https_client_injection_to_mconn_success);
    s += sxprintf(s, e, "    - Total number of zdx webprobe https request bytes sent to client mconn:                               %"PRId64"\n", stats.zdx_webprobe_https_client_injection_total_bytes_sent);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn:                                      %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_total_requested);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn failed due to bad argument:           %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn failed due to mtunnel gone:           %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn already connected:                    %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_already_connected);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn failed due to no memory:              %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_memory);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn failed due to no request data:        %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data);
    s += sxprintf(s, e, "    - Number of zdx webprobe https request injection to server mconn success:                              %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_success);
    s += sxprintf(s, e, "    - Total number of zdx webprobe https request bytes sent to server mconn:                               %"PRId64"\n", stats.zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev created:                                                %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_create);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev create failed:                                          %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_create_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev create init failed:                                     %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_connect_init_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev connect event callback failed:                          %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_connect_event_cb_fail);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev connected:                                              %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_connected);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev failed mtunnel gone:                                    %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev freed due to connect error:                             %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_free_connect_error);
    s += sxprintf(s, e, "    - Number of zdx webprobe https server side bev freed on local owner fail:                              %"PRId64"\n", stats.zdx_webprobe_https_server_side_bev_free_on_local_owner_fail);
    s += sxprintf(s, e, "    - zdx webprobe https server side stats ssl handshake count:                                            %"PRId64"\n", stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count);
    s += sxprintf(s, e, "    - zdx webprobe https server side stats ssl handshake count no msg callback error:                      %"PRId64"\n", stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error);
    s += sxprintf(s, e, "    - zdx webprobe https server side stats ssl get failed due to mtunnel gone:                             %"PRId64"\n", stats.zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone);
    s += sxprintf(s, e, "    - zdx webprobe https server side ssl messages max exceeded:                                            %"PRId64"\n", stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded);
    s += sxprintf(s, e, "    - zdx webprobe https server side stats ssl handshake request bytes:                                    %"PRId64"\n", stats.zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes);
    s += sxprintf(s, e, "    - zdx webprobe https server side stats ssl handshake response bytes:                                   %"PRId64"\n", stats.zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes);
    s += sxprintf(s, e, "    - Number of zdx webprobe https ssl_get_ex_data returned null:                                          %"PRId64"\n", stats.zdx_webprobe_https_ssl_get_ex_data_null_count);
    s += sxprintf(s, e, "    - zdx webprobe https ssl_ctx null:                                                                     %"PRId64"\n", stats.zdx_webprobe_https_ssl_ctx_null);
    s += sxprintf(s, e, "    - Number of zdx webprobe https ssl object null:                                                        %"PRId64"\n", stats.zdx_webprobe_https_ssl_object_null_count);
    s += sxprintf(s, e, "    - Number of zdx webprobe https ssl server name set fail:                                               %"PRId64"\n", stats.zdx_webprobe_https_server_name_set_fail_count);

    res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;

    return res;
}

static int
assistant_data_mtunnel_dump_webprobe_https_stats(struct zpath_debug_state*  request_state,
                                  const char **              query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    int res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    char result_str[4096] = "";

    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    res = assistant_data_mtunnel_webprobe_https_stats_get_string(result_str, sizeof(result_str));
    if (res) {
        ZDX_WEBPROBE_CACHE_LOG(AL_ERROR, "ERROR_assistant_data_mtunnel_dump_webprobe_https stats_get_string: %s\n",
                               zpath_result_string(res));
        ZDP("assistant_data_mtunnel_dump_webprobe_https stats_get_string: %s\n", zpath_result_string(res));
        return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    } else {
        ZDP("Assistant data mtunnel Https Webprobe Statistics:\n%s\n", result_str);
    }

    return res;
}

/* assistant data mtunnel webprobe get stats string */
int assistant_data_mtunnel_webprobe_stats_get_string(char *stats_str, size_t stats_str_len)
{
    int res;
    char *s = stats_str;
    char *e = stats_str + stats_str_len;

    s += sxprintf(s, e, "%-70s%"PRId64"\n", "Number of zdx webprobe requests injection to client failed to queue requests:  ", stats.zdx_webprobe_client_injection_failed_to_queue_request);

    s += sxprintf(s, e, "%-70s(%"PRId64" ,%"PRId64", %"PRId64", %"PRId64", %"PRId64 ")\n",
              "Total zdx webprobe delay from mtunnel request to webprobe response Bucket: (15s, 30s, 60s, 60s_plus, 60s_plus_max):  ",
              stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s,
              stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s,
              stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s,
              stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus,
              stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val);

    s += sxprintf(s, e, "%-70s(%" PRId64", %" PRId64 ")\n",
              "Total zdx webprobe delay from mtunnel request to mtunnel complete Bucket: (5s, 5s_plus):  ",
              stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s,
              stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus);

    s += sxprintf(s, e, "%-70s(%" PRId64", %" PRId64 ", %" PRId64 ", %" PRId64 ", %" PRId64 ")\n",
              "Total zdx webprobe delay from mtunnel complete to webprobe request Bucket: (15s, 30s, 60s, 60s_plus, 60s_plus_max_val):  ",
              stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s,
              stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s,
              stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s,
              stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus,
              stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val);

    res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;

    return res;
}

static int
assistant_data_mtunnel_dump_webprobe_stats(struct zpath_debug_state*  request_state,
                                  const char **              query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    int res = ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    char result_str[4096] = "";

    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    res = assistant_data_mtunnel_webprobe_stats_get_string(result_str, sizeof(result_str));
    if (res) {
        ZDX_WEBPROBE_CACHE_LOG(AL_ERROR, "ERROR_assistant_data_mtunnel_dump_webprobe stats_get_string: %s\n",
                               zpath_result_string(res));
        ZDP("assistant_data_mtunnel_dump_webprobe stats_get_string: %s\n", zpath_result_string(res));
        return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
    } else {
        ZDP("Assistant data mtunnel Webprobe Statistics:\n%s\n", result_str);
    }

    return res;
}

int assistant_data_mtunnel_reset_webprobe_http_stats(struct zpath_debug_state * request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    stats.zdx_webprobe_http_requests = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_total_requested = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_already_connected = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_memory = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_success = 0;
    stats.zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent = 0;
    stats.zdx_webprobe_http_server_side_bev_create = 0;
    stats.zdx_webprobe_http_server_side_bev_create_fail = 0;
    stats.zdx_webprobe_http_server_side_bev_connect_init_fail = 0;
    stats.zdx_webprobe_http_server_side_bev_socket_init_fail = 0;
    stats.zdx_webprobe_http_server_side_bev_connect_event_cb_fail = 0;
    stats.zdx_webprobe_http_server_side_bev_connected = 0;
    stats.zdx_webprobe_http_server_side_bev_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_http_server_side_bev_free_connect_error = 0;
    stats.zdx_webprobe_http_server_side_bev_free_on_local_owner_fail = 0;
    stats.zdx_webprobe_client_side_success = 0;
    stats.zdx_webprobe_client_side_fail = 0;
    stats.zdx_webprobe_client_injection_total_requested = 0;
    stats.zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_client_injection_failed_to_queue_response = 0;
    stats.zdx_webprobe_client_injection_already_injected_skipping = 0;
    stats.zdx_webprobe_client_injection_to_mconn_fail_no_memory = 0;
    stats.zdx_webprobe_client_injection_to_mconn_fail_bad_argument = 0;
    stats.zdx_webprobe_client_injection_to_mconn_serialize_err = 0;
    stats.zdx_webprobe_client_injection_to_mconn_success = 0;
    stats.zdx_webprobe_client_injection_total_bytes_sent = 0;
    stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested = 0;
    stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error = 0;
    stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error = 0;

    ZDP("Assistant data mtunnel http webprobe stats reset\n");
    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
}

int assistant_data_mtunnel_reset_webprobe_https_stats(struct zpath_debug_state * request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    stats.zdx_webprobe_https_requests = 0;
    stats.zdx_webprobe_https_requests_rejected_feature_disabled = 0;
    stats.zdx_webprobe_https_client_side_success = 0;
    stats.zdx_webprobe_https_client_side_fail = 0;
    stats.zdx_webprobe_https_client_injection_total_requested = 0;
    stats.zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_client_injection_failed_to_queue_response = 0;
    stats.zdx_webprobe_https_client_injection_already_injected_skipping = 0;
    stats.zdx_webprobe_https_client_injection_to_mconn_fail_no_memory = 0;
    stats.zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument = 0;
    stats.zdx_webprobe_https_client_injection_to_mconn_serialize_err = 0;
    stats.zdx_webprobe_https_client_injection_to_mconn_success = 0;
    stats.zdx_webprobe_https_client_injection_total_bytes_sent = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_total_requested = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_already_connected = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_memory = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_success = 0;
    stats.zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent = 0;
    stats.zdx_webprobe_https_server_side_bev_create = 0;
    stats.zdx_webprobe_https_server_side_bev_create_fail = 0;
    stats.zdx_webprobe_https_server_side_bev_connect_init_fail = 0;
    stats.zdx_webprobe_https_server_side_bev_connect_event_cb_fail = 0;
    stats.zdx_webprobe_https_server_side_bev_connected = 0;
    stats.zdx_webprobe_https_server_side_bev_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_https_server_side_bev_free_connect_error = 0;
    stats.zdx_webprobe_https_server_side_bev_free_on_local_owner_fail = 0;
    stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count = 0;
    stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error = 0;
    stats.zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone = 0;
    stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded = 0;
    stats.zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes = 0;
    stats.zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes = 0;
    stats.zdx_webprobe_https_ssl_get_ex_data_null_count = 0;
    stats.zdx_webprobe_https_ssl_ctx_null = 0;
    stats.zdx_webprobe_https_ssl_object_null_count = 0;
    stats.zdx_webprobe_https_server_name_set_fail_count = 0;


    ZDP("Assistant data mtunnel https webprobe stats reset\n");
    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
}

int assistant_data_mtunnel_reset_webprobe_stats(struct zpath_debug_state * request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    (void)request_state;
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    stats.zdx_webprobe_client_injection_failed_to_queue_request = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus = 0;
    stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s = 0;
    stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus = 0;
    stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val = 0;

    ZDP("Assistant data mtunnel webprobe stats reset\n");
    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
}

#define AST_MTUNNEL_ONE_DAY_IN_SECONDS (24 * 3600)
#define AST_MTUNNEL_ONE_HOUR_IN_SECONDS (60 * 60)
#define AST_MTUNNEL_ONE_MIN_IN_SECONDS 60
#define AST_MTUNNEL_MAX_DAY_STR_LEN 21
#define AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN 5
#define AST_MTUNNEL_MAX_UPTIME_BUF_LEN 33

static void
assistant_data_mtunnel_dump_mtunnel(struct zpath_debug_state*     request_state,
                                    const char*                   prefix_str,
                                    struct zpn_assistant_mtunnel* mt)
{
    int64_t current_us;
    int64_t tx_to_server_bytes;
    int64_t tx_to_broker_bytes;
    int64_t rx_to_broker_bytes;
    size_t  tx_buffer_to_server_bytes;
    size_t  rx_buffer_from_server_bytes;
    int64_t tx_server_pause_count = 0;
    int64_t tx_server_resume_count = 0;

    char    ip_str[ARGO_INET_ADDRSTRLEN];
    char    path_decision_str[ZPN_TX_PATH_DECISION_STR_LEN];
    int64_t day;
    int64_t hour;
    int64_t min;
    int64_t sec;
    char    day_str[AST_MTUNNEL_MAX_DAY_STR_LEN];
    char    hour_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    min_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    uptime_str[AST_MTUNNEL_MAX_UPTIME_BUF_LEN];
    uint64_t last_pause_time_s = 0;
    uint64_t last_pause_monotime_s = 0;
    uint64_t last_resume_time_s = 0;
    uint64_t pause_time_interval = 0;

    current_us = epoch_us();
    tx_buffer_to_server_bytes   = 0;
    rx_buffer_from_server_bytes = 0;
    tx_to_server_bytes          = 0;
    tx_to_broker_bytes          = 0;
    rx_to_broker_bytes          = 0;
    if (IPPROTO_UDP == mt->ip_protocol) {
        tx_buffer_to_server_bytes = 0;
        if (mt->mconn_udp_server.tx_queue) {
            tx_buffer_to_server_bytes = evbuffer_get_length(mt->mconn_udp_server.tx_queue);
            tx_to_server_bytes        = mt->mconn_udp_server.mconn.bytes_to_client;
            tx_to_broker_bytes        = mt->mconn_udp_server.mconn.bytes_to_peer;
            rx_to_broker_bytes        = mt->mconn_udp_server.mconn.bytes_from_peer;
        }
    } else if ((IPPROTO_ICMP == mt->ip_protocol) || (IPPROTO_ICMPV6 == mt->ip_protocol)){
            tx_to_server_bytes        = mt->mconn_icmp_server.mconn.bytes_to_client;
            tx_to_broker_bytes        = mt->mconn_icmp_server.mconn.bytes_to_peer;
            rx_to_broker_bytes        = mt->mconn_icmp_server.mconn.bytes_from_peer;
    } else {
        if (mt->mconn_bufferevent_server.bev) {
            tx_buffer_to_server_bytes   = evbuffer_get_length(bufferevent_get_output(mt->mconn_bufferevent_server.bev));
            rx_buffer_from_server_bytes = evbuffer_get_length(bufferevent_get_input(mt->mconn_bufferevent_server.bev));
            tx_to_server_bytes          = mt->mconn_bufferevent_server.mconn.bytes_to_client;
            tx_to_broker_bytes          = mt->mconn_bufferevent_server.mconn.bytes_to_peer;
            rx_to_broker_bytes          = mt->mconn_bufferevent_server.mconn.bytes_from_peer;
            tx_server_pause_count       = mt->mconn_bufferevent_server.pause_count;
            tx_server_resume_count      = mt->mconn_bufferevent_server.resume_count;
            last_pause_time_s           = mt->mconn_bufferevent_server.last_pause_time_s;
            last_resume_time_s          = mt->mconn_bufferevent_server.last_resume_time_s;
            pause_time_interval         = mt->mconn_bufferevent_server.max_pause_time_interval;
            last_pause_monotime_s       = mt->mconn_bufferevent_server.last_pause_time_monotime_s;

        }
    }

    zpn_tx_path_decision_get_str(mt->path_decision, path_decision_str, sizeof(path_decision_str));

    sec =((current_us - mt->start_us) / 1000000);
    day = sec/AST_MTUNNEL_ONE_DAY_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_DAY_IN_SECONDS;

    hour = sec/AST_MTUNNEL_ONE_HOUR_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_HOUR_IN_SECONDS;

    min = sec/AST_MTUNNEL_ONE_MIN_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_MIN_IN_SECONDS;

    snprintf(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf(uptime_str, AST_MTUNNEL_MAX_UPTIME_BUF_LEN, "%s%s%s%s",  (day==0)? "": day_str,
                                            (hour==0 && day==0)? "": hour_str,
                                            (day==0 && hour==0 && min==0)? "": min_str,
                                            (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    ZDP("%s %s uptime %s, server %s, port %d, ip_protocol %d, broker %"PRId64", dispatcher %"PRId64", app %"PRId64 ", "
        "tx to server: sent %"PRId64" buffered %zd, rx from server: buffered %zd, "
        "tx to broker: sent %"PRId64", rx from broker: received %"PRId64", double encrypt: %d, state: %s "
        "termination_started: %d, delay_fin_forward_us: %"PRId64", path_decision: %s "
        "connect_requested: %u, connect_success: %u, connect_timeout: %u, connect_eof: %u, connect_error: %u "
        "muted health: %u, dsp_bypassed: %u, broker_bind_sent: %u, broker_end_done: %u, in_broker_list: %u, ready: %u "
        "is_terminating_due_to_data_conn_lost: %u, allow_all_xport: %d, free_time_s: %"PRId64", on_free_queue_us: %"PRId64" "
        "e_inspection_type: %d, e_inspection_mode: %s, gid_inspection_profile: %"PRId64", gid_inspection_appl: %"PRId64""
        "tx_server_pause_count: %"PRId64" tx_server_resume_count: %"PRId64", last_pause_time_s: %"PRIu64", last_resume_time_s:%"PRIu64", now:%"PRIu64" last_pause_monotime_s:%"PRIu64" max_pause_intl:%"PRIu64", zdx_mtr: connect_end_terminated: %u, zdx_webprobe_cache: connect_end_terminated: %u",
        prefix_str, mt->mtunnel_id, uptime_str, argo_inet_generate(ip_str, &(mt->server_inet)),
        mt->server_port_he, mt->ip_protocol, mt->g_brk, mt->g_dsp, mt->g_app, tx_to_server_bytes,
        tx_buffer_to_server_bytes, rx_buffer_from_server_bytes, tx_to_broker_bytes, rx_to_broker_bytes, mt->double_encrypt,
        mtunnel_states[mt->state], mt->termination_started, mt->delayed_fin_forward_us, path_decision_str, mt->flag.connect_requested,
        mt->flag.connect_success, mt->flag.connect_timeout, mt->flag.connect_eof, mt->flag.connect_error, mt->flag.is_muted_health_app,
        mt->flag.dsp_bypassed, mt->flag.broker_bind_sent, mt->flag.broker_end_done, mt->flag.in_broker_list, mt->flag.ready,
        mt->flag.is_terminating_due_to_data_conn_lost, mt->allow_all_xport, mt->free_time_s, mt->on_free_queue_us,
        mt->e_inspection_type, traffic_inspection_modes[mt->e_inspection_mode], mt->gid_inspection_profile, mt->gid_inspection_appl,
        tx_server_pause_count, tx_server_resume_count, last_pause_time_s, last_resume_time_s, monotime_s(), last_pause_monotime_s, pause_time_interval, mt->flag.zdx_mtr_connect_end_terminated, mt->flag.zdx_webprobe_cache_connect_end_terminated);

    if (mt->flag.zdx_webprobe_cache_connect_end_terminated){
        ZDP(", connect_requested: %u, connect_success: %u, connect_error: %u, connect_ssl_requested: %u, connect_ssl_success: %u, connect_ssl_error: %u",
            mt->flag.zdx_webprobe_cache_connect_requested,
            mt->flag.zdx_webprobe_cache_connect_success,
            mt->flag.zdx_webprobe_cache_connect_error,
            mt->flag.zdx_webprobe_cache_connect_ssl_requested,
            mt->flag.zdx_webprobe_cache_connect_ssl_success,
            mt->flag.zdx_webprobe_cache_connect_ssl_error);
    }
    ZDP("\n");
}

// note this is temp function untill all stats will be exported into transaction logs - ET-69428
static void
assistant_data_mtunnel_dump_mtunnel_mconn(struct zpath_debug_state*     request_state,
                                          const char*                   prefix_str,
                                          struct zpn_assistant_mtunnel* mt)
{
    int64_t current_us;

    char    ip_str[ARGO_INET_ADDRSTRLEN];
    int64_t day;
    int64_t hour;
    int64_t min;
    int64_t sec;
    char    day_str[AST_MTUNNEL_MAX_DAY_STR_LEN];
    char    hour_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    min_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[AST_MTUNNEL_MAX_HOUR_MIN_SEC_STR_LEN];
    char    uptime_str[AST_MTUNNEL_MAX_UPTIME_BUF_LEN];

    current_us = epoch_us();

    if (IPPROTO_ICMP == mt->ip_protocol) {
        return;
    }

    sec =((current_us - mt->start_us) / 1000000);
    day = sec/AST_MTUNNEL_ONE_DAY_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_DAY_IN_SECONDS;

    hour = sec/AST_MTUNNEL_ONE_HOUR_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_HOUR_IN_SECONDS;

    min = sec/AST_MTUNNEL_ONE_MIN_IN_SECONDS;
    sec %= AST_MTUNNEL_ONE_MIN_IN_SECONDS;

    snprintf(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf(uptime_str, AST_MTUNNEL_MAX_UPTIME_BUF_LEN, "%s%s%s%s",  (day==0)? "": day_str,
             (hour==0 && day==0)? "": hour_str,
             (day==0 && hour==0 && min==0)? "": min_str,
             (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    if (IPPROTO_UDP == mt->ip_protocol) {
        ZDP("%s %s ip_protocol UDP uptime %s, server %s, port %d, ip_protocol %d, broker %"PRId64", app %"PRId64 ", "
                "rx_paused %"PRId32", tx_paused %"PRId32", rx_bytes %"PRId64", tx_bytes %"PRId64", last_rx_epoch_s %"PRId64", last_tx_epoch_s %"PRId64", dropped_bytes %"PRId64""
                "mconn to server: rx_diff_rx hist=%d,%d,%d,%d,%d, tx_peer_rx_data max_us %d, max_cnt %d hist=%d,%d,%d,%d,%d, tx_data_unblock max_us %d max_cnt=%d, tot_max=%d, --- "
                "mconn to broker: rx_diff_rx hist=%d,%d,%d,%d,%d, tx_peer_rx_data max_us %d, max_cnt %d hist=%d,%d,%d,%d,%d, tx_data_unblock max_us %d max_cnt=%d, tot_max=%d ",
                prefix_str, mt->mtunnel_id, uptime_str, argo_inet_generate(ip_str, &(mt->server_inet)), mt->server_port_he, mt->ip_protocol, mt->g_brk, mt->g_app,
                mt->mconn_udp_server.rx_paused, mt->mconn_udp_server.tx_paused, mt->mconn_udp_server.rx_bytes, mt->mconn_udp_server.tx_bytes, mt->mconn_udp_server.last_rx_epoch_s, mt->mconn_udp_server.last_tx_epoch_s, mt->mconn_udp_server.dropped_bytes,
                mt->mconn_udp_server.mconn.rx_diff_rx_data_hist[0], mt->mconn_udp_server.mconn.rx_diff_rx_data_hist[1], mt->mconn_udp_server.mconn.rx_diff_rx_data_hist[2], mt->mconn_udp_server.mconn.rx_diff_rx_data_hist[3], mt->mconn_udp_server.mconn.rx_diff_rx_data_hist[4],
                mt->mconn_udp_server.mconn.tx_peer_rx_data_max_us, mt->mconn_udp_server.mconn.tx_peer_rx_data_max_cnt,
                mt->mconn_udp_server.mconn.tx_peer_rx_data_hist[0], mt->mconn_udp_server.mconn.tx_peer_rx_data_hist[1], mt->mconn_udp_server.mconn.tx_peer_rx_data_hist[2], mt->mconn_udp_server.mconn.tx_peer_rx_data_hist[3], mt->mconn_udp_server.mconn.tx_peer_rx_data_hist[4],
                mt->mconn_udp_server.mconn.tx_data_unblock_max_us, mt->mconn_udp_server.mconn.tx_data_unblock_max_cnt, mt->mconn_udp_server.mconn.tx_peer_rx_data_max_unblock_cnt,
                mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[0], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[1], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[2], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[3], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[4],
                mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_us, mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_cnt,
                mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[0], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[1], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[2], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[3], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[4],
                mt->mconn_fohh_tlv_broker.mconn.tx_data_unblock_max_us, mt->mconn_fohh_tlv_broker.mconn.tx_data_unblock_max_cnt, mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_unblock_cnt);
    } else {
        ZDP("%s %s ip_protocol TCP uptime %s, server %s, port %d, broker %"PRId64", app %"PRId64 ", "
                "mconn to server: rx_diff_rx hist=%d,%d,%d,%d,%d, tx_peer_rx_data max_us %d, max_cnt %d hist=%d,%d,%d,%d,%d, tx_data_unblock max_us %d max_cnt=%d, tot_max=%d, --- "
                "mconn to broker: rx_diff_rx hist=%d,%d,%d,%d,%d, tx_peer_rx_data max_us %d, max_cnt %d hist=%d,%d,%d,%d,%d, tx_data_unblock max_us %d max_cnt=%d, tot_max=%d ",
                prefix_str, mt->mtunnel_id, uptime_str, argo_inet_generate(ip_str, &(mt->server_inet)), mt->server_port_he, mt->g_brk, mt->g_app,
                mt->mconn_bufferevent_server.mconn.rx_diff_rx_data_hist[0], mt->mconn_bufferevent_server.mconn.rx_diff_rx_data_hist[1], mt->mconn_bufferevent_server.mconn.rx_diff_rx_data_hist[2], mt->mconn_bufferevent_server.mconn.rx_diff_rx_data_hist[3], mt->mconn_bufferevent_server.mconn.rx_diff_rx_data_hist[4],
                mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_max_us, mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_max_cnt,
                mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_hist[0], mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_hist[1], mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_hist[2], mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_hist[3], mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_hist[4],
                mt->mconn_bufferevent_server.mconn.tx_data_unblock_max_us, mt->mconn_bufferevent_server.mconn.tx_data_unblock_max_cnt, mt->mconn_bufferevent_server.mconn.tx_peer_rx_data_max_unblock_cnt,
                mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[0], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[1], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[2], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[3], mt->mconn_fohh_tlv_broker.mconn.rx_diff_rx_data_hist[4],
                mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_us, mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_cnt,
                mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[0], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[1], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[2], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[3], mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_hist[4],
                mt->mconn_fohh_tlv_broker.mconn.tx_data_unblock_max_us, mt->mconn_fohh_tlv_broker.mconn.tx_data_unblock_max_cnt, mt->mconn_fohh_tlv_broker.mconn.tx_peer_rx_data_max_unblock_cnt);
    }

    ZDP("\n");
}

/* See header file for this api definition*/
int
assistant_mtunnel_inject_data_to_mconn(const char* mtunnel_id, char *JSON_body, size_t JSON_body_len)
{

    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct evbuffer *ev_buf;
    char http_ok_headers[] = "HTTP/1.1 200 OK\r\nServer: ZPN\r\nContent-Type: application/json; charset=utf-8\r\nContent-Length: ";
    char *http_response;
    size_t http_response_size;

    __sync_add_and_fetch_8(&(stats.zdx_injection_total_requested), 1);

    mtunnel_id_len = strlen(mtunnel_id);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        __sync_add_and_fetch_8(&(stats.zdx_injection_to_mconn_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_DEBUG, "%s : can not find the mtunnel to inject data", mtunnel_id);
        return  ZPN_RESULT_ERR;
    }

    ev_buf = evbuffer_new();
    if (!ev_buf) {
        __sync_add_and_fetch_8(&(stats.zdx_injection_to_mconn_fail_no_memory), 1);
        ZPN_LOG(AL_CRITICAL, "%s : assistant_mtunnel_inject: can not create evbuffer", mtunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    }

    char int_buf[10] = {0};
    char *s = int_buf;
    char *e = int_buf + sizeof(int_buf);
    sxprintf(s, e, "%d", (int)JSON_body_len);
    http_response_size = JSON_body_len + strlen(int_buf) + strlen(http_ok_headers) + 4 + 1;
    http_response = (char*)ASST_MALLOC(http_response_size);
    s = http_response;
    e = http_response + http_response_size;
    s += sxprintf(s, e, "%s", http_ok_headers);
    s += sxprintf(s, e, "%s", int_buf);
    s += sxprintf(s, e, "\r\n\r\n");
    s += sxprintf(s, e, "%s", JSON_body);

    evbuffer_add(ev_buf, http_response, http_response_size-1);

    ASSISTANT_DEBUG_MTUNNEL("inject: the total len of evbuffer is %zu", evbuffer_get_length(ev_buf));

    zpn_mconn_bufferevent_synthetic_response(&(mtunnel->mconn_bufferevent_server),  ev_buf);

    za_mtunnel_bucket_unlock(mtunnel);
    ASST_FREE(http_response);
    __sync_add_and_fetch_8(&(stats.zdx_injection_to_mconn_success), 1);
    __sync_add_and_fetch_8(&(stats.zdx_injection_total_bytes_sent), http_response_size);

    return ZPN_RESULT_NO_ERROR;

}



void assistant_mtunnel_generic_inject_data_by_mtunnel_id(struct zevent_base *base,
                                                         void *mtunnel_id_cookie,
                                                         int64_t int_cookie,
                                                         void *ev_buffer_cookie,
                                                         void *injection_cookie,
                                                         void *void_cookie,
                                                         int64_t int_cookie2) {
    //Validate and lock the mtunnel before we proceed with modifying the pipeline
    if (!mtunnel_id_cookie) {
        ZPN_LOG(AL_CRITICAL, "generic inject data called without mtunnel id");
        return;
    }

    char *mtunnel_id = mtunnel_id_cookie;

    if (!ev_buffer_cookie) {
        ZPN_LOG(AL_CRITICAL, "%s: generic inject data called without a data buf", mtunnel_id);
        ZPN_FREE(mtunnel_id);
        return;
    }

    struct evbuffer *ev_buf = ev_buffer_cookie;

    if (!injection_cookie) {
        ZPN_LOG(AL_CRITICAL, "%s: generic inject data called without injection function", mtunnel_id);
        ZPN_FREE(mtunnel_id);
        evbuffer_free(ev_buf);
        return;
    }

    size_t mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
    uint64_t mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    struct zpn_assistant_mtunnel *mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_DEBUG, "%s : can not find the mtunnel to inject data", mtunnel_id);
        ZPN_FREE(mtunnel_id);
        evbuffer_free(ev_buf);
        return;
    }

    int64_t ttfb_ts = int_cookie; //could add from all callers to pass proper value , otherwise it will be zero
    if (ttfb_ts) {

        int64_t now_s = epoch_s();
        int64_t diff_s = now_s - mtunnel->zpn_zdx_webprobe_start_ts;

        if (diff_s<15) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s), 1);
        } else if (diff_s<30 && diff_s>=15) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s), 1);
        } else if (diff_s<60 && diff_s>=30) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus), 1);
            if (diff_s > stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val) {
                stats.zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val = diff_s;
            }
        }

        diff_s = mtunnel->zpn_zdx_webprobe_mtunnel_setup_complete_ts - mtunnel->zpn_zdx_webprobe_start_ts;
        if(diff_s < 5) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus), 1);
        }

        int64_t ttfb_ts_sec = ttfb_ts / 1000000l;
        diff_s = ttfb_ts_sec - mtunnel->zpn_zdx_webprobe_mtunnel_setup_complete_ts;

        if (diff_s < 15) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s), 1);
        } else if (diff_s < 30 && diff_s >= 15) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s), 1);
        } else if (diff_s < 60 && diff_s >= 30) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus), 1);
            if (diff_s > stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val) {
                stats.zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val = diff_s;
            }
        }
    }

    ((mtunnel_injection_f*)(injection_cookie))(&(mtunnel->mconn_bufferevent_server), ev_buf);

    za_mtunnel_bucket_unlock(mtunnel);
    ZPN_FREE(mtunnel_id);
}

int
assistant_mtunnel_inject_webprobe_cache_https_response_to_mconn(const char *key, const char* mtunnel_id, void *request_cookie, void *response_cookie) {

    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_total_requested), 1);

    if (!request_cookie) {
        ZPN_LOG(AL_ERROR, "%s: could not forward a response without request cookie", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument), 1);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return ZDX_WEBPROBE_CACHE_RESULT_BAD_ARGUMENT;
    }

    if (!response_cookie) {
        ZPN_LOG(AL_ERROR, "%s: could not forward a response without response data", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument), 1);
        return ZDX_WEBPROBE_CACHE_RESULT_BAD_ARGUMENT;
    }

    struct zpn_zdx_webprobe_cache_https_pipeline_data *pl_data = request_cookie;
    struct evbuffer *ev_buf;

    if (!pl_data->attached_to_mconn){
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone),1);
        ZPN_LOG(AL_ERROR, "%s: pl_data not available, probably freed so skipping", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return ZDX_WEBPROBE_CACHE_RESULT_CANT_WRITE;
    }

    //Check with the pipeline to see if its allowing data to be synthetically forwarded
    if(zpn_zdx_webprobe_cache_http_make_cached_responder(pl_data->http_pipeline_data) != ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_already_injected_skipping), 1);
        ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s : synthetic write already performed on the pipeline so skipping", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return  ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    ev_buf = evbuffer_new();
    if (!ev_buf) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_fail_no_memory), 1);
        ZPN_LOG(AL_CRITICAL, "%s : assistant_mtunnel_inject: can not create evbuffer", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return ZDX_WEBPROBE_CACHE_RESULT_NO_MEMORY;
    }

    int res = zpn_zdx_webprobe_cache_https_request_generate_synthetic_response_from_pipeline_data_and_cached_response(pl_data,
                                                                                                                      zpn_zdx_webprobe_cache_http_response_data_hold(response_cookie),
                                                                                                                      ev_buf);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Failed to serialize response for mtunnel giving up", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        evbuffer_free(ev_buf);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_serialize_err), 1);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s: injected response with %zu bytes to client", mtunnel_id, evbuffer_get_length(ev_buf));
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_to_mconn_success), 1);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_total_bytes_sent), evbuffer_get_length(ev_buf));


    // Async call will need to materialize the mtunnel so it needs a stable reference to the id
    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel_id, strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1));
    int64_t ttfb_ts = pl_data->http_pipeline_data->tx_parser->stats.ttfb_ts;

    res = fohh_thread_call_big_zevent(pl_data->http_pipeline_data->tunnel_data.mtunnel_thread,
                                      assistant_mtunnel_generic_inject_data_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      ttfb_ts,
                                      ev_buf,
                                      zpn_mconn_bufferevent_synthetic_response);

    if (res) {
        ZPN_LOG(AL_ERROR, "%s: failed to queue synthetic response", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_client_injection_failed_to_queue_response), 1);
        ZPN_FREE(mtunnel_id_copy);
        evbuffer_free(ev_buf);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }
    zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;

}

int
assistant_mtunnel_inject_webprobe_cache_response_to_mconn(const char *key, const char* mtunnel_id, void *request_cookie, void *response_cookie)
{
    struct evbuffer *ev_buf;

    __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_total_requested), 1);

    if (!response_cookie) {
        ZPN_LOG(AL_ERROR, "%s: could not forward a response without response data", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_fail_bad_argument), 1);
        return ZDX_WEBPROBE_CACHE_RESULT_BAD_ARGUMENT;
    }

    if (!request_cookie) {
        ZPN_LOG(AL_ERROR, "%s: could not forward a response without request cookie", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_fail_bad_argument), 1);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return ZDX_WEBPROBE_CACHE_RESULT_BAD_ARGUMENT;
    }

    struct zpn_zdx_webprobe_cache_http_pipeline_data *pl_data = request_cookie;

    if (!pl_data->_internal_state.attached_to_mconn){
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone),1);
        ZPN_LOG(AL_ERROR, "%s: pl_data not available, probably freed so skipping", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
       return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    //Check with the pipeline to see if its allowing data to be synthetically forwarded
    if(zpn_zdx_webprobe_cache_http_make_cached_responder(pl_data) != ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_already_injected_skipping), 1);
        ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s : synthetic write already performed on the pipeline so skipping", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return  ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    ev_buf = evbuffer_new();
    if (!ev_buf) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_fail_no_memory), 1);
        ZPN_LOG(AL_CRITICAL, "%s : assistant_mtunnel_inject: can not create evbuffer", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        return ZDX_WEBPROBE_CACHE_RESULT_NO_MEMORY;
    }

    int res = zpn_zdx_webprobe_cache_http_request_generate_synthetic_response_from_pipeline_data_and_cached_response(pl_data,
                                                                                                                     zpn_zdx_webprobe_cache_http_response_data_hold(response_cookie),
                                                                                                                     ev_buf);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Failed to serialize response for mtunnel giving up", mtunnel_id);
        zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
        evbuffer_free(ev_buf);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_serialize_err), 1);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s: injected response with %zu bytes to client", mtunnel_id, evbuffer_get_length(ev_buf));
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_to_mconn_success), 1);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_total_bytes_sent), evbuffer_get_length(ev_buf));


    // Async call will need to materialize the mtunnel so it needs a stable reference to the id
    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel_id, strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1));
    int64_t ttfb_ts = pl_data->tx_parser->stats.ttfb_ts;

    res = fohh_thread_call_big_zevent(pl_data->tunnel_data.mtunnel_thread,
                                      assistant_mtunnel_generic_inject_data_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      ttfb_ts,
                                      ev_buf,
                                      zpn_mconn_bufferevent_synthetic_response);

    if (res) {
        ZPN_LOG(AL_ERROR, "%s: failed to queue synthetic response", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_failed_to_queue_response), 1);
        ZPN_FREE(mtunnel_id_copy);
        evbuffer_free(ev_buf);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }
    zpn_zdx_webprobe_cache_http_response_data_release(response_cookie);
    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
}

void assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn_by_mtunnel_id(__attribute__((unused)) struct zevent_base *base,
                                                                                     void *mtunnel_id_cookie,
                                                                                     __attribute__((unused)) int64_t int_cookie,
                                                                                     __attribute__((unused)) void *void_cookie1,
                                                                                     __attribute__((unused)) void *void_cookie2,
                                                                                     __attribute__((unused)) void *void_cookie3,
                                                                                     __attribute__((unused)) int64_t int_cookie2) {
    int res;
    char *mtunnel_id = mtunnel_id_cookie;

    if (!mtunnel_id) {
        ZPN_LOG(AL_CRITICAL, "inject_webprobe_cache_response_unblock_to_mconn_by_mtunnel_id called without mtunnel id");
        return;
    }

    size_t mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
    uint64_t mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    struct zpn_assistant_mtunnel *mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_ERROR, "%s : assistant_mtunnel_inject_webprobe_cache_response_unblock_by_mtunnel_id can not find the mtunnel to inject resume pause", mtunnel_id);
        ZPN_FREE(mtunnel_id);
        return;
    }

    //try to resume mconn in a case it is paused
    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s: inject_webprobe_cache_response_unblock_to_mconn_by_mtunnel_id", mtunnel_id);
    res = zpn_mconn_resume_client(&(mtunnel->mconn_bufferevent_server.mconn), 1);
    if (res && (res != ZPN_RESULT_WOULD_BLOCK)) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error), 1);
        ZPN_LOG(AL_ERROR, "assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn_by_mtunnel_id Cannot resume mconn, res = %s", zpn_result_string(res));
    }
    za_mtunnel_bucket_unlock(mtunnel);
    ZPN_FREE(mtunnel_id);
}

int
assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn(const char* mtunnel_id, int cookie_int) {
    int res;
    int mtunnel_thread = cookie_int;
    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel_id, strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1));

    __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested), 1);
    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s: inject_webprobe_cache_response_unblock_to_mconn  mtunnel_thread:%d", mtunnel_id, mtunnel_thread);

    res = fohh_thread_call_big_zevent(mtunnel_thread,
                                      assistant_mtunnel_inject_webprobe_cache_response_unblock_to_mconn_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      0,
                                      0,
                                      0);

    if (res) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error), 1);
        ZPN_LOG(AL_ERROR, "%s: inject_webprobe_cache_response_unblock_to_mconn", mtunnel_id);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    return ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR;
}

static int
assistant_mtunnel_inject_data_to_server_mconn(struct zpath_debug_state*  request_state,
                                              const char **              query_values,
                                              int                        query_value_count,
                                              void*                      cookie)
{

    char *mtunnel_id;
    char *buf;
    size_t buf_len;

    if (!query_values[0]) {
        ZDP("Missing mtunnel_id\n");
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_id = (char *)query_values[0];
    buf = "{ \"client_src_ip\": \"**********\", \"cloud\": \"zscalerfour.net\", \"deviceid\": 4363, \"doc_ver\": \"1.0\", \"egress_ip\": \"***************\", \"geo_lat\": 63.339, \"geo_long\": -35.895, \"orgid\": 46274, \"upm_ver\": \"*********\", \"userid\": 65644, \"zapp_ts\": 1605054900, \"zapp_ver\": \"********\", \"mtr\": { \"etype\": \"mtr\", \"probes\": [{ \"srefid\": 234526, \"erefid\": 161123, \"stime\": 1607720330, \"etime\": 1616390350, \"ver\": \"0.3\", \"err_count\": 2, \"mon\": { \"hop_count\": 30, \"id\": 9255, \"is_custom\": true, \"packet_count\": 11, \"protocol\": \"UDP\", \"type\": \"mtr\" }, \"app\": { \"id\": 8456, \"type\": \"mtr\" }, \"clt_ha_name\": \"12345\", \"clt_ip\": \"**********\", \"clt_name\": \"user3-laptop\", \"ecode\": 0, \"icode\": 0, \"stats\": { \"hopCount\": { \"avg\": 0, \"max\": 0, \"min\": 0, \"p50\": 0, \"p75\": 1, \"p90\": 0, \"p95\": 1, \"p99\": 0 }, \"latency\": { \"avg\": 1, \"max\": 1, \"min\": 0, \"p50\": 2, \"p75\": 2, \"p90\": -1, \"p95\": 0, \"p99\": 0 }, \"loss\": { \"avg\": 0, \"max\": 0, \"min\": 0, \"p50\": 0, \"p75\": 0, \"p90\": -1, \"p95\": -1, \"p99\": -1 }, \"packetcount\": { \"avg\": 24, \"max\": 24, \"min\": 24, \"p50\": 24, \"p75\": 24, \"p90\": 24, \"p95\": 24, \"p99\": 20 } }, \"legs\": [{ \"src\": \"client\", \"dst\": \"egress\", \"ecode\": 0, \"icode\": 0, \"sysid\": 0, \"index\": 0, \"proto\": \"ICMP\", \"src_ip\": \"***************\", \"dest_ip\": \"200.43 .5 .6\", \"latency\": 0, \"loss\": 0, \"name\": \"\", \"num_hops\": 3, \"num_unresp_hops\": 0, \"hops\": [{ \"avg\": 0, \"calc_avg\": 0, \"diff_avg\": 2, \"hop\": 1, \"src_ip\": \"***************\", \"dest_ip\": \"200.43 .5 .6\", \"loss\": 0, \"max\": 0, \"min\": 0, \"pkt_rcvd\": 24, \"pkt_sent\": 24, \"std\": 0, \"type\": 1 }] }, { \"src\": \"broker\", \"dst\": \"egress\", \"ecode\": 5, \"icode\": 0, \"sysid\": 0, \"index\": 1, \"proto\": \"UDP\", \"src_ip\": \"**************\", \"dest_ip\": \"36.43 .5 .6\", \"latency\": 6, \"loss\": 2, \"name\": \"\", \"num_hops\": 3, \"num_unresp_hops\": 2, \"hops\": [{ \"avg\": 0, \"calc_avg\": 0, \"diff_avg\": 0, \"hop\": 1, \"src_ip\": \"***************\", \"dest_ip\": \"200.43 .5 .6\", \"loss\": 0, \"max\": 0, \"min\": 0, \"pkt_rcvd\": 11, \"pkt_sent\": 11, \"std\": 0, \"type\": 1 }] }, { \"src\": \"broker\", \"dst\": \"cegress\", \"ecode\": 0, \"icode\": 0, \"sysid\": 0, \"index\": 2, \"proto\": \"UDP\", \"ip\": \"************\", \"latency\": 0, \"loss\": 0, \"name\": \"\", \"num_hops\": 3, \"num_unresp_hops\": 0, \"hops\": [{ \"avg\": 0, \"calc_avg\": 0, \"diff_avg\": 0, \"hop\": 1, \"src_ip\": \"322.33.4.2\", \"dest_ip\": \"33.463.5.6\", \"loss\": 0, \"max\": 0, \"min\": 0, \"pkt_rcvd\": 11, \"pkt_sent\": 11, \"std\": 0, \"type\": 1 }] }, { \"src\": \"connector\", \"dst\": \"cegress\", \"ecode\": 0, \"icode\": 0, \"sysid\": 0, \"index\": 3, \"proto\": \"UDP\", \"src_ip\": \"************\", \"dest_ip\": \"**********\", \"latency\": 0, \"loss\": 100, \"name\": \"\", \"num_hops\": 10, \"num_unresp_hops\": 4, \"hops\": [{ \"avg\": 3, \"calc_avg\": 3, \"diff_avg\": 24, \"hop\": 1, \"ip\": \"************\", \"loss\": 0, \"max\": 0, \"min\": 0, \"pkt_rcvd\": 11, \"pkt_sent\": 11, \"std\": 0, \"type\": 1 }] }, { \"src\": \"connector\", \"dst\": \"server\", \"ecode\": 2, \"icode\": 4, \"sysid\": 0, \"index\": 4, \"proto\": \"UDP\", \"src_ip\": \"***************\", \"dest_ip\": \"200.43 .5 .6\", \"latency\": 1, \"loss\": 1, \"name\": \"\", \"num_hops\": 3, \"num_unresp_hops\": 1, \"hops\": [{ \"avg\": 5, \"calc_avg\": 2, \"diff_avg\": 1, \"hop\": 2, \"ip\": \"************\", \"loss\": 1, \"max\": 6, \"min\": 0,\"pkt_rcvd\":20,\"pkt_sent\":20,\"std\":0,\"type\":1}]}]}]}} ";
    buf_len = strlen(buf);
    assistant_mtunnel_inject_data_to_mconn(mtunnel_id, buf, buf_len);

    return ZPN_RESULT_NO_ERROR;
}

static int assistant_mtunnel_zdx_webprobe_cache_connect_to_server_is_server_connected(struct zpn_assistant_mtunnel *mtunnel)
{
    if ((mtunnel->flag.connect_requested == 1) && (mtunnel->flag.connect_success == 1)) {
        return 1;
    }
    return 0;
}

int assistant_mtunnel_zdx_webprobe_cache_https_inject_request_to_mconn(struct zpn_assistant_mtunnel *mtunnel, void *cookie)
{
    struct zpn_zdx_webprobe_cache_https_pipeline_data *pl_data = cookie;
    struct evbuffer *ev_buf;
    int res;

    if (mtunnel->server_inet.length == IPV6_ADDR_LEN) {
        pl_data->http_pipeline_data->_internal_state.is_ipv6 = 1;
    } else {
        pl_data->http_pipeline_data->_internal_state.is_ipv6 = 0;
    }

    ev_buf = evbuffer_new();
    if (!ev_buf) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_memory), 1);
        ZPN_LOG(AL_CRITICAL, "%s : assistant_mtunnel_inject: can not create evbuffer", mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    }

    res = zpn_zdx_webprobe_cache_http_request_generate_synthetic_request_from_pipeline_data(pl_data->http_pipeline_data, ev_buf);
    if (res != ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data), 1);
        evbuffer_free(ev_buf);
        return ZPN_RESULT_ERR;
    }

    size_t ev_buf_size = evbuffer_get_length(ev_buf);

    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s webprobe_cache_https_request_to_mconn injected: %zu bytes to application", mtunnel->mtunnel_id, ev_buf_size);

     // Async call will need to materialize the mtunnel so it needs a stable reference to the id
    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel->mtunnel_id, strnlen(mtunnel->mtunnel_id, (ZPN_MTUNNEL_ID_BYTES_TEXT + 1)));

    res = fohh_thread_call_big_zevent(pl_data->http_pipeline_data->tunnel_data.mtunnel_thread,
                                      assistant_mtunnel_generic_inject_data_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      0,
                                      ev_buf,
                                      zpn_mconn_bufferevent_synthetic_request);

    if (res) {
        ZPN_LOG(AL_ERROR, "%s: failed to queue synthetic response", mtunnel->mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_failed_to_queue_request), 1);
        ZPN_FREE(mtunnel_id_copy);
        evbuffer_free(ev_buf);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_success), 1);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent), ev_buf_size);

    return ZPN_RESULT_NO_ERROR;
}

static void assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_event_process(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char *mtunnel_id = (char *)cookie;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_assistant_mtunnel *mtunnel = NULL;
    short events = int_cookie;
    struct bufferevent *bev;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int bev_errno_save = EVUTIL_SOCKET_ERROR(); /* Save bev errno */
    int res = ZPN_RESULT_NO_ERROR;

    mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%s: ERROR_webprobe_cache_https_connect_to_server_event_process no tunnel!?", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_fail_mtunnel_gone), 1);
        ASST_FREE(mtunnel_id);
        return;
    }

    za_mtunnel_lock(mtunnel);

    bev = mtunnel->bev_tmp;
    mtunnel->bev_tmp = NULL;

    if (!bev) {
        /* Should not happen, but in case the impossible happens, we don't crash and burn */
        ASSISTANT_LOG(AL_NOTICE, "ERROR_webprobe_cache_https_connect_to_server_event_process no bev?");
        res = ZPN_RESULT_ERR;
        goto exit;
    }

    /* Update generic connect flags */
    za_mtunnel_event_to_connect_flags(mtunnel, events, bev_errno_save);

    if (events & BEV_EVENT_CONNECTED) {
        evutil_socket_t s;
        struct sockaddr_storage addr;
        socklen_t addrlen = sizeof(addr);

        ASSISTANT_LOG(AL_INFO, "%s: Assistant webprobe_cache https Server event received: CONNECTED", mtunnel->mtunnel_id);

        mtunnel->flag.zdx_webprobe_cache_connect_ssl_success = 1;

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connected), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connected), 1);
        }
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_connected), 1);

        mtunnel->mconn_bufferevent_server.max_pause_time_interval = global_assistant.max_pause_time_interval;

        if (!mtunnel->server_connection_setup_end_us) {
            mtunnel->server_connection_setup_end_us = epoch_us();
        }

        /* Extract assistant address of the connection */
        uint16_t port_ne;
        s = bufferevent_getfd(bev);
        if (getsockname(s, (struct sockaddr *)&addr, &addrlen) != -1) {
            argo_sockaddr_to_inet((struct sockaddr *)&addr, &(mtunnel->assistant_inet), &port_ne);
            mtunnel->assistant_port_he = ntohs(port_ne);
        }
        ASSISTANT_LOG(AL_INFO, "%s: Assistant webprobe_cache https Server event received: CONNECTED Assistant Side: ip = %s, port = %d",
                      mtunnel->mtunnel_id,
                      argo_inet_generate(ipaddr, &(mtunnel->assistant_inet)),
                      mtunnel->assistant_port_he);
    } else {

        unsigned long oslerr = 0;
        int itr = 0;
        char buffer[128];
        oslerr = bufferevent_get_openssl_error(bev);
        ERR_error_string_n(oslerr, buffer, sizeof(buffer));
        ZPN_LOG(AL_ERROR,"%s ERROR_webprobe_cache_https_connect_to_server_event_process Connect to server SSL error (%ld): %s (hit count %d)",
                mtunnel->mtunnel_id, (long)oslerr, buffer, itr);

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connect_error), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connect_error), 1);
        }

        /* Server connect failed, set state to reaping and send connect failure error code back to broker */
        if (za_mtunnel_is_server_connect_failed(mtunnel)) {
            mtunnel->state = za_reaping;
        }
        res = ZPN_RESULT_ERR;
        goto exit;
    }

    /* Success: connection on demand to the server, triggered by webprobe_cache https request */
    /* Note za_mtunnel_connect_mconns() already setup the pipeline for webprobe_cache except connecting the local owner (bev) to server-mconn */
    /* Attach now self (bev) to client - server-mconn, finalizing connection on demand from webprobe_cache the server */
    ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process https Server side add local owner for webprobe_cache https pipeline", mtunnel->mtunnel_id);

    res = zpn_mconn_add_local_owner(&(mtunnel->mconn_bufferevent_server.mconn),
                                    0,
                                    bev,
                                    NULL,
                                    0,
                                    &mconn_bufferevent_calls);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "ERROR_webprobe_cache_connect_to_server_event_process add_local_owner: %s", zpn_result_string(res));
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_free_on_local_owner_fail), 1);
        mtunnel->state = za_reaping;
        res = ZPN_RESULT_ERR;
        goto exit;
    }

exit:
    if (res) {
        mtunnel->bev_tmp = NULL;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_free_connect_error), 1);

        if(bev) {
            zlibevent_bufferevent_free(bev);
        }

        mtunnel->flag.zdx_webprobe_cache_connect_ssl_error = 1;

        /* terminate broker connection */
        za_mtunnel_locked_state_machine(mtunnel);
    }

    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    /* after unlocking mtunnel - lets  inject request */
    if(res == ZPN_RESULT_NO_ERROR) {
        ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process inject request", mtunnel->mtunnel_id);
        assistant_mtunnel_zdx_webprobe_cache_https_inject_request_to_mconn(mtunnel, mtunnel->request_cookie);
    }

    ASST_FREE(mtunnel_id);
    return;
}

static void assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_event_cb(struct bufferevent *bev, short events, void *ptr)
{

    struct zpn_assistant_mtunnel *mtunnel = ptr;
    int thread_id;
    char* mtunnel_id_copy;

    size_t mtunnel_id_len = strnlen(mtunnel->mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT)+1;
    mtunnel_id_copy = (char *)ASST_MALLOC(mtunnel_id_len);
    snprintf(mtunnel_id_copy, mtunnel_id_len , "%s", mtunnel->mtunnel_id);

    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &thread_id);

    if (fohh_thread_call(thread_id,
                         assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_event_process,
                         mtunnel_id_copy,
                         events) != FOHH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_CRITICAL, "ERROR_webprobe_cache_connect_to_server_event_cb cannot make fohh_thread_call for za_mtunnel_event!");
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_connect_event_cb_fail), 1);
        ASST_FREE(mtunnel_id_copy);
    }
}

static void assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_ssl_ctx_info_callback(SSL *ssl, int where, int ret) {

    struct zpn_zdx_webprobe_cache_https_stats_ssl_data *ssl_stats;

    ssl_stats = SSL_get_ex_data(ssl, zpn_zdx_webprobe_cache_https_stats_ssl_data_ex_index);
    if (!ssl_stats) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_ssl_get_ex_data_null_count), 1);
        return;
    }

    (void)ret;
    if (where & SSL_CB_HANDSHAKE_START) {
        ssl_stats->ssl_handshake_start_ts = epoch_us();
        ssl_stats->ssl_handshake_done = 0;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count), 1);
        ASSISTANT_DEBUG_MTUNNEL("webprobe_cache_https_connect_to_server_ssl_ctx_info_callback SSL_CB_HANDSHAKE_START where=%x ", where);
    }
    if (where & SSL_CB_HANDSHAKE_DONE) {

        // calculate how long to complete ssl handshake
        ssl_stats->ssl_handshake_stop_ts = epoch_us();
        uint64_t hs_diff = 0;
        hs_diff = ssl_stats->ssl_handshake_stop_ts - ssl_stats->ssl_handshake_start_ts;
        hs_diff = hs_diff/ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_DATA_MILLI_CONVERSION_FACTOR;
        ssl_stats->ssl_stime += hs_diff; // cumulate if multiple handshakes
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_max;

        // note: version can be extracted reliably after handshake complete
        const char *ssl_get_version;
        int res;
        ssl_get_version = SSL_get_version(ssl);
        res = snprintf(ssl_stats->ssl_version, ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_VERSION_STR_LEN_MAX,"%s",ssl_get_version);
        if (res < 0) {
            ZPN_LOG(AL_ERROR, "Stats generate ssl_version string failed - giving up");
        }
        ssl_stats->ssl_handshake_done = 1;


        if (ssl_stats->ssl_message_pair_index_curr == 0) {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error), 1);

            // this should not happen, but seems initially (1st time) assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback()
            // is not called back by ssl, although in reality ssl handshake happened,
            // therefore lets initially fill stats with reasonable defaults, otherwise it is an error
#define ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_REQ_NBYTES_INDEX_0_DEFAULT   200
#define ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_RESP_NBYTES_INDEX_0_DEFAULT   1200
#define ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_REQ_NBYTES_INDEX_1_DEFAULT   200
#define ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_RESP_NBYTES_INDEX_1_DEFAULT   200
#define ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_NBYTES_INDEX_MAX_DEFAULT   1


            ssl_stats->ssl_req_nbytes[0] = ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_REQ_NBYTES_INDEX_0_DEFAULT;
            ssl_stats->ssl_resp_nbytes[0] = ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_RESP_NBYTES_INDEX_0_DEFAULT;
            ssl_stats->ssl_req_nbytes[1] = ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_REQ_NBYTES_INDEX_1_DEFAULT;
            ssl_stats->ssl_resp_nbytes[1] = ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_RESP_NBYTES_INDEX_1_DEFAULT;
            ssl_stats->ssl_message_pair_index_curr = ZDX_WEBPROBE_CACHE_HTTPS_CONNECT_TO_SERVER_SSL_CTX_INFO_CALLBACK_SSL_NBYTES_INDEX_MAX_DEFAULT;
            if (stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error > 1) {
                ZPN_LOG(AL_ERROR, "webprobe_cache_https_connect_to_server_ssl_ctx_info_callback SSL_CB_HANDSHAKE_DONE where=%x ssl_stime: %"PRId64" , ssl_get_version: %s, no_msg_cb: %"PRId64" ",
                        where, ssl_stats->ssl_stime, ssl_get_version, stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error);

            }
        }
        ASSISTANT_DEBUG_MTUNNEL("webprobe_cache_https_connect_to_server_ssl_ctx_info_callback SSL_CB_HANDSHAKE_DONE where=%x ssl_stime: %"PRId64" , ssl_get_version: %s, no_msg_cb: %"PRId64" ",
                                where, ssl_stats->ssl_stime, ssl_get_version, stats.zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error);
    }

    return;
}

void assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback(int write_p, __attribute__((unused)) int version, int content_type, __attribute__((unused))  const void *buf, size_t len, SSL *ssl, __attribute__((unused)) void *arg)
{
    struct zpn_zdx_webprobe_cache_https_stats_ssl_data *ssl_stats;
    uint8_t ssl_handshake_message_pair_count;
    size_t len_with_tcp;

    // Retrieve the pointer to the SSL of the connection currently treated
    // and the application specific data stored into the SSL object.
    ssl_stats = SSL_get_ex_data(ssl, zpn_zdx_webprobe_cache_https_stats_ssl_data_ex_index);
    if (!ssl_stats) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_ssl_get_ex_data_null_count), 1);
        return;
    }

    if(ssl_stats->ssl_handshake_done) {
        return;
    }

    // make sure index is within range - sonarqube
    if(ssl_stats->ssl_message_pair_index_curr >= ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_HANDSHAKE_MESSAGE_PAIR_MAX)
    {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded), 1);
        ZPN_LOG(AL_ERROR, "webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback, ERROR zdx_webprobe_https_server_side_ssl_messages_max_exceeded=%"PRId64" ",
                stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded);
        goto ERR;
    }

#define SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT 1
#define SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE 0
#define ZPN_ZDX_WEBPROBE_CACHE_STATS_SSL_ADJUST_NBYTES_ABOUT_TCP_HEADERS 68

    len_with_tcp = len + ZPN_ZDX_WEBPROBE_CACHE_STATS_SSL_ADJUST_NBYTES_ABOUT_TCP_HEADERS;

    // increment message index for new pair of messages, when protocol message has been received previously and now start to send
    if (ssl_stats->ssl_flag_write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE && write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT && ssl_stats->ssl_req_nbytes[ssl_stats->ssl_message_pair_index_curr] > 0) {

        if ((ssl_stats->ssl_message_pair_index_curr + 1) < ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_HANDSHAKE_MESSAGE_PAIR_MAX) {
            ssl_stats->ssl_message_pair_index_curr++;
        } else {
            __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded), 1);
            ZPN_LOG(AL_ERROR, "webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback, ERROR zdx_webprobe_https_server_side_ssl_messages_max_exceeded = %"PRId64" ",
                    stats.zdx_webprobe_https_server_side_ssl_messages_max_exceeded);
            goto ERR;
        }
    }

    ssl_handshake_message_pair_count = ssl_stats->ssl_message_pair_index_curr + 1;

    // states are followed based on previous and current value of write_p parameter passed to this call back from ssl
    ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_initial; // states for logging purposes only, keeping

    // protocol message has been received previously (or at init) and current start to send
    if (ssl_stats->ssl_flag_write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE && write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT && ssl_stats->ssl_req_nbytes[ssl_stats->ssl_message_pair_index_curr] == 0) {
        ssl_stats->ssl_flag_write_p = SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT;
        // for the first message sent include tcp header
        ssl_stats->ssl_req_nbytes[ssl_stats->ssl_message_pair_index_curr] += len_with_tcp;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes), len_with_tcp);
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_write_client_start;
    }

    // protocol message has been sent previously and current still sending tls record layer message
    else if (ssl_stats->ssl_flag_write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT && write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT) {
        ssl_stats->ssl_req_nbytes[ssl_stats->ssl_message_pair_index_curr] += len;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes), len);
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_write_client_continue;
    }

    // protocol message has been sent previously and current start to receive
    else if (ssl_stats->ssl_flag_write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_SENT && write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE && ssl_stats->ssl_resp_nbytes[ssl_stats->ssl_message_pair_index_curr] == 0) {
        ssl_stats->ssl_flag_write_p = SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE;
        // for the first message receive  include tcp header
        ssl_stats->ssl_resp_nbytes[ssl_stats->ssl_message_pair_index_curr] += len_with_tcp;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes), len_with_tcp);
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_read_server_start;
    }

    // protocol message has been received previously and current still receiving tls record layer message
    else if (ssl_stats->ssl_flag_write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE && write_p == SSL_CTX_MSG_CALLBACK_WRITE_P_MSG_RECEIVE) {
        ssl_stats->ssl_resp_nbytes[ssl_stats->ssl_message_pair_index_curr] += len;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes), len);
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_read_server_continue;
    } else {
        // should never happen - added for completeness
        ssl_stats->stats_ssl_data_state = zpn_zdx_webprobe_cache_https_stats_ssl_data_state_max;
        ZPN_LOG(AL_ERROR, "webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback, ERROR state=%d", ssl_stats->stats_ssl_data_state );
        goto ERR;
    }

    for(int i=0; i < ssl_handshake_message_pair_count; i+=4) {
        ASSISTANT_DEBUG_MTUNNEL("webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback, stats_p: %p, write_p:%d, len:%zd content_type:%d, pair_index: %d, state: %d, req_nbytes: [%d, %d, %d, %d], resp_nbytes: [%d, %d, %d, %d], ssl_state: %s, %s",
                                ssl_stats, write_p, len, content_type,
                                ssl_stats->ssl_message_pair_index_curr, ssl_stats->stats_ssl_data_state,
                                ssl_stats->ssl_req_nbytes[i], ssl_stats->ssl_req_nbytes[i+1], ssl_stats->ssl_req_nbytes[i+2], ssl_stats->ssl_req_nbytes[i+3],
                                ssl_stats->ssl_resp_nbytes[i], ssl_stats->ssl_resp_nbytes[i+1], ssl_stats->ssl_resp_nbytes[i+2], ssl_stats->ssl_resp_nbytes[i+3],
                                SSL_state_string(ssl), SSL_state_string_long(ssl));
    }
    return;

ERR:

    for(int i=0; i < ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_HANDSHAKE_MESSAGE_PAIR_MAX; i+=4) {
        ZPN_LOG(AL_ERROR, "webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback, ERROR stats_p: %p, write_p:%d, len:%zd content_type:%d, pair_index: %d, state: %d, req_nbytes: [%d, %d, %d, %d], resp_nbytes: [%d, %d, %d, %d], ssl_state: %s, %s",
                ssl_stats, write_p, len, content_type,
                ssl_stats->ssl_message_pair_index_curr, ssl_stats->stats_ssl_data_state,
                ssl_stats->ssl_req_nbytes[i], ssl_stats->ssl_req_nbytes[i+1], ssl_stats->ssl_req_nbytes[i+2], ssl_stats->ssl_req_nbytes[i+3],
                ssl_stats->ssl_resp_nbytes[i], ssl_stats->ssl_resp_nbytes[i+1], ssl_stats->ssl_resp_nbytes[i+2], ssl_stats->ssl_resp_nbytes[i+3],
                SSL_state_string(ssl), SSL_state_string_long(ssl));
    }
    return;
}

void assistant_mtunnel_get_ssl_data_by_mtunnel_id(struct zevent_base *base,
                                                  void *mtunnel_id_cookie,
                                                  int64_t int_cookie,
                                                  void *response_cookie,
                                                  void *void_cookie,
                                                  void *void_cookie1,
                                                  int64_t int_cookie2) {


    struct zpn_zdx_webprobe_cache_http_response_data *response_data = (struct zpn_zdx_webprobe_cache_http_response_data*) response_cookie;
    char *mtunnel_id = mtunnel_id_cookie;

    size_t mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
    uint64_t mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);

    struct zpn_assistant_mtunnel *mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        //TODO(greg) do we want for every case separate error count , or categorize ie: zdx_webprobe_https_server_side_mtunnel_gone
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_ERROR, "%s : ERROR_webprobe_cache_https_stats_ssl_data_get can not find the mtunnel", mtunnel_id);
        ZPN_FREE(mtunnel_id);
        return;
    }

    response_data->x_upm_info.ssl_stats.ssl_handshake_message_pair_count = mtunnel->webprobe_cache_stats_ssl_data.ssl_message_pair_index_curr + 1;
    for(int i=0; i < response_data->x_upm_info.ssl_stats.ssl_handshake_message_pair_count; i++) {
        response_data->x_upm_info.ssl_stats.ssl_req_nbytes[i] = mtunnel->webprobe_cache_stats_ssl_data.ssl_req_nbytes[i];
        response_data->x_upm_info.ssl_stats.ssl_resp_nbytes[i] = mtunnel->webprobe_cache_stats_ssl_data.ssl_resp_nbytes[i];
    }

    int stats_ssl_data_state = mtunnel->webprobe_cache_stats_ssl_data.stats_ssl_data_state;
    uint64_t ssl_stime = mtunnel->webprobe_cache_stats_ssl_data.ssl_stime;

    int res = snprintf(response_data->x_upm_info.ssl_stats.ssl_version, ZPN_ZDX_WEBPROBE_CACHE_HTTPS_STATS_SSL_VERSION_STR_LEN_MAX,"%s", mtunnel->webprobe_cache_stats_ssl_data.ssl_version);
    if (res < 0) {
        ZPN_LOG(AL_ERROR, "ERROR_webprobe_cache_https_stats_ssl_data_get generate ssl_version string failed");
    }
    za_mtunnel_bucket_unlock(mtunnel);

    for(int i=0; i < response_data->x_upm_info.ssl_stats.ssl_handshake_message_pair_count; i+=4) {
        ASSISTANT_LOG(AL_INFO,
                      "%s: Assistant webprobe_cache_https_stats_ssl_data_get: message_pair_count: %d, stats_ssl_data_state: %d, ssl_stime: %" PRId64
                      ", ssl_version: %s, req_nbytes: [%d, %d, %d, %d], resp_nbytes: [%d, %d, %d, %d]",
                      mtunnel_id, response_data->x_upm_info.ssl_stats.ssl_handshake_message_pair_count, stats_ssl_data_state, ssl_stime, response_data->x_upm_info.ssl_stats.ssl_version,
                      response_data->x_upm_info.ssl_stats.ssl_req_nbytes[i], response_data->x_upm_info.ssl_stats.ssl_req_nbytes[i+1],
                      response_data->x_upm_info.ssl_stats.ssl_req_nbytes[i+2], response_data->x_upm_info.ssl_stats.ssl_req_nbytes[i+3],
                      response_data->x_upm_info.ssl_stats.ssl_resp_nbytes[i], response_data->x_upm_info.ssl_stats.ssl_resp_nbytes[i+1],
                      response_data->x_upm_info.ssl_stats.ssl_resp_nbytes[i+2], response_data->x_upm_info.ssl_stats.ssl_resp_nbytes[i+3]);
    }
    ZPN_FREE(mtunnel_id);

}

int assistant_mtunnel_zdx_webprobe_cache_https_stats_ssl_data_get(const char* mtunnel_id,
                                                                  int thread_id,
                                                                  void *response_cookie)
{
    int res = ZPN_RESULT_NO_ERROR;

    if(!mtunnel_id) {
        ZPN_LOG(AL_ERROR, "ERROR_webprobe_cache_https_stats_ssl_data_get mtunnel id null");
        return ZPN_RESULT_ERR;
    }

    if (!response_cookie) {
        ZPN_LOG(AL_ERROR, "ERROR_webprobe_cache_https_stats_ssl_data_get response cookie null");
        return ZPN_RESULT_ERR;
    }

    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel_id, strnlen(mtunnel_id, (ZPN_MTUNNEL_ID_BYTES_TEXT + 1)));

    res = fohh_thread_call_big_zevent(thread_id,
                                      assistant_mtunnel_get_ssl_data_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      0,
                                      response_cookie,
                                      0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: failed to queue synthetic response", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_failed_to_queue_response), 1);
        ZPN_FREE(mtunnel_id_copy);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    return res;
}


int assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_cert_verify_callback(__attribute__((unused)) X509_STORE_CTX *ctx, __attribute__((unused)) void *arg)
{
    /* Currently zdx client does not verify certificates, therefore certs verification is not required at connector.
     * If zdx client required certs verification, assistant could store certs and send it back on demand to the zdx client
     */
    ASSISTANT_DEBUG_MTUNNEL("assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_cert_verify_callback NO certificate verification ");
    return 1;
}

int assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server(struct zpn_assistant_mtunnel *mtunnel, void *request_cookie, void *response_cookie)
{
    struct bufferevent *bev;
    struct sockaddr_storage addr;
    socklen_t addr_len;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int tmp_sock;
    int tmp_val;
    struct event_base *base;
    int fohh_thread_id;
    int res = ZPN_RESULT_ERR;
    SSL_CTX *mconn_ssl_ctx = NULL;

    pthread_mutex_lock(&(global_assistant.config_lock));

    ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_https_connect_to_server - server connection to addr %s:%d ",
                            mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                            mtunnel->server_port_he);

    /* Get a thread/event_base to use... */
    fohh_thread_id = fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_ASERVER);
    base = fohh_get_thread_event_base(fohh_thread_id);

    mconn_ssl_ctx = ssl_ctx_mtls;
    if (mconn_ssl_ctx == NULL) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_ssl_ctx_null), 1);
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server Trying to start SSL communication but do not have a SSL CTX", mtunnel->mtunnel_id);
        goto DONE;
    }

    if (mtunnel->lss_encrypted_ssl) {
        __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
        SSL_free(mtunnel->lss_encrypted_ssl);
        zpn_mem_free_stats_update(assistant_mtunnel_ssl);
        mtunnel->lss_encrypted_ssl = NULL;
    }

    mtunnel->lss_encrypted_ssl = SSL_new(mconn_ssl_ctx);
    if (!mtunnel->lss_encrypted_ssl) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_ssl_object_null_count), 1);
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server Cannot create ssl object, error: %s", mtunnel->mtunnel_id, strerror(errno));
        goto DONE;
    }

    if (!SSL_set_tlsext_host_name(mtunnel->lss_encrypted_ssl, mtunnel->domain)) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_name_set_fail_count), 1);
        ZPN_LOG(AL_ERROR,
                "%s: ERROR_webprobe_cache_https_connect_to_server Cannot set server domain in client hello, error: "
                "%s",
                mtunnel->mtunnel_id, strerror(errno));
        goto DONE;
    }

    zpn_mem_alloc_stats_update(assistant_mtunnel_ssl);
    __sync_add_and_fetch_8(&(stats.mtls_ssl_created), 1);

    SSL_set_ex_data(mtunnel->lss_encrypted_ssl, fohh_ssl_ex_index, &(mtunnel->lss_encrypted_ssl_status));
    SSL_set_ex_data(mtunnel->lss_encrypted_ssl, zpn_zdx_webprobe_cache_https_stats_ssl_data_ex_index, &(mtunnel->webprobe_cache_stats_ssl_data));

    argo_inet_to_sockaddr(&(mtunnel->server_inet), (struct sockaddr *)&addr, &addr_len, htons(mtunnel->server_port_he));

    SSL_CTX_set_cert_verify_callback(mconn_ssl_ctx, assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_cert_verify_callback, NULL);
    SSL_set_info_callback(mtunnel->lss_encrypted_ssl, (void *)assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_ssl_ctx_info_callback);
    SSL_set_msg_callback(mtunnel->lss_encrypted_ssl, assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_ssl_ctx_msg_callback);


    if (addr.ss_family == AF_INET) {
        tmp_sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    } else {
        tmp_sock = socket(AF_INET6, SOCK_STREAM, IPPROTO_TCP);
    }

    if (tmp_sock < 0) {
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server Could not allocate bufferevent socket, error: %s", mtunnel->mtunnel_id, strerror(errno));
        if (mtunnel->lss_encrypted_ssl) {
            __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
            SSL_free(mtunnel->lss_encrypted_ssl);
            zpn_mem_free_stats_update(assistant_mtunnel_ssl);
        }
        mtunnel->lss_encrypted_ssl = NULL;
        goto DONE;
    }
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
    tmp_val = 1;
    setsockopt(tmp_sock, SOL_SOCKET, SO_NOSIGPIPE, (void *)&tmp_val, sizeof(tmp_val));
#endif
    tmp_val = 1;
    if(setsockopt(tmp_sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val)) < 0) {
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server Could not setsockopt, error = %s", mtunnel->mtunnel_id, strerror(errno));
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_create_fail), 1);

        if (mtunnel->lss_encrypted_ssl) {
            __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
            SSL_free(mtunnel->lss_encrypted_ssl);
            zpn_mem_free_stats_update(assistant_mtunnel_ssl);
        }
        mtunnel->lss_encrypted_ssl = NULL;
        close(tmp_sock);
        goto DONE;
    }

    evutil_make_socket_nonblocking(tmp_sock);

    bev = bufferevent_openssl_socket_new(base, tmp_sock, mtunnel->lss_encrypted_ssl, BUFFEREVENT_SSL_CONNECTING, BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);

    if (!bev) {
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server Could not allocate bufferevent socket", mtunnel->mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_create_fail), 1);

        if (mtunnel->lss_encrypted_ssl) {
            __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
            SSL_free(mtunnel->lss_encrypted_ssl);
            zpn_mem_free_stats_update(assistant_mtunnel_ssl);
        }
        mtunnel->lss_encrypted_ssl = NULL;
        close(tmp_sock);
        goto DONE;
    } else {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_create), 1);
    }

    bufferevent_setcb(bev, NULL, NULL, assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_event_cb, mtunnel);
    bufferevent_enable(bev, EV_READ|EV_WRITE);

    mtunnel->bev_tmp = bev;
    mtunnel->request_cookie = request_cookie;

    if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {

        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server could not initiate connect to addr %s:%d : errno = %d, = %s",
                mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                mtunnel->server_port_he, errno, strerror(errno));
        mtunnel->bev_tmp = NULL;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_side_bev_connect_init_fail), 1);
        zlibevent_bufferevent_free(bev);
        if (mtunnel->lss_encrypted_ssl) {
            __sync_add_and_fetch_8(&(stats.mtls_ssl_free), 1);
            SSL_free(mtunnel->lss_encrypted_ssl);
            zpn_mem_free_stats_update(assistant_mtunnel_ssl);
        }
        mtunnel->lss_encrypted_ssl = NULL;
        close(tmp_sock);
        goto DONE;
    }

    mtunnel->flag.zdx_webprobe_cache_connect_ssl_requested = 1;
    mtunnel->lss_encrypted_sock = tmp_sock;

    ASSISTANT_DEBUG_MTUNNEL(" %s: webprobe_cache_https_connect_to_server waiting for asynchronous response - server connection to addr %s:%d ",
                            mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                            mtunnel->server_port_he);
    res = ZPN_RESULT_NO_ERROR;

DONE:
    pthread_mutex_unlock(&(global_assistant.config_lock));
    return res;
}

int assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn(const char* mtunnel_id, void *cookie)
{

    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int res = ZPN_RESULT_NO_ERROR;

    ASSISTANT_LOG(AL_INFO, "%s: Assistant webprobe_cache https inject request to server", mtunnel_id);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_total_requested), 1);

    if(!mtunnel_id) {
        ZPN_LOG(AL_ERROR, "ERROR_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn mtunnel_id null");
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument), 1);
        return ZPN_RESULT_ERR;
    }
    //TODO: Need to make this inline with http webprobe. Maybe check is not needed and we need to check only for attach_to_mconn flag.
    mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_DEBUG, "%s : ERROR_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn can not find the mtunnel to inject data", mtunnel_id);
        return  ZPN_RESULT_ERR;
    }

    if (!assistant_mtunnel_zdx_webprobe_cache_connect_to_server_is_server_connected(mtunnel)) {

        res = assistant_mtunnel_zdx_webprobe_cache_https_connect_to_server(mtunnel, cookie, NULL);
        if (res) {
            // error stats are accounted in above func
            ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn failed to connect to server", mtunnel_id);
        }
    } else {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_https_server_injection_to_mconn_already_connected), 1);
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_https_connect_to_server_and_webprobe_cache_https_inject_request_to_mconn already connected to server", mtunnel_id);
        res = ZPN_RESULT_ERR;
    }
    za_mtunnel_bucket_unlock(mtunnel);
    return res;
}

int assistant_mtunnel_zdx_webprobe_cache_http_inject_request_to_mconn(struct zpn_assistant_mtunnel *mtunnel, void *cookie) {
    struct zpn_zdx_webprobe_cache_http_pipeline_data *pl_data = cookie;
    struct evbuffer *ev_buf;
    int res;

    ev_buf = evbuffer_new();
    if (!ev_buf) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_memory), 1);
        ZPN_LOG(AL_CRITICAL, "%s : assistant_mtunnel_inject: can not create evbuffer", mtunnel->mtunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    }

    if (mtunnel->server_inet.length == IPV6_ADDR_LEN) {
        pl_data->_internal_state.is_ipv6 = 1;
    } else {
        pl_data->_internal_state.is_ipv6 = 0;
    }

    res = zpn_zdx_webprobe_cache_http_request_generate_synthetic_request_from_pipeline_data(pl_data, ev_buf);
    if (res != ZDX_WEBPROBE_CACHE_RESULT_NO_ERROR) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data), 1);
        evbuffer_free(ev_buf);
        return ZPN_RESULT_ERR;
    }

    size_t ev_buf_size = evbuffer_get_length(ev_buf);

    ZPN_DEBUG_WEBPROBE_HTTP_INSPECT("%s webprobe_cache_http_request_to_mconn injected: %zu bytes to application",
                                    mtunnel->mtunnel_id, ev_buf_size);

    char *mtunnel_id_copy = ZPN_ZDX_STRDUP(mtunnel->mtunnel_id, strnlen(mtunnel->mtunnel_id, (ZPN_MTUNNEL_ID_BYTES_TEXT + 1)));

    res = fohh_thread_call_big_zevent(pl_data->tunnel_data.mtunnel_thread,
                                      assistant_mtunnel_generic_inject_data_by_mtunnel_id,
                                      mtunnel_id_copy,
                                      0,
                                      ev_buf,
                                      zpn_mconn_bufferevent_synthetic_request);

    if (res) {
        ZPN_LOG(AL_ERROR, "%s: failed to queue synthetic response", mtunnel->mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_client_injection_failed_to_queue_request), 1);
        ZPN_FREE(mtunnel_id_copy);
        evbuffer_free(ev_buf);
        return ZDX_WEBPROBE_CACHE_RESULT_ERR;
    }

    __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_success), 1);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent), ev_buf_size);

    return ZPN_RESULT_NO_ERROR;
}

static void assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_event_process(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char *mtunnel_id = (char *)cookie;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_assistant_mtunnel *mtunnel = NULL;
    short events = int_cookie;
    struct bufferevent *bev;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int bev_errno_save = EVUTIL_SOCKET_ERROR(); /* Save bev errno */
    int res = ZPN_RESULT_NO_ERROR;

    mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        ASSISTANT_LOG(AL_WARNING, "%s: ERROR_webprobe_cache_connect_to_server_event_process no tunnel!?", mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_fail_mtunnel_gone), 1);
        ASST_FREE(mtunnel_id);
        return;
    }

    za_mtunnel_lock(mtunnel);

    bev = mtunnel->bev_tmp;
    mtunnel->bev_tmp = NULL;

    if (!bev) {
        /* Should not happen, but in case the impossible happens, we don't crash and burn */
        ASSISTANT_LOG(AL_NOTICE, "ERROR_webprobe_cache_connect_to_server_event_process no bev?");
        res = ZPN_RESULT_ERR;
        goto exit;
    }

    /* Update generic connect flags */
    za_mtunnel_event_to_connect_flags(mtunnel, events, bev_errno_save);

    if (events & BEV_EVENT_CONNECTED) {
        evutil_socket_t s;
        struct sockaddr_storage addr;
        socklen_t addrlen = sizeof(addr);

        ASSISTANT_LOG(AL_INFO, "%s: Assistant webprobe_cache http Server event received: CONNECTED", mtunnel->mtunnel_id);

        mtunnel->flag.zdx_webprobe_cache_connect_success = 1;

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connected), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connected), 1);
        }
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_connected), 1);

        mtunnel->mconn_bufferevent_server.max_pause_time_interval = global_assistant.max_pause_time_interval;

        if (!mtunnel->server_connection_setup_end_us) {
            mtunnel->server_connection_setup_end_us = epoch_us();
        }

        /* Extract assistant address of the connection */
        s = bufferevent_getfd(bev);
        if (getsockname(s, (struct sockaddr *)&addr, &addrlen) != -1) {
            uint16_t port_ne;

            argo_sockaddr_to_inet((struct sockaddr *)&addr, &(mtunnel->assistant_inet), &port_ne);
            mtunnel->assistant_port_he = ntohs(port_ne);

            ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process Assistant Side: ip = %s, port = %d",
                                    mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->assistant_inet)),
                                    mtunnel->assistant_port_he);
        }

    } else {
        ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process http Server event received: CLOSED", mtunnel->mtunnel_id);

        if (mtunnel->mtunnel_type == zmt_use_tls) {
            __sync_add_and_fetch_8(&(stats.mtls_bev_connect_error), 1);
        } else {
            __sync_add_and_fetch_8(&(stats.tcp_bev_connect_error), 1);
        }

        /* Server connect failed, set state to reaping and send connect failure error code back to broker */
        if (za_mtunnel_is_server_connect_failed(mtunnel)) {
            mtunnel->state = za_reaping;
        }
        res = ZPN_RESULT_ERR;
        goto exit;
    }

    /* Success: connection on demand to the server, triggered by webprobe_cache https request */
    /* Note za_mtunnel_connect_mconns() already setup the pipeline for webprobe_cache except connecting the local owner (bev) to server-mconn */
    /* Attach now self (bev) to client - server-mconn, finalizing connection on demand from webprobe_cache the server */
     ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process http Server side add local owner for webprobe_cache https pipeline", mtunnel->mtunnel_id);

    res = zpn_mconn_add_local_owner(&(mtunnel->mconn_bufferevent_server.mconn),
                                    0,
                                    bev,
                                    NULL,
                                    0,
                                    &mconn_bufferevent_calls);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "ERROR_webprobe_cache_connect_to_server_event_process add_local_owner: %s", zpn_result_string(res));
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_free_on_local_owner_fail), 1);
        mtunnel->state = za_reaping;
        res = ZPN_RESULT_ERR;
        goto exit;
    }

exit:
    if (res) {
        mtunnel->bev_tmp = NULL;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_free_connect_error), 1);

        if(bev) {
            zlibevent_bufferevent_free(bev);
        }

        mtunnel->flag.zdx_webprobe_cache_connect_error = 1;

        /* terminate broker connection */
        za_mtunnel_locked_state_machine(mtunnel);
    }

    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

    /* after unlocking mtunnel - lets  inject request */
    if(res == ZPN_RESULT_NO_ERROR) {
        ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server_event_process inject request", mtunnel->mtunnel_id);
        assistant_mtunnel_zdx_webprobe_cache_http_inject_request_to_mconn(mtunnel, mtunnel->request_cookie);
    }

    ASST_FREE(mtunnel_id);
    return;
}

static void assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_event_cb(struct bufferevent *bev, short events, void *ptr)
{

    struct zpn_assistant_mtunnel *mtunnel = ptr;
    int thread_id;
    char* mtunnel_id_copy;

    size_t mtunnel_id_len = strnlen(mtunnel->mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT)+1;
    mtunnel_id_copy = (char *)ASST_MALLOC(mtunnel_id_len);
    snprintf(mtunnel_id_copy, mtunnel_id_len , "%s", mtunnel->mtunnel_id);

    zpn_mconn_get_fohh_thread_id(&(mtunnel->mconn_bufferevent_server.mconn), &thread_id);

    if (fohh_thread_call(thread_id,
                         assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_event_process,
                         mtunnel_id_copy,
                         events) != FOHH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_CRITICAL, "ERROR_webprobe_cache_connect_to_server_event_cb cannot make fohh_thread_call for za_mtunnel_event!");
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_connect_event_cb_fail), 1);
        ASST_FREE(mtunnel_id_copy);
    }
}

int assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server(struct zpn_assistant_mtunnel *mtunnel, void *request_cookie, void *response_cookie)
{
    struct bufferevent *bev;
    struct sockaddr_storage addr;
    socklen_t addr_len;
    char ipaddr[ARGO_INET_ADDRSTRLEN];
    int sock;
    int tmp_val;
    struct event_base *base;
    int fohh_thread_id;
    int res = ZPN_RESULT_ERR;

    pthread_mutex_lock(&(global_assistant.config_lock));

    /* Get a thread/event_base to use... */
    fohh_thread_id = fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_ASERVER);
    base = fohh_get_thread_event_base(fohh_thread_id);

    bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
    if (!bev) {
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_connect_to_server could not allocate bufferevent socket", mtunnel->mtunnel_id);
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_create_fail), 1);
        goto DONE;
    } else {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_create), 1);
    }

    mtunnel->bev_tmp = bev;
    mtunnel->request_cookie = request_cookie;

    bufferevent_setcb(bev, NULL, NULL, assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_event_cb, mtunnel);

    argo_inet_to_sockaddr(&(mtunnel->server_inet), (struct sockaddr *)&addr, &addr_len, htons(mtunnel->server_port_he));

    if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {

        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_connect_to_server could not initiate connect to addr %s:%d : errno = %d, = %s",
                mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                mtunnel->server_port_he, errno, strerror(errno));
        mtunnel->bev_tmp = NULL;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_connect_init_fail), 1);
        zlibevent_bufferevent_free(bev);
        goto DONE;
    }
    mtunnel->flag.zdx_webprobe_cache_connect_requested = 1;

    /* Set TCP_NODELAY */
    tmp_val = 1;
    sock = bufferevent_getfd(bev);
    if(setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val)) < 0) {
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_connect_to_server could not set socket for no delay for connect to addr %s:%d : errno = %d, = %s",
                mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                mtunnel->server_port_he, errno, strerror(errno));
        mtunnel->bev_tmp = NULL;
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_side_bev_socket_init_fail), 1);
        zlibevent_bufferevent_free(bev);
        goto DONE;
    }
    ASSISTANT_DEBUG_MTUNNEL("%s: webprobe_cache_connect_to_server waiting for asynchronous response - server connection to addr %s:%d ",
                            mtunnel->mtunnel_id, argo_inet_generate(ipaddr, &(mtunnel->server_inet)),
                            mtunnel->server_port_he);
    res = ZPN_RESULT_NO_ERROR;

DONE:
    pthread_mutex_unlock(&(global_assistant.config_lock));
    return res;
}

/* Note webprobe http does not use connect on demand yet, but could use this function as entrance into connect on demand to server  */
int assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server_and_webprobe_cache_http_inject_request_to_mconn(const char* mtunnel_id, void *cookie)
{

    struct zpn_assistant_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int res = ZPN_RESULT_NO_ERROR;

    ASSISTANT_LOG(AL_INFO, "%s: Assistant webprobe_cache http inject request to server", mtunnel_id);
    __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_total_requested), 1);

    if(!mtunnel_id) {
        ZPN_LOG(AL_ERROR, "ERROR_webprobe_cache_connect_to_server mtunnel_id null");
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument), 1);
        return ZPN_RESULT_ERR;
    }
    mtunnel_id_len = strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT);
    mtunnel_id_hash = CityHash64(mtunnel_id, mtunnel_id_len);
    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    if (!mtunnel) {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone), 1);
        ZPN_LOG(AL_DEBUG, "%s : ERROR_webprobe_cache_connect_to_server can not find the mtunnel to inject data", mtunnel_id);
        return  ZPN_RESULT_ERR;
    }

    if (!assistant_mtunnel_zdx_webprobe_cache_connect_to_server_is_server_connected(mtunnel)) {

        res = assistant_mtunnel_zdx_webprobe_cache_http_connect_to_server(mtunnel, cookie, NULL);
        if (res) {
            // error stats are accounted in above func
            ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_connect_to_server failed to connect to server", mtunnel_id);
        }
    } else {
        __sync_add_and_fetch_8(&(stats.zdx_webprobe_http_server_injection_to_mconn_already_connected), 1);
        ZPN_LOG(AL_ERROR, "%s: ERROR_webprobe_cache_connect_to_server already connected to server", mtunnel_id);
        res = ZPN_RESULT_ERR;
    }
    za_mtunnel_bucket_unlock(mtunnel);
    return res;
}

static int
assistant_data_mtunnel_dump_mtunnels(struct zpath_debug_state*  request_state,
                                     const char **              query_values,
                                     int                        query_value_count,
                                     void*                      cookie)
{
    int     bucket_iter;

    for (bucket_iter = 0; bucket_iter < ZPN_ASSISTANT_BUCKETS; bucket_iter++)
    {
        struct zpn_assistant_bucket *bucket = &global_assistant.buckets[bucket_iter];
        struct zpn_assistant_mtunnel *mt;

        pthread_mutex_lock(&(bucket->lock));

        for (mt = TAILQ_FIRST(&(bucket->bucket_mtunnel_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            za_mtunnel_lock(mt);
            assistant_data_mtunnel_dump_mtunnel(request_state, "Active Mtunnel", mt);
            za_mtunnel_unlock(mt);
        }

        for (mt = TAILQ_FIRST(&(bucket->bucket_reaped_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            za_mtunnel_lock(mt);
            assistant_data_mtunnel_dump_mtunnel(request_state, "Reaped Mtunnel", mt);
            za_mtunnel_unlock(mt);
        }

        pthread_mutex_unlock(&(bucket->lock));
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_data_mtunnel_dump_mtunnels_mconn(struct zpath_debug_state*  request_state,
                                           const char **              query_values,
                                           int                        query_value_count,
                                           void*                      cookie)
{
    int     bucket_iter;

    for (bucket_iter = 0; bucket_iter < ZPN_ASSISTANT_BUCKETS; bucket_iter++)
    {
        struct zpn_assistant_bucket *bucket = &global_assistant.buckets[bucket_iter];
        struct zpn_assistant_mtunnel *mt;

        pthread_mutex_lock(&(bucket->lock));

        for (mt = TAILQ_FIRST(&(bucket->bucket_mtunnel_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            za_mtunnel_lock(mt);
            assistant_data_mtunnel_dump_mtunnel_mconn(request_state, "Active Mtunnel", mt);
            za_mtunnel_unlock(mt);
        }

        for (mt = TAILQ_FIRST(&(bucket->bucket_reaped_list)); mt != NULL; mt = TAILQ_NEXT(mt, bucket_list)) {
            za_mtunnel_lock(mt);
            assistant_data_mtunnel_dump_mtunnel_mconn(request_state, "Reaped Mtunnel", mt);
            za_mtunnel_unlock(mt);
        }

        pthread_mutex_unlock(&(bucket->lock));
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_mtunnel_inject_stats_dump(struct zpath_debug_state*  request_state,
                                    const char **              query_values,
                                    int                        query_value_count,
                                    void*                      cookie)
{

    ZDP("ZDX probing Injection stats: \n");
    ZDP("total injections - requested : %"PRId64" \n", stats.zdx_injection_total_requested);
    ZDP("total injections - SUCCESS : %"PRId64" \n", stats.zdx_injection_to_mconn_success);
    ZDP("total injections - FAIL - NO Mtunnel found : %"PRId64" \n", stats.zdx_injection_to_mconn_fail_mtunnel_gone);
    ZDP("total injections - FAIL - NO memory : %"PRId64" \n", stats.zdx_injection_to_mconn_fail_no_memory);
    ZDP("total injections - bytes sent : %"PRId64" \n", stats.zdx_injection_total_bytes_sent);

    return ZPN_RESULT_NO_ERROR;

}

int zpn_assistant_valid_stream_cb(struct zrdt_conn *conn,
                                  struct zrdt_stream *stream,
                                  uint64_t stream_id,
                                  int64_t cookie_int,
                                  void *cookie)
{
    int64_t original_incarnation = cookie_int;
    struct zpn_assistant_mtunnel *mtunnel = cookie;

    ASSISTANT_DEBUG_MTUNNEL("verify stream id %ld has mconn/mtunnel associated with it before deleting stream", (long)stream_id);

    if(mtunnel == NULL) {
        ASSISTANT_DEBUG_MTUNNEL("%ld: Invalid mtunnel. proceed with stream deletion", (long)stream_id);
        return 0;
    }

    if (mtunnel->incarnation != original_incarnation) {
        ASSISTANT_DEBUG_MTUNNEL("%ld: mtunnel incarnation mismatch. proceed with stream deletion", (long)stream_id);
        return 0;
    }

    if(mtunnel->state < za_free) {
        ASSISTANT_ASSERT_SOFT(0, "Trying to delete steam %ld when mconn/mtunnel %s exist",
                            (long)stream_id, mtunnel->mtunnel_id);
        return 1;
    }

    return 0;
}

#define MTUNNEL_DEBUG_FILTER_DOMAIN     0x1
#define MTUNNEL_DEBUG_FILTER_PORT       0x2

struct mtunnel_debug_filter {
    zpath_mutex_t filter_lock;
    unsigned all_domains:1;
    unsigned all_ports:1;
    struct zhash_table *mtunnel_debug_domain_lookup_table;
};

struct mtunnel_debug_filter g_mtunnel_filter = {0};

int zpn_assistant_mtunnel_debug_show_domain_cb(void *cookie, void *object, void *key, size_t key_len) {
    (void)key;
    (void)key_len;

    struct zpath_debug_state *rs = (struct zpath_debug_state *)cookie;
    zpath_debug_cb_printf_response(rs, "%s\n", (char*)object);

    return 0;
}

static int
zpn_assistant_mtunnel_debug_show_filter(struct zpath_debug_state *request_state,
        const char **query_values,
        int query_value_count,
        void *cookie)
{
    (void)query_values;
    (void)cookie;

    if (query_value_count != 0) {
        zpath_debug_cb_printf_response(request_state, "Error displaying all filters = [%d]\n", query_value_count);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    zpath_debug_cb_printf_response(request_state, "All Domain Filter : [%d]\n", g_mtunnel_filter.all_domains);
    zpath_debug_cb_printf_response(request_state, "====Configured Domain Filters====\n");
    zhash_table_walk(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, NULL, zpn_assistant_mtunnel_debug_show_domain_cb, request_state);
    ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    zpn_waf_debug_show_filter(request_state, query_values, query_value_count, cookie);

    return ZPN_RESULT_NO_ERROR;
}


int zpn_assistant_mtunnel_debug_delete_domain_filter_cb(void *cookie, void *object, void *key, size_t key_len)
{
    (void)cookie;
    int ret;

    if (!key)
        return ZPN_RESULT_NO_ERROR;

    ret = zhash_table_remove(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, key, key_len, object);
    return ret;
}

static int
zpn_assistant_mtunnel_debug_reset_domain_filter(struct zpath_debug_state *request_state)
{
    zpath_debug_cb_printf_response(request_state, "====Removing Domain Filters====\n");
    ZPATH_MUTEX_LOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    g_mtunnel_filter.all_domains = 0;
    zhash_table_walk(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, NULL, zpn_assistant_mtunnel_debug_delete_domain_filter_cb, request_state);
    ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_assistant_mtunnel_debug_reset_filter(struct zpath_debug_state *request_state,
        const char **query_values,
        int query_value_count,
        void *cookie)
{
    (void)query_values;
    (void)cookie;

    if (query_value_count != 0) {
        zpath_debug_cb_printf_response(request_state, "Error displaying all filters = [%d]\n", query_value_count);
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_assistant_mtunnel_debug_reset_domain_filter(request_state);
    zpn_waf_debug_reset_content_filter(request_state);

    return ZPN_RESULT_NO_ERROR;
}



static char *trim_space(const char *in)
{
    char *out = NULL;
    int len;
    if (in) {
        len = strlen(in);
        while(len && isspace(in[len - 1])) --len;
        while(len && *in && isspace(*in)) ++in, --len;
        if (len) {
            out = strndup(in, len);
        }
    }
    return out;
}

static int zpn_assistant_mtunnel_debug_add_domain(const char* domain)
{
    if (!domain || domain[0]=='\0')
        return ZPN_RESULT_ERR;

    char *d = trim_space(domain);
    if (!d)
        return ZPN_RESULT_ERR;

    ZPATH_MUTEX_LOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    if(strlen(d) == 1 && !strcmp(d, "*")) {
        g_mtunnel_filter.all_domains = 1;
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        free(d);
        return ZPN_RESULT_NO_ERROR;
    }

    if (zhash_table_lookup(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, d, strlen(d), NULL) != NULL) {
        /* domain already present */
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        free(d);
        return ZPN_RESULT_NO_ERROR;
    }

    zhash_table_store(g_mtunnel_filter.mtunnel_debug_domain_lookup_table,
        d,
        strlen(d), 0,
        d);

    ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_assistant_mtunnel_debug_create_domain_filter(struct zpath_debug_state *request_state,
        const char **query_values,
        int query_value_count,
        void *cookie)
{
    (void)cookie;

    if (!query_values[0] || (query_value_count != 1)) {
        zpath_debug_cb_printf_response(request_state, "Missing argument for Mtunnel domain filter\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_assistant_mtunnel_debug_add_domain(query_values[0]))
        zpath_debug_cb_printf_response(request_state, "Add Mtunnel filter for domain = [%s] failed", query_values[0]);
    else
        zpath_debug_cb_printf_response(request_state, "Added Mtunnel filter for domain = [%s]", query_values[0]);
    zpath_debug_cb_printf_response(request_state,"\n");

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_assistant_mtunnel_debug_delete_domain(const char* domain)
{
    if (!domain || domain[0] == '\0')
        return ZPN_RESULT_ERR;

    ZPATH_MUTEX_LOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    if(strlen(domain) == 1 && !strcmp(domain, "*")) {
        g_mtunnel_filter.all_domains = 0;
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_ERROR;
    }
    zhash_table_remove(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, domain, strlen(domain), NULL);
    ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}


static int
zpn_assistant_mtunnel_debug_remove_domain_filter(struct zpath_debug_state *request_state,
        const char **query_values,
        int query_value_count,
        void *cookie)
{
    (void)cookie;

    if (!query_values[0] || (query_value_count != 1)) {
        zpath_debug_cb_printf_response(request_state, "Missing argument for Mtunnel domain filter\n");
        return ZPN_RESULT_NO_ERROR;
    }

    int res = zpn_assistant_mtunnel_debug_delete_domain(query_values[0]);
    if (res)
        zpath_debug_cb_printf_response(request_state, "Remove Mtunnel filter for domain = [%s] failed with err [%d]",
                query_values[0], res);
    else
        zpath_debug_cb_printf_response(request_state, "Remove Mtunnel filter for domain = [%s]", query_values[0]);

    zpath_debug_cb_printf_response(request_state,"\n");

    return ZPN_RESULT_NO_ERROR;
}

uint8_t zpn_assistant_mtunnel_get_debug_status(char *domain)
{
    uint8_t debug_flag = 0;

    if (!domain || domain[0] == '\0')
        return 0;

    ZPATH_MUTEX_LOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);

    if (g_mtunnel_filter.all_domains) {
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        return 1;
    }

    if (!zhash_table_get_size(g_mtunnel_filter.mtunnel_debug_domain_lookup_table)) {
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        return 0;
    }

    if (!zhash_table_lookup(g_mtunnel_filter.mtunnel_debug_domain_lookup_table, domain, strlen(domain), NULL)) {
        ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
        ASSISTANT_DEBUG_WAF_INSPECT("Debugging disabled : No domain filter match - domain = [%s] len=[%ld] \n", domain, strlen(domain));
        return 0;
    }

    ZPATH_MUTEX_UNLOCK(&(g_mtunnel_filter.filter_lock), __FILE__, __LINE__);
    debug_flag |= MTUNNEL_DEBUG_FILTER_DOMAIN;
    ASSISTANT_DEBUG_WAF_INSPECT("Logging Enabled for Domain [%s] Debug Flag = 0x%d \n",
            domain, debug_flag);

    return debug_flag;
}

void zpn_assistant_data_mtunnel_stats_init()
{
    memset(&stats,0,sizeof(stats));
}

int zpn_assistant_mtunnel_debug_filter_init()
{
    int res = ZPN_RESULT_NO_ERROR;
    g_mtunnel_filter.filter_lock = ZPATH_MUTEX_INIT;
    g_mtunnel_filter.mtunnel_debug_domain_lookup_table = zhash_table_alloc(assistant_state_get_allocator());
    if(!g_mtunnel_filter.mtunnel_debug_domain_lookup_table) {
        ASSISTANT_ERR("MTunnel Debug Domain Lookup Table Alloc Failed");
        res = ZPN_RESULT_ERR;
        return res;
    }

    return res;
}

static struct argo_structure_description *assistant_debug_filter_description;

/* Sample config : config.feature.connector.debug_filter
 * {"assistant_debug_filter":
 *  {"domain":
 *      ["app1.seg1.jane.https.auto.off.com",
 *       "app3.seg1.jane.https.auto.off.com"],
 *   "content_encoding":
 *      ["gzip", "compress", "defalte", "br"],
 *   "content_type":
 *      ["application/zip"]
 *  }
 * } */

int parse_debug_filter_json_config(const char* config_val) {
    if (!(assistant_debug_filter_description = argo_register_global_structure(ASSISTANT_DEBUG_FILTER_HELPER)))
        return ZPATH_RESULT_ERR;
    struct argo_object *object = NULL;
    object = argo_deserialize_json(config_val, strlen(config_val));
    if (!object) {
        return ZPATH_RESULT_ERR;
    }


    if (assistant_debug_filter_description != argo_get_object_description(object)) {
        argo_object_release(object);
        return ZPATH_RESULT_ERR;
    }

    struct assistant_debug_filter *filter = object->base_structure_void;
    /* Extract the domain filters */
    for (int i = 0; i < filter->domain_count; i++) {
        zpn_assistant_mtunnel_debug_add_domain(filter->domain[i]);
    }

    /* Extract the content-type filters */
    for (int i = 0; i < filter->content_type_count; i++) {
        zpn_waf_debug_add_content_type_filter(filter->content_type[i]);
    }

    /* Extract the content-encoding filters */
    for (int i = 0; i < filter->content_encoding_count; i++) {
        zpn_waf_debug_add_content_encoding_filter(filter->content_encoding[i]);
    }

    argo_object_release(object);
    return ZPATH_RESULT_NO_ERROR;
}

void zpn_assistant_waf_populate_debug_filter(int64_t assistant_gid)
{
    char *config_val = NULL;
    config_val = zpath_config_override_get_config_str(ASST_WAF_DEBUG_FILTER_CONFIG,
                                                      &config_val,
                                                      NULL,
                                                      assistant_gid,
                                                      ZPATH_GID_GET_CUSTOMER_GID(assistant_gid),
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator

    if (!config_val || config_val[0] == '\0') {
        return;
    }

    if (ZPATH_RESULT_NO_ERROR != parse_debug_filter_json_config(config_val)) {
        ASSISTANT_LOG(AL_ERROR, "Parsing Debug Filter [%s] failed", config_val);
        return;
    } else {
        ASSISTANT_LOG(AL_INFO, "Debug Filter [%s] enabled", config_val);
    }

    return;
}

int
assistant_data_mtunnel_init()
{
    int res = ZPN_RESULT_NO_ERROR;

    if (!(assistant_data_mtunnel_stats_description = argo_register_global_structure(ASSISTANT_DATA_MTUNNEL_STATS_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create assistant data mtunnel stats desc");
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the stats of assistant data mtunnel",
                                  "/assistant/data/mtunnel/dump/stats",
                                  assistant_data_mtunnel_dump_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the stats of assistant data mtunnel http webprobe",
                                  "/assistant/data/mtunnel/dump/webprobe/http/stats",
                                  assistant_data_mtunnel_dump_webprobe_http_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/webprobe/http/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the stats of assistant data mtunnel webprobe",
                                  "/assistant/data/mtunnel/dump/webprobe/stats",
                                  assistant_data_mtunnel_dump_webprobe_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/webprobe/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the stats of assistant data mtunnel https webprobe",
                                  "/assistant/data/mtunnel/dump/webprobe/https/stats",
                                  assistant_data_mtunnel_dump_webprobe_https_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/webprobe/https/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("reset the stats of assistant data mtunnel http webprobe",
                                  "/assistant/data/mtunnel/reset/webprobe/http/stats",
                                  assistant_data_mtunnel_reset_webprobe_http_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/reset/webprobe/http/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("reset the stats of assistant data mtunnel https webprobe",
                                  "/assistant/data/mtunnel/reset/webprobe/https/stats",
                                  assistant_data_mtunnel_reset_webprobe_https_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/reset/webprobe/https/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("reset the stats of assistant data mtunnel webprobe",
                                  "/assistant/data/mtunnel/reset/webprobe/stats",
                                  assistant_data_mtunnel_reset_webprobe_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/reset/webprobe/stats to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the data mtunnels",
                                  "/assistant/data/mtunnel/dump/mtunnels",
                                  assistant_data_mtunnel_dump_mtunnels,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/mtunnels to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("inject a hardcoded mtr report to an active mtunnel ",
                                  "/assistant/mtunnel/ut/inject_data_to_server_mconn",
                                  assistant_mtunnel_inject_data_to_server_mconn,
                                  NULL,
                                  "mtunnel_id","current active mtunnel id",
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/ut/inject_data_to_server_mconn to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("zdx probe injection stats ",
                                  "/assistant/mtunnel/injection_stats/dump",
                                  assistant_mtunnel_inject_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/mtunnel/injection_stats/dump to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("Display all Mtunnel debug filters configured",
                                  "/assistant/mtunnel/filter/dump",
                                  zpn_assistant_mtunnel_debug_show_filter,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/mtunnel/filter/dump to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("Add domain filter for Mtunnel",
                                  "/assistant/mtunnel/filter/create/domain",
                                  zpn_assistant_mtunnel_debug_create_domain_filter,
                                  NULL,
                                  "domain", "Filter Domain Name",
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/mtunnel/filter/domain to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("Remove domain filter for Mtunnel",
                                  "/assistant/mtunnel/filter/remove/domain",
                                  zpn_assistant_mtunnel_debug_remove_domain_filter,
                                  NULL,
                                  "domain", "Filter Domain Name",
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/mtunnel/filter/domain to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_write_command("Reset all Mtunnel debug domain filters",
                                  "/assistant/mtunnel/filter/reset",
                                  zpn_assistant_mtunnel_debug_reset_filter,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/mtunnel/filter/reset to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    res = zpath_debug_add_read_command("dump the data mtunnels",
                                  "/assistant/data/mtunnel/dump/mtunnels/mconn",
                                  assistant_data_mtunnel_dump_mtunnels_mconn,
                                  NULL,
                                  NULL);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/data/mtunnel/dump/mtunnels/mconn to debug: %s",
                      zpn_result_string(res));
        res = ZPN_RESULT_ERR;
        goto done;
    }

    zpn_assistant_data_mtunnel_stats_init();
    zpn_assistant_mtunnel_debug_filter_init();
    zpn_assistant_waf_populate_debug_filter(global_assistant.gid);

done:
    return res;
}

/*
 * For health muted application, we may not have the IP address. Its upto us to figure out the IP with the help
 * of zcdns. We have already asked zcdns for help and here comes a callback.
 *
 * But if the state is not in za_request_received, then it means the mtunnel have moved on to next stages and don't
 * need the help. Silently move on.
 */
void
assistant_data_mtunnel_dns_cb(void*                 cb_void_cookie,
                              int64_t               cb_int_cookie,
                              struct zcdns_result*  result)
{
    char*                           mtunnel_id;
    int                             mtunnel_id_len;
    uint64_t                        mtunnel_id_hash;
    struct zpn_assistant_mtunnel*   mtunnel;

    mtunnel_id          = (char *) cb_void_cookie;
    mtunnel_id_len      = strlen(mtunnel_id);
    mtunnel_id_hash     = CityHash64(mtunnel_id, mtunnel_id_len);

    mtunnel = za_mtunnel_lookup_and_lock(mtunnel_id, mtunnel_id_len, mtunnel_id_hash);
    /*
     * If the transaction is already deleted(may be coz it couldn't talk to the server?), it will fail. Ok to ignore.
     */
    if (!mtunnel) {
        ASSISTANT_DEBUG_MTUNNEL("%s: DNS retry failed for this muted application, this transaction had failed already or succeed if there is another IP available", mtunnel_id);
        goto done;
    }
    assistant_dns_resolve_done(result, zthread_self()->stack.thread_num, mtunnel->domain);
    za_mtunnel_lock(mtunnel);

    if (za_request_received == mtunnel->state) {
        za_mtunnel_locked_state_machine(mtunnel);
    }

    za_mtunnel_unlock(mtunnel);
    za_mtunnel_bucket_unlock(mtunnel);

done:
    ASST_FREE(mtunnel_id);
    zcdns_result_release(result);
}

void zpn_connector_application_init(void *mconn_self, struct zpn_connector *conn) {
    if (!mconn_self || !conn)
        return;

    struct zpn_assistant_mtunnel *mtunnel = mconn_self;
    conn->mdbg_str = mtunnel->mdbg_str;
    conn->domain = mtunnel->domain;
    conn->server_port = mtunnel->server_port_he;
    conn->ip_proto = mtunnel->ip_protocol;

    return;
}
