/*
 * assistant_stats_tx.c. Copyright (C) 2018 - 2021 Zscaler Inc. All Rights Reserved.
 * Collects and transmits the assistant's stats to stats brokers
 *
 * Naming convention:
 *  1. The backend system (microservice or kafka or whatever) depends on two things to process the stats from
 *  assistant - l_role & l_name in the argo_object. l_role is zpa-connector-child, based on which it will pivot to a
 *  codepath to distinguish between different sub-systems in itasca. l_name is useful to identify what object is coming
 *  in from that particular subsystem. So any change to these have to be notified to the microservices team.
 *  2. l_name - We will prefix by assistant_stats_* (eg assistant_stats_rusage_process,
 *  assistant_stats_rusage_thread_fohh_x, ...) [ FIXME: this convention is not followed in the older stats,
 *  which should be fixed ]
 *
 * CAVEAT:
 * 1. stats_upload_reader is initialized when the connector inits, this means the events will be queued into fohh.
 * But fohh connection to control_broker will be done asynchronously later. When the connection is done,
 * fohh_queue_clear() is called which will wash off all the queued events and we lost the stats events. This will be
 * fixed in ET12750
 */

#include "zpath_lib/zpath_debug.h"
#include "argo/argo_hash.h"
#include "admin_probe/admin_probe_rpc.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_system.h"
#include "zpath_lib/zpath_system.h"
#include "zpn/zpn_rpc.h"
#include "zthread/zthread.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpath_lib/zpath_system_stats.h"
#include "np_connector/np_connector.h"

#include "zpn_assistantd/assistant_admin_probe.h"
#include "zpn_assistantd/assistant_app.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_assistantd/assistant_dns.h"
#include "zpn_assistantd/assistant_rpc.h"
#include "zpn_assistantd/assistant_sticky_cache.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_ncache.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_stats_tx.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_dr_interface.h"
#include "zpn_assistantd/assistant_features.h"


/* Please tune/refine this timing as and when we get more clarity on the kind of issues that we get */
/* 30 mins */
#define  ASSISTANT_STATS_TX_MALLINFO_TIMEPERIOD_USEC                ((int64_t)(30ll * 60ll * 1000ll * 1000ll))
/* 6 hrs */
#define  ASSISTANT_STATS_TX_SYSINFO_TIMEPERIOD_USEC                 ((int64_t)(6ll * 60ll * 60ll * 1000ll * 1000ll))

/*
 * We are debating on the frequency of the new stats. Till we get a clarity on that, lets change the time period to 1hr.
 * So that the feature is in and we don't overload the cassandra infra.
 */
#ifdef WAITING_FOR_CASSANDRA_SIZING_DISCUSSION_WITH_OPS_TEAM
/* 1 min */
#define  ASSISTANT_STATS_TX_DATA_TIMEPERIOD_USEC                    ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 min */
#define  ASSISTANT_STATS_TX_DATA_MTUNNEL_TIMEPERIOD_USEC            ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 30 mins */
#define  ASSISTANT_STATS_TX_SYSTEM_DISK_TIMEPERIOD_USEC             ((int64_t)(30ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_SYSTEM_CPU_TIMEPERIOD_USEC              ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_SYSTEM_FD_TIMEPERIOD_USEC               ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_SYSTEM_SOCK_TIMEPERIOD_USEC             ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_APP_TIMEPERIOD_USEC                     ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_RPC_TIMEPERIOD_USEC                     ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_SCACHE_TIMEPERIOD_USEC                  ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  ASSISTANT_STATS_TX_NCACHE_TIMEPERIOD_USEC                  ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
#else
#define  ASSISTANT_STATS_TX_DATA_TIMEPERIOD_USEC                    ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_DATA_MTUNNEL_TIMEPERIOD_USEC            ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_DISK_TIMEPERIOD_USEC             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_CPU_TIMEPERIOD_USEC              ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_NETWORK_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_FD_TIMEPERIOD_USEC               ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SYSTEM_SOCK_TIMEPERIOD_USEC             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_APP_TIMEPERIOD_USEC                     ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_RPC_TIMEPERIOD_USEC                     ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_SCACHE_TIMEPERIOD_USEC                  ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_DNS_STATS_TIMEPERIOD_USEC                        ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_NCACHE_TIMEPERIOD_USEC                  ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_ADMIN_PROBE_USEC                        ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_FPROXY_USEC                             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
#endif
/* 1 sec */
#define  ASSISTANT_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC       ((int64_t)(1ll * 1000ll * 1000ll))
/* 5 mins */
#define  ASSISTANT_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC           ((int64_t)(5ll * 60ll * 1000ll * 1000ll))
#define  ASSISTANT_STATS_TX_MONITOR_USEC                            ((int64_t)(5ll * 60ll  * 1000ll * 1000ll))
/*24hrs*/
#define  ASSISTANT_STATS_TX_SYSTEM_PERIOD_USEC                      ((uint64_t)(24ll * 60ll * 60ll * 1000ll * 1000ll))
/*24hrs*/
#define  ASSISTANT_STATS_TX_SYSTEM_PERIOD_USEC                      ((uint64_t)(24ll * 60ll * 60ll * 1000ll * 1000ll))
/*1hr*/
#define  ASSISTANT_STATS_TX_ZVM_TIMEPERIOD_USEC                     ((uint64_t)(60ll * 60ll * 1000ll * 1000ll))
/*1h*/
#define  ASSISTANT_STATS_TX_UPGRADE_TIMEPERIOD_USEC                 ((uint64_t)(60ll * 60ll * 1000ll * 1000ll))

#define  NP_CONNECTOR_STATS_TX_WIREGUARD_STATS_USEC                 ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/*
 *
 * Rough estimate of how much bytes generated by each connector in the field per day
 *
 * Cassandra is the destination of all the generated stats log messages. Each field in the stats log is saved as a row
 * in the database.
 *
 * Here is how the table in cassandra looks like and how much bytes of memory it takes in the disk.
 *
 * role = = 64bytes
 * gid = 8 bytes
 * log_type = 64 bytes
 * log_field = 64 bytes
 * interval = 8 bytes
 * time_window = 8 bytes
 * log_field = 64 bytes
 * log_value = 64 bytes
 *
 * i.e each field in stats log is converted to 350 bytes in cassandra - worst case for calucation point of view. On top
 * of this cassandra does disk compression.
 *
 * For the sake of disk storage, i will assume we are going to save the data for 1yr.
 * And out of these the last 14 days of data is going to have heavy IO requirement.
 * CI is Customer Infra; ZI is Zscaler Infra
 *
 * ifdef WAITING_FOR_CASSANDRA_SIZING_DISCUSSION_WITH_OPS_TEAM
 * Rough estimate of how much bytes generated by each connector in the field per day
 *
 * Smaps                      | 32768B/day   | 32768 B/day   | ZI
 * Linux sysifo               | 4900B/6hrs   | 19600 B/day   | ZI
 * Overall memory usage       | 2450B/10mins | 352800 B/day  | ZI
 * Per allocator memory usage | 24500B/12hrs | 49000 B/day   | ZI
 * Mallinfo                   | 3500B/10mins | 504000 B/day  | ZI
 * rpc                        | 5950B/min    | 8568000 B/day | ZI
 * data                       | 4550B/min    | 6552000 B/day | CI
 * mtunnel                    | 21000B/min   | 3024000 B/day | CI
 * disk                       | 1400B/30mins | 672000 B/day  | CI
 * CPU                        | 1400B/min    | 2016000 B/day | CI
 * memory                     | 3150B/min    | 4536000 B/day | CI
 * file desc                  | 1750B/min    | 2520000 B/day | CI
 * socket                     | 3500B/min    | 5040000 B/day | CI
 * app                        | 2450B/min    | 3528000 B/day | CI
 *
 * ZI:
 *  Total of 9526168B/day/connector => 9MB/day
 *  If we assume 5000 connectors => 44 GB/day hitting Cassandra => 15TB/year
 * CI:
 *  Total of 27888000B/day/connector => 26 MB/day
 *  If we assume 5000 connectors => 129 GB/day hitting Cassandra => 46 TB/year
 *
 * else
 *
 * Smaps                      | 32768B/day   | 32768 B/day  | ZI
 * Linux sysifo               | 4900B/6hrs   | 19600 B/day  | ZI
 * Overall memory usage       | 2450B/10mins | 352800 B/day | ZI
 * Per allocator memory usage | 24500B/12hrs | 49000 B/day  | ZI
 * Mallinfo                   | 3500B/10mins | 504000 B/day | ZI
 * rpc                        | 5950B/hr     | 142800 B/day | ZI
 * data                       | 4550B/hr     | 109200 B/day | CI
 * mtunnel                    | 21000B/hr    | 504000 B/day | CI
 * disk                       | 1400B/hr     | 336000 B/day | CI
 * CPU                        | 1400B/hr     | 336000 B/day | CI
 * memory                     | 3150B/hr     | 756000 B/day | CI
 * file desc                  | 1750B/hr     | 42000 B/day  | CI
 * socket                     | 3500B/hr     | 84000 B/day  | CI
 * app                        | 2450B/hr     | 58800 B/day  | CI
 *
 * ZI:
 *  Total of 1100968B/day/connector => 1MB/day
 *  If we assume 5000 connectors => 5 GB/day hitting Cassandra => 1.8TB/year
 * CI:
 *  Total of 2226000 B/day/connector => 2 MB/day
 *  If we assume 5000 connectors => 10 GB/day hitting Cassandra => 3.6 TB/year
 *
 * endif
 *
 *
 */

/*
 * FIXME:
 * Eventually all the stats object will be an array element here. Now the legacy!! stats objects are in its own land.
 */
struct assistant_stats_tx_cfg {
    char*                                   l_name;
    char*                                   l_otyp;
    int                                     enabled;
    int64_t                                 interval_us;
    struct argo_structure_description*      argo_description;
    int                                     size_of_object;
    argo_log_pre_log_callback_f*            pre_log_cb;
    void*                                   pre_log_cb_cookie;
    int                                     log_immediate;
};

#define ASSISTANT_STATS_TX_MAX_OBJECTS 22
static struct  assistant_stats_tx_cfg cfg[ASSISTANT_STATS_TX_MAX_OBJECTS];
static int     cfg_ready;

static struct argo_structure_description* assistant_stats_tx_stats_description;

static
struct assistant_stats_tx_stats {                                      /* _ARGO: object_definition */
    uint64_t stats;                                                      /* _ARGO: integer */
}stats;

#include "zpn_assistantd/assistant_stats_tx_compiled_c.h"

static int assistant_stats_zthread_stats_fill(void* structure_data);

static void
assistant_stats_tx_reader_cb_log(void*                       void_cookie,
                                 char*                       reason,
                                 int                         status)
{
    struct argo_log *log = void_cookie;

    if (FOHH_RESULT_NO_ERROR != status) {
        /*
         * Even this below message may be dropped while being sent to the stats broker if the FOHH is in the mood
         * of WOULD_BLOCK. But logging this, so that if any customer is willing to provide /var/log/messages, we
         * will get some insights.
         */
        ASSISTANT_LOG(AL_ERROR, "stats upload to stats broker otyp(%s) name(%s) - dropped",
                      log->l_otyp, log->l_name);
        if (0 == strncmp("assistant_stats_comprehensive", log->l_name, sizeof("assistant_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&global_assistant.num_comprehensive_stats_upload_fails, 1);
        }
    } else {
        ASSISTANT_DEBUG_STATS_TX("stats upload to stats broker otyp(%s) name(%s), queued into fohh",
                                         log->l_otyp, log->l_name);
        if (0 == strncmp("assistant_stats_comprehensive", log->l_name, sizeof("assistant_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&global_assistant.num_comprehensive_stats_upload, 1);
        }
    }

}

static int
assistant_zvm_stats_fill(void*     cookie,
                         int       counter,
                         void*     structure_data)
{
    struct zpn_assistant_zvm_stats *out_data;

    out_data = (struct zpn_assistant_zvm_stats *)structure_data;

    zpn_zvm_get_zvm_stats(&out_data->reg_id_fohh_http_create_count,
                          &out_data->reg_id_fohh_http_free_count,
                          &out_data->reg_id_imdsv2_token_alloc_count,
                          &out_data->reg_id_imdsv2_token_free_count,
                          &out_data->reg_id_api_ver_alloc_count,
                          &out_data->reg_id_api_ver_free_count,
                          &out_data->reg_id_query_count,
                          &out_data->reg_id_query_success_count,
                          &out_data->reg_id_query_failure_count);

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_upgrade_stats_fill(void*     cookie,
                             int       counter,
                             void*     structure_data)
{
    struct zpn_assistant_upgrade_stats *out_data;

    out_data = (struct zpn_assistant_upgrade_stats *)structure_data;

    out_data->os_upgrade_fail = global_assistant.upgrade_stats.os_upgrade_fail;
    out_data->sarge_upgrade_fail = global_assistant.upgrade_stats.sarge_upgrade_fail;
    out_data->os_upgrade_success = global_assistant.upgrade_stats.os_upgrade_success;
    out_data->sarge_upgrade_success = global_assistant.upgrade_stats.sarge_upgrade_success;
    out_data->os_upgrade_timeout = global_assistant.upgrade_stats.os_upgrade_timeout;
    out_data->sarge_os_cfg_read_fail = global_assistant.upgrade_stats.sarge_os_cfg_read_fail;
    out_data->sudo_path_fail = global_assistant.upgrade_stats.sudo_path_fail;
    out_data->package_manager_path_fail = global_assistant.upgrade_stats.package_manager_path_fail;

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Collect assistant comprehensive stats and upload
 * We have to collect the stats from different places...
 */
static int
assistant_comprehensive_stats_fill(void*     cookie,
                                   int       counter,
                                   void*     structure_data)
{
    struct zpn_assistant_comprehensive_stats *out_data;

    out_data = (struct zpn_assistant_comprehensive_stats *)structure_data;

    out_data->g_ast = global_assistant.gid;
    out_data->g_cst = ZPATH_GID_GET_CUSTOMER_GID(global_assistant.gid);
    out_data->cloud_time_us = assistant_state_get_current_time_cloud_us();
    out_data->g_microtenant = is_scope_default(global_assistant.scope_gid) ? 0 : global_assistant.scope_gid;

    out_data->imds_disabled = global_assistant.imds_disabled;
    out_data->imdsv2_required = global_assistant.imdsv2_required;
    out_data->disk_id_fail = global_assistant.disk_id_fail;

    if (global_assistant.cur_mode == np_connector) {
        out_data->np_connector_gid = np_connector_get_connector_gid();
        out_data->total_throughput_bits_per_sec = np_connector_get_throughput_bits_per_sec_for_comp_stats();
    }

    assistant_mtunnel_comprehensive_stats_fill(out_data);
    assistant_data_comprehensive_stats_fill(out_data);
    assistant_app_comprehensive_stats_fill(out_data);
    zpn_system_comprehensive_stats_fill(out_data, cookie);

    /* DR stats */
    assistant_get_dr_stats(&out_data->dr_activation_on_count,
                           &out_data->dr_activation_test_count,
                           &out_data->dr_activation_off_count,
                           &out_data->dr_activation_err_cnt,
                           &out_data->dr_activation_req_cnt,
                           &out_data->dr_activation_resp_cnt,
                           &out_data->dr_activation_no_resp_cnt,
                           &out_data->dr_activation_err_resp_cnt,
                           &out_data->dr_config_auto_dump_count,
                           &out_data->dr_config_auto_dump_fail_count,
                           &out_data->dr_config_snapshot_dump_count,
                           &out_data->dr_config_snapshot_dump_fail_count,
                           &out_data->dr_config_snapshot_current_count);

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_stats_alt_cloud_stats_fill(void* structure_data)
{
    struct zpn_assistant_monitor_stats* out_data;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;

    out_data->alt_cloud_enabled = global_assistant.alt_cloud_enabled;
    out_data->alt_cloud_resets = assistant_state_get_alt_cloud_resets_count();
    out_data->on_alt_cloud = assistant_state_get_alt_cloud_status();

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_stats_udp_timeout_fail_stats_fill(void* structure_data)
{
    struct zpn_assistant_monitor_stats* out_data;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;

    out_data->udp_health_timeout_failure_flag_enabled = global_assistant.udp_health_timeout_failure_flag_enabled;

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_stats_allocator_libevent_fill(void* structure_data)
{
    struct zpn_assistant_monitor_stats* out_data;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;

    out_data->allocator_libevent_max_bytes = assistant_state_allocator_libevent_max_bytes_get();
    out_data->allocator_libevent_used_bytes = assistant_state_allocator_libevent_used_bytes_get();
    out_data->allocator_libevent_used_percent = assistant_state_allocator_libevent_used_percent_get();
    out_data->allocator_libevent_out_queue_allowed_bytes = assistant_state_allocator_libevent_out_queue_allowed_bytes_get();

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Collect assistant comprehensive stats and upload
 * We have to collect the stats from different places...
 */
static int
assistant_monitor_stats_fill(void*     cookie,
                             int       counter,
                             void*     structure_data)
{
    struct zpn_assistant_monitor_stats *out_data;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;

    out_data->cloud_time_us = assistant_state_get_current_time_cloud_us();

    assistant_monitor_timer_stats_fill(out_data);
    assistant_mtunnel_monitor_stats_fill(out_data);
    assistant_stats_zthread_stats_fill(out_data);
    assistant_stats_alt_cloud_stats_fill(out_data);
    assistant_stats_udp_timeout_fail_stats_fill(out_data);
    assistant_stats_allocator_libevent_fill(out_data);

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_stats_zthread_stats_fill(void* structure_data)
{
    struct zpn_assistant_monitor_stats* out_data;
    struct zthread_stats stats;

    out_data = (struct zpn_assistant_monitor_stats *)structure_data;
    zthread_stats_get(&stats);

    out_data->zthread_time_skipped = stats.zthread_time_skipped;
    out_data->zthread_time_skipped_exceed_max = stats.zthread_time_skipped_exceed_max;
    out_data->zthread_healthy_counter = stats.zthread_healthy_counter;
    out_data->zthread_unhealthy_counter = stats.zthread_unhealthy_counter;
    out_data->zthread_monitor_healthy_counter = stats.zthread_monitor_healthy_counter;
    out_data->zthread_monitor_unhealthy_counter = stats.zthread_monitor_unhealthy_counter;

    return ZPATH_RESULT_NO_ERROR;
}

static void
assistant_stats_tx(struct argo_object*          log_object,
                   int64_t                      argo_log_sequence,
                   assistant_stats_tx_done_cb   done_cb,
                   void*                        done_cb_void_cookie,
                   char*                        done_cb_str_cookie)
{
    int fohh_ret;

    fohh_ret = fohh_argo_serialize_object(global_assistant.broker_stats, log_object, argo_log_sequence,
                                          fohh_queue_element_type_control);
    if (done_cb) done_cb(done_cb_void_cookie, done_cb_str_cookie, fohh_ret);
    stats.stats++;
}

/*
 * This is a filter for all the stats messages that are going out of the assistant. We need this because the
 * libraries that we rely on could register for a stats upload which we don't intend to be useful for the assistant.
 * This function will make sure that assistant know exactly what it is sending out of the system - so explicitly check
 * for each and every message going out.
 */
int
assistant_stats_tx_reader_cb(struct argo_object*    log_object,
                             void*                  callback_cookie,
                             int64_t                argo_log_sequence)
{
    struct argo_log *log = log_object->base_structure_void;
    int             upload_ok;
    size_t          cfg_iter;


    if (!global_assistant.stats_upload_enabled) {
        return ZPATH_RESULT_NO_ERROR;
    }

    upload_ok = 0;
    if (((0 == strncmp("zthread_rusage", log->l_otyp, sizeof("zthread_rusage"))) &&
         (0 == strncmp("rusage_thread_all", log->l_name, sizeof("rusage_thread_all"))) ) ||
        (0 == strncmp("zpath_debug_memory_allocator_stats", log->l_otyp, sizeof("zpath_debug_memory_allocator_stats")) ) ||
        (0 == strncmp("zpath_debug_mallinfo", log->l_otyp, sizeof("zpath_debug_mallinfo"))) ||
        (0 == strncmp("zpath_system_sysinfo", log->l_otyp, sizeof("zpath_system_sysinfo"))) ||
        (0 == strncmp("proc_smaps", log->l_name, sizeof("proc_smaps"))) ||
        (0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) ||
        (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_error_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_error_stats"))) ||
        (0 == strncmp("zpn_zdx_mtr_stats", log->l_name, sizeof("zpn_zdx_mtr_stats"))) ||
        (0 == strncmp("zpn_zdx_cache_stats", log->l_name, sizeof("zpn_zdx_cache_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_stats", log->l_name, sizeof("zpn_zdx_probe_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_telemetry_stats", log->l_name, sizeof("zpn_zdx_probe_telemetry_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_error_stats", log->l_name, sizeof("zpn_zdx_probe_error_stats"))) ||
        (0 == strncmp("zpn_np_nw_comp_stats", log->l_name, sizeof("zpn_np_nw_comp_stats"))) ||
        (0 == strncmp("zpn_np_route_comp_stats", log->l_name, sizeof("zpn_np_route_comp_stats"))) ||
        (0 == strncmp("zpn_np_cumulative_bgp_peer_stats", log->l_name, sizeof("zpn_np_cumulative_bgp_peer_stats"))) ||
        (0 == strncmp("zpn_np_cumulative_bgp_route_stats", log->l_name, sizeof("zpn_np_cumulative_bgp_route_stats"))) ||
        (0 == strncmp("zpn_np_frr_svc_stats", log->l_name, sizeof("zpn_np_frr_svc_stats"))) ||
        (0 == strncmp("zpn_fohh_worker_assistant_stats", log->l_name, sizeof("zpn_fohh_worker_assistant_stats"))) ||
        (0 == strncmp("zhealth_probe_lib_raw_sock_stats", log->l_name, sizeof("zhealth_probe_lib_raw_sock_stats"))) ||
        (0 == strncmp("zhealth_probe_lib_udp_raw_sock_stats", log->l_name, sizeof("zhealth_probe_lib_udp_raw_sock_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_https_ptls_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_https_ptls_stats"))) ||
        (0 == strncmp("zpn_zdx_webprobe_cache_https_ptls_err_stats", log->l_name, sizeof("zpn_zdx_webprobe_cache_https_ptls_err_stats"))) ||
        (0 == strncmp("fohh_connection_cipher_stats", log->l_otyp, sizeof("fohh_connection_cipher_stats"))) ||
        (0 == strncmp("zpn_zdx_probe_http_response_error_stats", log->l_name, sizeof("zpn_zdx_probe_http_response_error_stats"))) ||
        (0 == strncmp("fohh_connection_aggregated_hop_latency_stats", log->l_otyp, sizeof("fohh_connection_aggregated_hop_latency_stats"))) ||
        (0 == strncmp("fohh_connection_aggregated_pipeline_latency_stats", log->l_otyp, sizeof("fohh_connection_aggregated_pipeline_latency_stats")))) {
        upload_ok = 1;
    }

    if (0 == cfg_ready) {
        ASSISTANT_ASSERT_SOFT(0, "dropped stats as config is not yet initialized");
        return ZPATH_RESULT_NO_ERROR;
    }
    for (cfg_iter = 0; cfg_iter < ASSISTANT_STATS_TX_MAX_OBJECTS; cfg_iter++) {
        if (0 != strcmp(cfg[cfg_iter].l_name, log->l_name)) {
            continue;
        }

        if ((cfg[cfg_iter].l_otyp) && (0 != strcmp(cfg[cfg_iter].l_otyp, log->l_otyp))) {
            continue;
        }

        if (0 == cfg[cfg_iter].enabled) {
            continue;
        }

        upload_ok = 1;
        goto done;
    }

done:
    if (upload_ok && !is_zpn_drmode_enabled()) {
        assistant_stats_tx(log_object, argo_log_sequence, assistant_stats_tx_reader_cb_log, log, "");
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_stats_tx_dump(struct zpath_debug_state*   request_state,
                        const char**                query_values,
                        int                         query_value_count,
                        void*                       cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_stats_tx_stats_description, &stats, jsonout,
                                                    sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_stats_tx_comprehensive_stats_set_interval(struct zpath_debug_state*   request_state,
                                                    const char**                query_values,
                                                    int                         query_value_count,
                                                    void*                       cookie)
{
    int64_t interval_us;
    int res;

    if (!query_values[0]) {
        ZDP("Missing argument - interval_us\n");
        goto done;
    }

    interval_us = strtoll(query_values[0], NULL, 10);
    if (interval_us < ASSISTANT_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC) {
        ZDP("Change comprehensive stats logging interval failed, minimum interval: %"PRId64"\n",
                                    ASSISTANT_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC);
        goto done;
    }

    res = argo_log_set_interval_by_log_name(argo_log_get("statistics_log"),
                                            "assistant_stats_comprehensive",
                                            sizeof("assistant_stats_comprehensive"),
                                            interval_us);
    if (res) {
        ZDP("Change comprehensive stats logging interval failed: %s\n", zpath_result_string(res));
    } else {
        ZDP("Comprehensive stats logging interval successfully changed to: %"PRId64"\n", interval_us);
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}
/*
 * Adding the stats specific to connector here. At any point of time, if you feel this have to be moved
 * to common location and applicable for all software, pls move this to zpath_debug_app_stats()
 *
 * If diagnostic mode is enabled, dump the stats in connector's local disk. This is done only for engineering
 * development debugging.
 */
int
assistant_stats_tx_init()
{
    static int available_cpus;
    static struct zpn_system_state asst_sys_stats;
    int res = ZPATH_RESULT_NO_ERROR;

    available_cpus = assistant_state_get_available_cpus();
    asst_sys_stats.available_cpus = assistant_state_get_available_cpus();
    asst_sys_stats.configured_cpus = assistant_state_get_configured_cpus();
    asst_sys_stats.fohh_threads = assistant_state_get_fohh_threads();
    asst_sys_stats.is_container_env = assistant_state_is_container_env();
    asst_sys_stats.is_zscaler_os = assistant_state_is_zscaler_os();

    cfg[0] = (struct assistant_stats_tx_cfg){"assistant_stats_data", NULL, 1, ASSISTANT_STATS_TX_DATA_TIMEPERIOD_USEC, zpn_assistant_data_stats_description, sizeof(struct zpn_assistant_data_stats), assistant_data_stats_fill, NULL, 1};
    cfg[1] = (struct assistant_stats_tx_cfg){"assistant_stats_data_mtunnel_global", NULL, 1, ASSISTANT_STATS_TX_DATA_MTUNNEL_TIMEPERIOD_USEC, zpn_assistant_data_mtunnel_global_stats_description, sizeof(struct zpn_assistant_data_mtunnel_global_stats), assistant_data_mtunnel_global_stats_fill, NULL, 1};
    cfg[2] = (struct assistant_stats_tx_cfg){"assistant_stats_system_disk", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_DISK_TIMEPERIOD_USEC, zpath_system_disk_stats_description, sizeof(struct zpn_system_disk_stats), zpn_system_disk_stats_fill, NULL, 1};
    cfg[3] = (struct assistant_stats_tx_cfg) {"assistant_stats_system_cpu", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_CPU_TIMEPERIOD_USEC, zpath_system_cpu_stats_description, sizeof(struct zpn_system_cpu_stats), zpn_system_cpu_stats_fill, &available_cpus, 1};
    cfg[4] = (struct assistant_stats_tx_cfg){"assistant_stats_system_memory", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC, zpath_system_memory_stats_description, sizeof(struct zpn_system_memory_stats), assistant_pbroker_system_memory_stats_fill, NULL, 1};
    cfg[5] = (struct assistant_stats_tx_cfg){"assistant_stats_system_fd", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_FD_TIMEPERIOD_USEC, zpath_system_fd_stats_description, sizeof(struct zpn_system_fd_stats), zpn_system_fd_stats_fill, NULL, 1};
    cfg[6] = (struct assistant_stats_tx_cfg){"assistant_stats_system_sock", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_SOCK_TIMEPERIOD_USEC, zpath_system_sock_stats_description, sizeof(struct zpn_system_sock_stats), zpn_system_sock_stats_fill, NULL, 1};
    cfg[7] = (struct assistant_stats_tx_cfg){"assistant_stats_app", NULL, 1, ASSISTANT_STATS_TX_APP_TIMEPERIOD_USEC, zpn_assistant_app_stats_description, sizeof(struct zpn_assistant_app_stats), assistant_app_stats_fill, NULL, 1};
    cfg[8] = (struct assistant_stats_tx_cfg){"assistant_stats_rpc", NULL, 1, ASSISTANT_STATS_TX_RPC_TIMEPERIOD_USEC, zpn_assistant_rpc_stats_description, sizeof(struct zpn_assistant_rpc_stats), assistant_rpc_stats_fill, NULL, 1};
    cfg[9] = (struct assistant_stats_tx_cfg){"assistant_stats_scache", NULL, 1, ASSISTANT_STATS_TX_SCACHE_TIMEPERIOD_USEC, zpn_assistant_scache_stats_description, sizeof(struct zpn_assistant_scache_stats), assistant_sticky_cache_stats_fill, NULL, 1};
    cfg[10] = (struct assistant_stats_tx_cfg){"assistant_stats_system_inventory", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_PERIOD_USEC, zpn_system_inventory_description, sizeof(struct zpn_system_inventory_stats), zpn_system_inventory_stats_fill, &asst_sys_stats, 1};
    cfg[11] = (struct assistant_stats_tx_cfg){"assistant_stats_comprehensive", NULL, 1, ASSISTANT_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC, zpn_assistant_comprehensive_stats_description, sizeof(struct zpn_assistant_comprehensive_stats), assistant_comprehensive_stats_fill, &available_cpus, 1};
    cfg[12] = (struct assistant_stats_tx_cfg){"assistant_stats_dns", NULL, 1, ASSISTANT_DNS_STATS_TIMEPERIOD_USEC, zpn_assistant_dns_stats_description, sizeof(struct zpn_assistant_dns_stats), assistant_dns_stats_fill, NULL, 1};
    cfg[13] = (struct assistant_stats_tx_cfg){"assistant_stats_ncache", NULL, 1, ASSISTANT_STATS_TX_NCACHE_TIMEPERIOD_USEC, zpn_assistant_ncache_stats_description, sizeof(struct zpn_assistant_ncache_stats), assistant_ncache_stats_fill, NULL, 1};
    cfg[14] = (struct assistant_stats_tx_cfg){"assistant_stats_admin_probe", NULL, 1, ASSISTANT_STATS_TX_ADMIN_PROBE_USEC, admin_probe_stats_description, sizeof(struct admin_probe_stats), assistant_admin_probe_stats_fill, NULL, 1};
    cfg[15] = (struct assistant_stats_tx_cfg){"assistant_stats_monitor", NULL, 1, ASSISTANT_STATS_TX_MONITOR_USEC, zpn_assistant_monitor_stats_description, sizeof(struct zpn_assistant_monitor_stats), assistant_monitor_stats_fill, NULL, 1};
    cfg[16] = (struct assistant_stats_tx_cfg){"assistant_stats_data_mtunnel", NULL, 1, ASSISTANT_STATS_TX_DATA_MTUNNEL_TIMEPERIOD_USEC, zpn_assistant_data_mtunnel_stats_description, sizeof(struct zpn_assistant_data_mtunnel_stats), assistant_data_mtunnel_stats_fill, NULL, 1};
    cfg[17] = (struct assistant_stats_tx_cfg){"assistant_stats_network_memory", NULL, 1, ASSISTANT_STATS_TX_SYSTEM_NETWORK_TIMEPERIOD_USEC, zpath_system_network_stats_description, sizeof(struct zpn_system_network_stats), zpn_system_network_stats_fill, NULL, 1};
    cfg[18] = (struct assistant_stats_tx_cfg){"assistant_stats_fproxy", NULL, 1, ASSISTANT_STATS_TX_FPROXY_USEC, zpn_assistant_fproxy_stats_description, sizeof(struct zpn_assistant_fproxy_stats), assistant_fproxy_stats_fill, NULL, 1};
    cfg[19] = (struct assistant_stats_tx_cfg){"assistant_stats_zvm", NULL, 1, ASSISTANT_STATS_TX_ZVM_TIMEPERIOD_USEC, zpn_assistant_zvm_stats_description, sizeof(struct zpn_assistant_zvm_stats), assistant_zvm_stats_fill, NULL, 1};
    cfg[20] = (struct assistant_stats_tx_cfg){"assistant_stats_upgrade", NULL, 1, ASSISTANT_STATS_TX_UPGRADE_TIMEPERIOD_USEC, zpn_assistant_upgrade_stats_description, sizeof(struct zpn_assistant_upgrade_stats), assistant_upgrade_stats_fill, NULL, 1};
    cfg[21] = (struct assistant_stats_tx_cfg){"assistant_stats_np_wireguard_stats", NULL, 1, NP_CONNECTOR_STATS_TX_WIREGUARD_STATS_USEC, np_connector_wireguard_stats_description, sizeof(struct np_connector_wireguard_stats), np_connector_wireguard_stats_fill, NULL, 1};

    cfg_ready = 1;

    res = zpath_debug_mem_stats_init(zpath_service_assistant);
    if(res) {
        ASSISTANT_LOG(AL_ERROR, "Initializing memory allocator stats failed: %s", zpath_result_string(res));
        return res;
    }

#ifdef __linux__
    {
        void* zpath_debug_mallinfo_buffer;
        zpath_debug_mallinfo_buffer = ASST_MALLOC(sizeof(struct zpath_debug_mallinfo));

        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "zpath_debug_mallinfo",
                                         AL_INFO,
                                         ASSISTANT_STATS_TX_MALLINFO_TIMEPERIOD_USEC,
                                         zpath_debug_mallinfo_description,
                                         zpath_debug_mallinfo_buffer,
                                         1,
                                         zpath_debug_mallinfo_fill,
                                         0)) {
            ASSISTANT_LOG(AL_ERROR, "Could not register zpath_debug_mallinfo_app stats");
            res = ZPATH_RESULT_ERR;
            goto done;
        }

        void* zpath_system_sysinfo_buffer;
        zpath_system_sysinfo_buffer = ASST_MALLOC(sizeof(struct zpath_system_sysinfo));

        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "zpath_system_sysinfo",
                                         AL_INFO,
                                         ASSISTANT_STATS_TX_SYSINFO_TIMEPERIOD_USEC,
                                         zpath_system_sysinfo_description,
                                         zpath_system_sysinfo_buffer,
                                         1,
                                         zpath_system_sysinfo_fill,
                                         &available_cpus)) {
            ASSISTANT_LOG(AL_ERROR, "Could not register zpath_system_sysinfo stats");
            res = ZPATH_RESULT_ERR;
            goto done;
        }
    }
#endif

    {
        size_t                              cfg_iter;
        void*                               obj;

        for (cfg_iter = 0; cfg_iter < ASSISTANT_STATS_TX_MAX_OBJECTS; cfg_iter++) {
            if (0 == cfg[cfg_iter].enabled) {
                continue;
            }
            obj = ASST_CALLOC(cfg[cfg_iter].size_of_object);
            if (!argo_log_register_structure(argo_log_get("statistics_log"), cfg[cfg_iter].l_name, AL_INFO,
                                             cfg[cfg_iter].interval_us, cfg[cfg_iter].argo_description, obj,
                                             cfg[cfg_iter].log_immediate, cfg[cfg_iter].pre_log_cb,
                                             cfg[cfg_iter].pre_log_cb_cookie)) {
                ASSISTANT_LOG(AL_ERROR, "Could not register %s stats", cfg[cfg_iter].l_name);
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        }
    }

    global_assistant.stats_upload_reader = argo_log_read(zpath_stats_collection, "stats_upload", 0, 1,
                                                         assistant_stats_tx_reader_cb, NULL, 0, NULL,
                                                         60 * 1000 * 1000);
    if (!global_assistant.stats_upload_reader) {
        ASSISTANT_LOG(AL_ERROR, "Could not create stats log upload reader");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    /*
     * Write the stats logs to stats.log file in local disk - not to be enabled in production(but what if we
     * enable in production, wouldn't it help debugging if customer is willing to upload the file? If we choose do it,
     * then we should make sure that we delete logs that are X days old to make sure the disk is not blown out of
     * space).
     *
     * Note that stats_upload_reader takes a subset of stats_file_read as assistant_stats_tx_reader_cb()
     * filters most of the noisy stats from being uploaded.
     */
    if (global_assistant.stats_log_to_disk_enabled) {

        struct argo_log_file *stats_file;
        stats_file = argo_log_file_create(zpath_stats_collection, "stats.log", "stats.log", 1024 * 1024 * 1024,
                                          argo_serialize_binary);
        if (!stats_file) {
            ASSISTANT_LOG(AL_WARNING, "Could not create stats log file named stats.log");
        } else {
            global_assistant.stats_file_reader = argo_log_read(zpath_stats_collection, "stats_log_file", 0, 1,
                                                               argo_log_file_callback, NULL, stats_file,
                                                               NULL, 60 * 1000 * 1000);
            if (!global_assistant.stats_file_reader) {
                ASSISTANT_LOG(AL_WARNING, "Could not create file based stats log reader");
            }
        }
    }

    if (!(assistant_stats_tx_stats_description = argo_register_global_structure(ASSISTANT_STATS_TX_STATS_HELPER))) {
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (zpath_debug_add_read_command("Dump info related to stats TX operation.",
                               "/assistant/stats/tx/stats/dump", assistant_stats_tx_dump, NULL,
                               NULL)) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/stats/tx/stats/dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (zpath_debug_add_write_command("Set the rate of comprehensive stats log transmit",
                                "/assistant/stats/tx/comprehensive_stats",
                                assistant_stats_tx_comprehensive_stats_set_interval,
                                NULL,
                                "interval_us", "New interval for stats transmit",
                                NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/stats/tx/comprehensive_stats");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

done:
    return res;
}
