/*
 * assistant_site.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * All the site/site controller config coming via zpn_site/zpn_site_controller/zpn_site_controller_version table.
 */

#include "zpn_assistantd/assistant_site.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_rpc.h"
#include "zpn_assistantd/assistant_control.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn/zpn_private_broker.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zvm/zvm.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_firedrill_site.h"

extern struct zpn_firedrill_stats asst_firedrill_stats_obj;

static struct assistant_sitec_connection_stats {                                /* _ARGO: object_definition */
    uint64_t conn_cb_connected;                                                 /* _ARGO: integer */
    uint64_t conn_cb_disconnected;                                              /* _ARGO: integer */
    uint64_t conn_unblock_cb;
    uint64_t status;                                                   /* _ARGO: integer */
} stats;

static int assistant_site_info_dump(struct zpath_debug_state* request_state,
                                    const char**              query_values,
                                    int                       query_value_count,
                                    void*                     cookie) {
    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    if (global_assistant.site_gid == 0) {
        ZDP("Assistant is not part of any site.\n");
    } else {
        ZDP("customer gid:%"PRId64"\nsite gid: %"PRId64"\nsite status: %s\nsite offline domain exists: %"PRId64"\nsite offline domain: %s\nreenrollment period: %"PRId64"\nsitec_preferred: %d\nmax_allowed_downtime_s: %"PRId64"\nsitec_preferred_backdoor: %d\n",
            global_assistant.customer_gid,
            global_assistant.site_gid,
            global_assistant.site_is_active ? "active" : "inactive",
            global_assistant.is_site_offline_domain_exists,
            global_assistant.site_offline_domain,
            global_assistant.reenroll_period,
            global_assistant.sitec_preferred,
            global_assistant.max_allowed_downtime_s,
            global_assistant.sitec_preferred_backdoor);
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_site_control_stats_dump(struct zpath_debug_state* request_state,
                                             const char**              query_values,
                                             int                       query_value_count,
                                             void*                     cookie) {
    ZDP("conn_cb_connected: %"PRId64"\nconn_cb_disconnected: %"PRId64"\nconn_unblock_cb: %"PRId64"\n",
        stats.conn_cb_connected, stats.conn_cb_disconnected, stats.conn_unblock_cb);

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_site_flip_stats_dump(struct zpath_debug_state* request_state,
                                          const char**              query_values,
                                          int                       query_value_count,
                                          void*                     cookie) {
    struct {
        const char *label;
        struct fohh_connection *f_conn;
    } items[] = {
        {"actl to sitec", global_assistant.sitec_control, },
        {"acfg", get_cfg_channel(), },
        {"aovd", get_ovd_channel(), },
        {"astats", global_assistant.broker_stats, },
        {"log event", assistant_log_tx_get_event_channel(), },
        {"log inspection", assistant_log_tx_get_inspection_channel(), },
        {"log app inspection", assistant_log_tx_get_app_inspection_channel(), },
        {"log ptag", assistant_log_tx_get_ptag_channel(), },
    };

    for (int i = 0; i < sizeof(items) / sizeof(items[0]); ++i) {
        ZDP("%26s: to_sitec %"PRIu64", to_broker %"PRIu64"\n",
                items[i].label,
                items[i].f_conn ? fohh_connection_get_nr_switch_to_sitec(items[i].f_conn) : 0,
                items[i].f_conn ? fohh_connection_get_nr_switch_to_broker(items[i].f_conn) : 0);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_site_set_sitec_preferred_backdoor(struct zpath_debug_state* request_state,
                                                       const char**              query_values,
                                                       int                       query_value_count,
                                                       void*                     cookie) {
    int sitec_preferred_backdoor = 0;
    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    sitec_preferred_backdoor = global_assistant.sitec_preferred_backdoor = !global_assistant.sitec_preferred_backdoor;
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    ZDP("sitec_preferred_backdoor is set to %d\n", sitec_preferred_backdoor);

    return ZPATH_RESULT_NO_ERROR;
}

static int asst_firedrill_stats_obj_dump(struct zpath_debug_state*   request_state,
                                    const char **               query_values,
                                    int                         query_value_count,
                                    void*                       cookie) {

    struct zpn_firedrill_stats assistant_stats;
    assistant_stats.firedrill_triggered_count = __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_triggered_count, 0);
    assistant_stats.firedrill_completed_count = __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_completed_count, 0);
    assistant_stats.firedrill_cmdline_disable_count = __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_cmdline_disable_count, 0);
    assistant_stats.firedrill_transit_count = __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_transit_count, 0);

    ZDP("Firedrill stats:\n");
    ZDP("Triggered:%d\n", assistant_stats.firedrill_triggered_count);
    ZDP("Completed:%d\n", assistant_stats.firedrill_completed_count);
    ZDP("Transit triggered: %d\n",assistant_stats.firedrill_transit_count);
    ZDP("CmdLine disabled:%d\n", assistant_stats.firedrill_cmdline_disable_count);
    return ZPATH_RESULT_NO_ERROR;
}




static int asst_firedrill_status(struct zpath_debug_state* request_state,
                                const char **             query_values,
                                int                       query_value_count,
                                void*                     cookie)
{
    int firedrill_status = global_assistant.firedrill_status;
    char *ptr;
    switch(firedrill_status) {
        case ZPN_ASSISTANT_FIREDRILL_DISABLED:
            ptr = "disabled";
            break;
        case ZPN_ASSISTANT_FIREDRILL_ENABLED:
            ptr = "enabled";
            break;
        case ZPN_ASSISTANT_FIREDRILL_TRANSIT:
            ptr = "transit";
            break;
        default:
            ptr = "invalid";
            break;
    }
    ZDP("Firedrill status %s\n", ptr);
    return ZPN_RESULT_NO_ERROR;
}


static int assistant_sitec_conn_history_dump(struct zpath_debug_state*   request_state,
                                             const char **               query_values,
                                             int                         query_value_count,
                                             void*                       cookie) {
    char* history_str;

    history_str = ASST_MALLOC(sizeof(char) * FOHH_HISTORY_STR_MIN_LEN);

    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    if (!global_assistant.sitec_control) {
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
        ZDP("Assistant-Sitec control connection does not exist.\n");
        goto done;
    }
    fohh_history_get_str(global_assistant.sitec_control, history_str, FOHH_HISTORY_STR_MIN_LEN);
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    ZDP("%s", history_str);
done:
    ASST_FREE(history_str);
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_sitec_control_conn_unblock_cb(struct fohh_connection*      connection,
                                                   enum fohh_queue_element_type element_type,
                                                   void*                        cookie) {
    ASSISTANT_LOG(AL_NOTICE, "fohh unblock on assistant-sitec connection");
    __sync_add_and_fetch_8(&(stats.conn_unblock_cb), 1);
    return ZPATH_RESULT_NO_ERROR;
}

void assistant_sitec_control_tx_status()
{
    struct zpn_assistant_status_report status_data;

    assistant_rpc_status_report_fill(&status_data);
    zpn_send_zpn_assistant_status_report(global_assistant.sitec_control,
                                         fohh_connection_incarnation(global_assistant.sitec_control), &status_data);

    stats.status++;
}

static void zpn_asst_sitec_environment_report_update(void *cookie1, void *cookie2)
{
    (void)(cookie1);
    (void)(cookie2);

    struct zpn_asst_environment_report data;
    memset(&data, 0, sizeof(data));
    struct fohh_connection *connection = cookie1;

    data.sarge_version = assistant_state_get_sarge_version();
    data.configured_cpus = assistant_state_get_configured_cpus();
    data.available_cpus = assistant_state_get_available_cpus();
    zpn_send_zpn_asst_environment_report(connection, fohh_connection_incarnation(connection), &data);
    zevent_defer(zpn_asst_sitec_environment_report_update, connection, NULL, ASSISTANT_ENVIRONMENT_REPORT_UPDATE_US);
}

static int assistant_site_firedrill_disable_cb(void *argo_cookie_ptr,
                                                void *argo_structure_cookie_ptr,
                                                struct argo_object *object)
{
    struct zpn_sitec_firedrill_exit *firedrill_obj = object->base_structure_void;

    ASSISTANT_LOG(AL_ERROR, "firedrill assistant_site_firedrill_disable_cb entered ");

    if(firedrill_obj->firedrill_exit && global_assistant.firedrill_status) {
        ASSISTANT_LOG(AL_ERROR, "received firedrill exit cmd from pcc");

        /* disconnect from sitec and connect to cloud */
        if(global_assistant.asst_firedrill_timer) {
            event_del(global_assistant.asst_firedrill_timer);
        }

        zpn_assistant_switch_to_cloud();

        /* update the firedrill state */
        global_assistant.firedrill_status = ZPN_ASSISTANT_FIREDRILL_DISABLED;
        __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_cmdline_disable_count, 1);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_site_pbroker_load_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object)
{
    int cfg_add;
    int iter_publish;

    struct zpn_private_broker_load *load = object->base_structure_void;

    if (assistant_debug_log & ASSISTANT_DEBUG_SITE_BIT) {
        char dump[2000];
        if (ARGO_RESULT_NO_ERROR == argo_object_dump(object, dump, sizeof(dump), NULL, 1)) {
            ASSISTANT_DEBUG_SITE("zpn_private_broker_load data from site controller: %s", dump);
        } else {
            ASSISTANT_LOG(AL_ERROR, "Failed to dump zpn_private_broker_load object");
        }
    }

    /*
     * It is not ok to consume a dead pbroker. Let us consider a pbroker as dead after 7 days of it reporting the load.
     * 7 days because that it the amount of time we decided that pbroker can run without internet. Only with internet
     * pbroker load can be played to the connector(as it depends on s*-wally).
     */
    cfg_add = load->deleted ? 0 : 1;
    if (!is_zpn_drmode_enabled() && ((load->modified_time + ZPN_PRIVATE_BROKER_MAX_TIME_WITHOUT_INTERNET_S) < epoch_s())) {
        ASSISTANT_DEBUG_SITE("Pbroker load config is old, load gid(%"PRId64") deleted(%d) modified "
                             "time(%d), so delete the entry", load->gid, load->deleted, load->modified_time);
        cfg_add = 0;
    }

    if (!load->publish_count) {
        ASSISTANT_DEBUG_SITE("Private broker %ld has no published access (deleted = %d)", (long) load->gid, (int)load->deleted);
        goto done;
    }

    for (iter_publish = 0; iter_publish < load->publish_count ; iter_publish++) {
        assistant_pbroker_control_cfg_cb(load->gid, load->modified_time, load->publish[iter_publish], cfg_add);
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_sitec_control_conn_cb(struct fohh_connection*    connection,
                                           enum fohh_connection_state state,
                                           void*                      cookie) {
    int res;
    if (state == fohh_connection_connected) {
        struct zpn_asst_environment_report env_data;
        memset(&env_data, 0, sizeof(env_data));

        ASSISTANT_LOG(AL_NOTICE, "Assistant-Sitec control connection successfully connected: %s", fohh_description(connection));

        assistant_sitec_control_tx_status();
        __sync_add_and_fetch_8(&(stats.conn_cb_connected), 1);

        global_assistant.is_first_status_log_sent_to_sitec = 1;

        res = argo_register_structure(fohh_argo_get_rx(connection),
                                      zpn_private_broker_load_description,
                                      assistant_site_pbroker_load_cb,
                                      connection);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Could not register pbroker_load for control connection %s",
                          fohh_description(connection));
            return res;
        }

        res = argo_register_structure(fohh_argo_get_rx(connection),
                                      zpn_sitec_firedrill_exit_description,
                                      assistant_site_firedrill_disable_cb,
                                      connection);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Could not register for firedrill rpc %s", fohh_description(connection));
            return res;
        }

        env_data.sarge_version = assistant_state_get_sarge_version();
        env_data.configured_cpus = assistant_state_get_configured_cpus();
        env_data.available_cpus = assistant_state_get_available_cpus();
        zpn_send_zpn_asst_environment_report(connection, fohh_connection_incarnation(connection), &env_data);

        zevent_defer(zpn_asst_sitec_environment_report_update, connection, NULL, ASSISTANT_ENVIRONMENT_REPORT_UPDATE_US);

        zpn_send_zpn_tcp_info_report(connection, 0, connection, zvm_vm_type_to_str_concise(zvm_type_get()), g_asst_runtime_os);
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Assistant-Sitec control connection is closed: %s %s", fohh_description(connection), fohh_close_reason(connection));
        __sync_add_and_fetch_8(&(stats.conn_cb_disconnected), 1);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int assistant_site_dbg_init() {
    int result;

    result = zpath_debug_add_read_command("Get the site config information",
                                     "/assistant/cfg/site/info",
                                     assistant_site_info_dump,
                                     NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/cfg/site/info");
    }

    result = zpath_debug_add_read_command("Get the site control connection stats",
                                     "/assistant/site/control/stats",
                                     assistant_site_control_stats_dump,
                                     NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/control/stats");
    }

    result = zpath_debug_add_read_command("Get the site flipping stats of fohh connections",
                                     "/assistant/site/flip/stats",
                                     assistant_site_flip_stats_dump,
                                     NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/control/stats");
    }

    result = zpath_debug_add_read_command("Dump history of events happened to sitec control connection.",
                                     "/assistant/site/control/history/dump",
                                     assistant_sitec_conn_history_dump,
                                     NULL, NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/control/history/dump");
    }

    result = zpath_debug_add_write_command("Set/reset site controller as the primary source for ACs/PSEs. "
                                     "If sitec_preferred_backdoor is set to 1, sitec_preferred config will not be considered. "
                                     "If sitec_preferred_backdoor is set to 0, sitec_preferred config will be considered.",
                                     "/assistant/site/sitec/preferred_backdoor",
                                     assistant_site_set_sitec_preferred_backdoor,
                                     NULL, NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/sitec/preferred_backdoor");
    }

    result = zpath_debug_add_admin_command("Assistant firedrill stats.",
                                     "/assistant/site/firedrill_stats",
                                     asst_firedrill_stats_obj_dump,
                                     NULL, NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/firedrill_stats");
    }

    result = zpath_debug_add_admin_command("Assistant firedrill status.",
                                     "/assistant/site/firedrill_status",
                                     asst_firedrill_status,
                                     NULL, NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/site/firedrill_status");
    }
    return result;
}

void assistant_site_get_offline_domain_with_lock(char *offline_domain) {
    if (offline_domain && global_assistant.is_site_offline_domain_exists) {
        ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
        snprintf(offline_domain, strlen(global_assistant.site_offline_domain) + 1, "%s", global_assistant.site_offline_domain);
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    }
}

int64_t assistant_site_get_reenroll_period_with_lock() {
    int64_t site_reenroll_period = 0;
    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    site_reenroll_period = global_assistant.reenroll_period;
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    return site_reenroll_period;
}

int assistant_site_get_sitec_preferred_with_lock() {
    int sitec_preferred = 0;
    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    sitec_preferred = !global_assistant.sitec_preferred_backdoor ? global_assistant.sitec_preferred : global_assistant.sitec_preferred_backdoor;
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    return sitec_preferred;
}

void assistant_site_read_config_with_lock() {
    char line[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN + 24] = {0};
    char site_offline_domain_str[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    int64_t reenroll_period = 0;
    int site_is_active = 0;
    int sitec_preferred = 0;
    int64_t max_allowed_downtime_s = 0;
    FILE *fp;

    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    if (global_assistant.site_offline_domain != NULL) {
        ASST_FREE(global_assistant.site_offline_domain);
        global_assistant.site_offline_domain = NULL;
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    fp = fopen(ASSISTANT_SITE_CONFIG_FILE, "r");
    if (fp == NULL) {
        if (errno == ENOENT) {
            ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
            global_assistant.reenroll_period = 0;
            global_assistant.is_site_offline_domain_exists = 0;
            global_assistant.site_is_active = 0;
            global_assistant.sitec_preferred = 0;
            ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

            ZPATH_LOG(AL_NOTICE, "Site config not present");
        } else {
            ZPATH_LOG(AL_ERROR, "Failed to open file %s to read, err: %s", ASSISTANT_SITE_CONFIG_FILE, strerror(errno));
        }
        return;
    }

    while (fgets(line, sizeof(line), fp)) {
        /* Check if the line is not empty, a comment, or just a newline */
        if (line[0] != '\0' && line[0] != '#' && strcmp(line, "\n") != 0 && strcmp(line, "\r\n") != 0) {
            if (strstr(line, ASSISTANT_SITE_OFFLINE_DOMAIN_STR)) {
                sscanf(line, ASSISTANT_SITE_OFFLINE_DOMAIN_STR":%99s", site_offline_domain_str);
            } else if (strstr(line, ASSISTANT_SITE_REENROLL_PERIOD_STR)) {
                sscanf(line, ASSISTANT_SITE_REENROLL_PERIOD_STR":%"PRId64"", &reenroll_period);
            } else if (strstr(line, ASSISTANT_SITE_SITE_IS_ACTIVE_STR)) {
                sscanf(line, ASSISTANT_SITE_SITE_IS_ACTIVE_STR":%d", &site_is_active);
            } else if (strstr(line, ASSISTANT_SITE_SITEC_PREFERRED_STR)) {
                sscanf(line, ASSISTANT_SITE_SITEC_PREFERRED_STR":%d", &sitec_preferred);
            } else if (strstr(line, ASSISTANT_SITE_MAX_ALLOWED_DOWNTIME_S_STR)) {
                sscanf(line, ASSISTANT_SITE_MAX_ALLOWED_DOWNTIME_S_STR":%"PRId64"", &max_allowed_downtime_s);
            }
        }
    }

    fclose(fp);

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    if (strcmp(site_offline_domain_str, "") && strnlen(site_offline_domain_str, ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN)) {
        global_assistant.site_offline_domain = ASST_STRDUP(site_offline_domain_str, sizeof(site_offline_domain_str));
        global_assistant.is_site_offline_domain_exists = 1;
    }

    global_assistant.reenroll_period = reenroll_period;
    global_assistant.site_is_active = site_is_active;
    global_assistant.sitec_preferred = sitec_preferred;
    global_assistant.max_allowed_downtime_s = max_allowed_downtime_s;

    ZPATH_LOG(AL_NOTICE, "Successfully read cached site config, site offline domain: %s, reenrollment period: %d, site_is_active: %d, sitec_preferred: %d, max_allowed_downtime_s: %"PRId64"",
                          global_assistant.site_offline_domain, (int) global_assistant.reenroll_period, global_assistant.site_is_active, global_assistant.sitec_preferred, global_assistant.max_allowed_downtime_s);

    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
}

static int assistant_site_store_config_with_lock(int8_t site_is_active, char *site_offline_domain, int64_t reenroll_period, int sitec_preferred, int64_t max_allowed_downtime_s) {
    FILE *fp;
    int written;
    char out_str[ASSISTANT_SITE_CFG_FILE_MAX_SIZE] = {0};
    int need_reset = 0;
    int need_update = 0;

    char *s = out_str;
    char *e = out_str + ASSISTANT_SITE_CFG_FILE_MAX_SIZE;

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    /* Check if site_offline_domain or reenroll_period has changed */
    if ((!site_offline_domain && global_assistant.site_offline_domain) ||
        (!global_assistant.site_offline_domain && site_offline_domain && strcmp(site_offline_domain, "")) ||
        (global_assistant.site_offline_domain && site_offline_domain && strcmp(global_assistant.site_offline_domain, site_offline_domain) != 0)
        || global_assistant.site_is_active != site_is_active) {
        need_reset = 1;
        need_update = 1;
    } else if (global_assistant.reenroll_period != reenroll_period) {
        need_update = 1;
    } else if (global_assistant.sitec_preferred != sitec_preferred) {
        need_update = 1;
    } else if (global_assistant.max_allowed_downtime_s != max_allowed_downtime_s) {
        need_update = 1;
    }

    global_assistant.site_is_active = site_is_active;
    global_assistant.reenroll_period = reenroll_period;
    global_assistant.sitec_preferred = sitec_preferred;
    global_assistant.max_allowed_downtime_s = max_allowed_downtime_s;

    if (site_offline_domain == NULL || !strcmp(site_offline_domain, "")) {
        if (global_assistant.site_offline_domain) {
            ASST_FREE(global_assistant.site_offline_domain);
            global_assistant.site_offline_domain = NULL;
        }

        global_assistant.is_site_offline_domain_exists = 0;

        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

        if (need_update) {
            if (unlink(ASSISTANT_SITE_CONFIG_FILE) == 0) {
                ZPATH_LOG(AL_NOTICE, "Successfully deleted site config file");
            } else {
                /* Check if the error is ENOENT (no such file or directory) */
                if (errno != ENOENT) {
                    ZPATH_LOG(AL_ERROR, "Cannot delete site config file, error: %s", strerror(errno));
                }
            }
        }

        return need_reset;
    }

    s += sxprintf(s, e, "%s:%s\n", ASSISTANT_SITE_OFFLINE_DOMAIN_STR, site_offline_domain);
    s += sxprintf(s, e, "%s:%"PRId64"\n", ASSISTANT_SITE_REENROLL_PERIOD_STR, reenroll_period);
    s += sxprintf(s, e, "%s:%d\n", ASSISTANT_SITE_SITE_IS_ACTIVE_STR, site_is_active);
    s += sxprintf(s, e, "%s:%d\n", ASSISTANT_SITE_SITEC_PREFERRED_STR, sitec_preferred);
    s += sxprintf(s, e, "%s:%"PRId64"\n", ASSISTANT_SITE_MAX_ALLOWED_DOWNTIME_S_STR, max_allowed_downtime_s);

    if (global_assistant.site_offline_domain) {
        ASST_FREE(global_assistant.site_offline_domain);
        global_assistant.site_offline_domain = NULL;
    }

    global_assistant.site_offline_domain = ASST_STRDUP(site_offline_domain, strlen(site_offline_domain));
    global_assistant.is_site_offline_domain_exists = 1;
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    if (need_update) {
        fp = fopen(ASSISTANT_SITE_CONFIG_FILE, "w");
        if (fp == NULL) {
            ASSISTANT_LOG(AL_ERROR, "Failed to open file %s to write, err: %s", ASSISTANT_SITE_CONFIG_FILE, strerror(errno));
            return need_reset;
        }
        written = fwrite(out_str, strlen(out_str), 1, fp);
        if (written != 1) {
            ASSISTANT_LOG(AL_ERROR, "Failed to write site config to file, err: %s", strerror(errno));
        } else {
            ASSISTANT_LOG(AL_NOTICE, "Successfully updated local cache, site_offline_domain (%s), site_is_active (%d), reenroll_period (%d)",
                                    site_offline_domain, site_is_active, (int) reenroll_period);
        }
        fclose(fp);
    }
    return need_reset;
}

int assistant_site_default_endpoint_reachable() {
    int status = 0;
    struct fohh_connection *actl = global_assistant.broker_control;

    if (actl && fohh_get_state(actl) == fohh_connection_connected) {
        status = 1;
    }

    return status;
}

int64_t assistant_site_get_max_allowed_downtime_with_lock() {
    int64_t result = 0;

    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    result = global_assistant.max_allowed_downtime_s;
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    return result;
}

int assistant_site_sitec_reachable() {
    int status = 0;
    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    if (global_assistant.sitec_control && fohh_get_state(global_assistant.sitec_control) == fohh_connection_connected) {
        status = 1;
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    return status;
}

static void assistant_site_update_offline_domain(struct fohh_connection* connection, char* offline_domain) {
    if (fohh_connection_update_site_offline_domain(connection, offline_domain) != FOHH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "Couldn't update site offline domain for connection %s", fohh_description(connection));
    }
}

static void assistant_site_update_offline_domain_all(char* offline_domain) {
    assistant_site_update_offline_domain(get_cfg_channel(), offline_domain);

    assistant_site_update_offline_domain(get_ovd_channel(), offline_domain);

    assistant_site_update_offline_domain(global_assistant.broker_stats, offline_domain);

    assistant_site_update_offline_domain(assistant_log_tx_get_event_channel(), offline_domain);

    assistant_site_update_offline_domain(assistant_log_tx_get_inspection_channel(), offline_domain);

    assistant_site_update_offline_domain(assistant_log_tx_get_app_inspection_channel(), offline_domain);

    assistant_site_update_offline_domain(assistant_log_tx_get_ptag_channel(), offline_domain);
}

int assistant_site_fohh_connection_sanity_callback(struct fohh_connection* connection) {
    /* Sanity check 1: switch connection between siteC or default broker if required */
    ASSISTANT_DEBUG_SITE("SITE_CTL: [%s], sitec_reachable[%d], def_ep_reachable[%d], sitec_preferred[%d], sitec_preferred_backdoor[%d]",
                         fohh_description(connection),
                         assistant_site_sitec_reachable(),
                         assistant_site_default_endpoint_reachable(),
                         global_assistant.sitec_preferred,
                         global_assistant.sitec_preferred_backdoor);
    fohh_connection_site_state_check(connection, global_assistant.site_is_active, assistant_site_sitec_reachable(), assistant_site_default_endpoint_reachable(), assistant_site_get_sitec_preferred_with_lock(), assistant_site_get_max_allowed_downtime_with_lock(), NULL);
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_site_create_site_control_connection(const char *offline_domain) {
    char sitec_name[2*ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    char sni_customer_domain_ctl[2*ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    char sitec_stats_name[2*ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    char default_remote_address_name[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};

    snprintf(sitec_name, sizeof(sitec_name), "%s.%s", ASSISTANT_SITE_DOMAIN_PREFIX, offline_domain);
    snprintf(default_remote_address_name, sizeof(default_remote_address_name), "%s.%s", ASSISTANT_SITE_DOMAIN_PREFIX, offline_domain);

    ASSISTANT_LOG(AL_NOTICE, "Initing sitec control connection, offline domain: %s", offline_domain);

    /* V2 or V3 Style SNI: <assistant_id>.actl.<offline_domain> */
    snprintf(sni_customer_domain_ctl, sizeof(sni_customer_domain_ctl), "%ld.actl.%s", (long)global_assistant.gid, offline_domain);

    ASSISTANT_LOG(AL_NOTICE, "SNI for sitec control connection(%s)", sni_customer_domain_ctl);

    snprintf(sitec_stats_name, sizeof(sitec_stats_name), "brk-ctl-%s", offline_domain);

    global_assistant.sitec_control = fohh_client_create(FOHH_WORKER_ZPN_ACTL,
                                                        sitec_stats_name,
                                                        argo_serialize_binary,
                                                        fohh_connection_style_argo,
                                                        0,
                                                        NULL,
                                                        assistant_sitec_control_conn_cb,
                                                        NULL,
                                                        assistant_sitec_control_conn_unblock_cb,
                                                        NULL,
                                                        sitec_name,
                                                        sni_customer_domain_ctl,
                                                        offline_domain,
                                                        htons(ZPN_ASSISTANT_SITEC_PORT),
                                                        global_assistant.assistant_to_private_cloud_ctx,
                                                        1,
                                                        ZPN_ASSISTANT_SITEC_RX_TIMEOUT_S);

    if (!global_assistant.sitec_control) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize assistant-sitec control connection");
        return ZPATH_RESULT_ERR;
    }

    fohh_set_sticky(global_assistant.sitec_control, 1);
    fohh_suppress_connection_event_logs(global_assistant.sitec_control);
    fohh_history_enable(global_assistant.sitec_control);
    fohh_connection_set_dynamic_cookie(global_assistant.sitec_control, NULL);
    /*
     * the max timeout to flip to broker is defined as 60s, so make sure the backoff is smaller
     * that it so that sitec connection has the chance to be retried.
     */
    fohh_set_max_backoff(global_assistant.sitec_control, ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S >> 1);
    fohh_connection_site_init(global_assistant.sitec_control, offline_domain, ASSISTANT_SITE_DOMAIN_PREFIX, default_remote_address_name, 1, CTL_CONN_TO_SITEC /* for offline domain updation */,
                              1, global_assistant.assistant_to_private_cloud_ctx, global_assistant.assistant_to_public_cloud_ctx);
    fohh_connection_monitor_sanity(global_assistant.sitec_control,
                                   assistant_site_fohh_connection_sanity_callback,
                                   ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    return ZPATH_RESULT_NO_ERROR;
}

void assistant_site_setup_sitec_connection(char *site_offline_domain) {
    int res;
    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
    if (!site_offline_domain || strcmp(site_offline_domain, "") == 0 || !global_assistant.site_is_active) {
        if (global_assistant.sitec_control) {
            fohh_connection_delete_async(global_assistant.sitec_control, fohh_connection_incarnation(global_assistant.sitec_control), FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_REMOVED);
            global_assistant.sitec_control = NULL;
        }
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
        return;
    }

    if (global_assistant.sitec_control) {
        fohh_connection_update_site_offline_domain(global_assistant.sitec_control, site_offline_domain);
    } else {
        res = assistant_site_create_site_control_connection(site_offline_domain);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Couldn't set up control connection with site controller, error: %s", zpath_result_string(res));
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
}

void assistant_site_cfg_process(int64_t site_gid) {
    int res;
    int need_reset;
    struct zpn_site *site = NULL;
    struct zpn_ddil_config *ddil_config = NULL;
    size_t row_count = 1;
    char *offline_domain = NULL;
    int64_t reenroll_period = 0;
    int sitec_preferred = 0;
    char site_offline_domain[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    int64_t max_allowed_downtime_s = 0;
    int8_t site_is_active = global_assistant.site_is_active;

    if (site_gid != 0) {
        res = zpn_site_get_by_gid(site_gid, &site, 1, NULL, NULL, 0);
        if (res) {
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ASSISTANT_LOG(AL_ERROR, "Unable to fetch site info for gid: %"PRId64"", site_gid);
            }
            return;
        }

        site_is_active = !site->deleted && site->enabled;

        res = zpn_ddil_config_get_by_customer_gid(global_assistant.customer_gid, &ddil_config, &row_count, NULL, 0, 0);
        if (res) {
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ASSISTANT_LOG(AL_ERROR, "Unable to fetch ddil config error: %s", zpn_result_string(res));
            }
            return;
        }
    }

    offline_domain = (ddil_config) ? ddil_config->offline_domain : NULL;
    reenroll_period = (site) ? site->reenroll_period : 0;
    sitec_preferred = (site) ? site->sitec_preferred : 0;
    max_allowed_downtime_s = (ddil_config) ? ddil_config->max_allowed_downtime_s : 0;

    need_reset = assistant_site_store_config_with_lock(site_is_active, offline_domain, reenroll_period, sitec_preferred, max_allowed_downtime_s);
    assistant_site_get_offline_domain_with_lock(site_offline_domain);
    if (need_reset) {
        assistant_site_setup_sitec_connection(site_offline_domain);
    }
    assistant_site_update_offline_domain_all(site_offline_domain);
}

int assistant_site_control_init() {
    int res = ZPATH_RESULT_NO_ERROR;
    char site_offline_domain[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};

    assistant_site_get_offline_domain_with_lock(site_offline_domain);

    if (strcmp(site_offline_domain, "") != 0 && global_assistant.site_is_active) {
        res = assistant_site_create_site_control_connection(site_offline_domain);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Couldn't set up control connection with site controller, error: %s", zpath_result_string(res));
        }
    }
    return res;
}


int assistant_site_firedrill_config_fetch_callback(void *response_callback_cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    int64_t request_id,
                                                    int row_count)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_firedrill_site *firedrill_config = (struct zpn_firedrill_site *)response_callback_cookie;


    res = zpn_firedrill_site_by_site_gid(global_assistant.site_gid, global_assistant.customer_gid, assistant_site_firedrill_config_fetch_callback, &firedrill_config);
    if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
        ASSISTANT_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ASSISTANT_LOG(AL_ERROR, "firedrill config fetch asynchronous site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
        return ZPN_RESULT_NO_ERROR;
    }

    if(global_assistant.firedrill_status == ZPN_ASSISTANT_FIREDRILL_ENABLED) {
        ASSISTANT_LOG(AL_ERROR, "firedrill already activated");
        return ZPN_RESULT_NO_ERROR;
    }

    if(firedrill_config->firedrill_interval_s) {
        /* activate the timer */
        if(zpn_assistant_firedrill_timer_activate(firedrill_config->firedrill_interval_s)) {
            ASSISTANT_LOG(AL_ERROR, "firedrill timer activation failed");
            return ZPN_RESULT_ERR;
        }
       /* delete all the broker connections */
       zpn_assistant_switch_to_pcc();
    }
    return ZPN_RESULT_NO_ERROR;
}

static int assistant_site_row_callback(void *cookie,
                                        struct wally_registrant *registrant,
                                        struct wally_table *table,
                                        struct argo_object *previous_row,
                                        struct argo_object *row,
                                        int64_t request_id)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpn_site *site = row->base_structure_void;

    // if (zpn_debug_get(ASSISTANT_DEBUG_CFG_BIT)) {
    //     char dump[8000];
    //     if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
    //         ASSISTANT_DEBUG_LOG("Row callback: %s", dump);
    //     }
    // }
    if (site->firedrill_enabled && global_assistant.firedrill_status == ZPN_ASSISTANT_FIREDRILL_DISABLED ) {
        ASSISTANT_LOG(AL_INFO, "firedrill triggered for customer: %"PRId64"", site->customer_gid);
        /* get the firedrill config for this site using the site_gid */
        struct zpn_firedrill_site *firedrill_config;
        res = zpn_firedrill_site_by_site_gid(global_assistant.site_gid,
                                             global_assistant.customer_gid,
                                             assistant_site_firedrill_config_fetch_callback,
                                             &firedrill_config);
        if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
            ASSISTANT_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
            return ZPN_RESULT_ERR;
        } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ASSISTANT_LOG(AL_ERROR, "firedrill config fetch asynchronous site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
            return ZPN_RESULT_NO_ERROR;
        }
        if(firedrill_config->firedrill_interval_s) {
            if(zpn_assistant_firedrill_timer_activate(firedrill_config->firedrill_interval_s)) {
                ASSISTANT_LOG(AL_ERROR, "firedrill timer creation failed");
                return ZPATH_RESULT_ERR;
            }
            /* delete all the broker connections */
            zpn_assistant_switch_to_pcc();
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}


static int assistant_firedrill_row_callback(void *cookie,
    struct wally_registrant *registrant,
    struct wally_table *table,
    struct argo_object *previous_row,
    struct argo_object *row,
    int64_t request_id)
{
    if (zpn_debug_get(ASSISTANT_DEBUG_CFG_BIT)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_ERROR, "Row callback: %s", dump);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}


int assistant_cfg_site_init(struct wally *asst_wally) {
    int result;

    result = zpn_ddil_config_init(asst_wally, global_assistant.customer_gid, NULL, 0 /* no fully load */, 0);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Unable to init zpn_ddil_config table for customer_gid(%"PRId64") - %s",
                                global_assistant.customer_gid, zpath_result_string(result));
        goto done;
    }

    result = zpn_firedrill_site_init(asst_wally, global_assistant.customer_gid, assistant_firedrill_row_callback, 0, 1);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Could not init zpn_site_firedrill table");
        goto done;
    }

    result = zpn_site_init(asst_wally, global_assistant.customer_gid, assistant_site_row_callback, 0 /* no fully load */, 0);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Unable to init zpn_site table for customer_gid(%"PRId64") - %s",
                                global_assistant.customer_gid, zpath_result_string(result));
        goto done;
    }

    result = assistant_site_dbg_init();
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Unable to init debug handlers for site configs customer_gid(%"PRId64") - %s",
                                global_assistant.customer_gid, zpath_result_string(result));
        goto done;
    }



done:
    return result;
}
