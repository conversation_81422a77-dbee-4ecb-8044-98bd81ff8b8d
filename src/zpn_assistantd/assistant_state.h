/*
 * assistant_state.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_STATE_H_
#define _ASSISTANT_STATE_H_

#include "zpath_lib/zpath_debug.h"
#include "zevent/zevent.h"
#include "ztlv/zpn_tlv.h"

#define ASST_MALLOC(x) zpath_malloc(assistant_state_get_allocator(), x, __LINE__, __FILE__)
#define ASST_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ASST_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ASST_CALLOC(x) zpath_calloc(assistant_state_get_allocator(), x, __LINE__, __FILE__)
#define ASST_STRDUP(x, y) zpath_strdup(assistant_state_get_allocator(), x, y, __LINE__, __FILE__)
#define MDBG_STR_LEN(mdbg_str) ((mdbg_str && mdbg_str[0]) ? (int)strlen(mdbg_str):0)
#define ASSISTANT_STATE_MAX_CAPABILITIES 6

/* Max initialization time for alternate cloud connections,
 * If connection is on alternate cloud and is uninitialized for more than this amount of time, reset to default cloud */
#define ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S 60
#define ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_RETRIES 3

#ifndef CGROUP_SUPER_MAGIC
#define CGROUP_SUPER_MAGIC  0x27e0eb
#endif

#ifndef CGROUP2_SUPER_MAGIC
#define CGROUP2_SUPER_MAGIC  0x63677270
#endif

#ifndef TMPFS_MAGIC
#define TMPFS_MAGIC  0x01021994
#endif

extern int effective_asst_configured_capabilities[ASSISTANT_STATE_MAX_CAPABILITIES];
extern int permitted_asst_configured_capabilities[ASSISTANT_STATE_MAX_CAPABILITIES];
extern char g_asst_runtime_os[FOHH_MAX_NAMELEN];

enum asst_configured_capabilities {
    cap_net_admin = 0,
    cap_net_bind_service = 1,
    cap_net_raw = 2,
    cap_sys_boot = 3,
    cap_sys_nice = 4,
    cap_sys_time = 5,
};

int64_t assistant_state_get_inactive_time_cloud_s();
int64_t assistant_state_get_inactive_time_local_s();
int64_t assistant_state_get_current_time_cloud_s();
int64_t assistant_state_get_current_time_cloud_us();

void assistant_state_pause_evaluate();
int assistant_state_is_paused();
char* assistant_state_get_pause_reason();

int assistant_state_is_admin_probe_pause_for_restart_process();
void assistant_state_set_admin_probe_pause_for_process_restart();
int assistant_state_is_admin_probe_pause_for_restart_system();
void assistant_state_set_admin_probe_pause_for_system_restart();

int64_t assistant_state_get_max_allowed_connection_downtime_s();
char* assistant_state_get_uptime_str(char *buf, int buf_len);
int assistant_state_set_uptime_s();
int64_t assistant_state_get_uptime_s();

void assistant_state_set_derived_name(const char *asst_name);
char* assistant_state_get_derived_name();

void assistant_state_set_configured_name(const char *asst_name);
char* assistant_state_get_configured_name();

int assistant_state_set_cloud_name(const char *cloud_name);
char* assistant_state_get_cloud_name();

void assistant_state_set_customer_domain(const char *customer_domain);
char* assistant_state_get_customer_domain();
int assistant_state_is_dev_environment();

void assistant_state_set_next_restart_time_s(int64_t next_restart_time_cloud_s);
int64_t assistant_state_get_next_restart_time_s();
void assistant_state_set_time_lag_us(int64_t local_time_lags_cloud_time_delta_us);
int64_t assistant_state_get_time_lag_us();
int assistant_state_dump(struct zpath_debug_state *request_state, const char **query_values, int query_value_count,
                         void *cookie);
int assistant_state_init();
char* assistant_state_get_sarge_version();
int assistant_state_get_configured_cpus();
int assistant_state_get_available_cpus();
void assistant_state_set_fohh_threads(int threads);
uint32_t assistant_state_get_fohh_threads();
int assistant_state_system_inventory_stats_fill(void* cookie, int counter, void* structure_data);
const char * assistant_state_get_current_working_directory();
void assistant_state_set_health_evbase(struct event_base*);
struct event_base* assistant_state_get_health_evbase();
void assistant_state_set_reading_config();
void assistant_state_unset_reading_config();
int assistant_state_is_reading_config();
void assistant_state_set_health_zevbase(struct zevent_base*   asst_health_zbase);

void assistant_state_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed);
int  assistant_state_get_num_hw_id_changed();
int64_t* assistant_state_get_hw_id_changed_time_us();

/*
 * Get zevbase of health thread. Caller should assume that it can be NULL. This can happen when the health
 * thread is not yet fully initialized.
 */
struct zevent_base* assistant_state_get_health_zevbase();
struct zpath_allocator* assistant_state_get_allocator();
void assistant_state_set_pbroker_environment();
int assistant_state_is_pbroker_environment();
void assistant_state_set_asst_config_status(int is_config_enabled);
struct zpn_asst_state* assistant_state_get_zpn_asst_state_object();
void assistant_state_send_zpn_asst_state(struct zpn_tlv *tlv, int64_t tlv_incarnation);
void assistant_state_send_zpn_asst_state_on_fohh(struct fohh_connection *f_conn, int64_t fohh_incarnation);
void assistant_state_set_asst_grp_config_status(int is_atleast_one_grp_cfg_enabled);
int assistant_state_is_cfg_enabled();
uint16_t assistant_state_get_cpu_util(void);
uint16_t assistant_state_get_cpu_steal_perc(void);
int assistant_state_get_system_mem_util(void);
int assistant_state_get_process_mem_util(void);
int assistant_state_get_swap_config();
void assistant_state_set_swap_config(uint64_t swap);

void assistant_state_set_container_env();
int assistant_state_is_container_env();
void assistant_state_set_zscaler_os();
int assistant_state_is_zscaler_os();

int assistant_state_is_cgroup_enabled();
int assistant_state_get_system_loadavg(int load_avgs[], int num_avgs);

void assistant_state_read_alt_domain_config_with_lock();
int assistant_state_store_alt_domain_config_with_lock(int enabled, char *alt_cloud);
void assistant_state_delete_alt_domain_file();
/*
 * Reset all fohh connections due to alternate cloud feature toggle change
 * affects: control, config, config override, fohh_log, stats
 */
void assistant_state_reset_connections_alt_cloud_toggle(int use_default, int reset_pse_conns);
void assistant_state_set_alt_cloud_enabled(int64_t enabled);
void assistant_state_set_alt_domain(char* alt_cloud_domain);
int assistant_state_connection_is_on_alt_cloud(struct fohh_connection* connection);
int assistant_state_fohh_connection_sanity_callback_with_lock(struct fohh_connection* connection);
int assistant_state_get_alt_cloud_resets_count();
int assistant_state_get_alt_cloud_status();
void assistant_state_update_alt_cloud_status(int on_alt_cloud);
void assistant_state_get_alt_cloud_with_lock(char *alt_cloud);
int assistant_state_alt_cloud_reset_connection(struct fohh_connection* connection, int use_default);

void assistant_state_set_np_connector_disable();

void assistant_state_allocator_libevent_sys_mem_total_bytes_set(uint64_t bytes);
uint64_t assistant_state_allocator_libevent_sys_mem_total_bytes_get();

int64_t assistant_state_allocator_libevent_max_bytes_get();
int64_t assistant_state_allocator_libevent_used_bytes_get();
int64_t assistant_state_allocator_libevent_used_percent_get();
int64_t assistant_state_allocator_libevent_out_queue_allowed_bytes_get();

void
assistant_state_allocator_libevent_out_queue_set(int64_t allocator_libevent_out_queue_allowed_bytes);

void
assistant_state_allocator_libevent_out_queue_calc_and_update(int64_t connector_gid, int64_t *allocator_libevent_out_queue_allowed_bytes);


#endif // _ASSISTANT_STATE_H_
