/*
 * assistant_init.c. Copyright (C) 2014-2020 Zscaler Inc. All Rights Reserved.
 *
 * Assistant's init
 */

#include <arpa/inet.h>
#include "argo/argo_hash.h"
#include <unistd.h>
#include <event2/bufferevent.h>
#include "zhw/zhw_os.h"
#include "zcrypt/zcrypt.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn_pcap/zpn_pcap.h"
#include "zpn_pcap/zpn_pcap_lib.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "wally/wally_test_origin.h"

#include "zudp_conn/zudp_conn.h"

#include "zpn_zdx/zpn_zdx_combiner.h"
#include "zhealth/zhealth_probe_lib_thread.h"

#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_dr_interface.h"
#include "zpn_assistantd/zpn_assistant_health.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn_assistantd/assistant_app.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_stats.h"
#include "zpn_assistantd/assistant_stats_tx.h"
#include "zpn_assistantd/assistant_control.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_dns.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_cfg.h"
#include "zpn_assistantd/assistant_cfg_override.h"
#include "zpn_assistantd/assistant_cfg_assistant.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_assistantd/assistant_init.h"
#include "zpn_assistantd/assistant_config_override_desc.h"
#include "zpn_assistantd/assistant_site.h"
#include "zpn/zpn_zrdt_debug.h"
#include "zpn_waf/zpn_waf_debug.h"
#include "zpn_waf/zpn_waf_profile_manager.h"
#include "zpn_assistantd/assistant_broker.h"
#include "zpn/zpn_debug.h"
#include "zpn_inspection/zpn_inspection.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "zpn_waf/zpn_inspection_zsdefined_control.h"
#include "zpn_waf/zpn_waf_lib.h"
#include "zpn_zdx/zpn_zdx_encrypt.h"
#include "zpn_assistantd/assistant_admin_probe.h"
#include "zpn_assistantd/assistant_zdx.h"
#include "zpn_assistantd/assistant_waf_ssl.h"
#include "zpn_assistantd/ast_waf_sess_mgr.h"
#include "zpn/zpn_mconn_icmp.h"
#include "zpn/zpn_mconn_icmpv6.h"
#include "zpn_assistantd/assistant_rpc_tx.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn_assistantd/assistant_pbroker_control_tx.h"
#include "zpn_assistantd/zpn_assistant_two_hop_data_health.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpath_lib/zpath_upgrade_utils.h"

static struct assistant_init_stats {                                    /* _ARGO: object_definition */
    uint64_t place_holder;                                              /* _ARGO: integer */
} stats;
#include "zpn_assistantd/assistant_init_compiled_c.h"
#include "zpn/zpn_system.h"
#include "zpn_assistantd/assistant_sticky_cache.h"
#include "zpn_assistantd/assistant_ncache.h"

#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/np_command_probe.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_servergroup_server_relation.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn/zpn_public_cert.h"
#include "zpn_waf/zpn_inspection_profile.h"
#include "zpn_waf/zpn_inspection_profile_to_control.h"
#include "zvm/zvm.h"
#include "np_connector/np_connector.h"
#include "np_lib/np.h"
#include "np_lib/np_rpc.h"

static struct argo_structure_description*  assistant_init_stats_description;
struct zpn_assistant_state global_assistant;
struct wally *assistant_wally = NULL;
struct wally *assistant_gwally = NULL;
struct wally_test_origin *assistant_wally_test_origin = NULL;
struct wally_test_origin *assistant_gwally_test_origin = NULL;

int g_config_init_done = 0;

int write_rows_into_file(void *cookie, void *object, void *key, size_t key_len)
{
    int res;
    FILE *fp = cookie;
    struct wally_row *row = object;
    char *dump = NULL;

    dump = zpn_dr_allocate_buffer(ZPN_DR_MAX_BUF_SIZE);
    res = argo_object_dump(row->current_row, dump, ZPN_DR_MAX_BUF_SIZE, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping argo object while writing DR config into file, error: %s",
                          zpath_result_string(res));
        zpn_dr_free_buffer(dump);
        return ZPN_RESULT_ERR;
    }

    if (fwrite(dump, strlen(dump), 1, fp) != 1) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while writing json rows into DR config file");
        zpn_dr_free_buffer(dump);
        return ZPN_RESULT_ERR;
    }
    zpn_dr_free_buffer(dump);
    /* Insert a new line char into a file */
    if (fwrite("\n", 1, 1, fp) != 1) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while writing a new line into DR config file");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

int dump_wally_state(struct wally *wally_a, int is_append, struct zthread_info *z_info) {

    int result = ZPN_RESULT_NO_ERROR;
    FILE **config_fp = NULL;

    if (wally_a == NULL) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: wally is null for dumping wally config into a file");
        return ZPN_RESULT_BAD_DATA;
    }

    pthread_mutex_lock(&global_assistant.dr_handler_lock);
    config_fp = ASST_CALLOC(sizeof(FILE *) * get_cfg_file_category_size());

    ZPN_DR_CFG_DEBUG("dump_wally_state: wally_a: %p, is_append: %d, z_info: %p", wally_a, is_append, z_info);

    for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
        const char* filename = zpn_dr_lib_get_cfg_file_name_for_category(category_idx);
        config_fp[category_idx] = fopen(filename, is_append ? "a" : "w");
        if (config_fp[category_idx] == NULL) {
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error opening file(%s) for dumping wally state, error(%s)", filename, strerror(errno));
            result = ZPN_RESULT_ERR;
            goto done;
        }
    }

    for (int i = 0; i < wally_a->tables_count; i++) {
        int is_needed = 0;
        enum zpn_dr_file_category_e category = zpn_dr_file_category_unknown;
        // check whether table is needed for dr
        if (ZPN_DR_RESULT_NO_ERROR != zpn_dr_lib_is_db_cfg_table_needed_for_dr(wally_a->tables[i]->name, &is_needed, &category)) {
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while checking if the table(%s) is needed for dr", wally_a->tables[i]->name);
            result = ZPN_RESULT_ERR;
            goto done;
        }

        if (is_needed) {
            if (wally_a->tables[i]->integer_key) {
                int64_t walk_key = 0;
                zhash_table_walk(wally_a->tables[i]->key_rows_integer, &walk_key, write_rows_into_file, config_fp[(int)category]);
            } else {
                int64_t walk_key = 0;
                zhash_table_walk(wally_a->tables[i]->key_rows_string, &walk_key, write_rows_into_file, config_fp[(int)category]);
            }
            /*
             * tickle heartbeat after every table dump, ho harm in updating
             * to avoid unnecessary heartbeat failures
             */
            if (z_info) zthread_heartbeat(z_info);
        }
    }

done:
    if (config_fp) {
        for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
            if (config_fp[category_idx]) {
                fclose(config_fp[category_idx]);
                config_fp[category_idx] = NULL;
            }
        }

        ASST_FREE(config_fp);
        config_fp = NULL;
    }
    pthread_mutex_unlock(&global_assistant.dr_handler_lock);

    ZPN_DR_CFG_DEBUG("dump_wally_state: wally_a: %p, is_append: %d, z_info: %p - result: %s",
                     wally_a, is_append, z_info, zpath_result_string(result));

    return result;
}


int dump_dr_config(int force_dump_config, struct zthread_info *z_info) {
    int res;
    int cat_count = 0;
    enum zpn_dr_file_category_e categories[zpn_dr_file_category_other + 1];
    struct wally *wally_instances[2] = {assistant_wally, assistant_gwally};

    if (! force_dump_config) {
        zpn_dr_get_unmodified_categories(wally_instances, sizeof(wally_instances) / sizeof(wally_instances[0]), categories, &cat_count);

        if (!cat_count) {
            ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: Configurations for DR tables are recently modified, skipping the dumping !!");
            return ZPN_RESULT_CANT_WRITE;
        }

        ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: Configurations unchanged for few DR tables, hence dumping the config, number of categories to be dumped: %d", cat_count);
    }

    res = dump_wally_state(assistant_wally, 0, z_info);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping config wally state");
    }
    res = dump_wally_state(assistant_gwally, 1, z_info);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping config override wally state");
    }

    ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR config is written into files !");

    return res;
}

static int
assistant_init_debug_dump_all(struct zpath_debug_state*  request_state,
                              const char**               query_values,
                              int                        query_value_count,
                              void*                      cookie)
{
    char        jsonout[10000];

    ZDP("===assistant live states===\n");
    assistant_state_dump(request_state, query_values, query_value_count, cookie);
    assistant_app_debug_dump_all(request_state, query_values, query_value_count, cookie);

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_init_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_version_state_dump(struct zpath_debug_state*  request_state __attribute__((unused)),
                             const char**               query_values __attribute__((unused)),
                             int                        query_value_count __attribute__((unused)),
                             void*                      cookie __attribute__((unused)))
{
    ZDP("===assistant version state ===\n");
    if (global_assistant.version_data.client_version == zpn_client_version_invalid) {
        ZDP("%s\n", "Invalid");
    } else if (global_assistant.version_data.client_version == zpn_client_version_valid) {
        ZDP("%s\n", "Valid");
    } else {
        ZDP("%s\n", "Unknown");
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Record the time, of where this particular instance of the connector can be alive and when it should be killed.
 *
 * Assumption:
 * 1. global_assistant is already reset and ready to use.
 * 2. cert.pem is valid. This is correct as even before zpn_assistant_init() is called, enrollment is complete and
 * the new certs are in place.
 */
static void
assistant_init_cert_validity_counters(const char *cert_file_name)
{
    struct zcrypt_cert *cert;
    int64_t one_percent_time;

    cert = zcrypt_cert_read(cert_file_name);
    if (!cert) {
        ZPN_LOG(AL_ERROR, "Could not read cert file %s", cert_file_name);
        return;
    }
    global_assistant.cert_validity_start_time_cloud_s = zcrypt_cert_valid_from_epoch(cert);
    global_assistant.cert_validity_end_time_cloud_s = zcrypt_cert_valid_to_epoch(cert);
    zcrypt_cert_free(cert);

    one_percent_time  = (global_assistant.cert_validity_end_time_cloud_s - global_assistant.cert_validity_start_time_cloud_s)/100;
    global_assistant.cert_force_re_enroll_time_cloud_s = global_assistant.cert_validity_end_time_cloud_s -
                                                         (one_percent_time * (100 - ZPN_ASSISTANT_FORCE_RE_ENROLL_TIME_FACTOR));
}

void ast_dr_register_argo_objects()
{
    // Filter tables - Config connection
    argo_zpath_customer_init();
    argo_zpn_app_group_relation_init();
    argo_zpn_app_server_init();
    argo_zpn_application_init();
    argo_zpn_application_group_init();
    argo_zpn_application_group_application_mapping_init();
    argo_zpn_assistant_init();
    argo_zpn_assistant_group_init();
    argo_zpn_assistant_version_init();
    argo_zpn_assistantgroup_assistant_relation_init();
    argo_zpn_command_probe_init();
    argo_zpn_server_group_init();
    argo_zpn_server_group_assistant_group_init();
    argo_zpn_servergroup_server_relation_init();
    argo_zpn_private_broker_load_init();
    argo_zpn_private_broker_init();
    argo_zpn_private_broker_group_init();
    argo_zpn_private_broker_to_group_init();
    argo_zpn_inspection_application_init();
    argo_zpn_inspection_profile_init();
    argo_zpn_inspection_profile_to_control_init();

    // Filter tables - config override connection
    argo_zpath_config_override_init();
    argo_zpn_inspection_config_data_init();
}

/*
    Compare current running version with previously seen denied version.
    If current running version is the same as the denied version, exit...
*/
static void assistant_check_last_denied_version() {
    int ret = -1;
    char version[MAX_VERSION_STRING_LEN] = {0};

    if (zpn_version_control_denied_version_file_exists()) {
        ret = zpn_version_control_read_denied_version_file(version, MAX_VERSION_STRING_LEN);

        if (ret == ZPN_RESULT_NO_ERROR) {
            if (strcmp(version, ZPATH_VERSION) != 0) {
                ZPATH_LOG(AL_NOTICE, "current version does not %s match previous denied version %s, deleting denied_version file!", ZPATH_VERSION, version);
                ret = zpn_version_control_delete_denied_version_file();
                if (ret == ZPN_RESULT_ERR) {
                    global_assistant.version_data.denied_version_del_fail++;
                }
                global_assistant.version_data.restart_invalid_version++;
            } else {
                ZPATH_LOG(AL_CRITICAL, "current version matches previous denied version %s, Exiting!", version);
                sleep(1);
                exit(0);
            }
        } else {
            unlink(DENIED_VERSION_FILE);
            ZPATH_LOG(AL_NOTICE, "Unable to read version from %s file for version control enforcement on assistant, deleting the file", DENIED_VERSION_FILE);
        }
    }
}

static void assistant_validate_current_version() {

    int result;

    result = zpn_version_control_register_assistant_version_control_description();
    if (result) {
        ZPN_LOG(AL_CRITICAL, "Could not register descriptions for zpn_appc_version_control : %s", zpn_result_string(result));
        return;
    }

    if (zpn_version_control_assistant_feature_status(global_assistant.gid) == 0) {
        ZPN_LOG(AL_INFO, "Version control feature is disabled on assistant, continuing without version validation");
        return;
    }

    int64_t start_time = epoch_s();
    while (global_assistant.version_data.client_version == zpn_client_version_unknown) {
        if (zpn_version_control_assistant_feature_status(global_assistant.gid) == 0) {
            ZPN_LOG(AL_INFO, "Version control feature is disabled on assistant, continuing without version validation");
            return;
        }
        /* If appc does not receive reply to version_control msg within 10 secs, skip version validation */
        if (epoch_s() - start_time >= MAX_VERSION_CONTROL_CHECK_WAIT_TIME_S) {
            ZPN_LOG(AL_INFO, "Max time to receive version check msg exceeded, continuing without version validation");
            return;
        }
        ZPN_LOG(AL_WARNING, "Assistant version has not been verified. Checking again in 1 sec");
        sleep(1);
    }

    if (global_assistant.version_data.client_version == zpn_client_version_invalid) {
        if (zpn_version_control_denied_version_file_exists()) {
            ZPATH_LOG(AL_NOTICE, "file: %s already exists ...deleting it!", DENIED_VERSION_FILE);
            zpn_version_control_delete_denied_version_file();
        }
        if (zpn_version_control_create_denied_version_file()) {
            ZPATH_LOG(AL_NOTICE, "Unable to create the %s file!", DENIED_VERSION_FILE);
        }
        ZPN_LOG(AL_CRITICAL, "Assistant running incompatible version: %s. Exiting!!", ZPATH_VERSION);
        sleep(1);
        exit(0);
    } else {
        ZPN_LOG(AL_INFO, "Assistant running version: %s is compatible, continuing!", ZPATH_VERSION);
    }
    return;
}

struct zpn_assistant_state *
assistant_init(int64_t      assistant_gid,
               char*        customer_domain,
               const char*  cloud_name,
               const char*  broker,
               const char*  asst_name,
               const char*  cert_file_name,
               int          auto_upgrade_disabled,
               int          stats_log_to_disk,
               int          enroll_version,
               int          ut_mode,
               SSL_CTX      *public_cloud_ctx,
               SSL_CTX      *private_cloud_ctx,
               SSL_CTX      *public_cloud_ctx_dtls,
               SSL_CTX      *private_cloud_ctx_dtls,
               int          health_txn_prefer_local_cache_over_dsp_cache,
			   int          slow_asst_init,
               int          freebsd_compat_mode)
{
    int i;
    int res=0;
    static int initialized = 0;
    char *cfg_asst_name;

    if (!is_zpn_drmode_enabled()) {
        /* Check if current running version matches previous denied version*/
        assistant_check_last_denied_version();
    } else {
        ZPN_LOG(AL_INFO, "skipping version control enforcement in active dr mode");
    }

    /*
     * Enable the features as first thing to set the stage.
     */
    assistant_features_init();

    if (!customer_domain) return NULL;
    if (!cloud_name) return NULL;
    if (!assistant_gid) return NULL;

    if (__sync_fetch_and_add_4((&initialized), 1) == 0) {
        if (zpn_fohh_worker_state_init(zpath_service_assistant) != ZPN_RESULT_NO_ERROR) {
            return NULL;
        }
    }

    if (zpn_debug_add_allocators()) {
        ZPN_LOG(AL_ERROR, "Could not add zpn allocators");
        return NULL;
    }

    assistant_state_set_derived_name(asst_name);
    res=assistant_state_set_cloud_name(cloud_name);
    if (res==ZPATH_RESULT_ERR){
        ZPN_LOG(AL_ERROR, "Could not get zpa_cloud Config");
        return NULL;
    }
    assistant_state_set_customer_domain(customer_domain);
    assistant_state_read_alt_domain_config_with_lock();

    res = assistant_state_init();
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant state");
        return NULL;
    }
    zthread_record_cpu_time_since_last_heartbeat();

    res = assistant_broker_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init broker module");
        return NULL;
    }

    /*
     * simple_app_init have already init-ed role & build. We know the instance name only now and so let
     * us pass on that info to argo_log module.
     */
    if (argo_log_init(asst_name, assistant_gid, NULL, NULL, 1)) {
        ZPN_LOG(AL_ERROR, "Could not initialize argo library");
        return NULL;
    }
    assistant_log_init();

    global_assistant.gid = assistant_gid;
    global_assistant.enroll_version = enroll_version;
    global_assistant.assistant_to_public_cloud_ctx = public_cloud_ctx;
    global_assistant.assistant_to_private_cloud_ctx = private_cloud_ctx;
    global_assistant.assistant_to_public_cloud_ctx_dtls = public_cloud_ctx_dtls;
    global_assistant.assistant_to_private_cloud_ctx_dtls = private_cloud_ctx_dtls;
    global_assistant.freebsd_compat_mode = freebsd_compat_mode;
    global_assistant.cur_mode = invalid_connector_type;
    if (freebsd_compat_mode) {
        zhw_os_set_freebsd_compat_mode();
    }

    if (broker) {
        global_assistant.hardcoded_broker_name = ASST_STRDUP(broker, strlen(broker));
    } else {
        global_assistant.hardcoded_broker_name = NULL;
    }

    assistant_features_alt_cloud_cfg_monitor_init(assistant_gid);
    /* initialize forward proxy module if we are on Zscaler RHEL image and we are not on docker */
    if (zvm_vm_type_is_zscaler_rh_image() && !zvm_vm_type_is_docker()) {
        assistant_features_fproxy_cfg_monitor_init(assistant_gid);
    }

    /* This must be called before any fohh connection init */
    res = zpn_rpc_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register RPCs");
        return NULL;
    }

    res = np_rpc_stats_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register NP RPCs");
        return NULL;
    }

    res = argo_register_zpn_private_broker_load_desc();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register pbload description to recv from site controller");
        return NULL;
    }

    res = assistant_site_control_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init control connection to site controller");
        return NULL;
    }

    memset(&global_assistant.dr_stats, 0, sizeof(global_assistant.dr_stats));

    if (ut_mode) {
        assistant_features_enable_ut_mode();
    }
    if (health_txn_prefer_local_cache_over_dsp_cache) {
        assistant_features_enable_health_txn_prefer_local_cache_over_dsp_cache();
    }

    res = zpn_inspection_init(zpn_event_collection);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize inspection library : %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_waf_lib_init(zpn_event_collection, NULL, assistant_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize WAF library : %s", zpn_result_string(res));
        return NULL;
    }

    assistant_wally = assistant_cfg_conn_init(broker, &assistant_wally_test_origin);
    if (NULL == assistant_wally) {
        return NULL;
    }

    if (is_zpn_drmode_enabled()) {
        /* Register the expected argo objects before we start parsing the DR config files */
        ast_dr_register_argo_objects();
        /* Parse dr_wally config file (row by row for each table) and statically load JSON row object into appropriate tables */
        for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
            const char* filename = zpn_dr_lib_get_cfg_file_name_for_category(category_idx);
            if (strcmp(filename, "override_conf.json") == 0)
                continue;
            load_dr_config_to_wally_origin( filename,
                                    assistant_wally_test_origin,
                                    NULL);
        }

        /* Set property of test origin to automatically respond the dr_wally requests */
        wally_test_origin_autorespond(assistant_wally_test_origin, 1);
    }

    res = zpn_pcap_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_pcap");
        return NULL;
    }

    res = assistant_cfg_init(assistant_wally, assistant_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init config");
        return NULL;
    }

    res = zpn_pcap_lib_init(zpn_event_collection);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_pcap_lib");
        return NULL;
    }

    do {
        res = assistant_cfg_assistant_get_customer_gid(&global_assistant.customer_gid);
        if (res) {
            if (res == ZPN_RESULT_NOT_READY) {
                ZPN_LOG(AL_NOTICE, "Connector not yet connected to config channel(%s), please check connectivity, if this persists",
                        assistant_cfg_get_connection_description());
            } else {
                if (res != ZPN_RESULT_ASYNCHRONOUS) {
                    ZPN_LOG(AL_NOTICE, "Get connector returned %s, please contact customer support, if this persists",
                            zpn_result_string(res));
                } else {
                    ZPN_LOG(AL_NOTICE, "Waiting for connector to retrieve configuration");
                }
            }
            sleep(1);
        }
    } while (res);

    assistant_gwally = assistant_cfg_override_conn_init(broker, &assistant_gwally_test_origin);
    if (NULL == assistant_gwally) {
        return NULL;
    }

    if (is_zpn_drmode_enabled()) {
        /* Parse dr_wally config file (row by row for each table) and statically load JSON row object into appropriate tables */
        for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
            const char* filename = zpn_dr_lib_get_cfg_file_name_for_category(category_idx);
            if (strcmp(filename, "override_conf.json") == 0) {
                /* Parse dr_ovd_wally config file (row by row for each table) and statically load JSON row object into appropriate tables */
                load_dr_config_to_wally_origin( filename,
                                         assistant_gwally_test_origin,
                                         NULL);
            }
        }

        /* Set property of test origin to automatically respond the dr_wally requests */
        wally_test_origin_autorespond(assistant_gwally_test_origin, 1);
    }

    res = zpath_config_override_init(assistant_gwally, global_assistant.customer_gid, 0, config_component_appc);
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to init config override table %s", zpath_result_string(res));
        return NULL;
    }

    if (!is_zpn_drmode_enabled()) {
        assistant_validate_current_version();
        res = assistant_log_tx_init(broker, cloud_name);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init log tx, error: %s", zpath_result_string(res));
            return NULL;
        }
    }

    /* Step 1, initialize and register the config overrides
     */
    zpath_init_common_config_overrides(global_assistant.gid, global_assistant.customer_gid, DEFAULT_APP_CONNECTOR_CLIENT_CIPHERSUITE_INDEX);

    /* Step 2, Get the current/active override values, now that config override connection is created.
     */
    zpath_app_init_fohh_cipher_configuration(global_assistant.gid, global_assistant.customer_gid, DEFAULT_APP_CONNECTOR_CLIENT_CIPHERSUITE_INDEX);

    /* Step3, Set the custom cipher suite index as per the config override
     * on the SSL_Ctx created in previous steps, so that new FOHH connections shall use that cipher suite selection
    */
    fohh_ssl_ctx_client_custom_options(global_assistant.assistant_to_public_cloud_ctx, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    fohh_ssl_ctx_client_custom_options(global_assistant.assistant_to_private_cloud_ctx, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    if ( global_assistant.assistant_to_public_cloud_ctx_dtls ) {
        fohh_ssl_ctx_client_custom_options(global_assistant.assistant_to_public_cloud_ctx_dtls, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    }
    if ( global_assistant.assistant_to_private_cloud_ctx_dtls ) {
        fohh_ssl_ctx_client_custom_options(global_assistant.assistant_to_private_cloud_ctx_dtls, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    }

    res = assistant_config_ovd_desc_register_all();
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to resgister config override description %s", zpath_result_string(res));
        return NULL;
    }

    res = zpn_inspection_config_data_fetch(global_assistant.customer_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init inspection config data");
        return NULL;
    }

    res = zpn_inspection_zsdefined_control_fetch();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not fetch inspection zsdefined control");
        return NULL;
    }

    global_assistant.max_pause_time_interval = assistant_get_max_pause_time(assistant_gid);
    ZPN_LOG(AL_NOTICE,"max_pause_time_interval:%"PRId64"", global_assistant.max_pause_time_interval);
    assistant_monitor_max_pause_time(assistant_gid);

    /*
     * This will not fail as the assistant_cfg_assistant_get_customer_gid went through fine and the wally row is
     * cached
     */
    cfg_asst_name = assistant_cfg_assistant_get_name();
    assistant_state_set_configured_name(cfg_asst_name);

    if (!is_zpn_drmode_enabled()) {
        /* step2 of config init, the second set of configs */
        res = assistant_cfg_init_2(assistant_wally);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init_2 config");
            return NULL;
        }

        res = assistant_control_init(broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init control module");
            return NULL;
        }

        res = assistant_stats_init(broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init stats");
            return NULL;
        }
    } else {
        res = assistant_control_init(broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init control module");
            return NULL;
        }

        /* step2 of config init, the second set of configs */
        res = assistant_cfg_init_2(assistant_wally);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init_2 config");
            return NULL;
        }
    }

    global_assistant.auto_upgrade_disabled = auto_upgrade_disabled;
    /* Cleanup upgrade config before writing*/
    zpath_upgrade_cfg_cleanup();

    res = zpn_system_init(ZPN_SYSTEM_THRESHOLD_TYPE_ASSISTANT,
                          assistant_state_get_current_time_cloud_us,
                          zpath_assert,
                          assistant_state_is_dev_environment(),
                          assistant_is_ready,
                          assistant_state_get_cpu_util,
                          assistant_state_get_cpu_steal_perc,
                          assistant_state_get_system_mem_util,
                          assistant_state_get_process_mem_util,
                          assistant_state_get_available_cpus,
                          assistant_state_get_uptime_s());

    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init system module");
        return NULL;
    }

    res = zpn_sys_stats_init_keys();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_sys_stats_init");
        return NULL;
    }

    {
        static pthread_mutexattr_t mutex_attributes;
        pthread_mutexattr_init(&mutex_attributes);
        pthread_mutexattr_settype(&mutex_attributes, PTHREAD_MUTEX_RECURSIVE);
        pthread_mutex_init(&(global_assistant.config_lock), &mutex_attributes);
    }

    for (i = 0; i < ZPN_ASSISTANT_BUCKETS; i++) {
        struct zpn_assistant_bucket *bucket = &(global_assistant.buckets[i]);
        bucket->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
        bucket->mtunnel_by_id = argo_hash_alloc(7, 1);
        TAILQ_INIT(&(bucket->bucket_mtunnel_list));
        TAILQ_INIT(&(bucket->bucket_reaped_list));
        if (!bucket->mtunnel_by_id) {
            ZPN_LOG(AL_CRITICAL, "Could not allocate hash table");
            return NULL;
        }
    }

    res = assistant_ncache_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init ncache");
        return NULL;
    }

    res = assistant_sticky_cache_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init sticky cache");
        return NULL;
    }

    if (zudp_conn_table_initialize()) {
        ZPN_LOG(AL_CRITICAL, "zudp_conn_table_initialize() failed !");
        return NULL;
    }

    zpn_assistant_mtunnel_free_q_init();
    zpn_assistant_mtunnel_state_init();
    assistant_init_cert_validity_counters(cert_file_name);

    //global_assistant.applications = argo_hash_alloc(7, 1);
    //global_assistant.servers = argo_hash_alloc(7, 1);
    //global_assistant.groups = argo_hash_alloc(7, 1);
    res = assistant_app_init(0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init assistant app");
        return NULL;
    }

    res = assistant_service_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init service module");
        return NULL;
    }

    assistant_dns_init();

    /* Eventually we will change this to not rely on instance
     * table. For now we get the name of our system from the instance
     * table rather than from a certificate. */

    /* Get instance table config for assistant. */

    ZPN_LOG(AL_NOTICE, "Running with connector GID = %ld, name = %s", (long) global_assistant.gid, cfg_asst_name);

    /* start with upload enabled */
    global_assistant.log_upload_enabled = 1;
    global_assistant.stats_upload_enabled = 1;
    global_assistant.stats_log_to_disk_enabled = stats_log_to_disk;

    res = zpn_assistant_waf_sess_mgr_init(assistant_wally, assistant_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize WAF AST - Sess MGR : %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_insp_init_debug();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add INSP debug commands: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_waf_init_debug();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add WAF debug commands: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_waf_profile_manager_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to initialize WAF profile manager");
        return NULL;
    }

    assistant_waf_ssl_init();

    /* step3 of config init, the third set of configs */
    res = assistant_cfg_init_3(assistant_wally);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init_3 config");
        return NULL;
    }

    /*
     * Spawn health monitoring thread...
     */
    res = zpn_assistant_health_init(NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant health monitoring: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_zdx_combiner_init(assistant_mtunnel_inject_data_to_mconn, global_assistant.gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init combiner: %s", zpn_result_string(res));
        return NULL;
    }

    res = assistant_data_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant data : %s", zpn_result_string(res));
        return NULL;
    }

    if (zpn_mconn_fohh_tlv_init_debug()) {
        ZPN_LOG(AL_ERROR, "Could not initialize debug command for fohh_tlv");
    }

    /*
     * Add debug command for dumping the stats of all of assistant
     */
    if (!(assistant_init_stats_description = argo_register_global_structure(ASSISTANT_INIT_STATS_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create assistant stats desc");
        return NULL;
    }
    res = zpath_debug_add_read_command("dump the states and stats of assistant",
                                  "/assistant/debug_dump",
                                  assistant_init_debug_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /assistant/debug_dump to debug: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_zrdt_init_debug();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add zrdt debug command: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_mconn_icmp_debug_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_mconn_icmp debug command: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_mconn_icmpv6_debug_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_mconn_icmpv6 debug command: %s", zpn_result_string(res));
        return NULL;
    }

//TODO(blewis) remove this when ready
//    //temporary debugging stuff for ZDX
#define ZPN_ZDX_DEBUG_ENDPOINTS 1
#ifdef ZPN_ZDX_DEBUG_ENDPOINTS
    res = assistant_zdx_init(assistant_gid,zpn_event_collection, freebsd_compat_mode);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init assistant_zdx_init: %s", zpn_result_string(res));
        return NULL;
    }
#endif

    int is_asst_enabled = 0;
    int64_t connector_type = app_connector;
    assistant_cfg_assistant_is_enabled(&is_asst_enabled, &connector_type);

    // Initialize the admin probe later for the NP connector, only after the NP configuration has been successfully downloaded.
    if (connector_type == app_connector) {
        res = assistant_admin_probe_init(assistant_wally, assistant_gid, global_assistant.customer_gid, 0, zpn_event_collection, 1);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init assistant_admin_probe_init: %s", zpn_result_string(res));
            return NULL;
        }
    }

    res = admin_probe_rpc_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init admin_probe_rpc_init: %s", zpn_result_string(res));
        return NULL;
    }

    res = np_connector_pre_init(zpn_event_collection);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_connector_pre_init: %s", zpn_result_string(res));
        return NULL;
    }

    res = np_init_pre_appc();
    if (res) {
        ZPN_LOG(AL_ERROR, "could not init np_init_pre_appc");
        return NULL;
    }

    g_config_init_done = 1;

    {
        int64_t scope_gid = 0;

        global_assistant.scope_gid = global_assistant.customer_gid;

        res = assistant_cfg_assistant_get_scope_gid(&scope_gid);
        if (res == ZPN_RESULT_NO_ERROR) {
            global_assistant.scope_gid = scope_gid;
            ZPN_LOG(AL_NOTICE, "Scope gid for assistant %" PRId64 " is %" PRId64, global_assistant.gid, scope_gid);
        } else {
            ZPN_LOG(AL_CRITICAL, "Failed to obtain scope id for asst %" PRId64, global_assistant.gid);
        }
    }

    assistant_stats_tx_init();

    // Fetch last os and sarge upgrade timestamps
    zpath_upgrade_get_restart_timestamps(&(global_assistant.last_os_upgrade_time), &(global_assistant.last_sarge_upgrade_time));
    zpath_upgrade_read_stats(&(global_assistant.upgrade_stats));

    global_assistant.platform_version = zvm_vm_type_rh_image_version();

    /*
     * Spawn assistant monitoring thread, this has to be initialized AFTER stats tx init (ET-58026)
     */
    res = assistant_monitor_init(NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant monitoring: %s", zpn_result_string(res));
        return NULL;
    }

    res = assistant_two_hop_user_broker_cache_init();
    if (res) {
        ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST assistant_init: assistant_two_hop_user_broker_cache_init failed");
        ZPN_LOG(AL_ERROR, "TWO_HOP ASST assistant_init: assistant_two_hop_user_broker_cache_init failed");
    }

	if (slow_asst_init) {
		ZPN_LOG(AL_CRITICAL, "Sleeping for %d seconds due to -slow_init arg...", slow_asst_init);
		sleep(slow_asst_init);
	}

    res = zpath_debug_add_read_command("dump the state of assistant version control",
                                  "/assistant/version/state/dump",
                                  assistant_version_state_dump,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /assistant/version/state/dump to debug: %s", zpn_result_string(res));
        return NULL;
    }

    global_assistant.is_init_complete = 1;
    return &global_assistant;
}
