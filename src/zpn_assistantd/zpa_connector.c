/*
 * zpn_connector.c. Copyright (C) 2018 Zscaler, Inc. All Rights Reserved.
 *
 */
#define _GNU_SOURCE

#ifndef APP_ROLE
#error APP_ROLE is not defined
#endif

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <utime.h>
#include <limits.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_http.h"
#include "zcdns/zcdns_libevent.h"
#include "zhw/zhw_id.h"
#include "zhw/zhw_os.h"
#include "zcrypt/zcrypt.h"
#include "base64/base64.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_lib.h"
#include "zvm/zvm.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_misc/zpath_platform.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "argo/argo.h"

#include "zsdtls/zdtls.h"
#include "zrdt/zrdt.h"
#include "ztlv/zpn_tlv.h"

#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_control.h"
#include "zpn_assistantd/assistant_init.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_site.h"
#include "zpn_assistantd/assistant_stats_tx.h"
#include "zpn_assistantd/assistant_cfg_version.h"
#include "zpn_assistantd/assistant_cfg_assistant.h"
#include "zpn_assistantd/zpa_connector.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn/zpn_debug.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/sanitizer_config.h"
#include "zpn_inspection/zpn_transform_stats.h"
#include "zpn_inspection/zpn_pipeline_stats.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn/zpn_rpc.h"

#include "zpn_assistantd/assistant_admin_probe.h"
#include "zpn_assistantd/assistant_additional_debug_logs.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_dr_interface.h"
#include "zpn_event/zpn_event.h"
#include "zpn_assistantd/assistant_config_override_desc.h"
#include "np_connector/np_connector.h"
#include "zpn_assistantd/assistant_np_connector.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "np_connector/np_connector_bgp.h"
#include "np_lib/np_frr_utils.h"
#include "zpath_lib/zpath_oauth_utils.h"

#ifdef __linux__
#include <unistd.h>
#include <fcntl.h>
#include <linux/version.h>
#endif

/* Local state */
int config_daemon = 0;
int dr_mode_enabled = 0;

const char *logfile = NULL;
int debuglog = 0;

char *broker_name = NULL;

struct zpn_assistant_state *assistant;
int64_t assistant_id = 0;
int64_t customer_id = 0;

static int asst_fohh_thread_count = ASSISTANT_DEFAULT_FOHH_THREADS;

#define ZPN_ASSISTANT_FOHH_HEARTBEAT_TIMEOUT_S 40
/*
 * Just based on what we are doing today we have 8 non-fohh threads. Dirty, but found no way to do memory allocator
 * tuning.
 *
 * ET-48680 as of drop22, there are 20 non-fohh threads, approx 15/20 are doing malloc 'active' operations (not all of them doing active/periodic malloc operations)
 */
int asst_non_fohh_thread_count = 15;
int debug_port = 9000;

static int64_t cpu_starvation_interval = CONFIG_ZPN_EVENT_CPU_STARVATION_DEFAULT_INTERVAL;

/* Local configuration */
static uint8_t cfg_instance_bytes[INSTANCE_ID_BYTES];
static uint8_t cfg_fingerprint_bytes[INSTANCE_ID_BYTES];
static char cfg_fingerprint_str[(((INSTANCE_ID_BYTES + 2) / 3) * 4) + 1];
static char cfg_provisioning_key[CFG_PROVISION_KEY_SIZE];
static int cfg_key_shard;
static char cfg_key_api[100];
static char cfg_key_cloud[100];
static char config_fetch_ca_file[MAX_CA_FILE_LEN];

static int cfg_write_plain = 0;
static int cfg_api_direct = 0;

struct zhw_id cfg_hw_id;
struct zcrypt_key cfg_hw_key;
EVP_PKEY *cfg_pkey = NULL;

int assistant_enrollment_version = 2;

int64_t connector_quiet_cfg = 0;
int64_t stats_status_interval = 0;
int64_t log_status_interval = 0;
int64_t waf_status_interval = 0;
int64_t app_inspection_status_interval = 0;
int64_t ptag_status_interval = 0;
int64_t ctl_status_interval = 0;
int64_t cfg_status_interval = 0;
int64_t ovd_status_interval = 0;
int64_t data_status_interval = 0;
int64_t argo_logging_threshold_percentage = 0;

/* develop mode ? */
enum zcrypt_metadata_develop_mode develop_certs_mode = zcrypt_metadata_develop_mode_unknown;

/* Local prototypes */
static int zpn_connector_upgrade_prep(int64_t assistant_id, void *upgrade_state, int64_t time_delta, int auto_upgrade_disabled);

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));

/* ET-86784 usage() on app connector child will no longer exit
 * Caller may decide to exit if it's a critical error */
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    /*
     * Customers have started playing around with the connector options. There are some options which we don't expect
     * the customers to play with. Hide those under the #if 0 block. It is important to have the entry under #if 0 as
     * it helps developers to search for the options.
     */
    fprintf(stdout,
            "  -version          : Opt : Display version and exit\n"
            "  -role             : Opt : Display role and exit\n"
            "  -platform         : Opt : Display platform and exit\n"
            "  -arch             : Opt : Display platform architecture and exit\n"
            "  -threads COUNT    : Opt : Worker thread count. Defaults 4\n"
            "  -dir PATH         : Opt : Directory to find config, else local dir\n"
            "  -debug PORT       : Opt : Defaults 9000. Set to 0 to disable\n"
            "  -debuglog         : Opt : Specify in order to send debug messages to syslog/stderr\n"
            "  -container        : Opt : Set current environment to container\n"
            "  -disable_heartbeat_monitor: Opt : Disable thread-hang detection\n"
            "  -logfile NAME     : Opt : Send log events to specified file\n"
            "  -broker NAME      : Opt : Broker to connect to\n"
            "  -repo_ca_file PATH : optional, custom signing CA certicate file (pem) \n"
            "  -fproxy NAME      : Opt : Connect to brokers via forward proxy NAME\n"
            "  -fproxy_port #    : Opt : Connect to forward proxy via port #\n"
            "  -direct           : Opt : Make assistant connect to API directly rather than via broker\n"
            "  -quiet            : Opt : Operate without FOHH status messages\n"
            "  -daemon           : Opt : Run as daemon\n"
            "  -plain            : Opt : Write RSA key unencrypted (dangerous! only works during enrollment)\n"
            "  -tunca FILE       : Opt : Root CA to use for inner tunnel\n"
            "  -no_flow_control  : Opt : Disable flow control on FOHH\n"
            "  -enroll_v2        : Opt : Use v2 enrollment style\n"
            "  -stats_log_to_disk: Opt : Log the stats to stats.log file locally\n"
            "  --print-core FILE : Opt : Read FILE and print stack\n"
            "  --print-core-force FILE : Opt : Read FILE and print stack, without checking app name/version\n"
            "  -always_re_enroll : Opt : Reenroll for cert.pem renewal\n"
            "  -no_auto_upgrade  : Opt : Disable automatic upgrade\n"
            "  -slow_init #      : Opt : Slow down connector init by specified number of seconds\n"
            "  -ut_mode          : Opt : UT mode, to run some tests along with runtime code\n"
            "  -fohh-pool=NAME=N,M : Opt : Create worker pool named 'NAME' comprising threads N through M (inclusive)\n"
            "  -maxlogmb             : Opt : Specify maximum MB to use for argo logging.\n"
            "  -memory_arena_count # : Opt : Sets a hard limit on the maximum number of arenas that can be created. Linux only\n"
            "  -health_txn_prefer_local_cache_over_dsp_cache : Opt : prefer local cache over dispatcher cache for health-based-transaction\n"
           );
    zpath_app_logging_usage_print();
    va_end(list);
}

#ifdef __linux__
#include <sys/prctl.h>
static void set_process_name(char* str) {
    if (prctl(PR_SET_NAME, str, NULL, NULL, NULL)) {
        ZPN_LOG(AL_ERROR, "Failed to set child process name: %s", strerror(errno));
    } else {
        ZPN_LOG(AL_INFO, "Successfully set process name to: %s", str);
    }
}
#else
static void set_process_name(char* str) {
    return;
}
#endif


static void zpa_connector_sighandler(int sig_num, siginfo_t *sig_info, void *data)
{
    if (global_assistant.cur_mode == np_connector) {
        assistant_np_connector_exit_callback();
    }

    // Re-raise the signal.
    struct sigaction sig = {};
    sig.sa_handler = SIG_DFL;
    sigaction(sig_num, &sig, NULL);
    raise(sig_num);
}

static void zpa_connector_set_up_termination_handler()
{
    struct sigaction sig;
    memset(&sig, 0, sizeof(sig));

    sig.sa_sigaction = zpa_connector_sighandler;

    if (sigaction(SIGINT, &sig, NULL)) {
        ZPN_LOG(AL_ERROR, "Could not install signal handler SIGINT");
    }

    if (sigaction(SIGTERM, &sig, NULL)) {
        ZPN_LOG(AL_ERROR, "Could not install signal handler SIGTERM");
    }
}

void connector_quiet_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int quiet_cfg = 0;

    quiet_cfg = (int)*config_value;

    if ((quiet_cfg == CONNECTOR_QUIET_ENABLE) || (quiet_cfg == CONNECTOR_QUIET_DISABLE)) {
        connector_quiet_cfg = quiet_cfg;
        fohh_set_quiet(quiet_cfg);
        ZPN_LOG(AL_INFO, "Connector quiet configuration set to %"PRId64, connector_quiet_cfg);
    }

    return;
}

static int set_status_interval(int interval)
{
    int res = interval;

    /* Round up or round down the status interval, if it is not within FOHH_MIN_STATUS_INTERVAL and FOHH_MAX_STATUS_INTERVAL range */
    if (res < FOHH_MIN_STATUS_INTERVAL) {
        res = FOHH_MIN_STATUS_INTERVAL;
    } else if (res > FOHH_MAX_STATUS_INTERVAL) {
        res = FOHH_MAX_STATUS_INTERVAL;
    }

    return res;
}

void connector_stats_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int status_interval = set_status_interval((int)*config_value);

    global_assistant.stats_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Stats connection status interval configuration set to %d", status_interval);

    return;
}

void connector_ctl_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.ctl_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Control connection status interval configuration set to %d", status_interval);

    return;
}

void connector_log_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.log_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Log connection status interval configuration set to %d", status_interval);

    return;
}

void connector_waf_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.waf_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector WAF connection status interval configuration set to %d", status_interval);

    return;
}

void connector_app_inspection_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    (void)impacted_gid;
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.app_inspection_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector App Inspection connection status interval configuration set to %d", status_interval);

    return;
}

void connector_ptag_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    (void)impacted_gid;
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.ptag_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector PTAG connection status interval configuration set to %d", status_interval);

    return;
}

void connector_ovd_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.ovd_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Config Override connection status interval configuration set to %d", status_interval);

    return;
}

void connector_cfg_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.cfg_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Config connection status interval configuration set to %d", status_interval);

    return;
}

void connector_data_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);

    global_assistant.data_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Connector Data connection status interval configuration set to %d", status_interval);

    return;
}

void
connector_cpu_starvation_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int32_t wait_interval = 0;

    (void)(impacted_gid);

    if (config_value) {
        if (*config_value > INT_MAX) {
            wait_interval = INT_MAX;
        } else if (*config_value < INT_MIN) {
            wait_interval = INT_MIN;
        } else {
            wait_interval = (int32_t)(*config_value);
        }
    }
    zpn_event_cpu_starvation_notify_interval_set(wait_interval);
    ZPN_LOG(AL_INFO, "Connector cpu starvation notification interval set to %d", zpn_event_cpu_starvation_notify_interval_get());

    return;
}

void argo_logging_threshold_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int al_mem_threshold = (int)*config_value;

    if (al_mem_threshold > 100 || al_mem_threshold < 0) {
        ZPATH_LOG(AL_ERROR, "Invalid logging threshold received from config override: %d, using default", al_mem_threshold);
        return;
    }
    argo_log_global_set_mem_threshold(0, al_mem_threshold);
    ZPATH_LOG(AL_INFO, "argo memory logging threshold set to %d", al_mem_threshold);

    return;
}

/*
 * Verify connector environment.
 * 1. Verify if we have permission to write to disk
 */
static int zpn_connector_verify_env(void)
{
    char    cwd[PATH_MAX];
    int     result;

    if (getcwd(cwd, sizeof(cwd)) == NULL) {
        ZPN_LOG(AL_ERROR, "Cannot get current working directory: %s", strerror(errno));
        return ZPN_RESULT_CANT_WRITE;
    }

    result = access(cwd, W_OK);
    if (result != 0) {
        ZPN_LOG(AL_ERROR, "Do not have write permission to %s: %s - use chmod and correct the permission of the directory", cwd, strerror(errno));
        return ZPN_RESULT_CANT_WRITE;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    if (priority < argo_log_priority_notice) {
        ZPN_LOG(priority, "%s", dump);
    } else {
        ZPN_DEBUG_HEALTH("%s", dump);
    }
}

/* Verify version logger */
static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "Connector: %s", log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

/* Verify info version logger */
static void log_debug_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "%s: %s", CONNECTOR_LOG_NAME, log_buf);

    /* Override priority to debug */
    ZPATH_LOG(LOG_DEBUG, "%s", buf);
}

static int zpn_connector_verify_version(void)
{
    char restart_version[500] = "";
    int verify_res = ZCRYPT_RESULT_NO_ERROR;
    int using_develop_certs;

    if (develop_certs_mode != zcrypt_metadata_develop_mode_unknown) {

        /* We know the develop mode setting via env, this is new sarge */
        using_develop_certs = (develop_certs_mode == zcrypt_metadata_develop_mode_enabled) ? 1 : 0;
        verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                    ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                    NULL, restart_version, sizeof(restart_version),
                                                    log_verify_version_f, using_develop_certs, 1);
    } else {

        /* We dont have develop mode setting, old sarge, try both with/without develop mode enabled to be backward compatible */
        ZPN_LOG(AL_INFO, "Verify software with prod metatdata");
        using_develop_certs = 0; /* Default for prod, first try with no develop cert */
        verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                    ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                    NULL, restart_version, sizeof(restart_version),
                                                    log_debug_verify_version_f, using_develop_certs, 1);

        if (verify_res != ZCRYPT_RESULT_NO_ERROR) {
            using_develop_certs = 1; /* Try again with develop certs */
            ZPN_LOG(AL_INFO, "Verify software with develop metatdata, prod metadata verify unsuccessful");
            verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                        ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                        NULL, restart_version, sizeof(restart_version),
                                                        log_debug_verify_version_f, using_develop_certs, 1);
        } else {
            ZPN_LOG(AL_DEBUG, "Verify software with prod metatdata successful");
        }
    }

    /* Process results of checks by producing final logs */
    if ((verify_res == ZCRYPT_RESULT_NO_ERROR) && strlen(restart_version) > 0) {
        if (using_develop_certs) {
            ZPN_LOG(AL_NOTICE, "%s restart software version verified: %s, develop mode is enabled", CONNECTOR_LOG_NAME, restart_version);
        } else {
            ZPN_LOG(AL_NOTICE, "%s restart software version verified: %s", CONNECTOR_LOG_NAME, restart_version);
        }
    } else {
        ZPN_LOG(AL_ERROR, "%s restart software version not present or unable to verify", CONNECTOR_LOG_NAME);
    }

    return verify_res;
}

/*
 * 1. After the system has moved to paused state, don't bother reading the config again. No further config changes
 * are to be accepted - focus only on restarting the process.
 * 2. if assistant->next_restart_time_cloud_s is 0, it means the config haven't set a restart time and we would have
 * moved to puased state due to other conditions.
 * 3. If we are paused, either do log every few seconds or restart.
 * 4. If we are not paused, read the config and,
 *  a. update the next restart timer.
 *  b. update the version file with version info for the sarge to read and act on.
 *
 *  NOTE : auto_upgrade_disabled does NOT disable restart in the case of certificate enrollment required.
 *  NOTE : Code assumes that this function is called every 1 second. When the config channel is DOWN, we know that
 *  this won't get called, this is a known caveat - its ok because we assistant_cfg_conn_insance() will restart
 *  the connector at that time.
 */
static int
zpn_connector_upgrade_prep(int64_t assistant_id, void *upgrade_state, int64_t time_delta, int auto_upgrade_disabled)
{
    int result;
    static int seen_version = 0;
    static int frr_seen_version = 0;
    static char written_version[1000] = {'\0'};
    static char sarge_written_version[ZPATH_VERSION_STR_MAX_LEN] = {'\0'};
    static char frr_written_version[ZPATH_VERSION_STR_MAX_LEN] = {'\0'};
    char cfg_upgrade_version[ZPATH_VERSION_STR_MAX_LEN];
    char sarge_cfg_upgrade_version[ZPATH_VERSION_STR_MAX_LEN];
    char frr_cfg_upgrade_version[ZPATH_VERSION_STR_MAX_LEN];
    int64_t cfg_upgrade_time_s;
    static int64_t pause_state_start_time_local_s = 0;
    static int     pause_state_counter = 0;
    static int prev_os_upgrade_enabled = 0;
    int os_upgrade_enabled = 0;

    if (assistant_state_is_paused() &&
        !assistant_state_is_admin_probe_pause_for_restart_process() &&
        !assistant_state_is_admin_probe_pause_for_restart_system()) {
        int64_t current_time_cloud_s;
        int64_t next_restart_time_cloud_s;

        /*
         * This is the second & third level of insurance that we have, to make sure we don't hang in the paused state
         * for whatever reasons (being extra cautious as we are dependent on 1. clock time 2. correctness of
         * assistant_state*.c code to restart the process!).
         * second level of insurance: will make sure that we are not paused(i.e hung!) for not more than (2 *
         * SLOW_STOP_TIME_S) time period.
         * third level of insurance: will make sure that we are not looping here for more than 1000 times, ~15 minutes,
         * as this function is called every second(when the cfg channel is healthy). This level of protection gets
         * around wall clock change in the system.
         */
        {
            if (0 == pause_state_start_time_local_s) {
                pause_state_start_time_local_s = epoch_s();
            }
            if (epoch_s() > (pause_state_start_time_local_s + (2 * SLOW_STOP_TIME_S))) {

                if (global_assistant.broker_control) {
                    zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                             fohh_connection_incarnation(global_assistant.broker_control),
                                                             global_assistant.gid,
                                                             ASST_RSTR_UPGR_TIME_MIS);
                }  else {
                    ZPATH_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                          assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_UPGR_TIME_MIS);
                }

                ZPATH_LOG(AL_ERROR, "Connector restarting, stuck in a state without the next upgrade time, please "
                          "report to customer support team");
                sleep(1);
                exit(0);
            }
            pause_state_counter++;
            if (pause_state_counter > CONNECTOR_BAIL_AFTER_STUCK_IN_INVALID_STATE_COUNTER) {

                if (global_assistant.broker_control) {
                    zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                             fohh_connection_incarnation(global_assistant.broker_control),
                                                             global_assistant.gid,
                                                             ASST_RSTR_INVAL_PAUSE_CNTR);
                } else {
                    ZPATH_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                          assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_INVAL_PAUSE_CNTR);
                }

                ZPATH_LOG(AL_ERROR, "Connector restarting, stuck in a state without the next upgrade time for (%d)"
                          "secs, please report to customer support team",
                          CONNECTOR_BAIL_AFTER_STUCK_IN_INVALID_STATE_COUNTER);
                sleep(1);
                exit(0);
            }
        }

        current_time_cloud_s = assistant_state_get_current_time_cloud_s();
        next_restart_time_cloud_s = assistant_state_get_next_restart_time_s();

        if ((current_time_cloud_s < next_restart_time_cloud_s) &&
            (current_time_cloud_s > next_restart_time_cloud_s - SLOW_STOP_TIME_S)) {
            /* We need to restart in the next 5 minutes. We should soft-close connections, etc */
            if ((((next_restart_time_cloud_s - current_time_cloud_s) % 10) == 0) ||
                ((next_restart_time_cloud_s - current_time_cloud_s) < 10)) {
                ZPATH_LOG(AL_NOTICE, "Connector restarting in %ld seconds",
                          (long)(next_restart_time_cloud_s - current_time_cloud_s));
            }
        }

        if (current_time_cloud_s > next_restart_time_cloud_s) {
            zpn_connector_verify_version();

            if (global_assistant.broker_control) {
                zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                         fohh_connection_incarnation(global_assistant.broker_control),
                                                         global_assistant.gid,
                                                         ASST_RSTR_UPGRADING);
            } else {
                ZPATH_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                      assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_UPGRADING);
            }

            ZPATH_LOG(AL_NOTICE, "Connector restarting");
            sleep(1);
            exit(CHILD_UPGRADE_SUCCESS_EXIT_CODE);
        }

        return ZPATH_RESULT_NO_ERROR;
    }

    *cfg_upgrade_version = '\0';
    *sarge_cfg_upgrade_version = '\0';
    *frr_cfg_upgrade_version = '\0';
    result = assistant_cfg_version_get_upgrade_info(&cfg_upgrade_time_s, cfg_upgrade_version,
                                                    sarge_cfg_upgrade_version, sizeof(sarge_cfg_upgrade_version), &os_upgrade_enabled,
                                                    frr_cfg_upgrade_version, sizeof(frr_cfg_upgrade_version));

    if (auto_upgrade_disabled && ((ZPN_RESULT_NO_ERROR == result) || (ZPN_RESULT_NOT_FOUND == result) )) {
        /*
         * I am here just to let sarge know that we have successfully communicated to the cloud and ask sarge to be
         * happy about me. Writing the current version, so that sarge don't try to upgrade at any cost.
         * ZPN_RESULT_NOT_FOUND - means we CONNECTED to the cloud, but cloud didn't find any entry for the assitant.
         * ZPN_RESULT_NO_ERROR - means we CONNECTED to the cloud and golden.
         */
        if (!seen_version) {
            if (zpn_enroll_update_version(FILENAME_VERSION,
                               ZPATH_VERSION,
                               written_version,
                               sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                seen_version = 1;
            }
        }
        /*
         * NPC case should do vtysh check to look up the frr version being installed?
         */
    } else if (result == ZPN_RESULT_NO_ERROR) {

        assistant_state_set_next_restart_time_s(cfg_upgrade_time_s);

        if (strlen(cfg_upgrade_version)) {
            if (!seen_version) {
                if (zpn_enroll_update_version(FILENAME_VERSION,
                                   cfg_upgrade_version,
                                   written_version,
                                   sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                    seen_version = 1;
                }
            } else {
                if (strcmp(written_version, cfg_upgrade_version)) {
                    if (zpn_enroll_update_version(FILENAME_VERSION,
                                       cfg_upgrade_version,
                                       written_version,
                                       sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                        ZPN_LOG(AL_NOTICE, "Connector expected software version updating to %s", cfg_upgrade_version);
                        seen_version = 1;
                    }
                }
            }
        } else {
            /* Common, for brand new connectors that haven't ever been upgraded */
            //ZPN_LOG(AL_NOTICE, "Connector: No specific version of software specified");
            // No specific expected version of software is specified. Go ahead and write current running version
            if (!seen_version) {
                if (zpn_enroll_update_version(FILENAME_VERSION,
                                   ZPATH_VERSION,
                                   written_version,
                                   sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                    seen_version = 1;
                }
            }
        }

        if (zvm_vm_type_is_zscaler_rh_image()) {
            /* Update sarge version only if it changed */
            if (strlen(sarge_cfg_upgrade_version) && strcmp(sarge_written_version, sarge_cfg_upgrade_version) &&
            (strcmp(sarge_cfg_upgrade_version, assistant_state_get_sarge_version()))) {
                if (zpath_upgrade_set_sarge_version(sarge_cfg_upgrade_version,
                                    sarge_written_version,
                                    sizeof(sarge_written_version)) == ZPN_RESULT_NO_ERROR) {
                        ZPN_LOG(AL_NOTICE, "Sarge expected software version updating to %s", sarge_cfg_upgrade_version);
                }
            }

            /* Update os upgrade if it changed*/
            if (os_upgrade_enabled != prev_os_upgrade_enabled) {
                if (zpath_upgrade_set_os_upgrade_enabled_cfg(os_upgrade_enabled) == ZPN_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_NOTICE, "Os upgrade enabled is now %d", os_upgrade_enabled);
                    prev_os_upgrade_enabled = os_upgrade_enabled;
                }
            }
        }

        if ((global_assistant.app_role_type == np_connector) && strlen(frr_cfg_upgrade_version)) {
            if (!frr_seen_version) {
                if (zpn_enroll_update_version(FILENAME_FRR_VERSION,
                                              frr_cfg_upgrade_version,
                                              frr_written_version,
                                              sizeof(frr_written_version)) == ZPN_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_NOTICE, "FRR expected software version set to %s", frr_cfg_upgrade_version);
                    frr_seen_version = 1;
                }
            } else {
                if (strcmp(frr_written_version, frr_cfg_upgrade_version)) {
                    if (zpn_enroll_update_version(FILENAME_FRR_VERSION,
                                                  frr_cfg_upgrade_version,
                                                  frr_written_version,
                                                  sizeof(frr_written_version)) == ZPN_RESULT_NO_ERROR) {
                        ZPN_LOG(AL_NOTICE, "FRR expected software version updating to %s", frr_cfg_upgrade_version);
                        frr_seen_version = 1;
                    }
                }
            }
        } else {
            /*
             * should connector write FRR running verison? sarge handles the null case
             */
        }
    } else if (result == ZPN_RESULT_NOT_FOUND) {
        /*
         * Note that we cannot get a 'not found' response without successfully communicating with the cloud.
         * Can happen when the row is deleted in management API for whatever reasons. In this case we just write our
         * current version out.
         */
        if (!seen_version) {
            /* We do this relatively silently since it is no big deal */
            if (zpn_enroll_update_version(FILENAME_VERSION,
                               ZPATH_VERSION,
                               written_version,
                               sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                seen_version = 1;
            }
        } else {
            /* We saw a version once, but now we're not found. Odd, but not that noteworthy. */
        }

        /*
         * should npc write?
         */
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_connector_get_max_fohh_threads ()
{
    int num_cores;
    num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (-1 == num_cores) {
        fprintf(stderr, "Failed to get number of available cpus - %s\n", strerror(errno));
        num_cores = ASSISTANT_DEFAULT_FOHH_THREADS;
    } else if (num_cores > FOHH_MAX_THREADS) {
        num_cores = FOHH_MAX_THREADS;
    } else if (num_cores < ASSISTANT_DEFAULT_FOHH_THREADS) {
        num_cores = ASSISTANT_DEFAULT_FOHH_THREADS;
    }

    return num_cores;
}

static int assistant_get_raw_dr_stats(char *buf, size_t buf_len, int pretty)
{
    struct zpn_assistant_dr_stats *stats = &global_assistant.dr_stats;
    int ret = ZPN_RESULT_NO_ERROR;

    ret = argo_structure_dump(zpn_assistant_dr_stats_description, stats, buf, buf_len, NULL, pretty);
    if (ARGO_RESULT_NO_ERROR != ret) {
        ZPN_LOG(AL_ERROR, "Error while dumping argo object 'assistant dr stats' error:%s", zpath_result_string(ret));
    }

    return ret;
}

int sub_main(int argc, char *argv[])
{
    int result;
    int res;
    int i;
    char asst_name[ASSISTANT_NAME_MAX_LEN];
    char debug_str[1000];
    char *dom = NULL;
    char *root_cert = NULL;
    FILE *fp;
    int once_waiting = 0;
    int auto_upgrade_disabled;
    int stats_log_to_disk;
    struct zthread_info *zthread;
    struct stat st;
    time_t root_cert_time = 0;
    int write_cloud_root = 0;
    struct zpn_enroll_state *enroll_state = NULL;
    int memory_arena_count;
    int         ut_mode;
    int         always_re_enroll;
    char        env_val_develop[64] = "";
    int         health_txn_prefer_local_cache_over_dsp_cache;
    static int  num_of_hw_id_changed = 0;
    char        zhw_err_str[1000] = {0};
    int64_t     swap_config;
    int         cli_fohh_thread;
    int         validate_hw_key = 0;
    int         load_hw_info_fail = 0;
    char        err[1000];

	int slow_asst_init = 0;
    int     freebsd_compat_mode = 0;
    int64_t customer_gid = 0;
    enum zpn_enrollment_style enroll_style = ZPN_ENROLLMENT_STYLE_V2;
    char api_version[10] = {0};
    int enroll_v2_arg_passed = 0;
    char token[1024] = {0};
    char *env_oauth = NULL;
    int oauth_enroll = 0;

    config_fetch_ca_file[0] = '\0';

    /*
     * This have to be the first thing to be done. It pulls the --print-core argument out of argv.
     */
    zthread_do_stack_dump(&argc, argv);

    asst_fohh_thread_count = zpn_connector_get_max_fohh_threads();

    /*
     * Today, we are not considering the amount of CPU cores available in the system
     * We might want to allocate threads dynamically based on the available CPU cores in the future.
     * consider using get_nprocs()
     */

    res = fohh_worker_pool_parse_args(&argc, argv);
    if (res) {
        fprintf(stdout, "Could not parse worker pool args: %s\n", fohh_result_strings[res]);
        exit(1);
    }

    if (zpath_app_logging_parse_args(&argc, argv)) {
        exit(1);
    }

    /* Set default max logging size, will be rewritten if caller specifies */
    zpath_app_set_specific_max_logging_mb(DEFAULT_MAX_LOGGING_MB);
    fohh_set_connection_aggregated_hop_latency_stats(1);

    fohh_set_connection_aggregated_pipeline_latency_stats(1);

    memory_arena_count = -1;
    auto_upgrade_disabled = 0;
    stats_log_to_disk = 0;
    ut_mode = 0;
    always_re_enroll = 0;
    health_txn_prefer_local_cache_over_dsp_cache = 0;
    for (i = 1; i < argc; i++) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-plain") == 0) {
            cfg_write_plain = 1;
        } else if (strcmp(argv[i], "-quiet") == 0) {
            fohh_set_quiet(1);
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-role") == 0) {
            fprintf(stdout, "%s\n", APP_ROLE);
            exit(1);
        } else if (strcmp(argv[i], "-platform") == 0) {
            fprintf(stdout, "%s%d\n", ZPATH_PLATFORM_NAME, ZPATH_PLATFORM_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-arch") == 0) {
            fprintf(stdout, "%s\n", ZPATH_PLATFORM_ARCH);
            exit(1);
        } else if (strcmp(argv[i], "-direct") == 0) {
            cfg_api_direct = 1;
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-container") == 0) {
            assistant_state_set_container_env();
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-no_flow_control") == 0) {
            flow_control_enabled = 0;
        } else if (strcmp(argv[i], "-always_re_enroll") == 0) {
            always_re_enroll = 1;
        } else if (strcmp(argv[i], "-enroll_v2") == 0) {
            enroll_v2_arg_passed = 1;
        } else if (strcmp(argv[i], "-no_auto_upgrade") == 0) {
            auto_upgrade_disabled = 1;
		} else if (strcmp(argv[i], "-slow_init") == 0) {
			i++;
			slow_asst_init = atoi(argv[i]);
        } else if (strcmp(argv[i], "-stats_log_to_disk") == 0) {
            /* write a copy of stats log that we send to control broker to stats.log in local disk */
            stats_log_to_disk = 1;
        } else if (strcmp(argv[i], "-ut_mode") == 0) {
            ut_mode = 1;
        } else if (strcmp(argv[i], "-health_txn_prefer_local_cache_over_dsp_cache") == 0) {
            health_txn_prefer_local_cache_over_dsp_cache = 1;
        } else if (strcmp(argv[i], "-freebsd_compat_mode") == 0) {
            freebsd_compat_mode = 1;
            /* sarge won't notify us when running on ZscalerOS, but when on ZscalerOS, it will set -freebsd_compat_mode */
            assistant_state_set_zscaler_os();
	 } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                break;
            }
            if (strcmp(argv[i], "-threads") == 0) {
                i++;
                cli_fohh_thread = atoi(argv[i]);
                if ((cli_fohh_thread < 0) || (cli_fohh_thread > asst_fohh_thread_count)
                    || (cli_fohh_thread > FOHH_MAX_THREADS)) {
                    fprintf(stdout, "Thread count %d not supported in this hardware architecture max %d\n",
                            cli_fohh_thread, asst_fohh_thread_count);
                    exit(1);
                }
                asst_fohh_thread_count = cli_fohh_thread;
            } else if (strcmp(argv[i], "-dir") == 0) {
                i++;
                if (chdir(argv[i])) {
                    usage(argv[0], "Invalid directory: %s", argv[i]);
                    exit(1);
                }
            } else if (strcmp(argv[i], "-broker") == 0) {
                i++;
                broker_name = argv[i];
            } else if (strcmp(argv[i], "-logfile") == 0) {
                i++;
                logfile = argv[i];
            } else if (strcmp(argv[i], "-debug") == 0) {
                i++;
                debug_port = atoi(argv[i]);
            } else if (strcmp(argv[i], "-fproxy") == 0) {
                i++;
                fohh_proxy_hostname = (argv[i]);
            } else if (strcmp(argv[i], "-fproxy_port") == 0) {
                i++;
                fohh_proxy_port = atoi(argv[i]);
            } else if (strcmp(argv[i], "-repo_ca_file") == 0) {
                i++;
                snprintf(config_fetch_ca_file, MAX_CA_FILE_LEN , "%s", argv[i]);
            } else if (strcmp(argv[i], "-maxlogmb") == 0) {
                i++;
                zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
            } else if (strcmp(argv[i], "-memory_arena_count") == 0) {
                long lval = -1;
                i++;
                errno = 0;
                lval = strtol(argv[i], NULL, 10);

                if ((errno == ERANGE && (lval == LONG_MAX || lval == LONG_MIN))
                    || (errno != 0 && lval == 0)
                    || lval < 0) {
                    // incorrect parameters are ignored
                    memory_arena_count = -1;

                } else {
                    // value of 0 is allowed, as it the default value, determined by M_ARENA_TEST
                    memory_arena_count = lval;
                }

            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                // continue with the remaining arguments
                continue;
            }
        }
    }
    assistant_state_set_fohh_threads(asst_fohh_thread_count);

     if (0 == fohh_get_fohh_global_state_args_count()) {

        int count = MAX_FOHH_POOL;

        char *arr[MAX_FOHH_POOL];
        char fohh_pool_arr[MAX_FOHH_POOL][SIZE_FOHH_ARG_STRING];

        int last_asst_fohh_thread = asst_fohh_thread_count - 1;
        arr[0] = argv[0];
        snprintf(fohh_pool_arr[1], SIZE_FOHH_ARG_STRING, "-fohh-pool=data=%d,%d",
                 0, asst_fohh_thread_count - 2);
        snprintf(fohh_pool_arr[2], SIZE_FOHH_ARG_STRING, "-fohh-pool=wally=%d,%d",
                 last_asst_fohh_thread , last_asst_fohh_thread);
        snprintf(fohh_pool_arr[3], SIZE_FOHH_ARG_STRING, "-fohh-pool=control=%d,%d",
                 last_asst_fohh_thread, last_asst_fohh_thread);
        snprintf(fohh_pool_arr[4], SIZE_FOHH_ARG_STRING, "-fohh-pool=fohh_log=%d,%d",
                 last_asst_fohh_thread, last_asst_fohh_thread);
        for (int i = 1; i < MAX_FOHH_POOL; i++) {
            arr[i] = (char*) fohh_pool_arr[i];
        }
        fohh_worker_pool_parse_args(&count, arr);

    }

    /* Check if we are enabled for dev certs */
    if (zcrypt_metadata_get_zpa_develop_env(env_val_develop, sizeof(env_val_develop)) == ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Environment %s is set to %s", ZCRYPT_ZPA_DEVELOP_ENV, env_val_develop);
        develop_certs_mode = zcrypt_metatdata_get_develop_mode_from_env(env_val_develop);
    } else {
        develop_certs_mode = zcrypt_metadata_develop_mode_unknown;
    }

    if (assistant_state_is_container_env()) {
        zpath_app_enable_console_log();
    }

    if ((env_oauth = getenv(ENV_ZPA_OAUTH_ENROLLMENT))) {
        /* If OAuth based enrollment, use latest version(v4) of enrollment APIs */
        if (!strncmp(env_oauth, "TRUE", 4) && !is_oauth_enrollment_disabled()) {
            oauth_enroll = 1;
            assistant_enrollment_version = 4;
            enroll_style = ZPN_ENROLLMENT_STYLE_V4;
        }
    }

    if (-1 == memory_arena_count) {
        memory_arena_count = asst_fohh_thread_count + asst_non_fohh_thread_count;
    }

#ifdef __linux__
    /*
     * Have only one main thread arena(sbrk done here) + 12 thread arena(mmap done here). We don't want to aggressively
     * reduce the thread arena as that will lead to thread contention when accessing memory. Withtout the limit on
     * thread arena, we can see a huge growth in the number of thread arenas(say in some peak memory intensive events),
     * but the problem is that the entire arena have to be freed to release the memory back to the kernel.
     *
     * Allocator uses the main arena after it can't spawn any new thread arena. So there is no fear of memory
     * constraints.
     *
     * memory arena helps with thread contention when mallocing memory from heap during runtime.
     * ie, if there is only one heap, shared by all threads, when each thread doing malloc, it has to wait for that heap lock.
     * the more arenas we have, we can avoid fragmentation and less contention when accessing arenas, ref: https://man7.org/linux/man-pages/man3/mallopt.3.html
     *
     * arenas is there mainly to reduce lock of malloc.
     *
     * but too many allocated arenas can be wasteful as well, as each arena occupies a big chunk of continouges memory.
     * increase M_ARENA_MAX to match the number of threads is only useful when each threads is actively doing malloc operations.
     * REF: Multiple Arena section in article: https://sploitfun.wordpress.com/2015/02/10/understanding-glibc-malloc/
     *
     */
    mallopt(M_ARENA_MAX, memory_arena_count);
#endif

    /* Set this flag to ignore heartbeat exceeded crash when zthread is not healthy
     * must be called before zthread_init. Note that this will also trigger initialization
     * of zthread_monitor thread which is a watchdog thread for zthread */
    zthread_set_env_simple_app();
    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            fprintf(stderr, "fork failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            fprintf(stderr, "setsid failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

	init_openssl_allocator();

    /* If we are replicating the global DB, then we pay attention to
     * remote_host configuration for this DB when we initialize the
     * global DB. */
    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = APP_ROLE;
    app_params.fohh_thread_count = asst_fohh_thread_count;
    app_params.fohh_watchdog_s = ZPN_ASSISTANT_FOHH_HEARTBEAT_TIMEOUT_S;
    app_params.log_filename = logfile;
    app_params.debug_port = debug_port;
    app_params.debuglog = debuglog;
    app_params.personality = ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT;
    app_params.load_zpa_cloud_config = 0;

    result = zpath_simple_app_init(&app_params);
    if (result) {
        ZPN_LOG(AL_ERROR, "Error: Could not initialize\n");
        sleep(1);
        exit(1);
    }

    if ( config_fetch_ca_file[0] != '\0' ) {
        ZPN_LOG(AL_NOTICE, "Adding custom trusted ca certs from file %s\n", config_fetch_ca_file);
        fohh_add_ext_trusted_certs_from_file(config_fetch_ca_file);
    }

    /* Init enrollment lib */
    if (zpn_enroll_init(&enroll_state) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Unable to init enrollment api");
        exit(1);
    }
    enroll_state->enroll_type = ZPN_ENROLLMENT_TYPE_ASSISTANT;

    /*
     * Read proxy bypass config
     */
    if (fohh_proxy_hostname) {
        result = fohh_read_bypass_from_file("proxy-bypass");
        if (result == FOHH_RESULT_NOT_FOUND) {
            /* This is okay- the file not being there doesn't hurt
             * anyone */
        } else if (result == FOHH_RESULT_NO_ERROR) {
            /* This is okay- we read proxy config */
        } else {
            /* This is not okay- there was some error. */
            ZPN_LOG(AL_ERROR, "Could not properly parse proxy-bypass file");
            sleep(1);
            exit(1);
        }
    }

    zpn_event_collection = zpath_event_collection;

    ZPN_LOG(AL_NOTICE, "Connector version: %s", ZPATH_VERSION);
#ifdef __linux__
    ZPN_LOG(AL_NOTICE, "memory_arena_count=%d", memory_arena_count);
#endif

    if (!strcmp(APP_ROLE, "np-connector-child")) {
        global_assistant.app_role_type = np_connector;
    } else {
        global_assistant.app_role_type = app_connector;
    }
    ZPN_LOG(AL_NOTICE, "Setting App Role Type %d, %s", global_assistant.app_role_type, APP_ROLE);

    result = zpn_connector_verify_env();
    if (result != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Connector environment check failed - cannot proceed further");
        return 1;
    }

    result = zpn_dr_lib_init(zpath_event_collection, zpn_dr_system_type_app_connector, assistant_get_raw_dr_stats);
    if (result != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_dr_lib: %s", zpn_result_string(result));
        return 1;
    }

    if(is_zpn_drmode_enabled()) {
        ZPN_LOG(AL_NOTICE, "ZPN DR mode is detected!!");
        dr_mode_enabled = 1;
        ZPN_LOG(AL_WARNING, "Disabling auto upgrade in DR mode. current running version(%s)", ZPATH_VERSION);
        auto_upgrade_disabled = 1;
    }

    if (debuglog && dr_mode_enabled) {
          wally_debug =(
                      WALLY_DEBUG_RESULT_BIT |
                      WALLY_DEBUG_TABLE_BIT |
                      WALLY_DEBUG_REGISTRATION_BIT |
                      WALLY_DEBUG_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_BIT |
                      WALLY_DEBUG_FOHH_CLIENT_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_POLL_BIT |
                      WALLY_DEBUG_POSTGRES_FC_BIT |
                      WALLY_DEBUG_POSTGRES_CONN_BIT |
                      WALLY_DEBUG_POSTGRES_EVENT_BIT |
                      WALLY_DEBUG_ROW_DETAIL_BIT |
                      WALLY_DEBUG_WRITE_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_WRITE_BIT |
                      WALLY_DEBUG_TEST_ORIGIN_BIT |
                      WALLY_DEBUG_INTEREST_CB_BIT |
                      0);
    }


    ZPN_LOG(AL_NOTICE, "Checking Enrollment");

    enroll_state->zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                  1,
                                  NULL,
                                  "/etc/resolv.conf",
                                  "/etc/hosts",
                                  log_f,
                                  NULL);

    if (!enroll_state->zcdns) {
        ZPN_LOG(AL_ERROR, "Cannot start dns resolver");
        return 1;
    }

    /* Init ZVM. */
    result = zvm_init(zpath_event_collection, enroll_state->zcdns);
    if (result) {
        ZPN_LOG(AL_ERROR, "Cannot init zvm");
        return 1;
    }

    memset(debug_str, 0, sizeof(debug_str));
    load_hw_info_fail = load_prev_zhw_id_sha_info();
    if (load_hw_info_fail) {
        /* This is not a critical error for us to take an aggressive action */
        ZPN_LOG(AL_DEBUG, "Error while loading hw_id_info from file %s, it is possible that the file is not present", INSTANCE_ID_BIN_FILE);
    }

    /* Get Hardware ID */
    result = zhw_id_get(&cfg_hw_id, debug_str, sizeof(debug_str), zhw_err_str, sizeof(zhw_err_str), 1);

    /* since zhw_id_get won't exit when getting hw id or vm id fails, so we need to check the error message and print it. */
    if (strlen(zhw_err_str)) {
        ZPN_LOG(AL_ERROR, "Error occurred when getting hardware id: %s", zhw_err_str);
    }
    if (result) {
        ZPN_LOG(AL_ERROR, "Cannot get hardware id");
        return 1;
    }

    if (zcrypt_gen_key(&cfg_hw_key, cfg_hw_id.id, sizeof(cfg_hw_id.id)) != ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Cannot get key");
        return 1;
    }

    if (! load_hw_info_fail) {
        memset(debug_str, 0, sizeof(debug_str));
        result = validate_zhw_id_sha_info(&validate_hw_key, debug_str, sizeof(debug_str));
        if (! result) {
            if (!validate_hw_key && strnlen(debug_str, sizeof(debug_str))) {
                ZPN_LOG(AL_ERROR, "Validation for HW ID failed due to change in %s", debug_str);
            }
        } else {
            ZPN_LOG(AL_ERROR, "Cannot validate current zhw id info with the previous info");
        }
    } else {
        result = store_curr_zhw_id_sha_info();
        if (result) {
            ZPN_LOG(AL_DEBUG, "Error while storing current zhw id info into a file %s", INSTANCE_ID_BIN_FILE);
        }
    }

    res = zpn_enroll_get_instance_id(&cfg_hw_key, cfg_instance_bytes);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot get instance id");
        return 1;
    }

    global_assistant.imds_disabled = hw_id_fails.imds_disabled;
    global_assistant.imdsv2_required = hw_id_fails.imdsv2_required;
    global_assistant.disk_id_fail = hw_id_fails.disk_id_fail;

    for (i = 0; i < INSTANCE_ID_BYTES; i++) {
        cfg_fingerprint_bytes[i] = cfg_instance_bytes[i] ^ cfg_hw_id.id[i];
    }

    base64_encode_binary(cfg_fingerprint_str, cfg_fingerprint_bytes, sizeof(cfg_fingerprint_bytes));

    if (oauth_enroll) {
        res = zpn_enroll_get_oauth_key(cfg_provisioning_key, sizeof(cfg_provisioning_key));
    } else  {
        res = zpn_enroll_get_provisioning_key(&cfg_hw_key, cfg_provisioning_key, sizeof(cfg_provisioning_key));
    }

    if (res) {
        //ZPATH_LOG(AL_ERROR, "Cannot get provisioning key, please check if provision_key exists in the current working directory");
        ZPATH_LOG(AL_ERROR, "Cannot get oauth key, please check if provision_key exists in the current working directory");
        return 1;
    }


    enroll_state->cfg_rsa_key = zcrypt_rsa_key_create();
    if (!enroll_state->cfg_rsa_key) {
        ZPN_LOG(AL_ERROR, "Cannot create keyholder\n");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_enroll_get_private_key(&cfg_hw_key, enroll_state->cfg_rsa_key, FILENAME_KEY) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Cannot get private key\n");
        return ZPATH_RESULT_ERR;
    }

    int new_provision_key = 0;
    struct evbuffer *evbuffer = NULL;

    zpath_init_cloud_config();

    /* Read JWT Token required for V4 enrollment.
     * This will be available only for the fresh enrollment using OAuth Server
     */
    if (oauth_enroll) {
        fp = fopen(FILENAME_OAUTH_TOKEN, "r");
        if (fp) {
            if (fgets(token, sizeof(token), fp) == NULL) {
                ZPATH_LOG(AL_ERROR, "[OAuth] Unable to read token needed for V4 enrollment %s - %s",
                                                                                     FILENAME_OAUTH_TOKEN,
                                                                                     strerror(errno));
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            fclose(fp);
        }
    }

    switch (assistant_enrollment_version) {
        case 1:
            enroll_style = ZPN_ENROLLMENT_STYLE_V1;
            break;
        case 2:
            enroll_style = ZPN_ENROLLMENT_STYLE_V2;
            break;
        case 3:
            enroll_style = ZPN_ENROLLMENT_STYLE_V3;
            break;
        case 4:
            enroll_style = ZPN_ENROLLMENT_STYLE_V4;
            break;
    }

    if (oauth_enroll) {
        res = zpath_get_oauth_cloud_details(cfg_provisioning_key, cfg_key_api, cfg_key_cloud, &customer_gid);
        ZPATH_LOG(AL_NOTICE, " OAuth API_KEY [%s] KEY_CLOUD [%s] CUSTOMER_GID [%"PRId64"]", cfg_key_api, cfg_key_cloud, customer_gid);
    } else {
        res = zpath_get_provisioning_key_details(cfg_provisioning_key, cfg_key_api, cfg_key_cloud);
    }

    /* Only if parsing of new provisioning key is success
     * and we have a valid cloud name, we call the enrollment with api and cloud name
     */

    if ( (res == ZPATH_RESULT_NO_ERROR)  &&
          (cfg_key_cloud[0] != '\0')) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(cfg_key_cloud,
                                        enroll_state->cfg_rsa_key,
                                        cfg_fingerprint_str,
                                        cfg_provisioning_key,
                                        cfg_key_api,
                                        sizeof(cfg_key_api),
                                        ZPN_ENROLLMENT_TYPE_ASSISTANT,
                                        enroll_style,
                                        token,
                                        customer_gid);
        new_provision_key = 1;
    }

    result = zpath_load_cloud_config_for_customer_apps(&cfg_hw_key, cfg_key_api, evbuffer);
    if(result != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Cannot load zpa_cloud_config for the given cloud ");
        return -1;
    }

    if (!oauth_enroll && zpn_enroll_extract_key_fields(cfg_provisioning_key, &cfg_key_shard, cfg_key_api, sizeof(cfg_key_api),
                                      cfg_key_cloud, sizeof(cfg_key_cloud), &root_cert,
                                      &root_cert_time) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Invalid provisioning key: <%.*s...>",
                  PROVISION_KEY_LOG_BYTES,
                  cfg_provisioning_key);
        return 1;
    }
    zthread_set_cloud_name(cfg_key_cloud);

    if ( new_provision_key == 0 ) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(cfg_key_cloud,
                                        enroll_state->cfg_rsa_key,
                                        cfg_fingerprint_str,
                                        cfg_provisioning_key,
                                        cfg_key_api,
                                        sizeof(cfg_key_api),
                                        ZPN_ENROLLMENT_TYPE_ASSISTANT,
                                        enroll_style,
                                        token,
                                        customer_gid);

    }
    result = zpn_enroll_get_enrollment_details(cfg_key_api, sizeof(cfg_key_api), evbuffer,
                                               cfg_key_cloud, &root_cert, &root_cert_time, &customer_gid,
                                               api_version, sizeof(api_version),
                                               NULL, 0, NULL);

    if (result) {
        /*
         * Get enrollment details failed, still continue and use api hostname mentioned in
         * provision key and cloud cert coming from hardcoded data.
         */
        ZPN_LOG(AL_NOTICE, "Could not fetch enrollment details but it's okay, continuing with existing data");
    } else {
        /* Use V3 enrollment style if api_version returned is "V3". Otherwise V2 by default. */
        if (enroll_v2_arg_passed == 0 && api_version[0] != 0 && !strcasecmp(api_version, "v3")) {
            assistant_enrollment_version = 3;
            enroll_style = ZPN_ENROLLMENT_STYLE_V3;
            ZPN_LOG(AL_DEBUG, "Received api_version: %s in get_enrollment_details response, using api style %d for enrollment",
                    api_version, enroll_style);
        }
    }

    if (!root_cert && (stat(FILENAME_CLOUD_ROOT, &st) != 0)) {
        /*
         * This is a fresh enrollment (cloud.pem doesn't exist) and couldn't get cloud cert
         * from hardcoded data and fetch from api also failed. Log and stop.
         */
        ZPN_LOG(AL_ERROR, "Could not get cloud cert, stopping.");
        return ZPATH_RESULT_ERR;
    }

    /* this is being called here because zpn_enroll_check() is going to need reenroll_period from local site config */
    assistant_site_read_config_with_lock();

    result = zpn_enroll_check(&cfg_hw_key, enroll_state->cfg_rsa_key, cfg_fingerprint_str, cfg_provisioning_key,
                              cfg_key_shard, cfg_key_api, ZPN_ENROLLMENT_TYPE_ASSISTANT,
                              assistant_enrollment_version == 1 ? ZPN_ENROLLMENT_STYLE_V1 : enroll_style, cfg_key_cloud,
                              assistant_state_get_current_working_directory(), always_re_enroll,
                              assistant_site_get_reenroll_period_with_lock(), customer_gid, token);
    if (result) {
        //ZPN_LOG(AL_ERROR, "Failed to enroll.");
        return ZPATH_RESULT_ERR;
    }

    /* always init no matter if its np, this is to have data ready, not functional init */
    np_connector_enroll_state_init(enroll_state->cfg_rsa_key, cfg_key_api, ZPN_ENROLLMENT_TYPE_ASSISTANT, enroll_style,
                                       cfg_key_cloud, cfg_provisioning_key, cfg_fingerprint_str, &cfg_hw_key);

    struct zpath_cloud_config * cloud_config=zpath_get_cloud_config_from_name(cfg_key_cloud);
    if(!cloud_config) {
        ZPN_LOG(AL_ERROR, "Failed to get cloud config file based on cloud name");
        return ZPATH_RESULT_ERR;
    }
    if (stat(FILENAME_CLOUD_ROOT, &st) != 0) {
        // File not found...

        if (root_cert) {
            write_cloud_root = 1;
        } else {
            ZPN_LOG(AL_ERROR, "root_cert is NULL after enroll check");
        }
    } else {
        // Do root_cert_time check only if it's known cloud
        if (!zpath_is_flex_cloud(cfg_key_cloud)) {
            if (root_cert && (st.st_mtime != root_cert_time)) {
                // File has wrong timestamp...
                write_cloud_root = 1;
                unlink(FILENAME_CLOUD_ROOT);
                ZPN_LOG(AL_NOTICE, "Connector is updating cloud certificates");
            }
        }
    }

    if (write_cloud_root) {
        struct utimbuf tm;

        fp = fopen(FILENAME_CLOUD_ROOT, "w");
        if (!fp) {
            ZPN_LOG(AL_ERROR, "Could not open file for writing cloud certificates");
            return ZPATH_RESULT_ERR;
        }
        if (fwrite(root_cert, strlen(root_cert), 1, fp) != 1) {
            fclose(fp);
            ZPN_LOG(AL_ERROR, "Could not write cloud certificates");
            return ZPATH_RESULT_ERR;
        }

        fclose(fp);

        tm.actime = root_cert_time;
        tm.modtime = root_cert_time;
        if (utime(FILENAME_CLOUD_ROOT, &tm) != 0) {
            ZPN_LOG(AL_ERROR, "Could not update modification time on cloud certificates: %s. Continuing...", strerror(errno));
        }
    }

    cfg_pkey = EVP_PKEY_new();
    res = EVP_PKEY_set1_RSA(cfg_pkey, zcrypt_rsa_key_get_rsa(enroll_state->cfg_rsa_key));
    if (res != 1) {
        ZPATH_LOG(AL_ERROR, "Could not configure private key");
        return ZPATH_RESULT_ERR;
    }

    result = fohh_reset_global_ssl_ctx(FILENAME_CLOUD_ROOT,
                                       FILENAME_CERT,
                                       FILENAME_KEY_PRIV,
                                       cfg_pkey,
                                       VERIFY_PEER);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not configure SSL for cloud communication");
        return ZPATH_RESULT_ERR;
    }

    result = zdtls_reset_global_ssl_ctx(FILENAME_CLOUD_ROOT,
                                        FILENAME_CERT,
                                        FILENAME_KEY_PRIV,
                                        cfg_pkey);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not configure DTLS SSL for cloud communication");
        return ZPATH_RESULT_ERR;
    }

    /* Need to read assistant ID out of certificate. */
    {
        struct zcrypt_cert *cert;
        char *w;

        cert = zcrypt_cert_read(FILENAME_CERT);
        if (!cert) {
            ZPN_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (fohh_x509_get_cn(zcrypt_cert_get_x509(cert), asst_name, sizeof(asst_name))) {
            ZPN_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            zcrypt_cert_free(cert);
            exit(1);
        }
        zcrypt_cert_free(cert);

        /* Parse first integer of CN */
        for (w = &(asst_name[0]); *w; w++) {
            if (isdigit(*w)) {
                assistant_id = strtoll(w, &w, 0);
                break;
            }
        }

        if ((*w) == '.') w++;
        dom = w;

        if (!assistant_id) {
            ZPN_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }
        if (ZPATH_SHARD_FROM_GID(assistant_id) == 0) {
            assistant_id |= 0x0300000000000000ll;
        }
    }

    /* Only now we know the name of the inst, so update that to the zthread infra */
    zthread_init(APP_ROLE, ZPATH_VERSION, asst_name, NULL, get_assistant_additional_debug_logs);

    /* Need to read Organization Name out of certificate. */
    {
        struct zcrypt_cert *cert;
        char org_name[128] = {0};
        cert = zcrypt_cert_read(FILENAME_CERT);

        if (!cert) {
            ZPN_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (!fohh_x509_get_on(zcrypt_cert_get_x509(cert), org_name, sizeof(org_name))) {
            zthread_set_org_name(org_name);
        }
        else {
            ZPN_LOG(AL_NOTICE, "Could not get ON from cert file %s", FILENAME_CERT);
            sleep(1);
        }

        zcrypt_cert_free(cert);
    }

    customer_id = ZPATH_GID_GET_CUSTOMER_GID(assistant_id);

    zpn_init_debug();

    result = zpn_assistant_double_encrypt_ssl_ctx_init(FILENAME_ROOT, FILENAME_CERT, cfg_pkey);
    if (result) {
        ZPN_LOG(AL_NOTICE, "Cannot create inner tunnel SSL context");
    }

    result = zpn_assistant_mtunnel_mtls_ssl_ctx_init(FILENAME_ROOT, FILENAME_CERT, NULL, cfg_pkey);
    if (result) {
        ZPN_LOG(AL_NOTICE, "Cannot create mtunnel tunnel SSL context");
    }

    zpath_config_override_monitor_int(CONFIG_FOHH_LOG_MAX_IN_FLIGHT,
                                      &fohh_log_max_logs_in_flight,
                                      NULL,
                                      FOHH_LOG_MAX_LOGS_IN_FLIGHT_DEFAULT,
                                      (int64_t)assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)1,
                                      (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_CONNECTOR_QUIET,
                                      &connector_quiet_cfg,
                                      connector_quiet_config_override_monitor_callback,
                                      CONFIG_CONNECTOR_QUIET_DEFAULT,
                                      (int64_t)customer_id,
                                      (int64_t)0);

    /* FOHH Status Interval related feature flags */
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_CTL,
                                      &ctl_status_interval,
                                      connector_ctl_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_STATS,
                                      &stats_status_interval,
                                      connector_stats_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_LOG,
                                      &log_status_interval,
                                      connector_log_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
	zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_WAF,
                                      &waf_status_interval,
                                      connector_waf_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
	zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_APP_INSPECTION,
                                      &app_inspection_status_interval,
                                      connector_app_inspection_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_CFG,
                                      &cfg_status_interval,
                                      connector_cfg_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_OVD,
                                      &ovd_status_interval,
                                      connector_ovd_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_DATA,
                                      &data_status_interval,
                                      connector_data_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)0);
    /* Read argo logging memory threshold from config override and update */
    zpath_config_override_monitor_int(ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                                      &argo_logging_threshold_percentage,
                                      argo_logging_threshold_config_override_monitor_callback,
                                      DEFAULT_ARGO_MEM_THRESHOLD_PERCENTAGE_ASSISTANT,
                                      (int64_t)assistant_id,
                                      (int64_t)customer_id,
                                      (int64_t)1,
                                      (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_ZPN_EVENT_CPU_STARVATION_INTERVAL,
                                      &cpu_starvation_interval,
                                      connector_cpu_starvation_config_override_monitor_callback,
                                      CONFIG_ZPN_EVENT_CPU_STARVATION_DEFAULT_INTERVAL,
                                      assistant_id,
                                      customer_id,
                                      (int64_t)1,
                                      (int64_t)0);

    ZPN_LOG(AL_NOTICE, "Initializing assistant: %ld, customer_id: %ld belonging to customer domain: %s",
            (long) assistant_id, (long) customer_id, dom);

    result = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                      zpn_debug_catch_cnxt(),
                                      zpn_debug_cnxt_cnt(),
                                      "zpn",
                                      zpn_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    /* Create the SSL contexts we use for communicating. These should basically never fail. */
    SSL_CTX *public_cloud_ctx = fohh_client_ssl_ctx_create_with_cipher(FILENAME_CLOUD_ROOT,
                                                           FILENAME_CERT,
                                                           FILENAME_KEY_PRIV,
                                                           cfg_pkey,
                                                           FOHH_SET_CUSTOM_CIPHERSUITE_INDEX);
    if (!public_cloud_ctx) {
        ZPN_LOG(AL_ERROR, "Public SSL context creation failed");
        return ZPN_RESULT_ERR;
    }
    SSL_CTX *private_cloud_ctx = fohh_client_ssl_ctx_create_with_cipher(FILENAME_ROOT,
                                                            FILENAME_CERT,
                                                            FILENAME_KEY_PRIV,
                                                            cfg_pkey,
                                                            FOHH_SET_CUSTOM_CIPHERSUITE_INDEX);
    if (!private_cloud_ctx) {
        ZPN_LOG(AL_ERROR, "Private SSL context creation failed");
        return ZPN_RESULT_ERR;
    }

    SSL_CTX *public_cloud_ctx_dtls = zdtls_client_ssl_ctx_create(FILENAME_CLOUD_ROOT,
                                                                 FILENAME_CERT,
                                                                 FILENAME_KEY_PRIV,
                                                                 cfg_pkey);
    if (!public_cloud_ctx_dtls) {
        ZPN_LOG(AL_ERROR, "Public SSL context for DTLS creation failed");
        return ZPN_RESULT_ERR;
    }

    SSL_CTX *private_cloud_ctx_dtls = zdtls_client_ssl_ctx_create(FILENAME_ROOT,
                                                                  FILENAME_CERT,
                                                                  FILENAME_KEY_PRIV,
                                                                  cfg_pkey);
    if (!public_cloud_ctx_dtls) {
        ZPN_LOG(AL_ERROR, "Public SSL context for DTLS creation failed");
        return ZPN_RESULT_ERR;
    }

    global_assistant.dr_handler_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

    assistant_dr_debug_init();
    /*
     * Spawn assistant DR thread...
     */
    result = assistant_dr_init(NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant DR thread: %s", zpn_result_string(result));
        return result;
    }

    if (assistant_features_is_qbr_insights_feature_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
        if (zvm_ensure_region_id(err, sizeof(err)) != ZVM_RESULT_NO_ERROR) {
            ZPN_LOG(AL_WARNING, "Failed to retrieve region ID information from the IMDS service on the first attempt. Error: %s", err);
            /* Retry retrieving the region ID information */
            if (zvm_ensure_region_id(err, sizeof(err)) != ZVM_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Second attempt to retrieve region ID information from the IMDS service also failed. Error: %s", err);
            }
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Skipping region ID retrieval as the QBR extension feature is disabled by default.");
    }

    assistant = assistant_init(assistant_id, dom, cfg_key_cloud, broker_name, asst_name, FILENAME_CERT,
                               auto_upgrade_disabled, stats_log_to_disk, assistant_enrollment_version, ut_mode,
                               public_cloud_ctx, private_cloud_ctx, public_cloud_ctx_dtls, private_cloud_ctx_dtls,
                               health_txn_prefer_local_cache_over_dsp_cache, slow_asst_init, freebsd_compat_mode);
    if (!assistant) {
        ZPN_LOG(AL_CRITICAL, "Connector initialization failed!");
        sleep(10);
        return ZPN_RESULT_ERR;
    }


    result = zpn_dr_lib_cfg_override_register(zpath_event_collection, zpn_dr_system_type_app_connector);
    if (result) {
        ZPN_DR_LOG(AL_ERROR, "zpn_dr_lib_cfg_override_register failed: %s",
                zpath_result_string(result));
        return result;
    }

    /* Get the runtime OS information for the assistant */
    zhw_get_runtime_os(g_asst_runtime_os, sizeof(g_asst_runtime_os));

    ZPN_LOG(AL_NOTICE, "Configured CPUs=%d, Available CPUs=%d",
        assistant_state_get_configured_cpus(), assistant_state_get_available_cpus());

    int64_t     system_total_bytes = 0;
    zpath_system_get_system_memory(&system_total_bytes,
                                   NULL,
                                   NULL,
                                   &swap_config,
                                   NULL);

    assistant_state_set_swap_config(swap_config);
    assistant_state_allocator_libevent_sys_mem_total_bytes_set(system_total_bytes);

    if(cloud_config) {
        zpn_enroll_upload_stack(cloud_config->sarge->stack_upload_host, cloud_config->sarge->stack_upload_path, APP_ROLE, asst_name, customer_id);
        zpn_enroll_upload_stack(cloud_config->sarge->stack_upload_host, cloud_config->sarge->stack_upload_path, SARGE_ROLE, asst_name, customer_id);
    }

#if 0
    // needs to be updated
    zpn_debug =
        (ZPN_DEBUG_CLIENT_BIT) |
        (ZPN_DEBUG_BROKER_BIT) |
        (ZPN_DEBUG_APPLICATION_BIT) |
        (ZPN_DEBUG_RULE_BIT) |
        (ZPN_DEBUG_DISPATCHER_BIT) |
        (ZPN_DEBUG_LEARN_BIT) |
        (ZPN_DEBUG_APPLICATION_SERVER_BIT) |
        (ZPN_DEBUG_ASSISTANT_BIT) |
        (ZPN_DEBUG_BROKER_ASSISTANT_BIT) |
        (ZPN_DEBUG_SIGNING_CERT_BIT) |
        (ZPN_DEBUG_ISSUEDCERT_BIT) |
        //(ZPN_DEBUG_MCONN_BIT) |
        (ZPN_DEBUG_CLIENT_TABLE_BIT) |
        (ZPN_DEBUG_IDP_BIT) |
        (ZPN_DEBUG_TIMER_BIT) |
        (ZPN_DEBUG_AUTH_BIT) |
        (ZPN_DEBUG_APPLICATION_GROUP_BIT) |
        (ZPN_DEBUG_HEALTH_REPORT_BIT) |
        //(ZPN_DEBUG_LOG_BIT) |
        (ZPN_DEBUG_MTUNNEL_BIT) |
        (ZPN_DEBUG_CONFIG_BIT) |
        //(ZPN_DEBUG_BROKER_LOAD_BIT) |
        0;
#endif

    if (debuglog) {
        zpath_debug |=
                    (ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
                    0;
    }
    zdtls_debug = (
                   ZDTLS_DEBUG_SETUP_BIT |
                   //ZDTLS_DEBUG_SESSION_BIT |
                   //ZDTLS_DEBUG_BIO_BIT |
                   //ZDTLS_DEBUG_PACKET_BIT |
                   0);

    zrdt_debug = (
                   //ZRDT_DEBUG_BIT |
                   //ZRDT_DEBUG_PACKET_BIT |
                   //ZRDT_DEBUG_STREAM_BIT |
                   //ZRDT_DEBUG_CAPTURE_BIT |
                   //ZRDT_DEBUG_PING_BIT |
                   //ZRDT_DEBUG_CONN_BIT |
                   //ZRDT_DEBUG_STATS_BIT |
                   0);

    zdtls_init(zpn_event_collection);
    zrdt_init(zpn_event_collection, asst_fohh_thread_count);
    zrdt_set_cloud_environment(assistant_state_is_dev_environment());

    result = zpath_debug_add_flag(&zdtls_debug,
                                 zdtls_debug_catch_defaults,
                                 "zdtls",
                                 zdtls_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zrdt_debug,
                                  zrdt_debug_catch_defaults,
                                  "zrdt",
                                  zrdt_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&argo_debug,
                                  argo_debug_catch_defaults,
                                  "argo",
                                  argo_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(result));
        return result;
    }

    signal(SIGPIPE, SIG_IGN);
    zpa_connector_set_up_termination_handler();

    ZPN_LOG(AL_NOTICE, "Initialization Complete");
    /*
     * FIXME: How we do I make sure AUM component know that I am stuck with this
     * version? :-(
     */
    if (auto_upgrade_disabled) {
        ZPN_LOG(AL_WARNING, "Assistant booted up with version(%s), auto upgrade disabled", ZPATH_VERSION);
    }

    zpath_registration_completed();

    /*
     * Let us register to zthread only after the init is done. This is because the init can hang for minutues/hours
     * together if the connection to cloud is DOWN or network is choppy. Also sarge is monitoring the status of
     * assistant - it it doesn't connect to the cloud in certain time it anyway sends SIGTERM to the child process.
     * Since sarge is detecting the long-hang of child process, we don't want zthread also to do heartbeat monitoring
     * of this. Having said that from this point onwards, we are not doing anything fancy, let us register to zthread
     * infra to get the free goodies that zthread has to offer.
     *
     * Note that zthread_init is to initialize the generic zthread infra. zthread_register_self is to convert the main
     * thread into a zthread.
     */
    zthread = zthread_register_self(asst_name, 60);

    global_assistant.z_info = zthread;

    res = zpn_event_init(assistant_state_is_dev_environment(), 1);
    if (res) {
        ZPN_LOG(AL_ERROR, "zpn_event_init failed: %s", zpath_result_string(res));
        return res;
    }

    /* setup cpu starvation event notifications */

    {
        int64_t scope_gid = 0;

        global_assistant.scope_gid = global_assistant.customer_gid;

        res = assistant_cfg_assistant_get_scope_gid(&scope_gid);
        if (res == ZPN_RESULT_NO_ERROR) {
            global_assistant.scope_gid = scope_gid;
            ZPN_LOG(AL_NOTICE, "Scope gid for assistant %"PRId64" is %"PRId64, global_assistant.gid, scope_gid);
        } else {
            ZPN_LOG(AL_ERROR, "failed to obtain scope id for asst %"PRId64, global_assistant.gid);
        }

        zpn_event_system_register(zpn_event_system_assistant,
                              global_assistant.gid,
                              global_assistant.grp_gids[0],
                              global_assistant.customer_gid,
                              is_scope_default(global_assistant.scope_gid) ? 0 : global_assistant.scope_gid,
                              NULL);
    }

    /* when running on FreeBSD linux compat mode, the system picks first thread (lexicographical order) as process name
     * therefore connector child is renamed to 'admin_probe' (as of Jan.2023)..
     * the following code is called when linux compat mode is enabled, to bring back the right name.
     * Note: we should call this function delayed (end of initialization phase),
     *       this gives FreeBSD extra time to mess it up :) otherwise name will be reset by FreeBSD system
     */
    if (freebsd_compat_mode) {
        set_process_name(APP_ROLE);
    }

    int is_asst_enabled = 0;
    int64_t connector_type = app_connector;
    assistant_cfg_assistant_is_enabled(&is_asst_enabled, &connector_type);

    if (connector_type == np_connector) {

        while (!(np_connector_is_initialized() && np_connector_get_connector_gid())) {
            zthread_heartbeat(zthread);
            ZPN_LOG(AL_INFO, "waiting for np connector initialization....");
            sleep(1);
        }

        res = assistant_admin_probe_init(np_connector_get_np_wally(), np_connector_get_connector_gid(),
                             global_assistant.customer_gid, 1, zpn_event_collection, 1);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init assistant_admin_probe_init: %s", zpn_result_string(res));
            return res;
        }

        res = np_frr_utils_init(zpn_np_instance_type_connector, np_connector_get_connector_gid(), global_assistant.customer_gid, np_connector_get_bgp_peer_info_from_neighbor_ip);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init np_init_appc frr utils - %s", zpath_result_string(res));
            return res;
        }
    }

    while(1) {
        int64_t time_delta_us = 0;

        /*
         * Do this the first thing, so that even if fohh connection to control_broker is DOWN(when we loop in
         * assistant_control_get_time_differential), we will still keep us alive.
         */
        zthread_heartbeat(zthread);

        if (!is_zpn_drmode_enabled() && assistant_control_get_time_differential(&time_delta_us) != ZPN_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_NOTICE, "Connector- waiting for time synchronization");
            once_waiting = 1;
            sleep(1);
            continue;
        }

        if (once_waiting) {
            int64_t d;
            char neg = '-';
            if (time_delta_us < 0) {
                d = 0 - time_delta_us;
            } else {
                d = time_delta_us;
                neg = '+';
            }
            ZPATH_LOG(AL_NOTICE, "Connector- Time synchronized, Local time %c %ld.%06lds = cloud_time",
                      neg,
                      (long) d / 1000000, (long) d % 1000000);
            once_waiting = 0;
        }

        /* Upgrade prepare connector */
        assistant_state_set_time_lag_us(time_delta_us);
        assistant_state_pause_evaluate();
        zpn_enroll_upgrade_prep(zpn_connector_upgrade_prep, assistant_id, (void *)assistant, time_delta_us,
                                auto_upgrade_disabled);

        assistant_admin_probe_restart_evaluable();
        zthread_heartbeat(zthread);

        res = zhw_id_check_changed();
        if (res > num_of_hw_id_changed) {
            num_of_hw_id_changed = res;
            assistant_state_set_hw_id_changed_and_log_time_us(epoch_us(), num_of_hw_id_changed);
        }

        sleep(1);
    }
}



int main(int argc, char *argv[]) {
    int result;

    /* Turn off buffering of stdout. This needs to be called before any stdout operation */
    setvbuf(stdout, NULL, _IONBF, 0);

    assistant_state_set_uptime_s();

    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
