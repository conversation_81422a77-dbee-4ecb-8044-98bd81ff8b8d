/*
 * assistant_cfg_override_feature.h. Copyright (C) 2020 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_CFG_OVERRIDE_FEATURE_H_
#define _ASSISTANT_CFG_OVERRIDE_FEATURE_H_

#include "admin_probe/admin_probe_public.h"

#define  ASSISTANT_TARGET_MAX_AVG_RTT_SAMPLES  200

void
assistant_cfg_override_feature_process();

int
assistant_cfg_override_feature_get_is_to_app_server_keepalive_enabled();

int
assistant_cfg_override_feature_is_connector_admin_probe_enabled(enum admin_probe_task_type type);

int
assistant_cfg_override_get_sample_size_for_avg_rtt();

int
assistant_cfg_override_feature_get_tcp_keepalive_enable_from_app_segment();

uint64_t
assistant_cfg_override_feature_get_ad_protection_protocol_override();

int
assistant_cfg_override_zcdns_rcode_retry_enables();

int
assistant_cfg_override_is_fc_reset_recover_enabled();

int64_t
assistant_cfg_override_get_fc_reset_recover_interval_s();

int64_t
assistant_cfg_override_get_fc_reset_recover_threshold_times_hundred();

int
assistant_cfg_override_is_dns_check_use_static_server_config_enabled();

int
assistant_cfg_override_is_idle_data_conn_timeout_config_disabled();

int64_t
assistant_cfg_override_disable_tx_app_route_reg();
int64_t
assistant_cfg_override_get_idle_data_connection_timeout_s();

int64_t
assistant_cfg_override_get_udp_server_inactivity_timeout_s();

int64_t
assistant_cfg_override_get_udp_server_inactivity_fast_timeout_s();

#endif // _ASSISTANT_CFG_OVERRIDE_FEATURE_H_
