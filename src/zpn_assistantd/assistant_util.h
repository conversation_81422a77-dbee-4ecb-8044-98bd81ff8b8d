/*
 * assistant_util.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_UTIL_H_
#define _ASSISTANT_UTIL_H_

#define ASSISTANT_UTIL_DOMAIN_MAX_LEN        256

enum zpn_assistant_firedrill_state {
    ZPN_ASSISTANT_FIREDRILL_DISABLED = 0,
    ZPN_ASSISTANT_FIREDRILL_ENABLED,
    ZPN_ASSISTANT_FIREDRILL_TRANSIT,
};

int zpn_assistant_get_firedrill_state();

#endif // _ASSISTANT_UTIL_H_
