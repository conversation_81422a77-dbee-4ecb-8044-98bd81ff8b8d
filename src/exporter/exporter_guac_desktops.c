/*
 * exporter_guac_desktops.c Copyright (C) 2025 Zscaler Inc. All Rights Reserved
 *
 * Disposable jumpbox management in PRA
 */

#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpa_cloud_config.h"

#include "exporter/exporter.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_guac_api.h"

#include "zpn/zpn_customer_config.h"
#include "exporter/exporter_guac_desktops.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#ifdef UNIT_TEST
#include "exporter/exporter_guac_desktops_test.h"
#endif
#define ALLOC_COPY_STR(dest, src)                \
    do {                                         \
        const char *ptr = src;                   \
        if (NULL != ptr && ptr[0] != '\0')       \
            dest = EXPORTER_STRDUP(ptr, strlen(ptr)); \
    } while (0)

/* forward declarations */
static int parse_djb_info_response(struct exporter_request *request);
static int zpn_sra_djb_get_ipaddr_and_cred(struct exporter_request *request, int64_t console_id);

int check_query_param_djb(struct exporter_request *request)
{
    int is_djb = 0;
    char query_value[10] = {0};
    int ret = extract_query_value_from_request(request, query_value, sizeof(query_value), "djb");
    if (ret == ZPATH_RESULT_NO_ERROR) {
        is_djb = (int)strtol(query_value, NULL, 0);
        return (is_djb == 1);
    }
    return is_djb;
}

/* Invoked by websocket-tunnel call
 * Use djb ID to get IPAddress and credentials from the Privileged Desktop service
 */
int zpn_sra_djb_get_info(struct exporter_request *request, int64_t console_id)
{
    int result = ZPATH_RESULT_NO_ERROR;

    /* Get the ipaddr and creds from the priv desktop service */
    result = zpn_sra_djb_get_ipaddr_and_cred(request, console_id);
    if (result != ZPATH_RESULT_NO_ERROR ) return result;

    if (request->djb_info->djb_console_details_complete) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: DJB Console details already filled up in the request structure", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!request->sra_host_fqdn && request->djb_info->ipaddr) {
        request->sra_host_fqdn = EXPORTER_STRDUP(request->djb_info->ipaddr, strlen(request->djb_info->ipaddr));
    }
    request->sra_host_port = request->djb_info->port;
    request->pra_console_id = console_id;

    if (!request->pra_console_name) {
        request->pra_console_name = EXPORTER_STRDUP(DJB_CONSOLE_NAME, strlen(DJB_CONSOLE_NAME));
    }

    if (!request->sra_host_protocol) {
        if (strncasecmp(request->djb_info->os_type, DJB_CONSOLE_WIN_OS, strlen(DJB_CONSOLE_WIN_OS)) == 0) {
            request->sra_host_protocol = EXPORTER_STRDUP(DJB_CONSOLE_WIN_PROTOCOL, strlen(DJB_CONSOLE_WIN_PROTOCOL));
        } else {
            request->sra_host_protocol = EXPORTER_STRDUP(DJB_CONSOLE_LINUX_PROTOCOL, strlen(DJB_CONSOLE_LINUX_PROTOCOL));
        }
    }

    if (!request->sra_host_conn_security) {
        request->sra_host_conn_security = EXPORTER_STRDUP(DJB_RDP_CONN_SECURITY, strlen(DJB_RDP_CONN_SECURITY));
    }

    result = exporter_request_policy_state_check_access_for_djb(request);
    EXPORTER_DEBUG_USER_PORTAL_API("%s: Access Policy for DJB subnet returned result = %s, app_id = %"PRId64, request->name, zpath_result_string(result), request->app_id);

    if (result == ZPATH_RESULT_ASYNCHRONOUS) {
        return result;
    }

    if (!request->sra_remote_ip && request->app_id) {
        result = get_app_server(request, request->app_id);
        if (result == ZPATH_RESULT_ASYNCHRONOUS) {
            return result;
        }
    }

    request->djb_info->djb_console_details_complete = 1;
    return result;
}

/*
 * Allocate djb_info structure to hold ipaddr, creds and other details
 * If request is being reprocessed, parse the json djbinfo response
 * If request is processed first time, send djbinfo API call to Priv Desktop service
 */
static int zpn_sra_djb_get_ipaddr_and_cred(struct exporter_request *request, int64_t console_id)
{
    int result = ZPATH_RESULT_NO_ERROR;

    if (!request->djb_info ) {
        request->djb_info = EXPORTER_CALLOC(sizeof(struct djb_data));
        if (!request->djb_info ) {
            EXPORTER_LOG(AL_ERROR, "%s: Failed memory allocation", request->name);
            return ZPATH_RESULT_ERR;
        }

        request->djb_info->djb_response_data = evbuffer_new();
        if (!request->djb_info->djb_response_data) {
            EXPORTER_LOG(AL_ERROR, "%s: Failed memory allocation", request->name);
            return ZPATH_RESULT_ERR;
        }
        request->djb_info->djb_id = console_id;
        request->djb_info->rule_id = request->portal_policy_rule_id;
    }

    if (request->djb_info->djb_console_details_complete || request->djb_info->ipaddr) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Obtained credentials already", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!request->djb_info->djb_response_data && request->djb_info->djb_server_request_complete) {
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        EXPORTER_LOG(AL_ERROR, "%s: Privileged Desktop Service request failed to get djbinfo with an empty response.", request->name);
        return ZPATH_RESULT_ERR;
    } else if (request->djb_info->djb_response_data && request->djb_info->djb_server_request_complete) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Parse djbinfo API response and close DJB service connection", request->name);
        result = parse_djb_info_response(request);
        exporter_user_portal_conn_webserver_message_complete_cb(request);
        return result;
    }

    if (!request->generated_request_data) {
        request->generated_request_data = evbuffer_new();
        evbuffer_add_printf(request->generated_request_data, "?userEmail=%s&id=%"PRId64, request->policy_state->user_email, console_id);
    }

    /* Invoke connection to Privileged Desktop Service */
    result = exporter_sra_portal_handle_privileged_desktops_api(request);
    if (result == ZPATH_RESULT_NO_ERROR) {
        /* djbinfo api request read callback will reprocess request */
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Initiated connection to Privileged Desktop Service for djbinfo API call", request->name);
        result = ZPATH_RESULT_ASYNCHRONOUS;
    }
    return result;
}

/* Parse JSON response sent by Privileged Desktop Service for API: djbinfo */
static int parse_djb_info_response(struct exporter_request *request)
{
    size_t datalen = evbuffer_get_length(request->djb_info->djb_response_data);
    if (!datalen) {
        EXPORTER_LOG(AL_ERROR, "%s, djb_response_data datalen is 0", request->name);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }
    char *data = (char *)evbuffer_pullup(request->djb_info->djb_response_data, datalen);
    if (data == NULL) {
         EXPORTER_LOG(AL_ERROR, "%s, request failed pullup djb_response_data", request->name);
         exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
         return ZPATH_RESULT_ERR;
    }
    char *json_start = strstr(data, "{");
    if (!json_start) {
        EXPORTER_LOG(AL_ERROR, "%s: API djbInfo failed %s", request->name, data);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }
    JSON_Value *json_root_val = json_parse_string((const char *)json_start);
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR, "%s: JSON string parse failed for djb_response_data", request->name);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }

    JSON_Object *json_root_obj = json_value_get_object(json_root_val);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR, "%s: JSON get object failed for djb_response_data", request->name);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        json_value_free(json_root_val);
        return ZPATH_RESULT_ERR;
    }

    const char *djb_id_from_json_response = json_object_get_string(json_root_obj, "id");
    if (!djb_id_from_json_response || (request->djb_info->djb_id != strtoll(djb_id_from_json_response, NULL, 0))) {
        EXPORTER_LOG(AL_ERROR, "%s: API djbInfo response does not have the same DJB ID as requested", request->name);
        json_value_free(json_root_val);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }

    ALLOC_COPY_STR(request->djb_info->ipaddr, json_object_get_string(json_root_obj, "ip_address"));
    ALLOC_COPY_STR(request->djb_info->os_type, json_object_get_string(json_root_obj, "os_type"));
    request->djb_info->port = json_object_get_int_with_default(json_root_obj, "port", 0);
    ALLOC_COPY_STR(request->djb_info->admin_username, json_object_get_string(json_root_obj, "admin_userid"));
    ALLOC_COPY_STR(request->djb_info->admin_pwd, json_object_get_string(json_root_obj, "admin_userpwd"));
    ALLOC_COPY_STR(request->djb_info->user_email, json_object_get_string(json_root_obj, "user_email"));

    if (!request->djb_info->ipaddr ||
        !request->djb_info->os_type ||
        !request->djb_info->port ||
        !request->djb_info->admin_username ||
        !request->djb_info->admin_pwd) {
        EXPORTER_LOG(AL_ERROR, "%s: Response has missing djb data ", request->name);
        json_value_free(json_root_val);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }

    if (request->djb_info->port < 22 || request->djb_info->port > 65535) {
        EXPORTER_LOG(AL_ERROR, "%s: Privileged Desktop Response has invalid port number for DJB(%d)", request->name, request->djb_info->port);
        json_value_free(json_root_val);
        exporter_guac_api_stats_increment(DJB_SERVICE_RESP_FAIL_COUNT);
        return ZPATH_RESULT_ERR;
    }
    json_value_free(json_root_val);

    EXPORTER_DEBUG_USER_PORTAL_API("%s: DJB Info - IPaddr %s, Username %s",request->name, request->djb_info->ipaddr, request->djb_info->admin_username);
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_validate_portal_policy_for_desktops(struct exporter_request *request)
{
    int result = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    result = exporter_privileged_portal_policy_evaluate(request, &request->portal_policy_rule_id,
                                                        &request->portal_policy_capabilities_bitmap);
    if (result == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_LOG(AL_DEBUG, "%s: async portal policy eval (%d->%d)",
                     request->name, request->async_count, request->async_count + 1);
        return result;
    } else if (result != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s portal policy eval res=%s",
                     request->name, domain, zpath_result_string(result));
        return result;
    }
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Privileged Desktop Policy RuleID:%"PRId64" Capabilities bitmap: %"PRIx64, request->name, request->portal_policy_rule_id, request->portal_policy_capabilities_bitmap);
    if (!request->portal_policy_rule_id) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s portal policy failed with no configured rule",
                     request->name, domain);
        return ZPATH_RESULT_ERR;
    }

    if (!(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_PRIVILEGED_DESKTOPS)) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s portal policy failed as desktops capabiltity disabled",
                     request->name, domain);
                     return ZPATH_RESULT_ERR;
    }

    return result;
}

int validate_user_email(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char query_value[1024] = {'\0'};
    ret = query_string_find(request->url, &(request->url_parser), "userEmail", query_value, sizeof(query_value));
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "Failed to extract query value from the URL: %s", zpath_result_string(ret));
        return ret;
    }

    const char *expected_user_name = exporter_user_portal_request_state_get_name(request->portal_info);
    if (!expected_user_name) {
        EXPORTER_LOG(AL_ERROR, "%s: request is not associated with a user id", request->name);
        return ZPATH_RESULT_ERR;
    }

    if (0 == strncasecmp(expected_user_name, query_value, strlen(query_value))) {
        return ZPATH_RESULT_NO_ERROR;
    }

    EXPORTER_LOG(AL_ERROR, "%s: request userEmail mismatch: query param email = %s expected useremail = %s", request->name, query_value, expected_user_name);
    return ZPATH_RESULT_ERR;
}

enum zpa_user_portal_api_type exporter_validate_portal_desktops_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version = ZPA_USER_PORTAL_API_VERSION_INVALID;
    int offset = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    /* Format: https://domain_uri>/v<version>/dispjumpbox/customers/<customer-id>/<api_type_path>... */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* API can be requested with /zscope for PRA portals */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    /* Skip over /dispjumpbox*/
    offset += strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX);

    /* Skip over /customers */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS, strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS))) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS);

    /* Extract, validate and skip over customer ID */
    const char *customer_id_string = uri_path + offset + 1;
    char *end_ptr = NULL;
    int64_t uri_customer_id = strtoll(customer_id_string, &end_ptr, 0);                   /* Extract customer ID */
    if (uri_customer_id < 0) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Validate customer ID */
    if (customer_gid != uri_customer_id) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid customer ID %"PRId64" in the uri path: %s, len: %d",
                                request->name, domain, uri_customer_id, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    offset +=  1 + exporter_get_user_portal_id_len(uri_customer_id);

    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_AMI_LIST, strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_AMI_LIST))) {
        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_AMI_LIST);
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_VM, strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_VM))) {
        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_VM);
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_ACTIVE_LIST, strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_ACTIVE_LIST))) {
        // validate user email
        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_ACTIVE_LIST);
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_ACTIVE_LIST;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_DJBINFO, strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_DJBINFO))) {
        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_DJBINFO);
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO;
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid api type for privileged desktops", request->name);
        api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %d", request->name, uri_path, uri_len, api_type);

    exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);
    return api_type;
}

/* Add request->portal_policy_rule_id to request_body before sending to priv desktop service */
int add_policyId_to_request_body(struct exporter_request *request)
{
    size_t datalen = 0;
    unsigned char *data = NULL;
    char str_policy_id[EXPORTER_USER_PORTAL_ID_STR_LEN] = {0};
    JSON_Value *json_root_val = NULL;
    JSON_Object *json_root_obj = NULL;

    if (!request->portal_policy_rule_id) {
        EXPORTER_LOG(AL_ERROR, "%s: Missing policy Id for feature privileged desktops", request->name);
        return ZPATH_RESULT_ERR;
    }

    snprintf(str_policy_id, sizeof(str_policy_id), "%"PRId64, request->portal_policy_rule_id);

    if (request->request_body) {
        datalen = evbuffer_get_length(request->request_body);
        if (!datalen) {
            EXPORTER_LOG(AL_WARNING, "%s: Empty request body in privileged desktops api call", request->name);
        }
    }

    if (!request->request_body || !datalen) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Request body or data len empty", request->name);
        request->request_body = evbuffer_new();
        if (!request->request_body) {
            EXPORTER_LOG(AL_ERROR, "%s: Cannot allocate evbuffer for request body in POST for privileged desktops", request->name);
            return ZPATH_RESULT_ERR;
        }
        json_root_val = json_value_init_object();
        if (!json_root_val) {
            EXPORTER_LOG(AL_ERROR, "%s: Cannot initialize json object for feature privileged desktops", request->name);
            return ZPATH_RESULT_ERR;
        }
        json_root_obj = json_value_get_object(json_root_val);
        if (!json_root_obj) {
            EXPORTER_LOG(AL_ERROR, "%s: Cannot get json root object for feature privileged desktops", request->name);
            json_value_free(json_root_val);
            return ZPATH_RESULT_ERR;
        }
    } else {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Non empty request body", request->name);
        // Get JSON root object for request_body
        data = evbuffer_pullup(request->request_body, datalen);
        if (data == NULL) {
             EXPORTER_LOG(AL_ERROR, "%s, request failed pullup request_body", request->name);
             return ZPATH_RESULT_ERR;
        }
        json_root_val = json_parse_string((const char *)data);
        if (!json_root_val) {
            EXPORTER_LOG(AL_ERROR, "%s: JSON string parse failed for request_body", request->name);
            return ZPATH_RESULT_ERR;
        }

        json_root_obj = json_value_get_object(json_root_val);
        if (!json_root_obj) {
            EXPORTER_LOG(AL_ERROR, "%s: JSON get object failed for request_body", request->name);
            json_value_free(json_root_val);
            return ZPATH_RESULT_ERR;
        }
    }

    /* Append policyId in JSON root object */
    json_object_dotset_string(json_root_obj, "ruleId", str_policy_id);
    EXPORTER_DEBUG_USER_PORTAL_API("Adding policyId %s for Privileged Desktops API", str_policy_id);

    /* Serialize the modified data to buffer */
    size_t needed_size_in_bytes = json_serialization_size(json_root_val);
    char *out_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!out_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory", request->name);
        json_value_free(json_root_val);
        return ZPATH_RESULT_NO_MEMORY;
    }
    /* Serialise Json into buffer */
    if (json_serialize_to_buffer(json_root_val, out_buf , needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: Failed to serialise json into buffer for feature privileged desktops",
                     request->name);
        json_value_free(json_root_val);
        EXPORTER_FREE(out_buf);
        return ZPATH_RESULT_ERR;
    }
    /* Drain current body and replace it with prepared body */
    if (datalen) {
        evbuffer_drain(request->request_body, datalen);
    }

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Modified JSON(%d) -> \n%s\n", request->name,
                                          (int)strlen(out_buf), out_buf);

    evbuffer_add_printf(request->request_body, "%s", out_buf);
    EXPORTER_FREE(out_buf);
    json_value_free(json_root_val);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Handle the Privileged Desktops API calls
 * Check if PRA is enabled
 * Check PRA Desktops feature flags is enabled
 * Check PRA Portal Policy bitmap - Enable Desktops must be set for this user
 * Check the API calls are valid
 * Check the query parameters
 * Proxy request to Priv Desktops Service
 */
int exporter_sra_portal_handle_privileged_desktops_api(struct exporter_request *request)
{
    int result = ZPATH_RESULT_NO_ERROR;
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    if (!g_exporter_ot_mode || is_pra_disabled(customer_gid)) {
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_CONSOLE_HTTP_PROXY_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }

    /* ensure the feature flag for priv desktops is enabled */
    if (is_pra_desktops_disabled(customer_gid)) {
        exporter_guac_api_stats_increment(DJB_PORTAL_POLICY_FAIL_COUNT);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_DESKTOPS_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_DESKTOPS_FAIL,
                                           "Privileged Desktops is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ZPATH_RESULT_ERR;
    }

    result = exporter_validate_portal_policy_for_desktops(request);
    if (!request->is_djb && result == ZPATH_RESULT_ASYNCHRONOUS) {
        /* no need to invoke the following for websocket-tunnel requests
           handle_guac_api_request will take care of invoking the following call */
        exporter_portal_defer_api_request_async(request);
    } else if (result != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_api_stats_increment(DJB_PORTAL_POLICY_FAIL_COUNT);
        return result;
    }


    /* classify/validate the browser<--> exporter <--> priv desktop service apis
     * APIs:
     * create djb
     * get list of amis
     * delete djb
     * get status of the djbs
     * get ipaddr and credential of DJB
     */
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    /* For websocket-tunnel request, no URI validation needed as
       API DJBINFO call is handcrafted */
    if (request->is_djb) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO;
        exporter_user_portal_request_state_set_api_type(request->portal_info, ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO);
    } else {
        api_type = exporter_validate_portal_desktops_api_type(request);
    }

    /* Validate all the apis
     * check useremail
     * check customer gid
     */
    switch (api_type) {
        case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST:
        case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM:
            result = validate_user_email(request);
            if (result != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR,"%s: Privileged Desktop api (ami / vm) %d failed validation", request->name, api_type);
                exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
                return ZPATH_RESULT_ERR;
            }
            if ((api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST) &&
                (request->req_method != HTTP_POST))  {
                EXPORTER_LOG(AL_ERROR,"%s: Privileged Desktop api - ami (get ami list) - must be HTTP POST", request->name);
                exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
                return ZPATH_RESULT_ERR;
            } else if (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM) {
                if ((request->req_method != HTTP_POST) && (request->req_method != HTTP_DELETE)) {
                    EXPORTER_LOG(AL_ERROR,"%s: Privileged Desktop api - vm (create or delete djb) - must be either HTTP POST or DELETE", request->name);
                    exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
                    return ZPATH_RESULT_ERR;
                } else if (request->req_method == HTTP_DELETE) {
                    exporter_guac_api_stats_increment(DJB_DELETE_COUNT);
                } else if (request->req_method == HTTP_POST) {
                    exporter_guac_api_stats_increment(DJB_LAUNCH_COUNT);
                }
            }
            /* add rule ID to the body */
            result = add_policyId_to_request_body(request);
            if (result != ZPATH_RESULT_NO_ERROR) {
                return result;
            }
            break;
        case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_ACTIVE_LIST:
            result = validate_user_email(request);
            if (result != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR,"%s: Privileged Desktop api - active (get active desktop list) - failed validation", request->name);
                exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
                return result;
            }
            if (request->req_method != HTTP_GET) {
                EXPORTER_LOG(AL_ERROR,"%s: Privileged Desktop api - active (get active desktop list) - must be HTTP GET", request->name);
                exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
                return ZPATH_RESULT_ERR;
            }
            break;
        case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO:
            EXPORTER_DEBUG_USER_PORTAL_API("%s: Privileged Desktop api - djbInfo being crafted and sent to Privileged Desktop service", request->name);
            exporter_guac_api_stats_increment(DJB_CONNECT_COUNT);
            break;
        default:
            exporter_guac_api_stats_increment(DJB_VALIDATION_FAILURE_COUNT);
            return ZPATH_RESULT_ERR;
    }


    request->webserver_type = exporter_conn_webserver_type_pra_desktops;
    result = exporter_user_portal_conn_webserver(request);
    if (result != ZPATH_RESULT_NO_ERROR) {
       EXPORTER_LOG(AL_ERROR, "%s: domain: %s, PRA-desktop service failure", request->name, domain);
       return result;
    }

    return result;
}


/*
 * External call used to get the dns name for service
 * privileged-desktops.<cloudname>
 */
void exporter_get_pra_djbservice_hostname(char **hostname)
{
    const struct zpath_exporter_cloud_config *exporter_config = zpath_get_exporter_config(ZPATH_LOCAL_CLOUD_NAME);

    if (exporter_config && exporter_config->pra_djb_service_hostname) {
        *hostname = EXPORTER_STRDUP(exporter_config->pra_djb_service_hostname, strlen(exporter_config->pra_djb_service_hostname));
    } else {
        char *default_pra_djb_service_hostname = EXPORTER_CALLOC(strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("privileged-desktops.") + 1);
        snprintf(default_pra_djb_service_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("privileged-desktops.") + 1, "privileged-desktops.%s",
                 ZPATH_LOCAL_CLOUD_NAME);
        *hostname = EXPORTER_STRDUP(default_pra_djb_service_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("privileged-desktops."));
        EXPORTER_FREE(default_pra_djb_service_hostname);
    }
}

/* Called from request_destroy */
void exporter_request_free_djb_info(struct exporter_request *request)
{
    if (request->djb_info->ipaddr) {
        EXPORTER_FREE(request->djb_info->ipaddr);
        request->djb_info->ipaddr = NULL;
    }
    if (request->djb_info->username) {
        EXPORTER_FREE(request->djb_info->username);
        request->djb_info->username = NULL;
    }
    if (request->djb_info->pwd) {
        EXPORTER_FREE(request->djb_info->pwd);
        request->djb_info->pwd = NULL;
    }
    if (request->djb_info->admin_username) {
        EXPORTER_FREE(request->djb_info->admin_username);
        request->djb_info->admin_username = NULL;
    }
    if (request->djb_info->admin_pwd) {
        EXPORTER_FREE(request->djb_info->admin_pwd);
        request->djb_info->admin_pwd = NULL;
    }
    if (request->djb_info->djb_response_data) {
        evbuffer_free(request->djb_info->djb_response_data);
        request->djb_info->djb_response_data = NULL;
    }
    if (request->djb_info->os_type) {
        EXPORTER_FREE(request->djb_info->os_type);
        request->djb_info->os_type = NULL;
    }
    if (request->djb_info->user_email) {
        EXPORTER_FREE(request->djb_info->user_email);
        request->djb_info->user_email = NULL;
    }
    request->djb_info = NULL;
    EXPORTER_DEBUG_USER_PORTAL_API("Request djb info has been freed");
    return;
}

/* Allow reprocess of web socket tunnel request */
void exporter_request_djb_reset_api_type(struct exporter_request *request)
{
    if (request->djb_info) {
        request->djb_info->djb_api_type_reset_count++;
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Reprocess Web Socket Tunnel Request (api type reset count: %d)", request->name, request->djb_info->djb_api_type_reset_count);
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: DJB Info is found to be deleted during reset of the API type. Should not happen as its freed with request destroy", request->name);
    }
    exporter_user_portal_request_state_set_api_type(request->portal_info, ZPA_SRA_PORTAL_API_TYPE_GUAC_WEBSOCKET_TUNNEL);
}
