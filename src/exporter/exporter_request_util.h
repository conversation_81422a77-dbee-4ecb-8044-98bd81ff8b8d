/*
 * exporter_request_util.h. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#ifndef __EXPORTER_REQUEST_UTIL_H__
#define __EXPORTER_REQUEST_UTIL_H__

#include "exporter/exporter_request.h"

#define EXPORTER_MAX_NUM_DOMAINS 1000
#define EXPORTER_EXT_ID_NAME_MAX_SIZE 5

enum exporter_managed_ba_delete_type {
    EXPORTER_MANAGED_BA_DELETE_MATCHED_DOMAIN = 1,
    EXPORTER_MANAGED_BA_DELETE_ALWAYS = 2,
};

char *exporter_util_edit_redirect_header(const char *in_payload, size_t in_payload_len, const char *in_app_domain, const char *in_ext_domain);
char *exporter_util_edit_cookie_value(const char *in_payload, size_t in_payload_len, const char *in_app_domain, const char *in_ext_domain, int in_delete_domain, int in_delete_type);

int url_encode(const char *str, char *dest, size_t dest_len);
/*
 * ... = name/value pairs of (const char *), referring to query string parameters to write
 *
 * This routine performs URL encoding of query string values, and nothing else.
 *
 * set_cookie_header_value does not include the beginning "Set-Cookie:" or trailing "\r\n"
 *
 * if target_port or target_path is NULL, the target_domain is
 * considered to contain the full url string to which to redirect.
 */
int exporter_request_redirect_encode(struct exporter_request *request,
                                     const char *set_cookie_header_value,
                                     const char *set_cookie_header_legacy_value,
                                     const char *set_cookie_header_value2,
                                     const char *set_cookie_header_legacy_value2,
                                     const char *target_domain,
                                     int target_port,
                                     const char *target_path,
                                     ...);

int exporter_request_redirect_encode_caa(struct exporter_request *request,
                                         const char *set_cookie_header_value,
                                         const char *set_cookie_header_legacy_value,
                                         const char *set_cookie_header_value2,
                                         const char *set_cookie_header_legacy_value2,
                                         const char *target_domain,
                                         int target_port,
                                         const char *target_path,
                                         ...);

int exporter_domain_search(struct exporter *exporter, const char *domain, size_t domain_len, struct exporter_domain **exporter_domain);
int64_t exporter_get_mged_cert_id_for_domain(struct exporter_domain *exporter_domain);
const char *exporter_domain_fetch_mapped_domain(const char *domain);
const char * exporter_get_tld_customer_name(uint8_t is_user_portal, uint8_t is_ot);
void exporter_domain_generate_managed_app_fqdn (const char* prefix, char* domain, const char* label,
                                               const char* uniq_id, char** managed_domain_fqdn, int64_t);
#endif //__EXPORTER_REQUEST_UTIL_H__
