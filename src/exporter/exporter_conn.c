/*
 * exporter_conn.c. Copyright (C) 2017 Zscaler Inc. All Rights Reserved.
 */
#include <execinfo.h>
#include "zlibevent/zlibevent_bufferevent.h"
#include "zcrypto_lib/zcrypto_lib.h"
#include "base64/base64.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_debug.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#include "exporter/exporter_websocket_parser.h"
#include "exporter/exporter_assert.h"
#include "zpn/zpn_client_less.h"
#include "zpn_ot/zpn_ot.h"
#include "zpath_lib/zpath_local.h"

#include "fohh/fohh_private.h"
#include "exporter/exporter.h"
#include "exporter/exporter_domain.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_zpa.h"
#include "exporter/exporter_fohh_worker.h"
#include "exporter/exporter_util.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "zpath_lib/zpath_config_override.h"
#include "exporter/exporter_private.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_csp.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/guacd_perf_limits.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_guac_session_control_host.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_guac_cred_pool.h"
#include "exporter/exporter_request_util.h"
#include "exporter/exporter_guac_desktops.h"

static int32_t exporter_conn_id = 0;

static struct http_parser_settings req_parser_settings;
static struct http_parser_settings resp_parser_settings;

TAILQ_HEAD(exporter_request_head_tailq, exporter_request);

struct exporter_request_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
};

struct exporter_request_free_queue {
    zpath_mutex_t lock;
    struct exporter_request_head_tailq request_list;
    struct exporter_request_queue_stats stats;
};

TAILQ_HEAD(exporter_conn_head_tailq, exporter_conn);

struct exporter_conn_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
};

struct exporter_conn_free_queue {
    zpath_mutex_t lock;
    struct exporter_conn_head_tailq conn_list;
    struct exporter_conn_queue_stats stats;
};

struct exporter_conn_thread_usage_stats {
    int64_t conn_thread_attach;
    int64_t conn_thread_detach;
};

/* Connection usage by thread */
static struct exporter_conn_thread_usage_stats g_conn_thread_usage_stats[FOHH_MAX_THREADS];

#define EXPORTER_CONN_THREAD_ATTACH_INC(fohh_tid)  \
    __sync_fetch_and_add_8(&(g_conn_thread_usage_stats[fohh_tid].conn_thread_attach), 1)

#define EXPORTER_CONN_THREAD_DETACH_INC(fohh_tid)   \
    __sync_fetch_and_add_8(&(g_conn_thread_usage_stats[fohh_tid].conn_thread_detach), 1)

#define EXPORTER_CONN_THREAD_ATTACH_GET(fohh_tid)   \
    g_conn_thread_usage_stats[fohh_tid].conn_thread_attach

#define EXPORTER_CONN_THREAD_DETACH_GET(fohh_tid)   \
    g_conn_thread_usage_stats[fohh_tid].conn_thread_detach

static struct exporter_request_free_queue request_free_q;
static struct exporter_conn_free_queue conn_free_q;

static struct exporter_request* exporter_request_allocate(void);
static void exporter_request_free(struct exporter_request *request);
static struct exporter_conn *exporter_conn_allocate(void);
static void exporter_conn_free(struct exporter_conn *conn);

static struct exporter_request *request_create(struct exporter_conn *conn);
static int exporter_conn_process_request(struct exporter_request *request, int is_data_cb);
static int request_destroy(struct exporter_request *request, int from_exporter_conn_destroy);
static void exporter_conn_read_cb(struct bufferevent *bev, void *cookie);
static void exporter_conn_destroy(struct exporter_conn *conn);



static size_t exporter_mged_ba_response_send(struct exporter_request * request)
{
   size_t enq_len = 0;
   size_t len = 0;

    /* Send the newly crafted response, so far with the newly calculated
        response_len. */
    len = request->response_len;
    enq_len = evbuffer_remove_buffer(request->mged_ba_response_evbuf,
                                     bufferevent_get_output(request->conn->bev),
                                     len);

    request->response_len = 0;  /*this buffer is emptied, so reset */

    if (!enq_len || !len) {
        /* No need to go further */
        return (enq_len);
    }

    request->log.rsp_size += len;
    exporter_fohh_worker_bytes_to_client(request->conn->thread->fohh_thread_id, len);

    if (!request->log.rsp_tx_start_us) {
        request->log.rsp_tx_start_us = epoch_us();
    }

    EXPORTER_DEBUG_HTTP("%s: Mged to sent back %d out of %d bytes of data to browser", request->name, (int)enq_len, (int)len);

    return (enq_len);

}

/*
 * Parsing callbacks
 */
static int exporter_conn_parser_message_begin_cb(struct http_parser *parser)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request;

    /* Dont proceed if connection is being destroyed */
    if ((conn->destroying) || (conn->name == NULL)) {
        EXPORTER_LOG(AL_WARNING, "conn: %p : request_create for connection being destoyed, rejecting", conn);
        return 1;
    }

    request = request_create(conn);
    conn->current_request = request;
    conn->requests_count++;

    EXPORTER_DEBUG_HTTP("%s:%d:[UNIP_LOG_RESP] Parser begin total requests = %ld", conn->name, request->request_num, (long) conn->requests_count);
    return 0;
}

static int exporter_conn_parser_url_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;

    if (!request) return 1;

    if (request->url_ofs < 0) {
        /* First URL request... */
        request->url_ofs = zmicro_heap_str_index(&(request->heap),
                                                 data,
                                                 length);
    } else {
        request->url_ofs = zmicro_heap_append_str_index(&(request->heap),
                                                        request->url_ofs,
                                                        strlen(((const char *)request->heap.start) + request->url_ofs),
                                                        data,
                                                        length);
    }
    if (request->url_ofs < 0) {
        EXPORTER_LOG(AL_ERROR, "%s:%d: Heap allocation failure on HTTP parser URL callback", conn->name, request->request_num);
        return 1;
    }
    //EXPORTER_DEBUG_HTTP("%s:%d: Received following URL so far: <%s>, had appended <%.*s> (%d bytes)", conn->name, request->request_num, ((const char *)request->heap.start) + request->url_ofs, (int) length, data, (int) length);
    return 0;
}

static int exporter_conn_parser_status_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;
    EXPORTER_DEBUG_HTTP("%s:%d: Received status cb: <%.*s>", conn->name, request->request_num, (int) length, data);
    return 0;
}

static int exporter_conn_parser_header_field_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;


    if (!request) return 1;

    int current_header = request->total_headers - 1;

    if ((current_header < 0) ||
        (request->header_value_ofs[current_header] >= 0)) {
        /* If we haven't started yet, or if there is a value for the current header, we must be on to a new header! */
        if (request->total_headers == EXPORTER_REQUEST_MAX_HEADERS) {
            EXPORTER_LOG(AL_ERROR, "%s:%d: Max request headers", conn->name, request->request_num);
            return 1;
        }
        request->total_headers++;
        current_header++;
        request->header_name_ofs[current_header] = -1;
        request->header_value_ofs[current_header] = -1;
    }
    if (request->header_name_ofs[current_header] < 0) {
        request->header_name_ofs[current_header] = zmicro_heap_str_index(&(request->heap),
                                                                     data,
                                                                     length);
    } else {
        request->header_name_ofs[current_header] = zmicro_heap_append_str_index(&(request->heap),
                                                                            request->header_name_ofs[current_header],
                                                                            strlen(((const char *)request->heap.start) + request->header_name_ofs[current_header]),
                                                                            data,
                                                                            length);
    }
    if (request->header_name_ofs[current_header] < 0) {
        EXPORTER_LOG(AL_ERROR, "%s:%d: Heap allocation failure on HTTP parser URL callback", conn->name, request->request_num);
        return 1;
    }

    //EXPORTER_DEBUG_HTTP("%s:%d: Received following header %d name <%s> so far", conn->name, request->request_num, current_header, ((const char *)request->heap.start) + request->header_name_ofs[current_header]);

    return 0;
}

static int exporter_conn_parser_header_value_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;


    if (!request) return 1;

    int current_header = request->total_headers - 1;

    if (current_header < 0) {
        EXPORTER_LOG(AL_ERROR, "Huh?");
        return 1;
    }
    if (request->header_value_ofs[current_header] < 0) {
        request->header_value_ofs[current_header] = zmicro_heap_str_index(&(request->heap),
                                                                      data,
                                                                      length);
    } else {
        request->header_value_ofs[current_header] = zmicro_heap_append_str_index(&(request->heap),
                                                                             request->header_value_ofs[current_header],
                                                                             strlen(((const char *)request->heap.start) + request->header_value_ofs[current_header]),
                                                                             data,
                                                                             length);
    }
    if (request->header_value_ofs[current_header] < 0) {
        EXPORTER_LOG(AL_ERROR, "%s:%d: Heap allocation failure on HTTP parser header value callback", conn->name, request->request_num);
        return 1;
    }

    //EXPORTER_DEBUG_HTTP("%s:%d: Received following request header %d value <%s> so far", conn->name, request->request_num, current_header, ((const char *)request->heap.start) + request->header_value_ofs[current_header]);
    return 0;
}

static int exporter_conn_parser_headers_complete_cb(struct http_parser *parser)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request;
    int i;
    int res;
    size_t request_len = 0;

    request = conn->current_request;
    if (!request) return 1;
    if (request->url_ofs < 0) return 1;

    /*
     * Reconstructing request length (not including body)
     *
     * It would be better to do a direct measurement of how many bytes
     * have been parsed, but that would require that we dig into the
     * workings of the HTTP parser. This won't be quite as accurate in
     * some rare cases, but is more compatible
     *
     * +2 below is for the header's trailing \n\r
     */
    request_len = strlen(http_method_str(parser->method)) + 2;

    if ((parser->http_minor == 1) && (parser->http_major == 1)) {
        /*
         *  METHOD /PATH HTTP/1.1\n\r
         *
         * METHOD CHARS  = Variable
         * SPACE         = 1
         * URL           = Variable
         * SPACE         = included in version
         * VERSION       = 9 or 0
         * \n\r          = 2
         */
        request_len += 1 + 9 + 2; /* space + space + version + \n\r */
    } else {
        request_len += 1 + 0 + 2; /* space + \n\r */
    }

    request->url = ((const char *)request->heap.start) + request->url_ofs;
    request->url_len = strlen(request->url);
    request_len += request->url_len;

    for (i = 0; i < request->total_headers; i++) {
        const char *hdr_name = ((const char *)request->heap.start) + request->header_name_ofs[i];
        const char *hdr_value = ((const char *)request->heap.start) + request->header_value_ofs[i];
        /* +4 for ": " between name/value and "\n\r" trailing */
        request_len += strlen(hdr_name) + strlen(hdr_value) + 4;
        /*
         * From RFC 2616 HTTP header names are not case sensitive.
         * If we replay cookie using Google Chrome, it send header as
         * "cookie: " instead of "Cookie: "
         *
         * Due to this cookies are not extracted and session is not established
         *
         * For now we are keeping it case sensitive but for testing it should be
         * made case insensitive
         */
        if (strcmp(hdr_name, "Sec-Fetch-Mode") == 0) request->header_sec_fetch_mode = hdr_value;
        if (strcmp(hdr_name, "Sec-Fetch-Site") == 0) request->header_sec_fetch_site = hdr_value;
        if (strcmp(hdr_name, "Sec-Fetch-Dest") == 0) request->header_sec_fetch_dest = hdr_value;
        if (strcmp(hdr_name, "Accept") == 0) request->header_accept = hdr_value;
        if (strcmp(hdr_name, "Cookie") == 0) request->header_cookie = hdr_value;
        if (strcmp(hdr_name, "Origin") == 0) request->header_origin = hdr_value;
        if (strcmp(hdr_name, "X-Forwarded-For") == 0) {
            request->header_xff = hdr_value;

            /*
             * Pull out the last IP in the XFF header and save it for
             * other use.
             *
             * Get to last entry in the xff header value- basically
             * skip to last ',' + 1, if there is one
             */
            const char *w1, *w2;
            w1 = hdr_value;
            while ((w2 = strchr(w1, ','))) {
                w1 = w2 + 1;
            }
            while (isspace(*w1)) w1++;

            res = argo_string_to_inet(w1, &(request->log.client_private_ip));
            if (res) {
                EXPORTER_LOG(AL_WARNING, "%s: Invalid IP address in XFF: %s", request->name, hdr_value);
            } else {
                EXPORTER_DEBUG_HTTP("%s: Extracted XFF IP: %s", request->name, w1);
            }
        }
        if (strcmp(hdr_name, "Forwarded") == 0) {
            request->header_forwarded = hdr_value;
            EXPORTER_DEBUG_HTTP("%s: Extracted Forwarded header : %s\n", request->name, hdr_value);
        }
        if (strcmp(hdr_name, "Host") == 0) request->header_host = hdr_value;
        if (strcmp(hdr_name, "User-Agent") == 0) request->header_user_agent = hdr_value;
        if (strcmp(hdr_name, "Origin") == 0) request->header_origin = hdr_value;
        if (strcmp(hdr_name, "Access-Control-Request-Method") == 0) request->header_acrm = hdr_value;
        if (strcmp(hdr_name, "Access-Control-Request-Headers") == 0) request->header_acrh = hdr_value;
        if (strcmp(hdr_name, "Via") == 0) request->header_via = hdr_value;
        if (strcmp(hdr_name, "Authorization") == 0) {
            request->authorization = hdr_value;
            if (strncmp(request->authorization, "NTLM", strlen("NTLM")) == 0) {
                EXPORTER_DEBUG_HTTP("%s:%d: NTLM Authorization Header = %s", conn->name, request->request_num, request->authorization);
                conn->nailed_up_mt = 1;
            }
            if (strncmp(request->authorization, "Negotiate", strlen("Negotiate")) == 0) {
                EXPORTER_DEBUG_HTTP("%s:%d: Negotiate Authorization Header = %s", conn->name, request->request_num, request->authorization);
                conn->nailed_up_mt = 1;
            }
        }
        if (strcmp(hdr_name, "Connection") == 0) request->header_connection_keep_alive = hdr_value;
    }

    request->log.req_size = request_len;
    exporter_fohh_worker_bytes_from_client(request->conn->thread->fohh_thread_id, request_len);

    EXPORTER_DEBUG_HTTP("%s:%d: Parse header complete, %s: url = <%.*s>, cookie = %.*s, xff = %s, forwarded = %s, via = %s, host = %s, origin = %s, http version = %d.%d, len = %ld",
                        conn->name, request->request_num,
                        http_method_str(conn->parser.method),
                        EXPORTER_DEBUG_BYTES, request->url,
                        EXPORTER_DEBUG_BYTES, request->header_cookie ? request->header_cookie : "no cookie",
                        request->header_xff,
                        request->header_forwarded,
                        request->header_via,
                        request->header_host,
                        request->header_origin,
                        conn->parser.http_major,
                        conn->parser.http_minor,
                        (long) request_len);

    /* Serendipitous matching of return types... */
    request->input_state = input_state_process_request;
    request->req_method = parser->method;
    request->req_http_minor = parser->http_minor;
    request->req_http_major = parser->http_major;

    http_parser_url_init(&(request->url_parser));
    res = http_parser_parse_url(request->url, request->url_len, request->req_method == HTTP_CONNECT ? 1 : 0, &(request->url_parser));
    if (res) {
        EXPORTER_DEBUG_HTTP("%s:%d: http_parser_parse_url failed", conn->name, request->request_num);
        parser->upgrade = 0;
        return -1;
    }
    if (!(request->url_parser.field_set & (1 << UF_PATH))) {
        EXPORTER_DEBUG_HTTP("%s:%d: http_parser_parse_url did not find a path", conn->name, request->request_num);
        return 1;
    }

    char plain_url[4000];
    snprintf(plain_url, sizeof(plain_url), "%.*s", (int)request->url_parser.field_data[UF_PATH].len, &(request->url[request->url_parser.field_data[UF_PATH].off]));
    request->log.url = zmicro_heap_str(&(request->heap), plain_url, strlen(plain_url));
    request->log.req_rx_hdr_done_us = epoch_us();
    request->log.method = http_method_str(request->req_method);
    request->log.protocol = (request->log.port == 443) ? "HTTPS" : "HTTP";
    if (request->conn->exporter_domain) {
        request->log.customer_gid = request->conn->exporter_domain->customer_gid;
        request->log.g_cst = request->conn->exporter_domain->customer_gid;
    } else {
        request->log.customer_gid = 0;
        request->log.g_cst = 0;
    }

    request->log.x_forwarded_for = request->header_xff;
    request->log.forwarded = request->header_forwarded;
    request->log.host = request->header_host;
    request->log.user_agent = request->header_user_agent;
    request->log.client_public_ip = request->conn->remote_ip;

    request->cors_request = 0;
    request->cors_request_with_token = 0; // We will set this after receiving a valid token
    if (request->header_origin && request->header_host) {
        // Host and Origin have to be different for a CORS request
        // Note: Condition of null host header will be rejected downstream with 403 response to client.
        char origin[EXPORTER_URL_MAX_ENCODE_SIZE + 1] = "";
        if (sizeof(origin) <= 2048) {
            /* ET-40457: bail out to prevent a sscanf buffer overrun */
            EXPORTER_ASSERT_SOFT(sizeof(origin) > 2048, "Format string requires bigger buffer");
            return 1;
        }
        int ret = sscanf(request->header_origin, "https://%2048s", origin);
        if (strlen(origin) >= 2048) {
            EXPORTER_LOG(AL_ERROR, "%s: Origin header is too big. Truncated: %s", request->name, origin);
            return 1;
        }
        if (ret == 1) {
            if (strncasecmp(origin, request->header_host, strlen(origin))) {
                request->cors_request = 1;
                request->log.origin = request->header_origin;
            } else {
                EXPORTER_DEBUG_HTTP("%s:%d Non Cross Origin request: origin = '%s', host = '%s'", conn->name, request->request_num, request->header_origin, request->header_host);
            }
        } else {
            EXPORTER_DEBUG_HTTP("%s:%d:Invalid Origin url. Only https origins are allowed: '%s'", conn->name, request->request_num, request->header_origin);
        }
    }

    request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_NOT_PRESENT;
    request->sec_fetch_site = HTTP_SEC_FETCH_SITE_NOT_PRESENT;
    request->sec_fetch_dest = HTTP_SEC_FETCH_DEST_NOT_PRESENT;
    /*
     * Origin header is not present but this can be CORS request if below header is present
     * Sec-Fetch-Mode: cors
     * Sec-Fetch-Site: same-origin
     */
    if (request->header_sec_fetch_mode) {
        if (0 == strncasecmp("cors", request->header_sec_fetch_mode, strlen("cors"))) {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_CORS;
        } else if (0 == strncasecmp("navigate", request->header_sec_fetch_mode, strlen("navigate"))) {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_NAVIGATE;
        } else if (0 == strncasecmp("no-cors", request->header_sec_fetch_mode, strlen("no-cors"))) {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_NO_CORS;
        } else if (0 == strncasecmp("same-origin", request->header_sec_fetch_mode, strlen("same-origin"))) {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_SAME_ORIGIN;
        } else if (0 == strncasecmp("websocket", request->header_sec_fetch_mode, strlen("websocket"))) {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_WEBSOCKET;
        } else {
            request->sec_fetch_mode = HTTP_SEC_FETCH_MODE_INVALID;
        }
    }

    if (request->header_sec_fetch_site) {
        if (0 == strncasecmp("cross-site", request->header_sec_fetch_site, strlen("cross-site"))) {
            request->sec_fetch_site = HTTP_SEC_FETCH_SITE_CROSS_SITE;
        } else if (0 == strncasecmp("same-origin", request->header_sec_fetch_site, strlen("same-origin"))) {
            request->sec_fetch_site = HTTP_SEC_FETCH_SITE_SAME_ORIGIN;
        } else if (0 == strncasecmp("same-site", request->header_sec_fetch_site, strlen("same-site"))) {
            request->sec_fetch_site = HTTP_SEC_FETCH_SITE_SAME_SITE;
        } else if (0 == strncasecmp("none", request->header_sec_fetch_site, strlen("none"))) {
            request->sec_fetch_site = HTTP_SEC_FETCH_SITE_NONE;
        } else {
            request->sec_fetch_site = HTTP_SEC_FETCH_SITE_INVALID;
        }
    }

    if (request->header_sec_fetch_dest) {
        if (0 == strncasecmp("document", request->header_sec_fetch_dest, strlen("document"))) {
            request->sec_fetch_dest = HTTP_SEC_FETCH_DEST_DOCUMENT;
        } else {
            request->sec_fetch_dest = HTTP_SEC_FETCH_DEST_INVALID;
        }
    }

    EXPORTER_DEBUG_CSP("%s: url: [%s] exporter_conn_parser_headers_complete_cb", request->name,
            request->log.url);

    res = exporter_conn_process_request(request, 0);
    if (res) {
        EXPORTER_DEBUG_HTTP("%s:%d: exporter_conn_process_request returned error %s", conn->name, request->request_num, zpath_result_string(res));
        return 3;
    }
    return 0;
}

static int exporter_conn_parser_body_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;
    int res;

    EXPORTER_DEBUG_CSP("%s: exporter_conn_parser_body_cb req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    /* Use case 1:
     *   - Ideally the data should have been drained in function exporter_conn_process_request
     *     since we set state to input_state_drain
     *   - But in case of error while loop for state is not executed and we will reach here
     *     so we need to drain data and return
     *
     * Use case 2:
     *    - Header comes in 1st payload, body comes in 2nd payload
     *    - In first payload we set state to input_state_drain although we are not complete processing body
     *    - IMP - This is existing issue in exporter design where set response even before reading body
                  We are not changing this design as part of CSP
     *    - Now when body comes when state is drain we don't process data
     *    - With POST changes we need to process this data so we must change state to
     *      input_state_drain_and_process_request_data so that data is not drained
     */
    if (request->input_state == input_state_drain) {
        if (request->request_body) {
            conn->inbound_memory -= evbuffer_get_length(request->request_body);
            evbuffer_free(request->request_body);
            request->request_body = NULL;
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    if (request->input_state == input_state_drain_and_process_request_data) {
        /* Data for POST is drained here, we need to store it */
        EXPORTER_DEBUG_CSP("%s: Draining POST data url: [%s] data: [%.*s] len: %d",
                request->name,
                request->log.url,
                (int)length, data ? data:"nil",
                (int)length);

        if (!request->request_body) {
            request->request_body = evbuffer_new();
        }
        //PRCOMMENTS - Keep adding here until we get content length and discard rest
        evbuffer_add(request->request_body, data, length);
        conn->inbound_memory += length;

        res = exporter_conn_process_request(request, 1);
        if (res) {
            EXPORTER_DEBUG_HTTP("%s:%d: exporter_conn_process_request returned error %s", conn->name,
                    request->request_num, zpath_result_string(res));
            return 3;
        }

        /*
         * DO NOT free the data here since we have to read in it http_parser
         * Multiple async calls will be done to handle post request due to
         * object store
         * Data must be freed in POST request cb or when request is destroyed
         */
#if 0
        if (request->request_body) {
            conn->inbound_memory -= evbuffer_get_length(request->request_body);
            evbuffer_free(request->request_body);
            request->request_body = NULL;
        }
#endif

        return ZPATH_RESULT_NO_ERROR;
    }

    if (!request->request_body) {
        request->request_body = evbuffer_new();
    }

    evbuffer_add(request->request_body, data, length);
    conn->inbound_memory += length;

    request->log.req_size += length;
    exporter_fohh_worker_bytes_from_client(request->conn->thread->fohh_thread_id, length);

    res = exporter_request_send_data(request);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: send_data returned %s", request->name, zpath_result_string(res));
        if (res == ZPATH_RESULT_WOULD_BLOCK) {
            /* We don't care if it is blocked, because zpn code will unblock and pull data later */
            res = ZPATH_RESULT_NO_ERROR;
        }
    }

    return res;
}


static int exporter_conn_parser_message_complete_cb(struct http_parser *parser)
{
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request = conn->current_request;
    int res;

    EXPORTER_DEBUG_ADVANCED_FILE_TRANSFER("%s: exporter_conn_parser_message_complete_cb"
            " req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    //If we reach here it guarentee that full body has arrived now so call body function
    //But still we cannot parse body if we are processing ASYNC and waiting for policy to load
    //After loading policy we change input_state to drain and then data is processed

    conn->current_request->http_request_complete = 1;

    res = exporter_request_complete_process(request);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: exporter_request_complete_process %s", request->name, zpath_result_string(res));
        return res;
    }

    conn->current_request->log.req_rx_done_us = epoch_us();

    res = exporter_request_send_data(request);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: send_data returned %s", request->name, zpath_result_string(res));
        if (res == ZPATH_RESULT_WOULD_BLOCK) {
            /* We don't care if it is blocked, because zpn code will unblock and pull data later */
            res = ZPATH_RESULT_NO_ERROR;
        }
        return res;
    }

    EXPORTER_DEBUG_CSP("%s: Calling wakeup connection on message complete cb", request->name);
    return exporter_conn_wake(conn);
}

/*
 * Websocket parsing callback. Handle masked data (if it has MASK indication).
 */
static int websocket_frame_body_cb(websocket_parser *parser, const unsigned char *data, size_t offset, size_t length) {
    if (parser->request->guac_info->websocket_input == NULL) {
         parser->request->guac_info->websocket_input = evbuffer_new();
    }

    // We need the input buffer to avoid copying data
    struct evbuffer *read_buf = bufferevent_get_input(parser->request->conn->bev);

    // Guacamole data goes into parser's buffer
    evbuffer_remove_buffer(read_buf, parser->buf, offset + length);

    parser->header_offset += offset;

    // Since this callback is called even for the partial headers, return here
    // if the body of the frame is still not available.
    if (parser->state != s_body) {
        return 0;
    }

    // If WebSocket frame is fully received, decode it and process
    if (!(parser->offset + length < parser->length)) {

        if (parser->flags & WS_HAS_MASK) {
            // Decode data (mask info is already set in the parser)
            unsigned char *data =
                    (unsigned char *) evbuffer_pullup(parser->buf, parser->header_offset + parser->length);
            if (data == NULL) {
               EXPORTER_DEBUG_HTTP("%s: Failed to parse %lu bytes of Guacamole data in websocket frame", parser->request->conn->name,
                       parser->header_offset + parser->length);
               return 1;
            }
            websocket_parser_decode(
                    data + parser->header_offset,
                    parser->length,
                    parser);

            // Remove websocket header
            evbuffer_drain(parser->buf, parser->header_offset);

            evbuffer_remove_buffer(parser->buf, parser->request->guac_info->websocket_input, parser->length);
        }

        // Set callbacks for instructions coming from the client (browser)
        guac_proto_parser_settings settings = {
                .on_instruction_received = exporter_guac_process_instruction_from_client_cb
        };

        // Parse Guacamole data
        size_t guac_len = evbuffer_get_length(parser->request->guac_info->websocket_input);
        data = evbuffer_pullup(parser->request->guac_info->websocket_input, guac_len);

        size_t guac_parsed_bytes = guac_parser_execute(&parser->request->conn->guac_parser,
                &settings,
                (const char *) data,
                guac_len);

        // Some guacamole data has been successfully parsed
        if (parser->request->conn->guac_parser.state != PARSING_ERROR && guac_parsed_bytes == guac_len) {
            EXPORTER_DEBUG_GUACD("%s: Parsed %lu bytes of Guacamole data from browser", parser->request->conn->name, guac_parsed_bytes);
            // Parser handlers are done with the data at this point. Remove it.
            evbuffer_drain(parser->request->guac_info->websocket_input, guac_parsed_bytes);
            // Send data to guacd
            exporter_request_send_data(parser->request);
        } else if (parser->request->conn->guac_parser.state == PARSING_ERROR) {
            return 1;
        }
    }

    return 0;
}

/*
 * Websocket parser initializer.
 */
static int exporter_websocket_parser_init(struct exporter_request *request) {
    websocket_parser_settings_init(&request->conn->ws_settings);
    request->conn->ws_settings.on_frame_body     = websocket_frame_body_cb;
    request->conn->ws_settings.on_frame_header   = NULL;
    request->conn->ws_settings.on_frame_end      = NULL;
    websocket_parser_init(&request->conn->ws_parser);
    request->conn->ws_parser.buf = evbuffer_new();
    request->conn->ws_parser.request = request;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Guacamole response processor.
 */
static int exporter_guac_conn_process_response(struct exporter_request *request, const unsigned char *instr, size_t *len) {
    // Prepare content in the evbuffer for parsing.

    size_t len_old = *len;

    EXPORTER_DEBUG_GUACD("%s: received guacamole data from guacd", request->name);

    // If not a header bytes, process Guacamole instructions.
    if (!request->guac_info->is_header) {

        // Server-side parser
        guac_proto_parser_settings settings = {
                .on_instruction_received = exporter_guac_process_instruction_from_server_cb
        };

        *len = guac_parser_execute(&request->guac_info->guac_parser, &settings, (const char *)instr, *len);

        if (request->guac_info->guac_parser.state == PARSING_ERROR) {
            goto err;
        }

        if (*len == 0) {
            // Pull more data from guacd if available
            return ZPATH_RESULT_ASYNCHRONOUS;
        }

        // Prepare WebSocket frame for the client.
        *len = websocket_build_frame(
                request->response_data,
                *len,
                WS_OP_TEXT | WS_FINAL_FRAME,
                NULL);

        // Start processing messages from Browser
        if (!request->guac_info->is_guacd_ready) {
            request->guac_info->is_guacd_ready = 1;
        }
    }

    return ZPATH_RESULT_NO_ERROR;

err:
    if (len_old) {

        // Try to parse whatever is left in the buffer
        guac_proto_parser_settings force_settings = {
                .force_parsing = 1
        };

        *len = guac_parser_execute(&request->guac_info->guac_parser, &force_settings, (const char *)instr, len_old);
    }

    if (*len == 0) {
        // If no good message found in the buffer, send generic error message
        exporter_guac_api_stats_increment(GUACD_PARSER_ERROR_500);
        exporter_guac_send_response(request,
                HTTP_STATUS_INTERNAL_SERVER_ERROR,
                "%s",
                "Disconnected from server : Internal Server Error");
        *len = evbuffer_get_length(request->response_data);

    }

    *len = exporter_guac_send_to_browser(request, request->response_data, *len);

    return ZPATH_RESULT_ERR;
}

static int is_fin_received_from_peer (struct exporter_request *request)
{
   if (!request || !request->mt)
        return 0;

   return (request->mt->broker_mconn.mconn.fin_rcvd == 1 &&
                   request->mt->broker_mconn.mconn.drop_tx == 0) ? 1 : 0;
}

static int exporter_get_fin_timeout_s(struct exporter_conn *conn)
{
   int64_t config_value = 0;
   if (conn && conn->exporter_domain) {
       zpath_config_override_get_config_int (CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
                                             &config_value,
                                             DEFAULT_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
                                             zpath_instance_global_state.current_config->gid,
                                             conn->exporter_domain->customer_gid,
                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                             0);
   }

   return (config_value > 0)? config_value : DEFAULT_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT;
}

static int exporter_conn_process_response(struct exporter_request *request)
{
    int rx_data = 0;
    size_t bytes_in_response_buffer;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);

    /* Check if we have response to send to browser */
    if (request->response_data && evbuffer_get_length(request->response_data)) {
        EXPORTER_DEBUG_HTTP("%s: processing response buffer(%p)", request->name, request->response_data);

        while (1) {
            size_t pkt_len = 0;
            int enq_len = 0;
            size_t bytes_parsed = 0;
            unsigned char *data;

            /*
             * If output bufferevent already holds max amount of data, don't process the response enqueue
             * Response buffer's output buffer is not measured when enqueuing data into, so it is prefectly normal to
             * see this INFO message. FWIW, only the response buffer's input buffer is limited to
             * HTTP_RESPONSE_MAX_BUFFER_DATA and that will inturn fill up the output buffer slowly and there will be
             * timing cases where it fills the response buffer's output buffer with (2 * HTTP_RESPONSE_MAX_BUFFER_DATA)
             * bytes of data. The below condition will make sure it doesn't go beyond that.
             */
            size_t bytes_in_response_buffer_ouput_buffer = evbuffer_get_length(bufferevent_get_output(request->conn->bev));
            if (bytes_in_response_buffer_ouput_buffer > HTTP_RESPONSE_MAX_BUFFER_DATA) {
                EXPORTER_DEBUG_HTTP("%s: response output buffer bytes(%zd) is greater than allowed limit of (%d), wait till it drains further",
                                    request->name, evbuffer_get_length(bufferevent_get_output(request->conn->bev)),
                                    HTTP_RESPONSE_MAX_BUFFER_DATA);
                if (bytes_in_response_buffer_ouput_buffer > (2*HTTP_RESPONSE_MAX_BUFFER_DATA)) {
                    EXPORTER_LOG(AL_ERROR, "%s: response output buffer bytes(%zd) is greater than allowed limit of (%d)",
                                 request->name, evbuffer_get_length(bufferevent_get_output(request->conn->bev)),
                                 HTTP_RESPONSE_MAX_BUFFER_DATA);
                }
                break;
            }

            /* Get data from evbuffer without copying: Find how much
             * contiguous space, and then evbuffer_pullup that data */
            pkt_len = evbuffer_get_contiguous_space(request->response_data);
            if (pkt_len <= 0) {
                /* Normal- out of data, just waiting for more. If we
                 * have processed data, reset our timeout */
                if (rx_data) {
                    exporter_conn_reset_timeout(request->conn);
                    request->log.rsp_tx_done_us = epoch_us();
                }
                ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
                return ZPATH_RESULT_NO_ERROR;
            }

            /* We parse the response from the upgrade request in order
             * to properly fill in parsed header data, etc, for
             * logging purposes. So we keep parsing until response
             * complete even when upgraded. */
            if ((!request->http_upgrade) || (request->http_upgrade && (!request->http_response_complete))) {

                data = evbuffer_pullup(request->response_data, pkt_len);

                /* Enable this for detailed packet debugging */
                if ((exporter_debug & EXPORTER_DEBUG_RAW_BIT) && !(request->http_res_skip_raw_data_dump)) {
                    exporter_dump_raw_data(request->conn->name, data, pkt_len, 0, __FILE__, __LINE__);
                }

                // Parse guac before HTTP parser.
                if (request->guac_info) {
                    size_t old_len = pkt_len;
                    int res = exporter_guac_conn_process_response(request, data, &pkt_len);
                    if (res == ZPATH_RESULT_ERR) {
                        EXPORTER_DEBUG_HTTP("%s: Guacamole response processing error read %ld bytes.",
                                request->name,
                                pkt_len);
                        exporter_guac_api_stats_increment(GUAC_RESPONSE_ERROR);
                        request->http_response_complete = 1;
                        break;
                    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
                        // If more data is available than so far considered, reprocess...
                        if (evbuffer_get_length(request->response_data) > old_len) {
                            evbuffer_pullup(request->response_data, evbuffer_get_length(request->response_data));
                            continue;
                        }
                        // Asynchronous operation, yield.
                        break;
                    }
                }

                /* Parsing response from server */
                bytes_parsed = http_parser_execute(&(request->parser),
                                                   &resp_parser_settings,
                                                   (const char *)data,
                                                   pkt_len);

                if (request->parser.upgrade) {
                    EXPORTER_DEBUG_HTTP("%s: Parsed %d bytes of response out of %d bytes, encountered Connection: upgrade", request->name, (int) bytes_parsed, (int) pkt_len);
                } else {
                    if ((bytes_parsed != pkt_len) || request->parser.http_errno) {
                        if (request->parser.http_errno) {
                            EXPORTER_DEBUG_HTTP("%s: Response parse failed read %ld out of %ld bytes due to error %s: %s",
                                                request->name,
                                                bytes_parsed,
                                                pkt_len,
                                                http_errno_name(request->parser.http_errno),
                                                http_errno_description(request->parser.http_errno));
                        } else {
                            EXPORTER_DEBUG_HTTP("%s: Response parse failed read %ld out of %ld bytes",
                                                request->name,
                                                bytes_parsed,
                                                pkt_len);
                        }
                        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
                        return ZPATH_RESULT_ERR;
                    } else {
                        EXPORTER_DEBUG_HTTP("%s: Parsed %d bytes of response", request->name, (int)pkt_len);
                    }
                }

#if 0
                if (0) {
                    uint8_t data_buf[8*1024];
                    char text_buf[8*1024*8];

                    size_t len;
                    size_t p_len;

                    struct evbuffer_ptr pos;
                    pos.pos = 0;

                    len = evbuffer_get_length(request->response_data);

                    while (pos.pos < len) {
                        p_len = evbuffer_copyout_from(request->response_data,
                                                      &pos,
                                                      data_buf, sizeof(data_buf));
                        argo_hexdump_buf(data_buf, p_len, 0, text_buf, sizeof(text_buf));
                        EXPORTER_DEBUG_HTTP("%s: Received:\n%s", request->name, text_buf);
                        pos.pos += p_len;
                    }
                }
#endif
            } else {
                EXPORTER_DEBUG_HTTP("%s: %d of data from server to client", request->name, (int) pkt_len);
                if (request->guac_info) {
                    request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_DISCONNECT;
                    exporter_guac_conn_process_response(request, evbuffer_pullup(request->response_data, pkt_len), &pkt_len);
                }
            }

            /* In case of mged ba apps, generate a new response and send it to the client.
             * Copy into a new evbuffer. Since copy is available, drain the original evbuffer,
             * request->response_data.
             * ZS generated responses are skipped.
             */

            /* unified-portal
             * TODO - http_parser is only for guac, is there as case its not called ?
             * Check the case of http upgrade and response is complete
             * data will be NULL and parser will not execute
             * Verify WS connection from server
             */
            if (request->http_edit_response_from_server) {
                EXPORTER_DEBUG_HTTP_DETAIL("%s: [UNIP_LOG_PARSER] %s/%s, pkt_len: %d, resp_hdr_generated: %d, resp_hdr_len: %lu, offset_header_complete: %"PRId64,
                        request->name, request->log.host, request->log.url,
                        (int) pkt_len,
                        request->http_response_generated,  request->resp_hdr_len,
                        request->parser.bytes_processed_until_on_headers_complete);

                /* Since we have called parser data is sent out from there, drain it here */

                /*
                 * Drain only header bytes we sent, the body must be sent directly to client
                 * We get correct offset from parser if cookies are modified or we have spacebars
                 */
                if (0 == request->http_response_generated) {
                    evbuffer_drain(request->response_data, pkt_len);
                } else {
                    /*
                     *********************************************************
                     * Design - Refer src/exporter/README_UNIFIED_PORTAL.txt
                     * Any changes done here wrt to datapath must handle transfer-encoding
                     * and content-encoding cases mentioned in above file
                     *********************************************************
                     */

                    size_t send_to_client = pkt_len;

                    if (request->resp_hdr_len) {
                        /* Drain all data until response headers */
                        evbuffer_drain(request->response_data, request->parser.bytes_processed_until_on_headers_complete);

                        /* Calculate body bytes in packet, this data is sent to client and not buffered */
                        send_to_client = pkt_len - request->parser.bytes_processed_until_on_headers_complete;

                        /* Response headers are sent out, now only body will come so reset response header len
                         * When this is zero we simply drain the body to client
                         */
                        request->resp_hdr_len = 0;
                    }

                    enq_len = evbuffer_remove_buffer(request->response_data,
                            bufferevent_get_output(request->conn->bev),
                            send_to_client);
                    request->log.rsp_size += send_to_client;
                    exporter_fohh_worker_bytes_to_client(request->conn->thread->fohh_thread_id, send_to_client);

                    if (!request->log.rsp_tx_start_us) {
                        request->log.rsp_tx_start_us = epoch_us();
                    }
                }
                rx_data = 1;
            } else {
                enq_len = evbuffer_remove_buffer(request->response_data,
                        bufferevent_get_output(request->conn->bev),
                        pkt_len);
                request->log.rsp_size += pkt_len;
                exporter_fohh_worker_bytes_to_client(request->conn->thread->fohh_thread_id, pkt_len);

                if (!request->log.rsp_tx_start_us) {
                    request->log.rsp_tx_start_us = epoch_us();
                }

                rx_data = 1;

                EXPORTER_DEBUG_HTTP("%s: sent back %d out of %d bytes of data to browser", request->name, (int)enq_len, (int)pkt_len);
            }
        }

        if (rx_data) {
            request->log.rsp_tx_done_us = epoch_us();
            if (request->conn) exporter_conn_reset_timeout(request->conn);
        }

    } else {
        EXPORTER_DEBUG_HTTP("%s: attempted to process response buffer(%p) - but nothing exists", request->name,
                            request->response_data);
        if (is_fin_received_from_peer (request) && is_remote_fin_propogation_enabled(request->log.customer_gid)){
            EXPORTER_DEBUG_HTTP("%s: fin has already received,updating the conn timeout",request->name);
            /* SET-9056, Tearing down the connection at this point may cause some data to be heldup in                 evbuffer, hence we will add dampening timeout to flush the data in output evbuf */
            request->conn->timeout_s = exporter_get_fin_timeout_s(request->conn);
            exporter_conn_reset_timeout_1 (request->conn, 1);
        }
    }

    if (request->response_data) {
        bytes_in_response_buffer = evbuffer_get_length(request->response_data);
        if ((1u == request->need_to_unblock_source) && (bytes_in_response_buffer <= ((3*HTTP_RESPONSE_MAX_BUFFER_DATA)/4))) {
            enum zpa_user_portal_api_type api_type = exporter_get_user_portal_api_type(request);
            if (request->portal_info && (api_type != ZPA_SRA_PORTAL_API_TYPE_GUAC_WEBSOCKET_TUNNEL)) {
                EXPORTER_DEBUG_CONN("%s: response buffer(%p) got space(%zd bytes) to accept new bytes from user portal web server",
                                    request->name, request->response_data,
                                    HTTP_RESPONSE_MAX_BUFFER_DATA - bytes_in_response_buffer);
                exporter_user_portal_conn_webserver_response_buffer_space_available_another_thread(request);
            } else {
                /*
                 * unified-portal - Verify this case for BA2 apps
                 * We block source from sending more data if we get 64KB of data without processing
                 * After processing we need to check and unblock so that we can queue more data
                 * If we have less than 75% of 64KB we can consume more else we remain in block state
                 */
                EXPORTER_DEBUG_CONN("%s: response buffer(%p) got space(%zd bytes) to accept new bytes from broker",
                        request->name, request->response_data,
                        HTTP_RESPONSE_MAX_BUFFER_DATA - bytes_in_response_buffer);
                request->need_to_unblock_source = 0;
                zfce_mt_unblock(request->mt);
            }
        }
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * With HTTP pipeline, we can have multiple requests per connection. But, we should fully serve request#1 before
 * even starting to serve request#2
 */
int exporter_conn_wake(struct exporter_conn *conn)
{
    /* Check if we have anything to send out to client */
    struct exporter_request *request;
    int res;
    int requests = 0;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);
    EXPORTER_DEBUG_HTTP("%s: Waking connection%s to process all the requests in it", conn->name,
            conn->destroying ? " currently destroying":"");

    /* Even if destroying, we want to walk the requests... */
    while ((request = ZTAILQ_FIRST(&(conn->requests)))) {
        EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
        EXPORTER_DEBUG_HTTP("%s: now processing the request(%d/%d)", request->name?request->name:"nil", (requests + 1),
                conn->requests_count);
        EXPORTER_DEBUG_HTTP_DETAIL("%s: exporter_conn_wake req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
                request->name?request->name:"nil",
                request->log.url,
                exporter_request_input_state_get_str(request),
                request->http_request_complete, request->http_response_complete,
                exporter_request_async_state_get_str(request->async_state),
                request->async_count,
                request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

        requests++;
        if (!(conn->destroying)) {
            res = ZPATH_RESULT_NO_ERROR;

            if (request->http_request_complete) {
                /*
                 * Check to see if we still have body data queued up due to flow control,
                 * and send it out before trying to process response
                 */
                if (request->request_body && evbuffer_get_length(request->request_body)) {
                    exporter_request_send_data(conn->current_request);
                }
            }

            if (request->http_request_complete || request->http_upgrade) {

                /* We might be able to transmit */
                res = exporter_conn_process_response(request);
                if (res) {
                    /* This is a parsing error... */
                    EXPORTER_LOG(AL_ERROR, "%s: Got %s from process_response(%d %d)", conn->name,
                                 zpath_result_string(res), request->http_response_complete,request->http_upgrade);
                    res = request_destroy(request, 0);
                    break;
                } else {
                    if (request->http_response_complete
                            && (!request->http_upgrade || request->guac_info)) {
                        EXPORTER_DEBUG_HTTP("%s: Response complete for %s (%d %d %d)", request->name,
                                            request->log.url,
                                            request->http_response_complete,
                                            request->http_upgrade,(request->guac_info ? 1:0));
                        if (request->guac_info) {
                            conn->sni = conn->sni_orig;
                        }
                        res = request_destroy(request, 0);
                        if (res) break;
                        continue;
                    } else {
                        /* Still waiting for data... */
#if 0
                        EXPORTER_DEBUG_CSP("%s: waiting for data or async to finish req_state: {%s,%s,%d%d,%s,%d,%d,%d} flags: {%d,%d}",
                                request->name?request->name:"nil",
                                request->log.url,
                                exporter_request_input_state_get_str(request),
                                request->http_request_complete, request->http_response_complete,
                                exporter_request_async_state_get_str(request->async_state),
                                request->async_count,
                                request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                                request->response_data ? (int)evbuffer_get_length(request->response_data): -1,
                                request->http_upgrade, request->guac_info ? 1:0);
#endif
                        break;
                    }
                }
            } else {
                if (request->input_state != input_state_drain) {
                    /* Still waiting for more of the request to arrive... */
                    EXPORTER_DEBUG_HTTP("%s: Request not complete, should read", request->name);
                    exporter_conn_trigger_read_cb(request->conn);
                }
                break;
            }
        } else {
            res = request_destroy(request, 0);
            if (res) break;
        }
    }

    if (!requests) {
        if (conn->destroying) {
            exporter_conn_destroy(conn);
        }
    }

    return ZPATH_RESULT_NO_ERROR;

}

#if EXPORTER_USE_ZEVENT
static void exporter_conn_wake_on_thread(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void exporter_conn_wake_on_thread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct exporter_conn *conn = cookie;
    int res;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    __sync_fetch_and_sub_4(&(conn->async_count), 1);
    EXPORTER_DEBUG_HTTP_DETAIL("%s: Calling wakeup connection on thread", conn->current_request ? conn->current_request->name: "nil");
    res = exporter_conn_wake(conn);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "Could not conn_wake: %s", zpath_result_string(res));
    }
}


int exporter_conn_wake_from_other_thread(struct exporter_conn *conn)
{
    int res;

    if (!conn) {
       EXPORTER_LOG (AL_ERROR,"Error: Trying to wake on closed Connection");
       return ZPATH_RESULT_NO_ERROR;
    }

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    if (conn->destroying) {
        EXPORTER_LOG(AL_DEBUG, "Should not make thread_call if conn is being destroyed");
        return ZPATH_RESULT_ERR;
    }

    __sync_fetch_and_add_4(&(conn->async_count), 1);
#if EXPORTER_USE_ZEVENT
    res = fohh_thread_call_zevent(conn->thread->fohh_thread_id, exporter_conn_wake_on_thread, conn, 0);
#else
    res = fohh_thread_call(conn->thread->fohh_thread_id, exporter_conn_wake_on_thread, conn, 0);
#endif
    if (res) {
        EXPORTER_LOG(AL_ERROR, "Could not thread_call: %s", zpath_result_string(res));
        __sync_fetch_and_sub_4(&(conn->async_count), 1);
    }
    return res;
}


static int exporter_conn_process_request(struct exporter_request *request, int is_data_cb)
{
    /* This will do what needs to be done on the request */
    enum exporter_request_input_state orig_state;
    int iterations = 10;
    int res;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    do {
        orig_state = request->input_state;
        switch(orig_state) {
        case input_state_get_request:
            /* Do nothing- we will keep getting data */
            break;
        case input_state_process_request:
            /* Call request processor, see if it can make progress */
            res = exporter_request_process(request);
            if (res) return res;
            break;
        case input_state_drain_and_process_request_data:
            /* Since we have a while loop we come at this path from 2 places
             * - /doauth req processing
             *   - First we come from exporter_conn_parser_headers_complete_cb
             *   - State is input_state_process_request
             *   - We see we need to process data so we set state to input_state_drain_and_process_request_data
             *   - Since state is changed while loop is processed again
             *   - body is still ZERO/NULL since http parser have not processed
             * - /doauth body processing
             *   - from exporter_conn_parser_body_cb
             *   - Here the body is filled
             */
            if (is_data_cb) {
                res = exporter_request_process_data(request);
                if (res) return res;
            }
            break;
        case input_state_drain:
            if (request->request_body) {
                request->conn->inbound_memory -= evbuffer_get_length(request->request_body);
                evbuffer_free(request->request_body);
                request->request_body = NULL;
            }
            break;
        default:
            EXPORTER_LOG(AL_CRITICAL, "Err");
            break;
        }
    } while ((iterations--) && (orig_state != request->input_state));
    if (!iterations) {
        EXPORTER_LOG(AL_CRITICAL, "Loop");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}



static void exporter_conn_destroy(struct exporter_conn *conn)
{
    struct exporter_request *request, *tmp;
    int32_t destroy_count;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    EXPORTER_DEBUG_CONN("%s: Destroy", conn->name);

    destroy_count = __sync_add_and_fetch_4(&(conn->destroy_count), 1);
    if (destroy_count > 1) {
        /* Decrement if we are going away */
        destroy_count = __sync_sub_and_fetch_4(&(conn->destroy_count), 1);
        EXPORTER_LOG(AL_WARNING, "%s: Destroy already active, skip this destroy, decremented destroy_count: %d, bev: %p, timeout: %p, async_count: %d, sock: %d, flush_input: %d, input_paused: %d, destroying: %d",
                     conn->name, conn->destroy_count, conn->bev, conn->timeout, conn->async_count, conn->sock,
                     conn->flush_input, conn->input_paused, conn->destroying);
        return;
    } else {
        EXPORTER_LOG(AL_INFO, "%s: Destroy allowed, destroy_count: %d, bev: %p, timeout: %p, async_count: %d, sock: %d, flush_input: %d, input_paused: %d, destroying: %d",
                     conn->name, conn->destroy_count, conn->bev, conn->timeout, conn->async_count, conn->sock,
                     conn->flush_input, conn->input_paused, conn->destroying);
    }

    /* Set to 0 first to keep from recursing */
    conn->destroying = 1;

    if (conn->sni_orig) {
        conn->sni = conn->sni_orig;
    }

    /* Stop read/write callbacks on the bev so we don't get called
     * back into freed state */
    if (conn->bev) {
        /* FIXME: is this required as zlibevent_bufferevent_free() anyway does it?*/
        bufferevent_disable(conn->bev, EV_READ|EV_WRITE);
    } else {
        /* This might be a delayed bufferevent callback after conn object is deleted */
        /* Dont decrement here, bufferevent deleted means the "other caller" will run to completion and any subequents calls to this routine will not cross this point */
        return;
    }

    ZTAILQ_FOREACH_SAFE(request, &(conn->requests), list, tmp) {
        request_destroy(request, 1);
    }

    if (ZTAILQ_FIRST(&(conn->requests))) {
        /* Cannot destroy yet... need to wait for the reqeusts to go
         * away */

        destroy_count = __sync_sub_and_fetch_4(&(conn->destroy_count), 1);
        EXPORTER_DEBUG_HTTP("%s:%p Attempting to destroy conn with outstanding requests attached, decremented destroy_count: %ld before early return",
                            conn->name, conn, (long)destroy_count);
        return;
    }

    if (conn->async_count) {
        destroy_count = __sync_sub_and_fetch_4(&(conn->destroy_count), 1);
        EXPORTER_DEBUG_HTTP("%s:%p Attempting to destroy conn with outstanding async calls attached, async_count = %ld, decremented destroy_count = %ld before early return",
                            conn->name, conn, (long)conn->async_count, (long)destroy_count);
        return;
    }

    if (conn->thread) {
        EXPORTER_CONN_THREAD_DETACH_INC(conn->thread->fohh_thread_id);
        exporter_fohh_worker_num_conn_disconnect(conn->thread->fohh_thread_id);
    }

    EXPORTER_DEBUG_CONN("%s: Destroy %p", conn->name, conn);

    /* Stop timer before destroying connection */
    if (conn->timeout) {
        event_free(conn->timeout);
        conn->timeout = NULL;
    }

    if (conn->bev) {
        /* Free SSL */
        SSL *bev_ssl = bufferevent_openssl_get_ssl(conn->bev);
        if (bev_ssl) {
            SSL_free(bev_ssl);
        }

        zlibevent_bufferevent_free(conn->bev);
        conn->bev = NULL;

        close(conn->sock);
        conn->sock = -1;
    }

    if (conn->sni) {
        EXPORTER_FREE(conn->sni);
        conn->sni = NULL;
    }
    if (conn->name) {
        EXPORTER_FREE(conn->name);
        conn->name = NULL;
    }
    if (conn->client_hello_pkt) {
        EXPORTER_FREE(conn->client_hello_pkt);
        conn->client_hello_pkt = NULL;
        conn->client_hello_pkt_len = 0;
    }

    guac_parser_destroy(&(conn->guac_parser));
    if (conn->ws_parser.buf != NULL) {
        evbuffer_free(conn->ws_parser.buf);
        conn->ws_parser.buf = NULL;
    }

    SET_EXPORTER_OBJ_INVALID(conn);
#ifndef __FreeBSD__
    SET_EXPORTER_BACKTRACE(conn);
#endif

    //EXPORTER_FREE(conn);

    __sync_lock_release(&(conn->destroy_count));
    exporter_conn_free(conn);
}

static void exporter_conn_status_cb(struct bufferevent *bev, short style, void *cookie)
{
    struct exporter_conn *conn = cookie;
    char *b_reading = "";
    char *b_writing = "";
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";
    char *b_connected = "";

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    EXPORTER_DEBUG_CONN("%s: Received event %s%s%s%s%s%s",
                        conn->name,
                        b_reading,
                        b_writing,
                        b_eof,
                        b_error,
                        b_timeout,
                        b_connected);

    if (style & BEV_EVENT_CONNECTED) {
        /* Do nothing */
    } else if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
        /* Teardown! */
        exporter_conn_destroy(conn);
    } else {
        /* Huh? */
        EXPORTER_LOG(AL_ERROR, "%s: Received event %s%s%s%s%s%s",
                     conn->name,
                     b_reading,
                     b_writing,
                     b_eof,
                     b_error,
                     b_timeout,
                     b_connected);
    }
}

/* wrapper alloc/free functions for ephemeral exporter request with guac sftp */
void ephemeral_exporter_request_free(struct exporter_request *request)
{
    return exporter_request_free(request);
}

struct exporter_request *ephemeral_exporter_request_create()
{
    return exporter_request_allocate();
}

int32_t get_new_connection_id()
{
    return __sync_add_and_fetch_4(&(exporter_conn_id), 1);
}

static struct exporter_request *request_create(struct exporter_conn *conn)
{
    struct exporter_request *request;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);


    //request = EXPORTER_CALLOC(sizeof(*request));
    request = exporter_request_allocate();
    request->request_body = evbuffer_new();
    request->request_num = conn->total_requests;
    request->lock = ZPATH_MUTEX_INIT;
    conn->total_requests++;
    request->url_ofs = -1;
    request->conn = conn;
    request->input_state = input_state_get_request;
    request->parser.data = request;
    http_parser_init(&(request->parser), HTTP_RESPONSE);
    /*
     * unified-portal - Any domain or endpoint handled by exporter we don't have to process
     * - OT/PRA/AUTH-domains
     * - All endpoints with exporter magic number EXPORTER_MAGIC
     */
    /*
     * If sni is there fohh does diamond lookup and domain is never null
     * exporter_domain is 0x0 when sni is 0x0 means port 80
     */

    /* If SSL, only then check, below macros have check for HTTPS so we are safe */
    if (IS_MANAGED_APP(request) || IS_UNIFIED_PORTAL(request)) {
        request->http_edit_response_from_server = 1;
    }

    /* We are either processing request or response, so we we need a single heap only */
    if (request->http_edit_response_from_server) {
        zmicro_heap_init_alloc(&(request->heap), EXPORTER_RESPONSE_HEAP_START_SIZE,
                EXPORTER_RESPONSE_HEAP_MAX_SIZE, &exporter_allocator);
    } else {
        zmicro_heap_init_alloc(&(request->heap), EXPORTER_REQUEST_HEAP_START_SIZE, EXPORTER_REQUEST_HEAP_MAX_SIZE, &exporter_allocator);
    }
    conn->inbound_memory += EXPORTER_REQUEST_HEAP_START_SIZE;
    ZTAILQ_INSERT_TAIL(&(conn->requests), request, list);
    request->log.req_rx_start_us = epoch_us();
    request->log.port = conn->local_port_he;
    request->log.mt_status = NULL;
    request->log.mt_reason = NULL;
    request->log.client_public_port = conn->remote_port_he;
    request->log.g_exp = ZPATH_INSTANCE_GID;
    request->log.portal_api_status = NULL;
    request->ref_count = 0;
    request->portal_info = NULL;
    request->user_portal_request_user_state = NULL;
    request->is_authenticated = 0;
    request->sra_host_fqdn = NULL;
    request->sra_host_protocol = NULL;
    request->pra_console_name = NULL;
    request->sra_zconsole_url = NULL;
    request->sra_remote_ip = NULL;
    request->sra_host_conn_security = NULL;
    request->csp_data = NULL;
    request->csp_os_data = NULL;
    request->nameid = NULL;
    request->scope_gid = 0;
    request->server_gid = 0;
    request->destroy_session_entry = 0;
    request->user_sessions = NULL;
    request->shared_sess_count = 0;
    request->shared_has_count = 0;
    request->gposture_object = NULL;
    request->gposture = NULL;
    request->caa_data = NULL;
    request->caa_object = NULL;
    request->gprofiles = NULL;

    SET_EXPORTER_OBJ_VALID(request);

    request->name = EXPORTER_MALLOC(strlen(conn->name) + 30);
    snprintf(request->name, strlen(conn->name) + 30, "%s:%d:%p", conn->name, request->request_num, request);

    EXPORTER_DEBUG_HTTP("%s: %p: Request allocated", request->name, request);

    exporter_fohh_worker_num_request_create(conn->thread->fohh_thread_id);

    return request;
}

/*
 * Returns no_error if request is destroyed.
 *
 * often returns async for those cases where ya gotta wait.
 */
static int request_destroy(struct exporter_request *request, int from_exporter_conn_destroy)
{
    struct exporter_conn *conn = request->conn;
    int res;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    /* update redis, perform cleanup and send diagnostic log */
    exporter_session_proctoring_handle_disconnect(request);

    if (request->mt) {

        if (request->guac_info && request->guac_info->is_guacd_ready && from_exporter_conn_destroy) {
            evbuffer_add(request->request_body, GUAC_DISCONNECT_INSTR, strlen(GUAC_DISCONNECT_INSTR));
            exporter_request_send_data(request);
        }

        /* send final metadata RPC to kafka */
        exporter_session_recording_send_done_metadata(request);

        EXPORTER_DEBUG_HTTP("%s: Attempting to destroy request with mt still attached", request->name);
        res = exporter_zpa_terminate(request);
        if (request->mt) {
            return res;
        }
    }
    if (request->share_ctxt.infer_key) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC freeing infer_key %p, request %p", request->share_ctxt.infer_key, request);
        EXPORTER_FREE(request->share_ctxt.infer_key);
        request->share_ctxt.infer_key = NULL;
        exporter_sharing_stats_inc(infer_key_free_type);
    }

    if (request->async_count) {
        EXPORTER_DEBUG_HTTP("%s: Attempting to destroy request with outstanding async calls attached, retry later",
                            request->name);
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    /* PRA Cred Pooling cleanup operation if cred pooling is enabled */
    (void)delete_credential_from_cred_pool_service(request);

    assert(request->ref_count >= 0);
    if (request->ref_count) {
        EXPORTER_DEBUG_HTTP("%s: Attempting to destroy request with outstanding ref count(%d) attached, retry later",
                            request->name, request->ref_count);
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    if (is_guac_sftp_mt_alive(request)) {
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: destroying request %p", request);
    exporter_session_recording_encrypt_and_enqueue(request, 1);

    /* If we were an upgraded connection, we just rip everything down */
    if (request->http_upgrade) {
        conn->destroying = 1;
    }

    if (request == conn->current_request) {
        /* This is ONLY okay if the current request is complete */
        if (request->http_request_complete && request->http_response_complete && ((!request->response_data) || (evbuffer_get_length(request->response_data) == 0))) {
            /* This request is complete, all is well... */
        } else {
            /* Uh oh. Weird state. For now, just log it. The reqeust
             * will go away. */
            EXPORTER_LOG(AL_ERROR, "Unusual state in request going away, request_complete = %d, response_complete = %d, response_data = %p, length = %ld",
                         request->http_request_complete, request->http_response_complete, request->response_data, request->response_data ? evbuffer_get_length(request->response_data) : 0);
            conn->destroying = 1;
        }
        conn->current_request = NULL;
    }

    /* Log it before destroy */
    exporter_request_log(request);

    ZTAILQ_REMOVE(&(conn->requests), request, list);

    if (request->response_status_str) {
        EXPORTER_FREE((char *)request->response_status_str);
        request->response_status_str = NULL;
    }

    /* Make it NULL before free to avoid use-after-free */
    request->log.url = NULL;
    zmicro_heap_free(&(request->heap)); // Idempotent
    if (request->request_body) {
        evbuffer_free(request->request_body);
        request->request_body = NULL;
    }
    if (request->response_data) {
        evbuffer_free(request->response_data);
        request->response_data = NULL;
    }

    if (request->mged_ba_response_evbuf) {
        evbuffer_free(request->mged_ba_response_evbuf);
        request->mged_ba_response_evbuf = NULL;
    }

    if (request->generated_request_data) {
    	evbuffer_free(request->generated_request_data);
    	request->generated_request_data = NULL;
    }
    if (request->share_ctxt.browser_notification_evbuf) evbuffer_free(request->share_ctxt.browser_notification_evbuf);
    if (request->policy_state) {
        exporter_request_policy_state_free_and_reset(request);
    }
    if (request->portal_info) {
        exporter_user_portal_request_state_free(request);
    }
    exporter_guac_proxy_browser_push_notification_cleanup(request);
    exporter_request_log_cleanup(request);

    if (request->guac_info) {
        int headless = request->guac_info->is_headless;
        if (request->sra_host_protocol && (strncasecmp(request->sra_host_protocol, "RDP", 3) == 0) &&
                (!is_pra_ft_disabled(conn->exporter_domain->customer_gid))) {
            exporter_delete_remote_drive_directory(request, conn);
        }

        exporter_guac_api_stats_increment(GUAC_WS_REQUEST_DESTROY);
        exporter_request_free_guac_info(request);

        if (!request->is_proxy_conn && !headless) {
            exporter_guac_api_stats_decrement(GUACD_SERVICE_DEC_ACTIVE_SESSION_COUNT);
            exporter_guac_api_proto_stats_decrement(request);
        }
    }

    EXPORTER_DEBUG_HTTP("%s: %p: Request destroyed.", request->name, request);
    request->conn = NULL;

    if (request->csp_data) {
        exporter_request_free_csp_info(request);
    }
    if (request->csp_os_data) {
        exporter_csp_free(request->csp_os_data);
    }

    if (request->gposture_object) {
        argo_object_release(request->gposture_object);
        request->gposture_object = NULL;
    }

    if (request->gposture) {
        request->gposture = NULL;
    }

    if (request->caa_object) {
        argo_object_release(request->caa_object);
        request->caa_object = NULL;
    }

    if (request->caa_data) {
        request->caa_data = NULL;
    }

    if (request->gprofiles) {
        EXPORTER_FREE(request->gprofiles);
        request->gprofiles = NULL;
    }

    decrement_large_portal_access_count(request);

    EXPORTER_FREE(request->name);
    request->name = NULL;

    if (request->sra_zconsole_url) {
        EXPORTER_FREE(request->sra_zconsole_url);
        request->sra_zconsole_url = NULL;
    }
    if (request->sra_remote_ip) {
        EXPORTER_FREE(request->sra_remote_ip);
        request->sra_remote_ip = NULL;
    }
    if (request->sra_host_fqdn) {
        EXPORTER_FREE(request->sra_host_fqdn);
        request->sra_host_fqdn = NULL;
    }
    if (request->sra_host_protocol) {
        EXPORTER_FREE(request->sra_host_protocol);
        request->sra_host_protocol = NULL;
    }
    if (request->pra_console_name) {
        EXPORTER_FREE(request->pra_console_name);
        request->pra_console_name = NULL;
    }
    if (request->sra_host_conn_security) {
        EXPORTER_FREE(request->sra_host_conn_security);
        request->sra_host_conn_security = NULL;
    }
    if (request->nameid) {
        EXPORTER_FREE(request->nameid);
        request->nameid = NULL;
    }
    if (request->guac_proctored_session_id) {
        EXPORTER_FREE(request->guac_proctored_session_id);
        request->guac_proctored_session_id = NULL;
    }

    if (request->djb_info) {
        exporter_request_free_djb_info(request);
    }

    if (request->new_header) {
        evbuffer_free(request->new_header);
        request->new_header = NULL;
    }

    request->scope_gid = 0;
    request->server_gid = 0;

    SET_EXPORTER_OBJ_INVALID(request);
#ifndef __FreeBSD__
    SET_EXPORTER_BACKTRACE(request);
#endif
    //EXPORTER_FREE(request);
    exporter_request_free(request);

    exporter_fohh_worker_num_request_destroy(conn->thread->fohh_thread_id);

    if (conn->destroying && !from_exporter_conn_destroy) {
        EXPORTER_DEBUG_HTTP("%s: Request gone, attempting to destroy conn.", conn->name);
        exporter_conn_destroy(conn);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_destroy(struct exporter_request *request)
{
    return request_destroy(request, 0);
}

/*
 * This is basically an HTTP request parser...
 */
static void exporter_conn_read_cb(struct bufferevent *bev, void *cookie)
{
    struct exporter_conn *conn = cookie;
    size_t bytes_parsed;
    struct evbuffer *read_buf = bufferevent_get_input(bev);
    int tickle = 0;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    exporter_conn_reset_timeout(conn);

    if (conn->flush_input) {
        EXPORTER_DEBUG_CONN("Read callback - flushing");
        evbuffer_drain(read_buf, evbuffer_get_length(read_buf));
        return;
    }
    EXPORTER_DEBUG_CONN("%s: Read callback", conn->name);

    if (conn->current_request && conn->current_request->guac_info) {

        if (is_pra_disabled(conn->exporter_domain->customer_gid)) {
            EXPORTER_LOG(AL_WARNING, "%s: PRA is disabled. Closing connection", conn->name);
            conn->flush_input = 1;
            exporter_conn_destroy(conn);
            return;
        }

        if (conn->current_request->guac_info->is_stream_timeout_triggered) {
            exporter_guac_proxy_stream_clean(conn->current_request);
        }

        // Send proxied data to guacd if this guac request has it ready
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) &&
            (conn->current_request->guac_info->guac_in_stream[file_transfer].stream_transmit)) {
            if (exporter_guac_proxy_send(conn->current_request,
                        &conn->current_request->guac_info->guac_in_stream[file_transfer]) != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_WARNING, "%s: Guacamole Proxy send error, closing connection",
                             conn->name);
                conn->flush_input = 1;
                exporter_conn_destroy(conn);
            }
            tickle = 1;
        }
    }

    if (conn->current_request && conn->current_request->request_body &&
        evbuffer_get_length(conn->current_request->request_body) >
        (g_exporter_ot_mode ? EXPORTER_OT_MAX_BUFFER_USER_DATA: EXPORTER_MAX_BUFFER_USER_DATA)) {
        if (conn->current_request->input_state == input_state_drain) {
            /* No need to send out any queued request data */
        } else {
            /* If we are blocked and buffering large amount of data, stop reading from bufferevent */
            EXPORTER_DEBUG_CONN("%s: Buffering %ld bytes of data, send it out first", conn->name, evbuffer_get_length(conn->current_request->request_body));
            exporter_request_send_data(conn->current_request);
        }
        return;
    }

    /* Parse data (HTTP) */
    while (1) {
        size_t len;
        unsigned char *data;

        /* Get data from evbuffer without copying: Find how much
         * contiguous space, and then evbuffer_pullup that data */
        len = evbuffer_get_contiguous_space(read_buf);
        if (len <= 0) {
             tickle = 1;
            break;
        }

        if ((!conn->current_request) || (!conn->current_request->http_upgrade)) {
            data = evbuffer_pullup(read_buf, len);

            if (exporter_debug & EXPORTER_DEBUG_RAW_BIT) {
                exporter_dump_raw_data(conn->name, data, len, 1, __FILE__, __LINE__);
            }

            bytes_parsed = http_parser_execute(&(conn->parser),
                                               &req_parser_settings,
                                               (const char *)data,
                                               len);
            if (conn->parser.upgrade) {
                if (conn->current_request) {
                    conn->current_request->http_upgrade = 1;
                    EXPORTER_DEBUG_HTTP("%s: Upgrading connection!", conn->name);
                    if (conn->current_request->mt) {
                        ZPATH_MUTEX_LOCK(&(conn->current_request->mt->lock), __FILE__, __LINE__);
                        EXPORTER_DEBUG_HTTP("%s: Upgrading mtunnel, mtunnel_id = %s", conn->name, conn->current_request->mt->mtunnel_id);
                        conn->current_request->mt->is_mt_upgraded = 1;
                        ZPATH_MUTEX_UNLOCK(&(conn->current_request->mt->lock), __FILE__, __LINE__);
                    }
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: Upgrading connection desired, but no current request?", conn->name);
                }
            } else {
                if ((bytes_parsed != len) || conn->parser.http_errno) {
                    EXPORTER_LOG(AL_WARNING, "%s: Parse error, len = %ld, bytes_parsed = %ld, http_errno = %d, closing connection",
                                 conn->name, len, bytes_parsed, conn->parser.http_errno);
                    conn->flush_input = 1;
                    exporter_conn_destroy(conn);
                    return;
                } else {
                    EXPORTER_DEBUG_HTTP("%s: Parsed %ld bytes", conn->name, (long) len);
                }
            }
            evbuffer_drain(read_buf, len);

        // Guacamole data over websocket
        } else if (conn->current_request && conn->current_request->guac_info) {
            if (!conn->current_request->request_body) {
                conn->current_request->request_body = evbuffer_new();
            }

            // If WebSocket parser not initialized by this time, do it now.
            if (conn->ws_parser.request != conn->current_request) {
                exporter_websocket_parser_init(conn->current_request);
            }
            data = evbuffer_pullup(read_buf, len);
            bytes_parsed = websocket_parser_execute(
                    &conn->ws_parser,
                    &conn->ws_settings, data, len);
            if (bytes_parsed != len) {
                EXPORTER_LOG(AL_WARNING, "%s: Parse error, len = %ld, bytes_parsed = %ld, closing connection",
                             conn->name, len, bytes_parsed);
                conn->flush_input = 1;
                exporter_guac_api_stats_increment(GUAC_WS_PARSER_ERROR);
                exporter_conn_destroy(conn);
                return;
            } else {
                EXPORTER_DEBUG_HTTP("%s: Parsed %ld bytes of websocket frame", conn->name, (long) len);
                break;
            }
        } else {
            if (conn->current_request) {
                if (!conn->current_request->request_body) {
                    conn->current_request->request_body = evbuffer_new();
                }
                evbuffer_remove_buffer(bufferevent_get_input(bev),
                                       conn->current_request->request_body,
                                       len);
                tickle = 1;
            } else {
                EXPORTER_LOG(AL_ERROR, "%s: Upgraded connection, but no current request?", conn->name);
            }
        }
    }
    if (tickle) {
        if (conn->current_request) {
            exporter_request_send_data(conn->current_request);
        }
    }
}

#if EXPORTER_USE_ZEVENT
static void exporter_conn_read_cb_on_thread(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void exporter_conn_read_cb_on_thread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct exporter_conn *conn = cookie;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    __sync_fetch_and_sub_4(&(conn->async_count), 1);
    exporter_conn_read_cb(conn->bev, conn);
}

int exporter_conn_trigger_read_cb(struct exporter_conn *conn)
{
    int res;

    EXPORTER_OBJ_VALID(conn, __FILE__, __LINE__);

    __sync_fetch_and_add_4(&(conn->async_count), 1);

#if EXPORTER_USE_ZEVENT
    res = fohh_thread_call_zevent(conn->thread->fohh_thread_id, exporter_conn_read_cb_on_thread, conn, 0);
#else
    res = fohh_thread_call(conn->thread->fohh_thread_id, exporter_conn_read_cb_on_thread, conn, 0);
#endif
    if (res) {
        EXPORTER_LOG(AL_ERROR, "Could not thread_call: %s", zpath_result_string(res));
        __sync_fetch_and_sub_4(&(conn->async_count), 1);
    }
    return res;
}

static void exporter_conn_write_cb(struct bufferevent *bev, void *cookie)
{
    struct exporter_conn *conn = cookie;
    EXPORTER_DEBUG_CONN("%s: Write callback calling wakeup connection", conn->name);
    exporter_conn_wake(conn);
}

static int64_t exporter_conn_get_timeout(struct exporter_domain* domain, const char* tag)
{
    /* domain can be 0x0 if its port 80 */
    if (!domain || !domain->customer_gid || !domain->gid || domain->is_auth_domain || domain->is_user_portal) {
        return EXPORTER_DEFAULT_CONN_TIMEOUT_S;
    }
    int64_t config_value = zpath_config_override_get_config_int(EXPORTER_CONFIG_OVERRIDE_IDEL_TIMEOUT_S,
                                                                &config_value,
                                                                EXPORTER_DEFAULT_CONN_TIMEOUT_S,
                                                                domain->gid,
                                                                domain->customer_gid,
                                                                zpath_instance_global_state.current_config->gid,
                                                                ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid),
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

    if (config_value < EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MIN || config_value > EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MAX) {
        EXPORTER_LOG(AL_ERROR, "%s: invalid custom timeout: %"PRId64", default value of %d will be used.",
                                    tag ? tag : "?", config_value, EXPORTER_DEFAULT_CONN_TIMEOUT_S);
        config_value = EXPORTER_DEFAULT_CONN_TIMEOUT_S;
    }
    if (config_value != EXPORTER_DEFAULT_CONN_TIMEOUT_S) {
        EXPORTER_LOG(AL_INFO, "%s: custom timeout_s: %"PRId64, tag ? tag : "?", config_value);
    }
    return config_value;
}

static int exporter_conn_is_mtunnel_pinned(struct exporter_domain* domain, const char* tag) {
    /* exporter_domain is 0x0 if its port 80 */
    if (!domain || !domain->gid || domain->is_auth_domain || domain->is_user_portal) {
        return 0; /* not pinned */
    }
    int64_t config_value = zpath_config_override_get_config_int(EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED,
                                                                &config_value,
                                                                DEFAULT_CONFIG_VAL_EXPORTER_MTUNNEL_REUSE_DISABLED,
                                                                domain->gid,
                                                                domain->customer_gid,
                                                                zpath_instance_global_state.current_config->gid,
                                                                ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid),
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
    if (config_value) {
        EXPORTER_LOG(AL_INFO, "%s: mTunnel reuse is disabled.", tag ? tag : "?");
    }
    return config_value ? 1 : 0;
}

static int exporter_debug_config_override_per_domain(struct zpath_debug_state* request_state, const char** query_values,
                         int query_value_count, void* cookie) {
    char sni[256];
    void *results[10];
    int result_count;

    if (!query_values[0]) {
        ZDP("specify domain\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    if (strlen(query_values[0]) > 255) {
        ZDP("domain too long\n");
        return ZPATH_RESULT_NO_ERROR;

    }
    strcpy(sni, query_values[0]);
    zpath_downcase(sni);

    result_count = diamond_search(global_exporter.domains, (uint8 *)sni, strlen(sni), (void **)&(results[0]), NULL, sizeof(results) / sizeof(void *));
    ZDP(" %d entries found in domain diamond matching %s\n", result_count, sni);
    for (int i = 0; i < result_count; i++) {
        struct exporter_domain* domain = results[i];
        int64_t timeout = exporter_conn_get_timeout(domain, "exporter_debug");
        int mt_disabled = exporter_conn_is_mtunnel_pinned(domain, "exporter_debug");

        ZDP(" Matched (%p) with %s\n", domain, domain->domain);
        ZDP("  gid                 : %"PRId64"\n", domain->gid);
        ZDP("  customer_gid        : %"PRId64"\n", domain->customer_gid);
        ZDP("  is_auth_domain      : %d\n", domain->is_auth_domain);
        ZDP("  is_user_portal      : %d\n", domain->is_user_portal);
        ZDP("  idle timeout        : %"PRId64"\n", timeout);
        ZDP("  reuse mTunnel       : %s\n", mt_disabled ? "disabled" : "enabled");
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void exporter_conn_timeout(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct exporter_conn *conn = cookie;
    EXPORTER_LOG(AL_NOTICE, "%s: Timed out. Timeout = %"PRId64, conn->name, conn->timeout_s);
    exporter_conn_destroy(conn);
}


void exporter_conn_reset_timeout_1(struct exporter_conn *conn,bool force)
{
    struct timeval tv;
    int64_t now = epoch_s();

    if (conn->timeout_set_s != now || force == true) {
        conn->timeout_set_s = now;
        tv.tv_usec = 0;
        tv.tv_sec = conn->timeout_s;

        if (conn->timeout) {
            event_free(conn->timeout);
            conn->timeout = NULL;
        }

        conn->timeout = event_new(conn->thread->ev_base, -1, 0, exporter_conn_timeout, conn);
        if (event_add(conn->timeout, &tv)) {
            EXPORTER_LOG(AL_CRITICAL, "%s: Cannot timeout", conn->name);
        }
    }
}

void exporter_conn_reset_timeout(struct exporter_conn *conn)
{
    exporter_conn_reset_timeout_1 (conn, false);
}

int exporter_conn_create(struct exporter_domain *exporter_domain,
        struct fohh_thread *thread,
        int sock,
        struct sockaddr *sa,
        int sa_len,
        SSL_CTX *ctx,
        const char *sni,
        const char *sni_suffix,
        const unsigned char *pkt,
        int pkt_len)
{
    struct bufferevent *bev;
    SSL *ssl = NULL;
    struct exporter_conn *conn;


    if (!sni) {
        /* Unencrypted */
        bev = bufferevent_socket_new(thread->ev_base,
                                     sock,
                                     BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        if (!bev) {
            EXPORTER_LOG(AL_ERROR, "Could not create BEV for unencrypted socket");
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* Encrypted */
        ssl = SSL_new(ctx);
        if (!ssl) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not create SSL", sni);
            return ZPATH_RESULT_ERR;
        }

        bev = bufferevent_openssl_socket_new(thread->ev_base,
                                             sock,
                                             ssl,
                                             BUFFEREVENT_SSL_ACCEPTING,
                                             BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        if (!bev) {
            SSL_free(ssl);
            EXPORTER_LOG(AL_ERROR, "%s: Could not create BEV", sni);
            return ZPATH_RESULT_ERR;
        }
    }

    //conn = EXPORTER_CALLOC(sizeof(*conn));
    conn = exporter_conn_allocate();
    {
        /* Generate debug name for connection. Handy for logs */
        char remote_ip[INET6_ADDRSTRLEN];
        char local_ip[INET6_ADDRSTRLEN];
        char str[200];
        socklen_t socklen;
        struct sockaddr_storage ss;
        uint16_t port_he;
        int res;

        socklen = sizeof(ss);
        if (getsockname(sock, (struct sockaddr *)&ss, &socklen)) {
            local_ip[0] = 0;
        }

        zcdns_sockaddr_storage_to_str((struct sockaddr_storage *) sa, remote_ip, sizeof(remote_ip));
        zcdns_sockaddr_storage_to_str(&ss, local_ip, sizeof(local_ip));
        argo_sockaddr_to_inet(sa, &(conn->remote_ip_inet), &port_he);

        conn->local_port_he = sockaddr_get_port_he(&ss);
        conn->connection_id = __sync_add_and_fetch_4(&(exporter_conn_id), 1);

        snprintf(str, sizeof(str), "%p %s:%d->%s:%d s=%d sni=%s id=%d",
                 conn, remote_ip, sockaddr_get_port_he((struct sockaddr_storage *) sa),
                 local_ip, conn->local_port_he,
                 sock,
                 sni ? sni : "unencrypted",
                 conn->connection_id);

        conn->name = EXPORTER_STRDUP(str, strlen(str));
        res = argo_string_to_inet(local_ip, &(conn->local_ip));
        if(res) {
           EXPORTER_LOG(AL_ERROR, "%s: Failed to fetch local ip of the sni", sni);
        }

        SET_EXPORTER_OBJ_VALID(conn);
    }
    /* Save remote IP info in loggable form */
    argo_sockaddr_to_inet(sa, &(conn->remote_ip), &(conn->remote_port_he));
    conn->remote_port_he = ntohs(conn->remote_port_he);

    if (pkt) {
        conn->client_hello_pkt = EXPORTER_CALLOC(sizeof(unsigned char) * (pkt_len + 1));
        memcpy(conn->client_hello_pkt, pkt, pkt_len);
        conn->client_hello_pkt[pkt_len] = '\0';
        conn->client_hello_pkt_len = pkt_len;
    }

    /* exporter_domain is 0x0 if its port 80 */
    conn->exporter_domain = exporter_domain;
    conn->timeout_s = exporter_conn_get_timeout(exporter_domain, conn->name);
    if (exporter_conn_is_mtunnel_pinned(exporter_domain, conn->name)) {
        conn->nailed_up_mt = 1;
        conn->no_mt_reuse = 1;
    } else {
        conn->nailed_up_mt = 0;
        conn->no_mt_reuse = 0;
    }

    conn->sock = sock;
    if (sni) {
        conn->sni = EXPORTER_STRDUP(sni, strlen(sni));
        zpath_downcase(conn->sni);
    }
    conn->bev = bev;
    ZTAILQ_INIT(&(conn->requests));
    conn->thread = &(global_exporter.thread_state[fohh_thread_get_id(thread)]);

    EXPORTER_CONN_THREAD_ATTACH_INC(conn->thread->fohh_thread_id);

    exporter_conn_reset_timeout(conn);

    http_parser_init(&(conn->parser), HTTP_REQUEST);
    conn->parser.data = conn;

    guac_parser_init(&(conn->guac_parser), conn);

    bufferevent_setcb(conn->bev,
                      exporter_conn_read_cb,
                      exporter_conn_write_cb,
                      exporter_conn_status_cb,
                      conn);
    bufferevent_enable(conn->bev, EV_READ|EV_WRITE);

    /* We set the READ high watermark */
    bufferevent_setwatermark(conn->bev, EV_READ, 0, g_exporter_ot_mode ? EXPORTER_OT_MAX_BUFFER_USER_DATA: EXPORTER_MAX_BUFFER_USER_DATA);

    //EXPORTER_DEBUG_CONN("%s: New connection, setting thread to %s", conn->name, thread->zthread->stack.thread_name);

    exporter_fohh_worker_num_conn_connect(conn->thread->fohh_thread_id);

    return ZPATH_RESULT_NO_ERROR;
}


static struct http_parser_settings req_parser_settings = {
    exporter_conn_parser_message_begin_cb,             //    http_cb      on_message_begin;
    exporter_conn_parser_url_cb,                       //    http_data_cb on_url;
    exporter_conn_parser_status_cb,                    //    http_data_cb on_status;
    exporter_conn_parser_header_field_cb,              //    http_data_cb on_header_field;
    exporter_conn_parser_header_value_cb,              //    http_data_cb on_header_value;
    exporter_conn_parser_headers_complete_cb,          //    http_cb      on_headers_complete;//async to write sessiona
    exporter_conn_parser_body_cb,                      //    http_data_cb on_body;
    exporter_conn_parser_message_complete_cb,          //    http_cb      on_message_complete;

    /* When on_chunk_header is called, the current chunk length is stored
     * in parser->content_length.
     */
    NULL, //    http_cb      on_chunk_header;
    NULL, //    http_cb      on_chunk_complete;
};

struct http_parser_settings* exporter_conn_get_parser_settings() {
    return &req_parser_settings;
}




/* Response Parser */

/* unified-portal */
static int exporter_resp_parser_status_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_request *request = parser->data;

    if (!request->http_edit_response_from_server) return ZPATH_RESULT_NO_ERROR;

    request->response_status_str = EXPORTER_STRDUP(data,length);
    request->response_status_code = parser->status_code;

    if (request->conn->exporter_domain) {
        EXPORTER_DEBUG_HTTP("%s: [UNIP_LOG_PARSER] HTTP response status host: %s%s, orig_domain: %s, cfg_domain: %s, exporter_domain: %s, ot-up-auth-ba2: %d-%d-%d-%d, edit_resp: %d",
                request->name,
                request->log.host,
                request->log.url,
                request->orig_domain,
                request->conn->exporter_domain->cfg_domain,
                request->conn->exporter_domain->domain,
                request->conn->exporter_domain->is_ot,
                request->conn->exporter_domain->is_user_portal,
                request->conn->exporter_domain->is_auth_domain,
                request->conn->exporter_domain->is_managed_ba,
                request->http_edit_response_from_server);
    }

    return 0;
}

static int exporter_resp_parser_message_complete_cb(struct http_parser *parser)
{
    struct exporter_request *request = parser->data;

    if (request->guac_info) {
        EXPORTER_DEBUG_HTTP("%s: HTTP message is complete for the Guacamole connection", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    EXPORTER_DEBUG_HTTP("%s: HTTP message is complete", request->name);
    request->http_response_complete = 1;
    request->log.rsp_rx_done_us = epoch_us();
    request->log.response_status = parser->status_code;

    exporter_request_enter_state(request, async_state_response_done);

    return ZPATH_RESULT_NO_ERROR;
}

/* unified-portal start */
static int exporter_resp_parser_header_value_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_request *request = parser->data;
    const char * data_value = data;
    size_t data_length = length;
    char *new_data_value = NULL;
    int is_cookie_header = 0;

    if (!request->http_edit_response_from_server) return ZPATH_RESULT_NO_ERROR;

    int current_header = request->total_resp_headers - 1;

    if (current_header < 0) {
        EXPORTER_LOG(AL_ERROR, "Header field cb function did not found a valid header but parser gave us a value cb for header value");
        return 1;
    }

    const char * hdr_name = ((const char *)request->heap.start) + request->resp_header_name_ofs[current_header];

    /* If the managed BA response from the Server has Set-Cookie header, then the value for this
     * field is checked for internal domain name. If present, replace with external domain name.
     */
    is_cookie_header = !strncmp(hdr_name, "Set-Cookie", 10);
    if (is_cookie_header) {
        new_data_value = exporter_util_edit_cookie_value(data, length, request->conn->exporter_domain->cfg_domain,
                                     request->conn->exporter_domain->domain, 1, EXPORTER_MANAGED_BA_DELETE_ALWAYS);
    } else if ( IS_HTTP_REDIRECT_CODE(request->response_status_code) && (!strncmp(hdr_name, "Location", 8)) ) {
        /* If the status is 3XX and the location header has internal configured domain name,
         * rewrite the location header with the external FQDN.
         */
       new_data_value = exporter_util_edit_redirect_header(data, length, request->conn->exporter_domain->cfg_domain,
                                                      request->conn->exporter_domain->domain);
    }

    if (new_data_value) {
        data_value = new_data_value;
        data_length =  strlen(new_data_value);
    } else {
        data_value = data;
        data_length = length;
    }

    if (request->resp_header_value_ofs[current_header] < 0) {
        request->resp_header_value_ofs[current_header] = zmicro_heap_str_index(&(request->heap),
                                                                      data_value,
                                                                      data_length);
    } else {
        request->resp_header_value_ofs[current_header] = zmicro_heap_append_str_index(&(request->heap),
                                                                             request->resp_header_value_ofs[current_header],
                                                                             strlen(((const char *)request->heap.start) + request->resp_header_value_ofs[current_header]),
                                                                             data_value,
                                                                             data_length);
    }

    if (new_data_value) {
        EXPORTER_FREE(new_data_value);
    }

    if (request->resp_header_value_ofs[current_header] < 0) {
        EXPORTER_LOG(AL_ERROR, "%s: %d: Heap allocation failure on HTTP parser header value callback", request->name, request->request_num);
        return 1;
    }

    if (!is_cookie_header) {
        EXPORTER_DEBUG_HTTP_DETAIL("%s: %d: Received following response header %d value <%s>:<%s> so far",
                request->name,
                request->request_num,
                current_header,((const char *)request->heap.start) + request->resp_header_name_ofs[current_header],
                ((const char *)request->heap.start) + request->resp_header_value_ofs[current_header]);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_resp_parser_header_field_cb(struct http_parser *parser, const char *data, size_t length)
{
    struct exporter_request *request = parser->data;

    if (!request->http_edit_response_from_server) return ZPATH_RESULT_NO_ERROR;

    int current_header = request->total_resp_headers - 1;

    if ((current_header < 0) ||
        (request->resp_header_value_ofs[current_header] >= 0)) {
        /* If we haven't started yet, or if there is a value for the current header, we must be on to a new header! */
        if (request->total_resp_headers == EXPORTER_RESPONSE_MAX_HEADERS) {
            /* If max is reached we must terminate and reset the connection */
            EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_COOKIE] %d: Max response headers", request->name, request->request_num);
            return 1;
        }
        request->total_resp_headers++;
        current_header++;
        request->resp_header_name_ofs[current_header] = -1;
        request->resp_header_value_ofs[current_header] = -1;
    }
    if (request->resp_header_name_ofs[current_header] < 0) {
        request->resp_header_name_ofs[current_header] = zmicro_heap_str_index(&(request->heap),
                                                                     data,
                                                                     length);
    } else {
        request->resp_header_name_ofs[current_header] = zmicro_heap_append_str_index(&(request->heap),
                                                                            request->resp_header_name_ofs[current_header],
                                                                            strlen(((const char *)request->heap.start) + request->resp_header_name_ofs[current_header]),
                                                                            data,
                                                                            length);
    }
    if (request->resp_header_name_ofs[current_header] < 0) {
        EXPORTER_LOG(AL_ERROR, "%s: %d: Heap allocation failure on HTTP parser response hdr field callback", request->name, request->request_num);
        return 1;
    }

    EXPORTER_DEBUG_HTTP_DETAIL("%s: %d: Received following response header %d name <%s>",
            request->name,
            request->request_num, current_header,
            ((const char *)request->heap.start) + request->resp_header_name_ofs[current_header]);

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_response_generate_new_header(struct exporter_request *request)
{
    if (!request->http_edit_response_from_server) return ZPATH_RESULT_NO_ERROR;

    int len = 0;
    int ret = 0;

    /* Generate the packet with rewritten Set-Cookie value, send it to the client */
    if (!request->mged_ba_response_evbuf) {
        request->mged_ba_response_evbuf = evbuffer_new();
    }

    len = evbuffer_add_printf(request->mged_ba_response_evbuf,
            "HTTP/%d.%d %d %s\r\n",
            request->req_http_major,
            request->req_http_minor,
            request->response_status_code,
            request->response_status_str);

    if (-1 == len) {
        return ZPATH_RESULT_ERR;
    }

    for (int i = 0; i < request->total_resp_headers; i++) {
        /* For OT the reponse is coming from SRA nginx server
         * NGINX server has a strict CSP where only self is allowed
         * CORS call to User Portal FQDN shall be blocked by browser
         * Hence we need to add User Portal FQDN in CSP header
         * Browser will then send the CORS request for User Portal FQDN to Exporter
         *
         * For managed 2.0 we can add a wildcard and have static CSP header but for 1.0
         * we need to modify the CSP header with the configured value of User Portal FQDN
         */
        if (request->conn->exporter_domain->is_ot &&
                strcmp(zmicro_heap_ref(&request->heap, request->resp_header_name_ofs[i]), "Content-Security-Policy") == 0) {
            ret = evbuffer_add_printf(request->mged_ba_response_evbuf,
                    EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL,
                    request->conn->exporter_domain->domain,
                    ZPATH_LOCAL_CLOUD_NAME,
                    EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                    request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                    request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                    EXPORTER_DOMAIN_AUTH);
         } else {
			ret = evbuffer_add_printf(request->mged_ba_response_evbuf,
                    "%s: %s\r\n",
                    zmicro_heap_ref(&request->heap, request->resp_header_name_ofs[i]),
                    zmicro_heap_ref(&request->heap, request->resp_header_value_ofs[i]) );
        }

        EXPORTER_DEBUG_HTTP_DETAIL("[UNIP_PORTAL_PARSER] : Add into evbuf - %s:%s - len:%d",
                zmicro_heap_ref(&request->heap, request->resp_header_name_ofs[i]),
                zmicro_heap_ref(&request->heap, request->resp_header_value_ofs[i]), ret);

        if (-1 == ret) {
            return ZPATH_RESULT_ERR;
        }
        len += ret;  /*Std hdr + each hdr. */
    }

    /* Enhancement - Add exporter headers since exporter is modifying this response */

    ret = evbuffer_add_printf(request->mged_ba_response_evbuf, "\r\n");
    len += ret;

    request->response_len = len;
    request->resp_hdr_len = len;
    request->http_response_generated = 1;

    return ZPATH_RESULT_NO_ERROR;
}

/* unified-portal ends*/

static int exporter_resp_parser_headers_complete_cb(struct http_parser *parser)
{
    struct exporter_request *request = parser->data;

    EXPORTER_DEBUG_HTTP("%s[%s]: HTTP response headers is complete", request->name,request->url);

    if (request->req_method == HTTP_HEAD) {
        EXPORTER_DEBUG_HTTP("%s: Skip HTTP response body - this corresponds to a HEAD request.", request->name);
        request->http_response_complete = 1;
        request->log.rsp_rx_done_us = epoch_us();
        request->log.response_status = parser->status_code;
        exporter_request_enter_state(request, async_state_response_done);

        /* unified-portal - For head request body will never come and we need to send response */
        if (request->http_edit_response_from_server) {
            if (ZPATH_RESULT_NO_ERROR != exporter_response_generate_new_header(request)) {
                EXPORTER_LOG(AL_CRITICAL, "[UNIP_LOG] Implement me");
                /* Error case - Never return 1, its to skip body */
            }

            /* This is somewhat
             * different than other callbacks because if the user returns 1, we
             * will interpret that as saying that this message has no body. This
             * is needed for the annoying case of recieving a response to a HEAD
             * request.
             */

            exporter_mged_ba_response_send(request);
        }

        return 1; /* This is to tell the parser to skip body processing */
    }

    if (request->guac_info) {
        request->guac_info->is_header = 0;

        if (!is_pra_guacd_service_disabled()) {
            const char *error = guacd_subcomponent_allow_session();
            if (error != NULL) {
                /* clear_response_data arg has been set to 0 to avoid drain of HTTP upgrade response,
                 * that is still present in response_data */
                if (strcmp(error, EXP_MT_SETUP_ERR_PRA_UNAVAILABLE) == 0) {
                    exporter_guac_send_response_internal(request,
                            HTTP_STATUS_INTERNAL_SERVER_ERROR,0,
                            "%s",
                            "Disconnected from server : PRA is unavailable");
                } else {
                    exporter_guac_send_response_internal(request,
                            HTTP_STATUS_INTERNAL_SERVER_ERROR,0,
                            "%s",
                            "Disconnected from server : System is overloaded");
                }
                exporter_request_enter_state(request, async_state_response_done);
                return ZPATH_RESULT_NO_ERROR;
            }
        }
        if (!request->guac_info->is_pra_interactive_auth_disabled) {
            // Send getCredentials to open the tunnel
            exporter_guac_instr_push(request->response_data, registered_op_codes[OP_CODE_GET_CRED], "", 0);
        } else {
            EXPORTER_DEBUG_HTTP("%s: PRA Interactive Auth is disabled no need to send getCredentials", request->name);
            int res = exporter_guac_connect(request, 0);
            if (request->is_proxy_conn_async && (res == ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %s, request %s, received async from guac_connect", __func__, request->name);
            } else if (res != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s, request %s, Error from guac_connect. Sending Failed to connect to user", __func__, request->name);
                exporter_guac_send_response(request,
                        HTTP_STATUS_INTERNAL_SERVER_ERROR,
                        "%s",
                        "Disconnected from server : Failed to connect");
            }
        }
    }

    /* unified-portal */
    if (request->http_edit_response_from_server) {
        if (ZPATH_RESULT_NO_ERROR != exporter_response_generate_new_header(request)) {
            /* Error case - Never return 1 to parser`, its to skip body in HEAD request */
            EXPORTER_LOG(AL_CRITICAL, "%s: [UNIP_LOG] Error generating response header", request->name);
        }
        exporter_mged_ba_response_send(request);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static struct http_parser_settings resp_parser_settings = {
    NULL,                                              //    http_cb      on_message_begin;
    NULL,                                              //    http_data_cb on_url;
    exporter_resp_parser_status_cb,                    //    http_data_cb on_status;
    exporter_resp_parser_header_field_cb,              //    http_data_cb on_header_field;
    exporter_resp_parser_header_value_cb,              //    http_data_cb on_header_value;
    exporter_resp_parser_headers_complete_cb,          //    http_cb      on_headers_complete;
    NULL,                                              //    http_data_cb on_body;
    exporter_resp_parser_message_complete_cb,          //    http_cb      on_message_complete;

    /* When on_chunk_header is called, the current chunk length is stored
     * in parser->content_length.
     */
    NULL,                                              //    http_cb      on_chunk_header;
    NULL,                                              //    http_cb      on_chunk_complete;
};

/*************************************************************************************************
 *  Exporter Object Allocation/Free
 */
void exporter_request_free_q_init()
{
    memset(&request_free_q, 0, sizeof(request_free_q));
    request_free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(request_free_q.request_list));
}

static void exporter_request_free(struct exporter_request *request)
{
    ZPATH_MUTEX_LOCK(&(request_free_q.lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(request_free_q.request_list), request, queue_entry);
    request_free_q.stats.free_queue_count++;
    ZPATH_MUTEX_UNLOCK(&(request_free_q.lock), __FILE__, __LINE__);
}

static struct exporter_request *exporter_request_allocate(void)
{
    struct exporter_request *request = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&(request_free_q.lock), __FILE__, __LINE__);

    if ((request = TAILQ_FIRST(&(request_free_q.request_list)))) {
        TAILQ_REMOVE(&(request_free_q.request_list), request, queue_entry);
        request_free_q.stats.free_queue_count--;
        incarnation = request->incarnation;
        incarnation++;
        memset(request, 0, sizeof(struct exporter_request));
        request->incarnation = incarnation;
    } else {
        request = (struct exporter_request *)EXPORTER_CALLOC(sizeof(struct exporter_request));
        if (request) {
            request_free_q.stats.allocations++;
            request->incarnation = 1;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(request_free_q.lock), __FILE__, __LINE__);

    return request;
}

void exporter_conn_free_q_init()
{
    memset(&conn_free_q, 0, sizeof(conn_free_q));
    conn_free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(conn_free_q.conn_list));
}

static void exporter_conn_free(struct exporter_conn *conn)
{
    ZPATH_MUTEX_LOCK(&(conn_free_q.lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(conn_free_q.conn_list), conn, queue_entry);
    conn_free_q.stats.free_queue_count++;
    ZPATH_MUTEX_UNLOCK(&(conn_free_q.lock), __FILE__, __LINE__);
}

static struct exporter_conn *exporter_conn_allocate(void)
{
    struct exporter_conn *conn = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&(conn_free_q.lock), __FILE__, __LINE__);

    if ((conn = TAILQ_FIRST(&(conn_free_q.conn_list)))) {
        TAILQ_REMOVE(&(conn_free_q.conn_list), conn, queue_entry);
        conn_free_q.stats.free_queue_count--;
        incarnation = conn->incarnation;
        incarnation++;
        memset(conn, 0, sizeof(struct exporter_conn));
        conn->incarnation = incarnation;
    } else {
        conn = (struct exporter_conn *)EXPORTER_CALLOC(sizeof(struct exporter_conn));
        if (conn) {
            conn_free_q.stats.allocations++;
            conn->incarnation = 1;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(conn_free_q.lock), __FILE__, __LINE__);

    return conn;
}


static
int exporter_conn_stats_dump(struct zpath_debug_state*   request_state,
                                    const char**         query_values,
                                    int                  query_value_count,
                                    void*                cookie)
{
    int tid;
    int64_t attach_cnt;
    int64_t detach_cnt;


    ZPATH_MUTEX_LOCK(&(conn_free_q.lock), __FILE__, __LINE__);
    ZDP("Total connection objects allocated          = %"PRId64"\n", conn_free_q.stats.allocations);
    ZDP("Total connection objects waiting to be used = %"PRId64"\n", conn_free_q.stats.free_queue_count);
    ZDP("Total connection objects currently in use   = %"PRId64"\n", conn_free_q.stats.allocations -
                                                                     conn_free_q.stats.free_queue_count);
    ZPATH_MUTEX_UNLOCK(&(conn_free_q.lock), __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&(request_free_q.lock), __FILE__, __LINE__);
    ZDP("Total request objects allocated             = %"PRId64"\n", request_free_q.stats.allocations);
    ZDP("Total request objects waiting to be used    = %"PRId64"\n", request_free_q.stats.free_queue_count);
    ZDP("Total request objects currently in use      = %"PRId64"\n", request_free_q.stats.allocations -
                                                                  request_free_q.stats.free_queue_count);
    ZPATH_MUTEX_UNLOCK(&(request_free_q.lock), __FILE__, __LINE__);

    /* Usage stats */
    ZDP("Total connector objects created             = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_connnector_create));
    ZDP("Total connector objects destroyed           = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_connnector_destroy));
    ZDP("Total connector objects create error        = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_connnector_create_error));
    ZDP("Total connector objects connect error       = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_connnector_connect_error));

    /* SSL_CTX stats */
    ZDP("Total ssl_ctx objects created                    = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_ssl_ctx_create));
    ZDP("Total ssl_ctx objects create error               = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_ssl_ctx_create_error));
    ZDP("Total ssl_ctx objects destroyed                  = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_ssl_ctx_destroy));

    /*Mtunnel stats */
    ZDP("Total mt objects created                         = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_create));
    ZDP("Total mt objects destroyed                       = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_destroy));

    /* Connector slow free stats */
    ZDP("Total mt connector schedule slow free            = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_connector_sched_slow_free));
    ZDP("Total mt connector skip slow free queue          = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_connector_skip_slow_free_queue));

    /* ZFCE connection stats */
    ZDP("Total zfce connections created                   = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_create));
    ZDP("Total zfce connections create error              = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_create_error));
    ZDP("Total zfce connections destroyed                 = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_destroy));
    ZDP("Total zfce connections force destroyed           = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_force_destroy));

    /* Fatal errors */
    ZDP("Total fatal socket errors                        = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_fatal_connector_socket_error));

    /* Google posture stats */
    ZDP("Total google posture redirects to caa            = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_posture_to_caa_redirect));
    ZDP("Total google posture gkey failure from caa      = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_posture_failed_gkey_from_caa));
    ZDP("Total google posture not found in object store  = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_posture_not_found_in_object_store));
    ZDP("Total google posture failed parsing             = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_posture_failure_in_parsing));

    ZDP("Total redirects to authsp with levelid          = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_redirect_to_authsp_levelid));

    ZDP("Connection Attach|Detach by thread\n");
    for (tid = 0; tid < FOHH_MAX_THREADS; tid++) {
        attach_cnt = EXPORTER_CONN_THREAD_ATTACH_GET(tid);
        detach_cnt = EXPORTER_CONN_THREAD_DETACH_GET(tid);

        if ((attach_cnt != 0) || (detach_cnt != 0)) {
            ZDP("Thread[%d]: Attach: %ld, Detach: %ld, Diff: %ld\n", tid, (long)attach_cnt, (long)detach_cnt, (long)(attach_cnt - detach_cnt));
        }
    }
    ZDP("Complete\n");

    return ZPATH_RESULT_NO_ERROR;
}

/* Create upto 400K sockets !!! */
#define EXPORTER_TRIGGER_MAX_SOCKETS_EXHAUST    (4*100*1000)
#define EXPORTER_MAX_SOCKETS_EXHAUST_ERRS_STOP   5
static
int exporter_conn_trigger_socket_exhaustion(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    int save_errno;
    int res;
    int i;
    int sock_errs = 0;
    int num_alloc_fds = 0;
    int fd[2];

    ZDP("!!! WARNING: QA USE ONLY !!!! - Will trigger socket exhaustion leading to a fatal error !!!\n");

    for (i = 1; i <= EXPORTER_TRIGGER_MAX_SOCKETS_EXHAUST; i++) {
        res = socketpair(AF_UNIX, SOCK_STREAM, 0, fd);
        if (res) {
            save_errno = errno;
            EXPORTER_LOG(AL_INFO, "Error creating socketpair attempt[%d]: %s, allocated %d sockets",
                         i, strerror(save_errno), num_alloc_fds);

            //ZDP("Error creating socketpair attempt[%d]: %s, allocated %d sockets\n", i, strerror(save_errno), num_alloc_fds);
            sock_errs++;
        } else {
            num_alloc_fds = 2 * i;
            EXPORTER_LOG(AL_INFO, "Sucessfully creating socketpair attempt[%d]: %d total sockets", i, num_alloc_fds);
            //ZDP("Sucessfully creating socketpair attempt[%d]: %d total sockets\n", i, num_alloc_fds);
        }

        if (sock_errs >= EXPORTER_MAX_SOCKETS_EXHAUST_ERRS_STOP) {
            ZDP("Error creating more than %d sockets\n", num_alloc_fds);
            break;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static
int exporter_conn_stats(struct zpath_debug_state*   request_state,
                                    const char**         query_values,
                                    int                  query_value_count,
                                    void*                cookie)
{
    /* Mtunned Stats */
    ZDP("Total mtunnel objects active              = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt));
    ZDP("Total mtunnel objects created             = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_create));
    ZDP("Total mtunnel objects destroyed           = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_mt_destroy));

    /* User stats */
    ZDP("Total zfce connections active             = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn));
    ZDP("Total zfce connections created            = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_create));
    ZDP("Total zfce connections create error       = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_create_error));
    ZDP("Total zfce connections destroyed          = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_destroy));
    ZDP("Total zfce connections force destroyed    = %"PRId64"\n", EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_zfce_conn_force_destroy));

    /* Exporter - Browser Connection stats*/
    ZDP("Total number of exporter browser connections created   = %"PRId64"\n", exporter_fohh_worker_get_num_conn_connect());
    ZDP("Total number of exporter browser connections destroyed = %"PRId64"\n", exporter_fohh_worker_get_num_conn_disconnect());

    /* Number of requests stats */
    ZDP("Total number of active requests    = %"PRId64"\n", exporter_fohh_worker_get_num_request());
    ZDP("Total number of requests created   = %"PRId64"\n", exporter_fohh_worker_get_num_request_create());
    ZDP("Total number of requests destroyed = %"PRId64"\n", exporter_fohh_worker_get_num_request_destroy());

    return ZPATH_RESULT_NO_ERROR;
}


int
exporter_conn_init()
{
    int result;

    result = zpath_debug_add_read_command("Dump info related to conn operation.",
                                     "/exporter/conn/stats_dump", exporter_conn_stats_dump, NULL, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /exporter/conn/stats_dump");
        return result;
    }

    result = zpath_debug_add_read_command("Dump info related to conn operation.",
                                     "/exporter/conn/stats", exporter_conn_stats, NULL, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /exporter/conn/stats");
        return result;
    }

    result = zpath_debug_add_admin_command("Trigger fatal conn socket exhaustion error -> !!! WARNING QA USE ONLY !!!",
                                     "/exporter/conn/trigger_socket_exhaustion",
                                     exporter_conn_trigger_socket_exhaustion, NULL, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /exporter/conn/trigger_socket_exhaustion");
        return result;
    }

    result = zpath_debug_add_read_command("Exporter domain configuration", "/exporter/domain_config",
                                    exporter_debug_config_override_per_domain,
                                    NULL,
                                    "domain", "domain",
                                    NULL);
    if (result) {
        EXPORTER_LOG(AL_ERROR, "couldn't add /exporter/domain_config");
    }
    return result;
}
