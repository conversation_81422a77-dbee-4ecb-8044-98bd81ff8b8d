/*
 * exporter_request_util.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#include "exporter/exporter_request_util.h"
#include "diamond/diamond.h"
#include "exporter/exporter.h"
#include "exporter/exporter_cors.h"
#include "exporter/exporter_guac_api.h"
#include "zpath_lib/zpath_lib.h"

/* unified-portal start */
/*
 * RFC - https://datatracker.ietf.org/doc/html/rfc6265
 *
 * - We have to change domain name or remove it.
 * - To parse it we are using below logic
 * - We are not checking for token char accuracy since we
 *   only want to extract domain and change
 * - For eg "my cookie"="value" is invalid cookie due to space
 * - We don't have to correct this and parse, as proxy our job is to replace
 *    domain cookie only and we must sent rest of the data as it is
 *
 *
 * ############################
 *  Output after parsing
 * ############################
 * - All spaces are removed in before and start of tokens
 * - Multiple ; are replaced with a single semicolon only
 * - Domain is deleted if it matches
 *
 * ############################
 *  Basic Algo for parsing
 * ############################
 *
 * - All attributes are seperated by ";"
 * - Attribute is "key=value;" or "key;" only
 * - There can be spaces before start of key or start of value
 * - There can be spaces at the end of key or value
 * - Values/Keys can have spaces so "hello hello" is a valid value "my cookie" = "hello hello";
 * - Values can have "=" so "hello=hello" is a valid value "mycookie = hello=hello";
 * - Identify domain attributes only
 * - Handle domain with below values
 *   - [server1.ba.app.mockfirm.com]
 *   - [.server1.ba.app.mockfirm.com]
 *   - [   server1.ba.app.mockfirm.com   ]
 *   - [  .server1.ba.app.mockfirm.com   ]
 * - Set-Cookie header can be empty
 * - Domain can be empty
 * - Domain to be present only once else return the orig set-cookie
 *   - We do handle multiple domain keys but why to change invalid header ?
 */
enum e_cookie_key_name {
    KEY_INVALID,
    KEY_DOMAIN,
    KEY_EXPIRES,
    KEY_HTTPONLY,
    KEY_PARTITIONED,
    KEY_PATH,
    KEY_SAMESITE,
    KEY_SECURE,
};

const char *cookie_key_name[] = {
    [KEY_DOMAIN] = "domain",
    [KEY_SECURE] = "secure",
};

#define DOMAIN_KEY_LEN 6
#define SECURE_KEY_LEN 6

enum cookie_parsing_state {
    KEY_INIT, /* The first occurance of character after skipping spaces for KEY */
    KEY_PROCESS, /* We got first character now we are parsing until we get a = OR ; OR EOF */
    VAL_INIT, /* The first occurance of character after skipping spaces for VALUE */
    VAL_PROCESS, /* We got first character and we are parsing until we get a ; OR EOF */
};

/*
 * Done only for managed BA apps.
 */
char *exporter_util_edit_redirect_header(const char *in_payload, size_t in_payload_len, const char *in_app_domain, const char *in_ext_domain)
{
    if (!in_payload || !in_app_domain || !in_ext_domain || !in_payload_len) {
        return NULL;
    }

    /* Goal is to rewrite 'http(s)://<in_app_domain>' in the location header with
     * 'https://<in_ext_domain>/'.
     */
    size_t start = 0;
    char * new_location_value = NULL;
    const char * rest = NULL;
    size_t in_domain_len = strnlen(in_app_domain, EXPORTER_DOMAIN_NAME_MAX_SIZE);
    char http_host[EXPORTER_DOMAIN_NAME_MAX_SIZE] = {0};

    if (in_domain_len > in_payload_len) {
        /* Not an error, the location header value probably didnt have the
           internal app domain in it.*/
        return NULL;
    }

    /* Parse the location header Value using http url parser. Match for the PATH */
    struct http_parser_url url_parser;
    http_parser_url_init(&url_parser);
    int res = 0;

    res = http_parser_parse_url(in_payload, in_payload_len, 0, &url_parser);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "URL parsing failed url: %.*s", (int)in_payload_len, in_payload);
        return NULL;
    }

    if (url_parser.field_set & (1 << UF_HOST)) {
        snprintf(http_host, EXPORTER_DOMAIN_NAME_MAX_SIZE, "%.*s",
                 (int)url_parser.field_data[UF_HOST].len,
                 &(in_payload[url_parser.field_data[UF_HOST].off]));

        if (in_domain_len != (int)url_parser.field_data[UF_HOST].len) {
            return NULL;
        }

        if (!strncmp(http_host, in_app_domain, in_domain_len)) {
               /*** Replace with external fqdn ***/
            start = &(in_payload[url_parser.field_data[UF_HOST].off]) - in_payload;
            rest = &(in_payload[url_parser.field_data[UF_HOST].off]) + (int)url_parser.field_data[UF_HOST].len;

            /* new data value needs in_payload_len - lengthof(in_app_domain) + lengthof(in_ext_domain).
              For easy math, we will allocate 255 extra bytes. This is a temp memory hence it is ok.
             */
            size_t len = in_payload_len + EXPORTER_DOMAIN_NAME_MAX_SIZE;
            new_location_value = EXPORTER_CALLOC(len);
            if (new_location_value) {
                snprintf(new_location_value, len-1, "%.*s%s%s",
                         (int)start,in_payload,
                         in_ext_domain, rest);
                EXPORTER_DEBUG_HTTP("[UNIP_LOG_RESP]- New location header is : %s", new_location_value);
            }
        }
    }

    return new_location_value;
}

/*
 * DESCRIPTION
 *  - Takes Set-Cookie header payload and replaces domain with ext_domain if delete_domain is 0
 *  - If delete_domain is 1 domain attribute is deleted
 * RETURN VALUE
 *  - Returns the updated header and its caller who has to free it
 *  - Return string is terminated with null character
 *  - In case of error which is in variable res, the original header is strdup and returned
 *
 */
char *exporter_util_edit_cookie_value(const char *in_payload, size_t in_payload_len, const char *in_app_domain, const char *in_ext_domain, int in_delete_domain, int in_delete_type)
{
    if (!in_payload || !in_app_domain || !in_ext_domain) {
        return NULL;
    }

    if (in_payload_len == 0) {
        return NULL;
    }

    /* Add ; for termination in the end */
    const size_t MAX_LEN = in_payload_len + 2; // +2 for ; and null char
    const size_t MAX_OFFSET = MAX_LEN - 1;
    char *p = (char *)EXPORTER_MALLOC(MAX_LEN);

    snprintf(p, MAX_LEN, "%.*s;", (int)in_payload_len, in_payload);

    int idx = 0;
    int key_s = 0;
    int key_e = 0;
    int value_s = 0;
    int value_e = 0;
    int state = 0;
    int space_before_end = 0;
    int last_key_name = 0;
    int n_domain = 0;
    int is_domain_replaced = 0;
    int is_secure_key_found = 0;

    const size_t SET_COOKIE_MAX_LEN = in_payload_len + 256;
    char *resp = (char *)EXPORTER_MALLOC(SET_COOKIE_MAX_LEN);

    int resp_bytes = 0;
    int token_bytes = 0;
    int res = 0;
    char *S = resp;
    char *E = resp + SET_COOKIE_MAX_LEN;

    /* Init */
    memset(resp, 0, SET_COOKIE_MAX_LEN);
    key_s = key_e = 0;
    value_s = 0;
    value_e = 0;
    state = KEY_INIT;
    space_before_end = 0;
    last_key_name = KEY_INVALID;

    while (idx <= MAX_OFFSET) {
        char input = p[idx];

        switch (input) {
            case '=':
                /* = can be present inside value also, we need to stop at first = only */
                if (state == KEY_PROCESS) {
                    state = VAL_INIT;
                    //EXPORTER_DEBUG_HTTP_DETAIL("[UNIP_LOG_COOKIE] KEY found: [%.*s]\n", (int)(key_e - key_s + 1), &p[key_s]);
                    value_s = idx + 1;

                    token_bytes = (int)(key_e - key_s + 1); /* original value offsets */

                    if ( (token_bytes == DOMAIN_KEY_LEN) &&
                         (strncasecmp(&p[key_s], cookie_key_name[KEY_DOMAIN], token_bytes) == 0)) {
                        //EXPORTER_DEBUG_HTTP_DETAIL("Domain key found\n");
                        last_key_name = KEY_DOMAIN;
                        ++n_domain;
                        if (n_domain > 1) {
                            /*
                             * comment below line if we want to support multiple domains
                             * As per rfc we should never get multple domain entries
                             */
                            //res = 1; //Set this to 1 to treat multiple domains as error in Set-Cookie header
                        }
                        if (in_delete_domain) { token_bytes = 0; }
                    } else if ( (token_bytes == SECURE_KEY_LEN) &&
                                (strncasecmp(&p[key_s], cookie_key_name[KEY_SECURE], token_bytes) == 0)) {
                        is_secure_key_found = 1;
                    }
                    /* COPY KEY */
                    if (token_bytes) {
                        resp_bytes += (token_bytes + 1);

                        if (0 && last_key_name == KEY_DOMAIN) {
                            /* Do we want to sanitize domain for case in sensitive ? */
                            S += sxprintf(S, E, "%.*s=", token_bytes, cookie_key_name[KEY_DOMAIN]);
                        } else {
                            S += sxprintf(S, E, "%.*s=", token_bytes, &p[key_s]);
                        }

                        if (resp_bytes != (S - resp)) {
                            res = 2;
                        }
                    }
                } else if (state == VAL_INIT) state = VAL_PROCESS;
                else if (state == KEY_INIT) state = KEY_PROCESS;
                break;
            case ';':
                /* keys can be defined without values also */
                if (state == KEY_PROCESS) {
                    //EXPORTER_DEBUG_HTTP_DETAIL("[UNIP_LOG_COOKIE] KEY found: [%.*s]\n", (int)(key_e - key_s + 1), &p[key_s]);

                    token_bytes = (int)(key_e - key_s + 1);
                    if ( (token_bytes == SECURE_KEY_LEN) &&
                         (strncasecmp(&p[key_s], cookie_key_name[KEY_SECURE], SECURE_KEY_LEN) == 0) ) {
                        is_secure_key_found = 1;
                    }

                    /* COPY KEY */
                    resp_bytes += (token_bytes + 1);
                    S += sxprintf(S, E, "%.*s;", token_bytes, &p[key_s]);
                    if (resp_bytes != (S - resp)) {
                        res = 3;
                    }
                }
                else if (state == VAL_INIT) {
                    //EXPORTER_DEBUG_HTTP_DETAIL("[UNIP_LOG_COOKIE] VAL found in init state: [%.*s]\n", (int)(value_e - value_s + 1), &p[value_s]);

                    if (last_key_name != KEY_DOMAIN) {
                        ++resp_bytes;
                        S += sxprintf(S, E, ";");
                        if (resp_bytes != (S - resp)) {
                            res = 4;
                        }
                    }
                }
                else if (state == VAL_PROCESS) {
                    //EXPORTER_DEBUG_HTTP_DETAIL("[UNIP_LOG_COOKIE] VAL found: [%.*s]\n", (int)(value_e - value_s + 1), &p[value_s]);
                    token_bytes = (int)(value_e - value_s + 1); /* original value offsets */
                    /*
                     * Value for domain can be like below
                     * server1.ba.app.mockfirm.com
                     * .server1.ba.app.mockfirm.com
                     * *.ba.app.mockfirm.com - invalid
                     * "server1.ba.app.mockfirm.com" - invalid
                     * 'server1.ba.app.mockfirm.com' - invalid
                     *
                     * Wildcard - Do diamond search and remove wildcard, we cannot set wildcard cookies
                     * Single/double quotes - Retain quotes and extract value inside quotes
                     * Prefix dot - Retain dot and extract value after dot
                     *
                     */

                    const char *p_value = &p[value_s];
                    int match_bytes = token_bytes;

                    if (last_key_name == KEY_DOMAIN) {
                        if (p[value_s] == '.') { ++value_s; --match_bytes;}

                        int is_match = 0;
                        if (in_delete_type == EXPORTER_MANAGED_BA_DELETE_ALWAYS) {
                            is_match = 1;
                        } else {
                            is_match = (0 == strncasecmp(&p[value_s], in_app_domain, match_bytes));
                        }

                        if (is_match) {
                            if (in_delete_domain) { token_bytes = 0; }
                            else { token_bytes = (int)strnlen(in_ext_domain, EXPORTER_DOMAIN_NAME_MAX_SIZE); p_value = in_ext_domain; }
                            is_domain_replaced = 1;
                        }
                        else {
                            if (in_delete_domain) {
                                /* No match but we already deleted the domain key so add it again */

                                /* COPY KEY */
                                resp_bytes += (6 + 1);

                                /* Do we want to sanitize domain for case in sensitive ? */
                                //S += sxprintf(S, E, "%.*s=", 6, cookie_key_name[KEY_DOMAIN]);
                                S += sxprintf(S, E, "%.*s=", 6, &p[key_s]);

                                if (resp_bytes != (S - resp)) {
                                    res = 5;
                                }
                            }
                        }
                    }

                    /* COPY VALUE */
                    if (token_bytes) {
                        resp_bytes += (token_bytes + 1);
                        S += sxprintf(S, E, "%.*s;", token_bytes, p_value);
                        if (resp_bytes != (S - resp)) {
                            res = 6;
                        }
                    }
                } else {
                    /* skip adding ; in INIT stage */
                }

                /* reset for new key-value pair */
                key_s = idx + 1;
                state = KEY_INIT;
                value_s = value_e = space_before_end = 0;
                last_key_name = KEY_INVALID;
                break;
            case ' ':
                /* if we are in INIT state skip all spaces */
                if (state == KEY_INIT) { key_s = idx + 1;}
                else if (state == VAL_INIT) { value_s = idx + 1;}

                /*
                 * if we are PROCESS state and spacebar comes it could be end
                 * For Key = Spaces are not allowed
                 * For Value = Spaces are allowed
                 * We are keeping the same behaviour for both
                 */
                else if (state == VAL_PROCESS || state == KEY_PROCESS) space_before_end = 1;
                break;
            default:
                /* if we are in init state word start if any single char comes which is not space */
                if (state == VAL_INIT) state = VAL_PROCESS;
                else if (state == KEY_INIT) state = KEY_PROCESS;
                if (state == VAL_PROCESS || state == KEY_PROCESS) space_before_end = 0;
        }

        if (!space_before_end) { value_e = idx; key_e = idx;}

        idx++;

        if (res) break;
    }

    if (res || is_domain_replaced == 0) {
        S = resp;
        E = resp + SET_COOKIE_MAX_LEN;

        /* copy and send the orig header sanitized with ; at the end */
        S += sxprintf(S, E, "%.*s", (int)MAX_LEN, p);
    }

    if (!is_secure_key_found) {
        S += sxprintf(S, E, "Secure;");
    }

    EXPORTER_FREE(p);
    return resp;
}

/* unified-portal end */

int url_encode(const char *str, char *dest, size_t dest_len)
{
    static const char xlate[] = {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
    char *e = dest + dest_len;

    while ((dest < e) && (*str)) {
        if ((isalnum((unsigned char)(*str)))
            //            || ((*str) == '.')
            //            || ((*str) == '/')
            //            || ((*str) == ':')
            ) {
            *dest = *str;
            dest++;
            str++;
        } else {
            if ((dest + 4) < e) {
                dest[0] = '%';
                dest[1] = xlate[(((unsigned char)(*str)) >> 4)];
                dest[2] = xlate[(((unsigned char)(*str)) & 0xf)];
                dest+=3;
                str++;
            } else {
                return ZPATH_RESULT_ERR_TOO_LARGE;
            }
        }
    }

    if ((*str) || (dest >= e)) return ZPATH_RESULT_ERR_TOO_LARGE;
    *dest = 0;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_redirect_encode_caa(struct exporter_request *request,
                                         const char *set_cookie_header_value,
                                         const char *set_cookie_header_legacy_value,
                                         const char *set_cookie_header_value2,
                                         const char *set_cookie_header_legacy_value2,
                                         const char *target_domain,
                                         int target_port,
                                         const char *target_path,
                                         ...)
{
    char tmp_buf[10000];
    char location[2 * EXPORTER_URL_MAX_ENCODE_SIZE];
    char url_encoded_query_value[2 * EXPORTER_URL_MAX_ENCODE_SIZE];
    int status = HTTP_STATUS_TEMPORARY_REDIRECT;
    va_list vl;
    char *s, *e;
    int count = 0;
    const char *query_name;
    const char *query_value;
    int res;
    int resp_sec_header;
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    int64_t customer_gid;

    if (exporter_request_check_redirect_for_ot(request, target_domain) == ZPATH_RESULT_NO_ERROR) {
        return ZPATH_RESULT_NO_ERROR;
    }

    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    s = location;
    e = s + sizeof(location);

    if (target_path && target_port) {
        s += sxprintf(s, e, "https://%s:%d%s", target_domain, target_port, target_path);
    } else if (target_path) {
        s += sxprintf(s, e, "https://%s%s", target_domain, target_path);
    } else {
        s += sxprintf(s, e, "%s", target_domain);
    }

    va_start(vl, target_path);
    while ((query_name = va_arg(vl, const char *))) {
        query_value = va_arg(vl, const char *);
        if (!query_value) {
            EXPORTER_LOG(AL_CRITICAL, "Invalid number of query parameters");
            va_end(vl);
            return ZPATH_RESULT_ERR;
        }
        res = url_encode(query_value, url_encoded_query_value, sizeof(url_encoded_query_value));
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_CRITICAL, "%s: url encoded string error: %s", request->name, zpath_result_string(res));
            va_end(vl);
            return ZPATH_RESULT_ERR;
        }
        s += sxprintf(s, e, count?"&":"?");
        s += sxprintf(s, e, "%s=%s", query_name, url_encoded_query_value);
        count++;
    }
    va_end(vl);
    s = tmp_buf;
    e = tmp_buf+sizeof(tmp_buf);

    s += sxprintf(s, e, "%s", std_hdrs);
    s += sxprintf(s, e, "Content-Length: 0\r\n");
    s += sxprintf(s, e, "Content-Type: text/html\r\n");
    s += sxprintf(s, e, "Location: %s\r\n", location);

    int add_cors_headers = 0;
    if (request->caa_data && request->caa_data->customer_gid) {
        EXPORTER_DEBUG_AUTH("request->caa_data->customer_gid: %"PRId64"", request->caa_data->customer_gid);
        customer_gid = request->caa_data->customer_gid;
    } else {
        EXPORTER_DEBUG_AUTH("request->orig_customer_gid: %"PRId64"", request->orig_customer_gid);
        customer_gid = request->orig_customer_gid;
    }

    if ((request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_CORS) && is_cors_enabled(customer_gid)) {
        EXPORTER_DEBUG_AUTH("%s request->sec_fetch_mode is cors", request->name);
        add_cors_headers = 1;
    } else if (request->cors_request_with_token) {
        EXPORTER_DEBUG_AUTH("%s, cors_request_with_token: %d", request->name, request->cors_request_with_token);
        add_cors_headers = 1;
    } else if (request->conn->exporter_domain) {
        customer_gid = request->conn->exporter_domain->customer_gid;
        if (exporter_client_less_cors_enabled(customer_gid, request)) {
            add_cors_headers = 1;
            EXPORTER_DEBUG_AUTH("%s, exporter_client_less_cors_enabled is enabled", request->name);
        }
        if ((request->conn->exporter_domain->is_ot) &&
            (strcmp(target_domain, EXPORTER_DOMAIN_AUTH) == 0)) {
            add_cors_headers = 1;
        }
    }

    s += sxprintf(s, e, "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n");
    if (request->conn->exporter_domain) {
       resp_sec_header = (request->conn->exporter_domain->is_auth_domain) ? 1 : 0;
    } else {
       resp_sec_header = (request->header_host && !strcmp(request->header_host, EXPORTER_DOMAIN_AUTH)) ? 1 : 0;
    }

    if (resp_sec_header) {
        s += sxprintf(s, e, "X-Content-Type-Options: nosniff\r\n");
        s += sxprintf(s, e, "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n");
        s += sxprintf(s, e, "X-XSS-Protection: 1; mode = block\r\n");
    }

    if (add_cors_headers) {
        s += sxprintf(s, e, "Access-Control-Allow-Credentials: true\r\n");
        s += sxprintf(s, e, "Access-Control-Allow-Origin: %s\n", request->header_origin);
    }

    if (set_cookie_header_value) {
        s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_value);
        if (set_cookie_header_legacy_value) {
            s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_legacy_value);
        }
        if (set_cookie_header_value2) {
            s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_value2);
            if (set_cookie_header_legacy_value2) {
                s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_legacy_value2);
            }
        }
    }
    s += sxprintf(s, e, "\r\n");

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    evbuffer_add_printf(request->response_data,
                       "%s", tmp_buf);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_redirect_encode(struct exporter_request *request,
                                     const char *set_cookie_header_value,
                                     const char *set_cookie_header_legacy_value,
                                     const char *set_cookie_header_value2,
                                     const char *set_cookie_header_legacy_value2,
                                     const char *target_domain,
                                     int target_port,
                                     const char *target_path,
                                     ...)
{
    char tmp_buf[1024*16];
    char location[2 * EXPORTER_URL_MAX_ENCODE_SIZE];
    char url_encoded_query_value[2 * EXPORTER_URL_MAX_ENCODE_SIZE];
    //int status = HTTP_STATUS_TEMPORARY_REDIRECT;
    int status = HTTP_STATUS_SEE_OTHER;
    va_list vl;
    char *s, *e;
    int count = 0;
    const char *query_name;
    const char *query_value;
    int res;
    int resp_sec_header;
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];

    if (exporter_request_check_redirect_for_ot(request, target_domain) == ZPATH_RESULT_NO_ERROR) {
        return ZPATH_RESULT_NO_ERROR;
    }

    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));


    s = location;
    e = s + sizeof(location);

    if (target_path && target_port) {
        s += sxprintf(s, e, "https://%s:%d%s", target_domain, target_port, target_path);
    } else if (target_path) {
        s += sxprintf(s, e, "https://%s%s", target_domain, target_path);
    } else {
        s += sxprintf(s, e, "%s", target_domain);
    }

    va_start(vl, target_path);
    while ((query_name = va_arg(vl, const char *))) {
        query_value = va_arg(vl, const char *);
        if (!query_value) {
            EXPORTER_LOG(AL_CRITICAL, "Invalid number of query parameters");
            va_end(vl);
            return ZPATH_RESULT_ERR;
        }
        res = url_encode(query_value, url_encoded_query_value, sizeof(url_encoded_query_value));
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_CRITICAL, "%s: url encoded string error: %s", request->name, zpath_result_string(res));
            va_end(vl);
            return ZPATH_RESULT_ERR;
        }
        s += sxprintf(s, e, count?"&":"?");
        s += sxprintf(s, e, "%s=%s", query_name, url_encoded_query_value);
        count++;
    }
    va_end(vl);
    s = tmp_buf;
    e = tmp_buf+sizeof(tmp_buf);

    s += sxprintf(s, e, "%s", std_hdrs);
    s += sxprintf(s, e, "Content-Length: 0\r\n");
    s += sxprintf(s, e, "Content-Type: text/html\r\n");
    s += sxprintf(s, e, "Location: %s\r\n", location);

    // Respond with ACAO headers, if cors_request_with_token is set.
    // Or, if the cors request has global,company,exporter,host,origin CORS enabled
    // Or, if a PRA request is redirected to auth domain during re-auth
    int add_cors_headers = 0;
    if (request->cors_request_with_token) {
        add_cors_headers = 1;
    } else if (request->conn->exporter_domain) {
        int64_t customer_gid = request->conn->exporter_domain->customer_gid;
        if (exporter_client_less_cors_enabled(customer_gid, request)) {
            add_cors_headers = 1;
        }
        if ((request->conn->exporter_domain->is_ot) &&
            (strcmp(target_domain, EXPORTER_DOMAIN_AUTH) == 0)) {
            add_cors_headers = 1;
        }
    }

    s += sxprintf(s, e, "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n");
    if (request->conn->exporter_domain) {
       resp_sec_header = (request->conn->exporter_domain->is_auth_domain) ? 1 : 0;
    } else {
       resp_sec_header = (request->header_host && !strcmp(request->header_host, EXPORTER_DOMAIN_AUTH)) ? 1 : 0;
    }

    if (resp_sec_header) {
        s += sxprintf(s, e, "X-Content-Type-Options: nosniff\r\n");
        s += sxprintf(s, e, "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n");
        s += sxprintf(s, e, "X-XSS-Protection: 1; mode = block\r\n");
    }

    if (add_cors_headers) {
        s += sxprintf(s, e, "Access-Control-Allow-Credentials: true\r\n");
        s += sxprintf(s, e, "Access-Control-Allow-Origin: %s\n", request->header_origin);
    }

    if (set_cookie_header_value) {
        s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_value);
        if (set_cookie_header_legacy_value) {
            s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_legacy_value);
        }
        if (set_cookie_header_value2) {
            s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_value2);
            if (set_cookie_header_legacy_value2) {
                s += sxprintf(s, e, "Set-Cookie: %s\r\n", set_cookie_header_legacy_value2);
            }
        }
    }

    s += sxprintf(s, e, "\r\n");

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    evbuffer_add_printf(request->response_data,
                       "%s", tmp_buf);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_respond_status_and_data_csp(
        struct exporter_request *request,
        enum http_status status,
        void *data,
        size_t data_len,
        const char *target_domain)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    EXPORTER_DEBUG_CSP("%s: [CANFINPT] Returning status %d, buffer length = %ld", request->name, status, (long) data_len);

    request->log.response_status = status;

    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "\r\n",
                        std_hdrs,
                        (int) data_len);

    evbuffer_add(request->response_data, data, data_len);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_domain_search(struct exporter *exporter, const char *domain, size_t domain_len, struct exporter_domain **exporter_domain)
{
    struct exporter_domain *results[EXPORTER_MAX_NUM_DOMAINS] = {0};
    int res;
    int i;

    ZPATH_RWLOCK_RDLOCK(&(exporter->lock), __FILE__, __LINE__);
    res = diamond_search(exporter->domains,
                         (const uint8_t *)domain,
                         domain_len,
                         (void **)&(results[0]),
                         NULL,
                         sizeof(results) / sizeof(void *));
    ZPATH_RWLOCK_UNLOCK(&(exporter->lock), __FILE__, __LINE__);

    if (res) {
        if (res >= EXPORTER_MAX_NUM_DOMAINS) {
            EXPORTER_LOG(AL_ERROR, "Too many domains number = %d for search for domain = %s", res, domain);
            return ZPATH_RESULT_NOT_FOUND;
        }
        for (i = 0; i < res; i++) {
            /* Return the first non deleted entry */
            if (results[i] && results[i]->is_deleted == 0) {
                *exporter_domain = results[i];
                return ZPATH_RESULT_NO_ERROR;
            }
        }
    }

    return ZPATH_RESULT_NOT_FOUND;
}

int64_t exporter_get_mged_cert_id_for_domain(struct exporter_domain *exporter_domain)
{
    if (!exporter_domain || !exporter_domain->is_managed_ba) {
       return 0;
    }

    const char* tld_appln_name = exporter_get_tld_customer_name(exporter_domain->is_user_portal, exporter_domain->is_ot);

    if (!tld_appln_name) {
        return 0;
    }

    struct exporter_domain *tld_customer_domain = NULL;
    int res = ZPATH_RESULT_NOT_FOUND;
    size_t len = 0;
    size_t app_len = strnlen(tld_appln_name,MANAGED_BA_TLD_APP_MAX_SIZE);

   /* unified-portal
    * Managed BA domains will all end with <x>.zsproxy.net.
    * Look for the certificate id for a globally unique domain in a
    * specific cloud, for the ZS TLD customer.
    * TLD customer will have a domain <x>.zsproxy.net configured and a certificate
    * associated with it, which is the managed certificate for all apps
    *
    * Ideally we should check from right to left by adding a . and see if it matches the
    * top level domain
    */
    char *subdom = strstr(exporter_domain->domain, ".");

    if (subdom ) {
        subdom++; /* move past the '.' */
        len = strnlen(subdom,exporter_domain->domain_len-1);

        if ( len < app_len || (strncmp(subdom, tld_appln_name,len) != 0) ) {
            return 0;
        }

        /* The TLD customer's app name = the postfix of the tenant's managed BA app name.*/
        res = exporter_domain_search(&global_exporter, subdom, len, &tld_customer_domain);
    }


    /* Get the customer GID from the customer name */
    if (res || !tld_customer_domain) {
         return 0;
   }

    /* Today, there is just one certificate per app on the TLD customer. */
    EXPORTER_LOG(AL_INFO,"[UNIP_LOG_CERT] Managed cert id fetched from TLD app: %s, domain %s is  %"PRId64"",
                 tld_appln_name, exporter_domain->domain, tld_customer_domain->cert_id);

    return tld_customer_domain->cert_id;
}

/*
 * With Managed BA, n/w admins will configure an internal_app_domain and ZS will
 * assign an external app domain to be used by the end users, to access the app.
 * This function will accept the external app domain and find the admin configured
 * internal app domain. All policies, zpn_application tables, app-connectors etc
 * work with the internal app domain and not the ZS assigned external app domain.
 */
const char* exporter_domain_fetch_mapped_domain(const char *domain)
{
    struct exporter_domain *matched_domain = NULL;
    int res;
    size_t domain_len;

    if (!domain) {
        return NULL;
    }
    domain_len = strnlen(domain,EXPORTER_DOMAIN_NAME_MAX_SIZE);

    /* Lookup in the exporter's diamond datastructure. */
    res = exporter_domain_search(&global_exporter, domain, domain_len, &matched_domain);
    if (res || !matched_domain) {
       return (domain);
       /* Pass the incoming domain back. Error. */
    }

    return (matched_domain->cfg_domain);
}

/**
 *
 * Zscaler TLD customer will have static BA applications with certificates
 * uploaded by the OPs team. The application names will be unique to a cloud
 * and will have to read from the cloud config.
 */
const char * exporter_get_tld_customer_name(uint8_t is_user_portal, uint8_t is_ot)
{
    if (is_user_portal) {
        return EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP;
    } else if (is_ot) {
        return EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_PRA;
    } else {
        return EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_BA;
    }
}

void exporter_domain_generate_managed_app_fqdn (const char* prefix, char* domain,
                                               const char* label, const char* uniq_id,
                                               char** managed_domain_fqdn, int64_t cust_gid)
{
    if (!prefix || !domain || !label || !managed_domain_fqdn) {
        return;
    }
    *managed_domain_fqdn = NULL;

    /* Replace all . in the domain with - */
    size_t domain_len = strnlen(domain, EXPORTER_LABEL_NAME_MAX_SIZE);
    char *s = domain;
    char *e = s + domain_len;
    while (s < e) { if (*s == '.') *s = '-'; ++s; }

    size_t prefix_len = strnlen(prefix, EXPORTER_LABEL_NAME_MAX_SIZE);
    size_t label_len = strnlen(label, EXPORTER_LABEL_NAME_MAX_SIZE);
    size_t id_len = uniq_id ? strnlen(uniq_id, EXPORTER_EXT_ID_NAME_MAX_SIZE) : 0;
    size_t total_len = prefix_len + domain_len + label_len + id_len + 3; //an extra '.' and 2'-'s in the name

    if (total_len <= EXPORTER_DOMAIN_NAME_MAX_SIZE) {
        *managed_domain_fqdn = EXPORTER_CALLOC(total_len+ 1);
        if (*managed_domain_fqdn) {
            if (id_len) {
                snprintf(*managed_domain_fqdn, total_len + 1, "%s-%s-%s.%s", label, uniq_id, domain, prefix);
            } else {
                snprintf(*managed_domain_fqdn, total_len + 1, "%s-%s.%s", label, domain, prefix);
            }
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "[UNIP_LOG_CONFIG] Couldnot create a managed FQDN for the application for customer %"PRId64" with label %s[len:%ld]",
                               cust_gid, label, total_len);
    }

    EXPORTER_LOG(AL_INFO,"[UNIP_LOG_CONFIG] The generated managed FQDN for customer %"PRId64" from %s:%s:%s:%s is %s",
                          cust_gid, prefix,domain,label,uniq_id?uniq_id:"",*managed_domain_fqdn?*managed_domain_fqdn:"None");
}
