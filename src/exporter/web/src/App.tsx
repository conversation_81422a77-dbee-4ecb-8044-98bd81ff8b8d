/*
 * add App root level logic here: Routers, State Managers, etc
 */

import './common/scss/App.scss';

import Guacamole, { Mouse, WebSocketTunnel } from 'guacamole-common-js';
import zephyr from "zephyr";
import React, { SyntheticEvent, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getDisplayHeight, getDisplayWidth, getIdValueFromURL, toggleFullscreen } from './utils';

import AuthenticationModal from './components/modals/authenticationModal/AuthenticationModal';
import CredentialObject from './components/interfaces/CredentialObject';
import Display from './components/display/Display';
import ErrorBoundary from './components/error/ErrorBoundary';
import FileTransferModal from './components/modals/fileTransferModal/FileTransferModal';
import FileTransfer from './components/modals/fileTransfer/FileTransfer';
import FileTransferBody from './components/modals/fileTransfer/FileTransferBody';
import TransferBox from './components/modals/fileTransfer/TransferBox';
import FileTransferSourceContents from './components/modals/fileTransfer/FileTransferSourceContents';
import FileTransferDestinationContents from './components/modals/fileTransfer/FileTransferDestinationContents';
import FileTransferProgress from './components/modals/fileTransfer/FileTransferProgress';
import Header from './components/header/Header';
import Keyboard from './components/keyboard/Keyboard';
import SessionAvailabilityModal from './components/modals/sessionAvailabilityModal/SessionAvailabilityModal';
import UpdateCredentialObject from './components/interfaces/UpdateCredentialObject';
import Clipboard from './components/clipboard/Clipboard';
import ParticipantsListModal from './components/modals/participantsListModal/ParticipantsListModal';
import NotificationModal from './components/modals/notificationModal/NotificationModal';
import { debounce } from './utils/debounce';
import NavBarContainer from './components/collapsible-navbar';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle, faChevronDown, faChevronUp, faClipboard, faEllipsis, faFile, faKeyboard, faSpinner, faTerminal, faRecordVinyl, faUserGroup, faArrowUpRightAndArrowDownLeftFromCenter } from '@fortawesome/pro-regular-svg-icons';
import type { Action } from './components/collapsible-navbar/navbar';
import { defaultFavicon } from './Constants';
import FileTransferArrow from './components/modals/fileTransfer/FileTransferArrow';

enum ConnectionStatus {
    Idle = 0,
    Connecting,
    Waiting,
    Connected,
    Disconnecting,
    Disconnected
}

window.onbeforeunload = function (event) {
    event.preventDefault();
    event.returnValue = '';
};

function connectClient() {
    window.onbeforeunload = null;
    window.location.reload();
}

const App = () => {
    const [subHeaderText, setSubHeaderText] = useState<string>('');
    const [clientStatus, setClientStatus] = useState<string>('');
    const [showAlert, setShowAlert] = useState(null);
    const [showSpinner, setShowSpinner] = useState(false);
    const [displayText, setDisplayText] = useState<string>('');
    const [modalDisplayed, setModalDisplayed] = useState<boolean>(false);
    const [authenticationModalDisplayed, setAuthenticationModalDisplayed] = useState<boolean>(false);
    const [showFileTransferModal, setShowFileTransferModal] = useState<boolean>(false);
    const [consoleType, setConsoleType] = useState<string>('SSH');
    const [consoleName, setConsoleName] = useState<string>('');
    const [guac, setGuac] = useState<Guacamole.Client>(null);
    const [isFileTransferEnabled, setIsFileTransferEnabled] = useState<boolean>(false);
    const [isClipboardEnabled, setIsClipboardEnabled] = useState<boolean>(false);
    const [fileTransferCapabilites, setFileTransferCapabilites] = useState<any>(null);
    const [clipboardCapabilities, setClipboardCapibilities] = useState<any>(null);
    const [showClipboardArea, setShowClipboardArea] = useState<boolean>(false);
    const [sessionId, setSessionId] = useState<string>('');
    const [customerGid, setCustomerGid] = useState<number>(0);
    const [userId, setUserId] = useState<number>(0);
    const [inferKey, setInferKey] = useState<string>('');
    const numberOfRetries = useRef<number>(0);
    const [isRecording, setIsRecording] = useState<boolean>(false);
    const [isMonitoringMode, setIsMonitoringMode] = useState<boolean>(undefined);
    const [isSessionControlMode, setIsSessionControlMode] = useState<boolean>(undefined);
    const [showParticipants, setShowParticipants] = useState<boolean>(false);
    const [scopeId, setScopeId] = useState<string>("");
    const [controlTransfer, setControlTransfer] = useState<boolean>(false);
    const [hideKeyboardIcons, setHideKeyboardIcons] = useState<boolean>(false);
    const [notificationText, setNotificationText] = useState<string>('');
    const [showNotificationModal, setShowNotificationModal] = useState<boolean>(false);
    const [fasterFileTransferEnabled, setFasterFileTransferEnabled] = useState<boolean>(false);
    const [sendEmailNotifcation, setSendEmailNotifcation] = useState<boolean>(false);
    const [refreshToken, refresh] = useState<any>(Math.floor(Math.random() * 1000000));
    const fileSelectedRef = useRef<any>(null);
    const [fileTransferInProgress, setFileTransferInProgress] = useState(false);
    const fileTransferInProgressRef = useRef(false);
    const fileOutputStream = useRef<Guacamole.OutputStream>();
    const filesInTransferRef = useRef<Array<any>>([]);
    const curPathRef = useRef<string>('');
    const [privilegedFileSystemEnabled, setPrivilegedFileSystemEnabled] = useState<boolean>(false);
    const [showFileTransferProgress, setShowFileTransferProgress] = useState<boolean>(false);

    const guacRef = useRef<Guacamole.Client>(null);
    const capabilitiesRequestInProgress = useRef<boolean>(false);
    const isHostUserJoiningNotification = useRef<boolean>(true);
    const isMonitoringModeRef = useRef<boolean>(false);
    const controlTransferRef = useRef<boolean>(false);
    const [companyName, setCompanyName] = useState<string>('');
    const [logo, setLogo] = useState<string>('');
    const [favicon, setFavicon] = useState<string>('');
    let websocketTunnel = useRef<any>(null);
    const consoleTypeRef = useRef<string>('SSH');
    let guacKeyboardContainerEl: HTMLElement;
    guacRef.current = guac;

    const timeData = useRef<string>('');
    const credObjectRef = useRef({
        rdpUsername: '',
        domainname: '',
        rdppassword: '',
        sshUsername: '',
        sshPassword: '',
        sshPrivateKey: '',
        sshPassphrase: '',
        vncUsername: '',
        vncPassword: '',
    });
    const managedFileSystem = useRef<any>({});

    const zConsoleId: string = getIdValueFromURL('zconsole');
    const zScopeId: string = getIdValueFromURL('zscope');
    const keyboardLayoutString = new URLSearchParams(location.search).get('keyboardLayout');
    const displayResolutionString = new URLSearchParams(location.search).get('dr');
    const displayQualityString = new URLSearchParams(location.search).get('dq');
    const djbQueryString = new URLSearchParams(location.search).get('djb');
    const PIXELDENSITY: Readonly<number> = 1;
    const optimalDpi: number = PIXELDENSITY * 96;
    let connectRequestData = `zconsole=${zConsoleId}&GUAC_WIDTH=${Math.floor(getDisplayWidth())}&GUAC_HEIGHT=${Math.floor(getDisplayHeight())}&GUAC_DPI=${Math.floor(optimalDpi)}`;

    // GUACAMOLE MOUSE CONTROL LISTENERS
    let mouse: Mouse;
    let onMouseDown: null | ((state: Mouse.State) => void);
    let onMouseUp: null | ((state: Mouse.State) => void);
    let onMouseMove: null | ((state: Mouse.State) => void);

    useEffect(() => {
        // Hide scrollbar when resolution is Fit to Bounds
        const fullScreenClass = 'full-screen';
        if ((!displayResolutionString || parseInt(displayResolutionString) === 0) && !document.body.classList.contains(fullScreenClass)) {
            document.body.classList.add(fullScreenClass);
        }
    }, []);

    const updateMouseState = (mouseState: Mouse.State) => {
        let height = getDisplayHeight(),
            width = getDisplayWidth();
        const guacDisplay = guacRef.current.getDisplay();
        if (guacDisplay) {
            height = guacDisplay.getHeight();
            width = guacDisplay.getWidth();
        }
        mouseState.y =  Math.min(mouseState.y, height - 10);
        mouseState.x =  Math.min(mouseState.x, width - 10);
        guacRef.current.sendMouseState(mouseState);
    };

    const disconnectClient = () => {
        if(clientStatus === 'Disconnected') {
            window.onbeforeunload = null;
            window.location.reload();
        } else {
            guacRef.current.disconnect();
        }
    };

    const handleLoginButtonClick = () => {
        setAuthenticationModalDisplayed(false);
        initGuac();
    };

    const onGuacCallDisconnect = () => {
        guacRef.current.onstatechange(ConnectionStatus.Disconnected);
    };

    const getManagedFileSystem = function (client:Guacamole.Client, object:Guacamole.Object, name:string) {
        return {
            object,
            client,
            currentDir: {
                mimetype   : Guacamole.Object.STREAM_INDEX_MIMETYPE,
                streamName : Guacamole.Object.ROOT_STREAM,
                type       : 'DIRECTORY',
                parentDirPath  : ''
            }
        }
    };

    const handleResize = debounce(() =>{
        if (!parseInt(displayResolutionString) && !isMonitoringModeRef.current)
            guacRef.current.sendSize(Math.floor(getDisplayWidth()), Math.floor(getDisplayHeight()));
    }, 500);

    const addOnInitEventListeners = () => {
        document.addEventListener('callDisconnect', onGuacCallDisconnect, false);
        document.addEventListener('getAuthCredentials', function (e) {
            var credObject:Partial<CredentialObject> = {};
            if(consoleType === 'RDP') {
                credObject['username'] = credObjectRef.current.rdpUsername;
                credObject['password'] = credObjectRef.current.rdppassword;
                credObject['domainname'] = credObjectRef.current.domainname;
            } else if(consoleType === "SSH"){
                credObject['username'] = credObjectRef.current.sshUsername;
                credObject['password'] = credObjectRef.current.sshPassword;
                credObject['privatekey'] = credObjectRef.current.sshPrivateKey;
                credObject['passphrase'] = credObjectRef.current.sshPassphrase;
            } else if(consoleType === "VNC" || consoleType === "REALVNC") {
                credObject['username'] = credObjectRef.current.vncUsername;
                credObject['password'] = credObjectRef.current.vncPassword;
            }
            var wssMessageObject = btoa(JSON.stringify(credObject));
            websocketTunnel.current.sendMessage("credentials", wssMessageObject);
        }, false);
        document.addEventListener('sessionID',function(e:any) {
            if(e.detail) {
                setSessionId(e.detail[0]);
            }
        });
        document.addEventListener('keyMouseRefresh',function(e:any) {
            if(isMonitoringModeRef.current || isMonitoringModeRef.current === undefined){
                setNotificationText(`Keyboard and Mouse control ${controlTransferRef.current ? 'disabled' : 'enabled'}`);
                setShowNotificationModal(true);
            }
            controlTransferRef.current = !controlTransferRef.current;
            setControlTransfer((value) => !value);
        });
        document.addEventListener('userJoin',function(e:any) {
            if(isHostUserJoiningNotification.current) {
                isHostUserJoiningNotification.current = false;
            } else {
                setNotificationText(`${e.detail} has joined the session`);
                setShowNotificationModal(true);
            }
        });

        document.addEventListener('userDisconnect',function(e:any) {
            setNotificationText(`${e.detail} disconnected from the session`);
            setShowNotificationModal(true);
        });

        document.addEventListener('consoleInfoRecording',function(e:any) {
            const consoleInfoObj = JSON.parse(e.detail);
            setIsRecording(consoleInfoObj.recording === "true");
        });

        window.addEventListener("resize", handleResize);
    }

    const onNotificationModalClose = () => {
        setShowNotificationModal(false);
    }

    // GUACAMOLE CLIENT INSTANCE
    const setUpGuacConnection = () => {
        const host = new URL(window.location.href)?.host;
        const zScopeId: string = getIdValueFromURL('zscope');
        let socketTunnelUrl = '';
        if(zScopeId)
            socketTunnelUrl = `wss://${host}/v1/zscope/${zScopeId}/zconsole/websocket-tunnel`;
        else
            socketTunnelUrl = `wss://${host}/v1/zconsole/websocket-tunnel`;
        websocketTunnel.current = new Guacamole.WebSocketTunnel(socketTunnelUrl);
        let guac = new Guacamole.Client(
            websocketTunnel.current
        );
        guac.onerror = function(error) {
            // TODO: REFACTOR THIS CODE
            console.error("Error", JSON.stringify(error));
            if (error) {
                if(error.message && error.message.toLowerCase().indexOf('disconnected from server : system is overloaded') >= 0 && numberOfRetries.current < 4) {
                    numberOfRetries.current++;
                    setTimeout(() => {
                        setUpGuacConnection();
                    }, 500 * numberOfRetries.current);
                }
                else if (error.message && error.message.indexOf('BRK_MT_SETUP_FAIL_REPEATED_DISPATCH') !== -1 && confirm("Something went wrong. Do you want to try again?") == true) {
                    numberOfRetries.current = 0;
                    connectClient();
                } else if (error.message && error.message.indexOf('startTime') !== -1 && error.message.indexOf('endTime') !== -1) {
                    numberOfRetries.current = 0;
                    timeData.current = error.message;
                    setModalDisplayed(true);
                } else {
                    numberOfRetries.current = 0;
                    // Add custom message for invalid credentials
                    var message = (error.code === 769 || (consoleType === 'VNC' && error.code === 519) && error.message === "Aborted. See logs.") ? 'Incorrect credentials.' : error.message;
                    if (message) {
                        // ET-41609
                        message = message.replace('Aborted. See logs.', 'Aborted. Contact your administrator.');
                        message.indexOf('AST_MT_SETUP_ERR_RATE_LIMIT_REACHED') !== -1 && (message = 'Application request failed due to an exceeded rate limit. Please try again after two seconds.');
                        setShowAlert({
                            type: 'error',
                            message: message,
                        });
                    }
                }
            }
        };

        guac.onstatechange = function(state: ConnectionStatus) {
            var status = "";
            setShowSpinner(false);
            switch (state) {
                case 0:
                    status = "Idle";
                    break;
                case 1:
                    status = "Connecting";
                    break;
                case 2:
                    status = "Waiting";
                    break;
                case 3:
                    status = "Connected";
                    numberOfRetries.current = 0;
                    break;
                case 4:
                    //numberOfRetries is truthy only when retrying in progress
                    if(numberOfRetries.current)
                        status = "Retrying";
                    else
                        status = "Disconnecting";
                    break;
                case 5:
                    //numberOfRetries is truthy only when retrying in progress
                    if(numberOfRetries.current)
                        status = "Retrying";
                    else
                        status = "Disconnected";
                    break;
            }

            console.log("Status", status);
            if(status) {
                setClientStatus(status);
                if (status === 'Disconnected') {
                    // Close all modals that would have been opened.
                    // ET-44476 - Do not close error modal if status is disconnected.
                    //setModalDisplayed(false);
                    setAuthenticationModalDisplayed(false);
                    setShowFileTransferModal(false);
                    setShowClipboardArea(false);
                    setIsRecording(false);
                    setShowParticipants(false);
                    setShowFileTransferProgress(false);
                }
                else if(status === 'Connected') {
                    getCapabilitiesFromExporter();
                    // websocketTunnel.current.sendMessage("getCapabilities");
                } else if (status === 'Connecting' || status === 'Waiting') {
                    setShowSpinner(true);
                }
            }
        };

        // TODO: WRAP THIS IN TRY CATCH.
        guac.onfilesystem = function (object: Guacamole.Object, name: string) {
            managedFileSystem.current = getManagedFileSystem(guac, object, name);
            // TODO: REMOVE THIS
            // managedFileSystem.current = {};
        }

        if(!isMonitoringModeRef.current) {
            connectRequestData += `&keyboardLayout=${keyboardLayoutString || "en-us-qwerty"}`;
            connectRequestData += `&dr=${displayResolutionString || "0"}`;
            connectRequestData += `&dq=${displayQualityString || "0"}`;
            if(djbQueryString)
                connectRequestData += `&djb=1`;
        }

        guac.connect(connectRequestData);

        mouse = new Guacamole.Mouse(guac.getDisplay().getElement());

        // @ts-ignore: unreachable code
        onMouseDown = onMouseUp= (state) => guac.sendMouseState(state);
        mouse.onmousedown = onMouseDown;
        mouse.onmouseup = onMouseUp;

        // @ts-ignore: unreachable code
        onMouseMove = (state) => {
            updateMouseState(state);
        };
        mouse.onmousemove = onMouseMove;
        setGuac(guac);
    };

    const initGuac = () => {
        addOnInitEventListeners();
        setUpGuacConnection();
    }

    useEffect(() => {
        // Fetch Console Info
        const origin = new URL(window.location.href)?.origin;
        let url = '';
        if(zScopeId)
            url = `${origin}/v1/zscope/${zScopeId}/console_info?zconsole=${zConsoleId}`;
        else
            url = `${origin}/v1/console_info?zconsole=${zConsoleId}`;
        if(djbQueryString)
            url += '&djb=1';
        const getConsoleInfo = () => {
            const httpRequest = new XMLHttpRequest();
            httpRequest.overrideMimeType("application/json");
            httpRequest.open('GET', url, true);

            httpRequest.onload = function () {
                const jsonResponse = JSON.parse(httpRequest.responseText);
                if (httpRequest.status === 200) {
                    if (jsonResponse && jsonResponse.sra_console_name) {
                        document.title = jsonResponse.sra_console_name;
                        setSubHeaderText(jsonResponse.sra_console_name);
                        setConsoleType(jsonResponse.sra_console_type);
                        consoleTypeRef.current = jsonResponse.sra_console_type
                    setConsoleName(jsonResponse.sra_console_name);
                    setCustomerGid(jsonResponse.customer_gid);
                    setUserId(jsonResponse.user_id);
                    setInferKey(jsonResponse.infer_key);
                    setScopeId(jsonResponse.scope_id);
                    if(jsonResponse.interactive_auth_disabled) {
                        initGuac();
                    } else {
                        setAuthenticationModalDisplayed(true);
                    }
                }
                if (jsonResponse.fileTransferEnabled) {
                    setIsFileTransferEnabled(true);
                } else {
                    setIsFileTransferEnabled(false);
                }
                if(jsonResponse.clipboardEnabled) {
                    setIsClipboardEnabled(true);
                } else {
                    setIsClipboardEnabled(false);
                }
                if(jsonResponse.fasterFileTransferEnabled) {
                    setFasterFileTransferEnabled(true);
                } else {
                    setFasterFileTransferEnabled(false);
                }
                if(jsonResponse.privileged_file_system_enabled) {
                    setPrivilegedFileSystemEnabled(true);
                } else {
                    setPrivilegedFileSystemEnabled(false);
                }
                if(jsonResponse.emailNotificationEnabled) {
                    setSendEmailNotifcation(true);
                } else {
                    setSendEmailNotifcation(false);
                }
            } else if(httpRequest.status === 404 && jsonResponse?.exporterUserPortalApiErrorResponse?.reason === 'Console-Info-Response Error - ZPATH_RESULT_NOT_FOUND'){
                setShowAlert({
                    type: 'error',
                    message: 'Console not found'
                })
            } else {
                setShowAlert({
                    type: 'error',
                    message: jsonResponse?.exporterUserPortalApiErrorResponse?.reason
                })
            }
        };
        httpRequest.send(null);};

        fetch(`${origin}/v1/company`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Process the received data
                const companyResponse = data?.exporterUserPortalApiCompanyResponse;
                if (companyResponse) {
                    setCompanyName(companyResponse.name);
                    setLogo(companyResponse.logoImageData);
                    setFavicon(companyResponse.favIconImageData);
                }
                getConsoleInfo();
            })
            .catch(error => {
                console.log(error);
                getConsoleInfo();
            });
        document.addEventListener('capabilities', checkCapabilities);
        return () => {
            if(guac) {
                guac.disconnect();
                document.removeEventListener('callDisconnect', onGuacCallDisconnect);
                document.removeEventListener('capabilities', checkCapabilities);
            }
        }
    }, []);

    useEffect(() => {
        // Update favicon
        if (favicon) {
            let link = document.querySelector("link[rel~='icon']") as HTMLLinkElement;
            if (!link) {
                link = document.createElement('link');
                link.rel = 'icon';
                document.getElementsByTagName('head')[0].appendChild(link);
            }
            link.href = favicon;
        }
    }, [favicon]);

    const getActions = () => [
        {
            name:'recording',
            classes: 'recording',
            icon: <FontAwesomeIcon className={`far fa-1x ${isRecording ? 'recording-enabled-icon': 'recording-disabled-icon'}`} icon={faRecordVinyl} size="sm" />,
            tooltip: 'Recording',
            disabled: !(isRecording && clientStatus === 'Connected')
        },
        {
            name: 'users',
            classes: 'users',
            icon: <FontAwesomeIcon className="far fa-1x" icon={faUserGroup} size="sm" />,
            tooltip: 'Users',
            disabled: !(isSessionControlMode && clientStatus === 'Connected' && !isMonitoringMode)
        },
        {
            name: 'keyboard',
            classes: 'keyboard',
            icon: <FontAwesomeIcon className="far fa-1x" icon={faKeyboard} size="sm" />,
            tooltip: 'Keyboard',
            disabled: clientStatus !== 'Connected' || isMonitoringMode ? (hideKeyboardIcons || !controlTransfer) : hideKeyboardIcons
        },
        {
            name: 'fullscreen',
            classes: 'fullscreen',
            icon: <FontAwesomeIcon className="far fa-1x" icon={faArrowUpRightAndArrowDownLeftFromCenter} size="sm" />,
            tooltip: 'Fullscreen',
            disabled: clientStatus !== 'Connected'
        },
        {
            name: 'filetransfer',
            classes: 'filetransfer',
            icon: <FontAwesomeIcon className="far fa-1x" icon={faFile} size="sm" />,
            tooltip: 'File Transfer',
            disabled: isMonitoringMode || !isFileTransferEnabled || hideKeyboardIcons || clientStatus !== 'Connected'
        },
        {
            name: 'clipboard',
            classes: 'clipboard',
            icon: <FontAwesomeIcon className="far fa-1x" icon={faClipboard} size="sm" />,
            tooltip: 'Clipboard',
            disabled: isMonitoringMode || !isClipboardEnabled || hideKeyboardIcons || clientStatus !== 'Connected'
        }
    ];

    const updateCredObject = (obj:UpdateCredentialObject) => {
        credObjectRef.current.rdpUsername = obj.rdpUsername;
        credObjectRef.current.domainname = obj.domainName;
        credObjectRef.current.rdppassword = obj.rdpPassword;
        credObjectRef.current.sshUsername = obj.SSHUsername;
        credObjectRef.current.sshPassword = obj.SSHPassword;
        credObjectRef.current.sshPrivateKey = obj.SSHPrivateKey;
        credObjectRef.current.sshPassphrase = obj.SSHPassphrase;
        credObjectRef.current.vncUsername = obj.vncUsername;
        credObjectRef.current.vncPassword = obj.vncPassword;
    }

    const checkCapabilities = (e: any) => {
        if (e.detail) {
            capabilitiesRequestInProgress.current = false;
            const capabilitiesDetail = JSON.parse(e.detail);
            setFileTransferCapabilites(capabilitiesDetail);
            setClipboardCapibilities(capabilitiesDetail);
            if(capabilitiesDetail && capabilitiesDetail.isMonitoringMode === 'true') {
                isMonitoringModeRef.current = true;
                setIsMonitoringMode(true);
            } else {
                isMonitoringModeRef.current = false;
                setIsMonitoringMode(false);
            }
            if(!isMonitoringModeRef.current) {
                if(capabilitiesDetail && capabilitiesDetail.recording === 'true') {
                    setIsRecording(true);
                } else {
                    setIsRecording(false);
                }
            }
            if(capabilitiesDetail && capabilitiesDetail.isSessionControlBitEnabled === 'true') {
                setIsSessionControlMode(true);
            } else {
                setIsSessionControlMode(false);
            }
        }
    };

    const onFileTransferButtonClick = () => {
        // getCapabilitiesFromExporter();
        setShowFileTransferModal(true);
    }

    const onFileTransferModalClose = () => {
        setShowFileTransferModal(false);
    }

    const onClipboardButtonClick = () => {
        // getCapabilitiesFromExporter();
        setShowClipboardArea(true);
    }

    const onKeyBoardClick = (hideKeyboard: boolean) => {
        if (!guacKeyboardContainerEl) guacKeyboardContainerEl = document.querySelector(".keyboard-container");
        const keyboardEl = guacKeyboardContainerEl.querySelector('.guac-keyboard');
        if (hideKeyboard) {
            guacKeyboardContainerEl.classList.add('d-none');
            keyboardEl.classList.add('d-none');
        } else {
            const isOnScreenKeyboardContainerHidden = guacKeyboardContainerEl.classList.contains('d-none');
            guacKeyboardContainerEl.classList.toggle('d-none', !isOnScreenKeyboardContainerHidden);
            keyboardEl.classList.toggle('d-none', !isOnScreenKeyboardContainerHidden);
        }
    };

    const hideClipboardArea = () => {
        setClipboardCapibilities(null);
        setShowClipboardArea(false);
    }

    const getCapabilitiesFromExporter = () => {
        capabilitiesRequestInProgress.current = true;
        websocketTunnel.current.sendMessage("getCapabilities");
        setTimeout(() => {
            if(capabilitiesRequestInProgress.current)
                websocketTunnel.current.sendMessage("getCapabilities");
        },1000);
    }

    const onParticipantsClick = () => {
        setShowParticipants(true);
    }

    const onParticipantsCloseClick = () => {
        setShowParticipants(false);
    }

    const updateFilesFn = (progressStatus: string, percentage: number, errorMessage?: string) => {
        const id = filesInTransferRef.current.findIndex(f => f.progressStatus === "queue" || f.progressStatus === "progress");
        if(filesInTransferRef.current[id]) {
            filesInTransferRef.current[id].progressStatus = progressStatus;
            filesInTransferRef.current[id].percentage = percentage;
            filesInTransferRef.current[id].errorMessage = errorMessage;
        }
        refresh(Math.floor(Math.random() * 1000000));
    }
    const transferFiles = () => {
        if(!fileSelectedRef.current) {
            alert("Please select a file to transfer!");
            return;
        }
        if(fileTransferCapabilites.inspect_upload === "false" && fileTransferCapabilites.upload === "false")
            return;
        setShowFileTransferModal(false);
        let destinationFile = '';
        if(!curPathRef.current) {
            destinationFile = '/';
        } else if(curPathRef.current === '/') {
            destinationFile = '/';
        } else {
            destinationFile = `${curPathRef.current}/`;
        }
        const fileInfo = fileSelectedRef.current;
        setFileTransferInProgress(true);
        fileTransferInProgressRef.current = true;
        setShowFileTransferProgress(true);
        filesInTransferRef.current.push({...fileSelectedRef.current, progressStatus: 'queue', percentage: 0});
        const outputStream:any = guac.createOutputStream;
        fileOutputStream.current = outputStream(fileInfo.fileType || 0, fileInfo.fileName);
        let totalSize:number;
        let writtenSize:number;

        let lastUpdateTimestamp = 0; // Initialize last update timestamp
        const minUpdateInterval = 2000;

        fileOutputStream.current.onack = function ackReceived(status) {
            if(status.code === 0) {
                if(status.message.indexOf('TOTAL_SIZE') >= 0) {
                    totalSize = parseInt(status.message.split(":")[2]);
                }
                if(status.message.indexOf('WRITTEN') >= 0) {
                    writtenSize = parseInt(status.message.split(":")[2]);
                }
                const currentTimestamp = Date.now();
                const timeSinceLastUpdate = currentTimestamp - lastUpdateTimestamp;
                if(!isNaN(totalSize) && !isNaN(writtenSize) && totalSize === writtenSize) {
                    updateFilesFn('complete', 100);
                    fileOutputStream.current.sendEnd();
                    fileSelectedRef.current = null;
                    setFileTransferInProgress(false);
                    fileTransferInProgressRef.current = false;
                } else if(!isNaN(totalSize) && !isNaN(writtenSize)) {
                    if (timeSinceLastUpdate >= minUpdateInterval) {
                        updateFilesFn('progress', Math.min(Math.round((writtenSize / totalSize) * 99), 99));
                        lastUpdateTimestamp = currentTimestamp;
                    }
                }
            } else {
                updateFilesFn('cancel', 0, status.message);
                fileSelectedRef.current = null;
                setFileTransferInProgress(false);
            }
        }
        websocketTunnel.current.sendMessage("put_mf", 0, fileOutputStream.current.index, fileInfo.fileType || 0, `${destinationFile}${fileInfo.fileName}`, fileInfo.id, `${fileInfo.id}_${fileInfo.fileName}`);
    }

    const closeFileTransferProgress = () => {
        filesInTransferRef.current= [];
        setShowFileTransferProgress(false);
    };

    const onIndividualProgressCancelled = (progressItem: any) => {
        if(progressItem.progressStatus === 'progress'|| progressItem.progressStatus === 'queue') {
            websocketTunnel.current.sendMessage("cancel", 0, fileOutputStream.current.index);
            updateFilesFn('cancel', progressItem.percentage);
            fileSelectedRef.current = null;
            setFileTransferInProgress(false);
        } else {
            filesInTransferRef.current = filesInTransferRef.current.filter(f => parseInt(f.id) !== parseInt(progressItem.id))
        }
        refresh(Math.floor(Math.random() * 1000000));
    };

    const sourceBodyContent = () => {
        return (
            <div>
                <header className="source-files-header ">
                    {`My Files`}
                </header>
                <div className="list-container">
                    <FileTransferSourceContents
                        fileTransferCapabilites={fileTransferCapabilites}
                        customerGid={customerGid}
                        userId={userId}
                        key={`files-source-${refreshToken}`}
                        fileSelected={fileSelectedRef.current}
                        onFileItemSelect={(item:any) => {
                            if(!fileTransferInProgress)
                                fileSelectedRef.current = item;
                        }}
                        />
                </div>
            </div>
        );
    };

    const destinationBodyContent = () => {
        return (
            <div>
                <header>
                    {""}
                </header>
                <div>
                    <FileTransferDestinationContents
                        fileSystem={managedFileSystem.current}
                        fileTransferCapabilites={fileTransferCapabilites}
                        subHeaderText={subHeaderText}
                        outputStream={guac.createOutputStream}
                        websocketTunnel={websocketTunnel.current}
                        consoleType={consoleType}
                        curPathRef={curPathRef}
                    />
                </div>
            </div>
        );
    };

    const logox = (
        <div className='brand-logo'>
          <img src={ favicon || defaultFavicon } alt="logo"/>
        </div>
    );

    const renderNavbarHandle= (isOpen:boolean) => {
        if (isOpen) {
         return <FontAwesomeIcon className="far fa-1x" icon={faChevronUp} size="sm" />
        }
        return <FontAwesomeIcon className="far fa-1x" icon={faChevronDown} size="sm" />
     };


     const onNavActionClick= (e:SyntheticEvent, action:Action) => {
        console.log(action);
        //TODO - Switch on the action and handle
        switch(action.name) {
            case "clipboard":
                onClipboardButtonClick();
                break;
            case "filetransfer":
                onFileTransferButtonClick();
                break;
            case "fullscreen":
                toggleFullscreen();
                break;
            case "keyboard":
                onKeyBoardClick(false);
                break;
            case "users":
                onParticipantsClick();
                break;
            case "end-session":
                disconnectClient();
                break;
            default :
                console.log("Default Action");
                break;
        }
     };

    const getWorkStationIcon = () => {
        if (!clientStatus) {
            return <></>;
        }
        if (consoleTypeRef.current === 'SSH') {
            return <FontAwesomeIcon className="far fa-1x pr-5" icon={faTerminal} size="sm"/>
        } else {
            return <div className={`${consoleTypeRef.current}-icon`}></div>;
        }
    };

    return (
        <ErrorBoundary>
            <div>
                { showSpinner &&
                    <div className="spinner-icon">
                        <FontAwesomeIcon className="far fa-3x fa-spin" icon={faSpinner} />
                    </div>
                }
                {showAlert && <zephyr.Alert alert={showAlert} />}
                <NavBarContainer
                    actions={getActions()}
                    logo={logox}
                    workStationName={subHeaderText}
                    workStationIcon={getWorkStationIcon()}
                    defaultOpen={true}
                    renderNavbarHandle={renderNavbarHandle}
                    onNavActionClick={onNavActionClick}
                    moreOptionContent={
                        <FontAwesomeIcon className="far fa-1x" icon={faEllipsis} size="sm" />
                    }
                    endSessionContent={
                        <span>{clientStatus === 'Disconnected' ? 'Join' : clientStatus === 'Connected' ? 'End' : 'Connecting'}</span>
                    }
                    triggerSessionTimer={clientStatus === 'Connected'}
                    endSessionCustomClass={clientStatus === 'Disconnected' ? 'join-session' : clientStatus === 'Connected' ? 'end-session' : 'waiting-session'}
                />
                {guac ? <Display
                    guacDisplay={guac.getDisplay}
                    status={clientStatus}
                    consoleType={consoleType}
                    displayText={displayText}
                /> : null}
                {guac ? (
                    <Keyboard sendKeyEvent={guac.sendKeyEvent} fileTransferInProgress={fileTransferInProgress} onKeyBoardClick={onKeyBoardClick} status={clientStatus} isMonitoringMode={isMonitoringMode} isInviteModalShown={showParticipants} controlTransfer={controlTransfer} hideKeyboardIcons={hideKeyboardIcons} consoleType={consoleType} />
                ) : null}
                {modalDisplayed ? (
                    <SessionAvailabilityModal
                        showModal={modalDisplayed}
                        onModalClose={() => setModalDisplayed(false)}
                        timeData={timeData.current}
                    />
                ) : null}
                {authenticationModalDisplayed ? (
                <AuthenticationModal
                    showModal={authenticationModalDisplayed}
                    onModalClose={() => setAuthenticationModalDisplayed(false)}
                    onPrimaryButtonClick={handleLoginButtonClick}
                    consoleType={consoleType}
                    consoleName={consoleName}
                    updateCredObject={updateCredObject}
                />
                ) : null}
                {guac && websocketTunnel.current && showFileTransferModal ? (
                    privilegedFileSystemEnabled ? <FileTransfer
                        id="FileTransferModal"
                        size='lg'
                        width="1220px"
                        minHeight="600px"
                        showFileTransferModal={showFileTransferModal}
                        fileSystem={managedFileSystem.current}
                        fileTransferCapabilites={fileTransferCapabilites}
                        headerOptions={{ render: "File Transfer" }}
                        bodyOptions={{ render: () => {
                            return (
                                <FileTransferBody customClass='file-transfer-container'>
                                    <TransferBox
                                        height='100%'
                                        width='45.25%'
                                        customClass="source-files-container"
                                        bodyContent={sourceBodyContent}
                                    />
                                    <FileTransferArrow fileTransferInProgress={fileTransferInProgress} fileTransferCapabilites={fileTransferCapabilites} transferFiles={transferFiles}/>
                                    <TransferBox
                                        height='100%'
                                        width='45.25%'
                                        customClass="dest-files-container"
                                        bodyContent={destinationBodyContent}
                                    />
                                </FileTransferBody>
                            );
                        }}}
                        footerOptions={{ render: () => (<div className={`${consoleType !== 'RDP' ? 'd-none' : 'file-transfer-footer'}`}><FontAwesomeIcon onClick={onParticipantsClick} className="far fa-1x pr-5" icon={faInfoCircle} size="sm" color='#2160E1'/><p className='m-0'>Imported files are stored in the mapped Z drive. They are automatically deleted from this drive at the end of the session.</p></div>) }}
                        onClose={() => {
                            if(!fileTransferInProgress)
                                fileSelectedRef.current = null;
                            setShowFileTransferModal(false);
                        }}
                    /> :
                    <FileTransferModal
                        showModal={showFileTransferModal}
                        fileSystem={managedFileSystem.current}
                        onModalClose={onFileTransferModalClose}
                        subHeaderText={subHeaderText}
                        outputStream={guac.createOutputStream}
                        websocketTunnel={websocketTunnel.current}
                        consoleType={consoleType}
                        fileTransferCapabilites={fileTransferCapabilites}
                        fasterFileTransferEnabled={fasterFileTransferEnabled}
                    />
                ) : null}
                {guac && websocketTunnel.current ? (
                    <Clipboard
                        isMonitoringMode={isMonitoringMode}
                        hideWindowFocusListener={showFileTransferModal}
                        showClipboardArea={showClipboardArea}
                        hideClipboardArea={hideClipboardArea}
                        guac={guac}
                        clipboardCapabilities={clipboardCapabilities}
                    />
                ) : null}
                {showParticipants ? (
                    <ParticipantsListModal
                        showModal={showParticipants}
                        onModalClose={onParticipantsCloseClick}
                        customerGid={customerGid}
                        inferKey={inferKey}
                        sessionId={sessionId}
                        scopeId={scopeId}
                        setHideKeyboardIcons={setHideKeyboardIcons}
                        setNotificationText={setNotificationText}
                        setShowNotificationModal={setShowNotificationModal}
                        sendEmailNotifcation={sendEmailNotifcation}
                        websocketTunnel={websocketTunnel.current}
                        fileTransferInProgress={fileTransferInProgress}
                    />
                ) : null}
                {showNotificationModal && <NotificationModal showModal={showNotificationModal} onModalClose={onNotificationModalClose} notificationText={notificationText} />}
                {showFileTransferProgress && filesInTransferRef.current.length > 0 ? (<FileTransferProgress key={`file-transfer-progress-${refreshToken}`} fileTransferInProgress={fileTransferInProgress} filesInTransfer={filesInTransferRef.current} closeFileTransferProgress={closeFileTransferProgress} onIndividualProgressCancelled={onIndividualProgressCancelled}/>) : null}
            </div>
        </ErrorBoundary>
    );
}

export default App;
