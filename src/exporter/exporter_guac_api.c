/*
 * exporter_guac_api.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 *
 * After a user clicks an application that will be reached via Guacamole,
 * this module handles requests from the Guacamole JavaScript client.
 *
 */
#include "zcdns/zcdns_libevent.h"

#include <math.h>
#include <string.h>
#include "zpath_lib/zpath_customer.h"
#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include "zpath_lib/zpath_config_override.h"
#include "zpn/zpn_broker_policy.h"
#include "parson/parson.h"
#include "base64/base64.h"
#include <openssl/evp.h>
#include "zcrypt/zcrypt.h"
#include "zpath_lib/zpath_cloud.h"
#include "zlibevent/zlibevent_bufferevent.h"

#include "exporter/exporter.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guac_parser.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_guac_api_zia.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guacd.h"
#include "exporter/exporter_assert.h"

#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "exporter/zpn_sra_console.h"
#include "exporter/zpn_sra_application.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_servergroup_server_relation.h"
#include "zpn/zpn_application_server.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/exporter_recording_multipart.h"
#include "exporter/exporter_recording_pool.h"
#include "exporter/exporter_recording.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_guac_session_control_host.h"
#include "zpn/zpn_jit_approval_policy.h"
#include "exporter/exporter_guac_desktops.h"

// Online documentation says that string.h declares strlcpy and strcasecmp. On MacOS
// it does (strlcpy is a macro). But Jenkins BSD builds fail. We added the next two
// declarations as a workaround.
#ifndef strlcpy
size_t strlcpy(char *dst, const char *src, size_t size);
#endif
char *strcasestr(const char *haystack, const char *needle);
extern int exporter_guac_mconn_terminate(struct zpn_exporter_mtunnel *mt);

/* Guac API request handler init flag */
static int s_guac_api_init_done = 0;
int g_exporter_guacd_tunnel_forward = 0;
int g_exporter_is_guacd_managed_from_exporter = 0;
int g_temp_port = EXPORTER_GDPROXY_LISTENER_MINPORT_NUMBER;
extern char *exporter_guac_rdprd_path;
extern struct recording_global_state recording_pool_g;

#define GUAC_API_RESPONSE_BUF_SIZE (10 * 1024)
#define GUAC_ERROR_REASON_SIZE     (100)

/*
 * Guacamole API response definitions
 * Note:
 * The JavaScript client expects JSON structures and their fields to use CamelCase notation.
 * CamelCase notation is not our C coding style for structure names. We use C structure typedefs to insulate
 * itasca code from CamelCase, but these real structures follow the JavaScript client's convention,
 * for easy JSON marshalling
 */

// Matches Error.js in the Guacamole JavaScript client

struct guacApiErrorResponse {                       /* _ARGO: object_definition */
    char *message;                                  /* _ARGO: string */
    char *translatableMessage;                      /* _ARGO: string */
    // A Guacamole status code, meaningful only if `type` is "STREAM_ERROR"
    uint64_t statusCode;                            /* _ARGO: integer */
    char *type;                                     /* _ARGO: string */
    // TODO: Provide this field, with a value of null. ET-32552
    // struct argo_list *expected;                     /* > ARGO: list */
};
typedef struct guacApiErrorResponse guac_api_error_response_t;

// Values for *type in an error response
const char* GUAC_ERROR_TYPE_BAD_REQUEST = "BAD_REQUEST";
const char* GUAC_ERROR_TYPE_INVALID_CREDENTIALS = "INVALID_CREDENTIALS";
const char* GUAC_ERROR_TYPE_INSUFFICIENT_CREDENTIALS = "INSUFFICIENT_CREDENTIALS";
const char* GUAC_ERROR_TYPE_INTERNAL_ERROR = "INTERNAL_ERROR";
const char* GUAC_ERROR_TYPE_NOT_FOUND = "NOT_FOUND";
const char* GUAC_ERROR_TYPE_PERMISSION_DENIED = "PERMISSION_DENIED";
const char* GUAC_ERROR_TYPE_STREAM_ERROR = "STREAM_ERROR";

// Values for Guacamole instructions
const char* GUAC_SELECT_SSH_INSTR = "6.select,3.ssh;";
const char* GUAC_SELECT_RDP_INSTR = "6.select,3.rdp;";
const char* GUAC_SELECT_VNC_INSTR = "6.select,3.vnc;";
const char* GUAC_SELECT_REALVNC_INSTR = "6.select,7.realvnc;";
const char* GUAC_PROTOCOL_SELECT_INSTR = NULL;

//Value for VNC protocol
const char* VNC_PROTOCOL = "VNC";
const char* REALVNC_PROTOCOL = "REALVNC";

/* Guacamole API stats */
// TODO: Include these stats with other stats
struct exporter_guac_api_stats {                    /* _ARGO: object_definition */
    uint64_t tunnel_connect_success;                /* _ARGO: integer */
    uint64_t tunnel_connect_failed;                 /* _ARGO: integer */
    uint64_t tunnel_disconnect_received;            /* _ARGO: integer */
    uint64_t tunnel_write_success;                  /* _ARGO: integer */
    uint64_t tunnel_write_failed;                   /* _ARGO: integer */
    uint64_t tunnel_read_success;                   /* _ARGO: integer */
    uint64_t tunnel_read_failed;                    /* _ARGO: integer */

    uint64_t init_success;                          /* _ARGO: integer */
    uint64_t init_failed;                           /* _ARGO: integer */

    uint64_t total_async_requested;                 /* _ARGO: integer */
    uint64_t total_async_completed;                 /* _ARGO: integer */

    uint64_t websocket_request_destroy;             /* _ARGO: integer */
    uint64_t websocket_parser_error;                /* _ARGO: integer */
    uint64_t websocket_reauth_sent;                 /* _ARGO: integer */
    uint64_t guac_response_error;                   /* _ARGO: integer */

    uint64_t console_get_by_id_success;             /* _ARGO: integer */
    uint64_t console_get_by_id_failed;              /* _ARGO: integer */
    uint64_t console_not_enabled;                   /* _ARGO: integer */
    uint64_t console_misconfigured;                 /* _ARGO: integer */

    uint64_t application_get_by_id_success;         /* _ARGO: integer */
    uint64_t application_get_by_id_failed;          /* _ARGO: integer */
    uint64_t application_not_enabled;               /* _ARGO: integer */
    uint64_t application_misconfigured;             /* _ARGO: integer */

    uint64_t file_transfer_upload;                  /* _ARGO: integer */
    uint64_t file_transfer_download;                /* _ARGO: integer */
    uint64_t file_transfer_upload_failure;          /* _ARGO: integer */
    uint64_t file_transfer_download_failure;        /* _ARGO: integer */
    uint64_t file_transfer_upload_inspect;          /* _ARGO: integer */
    uint64_t file_transfer_upload_inspect_deny;     /* _ARGO: integer */
    uint64_t file_transfer_upload_inspect_fail;     /* _ARGO: integer */
    uint64_t file_transfer_cryptoservice_fail;      /* _ARGO: integer */
    uint64_t file_transfer_upload_policy_deny;      /* _ARGO: integer */
    uint64_t file_transfer_download_policy_deny;    /* _ARGO: integer */

    uint64_t clipboard_copy;                        /* _ARGO: integer */
    uint64_t clipboard_paste;                       /* _ARGO: integer */
    uint64_t clipboard_copy_exceed_limit;           /* _ARGO: integer */
    uint64_t clipboard_paste_exceed_limit;          /* _ARGO: integer */
    uint64_t clipboard_copy_policy_deny;            /* _ARGO: integer */
    uint64_t clipboard_paste_policy_deny;           /* _ARGO: integer */

    uint64_t privileged_credentials_policy_accept;  /* _ARGO: integer */
    uint64_t privileged_credentials_policy_reject;  /* _ARGO: integer */

    uint64_t guacd_service_enabled;                                /* _ARGO: integer */
    uint64_t guacd_service_handshake_failed;                       /* _ARGO: integer */
    uint64_t guacd_service_gd_proxy_listener_create_failed;        /* _ARGO: integer */
    uint64_t guacd_service_connect_failed;                         /* _ARGO: integer */
    uint64_t guacd_service_mt_consume_on_request_thread_failed;    /* _ARGO: integer */
    uint64_t guacd_service_child_proxy_connection_failed;          /* _ARGO: integer */
    uint64_t guacd_service_stiching_connection_failed;             /* _ARGO: integer */
    uint64_t guacd_service_child_proxy_server_create_failed;       /* _ARGO: integer */
    uint64_t guacd_service_child_proxy_server_listen_failed;       /* _ARGO: integer */
    uint64_t guacd_service_child_proxy_server_accept_failed;       /* _ARGO: integer */
    uint64_t guacd_service_active_session_count;                   /* _ARGO: integer */
    uint64_t guacd_service_mtunnel_drops_cpu_limit_reached;        /* _ARGO: integer */
    uint64_t guacd_service_mtunnel_drops_mem_limit_reached;        /* _ARGO: integer */
    uint64_t guacd_service_mtunnel_drops_max_sessions_reached;     /* _ARGO: integer */
    uint64_t guacd_parser_error_500;                               /* _ARGO: integer */
    uint64_t guacd_service_total_rdp_session_count;                /* _ARGO: integer */
    uint64_t guacd_service_total_vnc_session_count;                /* _ARGO: integer */
    uint64_t guacd_service_total_realvnc_session_count;            /* _ARGO: integer */
    uint64_t guacd_service_total_ssh_session_count;                /* _ARGO: integer */
    uint64_t large_portal_access_active_count;                     /* _ARGO: integer */
    uint64_t large_portal_access_blocked_count;                    /* _ARGO: integer */

    uint64_t credential_pool_conn_count;                           /* _ARGO: integer */
    uint64_t credential_pool_conn_failed;                          /* _ARGO: integer */
    uint64_t credential_pool_delete_api_failed;                    /* _ARGO: integer */
    uint64_t credential_pool_internal_callback_failure;            /* _ARGO: integer */
    uint64_t credential_pool_cred_id_found;                        /* _ARGO: integer */
    uint64_t credential_pool_err_no_cred_available;                /* _ARGO: integer */
    uint64_t credential_pool_err_service_unavailable;              /* _ARGO: integer */
    uint64_t credential_pool_err_cred_not_found_in_db;             /* _ARGO: integer */
    uint64_t credential_pool_request_status_check_failure;         /* _ARGO: integer */
    uint64_t credential_pool_err_json_response;                    /* _ARGO: integer */
    uint64_t credential_thread_cps_conn_failed;                    /* _ARGO: integer */
    uint64_t credential_thread_null_data;                          /* _ARGO: integer */
    uint64_t credential_thread_cps_request_error;                  /* _ARGO: integer */
    uint64_t credential_thread_cps_request_retry_count;            /* _ARGO: integer */
    uint64_t credential_thread_cps_task_enq_fail;                  /* _ARGO: integer */
    uint64_t credential_thread_cps_request_maxretry_fail;          /* _ARGO: integer */
    uint64_t credential_thread_cps_del_all_retry_count;            /* _ARGO: integer */
    uint64_t credential_thread_cps_del_retry_count;                /* _ARGO: integer */
    uint64_t credential_thread_cps_get_retry_count;                /* _ARGO: integer */
    uint64_t guacd_service_active_rdp_session_count;               /* _ARGO: integer */
    uint64_t guacd_service_active_vnc_session_count;               /* _ARGO: integer */
    uint64_t guacd_service_active_realvnc_session_count;           /* _ARGO: integer */
    uint64_t guacd_service_active_ssh_session_count;               /* _ARGO: integer */
    uint64_t pra_health_check_503_count;                           /* _ARGO: integer */
    uint64_t djb_launch_count;                                     /* _ARGO: integer */
    uint64_t djb_validation_failure_count;                         /* _ARGO: integer */
    uint64_t djb_delete_count;                                     /* _ARGO: integer */
    uint64_t djb_connect_count;                                    /* _ARGO: integer */
    uint64_t djb_portal_policy_fail_count;                         /* _ARGO: integer */
    uint64_t djb_service_resp_fail_count;                          /* _ARGO: integer */
};

const char * keyboard_layout_names[] = {
        "pt-br-qwerty",
        "en-us-qwerty",
        "en-gb-qwerty",
        "fr-fr-azerty",
        "fr-be-azerty",
        "fr-ch-qwertz",
        "de-de-qwertz",
        "de-ch-qwertz",
        "hu-hu-qwertz",
        "it-it-qwerty",
        "ja-jp-qwerty",
        "no-no-qwerty",
        "es-es-qwerty",
        "es-latam-qwerty",
        "sv-se-qwerty",
        "tr-tr-qwerty"
};

/*-------------------------------------------------*/


#include "exporter/exporter_guac_api_compiled_c.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_zpa.h"

/* Structure descriptions */
struct argo_structure_description *exporter_guac_api_stats_description                 = NULL;
struct argo_structure_description *guac_api_error_resp_description                     = NULL;

/* API stats counters instance */
static struct exporter_guac_api_stats s_guac_api_stats = {0};

/* Send data API */
static int exporter_guac_api_send_data(struct exporter_request *request, const void *data, size_t datlen);

static int exporter_conn_guac_parse_json_rdp_args(const char *authentication,
                                                  char **username,
                                                  char **password,
                                                  char **domain);
static int exporter_conn_guac_parse_json_ssh_args(const char *authentication,
                                                  char **username,
                                                  char **password,
                                                  char **pvt_key,
                                                  char **passphrase);
static int exporter_conn_guac_parse_json_vnc_args(const char *authentication,
                                                  char **username,
                                                  char **password);

struct zcdns *exporter_zcdns;

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    ZPATH_LOG(priority, "%s", dump);
}

/* Simple macros to increment success/failure counters */
#define EXPORTER_GUAC_API_STATS_INC(field, increment) __sync_add_and_fetch_8(&s_guac_api_stats.field, increment)
#define EXPORTER_GUAC_API_STATS_DEC(field, decrement) __sync_sub_and_fetch_8(&s_guac_api_stats.field, decrement)

/* Increment correct sucess/failed counters for given counter prefix */
#define EXPORTER_GUAC_API_STATS_INC_BY_RET_CODE(field_prefix, increment, retcode)                                     \
                                                (retcode == ZPATH_RESULT_NO_ERROR) ?                                    \
                                                 EXPORTER_GUAC_API_STATS_INC(field_prefix ## _success, increment) :   \
                                                 EXPORTER_GUAC_API_STATS_INC(field_prefix ## _failed, increment)

/* Increment total async requested */
#define EXPORTER_GUAC_API_STATS_INC_ASYNC_REQUESTED   EXPORTER_GUAC_API_STATS_INC(total_async_requested, 1)

/* Increment total async completed */
#define EXPORTER_GUAC_API_STATS_INC_ASYNC_COMPLETED   EXPORTER_GUAC_API_STATS_INC(total_async_completed, 1)

/* ------------------------- */
/* Local function prototypes */
/* ------------------------- */

void exporter_guac_api_free_response(const struct exporter_request *request, void *resp);


/* ------------------------- */


/* ------------------------- */
/* Functions                 */
/* ------------------------- */

/*
 * Map guac api type to descriptive name
 */
static const char*
get_guac_api_name(enum zpa_guac_api_type api_type)
{
    static const char *api_type_to_name[] = {
        "[Guac API Invalid]",
        "[Guac API Websocket Tunnel]",
    };

    if ((api_type > ZPA_GUAC_API_TYPE_INVALID) && (api_type < ZPA_GUAC_API_TYPE_LAST)) {
        return api_type_to_name[api_type];
    }

    return api_type_to_name[ZPA_GUAC_API_TYPE_INVALID];
}

/*
 * Get descriptive name of a request's API type
 */
static const char*
get_request_api_name(const struct exporter_request *request)
{
    if (request->conn->exporter_domain->is_ot) {
        return get_guac_api_name(request->guac_api_type);
    } else {
        return get_guac_api_name(ZPA_GUAC_API_TYPE_INVALID);
    }
}

/*
 * Get guac API type from path - If there is no matching API, it's an error
 */
enum zpa_guac_api_type exporter_get_guac_api_type(struct exporter_request *request)
{
    const char *uri_path = &(request->url[request->url_parser.field_data[UF_PATH].off]);
    enum zpa_guac_api_type api_type = ZPA_GUAC_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /*
     * PRA requests will have /zscope in the URL for Scope Folders in PRA Portal.
     * Format can be either of the 2:
     * https://domain_uri>/v<version>/zscope/<zscope_id>/zconsole/websocket-tunnel/<api_type_path>/...
     * https://domain_uri>/v<version>/zconsole/websocket-tunnel<api_type_path>/...
     */
    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        /*
         * If the request is invalid, we will still treat it as index.html request.
         */
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Invalid Portal API version: %d", request->name, api_version);
    }

    /* Check for /zscope */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    /*
     * Note: Must specify most specific match before less specific match
     */
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_GUAC_API_URI_WEBSOCKET_TUNNEL, strlen(EXPORTER_GUAC_API_URI_WEBSOCKET_TUNNEL))) {
        api_type = ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL;
    } else {
        return ZPA_GUAC_API_TYPE_INVALID;
    }

    EXPORTER_DEBUG_GUAC_API("%s: Guac uri path: %s, api_type: %s", request->name, uri_path, get_guac_api_name(api_type));

    return api_type;
}

/*
 * Validate and classify guac api call
 * - Initilize guac_info if needed
 * - Set the underlying API operation type
 * - Check the domain/sni match
 * - Check origin/sni match, if origin found
 */
static int exporter_guac_validate_classify_request(struct exporter_request *request)
{
    struct http_parser_url url_parser;
    int res = -1;

    if (request == NULL) {
        EXPORTER_LOG(AL_ERROR, "Null guac request");
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (request->conn == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: NULL guac request conn",
                     request->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (request->conn->sni == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: NULL guac request conn sni",
                     request->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    if (domain == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Null guac domain",
                     request->name);
        return ZPATH_RESULT_BAD_STATE;
    }

    /* Extract api type from uri path */
    request->guac_api_type = exporter_get_guac_api_type(request);
    if (request->guac_api_type == ZPA_GUAC_API_TYPE_INVALID) {
        EXPORTER_LOG(AL_ERROR, "%s: Guac api type is invalid. API call has invalid URI path %s", request->name, &(request->url[request->url_parser.field_data[UF_PATH].off]));
        return ZPATH_RESULT_BAD_DATA;
    }

    if (!request->conn->exporter_domain->is_ot) {
        EXPORTER_LOG(AL_ERROR, "%s - %s is not a valid Operational Technology (OT) domain", request->name, domain);
        return ZPATH_RESULT_BAD_STATE;
    } else {
        EXPORTER_LOG(AL_DEBUG, "%s Domain: %s is valid OT", request->name, domain);
    }

    if (strcmp(request->conn->sni, domain) != 0) {
        /* No match */
        EXPORTER_LOG(AL_ERROR, "%s: Session mismatch, req SNI = %s, domain = %s",
                     request->name,
                     request->conn->sni,
                     domain);
        return ZPATH_RESULT_BAD_STATE;
    }

    if (request->header_origin) {
        http_parser_url_init(&url_parser);
        res = http_parser_parse_url(request->header_origin, strlen(request->header_origin), 0, &url_parser);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not parse url", request->name);
            return ZPATH_RESULT_BAD_STATE;
        }
        if (!(url_parser.field_set & (1 << UF_HOST))) {
            EXPORTER_LOG(AL_ERROR, "%s: Received request without host in origin header", request->name);
            return ZPATH_RESULT_BAD_STATE;
        }

        if (strlen(request->conn->sni) != url_parser.field_data[UF_HOST].len ||
                strncmp(request->conn->sni,
                        &(request->header_origin[url_parser.field_data[UF_HOST].off]),
                        url_parser.field_data[UF_HOST].len) != 0) {
            /* No match */
            EXPORTER_LOG(AL_ERROR, "%s: Session mismatch, req SNI = %s, origin = %s",
                    request->name,
                    request->conn->sni,
                    request->header_origin);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
    }

    const char *api_name = get_request_api_name(request);
    EXPORTER_DEBUG_GUAC_API_DETAIL("%s: Guac state for domain: %s is valid, api_type: %s", request->name, domain, api_name);

    return ZPATH_RESULT_NO_ERROR;
}

#define KEEP_ALIVE (1)
#define WEBSOCKET_GUID "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
#define SERVER_KEY_LEN 128

/*
 * Add HTTP upgrade response data
 */
static int exporter_request_respond_with_upgrade(struct exporter_request *request, enum http_status status, size_t *hdr_len)
{
    int i;
    char response_key[SERVER_KEY_LEN];
    unsigned char response_hash[EVP_MAX_MD_SIZE];
    EVP_MD_CTX *ctx;

    if (!(ctx = EVP_MD_CTX_create())) {
        EXPORTER_LOG(AL_ERROR, "Could not create SHA context");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha1(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        EXPORTER_LOG(AL_ERROR, "Could not init SHA1 context");
        return ZPATH_RESULT_ERR;
    }

    response_key[0] = '\0';
    for (i = 0; i < request->total_headers; i++) {
        /* Extract Sec-Websocket-Key value */
        if (strcasecmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Sec-WebSocket-Key") == 0) {
            const char *websocket_key_in_header = zmicro_heap_ref(&request->heap, request->header_value_ofs[i]);
            if (base64_decoded_size(websocket_key_in_header, strlen(websocket_key_in_header)) + strlen(WEBSOCKET_GUID) <= SERVER_KEY_LEN) {
                snprintf(response_key, SERVER_KEY_LEN, "%s%s", websocket_key_in_header, WEBSOCKET_GUID);

                if (1 != EVP_DigestUpdate(ctx, response_key, strlen(response_key))) {
                    EVP_MD_CTX_destroy(ctx);
                    EXPORTER_LOG(AL_ERROR, "Could not update SHA digest");
                    return ZPATH_RESULT_ERR;
                }

                unsigned int shalen = sizeof(response_hash);
                if (1 != EVP_DigestFinal_ex(ctx, response_hash, &shalen)) {
                    EXPORTER_LOG(AL_ERROR, "Could not finalize SHA digest");
                    EVP_MD_CTX_destroy(ctx);
                    return ZPATH_RESULT_ERR;
                }
                EVP_MD_CTX_destroy(ctx);
                response_key[0] = '\0';
                if (base64_encoded_size(shalen) <= SERVER_KEY_LEN) {
                    base64_encode_binary(response_key, response_hash, shalen);
                }
            }
            break;
        }
    }

    if (!strlen(response_key)) return ZPATH_RESULT_BAD_DATA;

    const char *fmt =
            "HTTP/1.1 %d %s\r\n"
            "Server: " EXPORTER_SERVER_NAME "\r\n"
            "Date: %s\r\n"
            "Connection: upgrade\r\n"
            "Sec-WebSocket-Accept: %s\r\n"
            "Sec-WebSocket-Protocol: guacamole\r\n"
            "Upgrade: websocket\r\n"
            "Access-Control-Allow-Credentials: true\r\n"
            "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
            "Access-Control-Allow-Methods: GET, OPTIONS\r\n"
            "Access-Control-Allow-Origin: https://%s\r\n"
            "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
            "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
            "Pragma: no-cache\r\n"
            "Expires: 0\r\n"
            "X-Content-Type-Options: nosniff\r\n"
            "X-Frame-Options: DENY\r\n"
            "X-XSS-Protection: 1; mode = block\r\n"
            "\r\n";
    //"Sec-WebSocket-Extensions: permessage-deflate\r\n"
    char tm_buf[128] = "";
    const char *status_name = "Switching Protocols";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    time_t now = time(0);
    struct tm ts = { 0 };

    gmtime_r(&now, &ts);
    strftime(tm_buf, sizeof(tm_buf), "%a, %d %b %Y %H:%M:%S %Z", &ts);

    char http_headers_buf[strlen(fmt) + 3 + strlen(status_name)
            + strlen(domain) + strlen(tm_buf) +strlen(response_key)];
    http_headers_buf[0] = '\0';

    snprintf(http_headers_buf, sizeof(http_headers_buf), fmt, 101, status_name, tm_buf, response_key, domain);

    EXPORTER_DEBUG_GUAC_API_DETAIL("%s: Sending response headers and status: %s", request->name, status_name);

    /* Sample HTTP header options recommended for security audit */
    /* ============================================================
        access-control-allow-credentials: true
        access-control-allow-headers: Origin, X-Requested-With, Content-Type, Accept, Authorization
        access-control-allow-methods: POST, GET, PUT, DELETE, OPTIONS
        access-control-allow-origin: https://admin-release.dev.zpath.net
        cache-control: no-cache, no-store, max-age=0, must-revalidate
        content-type: (varies)
        date: Thu, 30 May 2019 21:37:23 GMT
        expires: 0
        pragma: no-cache
        status: 200
        x-content-type-options: nosniff
        x-frame-options: DENY
        x-xss-protection: 1; mode=block
    */

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }
    evbuffer_prepend(request->response_data, http_headers_buf, strlen(http_headers_buf));
    *hdr_len = strlen(http_headers_buf);

    exporter_conn_wake_from_other_thread(request->conn);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Send reauth message to browser
 */
static void exporter_request_send_reauth_message(struct exporter_request *request)
{
#define REAUTH_CMD "6.reauth;"
     if (!request->response_data) {
        request->response_data = evbuffer_new();
     }
     evbuffer_add(request->response_data, REAUTH_CMD, strlen(REAUTH_CMD));
     EXPORTER_GUAC_API_STATS_INC(websocket_reauth_sent, 1);
}

static int is_websocket_connection(struct exporter_request *request)
{
    if (request->guac_info && request->guac_info->is_websocket) return 1;
    return 0;
}

int exporter_request_check_redirect_for_ot(struct exporter_request *request, const char *target_domain)
{
    if ((request->conn->exporter_domain) &&
        (request->conn->exporter_domain->is_ot) &&
        (strcmp(target_domain, EXPORTER_DOMAIN_AUTH) == 0) &&
        is_websocket_connection(request)) {
        exporter_request_send_reauth_message(request);
        EXPORTER_LOG(AL_DEBUG, "%s Reauth message sent for %s", request->name, request->sra_host_fqdn);
        request->input_state = input_state_drain;
        return ZPATH_RESULT_NO_ERROR;
    }
    return ZPATH_RESULT_ERR;
}


int exporter_guac_send_response_internal(struct exporter_request *request, enum http_status status, int clear_response_data, char *format_str, ...)
{
    char body_buf[2000] = {'\0'};
    va_list vl;

    EXPORTER_DEBUG_HTTP("%s: Returning status %d", request->name, status);

    request->log.response_status = status;

    va_start(vl, format_str);
    vsnprintf(body_buf, sizeof(body_buf), format_str, vl);
    va_end(vl);

    EXPORTER_DEBUG_HTTP("%s: Returning error %s", request->name, body_buf);

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (clear_response_data) {
        // Remove all (now) stale data
        evbuffer_drain(request->response_data, evbuffer_get_length(request->response_data));
    }

    // Create Guacamole error instruction out of the message
    exporter_guac_instr_push(request->response_data, registered_op_codes[OP_CODE_ERROR], body_buf, 523);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

static int check_tls_setup_fail(struct exporter_request *request)
{
    if (request->log.mt_reason &&
        (strstr(request->log.mt_reason, "TLS_SETUP_FAIL") != NULL)) return 1;
    // no match
    return 0;
}

void exporter_session_proctoring_disconnect_stats_increment(struct exporter_request *request)
{
    if (!request || !request->guac_info) {
        return;
    }
    int session_monitoring_feature_enabled = 0;
    session_monitoring_feature_enabled = request->guac_info->is_session_share_enabled
        || request->guac_info->is_session_share_monitor || request->guac_info->is_session_share_control;

    if (session_monitoring_feature_enabled && check_tls_setup_fail(request)) {
        exporter_sharing_stats_inc(proctor_session_disconnect_with_error_type);
    }
}

/*
 * Process asynchronous events like auth timeouts or mtunnel errors and disconnects
 */
static int exporter_guac_async_callback_on_thread(struct exporter_request *request)
{
    int     res = ZPATH_RESULT_ERR;

    if ((request) && (request->log.mt_reason) &&
        ((strstr(request->log.mt_reason,"BRK_MT_TERMINATED_APPROVAL_TIMEOUT") != NULL) ||
         (strstr(request->log.mt_reason,"BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL") != NULL))) {
        char message[2000] = {'\0'};
        res = exporter_request_policy_state_check_approval(request, message, sizeof(message));
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return res;
        }
        res = exporter_guac_send_response(request,
                                          HTTP_STATUS_FORBIDDEN,
                                          "%s",
                                          (res == ZPATH_RESULT_NO_ERROR) ? message : request->log.mt_reason);
    } else if ((request) && (request->async_state == async_state_mt_connection_err)) {
        exporter_session_proctoring_disconnect_stats_increment(request);
        res = exporter_guac_send_response(request,
                                          HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                          "%s : %s",
                                          "Could not set up connection to server",
                                          request->log.mt_reason ? (check_tls_setup_fail(request) ?  "Session Terminated" : request->log.mt_reason) \
                                                                 : "Internal Server Error");
    } else if ((request) && (request->async_state == async_state_mt_remote_disconnect)) {
            exporter_session_proctoring_disconnect_stats_increment(request);
            res = exporter_guac_send_response(request,
                                              HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                              "%s : %s",
                                              "Disconnected from server",
                                              request->log.mt_reason ? (check_tls_setup_fail(request) ?  "Session Terminated" : request->log.mt_reason) \
                                                                     : "Internal Server Error");
    }

    return res;
}

/*
 * Send approval timeout error message to browser
 */
int exporter_request_send_approval_timeout_message(struct exporter_request *request)
{
    if (is_websocket_connection(request)) {
        if (!request->response_data) {
            request->response_data = evbuffer_new();
        }
        exporter_guac_instr_push(request->response_data, registered_op_codes[OP_CODE_ERROR], "Approval Timeout", 523);

        request->input_state = input_state_drain;
        return ZPATH_RESULT_NO_ERROR;
    }
    return ZPATH_RESULT_ERR;
}

/*
 * Make error response
 */
static int exporter_guac_make_api_error_response(const char *message, enum http_status status, guac_api_error_response_t **response)
{
    const char *type = 0;
    if (status == HTTP_STATUS_BAD_REQUEST) {
        type = GUAC_ERROR_TYPE_BAD_REQUEST;
    } else if (status == HTTP_STATUS_UNAUTHORIZED) {
        type = GUAC_ERROR_TYPE_INSUFFICIENT_CREDENTIALS;
    } else if (status == HTTP_STATUS_FORBIDDEN) {
        type = GUAC_ERROR_TYPE_PERMISSION_DENIED;
    } else if (status == HTTP_STATUS_NOT_FOUND) {
        type = GUAC_ERROR_TYPE_NOT_FOUND;
    } else if (status == HTTP_STATUS_BAD_GATEWAY) {
        // An unexpected error between the App Connector
        // and the Remote Desktop should bring us here.
        type = GUAC_ERROR_TYPE_STREAM_ERROR;
    } else {
        type = GUAC_ERROR_TYPE_INTERNAL_ERROR;
    }

    guac_api_error_response_t *resp = EXPORTER_CALLOC(sizeof(guac_api_error_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "Unable to allocate guac error response");
        return ZPATH_RESULT_NO_MEMORY;
    }

    resp->message = EXPORTER_PORTAL_SAFE_STRDUP(message);
    resp->translatableMessage = EXPORTER_PORTAL_SAFE_STRDUP(message);
    resp->type = EXPORTER_PORTAL_SAFE_STRDUP(type);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Send error response for request
 */
static int exporter_guac_api_error_response(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, char *message)
{
    int ret;
    char response_buf[GUAC_API_RESPONSE_BUF_SIZE] = "";
    guac_api_error_response_t *response = NULL;

    ret = exporter_guac_make_api_error_response(message, status, &response);

    EXPORTER_LOG(AL_ERROR, "%s: Guac api error: %s", request->name, message);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(guac_api_error_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            if (!request->response_data) {
                request->response_data = evbuffer_new();
            } else {
                evbuffer_drain(request->response_data, evbuffer_get_length(request->response_data));
            }
            evbuffer_add_printf(request->response_data, "Err: %s", message);
            ret = exporter_request_respond(request, status, error);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: Failed to send error response.", request->name);
            }
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User guac api_error_response: respond-with-json-ret: %s", request->name, zpath_result_string(ret));
            }
        } else {
            EXPORTER_LOG(AL_ERROR, "Guac api error, failed to dump structure: ret: %s\n", argo_result_string(ret));
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Guac response error: ret: %s", request->name, zpath_result_string(ret));
    }

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    } else {
        evbuffer_drain(request->response_data, evbuffer_get_length(request->response_data));
    }
    ret = exporter_request_respond_with_upgrade_error(request, status);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: User guac api_error_response, failed to send error response: respond-with-json-ret: %s", request->name, zpath_result_string(ret));
    }

    if (response != NULL) {
        EXPORTER_FREE(response->message);
        EXPORTER_FREE(response->translatableMessage);
        EXPORTER_FREE(response->type);
        // TODO: Free response->expected ET-32552

        EXPORTER_FREE(response);
    }

    return ret;
}

static const char* get_rdp_connection_security(struct exporter_request *request)
{
    const char *conn_security = request->sra_host_conn_security;
    if (!conn_security) {
        EXPORTER_LOG(AL_ERROR, "%s Host RDP Connection Security type is empty", request->name);
        return NULL;
    }
    if (strcasecmp(conn_security, "ANY") == 0) return "any";
    if (strcasecmp(conn_security, "TLS") == 0) return "tls";
    if (strcasecmp(conn_security, "RDP") == 0) return "rdp";
    if (strcasecmp(conn_security, "NLA") == 0) return "nla";

    EXPORTER_LOG(AL_ERROR, "%s Host RDP Connection Security type %s is unsupported", request->name, conn_security);
    return NULL;
}

static int get_guac_select_instruction(char *select_buffer, int len, struct exporter_request *request)
{
    if (request->is_proxy_conn && request->guac_proctored_session_id) {
        snprintf(select_buffer, len, "6.select,37.$%s;", request->guac_proctored_session_id);
        return ZPATH_RESULT_NO_ERROR;
    } else {
        if (request->sra_host_protocol != NULL) {
            if (strncasecmp(request->sra_host_protocol, "SSH", 3) == 0) {
                snprintf(select_buffer, len, "%s", GUAC_SELECT_SSH_INSTR);
            } else if (strncasecmp(request->sra_host_protocol, "RDP", 3) == 0) {
                snprintf(select_buffer, len, "%s", GUAC_SELECT_RDP_INSTR);
            } else if (strncasecmp(request->sra_host_protocol, "VNC", 3) == 0) {
                snprintf(select_buffer, len, "%s", GUAC_SELECT_VNC_INSTR);
            } else if (strncasecmp(request->sra_host_protocol, "REALVNC", 7) == 0) {
                snprintf(select_buffer, len, "%s", GUAC_SELECT_REALVNC_INSTR);
            }
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    EXPORTER_LOG(AL_ERROR, "Incorrect protocol string from zpn_sra_console: %s", request->sra_host_protocol);
    return ZPATH_RESULT_ERR;
}

void exporter_diagostic_info_cleanup(struct exporter_request *request)
{
    if (!request || !request->guac_info) {
        return;
    }

    if (request->guac_info->diag_info.console_user) {
        EXPORTER_FREE(request->guac_info->diag_info.console_user);
        request->guac_info->diag_info.console_user = NULL;
    }
    if (request->guac_info->diag_info.console_conn_type) {
        EXPORTER_FREE(request->guac_info->diag_info.console_conn_type);
        request->guac_info->diag_info.console_conn_type = NULL;
    }
    if (request->guac_info->diag_info.credential_id) {
        EXPORTER_FREE(request->guac_info->diag_info.credential_id);
        request->guac_info->diag_info.credential_id = NULL;
    }
}

#define BASE64_DECODE_BINARY(ginstr, out_addr, out_len, in_addr, in_len)  \
do { \
    out_len = base64_decode_binary(out_addr, in_addr, in_len); \
    if (out_len < 0) { \
      exporter_guac_instr_destroy(ginstr); \
      goto err;        \
    } \
}while(0)

int get_guac_connect_instruction(
        struct exporter_request *request, const char *args, char *connect_instr, size_t length)
{
    const char *protocol = request->sra_host_protocol;
    const char *host = NULL;
    int tcp_port = request->sra_host_port;
    struct exporter_guac_instr instr;
    int r = ZPATH_RESULT_NO_ERROR;
    char *username = NULL, *password = NULL;
    char *pvt_key = NULL, *passphrase = NULL;
    char *domain = NULL;
    int pvt_key_len = 0, password_len = 0, passphrase_len = 0;
    uint32_t creds_decoded_len = 0;
    char *temp = NULL;


    if (connect_instr == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s No space provided for connect instruction string",
                                request->name);
        return ZPATH_RESULT_ERR;
    }

    connect_instr[0] = '\0';

    if (request->sra_remote_ip) {
        host = request->sra_remote_ip;
    } else {
        host = request->sra_host_fqdn;
    }
    if (protocol == NULL || host == NULL ) {
        EXPORTER_LOG(AL_ERROR, "%s Empty argument: protocol=%s host=%s",
                                request->name, protocol, host);
        return ZPATH_RESULT_ERR;
    }

    if (tcp_port < 1 || tcp_port > 65535) {
        EXPORTER_LOG(AL_ERROR, "%s Incorrect TCP port obtained from config %d",
                                request->name, tcp_port);
        return ZPATH_RESULT_ERR;
    }

    r = exporter_guac_instr_init(&instr, "size", "width,height,dpi");
    if (r != ZPATH_RESULT_NO_ERROR) {
        return ZPATH_RESULT_ERR;
    }

    exporter_guac_instr_set_int_arg(&instr, "width", request->guac_info->viewport_width);
    exporter_guac_instr_set_int_arg(&instr, "height", request->guac_info->viewport_height);
    exporter_guac_instr_set_int_arg(&instr, "dpi", request->guac_info->viewport_dpi);

    // Emit screen size instruction
    exporter_guac_instr_append(&instr, connect_instr, length);

    r = exporter_guac_instr_reinit(&instr, "audio", "");
    if (r != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_instr_destroy(&instr);
        return ZPATH_RESULT_ERR;
    }

    // Emit audio instruction
    exporter_guac_instr_append(&instr, connect_instr, length);

    r = exporter_guac_instr_reinit(&instr, "video", "");
    if (r != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_instr_destroy(&instr);
        return ZPATH_RESULT_ERR;
    }

    // Emit video instruction
    exporter_guac_instr_append(&instr, connect_instr, length);

    r = exporter_guac_instr_reinit(&instr, "image", "mimetype1,mimetype2");
    if (r != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_instr_destroy(&instr);
        return ZPATH_RESULT_ERR;
    }
    exporter_guac_instr_set_string_arg(&instr, "mimetype1", "image/png");
    exporter_guac_instr_set_string_arg(&instr, "mimetype2", "image/jpeg");

    // Emit image instruction
    exporter_guac_instr_append(&instr, connect_instr, length);

    r = exporter_guac_instr_reinit(&instr, "timezone", "timezone");
    if (r != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_instr_destroy(&instr);
        return ZPATH_RESULT_ERR;
    }

    exporter_guac_instr_set_string_arg(&instr, "timezone","America/Los_Angeles");

    // Emit timezone instruction
    exporter_guac_instr_append(&instr, connect_instr, length);
    // Destroy?
    exporter_guac_instr_destroy(&instr);

    r = exporter_guac_connect_instr_init(&instr, "VERSION_1_5_0", args);
    if (r != ZPATH_RESULT_NO_ERROR) {
        return ZPATH_RESULT_ERR;
    }

    // Handshake instruction includes custom ipaddr and port with local guacd tunnels
    if (g_exporter_ot_mode && (request->guac_info->guacd_service) &&
        request->guac_info->gd_proxy.guacd_child_proxy_server &&
        !request->is_proxy_conn) {
        if (request->guac_info->gd_proxy.guacd_childproc_listener_addr) {
	        exporter_guac_instr_set_string_arg(&instr, "hostname",
                                               request->guac_info->gd_proxy.guacd_childproc_listener_addr);
        } else {
            EXPORTER_LOG(AL_ERROR, "DBG_GUACD Failure in handshake. Connect instr missing ipaddr");
            exporter_guac_api_stats_increment(GUACD_SERVICE_HANDSHAKE_FAILED);
            exporter_guac_instr_destroy(&instr);
            return ZPATH_RESULT_ERR;
        }
        if (request->guac_info->gd_proxy.guacd_childproc_listener_port) {
            exporter_guac_instr_set_int_arg(&instr, "port",
                                            request->guac_info->gd_proxy.guacd_childproc_listener_port);
        } else {
            EXPORTER_LOG(AL_ERROR, "DBG_GUACD Failure in handshake. Connect instr missing tcp port");
            exporter_guac_api_stats_increment(GUACD_SERVICE_HANDSHAKE_FAILED);
            exporter_guac_instr_destroy(&instr);
            return ZPATH_RESULT_ERR;
        }
    }
  else {
        exporter_guac_instr_set_string_arg(&instr, "hostname", host);
        exporter_guac_instr_set_int_arg(&instr, "port", tcp_port);
    }
    char *creds = NULL;
    if (!request->is_proxy_conn && !request->guac_info->is_pra_interactive_auth_disabled && request->guac_info->creds) {
        creds_decoded_len = base64_decoded_size(request->guac_info->creds, strnlen(request->guac_info->creds, MAX_CRED_LEN));
        creds = base64_decode(request->guac_info->creds);
        if (creds == NULL) {
            exporter_guac_instr_destroy(&instr);
            return ZPATH_RESULT_ERR;
        }
    }

    if (is_pra_clipboard_disabled(request->conn->exporter_domain->customer_gid)) {
        exporter_guac_instr_set_string_arg(&instr, "disable-copy", "true");
        exporter_guac_instr_set_string_arg(&instr, "disable-paste", "true");
    }
    request->guac_info->is_sftp_disabled = 1; /* default */
    if (request->is_proxy_conn) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Proxy request, skip setting username and other parameters");
        goto end;
    }
    if (strncasecmp(protocol, "RDP", 3) == 0) {
        exporter_guac_api_stats_increment(GUACD_SERVICE_RDP_SESSION_COUNT);
        const char *rdp_conn_security = get_rdp_connection_security(request);
        if(!rdp_conn_security) {
            if (creds) {
                memset(creds, '\0', creds_decoded_len);
                free((void *) creds);
                creds = NULL;
	    }
            exporter_guac_instr_destroy(&instr);
            return ZPATH_RESULT_ERR;
        }

        if (!request->guac_info->is_pra_interactive_auth_disabled ) {
            if (request->guac_info->creds) {
                r = exporter_conn_guac_parse_json_rdp_args((const char *) creds, &username,
                    &password,
                    &domain);
                memset(creds, '\0', creds_decoded_len);
                free((void *) creds);
                creds = NULL;
                if (r != ZPATH_RESULT_NO_ERROR) {
                    exporter_guac_instr_destroy(&instr);
                    return ZPATH_RESULT_ERR;
                }
		        if (request->mt) {
                    request->mt->console_cred_type = zpn_console_credential_type_username_password;
		        } else {
                    request->guac_info->diag_info.console_cred_type = zpn_console_credential_type_username_password;
                }
            }
        } else {
            username = request->guac_info->exporter_credentials.user_name;
            domain = request->guac_info->exporter_credentials.user_domain;
	        if (request->mt) {
                request->mt->console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
            } else {
                request->guac_info->diag_info.console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
            }
            EXPORTER_FREE(request->guac_info->exporter_credentials.cred_type);
            request->guac_info->exporter_credentials.cred_type = NULL;
    	    if (request->guac_info->exporter_credentials.decrypted_password) {
                password_len = base64_decoded_size(request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password = EXPORTER_MALLOC(password_len+1);
                if (!password || !username) {
                    exporter_guac_instr_destroy(&instr);
                    goto err;
                }
                BASE64_DECODE_BINARY(&instr, (unsigned char*)password, password_len, request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password[password_len] = '\0';
                memset(request->guac_info->exporter_credentials.decrypted_password, '\0', request->guac_info->exporter_credentials.decrypted_password_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_password);
                request->guac_info->exporter_credentials.decrypted_password = NULL;
            }
        }

        exporter_guac_instr_set_string_arg(&instr, "username", username);
        exporter_guac_instr_set_string_arg(&instr, "password", password);

        if (request->guac_info->is_headless) {
            exporter_guac_instr_set_string_arg(&instr, "sftp-username", username);
            exporter_guac_instr_set_string_arg(&instr, "sftp-password", password);
        }

        if (domain && username) {
            exporter_guac_instr_set_string_arg(&instr, "domain", domain);
            if (request->mt && !request->mt->console_user) {
                temp = EXPORTER_CALLOC(strlen(username)+strlen(domain)+2);
                request->mt->console_user = temp;
            }
	        if (request->mt) {
                memcpy(request->mt->console_user, username, strlen(username));
                memcpy(request->mt->console_user+strlen(username), "@", strlen("@"));
                memcpy(request->mt->console_user+strlen(username)+1, domain, strlen(domain));
            } else {
                temp = EXPORTER_CALLOC(strlen(username)+strlen(domain)+2);
                memcpy(temp, username, strlen(username));
                memcpy(temp+strlen(username), "@", strlen("@"));
                memcpy(temp+strlen(username)+1, domain, strlen(domain));
                request->guac_info->diag_info.console_user = temp;
            }
            memset(domain, '\0', strlen(domain));
            EXPORTER_FREE(domain);
            request->guac_info->exporter_credentials.user_domain = NULL;
        } else if (username) {
            if (request->mt && !request->mt->console_user) {
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->mt->console_user = temp;
            } else if (!request->guac_info->diag_info.console_user) {
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->guac_info->diag_info.console_user = temp;
            }
        }

        if (username) {
            memset(username, '\0', strlen(username));
            EXPORTER_FREE(username);
            request->guac_info->exporter_credentials.user_name = NULL;
            username = NULL;
        }
        if (password) {
             memset(password, '\0', strlen(password));
             EXPORTER_FREE(password);
             password = NULL;
        }

        exporter_guac_instr_set_string_arg(&instr, "security", rdp_conn_security);

        exporter_guac_instr_set_string_arg(&instr, "ignore-cert", "true");
        exporter_guac_instr_set_string_arg(&instr, "disable-glyph-caching", "true");
        exporter_guac_instr_set_int_arg(&instr, "width", request->guac_info->viewport_width);
        exporter_guac_instr_set_int_arg(&instr, "height", request->guac_info->viewport_height);
        exporter_guac_instr_set_int_arg(&instr, "dpi", request->guac_info->viewport_dpi);
        exporter_guac_instr_set_string_arg(&instr, "client-name", "Zscaler RDP");
        exporter_guac_instr_set_string_arg(&instr, "disable-audio", "true");
        exporter_guac_instr_set_string_arg(&instr, "server-layout",
                keyboard_layout_names[request->guac_info->server_layout]);
        exporter_guac_instr_set_string_arg(&instr, "resize-method", "display-update");

        /* if feature-flag enabled for UX improvements */
        if (!is_pra_display_quality_disabled(request->conn->exporter_domain->customer_gid)) {
            switch(request->guac_info->ux_quality) {
            case EXPORTER_UX_DISPLAY_QUALITY_NORMAL:
                exporter_guac_instr_set_int_arg(&instr, "color-depth", 24);
                exporter_guac_instr_set_string_arg(&instr, "enable-font-smoothing", "true");
                break;
            case EXPORTER_UX_DISPLAY_QUALITY_HIGH:
                exporter_guac_instr_set_string_arg(&instr, "force-lossless", "true");
                exporter_guac_instr_set_int_arg(&instr, "color-depth", 32);
                exporter_guac_instr_set_string_arg(&instr, "enable-font-smoothing", "true");
                exporter_guac_instr_set_string_arg(&instr, "enable-wallpaper", "true");
                exporter_guac_instr_set_string_arg(&instr, "enable-theming", "true");
                exporter_guac_instr_set_string_arg(&instr, "enable-full-window-drag", "true");
                exporter_guac_instr_set_string_arg(&instr, "enable-desktop-composition", "true");
                exporter_guac_instr_set_string_arg(&instr, "enable-menu-animations", "true");
                break;
            default:
                break;
            }
        }

        if (!is_pra_ft_disabled(request->conn->exporter_domain->customer_gid)) {
            char dir_path[GUAC_PATH_MAX_LEN] = { '\0' };

            int dir_path_len = snprintf(dir_path, GUAC_PATH_MAX_LEN, "%s/%"PRId64,
                    exporter_guac_rdprd_path,
                    request->conn->exporter_domain->customer_gid);

            if (mkdir(dir_path, S_IRWXU) < 0 && errno != EEXIST)  {
                EXPORTER_LOG(AL_ERROR, "%s: Could not create RDP-RD directory %s", request->name, dir_path);
                exporter_guac_instr_destroy(&instr);
                return ZPATH_RESULT_ERR;
            }

            snprintf(dir_path + dir_path_len, GUAC_PATH_MAX_LEN - dir_path_len, "/%d_%"PRId64,
                    request->conn->connection_id, request->conn->incarnation);

            if (request->guac_info->is_headless) {
                /* for headless RDP do not enable sftp at this moment.
                 * it will be enabled by guacd after setting up on RDP protocol conn.
                 * exporter_guac_instr_set_string_arg(&instr, "enable-sftp", "true");
                 */
                exporter_guac_instr_set_string_arg(&instr, "enable-drive", "false");
                request->guac_info->is_sftp_disabled = 0;
                /* Set sftp-port for RDP so it does NOT connect to loopback port 22 */
                if ((request->guac_info->guacd_service) &&
                    request->guac_info->gd_proxy.guacd_child_proxy_server) {
                    exporter_guac_instr_set_int_arg(&instr, "sftp-port",
                        request->guac_info->gd_proxy.guacd_childproc_listener_port);
                }
            } else {
                exporter_guac_instr_set_string_arg(&instr, "enable-drive", "true");
                exporter_guac_instr_set_string_arg(&instr, "drive-name", "Z");
                exporter_guac_instr_set_string_arg(&instr, "drive-path", dir_path);
                exporter_guac_instr_set_string_arg(&instr, "create-drive-path", "true");
            }
        }
    } else if (strncasecmp(protocol, "SSH", 3) == 0) {
        exporter_guac_api_stats_increment(GUACD_SERVICE_SSH_SESSION_COUNT);
        if (!request->guac_info->is_pra_interactive_auth_disabled) {
            if (request->guac_info->creds) {
                r = exporter_conn_guac_parse_json_ssh_args((const char *) creds, &username,
                        &password,
                        &pvt_key,
                        &passphrase);
                memset(creds, '\0', creds_decoded_len);
                free((void *) creds);
                creds = NULL;
                if (r != ZPATH_RESULT_NO_ERROR) {
                    exporter_guac_instr_destroy(&instr);
                    return ZPATH_RESULT_ERR;
                }
            }
        } else {
            username = request->guac_info->exporter_credentials.user_name;
	        if (request->mt) {
                request->mt->console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
	        } else {
                request->guac_info->diag_info.console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
            }
            EXPORTER_FREE(request->guac_info->exporter_credentials.cred_type);
            request->guac_info->exporter_credentials.cred_type = NULL;
            if (request->guac_info->exporter_credentials.decrypted_password) {
                password_len = base64_decoded_size(request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password = EXPORTER_MALLOC(password_len+1);
                if (password == NULL) {
                    exporter_guac_instr_destroy(&instr);
                    goto err;
                }
                BASE64_DECODE_BINARY(&instr, (unsigned char*)password, password_len, request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password[password_len] = '\0';
                memset(request->guac_info->exporter_credentials.decrypted_password, '\0', request->guac_info->exporter_credentials.decrypted_password_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_password);
                request->guac_info->exporter_credentials.decrypted_password = NULL;
            }
            if (request->guac_info->exporter_credentials.decrypted_passphrase) {
                passphrase_len = base64_decoded_size(request->guac_info->exporter_credentials.decrypted_passphrase, request->guac_info->exporter_credentials.decrypted_passphrase_len);
                passphrase = EXPORTER_MALLOC(passphrase_len+1);
                if (passphrase == NULL) {
                    exporter_guac_instr_destroy(&instr);
                    goto err;
                }
                BASE64_DECODE_BINARY(&instr, (unsigned char*)passphrase, passphrase_len, request->guac_info->exporter_credentials.decrypted_passphrase, request->guac_info->exporter_credentials.decrypted_passphrase_len);
                passphrase[passphrase_len] = '\0';
                memset(request->guac_info->exporter_credentials.decrypted_passphrase, '\0', request->guac_info->exporter_credentials.decrypted_passphrase_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_passphrase);
                request->guac_info->exporter_credentials.decrypted_passphrase = NULL;
            }
            if (request->guac_info->exporter_credentials.decrypted_private_key) {
                pvt_key_len = base64_decoded_size(request->guac_info->exporter_credentials.decrypted_private_key, request->guac_info->exporter_credentials.decrypted_private_key_len);
                pvt_key = EXPORTER_MALLOC(pvt_key_len + 1);
                if (pvt_key == NULL) {
                    exporter_guac_instr_destroy(&instr);
                    goto err;
                }
                BASE64_DECODE_BINARY(&instr, (unsigned char*)pvt_key, pvt_key_len, request->guac_info->exporter_credentials.decrypted_private_key, request->guac_info->exporter_credentials.decrypted_private_key_len);
                pvt_key[pvt_key_len] = '\0';
                memset(request->guac_info->exporter_credentials.decrypted_private_key, '\0', request->guac_info->exporter_credentials.decrypted_private_key_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_private_key);
                request->guac_info->exporter_credentials.decrypted_private_key = NULL;
            }
        }

        if (username) {
            exporter_guac_instr_set_string_arg(&instr, "username", username);
            if (request->mt && !request->mt->console_user) {
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->mt->console_user = temp;
            } else if (!request->guac_info->diag_info.console_user){
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->guac_info->diag_info.console_user = temp;
            }
        }

        if (password) {
            exporter_guac_instr_set_string_arg(&instr, "password", password);
            memset(password, '\0', strlen(password));
	        if (request->mt) {
                request->mt->console_cred_type = zpn_console_credential_type_username_password;
	        } else {
                request->guac_info->diag_info.console_cred_type = zpn_console_credential_type_username_password;
            }
            EXPORTER_FREE(password);
            password = NULL;
        }
        if (pvt_key) {
            exporter_guac_instr_set_string_arg(&instr, "private-key", pvt_key);
	        if (request->mt) {
                request->mt->console_cred_type = zpn_console_credential_type_ssh_key;
	        } else {
                request->guac_info->diag_info.console_cred_type = zpn_console_credential_type_ssh_key;
            }

            memset(pvt_key, '\0', strlen(pvt_key));
            EXPORTER_FREE(pvt_key);
            pvt_key = NULL;
        }

        if (passphrase) {
            exporter_guac_instr_set_string_arg(&instr, "passphrase", passphrase);
            memset(passphrase, '\0', strlen(passphrase));
            EXPORTER_FREE(passphrase);
            passphrase = NULL;
        }

        if (username) {
            memset(username, '\0', strlen(username));
            EXPORTER_FREE(username);
            username = NULL;
            request->guac_info->exporter_credentials.user_name = NULL;
        }

        // By this time, privileged credentials will be available for SSH
        int is_file_transfer_required = (request->guac_info->capabilities_policy_bitmap &
                (FILE_UPLOAD | FILE_DOWNLOAD | INSPECT_FILE_UPLOAD | INSPECT_FILE_DOWNLOAD)) != 0 ? 1 : 0;

        if (!is_pra_ft_disabled(request->conn->exporter_domain->customer_gid) && is_file_transfer_required) {
            exporter_guac_instr_set_string_arg(&instr, "enable-sftp", "true");
            exporter_guac_instr_set_string_arg(&instr, "sftp-root-directory", "/");
            request->guac_info->is_sftp_disabled = 0;
        }
    } else if ((strncasecmp(protocol, "VNC", 3) == 0) || (strncasecmp(protocol, "REALVNC", 7) == 0)) {
        if (strncasecmp(protocol, "VNC", 3) == 0) {
            exporter_guac_api_stats_increment(GUACD_SERVICE_VNC_SESSION_COUNT);
        } else {
            exporter_guac_api_stats_increment(GUACD_SERVICE_REALVNC_SESSION_COUNT);
            exporter_guac_instr_set_int_arg(&instr, "width", request->guac_info->viewport_width);
            exporter_guac_instr_set_int_arg(&instr, "height", request->guac_info->viewport_height);
        }

        /* international keyboard support for vnc & realvnc */
        exporter_guac_instr_set_string_arg(&instr, "server-layout",
            keyboard_layout_names[request->guac_info->server_layout]);

        /* if feature-flag enabled for UX improvements */
        if (!is_pra_display_quality_disabled(request->conn->exporter_domain->customer_gid)) {
            switch(request->guac_info->ux_quality) {
            case EXPORTER_UX_DISPLAY_QUALITY_NORMAL:
                exporter_guac_instr_set_int_arg(&instr, "color-depth", 24);
                break;
            case EXPORTER_UX_DISPLAY_QUALITY_HIGH:
                exporter_guac_instr_set_string_arg(&instr, "force-lossless", "true");
                exporter_guac_instr_set_int_arg(&instr, "color-depth", 32);
                exporter_guac_instr_set_string_arg(&instr, "cursor", "remote");
                break;
            default:
                break;
            }
        }

        if (!request->guac_info->is_pra_interactive_auth_disabled) {
            if (request->guac_info->creds) {
                r = exporter_conn_guac_parse_json_vnc_args((const char *) creds, &username,
                        &password);
                memset(creds, '\0', creds_decoded_len);
                free((void *) creds);
                creds = NULL;
                if (r != ZPATH_RESULT_NO_ERROR) {
                    exporter_guac_instr_destroy(&instr);
                    return ZPATH_RESULT_ERR;
                }
            }
        } else {
            if (request->guac_info->exporter_credentials.user_name) {
                username = request->guac_info->exporter_credentials.user_name;
                request->guac_info->exporter_credentials.user_name = NULL;
            }
	        if (request->mt) {
                request->mt->console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
	        } else {
                request->guac_info->diag_info.console_cred_type = get_cred_type(request->guac_info->exporter_credentials.cred_type);
            }
            EXPORTER_FREE(request->guac_info->exporter_credentials.cred_type);
            request->guac_info->exporter_credentials.cred_type = NULL;
            if (request->guac_info->exporter_credentials.decrypted_password) {
                password_len = base64_decoded_size(request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password = EXPORTER_MALLOC(password_len+1);
                if (password == NULL) {
                    exporter_guac_instr_destroy(&instr);
                    goto err;
                }
                BASE64_DECODE_BINARY(&instr, (unsigned char*)password, password_len, request->guac_info->exporter_credentials.decrypted_password, request->guac_info->exporter_credentials.decrypted_password_len);
                password[password_len] = '\0';
                memset(request->guac_info->exporter_credentials.decrypted_password, '\0', request->guac_info->exporter_credentials.decrypted_password_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_password);
                request->guac_info->exporter_credentials.decrypted_password = NULL;
            }
        }

        if (username) {
            exporter_guac_instr_set_string_arg(&instr, "username", username);
            if (request->mt && !request->mt->console_user) {
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->mt->console_user = temp;
            } else if (!request->guac_info->diag_info.console_user) {
                temp = EXPORTER_STRDUP(username, strlen(username));
                request->guac_info->diag_info.console_user = temp;
            }
	        if (request->mt) {
                request->mt->console_cred_type = zpn_console_credential_type_username_password;
	        } else {
                request->guac_info->diag_info.console_cred_type = zpn_console_credential_type_username_password;
            }
        } else {
	        if (request->mt) {
                request->mt->console_cred_type = zpn_console_credential_type_password;
	        } else {
                request->guac_info->diag_info.console_cred_type = zpn_console_credential_type_password;
            }
        }
        if (password) {
            exporter_guac_instr_set_string_arg(&instr, "password", password);
        }

        // By this time, privileged credentials will be available for VNC
        int is_file_transfer_required = (request->guac_info->capabilities_policy_bitmap &
                (FILE_UPLOAD | FILE_DOWNLOAD | INSPECT_FILE_UPLOAD | INSPECT_FILE_DOWNLOAD)) != 0 ? 1 : 0;

        if (!is_pra_ft_disabled(request->conn->exporter_domain->customer_gid) && is_file_transfer_required) {
            exporter_guac_instr_set_string_arg(&instr, "enable-sftp", "true");
            exporter_guac_instr_set_string_arg(&instr, "sftp-root-directory", "/");
            request->guac_info->is_sftp_disabled = 0;
            if ((request->guac_info->guacd_service) &&
                request->guac_info->gd_proxy.guacd_child_proxy_server) {
                /* Set sftp-port for VNC so it does not connect to loopback port 22 */
                exporter_guac_instr_set_int_arg(&instr, "sftp-port",
                                                request->guac_info->gd_proxy.guacd_childproc_listener_port);
            }
            if (username) {
                exporter_guac_instr_set_string_arg(&instr, "sftp-username", username);
            }
            if (password) {
                exporter_guac_instr_set_string_arg(&instr, "sftp-password", password);
            }
        }

        if (username) {
            memset(username, '\0', strlen(username));
            EXPORTER_FREE(username);
            username = NULL;
            request->guac_info->exporter_credentials.user_name = NULL;
        }
        if (password) {
            if (password && password[0]) {
                memset(password, '\0', strlen(password));
            }
            EXPORTER_FREE(password);
            password = NULL;
        }

    }

    // Remove creds from the request
    if (!request->guac_info->is_pra_interactive_auth_disabled && request->guac_info->creds) {
        EXPORTER_FREE(request->guac_info->creds);
        request->guac_info->creds = NULL;
        if (creds) {
            memset(creds, '\0', creds_decoded_len);
            free((void *) creds);
            creds = NULL;
	}
    }
 end:
    // Emit connect instruction
    if (exporter_guac_instr_append(&instr, connect_instr, length) != ZPATH_RESULT_NO_ERROR) {
        exporter_guac_instr_destroy(&instr);
        return ZPATH_RESULT_ERR;
    }
    exporter_guac_instr_destroy(&instr);

    EXPORTER_LOG(AL_DEBUG,"%s Guac connect string: %.*s", request->name, EXPORTER_DEBUG_BYTES, connect_instr);
    return ZPN_RESULT_NO_ERROR;
    err:
        if (request->guac_info->is_pra_interactive_auth_disabled) {
            if (username) {
                memset(username, '\0', request->guac_info->exporter_credentials.user_name_len);
                EXPORTER_FREE(username);
                username = NULL;
                request->guac_info->exporter_credentials.user_name = NULL;
            }
            if (password) {
                if (password_len > 0) {
                    memset(password, '\0', password_len);
                    EXPORTER_FREE(password);
                }
                password = NULL;
            }
            if (pvt_key) {
                if (pvt_key_len > 0) {
                    // coverity[DEADCODE]
                    memset(pvt_key, '\0', pvt_key_len);
                    EXPORTER_FREE(pvt_key);
                }
                pvt_key = NULL;
            }
            if (passphrase) {
                if (passphrase_len > 0) {
                    memset(passphrase, '\0', passphrase_len);
                    EXPORTER_FREE(passphrase);
                }
                passphrase = NULL;
            }
            if (domain) {
                memset(domain, '\0', request->guac_info->exporter_credentials.user_domain_len);
                EXPORTER_FREE(domain);
                domain = NULL;
                request->guac_info->exporter_credentials.user_domain = NULL;
            }
            if (request->guac_info->exporter_credentials.decrypted_passphrase) {
                memset(request->guac_info->exporter_credentials.decrypted_passphrase, '\0', request->guac_info->exporter_credentials.decrypted_passphrase_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_passphrase);
                request->guac_info->exporter_credentials.decrypted_passphrase = NULL;
            }
            if (request->guac_info->exporter_credentials.decrypted_private_key) {
                memset(request->guac_info->exporter_credentials.decrypted_private_key, '\0', request->guac_info->exporter_credentials.decrypted_private_key_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_private_key);
                request->guac_info->exporter_credentials.decrypted_private_key = NULL;
            }
            if (request->guac_info->exporter_credentials.decrypted_password) {
                memset(request->guac_info->exporter_credentials.decrypted_password, '\0', request->guac_info->exporter_credentials.decrypted_password_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.decrypted_password);
                request->guac_info->exporter_credentials.decrypted_password = NULL;
            }
            if (request->guac_info->exporter_credentials.cred_type) {
                EXPORTER_FREE(request->guac_info->exporter_credentials.cred_type);
                request->guac_info->exporter_credentials.cred_type = NULL;
            }
        }
        return ZPATH_RESULT_ERR;
}

static int zpn_sra_console_get_info(struct exporter_request *request, int64_t console_id)
{
    int result;
    struct zpn_sra_console *sra_console = NULL;

    /* For DJB websocket tunnel requests - get the ipaddr and creds info from desktop service */
    int is_djb = check_query_param_djb(request);
    if (is_djb) {
        /* Set for websocket-tunnel DJB requests only */
        if (exporter_get_guac_api_type(request) != ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL) {
            return ZPATH_RESULT_NO_ERROR;
        }
        request->is_djb = 1;
        return zpn_sra_djb_get_info(request, console_id);
    }

    result = zpn_sra_console_get_by_id_immediate(console_id, request->conn->exporter_domain->customer_gid, request->scope_gid, &sra_console);
    EXPORTER_GUAC_API_STATS_INC_BY_RET_CODE(console_get_by_id, 1, result);
    if (sra_console == NULL && result == ZPATH_RESULT_NO_ERROR) {
        result = ZPATH_RESULT_BAD_STATE;
    }
    if (result != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get zpn_sra_console configuration for console_id %"PRId64": %s", request->name, console_id, zpath_result_string(result));
        return result;
    }
    if (!sra_console->enabled) {
        EXPORTER_LOG(AL_ERROR, "%s: console %"PRId64" is disabled", request->name, console_id);
        EXPORTER_GUAC_API_STATS_INC(console_not_enabled, 1);
        return ZPN_RESULT_ACCESS_DENIED;
    }
    if (!sra_console->sra_app_id) {
        /* A leftover from previous data model */
        EXPORTER_LOG(AL_ERROR, "%s: console %"PRId64" is misconfigured", request->name, console_id);
        EXPORTER_GUAC_API_STATS_INC(console_misconfigured, 1);
        return ZPATH_RESULT_BAD_STATE;
    }

    /* Get sra_application */
    struct zpn_sra_application *sra_application = NULL;

    result = zpn_sra_application_get_by_id_immediate(sra_console->sra_app_id, &sra_application);
    EXPORTER_GUAC_API_STATS_INC_BY_RET_CODE(application_get_by_id, 1, result);
    if (sra_application == NULL && result == ZPATH_RESULT_NO_ERROR) {
        result = ZPATH_RESULT_BAD_STATE;
    }
    if (result != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get zpn_sra_application configuration for console_id %"PRId64": %s", request->name, console_id, zpath_result_string(result));
        return result;
    }
    if (sra_application->deleted || sra_application->customer_gid != request->conn->exporter_domain->customer_gid) {
        EXPORTER_LOG(AL_ERROR, "%s: application %"PRId64" is deleted or belongs to another customer/scope", request->name, sra_application->gid);
        EXPORTER_GUAC_API_STATS_INC(application_misconfigured, 1);
        return ZPATH_RESULT_BAD_STATE;
    }

    EXPORTER_LOG(AL_DEBUG, "%s: zpn_sra_application configuration from Wally - domain: %s; port: %d; protocol: %s; application_security: %s",
                 request->name, sra_application->domain, sra_application->port, sra_application->protocol, sra_application->connection_security);
    if (!sra_application->enabled) {
        EXPORTER_LOG(AL_ERROR, "%s: console %"PRId64" is disabled", request->name, console_id);
        EXPORTER_GUAC_API_STATS_INC(application_not_enabled, 1);
        return ZPN_RESULT_ACCESS_DENIED;
    }

    /* Check the values in the row */
    if (!sra_application->domain || !sra_application->port || !sra_application->protocol) {
        EXPORTER_LOG(AL_ERROR, "%s: Empty column values found in zpn_sra_application row with gid %"PRId64" configuration from Wally", request->name, sra_application->gid);
        EXPORTER_GUAC_API_STATS_INC(application_misconfigured, 1);
        return ZPATH_RESULT_BAD_DATA;
    }

    if (!request->sra_remote_ip) {
        result = get_app_server(request, sra_application->app_id);
        if (result == ZPATH_RESULT_ASYNCHRONOUS) {
            return result;
        }
    }

    if (!request->sra_host_fqdn) {
        request->sra_host_fqdn = EXPORTER_STRDUP(sra_application->domain, strlen(sra_application->domain));
    }
    request->sra_host_port = sra_application->port;
    request->pra_console_id = console_id;

    if (!request->pra_console_name) {
        request->pra_console_name = EXPORTER_STRDUP(sra_console->name, strlen(sra_console->name));
    }

    if (!request->sra_host_protocol) {
        request->sra_host_protocol = EXPORTER_STRDUP(sra_application->protocol, strlen(sra_application->protocol));
    }

    if (sra_application->connection_security && !request->sra_host_conn_security) {
        request->sra_host_conn_security = EXPORTER_STRDUP(sra_application->connection_security,
                                                            strlen(sra_application->connection_security));
    }
    request->app_id = sra_application->app_id;
    return result;
}

int64_t get_console_id_from_zconsole_param(struct exporter_request *request)
{
    int64_t console_id = 0;
    char console_id_string[32] = {'\0'};
    const char *zconsole_parameter = "zconsole";

    int res = query_string_find(request->url, &(request->url_parser), zconsole_parameter, console_id_string, sizeof(console_id_string));

    if (res == ZPATH_RESULT_NO_ERROR) {
        console_id = atol(console_id_string);
        EXPORTER_LOG(AL_DEBUG, "Console ID obtained from the URL parsing: %s %ld", console_id_string, (long)console_id);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console ID zconsole from url parsing failed %s", request->url);
    }

    return console_id;
}

static int64_t get_console_id_from_tunnel_request(struct exporter_request *request)
{
    int64_t console_id = 0;
    char zconsole_url_string[64] = {'\0'};

    console_id = request->is_proxy_conn ? request->pra_console_id : get_console_id_from_zconsole_param(request);
    if (!request->is_proxy_conn && console_id) {
        snprintf(zconsole_url_string, sizeof(zconsole_url_string), "/v1/zconsole/%"PRId64"", console_id);
        if (!request->sra_zconsole_url) {
            request->sra_zconsole_url = EXPORTER_STRDUP(zconsole_url_string, strlen(zconsole_url_string));
        }
    }

    return console_id;
}

static int get_viewport_params_from_tunnel_request(struct exporter_request *request, int viewport_params[7])
{
    int guac_width = 0;
    char guac_width_string[32];
    const char *GUAC_WIDTH_PARAMETER = "GUAC_WIDTH";

    int res = query_string_find(request->url, &(request->url_parser), GUAC_WIDTH_PARAMETER, guac_width_string, sizeof(guac_width_string));

    if (res == ZPATH_RESULT_NO_ERROR) {
        guac_width = atoi(guac_width_string);
        EXPORTER_LOG(AL_DEBUG, "Console width obtained from the URL parsing: %s %d", guac_width_string, guac_width);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console width from url parsing failed %s", request->url);
        guac_width = 0;
    }

    int guac_height = 0;
    char guac_height_string[32];
    const char *GUAC_HEIGHT_PARAMETER = "GUAC_HEIGHT";

    res = query_string_find(request->url, &(request->url_parser), GUAC_HEIGHT_PARAMETER, guac_height_string, sizeof(guac_height_string));

    if (res == ZPATH_RESULT_NO_ERROR) {
        guac_height = atoi(guac_height_string);
        EXPORTER_LOG(AL_DEBUG, "Console height obtained from the URL parsing: %s %d", guac_height_string, guac_height);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console height from url parsing failed %s", request->url);
        guac_height = 0;
    }

    int guac_dpi = 0;
    char guac_dpi_string[32];
    const char *GUAC_DPI_PARAMETER = "GUAC_DPI";

    res = query_string_find(request->url, &(request->url_parser), GUAC_DPI_PARAMETER, guac_dpi_string, sizeof(guac_dpi_string));

    if (res == ZPATH_RESULT_NO_ERROR) {
        guac_dpi = atoi(guac_dpi_string);
        EXPORTER_LOG(AL_DEBUG, "Console dpi obtained from the URL parsing: %s %d", guac_dpi_string, guac_dpi);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console dpi from url parsing failed %s", request->url);
        guac_dpi = 0;
    }

    char guac_server_layout_string[32];
    const char *GUAC_SERVER_LAYOUT_PARAMETER = "keyboardLayout";
    enum keyboard_layout guac_server_layout;
    int headless = 0;

    res = query_string_find(
            request->url,
            &(request->url_parser),
            GUAC_SERVER_LAYOUT_PARAMETER,
            guac_server_layout_string,
            sizeof(guac_server_layout_string));

    // The default layout
    guac_server_layout = en_us_qwerty;
    if (res == ZPATH_RESULT_NO_ERROR) {
        for (enum keyboard_layout l=0; l < end_of_layouts; l++) {
            if (!strncasecmp(guac_server_layout_string, keyboard_layout_names[l], strlen(keyboard_layout_names[l]))) {
                guac_server_layout = l;
                break;
            }
        }
        EXPORTER_LOG(AL_DEBUG, "Console server layout obtained from the URL %s matched to %s",
                guac_server_layout_string, keyboard_layout_names[guac_server_layout]);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console server layout not found in url %s, using the default %s",
                request->url, keyboard_layout_names[guac_server_layout]);
    }

    char guac_headless_string[32] = {0};
    const char *GUAC_HEADLESS_SSH_PARAMETER = "headless";

    res = query_string_find(
            request->url,
            &(request->url_parser),
            GUAC_HEADLESS_SSH_PARAMETER,
            guac_headless_string,
            sizeof(guac_headless_string));

    if (res == ZPATH_RESULT_NO_ERROR && (!strcmp(guac_headless_string, "1"))) {
        headless = 1;
        EXPORTER_LOG(AL_INFO, "PRA headless enabled, portal file transfer url %s", request->url);
    }

    int ux_display_resolution                   = 0;
    char ux_display_resolution_string[32]       = {0};
    const char *UX_DISPLAY_RESOLUTION_PARAMETER = "dr";

    res = query_string_find(
            request->url,
            &(request->url_parser),
            UX_DISPLAY_RESOLUTION_PARAMETER,
            ux_display_resolution_string,
            sizeof(ux_display_resolution_string));

    if (res == ZPATH_RESULT_NO_ERROR) {
        ux_display_resolution = atoi(ux_display_resolution_string);
    }

    int ux_display_quality             = 0;  /* normal */
    char ux_display_quality_string[32] = {0};
    const char *UX_QUALITY_PARAMETER   = "dq";

    res = query_string_find(
            request->url,
            &(request->url_parser),
            UX_QUALITY_PARAMETER,
            ux_display_quality_string,
            sizeof(ux_display_quality_string));

    if (res == ZPATH_RESULT_NO_ERROR && (!strcmp(ux_display_quality_string, "1"))) {
        ux_display_quality = EXPORTER_UX_DISPLAY_QUALITY_HIGH;  /* high quality */
    }

    viewport_params[0] = guac_width;
    viewport_params[1] = guac_height;
    viewport_params[2] = guac_dpi;
    viewport_params[3] = guac_server_layout;
    viewport_params[4] = headless;
    viewport_params[5] = ux_display_quality;
    viewport_params[6] = ux_display_resolution;

    return ZPATH_RESULT_NO_ERROR;
}

int
exporter_get_zconsole_connect_id_len(struct exporter_request *request)
{
  const char *uri_path = &(request->url[request->url_parser.field_data[UF_PATH].off]);
  const char *zconsole_uri = "/v1/zconsole/";
  size_t offset = 0;
  size_t length = strlen(zconsole_uri);
  if (strncasecmp(uri_path, zconsole_uri, length)) {
      return 0;
  }

  int console_id_len = 0;
  offset = length;
  while (uri_path[offset]) {
      if (uri_path[offset] == '/') {
          break;
    }
    offset++;
    console_id_len++;
  }
  return console_id_len;
}

/*
 * PRA Requests can have /zscope ID in the URL. This function is used to
 * extract the ZScope ID.
 */
int64_t get_scope_id_from_zscope_request(struct exporter_request *request)
{
    const char *uri_path = &(request->url[request->url_parser.field_data[UF_PATH].off]);
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /* Format: https://domain_uri>/v<version>/zscope/<zscope_id>/<api_type_path>/... */
    api_version = exporter_get_user_portal_api_version(request);
    if (api_version == ZPA_USER_PORTAL_API_VERSION_INVALID) {
        return 0;
    }
    offset = 2 + exporter_get_user_portal_api_version_len(api_version);   /* Skip over "/v" + version_len */

    const char *zscope_uri = EXPORTER_SRA_PORTAL_API_URL_ZSCOPE;
    int length = strlen(zscope_uri);
    if (strncasecmp(uri_path + offset, zscope_uri, length)) {
        return 0;
    }
    offset += length + 1;                                               /* Skip over /zscope + / */

    int64_t scope_id = 0;
    const char *scope_id_string = uri_path + offset;
    char *end_ptr = NULL;
    scope_id = strtoll(scope_id_string, &end_ptr, 0);                   /* Extract zscope ID */
    if (end_ptr == NULL) {
        EXPORTER_LOG(AL_DEBUG, "Scope ID obtained from the zscope request: %s %"PRId64, scope_id_string, scope_id);
    }
    return scope_id;
}

/*
 * PRA Requests can have /zconsole ID in the URL. This function is used to
 * extract the ZConsole ID.
 */
int64_t get_console_id_from_first_zconsole_request(struct exporter_request *request)
{
    const char *uri_path = &(request->url[request->url_parser.field_data[UF_PATH].off]);
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /*
     * PRA requests will have /zscope in the URL for Scope Folders in PRA Portal.
     * Format can be either of the 2:
     * https://domain_uri>/v<version>/zscope/<zscope_id>/zconsole/<console_id><api_type_path>/...
     * https://domain_uri>/v<version>/zconsole/<console_id><api_type_path>/...
     */
    api_version = exporter_get_user_portal_api_version(request);
    if (api_version == ZPA_USER_PORTAL_API_VERSION_INVALID) {
        return 0;
    }
    offset = 2 + exporter_get_user_portal_api_version_len(api_version);   /* Skip over "/v" + version_len */

    /* Check for /zscope */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    const char *zconsole_uri = EXPORTER_SRA_PORTAL_API_URI_ZCONSOLE;
    int length = strlen(zconsole_uri);
    if (strncasecmp(uri_path + offset, zconsole_uri, length)) {
        return 0;
    }
    offset += length + 1;

    int64_t console_id = 0;
    const char *console_id_string = uri_path + offset;
    char *end_ptr = NULL;
    console_id = strtoll(console_id_string, &end_ptr, 0);
    if (end_ptr == NULL) {
        EXPORTER_LOG(AL_DEBUG, "Console ID obtained from the first zconsole request: %s %ld", console_id_string, (long)console_id);
    }

    return console_id;
}

/* called when you get /v1/zconsole */
int
is_guac_remote_proxy_request(struct exporter_request *request, int *offset_len)
{
  const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
  const char *zconsole_parameter = "/v1/zconsole/$";
  const char *zconsole_wss_parameter = "/v1/zconsole/websocket-tunnel?zconsole=$";

  if (0 == strncasecmp(&uri_path[0], zconsole_parameter, strlen(zconsole_parameter))) {
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id in %s", request->url);
      *offset_len = strlen("/v1/zconsole/");
      return 1;
  } else if (0 == strncasecmp(&uri_path[0], zconsole_wss_parameter, strlen(zconsole_wss_parameter))) {
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id in %s", request->url);
      *offset_len = strlen("/v1/zconsole/websocket-tunnel?zconsole=");
      return 1;
  }
  return 0;
}

/*
 * This routine is used to identify if the request is a procotored request
 */
int
exporter_is_pra_shared_session_request(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const char *zconsole_parameter = "/v1/zconsole/$";
    const char *console_info_zconsole_parameter = "/v1/console_info?zconsole=$";
    const char *wss_zconsole_parameter = "/v1/zconsole/websocket-tunnel?zconsole=$";

    /* TODO DTA-PRA : Improve this logic */
    if (0 == strncasecmp(&uri_path[0], zconsole_parameter, strlen(zconsole_parameter))) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id in %s", request->url);
        return 1;
    } else if (0 == strncasecmp(&uri_path[0], console_info_zconsole_parameter, strlen(console_info_zconsole_parameter))) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id in %s", request->url);
        return 1;
    } else if (0 == strncasecmp(&uri_path[0], wss_zconsole_parameter, strlen(wss_zconsole_parameter))) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id in %s", request->url);
        return 1;
    }
    return 0;
}

/*
 * This routine is used to extract the shared session id from request
 */
int
exporter_extract_shared_session_id_from_all_request_type(struct exporter_request *request, char *session_id, size_t session_id_size)
{
    int offset = 0;
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;
    int ret = ZPATH_RESULT_NO_ERROR;

    /*
     * /console_info and /websocket-tunnel passes session ID as a query param
     */
    if ((exporter_get_guac_api_type(request) == ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL) ||
            (exporter_get_user_portal_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO)) {

        ret = extract_sessionid_from_request(request, session_id, session_id_size);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR,
                    "%s: Extracting session id failed for customer gid = %"PRId64", request %s", request->name,
                    customer_gid, request->url);
            return ret;
        }
    } else {
        /* TODO DTA-PRA : Find another way to get the session ID for index.html call */
        if(is_guac_remote_proxy_request(request, &offset)) {
            ret = extract_session_string_from_request(request, session_id, offset);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR,
                        "%s: Extracting session id failed for customer gid = %"PRId64", request %s", request->name,
                        customer_gid, request->url);
                return ret;
            }
        } else {
            return ZPATH_RESULT_ERR;
        }
    }

    return ret;
}

int
extract_session_string_from_request(struct exporter_request *request, char *session_string,
                                    int offset)
{
  const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
  int session_len = 0;

  while(uri_path[offset] && uri_path[offset] != '/' && uri_path[offset] != '&') {
      session_string[session_len] = uri_path[offset];
      offset++;
      session_len++;
  }
  EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Extracted session ID from request %s", session_string);
  return ZPATH_RESULT_NO_ERROR;
}

/* Helps with policy evaluation for all PRA requests.
 * exporter_request_policy_state needs application information
 * (not portal info) to apply policy rules effectively
 */
int sra_update_application_domain(struct exporter_request *request)
{
    int64_t console_id = 0;
    int ret = ZPATH_RESULT_NO_ERROR;
    char session_string[64] = {0};
    int offset = 0;

    /* If application domain is already updated just return */
    if (request->sra_host_fqdn != NULL) return ret;

    if (is_guac_remote_proxy_request(request, &offset)) {
        ret = extract_session_string_from_request(request, session_string, offset);
      if (ret != ZPATH_RESULT_NO_ERROR) {
	      return ret;
      }
      ret = resolve_console_id_from_remote_guac_session_string(request, session_string);
      if (ret == ZPN_RESULT_ASYNCHRONOUS) {
	      return ret;
      } else if (ret != ZPN_RESULT_NO_ERROR){
                 EXPORTER_LOG(AL_INFO, "%s: sra_update_application_domain: failed to get the console information with error = %s, console_id = %"PRId64,
	               request->name, zpath_result_string(ret), console_id);
      }
      console_id = request->pra_console_id;
    } else {
      if (exporter_get_guac_api_type(request) == ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL) {
          console_id = get_console_id_from_tunnel_request(request);
      } else if (exporter_get_user_portal_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO) {
          console_id = get_console_id_from_zconsole_param(request);
      } else {
          console_id = get_console_id_from_first_zconsole_request(request);
      }
    }
    if (console_id <= 0) return ret;

    if (check_query_param_djb(request)) {
        return ret;
    }

    ret = zpn_sra_console_get_info(request, console_id);
    if (ret == ZPN_RESULT_ASYNCHRONOUS) {
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: failed to get the console information with error = %s, console_id = %"PRId64,
                     request->name, zpath_result_string(ret), console_id);
    }
    return ret;
}

int remove_bevs_for_proxy_request(struct exporter_request *request)
{
    struct exporter_request *orig_request = NULL;
    struct zpn_exporter_bev_elem *elem = NULL;

    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    orig_request = zhash_table_lookup(global_exporter.proxy_conn_id, request->guac_proctored_session_id, strnlen(request->guac_proctored_session_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    if (!orig_request || !orig_request->guac_info) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", request->guac_proctored_session_id);
        return ZPATH_RESULT_ERR;
    }
    ZPATH_MUTEX_LOCK(&(orig_request->lock), __FILE__, __LINE__);
    ZLIST_FOREACH(elem, &(orig_request->guac_info->bevs_list), list) {
        if (!(elem && (elem->bev) && *(elem->bev))) {
            continue;
        }
        if (*elem->bev == request->guac_info->gd_remote_server_proxy.local_guacd_bev) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Removed bev %p from bevs_list for %s", elem->bev, orig_request->name);
            ZLIST_REMOVE(elem, list);
            *(elem->bev) = NULL;
            EXPORTER_FREE(elem);
            break;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(orig_request->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

/* used by session control eject participant feature in single exporter case */
void exporter_free_bevs_list_by_user(struct exporter_request *request, char *user)
{
    int found = 0;
    struct zpn_exporter_bev_elem *elem = NULL;

    if (!request || !request->guac_info) {
        return;
    }

    /* Send disconnects to the bevs associated with the proctored sessions for the target user */
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    ZLIST_FOREACH(elem, &(request->guac_info->bevs_list), list) {
        const char *user_name = exporter_user_portal_request_state_get_name(elem->proxy_request->portal_info);
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC bev users %s", user);
        if (user_name && (0 == strcmp(user_name, user))) {
            ZLIST_REMOVE(elem, list);
            found = 1;
            break;
        }
    }
    if (found && elem) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC disconnect %s, buffervent %p from bevs_list, request %p", user, elem->bev, request);
        zlibevent_bufferevent_free(*(elem->bev));
        *(elem->bev) = NULL;
        EXPORTER_FREE(elem);
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
}

void exporter_free_bevs_list(struct exporter_request *request)
{
    struct zpn_exporter_bev_elem *elem = NULL;

    if (!request->guac_info) {
        return;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (request->guac_info && !(request->guac_info->bevs_list.zlh_first))
    {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %p exporter_free_bevs_list: bev_list is empty", request);
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        return;
    }
    while ((elem = ZLIST_FIRST(&(request->guac_info->bevs_list)))) {
        if (elem) {
            ZLIST_REMOVE(elem, list);
            if (elem->bev && *(elem->bev)) {
                /* Send disconnects to the bevs associated with the proctored sessions  */
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Sending disconnect for buffervent %p from bevs_list for request %p", elem->bev, request);
                zlibevent_bufferevent_free(*(elem->bev));
                *(elem->bev) = NULL;
            }
            EXPORTER_FREE(elem);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
}

void exporter_free_mtunnels_list(struct exporter_request *request)
{
    struct zpn_exporter_mtunnel_elem *elem = NULL;

    if (!request->guac_info) {
        return;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    while ((elem = ZLIST_FIRST(&(request->guac_info->mtunnels_list)))) {
        ZLIST_REMOVE(elem, list);
        /* Send disconnects to the mconn's associated with the mtunnels */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Sending disconnect for mtunnel %p from mtunnels_list for %s", elem->mtunnel, request->name);
        int res = exporter_guac_mconn_terminate(elem->mtunnel);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: mtunnel_id = %s  Terminating exporter session proctoring mconn returned %s",
                    request->name, (char *)elem->mtunnel, zpn_result_string(res));
        }
        EXPORTER_FREE(elem);
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
}


void exporter_request_free_guac_info(struct exporter_request *request)
{
    if (request->guac_info->creds) {
        EXPORTER_FREE(request->guac_info->creds);
        request->guac_info->creds = NULL;
    }
    if (request->guac_info->websocket_input) {
        evbuffer_free(request->guac_info->websocket_input);
        request->guac_info->websocket_input = NULL;
    }
    if (request->guac_info->stream_response) {
        evbuffer_free(request->guac_info->stream_response);
        request->guac_info->stream_response = NULL;
    }
    if (request->guac_info->decrypted_buffer) {
        evbuffer_free(request->guac_info->decrypted_buffer);
        request->guac_info->decrypted_buffer = NULL;
    }
    if (request->guac_info->policy_name) {
        EXPORTER_FREE(request->guac_info->policy_name);
        request->guac_info->policy_name = NULL;
    }

    exporter_guac_free_stream(request);

    exporter_stream_create_del_timeout(request);

    exporter_zia_scan_del_timeout(request);

    exporter_guac_proxy_sandbox_info_free(request);

    exporter_request_free_file_log(request);

    exporter_cleanup_credentials(request);

    guac_parser_destroy(&(request->guac_info->stream_parser));
    guac_parser_destroy(&(request->guac_info->guac_parser));

    if (request->guac_info->gd_proxy.sftp_exporter_request != NULL) {
        int res = sftp_request_destroy(request->guac_info->gd_proxy.sftp_exporter_request);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_ASSERT_SOFT((request->guac_info->gd_proxy.sftp_exporter_request == NULL),
                    "%s: Memory Leak for SFTP request. res=%s", request->name, zpath_result_string(res));
            EXPORTER_LOG(AL_ERROR, "%s, Leaking mem. Should never reach here....%s", request->name, zpath_result_string(res));
        } else {
            request->guac_info->gd_proxy.sftp_exporter_request = NULL;
        }
    }

    /* reorder freeing of the guacd proxy server */
    if (request->is_proxy_conn && request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s gd_remote_proxy_server_free release memory for generic listener and generic server", request->name);
        fohh_generic_server_free(request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server);
        request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server = NULL;
    }

    if (request->guac_info->gd_proxy.guacd_child_proxy_server) {
        gd_child_proxy_generic_server_free(request);
    }

    exporter_diagostic_info_cleanup(request);

    EXPORTER_FREE(request->guac_info);
    request->guac_info = NULL;
}

void exporter_request_free_file_log(struct exporter_request *request)
{
    if (!request || !request->guac_info) {
        return;
    }
    if (request->guac_info->file_log.file_name) {
        EXPORTER_FREE(request->guac_info->file_log.file_name);
        request->guac_info->file_log.file_name = NULL;
    }
    if (request->guac_info->file_log.file_type) {
        EXPORTER_FREE(request->guac_info->file_log.file_type);
        request->guac_info->file_log.file_type = NULL;
    }
    if (request->guac_info->file_log.file_md5) {
        EXPORTER_FREE(request->guac_info->file_log.file_md5);
        request->guac_info->file_log.file_md5 = NULL;
    }
    if (request->guac_info->file_log.status) {
        EXPORTER_FREE(request->guac_info->file_log.status);
        request->guac_info->file_log.status = NULL;
    }
    if (request->guac_info->file_log.inspection_verdict) {
        EXPORTER_FREE(request->guac_info->file_log.inspection_verdict);
        request->guac_info->file_log.inspection_verdict = NULL;
    }
}

/* Approval policy check to determine if this request has access and approval is active */
int64_t
exporter_request_policy_state_check_approval(struct exporter_request *request, char *approval_info, size_t approval_info_len)
{
    int res = ZPATH_RESULT_NO_ERROR;
    const char *domain = request->sra_host_fqdn;
    enum zpe_access_approval_status approval_status = zpe_access_approval_none;
    int64_t approval_id = 0;
    enum zpe_access_action matched_action = zpe_access_action_deny;

    if (!domain || !request->app_id) return res;

    res = zpn_broker_policy_locked_link_policy_pra_check(request->scope_gid,
                                                     request->attr,
                                                     request->conn->exporter_domain->is_ot,
                                                     request->policy_state->idp_gid,
                                                     zpe_policy_type_access,
                                                     domain,
                                                     request->policy_state->general_state_hash,
                                                     request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                     request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                     NULL, // bypass approval
                                                     &approval_status,
                                                     NULL,
                                                     request->sra_host_port,
                                                     &matched_action,
                                                     NULL,
                                                     NULL,
                                                     NULL,
                                                     exporter_request_wally_callback,
                                                     request,
                                                     NULL,
                                                     0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    }
    /* Stub out approval processing in the above call using NULL approval_id
     * to replace with email based processing for Exporters */
    if ((matched_action == zpe_access_action_approval_required)
        && request->policy_state->user_email) {
        /* search based on user email for Exporters */
        res = zpn_policy_check_approval_based_on_user_email(request->conn->exporter_domain->customer_gid,
                                                            request->scope_gid,
                                                            request->policy_state->user_email,
                                                            request->app_id,
                                                            exporter_request_wally_callback,
                                                            request,
                                                            0,
                                                            &approval_id,
                                                            NULL,
                                                            &approval_status,
                                                            &matched_action);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            return res;
        }
    }

    if (matched_action == zpe_access_action_deny) {
        if (approval_id) {
            struct zpn_approval *approval = NULL;
            res = zpn_approval_get_by_id(approval_id, request->scope_gid, &approval, NULL, NULL, 0);
            if (res == ZPN_RESULT_NO_ERROR) {
                if (approval->working_hours) {
                    snprintf(approval_info, approval_info_len, "{\"startTime\":%"PRId64", \"endTime\":%"PRId64",\"approval_working_hours\":%s}",
                             approval->approval_start_time, approval->approval_end_time, approval->working_hours);
                } else {
                    snprintf(approval_info, approval_info_len, "{\"startTime\":%"PRId64", \"endTime\":%"PRId64"}",
                             approval->approval_start_time, approval->approval_end_time);
               }
               request->approval_id = approval_id;
               return ZPATH_RESULT_NO_ERROR;
            }
        }
    }

    EXPORTER_LOG(AL_DEBUG, "Approval policy check results: matched_action = %s, approval_status = %s",
                 zpe_access_action_string(matched_action), zpe_access_approval_status_string(approval_status));

    request->approval_id = approval_id;
    return ZPATH_RESULT_ERR;
}

static void exporter_ux_get_resolution(uint32_t ux_resolution, int *h, int *w)
{
    switch(ux_resolution) {
        case    EXPORTER_UX_RESOLUTION_640x480:
            *h = 640; *w = 480;
        break;
        case    EXPORTER_UX_RESOLUTION_800x600:
            *h = 800; *w = 600;
        break;
        case    EXPORTER_UX_RESOLUTION_1040x668:
            *h = 1040; *w = 668;
        break;
        case    EXPORTER_UX_RESOLUTION_1048x768:
            *h = 1048; *w = 768;
        break;
        case    EXPORTER_UX_RESOLUTION_1280x1024:
            *h = 1280; *w = 1024;
        break;
        case    EXPORTER_UX_RESOLUTION_1400x1050:
            *h = 1400; *w = 1050;
        break;
        case    EXPORTER_UX_RESOLUTION_1600x900:
            *h = 1600; *w = 900;
        break;
        case    EXPORTER_UX_RESOLUTION_1920x1080:
            *h = 1920; *w = 1080 ;
        break;
        default:
        break;
    }
}

/* Update resoultion if UX-improvements feature is enabled */
static void exporter_ux_update_viewport(struct exporter_request *request)
{
    int height = 0;
    int  width = 0;

    if (!request || !request->guac_info) {
        return;
    }

    if (is_pra_display_quality_disabled(request->conn->exporter_domain->customer_gid)) {
        return;
    }

    /* use fit to bounds */
    if (request->guac_info->ux_resolution == EXPORTER_UX_RESOLUTION_DEFAULT) {
        return;
    }

    /* based on the user-selection */
    exporter_ux_get_resolution(request->guac_info->ux_resolution, &height, &width);

    request->guac_info->viewport_width  = height;
    request->guac_info->viewport_height = width;
}

static int guac_handle_tunnel_websocket_api(struct exporter_request *request, const char *assertion_key, const char *assertion, int is_no_auth)
{
    int ret = -1;
    int64_t console_id = 0;
    enum zpe_access_action matched_action = zpe_access_action_deny;
    int64_t matched_rule_id = 0;
    int res = 0;
    if (request->req_method != HTTP_GET) {
        ret = ZPATH_RESULT_BAD_ARGUMENT;
        EXPORTER_LOG(AL_ERROR, "%s: guac_handle_tunnel_websocket_api: ret=%s; guac /api/websocket-tunnel invoked with wrong method %s", request->name, zpath_result_string(ret), http_method_names[request->req_method]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    } else {
        if (request->sra_host_fqdn == NULL) {
	        console_id = get_console_id_from_tunnel_request(request);
	        if (console_id <= 0) {
	            EXPORTER_LOG(AL_ERROR, "%s: guac_handle_tunnel_websocket_api: failed to get the console information from wally ret=%s console_id = %"PRId64,
		                    request->name, zpath_result_string(ret), console_id);
	            return ZPATH_RESULT_ERR;

	        }

	        ret = zpn_sra_console_get_info(request, console_id);
	        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                return ret;
            } else if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: sra_update_application_domain: failed to get the console information with error = %s, console_id = %"PRId64,
                             request->name, zpath_result_string(ret), console_id);
            }
        }

        if (is_vnc_disabled(request->conn->exporter_domain->customer_gid) &&
                !strcmp(request->sra_host_protocol, VNC_PROTOCOL)) {
            EXPORTER_LOG(AL_ERROR, "%s: VNC feature flag for sra_application is disabled", request->name);
            return ZPATH_RESULT_ERR;
        }

        if (is_realvnc_disabled(request->conn->exporter_domain->customer_gid) &&
                !strcmp(request->sra_host_protocol, REALVNC_PROTOCOL)) {
            EXPORTER_LOG(AL_ERROR, "%s: REALVNC feature flag for sra_application is disabled", request->name);
            return ZPATH_RESULT_ERR;
        }

        // Do not allow non-upgrade request to proceed further in this code. This guarantees that no spurious mtunnels are created.
        if (!request->http_upgrade) {
            ret = ZPATH_RESULT_BAD_ARGUMENT;
            EXPORTER_LOG(AL_ERROR, "%s: guac_handle_tunnel_websocket_api: ret=%s; guac /api/websocket-tunnel invoked without Upgrade header", request->name, zpath_result_string(ret));
            return -1;
        }

        int params[7];
        get_viewport_params_from_tunnel_request(request, params);

        if (!request->guac_info) {
            request->guac_info = EXPORTER_CALLOC(sizeof(struct exporter_guac_request_info));
        }
        if (!request->guac_info) {
            EXPORTER_LOG(AL_ERROR, "Could not allocate guac_info");
            return ENOMEM;
        }

        request->guac_info->created_ts = epoch_s();
        request->guac_info->is_header = 1;
        request->guac_info->is_websocket = 1;
        request->guac_info->is_guacd_ready = 0;
        request->guac_info->rdp_dir_list_cnt = 0;
        request->guac_info->ref_count = 0;
        request->guac_info->capabilities_policy_bitmap = 0;
        request->guac_info->viewport_width = params[0];
        request->guac_info->viewport_height = params[1];
        request->guac_info->viewport_dpi = params[2];
        request->guac_info->server_layout = params[3];
        request->guac_info->is_headless = params[4];
        request->guac_info->ux_quality  = params[5];
        request->guac_info->ux_resolution = params[6];
        request->guac_info->async_cb = exporter_guac_async_callback_on_thread;
        request->guac_info->creds = NULL;
        request->guac_info->is_stream_timeout_triggered = 0;
        request->http_request_generated = 1;
        request->guac_info->file_scan.is_zia_scan_req = 0;
        request->guac_info->file_scan.upload_file = NULL;
        request->guac_info->file_scan.upload_file_fd = -1;
        request->guac_info->file_info.upload_file_size = 0;
        request->guac_info->file_scan.zia_cloud_domain= NULL;
        request->guac_info->file_scan.zia_username = NULL;
        request->guac_info->file_scan.zia_password = NULL;
        request->guac_info->file_scan.zia_cloud_service_api_key = NULL;
        request->guac_info->file_scan.zia_sandbox_api_token = NULL;
        request->guac_info->file_scan.zia_auth_sess_id = NULL;
        request->guac_info->file_scan.file_md5 = NULL;
        request->guac_info->policy_name = NULL;
        request->guac_info->capabilities_policy_id = 0;
        request->guac_info->file_log.file_name = NULL;
        request->guac_info->file_log.file_type = NULL;
        request->guac_info->file_log.file_md5 = NULL;
        request->guac_info->file_log.inspection_verdict = NULL;
        request->guac_info->file_log.file_inspected = 0;
        request->guac_info->file_log.status = NULL;
        request->guac_info->file_log.start_ts = 0;
        request->guac_info->file_log.end_ts = 0;
        request->guac_info->session_start_time = 0;
        request->guac_info->file_log.inspection_start_ts = 0;
        request->guac_info->file_log.inspection_end_ts = 0;
        request->guac_info->shared_user_list_lock = ZPATH_MUTEX_INIT;

        exporter_ux_update_viewport(request);

        guac_parser_init(&(request->guac_info->guac_parser), request->conn);
        guac_parser_init(&(request->guac_info->stream_parser), request->conn);

        request->guac_info->exporter_credentials.user_name = NULL;
        request->guac_info->exporter_credentials.user_domain = NULL;
        request->guac_info->exporter_credentials.password = NULL;
        request->guac_info->exporter_credentials.passphrase = NULL;
        request->guac_info->exporter_credentials.private_key = NULL;
        request->guac_info->exporter_credentials.decrypted_password = NULL;
        request->guac_info->exporter_credentials.decrypted_private_key = NULL;
        request->guac_info->exporter_credentials.decrypted_passphrase = NULL;

        ZLIST_INIT(&(request->guac_info->bevs_list));
        ZLIST_INIT(&(request->guac_info->mtunnels_list));

        if (request->new_header) {
            evbuffer_free(request->new_header);
        }
        request->new_header = NULL;

        request->conn->sni_orig = request->conn->sni;
        request->conn->sni = request->sra_host_fqdn;
        request->conn->exporter_domain->application_port_he = request->sra_host_port;
        request->conn->exporter_domain->is_backend_tls = 0;
        request->conn->exporter_domain->ignore_trust_verification = 0;

        exporter_guac_proxy_browser_push_notification_init(request);

        exporter_guac_alloc_stream(&request->guac_info->guac_in_stream[file_transfer]);
        exporter_guac_alloc_stream(&request->guac_info->guac_in_stream[clipboard]);
        exporter_guac_alloc_stream(&request->guac_info->guac_out_stream[file_transfer]);
        exporter_guac_alloc_stream(&request->guac_info->guac_out_stream[clipboard]);

        /* Find or create (if not found) m-tunnel for this request
         * ZPA mtunnel gets reused when user refreshes browser
         * To prevent reuse of the ZPA mtunnel in zpn_fohh_client_export_mt_get
         * set nailed_up_mt to 1. exporter_conn 'connection_id' will be used to search for mtunnels
         */
        request->conn->nailed_up_mt = 1;
        if (!request->is_proxy_conn && !request->guac_info->is_headless) {
	        exporter_guac_api_stats_increment(GUACD_SERVICE_INC_ACTIVE_SESSION_COUNT);
        }

        if (console_id == 0) {
            if (request->is_proxy_conn) {
                console_id = request->pra_console_id;
            } else {
                console_id = get_console_id_from_tunnel_request(request);
            }
        }

        /* For DJB, credentials are fetched from privileged desktop service */
        if (request->is_djb) {
            request->guac_info->is_pra_interactive_auth_disabled = 1;
        }

        if (!request->is_djb && !is_privileged_policy_disabled(request->conn->exporter_domain->customer_gid) && console_id
            && g_exporter_ot_mode) {
            res = zpn_broker_policy_evaluate(request->scope_gid,
                    console_id,
                    zpe_policy_type_cred_map,
                    request->policy_state->general_state_hash,
                    request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                    request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                    &matched_action,
                    &matched_rule_id);
            if ((res == ZPATH_RESULT_NO_ERROR) && matched_rule_id && matched_action == zpe_access_action_inject_credentials) {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: CRED Map policy rule %"PRId64" is matched for console id %"PRId64"", request->name, matched_rule_id, console_id);
                request->guac_info->is_pra_interactive_auth_disabled = 1;
                EXPORTER_GUAC_API_STATS_INC(privileged_credentials_policy_accept, 1);
            } else {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: No matching CRED Map policy rule is found for console id %"PRId64"", request->name, console_id);
                EXPORTER_GUAC_API_STATS_INC(privileged_credentials_policy_reject, 1);
            }
        }
        if (matched_rule_id) {
            request->guac_info->cred_map_rule_id = matched_rule_id;
        }
    }

    size_t hdr_len;
    ret = exporter_request_respond_with_upgrade(request, 200, &hdr_len);

    EXPORTER_GUAC_API_STATS_INC_BY_RET_CODE(tunnel_connect, 1, ret);

    return ret;
}

int guacd_subcomponent_active_session_count(void){
    return s_guac_api_stats.guacd_service_active_session_count;
}

/* For Large PRA Portals - concurrent access will be restricted */
void decrement_large_portal_access_count(struct exporter_request *request)
{
    if (request->is_large_portal_request) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Decrement Large Portal Access Count", request->name);
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        exporter_guac_api_stats_decrement(LARGE_PORTAL_ACCESS_ACTIVE_COUNT);
        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        request->is_large_portal_request = 0;
    }
}

int get_large_portal_access_active_count(void) {
    return s_guac_api_stats.large_portal_access_active_count;
}

void exporter_guac_api_proto_stats_decrement(struct exporter_request* request)
{
    if (!request) {
        return;
    }
    const char *protocol = request->sra_host_protocol;
    if (protocol == NULL) {
        return;
    }
    if (strncasecmp(protocol, "RDP", 3) == 0) {
        if (s_guac_api_stats.guacd_service_active_rdp_session_count == 0) return;
        EXPORTER_GUAC_API_STATS_DEC(guacd_service_active_rdp_session_count, 1);
    } else if (strncasecmp(protocol, "VNC", 3) == 0) {
        if (s_guac_api_stats.guacd_service_active_vnc_session_count == 0) return;
        EXPORTER_GUAC_API_STATS_DEC(guacd_service_active_vnc_session_count, 1);
    } else if (strncasecmp(protocol, "REALVNC", 7) == 0) {
        if (s_guac_api_stats.guacd_service_active_realvnc_session_count == 0) return;
        EXPORTER_GUAC_API_STATS_DEC(guacd_service_active_realvnc_session_count, 1);
    } else if (strncasecmp(protocol, "SSH", 3) == 0) {
        if (s_guac_api_stats.guacd_service_active_ssh_session_count == 0) return;
        EXPORTER_GUAC_API_STATS_DEC(guacd_service_active_ssh_session_count, 1);
    }
}

void exporter_guac_api_stats_decrement(enum guac_stats_counter counter)
{
    switch(counter) {
        case GUACD_SERVICE_DEC_ACTIVE_SESSION_COUNT:
             assert(s_guac_api_stats.guacd_service_active_session_count != 0);
             EXPORTER_GUAC_API_STATS_DEC(guacd_service_active_session_count, 1);
             break;
        case LARGE_PORTAL_ACCESS_ACTIVE_COUNT:
             assert(s_guac_api_stats.large_portal_access_active_count != 0);
             EXPORTER_GUAC_API_STATS_DEC(large_portal_access_active_count, 1);
             break;
        default:
             break;
    }
}

void exporter_guac_api_stats_increment(enum guac_stats_counter counter)
{
    switch(counter) {
        case GUAC_TUNNEL_DISCONNECT_RECV:
            EXPORTER_GUAC_API_STATS_INC(tunnel_disconnect_received, 1);
            break;
        case GUAC_WS_REQUEST_DESTROY:
             EXPORTER_GUAC_API_STATS_INC(websocket_request_destroy, 1);
             break;
        case GUAC_WS_PARSER_ERROR:
             EXPORTER_GUAC_API_STATS_INC(websocket_parser_error, 1);
             break;
        case GUAC_RESPONSE_ERROR:
             EXPORTER_GUAC_API_STATS_INC(guac_response_error, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload, 1);
             break;
        case GUAC_FILE_TRANSFER_DOWNLOAD:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_download, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD_FAILURE:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload_failure, 1);
             break;
        case GUAC_FILE_TRANSFER_DOWNLOAD_FAILURE:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_download_failure, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD_INSPECT:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload_inspect, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD_INSPECT_DENY:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload_inspect_deny, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD_INSPECT_FAIL:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload_inspect_fail, 1);
             break;
        case GUAC_FILE_TRANSFER_CRYPTO_SERVICE_FAIL:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_cryptoservice_fail, 1);
             break;
        case GUAC_FILE_TRANSFER_UPLOAD_POLICY_DENY:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_upload_policy_deny, 1);
             break;
        case GUAC_FILE_TRANSFER_DOWNLOAD_POLICY_DENY:
             EXPORTER_GUAC_API_STATS_INC(file_transfer_download_policy_deny, 1);
             break;
        case GUAC_CLIPBOARD_COPY:
             EXPORTER_GUAC_API_STATS_INC(clipboard_copy, 1);
             break;
        case GUAC_CLIPBOARD_PASTE:
             EXPORTER_GUAC_API_STATS_INC(clipboard_paste, 1);
             break;
        case GUAC_CLIPBOARD_COPY_EXCEED_LIMIT:
             EXPORTER_GUAC_API_STATS_INC(clipboard_copy_exceed_limit, 1);
             break;
        case GUAC_CLIPBOARD_PASTE_EXCEED_LIMIT:
             EXPORTER_GUAC_API_STATS_INC(clipboard_paste_exceed_limit, 1);
             break;
        case GUAC_CLIPBOARD_COPY_POLICY_DENY:
             EXPORTER_GUAC_API_STATS_INC(clipboard_copy_policy_deny, 1);
             break;
        case GUAC_CLIPBOARD_PASTE_POLICY_DENY:
             EXPORTER_GUAC_API_STATS_INC(clipboard_paste_policy_deny, 1);
             break;
        case GUACD_SERVICE_HANDSHAKE_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_handshake_failed, 1);
             break;
        case GUACD_SERVICE_GD_PROXY_LISTENER_CREATE_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_gd_proxy_listener_create_failed, 1);
             break;
        case GUACD_SERVICE_CONNECT_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_connect_failed, 1);
             break;
        case GUACD_SERVICE_MT_CONSUME_ON_REQUEST_THREAD_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_mt_consume_on_request_thread_failed, 1);
             break;
        case GUACD_SERVICE_CHILD_PROXY_CONNECTION_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_child_proxy_connection_failed, 1);
             break;
        case GUACD_SERVICE_STICHING_CONNECTION_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_stiching_connection_failed, 1);
             break;
        case GUACD_SERVICE_CHILD_PROXY_SERVER_CREATE_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_child_proxy_server_create_failed, 1);
             break;
        case GUACD_SERVICE_CHILD_PROXY_SERVER_LISTEN_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_child_proxy_server_listen_failed, 1);
             break;
        case GUACD_SERVICE_CHILD_PROXY_SERVER_ACCEPT_FAILED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_child_proxy_server_accept_failed, 1);
             break;
        case GUACD_SERVICE_INC_ACTIVE_SESSION_COUNT:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_active_session_count, 1);
             break;
        case GUACD_SERVICE_MTUNNEL_DROPS_CPU_LIMIT_REACHED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_mtunnel_drops_cpu_limit_reached, 1);
             break;
        case GUACD_SERVICE_MTUNNEL_DROPS_MEM_LIMIT_REACHED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_mtunnel_drops_mem_limit_reached, 1);
             break;
        case GUACD_SERVICE_MTUNNEL_DROPS_MAX_SESSIONS_REACHED:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_mtunnel_drops_max_sessions_reached, 1);
             break;
        case GUACD_PARSER_ERROR_500:
             EXPORTER_GUAC_API_STATS_INC(guacd_parser_error_500, 1);
             break;
        case GUACD_SERVICE_RDP_SESSION_COUNT:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_total_rdp_session_count, 1);
             EXPORTER_GUAC_API_STATS_INC(guacd_service_active_rdp_session_count, 1);
             break;
        case GUACD_SERVICE_VNC_SESSION_COUNT:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_total_vnc_session_count, 1);
             EXPORTER_GUAC_API_STATS_INC(guacd_service_active_vnc_session_count, 1);
             break;
        case GUACD_SERVICE_REALVNC_SESSION_COUNT:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_total_realvnc_session_count, 1);
             EXPORTER_GUAC_API_STATS_INC(guacd_service_active_realvnc_session_count, 1);
             break;
        case GUACD_SERVICE_SSH_SESSION_COUNT:
             EXPORTER_GUAC_API_STATS_INC(guacd_service_total_ssh_session_count, 1);
             EXPORTER_GUAC_API_STATS_INC(guacd_service_active_ssh_session_count, 1);
             break;
        case LARGE_PORTAL_ACCESS_ACTIVE_COUNT:
             EXPORTER_GUAC_API_STATS_INC(large_portal_access_active_count, 1);
             break;
        case LARGE_PORTAL_ACCESS_BLOCKED_COUNT:
             EXPORTER_GUAC_API_STATS_INC(large_portal_access_blocked_count, 1);
             break;
        case CREDENTIAL_POOL_CONN_COUNT:
             EXPORTER_GUAC_API_STATS_INC(credential_pool_conn_count, 1);
             break;
        case CREDENTIAL_POOL_CONN_FAILED:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_conn_failed, 1);
            break;
        case CREDENTIAL_POOL_DELETE_API_FAILED:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_delete_api_failed, 1);
            break;
        case CREDENTIAL_POOL_INTERNAL_CALLBACK_FAILURE:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_internal_callback_failure, 1);
            break;
        case CREDENTIAL_POOL_CRED_ID_FOUND:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_cred_id_found, 1);
            break;
        case CREDENTIAL_POOL_ERR_NO_CRED_AVAILABLE:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_err_no_cred_available, 1);
            break;
        case CREDENTIAL_POOL_ERR_SERVICE_UNAVAILABLE:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_err_service_unavailable, 1);
            break;
        case CREDENTIAL_POOL_ERR_CRED_NOT_FOUND_IN_DB:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_err_cred_not_found_in_db, 1);
            break;
        case CREDENTIAL_POOL_REQUEST_STATUS_CHECK_FAILURE:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_request_status_check_failure, 1);
            break;
        case CREDENTIAL_POOL_ERR_JSON_RESPONSE:
            EXPORTER_GUAC_API_STATS_INC(credential_pool_err_json_response, 1);
            break;
        case CREDENTIAL_THREAD_CPS_CONN_FAILED:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_conn_failed, 1);
            break;
        case CREDENTIAL_THREAD_NULL_DATA:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_null_data, 1);
            break;
        case CREDENTIAL_THREAD_CPS_REQUEST_ERROR:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_request_error, 1);
            break;
        case CREDENTIAL_THREAD_CPS_REQUEST_RETRY_COUNT:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_request_retry_count, 1);
            break;
        case CREDENTIAL_THREAD_CPS_TASK_ENQ_FAIL:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_task_enq_fail, 1);
            break;
        case CREDENTIAL_THREAD_CPS_REQUEST_MAXRETRY_FAIL:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_request_maxretry_fail, 1);
            break;
        case CREDENTIAL_THREAD_CPS_DEL_ALL_RETRY_COUNT:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_del_all_retry_count, 1);
            break;
        case CREDENTIAL_THREAD_CPS_DEL_RETRY_COUNT:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_del_retry_count, 1);
            break;
        case CREDENTIAL_THREAD_CPS_GET_RETRY_COUNT:
            EXPORTER_GUAC_API_STATS_INC(credential_thread_cps_get_retry_count, 1);
            break;
        case PRA_HEALTH_CHECK_503_COUNT:
            EXPORTER_GUAC_API_STATS_INC(pra_health_check_503_count, 1);
            break;
        case DJB_LAUNCH_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_launch_count, 1);
            break;
        case DJB_VALIDATION_FAILURE_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_validation_failure_count, 1);
            break;
        case DJB_DELETE_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_delete_count, 1);
            break;
        case DJB_CONNECT_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_connect_count, 1);
            break;
        case DJB_PORTAL_POLICY_FAIL_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_portal_policy_fail_count, 1);
            break;
        case DJB_SERVICE_RESP_FAIL_COUNT:
            EXPORTER_GUAC_API_STATS_INC(djb_service_resp_fail_count, 1);
            break;
        default:
             break;
    }
}

/*
 * Free API response
 */
void exporter_guac_api_free_response(const struct exporter_request *request, void *resp)
{
    const enum zpa_guac_api_type api_type = request->guac_api_type;
    const char *api_name = get_guac_api_name(api_type);

    if (resp != NULL) {

        EXPORTER_LOG(AL_ERROR, "%s- Request to free unexpected API response type: %s", request->name, api_name);

        /* Finally free the response structure itself */
        EXPORTER_FREE(resp);
    } else {
        EXPORTER_LOG(AL_NOTICE, "%s - %s - Null response", request->name, api_name);
    }
}

/* ----------------------------------------------------------*
 * Guac API Request URI Handlers                           *
 * ----------------------------------------------------------*/

/*
 * Handle the websocket API
 */
static int guac_handle_websocket_tunnel_api(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth)
{
    int ret = ZPN_RESULT_ASYNCHRONOUS;

    if (!is_pra_session_monitoring_disabled(request->conn->exporter_domain->customer_gid) &&
        is_wss_sess_sharing_request(request)) {
        EXPORTER_LOG(AL_DEBUG, "websocket tunnel request is for session proctoring");
        if (is_primary_exporter_for_sess_proctoring_req(request)) {
            EXPORTER_LOG(AL_DEBUG, "wss request for session proctoring landed on proxy exporter");
            request->is_guac_sess_primary_exporter = 1;
        }
        EXPORTER_LOG(AL_DEBUG, "wss request for session proctoring landed on primary exporter");
    }

    ret = guac_handle_tunnel_websocket_api(request, auth_cookie, assertion, is_no_auth);

    if (ret != ZPATH_RESULT_NO_ERROR && ret != ZPATH_RESULT_ASYNCHRONOUS) {
        char error_reason[GUAC_ERROR_REASON_SIZE] = "";
        const char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
        const char *api_name = get_request_api_name(request);

        EXPORTER_LOG(AL_ERROR, "%s: guac %s - domain: %s, respond-with-image-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        snprintf(error_reason, sizeof(error_reason), "Internal error");
        exporter_guac_api_error_response(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_GUAC_API_WEBSOCKET_TUNNEL_RESPONSE_FAILED, error_reason);
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Re-process api request
 */
int handle_reprocess_guac_api_request(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth, uint8_t *cookie)
{

    int ret;

    EXPORTER_GUAC_API_STATS_INC_ASYNC_COMPLETED;

    const char *api_name = get_request_api_name(request);
    EXPORTER_DEBUG_GUAC_API_DETAIL("%s: Re-process request cookie: %p, api_name: %s, async_state: %s, async_count: %d",
                 request->name, cookie, api_name, exporter_request_async_state_get_str(request->async_state), request->async_count);

    ret = handle_guac_api_request(request, auth_cookie, assertion, is_no_auth);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Guac API %s re-process error: %s", request->name, api_name, zpath_result_string(ret));
    }

    return ret;
}

#if 0
// We might use this to implement troubleshooting logs, ET-30869

/*
 * Set guac api status log
 */
static void set_guac_api_status_log(struct exporter_request *request, const char *api_status)
{
    char reason[EXPORTER_USER_GUAC_ERROR_REASON_SIZE] = "";

    /* Skip if we have no status or log already set */
    if (!api_status || request->log.guac_api_status) {
        return;
    }

    const char *api_name = get_request_api_name(request);

    snprintf(reason, sizeof(reason), "%s, %s", api_name, api_status);

    request->log.guac_api_status = EXPORTER_PORTAL_SAFE_STRDUP(reason);
}
#endif

/*-------------------------------------------------------------------*/
/* Handle guac API request                                         */
/*-------------------------------------------------------------------*/
int handle_guac_api_request(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth)
{
    int ret;

    /* Make sure basic request state is valid and guac info is set */
    ret = exporter_guac_validate_classify_request(request);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Request preprocess failed with: %s", request->name, zpath_result_string(ret));
        return ret;
    }

    /* API specific handlers */
    switch (request->guac_api_type) {

    case ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL:
        ret = guac_handle_websocket_tunnel_api(request, auth_cookie, assertion, is_no_auth);
        break;

    default:
        EXPORTER_LOG(AL_ERROR, "%s: Request guac api type has not been implemented  %d", request->name, request->guac_api_type);
        ret = ZPATH_RESULT_NOT_IMPLEMENTED;
        break;

    }

    // TODO: Implement troubleshooting logs, ET-30869
    // set_guac_api_status_log(request, zpath_result_string(ret));

    return ret;
}

/*
 * Register guac api structures
 */

static int exporter_guac_api_register_structures(struct exporter *exporter)
{
    const struct argo_log_registered_structure *s;

    /* Register structures, in case it hasn't already been registered */
    if (s_guac_api_init_done == 1) {
        EXPORTER_LOG(AL_NOTICE, "Guac api register structures already done");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!(exporter_guac_api_stats_description =
          argo_register_global_structure(EXPORTER_GUAC_API_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(guac_api_error_resp_description =
        argo_register_global_structure(GUACAPIERRORRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    // Log once per minute...
    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                     "exporter_guac_api",
                                     AL_INFO,
                                     60*1000*1000,
                                     exporter_guac_api_stats_description,
                                     &s_guac_api_stats,
                                     1,
                                     NULL,
                                     NULL);
    if (!s) {
        EXPORTER_LOG(AL_CRITICAL, "Could not register exporter guac api stats- statistics_log not initialized?");
        return ZPATH_RESULT_ERR;
    }

    guac_op_codes_init();

    EXPORTER_LOG(AL_NOTICE, "Guac api register structures done");
    return ZPATH_RESULT_NO_ERROR;
}

static int
exporter_guacd_global_stats(struct zpath_debug_state* request_state, const char** query_values,
                                                       int query_value_count, void* cookie)
{
    ZDP(" PRA stats on Exporter \n");
    ZDP("   tunnel_connect_success: %"PRId64"\n", s_guac_api_stats.tunnel_connect_success);
    ZDP("   tunnel_connect_failed: %"PRId64"\n", s_guac_api_stats.tunnel_connect_failed);
    ZDP("   tunnel_disconnect_received: %"PRId64"\n", s_guac_api_stats.tunnel_disconnect_received);
    ZDP("   tunnel_write_success: %"PRId64"\n", s_guac_api_stats.tunnel_write_success);
    ZDP("   tunnel_write_failed: %"PRId64"\n", s_guac_api_stats.tunnel_write_failed);
    ZDP("   tunnel_read_success: %"PRId64"\n", s_guac_api_stats.tunnel_read_success);
    ZDP("   tunnel_read_failed: %"PRId64"\n", s_guac_api_stats.tunnel_read_failed);
    ZDP("   init_success: %"PRId64"\n", s_guac_api_stats.init_success);
    ZDP("   init_failed: %"PRId64"\n\n", s_guac_api_stats.init_failed);

    ZDP("   total_async_requested: %"PRId64"\n", s_guac_api_stats.total_async_requested);
    ZDP("   total_async_completed: %"PRId64"\n", s_guac_api_stats.total_async_completed);
    ZDP("   websocket_request_destroy: %"PRId64"\n", s_guac_api_stats.websocket_request_destroy);
    ZDP("   websocket_parser_error: %"PRId64"\n", s_guac_api_stats.websocket_parser_error);
    ZDP("   websocket_reauth_sent: %"PRId64"\n", s_guac_api_stats.websocket_reauth_sent);
    ZDP("   guac_response_error: %"PRId64"\n\n", s_guac_api_stats.guac_response_error);
    ZDP("   guac_parser_error_500: %"PRId64"\n\n", s_guac_api_stats.guacd_parser_error_500);

    ZDP("   console_get_by_id_success: %"PRId64"\n", s_guac_api_stats.console_get_by_id_success);
    ZDP("   console_get_by_id_failed: %"PRId64"\n", s_guac_api_stats.console_get_by_id_failed);
    ZDP("   console_not_enabled: %"PRId64"\n", s_guac_api_stats.console_not_enabled);
    ZDP("   console_misconfigured: %"PRId64"\n", s_guac_api_stats.console_misconfigured);
    ZDP("   application_get_by_id_success: %"PRId64"\n", s_guac_api_stats.application_get_by_id_success);
    ZDP("   application_get_by_id_failed: %"PRId64"\n", s_guac_api_stats.application_get_by_id_failed);
    ZDP("   application_not_enabled: %"PRId64"\n", s_guac_api_stats.application_not_enabled);
    ZDP("   application_misconfigured: %"PRId64"\n\n", s_guac_api_stats.application_misconfigured);

    ZDP("   file_transfer_upload: %"PRId64"\n", s_guac_api_stats.file_transfer_upload);
    ZDP("   file_transfer_download: %"PRId64"\n", s_guac_api_stats.file_transfer_download);
    ZDP("   file_transfer_upload_failure: %"PRId64"\n", s_guac_api_stats.file_transfer_upload_failure);
    ZDP("   file_transfer_download_failure: %"PRId64"\n", s_guac_api_stats.file_transfer_download_failure);

    ZDP("   file_transfer_upload_inspect: %"PRId64"\n", s_guac_api_stats.file_transfer_upload_inspect);
    ZDP("   file_transfer_upload_inspect_deny: %"PRId64"\n", s_guac_api_stats.file_transfer_upload_inspect_deny);
    ZDP("   file_transfer_upload_inspect_fail: %"PRId64"\n", s_guac_api_stats.file_transfer_upload_inspect_fail);
    ZDP("   file_transfer_cryptoservice_fail: %"PRId64"\n", s_guac_api_stats.file_transfer_cryptoservice_fail);
    ZDP("   file_transfer_upload_policy_deny: %"PRId64"\n", s_guac_api_stats.file_transfer_upload_policy_deny);
    ZDP("   file_transfer_download_policy_deny: %"PRId64"\n", s_guac_api_stats.file_transfer_download_policy_deny);
    ZDP("   privileged_credentials_policy_accept: %"PRId64"\n", s_guac_api_stats.privileged_credentials_policy_accept);
    ZDP("   privileged_credentials_policy_reject: %"PRId64"\n\n", s_guac_api_stats.privileged_credentials_policy_reject);

    ZDP("   guacd_service_enabled: %"PRId64"\n", s_guac_api_stats.guacd_service_enabled);
    ZDP("   guacd_service_handshake_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_handshake_failed);
    ZDP("   guacd_service_gd_proxy_listener_create_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_gd_proxy_listener_create_failed);
    ZDP("   guacd_service_connect_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_connect_failed);
    ZDP("   guacd_service_mt_consume_on_request_thread_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_mt_consume_on_request_thread_failed);
    ZDP("   guacd_service_child_proxy_connection_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_child_proxy_connection_failed);
    ZDP("   guacd_service_stiching_connection_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_stiching_connection_failed);
    ZDP("   guacd_service_child_proxy_server_create_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_child_proxy_server_create_failed);
    ZDP("   guacd_service_child_proxy_server_listen_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_child_proxy_server_listen_failed);
    ZDP("   guacd_service_child_proxy_server_accept_failed: %"PRId64"\n", s_guac_api_stats.guacd_service_child_proxy_server_accept_failed);
    ZDP("   guacd_service_active_session_count: %"PRId64"\n", s_guac_api_stats.guacd_service_active_session_count);
    ZDP("       guacd_service_active_ssh_session_count     : %"PRId64"\n", s_guac_api_stats.guacd_service_active_ssh_session_count);
    ZDP("       guacd_service_active_rdp_session_count     : %"PRId64"\n", s_guac_api_stats.guacd_service_active_rdp_session_count);
    ZDP("       guacd_service_active_vnc_session_count     : %"PRId64"\n", s_guac_api_stats.guacd_service_active_vnc_session_count);
    ZDP("       guacd_service_active_realvnc_session_count : %"PRId64"\n", s_guac_api_stats.guacd_service_active_realvnc_session_count);
    ZDP("   guacd_service_mtunnel_drops_cpu_limit_reached: %"PRId64"\n", s_guac_api_stats.guacd_service_mtunnel_drops_cpu_limit_reached);
    ZDP("   guacd_service_mtunnel_drops_mem_limit_reached: %"PRId64"\n", s_guac_api_stats.guacd_service_mtunnel_drops_mem_limit_reached);
    ZDP("   guacd_service_mtunnel_drops_max_sessions_reached: %"PRId64"\n", s_guac_api_stats.guacd_service_mtunnel_drops_max_sessions_reached);
    ZDP("   pra_health_check_503_count: %"PRId64"\n", s_guac_api_stats.pra_health_check_503_count);
    ZDP("   guacd_service_total_rdp_session_count: %"PRId64"\n", s_guac_api_stats.guacd_service_total_rdp_session_count);
    ZDP("   guacd_service_total_vnc_session_count: %"PRId64"\n", s_guac_api_stats.guacd_service_total_vnc_session_count);
    ZDP("   guacd_service_total_realvnc_session_count: %"PRId64"\n", s_guac_api_stats.guacd_service_total_realvnc_session_count);
    ZDP("   guacd_service_total_ssh_session_count: %"PRId64"\n", s_guac_api_stats.guacd_service_total_ssh_session_count);

    ZDP("   large_portal_access_active_count: %"PRId64"\n", s_guac_api_stats.large_portal_access_active_count);
    ZDP("   large_portal_access_blocked_count: %"PRId64"\n", s_guac_api_stats.large_portal_access_blocked_count);
    ZDP("   credential_pool_conn_count: %"PRId64"\n", s_guac_api_stats.credential_pool_conn_count);
    ZDP("   credential_pool_conn_failed: %"PRId64"\n", s_guac_api_stats.credential_pool_conn_failed);
    ZDP("   credential_pool_delete_api_failed: %"PRId64"\n", s_guac_api_stats.credential_pool_delete_api_failed);
    ZDP("   credential_pool_internal_callback_failure: %"PRId64"\n", s_guac_api_stats.credential_pool_internal_callback_failure);
    ZDP("   credential_pool_cred_id_found: %"PRId64"\n", s_guac_api_stats.credential_pool_cred_id_found);
    ZDP("   credential_pool_err_no_cred_available: %"PRId64"\n", s_guac_api_stats.credential_pool_err_no_cred_available);
    ZDP("   credential_pool_err_service_unavailable: %"PRId64"\n", s_guac_api_stats.credential_pool_err_service_unavailable);
    ZDP("   credential_pool_err_cred_not_found_in_db: %"PRId64"\n", s_guac_api_stats.credential_pool_err_cred_not_found_in_db);
    ZDP("   credential_pool_request_status_check_failure: %"PRId64"\n", s_guac_api_stats.credential_pool_request_status_check_failure);
    ZDP("   credential_pool_err_json_response: %"PRId64"\n", s_guac_api_stats.credential_pool_err_json_response);

    ZDP("   credential_thread_cps_conn_failed: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_conn_failed);
    ZDP("   credential_thread_null_data: %"PRId64"\n", s_guac_api_stats.credential_thread_null_data);
    ZDP("   credential_thread_cps_request_error: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_request_error);
    ZDP("   credential_thread_cps_request_retry_count: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_request_retry_count);
    ZDP("   credential_thread_cps_task_enq_fail: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_task_enq_fail);
    ZDP("   credential_thread_cps_request_maxretry_fail: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_request_maxretry_fail);
    ZDP("   credential_thread_cps_del_all_retry_count: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_del_all_retry_count);
    ZDP("   credential_thread_cps_del_retry_count: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_del_retry_count);
    ZDP("   credential_thread_cps_get_retry_count: %"PRId64"\n", s_guac_api_stats.credential_thread_cps_get_retry_count);
    ZDP("   djb_launch_count: %"PRId64"\n", s_guac_api_stats.djb_launch_count);
    ZDP("   djb_validation_failure_count: %"PRId64"\n", s_guac_api_stats.djb_validation_failure_count);
    ZDP("   djb_delete_count: %"PRId64"\n", s_guac_api_stats.djb_delete_count);
    ZDP("   djb_connect_count: %"PRId64"\n", s_guac_api_stats.djb_connect_count);
    ZDP("   djb_portal_policy_fail_count: %"PRId64"\n", s_guac_api_stats.djb_portal_policy_fail_count);
    ZDP("   djb_service_resp_fail_count: %"PRId64"\n", s_guac_api_stats.djb_service_resp_fail_count);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Guac API request init
 */
int exporter_guac_api_request_init(struct exporter *exporter)
{
    int ret = ZPATH_RESULT_NO_ERROR;

    ret = zpath_debug_add_read_command("Check if guacd service is running",
                                        "/exporter/guacd/status",
                                        exporter_guacd_status_callback,
                                        NULL,
                                        NULL);
    if (ret) {
        EXPORTER_LOG(AL_ERROR, "Could not add /exporter/guacd/status");
    }

    if (s_guac_api_init_done == 0) {
        ret = exporter_guac_api_register_structures(exporter);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_INFO, "Exporter guac api request init done");
            s_guac_api_init_done = 1;
        } else {
            EXPORTER_LOG(AL_ERROR, "Exporter guac api request init failed! ret=%s", zpath_result_string(ret));
        }
    }

    ret = zpath_debug_add_read_command("Dump global PRA stats",
                                  "/exporter/pra/stats",
                                  exporter_guacd_global_stats,
                                  NULL,
                                  NULL);
    if (ret) {
        EXPORTER_LOG(AL_ERROR, "Could not add /exporter/guacd/status");
    }

    exporter_zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                        1,
                                        NULL,
                                        "/etc/resolv.conf",
                                        "/etc/hosts",
                                        log_f,
                                        NULL);

    EXPORTER_GUAC_API_STATS_INC_BY_RET_CODE(init, 1, ret);

    return ret;
}

/*
 * Guac API stats clear
 */
void exporter_guac_api_stats_clear()
{
    bzero(&s_guac_api_stats, sizeof(s_guac_api_stats));
}

/*-------------------------------------------------------------------*/
/* Handle send Guacamole instructions to the server                  */
/*-------------------------------------------------------------------*/
int exporter_guac_api_send_data(struct exporter_request *request, const void *data, size_t len)
{
    int res = ZPATH_RESULT_NO_ERROR;

    EXPORTER_DEBUG_HTTP("%s: exporter_guac_api_send_data(): %.*s, len: %zu", request->name, EXPORTER_DEBUG_BYTES, (const char *) data, len);

    // Add Guacamole instructions to be sent
    evbuffer_add(request->request_body, data, len);

    // ET-39506: Do not send immediately as that can lead to a deadlock.
    // Once the conn thread is awaken again, it will send it out safely.
    // res = exporter_request_send_data(request);
    exporter_conn_wake_from_other_thread(request->conn);
    return res;
}

void exporter_populate_log_conn_type(struct exporter_request *request, const char *select_instr)
{
  if (!request->mt) {
      return;
  }

  char *temp = NULL;

  if (request->mt && !request->mt->console_conn_type) {
      temp = EXPORTER_STRDUP(request->sra_host_protocol, strlen(request->sra_host_protocol));
  }

  if (request->mt && !request->mt->console_conn_type) {
      request->mt->console_conn_type  = temp;
  } else if (!request->guac_info->diag_info.console_conn_type) {
      temp = EXPORTER_STRDUP(request->sra_host_protocol, strlen(request->sra_host_protocol));
      request->guac_info->diag_info.console_conn_type = temp;
  }
}

int exporter_guac_setup_mtunnel(struct exporter_request *request) {
    const char *assertion_key = exporter_user_portal_request_state_get_assertion_key(request);
    const char* assertion = exporter_user_portal_request_state_get_assertion(request);
    int ret = ZPATH_RESULT_NO_ERROR;
    ret = exporter_zpa_request(request, assertion_key, assertion, 0);
    return ret;
}

void  exporter_guac_disconnect(struct exporter_request *request) {
    if (is_pra_guacd_service_disabled() || !request->guac_info) { return; }

    /* Do not optimize the connection close for premature session errors and allow normal close by guacd */
    if (!request->guac_info->gd_proxy.gd_proxy_bev || /* Guacd server connection not set up */
        !request->guac_info->gd_proxy.guacd_childproc_data || /* Guacd child protocol connection not setup */
        /* SFTP is enabled based on policy and guacd child sftp connection not setup */
        (!request->guac_info->is_sftp_disabled && !request->guac_info->gd_proxy.guacd_childproc_sftp))
    {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s: Guacd connections are not fully setup. Cannot disconnect now", request->name);
        return;
    }

    int ret = exporter_forward_data_to_guacd(request, GUAC_DISCONNECT_INSTR, strlen(GUAC_DISCONNECT_INSTR));
    EXPORTER_LOG(AL_ERROR, "DBG_GUACD %s:Sending disconnect instruction to guacd %s", request->name, zpath_result_string(ret));
    exporter_guacd_child_conn_close(request);
    exporter_guacd_server_conn_close(request);
    exporter_guacd_remote_server_proxy_conn_close(request);
}

void set_guacd_service_stat_to_enabled(void){
     s_guac_api_stats.guacd_service_enabled = 1;
}

void set_guacd_service_stat_to_disabled(void){
     s_guac_api_stats.guacd_service_enabled = 0;
}

int exporter_guac_connect(struct exporter_request *request, int r_cnt) {
    int ret = ZPATH_RESULT_NO_ERROR;
    char select_buffer[128] = {0};

    if (request->sra_host_protocol == NULL) {
        return ZPATH_RESULT_ERR;
    }

    if (!is_pra_guacd_service_disabled() || request->is_proxy_conn) request->guac_info->guacd_service = 1;

    if (!r_cnt && !request->is_proxy_conn_async) {
        /* Identify privileged capabilities for this PRA session before we proceed */
        (void)exporter_process_priv_capabilities(request);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* create fohh-http client if session recording is enabled */
    if (r_cnt == 1
        && request->guac_info->recording_ctxt.recording_check_done && request->guac_info->recording_ctxt.recording_enabled) {
        request->ref_count++;
        EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: exporter_guac_connect creating client, req %p, after inc refcount %d",
            request, request->ref_count);
        if (ZPATH_RESULT_NO_ERROR != pra_session_recording_client_init(request)) {
            REF_CNT_DECR(request);
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    if (r_cnt < 3 && request->is_proxy_conn) {
        int res = exporter_session_share_check_user(request);
        if (res) {

            /* exporter_process_priv_capabilities calls this function. If we return ASYNC, it should not close the PRA
             * connection. However, in case of error it should terminate the connection */
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: request %s, received async from share_check_user", request->name);
                request->is_proxy_conn_async = 1;
            } else {
                exporter_sharing_stats_inc(proctor_session_join_fail_type);
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: request %s, received error from share_check_user", request->name);
            }
            return res;
        }
    }

    request->conn->flush_input = 1;
    evbuffer_drain(request->request_body, evbuffer_get_length(request->request_body));

    if (!request->is_proxy_conn || !request->is_guac_sess_primary_exporter) {
        ret = exporter_guac_setup_mtunnel(request);
        if (!((ZPN_RESULT_ASYNCHRONOUS == ret) || (ZPATH_RESULT_NO_ERROR == ret))) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not find an m-tunnel for this request", request->name);
            exporter_recording_destroy_context(request);
            return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_GUAC_MTUNNEL_NOT_FOUND);
        }
    } else {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC skipping setting up the mtunnel since this is a local proxy req %s",request->name);
    }

    if (get_guac_select_instruction(select_buffer, 128, request) != ZPATH_RESULT_NO_ERROR) {
        exporter_recording_destroy_context(request);
        return ZPATH_RESULT_ERR;
    }

    if (request->mt) {
        request->mt->capabilities_policy_id = request->guac_info->capabilities_policy_id;
    } else {
        request->guac_info->diag_info.capabilities_policy_id = request->guac_info->capabilities_policy_id;
    }
    exporter_populate_log_conn_type(request, select_buffer);

    if (!request->guac_info->guacd_service) {
        ret = exporter_guac_api_send_data(request, select_buffer, strnlen(select_buffer, 128));
    } else {
        ret = exporter_guacd_connect(request);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            exporter_recording_destroy_context(request);
            return ret;
        }
        ret = exporter_forward_data_to_guacd(request, select_buffer, strnlen(select_buffer, 128));
    }

    if (!((ZPN_RESULT_ASYNCHRONOUS == ret) || (ZPATH_RESULT_NO_ERROR == ret))) {
        EXPORTER_LOG(AL_ERROR, "%s: Error while sending Guacamole SELECT", request->name);
        exporter_recording_destroy_context(request);
        return ZPATH_RESULT_ERR;
    }

    guac_parser_set_state(&request->guac_info->guac_parser, GUAC_STATE_HANDSHAKE_SELECT_SENT);

    return ZPATH_RESULT_NO_ERROR;
}

int associate_bevs_for_proxy_request(char *guac_proctored_session_id, struct bufferevent **bev, struct exporter_request *proxy_request)
{
    struct exporter_request *request = NULL;
    struct zpn_exporter_bev_elem *elem = NULL;

    if (!bev) {
        return ZPATH_RESULT_ERR;
    }
    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    request = zhash_table_lookup(global_exporter.proxy_conn_id, guac_proctored_session_id, strnlen(guac_proctored_session_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", guac_proctored_session_id);
        return ZPATH_RESULT_ERR;
    }

    /* expecting that the original session-id should match with the requested conn-id */
    if (request->guac_info->guac_parser.guac_tunnel_params.conn_id == NULL) {
        return ZPATH_RESULT_ERR;
    } else if (strcmp(request->guac_info->guac_parser.guac_tunnel_params.conn_id + 1, guac_proctored_session_id)) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Proctored conn id %s not matching to orig conn id %s",
            guac_proctored_session_id, request->guac_info->guac_parser.guac_tunnel_params.conn_id);
        return ZPATH_RESULT_ERR;
    }

    /* Found the request associated with this conn-id */
    /* Store the bev in the guac_info for request */
    elem = EXPORTER_CALLOC(sizeof(struct zpn_exporter_bev_elem));
    elem->bev = (void**)bev;
    elem->proxy_request = proxy_request;

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    ZLIST_INSERT_HEAD(&request->guac_info->bevs_list, elem, list);
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Added bev %p to bevs_list for %s", bev, request->name);

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_session_share_user_join_success(struct exporter_proctoring_session_event_info *info)
{
    struct exporter_request *request = info->orig_request;
    char *user_name = info->user;
    if (!user_name) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: Could not update ostore data for join event. No user_name", request->name);
        return ZPATH_RESULT_ERR;
    }

    int ret = exporter_update_pra_sess_ostore_data_join_event(info);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        exporter_sharing_stats_inc(session_store_write_fail_type);
        EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s: Could not update session-store for join event: %s",
            request->name, zpath_result_string(ret));
        return ZPATH_RESULT_ERR;
    }

    EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: Join event Successful: %s, user %s",
            request->name, zpath_result_string(ret), user_name ? user_name : "null");

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_session_share_check_user(struct exporter_request *request)
{
    /* Get session sharing capabilities for the session */
    if (!request->guac_info->session_sharing_check_done) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed to read capability policy for request", request->name);
        return ZPATH_RESULT_ERR;
    } else if (!request->guac_info->is_session_share_monitor) {
        exporter_sharing_stats_inc(proctor_policy_fail_type);
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed because policy does not allow monitoring, policy_id %"PRId64,
                request->name, request->guac_info->capabilities_policy_id);
        exporter_guac_send_response(request,
                HTTP_STATUS_INTERNAL_SERVER_ERROR,
                "%s",
                "Disconnected from server : Disallowed due to capability policy");
        return ZPATH_RESULT_ERR;
    }

    char *user_name = exporter_user_portal_request_state_get_name(request->portal_info);
    char *session = request->guac_proctored_session_id;

    /*if session is shared with the specific user, allow user to connect */
    int ret = check_session_is_shared_with_user(request, session, user_name);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        if (ret == ZPN_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: request %s, received async from check_session_is_shared_with_user", request->name);
        } else {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: request %s, received error from check_session_is_shared_with_user", request->name);
        }
        return ret;
    }
    EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: session is shared with the user. Check if maximum users are joined", request->name);

    /* Check for single exporter if user is allowed to join */
    if (request->is_guac_sess_primary_exporter) {
        struct exporter_request *orig_request = lookup_orig_guac_request(request);
        if (orig_request) {
            char message[EXPORTER_JOIN_EVENT_ACK_ERROR_LEN] = {'\0'};

            ret = exporter_check_user_join(orig_request->cached_sess_data, session, user_name, message, orig_request->max_join_users_limit);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: request %s, received error from exporter_check_user_join", request->name);
                exporter_guac_send_response(request,
                    HTTP_STATUS_INTERNAL_SERVER_ERROR,
                    "%s",
                    message);
                return ret;
            }
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: User can join. Allow to connect.", request->name);
        }
    }

    request->share_ctxt.user_connect_allowed = 1;

    if (request->is_proxy_conn_async == 1) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC]: recalling exporter_guac_connect, request %p", request);
        request->is_proxy_conn_async = 2;
        exporter_guac_connect(request, 3);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_conn_guac_args_cb(struct exporter_request *request, const char *args) {
    char guac_protocol_connect_instr[GUAC_CONNECT_INSTRUCTION_MAX_LENGTH];
    int ret = get_guac_connect_instruction(request, args, guac_protocol_connect_instr, GUAC_INSTRUCTION_MAX_LENGTH);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        return ret;
    }
    EXPORTER_LOG(AL_DEBUG, "Exporter sending %.*s to guacd", EXPORTER_DEBUG_BYTES, guac_protocol_connect_instr);

    /* Send the guac conn id and user name to the original exporter */
    if (!request->is_guac_sess_primary_exporter && request->guac_proctored_session_id != NULL) {
        char *temp = EXPORTER_STRDUP(request->guac_proctored_session_id, strlen(request->guac_proctored_session_id));
        request->mt->pra_conn_id = temp;
        zpn_exporter_pra_guac_proxy_data(request->mt);
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Sending conn_id %s to exporter for %s", request->mt->pra_conn_id, request->name);
    } else if (request->is_guac_sess_primary_exporter && request->guac_proctored_session_id != NULL) {
        /* Store the bevs for the proxy conn in guac_info for the original request */
        /* This will be used to disconnect the proxy conn when the original session disconnects */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Associating bev %p for exporter request %p", &request->guac_info->gd_remote_server_proxy.local_guacd_bev, request);
        associate_bevs_for_proxy_request(request->guac_proctored_session_id, &request->guac_info->gd_remote_server_proxy.local_guacd_bev, request);
    }

    if (!request->guac_info->guacd_service) {
        ret = exporter_guac_api_send_data(request, guac_protocol_connect_instr, strlen(guac_protocol_connect_instr));
    } else {
        ret = exporter_forward_data_to_guacd(request, guac_protocol_connect_instr, strlen(guac_protocol_connect_instr));
    }
    if (!((ZPN_RESULT_ASYNCHRONOUS == ret) || (ZPATH_RESULT_NO_ERROR == ret))) {
        EXPORTER_LOG(AL_ERROR, "%s: Error while sending Guacamole CONNECT", request->name);
        return ZPATH_RESULT_ERR;
    }
    guac_parser_set_state(&request->guac_info->guac_parser, GUAC_STATE_HANDSHAKE_CONNECT_SENT);
    return ret;
}

int exporter_conn_guac_ready_cb(struct exporter_request *request) {
    int ret = ZPATH_RESULT_NO_ERROR;

    __sync_fetch_and_add_8(&recording_pool_g.upload_stats.guac_ready_cb_cnt, 1);
    guac_parser_set_state(&request->guac_info->guac_parser, GUAC_STATE_ESTABLISHED);
    request->conn->flush_input = 0;
    /* need assertion for the sftp mtunnel. defer free */
    //    exporter_user_portal_request_state_free_assertion(request);
    ret = exporter_conn_start_session_recording(request);

    return ret;
}

int get_app_server(struct exporter_request *request, int64_t app_id)
{

    struct zpn_app_group_relation *links;
    size_t count = 1;
    int    res = ZPN_RESULT_ERR;
    size_t  sgrp_to_server_map_count = 1;
    struct zpn_servergroup_server_relation *sgrp_to_server_map;
    struct zpn_app_server *srv = NULL;
    int is_async = 0;
    int64_t server_group_gid = 0;

    /* Get the server group ID from using application gId */
    res = zpn_app_group_relation_get_by_application(app_id,
                                                   &links,
                                                   &count,
                                                   exporter_request_wally_callback,
                                                   request,
                                                   0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return res;
    } else if (ZPN_RESULT_NOT_FOUND == res) {
        return res;
    }

    if (links->deleted) {
        return ZPATH_RESULT_NO_ERROR;
    } else {
        server_group_gid = links->group_id;
    }


    /* Get the server Id from server-group to server relation table */
    res = zpn_servergroup_server_relation_get_by_group(server_group_gid,
                                                       &sgrp_to_server_map,
                                                       &sgrp_to_server_map_count,
                                                       exporter_request_wally_callback,
                                                       request,
                                                       0);
    if (ZPN_RESULT_ASYNCHRONOUS == res) {
        return ZPN_RESULT_ASYNCHRONOUS;
    } else if (ZPN_RESULT_NOT_FOUND == res) {
        EXPORTER_LOG(AL_DEBUG, "No server configured for server group GID %"PRId64"", server_group_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (sgrp_to_server_map->deleted) {
        return ZPATH_RESULT_NO_ERROR;
    } else {
        request->server_gid = sgrp_to_server_map->server_id;
    }

    is_async = 0;
    srv = zpn_application_server_get_by_id(request->server_gid, &is_async,
					   exporter_request_wally_callback,
					   request,
					   0);
    if (is_async) {
        return ZPN_RESULT_ASYNCHRONOUS;
    } else if (res == ZPN_RESULT_NOT_FOUND) {
        EXPORTER_LOG(AL_DEBUG, "No server found for GID %"PRId64"", request->server_gid);
        return ZPATH_RESULT_ERR;
    }
    if (!srv) {
        EXPORTER_LOG(AL_DEBUG, "No server associated with ID %"PRId64"", request->server_gid);
        return ZPATH_RESULT_ERR;
    }

    if (!srv->enabled) {
        EXPORTER_LOG(AL_DEBUG, "App server GID(%"PRId64") is not enabled", request->server_gid);
        return ZPATH_RESULT_ERR;
    }

    if (!request->sra_remote_ip) {
        request->sra_remote_ip = EXPORTER_STRDUP(srv->address, strlen(srv->address));
        EXPORTER_LOG(AL_DEBUG, "App server GID(%"PRId64") has address %s", request->server_gid, srv->address);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Extract RDP credentials from JSON required for guac 'connect' instruction
 */
static int exporter_conn_guac_parse_json_rdp_args(const char *authentication,
                                                  char **username,
                                                  char **password,
                                                  char **domain)
{
    JSON_Object     *jo = NULL;
    JSON_Value      *jv = NULL;

    if (!authentication) {
        return ZPATH_RESULT_ERR;
    }

    jv = json_parse_string(authentication);
    if (json_value_get_type(jv) != JSONObject) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }
    jo = json_value_get_object(jv);
    if (!jo) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }

    const char *username_ptr = json_object_get_string (jo, "username");
    const char *password_ptr = json_object_get_string (jo, "password");
    const char *domain_ptr = json_object_get_string (jo, "domainname");
    if (!username_ptr || !strlen(username_ptr) ||
        !password_ptr || !strlen(password_ptr)) {
        /* Both username and password is mandatory for RDP */
        EXPORTER_LOG(AL_ERROR, "Failed to get mandatory authentication parameters");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }
    *username = EXPORTER_STRDUP(username_ptr, strlen(username_ptr));
    *password = EXPORTER_STRDUP(password_ptr, strlen(password_ptr));
    if (!(*username) || !(*password)) {
        EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
        json_value_free(jv);
        if (*username) {
            memset(*username,'\0', strlen(*username));
            EXPORTER_FREE(*username);
        }
        if (*password) {
            memset(*username,'\0', strlen(*password));
            EXPORTER_FREE(*password);
        }
        return ZPATH_RESULT_ERR;
    }

    /* Domain is optional parameter */
    if (domain_ptr && strlen(domain_ptr)) {
        *domain = EXPORTER_STRDUP(domain_ptr, strlen(domain_ptr));
        if (!(*domain)) {
            EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
            json_value_free(jv);
            memset(*username,'\0', strlen(*username));
            memset(*username,'\0', strlen(*password));
            EXPORTER_FREE(*username);
            EXPORTER_FREE(*password);
        }
    }
    json_value_free(jv);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Extract SSH credentials from JSON required for guac 'connect' instruction
 */
static int exporter_conn_guac_parse_json_ssh_args(const char *authentication,
                                                  char **username,
                                                  char **password,
                                                  char **pvt_key,
                                                  char **passphrase)
{
    JSON_Object     *jo = NULL;
    JSON_Value      *jv = NULL;

    if (!authentication) {
        return ZPATH_RESULT_ERR;
    }

    jv = json_parse_string(authentication);
    if (json_value_get_type(jv) != JSONObject) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }
    jo = json_value_get_object(jv);
    if (!jo) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }

    const char *username_ptr = json_object_get_string (jo, "username");
    const char *password_ptr = json_object_get_string (jo, "password");
    const char *pvt_key_ptr = json_object_get_string (jo, "privatekey");
    const char *passphrase_ptr = json_object_get_string (jo, "passphrase");
    if (!username_ptr || !strlen(username_ptr)) {
        /* Username is mandatory for SSH */
        EXPORTER_LOG(AL_ERROR, "Failed to get mandatory authentication parameters");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }

    if ((!password_ptr || !strlen(password_ptr)) &&
        (!pvt_key_ptr || !strlen(pvt_key_ptr))) {
        /* Either password or private key is mandatory for SSH */
        EXPORTER_LOG(AL_ERROR, "Failed to get mandatory authentication parameters");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }

    *username = EXPORTER_STRDUP(username_ptr, strlen(username_ptr));
    if (!(*username)) {
        EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }
    if (password_ptr && strlen(password_ptr)) {
        *password = EXPORTER_STRDUP(password_ptr, strlen(password_ptr));
        if (!(*password)) {
            EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
            json_value_free(jv);
            memset(*username,'\0', strlen(*username));
            EXPORTER_FREE(*username);
            return ZPATH_RESULT_ERR;
        }
    }
    if (pvt_key_ptr && strlen(pvt_key_ptr)) {
        *pvt_key = EXPORTER_STRDUP(pvt_key_ptr, strlen(pvt_key_ptr));
        if (!(*pvt_key)) {
            EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
            json_value_free(jv);
            memset(*username,'\0', strlen(*username));
            EXPORTER_FREE(*username);
            return ZPATH_RESULT_ERR;
        }
    }
    /* Passphrase is an optional parameter */
    if (passphrase_ptr && strlen(passphrase_ptr)) {
        *passphrase = EXPORTER_STRDUP(passphrase_ptr, strlen(passphrase_ptr));
        if (!(*passphrase)) {
            EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
            json_value_free(jv);
            memset(*username,'\0', strlen(*username));
            memset(*pvt_key, '\0', strlen(*pvt_key));
            EXPORTER_FREE(*username);
            EXPORTER_FREE(*pvt_key);
            return ZPATH_RESULT_ERR;
        }
    }

    if (jv) {
        json_value_free(jv);
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Extract VNC credentials from JSON required for guac 'connect' instruction
 */
static int exporter_conn_guac_parse_json_vnc_args(const char *authentication,
                                                  char **username,
                                                  char **password)
{
    JSON_Object     *jo = NULL;
    JSON_Value      *jv = NULL;

    if (!authentication) {
        return ZPATH_RESULT_ERR;
    }

    jv = json_parse_string(authentication);
    if (json_value_get_type(jv) != JSONObject) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }
    jo = json_value_get_object(jv);
    if (!jo) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate authentication JSON");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }

    const char *username_ptr = json_object_get_string (jo, "username");
    const char *password_ptr = json_object_get_string (jo, "password");
    if (!password_ptr || !strlen(password_ptr)) {
        /* Password is mandatory for VNC */
        EXPORTER_LOG(AL_ERROR, "Failed to get mandatory authentication parameters");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }

    /* Username is optional parameter */
    if (username_ptr && strlen(username_ptr)) {
        *username = EXPORTER_STRDUP(username_ptr, strlen(username_ptr));
        if (!(*username)) {
            EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
            json_value_free(jv);
            return ZPATH_RESULT_ERR;
        }
    }

    *password = EXPORTER_STRDUP(password_ptr, strlen(password_ptr));
    if (!(*password)) {
        EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
        json_value_free(jv);
        if (username && (*username)) {
            memset(*username,'\0', strlen(*username));
            EXPORTER_FREE(*username);
        }
        return ZPATH_RESULT_ERR;
    }
    json_value_free(jv);

    return ZPATH_RESULT_NO_ERROR;
}

int is_guac_sftp_mt_alive(struct exporter_request *request)
{
    int ret = 0;
    if (request->guac_info && request->guac_info->gd_proxy.sftp_exporter_request &&
        request->guac_info->gd_proxy.sftp_exporter_request->mt) {
        /* defer wss request destroy as sftp mtunnel hasn't closed yet
         * next conn wake or callback will destroy wss request and sftp request */
        EXPORTER_LOG(AL_DEBUG, "DBG_GUACD %s SFTP request mt is not destroyed. Invoking destroy again. ",
            request->name);
        exporter_zpa_terminate(request->guac_info->gd_proxy.sftp_exporter_request);
        /* if mt becomes NULL in above exporter_zpa_terminate, return 0 to fall through in caller (wss request_destroy)
         * and release wss request's guac_info and sftp req immediately */
        if (request->guac_info->gd_proxy.sftp_exporter_request->mt ||
            /* do not destroy if there are outstanding async requests */
            (request->guac_info->gd_proxy.sftp_exporter_request->async_count > 1)) ret = 1;
    }
    return ret;
}

/*
 * This function populates guacamole connection ID for exporter log.
 */
void exporter_populate_log_conn_id(struct exporter_request *request, const char *conn_id)
{
    if (!request || !request->mt || !conn_id || request->mt->pra_conn_id) {
        return;
    }

    char *temp = NULL;
    temp = EXPORTER_STRDUP(conn_id, strlen(conn_id));

    request->mt->pra_conn_id = temp;
}

void exporter_increment_credentials_policy_reject_stats()
{
    EXPORTER_GUAC_API_STATS_INC(privileged_credentials_policy_reject, 1);
}
