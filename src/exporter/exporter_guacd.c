/*
 * exporter_guacd.c  Copyright (C) 2022 Zscaler Inc. All Rights Reserved.
 */

#include "fohh/fohh.h"
#include "fohh/fohh_private.h"
#include "zpn/zpn_lib.h"
#include "zhash/zhash_table.h"
#include "zlibevent/zlibevent_bufferevent.h"
#include "zpath_lib/zpath_instance.h"
#include <netinet/tcp.h>
#include "exporter/exporter.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_zpa.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/guacd_perf_limits.h"
#include "exporter/exporter_guacd.h"
#include <arpa/inet.h>
#include "exporter/exporter_util.h"
#include "zpn/zpn_exporter_client.h"
#include "zpath_lib/zpath_instance.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "zpn/zpn_exporter_mtunnel.h"
#include "exporter/exporter_guac_session_control_host.h"

#define DBG_GUACD "DBG_GUACD "
#define SFTP_PORT 22
#define GUACD_SERVER_CONN_TIMEOUT_SECONDS 20
#define MAX_RETRY_COUNT 4

extern int g_temp_port;
char *gd_server_ip = NULL;
char *gd_child_proxy_ip = NULL;
char *exporter_guac_rdprd_path = "/opt/zscaler/rdprd";

/* For guacd child proc handling - exporter listener(generic server) functions */
static int start_guacd_child_proc_listener(struct exporter_request *request);
static int gd_child_proxy_generic_server_create(struct exporter_request *request);
static int gd_child_proxy_generic_server_listen(struct exporter_request *request);
static void gd_child_proxy_generic_server_destroy(struct exporter_request *request);

/* Process acccept callbacks in the context of request thread */
#if EXPORTER_USE_ZEVENT
static void exporter_guacd_child_proxy_accept_cb_on_thread(struct zevent_base *zbase, void *cookie, int64_t sock)
#else
static void exporter_guacd_child_proxy_accept_cb_on_thread(struct fohh_thread *thread, void *cookie, int64_t sock)
#endif
;

static int start_guacd_remote_proxy_listener(struct exporter_request *request);
static int gd_remote_proxy_generic_server_create(struct exporter_request *request);
static int gd_remote_proxy_generic_server_listen(struct exporter_request *request);

int create_proxy_guacd_client(struct exporter_request *request);

/*
 * Exporter <----> guacd server (initial handshake)
 * Add guac instr to the bev evbuffer
 */
int exporter_forward_data_to_guacd(struct exporter_request *request, const void *data, size_t len)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct evbuffer  *output_buffer;
    struct bufferevent *bev;
    if (!request->guac_info->gd_proxy.gd_proxy_bev) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Guacd server proxy bev not found");
        return ZPATH_RESULT_ERR;
    }
    if (len <= 0) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "exporter_forward_data_to_guacd:Nothing to forward");
        return ZPATH_RESULT_NO_ERROR;
    }
    bev = request->guac_info->gd_proxy.gd_proxy_bev;
    output_buffer = bufferevent_get_output(bev);
    if (!output_buffer) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s bev output buffer missing for exporter <--> guacd conn ", request->name);
        return ZPATH_RESULT_ERR;
    }

    if (!request->guac_info->gd_proxy.gd_proxy_request) {
        request->guac_info->gd_proxy.gd_proxy_request = evbuffer_new();
    }

    evbuffer_add(request->guac_info->gd_proxy.gd_proxy_request, data, len);
    //TODO  wake up conn to add to bev
    int enq_len = evbuffer_remove_buffer(request->guac_info->gd_proxy.gd_proxy_request, output_buffer, len);
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s Sent by exporter proxy to guacd server: %d bytes ", request->name, enq_len);

    return res;
}

/* Following 2 functions that are callbacks on bev read/write guac instructions
 *  exporter_send_data_to_guacd - take guac instructions from the bev evbuffer and send to guacd server
 *  exporter_send_data_to_browser - read guac instructions from the bev evbuffer and send to browser
 */
static void exporter_send_data_to_browser(struct bufferevent *bev, void *ptr)
{
    struct exporter_request *request = ptr;
    struct evbuffer *response_buffer = NULL;

    if ((request == NULL) || !request->conn || request->http_response_complete) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Cannot send guacd server data to browser");
        return;
    }
    // needs to create an event in the context of request conn thread
    // it can pick up the data from bev and add to response buffer of request
    // pick up from bev input buffer and add it to the gd_proxy_response
    // but depending on state machine we either send to browser or do args_cb

    if (!request->guac_info->gd_proxy.gd_proxy_bev) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "GD proxy bev is null");
        return;
    }

    response_buffer = bufferevent_get_input(request->guac_info->gd_proxy.gd_proxy_bev);

    // this can be done from the thread context
    // call  exporter_guac_send_to_browser - it builds websocket frame and adds to bev
    // call exporter_guac_process_instruction_from_server_cb
    // exporter_conn_process_response --- response_data
    //    exporter_guac_conn_process_response
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s Send %zu bytes to browser from guacd server ", request->name, evbuffer_get_length(response_buffer));

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (!request->response_data) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s response_data in request is missing. Allocating ", request->name);

        request->response_data  = evbuffer_new();
    }
    evbuffer_remove_buffer(response_buffer, request->response_data, evbuffer_get_length(response_buffer));
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    (void)exporter_conn_wake(request->conn);
}


static void exporter_send_data_to_guacd(struct bufferevent *bev, void *ptr)
{
    struct exporter_request *request = ptr;
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s For now we do nothing here. May be add number of bytes being sent", request->name);
    // later remove from someother place and add it to the bev output buffer???
}

static void exporter_guacd_conn_status_cb(struct bufferevent *bev, short style, void *cookie)
{
    struct exporter_request *request = cookie;
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";

    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";

    if (style & BEV_EVENT_CONNECTED) {
        struct timeval tv;
        /* Set large timeout. Takes longer to setup initial mtunnels */
        tv.tv_sec = GUACD_SERVER_CONN_TIMEOUT_SECONDS;
        tv.tv_usec = 0;
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Connected to guacd. Set %ld seconds timeout on reads from guacd server ", request->name, tv.tv_sec);
        bufferevent_set_timeouts(request->guac_info->gd_proxy.gd_proxy_bev, &tv, NULL);
    }
    else if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR | BEV_EVENT_TIMEOUT)) {
        EXPORTER_LOG(AL_ERROR, "DBG_GUACD %s: Received event(ref count decr) %s %s %s",
                     request->name, b_eof,  b_error, b_timeout);
        if (request->guac_info->gd_proxy.gd_proxy_bev) zlibevent_bufferevent_free(request->guac_info->gd_proxy.gd_proxy_bev);
        request->guac_info->gd_proxy.gd_proxy_bev = NULL;
        if (style & BEV_EVENT_ERROR) exporter_guac_api_stats_increment(GUACD_SERVICE_CONNECT_FAILED);
        exporter_guacd_remote_server_proxy_conn_close(request);

        gd_child_proxy_generic_server_destroy(request);

        /* Destroying bevs before request getting destroyed*/
        if (!request->is_proxy_conn) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %p exporter_guacd_conn_status_cb: bevs_list is getting freed from request_destroy", request);
            exporter_free_mtunnels_list(request);
            exporter_free_bevs_list(request);
        }

        /* Final Destroy - WSS request will be destroyed on Exporter <--> guacd server connection event
         * ref_count will increase by 2 (3 with sftp). During conn events, all 2(3 with sftp) reference counts are dropped */
        ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
        request->ref_count--;
        request->http_response_complete = 1;
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        if (request->guac_info->gd_proxy.gd_proxy_request) {
            evbuffer_free(request->guac_info->gd_proxy.gd_proxy_request);
            request->guac_info->gd_proxy.gd_proxy_request = NULL;
        }
        /* Wake up the connection to check if resources should be released */
        int res = exporter_conn_wake(request->conn);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "DBG_GUACD %s %s", request->name, zpath_result_string(res));
        }
    }
}


void exporter_guacd_server_conn_close(struct exporter_request *request)
{
    EXPORTER_LOG(AL_ERROR, "DBG_GUACD %s: Explicit exporter_guacd_server_conn_close due to mtunnel errors", request->name);

    if (request->guac_info->gd_proxy.gd_proxy_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_proxy.gd_proxy_bev);
        request->guac_info->gd_proxy.gd_proxy_bev = NULL;
        gd_child_proxy_generic_server_destroy(request);

        if (!request->is_proxy_conn) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC calling exporter_free_mtunnels_list from request_destroy");
            exporter_free_mtunnels_list(request);
            exporter_free_bevs_list(request);
        }

        ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
        request->ref_count--;
        request->http_response_complete = 1;
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    }
    if (request->guac_info->gd_proxy.gd_proxy_request) {
        evbuffer_free(request->guac_info->gd_proxy.gd_proxy_request);
        request->guac_info->gd_proxy.gd_proxy_request = NULL;
    }
}

int exporter_guacd_init_sockaddr(struct sockaddr_storage *addr, socklen_t *addr_len,
				 struct exporter_request *request)
{

    int res;
    if (request  && request->is_proxy_conn) {
      res = fohh_str_to_sockaddr_storage(EXPORTER_GUACD_IPADDR, addr, addr_len);
      fohh_sockaddr_set_port(addr, htons(request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_port));
    } else {
      if (gd_server_ip) {
        res = fohh_str_to_sockaddr_storage(gd_server_ip, addr, addr_len);
        EXPORTER_DEBUG_GUACD(DBG_GUACD "Using non local ip");
      } else {
        res = fohh_str_to_sockaddr_storage(EXPORTER_GUACD_IPADDR, addr, addr_len);
        EXPORTER_DEBUG_GUACD(DBG_GUACD "Using local ip");
      }
      fohh_sockaddr_set_port(addr, htons(EXPORTER_GUACD_PORT));
    }
    if (res < 0) { EXPORTER_LOG(AL_NOTICE, "DBG_GUACD error in initializing the socket addr to guacd");}
    return res;
}

/* When a WSS connect is received, exporter sets up a TCP connection to guacd
 * followed bu guac handshake */
int exporter_guacd_connect(struct exporter_request *request)
{
    /* Initiate a connect to guacd */
    // Verify and connect to guacd service running non-locally TODO later
    int fohh_thread_id = request->conn->thread->fohh_thread_id; //fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_EXPORTER_HTTP); //FOHH_WORKER_ZPN_MAINTENANCE);
    struct event_base *base = fohh_get_thread_event_base(fohh_thread_id);
    struct sockaddr_storage addr;
    socklen_t addr_len = sizeof(struct sockaddr_storage);
    int res;

    /* Handle initialization in case of guacd restart */
    if (!request->is_proxy_conn && !g_exporter_is_guacd_managed_from_exporter &&
        guacd_validate_pid() == ZPN_RESULT_ERR) {
        if (guacd_update_pid() == ZPN_RESULT_ERR) {
            EXPORTER_LOG(AL_ERROR, "FATAL: Unable to update guacd pid");
        }
    }

    /*If its a proxy conn for session proctoring, then don't create a guac child proc listener
      We will create a guac remote server proxy and write to it instead of guacd server  */
    if (request->is_proxy_conn) {
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Starting a GD remote proxy listener");
      res = start_guacd_remote_proxy_listener(request);
      if (res) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "-SESS_PROC - Failed to create GD remote proxy listener.");
	    goto gd_connect_fail;
      }
    } else {
      // start a listener on random port
      res = start_guacd_child_proc_listener(request);
      if (res) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " - Failed to create GD proxy listener.");
        exporter_guac_api_stats_increment(GUACD_SERVICE_GD_PROXY_LISTENER_CREATE_FAILED);
        goto gd_connect_fail;
      }
    }

    struct bufferevent *bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
    if (!bev) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s: Could not allocate bufferevent socket", request->name);
        goto gd_connect_fail;
    }

    bufferevent_setcb(bev, exporter_send_data_to_browser, exporter_send_data_to_guacd,
                      exporter_guacd_conn_status_cb, request);
    bufferevent_enable(bev, EV_READ|EV_WRITE);

    request->guac_info->gd_proxy.gd_proxy_request = NULL;
    request->guac_info->gd_proxy.gd_proxy_response = NULL;
    request->guac_info->gd_proxy.gd_proxy_bev = bev;

    res = exporter_guacd_init_sockaddr(&addr, &addr_len, request);
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " Failed to convert str to a valid sockaddr. Check the input args ...");
        goto gd_connect_fail;
    }

    if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s: Could not connect to guacd server from exporter", request->name);
        goto gd_connect_fail;
    }
    int sock = bufferevent_getfd(bev);
    int tmp_val = 1;

    res = setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " Failed to set socket options TCP_NODELAY");
        goto gd_connect_fail;
    }

    EXPORTER_DEBUG_GUACD("DBG_GUACD %s (THREAD ID %d) Connecting to guacd ", request->name, fohh_thread_id);

    request->guac_info->gd_proxy.gd_proxy_thread_id = fohh_thread_id;

     // TCP options todo later // save the thread id ?? to process in that at all times ?
     // Any browser data needs to goto guacd and any data arriving from app needs to go to guacd
     // Any guac instruction being sent back from guacd needs to goto browser
     // Any non-guac instructions being sent from guacd need to goto app
     // There should be 2 different processes
     // here the sole responsibility will be to send/recv guac instr/non-guac data to/from guacd


    /* Forward request to guacd */
    /* Wait for data to arrive and then invoke exporter_guac_api_send_data - includes ssh
     * This will be on a separate listener path .....
     * to protocol_server_thread_id
     */
    // Request lock already held in exporter_conn_process_response
    // exporter_conn_process_response -> exporter_resp_parser_headers_complete_cb -> exporter_guac_connect
    request->ref_count++;

    return res;
gd_connect_fail:
    exporter_guac_api_stats_increment(GUACD_SERVICE_CONNECT_FAILED);
    if (request->guac_info->gd_proxy.gd_proxy_bev) zlibevent_bufferevent_free(request->guac_info->gd_proxy.gd_proxy_bev);
    request->guac_info->gd_proxy.gd_proxy_bev = NULL;
    gd_child_proxy_generic_server_destroy(request);
    return ZPATH_RESULT_ERR;
}

static int start_guacd_child_proc_listener(struct exporter_request *request)
{
    int res;
    int retry_count = 0;

    if (gd_child_proxy_ip) {
        request->guac_info->gd_proxy.guacd_childproc_listener_addr = gd_child_proxy_ip;
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Child proxy ip non local", request->name);
    } else {
        request->guac_info->gd_proxy.guacd_childproc_listener_addr = EXPORTER_GUACD_CHILDPROC_LISTENER_IP;
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Child proxy ip local", request->name);
    }


    do {
        res = gd_child_proxy_generic_server_create(request); // dejan: shouldn't this be a global singleton?
        retry_count++;
    } while ((res != ZPATH_RESULT_NO_ERROR) && (retry_count < MAX_RETRY_COUNT));

    if (res) return res;

    retry_count = 0;

    do {
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        request->guac_info->gd_proxy.guacd_childproc_listener_port = g_temp_port;
        if (g_temp_port >= EXPORTER_GDPROXY_LISTENER_MAXPORT_NUMBER) {
            g_temp_port = EXPORTER_GDPROXY_LISTENER_MINPORT_NUMBER;
        } else {
            g_temp_port++;
        }
        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);

        res = gd_child_proxy_generic_server_listen(request);
        retry_count++;
    } while ((res != ZPATH_RESULT_NO_ERROR) && (retry_count < MAX_RETRY_COUNT));

    return res;
}

static int start_guacd_remote_proxy_listener(struct exporter_request *request)
{
    int res;
    int retry_count = 0;
    request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_addr = EXPORTER_GUACD_CHILDPROC_LISTENER_IP;
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s Child proxy ip local", request->name);
    do {
      res = gd_remote_proxy_generic_server_create(request);
      retry_count++;
    } while ((res != ZPATH_RESULT_NO_ERROR) && (retry_count < MAX_RETRY_COUNT));

    if (res) return res;
    retry_count = 0;

    do {
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_port = g_temp_port;
        if (g_temp_port >= EXPORTER_GDPROXY_LISTENER_MAXPORT_NUMBER) {
            g_temp_port = EXPORTER_GDPROXY_LISTENER_MINPORT_NUMBER;
        } else {
            g_temp_port++;
        }
        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);

        res = gd_remote_proxy_generic_server_listen(request);
        retry_count++;
    } while ((res != ZPATH_RESULT_NO_ERROR) && (retry_count < MAX_RETRY_COUNT));

    return res;
}

/*
 * Event callbacks for the new listener to child guacd
 *
 */

/*#if EXPORTER_USE_ZEVENT
void exporter_guacd_proxy_wake_on_request_thread(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else*/
//void exporter_guacd_proxy_wake_on_request_thread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
//#endif
/* Cookie is either the main wss request or it could be sftp request depending on the callstack */
void exporter_guacd_proxy_wake_on_request_thread(void *cookie, int64_t int_cookie)
{
    struct guacd_proxy_childproc *guacd_childproc_data = NULL;
    struct evbuffer *read_buf = NULL;
    struct exporter_request *request = cookie;
    size_t len = 0;

    if (!request || !request->guac_info) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD Request or guac_info of request is NULL");
        return;
    }

    if (request->is_proxy_conn) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Called in %s in remote proxy context", __FUNCTION__);
    }

    /* SFTP handling from read_cb, int_cookie will be set */
    if (int_cookie) {
        if (!request->guac_info->gd_proxy.sftp_exporter_request) {
            EXPORTER_DEBUG_GUACD("DBG_GUACD SFTP request is NULL");
            return;
        }
        /* Rest of processing will be on sftp request and not wss request */
        request = request->guac_info->gd_proxy.sftp_exporter_request;
    }

    if (!request->is_proxy_conn && !request->guac_info->gd_proxy.guacd_childproc_data) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Invalid request(guacd_childproc_data null. No child proc conn yet)", request->name);
        return;
    }

    if (request->is_proxy_conn && !request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s guacd_remote_server_proxy not initialized correctly)", request->name);
        return;
    }

    if (!request->mt || request->mt->status != zfce_mt_connected) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Mtunnel is not connected state yet ...", request->name);
        return;
    }

    /* For sftp connections, there will be new request <---> new mt mapping
     * guac_info ptr is same for main wss request and sftp request
     * gd_proxy.cookie holds the new mt for sftp conn
     * How do we know its a sftp request or wss request?
     * callstack from exporter_zpa - request ptr will be sftp request
     * callstack from read_cb - request ptr will be wss request, int_cookie will be 1
     */

    if (request->guac_info->gd_proxy.cookie == request->mt) {
        guacd_childproc_data = request->guac_info->gd_proxy.guacd_childproc_sftp;
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Consume into SFTP mtunnel", request->name);
        if (!guacd_childproc_data) {
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s SFTP conn is not initialized yet ", request->name);
            return;
        }
        /* Free assertion here as SFTP mt is connected.
         * Assertion size is significant. Do not defer free until request destroy */
        if (!request->guac_info->is_assertion_freed) {
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Assertion and key freed", request->name);
            exporter_user_portal_request_state_free_assertion(request);
            request->guac_info->is_assertion_freed = 1;
        }
    } else {
        guacd_childproc_data = request->guac_info->gd_proxy.guacd_childproc_data;
        /*  Free assertion here if SFTP is not supported and mt is connected
         *  If SFTP is supported defer free until sftp mt is connected
         *  Do not defer free until request destroy */
        if (request->guac_info->is_sftp_disabled && !request->guac_info->is_assertion_freed) {
            exporter_user_portal_request_state_free_assertion(request);
            request->guac_info->is_assertion_freed = 1;
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Assertion and key freed", request->name);
        }
    }
    if (request->is_proxy_conn) {
        read_buf = bufferevent_get_input(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
    } else {
        if (guacd_childproc_data->child_proc_bev != NULL) {
            read_buf = bufferevent_get_input(guacd_childproc_data->child_proc_bev);
        } else {
            return;
        }
    }

    //exporter_request_send_data(conn->current_request);
    len = evbuffer_get_length(read_buf);
    if (len <= 0) {
        return;
    }
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s %zu bytes ready to be consumed by mtunnel", request->name, len);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s %zu bytes ready to be consumed by mtunnel", request->name, len);
    int res = zfce_mt_consume(request->mt,
                              read_buf,
                              len,
                              request->mt_incarnation);
    if (res) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Failure in mt consume on request thread: %s", zpath_result_string(res));
        exporter_guac_api_stats_increment(GUACD_SERVICE_MT_CONSUME_ON_REQUEST_THREAD_FAILED);
    }
}

int exporter_guacd_proxy_wake_from_request_thread(struct exporter_request *request)
{
//    int res;
//    struct exporter_conn *conn = request->conn;
//    if (conn->destroying) {
//        EXPORTER_LOG(AL_DEBUG, DBG_GUACD "Should not make thread_call for guacd child if main wss conn is being destroyed");
//        return ZPATH_RESULT_ERR;
//    }
//
//#if EXPORTER_USE_ZEVENT
//    res = fohh_thread_call_zevent(conn->thread->fohh_thread_id, exporter_guacd_proxy_wake_on_request_thread, request, 0);
//#else
//    res = fohh_thread_call(conn->thread->fohh_thread_id, exporter_guacd_proxy_wake_on_request_thread, request, 0);
//#endif
//    if (res) {
//        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Could not thread_call: %s", zpath_result_string(res));
//    }
//    return res;
//    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    exporter_guacd_proxy_wake_on_request_thread(request, 0);
//    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;

}


// read from the child proc conn and send straight to mtunnel
static void exporter_guacd_proxy_read_cb(struct bufferevent *bev, void *cookie)
{
    struct guacd_proxy_childproc *guacd_childproc_data = cookie;
    int64_t int_sftp_cookie = 0;
    // this is not HTTP
    // for now we reuse same infra and let mt consume
    struct exporter_request *request = NULL;
    if (!guacd_childproc_data) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "child proc read callback with missing child proc info");
        return;
    }
    if (bev != guacd_childproc_data->child_proc_bev){
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "bev %p passed in the callback does not match child proc info bev %p", bev, guacd_childproc_data->child_proc_bev);
        guacd_childproc_data->child_proc_bev = bev;
    }

    request = guacd_childproc_data->request;
    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, "DBG_GUACD exporter_guacd_proxy_read_cb null contents in guacd_childproc_data");
        return;
    }

    EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_proxy_read_cb", request->name);
    if (guacd_childproc_data == request->guac_info->gd_proxy.guacd_childproc_sftp) int_sftp_cookie = 1;
    (void)exporter_guacd_proxy_wake_on_request_thread(request, int_sftp_cookie);
}


/* Destroy the ephemeral sftp exporter request
 * Triggered when the sftp conn or protocol conn to guacd child is gone */
int sftp_request_destroy(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    if (request->mt) {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s SFTP mt is still attached. destroying ... ", request->name);
        res = exporter_zpa_terminate(request);
        /* exporter_zpa_terminate will be asynchronous mt terminate
        * exporter_request_mt_status_callback_on_thread
        * will wake up conn and wss guac info destroy does the final destroy
        * if mt_destroy_sent & mt_release_received is set then request->mt will be NULL */
        if (request->mt) {
            return res;
        }
    }

    if (request->async_count > 1) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s async count for SFTP request is greater than 1, don't continue with destroy", request->name);
        return ZPATH_RESULT_ERR;
    }
    assert(request->async_count > 0);
    __sync_fetch_and_sub_4(&(request->async_count), 1);

    if (request->request_body) evbuffer_free(request->request_body);
    if (request->response_data) evbuffer_free(request->response_data);
    request->request_body = NULL;
    request->response_data = NULL;
    exporter_request_log_cleanup(request);
    request->name = NULL;
    request->conn = NULL;
    request->nameid = NULL;
    SET_EXPORTER_OBJ_INVALID(request);
    ephemeral_exporter_request_free(request);
    EXPORTER_LOG(AL_ERROR, DBG_GUACD "DBG_GUACD SFTP request destroyed");
    return ZPATH_RESULT_NO_ERROR;
}

static void exporter_guacd_remote_server_proxy_status_cb(struct bufferevent *bev __attribute__((unused)),
                                                         short style, void *cookie)
{
  struct exporter_request *request = cookie;
  char *b_eof = "";
  char *b_error = "";

  if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
  if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
  EXPORTER_LOG(AL_ERROR, "SESS_PROC local connection terminated with %s %s", b_eof, b_error);
  if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
      if (request->guac_info && request->guac_info->gd_remote_server_proxy.local_guacd_bev) {
          zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.local_guacd_bev);
          request->guac_info->gd_remote_server_proxy.local_guacd_bev = NULL;
      }
      if (request->guac_info && request->guac_info->gd_remote_server_proxy.remote_proxy_sock > 0) {
          close(request->guac_info->gd_remote_server_proxy.remote_proxy_sock);
          request->guac_info->gd_remote_server_proxy.remote_proxy_sock = -1;
      }
      if (request->guac_info && request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
          zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
          request->guac_info->gd_remote_server_proxy.remote_proxy_bev = NULL;
      }
  }
}


/* guacd child proc connection failure handler
 * handles protocol(ssh/rdp/vnc) connection failure and sftp connection failures
 */
static void exporter_guacd_proxy_status_cb(struct bufferevent *bev, short style, void *cookie)
{
    struct guacd_proxy_childproc *guacd_childproc_data = cookie;
    char *b_eof = "";
    char *b_error = "";

    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";

    if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
        struct exporter_request *request = guacd_childproc_data->request;
        if (!request) goto free_childproc_data;
        ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
        guacd_childproc_data->request = NULL;
        /* Bufferevent free will close sock and free ssl */
        if (guacd_childproc_data->child_proc_bev) zlibevent_bufferevent_free(guacd_childproc_data->child_proc_bev);
        guacd_childproc_data->child_proc_bev = NULL;

        if (request->guac_info->gd_proxy.guacd_childproc_sftp != guacd_childproc_data) {
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_proxy_status_cb(protocol connection error): %p", request->name, request);
            request->guac_info->gd_proxy.guacd_childproc_data = NULL;
            request->ref_count--;
            gd_child_proxy_generic_server_destroy(request);
            /* Destroy the deferred sftp exporter request */
            if ((request->guac_info->gd_proxy.guacd_childproc_sftp == NULL) &&
                (request->guac_info->gd_proxy.sftp_exporter_request != NULL)) {
                /*if mt is not destroyed then we defer it to further up to main wss destroy */
                EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_proxy_status_cb destroy deferred sftp request", request->name);
                int res = sftp_request_destroy(request->guac_info->gd_proxy.sftp_exporter_request);
                if (res == ZPATH_RESULT_NO_ERROR) request->guac_info->gd_proxy.sftp_exporter_request = NULL;
            }
            EXPORTER_LOG(AL_ERROR, DBG_GUACD "Exporter connection to guacd child proc received error %s %s", b_eof, b_error);
        } else if (request->guac_info->gd_proxy.guacd_childproc_sftp == guacd_childproc_data) {
            /* SFTP connection received error */
            struct exporter_request *sftp_request = request->guac_info->gd_proxy.sftp_exporter_request;
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_proxy_status_cb(sftp connection error): %p", request->name, sftp_request);
            if (sftp_request) {
                sftp_request->http_response_complete = 1;
                EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_proxy_status_cb destroy sftp request", request->name);
                int res = sftp_request_destroy(sftp_request);
                /* if the mtunnel is async destroy, sftp request destroy will be deferred */
                if (res == ZPATH_RESULT_NO_ERROR) {
                /* if sftp request is destroyed nullify the sftp_exporter_request pointer
                 * sftp req destroy should be mostly async and destroyed by main wss request guac_info destroy */
                    request->guac_info->gd_proxy.sftp_exporter_request = NULL;
                }
                // else -- ref count of wss request is held until the sftp mt and sftp request is destroyed
                // conn wake on the wss conn will destroy the wss guac info and sftp request
            }
            request->ref_count--;
            request->guac_info->gd_proxy.guacd_childproc_sftp = NULL;
            EXPORTER_LOG(AL_ERROR, DBG_GUACD "Exporter SFTP connection to guacd child proc received error %s %s", b_eof, b_error);
        }
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        free_childproc_data:
        EXPORTER_FREE(guacd_childproc_data);
        exporter_guac_api_stats_increment(GUACD_SERVICE_CHILD_PROXY_CONNECTION_FAILED);
    }
}

static void sftp_request_reset_fields(struct exporter_request *request)
{
    memset(&(request->log), 0 , sizeof(struct zpn_http_trans_log));
    request->mt = NULL;
    request->mt_incarnation = 0;
    request->mt_destroy_sent = 0;
    request->mt_release_received = 0;
    request->async_count = 0;
    request->ref_count = 0;
    request->async_state = 0;
    request->response_data = NULL;
    request->request_body = NULL;
    request->lock = ZPATH_MUTEX_INIT;
    request->log.req_rx_start_us = epoch_us();
    //    request->portal_info = NULL; need assertion for sftp mtunnel, so do not reset
    request->user_portal_request_user_state = NULL;
    SET_EXPORTER_OBJ_VALID(request);
}

/* Restrict inbound connections to Exporter ephemeral listener */
static int restrict_inbound_connections(int sock, struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    struct sockaddr_storage local = {0};
    struct sockaddr_storage remote = {0};
    socklen_t len = sizeof(remote);
    char remote_ip_str[ARGO_INET_ADDRSTRLEN] = {'\0'};
    char local_ip_str[ARGO_INET_ADDRSTRLEN] = {'\0'};
    uint16_t local_port = 0;
    uint16_t remote_port = 0;

    ret = getpeername(sock, (struct sockaddr *)&remote, &len);
    if (ret != 0) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s Failed to get peername from sockfd", request->name);
        return ZPATH_RESULT_ERR;
    }

    fohh_sockaddr_storage_to_str(&remote, remote_ip_str, sizeof(remote_ip_str));
    remote_port = sockaddr_get_port_he(&remote);
    if (strncasecmp(remote_ip_str, ((gd_server_ip) ? gd_server_ip : EXPORTER_GUACD_IPADDR), strlen(remote_ip_str)) != 0) {
        ret = getsockname(sock, (struct sockaddr *)&local, &len);
        if(ret == 0) {
            fohh_sockaddr_storage_to_str(&local, local_ip_str, sizeof(local_ip_str));
            local_port = sockaddr_get_port_he(&local);
        }

        EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s Dropping inbound connection as ephemeral server(%s:%d) cannot accept connections from %s:%d", request->name, local_ip_str, local_port, remote_ip_str, remote_port);
        return ZPATH_RESULT_ERR;
    }

    return ret;
}

// read from the child proc conn and send straight to tunnel or to the local guacd if the proctored session is local
static void exporter_guacd_remote_proxy_read_cb(struct bufferevent *bev, void *cookie)
{
    struct evbuffer *read_buf = bufferevent_get_input(bev);
    struct exporter_request *request = cookie;
    size_t len;
    unsigned char *data;

    len = evbuffer_get_contiguous_space(read_buf);
    data = evbuffer_pullup(read_buf, len);
    if (!request->is_guac_sess_primary_exporter) {
        //Previously sent CLIENT_DATA here
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Sending exporter CLIENT_DATA equivalent request with name = %s and len = %ld", request->name, len);
        if (request->guac_info && request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s in proxy context for sending over ztunnel",__FUNCTION__);
            exporter_guacd_proxy_wake_on_request_thread(request, 0);
            //Note there is a downstream zfce_mt_consume that results in the drain equivalent to the local case
        }
    } else {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s in proxy context for same proxy exporter. Writing to gd_remote_server_proxy.local_guacd_bev",__FUNCTION__);
        if (request->guac_info->gd_remote_server_proxy.local_guacd_bev &&
            bufferevent_write(request->guac_info->gd_remote_server_proxy.local_guacd_bev, (void*)data, len) < 0) {
            ZPN_LOG(AL_ERROR, "SESS_PROC couldn't write data from the browser onto the connection to guacd for request name %s", request->name);
            goto fail;
        }
        if (evbuffer_drain(read_buf, len) != 0) {
            EXPORTER_LOG(AL_ERROR, "SESS_PROC Draining of the evbuffer failed in exporter_guacd_remote_proxy_read_cb from the local sock for %d",
                         request->guac_info->gd_remote_server_proxy.remote_proxy_sock);
            goto fail;
        }
    }
    return;
 fail:
    if (request->guac_info->gd_remote_server_proxy.local_guacd_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.local_guacd_bev);
        request->guac_info->gd_remote_server_proxy.local_guacd_bev = NULL;
    }
    if (request->guac_info->gd_remote_server_proxy.remote_proxy_sock > 0) {
        close(request->guac_info->gd_remote_server_proxy.remote_proxy_sock);
        request->guac_info->gd_remote_server_proxy.remote_proxy_sock = -1;
    }
    if (request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
        request->guac_info->gd_remote_server_proxy.remote_proxy_bev = NULL;
    }
	return;
}

static void exporter_guacd_child_proxy_accept_cb(struct fohh_thread *f_thread,
                                                 int sock, struct sockaddr *sa,
                                                 int sa_len,
                                                 const char *sni,
                                                 const char *sni_suffix,
                                                 void *cookie,
                                                 void *bev,
                                                 struct pktinfo_s *pktinfo)
{
    struct exporter_request *request = (struct exporter_request *) cookie;
    int res = ZPATH_RESULT_NO_ERROR;

    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Cookie obtained in accept callback is NULL");
        close(sock);
        return;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    request->ref_count++; /* Each guacd_proxy_childproc connection holds reference */
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    // validate using strncasecmp both addresses
    // from received sock connection and the one passed on command line
    // fohh_sockaddr_storage_to_str
    if (restrict_inbound_connections(sock, request) != ZPATH_RESULT_NO_ERROR)
        goto accept_cb_fail;

    if (request->conn->destroying || request->http_response_complete) {
        goto accept_cb_fail;
    }

#if EXPORTER_USE_ZEVENT
    res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, exporter_guacd_child_proxy_accept_cb_on_thread, cookie, sock);
#else
    res = fohh_thread_call(request->conn->thread->fohh_thread_id, exporter_guacd_child_proxy_accept_cb_on_thread, cookie, sock);
#endif
    if (res) {
        EXPORTER_LOG(AL_ERROR, "Could not add event to thread (exporter_guacd_child_proxy_accept_cb_on_thread): %s", zpath_result_string(res));
        // fall through to accept_cb_fail
    } else {
        return;
    }

accept_cb_fail:
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    request->ref_count--;
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    EXPORTER_LOG(AL_WARNING, "Drop guacd child proxy conn");
    close(sock);
}

/* Perform the accept callback handling in the context of request thread
 * No need of request lock
 */
#if EXPORTER_USE_ZEVENT
static void exporter_guacd_child_proxy_accept_cb_on_thread(struct zevent_base *zbase, void *cookie, int64_t sock)
#else
static void exporter_guacd_child_proxy_accept_cb_on_thread(struct fohh_thread *thread, void *cookie, int64_t sock)
#endif
{
    int guacd_proxy_child_proc_thread_id; // = fohh_thread_get_id(f_thread);
    struct guacd_proxy_childproc *guacd_childproc_data = NULL;
    struct exporter_request *request = (struct exporter_request *) cookie;
    int setup_mtunnel_for_sftp = 0;
    int ret = ZPATH_RESULT_NO_ERROR;

    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Cookie obtained in accept callback is NULL");
        goto fail;
    }

    if (request->conn->destroying || request->http_response_complete) {
        goto fail;
    }

    guacd_childproc_data = EXPORTER_MALLOC( sizeof(struct guacd_proxy_childproc));
    guacd_proxy_child_proc_thread_id = request->conn->thread->fohh_thread_id;

    if (!guacd_childproc_data) goto fail;
    memset(guacd_childproc_data, 0, sizeof(struct guacd_proxy_childproc));

    // allocate a structure for accepted connection with the child proc
    guacd_childproc_data->child_proc_base = fohh_get_thread_event_base(guacd_proxy_child_proc_thread_id);
    evutil_make_socket_nonblocking(sock);
    guacd_childproc_data->child_proc_bev =  bufferevent_socket_new(guacd_childproc_data->child_proc_base, sock,
            BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS | BEV_OPT_CLOSE_ON_FREE | BEV_OPT_UNLOCK_CALLBACKS);
    if (!guacd_childproc_data->child_proc_bev) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "Failed no bev");
        goto fail;
    }

    guacd_childproc_data->request = request;
    if (!guacd_childproc_data->request) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " Stitching of connection failed ");
        exporter_guac_api_stats_increment(GUACD_SERVICE_STICHING_CONNECTION_FAILED);
        goto fail;
    }

    if (request->guac_info->gd_proxy.guacd_childproc_data && request->guac_info->gd_proxy.guacd_childproc_sftp) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " WSS request has already been stitched to child proc. Dropping this new child proc connection(sockfd %"PRId64") ....", sock);
        goto fail;
    } else if (request->guac_info->is_sftp_disabled && request->guac_info->gd_proxy.guacd_childproc_data) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " WSS request has already been stitched to child proc. File transfer is disabled. Cannot accept second connection");
        goto fail;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    {
        if (!request->guac_info->gd_proxy.guacd_childproc_data) {
            EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s Initializing guacd_childproc_data in request pointer", request->name);
            request->guac_info->gd_proxy.guacd_childproc_data = guacd_childproc_data;
            zfce_mt_unblock(request->mt);
            request->need_to_unblock_source = 0;
        } else if (!request->guac_info->gd_proxy.guacd_childproc_sftp ) {
            // would be good to add separate listener sftp port
            EXPORTER_LOG(AL_ERROR, DBG_GUACD "%s Initializing guacd_childproc_sftp in request pointer", request->name);
            request->guac_info->gd_proxy.guacd_childproc_sftp = guacd_childproc_data;
            setup_mtunnel_for_sftp = 1;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    if (setup_mtunnel_for_sftp) {
        /* New SFTP exporter request created for sftp mtunnel
         * untracked by the main wss conn and its not part of the conn's request list
         * this new reqest is accessible through the WSS request internal guac structures
         * New SFTP request will have a new mt object referencing the new mtunnel
         */
        struct exporter_request *sftp_request = ephemeral_exporter_request_create();
        if (!sftp_request) {
            EXPORTER_LOG(AL_ERROR, DBG_GUACD "Memory allocation for SFTP request failed");
            goto fail;
        }
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s SFTP request allocated", request->name);

        /* copy the wss request fields (reuse conn, assertion, guac_info pointers)
         * reset few fields and set new connection id, port into sftp request */
        memcpy(sftp_request, request, sizeof(*request));
        sftp_request->guac_info->is_sftp_connection = 1; // not needed delete later
        sftp_request_reset_fields(sftp_request);
        __sync_fetch_and_add_4(&(sftp_request->async_count), 1); // this will help stubbing out the async state machine for sftp request
        request->guac_info->gd_proxy.sftp_exporter_request = sftp_request;
        request->guac_info->gd_proxy.sftp_conn_id = get_new_connection_id();
        /* For VNC, SFTP port is non-configurable and its fixed to port 22 */
        request->guac_info->gd_proxy.sftp_port = (strncasecmp(request->sra_host_protocol, "SSH", 3) == 0) ? request->sra_host_port : SFTP_PORT;

        ret = exporter_guac_setup_mtunnel(sftp_request);
        if (!((ZPN_RESULT_ASYNCHRONOUS == ret) || (ZPATH_RESULT_NO_ERROR == ret))) {
             EXPORTER_LOG(AL_ERROR, "%s: Could not find an m-tunnel for file transfer connection", request->name);
             goto fail;
        }

        struct zpn_fohh_client_exporter_mt *mt = sftp_request->guac_info->gd_proxy.cookie;

        if (request->mt) {
            mt->console_cred_type = request->mt->console_cred_type;
        } else {
            mt->console_cred_type = request->guac_info->diag_info.console_cred_type;
        }

        if (request->mt) {
            exporter_populate_log_conn_id(sftp_request, request->mt->pra_conn_id);
        }

        zpn_fohh_client_exporter_send_exporter_log_data(mt);
    }

    /// set callbacks here
    bufferevent_setcb(guacd_childproc_data->child_proc_bev,
                      exporter_guacd_proxy_read_cb,
                      NULL, //exporter_guacd_proxy_write_cb,
                      exporter_guacd_proxy_status_cb,
                      guacd_childproc_data);

    bufferevent_setwatermark(guacd_childproc_data->child_proc_bev, EV_READ, 0, EXPORTER_MAX_BUFFER_USER_DATA);

    bufferevent_enable(guacd_childproc_data->child_proc_bev, EV_READ|EV_WRITE);

    EXPORTER_LOG(AL_NOTICE, DBG_GUACD "(THREAD ID %d) set child proc read write buffer event on accepted connection - complete", guacd_proxy_child_proc_thread_id);
    return;

    fail:
    EXPORTER_LOG(AL_WARNING, "Drop guacd child proxy conn");
    if (request) request->ref_count--; /* Drop reference held by guacd_proxy_childproc connection */

    // destroy the guacd_childproc_structure
    if (guacd_childproc_data) {
        guacd_childproc_data->request = NULL;
        /* Bufferevent free will close sock and free ssl */
        if (guacd_childproc_data->child_proc_bev) zlibevent_bufferevent_free(guacd_childproc_data->child_proc_bev);
        guacd_childproc_data->child_proc_bev = NULL;
        EXPORTER_FREE(guacd_childproc_data);
        guacd_childproc_data = NULL;
    } else {
        close(sock);
    }
}


int gd_child_proxy_generic_server_accept(struct exporter_request *request)
{
    int res;
    res = fohh_generic_server_register_accept(request->guac_info->gd_proxy.guacd_child_proxy_server,
                          NULL,
                          exporter_guacd_child_proxy_accept_cb,
                          request,
                          NULL,
                          NULL,
                          0,
                                              NULL);
                                              //					      "exp_guacd_child");
    if (res) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " Failed fohh_generic_server_register_accept");
    }
    return res;
}



static void exporter_guacd_remote_proxy_accept_cb(struct fohh_thread *f_thread __attribute__((unused)),
                                                  int sock, struct sockaddr *sa __attribute__((unused)),
                                                  int sa_len __attribute__((unused)),
                                                  const char *sni __attribute__((unused)),
                                                  const char *sni_suffix __attribute__((unused)),
                                                  void *cookie,
                                                  void *bev __attribute__((unused)),
                                                  struct pktinfo_s *pktinfo __attribute__((unused)))
{
    struct exporter_request *request = (struct exporter_request *) cookie;
    struct bufferevent *local_bev = NULL;
    struct event_base *event_base = NULL;

    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD "SESS_PROC:Cookie obtained in accept callback is NULL");
        goto fail;
    }

    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Accepted remote proxy conn. Remote exporter domain name %s",
		 request->remote_exporter_domain);
    // validate using strncasecmp both addresses
    // from received sock connection and the one passed on command line
    // fohh_sockaddr_storage_to_str
    if (restrict_inbound_connections(sock, request) != ZPATH_RESULT_NO_ERROR) {
        goto fail;
    }
    event_base = fohh_get_thread_event_base(request->conn->thread->fohh_thread_id);
    evutil_make_socket_nonblocking(sock);
    local_bev =  bufferevent_socket_new(event_base, sock,
                                        BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS | BEV_OPT_CLOSE_ON_FREE | BEV_OPT_UNLOCK_CALLBACKS);
    if (!local_bev) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC: Failed no bev when creating connection to local guacd for conn %s", request->name);
        goto fail;
    }

    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Initializing local bevs in request pointer");
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    {
        request->guac_info->gd_remote_server_proxy.remote_proxy_bev = local_bev;
        request->guac_info->gd_remote_server_proxy.remote_proxy_sock = sock;
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    //If the session we are connecting to is not the current exporter
    if (!request->is_guac_sess_primary_exporter) {
        //Previously sent CONN_START here
        /* Send data using mtunnel to exporter */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Not sending exporter ZPA request with name = %s", request->name);
    } else {
        /* If the proxy request lamded on the same exporter as original connection request */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Sending local guac initiation request");
        if (create_proxy_guacd_client(request) < 0) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Failed to create proxy guacd client for %s", request->name);
            goto fail;
        }
    }

    /// set callbacks here
    bufferevent_setcb(local_bev,
                      exporter_guacd_remote_proxy_read_cb, //Received from guac server proxy
                      NULL,
                      exporter_guacd_remote_server_proxy_status_cb, //handle failure and connection closes
                      request);

    bufferevent_setwatermark(local_bev, EV_READ, 0, EXPORTER_MAX_BUFFER_USER_DATA);
    bufferevent_enable(local_bev, EV_READ|EV_WRITE);

    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC (THREAD ID %d) set child proc read write buffer event on accepted connection - complete", request->conn->thread->fohh_thread_id);

    return;
    fail:
        close(sock);
}



int gd_remote_proxy_generic_server_accept(struct exporter_request *request)
{
    int res;
    res = fohh_generic_server_register_accept(request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server,
                                              NULL,
                                              exporter_guacd_remote_proxy_accept_cb,
                                              request,
                                              NULL,
                                              NULL,
                                              0,
                                              NULL);
    if (res) {
        EXPORTER_LOG(AL_ERROR, DBG_GUACD " Failed fohh_generic_server_register_accept");
    }
    return res;
}



static void gd_child_proxy_generic_server_destroy(struct exporter_request *request)
{
    fohh_generic_server_close(request->guac_info->gd_proxy.guacd_child_proxy_server);
    if (request->is_proxy_conn && request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server) {
        fohh_generic_server_close(request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server);
    }
    // delay free (exporter request guac_info destroy will release memory)
    // request->guac_info->gd_proxy.guacd_child_proxy_server = NULL;
}

/* Handle dangling accept callback events processed after socket close
 * Accept callbacks have a reference to the listener cookie
 * Process the deferred free of generic servers and listeners
 */
void gd_child_proxy_generic_server_free(struct exporter_request *request)
{
    EXPORTER_DEBUG_GUACD("DBG_GUACD %s gd_child_proxy_generic_server_free release memory for generic listener and generic server", request->name);
    fohh_generic_server_free(request->guac_info->gd_proxy.guacd_child_proxy_server);
    request->guac_info->gd_proxy.guacd_child_proxy_server = NULL;
}

static int gd_child_proxy_generic_server_create(struct exporter_request *request)
{
    if (request->guac_info->gd_proxy.guacd_child_proxy_server) {
        EXPORTER_LOG(AL_NOTICE, "gd_child_proxy_generic_server_create: Generic Server already created for this wss conn");
        return ZPATH_RESULT_NO_ERROR;
    }

    request->guac_info->gd_proxy.guacd_child_proxy_server = fohh_generic_server_create(0, 0);
    if (!request->guac_info->gd_proxy.guacd_child_proxy_server) {
        EXPORTER_LOG(AL_ERROR, "Could not create generic guacd child proxy server");
        exporter_guac_api_stats_increment(GUACD_SERVICE_CHILD_PROXY_SERVER_CREATE_FAILED);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int gd_remote_proxy_generic_server_create(struct exporter_request *request)
{
  if (request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC gd_remote__proxy_generic_server_create: Generic Server already created for this wss conn");
        return ZPATH_RESULT_ERR;
  }

    request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server = fohh_generic_server_create(0, 0);
    if (!request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC: Could not create generic guacd remote proxy server");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int gd_remote_proxy_generic_server_listen(struct exporter_request *request)
{
    /*Start guacd server proxy listener - for all child proc ssh/rdp/vnc connections - controlled by ssh forwarding */
    struct sockaddr_storage addr;
    socklen_t addr_len = sizeof(struct sockaddr_storage);
    int res;

    res = fohh_str_to_sockaddr_storage(request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_addr,
                                           &addr, &addr_len);
    fohh_sockaddr_set_port(&addr, htons(request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_port));
    if (res < 0) { EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: DBG_GUACD error in initializing the sockaddr remote server proxy listener");}

    res = fohh_generic_server_listen(request->guac_info->gd_remote_server_proxy.guacd_remote_proxy_server, (struct sockaddr *)&addr, addr_len, 0 /* Don't do proxy protocol */);
    if (res) {
        EXPORTER_LOG(AL_CRITICAL, "DBG_GUACD Could not listen for clients, port %d, reason = %s",
                             request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_port, fohh_result_strings[res]);
        return res;
    } else {
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: DBG_GUACD Listening on localhost, port %d for guacd remote server proxy", request->guac_info->gd_remote_server_proxy.guacd_remote_server_proxy_listener_port);
    }
    res = gd_remote_proxy_generic_server_accept(request);
    if (res) {
        EXPORTER_LOG(AL_CRITICAL, "SESS_PROC DBG_GUACD Could not create registerant for accept for remote proxy server: reason = %s", fohh_result_strings[res]);
    }
    return res;
}

static int gd_child_proxy_generic_server_listen(struct exporter_request *request)
{
  /*Start guacd remote server proxy listener - to forward all proctored traffic to remote exporter */
  struct sockaddr_storage addr;
  socklen_t addr_len = sizeof(struct sockaddr_storage);
  int res;

    res = fohh_str_to_sockaddr_storage(request->guac_info->gd_proxy.guacd_childproc_listener_addr,
                                           &addr, &addr_len);
    fohh_sockaddr_set_port(&addr, htons(request->guac_info->gd_proxy.guacd_childproc_listener_port));
    if (res < 0) { EXPORTER_LOG(AL_NOTICE, "DBG_GUACD error in initializing the sockaddr server proxy listener");}

    res = fohh_generic_server_listen(request->guac_info->gd_proxy.guacd_child_proxy_server, (struct sockaddr *)&addr, addr_len, 0 /* Don't do proxy protocol */);
    if (res) {
        EXPORTER_LOG(AL_CRITICAL, "DBG_GUACD Could not listen for clients, port %d, with proxy protocol, localhost, reason = %s",
                             request->guac_info->gd_proxy.guacd_childproc_listener_port, fohh_result_strings[res]);
        exporter_guac_api_stats_increment(GUACD_SERVICE_CHILD_PROXY_SERVER_LISTEN_FAILED);
        return res;
    } else {
      EXPORTER_LOG(AL_NOTICE, "DBG_GUACD %s Listening on localhost, port %d for guacd child proc proxy protocol", request->name, request->guac_info->gd_proxy.guacd_childproc_listener_port);
    }
    res = gd_child_proxy_generic_server_accept(request);
    if (res) {
        EXPORTER_LOG(AL_CRITICAL, "DBG_GUACD %s Could not create registerant for accept: reason = %s", request->name, fohh_result_strings[res]);
        exporter_guac_api_stats_increment(GUACD_SERVICE_CHILD_PROXY_SERVER_ACCEPT_FAILED);
    }
    return res;
}



static void exporter_guacd_child_protocol_conn_close(struct exporter_request *request)
{
    struct guacd_proxy_childproc *guacd_childproc_data;
    /* Bufferevent free will disable events, remove callbacks, close sock(BEV_OPT_CLOSE_ON_FREE) */
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (request->guac_info->gd_proxy.guacd_childproc_data) {
        guacd_childproc_data = request->guac_info->gd_proxy.guacd_childproc_data;
        if (guacd_childproc_data->child_proc_bev) zlibevent_bufferevent_free(guacd_childproc_data->child_proc_bev);
        guacd_childproc_data->child_proc_bev = NULL;

        EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_child_protocol_conn_close due to mtunnel errors (protocol connection close): %p", request->name, request);
        request->guac_info->gd_proxy.guacd_childproc_data = NULL;
        request->ref_count--;
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        gd_child_proxy_generic_server_destroy(request);
        /* Destroy the deferred sftp exporter request */
        if ((request->guac_info->gd_proxy.guacd_childproc_sftp == NULL) &&
            (request->guac_info->gd_proxy.sftp_exporter_request != NULL)) {
            /*if mt is not destroyed then we defer it to further up to main wss destroy */
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_child_protocol_conn_close destroy deferred sftp request", request->guac_info->gd_proxy.sftp_exporter_request->name);
            int res = sftp_request_destroy(request->guac_info->gd_proxy.sftp_exporter_request);
            if (res == ZPATH_RESULT_NO_ERROR) request->guac_info->gd_proxy.sftp_exporter_request = NULL;
        }
        EXPORTER_FREE(guacd_childproc_data);
        EXPORTER_LOG(AL_DEBUG, DBG_GUACD "Exporter protocol connection to guacd child proc closed due to mtunnel errors");
    } else {
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    }
}

void exporter_guacd_child_sftp_conn_close(struct exporter_request *request)
{
    struct guacd_proxy_childproc *guacd_childproc_data;

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (request->guac_info->gd_proxy.guacd_childproc_sftp) {
        guacd_childproc_data = request->guac_info->gd_proxy.guacd_childproc_sftp;
        if (guacd_childproc_data->child_proc_bev) zlibevent_bufferevent_free(guacd_childproc_data->child_proc_bev);
        guacd_childproc_data->child_proc_bev = NULL;

        struct exporter_request *sftp_request = request->guac_info->gd_proxy.sftp_exporter_request;
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_child_sftp_conn_close due to mtunnel errors (sftp connection close): %p", request->name, sftp_request);
        if (sftp_request) {
            sftp_request->http_response_complete = 1;
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s exporter_guacd_child_sftp_conn_close destroy sftp request", sftp_request->name);
            int res = sftp_request_destroy(sftp_request);
            /* if the mtunnel is async destroy, sftp request destroy will be deferred */
            if (res == ZPATH_RESULT_NO_ERROR) {
            /* if sftp request is destroyed nullify the sftp_exporter_request pointer
             * sftp req destroy should be mostly async and destroyed by main wss request guac_info destroy */
                request->guac_info->gd_proxy.sftp_exporter_request = NULL;
            }
        }
        request->ref_count--;
        request->guac_info->gd_proxy.guacd_childproc_sftp = NULL;
        EXPORTER_FREE(guacd_childproc_data);
        EXPORTER_LOG(AL_DEBUG, DBG_GUACD "Exporter SFTP connection to guacd child proc closed due to mtunnel errors");
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
}

void exporter_guacd_remote_server_proxy_conn_close(struct exporter_request *request)
{
    if(request->is_proxy_conn && request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
        request->guac_info->gd_remote_server_proxy.remote_proxy_bev = NULL;
    }
    if (request->is_proxy_conn && request->guac_info->gd_remote_server_proxy.local_guacd_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.local_guacd_bev);
        request->guac_info->gd_remote_server_proxy.local_guacd_bev = NULL;
    }

}

void exporter_guacd_child_conn_close(struct exporter_request *request)
{
    exporter_guacd_child_protocol_conn_close(request);
    exporter_guacd_child_sftp_conn_close(request);
}

int check_guacd_state()
{

    int clientSocket;
    char buffer[128] = {'\0'};
    struct sockaddr_storage serverAddr;
    socklen_t addr_size = sizeof(struct sockaddr_storage);
    int res = -1;
    const char *serverIP = NULL;
    struct sockaddr_in6 a6;

    if (gd_server_ip){
        serverIP = gd_server_ip;
    } else {
        serverIP = EXPORTER_GUACD_IPADDR;
    }

    clientSocket = socket(inet_pton(AF_INET6, serverIP, &a6.sin6_addr)?AF_INET6:AF_INET, SOCK_STREAM, 0);
    if (clientSocket < 0){
        EXPORTER_LOG(AL_ERROR, "Create socket error");
        return ZPATH_RESULT_ERR;
    }

    res = exporter_guacd_init_sockaddr(&serverAddr, &addr_size, NULL);
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, "Failed to convert str to a valid sockaddr. Check the input args ...");
        close(clientSocket);
        return ZPATH_RESULT_ERR;
    }

    res = connect(clientSocket, (struct sockaddr *) &serverAddr, addr_size);
    if (res < 0) {
        close(clientSocket);
        return ZPATH_RESULT_ERR;
    }

    snprintf(buffer, sizeof(buffer), "%s", "6.select,3.ssh;");
    if (send(clientSocket, buffer, 128, 0) < 0){
        EXPORTER_LOG(AL_ERROR, "Socket send() failed");
        close(clientSocket);
        return ZPATH_RESULT_ERR;
    }

    memset(&buffer, '\0', 128);

    if(recv(clientSocket, buffer, 128, 0) < 0){
        EXPORTER_LOG(AL_ERROR, "Socket recv() failed");
        close(clientSocket);
        return ZPATH_RESULT_ERR;
    }

    if(strncmp(buffer, "4.args", strlen("4.args")) != 0){
        close(clientSocket);
        return ZPATH_RESULT_ERR;
    }

    close(clientSocket);
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guacd_status_callback(struct zpath_debug_state *request_state,
                          const char **query_values,
                          int query_value_count,
                          void *cookie)
{
#ifdef __linux__
    if (guacd_update_pid() == ZPATH_RESULT_NO_ERROR){
        if (guacd_validate_pid() == ZPATH_RESULT_ERR){
            zpath_debug_cb_printf_response(request_state, "not running\n");
            return ZPATH_RESULT_NO_ERROR;
        }
    } else{
        zpath_debug_cb_printf_response(request_state, "not running\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    zpath_debug_cb_printf_response(request_state, "running\n");
    return ZPATH_RESULT_NO_ERROR;
#else
    zpath_debug_cb_printf_response(request_state, "Unsupported OS. This endpoint works with only linux OS\n");
    return ZPATH_RESULT_NO_ERROR;
#endif
}

static void exporter_send_data_to_remote_guacd_proxy(struct bufferevent *bev, void *cookie)
{
    struct evbuffer *read_buf = bufferevent_get_input(bev);
    struct exporter_request *request = (struct exporter_request *) cookie;
    size_t len;
    unsigned char *data;

    len = evbuffer_get_contiguous_space(read_buf);
    data = evbuffer_pullup(read_buf, len);

    /* If the proxy request lamded on the same exporter as original connection request */
    /* Used to be for SERVER_DATA */
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s in proxy context for same proxy exporter as original",__FUNCTION__);
    if (bufferevent_write(request->guac_info->gd_remote_server_proxy.remote_proxy_bev, (void*)data, len) < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Closing the socket to the guac client because we could not propagate data for request == %s", request->name);
        goto fail;
    }
    if (evbuffer_drain(read_buf, len) != 0) {
        EXPORTER_LOG(AL_ERROR,  "SESS_PROC Failed to drain evbuffer for connection %s when reading from local guac server", request->name);
        goto fail;
    }
    return;
 fail:
    if(request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
        request->guac_info->gd_remote_server_proxy.remote_proxy_bev = NULL;
    }
    if (request->guac_info->gd_remote_server_proxy.local_guacd_bev) {
        zlibevent_bufferevent_free(request->guac_info->gd_remote_server_proxy.local_guacd_bev);
        request->guac_info->gd_remote_server_proxy.local_guacd_bev = NULL;
    }
}

#if 0
void mt_remote_guacd_server_cb_on_destroy(struct zpn_exporter_mtunnel *mt)
{
    if (mt->guac_remote_proxy_bev) {
        zlibevent_bufferevent_free(mt->guac_remote_proxy_bev);
        mt->guac_remote_proxy_bev = NULL;
    }
}
#endif

void
exporter_proctoring_process_user_event(struct zevent_base *base __attribute__((unused)),
                                       void *void_info, int64_t int_task __attribute__((unused)))
{
    struct exporter_proctoring_session_event_info *info = (struct exporter_proctoring_session_event_info *) void_info;
    if (!info || !info->orig_request) {
        return;
    }

    if (info->orig_request->destroy_session_entry == 0) {
        switch (info->event_type) {
            case exporter_shares_session_event_join:
                exporter_session_share_user_join_success(info);
            break;
            case exporter_shares_session_event_disconnect:
                exporter_update_pra_sess_ostore_data_terminate_event(info);
            break;
            case exporter_shares_session_event_control:
                exporter_pra_update_ostore_session_control_event(info);
            break;
            case exporter_shares_session_event_eject:
                exporter_pra_update_ostore_session_control_eject(info);
            break;
            default:
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC unknown event %d", info->event_type);
        }
    }
    info->orig_request->ref_count--;

    if (info->user) {
        EXPORTER_FREE(info->user);
        info->user = NULL;
    }
    if (info) {
        EXPORTER_FREE(info);
        info = NULL;
    }
}

void exporter_proctoring_schedule_user_event(struct exporter_request *orig_request,
                                enum exporter_shares_session_event_type event_type,
                                const char *user_email,
                                int64_t capability_policy_id,
                                struct exporter_request *request)
{
    struct exporter_proctoring_session_event_info *info = EXPORTER_CALLOC(sizeof(struct exporter_proctoring_session_event_info));
    info->user          = EXPORTER_STRDUP(user_email, strlen(user_email));
    info->event_type    = event_type;
    info->orig_request  = orig_request;
    info->request       = request;
    info->capability_policy_id = capability_policy_id;

    orig_request->ref_count++;

    if (FOHH_RESULT_NO_ERROR != fohh_thread_call_zevent(orig_request->conn->thread->fohh_thread_id,
        exporter_proctoring_process_user_event, (void *)info, 0)) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC failed to call zevent process_user_event, request %s", orig_request->name);
    }
}

/* This is running in the context of proxy exporter, receive ack error if user is not allowed to join */
int mt_remote_guacd_server_cb_on_ack_update(char *mtunnel_id, const char *error, int32_t cmd_type, int64_t cmd_data)
{
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Proxy process data from primary. mt %s, cmd %d, data %"PRId64", error %s",
        mtunnel_id, cmd_type, cmd_data, error);

    /* Lookup proxy request for mtunnel id in hash */
    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    struct exporter_request *proxy_request = zhash_table_lookup(global_exporter.proxy_mtunnel_id, mtunnel_id, strlen(mtunnel_id), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);

    if (!proxy_request) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Could not get proxy request for mtunnel id %s", mtunnel_id);
    } else {
        switch (cmd_type) {
            case exporter_shares_session_event_error:
            {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Got proxy request %s for mtunnel id %s", proxy_request->name, mtunnel_id);
                proxy_request->mt->event_type = exporter_shares_session_event_error;
                ZPATH_MUTEX_LOCK(&(proxy_request->lock), __FILE__, __LINE__);
                exporter_guac_send_response(proxy_request,
                    HTTP_STATUS_INTERNAL_SERVER_ERROR,
                    "%s",
                    error);
                ZPATH_MUTEX_UNLOCK(&(proxy_request->lock), __FILE__, __LINE__);
                exporter_sharing_stats_inc(proctor_session_join_fail_type);
            }
            break;
            case exporter_shares_session_event_control:
            {
                proxy_request->share_ctxt.key_mouse_enabled = cmd_data & 1;
                exporter_guac_proxy_browser_push_notification_keymouse_refresh(proxy_request);
            }
            break;
            case exporter_shares_session_event_console_info:
            {
                /* Two exporter case, push console information to browser */
                exporter_guac_proxy_browser_push_notification_console_info(proxy_request, NULL, (char *)error);
            }
            break;
            default:
                EXPORTER_LOG(AL_WARNING, "SESS_PROC Unknown command from primary exporter");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* This is running in the context of primary exporter */
int mt_remote_guacd_server_cb_on_log_update(struct zpn_exporter_mtunnel *mt)
{
    struct exporter_request *request = NULL;
    int ret = ZPATH_RESULT_ERR;
    if (!mt || !mt->pra_conn_id) {
        return ret;
    }
    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    request = zhash_table_lookup(global_exporter.proxy_conn_id, mt->pra_conn_id, strnlen(mt->pra_conn_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    if (!request) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", mt->pra_conn_id);
        return ret;
    }

    EXPORTER_DEBUG_SESSION_SHARING( "SESS_PROC: Received event from proxy for conn id %s, event %s, user_email %s mtunnel id %s capability_policy_id: %"PRId64"",
        mt->pra_conn_id, (mt->event_type == 1 ) ? "join" : "disconnect", mt->user_email, mt->mtunnel_id, mt->capabilities_policy_id);

    if (mt->event_type == exporter_shares_session_event_join) {
        char message[EXPORTER_JOIN_EVENT_ACK_ERROR_LEN] = {'\0'};

        ret = exporter_check_user_join(request->cached_sess_data, mt->pra_conn_id, mt->user_email, message, request->max_join_users_limit);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "[SESS_PROC] User %s cannot join.", mt->user_email);
            /* send rpc from primary exporter to proxy about user-join fail */
            ret = zpn_send_exporter_data_ack(mt, message, exporter_shares_session_event_error, 0);
            if (ret) {
                EXPORTER_LOG(AL_ERROR, "SESS_PROC mtunnel_id = %s Failed to send mtunnel exporter ack error %s", mt->mtunnel_id, message);
            } else {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC mtunnel_id = %s Successfully send mtunnel exporter ack error %s", mt->mtunnel_id, message);
            }
        } else {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] User %s can join. Allow to connect.", mt->user_email);
            exporter_proctoring_schedule_user_event(request, mt->event_type, mt->user_email, mt->capabilities_policy_id, NULL);

            exporter_guac_generate_json_console_info_response(request, message, EXPORTER_JOIN_EVENT_ACK_ERROR_LEN);
            /* send rpc from primary exporter to proxy about console info */
            ret = zpn_send_exporter_data_ack(mt, message, exporter_shares_session_event_console_info, 0);
            if (ret) {
                EXPORTER_LOG(AL_ERROR, "SESS_PROC mtunnel_id = %s Failed to send mtunnel exporter console info %s", mt->mtunnel_id, message);
            } else {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC mtunnel_id = %s Successfully send mtunnel exporter console info %s", mt->mtunnel_id, message);
            }
            exporter_sharing_stats_inc(proctor_session_join_success_type);
        }
    } else if (mt->event_type == exporter_shares_session_event_disconnect) {
        exporter_proctoring_schedule_user_event(request, mt->event_type, mt->user_email,  mt->capabilities_policy_id, NULL);
    }

    return ZPATH_RESULT_NO_ERROR;
}


int mt_remote_guacd_server_cb_on_associate(struct zpn_exporter_mtunnel *mt)
{
    struct exporter_request *request = NULL;
    struct zpn_exporter_mtunnel_elem *elem = NULL;
    int found = 0;

    if (!mt) {
        return ZPATH_RESULT_ERR;
    }
    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    request = zhash_table_lookup(global_exporter.proxy_conn_id, mt->pra_conn_id, strnlen(mt->pra_conn_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    if (!request) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", mt->pra_conn_id);
        return ZPATH_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);

    /* do not add again if it exists in the list */
    ZLIST_FOREACH(elem, &(request->guac_info->mtunnels_list), list) {
        if (elem->mtunnel == (void*)mt) {
            found = 1;
        }
    }
    /* Found the request associated with this conn-id */
    /* Store the mt in the guac_info for request */
    if (!found) {
        elem = EXPORTER_CALLOC(sizeof(struct zpn_exporter_mtunnel_elem));
        elem->mtunnel = (void*)mt;
        ZLIST_INSERT_HEAD(&request->guac_info->mtunnels_list, elem, list);
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Added mtunnel %p to mtunnel_list for %s", mt, request->name);
    }

    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

int mt_remote_guacd_server_cb_on_destroy(struct zpn_exporter_mtunnel *mt)
{
    struct exporter_request *request = NULL;
    struct zpn_exporter_mtunnel_elem *elem = NULL;

    if (!mt) {
        return ZPATH_RESULT_ERR;
    }
    if (mt->pra_conn_id) {
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        request = zhash_table_lookup(global_exporter.proxy_conn_id, mt->pra_conn_id, strnlen(mt->pra_conn_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    } else {
        EXPORTER_LOG(AL_INFO, "PRA conn ID is not present in mtunnel for mt %p\n",mt);
        return ZPATH_RESULT_ERR;
    }
    if (!request) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", mt->pra_conn_id);
        return ZPATH_RESULT_ERR;
    }

    if (!request->guac_info) {
        EXPORTER_LOG(AL_INFO, "SESS_PROC guac_info has already been freed for %s",request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Found the request associated with this conn-id */
    /* Remove the mt in the guac_info for request */
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    ZLIST_FOREACH(elem, &(request->guac_info->mtunnels_list), list) {
        if (elem->mtunnel == (void*)mt) {
            /* Send disconnects to the mconn's associated with the mtunnels */
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Sending disconnect for mtunnel %p from mtunnels_list for %s", elem->mtunnel, request->name);
            int res = exporter_guac_mconn_terminate(elem->mtunnel);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s: mtunnel_id = %s  Terminating exporter session proctoring mconn returned %s",
                        request->name, (char *)elem->mtunnel, zpn_result_string(res));
            }
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Removed mtunnel %p from mtunnels_list for %s", mt, request->name);
            ZLIST_REMOVE(elem, list);
            EXPORTER_FREE(elem);
            break;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

int mt_remote_guacd_server_cb_on_connect(struct zpn_exporter_mtunnel *mt)
{
    /* Initiate a connect to guacd */
    // Verify and connect to guacd service running non-locally TODO later
    struct sockaddr_storage addr;
    socklen_t addr_len = sizeof(struct sockaddr_storage);
    struct bufferevent *bev = NULL;
    struct event_base *base = NULL;
    struct zpn_mconn *mconn = NULL;
    int res = -1;
    int thread_id = 0;

    if (!mt) {
        goto gd_connect_fail;
    }

    mconn = zpn_exporter_mtunnel_client_mconn(mt);
    (void) zpn_mconn_get_fohh_thread_id(mconn, &thread_id);
    base = fohh_get_thread_event_base(thread_id);

    bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
    //This is used by the mconn and then that eventually is peered with the incoming fohh_tlv_mconn
    mt->guac_remote_proxy_bev = bev;
    if (!bev) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC: Could not allocate bufferevent socket");
        goto gd_connect_fail;
    }
    bufferevent_enable(bev, EV_READ|EV_WRITE);

    res = exporter_guacd_init_sockaddr(&addr, &addr_len, NULL);
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Failed to convert str to a valid sockaddr. Check the input args ...");
        goto gd_connect_fail;
    }

    if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Could not connect to guacd server from exporter", mt->mtunnel_id);
        goto gd_connect_fail;
    }
    int sock = bufferevent_getfd(bev);
    int tmp_val = 1;

    res = setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Failed to set socket options TCP_NODELAY");
        goto gd_connect_fail;
    }
    EXPORTER_LOG(AL_ERROR, "SESS_PROC successfully connected for mtunnel request = %s", mt->mtunnel_id);
    return ZPATH_RESULT_NO_ERROR;
gd_connect_fail:
    if (bev) {
        zlibevent_bufferevent_free(bev);
        bev = NULL;
    }
    if (mt) {
        mt->guac_remote_proxy_bev = NULL;
    }
    return ZPATH_RESULT_ERR;
}


int create_proxy_guacd_client(struct exporter_request *request)
{
    /* Initiate a connect to guacd */
    // Verify and connect to guacd service running non-locally TODO later
    struct sockaddr_storage addr;
    socklen_t addr_len = sizeof(struct sockaddr_storage);
    struct bufferevent *bev = NULL;
    struct event_base *base = NULL;
    int res = -1;

    if (!request) {
        goto gd_connect_fail;
    }

    base = fohh_get_thread_event_base(request->conn->thread->fohh_thread_id);
    bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);

    bufferevent_setcb(bev,
                      exporter_send_data_to_remote_guacd_proxy, //When data comes back from guacd
                      NULL,
                      NULL,
                      request);
    bufferevent_enable(bev, EV_READ|EV_WRITE);

    res = exporter_guacd_init_sockaddr(&addr, &addr_len, NULL);
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Failed to convert str to a valid sockaddr. Check the input args ...");
        goto gd_connect_fail;
    }

    if (bufferevent_socket_connect(bev, (struct sockaddr *)&addr, addr_len) < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Could not connect to guacd server from exporter", request->name);
        goto gd_connect_fail;
    }
    int sock = bufferevent_getfd(bev);
    int tmp_val = 1;

    res = setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));
    if (res < 0) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Failed to set socket options TCP_NODELAY");
        goto gd_connect_fail;
    }

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    {
        request->guac_info->gd_remote_server_proxy.local_guacd_bev = bev;
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
gd_connect_fail:
    if (bev) zlibevent_bufferevent_free(bev);
    bev = NULL;
    return ZPATH_RESULT_ERR;
}
