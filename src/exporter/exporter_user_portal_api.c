/*
 * exporter_user_portal_api.c. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 *
 * User portal's portal api - Handling for portal API
 *
 */
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_broker_policy.h"
#include "parson/parson.h"

#include "object_store/object_store.h"

#include "exporter/exporter.h"
#include "exporter/exporter_domain.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_session.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_user_portal_cfg_portal.h"
#include "exporter/exporter_user_portal_cfg_links.h"
#include "zpn/zpn_application.h"
#include "exporter/zpn_sra_console.h"
#include "exporter/zpn_sra_application.h"
#include "exporter/zpn_sra_portal.h"
#include "exporter/zpn_sra_portal_sra_console_mapping.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "exporter/exporter_user_portal_cfg_zapp_links.h"
#include "exporter/exporter_user_portal_cfg_aup.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_user_portal_request.h"
#include "zpath_lib/zpath_customer_logo.h"
#include "exporter/exporter_user_portal_cfg_external_dependency.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_user_portal_cfg_link_mapping.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_assert.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_guac_pra_console.js_generated.h"
#include "exporter/exporter_guac_pra_console.css_generated.h"
#include "exporter/exporter_guac_pra_console.woff2_generated.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "zpn/zpn_privileged_capabilities.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_jit_approval_policy.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#include "exporter/exporter_http_proxy_session_store.h"
#include "exporter/exporter_guac_session_control_host.h"
#ifdef UNIT_TEST
#include "exporter/exporter_myfiles_test.h"
#include "exporter/exporter_enduser_approvals_test.h"
#endif
#include "exporter/Inter-Regular.woff2_generated.h"
#include "exporter/Inter-Regular.woff_generated.h"
#include "exporter/fa-solid-900.woff2_generated.h"
#include "exporter/fa-solid-900.ttf_generated.h"
#include "exporter/exporter_guac_desktops.h"

extern unsigned char exporter_guac_index_html[];
extern unsigned char exporter_guac_all_min_js[];
extern unsigned int exporter_guac_index_html_len;
extern unsigned int exporter_guac_all_min_js_len;
unsigned char exporter_guac_pra_console_js[];
unsigned int exporter_guac_pra_console_js_len;
unsigned char exporter_guac_pra_console_css[];
unsigned int exporter_guac_pra_console_css_len;
unsigned char exporter_guac_pra_console_woff2[];
unsigned int exporter_guac_pra_console_woff2_len;
unsigned char Inter_Regular_woff2[];
unsigned int Inter_Regular_woff2_len;
unsigned char Inter_Regular_woff[];
unsigned int Inter_Regular_woff_len;
unsigned char fa_solid_900_woff2[];
unsigned int fa_solid_900_woff2_len;
unsigned char fa_solid_900_ttf[];
unsigned int fa_solid_900_ttf_len;


/* Portal API request handler init flag */
static int s_portal_api_init_done = 0;

/* Exporter portal user auth states */
enum exporter_portal_user_auth_state {

    EXPORTER_PORTAL_USER_AUTH_STATE_UNKNOWN,

    EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_IN,
    EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_OUT,

    EXPORTER_PORTAL_USER_AUTH_STATE_LAST
};



/*
 * Portal API response definitions
 * Note:
 * The UI expects all their JSON strctures and their fields to use "camel Case" notation
 * The UI notation breaks our normal C coding style for structure names, we will use C structure typedefs to insulate
 * itasca code from GUI convention but these real structures will follow GUI convention for easy JSON marshalling
 */

/* User-Response */
struct exporterUserPortalApiUserResponse {          /* _ARGO: object_definition */
    char *nameId;                                   /* _ARGO: string */
    char *id;                                       /* _ARGO: string */
    char *uDid;                                     /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *authState;                                /* _ARGO: string */
    uint64_t myFilesEnabled;                        /* _ARGO: boolean */
    uint64_t myFilesDeleteEnabled;                  /* _ARGO: boolean */
    uint64_t myFilesUninspectedEnabled;             /* _ARGO: boolean */
    uint64_t portalFileTransferEnabled;             /* _ARGO: boolean */
    uint64_t realVncEnabled;                        /* _ARGO: boolean */
    uint64_t displayQualityEnabled;                 /* _ARGO: boolean */
    uint64_t endUserApprovalsEnabled;               /* _ARGO: boolean */
    uint64_t approvalsRequestEnabled;               /* _ARGO: boolean */
    uint64_t approvalsReviewEnabled;                /* _ARGO: boolean */
    uint64_t markdownContentEnabled;                /* _ARGO: boolean */
};

/* Company-Response */
struct exporterUserPortalApiCompanyResponse {       /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *name;                                     /* _ARGO: string */
    char *description;                              /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char **otherDomains;                            /* _ARGO: string */
    int otherDomainsCount;                          /* _ARGO: quiet, integer, count: otherDomains */
    uint64_t hasLogo;                               /* _ARGO: boolean */
    char *logoImageName;                            /* _ARGO: string */
    char *logoImageData;                            /* _ARGO: string */
    uint64_t hasFavIcon;                            /* _ARGO: boolean */
    char *favIconImageData;                         /* _ARGO: string  */
};

/* Portal-Response */
struct exporterUserPortalApiPortalResponse {        /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *name;                                     /* _ARGO: string */
    char *description;                              /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *userNotification;                         /* _ARGO: string */
    uint64_t hasAup;                                /* _ARGO: boolean */
    uint64_t hasUserNotification;                   /* _ARGO: boolean */
    uint64_t hasLogo;                               /* _ARGO: boolean */
    uint64_t isUnifiedPortal;                       /* _ARGO: boolean */
    char *logoImageName;                            /* _ARGO: string */
    char *logoImageData;                            /* _ARGO: string */
    uint64_t enabled;                               /* _ARGO: boolean */
    int timeout;                                    /* _ARGO: integer */
    uint64_t isDefaultPortal;                       /* _ARGO: boolean */
    char *scopeName;                                /* _ARGO: string */
    char *user_portal_host;                         /* _ARGO: string */
    char *scopeId;                                  /* _ARGO: string */
};

/* AUP-Response */
struct exporterUserPortalApiAupResponse {           /* _ARGO: object_definition */
    char *name;                                     /* _ARGO: string */
    char *description;                              /* _ARGO: string */
    char *aupMessage;                               /* _ARGO: string */
    char *email;                                    /* _ARGO: string */
    char *phone;                                    /* _ARGO: string */
    char *displayFrequency;                         /* _ARGO: string */
    char *displayInterval;                          /* _ARGO: string */
    uint64_t enabled;                               /* _ARGO: boolean */
};

/* Portal api link entry */
struct exporterUserPortalApiLink {                  /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *name;                                     /* _ARGO: string */
    char *description;                              /* _ARGO: string */
    char *linkUrl;                                  /* _ARGO: string */
    char *protocol;                                 /* _ARGO: string */
    char *linkPath;                                 /* _ARGO: string */
    uint64_t hasAccess;                             /* _ARGO: boolean */
    uint64_t hasZappAccess;                         /* _ARGO: boolean */
    uint64_t hasBrowserAccess;                      /* _ARGO: boolean */
    uint64_t hasIcon;                               /* _ARGO: boolean */
    int64_t application_gid;                        /* _ARGO: integer */
};

/* Portal api ZApp link entry */
struct exporterUserPortalApiZappLink {              /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *osType;                                   /* _ARGO: string */
    char *linkUrl;                                  /* _ARGO: string */
};

/* Portal api link icon entry */
struct exporterUserPortalApiLinkIcon {              /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *name;                                     /* _ARGO: string */
    char *description;                              /* _ARGO: string */
    char *linkUrl;                                  /* _ARGO: string */
    char *protocol;                                 /* _ARGO: string */
    char *linkPath;                                 /* _ARGO: string */
    uint64_t hasAccess;                             /* _ARGO: boolean */
    uint64_t hasZappAccess;                         /* _ARGO: boolean */
    uint64_t hasBrowserAccess;                      /* _ARGO: boolean */
    uint64_t hasIcon;                               /* _ARGO: boolean */
    char *imageName;                                /* _ARGO: string */
    char *imageData;                                /* _ARGO: string */
    int64_t application_gid;                        /* _ARGO: integer */
};

/* SRA portal api console link icon entry */
struct exporterSraPortalApiLinkIcon {               /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *name;                                     /* _ARGO: string */
    char *protocol;                                 /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *connectionSecurity;                       /* _ARGO: string */
    char *scopeId;                                  /* _ARGO: string */
    char *imageName;                                /* _ARGO: string */
    uint64_t approval_start_time;                   /* _ARGO: integer */
    uint64_t approval_end_time;                     /* _ARGO: integer */
    char *approval_working_hours;                   /* _ARGO: string */
    enum zpe_access_approval_status approval_status;/* _ARGO: integer */
    uint64_t hasAccess;                             /* _ARGO: boolean */
    uint64_t hasZappAccess;                         // TODO: Do we need ARGO? I don't know why we would.
    uint64_t hasBrowserAccess;
};

/* SRA portal api shared console link icon entry */
struct exporterSraPortalApiSharedLinkIcon {         /* _ARGO: object_definition */
    char *console_id;                               /* _ARGO: string */
    char *console_name;                             /* _ARGO: string */
    char *protocol;                                 /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *sess_id;                                  /* _ARGO: string */
    char *user_name;                                /* _ARGO: string */
    uint64_t session_start_time;                    /* _ARGO: integer */
    int64_t scope_gid;                              /* _ARGO: integer */
    char *scope_name;                               /* _ARGO: string */
};

/* SRA portal api scope link icon entry */
struct exporterSraPortalScopeApiLinkIcon {
    char *id;
    char *name;
};

/* SRA portal api console info response */
struct exporterSraPortalConsoleInfoResponse {       /* _ARGO: object_definition */
    char *sra_console_name;                         /* _ARGO: string */
    char *sra_console_type;                         /* _ARGO: string */
    int   fileTransferEnabled;                      /* _ARGO: integer */
    int   fasterFileTransferEnabled;                /* _ARGO: integer */
    uint64_t interactive_auth_disabled;             /* _ARGO: boolean */
    int   clipboardEnabled;                         /* _ARGO: integer */
    char *infer_key;                                /* _ARGO: string */
    char *customer_gid;                             /* _ARGO: string */
    char *scope_id;                                 /* _ARGO: string */
    int  emailNotificationEnabled;                  /* _ARGO: integer */
    char *user_id;                                  /* _ARGO: string */
    int  privileged_file_system_enabled;            /* _ARGO: boolean */
    int  display_quality_enabled;                   /* _ARGO: boolean */
};

/* Links-Response-Int */
struct exporterUserPortalApiLinksResponseInt {      /* _ARGO: object_definition */
    struct exporterUserPortalApiLink *links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];       /* _ARGO: object */
    int linksCount;                                 /* _ARGO: quiet, integer, count:links */
};

/* Links-Icons-Response-Int */
struct exporterUserPortalApiLinksIconsResponseInt { /* _ARGO: object_definition */
    struct exporterUserPortalApiLinkIcon *links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];   /* _ARGO: object */
    int linksCount;                                 /* _ARGO: quiet, integer, count:links */
};

#ifndef UNIT_TEST
/* PRA Console Links-Response-Int */
struct exporterConsoleLinksResponseInt           {  /* _ARGO: object_definition */
    struct exporterSraPortalApiLinkIcon *links[EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL];    /* _ARGO: object */
    int linksCount;                                 /* _ARGO: quiet, integer, count:links */
    struct zhash_table *requestable_app_ids;

};
#endif

/* PRA Console Shared Links-Response-Int */
struct exporterSharedConsoleLinksResponseInt           {  /* _ARGO: object_definition */
    struct exporterSraPortalApiSharedLinkIcon *links[EXPORTER_USER_PORTAL_CFG_MAX_SHARED_LINKS_PER_PORTAL];    /* _ARGO: object */
    int linksCount;                                 /* _ARGO: quiet, integer, count:links */
};

/* PRA Scope Links-Response-Int */
struct exporterSraPortalScopeLinksResponseInt             {
    struct exporterSraPortalScopeApiLinkIcon *links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    int linksCount;
};

/* Zapp Links-Response-Int */
struct exporterUserPortalApiZappLinksResponseInt {      /* _ARGO: object_definition */
    struct exporterUserPortalApiZappLink *links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];   /* _ARGO: object */
    int linksCount;                                 /* _ARGO: quiet, integer, count:links */
};

/* Links-Response */
struct exporterUserPortalApiLinksResponse {         /* _ARGO: object_definition */
    char *links;                                    /* _ARGO: string */
    int linksCount;                                 /* _ARGO: integer */
};

/* Image-Response */
struct exporterUserPortalApiImageResponse {         /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *imageName;                                /* _ARGO: string */
    char *imageData;                                /* _ARGO: string */
    char *faviconImageData;                         /* _ARGO: string */
};

/* User-Logout-Response */
struct exporterUserPortalApiUserLogoutResponse {    /* _ARGO: object_definition */
    char *nameId;                                   /* _ARGO: string */
    char *id;                                       /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *authState;                                /* _ARGO: string */
};

/* Error-Response */
struct exporterUserPortalApiErrorResponse {         /* _ARGO: object_definition */
    char *id;                                       /* _ARGO: string */
    char *reason;                                   /* _ARGO: string */
    char *domain;                                   /* _ARGO: string */
    char *nameId;                                   /* _ARGO: string */
};

/* UserPortalApiStats */
struct exporter_user_portal_api_stats {             /* _ARGO: object_definition */
    uint64_t get_user_success;                      /* _ARGO: integer */
    uint64_t get_user_failed;                       /* _ARGO: integer */

    uint64_t get_company_success;                   /* _ARGO: integer */
    uint64_t get_company_failed;                    /* _ARGO: integer */

    uint64_t get_company_logo_success;              /* _ARGO: integer */
    uint64_t get_company_logo_failed;               /* _ARGO: integer */

    uint64_t get_portal_success;                    /* _ARGO: integer */
    uint64_t get_portal_failed;                     /* _ARGO: integer */

    uint64_t get_portal_logo_success;               /* _ARGO: integer */
    uint64_t get_portal_logo_failed;                /* _ARGO: integer */

    uint64_t get_links_success;                     /* _ARGO: integer */
    uint64_t get_links_failed;                      /* _ARGO: integer */

    uint64_t get_links_entry_alloc;                 /* _ARGO: integer */
    uint64_t get_links_entry_free;                  /* _ARGO: integer */

    uint64_t get_links_icons_success;               /* _ARGO: integer */
    uint64_t get_links_icons_failed;                /* _ARGO: integer */

    uint64_t get_links_icons_entry_alloc;           /* _ARGO: integer */
    uint64_t get_links_icons_entry_free;            /* _ARGO: integer */

    uint64_t get_sra_links_icons_entry_alloc;       /* _ARGO: integer */
    uint64_t get_sra_links_icons_entry_free;        /* _ARGO: integer */

    uint64_t get_sra_shared_links_icons_entry_alloc;       /* _ARGO: integer */
    uint64_t get_sra_shared_links_icons_entry_free;        /* _ARGO: integer */

    uint64_t get_link_icon_success;                 /* _ARGO: integer */
    uint64_t get_link_icon_failed;                  /* _ARGO: integer */

    uint64_t get_zapp_links_success;                /* _ARGO: integer */
    uint64_t get_zapp_links_failed;                 /* _ARGO: integer */

    uint64_t get_aup_success;                       /* _ARGO: integer */
    uint64_t get_aup_failed;                        /* _ARGO: integer */

    uint64_t logout_user_success;                   /* _ARGO: integer */
    uint64_t logout_user_failed;                    /* _ARGO: integer */

    uint64_t init_success;                          /* _ARGO: integer */
    uint64_t init_failed;                           /* _ARGO: integer */

    uint64_t total_async_requested;                 /* _ARGO: integer */
    uint64_t total_async_completed;                 /* _ARGO: integer */

    uint64_t get_sra_scope_links_success;           /* _ARGO: integer */
    uint64_t get_sra_scope_links_failed;            /* _ARGO: integer */

    uint64_t get_sra_scope_links_icons_entry_alloc; /* _ARGO: integer */
    uint64_t get_sra_scope_links_icons_entry_free;  /* _ARGO: integer */

    uint64_t get_sra_approval_scopes_success;       /* _ARGO: integer */
    uint64_t get_sra_approval_scopes_failed;        /* _ARGO: integer */

};

/*-------------------------------------------------*/
/* Itasca typedefs for message response structures */
/*-------------------------------------------------*/

typedef struct exporterUserPortalApiUserResponse exporter_user_portal_api_user_response_t;
typedef struct exporterUserPortalApiCompanyResponse exporter_user_portal_api_company_response_t;
typedef struct exporterUserPortalApiPortalResponse exporter_user_portal_api_portal_response_t;
typedef struct exporterUserPortalApiAupResponse exporter_user_portal_api_aup_response_t;
typedef struct exporterUserPortalApiLinksResponse exporter_user_portal_api_links_response_t;
typedef struct exporterUserPortalApiLinksResponseInt exporter_user_portal_api_links_response_int_t;
typedef struct exporterUserPortalApiLinksIconsResponseInt exporter_user_portal_api_links_icons_response_int_t;
typedef struct exporterConsoleLinksResponseInt exporter_console_links_response_int_t;
typedef struct exporterSharedConsoleLinksResponseInt exporter_shared_console_links_response_int_t;
typedef struct exporterUserPortalApiZappLinksResponseInt exporter_user_portal_api_zapp_links_response_int_t;
typedef struct exporterUserPortalApiErrorResponse exporter_user_portal_api_error_response_t;
typedef struct exporterUserPortalApiImageResponse exporter_user_portal_api_image_response_t;
typedef struct exporterUserPortalApiUserLogoutResponse exporter_user_portal_api_user_logout_response_t;
typedef struct exporterUserPortalApiLink exporter_user_portal_api_link_t;
typedef struct exporterUserPortalApiLinkIcon exporter_user_portal_api_link_icon_t;
typedef struct exporterSraPortalApiLinkIcon exporter_sra_portal_api_link_icon_t;
typedef struct exporterSraPortalApiSharedLinkIcon exporter_sra_portal_api_shared_link_icon_t;
typedef struct exporterSraPortalConsoleInfoResponse exporter_sra_portal_console_info_response_t;
typedef struct exporterSraPortalScopeLinksResponseInt exporter_scope_links_response_int_t;
typedef struct exporterSraPortalScopeApiLinkIcon exporter_sra_portal_scope_api_link_icon_t;

/*-------------------------------------------------*/

#include "exporter/exporter_conn.h"
#include "exporter/exporter_user_portal_api_compiled_c.h"
#include "exporter/zpn_sra_portal.h"

/* Structure descriptions */
struct argo_structure_description *exporter_user_portal_api_user_resp_description           = NULL;
struct argo_structure_description *exporter_user_portal_api_company_resp_description        = NULL;
struct argo_structure_description *exporter_user_portal_api_portal_resp_description         = NULL;
struct argo_structure_description *exporter_user_portal_api_aup_resp_description            = NULL;
struct argo_structure_description *exporter_user_portal_api_links_resp_description          = NULL;
struct argo_structure_description *exporter_user_portal_api_links_resp_int_description      = NULL;
struct argo_structure_description *exporter_user_portal_api_links_icons_resp_int_description= NULL;
struct argo_structure_description *exporter_sra_portal_api_console_links_icons_resp_int_description = NULL;
struct argo_structure_description *exporter_sra_portal_api_shared_console_links_icons_resp_int_description = NULL;
struct argo_structure_description *exporter_user_portal_api_zapp_links_resp_int_description = NULL;
struct argo_structure_description *exporter_user_portal_api_link_description                = NULL;
struct argo_structure_description *exporter_user_portal_api_link_icon_description           = NULL;
struct argo_structure_description *exporter_sra_portal_api_console_link_icon_description    = NULL;
struct argo_structure_description *exporter_sra_portal_api_shared_console_link_icon_description = NULL;
struct argo_structure_description *exporter_user_portal_api_zapp_link_description           = NULL;
struct argo_structure_description *exporter_user_portal_api_error_resp_description          = NULL;
struct argo_structure_description *exporter_user_portal_api_image_resp_description          = NULL;
struct argo_structure_description *exporter_user_portal_api_user_logout_resp_description    = NULL;
struct argo_structure_description *exporter_user_portal_api_stats_description               = NULL;
struct argo_structure_description *exporter_sra_portal_api_console_info_description         = NULL;

/* API stats counters instance */
static struct exporter_user_portal_api_stats s_portal_api_stats;

extern struct argo_structure_description *exporter_user_sessions_description;
extern struct argo_structure_description *exporter_shared_session_data_description;

/* Simple macros to increment success/failure counters */
#define EXPORTER_PORTAL_API_STATS_INC(field, increment) __sync_add_and_fetch_8(&s_portal_api_stats.field, increment)

/* Increment correct sucess/failed counters for given counter prefix */
#define EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(field_prefix, increment, retcode)                                     \
                                                (retcode == ZPATH_RESULT_NO_ERROR) ?                                    \
                                                 EXPORTER_PORTAL_API_STATS_INC(field_prefix ## _success, increment) :   \
                                                 EXPORTER_PORTAL_API_STATS_INC(field_prefix ## _failed, increment)

/* Increment total async requested */
#define EXPORTER_PORTAL_API_STATS_INC_ASYNC_REQUESTED   EXPORTER_PORTAL_API_STATS_INC(total_async_requested, 1)

/* Increment total async completed */
#define EXPORTER_PORTAL_API_STATS_INC_ASYNC_COMPLETED   EXPORTER_PORTAL_API_STATS_INC(total_async_completed, 1)

/* ------------------------- */
/* Local function prototypes */
/* ------------------------- */
int
exporter_request_check_shared_session_capability_policy(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data);
int
exporter_request_shared_session_scope_validation(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data);
char *
exporter_request_get_shared_session_scope_name(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data);

void exporter_portal_api_free_response(const struct exporter_request *request, void *resp);

/* ------------------------- */


/* ------------------------- */
/* Functions                 */
/* ------------------------- */

/*
 * Map user auth state to descriptive name
 */
static char*
get_portal_user_auth_state_name(enum exporter_portal_user_auth_state state)
{
    static char *auth_state_to_name[] = {
        "UNKNOWN",
        "LOGGED_IN",
        "LOGGED_OUT"
    };

    if ((state > EXPORTER_PORTAL_USER_AUTH_STATE_UNKNOWN) && (state < EXPORTER_PORTAL_USER_AUTH_STATE_LAST)) {
        return auth_state_to_name[state];
    }

    return auth_state_to_name[EXPORTER_PORTAL_USER_AUTH_STATE_UNKNOWN];
}

/*
 * Map portal api type to descriptive name
 */
static const char*
get_portal_api_name(enum zpa_user_portal_api_type api_type)
{
    static char *api_type_to_name[] = {
        "[Portal API Invalid]",
        "[Portal API Get Index]",
        "[Portal API Get User]",
        "[Portal API Get Company]",
        "[Portal API Get Company Logo]",
        "[Portal API Get Portal]",
        "[Portal API Get Portal Logo]",
        "[Portal API Get AUP]",
        "[Portal API Get Links]",
        "[Portal API Get Links Icons]",
        "[SRA Portal API Get Links Icons]",
        "[Portal API Get ZApp Links]",
        "[Portal API Get Link Icon]",
        "[Portal API Logout]",
        "[SRA Portal API Get Guacamole Web Socket Tunnel]",
        "[SRA Portal API Get Guacamole Javascript]",
        "[SRA Portal API Get Zconsole Index]",
        "[SRA Portal API Get Console Info]",
        "[SRA Portal API Get Console JS]",
        "[SRA Portal API Get Console CSS]",
        "[SRA Portal API Get Console WOFF2]",
        "[SRA Portal API Get Console Inter-Regular WOFF2]",
        "[SRA Portal API Get Console Inter-Regular WOFF]",
        "[SRA Portal API Get Console FA SOLID WOFF2]",
        "[SRA Portal API Get Console FA SOLID TTF]",
        "[SRA Portal API Get Shared Links Icons]",
        "[SRA Portal API Get Scope Links Icon]",
        "[SRA Portal API Get ZScope]",
        "[SRA Portal API HTTP Proxy]",
        "[SRA Portal API My-Files Proxy]",
        "[SRA Portal API My-Files View Un-Inspected Files Proxy]",
        "[SRA Portal API My-Files Download Admin Priv Files Proxy]",
        "[SRA Portal API My-Files Delete File Proxy]",
        "[SRA Portal API My-Files Multipart-upload Initiate Proxy]",
        "[SRA Portal API My-Files Multipart-upload Proxy]",
        "[SRA Portal API My-Files Multipart-upload Complete Proxy]",
        "[SRA Portal API My-Files Multipart-upload Abort Proxy]",
        "[SRA Portal API Privileged-Approvals Get Requestable Consoles Proxy]",
        "[SRA Portal API Get Approval Scopes]",
        "[SRA Portal API Privileged Approvals]",
        "[SRA Portal API Privileged-Approvals Request Proxy]", // Proxy the create/delete/review approval request
        "[SRA Portal API Get Privileged-Approval-Requests Proxy]", // Proxy the Get approval requests
        "[SRA Portal API Privileged Desktops]", // disposable jumpbox management apis
        "[SRA Portal API Privileged Desktops AMI List]",
        "[SRA Portal API Privileged Desktops Create]",
        "[SRA Portal API Privileged Desktops Active List with status]",
        "[SRA Portal API Privileged Desktops DJB Info]",
    };

    if ((api_type > ZPA_USER_PORTAL_API_TYPE_INVALID) && (api_type < ZPA_USER_PORTAL_API_TYPE_LAST)) {
        return api_type_to_name[api_type];
    }

    return api_type_to_name[ZPA_USER_PORTAL_API_TYPE_INVALID];
}

/*
 * API Version len
 */
int exporter_get_user_portal_api_version_len(enum zpa_user_portal_api_version api_version)
{
    int v_int = (int)api_version;
    int v_len = 0;

    /* Extract digits */
    while (v_int > 0) {
        v_len++;
        v_int /= 10;
    }

    return v_len;
}

/*
 * Zconsole/Zscope identifer len
 */
int exporter_get_user_portal_id_len(int64_t zconsole_id)
{
    int z_len = 0;

    /* Extract digits */
    while (zconsole_id > 0) {
        z_len++;
        zconsole_id /= 10;
    }

    return z_len;
}

 /*
  * Get portal API type - If there is no matching API, then we assume it is index page request and let the webserver handle it
  */
enum zpa_user_portal_api_type exporter_get_user_portal_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /* Format: https://domain_uri>/v<version>/<api_type_path>/... */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        /*
         * If the request is invalid, we will still treat it as index.html request.
         */
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Invalid Portal API version: %d", request->name, api_version);
    }

    /*
     * PRA requests will have /zscope in the URL for Scope Folders in PRA Portal.
     * Skip over it if present
     */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    /*
     * Note: Must specify most specific match before less specific match
     */
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_USER, strlen(EXPORTER_PORTAL_API_URI_USER))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_USER;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_COMPANY_LOGO, strlen(EXPORTER_PORTAL_API_URI_COMPANY_LOGO))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_COMPANY_LOGO;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_PORTAL_LOGO, strlen(EXPORTER_PORTAL_API_URI_PORTAL_LOGO))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_PORTAL_LOGO;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_ZAPP_LINKS, strlen(EXPORTER_PORTAL_API_URI_ZAPP_LINKS))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_ZAPP_LINKS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_LINK_LOGO, strlen(EXPORTER_PORTAL_API_URI_LINK_LOGO))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_LINK_ICON;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_COMPANY, strlen(EXPORTER_PORTAL_API_URI_COMPANY))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_COMPANY;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_PORTAL, strlen(EXPORTER_PORTAL_API_URI_PORTAL))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_PORTAL;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_AUP, strlen(EXPORTER_PORTAL_API_URI_AUP))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_AUP;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_LINKS_ICONS, strlen(EXPORTER_PORTAL_API_URI_LINKS_ICONS))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_LINKS_ICONS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_CONSOLE_LINKS_ICONS, strlen(EXPORTER_SRA_PORTAL_API_URI_CONSOLE_LINKS_ICONS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_CONSOLE_LINKS_ICONS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_LINKS, strlen(EXPORTER_PORTAL_API_URI_LINKS))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_LINKS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_LOGOUT, strlen(EXPORTER_PORTAL_API_URI_LOGOUT))) {
        api_type = ZPA_USER_PORTAL_API_TYPE_LOGOUT;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE_LEGACY, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE_LEGACY))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_JS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE_LEGACY, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE_LEGACY))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_CSS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE_LEGACY, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE_LEGACY))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_WOFF2;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_GUAC_WEBSOCKET_TUNNEL, strlen(EXPORTER_SRA_PORTAL_API_URI_GUAC_WEBSOCKET_TUNNEL))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_WEBSOCKET_TUNNEL;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_GUAC_JS_LEGACY, strlen(EXPORTER_SRA_PORTAL_API_URI_GUAC_JS_LEGACY))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_JS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_ZCONSOLE, strlen(EXPORTER_SRA_PORTAL_API_URI_ZCONSOLE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_ZCONSOLE;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_CONSOLE_INFO, strlen(EXPORTER_SRA_PORTAL_API_URI_CONSOLE_INFO))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS, strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_HTTP_PROXY;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SHARED_CONSOLE_LINKS_ICONS, strlen(EXPORTER_SRA_PORTAL_API_URI_SHARED_CONSOLE_LINKS_ICONS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_SHARED_CONSOLE_LINKS_ICONS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SCOPE_LINKS_ICONS, strlen(EXPORTER_SRA_PORTAL_API_URI_SCOPE_LINKS_ICONS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_APPROVAL_SCOPES, strlen(EXPORTER_SRA_PORTAL_API_URI_APPROVAL_SCOPES))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS, strlen(EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES, strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS, strlen(EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES;
    } else if (0 == strncasecmp(&uri_path[offset],  EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX, strlen(EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS;
    } else {
        api_type = ZPA_USER_PORTAL_API_TYPE_INDEX;
    }

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s", request->name, uri_path, uri_len, get_portal_api_name(api_type));

    return api_type;
}

 /*
  * Get portal API type - Check if the API has /zscope
  */
enum zpa_user_portal_api_type exporter_get_user_portal_zscope_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /*
     * PRA requests will have /zscope in the URL for Scope Folders in PRA Portal.
     * Format can be either of the 2:
     * https://domain_uri>/v<version>/zscope/<zscope_id>/api_type_path>/...
     * https://domain_uri>/v<version>/<api_type_path>/...
     */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        /*
         * If the request is invalid, we will still treat it as index.html request.
         */
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Invalid Portal API version: %d", request->name, api_version);
    }

    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_ZSCOPE, strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_ZSCOPE;
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s", request->name, uri_path, uri_len, get_portal_api_name(api_type));
    }

    return api_type;
}

/*
 * Get portal api version from request
 */
enum zpa_user_portal_api_version exporter_get_user_portal_api_version(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    char version_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_version api_version = ZPA_USER_PORTAL_API_VERSION_INVALID;

    /* Format: https://domain_uri>/v<version>/<api_type_path>/... */
    if ((uri_len >= 4) && (0 == strncasecmp(uri_path, "/v", 2)) && (uri_path[3] == '/')) {
        /* Single digit for version */
        if (isdigit(uri_path[2])) {
            snprintf(version_str, sizeof(version_str), "%c", uri_path[2]);
        }
    } else if ((uri_len >= 5) && (0 == strncasecmp(uri_path, "/v", 2)) && (uri_path[4] == '/')) {
         /* Two digits for version */
        if (isdigit(uri_path[2]) && isdigit(uri_path[3])) {
            snprintf(version_str, sizeof(version_str), "%c%c", uri_path[2], uri_path[3]);
        }
    }

    /* Note: Map version numeric value to version enum, handle all valid versions here ! */
    switch (atoi(version_str)) {
    case ZPA_USER_PORTAL_API_VERSION_1:
        api_version = ZPA_USER_PORTAL_API_CURRENT_VERSION;
        break;

    default:
        api_version = ZPA_USER_PORTAL_API_VERSION_INVALID;
        break;

    }

   EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Portal %s - uri path: %s, len: %d, api_version: %d", request->name, domain, uri_path, uri_len, api_version);

    return api_version;
}

/*
 * Get uri link id
 */
static int exporter_user_portal_get_link_id(struct exporter_request *request, int64_t *id)
{

    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    char *link_start = NULL;
    char *icon_start = NULL;
    char *id_ptr = NULL;
    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = "";
    int ret = ZPATH_RESULT_NOT_FOUND;
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *cp;

    /* Format: https://<base_uri>/link/<link_id>/icon */

    link_start = strstr(uri_path, "/link/");
    icon_start = strstr(uri_path, "/icon");

    if ((link_start != NULL) && (icon_start != NULL)) {
        id_ptr = link_start + strlen("/link/");
        for (cp = id_str; (id_ptr < icon_start) && (*id_ptr != '\0'); ) {
            *cp++ = *id_ptr++;
        }
    }

    if (strlen(id_str) > 0) {
        *id = strtol(id_str, NULL, 10);
        ret = ZPATH_RESULT_NO_ERROR;
    } else {
        *id = 0;
        EXPORTER_LOG(AL_ERROR, "%s: %s - Unexpected link uri format: %s(%d)", request->name, api_name, uri_path, uri_len);
    }

    return ret;
}

/*
 * Mark API request state for deferred async processing
 */
int exporter_portal_defer_api_request_async(struct exporter_request *request)
{
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int total_async = exporter_user_portal_request_inc_total_async_count(request);

    __sync_fetch_and_add_4(&(request->async_count), 1);
    request->async_state = async_state_reprocess_portal_api;

    EXPORTER_PORTAL_API_STATS_INC_ASYNC_REQUESTED;

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Process API request async for domain: %s, api_type: %s, async_state: %s, async_count: %d, total_async_count: %d",
                 request->name, domain, api_name, exporter_request_async_state_get_str(request->async_state), request->async_count, total_async);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Validate and classify portal api exporter_request
 * - Initilize portal_info if needed
 * - Set the underlying API operation type
 * - Set the underlying API operation version
 * - Check the domain/sni match
 */
static int exporter_portal_validate_classify_request(struct exporter_request *request)
{
    if (request == NULL) {
        EXPORTER_LOG(AL_ERROR, "Null portal request");
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (request->conn == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: NULL portal request conn",
                     request->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (request->conn->sni == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: NULL portal request conn sni",
                     request->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    if (domain == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Null portal domain",
                     request->name);
        return ZPATH_RESULT_BAD_STATE;
    }

    if (!request->conn->exporter_domain->is_user_portal && !request->conn->exporter_domain->is_ot) {
        EXPORTER_LOG(AL_ERROR, "%s - Domain: %s is not valid portal", request->name, domain);
        return ZPATH_RESULT_BAD_STATE;
    } else {
        EXPORTER_LOG(AL_INFO, "%s Domain: %s is valid portal", request->name, domain);
    }

    if (strcmp(request->conn->sni, domain) != 0) {
        /* No match */
        EXPORTER_LOG(AL_ERROR, "%s: Session mismatch, req SNI = %s, domain = %s",
                     request->name,
                     request->conn->sni,
                     domain);
        return ZPATH_RESULT_BAD_STATE;
    }

    if (request->user_portal_request_user_state == NULL) {
        EXPORTER_ASSERT_SOFT((request->user_portal_request_user_state == NULL),
                             "%s: NULL user_portal_request_user_state on user portal request",
                             request->name);
        // Since we still need to alert the caller to not continue so we don't segfault later
        return ZPATH_RESULT_BAD_STATE;
    }

    /* All portal requests must have non-null portal state
     * for re-processed requests, they already have portal state set
     */

    if (!exporter_user_portal_request_state_is_valid(request->portal_info)) {
        /* Extract api type from uri path */
        enum zpa_user_portal_api_type api_type = exporter_get_user_portal_api_type(request);
        if (api_type == ZPA_USER_PORTAL_API_TYPE_INVALID) {
            EXPORTER_LOG(AL_ERROR, "%s:  Portal api is invalid", request->name);
            return ZPATH_RESULT_BAD_DATA;
        }

        /* Extract api version from uri path */
        enum zpa_user_portal_api_version api_version = exporter_get_user_portal_api_version(request);
        if (api_version == ZPA_USER_PORTAL_API_VERSION_INVALID) {
            EXPORTER_LOG(AL_ERROR, "%s:  Portal api version is invalid", request->name);
            return ZPATH_RESULT_BAD_DATA;
        }

        /* Set type and version in state */
        exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);
        exporter_user_portal_request_state_set_api_version(request->portal_info, api_version);

        /* Mark state as valid */
        exporter_user_portal_request_state_set_valid(request);
    }


    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Portal state for domain: %s is valid, api_type: %s", request->name, domain, api_name);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Get portal user auth state based on exporter_request state
 */
static int exporter_portal_get_user_auth_state(struct exporter_request *request,
                                               enum exporter_portal_user_auth_state *auth_state)
{
    char *name_id = exporter_user_portal_request_state_get_name(request->portal_info);

    if (request->is_authenticated && EXPORTER_PORTAL_SAFE_STRLEN(name_id) > 0) {
        *auth_state = EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_IN;
    } else {
        *auth_state = EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_OUT;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Priviledge portal policy state
 * Evaluate portal policy; results set in request.
 */
static int exporter_portal_get_policy_state(struct exporter_request *request)
{
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int res = ZPATH_RESULT_NO_ERROR;

    res = exporter_privileged_portal_policy_evaluate(request,
                                                    &request->portal_policy_rule_id,
                                                    &request->portal_policy_capabilities_bitmap);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s async portal policy eval (%d->%d)",
                                       request->name, api_name, domain, request->async_count, request->async_count + 1);
        exporter_portal_defer_api_request_async(request);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: portal - %s, domain: %s portal policy eval res=%s",
                                        request->name, api_name, domain, zpath_result_string(res));
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_validate_application_scope(int64_t scope_gid, struct zpn_application *app)
{
    if (!app) {
        return ZPATH_RESULT_ERR;
    }
    /* App segment is in this scope or default scope */
    if (app->scope_gid == scope_gid || app->scope_gid == app->customer_gid) {
        return ZPATH_RESULT_NO_ERROR;
    }
    /* App segment is shared to this scope */
    for (int i = 0; i < app->shared_scope_ids_count; i++) {
        if (scope_gid == app->shared_scope_ids[i]) {
            return ZPATH_RESULT_NO_ERROR;
        }
    }
    return ZPATH_RESULT_ERR;
}

/*
 * Add HTTP response data for content-type application/json
 */
static int exporter_request_respond_with_json(struct exporter_request *request,
                                              enum http_status status,
                                              enum exporter_error_codes error,
                                              char *json_body,
                                              int keep_alive)
{
    char body_buf[EXPORTER_USER_PORTAL_API_LARGE_RESPONSE_BUF_SIZE] = "";
    const char *status_name = http_status_names[(int)status] ? http_status_names[(int)status] : "unknown";
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int total_async = exporter_user_portal_request_get_total_async_count(request);
    const char *conn_control = keep_alive ? "Keep-Alive" : "Close";

    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    snprintf(body_buf, sizeof(body_buf), "\r\n%s\r\n", json_body);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Returning status %s", request->name, status_name);

    /* Sample HTTP header options recommended for security audit */
    /* ============================================================
        access-control-allow-credentials: true
        access-control-allow-headers: Origin, X-Requested-With, Content-Type, Accept, Authorization
        access-control-allow-methods: POST, GET, PUT, DELETE,OPTIONS
        access-control-allow-origin: https://admin-release.dev.zpath.net
        cache-control: no-cache, no-store, max-age=0, must-revalidate
        content-type: application/json;charset=utf-8
        date: Thu, 30 May 2019 21:37:23 GMT
        expires: 0
        pragma: no-cache
        status: 200
        x-content-type-options: nosniff
        x-frame-options: DENY
        x-xss-protection: 1; mode=block
    */

    request->log.response_status = (int)status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: application/json;charset=utf-8\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                        "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                        "Access-Control-Allow-Origin: %s%s\r\n"
                        "Connection: %s\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "Pragma: no-cache\r\n"
                        "X-Content-Type-Options: nosniff\r\n"
                        "X-Frame-Options: DENY\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n%s",
                        std_hdrs,
                        (int)strnlen(body_buf,(EXPORTER_USER_PORTAL_API_LARGE_RESPONSE_BUF_SIZE-1)),
                        request->header_origin ? "" : "https://", /* unified-portal */
                        request->header_origin ? request->header_origin : domain, /* unified-portal */
                        conn_control,
                        error,
                        domain,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH,
                        body_buf);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: application/json;charset=utf-8\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                        "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                        "Access-Control-Allow-Origin: %s%s\r\n"
                        "Connection: %s\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "Pragma: no-cache\r\n"
                        "X-Content-Type-Options: nosniff\r\n"
                        "X-Frame-Options: DENY\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n%s",
                        std_hdrs,
                        (int)strlen(body_buf),
                        request->header_origin ? "" : "https://", /* unified-portal */
                        request->header_origin ? request->header_origin : domain, /* unified-portal */
                        conn_control,
                        error,
                        domain,
                        ZPATH_LOCAL_CLOUD_NAME,
                        body_buf);
    }

    /* Mark response as complete/drain to trigger connection thread to send out response */
    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Response JSON(%d) -> \n%s\n", request->name,
                                          (int)strlen(body_buf), body_buf);

    EXPORTER_LOG(AL_NOTICE, "%s: portal: %s, api: %s, set response of size: %d, total async count: %d", request->name, domain, api_name, (int)strlen(body_buf), total_async);
    exporter_conn_wake_from_other_thread(request->conn);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Add HTTP response data for content-type application/json
 */
static int exporter_request_respond_with_json_dynamic_buffer(struct exporter_request *request,
                                              enum http_status status,
                                              enum exporter_error_codes error,
                                              char *json_body,
                                              int keep_alive)
{
    const char *status_name = http_status_names[(int)status] ? http_status_names[(int)status] : "unknown";
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int total_async = exporter_user_portal_request_get_total_async_count(request);
    const char *conn_control = keep_alive ? "Keep-Alive" : "Close";

    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Returning status %s", request->name, status_name);

    /* Sample HTTP header options recommended for security audit */
    /* ============================================================
        access-control-allow-credentials: true
        access-control-allow-headers: Origin, X-Requested-With, Content-Type, Accept, Authorization
        access-control-allow-methods: POST, GET, PUT, DELETE,OPTIONS
        access-control-allow-origin: https://admin-release.dev.zpath.net
        cache-control: no-cache, no-store, max-age=0, must-revalidate
        content-type: application/json;charset=utf-8
        date: Thu, 30 May 2019 21:37:23 GMT
        expires: 0
        pragma: no-cache
        status: 200
        x-content-type-options: nosniff
        x-frame-options: DENY
        x-xss-protection: 1; mode=block
    */

    request->log.response_status = (int)status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: application/json;charset=utf-8\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                        "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                        "Access-Control-Allow-Origin: %s%s\r\n"
                        "Connection: %s\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "Pragma: no-cache\r\n"
                        "X-Content-Type-Options: nosniff\r\n"
                        "X-Frame-Options: DENY\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n%s\r\n",
                        std_hdrs,
                        (int)strlen(json_body),
                        request->header_origin ? "" : "https://", /* unified-portal */
                        request->header_origin ? request->header_origin : domain, /* unified-portal */
                        conn_control,
                        error,
                        domain,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH,
                        json_body);
    } else {
      evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: application/json;charset=utf-8\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                        "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                        "Access-Control-Allow-Origin: %s%s\r\n"
                        "Connection: %s\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "Pragma: no-cache\r\n"
                        "X-Content-Type-Options: nosniff\r\n"
                        "X-Frame-Options: DENY\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n%s\r\n",
                        std_hdrs,
                        (int)strlen(json_body)+2,
                        request->header_origin ? "" : "https://", /* unified-portal */
                        request->header_origin ? request->header_origin : domain, /* unified-portal */
                        conn_control,
                        error,
                        domain,
                        ZPATH_LOCAL_CLOUD_NAME,
                        json_body);
    }

    /* Mark response as complete/drain to trigger connection thread to send out response */
    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Response JSON(%d) -> \n%s\n", request->name,
                                          (int)strlen(json_body), json_body);

    EXPORTER_LOG(AL_NOTICE, "%s: portal: %s, api: %s, set response of size: %d, total async count: %d", request->name, domain, api_name, (int)strlen(json_body), total_async);
    exporter_conn_wake_from_other_thread(request->conn);

    return ZPATH_RESULT_NO_ERROR;
}

int
exporter_request_respond_json_dynamic(struct exporter_request *request,
                                      enum http_status STATUS_CODE,
                                      enum exporter_error_codes EXPORTER_STATUS_CODE) {
    int ret = ZPATH_RESULT_NO_ERROR;
    if (!request) {
        EXPORTER_LOG(AL_ERROR, "[SESS_SHARE]: USER_EMAIL: Invalid request received");
        return ZPATH_RESULT_ERR;
    }

    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    char *response_buf = EXPORTER_CALLOC(EXPORTER_USER_PORTAL_ID_STR_LEN);
    if (response_buf == NULL) {
        EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s: api name %s - domain: %s, Failed to allocate memory for response buf", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    if (STATUS_CODE == HTTP_STATUS_OK) {
        snprintf(response_buf, EXPORTER_USER_PORTAL_ID_STR_LEN, "{\"status\": \"Success\"}");
    } else {
        snprintf(response_buf, EXPORTER_USER_PORTAL_ID_STR_LEN, "{\"status\": \"Failure in response\"}");
    }

    ret = exporter_request_respond_with_json_dynamic_buffer(request, STATUS_CODE, EXPORTER_STATUS_CODE, response_buf, 1);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "[SESS_PROC] USER_EMAIL: %s api name %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    }
    EXPORTER_FREE(response_buf);
    return ret;
}
/*
 * Make error response
 */
static int exporter_portal_make_api_error_response(char *id, char *reason, char *domain, char *name_id, exporter_user_portal_api_error_response_t **response)
{
    exporter_user_portal_api_error_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_error_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "Unable to allocate portal user response for portal: %s", domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    resp->domain = EXPORTER_PORTAL_SAFE_STRDUP(domain);
    resp->id = EXPORTER_PORTAL_SAFE_STRDUP(id);
    resp->reason = EXPORTER_PORTAL_SAFE_STRDUP(reason);
    resp->nameId = EXPORTER_PORTAL_SAFE_STRDUP(name_id);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Make image response
 */
static int exporter_portal_make_api_image_response(int64_t id, char *image_name, char *image_data, char *favicon_image_data, exporter_user_portal_api_image_response_t **response)
{
    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];

    exporter_user_portal_api_image_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_image_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "Unable to allocate portal iamge response for portal");
        return ZPATH_RESULT_NO_MEMORY;
    }

    snprintf(id_str, sizeof(id_str), "%"PRId64, id);

    resp->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
    resp->imageName = EXPORTER_PORTAL_SAFE_STRDUP(image_name);
    resp->imageData = EXPORTER_PORTAL_SAFE_STRDUP(image_data);
    resp->faviconImageData = EXPORTER_PORTAL_SAFE_STRDUP(favicon_image_data);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Send error response for request
 */
int exporter_portal_api_error_response(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, char *id, char *reason, char *domain, char *name_id)
{
    int ret;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    exporter_user_portal_api_error_response_t *response = NULL;

    ret = exporter_portal_make_api_error_response(id, reason, domain, name_id, &response);

    EXPORTER_LOG(AL_ERROR, "%s: User portal api error: domain: %s, name_id: %s, id: %s, reason: %s", request->name, domain, name_id, id, reason);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_error_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            ret = exporter_request_respond_with_json(request, status, error, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal api_error_response: domain: %s, respond-with-json-ret: %s", request->name, domain, zpath_result_string(ret));
            }
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: User portal response error: domain: %s, ret: %s", request->name, domain, zpath_result_string(ret));
    }

    if (response != NULL) {
        EXPORTER_FREE(response->id);
        EXPORTER_FREE(response->reason);
        EXPORTER_FREE(response->domain);
        EXPORTER_FREE(response->nameId);

        EXPORTER_FREE(response);
    }

    return ret;
}

/*
 * Send image response for request
 */
static int exporter_portal_api_image_response(struct exporter_request *request, int64_t id, char *image_name, char *image_data, char *favicon_image_data)
{
    int ret;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    exporter_user_portal_api_image_response_t *response = NULL;

    ret = exporter_portal_make_api_image_response(id, image_name, image_data, favicon_image_data, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal image response: name: %s, image_data_len: %d, ret=%s", request->name, image_name, (int)EXPORTER_PORTAL_SAFE_STRLEN(image_data), zpath_result_string(ret));

    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_image_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal api_image_response: name: %s, respond-with-json-ret: %s", request->name, image_name, zpath_result_string(ret));
            }
        }
    }

    exporter_portal_api_free_response(request, response);

    return ret;
}

/*
 * Process the Get-User API to produce response json
 */
int exporter_portal_process_get_user_response(struct exporter_request *request,
                                                     exporter_user_portal_api_user_response_t **response)
{
    enum exporter_portal_user_auth_state auth_state;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    exporter_user_portal_api_user_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_user_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s - Unable to allocate %s response for domain: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    ret = exporter_portal_get_user_auth_state(request, &auth_state);
    char *auth_state_str = NULL;
    if ((ret == ZPATH_RESULT_NO_ERROR) && (auth_state == EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_IN)) {
        resp->nameId = EXPORTER_PORTAL_SAFE_STRDUP(exporter_user_portal_request_state_get_name(request->portal_info));
        resp->domain = EXPORTER_PORTAL_SAFE_STRDUP(domain);
    } else {
        auth_state = EXPORTER_PORTAL_USER_AUTH_STATE_UNKNOWN;
        EXPORTER_LOG(AL_ERROR, "%s: User portal - %s, domain: %s ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    }

    auth_state_str = get_portal_user_auth_state_name(auth_state);
    resp->authState = EXPORTER_PORTAL_SAFE_STRDUP(auth_state_str);

    /* Check if markdown enabled */
    resp->markdownContentEnabled = is_markdown_content_enabled(request->conn->exporter_domain->customer_gid);

    if (request->conn->exporter_domain->is_ot) {
        uint myfiles_feature_disabled = 0, enduser_approvals_feature_disabled = 0;
        resp->realVncEnabled = !is_realvnc_disabled(request->conn->exporter_domain->customer_gid);
        resp->displayQualityEnabled =
            !is_pra_display_quality_disabled(request->conn->exporter_domain->customer_gid);

        if (!g_exporter_ot_mode || is_pra_disabled(request->conn->exporter_domain->customer_gid)) {
            EXPORTER_LOG(AL_ERROR, "%s: portal - %s, domain: %s error : PRA is not enabled",
                                request->name, api_name, domain);
            return ret;
        }

        if (is_pra_advanced_file_transfer_disabled(request->conn->exporter_domain->customer_gid)) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s PRA My-Files feature is disabled",
                                       request->name, api_name, domain);
            myfiles_feature_disabled = 1;
        }
        if (is_pra_enduser_approvals_disabled(request->conn->exporter_domain->customer_gid)) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s PRA EndUser-Approvals feature is disabled",
                                       request->name, api_name, domain);
            enduser_approvals_feature_disabled = 1;
        }
        if (myfiles_feature_disabled  && enduser_approvals_feature_disabled) {
           return ret;
        }
        ret = exporter_portal_get_policy_state(request);
        if (ret == ZPATH_RESULT_NO_ERROR && request->portal_policy_rule_id) {
            if (!myfiles_feature_disabled) {
                // If we have a Portal policy and feature flag is enabled, myFiles feature is enabled.
                resp->myFilesEnabled = 1;
                if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_DELETE_FILE) {
                    resp->myFilesDeleteEnabled = 1;
                }
                if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_ACCESS_UNINSPECTED_FILE) {
                    resp->myFilesUninspectedEnabled = 1;
                }
                if (exporter_portal_file_transfer_enabled(request->conn->exporter_domain->customer_gid)) {
                    resp->portalFileTransferEnabled = 1;
                }
            }
            if (!enduser_approvals_feature_disabled) {
                resp->endUserApprovalsEnabled = 1;
                if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REQUEST_APPROVALS) {
                    resp->approvalsRequestEnabled = 1;
                }
                if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REVIEW_APPROVALS) {
                    resp->approvalsReviewEnabled = 1;
                }
             }
        } else if (ret == ZPATH_RESULT_NOT_FOUND)  {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s domain: %s no portal policy configured", request->name, api_name, domain);
            ret = ZPATH_RESULT_NO_ERROR;
        } else if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: portal - %s, domain: %s ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    return ret;
}

/*
 * Process the Get-Company API to produce response json
 */
static int exporter_portal_process_get_company_response(struct exporter_request *request,
                                                        exporter_user_portal_api_company_response_t **response)
{
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    struct zpath_customer *customer = NULL;
    struct zpath_customer_logo *customer_logo = NULL;
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int logo_len;
    int name_len;
    size_t favicon_len = 0;
    int ret;
    int i;

    exporter_user_portal_api_company_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_company_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for domain: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    ret = zpath_customer_get(request->conn->exporter_domain->customer_gid, &customer, exporter_request_wally_callback, request, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {

        char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];

        snprintf(id_str, sizeof(id_str), "%"PRId64,  customer->gid);

        resp->domain = EXPORTER_PORTAL_SAFE_STRDUP(customer->domain_name);
        resp->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
        resp->otherDomainsCount = customer->other_domains_count;

        /* Allocate domains array */
        if (resp->otherDomainsCount) {
            resp->otherDomains = EXPORTER_CALLOC(resp->otherDomainsCount * sizeof(char *));
        }

        /* Set domain entries in domains array */
        for (i = 0; i < customer->other_domains_count; i++) {
            resp->otherDomains[i] = EXPORTER_PORTAL_SAFE_STRDUP(customer->other_domains[i]);
        }

        ret = zpath_customer_logo_get(request->conn->exporter_domain->customer_gid, &customer_logo, exporter_request_wally_callback, request, 0);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            logo_len = EXPORTER_PORTAL_SAFE_STRLEN(customer_logo->img_data);
            name_len = EXPORTER_PORTAL_SAFE_STRLEN(customer_logo->img_name);
            favicon_len = EXPORTER_PORTAL_SAFE_STRLEN(customer_logo->favicon_img_data);

            resp->hasLogo = (logo_len > 0) ? 1 : 0;
            resp->name = EXPORTER_PORTAL_SAFE_STRDUP(customer_logo->img_name);
            if (resp->hasLogo) {
                resp->logoImageData = EXPORTER_PORTAL_SAFE_STRDUP(customer_logo->img_data);
                resp->logoImageName = EXPORTER_PORTAL_SAFE_STRDUP(customer_logo->img_name);
            }
            resp->hasFavIcon = (favicon_len > 0) ? 1 : 0;
            if (resp->hasFavIcon) {
                resp->favIconImageData = EXPORTER_PORTAL_SAFE_STRDUP(customer_logo->favicon_img_data);
            }

            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - domain: %s - found customer logo, logo_len: %d, image_name_len: %d, favicon_len %lu",
                request->name, api_name, domain, logo_len, name_len, favicon_len);
        } else if (request->conn->exporter_domain->is_ot && ret == ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - customer logo not found for domain: %s", request->name, api_name, domain);
            return ZPATH_RESULT_NO_ERROR;
        } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer logo async for domain: %s", request->name, api_name, domain);
            exporter_portal_defer_api_request_async(request);
            return ret;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: %s error: %s - fetching customer logo for domain: %s", request->name, api_name, zpath_result_string(ret), domain);
        }
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer async for domain: %s", request->name, api_name, domain);
        exporter_portal_defer_api_request_async(request);
        return ret;
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: %s - error: %s fetching customer for domain: %s", request->name, api_name, zpath_result_string(ret), domain);
    }

    return ret;
}


/*
 * Process the Get-Portal API to produce portal response json
 */
static int exporter_portal_process_get_portal_response(struct exporter_request *request,
                                                       exporter_user_portal_api_portal_response_t **response)
{
    struct zpn_user_portal *user_portal = NULL;
    struct zpn_user_portal *tmp_user_portal = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    struct zpn_user_portal_aup *aup = NULL;
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int ret;
    uint8_t is_unified_portal = 0;

    exporter_user_portal_api_portal_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_portal_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s - Unable to allocate %s response - domain: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    if (request->conn->exporter_domain->is_ot) {
        ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid,
                                                            domain, &sra_portal);

        /* unified-portal - For Unified portal, get the user_portal_gid from the zpn_sra_portal, fetch the hostName
         * associated to that gid and pass it back in the response. This is needed for PRA Portal to render
         * the User portal associated to itself.
         */

        if (!ret && (is_unified_portal_feature_enabled(request->conn->exporter_domain->customer_gid) == 1) &&
            (sra_portal && sra_portal->user_portal_gid) ) {
            is_unified_portal = 1;
            ret = zpn_user_portal_table_get_by_gid(request->conn->exporter_domain->customer_gid,
                                                   sra_portal->user_portal_gid, &tmp_user_portal);
        }
    } else {
        ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain, &user_portal,
                                                        exporter_request_wally_callback, request, 0);
    }

    EXPORTER_LOG(AL_NOTICE, "%s [UNIP_PORTAL_LOG] [%s:%s:%d] User portal: %s, PRA Portal: %s[user portal- %"PRId64":%s], UnifiedPortal?%s ZS managed domain?%s",
                                          request->name, domain, api_name, ret,
                                          user_portal?user_portal->domain:"", sra_portal?sra_portal->domain:"",
                                          sra_portal?sra_portal->user_portal_gid:0, tmp_user_portal?tmp_user_portal->domain:"",
                                          is_unified_portal?"Yes":"No",
                                          request->conn->exporter_domain->is_managed_ba?"Yes":"No");

    if (ret == ZPATH_RESULT_NO_ERROR && (sra_portal || user_portal)) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s by domain: %s found", request->name, api_name, domain);
        char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];
        char logo_name_str[EXPORTER_USER_PORTAL_NAME_STR_LEN];
        int64_t reauth_timeout = 0;

        resp->description = user_portal ? EXPORTER_PORTAL_SAFE_STRDUP(user_portal->description) : EXPORTER_PORTAL_SAFE_STRDUP("SRA Portal");
        resp->name = user_portal ? EXPORTER_PORTAL_SAFE_STRDUP(user_portal->name) : EXPORTER_PORTAL_SAFE_STRDUP(sra_portal->name);
        resp->domain = user_portal ? EXPORTER_PORTAL_SAFE_STRDUP(user_portal->domain) : EXPORTER_PORTAL_SAFE_STRDUP(sra_portal->domain);
        resp->hasLogo = (user_portal ? EXPORTER_PORTAL_SAFE_STRLEN(user_portal->icon) :
                EXPORTER_PORTAL_SAFE_STRLEN(sra_portal->icon)) ? 1 : 0;
        resp->enabled = user_portal ? user_portal->enabled : sra_portal->enabled;
        if (resp->hasLogo) {
            resp->logoImageData = user_portal ? EXPORTER_PORTAL_SAFE_STRDUP(user_portal->icon) : EXPORTER_PORTAL_SAFE_STRDUP(sra_portal->icon);
            snprintf(logo_name_str, sizeof(logo_name_str), "/portal/%s/logo", domain);
            resp->logoImageName = EXPORTER_PORTAL_SAFE_STRDUP(logo_name_str);
        }

        /* unified-portal - Send hostname for CORS request to fetch resources from User Portal */
        resp->user_portal_host = tmp_user_portal?EXPORTER_PORTAL_SAFE_STRDUP(tmp_user_portal->domain):NULL;
        resp->isUnifiedPortal = is_unified_portal;

        snprintf(id_str, sizeof(id_str), "%"PRId64, user_portal ? user_portal->gid : sra_portal->gid);
        resp->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);

        resp->hasUserNotification = user_portal ? user_portal->user_notification_enabled :
                                                sra_portal->user_notification_enabled;
        if (resp->hasUserNotification) {
            resp->userNotification = user_portal ? EXPORTER_PORTAL_SAFE_STRDUP(user_portal->user_notification) :
                    EXPORTER_PORTAL_SAFE_STRDUP(sra_portal->user_notification);
        }

        ret = zpn_user_portal_aup_get_by_scope_gid(
                user_portal ? user_portal->scope_gid : sra_portal->customer_gid, &aup, NULL, NULL, 0);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - User portal domain: %s aup enabled: %d, found by customer_gid: %"PRId64,
                                                  request->name, api_name, domain, aup->enabled, user_portal ? user_portal->customer_gid : sra_portal->customer_gid);
            resp->hasAup = aup->enabled;
        } else {
            EXPORTER_LOG(AL_NOTICE, "%s: %s - error: %s - when fetching aup for domain: %s, treating aup as disabled, customer_gid: %"PRId64, request->name,
                         api_name, zpath_result_string(ret), domain, user_portal ? user_portal->customer_gid : sra_portal->customer_gid);
            resp->hasAup = 0;
            ret = ZPATH_RESULT_NO_ERROR;
        }

        if (user_portal) {
            ret = zpn_rule_customer_get_default_rule_reauth(user_portal->customer_gid, &reauth_timeout);
        } else {
            /* PRA default scope portal can support /zscope level access, pick the scope from the request */
            ret = zpn_rule_scope_get_default_rule_shortest_reauth(request->scope_gid, &reauth_timeout);
        }
        if (ret) {
            reauth_timeout = ZPN_DEFAULT_SAML_EXPIRE_TIME;
            EXPORTER_LOG(AL_ERROR, "%s: Failed to get default rule timeout for customer_gid: %"PRId64", reason: %s, sending default timeout...",
                             request->name, user_portal ? user_portal->customer_gid : sra_portal->customer_gid, zpn_result_string(ret));
            ret = ZPATH_RESULT_NO_ERROR;
        }
        /* ET-82536 - Observed re-auth kicks in 5+ minutes early.
        Hence adjusting reauth value to portal to reduce 320 sec
        so that portal reloads on time */
        if (reauth_timeout >= 320) {
            reauth_timeout = (reauth_timeout - 320);
        }
        resp->timeout = reauth_timeout;

        /* If PRA portal belongs to default scope, set boolean to yes. Set 0 for BA portal or PRA portal belonging to custom scope */
        resp->isDefaultPortal = (sra_portal? (sra_portal->scope_gid == request->conn->exporter_domain->customer_gid? 1: 0): 0);

        int is_dta_enabled = zpn_get_delegated_admin_status(request->conn->exporter_domain->customer_gid);
        if (!is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid) &&
                is_dta_enabled) {
            /* Send the name of the scope only for /zscope level access */
            if (sra_portal && sra_portal->scope_gid == sra_portal->customer_gid && sra_portal->scope_gid != request->scope_gid) {
                char *scope_name = zpn_scope_get_name(request->scope_gid);
                if (scope_name) {
                    char scope_id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = {0};
                    snprintf(scope_id_str, sizeof(scope_id_str), "%"PRId64, request->scope_gid);
                    resp->scopeName  = EXPORTER_PORTAL_SAFE_STRDUP(scope_name);
                    resp->scopeId = EXPORTER_PORTAL_SAFE_STRDUP(scope_id_str);
                }
            }
        }

    } else {
        EXPORTER_LOG(AL_ERROR, "%s: %s - error: %s fetching user portal for domain: %s", request->name, api_name, zpath_result_string(ret), domain);
    }

    return ret;
}

/*
 * Process the Get-AUP API to produce aup response json
 */
static int exporter_portal_process_get_aup_response(struct exporter_request *request,
                                                    exporter_user_portal_api_aup_response_t **response)
{

    struct zpn_user_portal_aup *aup = NULL;
    struct zpn_user_portal *user_portal = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    exporter_user_portal_api_aup_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_aup_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    if (request->conn->exporter_domain->is_ot) {
        ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain,
                                                                &sra_portal);
    } else {
        ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain, &user_portal, NULL, NULL, 0);
    }
    /* Appease SonarQube */
    if (!sra_portal && !user_portal) ret = ZPATH_RESULT_BAD_STATE;
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = zpn_user_portal_aup_get_by_scope_gid(user_portal? user_portal->scope_gid: sra_portal->scope_gid, &aup, NULL, NULL, 0);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            resp->name = EXPORTER_PORTAL_SAFE_STRDUP(aup->name);
            resp->description = EXPORTER_PORTAL_SAFE_STRDUP(aup->description);
            resp->aupMessage = EXPORTER_PORTAL_SAFE_STRDUP(aup->aup);
            resp->displayFrequency = EXPORTER_PORTAL_SAFE_STRDUP(aup->display_frequency);
            resp->email = EXPORTER_PORTAL_SAFE_STRDUP(aup->email);
            resp->phone = EXPORTER_PORTAL_SAFE_STRDUP(aup->phone);
            resp->enabled = aup->enabled;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: %s - error: %s fetching aup for domain: %s, customer_gid: %"PRId64, request->name, api_name, zpath_result_string(ret), domain, user_portal? user_portal->customer_gid: sra_portal->customer_gid);
        }
    }

    return ret;
}

/* Compare link names for ordering */
static int compare_link_names(const void *l1, const void *l2)
{
    const struct zpn_user_portal_links *link1 = *((const struct zpn_user_portal_links **) l1);
    const struct zpn_user_portal_links *link2 = *((const struct zpn_user_portal_links **) l2);
    int cmp = 0;

    if ((link1->name != NULL) && (link2->name != NULL)) {
        cmp = strcmp(link1->name, link2->name);
    }

    //EXPORTER_LOG(AL_DEBUG, "link1=%s, link2=%s, cmp=%d", link1->name, link2->name, cmp);

    return cmp;
}

/* Compare console names for ordering */
static int compare_sra_console_names(const void *l1, const void *l2)
{
    const struct zpn_sra_console *console1 = *((const struct zpn_sra_console **) l1);
    const struct zpn_sra_console *console2 = *((const struct zpn_sra_console **) l2);
    int cmp = 0;

    if ((console1->name != NULL) && (console2->name != NULL)) {
        cmp = strcmp(console1->name, console2->name);
    } else if (console2->name != NULL) {
        cmp = 1;
    } else if (console1->name != NULL) {
        cmp = -1;
    }

    //EXPORTER_LOG(AL_DEBUG, "console1=%s, console2=%s, cmp=%d", console1->name, console2->name, cmp);

    return cmp;
}

/* Compare link names for ordering */
static int compare_zapp_link_os_types(const void *l1, const void *l2)
{
    const struct zpn_user_portal_zapp_links *link1 = *((const struct zpn_user_portal_zapp_links **)l1);
    const struct zpn_user_portal_zapp_links *link2 = *((const struct zpn_user_portal_zapp_links **)l2);
    int cmp = 0;

    if ((link1->os_type != NULL) && (link2->os_type != NULL)) {
        cmp = strcmp(link1->os_type, link2->os_type);
    }

    //EXPORTER_LOG(AL_DEBUG, "link1=%s, link2=%s, cmp=%d", link1->os_type, link2->os_type, cmp);

    return cmp;
}

/* Compare scope names for ordering */
static int compare_sra_scope_names(const void *s1, const void *s2)
{
    const struct zpn_scope *scope1 = *((const struct zpn_scope **)s1);
    const struct zpn_scope *scope2 = *((const struct zpn_scope **)s2);
    int cmp = 0;

    if ((scope1->name != NULL) && (scope2->name != NULL)) {
        cmp = strcmp(scope1->name, scope2->name);
    } else if (scope2->name != NULL) {
        cmp = 1;
    } else if (scope1->name != NULL) {
        cmp = -1;
    }

    return cmp;
}


/*
 * Get ordered links by portal gid
 */
static int exporter_portal_get_ordered_links_by_portal_gid(int64_t gid, struct zpn_user_portal_links *links_ordered[], size_t *out_ordered_count, size_t in_list_len)
{
    struct zpn_user_portal_links *link;
    struct zpn_user_portal_user_portal_link_mapping *link_mapping[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    size_t enabled_count = 0;
    size_t link_mapping_count = 0;
    size_t i;
    int ret;

    /* Get all mapping links pointing at current portal gid */
    ret = zpn_user_portal_link_mapping_table_get_by_user_portal_id(gid, link_mapping, &link_mapping_count, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Ensure we dont exceed user supplied buffer size */
        if (in_list_len < link_mapping_count) {
            EXPORTER_LOG(AL_ERROR, "Ordered links list len: %zd is smaller than number of links: %zd", in_list_len, link_mapping_count);
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }

        /* Get link id for each link mapping entry, read the link by gid */
        for (i = 0; i < link_mapping_count; i++) {
            /* unified-portal - Form dynamic link and give back to UI */
            ret = zpn_user_portal_links_table_get_by_gid(link_mapping[i]->user_portal_link_id, &link, NULL, NULL, 0);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                /* Extract enabled link */
                if (link->enabled) {
                    links_ordered[enabled_count++] = link;
                }
            } else {
                /* We should never have any invalid link id reference in mapping table */
                EXPORTER_LOG(AL_ERROR, "Error reading portal links table for user_portal(%ld), user_portal_link_id(%ld), error = %s",
                             (long)gid, (long)link_mapping[i]->user_portal_link_id, zpath_result_string(ret));
            }
        }
    } else {
        if (ret != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "Error reading portal links mapping table for user_portal(%ld), error = %s", (long)gid, zpath_result_string(ret));
        } else {
            EXPORTER_DEBUG_USER_PORTAL("No link mapping entry found for user_portal(%ld)", (long)gid);
            ret = ZPATH_RESULT_NO_ERROR;
        }
    }

    if (enabled_count > 0) {
        /* Sort links by name */
        qsort(links_ordered, enabled_count, sizeof(struct zpn_user_portal_links *), compare_link_names);
    }
    *out_ordered_count = enabled_count;

    return ret;
}

/*
 * Get ordered consoles by SRA portal gid
 */
static int exporter_sra_portal_get_ordered_consoles_by_portal_gid(int64_t gid, int64_t customer_id, int64_t scope_gid, struct zpn_sra_console *consoles_ordered[], size_t *out_ordered_count, size_t in_list_len)
{
    struct zpn_sra_console *console = NULL;
    struct zpn_sra_portal_sra_console_mapping *link_mapping[EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL] = {NULL};
    size_t enabled_count = 0;
    size_t link_mapping_count = 0;
    int i;
    int ret;

    /* Get all mapping links pointing at current portal gid */
    ret = zpn_sra_portal_get_console_mappings_for_portal(gid, link_mapping, &link_mapping_count, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Ensure we dont exceed user supplied buffer size */
        if (in_list_len < link_mapping_count) {
            EXPORTER_LOG(AL_ERROR, "Ordered links list len: %zd is smaller than number of links: %zd", in_list_len, link_mapping_count);
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }

        /* Get link id for each link mapping entry, read the link by gid */
        for (i = 0; i < link_mapping_count; i++) {

            if (link_mapping[i]->scope_gid != scope_gid) {
                EXPORTER_DEBUG_USER_PORTAL("Mapping entry (%" PRId64 ") does not belong to scope (%" PRId64 ") for sra_portal(%" PRId64 ")",
                                            link_mapping[i]->sra_console_id, scope_gid, gid);
                continue;
            }

            ret = zpn_sra_console_get_by_id_immediate(link_mapping[i]->sra_console_id, customer_id, scope_gid, &console);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                /* Extract enabled link */
                if (console->enabled) {
                    consoles_ordered[enabled_count++] = console;
                }
            } else {
                /* We should never have any invalid link id reference in mapping table */
                EXPORTER_LOG(AL_ERROR, "Error reading portal links table for sra_portal(%" PRId64 "), sra_portal_link_id(%" PRId64 "), error = %s",
                             gid, link_mapping[i]->sra_console_id, zpath_result_string(ret));
            }
        }
    } else {
        if (ret != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "Error reading portal links mapping table for sra_portal(%" PRId64 "), error = %s", gid, zpath_result_string(ret));
        } else {
            EXPORTER_DEBUG_USER_PORTAL("No link mapping entry found for sra_portal(%" PRId64 ")", gid);
            ret = ZPATH_RESULT_NO_ERROR;
        }
    }

    if (enabled_count > 0) {
        /* Sort links by name */
        qsort(consoles_ordered, enabled_count, sizeof(struct zpn_sra_console *), compare_sra_console_names);
    }
    *out_ordered_count = enabled_count;

    return ret;
}


/*
 * Get ordered zapp links by customer gid
 */
static int exporter_portal_get_ordered_zapp_links_by_customer_gid(int64_t customer_gid, struct zpn_user_portal_zapp_links *links_ordered[], size_t *out_ordered_count, size_t in_list_len)
{
    struct zpn_user_portal_zapp_links *links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    size_t enabled_count = 0;
    size_t links_count = 0;
    size_t i;
    int ret;

    ret = zpn_user_portal_zapp_links_table_get_by_customer_gid(customer_gid, links, &links_count, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (in_list_len < links_count) {
            EXPORTER_LOG(AL_ERROR, "Ordered links list len: %zd is smaller than number of links: %zd", in_list_len, links_count);
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }

        /* Extract enabled links and sort by name */
        for (i = 0; i < links_count; i++) {
            if (links[i]->enabled) {
                links_ordered[enabled_count++] = links[i];
            }
        }
    }

    if (enabled_count > 0) {
        qsort(links_ordered, enabled_count, sizeof(struct zpn_user_portal_zapp_links *), compare_zapp_link_os_types);
    }
    *out_ordered_count = enabled_count;

    return ret;
}


static void exporter_portal_free_link_entry(struct exporterUserPortalApiLink *link)
{
    if (link) {
        EXPORTER_FREE(link->id);
        EXPORTER_FREE(link->description);
        EXPORTER_FREE(link->name);
        EXPORTER_FREE(link->linkUrl);
        EXPORTER_FREE(link->protocol);
        EXPORTER_FREE(link->linkPath);

        /* Free link itself */
        EXPORTER_FREE(link);

        EXPORTER_PORTAL_API_STATS_INC(get_links_entry_free, 1);
    }
}

/* Perform policy evaluation for SRA applications */
static int exporter_sra_portal_check_approval_access_for_sra_console(struct exporter_request *request,
                                                                     struct zpn_sra_console *sra_console,
                                                                     int64_t scope_gid,
                                                                     uint64_t *hasAccess,
                                                                     int64_t *approval_id,
                                                                     enum zpe_access_approval_status *approval_status) {
    int ret = 0;
    int vnc_disabled = 0;
    int realvnc_disabled = 0;
    vnc_disabled = is_vnc_disabled(request->conn->exporter_domain->customer_gid);
    realvnc_disabled = is_realvnc_disabled(request->conn->exporter_domain->customer_gid);

    if (!sra_console->enabled) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: console %s disabled", request->name, sra_console->name);
        return ZPATH_RESULT_ERR;
    }
    if (sra_console->scope_gid != scope_gid) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: console %s does not belong to %"PRId64, request->name, sra_console->name, scope_gid);
        return ZPATH_RESULT_ERR;
    }
    if (!sra_console->sra_app_id) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: console %s has no sra_app_id", request->name, sra_console->name);
        return ZPATH_RESULT_ERR;
    }

    /* Get sra_application */
    struct zpn_sra_application *sra_application = NULL;

    ret = zpn_sra_application_get_by_id_immediate(sra_console->sra_app_id, &sra_application);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_application %"PRId64": %s", request->name, sra_console->sra_app_id, zpn_result_string(ret));
        return ZPATH_RESULT_ERR;
    }
    if (sra_application == NULL) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_application %"PRId64, request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }
    if (sra_application->deleted) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" is deleted", request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }
    /* SRA applications can be shared across scopes, so do not verify scope GID directly */
    if (sra_application->customer_gid != request->conn->exporter_domain->customer_gid) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" belongs to another customer", request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }
    if (!sra_application->enabled) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" is disabled", request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }

    if (vnc_disabled && !strcmp(sra_application->protocol, "VNC")) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: VNC feature flag for sra_application %"PRId64" is disabled",
                request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }

    if (realvnc_disabled && !strcmp(sra_application->protocol, "REALVNC")) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: REALVNC feature flag for sra_application %"PRId64" is disabled",
                request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }

    /* Get zpn_application object, AKA app segment */
    struct zpn_application *application = NULL;

    ret = zpn_application_get_by_id_immediate(sra_application->app_id, &application);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve application %"PRId64": %s", request->name, sra_application->app_id, zpn_result_string(ret));
        return ZPATH_RESULT_ERR;
    }
    if (application == NULL) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve application %"PRId64, request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }
    if (application->deleted) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" is deleted", request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }
    if (application->customer_gid != request->conn->exporter_domain->customer_gid) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" belongs to another customer", request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }
    if (!application->enabled) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" is disabled", request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }

    ret = exporter_validate_application_scope(scope_gid, application);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: scope %"PRId64" cannot access application %"PRId64"",
                request->name, scope_gid, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }

    enum zpe_access_action matched_action = zpe_access_action_deny;

    if (!request->policy_state->scim_policy_enabled || request->policy_state->active) {
         ret = zpn_broker_policy_locked_link_policy_pra_check(scope_gid,
                                                              request->attr,
                                                              request->conn->exporter_domain->is_ot,
                                                              request->policy_state->idp_gid,
                                                              zpe_policy_type_access,
                                                              sra_application->domain,
                                                              request->policy_state->general_state_hash,
                                                              request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                              request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                              NULL, // NULL approval_id to bypass approval
                                                              approval_status,
                                                              sra_application->protocol,
                                                              sra_application->port,
                                                              &matched_action,
                                                              NULL, NULL,NULL,
                                                              exporter_request_wally_callback,
                                                              request,
                                                              NULL,
                                                              0);

        /* Stub out approval processing in the above call using NULL approval_id
         * to replace with email based processing for faster lookups only for Portals */
        if ((matched_action == zpe_access_action_approval_required)
            && request->policy_state->user_email) {
            ret = ZPN_RESULT_NO_ERROR; /* Reset as ret from the above call will be ZPN_RESULT_ERR */
            /* Optimize and search based on user email for global portals */
            ret = zpn_policy_check_approval_based_on_user_email(request->conn->exporter_domain->customer_gid,
                                                                scope_gid,
                                                                request->policy_state->user_email,
                                                                sra_application->app_id,
                                                                exporter_request_wally_callback,
                                                                request,
                                                                0,
                                                                approval_id,
                                                                NULL,
                                                                approval_status,
                                                                &matched_action);
        }
    } else {
        EXPORTER_LOG(AL_WARNING, "%s: policy state not active", request->name);
        return ZPATH_RESULT_ERR;
    }
    if (ret == ZPN_RESULT_NO_ERROR) {
        if (matched_action == zpe_access_action_allow) *hasAccess = 1;
    } else if (ret != ZPN_RESULT_ASYNCHRONOUS) {
        // in error situations we don't want to give access,
        // but we also don't want to kill the api call so we will just let it continue
        ret = ZPN_RESULT_NO_ERROR;
    }

    return ret;
}

/*
 * Process the Get-Links API to produce links response json
 */
static int exporter_portal_process_get_links_response(struct exporter_request *request,
                                                      exporter_user_portal_api_links_response_int_t **response)
{
    struct zpn_user_portal_links *ordered_links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    struct exporterUserPortalApiLink *api_link;
    struct zpn_user_portal *user_portal = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    size_t ordered_count = 0;
    int ret;
    size_t i;
    int has_access_count = 0;

    exporter_user_portal_api_links_response_int_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_links_response_int_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    if (request->conn->exporter_domain->is_ot) {
        ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain,
                                                            &sra_portal);
    } else {
        ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                        &user_portal, NULL, NULL, 0);
    }
    /* Appease SonarQube */
    if (!sra_portal && !user_portal) ret = ZPATH_RESULT_BAD_STATE;
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal ? user_portal->enabled : sra_portal->enabled) {
            ret = exporter_portal_get_ordered_links_by_portal_gid(
                        user_portal ? user_portal->gid : sra_portal->gid, ordered_links, &ordered_count, EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                for (i = 0; i < ordered_count; i++) {
                    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];

                    api_link = EXPORTER_CALLOC(sizeof(struct exporterUserPortalApiLink));
                    if (api_link == NULL) {
                        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate portal api link entry for portal: %s", request->name, domain);
                        return ZPATH_RESULT_NO_MEMORY;
                    }
                    EXPORTER_PORTAL_API_STATS_INC(get_links_entry_alloc, 1);

                    snprintf(id_str, sizeof(id_str), "%"PRId64, ordered_links[i]->gid);

                    api_link->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
                    api_link->name = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->name);
                    api_link->description = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->description);
                    api_link->linkUrl = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->link);
                    api_link->hasIcon = EXPORTER_PORTAL_SAFE_STRLEN(ordered_links[i]->icon) ? 1 : 0;
                    api_link->protocol = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->protocol);
                    api_link->linkPath = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->link_path);
                    api_link->application_gid = ordered_links[i]->application_gid;

                    /* unified-portal - Request for "/links" */
                    /* Overwrite the linkUrl with external fqdn */
                    char *ext_domain = exporter_get_managed_domain_name(request->conn->exporter_domain->customer_gid, ordered_links[i]->link,
                                                                             strnlen(ordered_links[i]->link,MAX_DOMAIN_LEN_SIZE));

                    if (!request->policy_state->scim_policy_enabled || request->policy_state->active) {
                        ret = zpn_broker_policy_classify_link(request->scope_gid,
                                                              request->attr,
                                                              request->policy_state->idp_gid,
                                                              api_link->linkUrl,
                                                              ext_domain,
                                                              &(api_link->hasZappAccess),
                                                              &(api_link->hasBrowserAccess),
                                                              &(api_link->hasAccess),
                                                              request->policy_state->general_state_hash,
                                                              request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                              request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                              exporter_request_wally_callback,
                                                              request,
                                                              api_link->application_gid);
                    } else {
                        // User was not active don't display any link!
                        ret = ZPATH_RESULT_NO_ERROR;
                        api_link->hasAccess = 0;
                    }

                    if (ext_domain) {
                        EXPORTER_FREE(api_link->linkUrl);
                        api_link->linkUrl = ext_domain; /* MAlloced memory */
                    }

                    if (ret == ZPATH_RESULT_NO_ERROR) {
                        /* Update link into response only if user has access */
                        if (api_link->hasAccess) {
                            resp->links[has_access_count] = api_link;
                            has_access_count++;
                        } else {
                            EXPORTER_DEBUG_USER_PORTAL_API("%s: User: %s does not have access to link: %s for portal domain: %s",
                                    request->name, exporter_user_portal_request_state_get_name(request->portal_info),
                                    api_link->name, domain);
                            /* Dont leak inaccessible links */
                            exporter_portal_free_link_entry(api_link);
                        }
                    } else {

                        /* Dont leak links unattached to response */
                        exporter_portal_free_link_entry(api_link);

                        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer link: %s async", request->name, api_name, api_link->name);
                            exporter_portal_defer_api_request_async(request);
                        } else {
                            EXPORTER_LOG(AL_WARNING, "%s: %s - fetching customer link: %s failed", request->name, api_name, api_link->name);
                        }
                        break;
                    }
                }
                resp->linksCount = has_access_count;
            } else {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s ordered links get error: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }  else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}

static void exporter_portal_free_link_icon_entry(struct exporterUserPortalApiLinkIcon *link)
{
    if (link) {
        EXPORTER_FREE(link->id);
        EXPORTER_FREE(link->description);
        EXPORTER_FREE(link->name);
        EXPORTER_FREE(link->linkUrl);
        EXPORTER_FREE(link->protocol);
        EXPORTER_FREE(link->linkPath);
        EXPORTER_FREE(link->imageName);
        EXPORTER_FREE(link->imageData);

        /* Free entry itself */
        EXPORTER_FREE(link);

        EXPORTER_PORTAL_API_STATS_INC(get_links_icons_entry_free, 1);
    }
}

static void exporter_sra_portal_free_link_icon_entry(struct exporterSraPortalApiLinkIcon *link)
{
    if (link) {
        EXPORTER_FREE(link->id);
        EXPORTER_FREE(link->name);
        EXPORTER_FREE(link->protocol);
        EXPORTER_FREE(link->connectionSecurity);
        EXPORTER_FREE(link->domain);
        EXPORTER_FREE(link->imageName);
        if (link->approval_working_hours) EXPORTER_FREE(link->approval_working_hours);
        if (link->scopeId) EXPORTER_FREE(link->scopeId);

        /* Free entry itself */
        EXPORTER_FREE(link);

        EXPORTER_PORTAL_API_STATS_INC(get_sra_links_icons_entry_free, 1);
    }
}

static void exporter_sra_portal_free_shared_link_icon_entry(struct exporterSraPortalApiSharedLinkIcon *link)
{
    if (link) {
        EXPORTER_FREE(link->console_id);
        EXPORTER_FREE(link->console_name);
        EXPORTER_FREE(link->sess_id);
        EXPORTER_FREE(link->protocol);
        EXPORTER_FREE(link->domain);
        EXPORTER_FREE(link->user_name);
        if (link->scope_name) {
            EXPORTER_FREE(link->scope_name);
        }

        /* Free entry itself */
        EXPORTER_FREE(link);

        EXPORTER_PORTAL_API_STATS_INC(get_sra_shared_links_icons_entry_free, 1);
    }
}

static void exporter_sra_portal_free_scope_link_icon_entry(struct exporterSraPortalScopeApiLinkIcon*link)
{
    if (link) {
        EXPORTER_FREE(link->id);
        EXPORTER_FREE(link->name);

        /* Free entry itself */
        EXPORTER_FREE(link);

        EXPORTER_PORTAL_API_STATS_INC(get_sra_scope_links_icons_entry_free, 1);
    }
}

static struct exporterSraPortalScopeApiLinkIcon *exporter_sra_portal_alloc_scope_link_icon_entry(struct zpn_scope *scope_data)
{
    struct exporterSraPortalScopeApiLinkIcon *api_link = NULL;
    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};

    api_link = EXPORTER_CALLOC(sizeof(exporter_sra_portal_scope_api_link_icon_t));
    if (!api_link) {
        EXPORTER_LOG(AL_ERROR, "Unable to allocate scope api link icon entry");
        return NULL;
    }
    EXPORTER_PORTAL_API_STATS_INC(get_sra_scope_links_icons_entry_alloc, 1);

    snprintf(id_str, sizeof(id_str), "%"PRId64, scope_data->gid);
    api_link->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
    api_link->name = EXPORTER_PORTAL_SAFE_STRDUP(scope_data->name);
    return api_link;
}

/*
 * Process the Get-Links-Icons API to produce links-icons response json
 */
static int exporter_portal_process_get_links_icons_response(struct exporter_request *request,
                                                            exporter_user_portal_api_links_icons_response_int_t **response)
{
    struct zpn_user_portal_links *ordered_links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    struct exporterUserPortalApiLinkIcon *api_link;
    struct zpn_user_portal *user_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    size_t ordered_count = 0;
    int ret;
    size_t i;
    int has_access_count = 0;


    exporter_user_portal_api_links_icons_response_int_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_links_icons_response_int_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                    &user_portal, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal->enabled) {
            ret = exporter_portal_get_ordered_links_by_portal_gid(user_portal->gid, ordered_links, &ordered_count, EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL);

            EXPORTER_LOG(AL_NOTICE, "%s[UNIP_PORTAL_LOG] [%s:%s:%d] User Portal:%s ZS managed?%s has %ld apps linked.",
                          request->name, domain, api_name, ret, user_portal->domain,
                          request->conn->exporter_domain->is_managed_ba?"Yes":"No",
                          ordered_count);

            if (ret == ZPATH_RESULT_NO_ERROR) {
                for (i = 0; i < ordered_count; i++) {
                    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];
                    char image_name[2 * EXPORTER_USER_PORTAL_ID_STR_LEN];
                    char *ext_domain = NULL;

                    api_link = EXPORTER_CALLOC(sizeof(struct exporterUserPortalApiLinkIcon));
                    if (api_link == NULL) {
                        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate portal api link icon entry for portal: %s", request->name, domain);
                        return ZPATH_RESULT_NO_MEMORY;
                    }
                    EXPORTER_PORTAL_API_STATS_INC(get_links_icons_entry_alloc, 1);

                    snprintf(id_str, sizeof(id_str), "%"PRId64, ordered_links[i]->gid);
                    snprintf(image_name, sizeof(image_name), "link/%s/icon", id_str);

                    api_link->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
                    api_link->name = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->name);
                    api_link->description = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->description);
                    api_link->hasIcon = EXPORTER_PORTAL_SAFE_STRLEN(ordered_links[i]->icon) ? 1 : 0;
                    api_link->imageName = EXPORTER_PORTAL_SAFE_STRDUP(image_name);
                    api_link->imageData = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->icon);
                    api_link->protocol = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->protocol);
                    api_link->linkPath = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->link_path);
                    api_link->application_gid = ordered_links[i]->application_gid;

                    ext_domain = exporter_get_managed_domain_name(request->conn->exporter_domain->customer_gid, ordered_links[i]->link,
                                                                             strnlen(ordered_links[i]->link,MAX_DOMAIN_LEN_SIZE));

                    if (!request->policy_state->scim_policy_enabled || request->policy_state->active) {
                        ret = zpn_broker_policy_classify_link(request->scope_gid,
                                                              request->attr,
                                                              request->policy_state->idp_gid,
                                                              ordered_links[i]->link,
                                                              ext_domain,
                                                              &(api_link->hasZappAccess),
                                                              &(api_link->hasBrowserAccess),
                                                              &(api_link->hasAccess),
                                                              request->policy_state->general_state_hash,
                                                              request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                              request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                              exporter_request_wally_callback,
                                                              request,
                                                              api_link->application_gid);
                    } else {
                        // User was not active don't display any link!
                        ret = ZPN_RESULT_NO_ERROR;
                        api_link->hasAccess = 0;
                    }

                    /* unified-portal - The application is allowed by policy as well.
                     * Unified Portal will have internal fqdns of the apps linked to it. We should fetch the
                     * corresponding external fqdn to fill up the response to the WebServer.
                     */
                    /* unified-portal - Request for "/links_icons" */
                    api_link->linkUrl = ext_domain ? ext_domain : EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->link);

                    if (ret == ZPATH_RESULT_NO_ERROR) {
                        /* Update link into response only if user has access */
                        if (api_link->hasAccess) {
                            resp->links[has_access_count] = api_link;
                            has_access_count++;
                        } else {
                            EXPORTER_DEBUG_USER_PORTAL_API("%s: User: %s does not have access to link icon: %s for portal domain: %s",
                                    request->name, exporter_user_portal_request_state_get_name(request->portal_info), api_link->name, domain);
                            /* Dont leak inaccessible links */
                            exporter_portal_free_link_icon_entry(api_link);
                        }
                    } else {

                        /* Dont leak links unattached to response */
                        exporter_portal_free_link_icon_entry(api_link);

                        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer link icon: %s async", request->name, api_name, api_link->name);
                            exporter_portal_defer_api_request_async(request);
                            break;
                        } else {
                            EXPORTER_LOG(AL_WARNING, "%s: %s - fetching customer link icon: %s failed", request->name, api_name, api_link->name);
                        }
                        break;
                    }
                }
                resp->linksCount = has_access_count;
            } else {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s ordered links get error: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }  else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}
#ifndef UNIT_TEST
static void exporter_get_working_hours_from_approval(struct exporterSraPortalApiLinkIcon *api_link,
                                                     int64_t approval_id, int64_t scope_gid)
{
    struct zpn_approval *approval = NULL;
    int res;
    res = zpn_approval_get_by_id(approval_id, scope_gid, &approval, NULL, NULL, 0);
    if (res == ZPN_RESULT_NO_ERROR) {
        if (approval->working_hours) api_link->approval_working_hours = EXPORTER_PORTAL_SAFE_STRDUP(approval->working_hours);
        api_link->approval_start_time = approval->approval_start_time;
        api_link->approval_end_time = approval->approval_end_time;
    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to get working hours for approval %"PRId64" result %s", approval_id, zpath_result_string(res));
    }
}

/*
 * Process the PRA Get-Links-Icons API to produce console-links response json
 */

static int exporter_sra_portal_process_get_console_links_response(struct exporter_request *request,
                                                                  exporter_console_links_response_int_t **response)
{
    struct zpn_sra_console *ordered_consoles[EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    struct exporterSraPortalApiLinkIcon *api_link = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);
    size_t ordered_count = 0;
    int ret;
    int i;
    int has_access_count = 0;
    int vnc_disabled = 0;
    int realvnc_disabled = 0;
    vnc_disabled = is_vnc_disabled(request->conn->exporter_domain->customer_gid);
    realvnc_disabled = is_realvnc_disabled(request->conn->exporter_domain->customer_gid);
    int64_t check_scope_gid = 0;

    exporter_console_links_response_int_t *resp = EXPORTER_CALLOC(sizeof(exporter_console_links_response_int_t));

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    resp->requestable_app_ids = NULL;
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES) {
        resp->requestable_app_ids = zhash_table_alloc(&exporter_allocator);
    }

    *response = resp;

    ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (sra_portal->enabled) {
            int is_pra_dta_disabled = is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid);
            if (is_pra_dta_disabled) {
                // PRA-DTA is disabled, display only default scope consoles irrespective of the scope of the user
                check_scope_gid = request->conn->exporter_domain->customer_gid;
            } else {
                check_scope_gid = request->scope_gid;
            }
            ret = zpn_scope_ready_short_1(check_scope_gid,
                                          exporter_request_wally_callback,
                                          request,
                                          fohh_get_current_thread_id());
            if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s zpn scope ready async, prefetching data for scope... async_count: (%d->%d)",
                                       request->name, api_name, domain, request->async_count, request->async_count + 1);
                exporter_portal_defer_api_request_async(request);
                return ret;
            }

            ret = exporter_sra_portal_get_ordered_consoles_by_portal_gid(sra_portal->gid, request->conn->exporter_domain->customer_gid,
                    check_scope_gid, ordered_consoles, &ordered_count, EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                /* In async case, is_large_portal_request will be set already so skip large portal access checks */
                if ((ordered_count > 1000) && (!request->is_large_portal_request)) {
                    int portal_access_count = 0;
                    int64_t allowed_large_portal_access_count = get_allowed_large_portal_access_count();
                    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
                    portal_access_count = get_large_portal_access_active_count();
                    if (portal_access_count >= allowed_large_portal_access_count) {
                        exporter_guac_api_stats_increment(LARGE_PORTAL_ACCESS_BLOCKED_COUNT);
                        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s, portal_access_count %d is greater than max allowed",
                                                       request->name, api_name, domain, get_large_portal_access_active_count());
                        return ZPATH_RESULT_WOULD_BLOCK;
                    } else {
                        exporter_guac_api_stats_increment(LARGE_PORTAL_ACCESS_ACTIVE_COUNT);
                        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s, portal_access_count %d",
                                                       request->name, api_name, domain, get_large_portal_access_active_count());
                        request->is_large_portal_request = 1;
                    }
                }
                for (i = 0; i < ordered_count; i++) {
                    struct zpn_sra_console *sra_console = ordered_consoles[i];
                    if (!sra_console->enabled) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: console %s disabled", request->name, sra_console->name);
                        continue;
                    }
                    if (!sra_console->sra_app_id) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: console %s has no sra_app_id", request->name, sra_console->name);
                        continue;
                    }

                    /* Get sra_application */
                    struct zpn_sra_application *sra_application = NULL;

                    ret = zpn_sra_application_get_by_id_immediate(sra_console->sra_app_id, &sra_application);
                    if (ret != ZPATH_RESULT_NO_ERROR) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_application %"PRId64": %s", request->name, sra_console->sra_app_id, zpn_result_string(ret));
                        continue;
                    }
                    if (sra_application == NULL) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_application %"PRId64, request->name, sra_console->sra_app_id);
                        continue;
                    }
                    if (sra_application->deleted) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" is deleted", request->name, sra_console->sra_app_id);
                        continue;
                    }
                    /* SRA applications can be shared across scopes, so do not verify scope GID directly */
                    if (sra_application->customer_gid != request->conn->exporter_domain->customer_gid) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" belongs to another customer", request->name, sra_console->sra_app_id);
                        continue;
                    }
                    if (!sra_application->enabled) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: sra_application %"PRId64" is disabled", request->name, sra_console->sra_app_id);
                        continue;
                    }
                    if (vnc_disabled && !strcmp(sra_application->protocol, "VNC")) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: VNC feature flag for sra_application %"PRId64" is disabled",
                                                       request->name, sra_console->sra_app_id);
                        continue;
                    }

                    if (realvnc_disabled && !strcmp(sra_application->protocol, "REALVNC")) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: REALVNC feature flag for sra_application %"PRId64" is disabled",
                                                       request->name, sra_console->sra_app_id);
                        continue;
                    }

                    /* Get zpn_application object, AKA app segment */
                    struct zpn_application *application = NULL;

                    ret = zpn_application_get_by_id_immediate(sra_application->app_id, &application);
                    if (ret != ZPATH_RESULT_NO_ERROR) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve application %"PRId64": %s", request->name, sra_application->app_id, zpn_result_string(ret));
                        continue;
                    }
                    if (application == NULL) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve application %"PRId64, request->name, sra_application->app_id);
                        continue;
                    }
                    if (application->deleted) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" is deleted", request->name, sra_application->app_id);
                        continue;
                    }
                    if (application->customer_gid != request->conn->exporter_domain->customer_gid) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" belongs to another customer", request->name, sra_application->app_id);
                        continue;
                    }
                    if (!application->enabled) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: application %"PRId64" is disabled", request->name, sra_application->app_id);
                        continue;
                    }

                    ret = exporter_validate_application_scope(request->scope_gid, application);
                    if (ret != ZPATH_RESULT_NO_ERROR) {
                        EXPORTER_DEBUG_USER_PORTAL_API("%s: scope %"PRId64" cannot access application %"PRId64"",
                                request->name, request->scope_gid, sra_application->app_id);
                        continue;
                    }

                    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
                    char scope_id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
                    char image_name[2 * EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
                    int64_t approval_id = 0;

                    api_link = EXPORTER_CALLOC(sizeof(struct exporterSraPortalApiLinkIcon));
                    if (api_link == NULL) {
                        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate portal api link icon entry for portal: %s", request->name, domain);
                        return ZPATH_RESULT_NO_MEMORY;
                    }
                    EXPORTER_PORTAL_API_STATS_INC(get_sra_links_icons_entry_alloc, 1);

                    snprintf(id_str, sizeof(id_str), "%"PRId64, sra_console->gid);
                    snprintf(image_name, sizeof(image_name), "link/%s/icon", id_str);
                    if (sra_console->scope_gid != sra_console->customer_gid) {
                        snprintf(scope_id_str, sizeof(scope_id_str), "%"PRId64, sra_console->scope_gid);
                    }

                    api_link->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
                    api_link->name = EXPORTER_PORTAL_SAFE_STRDUP(sra_console->name);
                    api_link->protocol = EXPORTER_PORTAL_SAFE_STRDUP(sra_application->protocol);
                    api_link->domain = EXPORTER_PORTAL_SAFE_STRDUP(sra_application->domain);
                    api_link->connectionSecurity = EXPORTER_PORTAL_SAFE_STRDUP(sra_application->connection_security);
                    api_link->scopeId = EXPORTER_PORTAL_SAFE_STRDUP(scope_id_str);
                    api_link->imageName = NULL; // TODO: Can we supply this?

                    enum zpe_access_action saved_matched_action = zpe_access_action_deny;
                    if (!request->policy_state->scim_policy_enabled || request->policy_state->active) {
                         enum zpe_access_action matched_action = zpe_access_action_deny;
                         ret = zpn_broker_policy_locked_link_policy_pra_check(request->scope_gid,
                                                              request->attr,
                                                              request->conn->exporter_domain->is_ot,
                                                              request->policy_state->idp_gid,
                                                              zpe_policy_type_access,
                                                              sra_application->domain,
                                                              request->policy_state->general_state_hash,
                                                              request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                              request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                              NULL, // NULL approval_id to bypass approval
                                                              &(api_link->approval_status),
                                                              sra_application->protocol,
                                                              sra_application->port,
                                                              &matched_action,
                                                              NULL, NULL,NULL,
                                                              exporter_request_wally_callback,
                                                              request,
                                                              NULL,
                                                              0);
                         /* Stub out approval processing in the above call using NULL approval_id
                          * to replace with email based processing for faster lookups only for Portals */
                         if ((matched_action == zpe_access_action_approval_required)
                             && request->policy_state->user_email) {
                             ret = ZPN_RESULT_NO_ERROR; /* Reset as ret from the above call will be ZPN_RESULT_ERR */
                             saved_matched_action = matched_action;
                             /* Optimize and search based on user email for global portals */
                             ret = zpn_policy_check_approval_based_on_user_email(request->conn->exporter_domain->customer_gid,
                                                                request->scope_gid,
                                                                request->policy_state->user_email,
                                                                sra_application->app_id,
                                                                exporter_request_wally_callback,
                                                                request,
                                                                0,
                                                                &approval_id,
                                                                NULL,
                                                                &(api_link->approval_status),
                                                                &matched_action);
                         } // complete email based approval processing
                         if (ret == ZPN_RESULT_NO_ERROR) {
                             if (matched_action == zpe_access_action_allow) api_link->hasAccess = 1;
                         } else if (ret != ZPN_RESULT_ASYNCHRONOUS) {
                             // in error situations we don't want to give access,
                             // but we also don't want to kill the api call so we will just let it continue
                            ret = ZPN_RESULT_NO_ERROR;
                         }
                    } else {
                        // User was not active don't display any link!
                        ret = ZPN_RESULT_NO_ERROR;
                        api_link->hasAccess = 0;
                        api_link->approval_status = zpe_access_approval_none;
                    }

                    if (ret == ZPATH_RESULT_NO_ERROR) {
                        /*
                         * For Auth Domain users : Update link into response only if user has access
                         * or if approval_status is either active, expired or inactive.
                         * For Third-Party users : Update link into response only if user has access
                         * with aproval and approval status is wither active, expired or inactive
                         */
                        if ((!request->is_ot_third_party_login && (api_link->hasAccess || (api_link->approval_status != zpe_access_approval_none))) ||
                            (request->is_ot_third_party_login && approval_id)) {
                            resp->links[has_access_count] = api_link;
                            exporter_get_working_hours_from_approval(api_link, approval_id, request->scope_gid);
                            has_access_count++;
                        } else {
                            EXPORTER_DEBUG_USER_PORTAL_API("%s: User: %s does not have access to link icon: %s for portal domain: %s",
                                    request->name, exporter_user_portal_request_state_get_name(request->portal_info), api_link->name, domain);
                            /* Dont leak inaccessible links */
                            exporter_sra_portal_free_link_icon_entry(api_link);
                        }

                        /*
                         * If the original (saved) action was "approval required"
                         * add the corresponding app segment gid to the list of requestable gids
                         */
                        if (saved_matched_action == zpe_access_action_approval_required &&
                                api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES) {
                            zhash_table_store_not_exist(resp->requestable_app_ids, &application->gid, sizeof(application->gid), 0, &application->gid);
                        }
                    } else {
                        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer link icon: %s async", request->name, api_name, api_link->name);
                            exporter_portal_defer_api_request_async(request);
                            exporter_sra_portal_free_link_icon_entry(api_link);
                            return ret;
                        } else {
                            EXPORTER_LOG(AL_WARNING, "%s: %s - fetching customer link icon: %s failed", request->name, api_name, api_link->name);
                            exporter_sra_portal_free_link_icon_entry(api_link);
                            break;
                        }

                    }
                }
                resp->linksCount = has_access_count;
            } else {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s ordered links get error: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }  else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Error %s retrieving portal for domain %s", request->name, zpath_result_string(ret), domain);
    }
    return ret;
}
#endif

/*
 * Process the PRA Get-Links-Icons API to produce shared console-links response json
 */
static int exporter_sra_portal_process_get_shared_console_links_response(struct exporter_request *request, int64_t customer_gid)
{

    struct exporterSraPortalApiSharedLinkIcon *api_link = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int has_access_count = request->shared_has_count;
    int ret = ZPATH_RESULT_NO_ERROR;
    int is_global_portal = 0;

    if (is_pra_session_monitoring_disabled(customer_gid)) {
        EXPORTER_LOG(AL_INFO, "[SESS_PROC] %s: Session proctoring is disabled for tenant %"PRId64"", request->name, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    /* this is a proxy-exporter case where user is trying to join a shared-session */
    if (!request->share_ctxt.infer_key) {
        char *infer_key = NULL;
        ret = exporter_guac_sess_get_infer_key(customer_gid, &infer_key);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_INFO, "[SESS_PROC] %s: Could not find infer_key for tenant %"PRId64"", request->name, customer_gid);
            return ZPATH_RESULT_ERR;
        }
        request->share_ctxt.infer_key = infer_key;
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC console-api allocated infer_key %p, request %p", request->share_ctxt.infer_key, request);
    }

    if (!request->shared_console_links_response) {
        request->shared_console_links_response = EXPORTER_CALLOC(sizeof(exporter_shared_console_links_response_int_t));
        if (request->shared_console_links_response == NULL) {
            EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
            return ZPATH_RESULT_NO_MEMORY;
        }
    }

    /* Fetch zpn_sra_portal */
    ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
    if ((ret != ZPATH_RESULT_NO_ERROR) || (!sra_portal)) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - Error %s retrieving portal for domain %s", request->name, api_name, zpath_result_string(ret), domain);
        return ret;
    }

    /* check if the portal is global portal (default scope portal) */
    if (sra_portal->scope_gid == sra_portal->customer_gid) {
        is_global_portal = 1;
    }

    const char *user_name = exporter_user_portal_request_state_get_name(request->portal_info);

    if (!request->user_sessions) {
        char   object_store_key[EXPORTER_PRA_SESS_OS_KEY_MAX_SIZE]  = {0};
        snprintf(object_store_key, EXPORTER_PRA_SESS_OS_KEY_MAX_SIZE, "sharedSession|%s|%"PRId64"", user_name, request->conn->exporter_domain->customer_gid);
        ret = exporter_pra_sess_data_fetch(request, object_store_key, &request->user_sessions, exporter_user_sessions_description);
    }
    if ((ret == ZPATH_RESULT_NO_ERROR) && request->user_sessions) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: sucessfully fetched user sessions entry from ostore for user %s", request->name, user_name);

        struct exporter_user_sessions *sess = request->user_sessions->base_structure_void;
        int i = request->shared_sess_count;
        if (sess->sessions_count > EXPORTER_USER_PORTAL_CFG_MAX_SHARED_LINKS_PER_PORTAL) {
            sess->sessions_count = EXPORTER_USER_PORTAL_CFG_MAX_SHARED_LINKS_PER_PORTAL;
        }
        for (; i < sess->sessions_count; i++) {
            struct exporter_user_session_token *sess_token = sess->sessions[i]->base_structure_void;
            if (sess_token->session) {
                struct argo_object *session_data = NULL;
                char *session_token = strstr(sess_token->session, "privilegedSession");
                if (!session_token) {
                    EXPORTER_LOG(AL_ERROR, "%s: Read invalid session key: %s", request->name, sess_token->session);
                    continue;
                }
                ret = exporter_pra_sess_data_fetch(request, session_token, &session_data, exporter_shared_session_data_description);
                if ((ret == ZPATH_RESULT_NO_ERROR) && session_data) {
                    EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: sucessfully fetched PRA session data from ostore for session %s", request->name, session_token);
                    struct exporter_shared_session_data *sess_data = session_data->base_structure_void;

                    EXPORTER_DEBUG_SESSION_SHARING("PRA session data: session uuid %s user name %s Rule:%"PRId64" Console:%"PRId64" "
                            "Exporter:%"PRId64" Session start time:%"PRId64" portal %s domain %s protocol %s"
                            " customer gid %"PRId64" scope gid %"PRId64" console name %s", sess_data->sess_id,
                            sess_data->user_name, sess_data->policy_rule_id, sess_data->console_id, sess_data->exporter_id,
                            sess_data->session_start_time, sess_data->portal, sess_data->domain, sess_data->protocol,
                            sess_data->customer_gid, sess_data->scope_gid, sess_data->console_name);

                    if (strcmp(sess_data->portal, request->conn->sni) == 0) {

                        /* Validate the shared sessions before adding it to portal response */
                        ret = exporter_request_check_shared_session_capability_policy(request, sess_data);
                        if (ret == ZPN_RESULT_ASYNCHRONOUS) {
                            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: validation of shared session data"
                                                            " is in async state for the session %s", request->name, session_token);
                            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - validation of shared session data"
                                                            " is in async state for the session %s", request->name, api_name, session_token);
                            exporter_portal_defer_api_request_async(request);
                            exporter_fetched_data_free(session_data);
                            break;
                        } else if (ret != ZPN_RESULT_NO_ERROR) {
                            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: policy evaluation check failed for the session %s",
                                                            request->name, session_token);
                            exporter_fetched_data_free(session_data);
                            ret = ZPATH_RESULT_NO_ERROR;
                            continue;
                        }

                        api_link = EXPORTER_CALLOC(sizeof(struct exporterSraPortalApiSharedLinkIcon));
                        if (api_link == NULL) {
                            EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate portal api shared link icon entry for portal: %s", request->name, domain);
                            exporter_fetched_data_free(session_data);
                            ret = ZPATH_RESULT_NO_MEMORY;
                            break;
                        }
                        EXPORTER_PORTAL_API_STATS_INC(get_sra_shared_links_icons_entry_alloc, 1);

                        char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN] = {0};
                        snprintf(id_str, sizeof(id_str), "%"PRId64, sess_data->console_id);
                        api_link->console_id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);

                        api_link->session_start_time = sess_data->session_start_time;
                        api_link->scope_gid = sess_data->scope_gid;
                        api_link->sess_id = EXPORTER_PORTAL_SAFE_STRDUP(sess_data->sess_id);
                        api_link->protocol = EXPORTER_PORTAL_SAFE_STRDUP(sess_data->protocol);
                        api_link->domain = EXPORTER_PORTAL_SAFE_STRDUP(sess_data->domain);
                        api_link->user_name = EXPORTER_PORTAL_SAFE_STRDUP(sess_data->user_name);
                        api_link->console_name = EXPORTER_PORTAL_SAFE_STRDUP(sess_data->console_name);
                        /* Display scope name of shared sessions only for Global Portal */
                        if (is_global_portal) {
                            api_link->scope_name = EXPORTER_PORTAL_SAFE_STRDUP(exporter_request_get_shared_session_scope_name(request,
                                                                            sess_data));
                        }

                        request->shared_console_links_response->links[has_access_count] = api_link;
                        has_access_count++;
                    }
                    exporter_fetched_data_free(session_data);
                } else {
                    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: fetching user shared session data is in async state for the session %s", request->name, session_token);
                        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching user shared session data is in async state for the session %s", request->name, api_name, session_token);
                        exporter_portal_defer_api_request_async(request);
                    } else if (ret == ZPATH_RESULT_NOT_FOUND) {
                        EXPORTER_DEBUG_SESSION_SHARING("%s: %s - user shared session data for the session %s is not found", request->name, api_name, session_token);
                        ret = ZPATH_RESULT_NO_ERROR;
                        continue;
                    } else {
                        EXPORTER_LOG(AL_ERROR, "%s: %s - fetching user shared session data failed for the session %s", request->name, api_name, session_token);
                    }
                    break;
                }
            }
        }
        request->shared_sess_count = i;
        request->shared_has_count = has_access_count;

        if ((ret == ZPATH_RESULT_NO_ERROR) || (ret == ZPATH_RESULT_ERR) || (ret == ZPATH_RESULT_NO_MEMORY)) {
            request->shared_console_links_response->linksCount = request->shared_has_count;
            request->shared_sess_count = 0;
            request->shared_has_count = 0;
            exporter_fetched_data_free(request->user_sessions);
        }
    } else {
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: fetching user sessions entry is in async state for the user %s", request->name, user_name);
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching user sessions entry is in async state for the user %s", request->name, api_name, user_name);
            exporter_portal_defer_api_request_async(request);
        } else if (ret == ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_DEBUG_SESSION_SHARING("%s: %s - user sessions entry not found for the user %s", request->name, api_name, user_name);
            ret = ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: %s - fetching user sessions entry failed for the user %s", request->name, api_name, user_name);
        }
    }

    return ret;
}


/*
 * Process the PRA Get-Scope-Links-Icons API
 * ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS,
 * ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPE_LINK_ICONS
 * to produce scope-links response json
 */
static int exporter_sra_portal_process_get_scope_links_response(struct exporter_request *request,
                                                                exporter_scope_links_response_int_t **response)
{
    struct zpn_sra_console *mapped_consoles[EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    struct exporterSraPortalScopeApiLinkIcon *api_link = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);
    struct zpn_scope *db_scopes[1000];
    int index = 0;
    int async_event_count = 0;
    size_t scope_count = sizeof(db_scopes) / sizeof(db_scopes[0]);
    size_t mapped_console_count = 0;
    int ret = 0;
    int has_access_count = 0;
    int64_t approval_id = 0;
    enum zpe_access_approval_status approval_status;
    uint64_t hasAccess = 0;
    int async_reprocess = 0;

    exporter_scope_links_response_int_t *resp = EXPORTER_CALLOC(sizeof(exporter_scope_links_response_int_t));
    *response = resp;

    // Check if DTA is enabled for this customer, for the SCOPE_LINKS_ICONS API
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS) {
        int is_dta_enabled = zpn_get_delegated_admin_status(request->conn->exporter_domain->customer_gid);
        if (is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid) ||
            (!is_dta_enabled)) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s, PRA DTA or DTA is disabled for the customer", request->name, api_name, domain);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* Fetch zpn_sra_portal */
    ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - Error %s retrieving portal for domain %s", request->name, api_name, zpath_result_string(ret), domain);
        return ret;
    }

    if (!sra_portal->enabled) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s is disabled", request->name, api_name, domain);
        return ZPATH_RESULT_NOT_FOUND;
    }

    /* /scope_link_icons is only supported for default scope portal */
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS) {
        if (sra_portal->scope_gid != sra_portal->customer_gid) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s is not a default scope portal", request->name, api_name, domain);
            return ZPATH_RESULT_NOT_FOUND;
        }
    }

    /* Ensure scope is ready */
    ret = zpn_scope_ready_short_1(sra_portal->scope_gid,
                                  exporter_request_wally_callback,
                                  request,
                                  fohh_get_current_thread_id());
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s zpn default scope ready async, prefetching data for default scope... async_count: (%d->%d)",
                                       request->name, api_name, domain, request->async_count, request->async_count + 1);
        exporter_portal_defer_api_request_async(request);
        return ret;
    }

    /* Get all scopes configured for the customer */
    ret = zpn_scope_get_by_customer_gid_immediate(request->conn->exporter_domain->customer_gid,
                                                  db_scopes,
                                                  &scope_count);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s Error %s retrieving scopes for domain %s", request->name, api_name, zpath_result_string(ret), domain);
        return ret;
    }

    /* Number of scopes > 100 and bit 'is_large_portal_request' is not set (async request will have this bit already set)*/
    if ((scope_count > 100) && (!request->is_large_portal_request)) {
        int portal_access_count = 0;
        int64_t allowed_large_portal_access_count = get_allowed_large_portal_access_count();
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        portal_access_count = get_large_portal_access_active_count();
        if (portal_access_count >= allowed_large_portal_access_count) {
            exporter_guac_api_stats_increment(LARGE_PORTAL_ACCESS_BLOCKED_COUNT);
            ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s, portal_access_count %d is greater than max allowed",
                                                       request->name, api_name, domain, get_large_portal_access_active_count());
            return ZPATH_RESULT_WOULD_BLOCK;
        } else {
            exporter_guac_api_stats_increment(LARGE_PORTAL_ACCESS_ACTIVE_COUNT);
            ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s, portal_access_count %d",
                                                       request->name, api_name, domain, get_large_portal_access_active_count());
            request->is_large_portal_request = 1;
        }
    }

    if (scope_count > 0) {
        /* Sort scope by name */
        qsort(db_scopes, scope_count, sizeof(struct zpn_scope *), compare_sra_scope_names);
    }

    /* Iterate through all the scopes configured and check for data based on API
     */
    for (index = 0; index < scope_count; index++) {
        ret = 0;

        if (!db_scopes[index]) {
            EXPORTER_LOG(AL_ERROR, "%s: %s Invalid scope data for customer  %"PRId64, request->name, api_name, request->conn->exporter_domain->customer_gid);
            continue;
        }

        /* Scope is disabled, move to the next scope */
        if (!db_scopes[index]->enabled) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s scope %"PRId64" is disabled for customer %"PRId64,
                                            request->name, api_name, db_scopes[index]->gid, request->conn->exporter_domain->customer_gid);
            continue;
        }

        /*
         * Check if the user is Third-Party login to the scope
         */
        unsigned int is_ot_third_party_login = 0;
        ret = zpn_is_pra_third_party_login(request, db_scopes[index]->gid, &is_ot_third_party_login);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s Third-Party login eval for (%"PRId64") failed error : %s",
                                    request->name, api_name, domain, db_scopes[index]->gid, zpath_result_string(ret));
            return ret;
        }

        /*
         * If user is a Third-Party login to a scope, only proceed if Privileged Approvals is enabled
         * for the scope.
         */
        if (is_ot_third_party_login && !db_scopes[index]->privileged_approvals_enabled) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User %s is a Third-Party login to scope %"PRId64", but scope has privileged approval disabled",
                                            request->name, exporter_user_portal_request_state_get_name(request->portal_info), db_scopes[index]->gid);
            continue;
        }

        /*
         * For API_TYPE_APPROVAL_SCOPES, add the scope to response and continue
         */
        if (api_type == ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS) {
            if (db_scopes[index]->privileged_approvals_enabled) {
                api_link = exporter_sra_portal_alloc_scope_link_icon_entry(db_scopes[index]);
                if (api_link == NULL) {
                    EXPORTER_LOG(AL_ERROR, "%s: %s Unable to allocate scope api link icon entry for portal: %s", request->name, api_name, domain);
                    return ZPATH_RESULT_NO_MEMORY;
                }
                resp->links[resp->linksCount++] = api_link;
            }
            continue;
        }

        int i = 0;
        mapped_console_count = 0;
        /* Get SRA consoles mapped to portal for the Scope */
        ret = exporter_sra_portal_get_ordered_consoles_by_portal_gid(sra_portal->gid, request->conn->exporter_domain->customer_gid, db_scopes[index]->gid, mapped_consoles, &mapped_console_count, EXPORTER_PRA_PORTAL_CFG_MAX_LINKS_PER_PORTAL);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s - fetching consoles failed for %"PRId64"[%"PRId64"] error : %s",
                                           request->name, api_name, domain, request->conn->exporter_domain->customer_gid,
                                           db_scopes[index]->gid, zpath_result_string(ret));
            return ret;
        }

        if (mapped_console_count == 0) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s no mapped consoles for %"PRId64"[%"PRId64"] result : %s",
                                           request->name, api_name, domain, request->conn->exporter_domain->customer_gid,
                                           db_scopes[index]->gid, zpath_result_string(ret));
            continue;
        } else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s %zu mapped consoles for %"PRId64"[%"PRId64"] result : %s",
                                            request->name, api_name, domain, mapped_console_count, request->conn->exporter_domain->customer_gid,
                                            db_scopes[index]->gid, zpath_result_string(ret));
        }

        /*
         * Portal has consoles mapped to the scope. Load the scope policy to proceed with
         * policy evaluations.
         */
        ret = zpn_scope_ready_short_1(db_scopes[index]->gid,
                                      exporter_request_wally_callback,
                                      request,
                                      fohh_get_current_thread_id());
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s zpn scope ready async (%"PRId64"), prefetching data for scope, continue... async_count: (%d->%d)",
                                           request->name, api_name, domain, db_scopes[index]->gid, request->async_count, request->async_count + 1);
            async_reprocess = 1;
            exporter_portal_defer_api_request_async(request);
            /* Preempt process after 100 async events */
            async_event_count++;
            zthread_heartbeat(NULL);
            if (async_event_count >= 100) return ret;
            continue;
        } else if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s zpn scope ready (%"PRId64") failed error : %s",
                                    request->name, api_name, domain, db_scopes[index]->gid, zpath_result_string(ret));
            return ret;
        }
        /*
         * Check if the user has access to any one of the mapped consoles for the
         * portal in the scope.
         * If user has access to atleast, add the scope detail to response,
         * break the loop and move on to evaluate the next scope.
         */
        for (i = 0; i < mapped_console_count; i++) {
            approval_id = 0;
            approval_status = zpe_access_approval_none;
            hasAccess = 0;

            ret = exporter_sra_portal_check_approval_access_for_sra_console(request,
                                                                            mapped_consoles[i],
                                                                            db_scopes[index]->gid,
                                                                            &hasAccess,
                                                                            &approval_id,
                                                                            &approval_status);
            if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - fetching customer scope link icon: %s async, continue", request->name, api_name, mapped_consoles[i]->name);
                exporter_portal_defer_api_request_async(request);
                return ret;
            } else if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_WARNING, "%s: %s - fetching user access failed for %"PRId64" in scope %"PRId64" failed, domain: %s, error : %s",
                                          request->name, api_name, mapped_consoles[i]->gid,
                                          db_scopes[index]->gid, domain, zpath_result_string(ret));
                return ret;
            } else {
                    /*
                     * If the user is Third-Party login to the scope, then there should be
                     * a approval for the user(active, future, expired).
                     */
                if ((is_ot_third_party_login && approval_id) ||
                    (!is_ot_third_party_login && (hasAccess || (approval_status != zpe_access_approval_none)))) {

                    api_link = exporter_sra_portal_alloc_scope_link_icon_entry(db_scopes[index]);
                    if (api_link == NULL) {
                        EXPORTER_LOG(AL_ERROR, "%s: %s Unable to allocate scope api link icon entry for portal: %s", request->name, api_name, domain);
                        return ZPATH_RESULT_NO_MEMORY;
                    }
                    resp->links[has_access_count] = api_link;
                    has_access_count++;
                    resp->linksCount = has_access_count;

                    EXPORTER_DEBUG_USER_PORTAL_API("%s: User: %s has access to scope : %s for portal domain: %s",
                            request->name, exporter_user_portal_request_state_get_name(request->portal_info), api_link->name, domain);
                    /* We know there is one console user has access for in this scope, break */
                    break;
                }
            }
        }

        /* We have iterated through all consoles mapped in the scope to the portal, no consoles with access found */
        if (i == mapped_console_count) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User: %s does not have access to scope : %"PRId64" for portal domain: %s",
                                            request->name, exporter_user_portal_request_state_get_name(request->portal_info),
                                            db_scopes[index]->gid, domain);
        }
    }

    /* Complete one iteration of max scopes */
    if (async_reprocess && (scope_count && index == scope_count)) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Policy load for all scopes async trigger complete", request->name);
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    return ret;
}

/*
 * Process the Get-ZApp-Links API to produce links response json
 */
static int exporter_portal_process_get_zapp_links_response(struct exporter_request *request,
                                                           exporter_user_portal_api_zapp_links_response_int_t **response)
{
    struct zpn_user_portal_zapp_links *ordered_links[EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL];
    struct exporterUserPortalApiZappLink *api_link;
    struct zpn_user_portal *user_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    size_t ordered_count = 0;
    int ret;
    size_t i;

    exporter_user_portal_api_zapp_links_response_int_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_zapp_links_response_int_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                    &user_portal, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal->enabled) {
            ret = exporter_portal_get_ordered_zapp_links_by_customer_gid(user_portal->customer_gid, ordered_links, &ordered_count, EXPORTER_USER_PORTAL_CFG_MAX_LINKS_PER_PORTAL);
            EXPORTER_LOG(AL_NOTICE, "%s[UNIP_PORTAL_LOG] [%s:%s:%d] User Portal:%s, ZS managed?%s has %ld zapps linked.",
                          request->name, domain, api_name, ret, user_portal->domain,
                          request->conn->exporter_domain->is_managed_ba?"Yes":"No",
                          ordered_count);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                for (i = 0; i < ordered_count; i++) {
                    char id_str[EXPORTER_USER_PORTAL_ID_STR_LEN];

                    api_link = EXPORTER_CALLOC(sizeof(struct exporterUserPortalApiZappLink));
                    if (api_link == NULL) {
                        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate portal api link entry for portal: %s", request->name, domain);
                        return ZPATH_RESULT_NO_MEMORY;
                    }

                    snprintf(id_str, sizeof(id_str), "%"PRId64, ordered_links[i]->gid);

                    api_link->id = EXPORTER_PORTAL_SAFE_STRDUP(id_str);
                    api_link->osType = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->os_type);
                    api_link->linkUrl = EXPORTER_PORTAL_SAFE_STRDUP(ordered_links[i]->link);

                    /* Update into response */
                    resp->links[i] = api_link;
                }
                resp->linksCount = ordered_count;
            }
        }  else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}


/*
 * Process the Get-Company-Logo API to return logo
 */
static int exporter_portal_process_get_company_logo(struct exporter_request *request,
                                                    int64_t *id,
                                                    char *image_data,
                                                    int image_data_len,
                                                    char *name_buf,
                                                    int name_buf_len,
                                                    char *favicon_image_data,
                                                    size_t favicon_image_data_len)
{
    struct zpath_customer_logo *logo = NULL;
    struct zpn_user_portal *user_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int logo_len;
    int name_len;
    size_t favicon_len = 0;
    int ret;

    ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                    &user_portal, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal->enabled) {
            ret = zpath_customer_logo_get(request->conn->exporter_domain->customer_gid, &logo, exporter_request_wally_callback, request, 0);
            if (ret == ZPATH_RESULT_NO_ERROR) {

                logo_len = EXPORTER_PORTAL_SAFE_STRLEN(logo->img_data);
                name_len = EXPORTER_PORTAL_SAFE_STRLEN(logo->img_name);
                favicon_len = EXPORTER_PORTAL_SAFE_STRLEN(logo->favicon_img_data);

                if (image_data_len <= logo_len) {
                    EXPORTER_LOG(AL_ERROR, "%s: %s - Response buffer too small - domain: %s, image len: %d, image buffer len: %d", request->name, api_name, domain, logo_len, image_data_len);
                    return ZPATH_RESULT_ERR_TOO_LARGE;
                } else {
                    assert(logo->img_data); // Weird diagnostics without: ‘%s’ directive argument is null [-Werror=format-truncation=]
                    snprintf(image_data, image_data_len, "%s", logo->img_data);
                }

                if (name_buf_len <= name_len) {
                    EXPORTER_LOG(AL_ERROR, "%s: %s - Image name buffer too small - domain: %s, image name len: %d, image name buffer len: %d", request->name, api_name, domain, name_len, name_buf_len);
                    return ZPATH_RESULT_ERR_TOO_LARGE;
                } else {
                    snprintf(name_buf, name_buf_len, "%s", logo->img_name);
                }

                if (favicon_image_data_len <= favicon_len) {
                    EXPORTER_LOG(AL_ERROR, "%s: %s - Response buffer too small - domain: %s, favicon image len: %lu, favicon image buffer len: %lu",
                    request->name, api_name, domain, favicon_len, favicon_image_data_len);
                    return ZPATH_RESULT_ERR_TOO_LARGE;
                } else if (favicon_len > 0) {
                    snprintf(favicon_image_data, favicon_image_data_len, "%s", logo->favicon_img_data);
                }

                *id = logo->gid;
            } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching customer logo async for domain: %s", request->name, api_name, domain);
                exporter_portal_defer_api_request_async(request);
            } else {
                EXPORTER_LOG(AL_ERROR, "%s: %s error: %s fetching customer logo for domain: %s", request->name, api_name, zpath_result_string(ret), domain);
            }
        }  else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}

/*
 * Process the Get-Portal-Logo API to return logo
 */
static int exporter_portal_process_get_portal_logo(struct exporter_request *request,
                                                   int64_t *id,
                                                   char *image_data,
                                                   int image_data_len)
{
    struct zpn_user_portal *user_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int icon_len;
    int ret;

    ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                    &user_portal, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal->enabled) {
            icon_len = EXPORTER_PORTAL_SAFE_STRLEN(user_portal->icon);
            if (image_data_len <= icon_len) {
                EXPORTER_LOG(AL_ERROR, "%s: %s - Response buffer too small - domain: %s, portal icon len: %d, portal icon buffer len: %d", request->name, api_name, domain, icon_len, image_data_len);
                return ZPATH_RESULT_ERR_TOO_LARGE;
            } else {
                *id = user_portal->gid;
                snprintf(image_data, image_data_len, "%s", user_portal->icon);
            }
        } else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: User portal %s - domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}

/*
 * Process the Get-Link-Logo API to return link logo
 */
static int exporter_portal_process_get_link_logo(struct exporter_request *request,
                                                 int64_t *id,
                                                 char *image_data,
                                                 int image_data_len)
{
    struct zpn_user_portal *user_portal = NULL;
    struct zpn_user_portal_links *link = NULL;
    int64_t link_id = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int icon_len;
    int ret;

    *id = -1; /* Invalid id */
    ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid, request->scope_gid, domain,
                                                    &user_portal, NULL, NULL, 0);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (user_portal->enabled) {
            ret = exporter_user_portal_get_link_id(request, &link_id);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                if (link_id > 0) {
                    /* There will be at most one link, as gid is unique */
                    ret = zpn_user_portal_links_table_get_by_gid(link_id, &link, NULL, NULL, 0);
                    if (ret == ZPATH_RESULT_NO_ERROR) {
                        icon_len = EXPORTER_PORTAL_SAFE_STRLEN(link->icon);
                        if (image_data_len <= icon_len) {
                            EXPORTER_LOG(AL_ERROR, "%s: %s - Response buffer too small - domain: %s, link icon len: %d, link icon buffer len: %d", request->name, api_name, domain, icon_len, image_data_len);
                            return ZPATH_RESULT_ERR_TOO_LARGE;
                        } else {
                            snprintf(image_data, image_data_len, "%s", link->icon);
                            *id = link_id;
                        }
                    }
                }
            }
        } else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - User portal domain: %s is disabled", request->name, api_name, domain);
            ret = ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ret;
}

/*
 * Clear object store login state for user
 */
static int exporter_portal_clear_object_store_login_state(struct exporter_request *request)
{
    int ret;
    char cookie[EXPORTER_URL_MAX_ENCODE_SIZE] = "";
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *domain_part = NULL;
    const char *session_key_with_domain;
    const char *walk;
    char session_key[EXPORTER_URL_MAX_ENCODE_SIZE] = "";
    struct argo_object *session = NULL;
    char assertion_key_query_value[EXPORTER_URL_MAX_ENCODE_SIZE] = "";

    /* Get cookie key */
    ret = exporter_request_find_cookie(request, EXPORTER_COOKIE_DOMAIN, 1, cookie, sizeof(cookie));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Get session cookie for this domain, if it's there. */
        ret = object_store_get(request->name,
                               cookie,
                               &session_key_with_domain,
                               exporter_request_async_callback,
                               request,
                               0,
                               ostore_role_object_store);

        if (ret) {
            if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s - fetching session key async for domain: %s", request->name, api_name, domain);
                exporter_portal_defer_api_request_async(request);
                return ret;
            } else if (ret == ZPATH_RESULT_NOT_FOUND) {
                session_key_with_domain = NULL;
            } else {
                if (object_store_ready_count(ostore_role_object_store)) {
                    /* There is an object store */
                    EXPORTER_LOG(AL_ERROR, "%s: Object store read failed. Restarting auth to try a better store", request->name);
                    /* Fall through to re-auth... */
                } else {
                    /* No object stores available. We're pretty much done here */
                    EXPORTER_LOG(AL_ERROR, "%s: No object stores available", request->name);
                    return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_OBJECT_STORES_UNAVAILABLE);
                }
            }
        } else {
            /*
             * Extract the session_key_with_domain into session_key and domain
             */
            for (walk = session_key_with_domain; *walk; walk++) {
                if ((*walk) == ',') domain_part = walk + 1;
            }
            if (!domain_part) {
                EXPORTER_LOG(AL_WARNING, "%s: Bad object: Has no domain part, reauthing", request->name);
                /* Fall through to re-auth... */
            } else {
                /* Extract just the beginning of the session key- the part without the domain */
                snprintf(session_key, sizeof(session_key), "%.*s", (int)((domain_part - 1) - session_key_with_domain), session_key_with_domain);
            }
        }

        /* Delete the cookie object */
        EXPORTER_DEBUG_AUTH("%s: %s - Deleting key %.*s", request->name, api_name, EXPORTER_DEBUG_BYTES, session_key);

        ret = exporter_session_get(request->name, session_key, &session, exporter_request_async_callback, request, 0);
        if (ret) {
            if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Looking up session %.*s, async_count(%d->%d)", request->name,
                                    EXPORTER_DEBUG_BYTES, session_key, request->async_count, request->async_count + 1);
                exporter_portal_defer_api_request_async(request);
                return ret;
            } else if (ret == ZPATH_RESULT_NOT_FOUND) {
                session = NULL;
                EXPORTER_DEBUG_AUTH("%s: Fetching session object- not found", request->name);
            } else {
                /* Other error */
                return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_SESSION);
            }
        } else {

            /* Session found, get assertion_key */
            const char *assertion_key = NULL;
            ret = exporter_session_get_auth_assertion_key(session, request->conn->exporter_domain->customer_gid, &assertion_key);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                exporter_url_decode(assertion_key_query_value);
                if (strcmp(assertion_key, assertion_key_query_value) == 0) {
                    /*
                     * Check for duplicate SP key and log it
                     */
                    EXPORTER_LOG(AL_WARNING, "%s: Got duplicate key from SP: %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                }
                EXPORTER_DEBUG_AUTH("%s: Deleting current assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                ret = object_store_delete(request->name, assertion_key, NULL, NULL, 0, ostore_role_object_store);
                if (ret && (ret != ZPATH_RESULT_ASYNCHRONOUS)) {
                    EXPORTER_LOG(AL_ERROR, "%s: Could not delete assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                } else {
                    EXPORTER_LOG(AL_NOTICE, "%s: Delete assertion key %.*s, ret=%s", request->name, EXPORTER_DEBUG_BYTES, assertion_key, zpath_result_string(ret));
                    ret = ZPATH_RESULT_NO_ERROR;
                }
            }

            /* Free session object copy */
            argo_object_release(session);
            session = NULL;
        }
    } else {
        EXPORTER_DEBUG_AUTH("%s: %s - Cannot find session key %.*s - ret: %s", request->name, api_name, EXPORTER_DEBUG_BYTES, cookie, zpath_result_string(ret));
    }

    return ret;
}

/*
 * Process the User-Logout API to produce response json
 */
static int exporter_portal_process_user_logout_response(struct exporter_request *request,
                                                            exporter_user_portal_api_user_logout_response_t **response)
{
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum exporter_portal_user_auth_state auth_state;
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *auth_state_str = NULL;
    int ret;
    const char *assertion_key = NULL;
    exporter_user_portal_api_user_logout_response_t *resp = EXPORTER_CALLOC(sizeof(exporter_user_portal_api_user_logout_response_t));
    *response = resp;

    if (resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s - unable to allocate %s response for portal: %s", request->name, api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    /* Achieve a logoff by clearing the object store login state, this will force user to re-auth */
    assertion_key = exporter_user_portal_request_state_get_assertion_key(request);
    if (assertion_key) {
        ret = object_store_delete(request->name, assertion_key, NULL, NULL, 0, ostore_role_object_store);
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Object store login state will be cleared asynchronously", request->name);
            ret = ZPATH_RESULT_NO_ERROR;
        }
    } else {
        ret = exporter_portal_clear_object_store_login_state(request);
    }

    if (ret == ZPATH_RESULT_NO_ERROR) {
        resp->nameId = EXPORTER_PORTAL_SAFE_STRDUP(exporter_user_portal_request_state_get_name(request->portal_info));
        resp->domain = EXPORTER_PORTAL_SAFE_STRDUP(domain);
        auth_state = EXPORTER_PORTAL_USER_AUTH_STATE_LOGGED_OUT;
    } else {
        auth_state = EXPORTER_PORTAL_USER_AUTH_STATE_UNKNOWN;
        EXPORTER_LOG(AL_ERROR, "%s: %s response, domain: %s ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    }

    auth_state_str = get_portal_user_auth_state_name(auth_state);
    resp->authState = EXPORTER_PORTAL_SAFE_STRDUP(auth_state_str);

    return ret;
}

/*
 * Free API response
*/
void exporter_portal_api_free_response(const struct exporter_request *request, void *resp)
{
    const enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);

    if (resp != NULL) {

        EXPORTER_LOG(AL_NOTICE, "%s - Free %s response, resp-ptr: %p", request->name, api_name, resp);

        switch (api_type) {

        case ZPA_USER_PORTAL_API_TYPE_USER:
            {
                exporter_user_portal_api_user_response_t *user = resp;

                EXPORTER_FREE(user->id);
                EXPORTER_FREE(user->nameId);
                EXPORTER_FREE(user->authState);
                EXPORTER_FREE(user->domain);
                EXPORTER_FREE(user->uDid);
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_COMPANY:
            {
                exporter_user_portal_api_company_response_t *company = resp;

                EXPORTER_FREE(company->id);
                EXPORTER_FREE(company->name);
                EXPORTER_FREE(company->description);
                EXPORTER_FREE(company->domain);
                EXPORTER_FREE(company->logoImageName);
                EXPORTER_FREE(company->logoImageData);
                if (company->favIconImageData) EXPORTER_FREE(company->favIconImageData);

                /* Free additional domains */
                if (company->otherDomainsCount) {
                    int i;
                    int domain_count = company->otherDomainsCount;
                    for (i = 0; i < domain_count; i++) {
                        EXPORTER_FREE(company->otherDomains[i]);
                    }
                    EXPORTER_FREE(company->otherDomains);
                }
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_PORTAL:
            {
                exporter_user_portal_api_portal_response_t *portal = resp;

                EXPORTER_FREE(portal->id);
                EXPORTER_FREE(portal->name);
                EXPORTER_FREE(portal->description);
                EXPORTER_FREE(portal->domain);
                EXPORTER_FREE(portal->userNotification);
                EXPORTER_FREE(portal->logoImageName);
                EXPORTER_FREE(portal->logoImageData);
                if (portal->user_portal_host) {
                    EXPORTER_FREE(portal->user_portal_host);
                    portal->user_portal_host = NULL;
                }
                if (portal->scopeName) {
                    EXPORTER_FREE(portal->scopeName);
                    EXPORTER_FREE(portal->scopeId);
                }
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_AUP:
            {
                exporter_user_portal_api_aup_response_t *aup = resp;

                EXPORTER_FREE(aup->name);
                EXPORTER_FREE(aup->description);
                EXPORTER_FREE(aup->aupMessage);
                EXPORTER_FREE(aup->email);
                EXPORTER_FREE(aup->phone);
                EXPORTER_FREE(aup->displayFrequency);
                EXPORTER_FREE(aup->displayInterval);
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_LINKS:
            {
                exporter_user_portal_api_links_response_int_t *links_resp = resp;

                int i;
                for (i = 0; i < links_resp->linksCount; i++) {
                    struct exporterUserPortalApiLink *link = links_resp->links[i];
                    exporter_portal_free_link_entry(link);
                }
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_LINKS_ICONS:
            {
                exporter_user_portal_api_links_icons_response_int_t *link_icons_resp = resp;

                int i;
                for (i = 0; i < link_icons_resp->linksCount; i++) {
                    struct exporterUserPortalApiLinkIcon *link = link_icons_resp->links[i];
                    exporter_portal_free_link_icon_entry(link);
                }
            }
            break;

        case ZPA_SRA_PORTAL_API_TYPE_CONSOLE_LINKS_ICONS:
            {
                exporter_console_links_response_int_t *link_icons_resp = resp;

                int i;
                for (i = 0; i < link_icons_resp->linksCount; i++) {
                    struct exporterSraPortalApiLinkIcon *link = link_icons_resp->links[i];
                    exporter_sra_portal_free_link_icon_entry(link);
                }
            }
            break;

        case ZPA_SRA_PORTAL_API_TYPE_SHARED_CONSOLE_LINKS_ICONS:
            {
                exporter_shared_console_links_response_int_t *link_icons_resp = resp;

                int i;
                for (i = 0; i < link_icons_resp->linksCount; i++) {
                    struct exporterSraPortalApiSharedLinkIcon *link = link_icons_resp->links[i];
                    exporter_sra_portal_free_shared_link_icon_entry(link);
                }
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_ZAPP_LINKS:
            {
                exporter_user_portal_api_zapp_links_response_int_t *links = resp;

                int i;
                for (i = 0; i < links->linksCount; i++) {
                    struct exporterUserPortalApiZappLink *link = links->links[i];
                    {
                        EXPORTER_FREE(link->id);
                        EXPORTER_FREE(link->osType);
                        EXPORTER_FREE(link->linkUrl);
                    }
                    EXPORTER_FREE(link);
                }
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_COMPANY_LOGO:
        case ZPA_USER_PORTAL_API_TYPE_PORTAL_LOGO:
        case ZPA_USER_PORTAL_API_TYPE_LINK_ICON:
            {
                exporter_user_portal_api_image_response_t *image = resp;

                EXPORTER_FREE(image->id);
                EXPORTER_FREE(image->imageData);
                EXPORTER_FREE(image->imageName);
                EXPORTER_FREE(image->faviconImageData);
            }
            break;

        case ZPA_USER_PORTAL_API_TYPE_LOGOUT:
            {
                exporter_user_portal_api_user_logout_response_t *logout = resp;

                EXPORTER_FREE(logout->id);
                EXPORTER_FREE(logout->nameId);
                EXPORTER_FREE(logout->authState);
                EXPORTER_FREE(logout->domain);
            }
            break;
        case ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO:
            {
                exporter_sra_portal_console_info_response_t *response = resp;
                EXPORTER_FREE(response->sra_console_name);
                EXPORTER_FREE(response->sra_console_type);
                if (response->customer_gid) {
                    EXPORTER_FREE(response->customer_gid);
                }
                if (response->scope_id) {
                    EXPORTER_FREE(response->scope_id);
                }
            }
            break;

        case ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS:
        case ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS:
            {
                exporter_scope_links_response_int_t *link_icons_resp = resp;

                int i;
                for (i = 0; i < link_icons_resp->linksCount; i++) {
                    struct exporterSraPortalScopeApiLinkIcon *link = link_icons_resp->links[i];
                    exporter_sra_portal_free_scope_link_icon_entry(link);
                }
            }
            break;

        case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES:
            {
                exporter_console_links_response_int_t *link_icons_resp = resp;

                int i;
                for (i = 0; i < link_icons_resp->linksCount; i++) {
                    struct exporterSraPortalApiLinkIcon *link = link_icons_resp->links[i];
                    exporter_sra_portal_free_link_icon_entry(link);
                }
                if (link_icons_resp->requestable_app_ids) {
                    zhash_table_free(link_icons_resp->requestable_app_ids);
                    link_icons_resp->requestable_app_ids = NULL;
                }
            }
            break;

        default:
            EXPORTER_LOG(AL_ERROR, "%s- Request to free unexpected API response type: %s", request->name, api_name);
            break;
        }

        /* Finally free the response structure itself */
        EXPORTER_FREE(resp);
    } else {
        EXPORTER_LOG(AL_NOTICE, "%s - %s - Null response", request->name, api_name);
    }
}

/* ----------------------------------------------------------*
 * Portal API Request URI Handlers                           *
 * ----------------------------------------------------------*/

/*
 * Handle User API
 */
static int exporter_portal_handle_user_api(struct exporter_request *request)
{
    exporter_user_portal_api_user_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    ret = exporter_portal_process_get_user_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - get response domain: %s, ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_user_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    }

    exporter_portal_api_free_response(request, response);

    if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "User-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_USER_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_USER_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_user, 1, ret);

    return ret;
}

/*
 * Handle Company API
 */
static int exporter_portal_handle_company_api(struct exporter_request *request)
{
    exporter_user_portal_api_company_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;
    JSON_Value *otherDomainsValue = NULL;
    JSON_Array *otherDomains = NULL;
    int i;
    int ret;
    int json_err = 1;

    ret = exporter_portal_process_get_company_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s response - domain: %s, ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        otherDomainsValue = json_value_init_array();
        if (!otherDomainsValue) {
            goto erk;
        }
        otherDomains = json_value_get_array(otherDomainsValue);
        if (!otherDomains) {
            goto erk;
        }

        /* Add additional domains if any into array */
        for (i = 0; i < response->otherDomainsCount; i++) {
            if (response->otherDomains[i]) {
                if (json_array_append_string(otherDomains, response->otherDomains[i]) != JSONSuccess) goto erk;
            }
        }

        /* Add top level fields */
        if (response->id && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.id", response->id) != JSONSuccess) goto erk;
        if (response->name && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.name", response->name) != JSONSuccess) goto erk;
        if (response->description && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.description", response->description) != JSONSuccess) goto erk;
        if (response->domain && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.domain", response->domain) != JSONSuccess) goto erk;
        if (response->hasLogo && json_object_dotset_number(rootObject, "exporterUserPortalApiCompanyResponse.hasLogo", response->hasLogo) != JSONSuccess) goto erk;
        if (response->logoImageName && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.logoImageName", response->logoImageName) != JSONSuccess) goto erk;
        if (response->logoImageData && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.logoImageData", response->logoImageData) != JSONSuccess) goto erk;
        if (json_object_dotset_value(rootObject, "exporterUserPortalApiCompanyResponse.otherDomains", otherDomainsValue) != JSONSuccess) goto erk;
        if (response->hasFavIcon && json_object_dotset_number(rootObject, "exporterUserPortalApiCompanyResponse.hasFavIcon", response->hasFavIcon) != JSONSuccess) goto erk;
        if (response->favIconImageData && json_object_dotset_string(rootObject, "exporterUserPortalApiCompanyResponse.favIconImageData", response->favIconImageData) != JSONSuccess) goto erk;

        /* Set success, json is valid */
        ret = ZPATH_RESULT_NO_ERROR;
        json_err = 0;

        if (ret == ARGO_RESULT_NO_ERROR) {
            if (json_serialize_to_buffer_pretty(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) goto erk;
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS)  {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s response for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else {
        snprintf(error_reason, sizeof(error_reason), "Company-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_COMPANY_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_COMPANY_FAIL,
                                           error_reason, domain, exporter_user_portal_request_state_get_name(request->portal_info));
    }

erk:

    if (rootValue) json_value_free(rootValue);

    if ((ret == ZPATH_RESULT_BAD_DATA) && json_err) {
        snprintf(error_reason, sizeof(error_reason), "Company-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_COMPANY_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_COMPANY_FAIL,
                                           error_reason, domain, exporter_user_portal_request_state_get_name(request->portal_info));
    }

    exporter_portal_api_free_response(request, response);

    /* Count if not deferred async */
    if (request->async_count == 0) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_company, 1, ret);
    }

    return ret;
}

/*
 * Handle Portal API
 */
static int exporter_portal_handle_portal_api(struct exporter_request *request)
{

    exporter_user_portal_api_portal_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    ret = exporter_portal_process_get_portal_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - domain: %s, ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_portal_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else {

        if (ret != ZPATH_RESULT_NO_ERROR) {
            snprintf(error_reason, sizeof(error_reason), "Portal-Response Error - %s", zpath_result_string(ret));
            exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_PORTAL_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_PORTAL_FAIL,
                                               error_reason, domain,
                                               exporter_user_portal_request_state_get_name(request->portal_info));
        }
    }

    exporter_portal_api_free_response(request, response);

    /* Count if not deferred async */
    if (request->async_count == 0) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_portal, 1, ret);
    }

    return ret;
}

/*
 * Handle AUP API
 */
static int exporter_portal_handle_aup_api(struct exporter_request *request)
{

    exporter_user_portal_api_aup_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    ret = exporter_portal_process_get_aup_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - domain: %s, get-user-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_aup_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }
    }

    exporter_portal_api_free_response(request, response);

    if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "AUP-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_PORTAL_AUP_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_AUP_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_aup, 1, ret);

    return ret;
}

/*
 * Handle links API
 */
static int exporter_portal_handle_links_api(struct exporter_request *request)
{
    exporter_user_portal_api_links_response_int_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;

    ret = exporter_portal_process_get_links_response(request, &response);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s -  domain: %s, Get-Links-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }

        /* Add links into response json */
        for (i = 0; i < response->linksCount; i++) {

            struct exporterUserPortalApiLink *link = (struct exporterUserPortalApiLink *)response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->id && json_object_dotset_string(object, "id", link->id) != JSONSuccess) goto erk;
            if (link->name && json_object_dotset_string(object, "name", link->name) != JSONSuccess) goto erk;
            if (link->description && json_object_dotset_string(object, "description", link->description) != JSONSuccess) goto erk;
            if (link->protocol && json_object_dotset_string(object, "protocol", link->protocol) != JSONSuccess) goto erk;
            if (link->linkUrl && json_object_dotset_string(object, "linkUrl", link->linkUrl) != JSONSuccess) goto erk;
            if (link->linkPath && json_object_dotset_string(object, "linkPath", link->linkPath) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasAccess", link->hasAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasZappAccess", link->hasZappAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasBrowserAccess", link->hasBrowserAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasIcon", link->hasIcon) != JSONSuccess) goto erk;

            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        if (json_object_dotset_value(rootObject, "exporterUserPortalApiLinksResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterUserPortalApiLinksResponse.linksCount", response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is valid */
        ret = ZPATH_RESULT_NO_ERROR;
    }
erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (json_serialize_to_buffer_pretty(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) goto erk;
        ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    exporter_portal_api_free_response(request, response);

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Links-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_LINKS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_LINKS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links, 1, ret);

    return ret;
}

/*
 * Handle links icons API
 */
static int exporter_portal_handle_links_icons_api(struct exporter_request *request)
{
    exporter_user_portal_api_links_icons_response_int_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;

    ret = exporter_portal_process_get_links_icons_response(request, &response);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s -  domain: %s, get-links-icons-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }

        /* Add links icons into response json */
        for (i = 0; i < response->linksCount; i++) {

            struct exporterUserPortalApiLinkIcon *link = (struct exporterUserPortalApiLinkIcon *)response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->id && json_object_dotset_string(object, "id", link->id) != JSONSuccess) goto erk;
            if (link->name && json_object_dotset_string(object, "name", link->name) != JSONSuccess) goto erk;
            if (link->description && json_object_dotset_string(object, "description", link->description) != JSONSuccess) goto erk;
            if (link->protocol && json_object_dotset_string(object, "protocol", link->protocol) != JSONSuccess) goto erk;
            if (link->linkUrl && json_object_dotset_string(object, "linkUrl", link->linkUrl) != JSONSuccess) goto erk;
            if (link->linkPath && json_object_dotset_string(object, "linkPath", link->linkPath) != JSONSuccess) goto erk;
            if (link->imageName && json_object_dotset_string(object, "imageName", link->imageName) != JSONSuccess) goto erk;
            if (link->imageData && json_object_dotset_string(object, "imageData", link->imageData) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasAccess", link->hasAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasZappAccess", link->hasZappAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasBrowserAccess", link->hasBrowserAccess) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasIcon", link->hasIcon) != JSONSuccess) goto erk;
            if (json_object_dotset_number(object, "applicationSegmentId", link->application_gid) != JSONSuccess) goto erk;
            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        if (json_object_dotset_value(rootObject, "exporterUserPortalApiLinksIconsResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterUserPortalApiLinksIconsResponse.linksCount", response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is created */
        ret = ZPATH_RESULT_NO_ERROR;
    }

erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (json_serialize_to_buffer_pretty(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) goto erk;
        ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    exporter_portal_api_free_response(request, response);

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Links-Icons-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_LINKS_ICONS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ret);

    return ret;
}

/*
 * Handle SRA links icons API
 */
static int exporter_sra_portal_handle_console_links_icons_api(struct exporter_request *request)
{
    exporter_console_links_response_int_t *response = NULL;
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;

    if (is_pra_disabled(request->conn->exporter_domain->customer_gid)) {
        EXPORTER_LOG(AL_WARNING, "%s: PRA is disabled", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_PRA_DISABLED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        ret = ZPATH_RESULT_ERR;
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ret);
        return ret;
    }

    ret = exporter_sra_portal_process_get_console_links_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: PRA portal %s -  domain: %s, get-links-icons-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: PRA portal %s -  domain: %s, Links Count: %d", request->name, api_name, domain, response->linksCount);
        /* Add links icons into response json */
        for (i = 0; i < response->linksCount; i++) {

            struct exporterSraPortalApiLinkIcon *link = (struct exporterSraPortalApiLinkIcon *)response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->id && json_object_dotset_string(object, "id", link->id) != JSONSuccess) goto erk;
            if (link->name && json_object_dotset_string(object, "name", link->name) != JSONSuccess) goto erk;
            if (link->protocol && json_object_dotset_string(object, "protocol", link->protocol) != JSONSuccess) goto erk;
            if (link->domain && json_object_dotset_string(object, "domain", link->domain) != JSONSuccess) goto erk;
            if (link->connectionSecurity && json_object_dotset_string(object, "connectionSecurity", link->connectionSecurity) != JSONSuccess) goto erk;
            if (link->scopeId && json_object_dotset_string(object, "scopeId", link->scopeId) != JSONSuccess) goto erk;
            if (link->imageName && json_object_dotset_string(object, "imageName", link->imageName) != JSONSuccess) goto erk;
            if (json_object_dotset_number(object, "startTime", link->approval_start_time) != JSONSuccess) goto erk;
            if (json_object_dotset_number(object, "endTime", link->approval_end_time) != JSONSuccess) goto erk;
            if (link->approval_working_hours && json_object_dotset_string(object, "approval_working_hours", link->approval_working_hours) != JSONSuccess) goto erk;
            if (link->approval_status && json_object_dotset_number(object, "approval_status", link->approval_status) != JSONSuccess) goto erk;
            if (json_object_dotset_boolean(object, "hasAccess", link->hasAccess) != JSONSuccess) goto erk;

            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        /*
         * The URL path is /console_links_icons, but the top-level key of the JSON response is "exporterConsoleLinksResponse".
         * It's not symmetrical.
         */
        if (json_object_dotset_value(rootObject, "exporterConsoleLinksResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterConsoleLinksResponse.linksCount", response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is created */
        ret = ZPATH_RESULT_NO_ERROR;
    }

erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        size_t needed_size_in_bytes = json_serialization_size_pretty(rootValue);
        if (needed_size_in_bytes == 0) {
            ret = ZPATH_RESULT_ERR;
            EXPORTER_LOG(AL_ERROR, "%s: PRA portal %s - domain: %s, Json size calculation ZPATH_RESULT_ERR", request->name, api_name, domain);
            goto erk;
        }
        char *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
        if (response_buf == NULL) {
            ret = ZPATH_RESULT_NO_MEMORY;
            EXPORTER_LOG(AL_ERROR, "%s: PRA portal %s - domain: %s, Json needed size(%zu)  ZPATH_RESULT_NO_MEMORY", request->name, api_name, domain, needed_size_in_bytes);
            goto erk;
        }
        int written = -1;
        written = json_serialize_to_buffer_r(rootValue, response_buf, 0, 1, NULL);
        if (written >= 0) {
            ret = exporter_request_respond_with_json_dynamic_buffer(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: PRA portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: JSON serialization failure", request->name);
            ret = ZPATH_RESULT_ERR;
        }
        EXPORTER_FREE(response_buf);
    }

    exporter_portal_api_free_response(request, response);

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        if (ret == ZPATH_RESULT_WOULD_BLOCK) {
            snprintf(error_reason, sizeof(error_reason), "Links-Icons-Response Error - Try again later");
        } else {
            snprintf(error_reason, sizeof(error_reason), "Links-Icons-Response Error - %s", zpath_result_string(ret));
        }
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_SRA_LINKS_ICONS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ret);

    return ret;
}

/* Called with session string where the $ is still in there */
int
resolve_console_id_from_remote_guac_session_string(struct exporter_request *request,
						   char *session_string)
{
  int ret = ZPATH_RESULT_NO_ERROR;

  if (!session_string) {
      return ZPATH_RESULT_ERR;
  }
  //Skip past the $ as the first order of business
  session_string += 1;

  if (request->guac_proctored_session_id) {
      EXPORTER_FREE(request->guac_proctored_session_id);
      request->guac_proctored_session_id = NULL;
  }
  request->guac_proctored_session_id = EXPORTER_STRDUP(session_string, strlen(session_string));
  struct argo_object *session_data = NULL;
  char object_store_key[EXPORTER_PRA_SESS_OS_KEY_MAX_SIZE] = {0};

  /* this is a proxy-exporter case where user is joining a PRA session entry */
  if (!request->share_ctxt.infer_key) {
      char *infer_key = NULL;
      int64_t customer_gid = request->conn->exporter_domain->customer_gid;
      exporter_guac_sess_get_infer_key(customer_gid, &infer_key);
      request->share_ctxt.infer_key = infer_key;
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC allocated infer_key %p, request %p", request->share_ctxt.infer_key, request);
  }

  EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Perform session-store lookup, request %s, session %s", request->name, request->guac_proctored_session_id);
  snprintf(object_store_key, EXPORTER_PRA_SESS_OS_KEY_MAX_SIZE, "privilegedSession|%"PRId64"|%s", request->conn->exporter_domain->customer_gid, session_string);
  ret = exporter_pra_sess_data_fetch(request, object_store_key, &session_data, exporter_shared_session_data_description);
  if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
      EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Waiting for data from session-store, request %s, session %s", request->name, request->guac_proctored_session_id);
      EXPORTER_FREE(request->guac_proctored_session_id);
      request->guac_proctored_session_id = NULL;
      return ret;
  } else if (ret) {
      EXPORTER_LOG(AL_ERROR, "SESS_PROC: Session-store lookup failed. request %s, session %s", request->name, request->guac_proctored_session_id);
      EXPORTER_FREE(request->guac_proctored_session_id);
      request->guac_proctored_session_id = NULL;
      return ret;
  } else {
      if (session_data) {
          //Populates remote_exporter_id and pra_console_id
          struct exporter_shared_session_data *sess_data = session_data->base_structure_void;
          EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: exporter id %"PRId64"", request->name, sess_data->exporter_id);
          EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Session-store lookup is successful. request %s, session %s", request->name, request->guac_proctored_session_id);
          EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %s, get FQDN of remote exporter %"PRId64"", request->name, request->remote_exporter_id);
          request->remote_exporter_id = sess_data->exporter_id;
          ret = exporter_guac_sess_sharing_get_exporter_name(request->remote_exporter_id, request->remote_exporter_domain);
          request->is_proxy_conn = 1;
          request->pra_console_id = sess_data->console_id;
          argo_object_release(session_data);
          session_data = NULL;
          if (ret != ZPATH_RESULT_NO_ERROR) {
              EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: failed to resolve remote exporter. Exporter ID %"PRId64"", request->remote_exporter_id);
              exporter_sharing_stats_inc(proctor_exporter_communication_fail_type);
              exporter_fetched_data_free(session_data);
              return ret;
          }
          EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Resolved exporter ID = %"PRId64" to %s", request->remote_exporter_id,
                       request->remote_exporter_domain);
          exporter_fetched_data_free(session_data);
      }
  }
  return ret;
}

int
extract_sessionid_from_request(struct exporter_request *request, char *console_id_string, int console_id_len)
{
  const char *zconsole_string = "zconsole";

  int ret = query_string_find(request->url, &(request->url_parser), zconsole_string, console_id_string, console_id_len);
  EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("SESS_PROC %s: SRA portal: %s", request->name, console_id_string);

  if (ret == ZPATH_RESULT_NO_ERROR) {
      EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("SESS_PROC %s: SRA portal: %s", request->name, console_id_string);
  } else {
      EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: SRA portal: Failed to get console ID %s", request->name, request->url);
      return ZPATH_RESULT_ERR;
  }
  return ret;
}

int
resolve_console_id_from_console_string(struct exporter_request *request, char *console_id_string,
				       int64_t *console_id)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    *console_id = 0;
    if (console_id_string[0] == '$') {
        //Resolve the zconsole id
        ret = resolve_console_id_from_remote_guac_session_string(request, console_id_string);
        if (ret != ZPATH_RESULT_NO_ERROR) {
	        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: returning ret %d resolve_console_id_from_console_string",ret);
            return ret;
        }
        *console_id = request->pra_console_id;
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Found connect id = %s %s fetch", console_id_string, request->guac_proctored_session_id);
    } else {
        *console_id = atol(console_id_string);
	    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Got console id %"PRId64" for console-string %s", *console_id, console_id_string);
        return ZPATH_RESULT_NO_ERROR;
    }
    return ZPATH_RESULT_NO_ERROR;
}


static int
extract_sessionid_and_consoleid_from_request(struct exporter_request *request,
					                         int64_t *console_id)
{
    char session_id_string[64] = {0};
    int ret = ZPATH_RESULT_NO_ERROR;

    *console_id = 0;
    ret = extract_sessionid_from_request(request, session_id_string, sizeof(session_id_string));
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: SRA portal: Failed to get session ID from %s", request->name, request->url);
        return ret;
    }
    ret = resolve_console_id_from_console_string(request, session_id_string, console_id);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: extract_sessionid_and_consoleid_from_request returned ret %d",ret);
    return ret;
}

int exporter_cleanup_shared_console_links_icons_send_error(struct exporter_request *request, JSON_Value *rootValue, char *error_reason) {
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    exporter_portal_api_free_response(request, request->shared_console_links_response);

    if (rootValue) json_value_free(rootValue);

    exporter_portal_api_error_response(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_API_SRA_SHARED_LINKS_ICONS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                        error_reason, domain,
                                        exporter_user_portal_request_state_get_name(request->portal_info));

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ZPATH_RESULT_ERR);

    return ZPATH_RESULT_ERR;
}

/*
 * Handle SRA shared links icons API
 */
static int exporter_sra_portal_handle_shared_console_links_icons_api(struct exporter_request *request)
{
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    if (!g_exporter_ot_mode || is_pra_disabled(customer_gid)) {
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_PRA_DISABLED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        ret = ZPATH_RESULT_ERR;
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ret);
        return ret;
    }

    ret = exporter_sra_portal_process_get_shared_console_links_response(request, customer_gid);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s -  domain: %s, get-links-icons-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }

        /* Add links icons into response json */
        for (i = 0; i < request->shared_console_links_response->linksCount; i++) {

            struct exporterSraPortalApiSharedLinkIcon *link = (struct exporterSraPortalApiSharedLinkIcon *)request->shared_console_links_response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->console_id && json_object_dotset_string(object, "console_id", link->console_id) != JSONSuccess) goto erk;
            if (link->console_name && json_object_dotset_string(object, "console_name", link->console_name) != JSONSuccess) goto erk;
            if (link->sess_id && json_object_dotset_string(object, "sess_id", link->sess_id) != JSONSuccess) goto erk;
            if (link->protocol && json_object_dotset_string(object, "protocol", link->protocol) != JSONSuccess) goto erk;
            if (link->domain && json_object_dotset_string(object, "domain", link->domain) != JSONSuccess) goto erk;
            if (link->user_name && json_object_dotset_string(object, "user_name", link->user_name) != JSONSuccess) goto erk;
            if (json_object_dotset_number(object, "session_start_time", link->session_start_time) != JSONSuccess) goto erk;
            if (json_object_dotset_number(object, "scope_gid", link->scope_gid) != JSONSuccess) goto erk;
            if (link->scope_name && json_object_dotset_string(object, "scope_name", link->scope_name) != JSONSuccess) goto erk;

            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        /*
         * The URL path is /console_links_icons, but the top-level key of the JSON response is "exporterConsoleLinksResponse".
         * It's not symmetrical.
         */
        if (json_object_dotset_value(rootObject, "exporterSharedConsoleLinksResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterSharedConsoleLinksResponse.linksCount", request->shared_console_links_response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is created */
        ret = ZPATH_RESULT_NO_ERROR;
    }

erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        size_t needed_size_in_bytes = json_serialization_size_pretty(rootValue);
        if (needed_size_in_bytes == 0) {
            snprintf(error_reason, sizeof(error_reason), "JSON serialization error");
            return exporter_cleanup_shared_console_links_icons_send_error(request, rootValue, error_reason);
        }
        char *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
        if (response_buf == NULL) {
            EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s:  User portal %s - domain: %s, Failed to allocate memory for response buf", request->name, api_name, domain);
            ret = ZPATH_RESULT_NO_MEMORY;
        } else {
            int written = -1;
            written = json_serialize_to_buffer_r(rootValue, response_buf, 0, 1, NULL);
            if (written < 0) {
                EXPORTER_FREE(response_buf);
                snprintf(error_reason, sizeof(error_reason), "JSON serialization error");
                return exporter_cleanup_shared_console_links_icons_send_error(request, rootValue, error_reason);
            }
            ret = exporter_request_respond_with_json_dynamic_buffer(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
            EXPORTER_FREE(response_buf);
        }
    }

    if (ret != ZPATH_RESULT_ASYNCHRONOUS) {
        exporter_portal_api_free_response(request, request->shared_console_links_response);
    }

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Shared-Links-Icons-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_SRA_SHARED_LINKS_ICONS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_links_icons, 1, ret);
    return ret;
}

/*
 * Handle SRA scope links icons API
 * ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS
 * ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPE_LINK_ICONS
 */
static int exporter_sra_portal_handle_scope_links_icons_api(struct exporter_request *request)
{
    exporter_scope_links_response_int_t *response = NULL;
    char *response_buf = NULL;
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;


    if (is_pra_disabled(request->conn->exporter_domain->customer_gid)) {
        EXPORTER_LOG(AL_WARNING, "%s: PRA is disabled", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_PRA_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_SCOPE_ICONS_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        ret = ZPATH_RESULT_ERR;
        goto stat;
    }

    ret = exporter_sra_portal_process_get_scope_links_response(request, &response);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: SRA portal %s -  domain: %s, get-scope-links-icons-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }

        /* Add links icons into response json */
        for (i = 0; i < response->linksCount; i++) {

            struct exporterSraPortalScopeApiLinkIcon *link = (struct exporterSraPortalScopeApiLinkIcon*)response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->id && json_object_dotset_string(object, "id", link->id) != JSONSuccess) goto erk;
            if (link->name && json_object_dotset_string(object, "name", link->name) != JSONSuccess) goto erk;

            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        /*
         * The URL path is /scope_links_icons or /approval_scoes but the top-level key of the JSON response is
         * "exporterScopeLinksResponse".
         */
        if (json_object_dotset_value(rootObject, "exporterScopeLinksResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterScopeLinksResponse.linksCount", response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is created */
        ret = ZPATH_RESULT_NO_ERROR;
    }

erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        size_t response_buf_size = EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE;
        response_buf = EXPORTER_CALLOC(response_buf_size);
        if (!response_buf) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, failed to alloc memory for json resp",
                                    request->name, api_name, domain);
            ret = ZPATH_RESULT_NO_MEMORY;
            goto erk;
        }

        if (json_serialize_to_buffer_pretty(rootValue, response_buf, response_buf_size) == JSONSuccess) {
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, json-serialization failed", request->name, api_name, domain);
            ret = ZPATH_RESULT_ERR;
        }
        EXPORTER_FREE(response_buf);
    }

    exporter_portal_api_free_response(request, response);

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        if (ret == ZPATH_RESULT_WOULD_BLOCK) {
            snprintf(error_reason, sizeof(error_reason), "Scope-Links-Icons-Response Error - Try again later");
        } else {
            snprintf(error_reason, sizeof(error_reason), "Scope-Links-Icons-Response Error - %s", zpath_result_string(ret));
        }
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_SRA_SCOPE_LINKS_ICONS_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_SCOPE_ICONS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

stat:
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_sra_scope_links, 1, ret);
    } else if (api_type == ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_sra_approval_scopes, 1, ret);
    }

    return ret;
}

static int exporter_sra_portal_process_get_console_info_response(struct exporter_request *request,
                                                                 exporter_sra_portal_console_info_response_t **response, char *error_reason)
{
    struct zpn_sra_application *sra_application = NULL;
    struct zpn_sra_console *sra_console = NULL;
    exporter_sra_portal_console_info_response_t *resp = NULL;
    int64_t sra_console_id = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int ret;
    enum zpe_access_action matched_action = zpe_access_action_deny;
    int64_t matched_rule_id = 0;
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int is_djb = 0;

    ret = extract_sessionid_and_consoleid_from_request(request, &sra_console_id);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: extract_sessionid_and_consoleid_from_request returned async");
	    exporter_portal_defer_api_request_async(request);
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: SRA portal: Failed to get console ID %s", request->name, request->url);
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to get console ID %s", request->url);
        return ret;
    }

    resp = EXPORTER_CALLOC(sizeof(exporter_sra_portal_console_info_response_t));
    *response = resp;

    if(resp == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to allocate %s response for domain: %s", request->name, api_name, domain);
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Unable to allocate %s response for domain: %s", api_name, domain);
        return ZPATH_RESULT_NO_MEMORY;
    }

    is_djb = check_query_param_djb(request);

    if (!is_djb) {
        ret = zpn_sra_console_get_by_id_immediate(sra_console_id, request->conn->exporter_domain->customer_gid, request->scope_gid, &sra_console);
        if (ret != ZPATH_RESULT_NO_ERROR || !(sra_console->enabled)) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_console %"PRId64": %s", request->name, sra_console_id, zpn_result_string(ret));
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "could not retrieve sra_console %"PRId64"", sra_console_id);
            return ret;
        }

        ret = zpn_sra_application_get_by_id_immediate(sra_console->sra_app_id, &sra_application);
        if (ret != ZPATH_RESULT_NO_ERROR ||  !(sra_application->enabled)) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: could not retrieve sra_application %"PRId64": %s", request->name, sra_console->sra_app_id, zpn_result_string(ret));
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "could not retrieve sra_application %"PRId64"", sra_console->sra_app_id);
            return ret;
        }
        resp->sra_console_name = EXPORTER_PORTAL_SAFE_STRDUP(sra_console->name);
        resp->sra_console_type = EXPORTER_PORTAL_SAFE_STRDUP(sra_application->protocol);
        if (is_vnc_disabled(request->conn->exporter_domain->customer_gid) &&
            !strcmp(sra_application->protocol, "VNC")) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: VNC feature flag for sra_application %"PRId64" is disabled",
                    request->name, sra_console->sra_app_id);
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "VNC feature flag for sra_application %"PRId64" is disabled", sra_console->sra_app_id);
            return ZPATH_RESULT_ERR;
        }

        if (is_realvnc_disabled(request->conn->exporter_domain->customer_gid) &&
            !strcmp(sra_application->protocol, "REALVNC")) {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: REALVNC feature flag for sra_application %"PRId64" is disabled",
                    request->name, sra_console->sra_app_id);
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "REALVNC feature flag for sra_application %"PRId64" is disabled", sra_console->sra_app_id);
            return ZPATH_RESULT_ERR;
        }

    } else {
        /* there is no console associated to the DJB */
        resp->sra_console_name = EXPORTER_PORTAL_SAFE_STRDUP(DJB_CONSOLE_NAME);
        resp->sra_console_type = EXPORTER_PORTAL_SAFE_STRDUP(DJB_CONSOLE_WIN_PROTOCOL);
        EXPORTER_DEBUG_USER_PORTAL_API("%s: Console Info call for DJB ID: %"PRId64, request->name, sra_console_id);
    }

    resp->fileTransferEnabled = !is_pra_ft_disabled(request->conn->exporter_domain->customer_gid);
    resp->fasterFileTransferEnabled = !is_pra_faster_ft_disabled(request->conn->exporter_domain->customer_gid);

    if (request->is_proxy_conn) {
        EXPORTER_LOG(AL_INFO,"SESS_PROC this is a session monitoring request, setting interactive_auth_disabled to true");
        resp->interactive_auth_disabled = 1;
    } else if (is_djb) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: this is a DJB Privileged Desktops request, setting interactive_auth_disabled to true", request->name);
        resp->interactive_auth_disabled = 1;
    } else if (!is_privileged_policy_disabled(request->conn->exporter_domain->customer_gid) && g_exporter_ot_mode) {
        ret = zpn_broker_policy_evaluate(request->scope_gid,
                sra_console_id,
                zpe_policy_type_cred_map,
                request->policy_state->general_state_hash,
                request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                &matched_action,
                &matched_rule_id);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            if (matched_rule_id && matched_action == zpe_access_action_inject_credentials) {
                EXPORTER_DEBUG_USER_PORTAL_API("%s: CRED Map policy rule %"PRId64" is matched for console id %"PRId64"", request->name, matched_rule_id, sra_console_id);
                resp->interactive_auth_disabled = 1;
            }
        } else {
            EXPORTER_DEBUG_USER_PORTAL_API("%s: No matching rule is found in CRED Map policy for console id %"PRId64"", request->name, sra_console_id);
            exporter_increment_credentials_policy_reject_stats();
        }
    }
    resp->clipboardEnabled = !is_pra_clipboard_disabled(request->conn->exporter_domain->customer_gid);

    resp->emailNotificationEnabled = -1;
    if (!is_pra_session_monitoring_disabled(request->conn->exporter_domain->customer_gid)) {
        if (!request->share_ctxt.infer_key) {
            ret = exporter_guac_sess_get_infer_key(request->conn->exporter_domain->customer_gid, &resp->infer_key);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                request->share_ctxt.infer_key = resp->infer_key;
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC console_info api call allocated infer_key %p, request %p", request->share_ctxt.infer_key, request);
            } else {
                snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to get infer key, request %p", request);
                return ZPATH_RESULT_ERR;
            }
        } else {
            resp->infer_key = request->share_ctxt.infer_key;
        }

        resp->emailNotificationEnabled = !is_pra_email_notification_disabled_for_customer(request->conn->exporter_domain->customer_gid);
    }

    resp->customer_gid = EXPORTER_CALLOC(EXPORTER_CUSTOMER_GID_LEN);
    snprintf(resp->customer_gid, EXPORTER_CUSTOMER_GID_LEN, "%"PRId64"", request->conn->exporter_domain->customer_gid);

    resp->scope_id = EXPORTER_CALLOC(EXPORTER_CUSTOMER_GID_LEN);
    if (is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid)) {
        resp->scope_id[0] = '0';
    } else {
        if(request->scope_gid == request->conn->exporter_domain->customer_gid) {
            resp->scope_id[0] = '0';
        } else {
            snprintf(resp->scope_id, EXPORTER_CUSTOMER_GID_LEN, "%"PRId64"", request->scope_gid);
        }
    }

    resp->user_id = exporter_user_portal_request_state_get_name(request->portal_info);
    resp->privileged_file_system_enabled = !is_pra_advanced_file_transfer_disabled(request->conn->exporter_domain->customer_gid);
    resp->display_quality_enabled = !is_pra_display_quality_disabled(request->conn->exporter_domain->customer_gid);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Handle SRA console info API
 */
static int exporter_sra_portal_handle_console_info_api(struct exporter_request *request)
{
    exporter_sra_portal_console_info_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = {'\0'};
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int ret;

    ret = exporter_sra_portal_process_get_console_info_response(request, &response, error_reason);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: PRA portal %s -  domain: %s, get-console-info-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = ZPATH_RESULT_BAD_DATA;
        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        if (!response || !response->sra_console_name || json_object_dotset_string(rootObject, "sra_console_name", response->sra_console_name) != JSONSuccess || json_object_dotset_string(rootObject, "sra_console_type", response->sra_console_type) != JSONSuccess) goto erk;

        if (json_object_dotset_boolean(rootObject, "fileTransferEnabled", response->fileTransferEnabled) != JSONSuccess) goto erk;

        if (json_object_dotset_boolean(rootObject, "fasterFileTransferEnabled", response->fasterFileTransferEnabled) != JSONSuccess) goto erk;

        if (!response || !response->sra_console_name || json_object_dotset_string(rootObject, "sra_console_name", response->sra_console_name) != JSONSuccess ||
            json_object_dotset_string(rootObject, "sra_console_type", response->sra_console_type) != JSONSuccess ||
            json_object_dotset_boolean(rootObject, "interactive_auth_disabled", response->interactive_auth_disabled)) goto erk;

        if (json_object_dotset_boolean(rootObject, "clipboardEnabled", response->clipboardEnabled) != JSONSuccess) goto erk;

        if (response->infer_key) {
            if (json_object_dotset_string(rootObject, "infer_key", response->infer_key) != JSONSuccess) goto erk;
        }

        if (response->customer_gid) {
            if (json_object_dotset_string(rootObject, "customer_gid", response->customer_gid) != JSONSuccess) goto erk;
        }
        if (response->scope_id) {
            if (json_object_dotset_string(rootObject, "scope_id", response->scope_id) != JSONSuccess) goto erk;
        }

        if (response->emailNotificationEnabled != -1) {
            if (json_object_dotset_boolean(rootObject, "emailNotificationEnabled", response->emailNotificationEnabled) != JSONSuccess) goto erk;
        }
        if (response->user_id) {
            if (json_object_dotset_string(rootObject, "user_id", response->user_id) != JSONSuccess) goto erk;
        }
        if (json_object_dotset_boolean(rootObject, "privileged_file_system_enabled", response->privileged_file_system_enabled)) goto erk;
        if (json_object_dotset_boolean(rootObject, "display_quality_enabled", response->display_quality_enabled)) goto erk;

        /* Set success, json is created */
        ret = ZPATH_RESULT_NO_ERROR;
    }
erk:
    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (json_serialize_to_buffer_pretty(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) goto erk;
        ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: SRA portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    exporter_portal_api_free_response(request, response);

    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        if (error_reason[0] == '\0') {
            snprintf(error_reason, sizeof(error_reason), "Console-Info-Response Error - %s", zpath_result_string(ret));
        }
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_SRA_PORTAL_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_CONSOLE_INFO_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }
    if (rootValue) json_value_free(rootValue);
    return ret;
}


/*
 * Handle Zapp links API
 */
static int exporter_portal_handle_zapp_links_api(struct exporter_request *request)
{
    exporter_user_portal_api_zapp_links_response_int_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    JSON_Value *linksValue = NULL;
    JSON_Array *links = NULL;
    JSON_Value *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    int i;
    int ret;

    ret = exporter_portal_process_get_zapp_links_response(request, &response);

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s -  domain: %s, Get-Zapp-Links-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {

        /* Set error while we build the json, if json is correctly created, error will be cleared */
        ret = ZPATH_RESULT_BAD_DATA;

        rootValue = json_value_init_object();
        if (!rootValue) goto erk;

        rootObject = json_value_get_object(rootValue);
        if (!rootObject) goto erk;

        linksValue = json_value_init_array();
        if (!linksValue) {
            goto erk;
        }
        links = json_value_get_array(linksValue);
        if (!links) {
            goto erk;
        }

        /* Add links into response json */
        for (i = 0; i < response->linksCount; i++) {

            struct exporterUserPortalApiZappLink *link = (struct exporterUserPortalApiZappLink *)response->links[i];

            JSON_Value *linkValue = NULL;
            JSON_Object *object = NULL;
            linkValue = json_value_init_object();
            if (!linkValue) goto erk;

            object = json_value_get_object(linkValue);
            if (!object) goto erk;

            if (link->id && json_object_dotset_string(object, "id", link->id) != JSONSuccess) goto erk;
            if (link->osType && json_object_dotset_string(object, "osType", link->osType) != JSONSuccess) goto erk;
            if (link->linkUrl && json_object_dotset_string(object, "linkUrl", link->linkUrl) != JSONSuccess) goto erk;

            if (json_array_append_value(links, linkValue) != JSONSuccess) goto erk;
        }

        if (json_object_dotset_value(rootObject, "exporterUserPortalApiZappLinksResponse.links", linksValue) != JSONSuccess) goto erk;
        if (json_object_dotset_number(rootObject, "exporterUserPortalApiZappLinksResponse.linksCount", response->linksCount) != JSONSuccess) goto erk;

        /* Set success, json is valid */
        ret = ZPATH_RESULT_NO_ERROR;
    }

erk:

    if (ret == ZPATH_RESULT_NO_ERROR) {
        if (json_serialize_to_buffer_pretty(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) goto erk;
        ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    exporter_portal_api_free_response(request, response);

    if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Zapp-Links-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_ZAPP_LINKS_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_ZAPP_LINKS_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    if (rootValue) json_value_free(rootValue);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_zapp_links, 1, ret);

    return ret;
}

/*
 * Handle Company logo API
 */
static int exporter_portal_handle_company_logo_api(struct exporter_request *request)
{
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char image_name[EXPORTER_USER_PORTAL_NAME_STR_LEN] = "";
    char image_data[EXPORTER_USER_PORTAL_IMAGE_STR_LEN] = "";
    char favicon_image_data[EXPORTER_USER_PORTAL_FAVICON_IMAGE_STR_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    int64_t id = 0;
    int ret;

    ret = exporter_portal_process_get_company_logo(request, &id,
            image_data, sizeof(image_data),
            image_name, sizeof(image_name),
            favicon_image_data, sizeof(favicon_image_data));
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - domain: %s, ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = exporter_portal_api_image_response(request, id, image_name, image_data, favicon_image_data);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, image_name: %s, respond-with-image-ret: %s", request->name, api_name, domain, image_name, zpath_result_string(ret));
        }
    }  else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else {
        snprintf(error_reason, sizeof(error_reason), "Company-Icon-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_COMPANY_ICON_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_COMPANY_LOGO_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    /* Count if not deferred async */
    if (request->async_count == 0) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_company_logo, 1, ret);
    }

    return ret;
}


/*
 * Handle Portal logo API
 */
static int exporter_portal_handle_portal_logo_api(struct exporter_request *request)
{
    char image_data[EXPORTER_USER_PORTAL_IMAGE_STR_LEN] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int64_t id = 0;
    int ret;

    ret = exporter_portal_process_get_portal_logo(request, &id, image_data, sizeof(image_data));
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - domain: %s, get-portal-logo-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = exporter_portal_api_image_response(request, id, "portal/logo", image_data, NULL);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-image-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Portal-Icon-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_PORTAL_ICON_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_PORTAL_LOGO_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_portal_logo, 1, ret);

    return ret;
}

/*
 * Handle link icon API
 */
static int exporter_portal_handle_link_icon_api(struct exporter_request *request)
{
    char image_data[EXPORTER_USER_PORTAL_IMAGE_STR_LEN] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int64_t id;
    char id_str[EXPORTER_USER_PORTAL_IMAGE_STR_LEN];
    int ret;

    ret = exporter_portal_process_get_link_logo(request, &id, image_data, sizeof(image_data));
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal %s - domain: %s, get-link-logo-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        snprintf(id_str, sizeof(id_str), "link/%"PRId64"/icon", id);
        ret = exporter_portal_api_image_response(request, id, id_str, image_data, NULL);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: User portal %s - domain: %s, respond-with-image-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
        }
    }

    if (ret != ZPATH_RESULT_NO_ERROR) {
        snprintf(error_reason, sizeof(error_reason), "Link ID %"PRId64" Icon-Response Error - %s", id, zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_ICON_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_PORTAL_LOGO_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(get_link_icon, 1, ret);

    return ret;
}

/*
 * Handle Logout-User API
 */
static int exporter_portal_handle_user_logout_api(struct exporter_request *request)
{
    exporter_user_portal_api_user_logout_response_t *response = NULL;
    char response_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";
    char error_reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    int ret;

    ret = exporter_portal_process_user_logout_response(request, &response);
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal - %s: domain: %s, ret: %s", request->name, api_name, domain, zpath_result_string(ret));
    if (ret == ZPATH_RESULT_NO_ERROR) {
        ret = argo_structure_dump(exporter_user_portal_api_user_logout_resp_description, response, response_buf, sizeof(response_buf), NULL, 1);
        if (ret == ARGO_RESULT_NO_ERROR) {
            EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: User portal - %s: domain: %s, ret: %s, JSON: \n%s\n", request->name, api_name, domain, zpath_result_string(ret), response_buf);
            ret = exporter_request_respond_with_json(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, response_buf, 1);
            if (ret != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: User portal - %s: domain: %s, respond-with-json-ret: %s", request->name, api_name, domain, zpath_result_string(ret));
            }
        }
    } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s response for domain: %s will be processed async", request->name, api_name, domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else {
        snprintf(error_reason, sizeof(error_reason), "User-Logout-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_API_USER_LOGOUT_RESPONSE_FAILED, EXPORTER_PORTAL_API_ERROR_USER_LOGOUT_FAIL,
                                           error_reason, domain, exporter_user_portal_request_state_get_name(request->portal_info));
    }

    exporter_portal_api_free_response(request, response);

    /* Count if not deferred async */
    if (request->async_count == 0) {
        EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(logout_user, 1, ret);
    }

    return ret;
}

/*
 * Validate PRA-service proxy requests
 */
int exporter_sra_portal_pra_service_request_validate(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);

    /* Policy evaluation, will be async */
    ret = exporter_privileged_portal_policy_evaluate(request,
                                                     &request->portal_policy_rule_id,
                                                     &request->portal_policy_capabilities_bitmap);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s async portal policy eval (%d->%d)",
                                       request->name, api_name, domain, request->async_count, request->async_count + 1);
        exporter_portal_defer_api_request_async(request);
        return ret;
    } else if ((ret != ZPATH_RESULT_NO_ERROR) || (!request->portal_policy_rule_id)) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, portal policy evaluation failed ret : %s",
                                request->name, api_name, domain, zpath_result_string(ret));
        return ret;
    }

    if ((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DELETE_FILE) &&
            !(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_DELETE_FILE)) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, portal policy does not allow this",
                                request->name, api_name, domain);
        return ZPATH_RESULT_ERR;
    }

    if (((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_VIEW_UNINSPECTED) ||
            (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DOWNLOAD_FILE_ADMIN)) &&
            !(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_ACCESS_UNINSPECTED_FILE)) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, portal policy does not allow this",
                                request->name, api_name, domain);
        return ZPATH_RESULT_ERR;
    }

    if ((api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES) &&
            !(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REQUEST_APPROVALS)) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, portal policy does not allow this",
                                request->name, api_name, domain);
        return ZPATH_RESULT_ERR;
    }

    // PRIVILEGED_APPROVAL_REQUESTS can be called for enduser (My Requests) or admin-user (My Approvals).
    // Either REQUEST_APPROVALS or REVIEW_APPROVALS capability should be set.
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_GET_PRIVILEGED_APPROVAL_REQUESTS) {
        if (!(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REQUEST_APPROVALS) &&
            !(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REVIEW_APPROVALS)) {
            EXPORTER_LOG(AL_ERROR, "%s: %s %s - domain: %s, portal policy does not allow this",
                                       request->name, api_name, http_method_names[request->req_method], domain);
            return ZPATH_RESULT_ERR;
        }
    }

    // PRIVILEGED_APPROVAL_REQUEST allows methods POST(CreateRequest), PATCH(ReviewRequest) and DELETE(DeleteRequest).
    // Create/Delete Requests require REQUEST_APPROVALS capability
    // ReviewRequest requires REVIEW_APPROVALS capability
    if (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST) {
        enum http_method req_method = request->req_method;
        if (!(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REQUEST_APPROVALS)) {
            if ((req_method == HTTP_POST) || (req_method == HTTP_DELETE)) {
                EXPORTER_LOG(AL_ERROR, "%s: %s %s - domain: %s, portal policy does not allow this",
                                       request->name, api_name, http_method_names[request->req_method], domain);
                return ZPATH_RESULT_ERR;
            }
        }
        if (!(request->portal_policy_capabilities_bitmap & PRIV_PORTAL_REVIEW_APPROVALS)) {
            if (req_method == HTTP_PATCH) {
                EXPORTER_LOG(AL_ERROR, "%s: %s %s - domain: %s, portal policy does not allow this",
                                       request->name, api_name, http_method_names[request->req_method], domain);
                return ZPATH_RESULT_ERR;
            }
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int requestable_app_ids_walk_f(void *cookie, void *object, void *key, size_t key_len)
{
    struct exporter_request *request = cookie;

    if (!request->generated_request_data) {
        request->generated_request_data = evbuffer_new();
    }

    if (evbuffer_get_length(request->generated_request_data) == 0) {
        if (request->url_parser.field_set & (1 << UF_QUERY)) {
            evbuffer_add_printf(request->generated_request_data, "&appSegmentIds=%"PRId64"", *((int64_t *)key));
        } else {
            evbuffer_add_printf(request->generated_request_data, "?appSegmentIds=%"PRId64"", *((int64_t *)key));
        }
    } else {
        evbuffer_add_printf(request->generated_request_data, ",%"PRId64"", *((int64_t *)key));
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Handle PRA-service proxy requests
 */
int exporter_sra_portal_http_pra_service_proxy_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);
    char *out_buf = NULL;

    ret = exporter_sra_portal_pra_service_request_validate(request);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s async portal policy eval",
                                       request->name, api_name, domain);
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: portal - %s, domain: %s pra service validation failed res=%s",
                request->name, api_name, domain, zpath_result_string(ret));
        return ret;
    }

    /*
     * Multipart requests are POST requests with body. Do not progress until
     * request is in complete state.
     */
    if (((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_ABORT)) &&
        (!request->http_request_complete)) {
        EXPORTER_DEBUG_USER_PORTAL_API("%s: %s - domain: %s request is not complete, "
                "reprocess at complete_cb", request->name, api_name, domain);
        return ZPATH_RESULT_NO_ERROR;
    }

    if ((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE)) {
        ret = exporter_multipart_upload_request_append_fields(request);
        if (ret) {
            EXPORTER_LOG(AL_ERROR, "%s: portal - %s, domain: %s failed to prepare request body res=%s",
                                    request->name, api_name, domain, zpath_result_string(ret));
            return ret;
        }
    }

    if (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES) {
    	// Extract optional scopeId and set it in the request
    	char query_value[1024];
    	char *endptr;
        ret = extract_query_value_from_request(request, query_value, sizeof(query_value), "scopeId");
        if (ret == ZPATH_RESULT_NO_ERROR) {
            uint64_t scope_gid = strtoll(query_value, &endptr, 0);
            if (scope_gid != 0) {
                request->scope_gid = scope_gid;
            }
        }

        exporter_console_links_response_int_t *response = NULL;
        ret = exporter_sra_portal_process_get_console_links_response(request, &response);
        if (ret == ZPATH_RESULT_NO_ERROR && response && response->requestable_app_ids) {
            int64_t count = zhash_table_get_size(response->requestable_app_ids);
            int64_t key = 0;
            if (count) {
                /* ENRICH the proxied request with the list of the matched app segments */
                zhash_table_walk(response->requestable_app_ids, &key,
                        requestable_app_ids_walk_f, request);
            } else {
                EXPORTER_LOG(AL_ERROR,
                        "%s: requestable app segment gid list is empty",
                        request->name);
            }
            exporter_portal_api_free_response(request, response);
        } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            exporter_portal_api_free_response(request, response);
            return ret;
        } else {
            EXPORTER_LOG(AL_ERROR,
                    "%s: error in calculating requestable app segment gid list",
                    request->name);
            exporter_portal_api_free_response(request, response);
            return ret;
        }
     }

    if (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST &&
            request->req_method == HTTP_POST ) {
        // Add policyId in requestBody for createApprovalsRequest
        if (!request->request_body) {
             EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, Privileged Approval POST request with no request_body. dropping",
                 request->name, api_name, domain);
             return ZPATH_RESULT_BAD_DATA;
        }

        // Get JSON root object for request_body
        size_t datalen = evbuffer_get_length(request->request_body);
        unsigned char *data = evbuffer_pullup(request->request_body, datalen);
        if (data == NULL) {
             EXPORTER_LOG(AL_ERROR, "%s, request failed pullup request_body", request->name);
             return ZPATH_RESULT_ERR;
        }
        JSON_Value *json_root_val = json_parse_string((const char *)data);
        if (!json_root_val) {
            EXPORTER_LOG(AL_ERROR, "%s: JSON string parse failed for request_body", request->name);
            return ZPATH_RESULT_ERR;
        }

        JSON_Object *json_root_obj = json_value_get_object(json_root_val);
        if (!json_root_obj) {
            EXPORTER_LOG(AL_ERROR, "%s: JSON get object failed for request_body", request->name);
            json_value_free(json_root_val);
            return ZPATH_RESULT_ERR;
        }

        // Append policyId in JSON root object
        char str_policy_id[EXPORTER_USER_PORTAL_ID_STR_LEN] = {0};
        snprintf(str_policy_id, sizeof(str_policy_id), "%"PRId64, request->portal_policy_rule_id);
        json_object_set_string(json_root_obj, "policyId", str_policy_id);
        EXPORTER_DEBUG_USER_PORTAL_API("Adding policyId %s for CreateAprovalRequest", str_policy_id);

        // Serialize the modified data to buffer
        size_t needed_size_in_bytes = json_serialization_size(json_root_val);
        out_buf = EXPORTER_CALLOC(needed_size_in_bytes);
        if (!out_buf) {
            EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory", request->name);
            json_value_free(json_root_val);
            return ZPATH_RESULT_NO_MEMORY;
        }
        /* Serialise JSon into buffer */
        if (json_serialize_to_buffer(json_root_val, out_buf , needed_size_in_bytes) != JSONSuccess) {
            EXPORTER_LOG(AL_ERROR,"%s: %s - domain: %s, failed to serialise json into buffer",
                request->name, api_name, domain);
            json_value_free(json_root_val);
            EXPORTER_FREE(out_buf);
            return ZPATH_RESULT_ERR;
        }

        EXPORTER_DEBUG_USER_PORTAL_API("%s: Modified JSON(%d) -> \n%s\n", request->name,
                                          (int)strlen(out_buf), out_buf);

        // Drain current body and replace it with prepared body
        evbuffer_drain(request->request_body, datalen);
        evbuffer_add_printf(request->request_body, "%s", out_buf);
        EXPORTER_FREE(out_buf);
        json_value_free(json_root_val);
    }

    request->webserver_type = exporter_conn_webserver_type_pra_service;
#ifndef UNIT_TEST
    ret = exporter_user_portal_conn_webserver(request);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, PRA-service Proxy failed", request->name, api_name, domain);
        return ret;
    }
#endif
    return ret;
}

/*
 * Handle url /zsconsole/<console_id>/index.html
 */
static int exporter_portal_handle_zconsole_index_api(struct exporter_request *request)
{
    int ret;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    ret = exporter_request_respond_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, exporter_guac_index_html, exporter_guac_index_html_len);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: PRA portal %s - domain: %s, exporter_request_respond_status_and_data return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Handle url /zconsole/exporter_guac_pra_console.js
 */
static int exporter_portal_handle_guac_pra_console_js_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    ret = exporter_request_respond_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, exporter_guac_pra_console_js, exporter_guac_pra_console_js_len);
    EXPORTER_LOG(AL_DEBUG, "%s: PRA portal %s - domain: %s, exporter_portal_handle_guac_pra_console_js_api return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));
    request->http_res_skip_raw_data_dump = 1;

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Handle url /zconsole/exporter_guac_pra_console.css
 */
static int exporter_portal_handle_guac_pra_console_css_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    ret = exporter_request_respond_status_data_content_type(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, exporter_guac_pra_console_css, exporter_guac_pra_console_css_len, "text/css");
    EXPORTER_LOG(AL_DEBUG, "%s: PRA portal %s - domain: %s, exporter_portal_handle_guac_pra_console_css_api return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));
    request->http_res_skip_raw_data_dump = 1;

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Handle url /zconsole/exporter_guac_pra_console.woff2
 */
static int exporter_portal_handle_exporter_guac_pra_console_woff2_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    ret = exporter_request_respond_status_data_content_type(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, exporter_guac_pra_console_woff2, exporter_guac_pra_console_woff2_len, "application/font-woff2");
    EXPORTER_LOG(AL_DEBUG, "%s: PRA portal %s - domain: %s, exporter_portal_handle_exporter_exporter_guac_pra_console_woff2_api return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));
    request->http_res_skip_raw_data_dump = 1;

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Handle url /guacamole-common-js/all.min.js
 */
static int exporter_portal_handle_guac_javascript_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    ret = exporter_request_respond_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, exporter_guac_all_min_js, exporter_guac_all_min_js_len);
    EXPORTER_LOG(AL_DEBUG, "%s: PRA portal %s - domain: %s, exporter_portal_handle_guac_javascript_api return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Handle url /zconsole/\*.woff, /zconsole/\*.woff2, /zconsole/\*.ttf
 */
static int exporter_portal_handle_exporter_guac_pra_console_all_woff_ttf_api(struct exporter_request *request)
{
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    const char *api_name = get_portal_api_name(api_type);
    unsigned char *data = NULL;
    char *datatype = NULL;
    size_t datalen = 0;
    int ret = ZPATH_RESULT_NO_ERROR;

    if (api_type == ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF2) {
        data = Inter_Regular_woff2;
        datalen = Inter_Regular_woff2_len;
        datatype = "application/font-woff2";
    } else if (api_type == ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF) {
        data = Inter_Regular_woff;
        datalen = Inter_Regular_woff_len;
        datatype = "application/font-woff";
    } else if (api_type == ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_WOFF2) {
        data = fa_solid_900_woff2;
        datalen = fa_solid_900_woff2_len;
        datatype = "application/font-woff2";
    } else if (api_type == ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_TTF) {
        data = fa_solid_900_ttf;
        datalen = fa_solid_900_ttf_len;
        datatype = "application/font-ttf";
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: PRA portal %s - domain: %s, api_type not implemented",
                                request->name, api_name, domain);
        return ZPATH_RESULT_ERR;
    }

    ret = exporter_request_respond_status_data_content_type(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, data, datalen, datatype);
    EXPORTER_LOG(AL_DEBUG, "%s: PRA portal %s - domain: %s, exporter_portal_handle_exporter_guac_pra_console_all_woff_ttf_api return status: %s",
                 request->name, api_name, domain, zpath_result_string(ret));
    request->http_res_skip_raw_data_dump = 1;

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Generate MD5 hash and validate
 */
int exporter_validate_user_name_hash(struct exporter_request *request, const char *expected_user_name_hash)
{
    char hashed_user_name[2 * EXPORTER_SHA256_HASH_SIZE + 1];
    int res = ZPATH_RESULT_NO_ERROR;

    const char *user_name = exporter_user_portal_request_state_get_name(request->portal_info);
    if (!user_name) {
        EXPORTER_LOG(AL_ERROR, "%s: request is not associated with a user id", request->name);
        return ZPATH_RESULT_ERR;
    }

    res = exporter_get_sha256_hash_hex_str(user_name, strlen(user_name), hashed_user_name, sizeof(hashed_user_name) - 1);
    if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to generate SHA256 hash of the user %s",
                                    request->name, user_name);
        return res;
    }

    if (0 == strncasecmp(expected_user_name_hash, hashed_user_name,strlen(hashed_user_name))) {
        res = ZPATH_RESULT_NO_ERROR;
    } else {
        res = ZPATH_RESULT_ERR;
    }
    return res;
}

/*
 * Classification of My-Files URI for a user
 */
enum zpa_user_portal_api_type exporter_get_portal_my_files_user_api_type(struct exporter_request *request, int offset)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    int is_download = 0;
    int res = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    /* Extract, validate and skip over User ID */
    char user_id_string_copy[1024 + 1] = {'\0'};
    memcpy(user_id_string_copy, uri_path, request->url_len > 1024? 1024 : request->url_len);
    char *rest = user_id_string_copy;
    const char *uri_hashed_user_id = strtok_r(&user_id_string_copy[offset + 1], "/", &rest);      /* Extract user ID */
    if (uri_hashed_user_id == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, could not extract hashed user ID from the uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset +=  1 + strlen(uri_hashed_user_id);

    /*
     * Note: Must specify most specific match before less specific match
     */
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_COUNT,
                                            strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_COUNT))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_INITIATE,
                                                   strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_INITIATE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD,
                                                   strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_COMPLETE,
                                                   strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_COMPLETE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_ABORT,
                                                   strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_ABORT))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_ABORT;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE,
                                                   strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE))) {
        /* User File specific operation : Could be delete or download file */

        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE);

        const char *file_id_string = uri_path + offset;
        char *end_ptr = NULL;
        int64_t file_id = strtoll(file_id_string, &end_ptr, 0);                   /* Extract File ID */
        if (file_id <= 0) {
            EXPORTER_LOG(AL_ERROR, "%s: domain: %s, could not extract hashed file ID from the uri path: %s, len: %d",
                                    request->name, domain, uri_path, uri_len);
            return ZPA_USER_PORTAL_API_TYPE_INVALID;
        }
        offset += exporter_get_user_portal_id_len(file_id);

        if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE_DOWNLOAD,
                                                strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE_DOWNLOAD))) {
            api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY;
            is_download = 1;
        } else {
            api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DELETE_FILE;
        }
    } else if ((0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILES,
                                                    strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILES)))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY;
    } else {
        api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Validate SHA256 hash of the username */
    res = exporter_validate_user_name_hash(request, uri_hashed_user_id);
    if (res) {
        /* Hash will be different for download */
        if (is_download) {
            return ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DOWNLOAD_FILE_ADMIN;
        }
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, username hash does not match uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    return api_type;
}

/*
 * Classification of My-Files URI for a customer
 */
enum zpa_user_portal_api_type exporter_get_portal_my_files_customer_api_type(struct exporter_request *request, int offset)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;

    if ((0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_MY_FILES_VIEW_UNINSPECTED,
                          strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES_VIEW_UNINSPECTED)))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_MY_FILES_VIEW_UNINSPECTED;
    } else {
        api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    return api_type;
}

/*
 * Get portal My-Files API type - If there is no matching API, then it is an invalid request.
 */
enum zpa_user_portal_api_type exporter_get_portal_my_files_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    /* Format: https://domain_uri>/v<version>/my-files/customers/<customer-id>/users/<user-id><api_type_path>... */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* API can be requested with /zscope for PRA portals */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    /* Skip over /my-files */
    offset += strlen(EXPORTER_SRA_PORTAL_API_URI_MY_FILES);

    /* Skip over /customers */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS, strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS))) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS);

    /* Extract, validate and skip over customer ID */
    const char *customer_id_string = uri_path + offset + 1;
    char *end_ptr = NULL;
    int64_t uri_customer_id = strtoll(customer_id_string, &end_ptr, 0);                   /* Extract customer ID */
    if (uri_customer_id < 0) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Validate customer ID */
    if (customer_gid != uri_customer_id) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid customer ID %"PRId64" in the uri path: %s, len: %d",
                                request->name, domain, uri_customer_id, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    offset +=  1 + exporter_get_user_portal_id_len(uri_customer_id);


#define USERS           "/users"
    if (0 == strncasecmp(&uri_path[offset], USERS, strlen(USERS))) {
        offset += strlen(USERS);
        api_type = exporter_get_portal_my_files_user_api_type(request, offset);
    } else {
        api_type = exporter_get_portal_my_files_customer_api_type(request, offset);
    }

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s", request->name, uri_path, uri_len, get_portal_api_name(api_type));

    return api_type;
}

/*-------------------------------------------------------------------*/
/* Handle portal My-Files API request                                */
/*-------------------------------------------------------------------*/
static int exporter_sra_portal_handle_my_files_api(struct exporter_request *request)
{
    char error_reason[EXPORTER_USER_PORTER_API_SMALL_RESPONSE_BUFF_SIZE] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int ret = 0;

    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    if (!g_exporter_ot_mode || is_pra_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA is disabled", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }

    if (is_pra_advanced_file_transfer_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA Advanced My-files feature is disabled for the customer", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access Advanced My-Files is disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }


    /* Extract api type from uri path */
    enum zpa_user_portal_api_type api_type = exporter_get_portal_my_files_api_type(request);
    if (api_type == ZPA_USER_PORTAL_API_TYPE_INVALID) {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid PRA My-files endpoint", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "PRA My-files endpoint is not valid", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ZPATH_RESULT_ERR;
    }

    /* Set type in state */
    exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);

    ret = exporter_sra_portal_http_pra_service_proxy_api(request);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async",
                                                request->name, get_portal_api_name(api_type), domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, my-files request failed", request->name, get_portal_api_name(api_type), domain);
        snprintf(error_reason, sizeof(error_reason), "HTTP-PRA-Service-Proxy-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ret;
    }

    return ret;
}

/*
 * Get portal Requestable-Consoles API type - If there is no matching API, then it is an invalid request.
 */
enum zpa_user_portal_api_type exporter_get_portal_requestable_consoles_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    /* Format: https://domain_uri>/v<version>/privileged-consoles/customers/<customer-id>/praPortal/<portal-id>... */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* API can be requested with /zscope for PRA portals */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS,
                                            strlen(EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES;

        /* Skip over /privileged-consoles */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS);
    } else {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Skip over /customers */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS, strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS))) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS);

    /* Extract, validate and skip over customer ID */
    const char *customer_id_string = uri_path + offset + 1;
    char *end_ptr = NULL;
    int64_t uri_customer_id = strtoll(customer_id_string, &end_ptr, 0);                   /* Extract customer ID */
    if (uri_customer_id < 0) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Validate customer ID */
    if (customer_gid != uri_customer_id) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid customer ID %"PRId64" in the uri path: %s, len: %d",
                                request->name, domain, uri_customer_id, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    offset +=  1 + exporter_get_user_portal_id_len(uri_customer_id);

    /* Skip over /portals */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_PORTAL_API_URI_PRA_PORTAL, strlen(EXPORTER_PORTAL_API_URI_PRA_PORTAL))) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_PORTAL_API_URI_PRA_PORTAL);

    /* Extract, validate and skip over portal ID */
    const char *portal_id_string = uri_path + offset + 1;
    int64_t uri_portal_id = strtoll(portal_id_string, &end_ptr, 0);                   /* Extract portal ID */
    if (uri_portal_id < 0) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset +=  1 + exporter_get_user_portal_id_len(uri_portal_id);

    /* TODO: Extract optional query parameters */

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s", request->name, uri_path, uri_len, get_portal_api_name(api_type));

    return api_type;
}

/*-------------------------------------------------------------------------------*/
/* Handle portal Requestable-Consoles API request                                */
/*-------------------------------------------------------------------------------*/
static int exporter_sra_portal_handle_requestable_consoles_api(struct exporter_request *request)
{
    char error_reason[EXPORTER_USER_PORTER_API_SMALL_RESPONSE_BUFF_SIZE] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int ret = 0;

    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    if (!g_exporter_ot_mode || is_pra_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA is disabled", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }

    if (is_pra_enduser_approvals_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA Privileged Approvals feature is disabled for the customer", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access Privileged Approvals is disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }


    /* Extract api type from uri path */
    enum zpa_user_portal_api_type api_type = exporter_get_portal_requestable_consoles_api_type(request);
    if (api_type == ZPA_USER_PORTAL_API_TYPE_INVALID) {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid PRA Requestable Consoles endpoint", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "PRA Requestable Consoles endpoint is not valid", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ZPATH_RESULT_ERR;
    }

    /* Set type in state */
    exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);

    ret = exporter_sra_portal_http_pra_service_proxy_api(request);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async",
                                                request->name, get_portal_api_name(api_type), domain);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, requestable-consoles request failed", request->name, get_portal_api_name(api_type), domain);
        snprintf(error_reason, sizeof(error_reason), "HTTP-PRA-Service-Proxy-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ret;
    }

    return ret;
}

/*
 * Get portal Zconsole API type - If there is no matching API, then we assume it is index page request.
 */
static enum zpa_user_portal_api_type exporter_get_user_portal_zconsole_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_SRA_PORTAL_API_TYPE_ZCONSOLE;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;

    /* Format: https://domain_uri>/v<version>/zsconsole/<console_id>/<api_type_path>... */

    api_version = exporter_get_user_portal_api_version(request);
    if (api_version != ZPA_USER_PORTAL_API_VERSION_INVALID) {
        /* Valid version detected, adjust api_type uri_path offset to be after version */
        offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */
    } else {
        /*
         * If the request is invalid, we will still treat it as index.html request.
         */
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Invalid Portal API version: %d", request->name, api_version);
    }

    /* API can be requested with /zscope for PRA portals */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);

        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    /* Skip over /zconsole */
    offset += strlen(EXPORTER_SRA_PORTAL_API_URI_ZCONSOLE);

    if (uri_path[offset + 1] == '$') {
        offset += 1 + exporter_get_zconsole_connect_id_len(request);
    } else {
        /* Skip over /console_ID */
        int64_t console_id = get_console_id_from_first_zconsole_request(request);
        if (console_id) {
            offset +=  1 + exporter_get_user_portal_id_len(console_id);
        }
    }

    /*
     * Note: Must specify most specific match before less specific match
     */
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_JS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_CSS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_WOFF2;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_GUAC_JS, strlen(EXPORTER_SRA_PORTAL_API_URI_GUAC_JS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_JS;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF2, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF2))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF2;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_WOFF2, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_WOFF2))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_WOFF2;
    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_TTF, strlen(EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_TTF))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_TTF;
    }

    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s", request->name, uri_path, uri_len, get_portal_api_name(api_type));

    return api_type;
}

/*
 * Get portal privileged approvals API type - If there is no matching API, then it is an invalid request.
 */
static enum zpa_user_portal_api_type exporter_get_privileged_approvals_api_type(struct exporter_request *request)
{
    const char *uri_path = (const char *)&(request->url[request->url_parser.field_data[UF_PATH].off]);
    const uint16_t uri_len = request->url_parser.field_data[UF_PATH].len;
    enum zpa_user_portal_api_type api_type = ZPA_USER_PORTAL_API_TYPE_INVALID;
    enum zpa_user_portal_api_version api_version;
    int offset = 0;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int64_t customer_gid = request->conn->exporter_domain->customer_gid;
    struct zpn_sra_portal *sra_portal = NULL;

    /* Parse the URL
     * Format: "/v1/privileged-approvals/customers/{customerId}/users/{userId}/portals/{portalId}/approval-request"
     */
    api_version = exporter_get_user_portal_api_version(request);
    if (api_version == ZPA_USER_PORTAL_API_VERSION_INVALID) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset = 2 + exporter_get_user_portal_api_version_len(api_version); /* Skip over "/v" + version_len */

    /* API can be requested with /zscope */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        /* Skip over /zscope */
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_ZSCOPE);
        /* Skip over /zscope_ID */
        int64_t zscope_id = get_scope_id_from_zscope_request(request);
        if (zscope_id) {
            offset +=  1 + exporter_get_user_portal_id_len(zscope_id);
        }
    }

    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS,
                                            strlen(EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS))) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Skip over /privileged-approvals */
    offset += strlen(EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS);

    /* Skip over /customers */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS, strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS))) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri.. expected customers, path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS);

    /* Extract and validate customer ID */
    const char *customer_id_string = uri_path + offset + 1;
    char *end_ptr = NULL;
    int64_t uri_customer_id = strtoll(customer_id_string, &end_ptr, 0);                   /* Extract customer ID */
    if (uri_customer_id < 0) {
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    /* Validate customer ID */
    if (customer_gid != uri_customer_id) {
        EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid customer ID %"PRId64" in the uri path: %s, len: %d",
                                request->name, domain, uri_customer_id, uri_path, uri_len);
        return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset +=  1 + exporter_get_user_portal_id_len(uri_customer_id);

    /* Check for /users
     * Create/delete approval requests should have this
     * list requests can be for users or based on customer-id only
     */
    bool user_id_present = false;
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URL_USERS, strlen(EXPORTER_SRA_PORTAL_API_URL_USERS))) {
        offset += strlen(EXPORTER_SRA_PORTAL_API_URL_USERS);

        /* Extract and validate user */
        const char *user_id_string = uri_path + offset + 1;
        end_ptr = strchr(uri_path + offset + 1, '/');
        long user_id_len  = end_ptr - user_id_string;

        /* Validate user ID */
        if (exporter_validate_user_name_hash(request, user_id_string) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid username, path: %s, len: %d",
                                request->name, domain, uri_path, uri_len);
            return ZPA_USER_PORTAL_API_TYPE_INVALID;
        }
        offset +=  1 + user_id_len;
        user_id_present = true;
    }

    /* Skip over /portals */
    if (0 != strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_PORTAL, strlen(EXPORTER_SRA_PORTAL_API_URI_PORTAL))) {
	EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri.. expected portals, path: %s, len: %d",
							request->name, domain, uri_path, uri_len);
	return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset += strlen(EXPORTER_SRA_PORTAL_API_URI_PORTAL);

    /* Extract and validate portal ID */
    const char *portal_id_string = uri_path + offset + 1;
    int64_t uri_portal_id = strtoll(portal_id_string, &end_ptr, 0);                   /* Extract portal ID */
    if (uri_portal_id < 0) {
	return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }

    int res = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
    if ((res != ZPATH_RESULT_NO_ERROR) || (sra_portal == NULL) || (sra_portal->gid != uri_portal_id)) {
	EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal ID. path: %s, len: %d",
	                       request->name, domain, uri_path, uri_len);
	return ZPA_USER_PORTAL_API_TYPE_INVALID;
    }
    offset +=  1 + exporter_get_user_portal_id_len(uri_portal_id);

    /* Check for /approval-requests */
    if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_GET_PRIVILEGED_APPROVAL_REQUESTS,
                         strlen(EXPORTER_SRA_PORTAL_API_URI_GET_PRIVILEGED_APPROVAL_REQUESTS))) {
        api_type = ZPA_SRA_PORTAL_API_TYPE_GET_PRIVILEGED_APPROVAL_REQUESTS;

    } else if (0 == strncasecmp(&uri_path[offset], EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVAL_REQUEST,
                                strlen(EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVAL_REQUEST))) {
        /* approval request, validate user_id is present as well. */
        if (user_id_present) {
            api_type = ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST;
        } else {
	    EXPORTER_LOG(AL_ERROR, "%s: domain: %s, Invalid Portal uri.. expected user, path: %s, len: %d",
							request->name, domain, uri_path, uri_len);
        }
    }
    EXPORTER_DEBUG_USER_PORTAL_API("%s: Portal uri path: %s, len: %d, api_type: %s, method: %s",
             request->name, uri_path, uri_len, get_portal_api_name(api_type), http_method_names[request->req_method]);
    return api_type;
}

/*
 * Handle the Privileged approvals apis
 */
static int exporter_sra_portal_handle_privileged_approvals_api(struct exporter_request *request)
{
    char error_reason[EXPORTER_USER_PORTER_API_SMALL_RESPONSE_BUFF_SIZE] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int ret = 0;

    int64_t customer_gid = request->conn->exporter_domain->customer_gid;

    /* Feature validations */
    if (!g_exporter_ot_mode || is_pra_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA is disabled", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_DISABLED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access is currently disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }

    if (is_pra_enduser_approvals_disabled(customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA Privileged Approvals feature is disabled for the customer", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "Privileged Remote Access Privileged Approvals is disabled", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));

        return ZPATH_RESULT_ERR;
    }


    /* Extract the feature specific api type from uri path */
    enum zpa_user_portal_api_type api_type = exporter_get_privileged_approvals_api_type(request);
    if (api_type == ZPA_USER_PORTAL_API_TYPE_INVALID) {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid PRA Approvals Request endpoint", request->name);
        exporter_portal_api_error_response(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           "PRA Approvals  endpoint is not valid", domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
        return ZPATH_RESULT_ERR;
    }

    /* Set type in state */
    exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);

    ret = exporter_sra_portal_http_pra_service_proxy_api(request);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        // We will be processing this request again but we did not have an error
        EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: %s for domain: %s will be processed async",
                                                request->name, get_portal_api_name(api_type), domain);
        // We process the api from the starting point, repeating all the parsing for async requests.
        // Set the API type to the base type ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS for this.
        exporter_user_portal_request_state_set_api_type(request->portal_info, ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS);
        ret = ZPATH_RESULT_NO_ERROR;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: %s - domain: %s, privileged-approvals request failed", request->name, get_portal_api_name(api_type), domain);
        snprintf(error_reason, sizeof(error_reason), "HTTP-PRA-Service-Proxy-Response Error - %s", zpath_result_string(ret));
        exporter_portal_api_error_response(request, HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                           EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,
                                           EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL,
                                           error_reason, domain,
                                           exporter_user_portal_request_state_get_name(request->portal_info));
    }
    return ret;
}

/*-------------------------------------------------------------------*/
/* Handle portal Zconsole API request                                */
/*-------------------------------------------------------------------*/
static int exporter_portal_handle_zconsole_api(struct exporter_request *request)
{
    int ret;

    /* Extract api type from uri path */
    enum zpa_user_portal_api_type api_type = exporter_get_user_portal_zconsole_api_type(request);

    /* Set type in state */
    exporter_user_portal_request_state_set_api_type(request->portal_info, api_type);

    /* API specific handlers */
    switch (api_type) {
        case ZPA_SRA_PORTAL_API_TYPE_ZCONSOLE:
            ret = exporter_portal_handle_zconsole_index_api(request);
            break;

        case ZPA_SRA_PORTAL_API_TYPE_GUAC_JS:
            ret = exporter_portal_handle_guac_javascript_api(request);
            break;

        case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_JS:
            ret = exporter_portal_handle_guac_pra_console_js_api(request);
            break;

        case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_CSS:
            ret = exporter_portal_handle_guac_pra_console_css_api(request);
            break;

        case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_WOFF2:
            ret = exporter_portal_handle_exporter_guac_pra_console_woff2_api(request);
            break;
        case ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF2:
        case ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF:
        case ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_WOFF2:
        case ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_TTF:
            ret = exporter_portal_handle_exporter_guac_pra_console_all_woff_ttf_api(request);
            break;
        default:
            ret = ZPATH_RESULT_NOT_IMPLEMENTED;
            break;
    }

    return ret;
}


static int exporter_portal_handle_guac_tunnel_api(struct exporter_request *request)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    int64_t console_id;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    const char *assertion_key = exporter_user_portal_request_state_get_assertion_key(request);
    const char* assertion = exporter_user_portal_request_state_get_assertion(request);

    if (!assertion_key || !assertion) {
        EXPORTER_LOG(AL_ERROR, "%s: Missing SAML assertion info",  request->name);
        return ZPATH_RESULT_ERR;
    }


    ret = extract_sessionid_and_consoleid_from_request(request, &console_id);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        exporter_portal_defer_api_request_async(request);
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        return ret;
    }

    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC calling handle_guac_api_request");
    ret = handle_guac_api_request(request, assertion_key, assertion, 0);
    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC Defering handle_guac_api_request since result is async");
        exporter_portal_defer_api_request_async(request);
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC handle_guac_api_request returned ERROR");
        return ret;
    }
    EXPORTER_LOG(AL_INFO, "%s: User portal %s - domain: %s, %s return status: %s",
                 request->name, api_name, domain, __FUNCTION__, zpath_result_string(ret));

    ret = exporter_conn_wake_from_other_thread(request->conn);

    return ret;
}

/*
 * Re-process api request
 */
int handle_reprocess_portal_api_request(struct exporter_request *request, uint8_t *cookie)
{

    int ret;

    EXPORTER_PORTAL_API_STATS_INC_ASYNC_COMPLETED;

    if (!exporter_user_portal_request_state_is_valid(request->portal_info)) {
        EXPORTER_LOG(AL_ERROR, "%s: Portal api re-proess error, invalid portal state", request->name);
        return ZPATH_RESULT_BAD_STATE;
    }

    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Re-process request cookie: %p, api_type: %s, async_state: %s, async_count: %d",
                 request->name, cookie, api_name, exporter_request_async_state_get_str(request->async_state), request->async_count);

    ret = handle_portal_api_request(request);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Portal API %s re-process error: %s", request->name, api_name, zpath_result_string(ret));
    }

    return ret;
}

/*
 * Set portal api status log
 */
static void set_portal_api_status_log(struct exporter_request *request, const char *api_status)
{
    char reason[EXPORTER_USER_PORTAL_ERROR_REASON_LEN] = "";

    /* Skip if we have no status or log already set */
    if (!api_status || request->log.portal_api_status) {
        return;
    }

    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));

    snprintf(reason, sizeof(reason), "%s, %s", api_name, api_status);

    request->log.portal_api_status = EXPORTER_PORTAL_SAFE_STRDUP(reason);
}

int
extract_query_value_from_request(struct exporter_request *request,
                                char *query_value,
                                size_t  query_length,
                                const char *parameter)
{
    int ret = ZPATH_RESULT_NO_ERROR;

    ret = query_string_find(request->url, &(request->url_parser), parameter, query_value, query_length);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("Query string: %s value: %s ", parameter, query_value);
    } else {
        EXPORTER_LOG(AL_ERROR, "Console query string: %s parsing failed %s", parameter, request->url);
    }
    return ret;
}

/*-------------------------------------------------------------------*/
/* Handle portal API request                                         */
/*-------------------------------------------------------------------*/
int handle_portal_api_request(struct exporter_request *request)
{
    int ret;

    /* Make sure basic request state is valid and portal info is set */
    ret = exporter_portal_validate_classify_request(request);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Request preprocess failed with: %s", request->name, zpath_result_string(ret));
        return ret;
    }

    /* Access following only if valid request */
    const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    /* API specific handlers */
    switch (exporter_user_portal_request_state_get_api_type(request->portal_info)) {
    case ZPA_USER_PORTAL_API_TYPE_INDEX:
        ret = ZPATH_RESULT_NO_ERROR;
        break;

    case ZPA_USER_PORTAL_API_TYPE_USER:
        ret = exporter_portal_handle_user_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_COMPANY:
        ret = exporter_portal_handle_company_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_COMPANY_LOGO:
        ret = exporter_portal_handle_company_logo_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_PORTAL:
        ret = exporter_portal_handle_portal_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_PORTAL_LOGO:
        ret = exporter_portal_handle_portal_logo_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_AUP:
        ret = exporter_portal_handle_aup_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_LINKS:
        ret = exporter_portal_handle_links_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_LINKS_ICONS:
        ret = exporter_portal_handle_links_icons_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_CONSOLE_LINKS_ICONS:
        ret = exporter_sra_portal_handle_console_links_icons_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_ZAPP_LINKS:
        ret = exporter_portal_handle_zapp_links_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_LINK_ICON:
        ret = exporter_portal_handle_link_icon_api(request);
        break;

    case ZPA_USER_PORTAL_API_TYPE_LOGOUT:
        ret = exporter_portal_handle_user_logout_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_ZCONSOLE:
        ret = exporter_portal_handle_zconsole_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_GUAC_JS:
        ret = exporter_portal_handle_guac_javascript_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_GUAC_WEBSOCKET_TUNNEL:
        ret = exporter_portal_handle_guac_tunnel_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO:
        ret = exporter_sra_portal_handle_console_info_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_HTTP_PROXY:
        ret = exporter_sra_portal_http_proxy_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_SHARED_CONSOLE_LINKS_ICONS:
        ret = exporter_sra_portal_handle_shared_console_links_icons_api(request);
        break;

    case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_JS:
        ret = exporter_portal_handle_guac_pra_console_js_api(request);
        break;

    case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_CSS:
        ret = exporter_portal_handle_guac_pra_console_css_api(request);
        break;

    case  ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_WOFF2:
        ret = exporter_portal_handle_exporter_guac_pra_console_woff2_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS:
    case ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS: // Only include scopes that have priviledge-approval enabled
        ret = exporter_sra_portal_handle_scope_links_icons_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY:
        ret = exporter_sra_portal_handle_my_files_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES:
        ret = exporter_sra_portal_handle_requestable_consoles_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS:
        ret = exporter_sra_portal_handle_privileged_approvals_api(request);
        break;

    case ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS:
        ret = exporter_sra_portal_handle_privileged_desktops_api(request);
        break;

    default:
        ret = ZPATH_RESULT_NOT_IMPLEMENTED;
        break;

    }

    EXPORTER_LOG(AL_NOTICE, "%s: handle portal api request: portal domain: %s, api: %s, ret: %s", request->name, domain, api_name, zpath_result_string(ret));
    set_portal_api_status_log(request, zpath_result_string(ret));

    return ret;
}

/*
 * Register portal api structures
 */
static int exporter_portal_api_register_structures(struct exporter *exporter)
{
    /* Register structures, in case it hasn't already been registered */
    if (s_portal_api_init_done == 1) {
        EXPORTER_LOG(AL_NOTICE, "Portal api register structures already done");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!(exporter_user_portal_api_user_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIUSERRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_company_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPICOMPANYRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_portal_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIPORTALRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_aup_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIAUPRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_link_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPILINK_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_link_icon_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPILINKICON_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_sra_portal_api_console_link_icon_description =
          argo_register_global_structure(EXPORTERSRAPORTALAPILINKICON_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_sra_portal_api_shared_console_link_icon_description =
          argo_register_global_structure(EXPORTERSRAPORTALAPISHAREDLINKICON_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_zapp_link_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIZAPPLINK_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_links_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPILINKSRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_links_resp_int_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPILINKSRESPONSEINT_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_links_icons_resp_int_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPILINKSICONSRESPONSEINT_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_sra_portal_api_console_links_icons_resp_int_description =
          argo_register_global_structure(EXPORTERCONSOLELINKSRESPONSEINT_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_sra_portal_api_shared_console_links_icons_resp_int_description =
          argo_register_global_structure(EXPORTERSHAREDCONSOLELINKSRESPONSEINT_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_zapp_links_resp_int_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIZAPPLINKSRESPONSEINT_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_error_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIERRORRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_image_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIIMAGERESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_user_logout_resp_description =
          argo_register_global_structure(EXPORTERUSERPORTALAPIUSERLOGOUTRESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_api_stats_description =
          argo_register_global_structure(EXPORTER_USER_PORTAL_API_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_sra_portal_api_console_info_description =
          argo_register_global_structure(EXPORTERSRAPORTALCONSOLEINFORESPONSE_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    EXPORTER_LOG(AL_NOTICE, "Portal api register structures done");
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Portal API request init
 */
int exporter_portal_api_request_init(struct exporter *exporter)
{
    int ret = ZPATH_RESULT_NO_ERROR;

    if (s_portal_api_init_done == 0) {
        ret = exporter_portal_api_register_structures(exporter);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_INFO, "Exporter portal api request init done");
            s_portal_api_init_done = 1;
        } else {
            EXPORTER_LOG(AL_ERROR, "Exporter portal api request init failed! ret=%s", zpath_result_string(ret));
        }
    }

    exporter_guac_api_request_init(exporter);

    EXPORTER_PORTAL_API_STATS_INC_BY_RET_CODE(init, 1, ret);

    return ret;
}

/*
 * Portal API stats clear
 */
void exporter_portal_api_stats_clear()
{
    bzero(&s_portal_api_stats, sizeof(s_portal_api_stats));
}

/*
 * Portal API stats dump
 */
void exporter_portal_api_stats_dump(struct zpath_debug_state *request_state)
{
    char output_buf[EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE] = "";

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(exporter_user_portal_api_stats_description,
                                                    &s_portal_api_stats, output_buf, sizeof(output_buf), NULL, 1)) {
        ZDP("%s\n", output_buf);
    }
}

/*
 * config.feature.privileged.vnc.globalDisable = 1 (Default - Globally disable VNC)
 * config.feature.privileged.vnc.globalDisable = 0 (VNC not globally disabled)
 */
int is_vnc_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_VNC_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_PRA_VNC_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

/*
 * config.feature.privileged.realvnc.globalDisable = 1 (Default - Globally disable REALVNC)
 * config.feature.privileged.realvnc.globalDisable = 0 (REALVNC not globally disabled)
 */
int is_realvnc_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_REALVNC_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_PRA_REALVNC_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

/*
 * VNC applications are disabled by default in itasca and UI
 * is_vnc_disabled - returns 1 by default as feature is disabled by default.
 * is_vnc_disabled - returns 1 if "feature.privileged.vnc" = "disabled" for
 * a specific customer or for all customers with global GID or global disable
 */
int is_vnc_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
	int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    if (is_vnc_globally_disabled()) return 1;

    config_value = zpath_config_override_get_config_str(PRA_VNC_ACCESS,
                                                      &config_value,
                                                      DEFAULT_PRA_VNC_ACCESS,
                                                      customer_gid,
                                                      zpath_instance_global_state.current_config->gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_PRA_VNC_ACCESS) == 0) {
        return 1;
    } else {
        return 0;
    }
}

/*
 * is_markdown_content_enabled - returns 0 by default as feature is disabled by default.
 * is_markdown_content_enabled - returns 1 if "feature.privileged.markdowncontent" = "enabled" for
 * a specific customer or for all customers with global GID or global disable
 */
int is_markdown_content_enabled(int64_t customer_gid)
{
    char *config_value = NULL;
	int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_str(PORTAL_MARKDOWN_CONTENT,
                                                      &config_value,
                                                      DEFAULT_PORTAL_MARKDOWN_CONTENT,
                                                      customer_gid,
                                                      zpath_instance_global_state.current_config->gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator

    if (strcmp(config_value, DEFAULT_PORTAL_MARKDOWN_CONTENT_DISABLED) == 0) {
        return 0;
    } else {
        return 1;
    }
}

/*
 * RealVNC applications are disabled by default in itasca and UI
 * is_realvnc_disabled - returns 1 by default as feature is disabled by default.
 * is_realvnc_disabled - returns 1 if "feature.privileged.realvnc" = "disabled" for
 * a specific customer or for all customers with global GID or global disable
 */
int is_realvnc_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
	int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    if (is_realvnc_globally_disabled()) return 1;

    config_value = zpath_config_override_get_config_str(PRA_REALVNC_ACCESS,
                                                      &config_value,
                                                      DEFAULT_PRA_REALVNC_ACCESS,
                                                      customer_gid,
                                                      zpath_instance_global_state.current_config->gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_PRA_REALVNC_ACCESS) == 0) {
        return 1;
    } else {
        return 0;
    }
}

/*
 * This function is used to validate the shared session from the session-store.
 * We validate the console, SRA application and capability policy mapped to the shared session
 */
int
exporter_request_check_shared_session_capability_policy(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data)
{
    int ret = 0;
    struct zpn_sra_console *sra_console = NULL;

    /* Scope related validation for shared session */
    ret = exporter_request_shared_session_scope_validation(request, shared_session_data);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: scope validation failed for shared session %s", request->name, shared_session_data->sess_id);
        return ZPATH_RESULT_ERR;
    }

    int64_t scope_gid = shared_session_data->scope_gid? shared_session_data->scope_gid: shared_session_data->customer_gid;
    ret = zpn_sra_console_get_by_id_immediate(shared_session_data->console_id, request->conn->exporter_domain->customer_gid,
                                               scope_gid, &sra_console);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: cannot fetch console %"PRId64"", request->name, shared_session_data->console_id);
        return ZPATH_RESULT_ERR;
    }

    if (!sra_console->enabled) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: console %"PRId64" is not enabled", request->name, shared_session_data->console_id);
        return ZPATH_RESULT_ERR;
    }

    /* Get sra_application */
    struct zpn_sra_application *sra_application = NULL;

    ret = zpn_sra_application_get_by_id_immediate(sra_console->sra_app_id, &sra_application);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: could not retrieve sra_application %"PRId64": %s",
                                        request->name, sra_console->sra_app_id, zpn_result_string(ret));
        return ZPATH_RESULT_ERR;;
    }
    if (sra_application == NULL) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: could not retrieve sra_application %"PRId64, request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }
    if (sra_application->deleted) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: sra_application %"PRId64" is deleted", request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }

    if (!sra_application->enabled) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: sra_application %"PRId64" is disabled", request->name, sra_console->sra_app_id);
        return ZPATH_RESULT_ERR;
    }

    /* Get zpn_application object, AKA app segment */
    struct zpn_application *application = NULL;

    ret = zpn_application_get_by_id_immediate(sra_application->app_id, &application);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: could not retrieve application %"PRId64": %s",
                                        request->name, sra_application->app_id, zpn_result_string(ret));
        return ZPATH_RESULT_ERR;
    }
    if (application == NULL) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: could not retrieve application %"PRId64, request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }

    if (application->deleted) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: application %"PRId64" is deleted", request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }

    if (!application->enabled) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: application %"PRId64" is disabled", request->name, sra_application->app_id);
        return ZPATH_RESULT_ERR;
    }

    struct zpn_privileged_capabilities *mapping = NULL;
    size_t mapping_count = 1;
    ret = zpn_privileged_capabilities_get_by_zpn_rule_gid(shared_session_data->policy_rule_id, &mapping, &mapping_count,
                                                          exporter_request_wally_callback, request, 0);
    if (ret == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s Asynchronous evaluation of zpn_rule %"PRId64"",
                request->name, shared_session_data->policy_rule_id);
        return ret;
    } else if (ret != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s Failed to session sharing zpn_rule %"PRId64", error = %s",
                request->name, shared_session_data->policy_rule_id, zpath_result_string(ret));
        return ret;
    } else if ((ret == ZPATH_RESULT_NO_ERROR) && mapping &&
            (!mapping->deleted) &&
            (mapping->customer_gid == (request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid))) {

        if (!(mapping->capabilities_mask & SHARE_SESSION)) {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s Shared session does not have the sharing capability policy set", request->name);
            return ZPATH_RESULT_ERR;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * This function is used to validate the shared session with respect to scope
 */
int
exporter_request_shared_session_scope_validation(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data)
{
    int enabled = 0;
    int ret = 0;

    /* Session belongs to default scope, allow */
    if (shared_session_data->scope_gid == 0) {
        return ZPATH_RESULT_NO_ERROR;
    }

    int is_dta_enabled = zpn_get_delegated_admin_status(request->conn->exporter_domain->customer_gid);
    if (is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid) ||
            (!is_dta_enabled)) {
        EXPORTER_LOG(AL_ERROR, "%s: PRA-DTA or DTA is disabled, cannot display session %s shared in MT", request->name, shared_session_data->sess_id);
        return ZPATH_RESULT_ERR;
    }

    ret = zpn_is_scope_enabled(shared_session_data->scope_gid, &enabled);
    if (ret != ZPN_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: zpn get scope %"PRId64" enabled status failed %s", request->name, shared_session_data->scope_gid, zpath_result_string(ret));
        return ret;
    }

    if (!enabled) {
        EXPORTER_LOG(AL_ERROR, "%s: shared session %s belongs to scope %"PRId64", but scope is disabled",
                                request->name, shared_session_data->sess_id, shared_session_data->scope_gid);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Get scope name for shared session from MT on global portal
 */
char *
exporter_request_get_shared_session_scope_name(struct exporter_request *request, struct exporter_shared_session_data *shared_session_data)
{
    /* Shared sessions of default scope, no need for the name */
    if (shared_session_data->scope_gid == 0) {
        return NULL;
    }
    return (zpn_scope_get_name(shared_session_data->scope_gid));
}
