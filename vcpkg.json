{"name": "itasca", "version": "0.0.1", "builtin-baseline": "ce613c41372b23b1f51333815feb3edd87ef8a8b", "dependencies": ["benchmark", "ccronexpr", "expat", "fast-ber", "gtest", "icu", "inja", "ldns", {"name": "libcap", "platform": "linux"}, "libevent", "libmaxminddb", "libp<PERSON>", "libpq", {"name": "librdkafka", "features": ["ssl", "zlib", "zstd"]}, {"name": "libxml2", "default-features": false}, "lz4", "modsecurity", "openssl", "pcre", {"name": "sqlite3", "features": ["dbpage-vtab", "dbstat", "fts3", "fts4", "fts5", "geopoly", "limit", "math", "memsys3", "memsys5", "omit-load-extension", "rtree", "session", "snapshot", "soundex"]}, "wireguard-tools", "xmlsec", "zpa-cloud-config", {"name": "zvelodb", "platform": "linux & x64"}], "overrides": [{"name": "librdkafka", "version": "2.6.0"}]}