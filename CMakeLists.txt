cmake_minimum_required(VERSION 3.29.6)

project(itasca)

option(USE_CCACHE "Use ccache if available" ON)
option(ENABLE_FIPSLD "Do fipsld thing on executables linking to openssl" ON)
option(SANITIZED_INTERFACE "Enable sanitizer interface and rpm packaging" OFF)
option(BUILD_FUZZING "Build fuzzing targets" OFF)
set(VERSION_SUFFIX "" CACHE STRING "Suffix for custom builds, like asan/debug")

list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/cmake/")
include(ItascaHelpers)

find_program(ccache ccache)
if(USE_CCACHE AND ccache)
    message(STATUS "Using ccache at ${ccache}")
    set(CMAKE_C_COMPILER_LAUNCHER ${ccache})
    set(CMAKE_CXX_COMPILER_LAUNCHER ${ccache})
endif()

if(ENABLE_FIPSLD AND CMAKE_SYSTEM_NAME STREQUAL "Linux")
    message(STATUS "Enabling fipsld")
    find_file(fips_premain NAMES fips_premain.o PATH_SUFFIXES lib REQUIRED)
    find_program(incore NAMES incore REQUIRED)
    set(CMAKE_C_LINKER_LAUNCHER "${CMAKE_CURRENT_LIST_DIR}/cmake/fipsld.sh" "${fips_premain}" "${incore}")
    set(CMAKE_CXX_LINKER_LAUNCHER "${CMAKE_CURRENT_LIST_DIR}/cmake/fipsld.sh" "${fips_premain}" "${incore}")
endif()

if(SANITIZED_INTERFACE)
    add_definitions(-DADDRESS_SANITIZER_RPM)
endif()

find_package(Threads REQUIRED)
find_package(PkgConfig REQUIRED) # Needed for 3rd party lib version reporting/enforcing.
find_package(OpenSSL REQUIRED)
find_package(LibEvent REQUIRED)
find_package(LibXmlSec REQUIRED)
find_package(PostgreSQL REQUIRED)
find_package(unofficial-sqlite3 CONFIG REQUIRED)
find_package(LibXml2 REQUIRED)
find_package(LibXslt REQUIRED)
find_package(maxminddb CONFIG REQUIRED)
find_package(ICU REQUIRED COMPONENTS uc i18n data)
find_package(LDNS REQUIRED)
find_package(GTest CONFIG REQUIRED)
find_package(benchmark CONFIG REQUIRED)
find_package(LibPCAP REQUIRED)
find_package(RdKafka CONFIG REQUIRED)
find_package(LibPCRE REQUIRED)
find_package(ModSecurity REQUIRED)
find_package(LibCcronexpr REQUIRED)
find_package(lz4 CONFIG REQUIRED)
find_package(EXPAT REQUIRED)
find_package(LibZpaCloudConfig REQUIRED)

if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    find_package(LibCAP REQUIRED)
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
        find_package(ZVeloDB REQUIRED)
    endif()
endif()

enable_testing()
init_argo_parse()
init_packaging()
init_coverage()

add_subdirectory(src)

get_property(headers TARGET gen-headers PROPERTY output)
add_tar(GZ NAME headers FILES ${headers})
