{"version": 6, "configurePresets": [{"name": "base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/out/${presetName}", "cacheVariables": {"CMAKE_C_STANDARD": "11", "CMAKE_CXX_STANDARD": "17", "CMAKE_CXX_EXTENSIONS": "OFF", "CMAKE_TOOLCHAIN_FILE": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake", "VCPKG_INSTALL_OPTIONS": "--no-print-usage;--disable-metrics;--x-asset-sources=clear\\;x-script,${sourceDir}/vcpkg/asset-cache.sh {url} {sha512} {dst}", "VCPKG_TARGET_TRIPLET": "${presetName}"}}, {"name": "linux", "hidden": true, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}, "environment": {"OS_C_FLAGS": "-Werror", "OS_CXX_FLAGS": "-Werror", "OS_LD_FLAGS": "-Wl,-z,now -Wl,-z,relro -Wl,-z,noexecstack -static-libgcc"}}, {"name": "osx", "hidden": true, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "<PERSON>"}, "environment": {"OS_C_FLAGS": "", "OS_CXX_FLAGS": "", "OS_LD_FLAGS": ""}}, {"name": "cc_base", "hidden": true, "environment": {"BASE_C_FLAGS": "$env{OS_C_FLAGS} -g -fno-common -fno-omit-frame-pointer -Wall -fPIC -ffile-prefix-map=${sourceDir}/src/=", "BASE_CXX_FLAGS": "$env{OS_CXX_FLAGS} -g -fno-common -fno-omit-frame-pointer -Wall -Wextra -fPIC -ffile-prefix-map=${sourceDir}/src/=", "BASE_LD_FLAGS": "$env{OS_LD_FLAGS} -pie -static-libstdc++ -Wno-unused-command-line-argument"}, "cacheVariables": {"CMAKE_C_FLAGS": "$env{BASE_C_FLAGS}", "CMAKE_CXX_FLAGS": "$env{BASE_CXX_FLAGS}", "CMAKE_EXE_LINKER_FLAGS": "$env{BASE_LD_FLAGS}"}}, {"name": "cc_debug", "hidden": true, "inherits": ["base", "cc_base"], "cacheVariables": {"VERSION_SUFFIX": "debug"}}, {"name": "cc_asan", "hidden": true, "inherits": ["base", "cc_base"], "cacheVariables": {"CMAKE_C_FLAGS": "$env{BASE_C_FLAGS} -fsanitize=address -O2", "CMAKE_CXX_FLAGS": "$env{BASE_CXX_FLAGS} -fsanitize=address -O2", "CMAKE_EXE_LINKER_FLAGS": "$env{BASE_LD_FLAGS} -static-libasan", "VERSION_SUFFIX": "asan", "SANITIZED_INTERFACE": "ON"}, "environment": {"ASAN_OPTIONS": "detect_leaks=0"}}, {"name": "cc_fuzz", "hidden": true, "inherits": ["base", "cc_base"], "cacheVariables": {"CMAKE_C_COMPILER": "clang", "CMAKE_CXX_COMPILER": "clang++", "CMAKE_C_FLAGS": "$env{BASE_C_FLAGS} -fsanitize=fuzzer-no-link,address,undefined -O2", "CMAKE_CXX_FLAGS": "$env{BASE_CXX_FLAGS} -fsanitize=fuzzer-no-link,address,undefined -O2", "CMAKE_EXE_LINKER_FLAGS": "$env{BASE_LD_FLAGS}", "VERSION_SUFFIX": "fuzz"}, "environment": {"ASAN_OPTIONS": "detect_leaks=0"}}, {"name": "cc_release", "hidden": true, "inherits": ["base", "cc_base"], "cacheVariables": {"CMAKE_C_FLAGS": "$env{BASE_C_FLAGS} -O2 -D_FORTIFY_SOURCE=2 -fstack-protector-strong", "CMAKE_CXX_FLAGS": "$env{BASE_CXX_FLAGS} -O2 -D_FORTIFY_SOURCE=2 -fstack-protector-strong"}}, {"name": "cc_cov", "hidden": true, "inherits": ["base", "cc_base"], "cacheVariables": {"CMAKE_C_FLAGS": "-g -fPIC --coverage -fprofile-update=atomic", "CMAKE_CXX_FLAGS": "-g -fPIC --coverage -fprofile-update=atomic"}}, {"name": "arm64-osx-release", "inherits": ["cc_release", "osx"]}, {"name": "arm64-osx-debug", "inherits": ["cc_debug", "osx"]}, {"name": "arm64-osx-asan", "inherits": ["cc_asan", "osx"]}, {"name": "arm64-osx-cov", "inherits": ["cc_cov", "osx"], "cacheVariables": {"VCPKG_TARGET_TRIPLET": "arm64-osx-debug"}}, {"name": "x64-osx-release", "inherits": ["cc_release", "osx"]}, {"name": "x64-osx-debug", "inherits": ["cc_debug", "osx"]}, {"name": "x64-osx-asan", "inherits": ["cc_asan", "osx"]}, {"name": "x64-osx-cov", "inherits": ["cc_cov", "osx"], "cacheVariables": {"VCPKG_TARGET_TRIPLET": "x64-osx-debug"}}, {"name": "arm64-linux-release", "inherits": ["cc_release", "linux"]}, {"name": "arm64-linux-debug", "inherits": ["cc_debug", "linux"]}, {"name": "arm64-linux-asan", "inherits": ["cc_asan", "linux"]}, {"name": "arm64-linux-cov", "inherits": ["cc_cov", "linux"], "cacheVariables": {"VCPKG_TARGET_TRIPLET": "arm64-linux-debug"}}, {"name": "arm64-linux-fuzz", "inherits": ["cc_fuzz", "linux"], "cacheVariables": {"VCPKG_TARGET_TRIPLET": "arm64-linux-debug", "BUILD_FUZZING": "ON"}}, {"name": "x64-linux-release", "inherits": ["cc_release", "linux"]}, {"name": "x64-linux-debug", "inherits": ["cc_debug", "linux"]}, {"name": "x64-linux-asan", "inherits": ["cc_asan", "linux"]}, {"name": "x64-linux-cov", "inherits": ["cc_cov", "linux"], "cacheVariables": {"VCPKG_TARGET_TRIPLET": "x64-linux-debug"}}], "buildPresets": [{"name": "arm64-linux-release", "configurePreset": "arm64-linux-release", "targets": ["all", "package"]}, {"name": "arm64-linux-debug", "configurePreset": "arm64-linux-debug", "targets": ["all", "package"]}, {"name": "arm64-linux-asan", "configurePreset": "arm64-linux-asan", "targets": ["package"]}, {"name": "arm64-linux-cov", "configurePreset": "arm64-linux-cov"}, {"name": "arm64-linux-cov-gen", "configurePreset": "arm64-linux-cov", "targets": ["cov-cobertura"]}, {"name": "x64-linux-release", "configurePreset": "x64-linux-release", "targets": ["all", "package"]}, {"name": "x64-linux-debug", "configurePreset": "x64-linux-debug", "targets": ["all", "rpm-zpn-brokerd", "rpm-zpn-ot-brokerd"]}, {"name": "x64-linux-asan", "configurePreset": "x64-linux-asan", "targets": ["all", "package"]}, {"name": "x64-linux-cov", "configurePreset": "x64-linux-cov"}, {"name": "x64-linux-cov-gen", "configurePreset": "x64-linux-cov", "targets": ["cov-cobertura"]}], "testPresets": [{"name": "base", "hidden": true, "output": {"outputOnFailure": true, "outputJUnitFile": "${sourceDir}/out/${presetName}/junit.xml"}, "execution": {"noTestsAction": "error"}, "filter": {"exclude": {"name": "zbalance|inclusive_app_domain_test|test_redir"}}, "environment": {"CTEST_PARALLEL_LEVEL": "$penv{CTEST_PARALLEL_LEVEL}"}}, {"name": "arm64-linux-release", "configurePreset": "arm64-linux-release", "inherits": "base"}, {"name": "arm64-linux-debug", "configurePreset": "arm64-linux-debug", "inherits": "base"}, {"name": "arm64-linux-asan", "configurePreset": "arm64-linux-asan", "inherits": "base"}, {"name": "arm64-linux-cov", "configurePreset": "arm64-linux-cov", "inherits": "base"}, {"name": "x64-linux-release", "configurePreset": "x64-linux-release", "inherits": "base"}, {"name": "x64-linux-debug", "configurePreset": "x64-linux-debug", "inherits": "base"}, {"name": "x64-linux-asan", "configurePreset": "x64-linux-asan", "inherits": "base"}, {"name": "x64-linux-cov", "configurePreset": "x64-linux-cov", "inherits": "base"}], "workflowPresets": [{"name": "arm64-linux-release", "steps": [{"type": "configure", "name": "arm64-linux-release"}, {"type": "build", "name": "arm64-linux-release"}, {"type": "test", "name": "arm64-linux-release"}]}, {"name": "arm64-linux-debug", "steps": [{"type": "configure", "name": "arm64-linux-debug"}, {"type": "build", "name": "arm64-linux-debug"}, {"type": "test", "name": "arm64-linux-debug"}]}, {"name": "arm64-linux-asan", "steps": [{"type": "configure", "name": "arm64-linux-asan"}, {"type": "build", "name": "arm64-linux-asan"}]}, {"name": "arm64-linux-cov", "steps": [{"type": "configure", "name": "arm64-linux-cov"}, {"type": "build", "name": "arm64-linux-cov"}, {"type": "test", "name": "arm64-linux-cov"}, {"type": "build", "name": "arm64-linux-cov-gen"}]}, {"name": "x64-linux-release", "steps": [{"type": "configure", "name": "x64-linux-release"}, {"type": "build", "name": "x64-linux-release"}, {"type": "test", "name": "x64-linux-release"}]}, {"name": "x64-linux-debug", "steps": [{"type": "configure", "name": "x64-linux-debug"}, {"type": "build", "name": "x64-linux-debug"}, {"type": "test", "name": "x64-linux-debug"}]}, {"name": "x64-linux-asan", "steps": [{"type": "configure", "name": "x64-linux-asan"}, {"type": "build", "name": "x64-linux-asan"}, {"type": "test", "name": "x64-linux-asan"}]}, {"name": "x64-linux-cov", "steps": [{"type": "configure", "name": "x64-linux-cov"}, {"type": "build", "name": "x64-linux-cov"}, {"type": "test", "name": "x64-linux-cov"}, {"type": "build", "name": "x64-linux-cov-gen"}]}]}