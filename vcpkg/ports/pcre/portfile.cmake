vcpkg_download_distfile(ARCHIVE
    URLS "https://nexus.et.zscaler.com/repository/raw-thirdparty/pcre/pcre_master_8.45_01_09_2023.tar.gz"
    FILENAME "pcre_master_8.45_01_09_2023.tar.gz"
    SHA512 f067057a17ade5cd5c5a230918c5fc2da057f83e15b6d5733474fccfdbadad049a3bd2be9e7d6324d4c34a4801de28071802cdf94bfef633c322e1875829d968
)

vcpkg_extract_source_archive(SOURCE_PATH
    ARCHIVE "${ARCHIVE}"
    SOURCE_BASE "v${VERSION}"
    PATCHES pcre-8.45_suppress_cmake_and_compiler_warnings-errors.patch
)

vcpkg_cmake_configure(
    SOURCE_PATH ${SOURCE_PATH}
    OPTIONS
        -DPCRE_BUILD_TESTS=NO
        -DPCRE_BUILD_PCREGREP=NO
        -DPCRE_BUILD_PCRE8=YES
        -DPCRE_SUPPORT_JIT=YES
        -DPCRE_SUPPORT_UTF=YES
        -DPCRE_SUPPORT_UNICODE_PROPERTIES=YES
        # optional dependencies for PCREGREP
        -DPCRE_SUPPORT_LIBBZ2=OFF
        -DPCRE_SUPPORT_LIBZ=OFF
        -DPCRE_SUPPORT_LIBEDIT=OFF
        -DPCRE_SUPPORT_LIBREADLINE=OFF
)

vcpkg_cmake_install()

foreach(FILE "${CURRENT_PACKAGES_DIR}/include/pcre.h" "${CURRENT_PACKAGES_DIR}/include/pcreposix.h")
    vcpkg_replace_string(${FILE} "defined(PCRE_STATIC)" "1")
endforeach()

vcpkg_fixup_pkgconfig()

file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/man")
vcpkg_replace_string("${CURRENT_PACKAGES_DIR}/bin/pcre-config" "${CURRENT_PACKAGES_DIR}" "`dirname $0`/..")
vcpkg_copy_tools(TOOL_NAMES "pcre-config" AUTO_CLEAN)

vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/COPYING")
