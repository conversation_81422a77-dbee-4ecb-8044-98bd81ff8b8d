diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4c0a9be..a49e52c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -12,7 +12,7 @@
 # ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 # OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 
-CMAKE_MINIMUM_REQUIRED(VERSION 2.6)
+CMAKE_MINIMUM_REQUIRED(VERSION 3.5)
 
 PROJECT(YetAnotherJSONParser C)
 
diff --git a/reformatter/CMakeLists.txt b/reformatter/CMakeLists.txt
index 52a9bee..f23dff9 100644
--- a/reformatter/CMakeLists.txt
+++ b/reformatter/CMakeLists.txt
@@ -34,10 +34,7 @@ IF (NOT WIN32)
     TARGET_LINK_LIBRARIES(json_reformat m)
 ENDIF (NOT WIN32)
 
-# copy the binary into the output directory
-GET_TARGET_PROPERTY(binPath json_reformat LOCATION)
-
 ADD_CUSTOM_COMMAND(TARGET json_reformat POST_BUILD
-    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${binPath} ${binDir})
+    COMMAND ${CMAKE_COMMAND} -E copy_if_different json_reformat ${binDir})
 
 INSTALL(TARGETS json_reformat RUNTIME DESTINATION bin)
diff --git a/verify/CMakeLists.txt b/verify/CMakeLists.txt
index 967fca1..263289b 100644
--- a/verify/CMakeLists.txt
+++ b/verify/CMakeLists.txt
@@ -28,10 +28,7 @@ ADD_EXECUTABLE(json_verify ${SRCS})
 
 TARGET_LINK_LIBRARIES(json_verify yajl_s)
 
-# copy in the binary
-GET_TARGET_PROPERTY(binPath json_verify LOCATION)
-
 ADD_CUSTOM_COMMAND(TARGET json_verify POST_BUILD
-    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${binPath} ${binDir})
+    COMMAND ${CMAKE_COMMAND} -E copy_if_different json_verify ${binDir})
 
 INSTALL(TARGETS json_verify RUNTIME DESTINATION bin)
