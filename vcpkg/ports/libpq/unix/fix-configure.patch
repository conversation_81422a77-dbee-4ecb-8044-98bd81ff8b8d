diff --git a/configure.ac b/configure.ac
index 7f97248..48ff1a1 100644
--- a/configure.ac
+++ b/configure.ac
@@ -19,7 +19,8 @@ m4_pattern_forbid(^PGAC_)dnl to catch undefined macros
 
 AC_INIT([PostgreSQL], [16.8], [<EMAIL>], [], [https://www.postgresql.org/])
 
-m4_if(m4_defn([m4_PACKAGE_VERSION]), [2.69], [], [m4_fatal([Autoconf version 2.69 is required.
+cross_compiling=yes # Avoid conftest loading shared objects
+m4_if(m4_defn([m4_PACKAGE_VERSION]), [2.69], [], [m4_warn([unsupported],[Autoconf version 2.69 is required.
 Untested combinations of 'autoconf' and PostgreSQL versions are not
 recommended.  You can remove the check from 'configure.ac' but it is then
 your responsibility whether the result works or not.])])
@@ -1311,7 +1312,8 @@ if test "$enable_thread_safety" = yes; then
 fi
 
 if test "$with_readline" = yes; then
-  PGAC_CHECK_READLINE
+  PKG_CHECK_MODULES([READLINE], [readline], [HAVE_LIBREADLINE=1], [pgac_cv_check_readline=no])
+  LIBS="$READLINE_LIBS $LIBS"
   if test x"$pgac_cv_check_readline" = x"no"; then
     AC_MSG_ERROR([readline library not found
 If you have readline already installed, see config.log for details on the
@@ -1321,7 +1323,7 @@ Use --without-readline to disable readline support.])
 fi
 
 if test "$with_zlib" = yes; then
-  AC_CHECK_LIB(z, inflate, [],
+  PKG_CHECK_MODULES([ZLIB], [zlib], [LIBS="$ZLIB_LIBS $LIBS"],
                [AC_MSG_ERROR([zlib library not found
 If you have zlib already installed, see config.log for details on the
 failure.  It is possible the compiler isn't looking in the proper directory.
@@ -1370,6 +1372,9 @@ if test "$with_ssl" = openssl ; then
   # Minimum required OpenSSL version is 1.0.1
   AC_DEFINE(OPENSSL_API_COMPAT, [0x10001000L],
             [Define to the OpenSSL API version in use. This avoids deprecation warnings from newer OpenSSL versions.])
+  PKG_CHECK_MODULES([CRYPTO_new_ex_data], [libcrypto], [LIBS="$CRYPTO_new_ex_data_LIBS $LIBS"], [AC_MSG_ERROR([library 'crypto' is required for OpenSSL])])
+  PKG_CHECK_MODULES([SSL_new], [libssl], [LIBS="$SSL_new_LIBS $LIBS"], [AC_MSG_ERROR([library 'ssl' is required for OpenSSL])])
+  if false ; then
   if test "$PORTNAME" != "win32"; then
      AC_CHECK_LIB(crypto, CRYPTO_new_ex_data, [], [AC_MSG_ERROR([library 'crypto' is required for OpenSSL])])
      AC_CHECK_LIB(ssl,    SSL_new, [], [AC_MSG_ERROR([library 'ssl' is required for OpenSSL])])
@@ -1377,6 +1382,7 @@ if test "$with_ssl" = openssl ; then
      AC_SEARCH_LIBS(CRYPTO_new_ex_data, [eay32 crypto], [], [AC_MSG_ERROR([library 'eay32' or 'crypto' is required for OpenSSL])])
      AC_SEARCH_LIBS(SSL_new, [ssleay32 ssl], [], [AC_MSG_ERROR([library 'ssleay32' or 'ssl' is required for OpenSSL])])
   fi
+  fi
   # Functions introduced in OpenSSL 1.0.2. LibreSSL does not have
   # SSL_CTX_set_cert_cb().
   AC_CHECK_FUNCS([X509_get_signature_nid SSL_CTX_set_cert_cb])
@@ -1403,19 +1409,23 @@ if test "$with_pam" = yes ; then
 fi
 
 if test "$with_libxml" = yes ; then
-  AC_CHECK_LIB(xml2, xmlSaveToBuffer, [], [AC_MSG_ERROR([library 'xml2' (version >= 2.6.23) is required for XML support])])
+  PKG_CHECK_MODULES([LIBXML2], [libxml-2.0 >= 2.6.23], [AC_DEFINE(HAVE_LIBXML2,1,[Define to 1 if with xml2])], [AC_MSG_ERROR([library 'xml2' (version >= 2.6.23) is required for XML support])])
+  LIBS="$LIBXML2_LIBS $LIBS"
 fi
 
 if test "$with_libxslt" = yes ; then
-  AC_CHECK_LIB(xslt, xsltCleanupGlobals, [], [AC_MSG_ERROR([library 'xslt' is required for XSLT support])])
+  PKG_CHECK_MODULES([LIBXSLT], [libxslt], [AC_DEFINE(HAVE_LIBXSLT,1,[Define to 1 if with xslt])], [AC_MSG_ERROR([library 'xslt' is required for XSLT support])])
+  LIBS="$LIBXSLT_LIBS $LIBS"
 fi
 
 if test "$with_lz4" = yes ; then
-  AC_CHECK_LIB(lz4, LZ4_compress_default, [], [AC_MSG_ERROR([library 'lz4' is required for LZ4 support])])
+  PKG_CHECK_MODULES([LZ4], [liblz4], [AC_DEFINE(HAVE_LIBLZ4,1,[Define to 1 if with lz4])], [AC_MSG_ERROR([library 'lz4' is required for LZ4 support])])
+  LIBS="$LZ4_LIBS $LIBS"
 fi
 
 if test "$with_zstd" = yes ; then
-  AC_CHECK_LIB(zstd, ZSTD_compress, [], [AC_MSG_ERROR([library 'zstd' is required for ZSTD support])])
+  PKG_CHECK_MODULES([ZSTD], [libzstd], [AC_DEFINE(HAVE_LIBZSTD,1,[Define to 1 if with zstd])], [AC_MSG_ERROR([library 'zstd' is required for ZSTD support])])
+  LIBS="$ZSTD_LIBS $LIBS"
 fi
 
 # Note: We can test for libldap_r only after we know PTHREAD_LIBS;
