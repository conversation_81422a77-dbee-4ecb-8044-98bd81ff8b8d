vcpkg_download_distfile(ARCHIVE
    URLS "https://nexus.et.zscaler.com/repository/raw-thirdparty/fast_ber/fast_ber-${VERSION}.tar.gz" # Some pre 0.4 commit
    FILENAME "fast_ber-${VERSION}.tar.gz"
    SHA512 0f00681e3ba2aa0013c0fc7fb4c4f15edf8b4d27bb83f48dce34814564befe8fc08c117fc598f7e83f6716e80cbcc6ae847650bb51e6cb76508ead0d29919dcf
)

vcpkg_extract_source_archive(SOURCE_PATH
    ARCHIVE "${ARCHIVE}"
    SOURCE_BASE "v${VERSION}"
    PATCHES
        fix-absl.patch
        drop-compiler-flags.patch
        fragment-payload.patch
        cmake.patch
)

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        -DSKIP_AUTO_GENERATION=ON
        -DSKIP_TESTING=ON
        -DCMAKE_CXX_STANDARD=14 # Bump fast_ber to latest version to be able to build with latest standards.
)

vcpkg_cmake_install()

vcpkg_cmake_config_fixup(PACKAGE_NAME "fast_ber" CONFIG_PATH "lib/fast_ber")
vcpkg_copy_tools(TOOL_NAMES fast_ber_compiler fast_ber_view AUTO_CLEAN)

vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
